/**
 * Configuration Type Definitions
 * Centralized type definitions for all configuration domains
 */

// Base configuration interfaces
export interface BaseConfig {
  enabled: boolean;
}

// Node configuration
export interface NodeConfigDomain {
  name: string;
  hubUrl: string;
  capabilities: string[];
  version: string;
  environment: string;
  useSystemInfo: boolean;
  port: number;
  nodeId: string;
}

// File system configuration
export interface FileSystemConfigDomain {
  screenshotsDir: string;
  nodeScenarioDir: string;
}

// Redis configuration
export interface RedisConfigDomain extends BaseConfig {
  host: string;
  port: number;
  password: string;
  username: string;
  db: number;
  prefix: string;
  url: string;
}

// BullMQ configuration
export interface BullMQConfigDomain {
  // Job lock configuration
  jobLockRefreshInterval: number;    // How often to refresh job locks (ms)
  stalledJobLockExtension: number;   // How long to extend stalled job locks (ms)

  // Worker configuration
  lockDuration: number;              // Initial lock duration for jobs (ms)
  stalledInterval: number;           // How often to check for stalled jobs (ms)
  maxStalledCount: number;           // Maximum number of times a job can be stalled

  // Active job monitoring
  activeJobMonitoringTimeout: number; // Timeout for active job monitoring (ms)
  activeJobLockExtension: number;     // Lock extension duration for active jobs (ms)

  // Queue processing
  concurrency: number;               // Number of concurrent jobs to process
  drainDelay: number;                // Delay before checking for new jobs when queue is empty (ms)
}

// WebSocket configuration
export interface WebSocketConfigDomain extends BaseConfig {
  // Additional WebSocket-specific properties can be added here
}

// MinIO storage configuration
export interface MinioConfigDomain extends BaseConfig {
  endPoint: string;
  port: number;
  useSSL: boolean;
  accessKey: string;
  secretKey: string;
  region: string;
  screenshotBucket: string;
  reportBucket: string;
  publicBaseUrl: string;
}

// Network Collector configuration
export interface NetworkCollectorConfigDomain extends BaseConfig {
  // Memory management settings
  maxInMemoryEntries: number;        // Maximum entries to keep in memory
  enableTemporaryStorage: boolean;   // Whether to use temporary file storage
  tempFilePrefix: string;            // Prefix for temporary files
  cleanupOnDestroy: boolean;         // Whether to cleanup temp files on destroy

  // Performance settings
  flushThreshold: number;            // When to flush to temp storage
  compressionEnabled: boolean;       // Whether to compress temp files

  // Monitoring settings
  memoryWarningThreshold: number;    // Warn when memory usage exceeds this
  enableMemoryMonitoring: boolean;   // Whether to monitor memory usage
}

// AWS S3 storage configuration
export interface S3ConfigDomain extends BaseConfig {
  region: string;
  accessKey: string;
  secretKey: string;
  endpoint?: string;
  forcePathStyle: boolean;
  screenshotBucket: string;
  reportBucket: string;
  publicBaseUrl: string;
}

// Test options configuration
export interface TestOptionsConfigDomain {
  maxRetries: number;
  timeout: number;
}

// Appium server configuration
export interface AppiumServerConfigDomain {
  hostname: string;
  port: number;
  path: string;
  protocol: 'http' | 'https';
}

// Appium capabilities configuration
export interface AppiumCapabilitiesConfigDomain {
  platformName: string;
  'appium:automationName': string;
  'appium:deviceName': string;
  'appium:platformVersion'?: string;
  'appium:udid'?: string;
  'appium:appPackage'?: string;
  'appium:appActivity'?: string;
}

// Sauce Labs configuration
export interface SauceLabsConfigDomain extends BaseConfig {
  user: string;
  key: string;
  region: string;
  options: {
    build?: string;
    name?: string;
    appiumVersion?: string; // Required for Appium 2 support
  };
}

// Android platform configuration
// Note: Appium and Sauce Labs settings are provided dynamically in test requests
export interface AndroidPlatformConfigDomain extends BaseConfig {
  // Dynamic settings (appium, capabilities, sauceLabs) are provided in test requests
}

// Web platform configuration
export interface WebPlatformConfigDomain extends BaseConfig {
  // Additional web-specific properties can be added here
}

// Platform configuration
export interface PlatformConfigDomain {
  web: WebPlatformConfigDomain;
  android: AndroidPlatformConfigDomain;
}

// Configuration validation result
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Configuration source types
export type ConfigSource = 'environment' | 'file' | 'default' | 'runtime';

// Configuration change event
export interface ConfigChangeEvent<T = any> {
  key: string;
  oldValue: T;
  newValue: T;
  source: ConfigSource;
  timestamp: Date;
}

// Configuration watcher callback
export type ConfigWatcher<T = any> = (event: ConfigChangeEvent<T>) => void;

// Configuration validator function
export type ConfigValidator<T = any> = (value: T) => ConfigValidationResult;
