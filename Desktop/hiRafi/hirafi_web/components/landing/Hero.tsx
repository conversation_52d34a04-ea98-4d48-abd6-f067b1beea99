import React from 'react';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';

const Hero = () => {
  return (
    <section className="pt-24 md:pt-32 pb-16 md:pb-24 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-10 md:mb-0">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              <span className="block">AI-Powered</span>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
                Test Automation
              </span>
              <span className="block">Redefined</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Transform your testing process with HiRafi's AI-driven platform.
              No manual scripting. Self-healing tests. 50% less maintenance.
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <a href="#contact" className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 inline-block text-center">
                Schedule POC
              </a>
              <a href="#features" className="px-8 py-3 bg-white text-blue-600 border border-blue-600 font-medium rounded-lg shadow-sm hover:shadow transition-all flex items-center justify-center">
                Learn More <ArrowRight size={18} className="ml-2" />
              </a>
            </div>
          </div>
          <div className="md:w-1/2 relative">
            <div className="w-full rounded-xl overflow-hidden shadow-2xl transform md:rotate-1 transition-all hover:rotate-0 duration-500">
              <Image
                src="/images/image_fx.jpg"
                alt="AI-powered testing dashboard"
                width={800}
                height={600}
                className="w-full h-auto"
              />
            </div>
            <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg max-w-xs transform transition-transform hover:scale-105">
              <div className="flex items-center mb-2">
                <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                <span className="text-sm font-medium">Test Passed</span>
              </div>
              <p className="text-xs text-gray-600">Self-healing automation fixed 12 tests with no intervention</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;