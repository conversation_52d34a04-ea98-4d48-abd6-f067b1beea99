"use client"

import { ReactNode } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft, ArrowRight, CheckSquare, Save } from "lucide-react"
import { useRunForm } from "./run-context"
import { RunDetails } from "./common/run-details"
import { SelectScenarios } from "./common/select-scenarios"
import { EnvironmentSettings } from "./environment-settings"
import { ReportSettings } from "./report-settings"
import { RunPreview } from "./run-preview"

// Layout için boş children prop alıyoruz ancak kullanmıyoruz, çünkü içerik dinamik olarak oluşturuluyor
interface CreateRunLayoutProps {
  children?: ReactNode
  isEditing?: boolean
  runId?: string
}

export function CreateRunLayout({ children, isEditing = false, runId }: CreateRunLayoutProps) {
  const {
    currentStep,
    nextStep,
    prevStep,
    setStep,
    isSubmitting,
    submitRun,
    validateCurrentStep,
    formData,
    errors
  } = useRunForm()

  // Step başlıkları ve açıklamaları
  const steps = [
    {
      title: "Run Details",
      description: "Set the basic run information."
    },
    {
      title: "Select Scenarios",
      description: "Choose which scenarios to include."
    },
    {
      title: "Environment & Reports",
      description: "Configure test environment and reporting options."
    },
    {
      title: "Review & Run",
      description: "Review your settings and start the run."
    }
  ]

  // Belirli bir adıma geçiş yapma
  const handleStepClick = (stepIndex: number) => {
    // Sadece tamamlanmış adımlara veya bir sonraki adıma gidilebilir
    if (stepIndex <= currentStep + 1) {
      // Eğer mevcut adımdan ileri gidiyorsak validasyon yapalım
      if (stepIndex > currentStep) {
        const isValid = validateCurrentStep();
        if (isValid) {
          setStep(stepIndex);
        } else {
          console.log("Validation failed, cannot proceed to step:", stepIndex);
        }
      } else {
        // Mevcut adımdan geri gidiyorsak validasyon yapmadan geçebiliriz
        setStep(stepIndex);
      }
    }
  };

  // Hangi içeriğin gösterileceğini belirle
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <RunDetails />
      case 1:
        return <SelectScenarios />
      case 2:
        return (
          <div className="space-y-6">
            {formData.platform === 'android' ? (
              // For Android, only render EnvironmentSettings in full width
              // since it already includes the report settings in its tabbed interface
              <div className="grid grid-cols-1 gap-6">
                <EnvironmentSettings />
              </div>
            ) : (
              // For Web, render both components side by side with extended height
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 min-h-[800px]">
                <div className="h-full">
                  <EnvironmentSettings />
                </div>
                <div className="h-full">
                  <ReportSettings />
                </div>
              </div>
            )}
          </div>
        )
      case 3:
        return <RunPreview />
      default:
        return <RunDetails />
    }
  }

  // Next button'a tıklanınca
  const handleNextClick = () => {
    console.log("Next button clicked for step:", currentStep);

    // Form verilerini ve hatalarını kontrol et
    console.log("Form data before validation:", {
      name: formData.name,
      nameLength: formData.name?.length || 0,
      nameTrimmedLength: formData.name?.trim().length || 0,
      currentErrors: errors
    });

    // Doğrulama yap
    const isValid = validateCurrentStep();
    console.log("Validation result:", isValid);

    // Doğrulamayı geçerse sonraki adıma git
    if (isValid) {
      nextStep();
    } else {
      console.log("Validation failed for step:", currentStep);
    }
  };

  return (
    <div className="flex-1 flex flex-col h-full max-h-full">
      {/* Header */}
      <header className="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/40 dark:to-blue-950/40 p-4 border-b border-indigo-100 dark:border-indigo-900/30 flex-shrink-0 shadow-sm">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">
            {isEditing
              ? `Edit Test Run${formData.name ? `: ${formData.name}` : ''}`
              : "Create New Test Run"
            }
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {isEditing ? "Modify an existing test run configuration" : "Configure and start a new test run"}
          </p>
        </div>
      </header>

      {/* Progress steps with navigation buttons */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 flex-shrink-0 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center">
            {/* Steps */}
            <div className="flex-1 flex items-center justify-between">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className={`flex-1 flex flex-col items-center relative ${index < steps.length - 1 ? "after:content-[''] after:absolute after:top-4 after:w-full after:h-0.5 after:bg-gradient-to-r after:from-indigo-200 after:to-blue-200 after:dark:from-indigo-800 after:dark:to-blue-800 after:left-1/2 after:z-[1]" : ""}`}
                >
                  <button
                    onClick={() => handleStepClick(index)}
                    disabled={index > currentStep + 1}
                    className={`w-9 h-9 rounded-full flex items-center justify-center font-semibold z-10 transition-all duration-200 shadow-md
                      ${index <= currentStep + 1 ? "cursor-pointer hover:scale-110" : "cursor-not-allowed opacity-70"}
                      ${currentStep > index
                        ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white"
                        : currentStep === index
                          ? "bg-gradient-to-r from-indigo-600 to-blue-600 text-white ring-2 ring-indigo-200 dark:ring-indigo-800"
                          : index === currentStep + 1
                            ? "bg-gradient-to-r from-indigo-200 to-blue-200 dark:from-indigo-800 dark:to-blue-800 text-indigo-800 dark:text-indigo-200"
                            : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                      }
                    `}
                  >
                    {currentStep > index ? <CheckSquare className="h-4 w-4" /> : index + 1}
                  </button>
                  <div className={`mt-2 text-xs font-medium ${
                    currentStep === index
                      ? "text-indigo-600 dark:text-indigo-400 font-semibold"
                      : currentStep > index
                        ? "text-emerald-600 dark:text-emerald-400"
                        : "text-gray-600 dark:text-gray-400"
                  }`}>
                    {step.title}
                  </div>
                </div>
              ))}
            </div>

            {/* Navigation buttons */}
            <div className="ml-6 flex items-center gap-3 flex-shrink-0">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 0 || isSubmitting}
                className="h-10 border-indigo-200 dark:border-indigo-800 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/30 transition-all duration-200"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </Button>

              {currentStep < steps.length - 1 ? (
                <Button
                  onClick={handleNextClick}
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white glow-animation h-10 shadow-md transition-all duration-200 font-medium"
                >
                  {isSubmitting ? (
                    <>
                      <div className="mr-2 h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                      Please wait...
                    </>
                  ) : (
                    <>
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={submitRun}
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white h-10 shadow-md transition-all duration-200 font-medium"
                >
                  {isSubmitting ? (
                    <>
                      <div className="mr-2 h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Run
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-b from-gray-50 to-white dark:from-gray-950 dark:to-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="bg-white dark:bg-gray-900 rounded-lg shadow-md border border-indigo-100 dark:border-indigo-900/30 overflow-hidden backdrop-blur-sm bg-white/80 dark:bg-gray-900/80">
            <div className="p-6">
              <div className="space-y-3 mb-5">
                <h2 className="text-xl font-semibold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">
                  {steps[currentStep].title}
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {steps[currentStep].description}
                </p>
              </div>

              <div className="border-t border-indigo-100 dark:border-indigo-900/30 pt-6">
                {renderStepContent()}
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* CSS Animation */}
      <style jsx global>{`
        .glow-animation {
          animation: glow 2s infinite alternate;
          position: relative;
          overflow: hidden;
        }

        .glow-animation::after {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transform: rotate(30deg);
          animation: shine 3s infinite;
        }

        @keyframes glow {
          from {
            box-shadow: 0 0 5px -2px rgba(79, 70, 229, 0.4);
          }
          to {
            box-shadow: 0 0 15px 3px rgba(79, 70, 229, 0.6);
          }
        }

        @keyframes shine {
          0% {
            left: -100%;
            top: -100%;
          }
          100% {
            left: 100%;
            top: 100%;
          }
        }
      `}</style>
    </div>
  )
}