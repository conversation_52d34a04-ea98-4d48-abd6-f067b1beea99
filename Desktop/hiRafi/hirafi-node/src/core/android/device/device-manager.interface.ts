/**
 * Device Manager Interface
 * Defines the contract for Android device management
 */

import { TestRequest } from '../../../models/types.js';

/**
 * Android Agent type
 * Represents an agent for interacting with Android devices
 */
export type AndroidAgent = any;

/**
 * Appium Server Configuration
 * Configuration for connecting to an Appium server
 */
export interface AppiumServerConfig {
  hostname: string;
  port?: number;
  path?: string;
  protocol?: 'http' | 'https';
}

/**
 * Appium Base Capabilities
 * Base capabilities for Appium sessions
 */
export interface AppiumBaseCapabilities {
  platformName: string;
  'appium:automationName'?: string;
  'appium:deviceName'?: string;
  'appium:udid'?: string;
  'appium:platformVersion'?: string;
  'appium:appPackage'?: string;
  'appium:appActivity'?: string;
  [key: string]: any;
}

/**
 * Sauce Labs Configuration
 * Configuration for connecting to Sauce Labs
 */
export interface SauceLabsConfig {
  user: string;
  key: string;
  region?: "us-west-1" | "eu-central-1" | "us-east-1" | "apac-southeast-1";
}

/**
 * Sauce Labs Capabilities
 * Additional capabilities for Sauce Labs sessions
 */
export interface SauceLabsCapabilities {
  'sauce:options'?: {
    build?: string;
    name?: string;
    appiumVersion?: string; // Required for Appium 2 support
    [key: string]: any;
  };
}

/**
 * Device Manager Interface
 * Handles Android device connection and management
 */
export interface IDeviceManager {
  /**
   * Connect to a device
   * @param deviceId Optional device identifier
   * @param scenarioId Optional scenario identifier for reporting (should be scenarioId, not testId)
   * @param platformVersion Optional platform version to override default
   * @returns Promise resolving to a device agent
   */
  connectToDevice(deviceId?: string, scenarioId?: string, platformVersion?: string): Promise<AndroidAgent>;

  /**
   * Connect to an Appium hub using test request configuration
   * @param testRequest Test request containing Appium configuration
   * @returns Promise resolving to a device agent
   */
  connectToAppiumHub(testRequest: TestRequest): Promise<AndroidAgent>;

  /**
   * Disconnect from a device
   * @param device Device agent to disconnect from
   */
  disconnectFromDevice(device: AndroidAgent): Promise<void>;

  /**
   * Disconnect from the current device
   */
  disconnectFromCurrentDevice(): Promise<void>;

  /**
   * Get the current device agent
   * @returns Current device agent or null if not connected
   */
  getCurrentDevice(): AndroidAgent | null;

  /**
   * Get a list of connected devices
   * @returns Promise resolving to a list of device identifiers
   */
  getConnectedDevices(): Promise<string[]>;

  /**
   * Create an AI agent for a device
   * @param device Device agent to create AI agent for
   * @returns Promise resolving to an Android agent
   */
  createAgent(device: AndroidAgent): Promise<AndroidAgent>;

  /**
   * Get the AI agent for a device
   * @param device Device agent to get AI agent for
   * @returns Android agent or undefined if not found
   */
  getDeviceAgent(device: AndroidAgent): AndroidAgent | undefined;

  /**
   * Initialize the device manager
   * @param config Configuration object
   */
  initialize(config: any): Promise<void>;
}
