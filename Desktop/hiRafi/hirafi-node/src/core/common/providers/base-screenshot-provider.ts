/**
 * Unified Screenshot Provider
 * Handles screenshot functionality for all platforms (Android and Web)
 * Consolidates AndroidScreenshotProvider and WebScreenshotProvider
 */

import { IScreenshotProvider } from '../interfaces/screenshot-provider.interface.js';
import { IArtifactStorage } from '../storage/artifact-storage.interface.js';
import { logger } from '../../../utils/logger.js';
import { takeScreenshot as misoTakeScreenshot } from 'rfi-ai-android';
import sharp from 'sharp';
import {
  generateScreenshotPath,
  ensureDirectoryExists,
  writeBufferToFile
} from '../utils/file-utils.js';
import {
  createScreenshotArtifact,
  validateArtifact
} from '../utils/artifact-utils.js';
import {
  handleAsyncOperation
} from '../utils/error-handling.js';
import { IScreenshotQueueService, ScreenshotQueueServiceFactory } from '../storage/screenshot-queue-service.js';

/**
 * Unified Screenshot Provider
 * Implements screenshot handling logic for all platforms
 */
export class UnifiedScreenshotProvider implements IScreenshotProvider {
  protected artifactStorage: IArtifactStorage;
  protected screenshotDir: string;
  protected config: any;
  protected platform: 'web' | 'mobile';
  private screenshotQueueService: IScreenshotQueueService | null = null;

  /**
   * Create a new BaseScreenshotProvider
   * @param artifactStorage Storage service for screenshots
   * @param platform Platform identifier
   * @param config Configuration object
   */
  constructor(
    artifactStorage: IArtifactStorage,
    platform: 'web' | 'mobile',
    config: any = {}
  ) {
    this.artifactStorage = artifactStorage;
    this.platform = platform;
    this.screenshotDir = config.screenshotDir || 'screenshots';
    this.config = config;

    logger.debug(`BaseScreenshotProvider: Initialized for ${platform} platform`);
  }

  /**
   * Take a screenshot for any platform (immediate capture, async upload)
   * @param target Platform-specific target (Page, AndroidAgent, etc.)
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @param stepIndex Step index for queue ordering
   * @returns Promise resolving to the URL of the stored screenshot
   */
  async takeScreenshot(
    target: any,
    testId: string,
    stepId: string,
    type: 'before' | 'after',
    stepIndex?: number
  ): Promise<string> {
    try {
      logger.debug(`UnifiedScreenshotProvider: Taking ${type} screenshot for test ${testId}, step ${stepId}`);

      // CRITICAL FIX: Capture screenshot buffer IMMEDIATELY while page is still open
      // This ensures we get the actual page content before any cleanup happens
      const buffer = await this.captureScreenshotBuffer(target);
      logger.debug(`UnifiedScreenshotProvider: Screenshot buffer captured immediately (${buffer.length} bytes) for test ${testId}, step ${stepId}`);

      // Save to local file immediately to prevent memory bloat
      const localPath = await this.saveScreenshotToLocal(buffer, testId, stepId, type);
      logger.debug(`UnifiedScreenshotProvider: Screenshot saved to local file: ${localPath}`);

      // Queue screenshot for async upload with the already-captured buffer
      return await this.queueScreenshotUpload(buffer, testId, stepId, type, stepIndex || 0);
    } catch (error: any) {
      // Provide more specific error messages based on error type
      let errorMessage = error.message;

      if (this.isProtocolError(error)) {
        errorMessage = `Protocol error during screenshot capture: ${error.message}`;
        logger.warn(`UnifiedScreenshotProvider: ${errorMessage}`);
      } else if (errorMessage.includes('Page is closed') || errorMessage.includes('Browser is disconnected')) {
        errorMessage = `Browser/page state error: ${error.message}`;
        logger.warn(`UnifiedScreenshotProvider: ${errorMessage}`);
      } else {
        logger.error(`UnifiedScreenshotProvider: Error taking screenshot: ${errorMessage}`);
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * Queue screenshot for asynchronous upload
   * @param buffer Screenshot buffer
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @param stepIndex Step index for queue ordering
   * @returns Promise resolving to the URL of the stored screenshot
   */
  private async queueScreenshotUpload(
    buffer: Buffer,
    testId: string,
    stepId: string,
    type: 'before' | 'after',
    stepIndex: number
  ): Promise<string> {
    logger.debug(`UnifiedScreenshotProvider: Processing screenshot buffer before queuing (${buffer.length} bytes)`);

    // Process buffer with Sharp optimization BEFORE queuing
    const processedBuffer = await this.processScreenshotBuffer(buffer);

    logger.debug(`UnifiedScreenshotProvider: Screenshot buffer processed, queuing for upload (${processedBuffer.length} bytes)`);

    // Get or create the queue service instance
    const queueService = await this.getScreenshotQueueService();

    // Queue the processed screenshot for upload
    return await queueService.queueScreenshot(
      testId,
      stepId,
      stepIndex,
      processedBuffer,
      type,
      this.platform
    );
  }

  private async getScreenshotQueueService(): Promise<IScreenshotQueueService> {
    if (!this.screenshotQueueService) {
      logger.debug('UnifiedScreenshotProvider: Creating ScreenshotQueueService instance');
      this.screenshotQueueService = await ScreenshotQueueServiceFactory.createService(
        {
          maxRetries: this.config.screenshotMaxRetries || 3,
          maxQueueSize: this.config.screenshotMaxQueueSize || 1000,
          enableLogging: this.config.enableScreenshotQueueLogging || false,
        },
        this.artifactStorage
      );
    }
    return this.screenshotQueueService;
  }

  /**
   * Capture screenshot buffer from platform-specific target
   * @param target Platform-specific target
   * @returns Promise resolving to screenshot buffer
   */
  protected async captureScreenshotBuffer(target: any): Promise<Buffer> {
    if (this.platform === 'web') {
      return this.captureWebScreenshot(target);
    } else {
      return this.captureAndroidScreenshot(target);
    }
  }

  /**
   * Capture screenshot from web page
   * @param page Puppeteer page
   * @returns Promise resolving to screenshot buffer
   */
  private async captureWebScreenshot(page: any): Promise<Buffer> {
    try {
      logger.debug('UnifiedScreenshotProvider: Starting web screenshot capture');

      // Simple wait for page readiness
      try {
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 10000 });
      } catch (waitError: any) {
        // If wait fails, proceed anyway - page might already be ready
        logger.debug('UnifiedScreenshotProvider: Network idle wait timed out, proceeding with screenshot');
      }

      // Small delay to ensure rendering is complete
      await this.delay(500);

      const buffer = await page.screenshot({
        type: 'jpeg',
        quality: this.config.quality || 85,
        fullPage: this.config.fullPage || false,
        timeout: 30000
      });

      return Buffer.from(buffer);
    } catch (error: any) {
      logger.error(`UnifiedScreenshotProvider: Error capturing web screenshot: ${error.message}`);

      // Single retry with reduced quality
      if (this.isProtocolError(error)) {
        logger.warn(`UnifiedScreenshotProvider: Protocol error detected, attempting single retry`);
        
        try {
          await this.delay(1000);
          const retryBuffer = await page.screenshot({
            type: 'jpeg',
            quality: 70,
            fullPage: false,
            timeout: 20000
          });

          return Buffer.from(retryBuffer);
        } catch (retryError: any) {
          logger.error(`UnifiedScreenshotProvider: Retry failed: ${retryError.message}`);
          throw retryError;
        }
      }

      throw error;
    }
  }

  /**
   * Capture screenshot from Android device
   * @param agent Android agent
   * @returns Promise resolving to screenshot buffer
   */
  private async captureAndroidScreenshot(agent: any): Promise<Buffer> {
    try {
      if (!agent || !agent.page) {
        throw new Error('Invalid Android agent or page');
      }

      // Use misoai-android's takeScreenshot to get buffer
      const tempPath = `/tmp/android_screenshot_${Date.now()}.png`;

      await misoTakeScreenshot(agent.page, {
        filePath: tempPath,
        createDir: true,
        quality: this.config.quality || 90
      });

      // Read the file as buffer
      const fs = await import('fs');
      const buffer = await fs.promises.readFile(tempPath);

      // Clean up temporary file
      try {
        await fs.promises.unlink(tempPath);
      } catch (cleanupError) {
        logger.warn(`UnifiedScreenshotProvider: Could not clean up temp file ${tempPath}`);
      }

      return buffer;
    } catch (error: any) {
      logger.error(`UnifiedScreenshotProvider: Error capturing Android screenshot: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process a screenshot buffer and store it
   * @param buffer Screenshot buffer
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @returns Promise resolving to the URL of the stored screenshot
   */
  async processScreenshot(
    buffer: Buffer,
    testId: string,
    stepId: string,
    type: 'before' | 'after'
  ): Promise<string> {
    const context = `BaseScreenshotProvider.processScreenshot`;

    const result = await handleAsyncOperation(async () => {
      // Get screenshot path
      const screenshotPath = this.getScreenshotPath(testId, stepId, type);

      // Ensure directory exists
      await ensureDirectoryExists(screenshotPath.substring(0, screenshotPath.lastIndexOf('/')));

      // Process buffer if needed (can be overridden by subclasses)
      const processedBuffer = await this.processScreenshotBuffer(buffer);

      // Write screenshot to file
      await writeBufferToFile(screenshotPath, processedBuffer);

      logger.debug(`${context}: Saved screenshot to ${screenshotPath}`);

      // Create artifact
      const artifact = createScreenshotArtifact(
        testId,
        stepId,
        type,
        this.platform,
        screenshotPath,
        this.getCustomTags()
      );

      // Validate artifact
      if (!validateArtifact(artifact)) {
        throw new Error('Invalid artifact created');
      }

      // Upload to storage
      const uploadResult = await this.artifactStorage.uploadArtifact(artifact);

      logger.info(`${context}: Screenshot uploaded successfully`);
      return uploadResult.url || uploadResult.storagePath;
    }, context);

    if (!result.success) {
      throw new Error((result as any).error || 'Screenshot operation failed');
    }

    return result.data!;
  }

  /**
   * Get the path where a screenshot would be stored
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @returns Path to the screenshot
   */
  getScreenshotPath(testId: string, stepId: string, type: 'before' | 'after'): string {
    return generateScreenshotPath(this.screenshotDir, testId, stepId, type, this.platform);
  }

  /**
   * Save screenshot to local file immediately to prevent memory bloat
   * @param buffer Screenshot buffer
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @returns Promise resolving to the local file path
   */
  private async saveScreenshotToLocal(
    buffer: Buffer,
    testId: string,
    stepId: string,
    type: 'before' | 'after'
  ): Promise<string> {
    try {
      // Get screenshot path
      const screenshotPath = this.getScreenshotPath(testId, stepId, type);

      // Ensure directory exists
      await ensureDirectoryExists(screenshotPath.substring(0, screenshotPath.lastIndexOf('/')));

      // Process buffer if needed (can be overridden by subclasses)
      const processedBuffer = await this.processScreenshotBuffer(buffer);

      // Write screenshot to file immediately
      await writeBufferToFile(screenshotPath, processedBuffer);

      logger.debug(`UnifiedScreenshotProvider: Saved screenshot to local file: ${screenshotPath} (${processedBuffer.length} bytes)`);
      return screenshotPath;
    } catch (error: any) {
      logger.warn(`UnifiedScreenshotProvider: Failed to save screenshot to local file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process screenshot buffer with platform-specific optimizations
   * @param buffer Original screenshot buffer
   * @returns Processed screenshot buffer
   */
  protected async processScreenshotBuffer(buffer: Buffer): Promise<Buffer> {
    if (this.platform === 'web') {
      return this.processWebScreenshot(buffer);
    } else {
      return this.processAndroidScreenshot(buffer);
    }
  }

  /**
   * Process web screenshot with Sharp optimization
   * @param buffer Original screenshot buffer
   * @returns Optimized screenshot buffer
   */
  private async processWebScreenshot(buffer: Buffer): Promise<Buffer> {
    try {
      logger.debug(`UnifiedScreenshotProvider: Starting Sharp optimization for web screenshot (${buffer.length} bytes)`);

      // Get image metadata to check dimensions
      const metadata = await sharp(buffer).metadata();
      const { width, height } = metadata;

      // Optimize the screenshot using Sharp with resizing if needed
      let sharpInstance = sharp(buffer);

      // Resize if image is too large (reduce dimensions to save space)
      if (width && height && (width > 1920 || height > 1080)) {
        sharpInstance = sharpInstance.resize(1920, 1080, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }

      const optimizedBuffer = await sharpInstance
        .jpeg({
          quality: 45, // Further reduced from 60 to 45 for better compression
          mozjpeg: true,
          trellisQuantisation: true,
          overshootDeringing: true,
          optimizeScans: true
        })
        .toBuffer();

      // Log optimization results
      this.logOptimizationResults(buffer, optimizedBuffer);

      logger.debug(`UnifiedScreenshotProvider: Sharp optimization completed successfully (${optimizedBuffer.length} bytes)`);
      return optimizedBuffer;
    } catch (error: any) {
      logger.error(`UnifiedScreenshotProvider: Error optimizing web screenshot: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process Android screenshot with Sharp optimization
   * @param buffer Original screenshot buffer
   * @returns Optimized screenshot buffer
   */
  private async processAndroidScreenshot(buffer: Buffer): Promise<Buffer> {
    try {
      logger.debug(`UnifiedScreenshotProvider: Starting Sharp optimization for Android screenshot (${buffer.length} bytes)`);

      // Get image metadata to check dimensions
      const metadata = await sharp(buffer).metadata();
      const { width, height } = metadata;

      // Optimize the screenshot using Sharp with resizing if needed
      let sharpInstance = sharp(buffer);

      // Resize if image is too large (reduce dimensions to save space)
      if (width && height && (width > 1920 || height > 1080)) {
        sharpInstance = sharpInstance.resize(1920, 1080, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }

      // Optimize Android screenshots with reduced quality for better compression
      const optimizedBuffer = await sharpInstance
        .jpeg({
          quality: 55, // Reduced from 75 to 55 for better compression while maintaining readability
          mozjpeg: true,
          trellisQuantisation: true,
          overshootDeringing: true,
          optimizeScans: true
        })
        .toBuffer();

      // Log optimization results
      this.logOptimizationResults(buffer, optimizedBuffer);

      logger.debug(`UnifiedScreenshotProvider: Sharp optimization completed successfully (${optimizedBuffer.length} bytes)`);
      return optimizedBuffer;
    } catch (error: any) {
      logger.error(`UnifiedScreenshotProvider: Error optimizing Android screenshot: ${error.message}`);
      throw error;
    }
  }

  /**
   * Log optimization results for screenshots
   * @param originalBuffer Original image buffer
   * @param optimizedBuffer Optimized image buffer
   */
  private logOptimizationResults(originalBuffer: Buffer, optimizedBuffer: Buffer): void {
    const originalSize = originalBuffer.length;
    const optimizedSize = optimizedBuffer.length;
    const reductionBytes = originalSize - optimizedSize;
    const reductionPercent = Math.round((reductionBytes / originalSize) * 100);
    const reductionRatio = originalSize / optimizedSize;

    logger.debug(`UnifiedScreenshotProvider: Image optimization results:
      - Original size: ${originalSize} bytes
      - Optimized size: ${optimizedSize} bytes
      - Size reduction: ${reductionBytes} bytes (${reductionPercent}%)
      - Reduction ratio: ${reductionRatio.toFixed(2)}x`);
  }

  /**
   * Get custom tags for artifacts with platform-specific information
   * @returns Custom tags object
   */
  protected getCustomTags(): Record<string, string> {
    const baseTags = {
      platform: this.platform,
      provider: this.constructor.name
    };

    if (this.platform === 'web') {
      return {
        ...baseTags,
        optimization: 'sharp-jpeg-resized',
        quality: '45',
        compression: 'mozjpeg',
        maxDimensions: '1920x1080'
      };
    } else {
      return {
        ...baseTags,
        deviceType: 'android',
        optimization: 'sharp-jpeg-resized',
        quality: '55',
        compression: 'mozjpeg',
        captureMethod: 'misoai-android',
        maxDimensions: '1920x1080'
      };
    }
  }

  /**
   * Initialize the screenshot provider
   * @param config Configuration object
   */
  async initialize(config: any): Promise<void> {
    this.config = { ...this.config, ...config };

    // Ensure screenshot directory exists
    await ensureDirectoryExists(this.screenshotDir);

    logger.info(`BaseScreenshotProvider: Initialized for ${this.platform} platform`);
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    logger.debug(`BaseScreenshotProvider: Cleanup completed for ${this.platform} platform`);
  }



  /**
   * Check if error is a Protocol error that can be retried
   * @param error Error object
   * @returns True if it's a retryable Protocol error
   */
  private isProtocolError(error: any): boolean {
    const errorMessage = error.message || '';
    const errorCode = error.code || '';

    // Check for specific Chrome DevTools Protocol error codes
    const protocolErrorCodes = [
      -32603, // Internal error
      -32000, // Server error
      -32001, // Invalid request
      -32002, // Method not found
      -32003  // Invalid params
    ];

    const protocolErrorPatterns = [
      'Protocol error',
      'Page.captureScreenshot',
      'Internal error',
      'Target closed',
      'Session closed',
      'Connection closed',
      'WebSocket is not open',
      'Runtime.callFunctionOn',
      'Page.navigate',
      'Target.createTarget',
      'Browser context destroyed',
      'Execution context was destroyed',
      'Cannot find context with specified id'
    ];

    // Check both error codes and message patterns
    return protocolErrorCodes.includes(errorCode) ||
           protocolErrorPatterns.some(pattern => errorMessage.includes(pattern));
  }



  /**
   * Simple delay utility
   * @param ms Milliseconds to delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get provider statistics with platform-specific information
   * @returns Provider statistics
   */
  getStats(): Record<string, any> {
    const baseStats = {
      platform: this.platform,
      screenshotDir: this.screenshotDir,
      providerType: this.constructor.name
    };

    if (this.platform === 'web') {
      return {
        ...baseStats,
        optimization: 'enabled-with-resizing',
        compressionEngine: 'sharp-mozjpeg',
        quality: 45,
        maxDimensions: '1920x1080'
      };
    } else {
      return {
        ...baseStats,
        optimization: 'enabled-with-resizing',
        compressionEngine: 'sharp-mozjpeg',
        captureMethod: 'misoai-android',
        quality: 55,
        maxDimensions: '1920x1080'
      };
    }
  }
}

// Backward compatibility export
export { UnifiedScreenshotProvider as BaseScreenshotProvider };
