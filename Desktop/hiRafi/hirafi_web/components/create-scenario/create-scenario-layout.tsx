"use client"

import { useCallback } from "react"
import { useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Save, Loader2, ChevronRight, ChevronLeft, Check, ClipboardList, Laptop, FileBarChart, PlayCircle } from "lucide-react"
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"

interface CreateScenarioLayoutProps {
  children: React.ReactNode
}

export function CreateScenarioLayout({ children }: CreateScenarioLayoutProps) {
  const router = useRouter()
  const {
    formData,
    ui,
    setCurrentStep,
    saveScenario,
    setSidebarCollapsed
  } = useUnifiedScenarioContext()

  // Extract values from unified hook
  const currentStep = ui.currentStep || 0
  const isLoading = ui.isSaving || false
  const sidebarCollapsed = ui.sidebarCollapsed || false

  // Step labels for the wizard (5 adım olacak şekilde güncellendi - TestRail Integration eklendi)
  const stepLabels = ["Scenario Details", "Test Steps", "TestRail Integration", "Preview"]



  // Step ikonları
  const stepIcons = [
    <ClipboardList key="details" className="h-5 w-5" />,
    <PlayCircle key="teststeps" className="h-5 w-5" />,
    <svg width="20" height="20" viewBox="0 0 256 236" fill="currentColor" xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" key="testrail">
      <path d="M153.6 236H89.6L0 118.2 89.6 0h64L64 118.2 153.6 236z" />
      <path d="M166.4 236h-64l89.6-117.8L102.4 0h64l89.6 118.2L166.4 236z" />
    </svg>,
    <Check key="preview" className="h-5 w-5" />
  ]

  // Check if can save (name and at least one step are required)
  const canSave = !!formData.name.trim() && formData.steps.length > 0



  // Check if a step is completed
  const isStepCompleted = (stepIndex: number) => {
    // First step is completed if name is provided or AI details is enabled
    if (stepIndex === 0) {
      return formData.useAIForDetails || (!!formData.name && formData.name.trim() !== '');
    }

    // Second step is completed if there are steps
    if (stepIndex === 1) {
      return formData.steps.length > 0;
    }

    // Third step (TestRail integration) is completed if there are TestRail cases selected
    if (stepIndex === 2) {
      return formData.testrailCases && formData.testrailCases.length > 0;
    }

    return false;
  }

  // Check if can proceed to next step - memoized to prevent unnecessary re-renders
  const canProceedToNextStep = useCallback(() => {
    if (currentStep === 0) {
      // Allow proceeding if AI details is enabled or if name is provided
      const canProceed = formData.useAIForDetails || (!!formData.name && formData.name.trim() !== '');



      return canProceed;
    } else if (currentStep === 1) {
      // Test Steps adımından sonraki adıma geçiş şartları - en az bir adım olmalı
      return formData.steps.length > 0;
    } else if (currentStep === 2) {
      // TestRail Integration adımından sonraki adıma geçiş şartları - her zaman ilerlenebilir
      return true;
    }

    return false;
  }, [currentStep, formData.useAIForDetails, formData.name, formData.steps.length])

  // Go to next step - memoized to prevent unnecessary re-renders
  const handleNextStep = useCallback(() => {
    if (currentStep < stepLabels.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }, [currentStep, stepLabels.length, setCurrentStep])

  // Go to previous step
  const handlePreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Calculate progress percentage
  const progressPercentage = ((currentStep + 1) / stepLabels.length) * 100;

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header with gradient - Daha kompakt ve responsive hale getirildi */}
        <header className="border-b border-gray-200 dark:border-gray-800 bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
          {/* Ana içerik ve kontrolleri tek satıra sığdıran kompakt header */}
          <div className="py-3 px-4">
            <div className="flex flex-col md:flex-row md:items-center gap-3">
              {/* Başlık ve navigasyon */}
              <div className="flex items-center justify-between w-full md:w-auto">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-full text-white hover:bg-white/20 flex-shrink-0"
                    onClick={() => router.push("/scenarios")}
                  >
                    <ArrowLeft className="h-5 w-5" />
                  </Button>
                  <div>
                    <h1 className="text-xl font-bold">Create New Scenario</h1>
                    <p className="text-sm text-white/80 hidden md:block">Define your test scenario details and steps</p>
                  </div>
                </div>

                {/* Mobil için adım göstergesi */}
                <span className="md:hidden text-sm font-medium text-white/80">
                  {currentStep + 1}/{stepLabels.length}
                </span>
              </div>

              {/* Steps indicators - orta sütun */}
              <div className="hidden md:flex flex-1 items-center justify-center space-x-2 relative z-10 px-4">
                {stepLabels.map((label, index) => (
                  <div
                    key={label}
                    className={`
                      flex items-center gap-1
                      ${index === 0 || (index > 0 && index <= currentStep) || (index === currentStep + 1 && canProceedToNextStep())
                        ? 'cursor-pointer'
                        : 'opacity-60 cursor-not-allowed'}
                      ${currentStep === index ? 'text-white font-medium' : 'text-indigo-100/60'}
                      ${index === 0 ? '' : 'ml-4'}
                    `}
                    onClick={() => {
                      // Tıklanabilir adımlara geçiş
                      if (index === 0 || (index > 0 && index <= currentStep) || (index === currentStep + 1 && canProceedToNextStep())) {
                        setCurrentStep(index)
                      }
                    }}
                  >
                    {/* Step circle */}
                    <div
                      className={`
                        w-8 h-8 rounded-full flex items-center justify-center
                        ${currentStep === index
                          ? 'bg-white text-indigo-600 ring-2 ring-white/30 shadow-glow'
                          : isStepCompleted(index)
                            ? 'bg-green-400 text-white'
                            : 'bg-white/20 text-white'}
                        transition-all duration-500 ease-in-out
                      `}
                    >
                      {isStepCompleted(index) && currentStep !== index ? (
                        <Check className="h-4 w-4 stroke-[3]" />
                      ) : (
                        stepIcons[index]
                      )}
                    </div>

                    {/* Step label - sadece mevcut adımı göster */}
                    {index === currentStep && (
                      <span className="text-xs font-medium text-white whitespace-nowrap">
                        {label}
                      </span>
                    )}

                    {/* Adımlar arası bağlantı çizgisi */}
                    {index < stepLabels.length - 1 && (
                      <div className="w-8 h-0.5 bg-white/20 mx-1">
                        <div
                          className="h-full bg-gradient-to-r from-green-400 to-teal-400 transition-all duration-700 ease-out"
                          style={{
                            width: isStepCompleted(index) ? '100%' : '0%'
                          }}
                        ></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Butonlar - sağ sütun */}
              <div className="flex items-center gap-2 self-end md:self-auto mt-2 md:mt-0">
                {/* Only show Next button if not on last step */}
                {currentStep < stepLabels.length - 1 && (
                  <Button
                    onClick={handleNextStep}
                    disabled={!canProceedToNextStep()}
                    className="group relative overflow-hidden gap-1 bg-white text-indigo-600 hover:text-white transition-all duration-500 px-5 py-2 text-sm font-medium border-2 border-white hover:border-transparent shadow-md hover:shadow-lg"
                  >
                    <span className="relative z-10 transition-transform duration-500 group-hover:translate-x-1">Next</span>
                    <ChevronRight className="h-4 w-4 relative z-10 transition-all duration-500 group-hover:translate-x-1 group-hover:scale-110" />
                    <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-indigo-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-500 scale-x-0 group-hover:scale-x-100 origin-left transform"></div>
                  </Button>
                )}

                <Button
                  onClick={saveScenario}
                  disabled={!canSave || isLoading}
                  className="gap-1 bg-white text-indigo-600 hover:bg-white/90 border-none transition-all duration-300 hover:scale-105 text-sm"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="hidden sm:inline">Saving...</span>
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      <span className="hidden sm:inline">Save</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Mobil için progress bar */}
          <div className="md:hidden h-1 bg-white/20 w-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-green-400 to-teal-400 transition-all duration-700 ease-out"
              style={{
                width: `${progressPercentage}%`,
                boxShadow: '0 0 8px rgba(74, 222, 128, 0.6)'
              }}
            ></div>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-7xl mx-auto">
            {/* Current Step Content */}
            {children}
          </div>
        </main>
      </div>

      {/* Add glow animation for active step */}
      <style jsx global>{`
        @keyframes glow {
          0% {
            box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
          }
          50% {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.8), 0 0 5px rgba(129, 140, 248, 0.6);
          }
          100% {
            box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
          }
        }
        .shadow-glow {
          animation: glow 2s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}
// Removed duplicate StepNavigation function - using the one from step-navigation.tsx instead
