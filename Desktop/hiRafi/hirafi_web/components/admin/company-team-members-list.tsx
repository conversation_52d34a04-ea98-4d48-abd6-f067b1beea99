"use client"

import { useState, useEffect } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { MoreHorizontal, Edit, Trash2, Eye, Mail, Phone } from "lucide-react"
import Link from "next/link"
import { getCompanyTeams, getTeamMembers, removeTeamMember } from "@/lib/api/admin-api"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"

interface TeamMember {
  id: string
  userId: string
  teamId: string
  roleId: string
  roleName: string
  status: 'active' | 'inactive' | 'pending'
  addedAt: string
  user: {
    id: string
    name: string
    email: string
    phone?: string
    avatarUrl?: string
  }
}

interface CompanyTeamMembersListProps {
  companyId: string
}

export function CompanyTeamMembersList({ companyId }: CompanyTeamMembersListProps) {
  const [members, setMembers] = useState<TeamMember[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [memberToRemove, setMemberToRemove] = useState<string | null>(null)
  const [isRemoving, setIsRemoving] = useState(false)
  const [teamId, setTeamId] = useState<string | null>(null)

  useEffect(() => {
    if (companyId) {
      fetchTeamAndMembers()
    }
  }, [companyId])

  const fetchTeamAndMembers = async () => {
    if (!companyId) {
      setMembers([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    try {
      // Önce şirketin takımını bul
      const teamsResponse = await getCompanyTeams(companyId)

      if (teamsResponse.success && teamsResponse.teams && teamsResponse.teams.length > 0) {
        const team = teamsResponse.teams[0] // Şirketin ilk takımını al
        setTeamId(team.id)

        // Takım üyelerini getir
        const membersResponse = await getTeamMembers(team.id)

        if (membersResponse.success && membersResponse.members) {
          setMembers(membersResponse.members)
        } else {
          console.error("Failed to fetch team members:", membersResponse.error)
          toast({
            title: "Hata",
            description: `Takım üyeleri yüklenemedi: ${membersResponse.error || 'Bilinmeyen hata'}`,
            variant: "destructive",
          })
          setMembers([])
        }
      } else {
        console.error("Failed to fetch teams or no teams found:", teamsResponse.error)
        toast({
          title: "Bilgi",
          description: "Bu şirketin henüz bir takımı bulunmuyor.",
        })
        setMembers([])
      }
    } catch (error) {
      console.error("Error fetching team members:", error)
      toast({
        title: "Hata",
        description: "Takım üyeleri yüklenirken bir hata oluştu.",
        variant: "destructive",
      })
      setMembers([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveMember = async () => {
    if (!memberToRemove || !teamId) return

    setIsRemoving(true)
    try {
      const response = await removeTeamMember(teamId, memberToRemove)

      if (response.success) {
        toast({
          title: "Üye çıkarıldı",
          description: "Üye başarıyla takımdan çıkarıldı.",
        })
        fetchTeamAndMembers()
      } else {
        toast({
          title: "Hata",
          description: response.error || "Üye çıkarılırken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error removing team member:", error)
      toast({
        title: "Hata",
        description: "Üye çıkarılırken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsRemoving(false)
      setMemberToRemove(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'secondary'
      case 'pending':
        return 'warning'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif'
      case 'inactive':
        return 'Pasif'
      case 'pending':
        return 'Beklemede'
      default:
        return status
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Takım üyeleri yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (!teamId) {
    return (
      <div className="flex h-40 flex-col items-center justify-center gap-4">
        <p className="text-muted-foreground">Bu şirkete henüz takım eklenmemiş.</p>
      </div>
    )
  }

  if (members.length === 0) {
    return (
      <div className="flex h-40 flex-col items-center justify-center gap-4">
        <p className="text-muted-foreground">Bu takımda henüz üye bulunmuyor.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Kullanıcı</TableHead>
              <TableHead>E-posta</TableHead>
              <TableHead>Rol</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Eklenme Tarihi</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member) => (
              <TableRow key={member.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {member.user.avatarUrl ? (
                      <img
                        src={member.user.avatarUrl}
                        alt={member.user.name}
                        className="h-8 w-8 rounded-full"
                      />
                    ) : (
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                        {member.user.name.charAt(0).toUpperCase()}
                      </div>
                    )}
                    <div>
                      <div className="font-medium">{member.user.name}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{member.user.email}</span>
                  </div>
                  {member.user.phone && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Phone className="h-3 w-3" />
                      <span>{member.user.phone}</span>
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{member.roleName}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(member.status) as any}>
                    {getStatusText(member.status)}
                  </Badge>
                </TableCell>
                <TableCell>{formatDate(member.addedAt)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">İşlemler</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/users/${member.userId}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          Kullanıcıyı Görüntüle
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/users/${member.userId}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Kullanıcıyı Düzenle
                        </Link>
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem onSelect={(e) => {
                            e.preventDefault()
                            setMemberToRemove(member.userId)
                          }}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Takımdan Çıkar
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Üyeyi takımdan çıkarmak istediğinize emin misiniz?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bu işlem geri alınabilir. Kullanıcı takımdan çıkarılacak ancak sistemden silinmeyecektir.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel onClick={() => setMemberToRemove(null)}>İptal</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleRemoveMember}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              disabled={isRemoving}
                            >
                              {isRemoving ? (
                                <div className="flex items-center">
                                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-current"></div>
                                  Çıkarılıyor...
                                </div>
                              ) : (
                                "Takımdan Çıkar"
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
