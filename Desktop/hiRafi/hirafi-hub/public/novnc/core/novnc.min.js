/**
 * Minified by jsDelivr using Terser v5.15.1.
 * Original file: /npm/@novnc/novnc@1.4.0/lib/rfb.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
"use strict";function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _int=require("./util/int.js"),Log=_interopRequireWildcard(require("./util/logging.js")),_strings=require("./util/strings.js"),_browser=require("./util/browser.js"),_element=require("./util/element.js"),_events=require("./util/events.js"),_eventtarget=_interopRequireDefault(require("./util/eventtarget.js")),_display=_interopRequireDefault(require("./display.js")),_inflator=_interopRequireDefault(require("./inflator.js")),_deflator=_interopRequireDefault(require("./deflator.js")),_keyboard=_interopRequireDefault(require("./input/keyboard.js")),_gesturehandler=_interopRequireDefault(require("./input/gesturehandler.js")),_cursor=_interopRequireDefault(require("./util/cursor.js")),_websock=_interopRequireDefault(require("./websock.js")),_des=_interopRequireDefault(require("./des.js")),_keysym=_interopRequireDefault(require("./input/keysym.js")),_xtscancodes=_interopRequireDefault(require("./input/xtscancodes.js")),_encodings=require("./encodings.js"),_ra=_interopRequireDefault(require("./ra2.js")),_md=require("./util/md5.js"),_raw=_interopRequireDefault(require("./decoders/raw.js")),_copyrect=_interopRequireDefault(require("./decoders/copyrect.js")),_rre=_interopRequireDefault(require("./decoders/rre.js")),_hextile=_interopRequireDefault(require("./decoders/hextile.js")),_tight=_interopRequireDefault(require("./decoders/tight.js")),_tightpng=_interopRequireDefault(require("./decoders/tightpng.js")),_zrle=_interopRequireDefault(require("./decoders/zrle.js")),_jpeg=_interopRequireDefault(require("./decoders/jpeg.js"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,i=new WeakMap;return(_getRequireWildcardCache=function(e){return e?i:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==_typeof(e)&&"function"!=typeof e)return{default:e};var i=_getRequireWildcardCache(t);if(i&&i.has(e))return i.get(e);var n={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var r in e)if("default"!==r&&Object.prototype.hasOwnProperty.call(e,r)){var o=s?Object.getOwnPropertyDescriptor(e,r):null;o&&(o.get||o.set)?Object.defineProperty(n,r,o):n[r]=e[r]}return n.default=e,i&&i.set(e,n),n}function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return e};var e={},t=Object.prototype,i=t.hasOwnProperty,n=Object.defineProperty||function(e,t,i){e[t]=i.value},s="function"==typeof Symbol?Symbol:{},r=s.iterator||"@@iterator",o=s.asyncIterator||"@@asyncIterator",a=s.toStringTag||"@@toStringTag";function c(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,i){return e[t]=i}}function u(e,t,i,s){var r=t&&t.prototype instanceof d?t:d,o=Object.create(r.prototype),a=new E(s||[]);return n(o,"_invoke",{value:m(e,i,a)}),o}function h(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var l={};function d(){}function _(){}function f(){}var p={};c(p,r,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(x([])));v&&v!==t&&i.call(v,r)&&(p=v);var y=f.prototype=d.prototype=Object.create(p);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function s(n,r,o,a){var c=h(e[n],e,r);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==_typeof(l)&&i.call(l,"__await")?t.resolve(l.__await).then((function(e){s("next",e,o,a)}),(function(e){s("throw",e,o,a)})):t.resolve(l).then((function(e){u.value=e,o(u)}),(function(e){return s("throw",e,o,a)}))}a(c.arg)}var r;n(this,"_invoke",{value:function(e,i){function n(){return new t((function(t,n){s(e,i,t,n)}))}return r=r?r.then(n,n):n()}})}function m(e,t,i){var n="suspendedStart";return function(s,r){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===s)throw r;return T()}for(i.method=s,i.arg=r;;){var o=i.delegate;if(o){var a=S(o,i);if(a){if(a===l)continue;return a}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var c=h(e,t,i);if("normal"===c.type){if(n=i.done?"completed":"suspendedYield",c.arg===l)continue;return{value:c.arg,done:i.done}}"throw"===c.type&&(n="completed",i.method="throw",i.arg=c.arg)}}}function S(e,t){var i=t.method,n=e.iterator[i];if(void 0===n)return t.delegate=null,"throw"===i&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==i&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+i+"' method")),l;var s=h(n,e.iterator,t.arg);if("throw"===s.type)return t.method="throw",t.arg=s.arg,t.delegate=null,l;var r=s.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,l):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,l)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function x(e){if(e){var t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,s=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return s.next=s}}return{next:T}}function T(){return{value:void 0,done:!0}}return _.prototype=f,n(y,"constructor",{value:f,configurable:!0}),n(f,"constructor",{value:_,configurable:!0}),_.displayName=c(f,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,a,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(k.prototype),c(k.prototype,o,(function(){return this})),e.AsyncIterator=k,e.async=function(t,i,n,s,r){void 0===r&&(r=Promise);var o=new k(u(t,i,n,s),r);return e.isGeneratorFunction(i)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},b(y),c(y,a,"Generator"),c(y,r,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),i=[];for(var n in t)i.push(n);return i.reverse(),function e(){for(;i.length;){var n=i.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=x,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(i,n){return o.type="throw",o.arg=e,t.next=i,n&&(t.method="next",t.arg=void 0),!!n}for(var s=this.tryEntries.length-1;s>=0;--s){var r=this.tryEntries[s],o=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var a=i.call(r,"catchLoc"),c=i.call(r,"finallyLoc");if(a&&c){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(a){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n];if(s.tryLoc<=this.prev&&i.call(s,"finallyLoc")&&this.prev<s.finallyLoc){var r=s;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var o=r?r.completion:{};return o.type=e,o.arg=t,r?(this.method="next",this.next=r.finallyLoc,l):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),l},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),C(i),l}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var s=n.arg;C(i)}return s}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:x(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=void 0),l}},e}function asyncGeneratorStep(e,t,i,n,s,r,o){try{var a=e[r](o),c=a.value}catch(e){return void i(e)}a.done?t(c):Promise.resolve(c).then(n,s)}function _asyncToGenerator(e){return function(){var t=this,i=arguments;return new Promise((function(n,s){var r=e.apply(t,i);function o(e){asyncGeneratorStep(r,n,s,o,a,"next",e)}function a(e){asyncGeneratorStep(r,n,s,o,a,"throw",e)}o(void 0)}))}}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArrayLimit(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var n,s,r,o,a=[],c=!0,u=!1;try{if(r=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;c=!1}else for(;!(c=(n=r.call(i)).done)&&(a.push(n.value),a.length!==t);c=!0);}catch(e){u=!0,s=e}finally{try{if(!c&&null!=i.return&&(o=i.return(),Object(o)!==o))return}finally{if(u)throw s}}return a}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _createForOfIteratorHelper(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,s=function(){};return{s:s,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o=!0,a=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return o=e.done,e},e:function(e){a=!0,r=e},f:function(){try{o||null==i.return||i.return()}finally{if(a)throw r}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_toPropertyKey(n.key),n)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"===_typeof(t)?t:String(t)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!==_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_setPrototypeOf(e,t)}function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var i,n=_getPrototypeOf(e);if(t){var s=_getPrototypeOf(this).constructor;i=Reflect.construct(n,arguments,s)}else i=n.apply(this,arguments);return _possibleConstructorReturn(this,i)}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function _getPrototypeOf(e){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_getPrototypeOf(e)}var DISCONNECT_TIMEOUT=3,DEFAULT_BACKGROUND="rgb(40, 40, 40)",MOUSE_MOVE_DELAY=17,WHEEL_STEP=50,WHEEL_LINE_HEIGHT=19,GESTURE_ZOOMSENS=75,GESTURE_SCRLSENS=50,DOUBLE_TAP_TIMEOUT=1e3,DOUBLE_TAP_THRESHOLD=50,securityTypeNone=1,securityTypeVNCAuth=2,securityTypeRA2ne=6,securityTypeTight=16,securityTypeVeNCrypt=19,securityTypeXVP=22,securityTypeARD=30,securityTypeMSLogonII=113,securityTypeUnixLogon=129,securityTypePlain=256,extendedClipboardFormatText=1,extendedClipboardFormatRtf=2,extendedClipboardFormatHtml=4,extendedClipboardFormatDib=8,extendedClipboardFormatFiles=16,extendedClipboardActionCaps=1<<24,extendedClipboardActionRequest=1<<25,extendedClipboardActionPeek=1<<26,extendedClipboardActionNotify=1<<27,extendedClipboardActionProvide=1<<28,RFB=function(e){_inherits(s,_eventtarget["default"]);var t,i,n=_createSuper(s);function s(e,t,i){var r;if(_classCallCheck(this,s),!e)throw new Error("Must specify target");if(!t)throw new Error("Must specify URL, WebSocket or RTCDataChannel");window.isSecureContext||Log.Error("noVNC requires a secure context (TLS). Expect crashes!"),(r=n.call(this))._target=e,"string"==typeof t?r._url=t:(r._url=null,r._rawChannel=t),i=i||{},r._rfbCredentials=i.credentials||{},r._shared=!("shared"in i)||!!i.shared,r._repeaterID=i.repeaterID||"",r._wsProtocols=i.wsProtocols||[],r._rfbConnectionState="",r._rfbInitState="",r._rfbAuthScheme=-1,r._rfbCleanDisconnect=!0,r._rfbRSAAESAuthenticationState=null,r._rfbVersion=0,r._rfbMaxVersion=3.8,r._rfbTightVNC=!1,r._rfbVeNCryptState=0,r._rfbXvpVer=0,r._fbWidth=0,r._fbHeight=0,r._fbName="",r._capabilities={power:!1},r._supportsFence=!1,r._supportsContinuousUpdates=!1,r._enabledContinuousUpdates=!1,r._supportsSetDesktopSize=!1,r._screenID=0,r._screenFlags=0,r._qemuExtKeyEventSupported=!1,r._clipboardText=null,r._clipboardServerCapabilitiesActions={},r._clipboardServerCapabilitiesFormats={},r._sock=null,r._display=null,r._flushing=!1,r._keyboard=null,r._gestures=null,r._resizeObserver=null,r._disconnTimer=null,r._resizeTimeout=null,r._mouseMoveTimer=null,r._decoders={},r._FBU={rects:0,x:0,y:0,width:0,height:0,encoding:null},r._mousePos={},r._mouseButtonMask=0,r._mouseLastMoveTime=0,r._viewportDragging=!1,r._viewportDragPos={},r._viewportHasMoved=!1,r._accumulatedWheelDeltaX=0,r._accumulatedWheelDeltaY=0,r._gestureLastTapTime=null,r._gestureFirstDoubleTapEv=null,r._gestureLastMagnitudeX=0,r._gestureLastMagnitudeY=0,r._eventHandlers={focusCanvas:r._focusCanvas.bind(_assertThisInitialized(r)),handleResize:r._handleResize.bind(_assertThisInitialized(r)),handleMouse:r._handleMouse.bind(_assertThisInitialized(r)),handleWheel:r._handleWheel.bind(_assertThisInitialized(r)),handleGesture:r._handleGesture.bind(_assertThisInitialized(r)),handleRSAAESCredentialsRequired:r._handleRSAAESCredentialsRequired.bind(_assertThisInitialized(r)),handleRSAAESServerVerification:r._handleRSAAESServerVerification.bind(_assertThisInitialized(r))},Log.Debug(">> RFB.constructor"),r._screen=document.createElement("div"),r._screen.style.display="flex",r._screen.style.width="100%",r._screen.style.height="100%",r._screen.style.overflow="auto",r._screen.style.background=DEFAULT_BACKGROUND,r._canvas=document.createElement("canvas"),r._canvas.style.margin="auto",r._canvas.style.outline="none",r._canvas.width=0,r._canvas.height=0,r._canvas.tabIndex=-1,r._screen.appendChild(r._canvas),r._cursor=new _cursor.default,r._cursorImage=s.cursors.none,r._decoders[_encodings.encodings.encodingRaw]=new _raw.default,r._decoders[_encodings.encodings.encodingCopyRect]=new _copyrect.default,r._decoders[_encodings.encodings.encodingRRE]=new _rre.default,r._decoders[_encodings.encodings.encodingHextile]=new _hextile.default,r._decoders[_encodings.encodings.encodingTight]=new _tight.default,r._decoders[_encodings.encodings.encodingTightPNG]=new _tightpng.default,r._decoders[_encodings.encodings.encodingZRLE]=new _zrle.default,r._decoders[_encodings.encodings.encodingJPEG]=new _jpeg.default;try{r._display=new _display.default(r._canvas)}catch(e){throw Log.Error("Display exception: "+e),e}return r._display.onflush=r._onFlush.bind(_assertThisInitialized(r)),r._keyboard=new _keyboard.default(r._canvas),r._keyboard.onkeyevent=r._handleKeyEvent.bind(_assertThisInitialized(r)),r._gestures=new _gesturehandler.default,r._sock=new _websock.default,r._sock.on("open",r._socketOpen.bind(_assertThisInitialized(r))),r._sock.on("close",r._socketClose.bind(_assertThisInitialized(r))),r._sock.on("message",r._handleMessage.bind(_assertThisInitialized(r))),r._sock.on("error",r._socketError.bind(_assertThisInitialized(r))),r._expectedClientWidth=null,r._expectedClientHeight=null,r._resizeObserver=new ResizeObserver(r._eventHandlers.handleResize),r._updateConnectionState("connecting"),Log.Debug("<< RFB.constructor"),r.dragViewport=!1,r.focusOnClick=!0,r._viewOnly=!1,r._clipViewport=!1,r._clippingViewport=!1,r._scaleViewport=!1,r._resizeSession=!1,r._showDotCursor=!1,void 0!==i.showDotCursor&&(Log.Warn("Specifying showDotCursor as a RFB constructor argument is deprecated"),r._showDotCursor=i.showDotCursor),r._qualityLevel=6,r._compressionLevel=2,r}return _createClass(s,[{key:"viewOnly",get:function(){return this._viewOnly},set:function(e){this._viewOnly=e,"connecting"!==this._rfbConnectionState&&"connected"!==this._rfbConnectionState||(e?this._keyboard.ungrab():this._keyboard.grab())}},{key:"capabilities",get:function(){return this._capabilities}},{key:"clippingViewport",get:function(){return this._clippingViewport}},{key:"_setClippingViewport",value:function(e){e!==this._clippingViewport&&(this._clippingViewport=e,this.dispatchEvent(new CustomEvent("clippingviewport",{detail:this._clippingViewport})))}},{key:"touchButton",get:function(){return 0},set:function(e){Log.Warn("Using old API!")}},{key:"clipViewport",get:function(){return this._clipViewport},set:function(e){this._clipViewport=e,this._updateClip()}},{key:"scaleViewport",get:function(){return this._scaleViewport},set:function(e){this._scaleViewport=e,e&&this._clipViewport&&this._updateClip(),this._updateScale(),!e&&this._clipViewport&&this._updateClip()}},{key:"resizeSession",get:function(){return this._resizeSession},set:function(e){this._resizeSession=e,e&&this._requestRemoteResize()}},{key:"showDotCursor",get:function(){return this._showDotCursor},set:function(e){this._showDotCursor=e,this._refreshCursor()}},{key:"background",get:function(){return this._screen.style.background},set:function(e){this._screen.style.background=e}},{key:"qualityLevel",get:function(){return this._qualityLevel},set:function(e){!Number.isInteger(e)||e<0||e>9?Log.Error("qualityLevel must be an integer between 0 and 9"):this._qualityLevel!==e&&(this._qualityLevel=e,"connected"===this._rfbConnectionState&&this._sendEncodings())}},{key:"compressionLevel",get:function(){return this._compressionLevel},set:function(e){!Number.isInteger(e)||e<0||e>9?Log.Error("compressionLevel must be an integer between 0 and 9"):this._compressionLevel!==e&&(this._compressionLevel=e,"connected"===this._rfbConnectionState&&this._sendEncodings())}},{key:"disconnect",value:function(){this._updateConnectionState("disconnecting"),this._sock.off("error"),this._sock.off("message"),this._sock.off("open"),null!==this._rfbRSAAESAuthenticationState&&this._rfbRSAAESAuthenticationState.disconnect()}},{key:"approveServer",value:function(){null!==this._rfbRSAAESAuthenticationState&&this._rfbRSAAESAuthenticationState.approveServer()}},{key:"sendCredentials",value:function(e){this._rfbCredentials=e,this._resumeAuthentication()}},{key:"sendCtrlAltDel",value:function(){"connected"!==this._rfbConnectionState||this._viewOnly||(Log.Info("Sending Ctrl-Alt-Del"),this.sendKey(_keysym.default.XK_Control_L,"ControlLeft",!0),this.sendKey(_keysym.default.XK_Alt_L,"AltLeft",!0),this.sendKey(_keysym.default.XK_Delete,"Delete",!0),this.sendKey(_keysym.default.XK_Delete,"Delete",!1),this.sendKey(_keysym.default.XK_Alt_L,"AltLeft",!1),this.sendKey(_keysym.default.XK_Control_L,"ControlLeft",!1))}},{key:"machineShutdown",value:function(){this._xvpOp(1,2)}},{key:"machineReboot",value:function(){this._xvpOp(1,3)}},{key:"machineReset",value:function(){this._xvpOp(1,4)}},{key:"sendKey",value:function(e,t,i){if("connected"===this._rfbConnectionState&&!this._viewOnly){if(void 0===i)return this.sendKey(e,t,!0),void this.sendKey(e,t,!1);var n=_xtscancodes.default[t];if(this._qemuExtKeyEventSupported&&n)e=e||0,Log.Info("Sending key ("+(i?"down":"up")+"): keysym "+e+", scancode "+n),s.messages.QEMUExtendedKeyEvent(this._sock,e,i,n);else{if(!e)return;Log.Info("Sending keysym ("+(i?"down":"up")+"): "+e),s.messages.keyEvent(this._sock,e,i?1:0)}}}},{key:"focus",value:function(e){this._canvas.focus(e)}},{key:"blur",value:function(){this._canvas.blur()}},{key:"clipboardPasteFrom",value:function(e){if("connected"===this._rfbConnectionState&&!this._viewOnly)if(this._clipboardServerCapabilitiesFormats[extendedClipboardFormatText]&&this._clipboardServerCapabilitiesActions[extendedClipboardActionNotify])this._clipboardText=e,s.messages.extendedClipboardNotify(this._sock,[extendedClipboardFormatText]);else{var t,i,n;t=0;var r,o=_createForOfIteratorHelper(e);try{for(o.s();!(r=o.n()).done;){r.value;t++}}catch(e){o.e(e)}finally{o.f()}n=new Uint8Array(t),i=0;var a,c=_createForOfIteratorHelper(e);try{for(c.s();!(a=c.n()).done;){var u=a.value.codePointAt(0);u>255&&(u=63),n[i++]=u}}catch(e){c.e(e)}finally{c.f()}s.messages.clientCutText(this._sock,n)}}},{key:"getImageData",value:function(){return this._display.getImageData()}},{key:"toDataURL",value:function(e,t){return this._display.toDataURL(e,t)}},{key:"toBlob",value:function(e,t,i){return this._display.toBlob(e,t,i)}},{key:"_connect",value:function(){if(Log.Debug(">> RFB.connect"),this._url)Log.Info("connecting to ".concat(this._url)),this._sock.open(this._url,this._wsProtocols);else{if(Log.Info("attaching ".concat(this._rawChannel," to Websock")),this._sock.attach(this._rawChannel),"closed"===this._sock.readyState)throw Error("Cannot use already closed WebSocket/RTCDataChannel");"open"===this._sock.readyState&&this._socketOpen()}this._target.appendChild(this._screen),this._gestures.attach(this._canvas),this._cursor.attach(this._canvas),this._refreshCursor(),this._resizeObserver.observe(this._screen),this._canvas.addEventListener("mousedown",this._eventHandlers.focusCanvas),this._canvas.addEventListener("touchstart",this._eventHandlers.focusCanvas),this._canvas.addEventListener("mousedown",this._eventHandlers.handleMouse),this._canvas.addEventListener("mouseup",this._eventHandlers.handleMouse),this._canvas.addEventListener("mousemove",this._eventHandlers.handleMouse),this._canvas.addEventListener("click",this._eventHandlers.handleMouse),this._canvas.addEventListener("contextmenu",this._eventHandlers.handleMouse),this._canvas.addEventListener("wheel",this._eventHandlers.handleWheel),this._canvas.addEventListener("gesturestart",this._eventHandlers.handleGesture),this._canvas.addEventListener("gesturemove",this._eventHandlers.handleGesture),this._canvas.addEventListener("gestureend",this._eventHandlers.handleGesture),Log.Debug("<< RFB.connect")}},{key:"_disconnect",value:function(){Log.Debug(">> RFB.disconnect"),this._cursor.detach(),this._canvas.removeEventListener("gesturestart",this._eventHandlers.handleGesture),this._canvas.removeEventListener("gesturemove",this._eventHandlers.handleGesture),this._canvas.removeEventListener("gestureend",this._eventHandlers.handleGesture),this._canvas.removeEventListener("wheel",this._eventHandlers.handleWheel),this._canvas.removeEventListener("mousedown",this._eventHandlers.handleMouse),this._canvas.removeEventListener("mouseup",this._eventHandlers.handleMouse),this._canvas.removeEventListener("mousemove",this._eventHandlers.handleMouse),this._canvas.removeEventListener("click",this._eventHandlers.handleMouse),this._canvas.removeEventListener("contextmenu",this._eventHandlers.handleMouse),this._canvas.removeEventListener("mousedown",this._eventHandlers.focusCanvas),this._canvas.removeEventListener("touchstart",this._eventHandlers.focusCanvas),this._resizeObserver.disconnect(),this._keyboard.ungrab(),this._gestures.detach(),this._sock.close();try{this._target.removeChild(this._screen)}catch(e){if("NotFoundError"!==e.name)throw e}clearTimeout(this._resizeTimeout),clearTimeout(this._mouseMoveTimer),Log.Debug("<< RFB.disconnect")}},{key:"_socketOpen",value:function(){"connecting"===this._rfbConnectionState&&""===this._rfbInitState?(this._rfbInitState="ProtocolVersion",Log.Debug("Starting VNC handshake")):this._fail("Unexpected server connection while "+this._rfbConnectionState)}},{key:"_socketClose",value:function(e){Log.Debug("WebSocket on-close event");var t="";switch(e.code&&(t="(code: "+e.code,e.reason&&(t+=", reason: "+e.reason),t+=")"),this._rfbConnectionState){case"connecting":this._fail("Connection closed "+t);break;case"connected":this._updateConnectionState("disconnecting"),this._updateConnectionState("disconnected");break;case"disconnecting":this._updateConnectionState("disconnected");break;case"disconnected":this._fail("Unexpected server disconnect when already disconnected "+t);break;default:this._fail("Unexpected server disconnect before connecting "+t)}this._sock.off("close"),this._rawChannel=null}},{key:"_socketError",value:function(e){Log.Warn("WebSocket on-error event")}},{key:"_focusCanvas",value:function(e){this.focusOnClick&&this.focus({preventScroll:!0})}},{key:"_setDesktopName",value:function(e){this._fbName=e,this.dispatchEvent(new CustomEvent("desktopname",{detail:{name:this._fbName}}))}},{key:"_saveExpectedClientSize",value:function(){this._expectedClientWidth=this._screen.clientWidth,this._expectedClientHeight=this._screen.clientHeight}},{key:"_currentClientSize",value:function(){return[this._screen.clientWidth,this._screen.clientHeight]}},{key:"_clientHasExpectedSize",value:function(){var e=_slicedToArray(this._currentClientSize(),2),t=e[0],i=e[1];return t==this._expectedClientWidth&&i==this._expectedClientHeight}},{key:"_handleResize",value:function(){var e=this;this._clientHasExpectedSize()||(window.requestAnimationFrame((function(){e._updateClip(),e._updateScale()})),this._resizeSession&&(clearTimeout(this._resizeTimeout),this._resizeTimeout=setTimeout(this._requestRemoteResize.bind(this),500)))}},{key:"_updateClip",value:function(){var e=this._display.clipViewport,t=this._clipViewport;if(this._scaleViewport&&(t=!1),e!==t&&(this._display.clipViewport=t),t){var i=this._screenSize();this._display.viewportChangeSize(i.w,i.h),this._fixScrollbars(),this._setClippingViewport(i.w<this._display.width||i.h<this._display.height)}else this._setClippingViewport(!1);e!==t&&this._saveExpectedClientSize()}},{key:"_updateScale",value:function(){if(this._scaleViewport){var e=this._screenSize();this._display.autoscale(e.w,e.h)}else this._display.scale=1;this._fixScrollbars()}},{key:"_requestRemoteResize",value:function(){if(clearTimeout(this._resizeTimeout),this._resizeTimeout=null,this._resizeSession&&!this._viewOnly&&this._supportsSetDesktopSize){var e=this._screenSize();s.messages.setDesktopSize(this._sock,Math.floor(e.w),Math.floor(e.h),this._screenID,this._screenFlags),Log.Debug("Requested new desktop size: "+e.w+"x"+e.h)}}},{key:"_screenSize",value:function(){var e=this._screen.getBoundingClientRect();return{w:e.width,h:e.height}}},{key:"_fixScrollbars",value:function(){var e=this._screen.style.overflow;this._screen.style.overflow="hidden",this._screen.getBoundingClientRect(),this._screen.style.overflow=e}},{key:"_updateConnectionState",value:function(e){var t=this,i=this._rfbConnectionState;if(e!==i)if("disconnected"!==i){switch(e){case"connected":if("connecting"!==i)return void Log.Error("Bad transition to connected state, previous connection state: "+i);break;case"disconnected":if("disconnecting"!==i)return void Log.Error("Bad transition to disconnected state, previous connection state: "+i);break;case"connecting":if(""!==i)return void Log.Error("Bad transition to connecting state, previous connection state: "+i);break;case"disconnecting":if("connected"!==i&&"connecting"!==i)return void Log.Error("Bad transition to disconnecting state, previous connection state: "+i);break;default:return void Log.Error("Unknown connection state: "+e)}switch(this._rfbConnectionState=e,Log.Debug("New state '"+e+"', was '"+i+"'."),this._disconnTimer&&"disconnecting"!==e&&(Log.Debug("Clearing disconnect timer"),clearTimeout(this._disconnTimer),this._disconnTimer=null,this._sock.off("close")),e){case"connecting":this._connect();break;case"connected":this.dispatchEvent(new CustomEvent("connect",{detail:{}}));break;case"disconnecting":this._disconnect(),this._disconnTimer=setTimeout((function(){Log.Error("Disconnection timed out."),t._updateConnectionState("disconnected")}),1e3*DISCONNECT_TIMEOUT);break;case"disconnected":this.dispatchEvent(new CustomEvent("disconnect",{detail:{clean:this._rfbCleanDisconnect}}))}}else Log.Error("Tried changing state of a disconnected RFB object");else Log.Debug("Already in state '"+e+"', ignoring")}},{key:"_fail",value:function(e){switch(this._rfbConnectionState){case"disconnecting":Log.Error("Failed when disconnecting: "+e);break;case"connected":Log.Error("Failed while connected: "+e);break;case"connecting":Log.Error("Failed when connecting: "+e);break;default:Log.Error("RFB failure: "+e)}return this._rfbCleanDisconnect=!1,this._updateConnectionState("disconnecting"),this._updateConnectionState("disconnected"),!1}},{key:"_setCapability",value:function(e,t){this._capabilities[e]=t,this.dispatchEvent(new CustomEvent("capabilities",{detail:{capabilities:this._capabilities}}))}},{key:"_handleMessage",value:function(){if(0!==this._sock.rQlen)switch(this._rfbConnectionState){case"disconnected":Log.Error("Got data while disconnected");break;case"connected":for(;!this._flushing&&this._normalMsg()&&0!==this._sock.rQlen;);break;case"connecting":for(;"connecting"===this._rfbConnectionState&&this._initMsg(););break;default:Log.Error("Got data while in an invalid state")}else Log.Warn("handleMessage called on an empty receive queue")}},{key:"_handleKeyEvent",value:function(e,t,i){this.sendKey(e,t,i)}},{key:"_handleMouse",value:function(e){if(("click"!==e.type||e.target===this._canvas)&&(e.stopPropagation(),e.preventDefault(),"click"!==e.type&&"contextmenu"!==e.type)){var t=(0,_element.clientToElement)(e.clientX,e.clientY,this._canvas);switch(e.type){case"mousedown":(0,_events.setCapture)(this._canvas),this._handleMouseButton(t.x,t.y,!0,1<<e.button);break;case"mouseup":this._handleMouseButton(t.x,t.y,!1,1<<e.button);break;case"mousemove":this._handleMouseMove(t.x,t.y)}}}},{key:"_handleMouseButton",value:function(e,t,i,n){if(this.dragViewport){if(i&&!this._viewportDragging)return this._viewportDragging=!0,this._viewportDragPos={x:e,y:t},void(this._viewportHasMoved=!1);if(this._viewportDragging=!1,this._viewportHasMoved)return;this._sendMouse(e,t,n)}null!==this._mouseMoveTimer&&(clearTimeout(this._mouseMoveTimer),this._mouseMoveTimer=null,this._sendMouse(e,t,this._mouseButtonMask)),i?this._mouseButtonMask|=n:this._mouseButtonMask&=~n,this._sendMouse(e,t,this._mouseButtonMask)}},{key:"_handleMouseMove",value:function(e,t){var i=this;if(this._viewportDragging){var n=this._viewportDragPos.x-e,s=this._viewportDragPos.y-t;(this._viewportHasMoved||Math.abs(n)>_browser.dragThreshold||Math.abs(s)>_browser.dragThreshold)&&(this._viewportHasMoved=!0,this._viewportDragPos={x:e,y:t},this._display.viewportChangePos(n,s))}else if(this._mousePos={x:e,y:t},null==this._mouseMoveTimer){var r=Date.now()-this._mouseLastMoveTime;r>MOUSE_MOVE_DELAY?(this._sendMouse(e,t,this._mouseButtonMask),this._mouseLastMoveTime=Date.now()):this._mouseMoveTimer=setTimeout((function(){i._handleDelayedMouseMove()}),MOUSE_MOVE_DELAY-r)}}},{key:"_handleDelayedMouseMove",value:function(){this._mouseMoveTimer=null,this._sendMouse(this._mousePos.x,this._mousePos.y,this._mouseButtonMask),this._mouseLastMoveTime=Date.now()}},{key:"_sendMouse",value:function(e,t,i){"connected"===this._rfbConnectionState&&(this._viewOnly||s.messages.pointerEvent(this._sock,this._display.absX(e),this._display.absY(t),i))}},{key:"_handleWheel",value:function(e){if("connected"===this._rfbConnectionState&&!this._viewOnly){e.stopPropagation(),e.preventDefault();var t=(0,_element.clientToElement)(e.clientX,e.clientY,this._canvas),i=e.deltaX,n=e.deltaY;0!==e.deltaMode&&(i*=WHEEL_LINE_HEIGHT,n*=WHEEL_LINE_HEIGHT),this._accumulatedWheelDeltaX+=i,this._accumulatedWheelDeltaY+=n,Math.abs(this._accumulatedWheelDeltaX)>=WHEEL_STEP&&(this._accumulatedWheelDeltaX<0?(this._handleMouseButton(t.x,t.y,!0,32),this._handleMouseButton(t.x,t.y,!1,32)):this._accumulatedWheelDeltaX>0&&(this._handleMouseButton(t.x,t.y,!0,64),this._handleMouseButton(t.x,t.y,!1,64)),this._accumulatedWheelDeltaX=0),Math.abs(this._accumulatedWheelDeltaY)>=WHEEL_STEP&&(this._accumulatedWheelDeltaY<0?(this._handleMouseButton(t.x,t.y,!0,8),this._handleMouseButton(t.x,t.y,!1,8)):this._accumulatedWheelDeltaY>0&&(this._handleMouseButton(t.x,t.y,!0,16),this._handleMouseButton(t.x,t.y,!1,16)),this._accumulatedWheelDeltaY=0)}}},{key:"_fakeMouseMove",value:function(e,t,i){this._handleMouseMove(t,i),this._cursor.move(e.detail.clientX,e.detail.clientY)}},{key:"_handleTapEvent",value:function(e,t){var i=(0,_element.clientToElement)(e.detail.clientX,e.detail.clientY,this._canvas);if(null!==this._gestureLastTapTime&&Date.now()-this._gestureLastTapTime<DOUBLE_TAP_TIMEOUT&&this._gestureFirstDoubleTapEv.detail.type===e.detail.type){var n=this._gestureFirstDoubleTapEv.detail.clientX-e.detail.clientX,s=this._gestureFirstDoubleTapEv.detail.clientY-e.detail.clientY;Math.hypot(n,s)<DOUBLE_TAP_THRESHOLD?i=(0,_element.clientToElement)(this._gestureFirstDoubleTapEv.detail.clientX,this._gestureFirstDoubleTapEv.detail.clientY,this._canvas):this._gestureFirstDoubleTapEv=e}else this._gestureFirstDoubleTapEv=e;this._gestureLastTapTime=Date.now(),this._fakeMouseMove(this._gestureFirstDoubleTapEv,i.x,i.y),this._handleMouseButton(i.x,i.y,!0,t),this._handleMouseButton(i.x,i.y,!1,t)}},{key:"_handleGesture",value:function(e){var t,i=(0,_element.clientToElement)(e.detail.clientX,e.detail.clientY,this._canvas);switch(e.type){case"gesturestart":switch(e.detail.type){case"onetap":this._handleTapEvent(e,1);break;case"twotap":this._handleTapEvent(e,4);break;case"threetap":this._handleTapEvent(e,2);break;case"drag":this._fakeMouseMove(e,i.x,i.y),this._handleMouseButton(i.x,i.y,!0,1);break;case"longpress":this._fakeMouseMove(e,i.x,i.y),this._handleMouseButton(i.x,i.y,!0,4);break;case"twodrag":this._gestureLastMagnitudeX=e.detail.magnitudeX,this._gestureLastMagnitudeY=e.detail.magnitudeY,this._fakeMouseMove(e,i.x,i.y);break;case"pinch":this._gestureLastMagnitudeX=Math.hypot(e.detail.magnitudeX,e.detail.magnitudeY),this._fakeMouseMove(e,i.x,i.y)}break;case"gesturemove":switch(e.detail.type){case"onetap":case"twotap":case"threetap":break;case"drag":case"longpress":this._fakeMouseMove(e,i.x,i.y);break;case"twodrag":for(this._fakeMouseMove(e,i.x,i.y);e.detail.magnitudeY-this._gestureLastMagnitudeY>GESTURE_SCRLSENS;)this._handleMouseButton(i.x,i.y,!0,8),this._handleMouseButton(i.x,i.y,!1,8),this._gestureLastMagnitudeY+=GESTURE_SCRLSENS;for(;e.detail.magnitudeY-this._gestureLastMagnitudeY<-GESTURE_SCRLSENS;)this._handleMouseButton(i.x,i.y,!0,16),this._handleMouseButton(i.x,i.y,!1,16),this._gestureLastMagnitudeY-=GESTURE_SCRLSENS;for(;e.detail.magnitudeX-this._gestureLastMagnitudeX>GESTURE_SCRLSENS;)this._handleMouseButton(i.x,i.y,!0,32),this._handleMouseButton(i.x,i.y,!1,32),this._gestureLastMagnitudeX+=GESTURE_SCRLSENS;for(;e.detail.magnitudeX-this._gestureLastMagnitudeX<-GESTURE_SCRLSENS;)this._handleMouseButton(i.x,i.y,!0,64),this._handleMouseButton(i.x,i.y,!1,64),this._gestureLastMagnitudeX-=GESTURE_SCRLSENS;break;case"pinch":if(this._fakeMouseMove(e,i.x,i.y),t=Math.hypot(e.detail.magnitudeX,e.detail.magnitudeY),Math.abs(t-this._gestureLastMagnitudeX)>GESTURE_ZOOMSENS){for(this._handleKeyEvent(_keysym.default.XK_Control_L,"ControlLeft",!0);t-this._gestureLastMagnitudeX>GESTURE_ZOOMSENS;)this._handleMouseButton(i.x,i.y,!0,8),this._handleMouseButton(i.x,i.y,!1,8),this._gestureLastMagnitudeX+=GESTURE_ZOOMSENS;for(;t-this._gestureLastMagnitudeX<-GESTURE_ZOOMSENS;)this._handleMouseButton(i.x,i.y,!0,16),this._handleMouseButton(i.x,i.y,!1,16),this._gestureLastMagnitudeX-=GESTURE_ZOOMSENS}this._handleKeyEvent(_keysym.default.XK_Control_L,"ControlLeft",!1)}break;case"gestureend":switch(e.detail.type){case"onetap":case"twotap":case"threetap":case"pinch":case"twodrag":break;case"drag":this._fakeMouseMove(e,i.x,i.y),this._handleMouseButton(i.x,i.y,!1,1);break;case"longpress":this._fakeMouseMove(e,i.x,i.y),this._handleMouseButton(i.x,i.y,!1,4)}}}},{key:"_negotiateProtocolVersion",value:function(){if(this._sock.rQwait("version",12))return!1;var e=this._sock.rQshiftStr(12).substr(4,7);Log.Info("Server ProtocolVersion: "+e);var t=0;switch(e){case"000.000":t=1;break;case"003.003":case"003.006":this._rfbVersion=3.3;break;case"003.007":this._rfbVersion=3.7;break;case"003.008":case"003.889":case"004.000":case"004.001":case"005.000":this._rfbVersion=3.8;break;default:return this._fail("Invalid server version "+e)}if(t){for(var i="ID:"+this._repeaterID;i.length<250;)i+="\0";return this._sock.sendString(i),!0}this._rfbVersion>this._rfbMaxVersion&&(this._rfbVersion=this._rfbMaxVersion);var n="00"+parseInt(this._rfbVersion,10)+".00"+10*this._rfbVersion%10;this._sock.sendString("RFB "+n+"\n"),Log.Debug("Sent ProtocolVersion: "+n),this._rfbInitState="Security"}},{key:"_isSupportedSecurityType",value:function(e){return[securityTypeNone,securityTypeVNCAuth,securityTypeRA2ne,securityTypeTight,securityTypeVeNCrypt,securityTypeXVP,securityTypeARD,securityTypeMSLogonII,securityTypePlain].includes(e)}},{key:"_negotiateSecurity",value:function(){if(this._rfbVersion>=3.7){var e=this._sock.rQshift8();if(this._sock.rQwait("security type",e,1))return!1;if(0===e)return this._rfbInitState="SecurityReason",this._securityContext="no security types",this._securityStatus=1,!0;var t=this._sock.rQshiftBytes(e);Log.Debug("Server security types: "+t),this._rfbAuthScheme=-1;var i,n=_createForOfIteratorHelper(t);try{for(n.s();!(i=n.n()).done;){var s=i.value;if(this._isSupportedSecurityType(s)){this._rfbAuthScheme=s;break}}}catch(e){n.e(e)}finally{n.f()}if(-1===this._rfbAuthScheme)return this._fail("Unsupported security types (types: "+t+")");this._sock.send([this._rfbAuthScheme])}else{if(this._sock.rQwait("security scheme",4))return!1;if(this._rfbAuthScheme=this._sock.rQshift32(),0==this._rfbAuthScheme)return this._rfbInitState="SecurityReason",this._securityContext="authentication scheme",this._securityStatus=1,!0}return this._rfbInitState="Authentication",Log.Debug("Authenticating using scheme: "+this._rfbAuthScheme),!0}},{key:"_handleSecurityReason",value:function(){if(this._sock.rQwait("reason length",4))return!1;var e=this._sock.rQshift32(),t="";if(e>0){if(this._sock.rQwait("reason",e,4))return!1;t=this._sock.rQshiftStr(e)}return""!==t?(this.dispatchEvent(new CustomEvent("securityfailure",{detail:{status:this._securityStatus,reason:t}})),this._fail("Security negotiation failed on "+this._securityContext+" (reason: "+t+")")):(this.dispatchEvent(new CustomEvent("securityfailure",{detail:{status:this._securityStatus}})),this._fail("Security negotiation failed on "+this._securityContext))}},{key:"_negotiateXvpAuth",value:function(){if(void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password||void 0===this._rfbCredentials.target)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password","target"]}})),!1;var e=String.fromCharCode(this._rfbCredentials.username.length)+String.fromCharCode(this._rfbCredentials.target.length)+this._rfbCredentials.username+this._rfbCredentials.target;return this._sock.sendString(e),this._rfbAuthScheme=securityTypeVNCAuth,this._negotiateAuthentication()}},{key:"_negotiateVeNCryptAuth",value:function(){if(0==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt version",2))return!1;var e=this._sock.rQshift8(),t=this._sock.rQshift8();if(0!=e||2!=t)return this._fail("Unsupported VeNCrypt version "+e+"."+t);this._sock.send([0,2]),this._rfbVeNCryptState=1}if(1==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt ack",1))return!1;var i=this._sock.rQshift8();if(0!=i)return this._fail("VeNCrypt failure "+i);this._rfbVeNCryptState=2}if(2==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt subtypes length",1))return!1;var n=this._sock.rQshift8();if(n<1)return this._fail("VeNCrypt subtypes empty");this._rfbVeNCryptSubtypesLength=n,this._rfbVeNCryptState=3}if(3==this._rfbVeNCryptState){if(this._sock.rQwait("vencrypt subtypes",4*this._rfbVeNCryptSubtypesLength))return!1;for(var s=[],r=0;r<this._rfbVeNCryptSubtypesLength;r++)s.push(this._sock.rQshift32());this._rfbAuthScheme=-1;for(var o=0,a=s;o<a.length;o++){var c=a[o];if(c!==securityTypeVeNCrypt&&this._isSupportedSecurityType(c)){this._rfbAuthScheme=c;break}}return-1===this._rfbAuthScheme?this._fail("Unsupported security types (types: "+s+")"):(this._sock.send([this._rfbAuthScheme>>24,this._rfbAuthScheme>>16,this._rfbAuthScheme>>8,this._rfbAuthScheme]),this._rfbVeNCryptState,!0)}}},{key:"_negotiatePlainAuth",value:function(){if(void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1;var e=(0,_strings.encodeUTF8)(this._rfbCredentials.username),t=(0,_strings.encodeUTF8)(this._rfbCredentials.password);return this._sock.send([e.length>>24&255,e.length>>16&255,e.length>>8&255,255&e.length]),this._sock.send([t.length>>24&255,t.length>>16&255,t.length>>8&255,255&t.length]),this._sock.sendString(e),this._sock.sendString(t),this._rfbInitState="SecurityResult",!0}},{key:"_negotiateStdVNCAuth",value:function(){if(this._sock.rQwait("auth challenge",16))return!1;if(void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["password"]}})),!1;var e=Array.prototype.slice.call(this._sock.rQshiftBytes(16)),t=s.genDES(this._rfbCredentials.password,e);return this._sock.send(t),this._rfbInitState="SecurityResult",!0}},{key:"_negotiateARDAuth",value:function(){if(void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1;if(null!=this._rfbCredentials.ardPublicKey&&null!=this._rfbCredentials.ardCredentials)return this._sock.send(this._rfbCredentials.ardCredentials),this._sock.send(this._rfbCredentials.ardPublicKey),this._rfbCredentials.ardCredentials=null,this._rfbCredentials.ardPublicKey=null,this._rfbInitState="SecurityResult",!0;if(this._sock.rQwait("read ard",4))return!1;var e=this._sock.rQshiftBytes(2),t=this._sock.rQshift16();if(this._sock.rQwait("read ard keylength",2*t,4))return!1;var i=this._sock.rQshiftBytes(t),n=this._sock.rQshiftBytes(t),s=window.crypto.getRandomValues(new Uint8Array(t)),r=Array.from(window.crypto.getRandomValues(new Uint8Array(64)),(function(e){return String.fromCharCode(65+e%26)})).join("");return this._negotiateARDAuthAsync(e,t,i,n,s,r),!1}},{key:"_modPow",value:function(e,t,i){var n="0x"+Array.from(e,(function(e){return("0"+(255&e).toString(16)).slice(-2)})).join(""),s="0x"+Array.from(t,(function(e){return("0"+(255&e).toString(16)).slice(-2)})).join(""),r="0x"+Array.from(i,(function(e){return("0"+(255&e).toString(16)).slice(-2)})).join(""),o=BigInt(n),a=BigInt(s),c=BigInt(r),u=1n;for(o%=c;a>0;)a%2n===1n&&(u=u*o%c),a/=2n,o=o*o%c;for(var h=u.toString(16);h.length/2<t.length||h.length%2!=0;)h="0"+h;for(var l=[],d=0;d<h.length;d+=2)l.push(parseInt(h.substr(d,2),16));return l}},{key:"_aesEcbEncrypt",value:(i=_asyncToGenerator(_regeneratorRuntime().mark((function e(t,i){var n,s,r,o,a,c,u,h;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=Array.from(i,(function(e){return String.fromCharCode(e)})).join(""),e.next=3,window.crypto.subtle.importKey("raw",(0,_md.MD5)(n),{name:"AES-CBC"},!1,["encrypt"]);case 3:for(s=e.sent,r=new Uint8Array(t.length),o=0;o<t.length;++o)r[o]=t.charCodeAt(o);a=new Uint8Array(r.length),c=0;case 8:if(!(c<r.length)){e.next=17;break}return u=r.slice(c,c+16),e.next=12,window.crypto.subtle.encrypt({name:"AES-CBC",iv:u},s,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]));case 12:h=e.sent,a.set(new Uint8Array(h).slice(0,16),c);case 14:c+=16,e.next=8;break;case 17:return e.abrupt("return",a);case 18:case"end":return e.stop()}}),e)}))),function(e,t){return i.apply(this,arguments)})},{key:"_negotiateARDAuthAsync",value:(t=_asyncToGenerator(_regeneratorRuntime().mark((function e(t,i,n,s,r,o){var a,c,u,h,l,d,_,f;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=this._modPow(t,r,n),c=this._modPow(s,r,n),u=(0,_strings.encodeUTF8)(this._rfbCredentials.username).substring(0,63),h=(0,_strings.encodeUTF8)(this._rfbCredentials.password).substring(0,63),l=u+"\0"+o.substring(0,63),d=h+"\0"+o.substring(0,63),_=l.substring(0,64)+d.substring(0,64),e.next=9,this._aesEcbEncrypt(_,c);case 9:f=e.sent,this._rfbCredentials.ardCredentials=f,this._rfbCredentials.ardPublicKey=a,this._resumeAuthentication();case 13:case"end":return e.stop()}}),e,this)}))),function(e,i,n,s,r,o){return t.apply(this,arguments)})},{key:"_negotiateTightUnixAuth",value:function(){return void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password?(this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1):(this._sock.send([0,0,0,this._rfbCredentials.username.length]),this._sock.send([0,0,0,this._rfbCredentials.password.length]),this._sock.sendString(this._rfbCredentials.username),this._sock.sendString(this._rfbCredentials.password),this._rfbInitState="SecurityResult",!0)}},{key:"_negotiateTightTunnels",value:function(e){for(var t={vendor:"TGHT",signature:"NOTUNNEL"},i={},n=0;n<e;n++){var s=this._sock.rQshift32(),r=this._sock.rQshiftStr(4),o=this._sock.rQshiftStr(8);i[s]={vendor:r,signature:o}}return Log.Debug("Server Tight tunnel types: "+i),i[1]&&"SICR"===i[1].vendor&&"SCHANNEL"===i[1].signature&&(Log.Debug("Detected Siemens server. Assuming NOTUNNEL support."),i[0]={vendor:"TGHT",signature:"NOTUNNEL"}),i[0]?i[0].vendor!=t.vendor||i[0].signature!=t.signature?this._fail("Client's tunnel type had the incorrect vendor or signature"):(Log.Debug("Selected tunnel type: "+t),this._sock.send([0,0,0,0]),!1):this._fail("Server wanted tunnels, but doesn't support the notunnel type")}},{key:"_negotiateTightAuth",value:function(){if(!this._rfbTightVNC){if(this._sock.rQwait("num tunnels",4))return!1;var e=this._sock.rQshift32();if(e>0&&this._sock.rQwait("tunnel capabilities",16*e,4))return!1;if(this._rfbTightVNC=!0,e>0)return this._negotiateTightTunnels(e),!1}if(this._sock.rQwait("sub auth count",4))return!1;var t=this._sock.rQshift32();if(0===t)return this._rfbInitState="SecurityResult",!0;if(this._sock.rQwait("sub auth capabilities",16*t,4))return!1;for(var i={STDVNOAUTH__:1,STDVVNCAUTH_:2,TGHTULGNAUTH:129},n=[],s=0;s<t;s++){this._sock.rQshift32();var r=this._sock.rQshiftStr(12);n.push(r)}for(var o in Log.Debug("Server Tight authentication types: "+n),i)if(-1!=n.indexOf(o))switch(this._sock.send([0,0,0,i[o]]),Log.Debug("Selected authentication type: "+o),o){case"STDVNOAUTH__":return this._rfbInitState="SecurityResult",!0;case"STDVVNCAUTH_":return this._rfbAuthScheme=securityTypeVNCAuth,!0;case"TGHTULGNAUTH":return this._rfbAuthScheme=securityTypeUnixLogon,!0;default:return this._fail("Unsupported tiny auth scheme (scheme: "+o+")")}return this._fail("No supported sub-auth types!")}},{key:"_handleRSAAESCredentialsRequired",value:function(e){this.dispatchEvent(e)}},{key:"_handleRSAAESServerVerification",value:function(e){this.dispatchEvent(e)}},{key:"_negotiateRA2neAuth",value:function(){var e=this;return null===this._rfbRSAAESAuthenticationState&&(this._rfbRSAAESAuthenticationState=new _ra.default(this._sock,(function(){return e._rfbCredentials})),this._rfbRSAAESAuthenticationState.addEventListener("serververification",this._eventHandlers.handleRSAAESServerVerification),this._rfbRSAAESAuthenticationState.addEventListener("credentialsrequired",this._eventHandlers.handleRSAAESCredentialsRequired)),this._rfbRSAAESAuthenticationState.checkInternalEvents(),this._rfbRSAAESAuthenticationState.hasStarted||this._rfbRSAAESAuthenticationState.negotiateRA2neAuthAsync().catch((function(t){"disconnect normally"!==t.message&&e._fail(t.message)})).then((function(){return e.dispatchEvent(new CustomEvent("securityresult")),e._rfbInitState="SecurityResult",!0})).finally((function(){e._rfbRSAAESAuthenticationState.removeEventListener("serververification",e._eventHandlers.handleRSAAESServerVerification),e._rfbRSAAESAuthenticationState.removeEventListener("credentialsrequired",e._eventHandlers.handleRSAAESCredentialsRequired),e._rfbRSAAESAuthenticationState=null})),!1}},{key:"_negotiateMSLogonIIAuth",value:function(){if(this._sock.rQwait("mslogonii dh param",24))return!1;if(void 0===this._rfbCredentials.username||void 0===this._rfbCredentials.password)return this.dispatchEvent(new CustomEvent("credentialsrequired",{detail:{types:["username","password"]}})),!1;var e=this._sock.rQshiftBytes(8),t=this._sock.rQshiftBytes(8),i=this._sock.rQshiftBytes(8),n=window.crypto.getRandomValues(new Uint8Array(8)),s=new Uint8Array(this._modPow(e,n,t)),r=new Uint8Array(this._modPow(i,n,t)),o=new _des.default(r),a=(0,_strings.encodeUTF8)(this._rfbCredentials.username).substring(0,255),c=(0,_strings.encodeUTF8)(this._rfbCredentials.password).substring(0,63),u=new Uint8Array(256),h=new Uint8Array(64);window.crypto.getRandomValues(u),window.crypto.getRandomValues(h);for(var l=0;l<a.length;l++)u[l]=a.charCodeAt(l);u[a.length]=0;for(var d=0;d<c.length;d++)h[d]=c.charCodeAt(d);h[c.length]=0;for(var _=new Uint8Array(r),f=0;f<32;f++){for(var p=0;p<8;p++)_[p]^=u[8*f+p];_=o.enc8(_),u.set(_,8*f)}_=new Uint8Array(r);for(var g=0;g<8;g++){for(var v=0;v<8;v++)_[v]^=h[8*g+v];_=o.enc8(_),h.set(_,8*g)}return this._sock.send(s),this._sock.send(u),this._sock.send(h),this._rfbInitState="SecurityResult",!0}},{key:"_negotiateAuthentication",value:function(){switch(this._rfbAuthScheme){case securityTypeNone:return this._rfbInitState="SecurityResult",!0;case securityTypeXVP:return this._negotiateXvpAuth();case securityTypeARD:return this._negotiateARDAuth();case securityTypeVNCAuth:return this._negotiateStdVNCAuth();case securityTypeTight:return this._negotiateTightAuth();case securityTypeVeNCrypt:return this._negotiateVeNCryptAuth();case securityTypePlain:return this._negotiatePlainAuth();case securityTypeUnixLogon:return this._negotiateTightUnixAuth();case securityTypeRA2ne:return this._negotiateRA2neAuth();case securityTypeMSLogonII:return this._negotiateMSLogonIIAuth();default:return this._fail("Unsupported auth scheme (scheme: "+this._rfbAuthScheme+")")}}},{key:"_handleSecurityResult",value:function(){if(this._rfbVersion<3.7)return this._rfbInitState="ClientInitialisation",!0;if(this._sock.rQwait("VNC auth response ",4))return!1;var e=this._sock.rQshift32();return 0===e?(this._rfbInitState="ClientInitialisation",Log.Debug("Authentication OK"),!0):this._rfbVersion>=3.8?(this._rfbInitState="SecurityReason",this._securityContext="security result",this._securityStatus=e,!0):(this.dispatchEvent(new CustomEvent("securityfailure",{detail:{status:e}})),this._fail("Security handshake failed"))}},{key:"_negotiateServerInit",value:function(){if(this._sock.rQwait("server initialization",24))return!1;var e=this._sock.rQshift16(),t=this._sock.rQshift16(),i=this._sock.rQshift8(),n=this._sock.rQshift8(),r=this._sock.rQshift8(),o=this._sock.rQshift8(),a=this._sock.rQshift16(),c=this._sock.rQshift16(),u=this._sock.rQshift16(),h=this._sock.rQshift8(),l=this._sock.rQshift8(),d=this._sock.rQshift8();this._sock.rQskipBytes(3);var _=this._sock.rQshift32();if(this._sock.rQwait("server init name",_,24))return!1;var f=this._sock.rQshiftStr(_);if(f=(0,_strings.decodeUTF8)(f,!0),this._rfbTightVNC){if(this._sock.rQwait("TightVNC extended server init header",8,24+_))return!1;var p=this._sock.rQshift16(),g=this._sock.rQshift16(),v=this._sock.rQshift16();this._sock.rQskipBytes(2);var y=16*(p+g+v);if(this._sock.rQwait("TightVNC extended server init header",y,32+_))return!1;this._sock.rQskipBytes(16*p),this._sock.rQskipBytes(16*g),this._sock.rQskipBytes(16*v)}return Log.Info("Screen: "+e+"x"+t+", bpp: "+i+", depth: "+n+", bigEndian: "+r+", trueColor: "+o+", redMax: "+a+", greenMax: "+c+", blueMax: "+u+", redShift: "+h+", greenShift: "+l+", blueShift: "+d),this._setDesktopName(f),this._resize(e,t),this._viewOnly||this._keyboard.grab(),this._fbDepth=24,"Intel(r) AMT KVM"===this._fbName&&(Log.Warn("Intel AMT KVM only supports 8/16 bit depths. Using low color mode."),this._fbDepth=8),s.messages.pixelFormat(this._sock,this._fbDepth,!0),this._sendEncodings(),s.messages.fbUpdateRequest(this._sock,!1,0,0,this._fbWidth,this._fbHeight),this._updateConnectionState("connected"),!0}},{key:"_sendEncodings",value:function(){var e=[];e.push(_encodings.encodings.encodingCopyRect),24==this._fbDepth&&(e.push(_encodings.encodings.encodingTight),e.push(_encodings.encodings.encodingTightPNG),e.push(_encodings.encodings.encodingZRLE),e.push(_encodings.encodings.encodingJPEG),e.push(_encodings.encodings.encodingHextile),e.push(_encodings.encodings.encodingRRE)),e.push(_encodings.encodings.encodingRaw),e.push(_encodings.encodings.pseudoEncodingQualityLevel0+this._qualityLevel),e.push(_encodings.encodings.pseudoEncodingCompressLevel0+this._compressionLevel),e.push(_encodings.encodings.pseudoEncodingDesktopSize),e.push(_encodings.encodings.pseudoEncodingLastRect),e.push(_encodings.encodings.pseudoEncodingQEMUExtendedKeyEvent),e.push(_encodings.encodings.pseudoEncodingExtendedDesktopSize),e.push(_encodings.encodings.pseudoEncodingXvp),e.push(_encodings.encodings.pseudoEncodingFence),e.push(_encodings.encodings.pseudoEncodingContinuousUpdates),e.push(_encodings.encodings.pseudoEncodingDesktopName),e.push(_encodings.encodings.pseudoEncodingExtendedClipboard),24==this._fbDepth&&(e.push(_encodings.encodings.pseudoEncodingVMwareCursor),e.push(_encodings.encodings.pseudoEncodingCursor)),s.messages.clientEncodings(this._sock,e)}},{key:"_initMsg",value:function(){switch(this._rfbInitState){case"ProtocolVersion":return this._negotiateProtocolVersion();case"Security":return this._negotiateSecurity();case"Authentication":return this._negotiateAuthentication();case"SecurityResult":return this._handleSecurityResult();case"SecurityReason":return this._handleSecurityReason();case"ClientInitialisation":return this._sock.send([this._shared?1:0]),this._rfbInitState="ServerInitialisation",!0;case"ServerInitialisation":return this._negotiateServerInit();default:return this._fail("Unknown init state (state: "+this._rfbInitState+")")}}},{key:"_resumeAuthentication",value:function(){setTimeout(this._initMsg.bind(this),0)}},{key:"_handleSetColourMapMsg",value:function(){return Log.Debug("SetColorMapEntries"),this._fail("Unexpected SetColorMapEntries message")}},{key:"_handleServerCutText",value:function(){if(Log.Debug("ServerCutText"),this._sock.rQwait("ServerCutText header",7,1))return!1;this._sock.rQskipBytes(3);var e=this._sock.rQshift32();if(e=(0,_int.toSigned32bit)(e),this._sock.rQwait("ServerCutText content",Math.abs(e),8))return!1;if(e>=0){var t=this._sock.rQshiftStr(e);if(this._viewOnly)return!0;this.dispatchEvent(new CustomEvent("clipboard",{detail:{text:t}}))}else{e=Math.abs(e);var i=this._sock.rQshift32(),n=65535&i,r=4278190080&i;if(!!(r&extendedClipboardActionCaps)){this._clipboardServerCapabilitiesFormats={},this._clipboardServerCapabilitiesActions={};for(var o=0;o<=15;o++){var a=1<<o;n&a&&(this._clipboardServerCapabilitiesFormats[a]=!0,this._sock.rQshift32())}for(var c=24;c<=31;c++){var u=1<<c;this._clipboardServerCapabilitiesActions[u]=!!(r&u)}var h=[extendedClipboardActionCaps,extendedClipboardActionRequest,extendedClipboardActionPeek,extendedClipboardActionNotify,extendedClipboardActionProvide];s.messages.extendedClipboardCaps(this._sock,h,{extendedClipboardFormatText:0})}else if(r===extendedClipboardActionRequest){if(this._viewOnly)return!0;null!=this._clipboardText&&this._clipboardServerCapabilitiesActions[extendedClipboardActionProvide]&&n&extendedClipboardFormatText&&s.messages.extendedClipboardProvide(this._sock,[extendedClipboardFormatText],[this._clipboardText])}else if(r===extendedClipboardActionPeek){if(this._viewOnly)return!0;this._clipboardServerCapabilitiesActions[extendedClipboardActionNotify]&&(null!=this._clipboardText?s.messages.extendedClipboardNotify(this._sock,[extendedClipboardFormatText]):s.messages.extendedClipboardNotify(this._sock,[]))}else if(r===extendedClipboardActionNotify){if(this._viewOnly)return!0;this._clipboardServerCapabilitiesActions[extendedClipboardActionRequest]&&n&extendedClipboardFormatText&&s.messages.extendedClipboardRequest(this._sock,[extendedClipboardFormatText])}else{if(r!==extendedClipboardActionProvide)return this._fail("Unexpected action in extended clipboard message: "+r);if(this._viewOnly)return!0;if(!(n&extendedClipboardFormatText))return!0;this._clipboardText=null;var l=this._sock.rQshiftBytes(e-4),d=new _inflator.default,_=null;d.setInput(l);for(var f=0;f<=15;f++){var p=1<<f;if(n&p){var g=0,v=d.inflate(4);g|=v[0]<<24,g|=v[1]<<16,g|=v[2]<<8,g|=v[3];var y=d.inflate(g);p===extendedClipboardFormatText&&(_=y)}}if(d.setInput(null),null!==_){for(var b="",k=0;k<_.length;k++)b+=String.fromCharCode(_[k]);_=b,(_=(0,_strings.decodeUTF8)(_)).length>0&&"\0"===_.charAt(_.length-1)&&(_=_.slice(0,-1)),_=_.replace("\r\n","\n"),this.dispatchEvent(new CustomEvent("clipboard",{detail:{text:_}}))}}}return!0}},{key:"_handleServerFenceMsg",value:function(){if(this._sock.rQwait("ServerFence header",8,1))return!1;this._sock.rQskipBytes(3);var e=this._sock.rQshift32(),t=this._sock.rQshift8();if(this._sock.rQwait("ServerFence payload",t,9))return!1;t>64&&(Log.Warn("Bad payload length ("+t+") in fence response"),t=64);var i=this._sock.rQshiftStr(t);return this._supportsFence=!0,e&1<<31?(e&=3,s.messages.clientFence(this._sock,e,i),!0):this._fail("Unexpected fence response")}},{key:"_handleXvpMsg",value:function(){if(this._sock.rQwait("XVP version and message",3,1))return!1;this._sock.rQskipBytes(1);var e=this._sock.rQshift8(),t=this._sock.rQshift8();switch(t){case 0:Log.Error("XVP Operation Failed");break;case 1:this._rfbXvpVer=e,Log.Info("XVP extensions enabled (version "+this._rfbXvpVer+")"),this._setCapability("power",!0);break;default:this._fail("Illegal server XVP message (msg: "+t+")")}return!0}},{key:"_normalMsg",value:function(){var e,t,i;switch(e=this._FBU.rects>0?0:this._sock.rQshift8()){case 0:return(i=this._framebufferUpdate())&&!this._enabledContinuousUpdates&&s.messages.fbUpdateRequest(this._sock,!0,0,0,this._fbWidth,this._fbHeight),i;case 1:return this._handleSetColourMapMsg();case 2:return Log.Debug("Bell"),this.dispatchEvent(new CustomEvent("bell",{detail:{}})),!0;case 3:return this._handleServerCutText();case 150:return t=!this._supportsContinuousUpdates,this._supportsContinuousUpdates=!0,this._enabledContinuousUpdates=!1,t&&(this._enabledContinuousUpdates=!0,this._updateContinuousUpdates(),Log.Info("Enabling continuous updates.")),!0;case 248:return this._handleServerFenceMsg();case 250:return this._handleXvpMsg();default:return this._fail("Unexpected server message (type "+e+")"),Log.Debug("sock.rQslice(0, 30): "+this._sock.rQslice(0,30)),!0}}},{key:"_onFlush",value:function(){this._flushing=!1,this._sock.rQlen>0&&this._handleMessage()}},{key:"_framebufferUpdate",value:function(){if(0===this._FBU.rects){if(this._sock.rQwait("FBU header",3,1))return!1;if(this._sock.rQskipBytes(1),this._FBU.rects=this._sock.rQshift16(),this._display.pending())return this._flushing=!0,this._display.flush(),!1}for(;this._FBU.rects>0;){if(null===this._FBU.encoding){if(this._sock.rQwait("rect header",12))return!1;var e=this._sock.rQshiftBytes(12);this._FBU.x=(e[0]<<8)+e[1],this._FBU.y=(e[2]<<8)+e[3],this._FBU.width=(e[4]<<8)+e[5],this._FBU.height=(e[6]<<8)+e[7],this._FBU.encoding=parseInt((e[8]<<24)+(e[9]<<16)+(e[10]<<8)+e[11],10)}if(!this._handleRect())return!1;this._FBU.rects--,this._FBU.encoding=null}return this._display.flip(),!0}},{key:"_handleRect",value:function(){switch(this._FBU.encoding){case _encodings.encodings.pseudoEncodingLastRect:return this._FBU.rects=1,!0;case _encodings.encodings.pseudoEncodingVMwareCursor:return this._handleVMwareCursor();case _encodings.encodings.pseudoEncodingCursor:return this._handleCursor();case _encodings.encodings.pseudoEncodingQEMUExtendedKeyEvent:return this._qemuExtKeyEventSupported=!0,!0;case _encodings.encodings.pseudoEncodingDesktopName:return this._handleDesktopName();case _encodings.encodings.pseudoEncodingDesktopSize:return this._resize(this._FBU.width,this._FBU.height),!0;case _encodings.encodings.pseudoEncodingExtendedDesktopSize:return this._handleExtendedDesktopSize();default:return this._handleDataRect()}}},{key:"_handleVMwareCursor",value:function(){var e=this._FBU.x,t=this._FBU.y,i=this._FBU.width,n=this._FBU.height;if(this._sock.rQwait("VMware cursor encoding",1))return!1;var s,r=this._sock.rQshift8();this._sock.rQshift8();if(0==r){var o=-256;if(s=new Array(i*n*4),this._sock.rQwait("VMware cursor classic encoding",i*n*4*2,2))return!1;for(var a=new Array(i*n),c=0;c<i*n;c++)a[c]=this._sock.rQshift32();for(var u=new Array(i*n),h=0;h<i*n;h++)u[h]=this._sock.rQshift32();for(var l=0;l<i*n;l++)if(0==a[l]){var d=u[l],_=d>>8&255,f=d>>16&255,p=d>>24&255;s[4*l]=_,s[4*l+1]=f,s[4*l+2]=p,s[4*l+3]=255}else(a[l]&o)==o?0==u[l]?(s[4*l]=0,s[4*l+1]=0,s[4*l+2]=0,s[4*l+3]=0):(u[l],s[4*l]=0,s[4*l+1]=0,s[4*l+2]=0,s[4*l+3]=255):(s[4*l]=0,s[4*l+1]=0,s[4*l+2]=0,s[4*l+3]=255)}else{if(1!=r)return Log.Warn("The given cursor type is not supported: "+r+" given."),!1;if(this._sock.rQwait("VMware cursor alpha encoding",i*n*4,2))return!1;s=new Array(i*n*4);for(var g=0;g<i*n;g++){var v=this._sock.rQshift32();s[4*g]=v>>24&255,s[4*g+1]=v>>16&255,s[4*g+2]=v>>8&255,s[4*g+3]=255&v}}return this._updateCursor(s,e,t,i,n),!0}},{key:"_handleCursor",value:function(){var e=this._FBU.x,t=this._FBU.y,i=this._FBU.width,n=this._FBU.height,s=i*n*4,r=Math.ceil(i/8)*n,o=s+r;if(this._sock.rQwait("cursor encoding",o))return!1;for(var a=this._sock.rQshiftBytes(s),c=this._sock.rQshiftBytes(r),u=new Uint8Array(i*n*4),h=0,l=0;l<n;l++)for(var d=0;d<i;d++){var _=c[l*Math.ceil(i/8)+Math.floor(d/8)]<<d%8&128?255:0;u[h]=a[h+2],u[h+1]=a[h+1],u[h+2]=a[h],u[h+3]=_,h+=4}return this._updateCursor(u,e,t,i,n),!0}},{key:"_handleDesktopName",value:function(){if(this._sock.rQwait("DesktopName",4))return!1;var e=this._sock.rQshift32();if(this._sock.rQwait("DesktopName",e,4))return!1;var t=this._sock.rQshiftStr(e);return t=(0,_strings.decodeUTF8)(t,!0),this._setDesktopName(t),!0}},{key:"_handleExtendedDesktopSize",value:function(){if(this._sock.rQwait("ExtendedDesktopSize",4))return!1;var e=this._sock.rQpeek8(),t=4+16*e;if(this._sock.rQwait("ExtendedDesktopSize",t))return!1;var i=!this._supportsSetDesktopSize;this._supportsSetDesktopSize=!0,i&&this._requestRemoteResize(),this._sock.rQskipBytes(1),this._sock.rQskipBytes(3);for(var n=0;n<e;n+=1)0===n?(this._screenID=this._sock.rQshiftBytes(4),this._sock.rQskipBytes(2),this._sock.rQskipBytes(2),this._sock.rQskipBytes(2),this._sock.rQskipBytes(2),this._screenFlags=this._sock.rQshiftBytes(4)):this._sock.rQskipBytes(16);if(1===this._FBU.x&&0!==this._FBU.y){var s="";switch(this._FBU.y){case 1:s="Resize is administratively prohibited";break;case 2:s="Out of resources";break;case 3:s="Invalid screen layout";break;default:s="Unknown reason"}Log.Warn("Server did not accept the resize request: "+s)}else this._resize(this._FBU.width,this._FBU.height);return!0}},{key:"_handleDataRect",value:function(){var e=this._decoders[this._FBU.encoding];if(!e)return this._fail("Unsupported encoding (encoding: "+this._FBU.encoding+")"),!1;try{return e.decodeRect(this._FBU.x,this._FBU.y,this._FBU.width,this._FBU.height,this._sock,this._display,this._fbDepth)}catch(e){return this._fail("Error decoding rect: "+e),!1}}},{key:"_updateContinuousUpdates",value:function(){this._enabledContinuousUpdates&&s.messages.enableContinuousUpdates(this._sock,!0,0,0,this._fbWidth,this._fbHeight)}},{key:"_resize",value:function(e,t){this._fbWidth=e,this._fbHeight=t,this._display.resize(this._fbWidth,this._fbHeight),this._updateClip(),this._updateScale(),this._updateContinuousUpdates(),this._saveExpectedClientSize()}},{key:"_xvpOp",value:function(e,t){this._rfbXvpVer<e||(Log.Info("Sending XVP operation "+t+" (version "+e+")"),s.messages.xvpOp(this._sock,e,t))}},{key:"_updateCursor",value:function(e,t,i,n,s){this._cursorImage={rgbaPixels:e,hotx:t,hoty:i,w:n,h:s},this._refreshCursor()}},{key:"_shouldShowDotCursor",value:function(){if(!this._showDotCursor)return!1;for(var e=3;e<this._cursorImage.rgbaPixels.length;e+=4)if(this._cursorImage.rgbaPixels[e])return!1;return!0}},{key:"_refreshCursor",value:function(){if("connecting"===this._rfbConnectionState||"connected"===this._rfbConnectionState){var e=this._shouldShowDotCursor()?s.cursors.dot:this._cursorImage;this._cursor.change(e.rgbaPixels,e.hotx,e.hoty,e.w,e.h)}}}],[{key:"genDES",value:function(e,t){var i=e.split("").map((function(e){return e.charCodeAt(0)}));return new _des.default(i).encrypt(t)}}]),s}();exports.default=RFB,RFB.messages={keyEvent:function(e,t,i){var n=e._sQ,s=e._sQlen;n[s]=4,n[s+1]=i,n[s+2]=0,n[s+3]=0,n[s+4]=t>>24,n[s+5]=t>>16,n[s+6]=t>>8,n[s+7]=t,e._sQlen+=8,e.flush()},QEMUExtendedKeyEvent:function(e,t,i,n){var s=e._sQ,r=e._sQlen;s[r]=255,s[r+1]=0,s[r+2]=i>>8,s[r+3]=i,s[r+4]=t>>24,s[r+5]=t>>16,s[r+6]=t>>8,s[r+7]=t;var o,a,c=(o=n,a=255&n,224==n>>8&&a<127?128|a:o);s[r+8]=c>>24,s[r+9]=c>>16,s[r+10]=c>>8,s[r+11]=c,e._sQlen+=12,e.flush()},pointerEvent:function(e,t,i,n){var s=e._sQ,r=e._sQlen;s[r]=5,s[r+1]=n,s[r+2]=t>>8,s[r+3]=t,s[r+4]=i>>8,s[r+5]=i,e._sQlen+=6,e.flush()},_buildExtendedClipboardFlags:function(e,t){for(var i=new Uint8Array(4),n=0,s=0,r=0;r<e.length;r++)s|=e[r];for(var o=0;o<t.length;o++)n|=t[o];return i[0]=s>>24,i[1]=0,i[2]=0,i[3]=n,i},extendedClipboardProvide:function(e,t,i){for(var n=new _deflator.default,s=[],r=0;r<t.length;r++){if(t[r]!=extendedClipboardFormatText)throw new Error("Unsupported extended clipboard format for Provide message.");i[r]=i[r].replace(/\r\n|\r|\n/gm,"\r\n");var o=(0,_strings.encodeUTF8)(i[r]+"\0");s.push(o.length>>24&255,o.length>>16&255,o.length>>8&255,255&o.length);for(var a=0;a<o.length;a++)s.push(o.charCodeAt(a))}var c=n.deflate(new Uint8Array(s)),u=new Uint8Array(4+c.length);u.set(RFB.messages._buildExtendedClipboardFlags([extendedClipboardActionProvide],t)),u.set(c,4),RFB.messages.clientCutText(e,u,!0)},extendedClipboardNotify:function(e,t){var i=RFB.messages._buildExtendedClipboardFlags([extendedClipboardActionNotify],t);RFB.messages.clientCutText(e,i,!0)},extendedClipboardRequest:function(e,t){var i=RFB.messages._buildExtendedClipboardFlags([extendedClipboardActionRequest],t);RFB.messages.clientCutText(e,i,!0)},extendedClipboardCaps:function(e,t,i){var n=Object.keys(i),s=new Uint8Array(4+4*n.length);n.map((function(e){return parseInt(e)})),n.sort((function(e,t){return e-t})),s.set(RFB.messages._buildExtendedClipboardFlags(t,[]));for(var r=4,o=0;o<n.length;o++)s[r]=i[n[o]]>>24,s[r+1]=i[n[o]]>>16,s[r+2]=i[n[o]]>>8,s[r+3]=i[n[o]]>>0,r+=4,s[3]|=1<<n[o];RFB.messages.clientCutText(e,s,!0)},clientCutText:function(e,t){var i,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=e._sQ,r=e._sQlen;s[r]=6,s[r+1]=0,s[r+2]=0,s[r+3]=0,i=n?(0,_int.toUnsigned32bit)(-t.length):t.length,s[r+4]=i>>24,s[r+5]=i>>16,s[r+6]=i>>8,s[r+7]=i,e._sQlen+=8;for(var o=0,a=t.length;a>0;){for(var c=Math.min(a,e._sQbufferSize-e._sQlen),u=0;u<c;u++)s[e._sQlen+u]=t[o+u];e._sQlen+=c,e.flush(),a-=c,o+=c}},setDesktopSize:function(e,t,i,n,s){var r=e._sQ,o=e._sQlen;r[o]=251,r[o+1]=0,r[o+2]=t>>8,r[o+3]=t,r[o+4]=i>>8,r[o+5]=i,r[o+6]=1,r[o+7]=0,r[o+8]=n>>24,r[o+9]=n>>16,r[o+10]=n>>8,r[o+11]=n,r[o+12]=0,r[o+13]=0,r[o+14]=0,r[o+15]=0,r[o+16]=t>>8,r[o+17]=t,r[o+18]=i>>8,r[o+19]=i,r[o+20]=s>>24,r[o+21]=s>>16,r[o+22]=s>>8,r[o+23]=s,e._sQlen+=24,e.flush()},clientFence:function(e,t,i){var n=e._sQ,s=e._sQlen;n[s]=248,n[s+1]=0,n[s+2]=0,n[s+3]=0,n[s+4]=t>>24,n[s+5]=t>>16,n[s+6]=t>>8,n[s+7]=t;var r=i.length;n[s+8]=r;for(var o=0;o<r;o++)n[s+9+o]=i.charCodeAt(o);e._sQlen+=9+r,e.flush()},enableContinuousUpdates:function(e,t,i,n,s,r){var o=e._sQ,a=e._sQlen;o[a]=150,o[a+1]=t,o[a+2]=i>>8,o[a+3]=i,o[a+4]=n>>8,o[a+5]=n,o[a+6]=s>>8,o[a+7]=s,o[a+8]=r>>8,o[a+9]=r,e._sQlen+=10,e.flush()},pixelFormat:function(e,t,i){var n,s=e._sQ,r=e._sQlen;n=t>16?32:t>8?16:8;var o=Math.floor(t/3);s[r]=0,s[r+1]=0,s[r+2]=0,s[r+3]=0,s[r+4]=n,s[r+5]=t,s[r+6]=0,s[r+7]=i?1:0,s[r+8]=0,s[r+9]=(1<<o)-1,s[r+10]=0,s[r+11]=(1<<o)-1,s[r+12]=0,s[r+13]=(1<<o)-1,s[r+14]=0*o,s[r+15]=1*o,s[r+16]=2*o,s[r+17]=0,s[r+18]=0,s[r+19]=0,e._sQlen+=20,e.flush()},clientEncodings:function(e,t){var i=e._sQ,n=e._sQlen;i[n]=2,i[n+1]=0,i[n+2]=t.length>>8,i[n+3]=t.length;for(var s=n+4,r=0;r<t.length;r++){var o=t[r];i[s]=o>>24,i[s+1]=o>>16,i[s+2]=o>>8,i[s+3]=o,s+=4}e._sQlen+=s-n,e.flush()},fbUpdateRequest:function(e,t,i,n,s,r){var o=e._sQ,a=e._sQlen;void 0===i&&(i=0),void 0===n&&(n=0),o[a]=3,o[a+1]=t?1:0,o[a+2]=i>>8&255,o[a+3]=255&i,o[a+4]=n>>8&255,o[a+5]=255&n,o[a+6]=s>>8&255,o[a+7]=255&s,o[a+8]=r>>8&255,o[a+9]=255&r,e._sQlen+=10,e.flush()},xvpOp:function(e,t,i){var n=e._sQ,s=e._sQlen;n[s]=250,n[s+1]=0,n[s+2]=t,n[s+3]=i,e._sQlen+=4,e.flush()}},RFB.cursors={none:{rgbaPixels:new Uint8Array,w:0,h:0,hotx:0,hoty:0},dot:{rgbaPixels:new Uint8Array([255,255,255,255,0,0,0,255,255,255,255,255,0,0,0,255,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,255,255,255,255,255]),w:3,h:3,hotx:1,hoty:1}};
//# sourceMappingURL=/sm/2b9fc89d28872dff788c6d54017d217263ea6031f02d4c80413fbd96a2504bd0.map