import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger.js';
import { getUserById, verifyToken } from '../../services/mongo/userService.js';
import { UserTokenData, AccountType, TeamRole } from '../../models/user.js';

// JWT Secret from environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'default_jwt_secret_for_development';

// Test node secret key from environment variable
const NODE_SECRET_KEY = process.env.NODE_SECRET_KEY || 'test-node-secret-key-for-secure-communication';

// Bypass authentication in development mode if needed
const BYPASS_AUTH = process.env.BYPASS_AUTH === 'true';

// Extended Request type with user information
export interface AuthRequest extends Request {
  user?: UserTokenData;
}

/**
 * Authentication middleware - Verifies JWT token in Authorization header
 */
export async function authenticate(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    // Skip authentication if BYPASS_AUTH is enabled (development only)
    if (BYPASS_AUTH) {
      req.user = {
        id: 'system',
        email: '<EMAIL>',
        accountType: 'admin',
        role: 'admin'
      };
      return next();
    }

    // Check for test node authentication
    const nodeToken = req.headers['x-node-key'];
    if (nodeToken === NODE_SECRET_KEY) {
      logger.debug('Node authentication successful via x-node-key');
      req.user = {
        id: 'node-system',
        email: 'node@system',
        accountType: 'admin',
        role: 'admin'
      };
      return next();
    }

    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        error: 'No authorization token provided'
      });
    }

    // Extract token (Bearer token format)
    const [scheme, token] = authHeader.split(' ');
    if (scheme !== 'Bearer' || !token) {
      return res.status(401).json({
        success: false,
        error: 'Invalid authorization format'
      });
    }

    // Use token verification service
    const tokenResult = verifyToken(token);
    if (!tokenResult.success || !tokenResult.data) {
      return res.status(401).json({
        success: false,
        error: tokenResult.message || 'Invalid or expired token'
      });
    }

    // Add user to request
    logger.debug(`[AUTH] Token verified for user ${tokenResult.data.id}, data:`, tokenResult.data);

    // Admin kullanıcıları için companyId ve teamId kontrolü yapmaya gerek yok
    if (tokenResult.data.accountType === 'admin') {
      logger.debug(`[AUTH] Admin user detected, skipping companyId and teamId check`);
    }
    // Normal kullanıcılar için companyId ve teamId kontrolü yap
    else if (!tokenResult.data.companyId || !tokenResult.data.teamId) {
      logger.warn(`[AUTH] User missing companyId or teamId in token, fetching from database`);

      // Kullanıcı bilgilerini veritabanından al
      const userResult = await getUserById(tokenResult.data.id);
      if (userResult.success && userResult.data) {


        // Token verisini kullanıcı veritabanı bilgileriyle zenginleştir
        if (!tokenResult.data.companyId && userResult.data.companyId) {
          tokenResult.data.companyId = userResult.data.companyId;
          logger.info(`[AUTH] Added companyId from database: ${tokenResult.data.companyId}`);
        }

        if (!tokenResult.data.teamId && userResult.data.teamId) {
          tokenResult.data.teamId = userResult.data.teamId;
          logger.info(`[AUTH] Added teamId from database: ${tokenResult.data.teamId}`);
        }
      }
    } else {
      logger.debug(`[AUTH] User has companyId: ${tokenResult.data.companyId} and teamId: ${tokenResult.data.teamId}`);
    }

    req.user = tokenResult.data as UserTokenData;
    next();
  } catch (error: any) {
    logger.error('[AUTH] Authentication error:', error);
    return res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
}

/**
 * Role-based authorization middleware
 */
export function authorize(roles: string | string[]) {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    // Skip authorization if BYPASS_AUTH is enabled
    if (BYPASS_AUTH) {
      return next();
    }

    // Check if user exists in request
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Convert single role to array
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    // Check if user accountType is in allowed roles
    if (!allowedRoles.includes(req.user.accountType)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
    }

    next();
  };
}

/**
 * Team role-based authorization middleware
 */
export function authorizeTeamRole(teamRoles: TeamRole | TeamRole[]) {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    // Skip authorization if BYPASS_AUTH is enabled
    if (BYPASS_AUTH) {
      return next();
    }

    // Check if user exists in request
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Convert single role to array
    const allowedTeamRoles = Array.isArray(teamRoles) ? teamRoles : [teamRoles];

    // Check if user has a team role
    if (!req.user.teamRole) {
      return res.status(403).json({
        success: false,
        error: 'No team role assigned'
      });
    }

    // Check if user's team role is in allowed roles
    // String olarak karşılaştır
    const userTeamRole = req.user.teamRole as string;
    const allowedRolesStr = allowedTeamRoles.map(role => role.toString());

    if (!allowedRolesStr.includes(userTeamRole)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient team permissions'
      });
    }

    next();
  };
}