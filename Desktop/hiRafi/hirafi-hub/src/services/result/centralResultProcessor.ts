/**
 * Central Result Processor
 *
 * This service acts as a single entry point for all test results,
 * regardless of their source (WebSocket, Redis, etc.).
 *
 * It ensures that:
 * 1. Each test result is processed only once
 * 2. Node status and test status are properly synchronized
 * 3. Duplicate results are properly handled
 * 4. Transaction-based processing for atomic updates
 * 5. Enhanced duplicate detection with node-specific keys
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { nodeManager } from '../../core/node-manager/index.js';
import { TestStatus } from '../../models/test-types.js';

// Import types from dedicated types folder
import { ProcessedMetrics } from '../../types/metrics.js';
import { TestReportDTO, TestReportStepDTO, validateTestReportDTO } from '../../types/testReportDTO.js';

/**
 * Map test node status to test hub status
 * @param nodeStatus Status string from test node
 * @returns Mapped TestStatus enum value
 */
function mapNodeStatusToHubStatus(nodeStatus: string): TestStatus {
  switch (nodeStatus) {
    case 'success':
    case 'completed':
    case 'passed':
      return TestStatus.COMPLETED;
    case 'failed':
      return TestStatus.FAILED;
    case 'stopped':
      return TestStatus.STOPPED;
    case 'running':
      return TestStatus.RUNNING;
    case 'queued':
      return TestStatus.QUEUED;
    case 'error':
      return TestStatus.ERROR;
    case 'timeout':
      return TestStatus.TIMEOUT;
    default:
      logger.warn(`CentralResultProcessor: Unknown node status '${nodeStatus}', mapping to UNKNOWN`);
      return TestStatus.UNKNOWN;
  }
}

interface ProcessedResult {
  timestamp: number;
  source: string;
  status: string;
  nodeId?: string;
  transactionId?: string;
}

class CentralResultProcessor extends EventEmitter {
  private static instance: CentralResultProcessor;

  // Track processed test results to prevent duplicates with more context
  private processedResults: Map<string, ProcessedResult> = new Map();
  private readonly RESULT_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes - increased to match lock TTL

  // Track processing locks to prevent concurrent processing of the same test
  private processingLocks: Map<string, {
    locked: boolean,
    timestamp: number,
    transactionId?: string
  }> = new Map();

  // Lock timeout to prevent stuck locks
  private readonly LOCK_TIMEOUT = 120 * 1000; // 120 seconds - increased for database operations

  private constructor() {
    super();

    // Clean up expired results periodically
    setInterval(() => this.cleanupExpiredResults(), 5 * 60 * 1000); // Every 5 minutes

    // Clean up stuck locks periodically
    setInterval(() => this.cleanupStuckLocks(), 1 * 60 * 1000); // Every 1 minute
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): CentralResultProcessor {
    if (!CentralResultProcessor.instance) {
      CentralResultProcessor.instance = new CentralResultProcessor();
    }
    return CentralResultProcessor.instance;
  }

  /**
   * Process metrics from the test result payload
   * Extracts, validates, and transforms metrics data for consistent storage
   * @param payload The test result payload containing metrics
   * @returns Processed metrics object or undefined if no metrics found
   */
  private processMetrics(payload: any): ProcessedMetrics | undefined {
    if (!payload) {
      return undefined;
    }

    // Check for metrics in various possible locations
    // First check if metrics exist and are not empty objects
    let metricsSource = null;

    if (payload.metrics && typeof payload.metrics === 'object' && Object.keys(payload.metrics).length > 0) {
      metricsSource = payload.metrics;
    } else if (payload.enhancedMetrics && typeof payload.enhancedMetrics === 'object' && Object.keys(payload.enhancedMetrics).length > 0) {
      metricsSource = payload.enhancedMetrics;
    } else if (payload.performanceMetrics && typeof payload.performanceMetrics === 'object' && Object.keys(payload.performanceMetrics).length > 0) {
      metricsSource = payload.performanceMetrics;
    }

    if (!metricsSource) {
      logger.info('CentralResultProcessor: No meaningful metrics found in payload (empty or missing)');
      return undefined;
    }

    logger.info(`CentralResultProcessor: Processing metrics from payload: ${JSON.stringify(metricsSource, null, 2)}`);

    const processed: ProcessedMetrics = {
      timestamp: new Date().toISOString()
    };

    // Process Web Vitals
    if (metricsSource.webVitals || metricsSource.performanceMetrics?.webVitals) {
      const webVitalsSource = metricsSource.webVitals || metricsSource.performanceMetrics?.webVitals;
      processed.webVitals = {
        lcp: this.validateAndConvertMetric(webVitalsSource.lcp, 'LCP'),
        cls: this.validateAndConvertMetric(webVitalsSource.cls, 'CLS'),
        fid: this.validateAndConvertMetric(webVitalsSource.fid, 'FID'),
        ttfb: this.validateAndConvertMetric(webVitalsSource.ttfb, 'TTFB'),
        inp: this.validateAndConvertMetric(webVitalsSource.inp, 'INP'),
        fcp: this.validateAndConvertMetric(webVitalsSource.fcp, 'FCP')
      };

      // Convert LCP from milliseconds to seconds if needed
      if (processed.webVitals.lcp && processed.webVitals.lcp > 100) {
        processed.webVitals.lcp = parseFloat((processed.webVitals.lcp / 1000).toFixed(2));
      }

      // Convert TTFB from milliseconds to seconds if needed
      if (processed.webVitals.ttfb && processed.webVitals.ttfb > 100) {
        processed.webVitals.ttfb = parseFloat((processed.webVitals.ttfb / 1000).toFixed(2));
      }

      logger.debug(`CentralResultProcessor: Processed Web Vitals - LCP: ${processed.webVitals.lcp}s, CLS: ${processed.webVitals.cls}, FID: ${processed.webVitals.fid}ms`);
    }

    // Process Page Metrics
    if (metricsSource.pageMetrics) {
      const pageMetricsSource = metricsSource.pageMetrics;
      processed.pageMetrics = {
        documents: this.validateAndConvertMetric(pageMetricsSource.documents || pageMetricsSource.Documents, 'Documents'),
        nodes: this.validateAndConvertMetric(pageMetricsSource.nodes || pageMetricsSource.Nodes, 'Nodes'),
        jsHeapUsedSize: this.validateAndConvertMetric(pageMetricsSource.jsHeapUsedSize || pageMetricsSource.JSHeapUsedSize, 'JSHeapUsedSize'),
        jsHeapTotalSize: this.validateAndConvertMetric(pageMetricsSource.jsHeapTotalSize || pageMetricsSource.JSHeapTotalSize, 'JSHeapTotalSize'),
        scriptDuration: this.validateAndConvertMetric(pageMetricsSource.scriptDuration || pageMetricsSource.ScriptDuration, 'ScriptDuration'),
        layoutCount: this.validateAndConvertMetric(pageMetricsSource.layoutCount || pageMetricsSource.LayoutCount, 'LayoutCount'),
        recalcStyleCount: this.validateAndConvertMetric(pageMetricsSource.recalcStyleCount || pageMetricsSource.RecalcStyleCount, 'RecalcStyleCount')
      };

      logger.debug(`CentralResultProcessor: Processed Page Metrics - Documents: ${processed.pageMetrics.documents}, Nodes: ${processed.pageMetrics.nodes}, JS Heap: ${processed.pageMetrics.jsHeapUsedSize} bytes`);
    }

    // Process Network Data
    if (metricsSource.networkData) {
      const networkSource = metricsSource.networkData;

      // Check if we have the new networkEntries format
      if (Array.isArray(networkSource.networkEntries) && networkSource.networkEntries.length > 0) {
        // Process networkEntries array to extract statistics
        const networkEntries = networkSource.networkEntries;
        const stats = this.processNetworkEntries(networkEntries);

        processed.networkData = {
          requests: {
            total: stats.totalRequests,
            successful: stats.successfulRequests,
            failed: stats.failedRequests,
            byType: stats.requestsByType
          },
          transferred: {
            total: stats.totalTransferred,
            unit: 'bytes'
          },
          logs: networkEntries // Use networkEntries as logs for backward compatibility
        };

        logger.debug(`CentralResultProcessor: Processed Network Data from networkEntries - Total: ${stats.totalRequests}, Successful: ${stats.successfulRequests}, Failed: ${stats.failedRequests}`);
      } else {
        // Fallback to old format processing
        processed.networkData = {
          requests: {
            total: this.validateAndConvertMetric(networkSource.requests?.total, 'Network Requests Total'),
            successful: this.validateAndConvertMetric(networkSource.requests?.successful, 'Network Requests Successful'),
            failed: this.validateAndConvertMetric(networkSource.requests?.failed, 'Network Requests Failed'),
            byType: networkSource.requests?.byType || {}
          },
          transferred: {
            total: this.validateAndConvertMetric(networkSource.transferred?.total, 'Network Transferred Total'),
            unit: networkSource.transferred?.unit || 'bytes'
          },
          logs: Array.isArray(networkSource.logs) ? networkSource.logs : []
        };

        logger.debug(`CentralResultProcessor: Processed Network Data from legacy format - Total Requests: ${processed.networkData?.requests?.total ?? 'N/A'}, Transferred: ${processed.networkData?.transferred?.total ?? 'N/A'} ${processed.networkData?.transferred?.unit ?? 'bytes'}`);
      }
    }

    // Process Accessibility Data
    if (metricsSource.accessibilityData) {
      const accessibilitySource = metricsSource.accessibilityData;
      processed.accessibilityData = {
        violations: {
          count: this.validateAndConvertMetric(accessibilitySource.violations?.count, 'Accessibility Violations Count'),
          items: Array.isArray(accessibilitySource.violations?.items) ? accessibilitySource.violations.items : [],
          byType: accessibilitySource.violations?.byType || {},
          bySeverity: accessibilitySource.violations?.bySeverity || {}
        },
        passes: Array.isArray(accessibilitySource.passes) ? accessibilitySource.passes : [],
        summary: {
          totalElements: this.validateAndConvertMetric(accessibilitySource.summary?.totalElements, 'Accessibility Total Elements'),
          accessibilityScore: this.validateAndConvertMetric(accessibilitySource.summary?.accessibilityScore, 'Accessibility Score'),
          ...accessibilitySource.summary
        }
      };

      logger.debug(`CentralResultProcessor: Processed Accessibility Data - Violations: ${processed.accessibilityData?.violations?.count ?? 'N/A'}, Score: ${processed.accessibilityData?.summary?.accessibilityScore ?? 'N/A'}`);
    }

    // Process Performance Metrics (additional)
    if (metricsSource.performanceMetrics && typeof metricsSource.performanceMetrics === 'object') {
      processed.performanceMetrics = { ...metricsSource.performanceMetrics };
    }

    // Process Tracing Data
    if (metricsSource.tracingData && typeof metricsSource.tracingData === 'object') {
      processed.tracingData = { ...metricsSource.tracingData };
    }

    // Process Collection Performance
    if (metricsSource.collectionPerformance || metricsSource.performanceMetrics?.collectionPerformance) {
      const perfSource = metricsSource.collectionPerformance || metricsSource.performanceMetrics?.collectionPerformance;
      processed.collectionPerformance = {
        totalTime: this.validateAndConvertMetric(perfSource.totalTime, 'Collection Total Time'),
        pageMetricsCollection: this.validateAndConvertMetric(perfSource.pageMetricsCollection, 'Page Metrics Collection Time'),
        networkDataCollection: this.validateAndConvertMetric(perfSource.networkDataCollection, 'Network Data Collection Time'),
        accessibilityDataCollection: this.validateAndConvertMetric(perfSource.accessibilityDataCollection, 'Accessibility Data Collection Time')
      };
    }

    // Return processed metrics only if we have meaningful data
    const hasData = processed.webVitals || processed.pageMetrics || processed.networkData ||
                   processed.accessibilityData || processed.performanceMetrics || processed.tracingData;

    if (hasData) {
      logger.info('CentralResultProcessor: Successfully processed metrics data');
      return processed;
    }

    logger.debug('CentralResultProcessor: No meaningful metrics data found after processing');
    return undefined;
  }

  /**
   * Process networkEntries array to extract network statistics
   * @param networkEntries Array of network entry objects
   * @returns Processed network statistics
   */
  private processNetworkEntries(networkEntries: any[]): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    requestsByType: Record<string, number>;
    totalTransferred: number;
  } {
    const stats = {
      totalRequests: networkEntries.length,
      successfulRequests: 0,
      failedRequests: 0,
      requestsByType: {} as Record<string, number>,
      totalTransferred: 0
    };

    networkEntries.forEach(entry => {
      // Count successful vs failed requests
      if (entry.status >= 200 && entry.status < 400) {
        stats.successfulRequests++;
      } else {
        stats.failedRequests++;
      }

      // Count requests by type
      const type = entry.type || 'unknown';
      stats.requestsByType[type] = (stats.requestsByType[type] || 0) + 1;

      // Calculate total transferred data
      if (entry.size && typeof entry.size === 'string') {
        const sizeMatch = entry.size.match(/(\d+(?:\.\d+)?)\s*([KMGT]?B)/i);
        if (sizeMatch) {
          const value = parseFloat(sizeMatch[1]);
          const unit = sizeMatch[2].toUpperCase();

          let bytes = value;
          switch (unit) {
            case 'KB': bytes *= 1024; break;
            case 'MB': bytes *= 1024 * 1024; break;
            case 'GB': bytes *= 1024 * 1024 * 1024; break;
            case 'TB': bytes *= 1024 * 1024 * 1024 * 1024; break;
            // 'B' or default - no conversion needed
          }

          stats.totalTransferred += Math.round(bytes);
        }
      }
    });

    return stats;
  }

  /**
   * Validate and convert a metric value to appropriate type
   * @param value The raw metric value
   * @param metricName Name of the metric for logging
   * @returns Validated and converted metric value or undefined
   */
  private validateAndConvertMetric(value: any, metricName: string): number | undefined {
    if (value === null || value === undefined) {
      return undefined;
    }

    // Convert to number if it's a string
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    // Validate it's a valid number
    if (typeof numValue !== 'number' || isNaN(numValue) || !isFinite(numValue)) {
      logger.debug(`CentralResultProcessor: Invalid ${metricName} value: ${value}, setting to undefined`);
      return undefined;
    }

    // Ensure positive values for most metrics (except CLS which can be 0)
    if (numValue < 0 && metricName !== 'CLS') {
      logger.debug(`CentralResultProcessor: Negative ${metricName} value: ${numValue}, setting to undefined`);
      return undefined;
    }

    return numValue;
  }

  /**
   * Sanitize environment settings to remove sensitive data before storing in test reports
   * @param environmentSettings Raw environment settings that may contain sensitive data
   * @returns Sanitized environment settings safe for storage
   */
  private sanitizeEnvironmentSettings(environmentSettings: any): any {
    if (!environmentSettings || typeof environmentSettings !== 'object') {
      return environmentSettings;
    }

    // Deep clone to avoid modifying the original object
    const sanitized = JSON.parse(JSON.stringify(environmentSettings));

    // Remove sensitive SauceLabs credentials
    if (sanitized.sauceLabs) {
      delete sanitized.sauceLabs.accessKey;
      delete sanitized.sauceLabs.username;
    }

    // Remove sensitive Testinium credentials
    if (sanitized.testinium) {
      delete sanitized.testinium.clientId;
      delete sanitized.testinium.clientSecret;
    }

    // Remove sensitive AI model configuration
    if (sanitized.aiModelConfig) {
      delete sanitized.aiModelConfig.OPENAI_API_KEY;
      delete sanitized.aiModelConfig.OPENAI_BASE_URL;
      // Keep only non-sensitive fields like model name
      if (sanitized.aiModelConfig.MIDSCENE_MODEL_NAME) {
        sanitized.aiModelConfig = {
          MIDSCENE_MODEL_NAME: sanitized.aiModelConfig.MIDSCENE_MODEL_NAME
        };
      } else {
        delete sanitized.aiModelConfig;
      }
    }

    // Remove sensitive proxy credentials
    if (sanitized.proxy) {
      delete sanitized.proxy.username;
      delete sanitized.proxy.password;
    }

    // Remove redundant platform field from root level if it exists in environment settings
    if (sanitized.platform && environmentSettings.platform) {
      // Keep the platform in environmentSettings, remove from root
      // This will be handled in the DTO creation
    }

    logger.debug('CentralResultProcessor: Sanitized environment settings by removing sensitive credentials');
    
    return sanitized;
  }

  /**
   * Remove redundant fields from test report data to reduce storage size
   * @param reportData The test report data that may contain redundant fields
   * @returns Clean report data with redundant fields removed
   */
  private removeRedundantFields(reportData: any): any {
    if (!reportData || typeof reportData !== 'object') {
      return reportData;
    }

    const cleaned = { ...reportData };

    // Remove redundant fields identified in the requirements
    // Keep timestamp, remove date if they are the same
    if (cleaned.timestamp && cleaned.date && cleaned.timestamp === cleaned.date) {
      delete cleaned.date;
      logger.debug('CentralResultProcessor: Removed redundant date field (same as timestamp)');
    }

    // Remove redundant executedUser if it's the same as userId
    if (cleaned.executedUser && cleaned.userId && cleaned.executedUser === cleaned.userId) {
      delete cleaned.executedUser;
      logger.debug('CentralResultProcessor: Removed redundant executedUser field (same as userId)');
    }

    // Remove redundant scenario fields if they have the same value
    if (cleaned.scenarioName && cleaned.scenarioTitle && cleaned.name) {
      if (cleaned.scenarioName === cleaned.name && cleaned.scenarioTitle === cleaned.name) {
        // Keep only name, remove the others
        delete cleaned.scenarioName;
        delete cleaned.scenarioTitle;
        logger.debug('CentralResultProcessor: Removed redundant scenarioName and scenarioTitle fields (same as name)');
      } else if (cleaned.scenarioName === cleaned.scenarioTitle) {
        // Keep scenarioName, remove scenarioTitle
        delete cleaned.scenarioTitle;
        logger.debug('CentralResultProcessor: Removed redundant scenarioTitle field (same as scenarioName)');
      }
    }

    return cleaned;
  }

  /**
   * Transform raw test result data into a clean TestReportDTO
   * This is the single transformation point that creates the authoritative report structure
   * @param rawData Raw test result data from various sources
   * @param contextData Additional context data (runId, executionId, etc.)
   * @returns Clean TestReportDTO ready for database storage
   */
  private transformToTestReportDTO(rawData: any, contextData: {
    testId: string;
    nodeId?: string;
    runId?: string;
    executionId?: string;
    scenarioId?: string;
    executedUser?: string;
    executedUserName?: string;
    teamId?: string | null;
    companyId?: string | null;
    status: string;
    duration?: number;
  }): TestReportDTO {
    const now = new Date().toISOString();

    // Process metrics explicitly
    const processedMetrics = this.processMetrics(rawData);

    // Transform steps to clean DTO format
    const steps: TestReportStepDTO[] = (rawData.steps || []).map((step: any) => ({
      id: step.id || `step-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      name: step.name || 'Unnamed Step',
      type: step.type || 'unknown',
      status: step.success !== undefined ? (step.success ? 'passed' : 'failed') : (step.status || 'unknown'),
      success: step.success !== undefined ? step.success : (!step.error && step.status !== 'failed' && step.status !== 'error'),
      duration: step.duration || 0,
      error: step.error || null,
      timestamp: step.timestamp ?
        (typeof step.timestamp === 'number' ? new Date(step.timestamp).toISOString() : step.timestamp) :
        now,
      beforeScreenshotUrl: step.beforeScreenshotUrl || null,
      afterScreenshotUrl: step.afterScreenshotUrl || null,
      logs: step.logs || [],
      description: step.description,
      data: step.data,
      startTime: step.startTime,
      endTime: step.endTime,
      
      // Transform control flow data for enhanced report visualization
      controlFlowData: this.transformControlFlowData(step.controlFlowData)
    }));

    // Calculate summary from steps
    const summary = {
      total: steps.length,
      passed: steps.filter(s => s.status === 'passed').length,
      failed: steps.filter(s => s.status === 'failed').length,
      errors: steps.filter(s => s.status === 'error').length,
      // Override with provided summary if available and valid
      ...(rawData.summary && typeof rawData.summary === 'object' ? rawData.summary : {})
    };

    // Define a consistent name for the test report
    const primaryName = rawData.scenarioTitle || rawData.scenarioName || rawData.name || `Test ${contextData.testId}`;

    // Sanitize environment settings to remove sensitive data
    const sanitizedEnvironmentSettings = this.sanitizeEnvironmentSettings(rawData.environmentSettings);

    // Create the clean DTO
    const dto: TestReportDTO = {
      // Core identification
      id: contextData.testId,
      status: contextData.status,

      // Test metadata
      name: primaryName,
      scenarioId: contextData.scenarioId || '',
      scenarioName: primaryName,
      scenarioTitle: primaryName,
      url: rawData.url,

      // Execution context
      runId: contextData.runId,
      executionId: contextData.executionId,
      nodeId: contextData.nodeId,

      // User and organization context
      executedUser: contextData.executedUser,
      executedUserName: contextData.executedUserName,
      userId: contextData.executedUser, // For backward compatibility
      teamId: contextData.teamId,
      companyId: contextData.companyId,

      // Timing information - ensure timestamps are always ISO strings
      timestamp: rawData.timestamp ?
        (typeof rawData.timestamp === 'number' ? new Date(rawData.timestamp).toISOString() : rawData.timestamp) :
        now,
      date: rawData.date ?
        (typeof rawData.date === 'number' ? new Date(rawData.date).toISOString() : rawData.date) :
        now,
      startTime: rawData.startTime,
      endTime: rawData.endTime,
      duration: contextData.duration !== undefined ? 
        (contextData.duration < 0 ? Math.abs(contextData.duration) : contextData.duration) : 
        undefined,

      // Test execution results
      success: rawData.success,
      error: rawData.error || null,
      logs: rawData.logs || [],
      steps,
      summary,

      // Media and artifacts
      videoUrl: rawData.videoUrl,

      // Platform information
      platform: rawData.platform,

      // Environment settings - SANITIZED to remove sensitive data
      environmentSettings: sanitizedEnvironmentSettings,

      // Testinium session information for Android tests using Testinium provider
      testiniumSessionId: rawData.testiniumSessionId,

      // Enhanced metrics - the clean, processed metrics structure
      enhancedMetrics: processedMetrics
    };

    // Clean up undefined fields and remove redundant data
    Object.keys(dto).forEach(key => {
      if ((dto as any)[key] === undefined) {
        delete (dto as any)[key];
      }
    });

    // Remove redundant fields to optimize storage
    const finalDto = this.removeRedundantFields(dto);

    // Log sanitization action for security audit
    if (rawData.environmentSettings) {
      logger.info(`CentralResultProcessor: Sanitized environment settings for test ${contextData.testId} - removed sensitive credentials from storage`);
    }

    return finalDto;
  }

  /**
   * Transform control flow data from raw step data to TestReportStepDTO format
   * @param controlFlowData Raw control flow data from step execution
   * @returns Transformed control flow data or undefined
   */
  private transformControlFlowData(controlFlowData: any): any {
    if (!controlFlowData || typeof controlFlowData !== 'object') {
      return undefined;
    }

    const transformed: any = {};

    // Handle IF-ELSE specific data
    if (controlFlowData.conditionResult !== undefined) {
      transformed.conditionResult = controlFlowData.conditionResult;
    }
    if (controlFlowData.executedBranch) {
      transformed.executedBranch = controlFlowData.executedBranch;
    }
    if (controlFlowData.trueSteps && Array.isArray(controlFlowData.trueSteps)) {
      transformed.trueSteps = controlFlowData.trueSteps.map((step: any) => this.transformNestedStep(step));
    }
    if (controlFlowData.falseSteps && Array.isArray(controlFlowData.falseSteps)) {
      transformed.falseSteps = controlFlowData.falseSteps.map((step: any) => this.transformNestedStep(step));
    }

    // Handle FOR/WHILE LOOP specific data
    if (controlFlowData.iterationCount !== undefined) {
      transformed.iterationCount = controlFlowData.iterationCount;
    }
    if (controlFlowData.completedIterations !== undefined) {
      transformed.completedIterations = controlFlowData.completedIterations;
    }
    if (controlFlowData.totalStepsExecuted !== undefined) {
      transformed.totalStepsExecuted = controlFlowData.totalStepsExecuted;
    }
    if (controlFlowData.hitMaxIterations !== undefined) {
      transformed.hitMaxIterations = controlFlowData.hitMaxIterations;
    }
    if (controlFlowData.failedIteration !== undefined) {
      transformed.failedIteration = controlFlowData.failedIteration;
    }
    if (controlFlowData.failedStepIndex !== undefined) {
      transformed.failedStepIndex = controlFlowData.failedStepIndex;
    }

    // Return undefined if no relevant control flow data found
    return Object.keys(transformed).length > 0 ? transformed : undefined;
  }

  /**
   * Transform a nested step to TestReportStepDTO format
   * @param step Raw nested step data
   * @returns Transformed nested step
   */
  private transformNestedStep(step: any): any {
    const now = new Date().toISOString();
    
    return {
      id: step.id || `nested-step-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      name: step.name || 'Unnamed Nested Step',
      type: step.type || 'unknown',
      status: step.success !== undefined ? (step.success ? 'passed' : 'failed') : (step.status || 'unknown'),
      success: step.success !== undefined ? step.success : (!step.error && step.status !== 'failed' && step.status !== 'error'),
      duration: step.duration || 0,
      error: step.error || null,
      timestamp: step.timestamp ?
        (typeof step.timestamp === 'number' ? new Date(step.timestamp).toISOString() : step.timestamp) :
        now,
      beforeScreenshotUrl: step.beforeScreenshotUrl || null,
      afterScreenshotUrl: step.afterScreenshotUrl || null,
      logs: step.logs || [],
      description: step.description,
      data: step.data,
      startTime: step.startTime,
      endTime: step.endTime,
      
      // Recursively transform nested control flow data if present
      controlFlowData: this.transformControlFlowData(step.controlFlowData)
    };
  }

  /**
   * Process a test result from any source
   * @param result Test result data
   * @param source Source of the result (WebSocket, Redis, etc.)
   * @param transactionId Optional transaction ID for tracing
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async processResult(result: any, source: string, transactionId?: string): Promise<boolean> {
    // Skip circular references and low-priority sources
    const skipSources = ['API-Status-Update', 'Internal-Update'];
    if (skipSources.includes(source)) {
      logger.debug(`CentralResultProcessor: Skipping ${source} source to prevent circular reference`);
      return true;
    }

    // Handle both direct result and jobData structure
    const outerJobData = result.jobData || result; // This refers to the structure that might wrap the actual test result details
    const testId = result.testId || outerJobData.testId || outerJobData.id;
    const nodeId = result.nodeId || outerJobData.nodeId;
    let status = result.status || outerJobData.status; // Status from the wrapper or node's perspective
    const resultTransactionId = transactionId || result.transactionId || outerJobData.transactionId || `auto-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    const runId = result.runId || outerJobData.runId;
    const executionId = result.executionId || outerJobData.executionId;
    const scenarioId = result.scenarioId || outerJobData.scenarioId;
    const executedUser = result.executedUser || outerJobData.executedUser || result.userId || outerJobData.userId;
    const executedUserName = result.executedUserName || outerJobData.executedUserName;
    const teamId = result.teamId || outerJobData.teamId;
    const companyId = result.companyId || outerJobData.companyId;
    const previousStatus = result.previousStatus || outerJobData.previousStatus;

    if (!testId) {
      logger.warn(`CentralResultProcessor: Received result without testId from ${source}`);
      return false;
    }
    
    // Determine the actual payload containing the test result details
    // The 'result' parameter is the item from the queue.
    // It might be flat, or nested like { jobData: { result: { ... } } } or { result: { ... } }
    let actualDetailedResultPayload = result; // Default assumption: 'result' is the flat payload
    if (result.jobData && typeof result.jobData === 'object' && result.jobData.result && typeof result.jobData.result === 'object') {
      actualDetailedResultPayload = result.jobData.result;
      logger.debug(`CentralResultProcessor: Using result.jobData.result as actual payload for test ${testId}`);
      // If the outer status was just a wrapper status, prefer the one from the detailed payload
      status = actualDetailedResultPayload.status || status;
    } else if (result.result && typeof result.result === 'object' && result.result !== result) { // Ensure result.result is not the object itself
      actualDetailedResultPayload = result.result;
      logger.debug(`CentralResultProcessor: Using result.result as actual payload for test ${testId}`);
      status = actualDetailedResultPayload.status || status;
    } else {
      logger.debug(`CentralResultProcessor: Using top-level result as actual payload for test ${testId}`);
    }

    // Define terminal states
    const terminalStatuses = [
      TestStatus.COMPLETED,
      TestStatus.FAILED,
      TestStatus.STOPPED,
      TestStatus.ERROR,
      TestStatus.TIMEOUT,
    ];

    // Check database for existing final status BEFORE in-memory checks and locks
    try {
      const { getReportById } = await import('../../services/mongo/reportService.js');
      const dbReportResult = await getReportById(testId);
      if (dbReportResult.success && dbReportResult.report) {
        const dbStatus = dbReportResult.report.status;
        // Ensure dbStatus is compared against string values of TestStatus enum
        if (terminalStatuses.map(s => s.toString()).includes(dbStatus)) {
          logger.info(`CentralResultProcessor: Test ${testId} already in terminal state '${dbStatus}' in DB. Skipping processing.`);
          return true; // Indicate successful handling (by skipping)
        }
      }
    } catch (dbCheckError) {
      logger.error(`CentralResultProcessor: Error checking test ${testId} status in DB: ${dbCheckError}. Proceeding with normal processing.`);
      // If DB check fails, proceed with existing logic, but log the error.
    }

    // Create a more specific key that includes the node ID and source to prevent cross-node duplicates
    const resultKey = nodeId ? `${testId}-${nodeId}-${source}` : `${testId}-${source}`;

    // Check if we're already processing this test
    const lockInfo = this.processingLocks.get(resultKey);
    if (lockInfo && lockInfo.locked) {
      const lockAge = Date.now() - lockInfo.timestamp;

      // If the lock is too old, force release it
      if (lockAge < this.LOCK_TIMEOUT) {
        logger.info(`CentralResultProcessor: Test ${testId} from node ${nodeId || 'unknown'} is already being processed (transaction: ${lockInfo.transactionId}), skipping duplicate from ${source} - this is normal behavior to prevent concurrent processing`);
        return false;
      }

      logger.warn(`CentralResultProcessor: Found stale lock for test ${testId} from node ${nodeId || 'unknown'} (age: ${lockAge}ms), releasing and proceeding`);
      this.processingLocks.delete(resultKey);
    }

    // Check if we've recently processed this result
    const now = Date.now();
    const lastProcessed = this.processedResults.get(resultKey);

    if (lastProcessed) {
      const timeSinceLastProcessed = now - lastProcessed.timestamp;

      if (timeSinceLastProcessed < this.RESULT_EXPIRY_TIME) {
        // Enhanced duplicate detection - check for any terminal status
        const terminalStatuses = ['completed', 'failed', 'stopped', 'error'];
        const isLastTerminal = terminalStatuses.includes(lastProcessed.status || '');

        // If we already processed a terminal status, reject any new results
        if (isLastTerminal) {
          logger.info(`CentralResultProcessor: Skipping result for test ${testId} from ${source} - test already has terminal status ${lastProcessed.status} from ${lastProcessed.source} (${timeSinceLastProcessed}ms ago)`);
          return false;
        }

        // If this is the same source and status, it's definitely a duplicate
        if (lastProcessed.source === source && lastProcessed.status === status) {
          logger.info(`CentralResultProcessor: Skipping duplicate result for test ${testId} from ${source}, last processed ${timeSinceLastProcessed}ms ago with same status ${status} - this prevents duplicate processing and is normal behavior`);
          return false;
        }

        // Check status progression - only allow forward progression
        const statusPriority = {
          'pending': 0,
          'queued': 1,
          'running': 2,
          'completed': 3,
          'failed': 3,
          'stopped': 3,
          'error': 3
        };

        const previousPriority = statusPriority[lastProcessed.status as keyof typeof statusPriority] || 0;
        const currentPriority = statusPriority[status as keyof typeof statusPriority] || 0;

        if (currentPriority <= previousPriority) {
          logger.info(`CentralResultProcessor: Skipping result for test ${testId} from ${source} with status ${status}, previously processed with higher/equal priority status ${lastProcessed.status} - this prevents status regression and is normal behavior`);
          return false;
        }

        // Allow forward progression
        logger.info(`CentralResultProcessor: Processing result for test ${testId} from ${source} with status ${status}, progressing from ${lastProcessed.status}`);
      }
    }

    try {
      // Acquire processing lock with transaction ID
      this.processingLocks.set(resultKey, {
        locked: true,
        timestamp: now,
        transactionId: resultTransactionId
      });

      // Map the status from node format to hub format if needed
      const mappedStatus = status ? mapNodeStatusToHubStatus(status) : undefined;

      if (!mappedStatus) {
        logger.warn(`CentralResultProcessor: No status provided for test ${testId}`);
        return false;
      }

      // Handle duration calculation for stopped/failed tests
      let duration = actualDetailedResultPayload.duration;
      if (mappedStatus === TestStatus.STOPPED || mappedStatus === TestStatus.FAILED || mappedStatus === TestStatus.ERROR) {
        if (actualDetailedResultPayload.startTime && actualDetailedResultPayload.endTime) {
          const startTimeMs = new Date(actualDetailedResultPayload.startTime).getTime();
          const endTimeMs = new Date(actualDetailedResultPayload.endTime).getTime();
          
          // Validate timestamps are valid dates
          if (!isNaN(startTimeMs) && !isNaN(endTimeMs)) {
            const calculatedDuration = endTimeMs - startTimeMs;
            
            // Check for negative duration (endTime before startTime)
            if (calculatedDuration < 0) {
              logger.warn(`CentralResultProcessor: Negative duration detected for test ${testId}. startTime: ${actualDetailedResultPayload.startTime}, endTime: ${actualDetailedResultPayload.endTime}, calculated duration: ${calculatedDuration}ms`);
              
              // Use the absolute value and log the correction
              duration = Math.abs(calculatedDuration);
              logger.info(`CentralResultProcessor: Corrected negative duration to positive value: ${duration}ms for test ${testId}`);
            } else {
              duration = calculatedDuration;
              logger.debug(`CentralResultProcessor: Calculated duration from timestamps: ${duration}ms for test ${testId}`);
            }
          } else {
            logger.warn(`CentralResultProcessor: Invalid timestamps for test ${testId}. startTime: ${actualDetailedResultPayload.startTime}, endTime: ${actualDetailedResultPayload.endTime}`);
            // Keep existing duration if timestamps are invalid
            if (duration !== undefined && duration < 0) {
              duration = Math.abs(duration);
              logger.info(`CentralResultProcessor: Corrected negative stored duration to positive value: ${duration}ms for test ${testId}`);
            }
          }
        } else if (actualDetailedResultPayload.startTime && !actualDetailedResultPayload.endTime) {
          // If endTime is missing, calculate duration from startTime to now
          const startTimeMs = new Date(actualDetailedResultPayload.startTime).getTime();
          if (!isNaN(startTimeMs)) {
            duration = Date.now() - startTimeMs;
            logger.info(`CentralResultProcessor: Calculated duration from startTime to now: ${duration}ms for test ${testId} (endTime was missing)`);
          }
        }
        
        // Final validation - ensure duration is never negative
        if (duration !== undefined && duration < 0) {
          logger.warn(`CentralResultProcessor: Final validation caught negative duration (${duration}ms) for test ${testId}, converting to positive`);
          duration = Math.abs(duration);
        }
        
        // Sanity check - if duration is unreasonably large (> 24 hours), log a warning
        if (duration !== undefined && duration > 24 * 60 * 60 * 1000) {
          logger.warn(`CentralResultProcessor: Unusually large duration detected for test ${testId}: ${duration}ms (${Math.round(duration / 1000 / 60)} minutes)`);
        }
      }

      // Validate scenarioId before transformation
      if (!scenarioId || scenarioId.trim() === '') {
        logger.error(`CentralResultProcessor: Missing or empty scenarioId for test ${testId}. rawData.scenarioId: ${actualDetailedResultPayload.scenarioId}, result.scenarioId: ${result.scenarioId}, outerJobData.scenarioId: ${outerJobData.scenarioId}`);
        return false;
      }

      // Transform raw data to clean TestReportDTO using the new transformer
      const reportToSave = this.transformToTestReportDTO(actualDetailedResultPayload, {
        testId,
        nodeId,
        runId,
        executionId,
        scenarioId,
        executedUser,
        executedUserName,
        teamId,
        companyId,
        status: mappedStatus.toString(),
        duration
      });

      // Validate the DTO structure
      if (!validateTestReportDTO(reportToSave)) {
        logger.error(`CentralResultProcessor: Generated TestReportDTO is invalid for test ${testId}. DTO: ${JSON.stringify(reportToSave)}`);
        return false;
      }

      logger.info(`CentralResultProcessor: Successfully transformed test ${testId} to clean TestReportDTO`);

      // 1. Create or update the test report in MongoDB atomically
      try {
        // Import the atomic report service to prevent race conditions
        const { createTestReportAtomic } = await import('../../services/mongo/atomicReportService.js');

        // Log runId for debugging
        logger.info(`CentralResultProcessor: Creating test report atomically with runId: ${runId || 'none'} for test ${testId}`);

        // Create/update the test report atomically
        const reportResult = await createTestReportAtomic(reportToSave, {
          source: source,
          transactionId: resultTransactionId
        });

        if (!reportResult.success) {
          logger.warn(`CentralResultProcessor: Failed to create/update test report atomically for ${testId}: ${reportResult.message}`);
        } else {
          logger.info(`CentralResultProcessor: Successfully created/updated test report atomically for ${testId}`);
        }
      } catch (reportError) {
        logger.error(`CentralResultProcessor: Error creating/updating test report atomically: ${reportError}`);
      }

      // 2. Update scenario status and run status based on result (scenario-based logic only)
      if (runId && executionId) {
        try {
          // Import the atomic report service dynamically
          const { getRunReportByRunId, updateRunReport } = await import('../../services/mongo/atomicReportService.js');

          // Check if run report exists
          const existingReportCheck = await getRunReportByRunId(runId, executionId);
          
          if (!existingReportCheck.success) {
            logger.warn(`CentralResultProcessor: Run report not found for runId: ${runId}, executionId: ${executionId}`);
            
            // Try to get ANY run report for this runId to debug execution ID mismatch
            const anyReportCheck = await getRunReportByRunId(runId);
            if (anyReportCheck.success) {
              logger.warn(`CentralResultProcessor: Found run report with different execution ID: ${anyReportCheck.report.executionId} (requested: ${executionId})`);
            } else {
              logger.error(`CentralResultProcessor: No run report exists for runId: ${runId}`);
            }
          }

          // Validate required fields for run report updates
          if (!executionId) {
            logger.warn(`CentralResultProcessor: Missing executionId for test ${testId}, which is required for run report updates`);
          }

          if (!executedUserName) {
            logger.warn(`CentralResultProcessor: Missing executedUserName for test ${testId}, which is required for run report updates`);
          }

          // Update metadata fields if they're missing
          const reportResult = await getRunReportByRunId(runId, executionId);
          if (reportResult.success && reportResult.report) {
            const report = reportResult.report;
            const metadataUpdates: any = {};

            // If userId, teamId, or companyId are null in the report but available in the result, update them
            if (report.userId === null && executedUser) {
              metadataUpdates.userId = executedUser;
            }

            if (report.teamId === null && teamId) {
              metadataUpdates.teamId = teamId;
            }

            if (report.companyId === null && companyId) {
              metadataUpdates.companyId = companyId;
            }

            // Update metadata if there are any changes
            if (Object.keys(metadataUpdates).length > 0) {
              metadataUpdates.lastUpdated = new Date();

              const metadataUpdateResult = await updateRunReport(report.id, {
                $set: metadataUpdates
              });

              if (!metadataUpdateResult.success) {
                logger.warn(`CentralResultProcessor: Failed to update run report metadata for run ${runId}: ${metadataUpdateResult.message}`);
              }
            }
          }

          // Update scenario status - this will automatically trigger run status check
          if (scenarioId) {
            try {
              // Map test status to scenario status
              let scenarioStatus: string;
              if (mappedStatus === TestStatus.COMPLETED) {
                scenarioStatus = 'passed';
              } else if (mappedStatus === TestStatus.FAILED || mappedStatus === TestStatus.ERROR) {
                scenarioStatus = 'failed';
              } else if (mappedStatus === TestStatus.RUNNING) {
                scenarioStatus = 'running';
              } else if (mappedStatus === TestStatus.STOPPED) {
                scenarioStatus = 'stopped';
              } else {
                scenarioStatus = 'queued';
              }

              // Update scenario status in run report using atomic service
              // This will automatically trigger checkAndUpdateRunStatus when complete
              const { updateScenarioStatusAtomic } = await import('../../services/mongo/atomicReportService.js');

              const scenarioUpdateResult = await updateScenarioStatusAtomic(runId, executionId, scenarioId, scenarioStatus as any, {
                testId: testId,
                completionTime: mappedStatus === TestStatus.COMPLETED ||
                               mappedStatus === TestStatus.FAILED ||
                               mappedStatus === TestStatus.STOPPED ?
                               new Date() : undefined,
                duration: duration,
                source: source,
                transactionId: resultTransactionId,
                previousStatus: previousStatus?.toLowerCase()
              }, existingReportCheck.success ? existingReportCheck.report : undefined);

              if (!scenarioUpdateResult.success) {
                logger.error(`CentralResultProcessor: Failed to update scenario ${scenarioId} status: ${scenarioUpdateResult.message}`);
              }

            } catch (scenarioError) {
              logger.error(`CentralResultProcessor: Error updating scenario status: ${scenarioError}`);
            }
          }
        } catch (runReportError) {
          logger.error(`CentralResultProcessor: Error updating run report: ${runReportError}`);
        }
      } else {
        logger.warn(`CentralResultProcessor: Missing runId (${runId}) or executionId (${executionId}) for test ${testId} - skipping run report updates`);
      }

      // Record that we've processed this result with more context
      this.processedResults.set(resultKey, {
        timestamp: now,
        source,
        status: status || 'unknown',
        nodeId,
        transactionId: resultTransactionId
      });

      // Emit event for any listeners
      this.emit('resultProcessed', {
        testId,
        nodeId,
        status: mappedStatus,
        source,
        transactionId: resultTransactionId,
        timestamp: now
      });

      // Clean up step progress for completed tests
      if (mappedStatus === TestStatus.COMPLETED || mappedStatus === TestStatus.FAILED || mappedStatus === TestStatus.STOPPED || mappedStatus === TestStatus.ERROR) {
        try {
          const { stepProgressService } = await import('../step-progress/stepProgressService.js');
          await stepProgressService.cleanupTestStepProgress(testId);
        } catch (error) {
          logger.warn(`CentralResultProcessor: Error cleaning up step progress for test ${testId}: ${error}`);
        }
      }

      return true;
    } catch (error) {
      logger.error(`CentralResultProcessor: Error processing result for test ${testId} from ${source} (transaction: ${resultTransactionId}): ${error}`);
      return false;
    } finally {
      // Release processing lock
      this.processingLocks.delete(resultKey);
    }
  }

  /**
   * Clean up expired results
   */
  private cleanupExpiredResults(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [resultKey, result] of this.processedResults.entries()) {
      if (now - result.timestamp > this.RESULT_EXPIRY_TIME) {
        this.processedResults.delete(resultKey);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`CentralResultProcessor: Cleaned up ${cleanedCount} expired results`);
    }
  }

  /**
   * Clean up stuck locks
   * This prevents tests from getting stuck due to a lock that was never released
   */
  private cleanupStuckLocks(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [resultKey, lockInfo] of this.processingLocks.entries()) {
      if (now - lockInfo.timestamp > this.LOCK_TIMEOUT) {
        logger.warn(`CentralResultProcessor: Cleaning up stuck lock for ${resultKey} (transaction: ${lockInfo.transactionId}, age: ${now - lockInfo.timestamp}ms)`);
        this.processingLocks.delete(resultKey);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`CentralResultProcessor: Cleaned up ${cleanedCount} stuck locks`);
    }
  }
}

// Export singleton instance
export const centralResultProcessor = CentralResultProcessor.getInstance();
export default centralResultProcessor;
