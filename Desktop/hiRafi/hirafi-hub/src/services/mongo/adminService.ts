/**
 * Admin Service
 * Admin kullanıcıları için servis fonksiyonları
 */

import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { logger } from '../../utils/logger.js';
import { ensureMongoDBConnection, adminsCollection, initAdminCollection } from './dbConnection.js';
import { Admin, AdminRole, AdminLoginResponse, AdminTokenPayload, AdminValidationResponse } from '../../models/admin.js';
import { config } from '../../config/index.js';

// Admin koleksiyonu başlatma fonksiyonu dbConnection.ts dosyasına taşındı

/**
 * Şifreyi hash'le
 * @param password Plain text password
 * @returns Hashed password
 */
async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}

/**
 * Şifreleri karşılaştır
 * @param plainPassword Plain text password
 * @param hashedPassword Hashed password
 * @returns true if passwords match
 */
async function comparePasswords(plainPassword: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(plainPassword, hashedPassword);
}

/**
 * JWT token oluştur
 * @param payload Token payload
 * @returns JWT token
 */
function generateToken(payload: AdminTokenPayload): string {
  const secret = config.jwt.adminSecret || 'admin-secret-key';
  const expiresIn = config.jwt.adminExpiresIn || '24h';

  // @ts-ignore - Ignoring type issues with jwt.sign
  return jwt.sign(payload, secret, { expiresIn });
}

/**
 * JWT token doğrula
 * @param token JWT token
 * @returns Decoded token payload or null
 */
export function verifyToken(token: string): AdminTokenPayload | null {
  try {
    const secret = config.jwt.adminSecret || 'admin-secret-key';
    return jwt.verify(token, secret) as AdminTokenPayload;
  } catch (error) {
    logger.error('[ADMIN] Token verification error:', error);
    return null;
  }
}

/**
 * Admin koleksiyonunun başlatıldığından emin ol
 * @returns true if initialized, false otherwise
 */
async function ensureAdminCollection(): Promise<boolean> {
  if (!adminsCollection) {
    return await initAdminCollection();
  }
  return true;
}

/**
 * Admin login
 * @param email Admin email
 * @param password Admin password
 * @returns Login response
 */
export async function loginAdmin(email: string, password: string): Promise<AdminLoginResponse> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Debug: Gelen email ve password'u logla
    logger.info(`[ADMIN] Login attempt for email: ${email}`);

    // Admin kullanıcısını bul
    const admin = await adminsCollection.findOne({ email });

    if (!admin) {
      logger.warn(`[ADMIN] Admin not found for email: ${email}`);
      // Debug: Tüm adminleri listele
      const allAdmins = await adminsCollection.find({}).toArray();
      logger.info(`[ADMIN] Total admins in database: ${allAdmins.length}`);
      allAdmins.forEach(a => logger.info(`[ADMIN] Existing admin: ${a.email}, active: ${a.active}`));

      return {
        success: false,
        error: 'Invalid credentials'
      };
    }

    logger.info(`[ADMIN] Admin found: ${admin.email}, active: ${admin.active}`);

    // Aktif değilse giriş yapamaz
    if (!admin.active) {
      return {
        success: false,
        error: 'Account is disabled'
      };
    }

    // Debug: Şifre karşılaştırması öncesi
    logger.info(`[ADMIN] Comparing passwords for: ${email}`);
    logger.info(`[ADMIN] Plain password length: ${password.length}`);
    logger.info(`[ADMIN] Hashed password exists: ${!!admin.password}`);

    // Şifreleri karşılaştır
    const passwordMatch = await comparePasswords(password, admin.password);

    logger.info(`[ADMIN] Password match result: ${passwordMatch}`);

    if (!passwordMatch) {
      return {
        success: false,
        error: 'Invalid credentials'
      };
    }

    // Token payload oluştur
    const tokenPayload: AdminTokenPayload = {
      id: admin.id,
      email: admin.email,
      role: admin.role,
      accountType: admin.accountType || 'admin' // accountType yoksa 'admin' olarak ayarla
    };

    // JWT token oluştur
    const token = generateToken(tokenPayload);

    // Son giriş zamanını güncelle
    await adminsCollection.updateOne(
      { id: admin.id },
      { $set: { lastLogin: new Date(), updatedAt: new Date() } }
    );

    // Başarılı login yanıtı
    return {
      success: true,
      token,
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        accountType: admin.accountType || 'admin' // accountType yoksa 'admin' olarak ayarla
      }
    };
  } catch (error) {
    logger.error('[ADMIN] Login error:', error);
    return {
      success: false,
      error: 'Authentication failed'
    };
  }
}

/**
 * Admin token doğrula
 * @param token JWT token
 * @returns Validation response
 */
export async function validateAdminToken(token: string): Promise<AdminValidationResponse> {
  try {
    // Token doğrula
    const decoded = verifyToken(token);

    if (!decoded) {
      return {
        success: false,
        error: 'Invalid token'
      };
    }

    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Admin kullanıcısını bul
    const admin = await adminsCollection.findOne({ id: decoded.id });

    if (!admin) {
      return {
        success: false,
        error: 'Admin not found'
      };
    }

    // Aktif değilse erişim yok
    if (!admin.active) {
      return {
        success: false,
        error: 'Account is disabled'
      };
    }

    // Başarılı doğrulama yanıtı
    return {
      success: true,
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        accountType: admin.accountType || 'admin' // accountType yoksa 'admin' olarak ayarla
      }
    };
  } catch (error) {
    logger.error('[ADMIN] Token validation error:', error);
    return {
      success: false,
      error: 'Validation failed'
    };
  }
}

/**
 * Admin kullanıcısı oluştur
 * @param adminData Admin kullanıcı verileri
 * @returns Oluşturma sonucu
 */
export async function createAdmin(adminData: Partial<Admin>): Promise<{ success: boolean; adminId?: string; error?: string }> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Zorunlu alanları kontrol et
    if (!adminData.email || !adminData.password || !adminData.role) {
      return {
        success: false,
        error: 'Email, password and role are required'
      };
    }

    // Email formatını kontrol et
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(adminData.email)) {
      return {
        success: false,
        error: 'Invalid email format'
      };
    }

    // Email benzersizliğini kontrol et
    const existingAdmin = await adminsCollection.findOne({ email: adminData.email });
    if (existingAdmin) {
      return {
        success: false,
        error: 'Email already in use'
      };
    }

    // Admin ID oluştur
    const adminId = uuidv4();

    // Şifre hash'le
    const hashedPassword = await hashPassword(adminData.password);

    // Admin nesnesini oluştur
    const admin: Admin = {
      id: adminId,
      email: adminData.email,
      password: hashedPassword,
      name: adminData.name || adminData.email.split('@')[0],
      role: adminData.role,
      accountType: 'admin', // Her zaman admin olarak ayarla
      permissions: adminData.permissions || [],
      createdAt: new Date(),
      active: adminData.active !== undefined ? adminData.active : true
    };

    // Admin kullanıcısını kaydet
    await adminsCollection.insertOne(admin);

    logger.info(`[ADMIN] New admin created: ${admin.email} with role: ${admin.role}`);

    return {
      success: true,
      adminId
    };
  } catch (error) {
    logger.error('[ADMIN] Error creating admin:', error);
    return {
      success: false,
      error: 'Failed to create admin'
    };
  }
}

/**
 * Admin kullanıcısını güncelle
 * @param adminId Admin ID
 * @param updateData Güncellenecek veriler
 * @returns Güncelleme sonucu
 */
export async function updateAdmin(adminId: string, updateData: Partial<Admin>): Promise<{ success: boolean; error?: string }> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Admin kullanıcısını bul
    const admin = await adminsCollection.findOne({ id: adminId });

    if (!admin) {
      return {
        success: false,
        error: 'Admin not found'
      };
    }

    // Güncellenecek verileri hazırla
    const updateFields: Partial<Admin> = {
      updatedAt: new Date()
    };

    // İzin verilen alanları güncelle
    if (updateData.name) updateFields.name = updateData.name;
    if (updateData.role) updateFields.role = updateData.role;
    if (updateData.permissions) updateFields.permissions = updateData.permissions;
    if (updateData.active !== undefined) updateFields.active = updateData.active;

    // Email güncelleniyorsa benzersizliğini kontrol et
    if (updateData.email && updateData.email !== admin.email) {
      const existingAdmin = await adminsCollection.findOne({ email: updateData.email });
      if (existingAdmin) {
        return {
          success: false,
          error: 'Email already in use'
        };
      }
      updateFields.email = updateData.email;
    }

    // Şifre güncelleniyorsa hash'le
    if (updateData.password) {
      updateFields.password = await hashPassword(updateData.password);
    }

    // Admin kullanıcısını güncelle
    await adminsCollection.updateOne(
      { id: adminId },
      { $set: updateFields }
    );

    logger.info(`[ADMIN] Admin updated: ${adminId}`);

    return {
      success: true
    };
  } catch (error) {
    logger.error('[ADMIN] Error updating admin:', error);
    return {
      success: false,
      error: 'Failed to update admin'
    };
  }
}

/**
 * Admin kullanıcısını sil
 * @param adminId Admin ID
 * @returns Silme sonucu
 */
export async function deleteAdmin(adminId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Admin kullanıcısını bul
    const admin = await adminsCollection.findOne({ id: adminId });

    if (!admin) {
      return {
        success: false,
        error: 'Admin not found'
      };
    }

    // Super admin'i silmeye çalışıyorsa engelle
    if (admin.role === AdminRole.SUPER_ADMIN) {
      return {
        success: false,
        error: 'Cannot delete super admin'
      };
    }

    // Admin kullanıcısını sil
    await adminsCollection.deleteOne({ id: adminId });

    logger.info(`[ADMIN] Admin deleted: ${adminId}`);

    return {
      success: true
    };
  } catch (error) {
    logger.error('[ADMIN] Error deleting admin:', error);
    return {
      success: false,
      error: 'Failed to delete admin'
    };
  }
}

/**
 * Tüm admin kullanıcılarını getir
 * @returns Admin kullanıcıları listesi
 */
export async function getAllAdmins(): Promise<{ success: boolean; admins?: Partial<Admin>[]; error?: string }> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Tüm admin kullanıcılarını getir
    const admins = await adminsCollection.find({}).toArray();

    // Şifreleri çıkar
    const safeAdmins = admins.map(admin => {
      const { password, ...safeAdmin } = admin;
      return safeAdmin;
    });

    return {
      success: true,
      admins: safeAdmins
    };
  } catch (error) {
    logger.error('[ADMIN] Error getting admins:', error);
    return {
      success: false,
      error: 'Failed to get admins'
    };
  }
}

/**
 * Admin kullanıcısını ID'ye göre getir
 * @param adminId Admin ID
 * @returns Admin kullanıcısı
 */
export async function getAdminById(adminId: string): Promise<{ success: boolean; admin?: Partial<Admin>; error?: string }> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Admin kullanıcısını bul
    const admin = await adminsCollection.findOne({ id: adminId });

    if (!admin) {
      return {
        success: false,
        error: 'Admin not found'
      };
    }

    // Şifreyi çıkar
    const { password, ...safeAdmin } = admin;

    return {
      success: true,
      admin: safeAdmin
    };
  } catch (error) {
    logger.error('[ADMIN] Error getting admin:', error);
    return {
      success: false,
      error: 'Failed to get admin'
    };
  }
}

/**
 * Şifre değiştirme
 * @param adminId Admin ID
 * @param currentPassword Mevcut şifre
 * @param newPassword Yeni şifre
 * @returns Şifre değiştirme sonucu
 */
export async function changePassword(adminId: string, currentPassword: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    // Admin koleksiyonunun başlatıldığından emin ol
    const initialized = await ensureAdminCollection();

    if (!initialized || !adminsCollection) {
      return {
        success: false,
        error: 'Admin service not available'
      };
    }

    // Admin kullanıcısını bul
    const admin = await adminsCollection.findOne({ id: adminId });

    if (!admin) {
      return {
        success: false,
        error: 'Admin not found'
      };
    }

    // Mevcut şifreyi kontrol et
    const passwordMatch = await comparePasswords(currentPassword, admin.password);

    if (!passwordMatch) {
      return {
        success: false,
        error: 'Current password is incorrect'
      };
    }

    // Yeni şifreyi hash'le
    const hashedPassword = await hashPassword(newPassword);

    // Şifreyi güncelle
    await adminsCollection.updateOne(
      { id: adminId },
      {
        $set: {
          password: hashedPassword,
          updatedAt: new Date()
        }
      }
    );

    logger.info(`[ADMIN] Password changed for admin: ${adminId}`);

    return {
      success: true
    };
  } catch (error) {
    logger.error('[ADMIN] Error changing password:', error);
    return {
      success: false,
      error: 'Failed to change password'
    };
  }
}

// Admin service initialization is now handled by dbConnection.ts
// This ensures that all collections are properly initialized when the database connection is established
