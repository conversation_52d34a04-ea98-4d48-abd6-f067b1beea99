/**
 * Environment Variable Utilities
 * Centralized utilities for handling environment variables with type safety
 */

/**
 * Gets a value from environment variables or returns the default
 * @param key Environment variable key
 * @param defaultValue Default value if env variable is not set
 */
export function getEnv<T>(key: string, defaultValue: T): T {
  const value = process.env[key];
  return value !== undefined ? (value as unknown as T) : defaultValue;
}

/**
 * Gets a boolean value from environment variables
 * @param key Environment variable key
 * @param defaultValue Default value if env variable is not set
 */
export function getEnvBoolean(key: string, defaultValue: boolean): boolean {
  const value = process.env[key];
  if (value === undefined) return defaultValue;
  return value === 'true';
}

/**
 * Gets an integer value from environment variables
 * @param key Environment variable key
 * @param defaultValue Default value if env variable is not set
 */
export function getEnvInt(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Gets a float value from environment variables
 * @param key Environment variable key
 * @param defaultValue Default value if env variable is not set
 */
export function getEnvFloat(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (value === undefined) return defaultValue;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Gets an array from comma-separated environment variable
 * @param key Environment variable key
 * @param defaultValue Default array if env variable is not set
 */
export function getEnvArray(key: string, defaultValue: string[]): string[] {
  const value = process.env[key];
  if (!value) return defaultValue;
  return value.split(',').filter(Boolean);
}

/**
 * Gets a required environment variable, throws error if not set
 * @param key Environment variable key
 * @throws Error if environment variable is not set
 */
export function getRequiredEnv(key: string): string {
  const value = process.env[key];
  if (value === undefined || value === '') {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  return value;
}

/**
 * Validates that all required environment variables are set
 * @param requiredKeys Array of required environment variable keys
 * @throws Error if any required environment variable is not set
 */
export function validateRequiredEnvVars(requiredKeys: string[]): void {
  const missing = requiredKeys.filter(key => !process.env[key]);
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Gets environment variable with validation
 * @param key Environment variable key
 * @param defaultValue Default value if env variable is not set
 * @param validator Validation function
 * @throws Error if validation fails
 */
export function getEnvWithValidation<T>(
  key: string,
  defaultValue: T,
  validator: (value: T) => boolean
): T {
  const value = getEnv(key, defaultValue);
  if (!validator(value)) {
    throw new Error(`Environment variable ${key} has invalid value: ${value}`);
  }
  return value;
}
