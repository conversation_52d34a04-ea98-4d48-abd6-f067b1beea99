"use client"

import { useState } from "react"
import { MoreHorizontal, Check, LayoutGrid, List } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface MoreFiltersProps {
  itemsPerPage: number
  onItemsPerPageChange: (count: number) => void
  displayView: "grid" | "list"
  onDisplayViewChange: (view: "grid" | "list") => void
}

// Items per page options
const itemsPerPageOptions = [5, 10, 20, 50]

export function MoreFilters({
  itemsPerPage,
  onItemsPerPageChange,
  displayView,
  onDisplayViewChange,
}: MoreFiltersProps) {
  const [open, setOpen] = useState(false)
  
  // Handle items per page selection
  const handleItemsPerPageSelect = (count: number) => {
    onItemsPerPageChange(count)
  }
  
  // Handle display view selection
  const handleDisplayViewSelect = (view: "grid" | "list") => {
    onDisplayViewChange(view)
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-9 border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center">
            <MoreHorizontal className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
            <span className="text-sm font-medium">More</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[220px]">
        <DropdownMenuLabel>Display Options</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Items per page selection */}
        <div className="px-3 py-2">
          <div className="text-sm text-gray-700 dark:text-gray-300 mb-2">Items per page:</div>
          <div className="flex flex-wrap gap-2">
            {itemsPerPageOptions.map((count) => (
              <Button
                key={count}
                variant={itemsPerPage === count ? "default" : "outline"}
                size="sm"
                className={cn(
                  "h-7 px-2.5 text-xs",
                  itemsPerPage === count 
                    ? "bg-primary text-primary-foreground" 
                    : "bg-transparent text-gray-700 dark:text-gray-300"
                )}
                onClick={() => handleItemsPerPageSelect(count)}
              >
                {count}
              </Button>
            ))}
          </div>
        </div>
        
        <DropdownMenuSeparator />
        
        {/* View selection */}
        <div className="px-3 py-2">
          <div className="text-sm text-gray-700 dark:text-gray-300 mb-2">View:</div>
          <div className="flex gap-2">
            <Button
              variant={displayView === "grid" ? "default" : "outline"}
              size="sm"
              className={cn(
                "h-8 px-3 text-xs flex-1",
                displayView === "grid" 
                  ? "bg-primary text-primary-foreground" 
                  : "bg-transparent text-gray-700 dark:text-gray-300"
              )}
              onClick={() => handleDisplayViewSelect("grid")}
            >
              <LayoutGrid className="h-3.5 w-3.5 mr-1.5" />
              Grid
            </Button>
            <Button
              variant={displayView === "list" ? "default" : "outline"}
              size="sm"
              className={cn(
                "h-8 px-3 text-xs flex-1",
                displayView === "list" 
                  ? "bg-primary text-primary-foreground" 
                  : "bg-transparent text-gray-700 dark:text-gray-300"
              )}
              onClick={() => handleDisplayViewSelect("list")}
            >
              <List className="h-3.5 w-3.5 mr-1.5" />
              List
            </Button>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
