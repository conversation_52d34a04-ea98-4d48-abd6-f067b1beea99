import React from 'react';
import { RunCard } from './RunCard';
import { But<PERSON> } from '@/components/ui/button';
import { LoaderCircle, PlayCircle, Plus, X } from 'lucide-react';

export interface RunsGridProps {
  runs: any[];
  loading: boolean;
  error?: string | null;
  onStart: (runId: string) => void;
  onStop: (runId: string) => void;
  onDelete: (runId: string) => void;
  onCreateRun: () => void;
  onRetry?: () => void;
}

export function RunsGrid({ 
  runs, 
  loading, 
  error, 
  onStart, 
  onStop, 
  onDelete, 
  onCreateRun,
  onRetry 
}: RunsGridProps) {
  
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-16 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
        <div className="h-16 w-16 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center mb-6">
          <LoaderCircle className="h-10 w-10 text-indigo-600 dark:text-indigo-400 animate-spin" />
        </div>
        <h3 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-2">Loading test runs...</h3>
        <p className="text-center text-gray-500 dark:text-gray-400">Please wait while we fetch your test runs.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-16 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
        <div className="h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
          <X className="h-8 w-8 text-red-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">Error loading runs</h3>
        <p className="text-center text-gray-500 dark:text-gray-400 max-w-md mb-6">
          {error}
        </p>
        {onRetry && (
          <Button
            onClick={onRetry}
            className="bg-indigo-600 hover:bg-indigo-700 text-white"
          >
            Try Again
          </Button>
        )}
      </div>
    );
  }

  if (runs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-16 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
        <div className="h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
          <PlayCircle className="h-8 w-8 text-gray-400 dark:text-gray-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">No test runs found</h3>
        <p className="text-center text-gray-500 dark:text-gray-400 max-w-md mb-6">
          You haven't created any test runs yet. Create a new run to get started.
        </p>
        <Button
          onClick={onCreateRun}
          className="bg-indigo-600 hover:bg-indigo-700 text-white"
        >
          <Plus className="mr-2 h-4 w-4" />
          Create New Run
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {runs.map(run => (
        <RunCard
          key={run.id}
          run={run}
          onStart={onStart}
          onStop={onStop}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
}
