/**
 * Execution Coordinator Interface
 * 
 * Defines the contract for test execution coordination.
 * This interface separates the execution logic from implementation details.
 */

import { EventEmitter } from 'events';
import { TestRequest, TestResult } from '../../../models/types.js';
import { TestExecutionState } from '../../node-manager/types/node-manager-types.js';

/**
 * Test execution events that can be emitted during test lifecycle
 */
export interface TestExecutionEvents {
  'test:claimed': (testId: string) => void;
  'test:started': (testId: string) => void;
  'test:completed': (testId: string, result: TestResult) => void;
  'test:failed': (testId: string, error: Error) => void;
  'test:result': (testId: string, result: TestResult) => void;
  'test:log': (testId: string, result: TestResult, duration: number) => void;
}

/**
 * Test execution options for customizing execution behavior
 */
export interface TestExecutionOptions {
  extendLockFunction?: (duration: number) => Promise<any>;
  jobToken?: string;
  timeout?: number;
  retryCount?: number;
}

/**
 * Test execution status information
 */
export interface TestExecutionStatus {
  state: TestExecutionState;
  currentTestId: string | null;
  canAcceptNewTest: boolean;
  isTestRunning: boolean;
}

/**
 * Interface for coordinating test execution
 * 
 * This interface defines the main contract for test execution coordination,
 * separating it from the implementation details and making it easily testable.
 */
export interface IExecutionCoordinator extends EventEmitter {
  /**
   * Set the event handler for test execution events.
   * @param eventHandler The event handler to use for emitting events.
   */
  setEventHandler(eventHandler: EventEmitter): void;

  /**
   * Execute a test request
   * @param testRequest The test request to execute
   * @param options Optional execution parameters
   * @returns Promise resolving to the test result
   */
  executeTest(testRequest: TestRequest, options?: TestExecutionOptions): Promise<TestResult>;

  /**
   * Get the current execution status
   * @returns Current execution status information
   */
  getExecutionStatus(): TestExecutionStatus;

  /**
   * Check if the coordinator can accept a new test
   * @returns True if can accept new test, false otherwise
   */
  canAcceptNewTest(): boolean;

  /**
   * Check if a test is currently running
   * @returns True if a test is running, false otherwise
   */
  isTestRunning(): boolean;

  /**
   * Check if a specific test is currently running
   * @param testId The test ID to check
   * @returns True if the specified test is running, false otherwise
   */
  isSpecificTestRunning(testId: string): boolean;

  /**
   * Get the current test ID being processed
   * @returns Current test ID or null if no test is running
   */
  getCurrentTestId(): string | null;

  /**
   * Abort the currently running test
   * @returns Promise that resolves when the test is aborted
   */
  abortCurrentTest(): Promise<void>;

  /**
   * Stop a specific test if it is currently running
   * @param testId The ID of the test to stop
   * @param reason The reason for stopping the test
   * @returns Promise that resolves when the test is stopped
   */
  stopSpecificTest(testId: string, reason: string): Promise<void>;

  /**
   * Handle process crash notification
   * @param reason Reason for the crash
   */
  handleProcessCrash(reason: string): void;

  /**
   * Handle graceful shutdown notification
   * @param reason Reason for the shutdown
   */
  handleGracefulShutdown(reason: string): void;

  /**
   * Set test runner factory configuration
   * @param websocketConnector WebSocket connector instance
   * @param config Factory configuration
   * @param storage Storage instance
   */
  setTestRunnerFactoryConfig(websocketConnector: any, config: any, storage: any): void;

  /**
   * Initialize the execution coordinator
   * @returns Promise that resolves when initialization is complete
   */
  initialize(): Promise<void>;

  /**
   * Cleanup and dispose of the execution coordinator
   * @returns Promise that resolves when cleanup is complete
   */
  dispose(): Promise<void>;
}
