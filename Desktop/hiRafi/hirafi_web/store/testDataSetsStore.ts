/**
 * Test Data Sets Zustand Store
 *
 * Manages test data sets with environment-specific variable values.
 * Each variable can have different values per environment, and values
 * are user-controlled (not auto-generated).
 */

import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import type {
  VariableType,
  ValueConstraints,
  EnvironmentType,
} from "@/types/test-data";
import { storeManager } from "@/lib/utils/store-management";
import { companyAwareStorage } from "@/lib/utils/company-aware-storage";

// Core interfaces
export interface DataEnvironment {
  id: string;
  name: string;
  description: string;
  color: string;
  type: "default" | "custom";
  isActive: boolean;
}

export interface TestDataVariable {
  id: string;
  name: string;
  type: VariableType;
  description: string;
  isRequired: boolean;
  format?: string;
  constraints?: ValueConstraints;
  environmentValues: Record<string, string>; // environmentId -> value
  // API Source integration
  sourceType?: "api" | "database" | "csv" | "excel";
  sourceId?: string;
  jsonPath?: string;
  // Database Source integration
  dbMode?: string;
  needsColumnMapping?: boolean;
  nameColumn?: string;
  valueColumn?: string;
}

export interface DataSetFormData {
  name: string;
  description: string;
  tags: string[];
  aiPrompt: string;
  environment: EnvironmentType; // For compatibility with existing components
}

export interface DataTemplate {
  id: string;
  name: string;
  description: string;
  variables: Array<{
    name: string;
    type: VariableType;
    description: string;
  }>;
}

// Store state interface
interface TestDataSetsState {
  // Form data
  formData: DataSetFormData;

  // Variables with environment-specific values
  variables: TestDataVariable[];

  // Environment management
  activeEnvironmentId: string;
  userEnvironments: DataEnvironment[];
  selectedEnvironments: string[]; // Track which environments are enabled for the data set
  isLoadingEnvironments: boolean;

  // UI state
  activeTab: string;
  showVariableValues: boolean;
  showEnvironmentSelector: boolean;
  selectedTemplate: string | null;
  isGenerating: boolean;
  isSaving: boolean;

  // Editing state
  editingVariable: TestDataVariable | null;
  tempConstraints: ValueConstraints;

  // Tags
  newTag: string;

  // State for loading a single data set
  isLoadingDataSet: boolean;
  loadDataSetError: string | null;
}

// Store actions interface
interface TestDataSetsActions {
  // Form actions
  updateFormData: (data: Partial<DataSetFormData>) => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  setNewTag: (tag: string) => void;

  // Variable actions
  addVariable: () => void;
  removeVariable: (id: string) => void;
  duplicateVariable: (variable: TestDataVariable) => void;
  updateVariable: (
    id: string,
    updates: Partial<Omit<TestDataVariable, "id" | "environmentValues">>,
  ) => void;
  updateVariableValue: (
    variableId: string,
    environmentId: string,
    value: string,
  ) => void;
  setVariables: (variables: TestDataVariable[]) => void;

  // Environment actions
  setActiveEnvironment: (environmentId: string) => void;
  setUserEnvironments: (environments: DataEnvironment[]) => void;
  setSelectedEnvironments: (environmentIds: string[]) => void;
  toggleEnvironmentSelection: (environmentId: string) => void;
  setLoadingEnvironments: (loading: boolean) => void;
  initializeVariableForEnvironments: (
    variableId: string,
    environments: DataEnvironment[],
  ) => void;

  // Template and generation actions
  setSelectedTemplate: (templateId: string | null) => void;
  generateFromTemplate: (template: DataTemplate) => void;
  generateBasicVariables: () => void;

  // UI actions
  setActiveTab: (tab: string) => void;
  setShowVariableValues: (show: boolean) => void;
  setShowEnvironmentSelector: (show: boolean) => void;
  setGenerating: (generating: boolean) => void;
  setSaving: (saving: boolean) => void;

  // Constraint editing actions
  setEditingVariable: (variable: TestDataVariable | null) => void;
  setTempConstraints: (constraints: ValueConstraints) => void;
  updateTempConstraint: (key: keyof ValueConstraints, value: any) => void;

  // Utility actions
  reset: () => void;
  resetFormAndVariables: () => void;
  getVariableValue: (variableId: string, environmentId: string) => string;

  // Actions for loading a single data set
  setLoadingDataSet: (loading: boolean) => void;
  setLoadDataSetError: (error: string | null) => void;

  // Clear persisted state for store manager
  clearPersistedState: () => void;
}

// Combined store type
type TestDataSetsStore = TestDataSetsState & TestDataSetsActions;

// Initial state
const initialState: TestDataSetsState = {
  formData: {
    name: "",
    description: "",
    tags: [],
    aiPrompt: "",
    environment: "development",
  },
  variables: [],
  activeEnvironmentId: "",
  userEnvironments: [],
  selectedEnvironments: [],
  isLoadingEnvironments: false,
  activeTab: "basic-info",
  showVariableValues: true,
  showEnvironmentSelector: true,
  selectedTemplate: null,
  isGenerating: false,
  isSaving: false,
  editingVariable: null,
  tempConstraints: {},
  newTag: "",

  // Initial state for loading a single data set
  isLoadingDataSet: false,
  loadDataSetError: null,
};

// Create the store
export const useTestDataSetsStore = create<TestDataSetsStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Form actions
        updateFormData: (data) =>
          set((state) => {
            Object.assign(state.formData, data);
          }),

        addTag: (tag) =>
          set((state) => {
            if (tag.trim() && !state.formData.tags.includes(tag.trim())) {
              state.formData.tags.push(tag.trim());
            }
          }),

        removeTag: (tag) =>
          set((state) => {
            state.formData.tags = state.formData.tags.filter((t) => t !== tag);
          }),

        setNewTag: (tag) =>
          set((state) => {
            state.newTag = tag;
          }),

        // Variable actions
        addVariable: () =>
          set((state) => {
            const newVariable: TestDataVariable = {
              id: `var-${Date.now()}`,
              name: "",
              type: "string",
              description: "",
              isRequired: true,
              environmentValues: {},
            };

            // Initialize empty values for all environments
            if (Array.isArray(state.userEnvironments)) {
              state.userEnvironments.forEach((env) => {
                newVariable.environmentValues[env.id] = "";
              });
            }

            state.variables.push(newVariable);
          }),

        removeVariable: (id) =>
          set((state) => {
            state.variables = state.variables.filter((v) => v.id !== id);
          }),

        duplicateVariable: (variable) =>
          set((state) => {
            const newVariable: TestDataVariable = {
              ...variable,
              id: `var-${Date.now()}`,
              name: `${variable.name}_copy`,
              environmentValues: { ...variable.environmentValues },
            };
            state.variables.push(newVariable);
          }),

        updateVariable: (id, updates) =>
          set((state) => {
            const variable = state.variables.find((v) => v.id === id);
            if (variable) {
              Object.assign(variable, updates);
            }
          }),

        updateVariableValue: (variableId, environmentId, value) =>
          set((state) => {
            const variable = state.variables.find((v) => v.id === variableId);
            if (variable) {
              variable.environmentValues[environmentId] = value;
            }
          }),

        setVariables: (variables) =>
          set((state) => {
            // Ensure state.variables is always an array
            state.variables = Array.isArray(variables) ? variables : [];

            // Initialize environment values for all environments if missing
            if (Array.isArray(state.userEnvironments)) {
              state.variables.forEach((variable) => {
                // Ensure variable is an object and has environmentValues
                if (
                  typeof variable === "object" &&
                  variable !== null &&
                  variable.environmentValues
                ) {
                  state.userEnvironments.forEach((env) => {
                    if (
                      env &&
                      env.id &&
                      !(env.id in variable.environmentValues)
                    ) {
                      variable.environmentValues[env.id] = "";
                    }
                  });
                } else if (
                  typeof variable === "object" &&
                  variable !== null &&
                  !variable.environmentValues
                ) {
                  // If environmentValues is missing on a variable, initialize it.
                  (variable as TestDataVariable).environmentValues = {};
                  state.userEnvironments.forEach((env) => {
                    if (env && env.id) {
                      (variable as TestDataVariable).environmentValues[env.id] =
                        "";
                    }
                  });
                }
              });
            }
          }),

        // Environment actions
        setActiveEnvironment: (environmentId) =>
          set((state) => {
            // Only update if different to prevent unnecessary re-renders
            if (state.activeEnvironmentId !== environmentId) {
              state.activeEnvironmentId = environmentId;
            }
          }),

        setUserEnvironments: (environments) =>
          set((state) => {
            // Ensure environments is an array
            const safeEnvironments = Array.isArray(environments)
              ? environments
              : [];
            state.userEnvironments = safeEnvironments;

            // Initialize environment values for existing variables
            if (safeEnvironments.length > 0) {
              state.variables.forEach((variable) => {
                safeEnvironments.forEach((env) => {
                  if (
                    env &&
                    env.id &&
                    !(env.id in variable.environmentValues)
                  ) {
                    variable.environmentValues[env.id] = "";
                  }
                });
              });

              // Set first environment as active if none selected
              if (!state.activeEnvironmentId && safeEnvironments[0]?.id) {
                state.activeEnvironmentId = safeEnvironments[0].id;
              }

              // Initialize selected environments with all active environments by default
              if (state.selectedEnvironments.length === 0) {
                state.selectedEnvironments = safeEnvironments
                  .filter((env) => env.isActive)
                  .map((env) => env.id);
              }
            }
          }),

        setSelectedEnvironments: (environmentIds) =>
          set((state) => {
            state.selectedEnvironments = [...environmentIds];
          }),

        toggleEnvironmentSelection: (environmentId) =>
          set((state) => {
            const currentSelected = state.selectedEnvironments;
            if (currentSelected.includes(environmentId)) {
              state.selectedEnvironments = currentSelected.filter(
                (id) => id !== environmentId,
              );
            } else {
              state.selectedEnvironments = [...currentSelected, environmentId];
            }
          }),

        setLoadingEnvironments: (loading) =>
          set((state) => {
            state.isLoadingEnvironments = loading;
          }),

        initializeVariableForEnvironments: (variableId, environments) =>
          set((state) => {
            const variable = state.variables.find((v) => v.id === variableId);
            if (variable) {
              environments.forEach((env) => {
                if (!(env.id in variable.environmentValues)) {
                  variable.environmentValues[env.id] = "";
                }
              });
            }
          }),

        // Template and generation actions
        setSelectedTemplate: (templateId) =>
          set((state) => {
            state.selectedTemplate = templateId;
          }),

        generateFromTemplate: (template) =>
          set((state) => {
            const newVariables: TestDataVariable[] = template.variables.map(
              (v, index) => ({
                id: `var-${Date.now()}-${index}`,
                name: v.name,
                type: v.type,
                description: v.description,
                isRequired: true,
                environmentValues: {},
              }),
            );

            // Initialize empty values for all environments
            if (Array.isArray(state.userEnvironments)) {
              newVariables.forEach((variable) => {
                state.userEnvironments.forEach((env) => {
                  variable.environmentValues[env.id] = "";
                });
              });
            }

            state.variables = newVariables;
          }),

        generateBasicVariables: () =>
          set((state) => {
            const basicVariables: TestDataVariable[] = [
              {
                id: `var-${Date.now()}-1`,
                name: "userName",
                type: "string",
                description: "User's full name",
                isRequired: true,
                environmentValues: {},
              },
              {
                id: `var-${Date.now()}-2`,
                name: "userEmail",
                type: "email",
                description: "User's email address",
                isRequired: true,
                environmentValues: {},
              },
              {
                id: `var-${Date.now()}-3`,
                name: "isActive",
                type: "boolean",
                description: "User account status",
                isRequired: true,
                environmentValues: {},
              },
            ];

            // Initialize empty values for all environments
            if (Array.isArray(state.userEnvironments)) {
              basicVariables.forEach((variable) => {
                state.userEnvironments.forEach((env) => {
                  variable.environmentValues[env.id] = "";
                });
              });
            }

            state.variables = basicVariables;
          }),

        // UI actions
        setActiveTab: (tab) =>
          set((state) => {
            state.activeTab = tab;
          }),

        setShowVariableValues: (show) =>
          set((state) => {
            state.showVariableValues = show;
          }),

        setShowEnvironmentSelector: (show) =>
          set((state) => {
            state.showEnvironmentSelector = show;
          }),

        setGenerating: (generating) =>
          set((state) => {
            state.isGenerating = generating;
          }),

        setSaving: (saving) =>
          set((state) => {
            state.isSaving = saving;
          }),

        // Constraint editing actions
        setEditingVariable: (variable) =>
          set((state) => {
            state.editingVariable = variable;
            state.tempConstraints = variable?.constraints || {};
          }),

        setTempConstraints: (constraints) =>
          set((state) => {
            state.tempConstraints = constraints;
          }),

        updateTempConstraint: (key, value) =>
          set((state) => {
            state.tempConstraints[key] = value;
          }),

        // Utility actions
        reset: () => set(() => ({ ...initialState })),

        // Clear persisted state for store manager
        clearPersistedState: () => {
          set(() => ({ ...initialState }));

          if (typeof window !== "undefined") {
            try {
              companyAwareStorage.removeItem("test-data-sets-store");
            } catch (error) {
              console.warn(
                "Failed to clear test data sets persisted state:",
                error,
              );
            }
          }
        },

        resetFormAndVariables: () =>
          set((state) => {
            // Reset form data to initial state
            state.formData = {
              name: "",
              description: "",
              tags: [],
              aiPrompt: "",
              environment: "development",
            };

            // Clear all variables
            state.variables = [];

            // Reset selected environments to all active environments
            if (Array.isArray(state.userEnvironments)) {
              state.selectedEnvironments = state.userEnvironments
                .filter((env) => env.isActive)
                .map((env) => env.id);
            }

            // Reset UI state but keep environment data
            state.activeTab = "basic-info";
            state.selectedTemplate = null;
            state.isGenerating = false;
            state.isSaving = false;
            state.editingVariable = null;
            state.tempConstraints = {};
            state.newTag = "";
          }),

        getVariableValue: (variableId, environmentId) => {
          const variable = get().variables.find((v) => v.id === variableId);
          return variable?.environmentValues[environmentId] || "";
        },

        // Implementations for new actions
        setLoadingDataSet: (loading) =>
          set((state) => {
            state.isLoadingDataSet = loading;
          }),

        setLoadDataSetError: (error) =>
          set((state) => {
            state.loadDataSetError = error;
          }),
      })),
      {
        name: "test-data-sets-store",
        storage: companyAwareStorage,
        partialize: (state) => ({
          formData: state.formData,
          variables: state.variables,
          activeEnvironmentId: state.activeEnvironmentId,
          userEnvironments: state.userEnvironments,
          selectedEnvironments: state.selectedEnvironments,
          activeTab: state.activeTab,
          showVariableValues: state.showVariableValues,
          showEnvironmentSelector: state.showEnvironmentSelector,
          selectedTemplate: state.selectedTemplate,
          // isLoadingDataSet and loadDataSetError are typically transient and not persisted.
        }),
      },
    ),
    { name: "TestDataSetsStore" },
  ),
);

// Selectors for computed values
export const useTestDataSetsSelectors = () => {
  return {
    getVariablesWithCurrentValues: () => {
      const currentState = useTestDataSetsStore.getState(); // Get fresh state at the moment of call
      if (!currentState.variables || !Array.isArray(currentState.variables)) {
        return []; // Return empty array if variables is not an array to prevent .map error
      }
      return currentState.variables.map((variable) => ({
        ...variable,
        value:
          variable.environmentValues && currentState.activeEnvironmentId
            ? variable.environmentValues[currentState.activeEnvironmentId] || ""
            : "",
      }));
    },

    getActiveEnvironment: () => {
      const currentState = useTestDataSetsStore.getState();
      if (!Array.isArray(currentState.userEnvironments)) return null;
      return (
        currentState.userEnvironments.find(
          (env) => env.id === currentState.activeEnvironmentId,
        ) || null
      );
    },

    canSave: () => {
      const currentState = useTestDataSetsStore.getState();
      const variablesExistAndPopulated =
        currentState.variables &&
        Array.isArray(currentState.variables) &&
        currentState.variables.length > 0;
      return !!currentState.formData.name && variablesExistAndPopulated;
    },

    getVariable: (id: string) => {
      const currentState = useTestDataSetsStore.getState();
      if (!currentState.variables || !Array.isArray(currentState.variables))
        return undefined;
      return currentState.variables.find((v) => v.id === id);
    },

    getVariableEnvironmentValues: (variableId: string) => {
      const currentState = useTestDataSetsStore.getState();
      if (!currentState.variables || !Array.isArray(currentState.variables))
        return {};
      const variable = currentState.variables.find((v) => v.id === variableId);
      return variable?.environmentValues || {};
    },

    getEditingVariableWithValue: () => {
      const currentState = useTestDataSetsStore.getState();
      const editingVar = currentState.editingVariable;
      if (!editingVar) {
        return null;
      }
      const value =
        editingVar.environmentValues && currentState.activeEnvironmentId
          ? editingVar.environmentValues[currentState.activeEnvironmentId] || ""
          : "";
      return {
        ...editingVar,
        value: value,
      };
    },
  };
};

// Register this store with the store manager for automatic cleanup
if (typeof window !== "undefined") {
  storeManager.registerStore({
    clearPersistedState: () =>
      useTestDataSetsStore.getState().clearPersistedState(),
  });
}
