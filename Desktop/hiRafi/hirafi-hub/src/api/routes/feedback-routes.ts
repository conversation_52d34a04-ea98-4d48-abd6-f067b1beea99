/**
 * Feedback API Routes
 * Kullanıcı geri bildirimleri için API rotaları
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import {
  createFeedback,
  getAllFeedback,
  getFeedbackById,
  updateFeedbackStatus
} from '../../services/mongo/feedbackService.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = Router();

/**
 * POST /api/feedback
 * Yeni geri bildirim oluşturur
 */
router.post('/', authenticate as any, async (req: Request, res: Response) => {
  try {
    const feedbackData = req.body;

    logger.info(`[API] POST /feedback - Creating new feedback`);

    // Gerekli alanları kontrol et
    if (!feedbackData || !feedbackData.feedback || !feedbackData.subject) {
      return res.status(400).json({
        success: false,
        error: 'Feedback ve konu alanı zorunludur'
      });
    }

    // MongoDB servisinden geri bildirim oluştur
    const result = await createFeedback({
      userId: feedbackData.userId,
      email: feedbackData.email,
      subject: feedbackData.subject,
      feedback: feedbackData.feedback,
      screenshot: feedbackData.screenshot
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.status(201).json({
      success: true,
      feedbackId: result.data?.id, // Use data.id instead of feedbackId
      message: 'Geri bildiriminiz için teşekkürler'
    });
  } catch (error: any) {
    logger.error(`API Error in POST /feedback: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/feedback
 * Tüm geri bildirimleri listeler (sadece admin kullanıcıları için)
 */
router.get('/', authenticate as any, async (req: Request, res: Response) => {
  try {
    const user = req.user;

    // Admin kontrolü
    if (!user?.role || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Bu işlem için admin yetkisi gereklidir'
      });
    }

    const limit = parseInt(req.query.limit?.toString() || '10');
    const skip = parseInt(req.query.skip?.toString() || '0');
    const status = req.query.status?.toString() as 'new' | 'read' | 'in-progress' | 'resolved' | 'closed' | undefined;


    // MongoDB servisinden tüm geri bildirimleri çek
    const result = await getAllFeedback({
      limit,
      offset: skip, // Use offset instead of skip
      status,
      excludeScreenshotData: true // Use excludeScreenshotData instead of excludeScreenshot
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      count: result.count || 0,
      feedback: result.data // Use data instead of feedback
    });
  } catch (error: any) {
    logger.error(`API Error in GET /feedback: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/feedback/:feedbackId
 * Tek bir geri bildirimi ID'ye göre getirir (sadece admin kullanıcıları için)
 */
router.get('/:feedbackId', authenticate as any, async (req: Request, res: Response) => {
  try {
    const { feedbackId } = req.params;
    const user = req.user;

    // Admin kontrolü
    if (!user?.role || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Bu işlem için admin yetkisi gereklidir'
      });
    }

    // MongoDB servisinden geri bildirimi getir
    const includeScreenshot = req.query.includeScreenshot === 'true';
    const result = await getFeedbackById(feedbackId, !includeScreenshot);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Geri bildirim bulunamadı: ${feedbackId}`
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      data: {
        feedback: result.data
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /feedback/${req.params.feedbackId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * PUT /api/feedback/:feedbackId/status
 * Geri bildirim durumunu günceller (sadece admin kullanıcıları için)
 */
router.put('/:feedbackId/status', authenticate as any, async (req: Request, res: Response) => {
  try {
    const { feedbackId } = req.params;
    const { status } = req.body;
    const user = req.user;

    // Admin kontrolü
    if (!user?.role || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Bu işlem için admin yetkisi gereklidir'
      });
    }

    // Status değerini kontrol et
    const validStatuses = ['new', 'read', 'in-progress', 'resolved', 'closed'];
    if (!status || !validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir durum değeri girin (new, read, in-progress, resolved, closed)'
      });
    }

    // MongoDB servisinden geri bildirim durumunu güncelle
    const result = await updateFeedbackStatus(feedbackId, status as any);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Geri bildirim bulunamadı: ${feedbackId}`
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message,
      feedback: result.data // Use data instead of feedback
    });
  } catch (error: any) {
    logger.error(`API Error in PUT /feedback/${req.params.feedbackId}/status: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;