"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import Papa from "papaparse"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/lib/utils/toast-utils"
import { FileUp, Upload, Loader2, FileText, Check, AlertCircle, ChevronDown, ChevronUp, X } from "lucide-react"
import { useScenarioManager } from "@/hooks/useScenarioManager"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useAuth } from "@/lib/api/auth"
import { importSingleScenario, parseScenarios } from "@/lib/api/scenario-api"

interface ImportScenariosDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete?: () => void
}

// Başlık adına göre değer alma yardımcı fonksiyonu
const getValueByHeader = (row: string[], headers: string[], headerName: string): string => {
  const index = headers.findIndex(h => h.toLowerCase() === headerName.toLowerCase())
  return index !== -1 ? row[index] : ''
}

// JSON formatını düzgün gösterme yardımcı fonksiyonu
const formatJsonPreview = (jsonString: string): React.ReactNode => {
  if (!jsonString) return '-'

  try {
    // JSON string'i parse et
    const parsed = JSON.parse(jsonString)

    // Düzgün formatlanmış JSON string'i oluştur
    const formatted = JSON.stringify(parsed, null, 2)

    // İlk 300 karakteri göster
    const preview = formatted.length > 300 ? formatted.substring(0, 300) + '...' : formatted

    // Renklendirme ve biçimlendirme için JSX döndür
    return (
      <pre className="text-xs m-0 p-0">
        {preview.split('\n').map((line, i) => {
          // Anahtar ve değerleri renklendirme
          const coloredLine = line
            // Anahtarları renklendirme
            .replace(/"([^"]+)"\s*:/g, '<span class="text-blue-500">"$1"</span>:')
            // String değerleri renklendirme
            .replace(/:\s*"([^"]*)"/g, ': <span class="text-green-500">"$1"</span>')
            // Sayıları renklendirme
            .replace(/:\s*([0-9]+)/g, ': <span class="text-orange-500">$1</span>')
            // Boolean değerleri renklendirme
            .replace(/:\s*(true|false)/g, ': <span class="text-purple-500">$1</span>')

          return (
            <div key={i} dangerouslySetInnerHTML={{ __html: coloredLine }} />
          )
        })}
      </pre>
    )
  } catch (e) {
    // JSON parse hatası durumunda orijinal string'i göster
    return (
      <pre className="text-xs m-0 p-0 text-gray-500">
        {jsonString.length > 300 ? jsonString.substring(0, 300) + '...' : jsonString}
      </pre>
    )
  }
}

// Steps sayısını gösteren yardımcı fonksiyon
const getStepsCount = (stepsJson: string): React.ReactNode => {
  if (!stepsJson) return '-'

  try {
    // JSON string'i parse et
    const parsed = JSON.parse(stepsJson)

    // Eğer bir dizi ise, eleman sayısını göster
    if (Array.isArray(parsed)) {
      return (
        <div className="flex items-center gap-2">
          <span className="font-medium">{parsed.length}</span>
          <span className="text-gray-500">steps</span>
        </div>
      )
    }

    // Eğer bir nesne ise ve steps alanı varsa
    if (parsed && typeof parsed === 'object' && parsed.steps) {
      const stepsCount = Array.isArray(parsed.steps) ? parsed.steps.length : 0
      return (
        <div className="flex items-center gap-2">
          <span className="font-medium">{stepsCount}</span>
          <span className="text-gray-500">steps</span>
        </div>
      )
    }

    // Diğer durumlar için
    return (
      <div className="flex items-center gap-2">
        <span className="font-medium">Unknown</span>
        <span className="text-gray-500">format</span>
      </div>
    )
  } catch (e) {
    // JSON parse hatası durumunda
    return (
      <div className="flex items-center gap-2">
        <span className="font-medium">Invalid</span>
        <span className="text-gray-500">JSON</span>
      </div>
    )
  }
}

export function ImportScenariosDialog({ open, onOpenChange, onImportComplete }: ImportScenariosDialogProps) {
  const { user } = useAuth()
  const [isImporting, setIsImporting] = useState(false)
  const [selectedFolder, setSelectedFolder] = useState<string>("uncategorized")
  const [useDefaultFolder, setUseDefaultFolder] = useState<boolean>(true)
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvContent, setCsvContent] = useState<string>("")
  const [csvHeaders, setCsvHeaders] = useState<string[]>([])
  const [csvRows, setCsvRows] = useState<string[][]>([])
  const [previewMode, setPreviewMode] = useState<"file" | "preview" | "importing" | "results">("file")
  const [importResults, setImportResults] = useState<any>(null)
  const [selectedScenarios, setSelectedScenarios] = useState<number[]>([])
  const [selectAll, setSelectAll] = useState(true)
  const [invalidRows, setInvalidRows] = useState<number[]>([])
  const [importProgress, setImportProgress] = useState(0)
  const [importedCount, setImportedCount] = useState(0)
  const [totalToImport, setTotalToImport] = useState(0)
  const [importError, setImportError] = useState<string | null>(null)
  const [scenariosCollapsed, setScenariosCollapsed] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Klasörleri çek - centralized hook kullan
  const { folders, foldersLoading } = useScenarioManager({
    folderLimit: 50,
    autoFetch: true
  })

  // Dialog kapandığında state'i sıfırla
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        setCsvFile(null)
        setCsvContent("")
        setCsvHeaders([])
        setCsvRows([])
        setPreviewMode("file")
        setImportResults(null)
        setSelectedFolder("")
      }, 300)
    }
  }, [open])

  // CSV dosyasını oku
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setCsvFile(file)

    const reader = new FileReader()
    reader.onload = (event) => {
      const content = event.target?.result as string
      setCsvContent(content)

      // CSV içeriğini Papa Parse ile parse et
      Papa.parse(content, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => header.trim(),
        transform: (value) => value.trim(),
        dynamicTyping: false, // String olarak bırak
        comments: '#', // # ile başlayan satırları yorum olarak kabul et
        complete: (results) => {
          if (results.data && results.data.length > 0) {
            // Başlıkları al
            const headers = results.meta.fields || []
            setCsvHeaders(headers)

            // Veri satırlarını düzgün formata çevir
            const dataRows = results.data.map((row: any) => {
              return headers.map(header => row[header] || '')
            })
            setCsvRows(dataRows)

            // Zorunlu alanları kontrol et ve geçersiz satırları işaretle - case insensitive
            const nameIndex = headers.findIndex(h => h.trim().toLowerCase() === 'name')
            const stepsIndex = headers.findIndex(h => h.trim().toLowerCase() === 'steps')



            const invalidRowIndices = dataRows.reduce((acc, row, index) => {
              const hasName = nameIndex !== -1 && row[nameIndex] && row[nameIndex].trim() !== ''
              const hasSteps = stepsIndex !== -1 && row[stepsIndex] && row[stepsIndex].trim() !== ''

              if (!hasName || !hasSteps) {
                acc.push(index)
              }
              return acc
            }, [] as number[])

            setInvalidRows(invalidRowIndices)

            // Tüm geçerli senaryoları varsayılan olarak seç
            if (selectAll) {
              const validRows = dataRows.map((_, index) => index).filter(index => !invalidRowIndices.includes(index))
              setSelectedScenarios(validRows)
            } else {
              setSelectedScenarios([])
            }

            // Preview moduna geç
            setPreviewMode("preview")
          }
        },
        error: (error) => {
          toast.error(`CSV parse error: ${error.message}`)
        }
      })
    }
    reader.readAsText(file)
  }

  // Senaryo seçim durumunu değiştir
  const toggleScenarioSelection = (index: number) => {
    // Geçersiz satırlar seçilemez
    if (invalidRows.includes(index)) {
      toast.error("This scenario is missing required fields and cannot be imported")
      return
    }

    setSelectedScenarios(prev => {
      if (prev.includes(index)) {
        return prev.filter(i => i !== index)
      } else {
        return [...prev, index]
      }
    })
  }

  // Tüm senaryoları seç/kaldır
  const toggleSelectAll = () => {
    setSelectAll(!selectAll)
    if (!selectAll) {
      // Tüm geçerli senaryoları seç
      const validRows = csvRows.map((_, index) => index).filter(index => !invalidRows.includes(index))
      setSelectedScenarios(validRows)
    } else {
      // Tüm seçimleri kaldır
      setSelectedScenarios([])
    }
  }

  // Import işlemi
  const handleImport = async () => {
    if (!csvContent || !selectedFolder) {
      toast.error("Please select a CSV file and a folder")
      return
    }

    if (selectedScenarios.length === 0) {
      toast.error("Please select at least one scenario to import")
      return
    }

    setIsImporting(true)
    setImportProgress(0)
    setImportedCount(0)
    setTotalToImport(selectedScenarios.length)
    setImportError(null)
    setPreviewMode("importing")

    try {
      // Senaryoları parse et
      const parseResult = parseScenarios(
        csvContent,
        selectedScenarios,
        user?.id || "",
        user?.teamId || "",
        user?.companyId || "",
        useDefaultFolder ? "uncategorized" : selectedFolder
      )

      if (!parseResult.success) {
        setImportError(parseResult.error || "Failed to parse scenarios")
        toast.error(parseResult.error || "Failed to parse scenarios")
        setIsImporting(false)
        return
      }

      const scenarios = parseResult.scenarios
      const importedScenarios = []

      // Senaryoları tek tek import et
      for (let i = 0; i < scenarios.length; i++) {
        try {
          const result = await importSingleScenario(scenarios[i])

          if (result.success) {
            // Extract scenario data from new response format
            const scenarioData = result.data?.scenario || result.scenario || scenarios[i]
            importedScenarios.push(scenarioData)
          }

          // İlerlemeyi güncelle
          setImportedCount(i + 1)
          setImportProgress(Math.round(((i + 1) / scenarios.length) * 100))
        } catch (error) {
          // Scenario import error handled silently
        }
      }

      // Sonuçları göster
      setImportResults({
        success: true,
        message: `${importedScenarios.length} of ${scenarios.length} scenarios imported successfully`,
        scenarios: importedScenarios
      })

      setPreviewMode("results")
      toast.success(`${importedScenarios.length} of ${scenarios.length} scenarios imported successfully`)

      // Callback'i çağır
      if (onImportComplete) {
        onImportComplete()
      }

      // Hata mesajı gösterilse bile import başarılı oldu
    } catch (error) {
      setImportError("An error occurred while importing scenarios")
      toast.error("An error occurred while importing scenarios")
    } finally {
      setIsImporting(false)
    }
  }

  // Dosya seçme butonunu tetikle
  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[850px] max-h-[90vh] border-0 shadow-xl bg-white dark:bg-gray-900 rounded-xl overflow-hidden flex flex-col">
        <DialogHeader className="pb-4 border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/10 dark:to-blue-900/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/30">
                <FileUp className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">Import Scenarios</DialogTitle>
                <DialogDescription className="text-gray-500 dark:text-gray-400">
                  Import scenarios from a CSV file with the same format as exported scenarios.
                </DialogDescription>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="py-4 overflow-y-auto flex-grow">
          {previewMode === "importing" && (
            <div className="space-y-6 py-8">
              <div className="text-center">
                <div className="inline-flex items-center justify-center p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
                  <Loader2 className="h-8 w-8 text-blue-600 dark:text-blue-400 animate-spin" />
                </div>
                <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">Importing Scenarios</h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Please wait while your scenarios are being imported...
                </p>
              </div>

              <div className="space-y-2 max-w-md mx-auto">
                <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-1">
                  <span>Progress</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="h-2" />
                <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-2">
                  Imported {importedCount} of {totalToImport} scenarios
                </p>
              </div>

              {importError && (
                <Alert className="bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 mt-4">
                  <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                  <AlertTitle className="text-red-800 dark:text-red-300">Import Error</AlertTitle>
                  <AlertDescription className="text-red-700 dark:text-red-400">
                    {importError}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {previewMode === "file" && (
            <div className="space-y-4">
              <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-indigo-200 dark:border-indigo-800/50 rounded-lg bg-indigo-50/50 dark:bg-indigo-900/10 transition-all duration-200 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 group">
                <input
                  type="file"
                  ref={fileInputRef}
                  accept=".csv"
                  className="hidden"
                  onChange={handleFileChange}
                />
                <div className="mb-4 p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/30 group-hover:scale-110 transition-transform duration-200">
                  <Upload className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Upload CSV File</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-4">
                  Drag and drop your CSV file here, or click to browse
                </p>
                <Button
                  onClick={triggerFileInput}
                  className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200 border-0"
                >
                  <FileUp className="mr-2 h-4 w-4" />
                  Select CSV File
                </Button>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="useDefaultFolder"
                    checked={useDefaultFolder}
                    onCheckedChange={(checked) => {
                      setUseDefaultFolder(!!checked)
                      if (checked) {
                        setSelectedFolder("uncategorized")
                      }
                    }}
                  />
                  <Label htmlFor="useDefaultFolder" className="text-sm font-medium">
                    Import to default folder (Uncategorized)
                  </Label>
                </div>

                {!useDefaultFolder && (
                  <div className="space-y-2">
                    <Label htmlFor="folder">Select Folder</Label>
                    <Select
                      value={selectedFolder}
                      onValueChange={setSelectedFolder}
                      disabled={foldersLoading}
                    >
                      <SelectTrigger id="folder" className="w-full">
                        <SelectValue placeholder="Select a folder" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="uncategorized">
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                            Uncategorized
                          </div>
                        </SelectItem>
                        {folders.map(folderItem => (
                          <SelectItem key={folderItem.id} value={folderItem.id}>
                            <div className="flex items-center gap-2">
                              <div className={`h-2 w-2 rounded-full bg-${folderItem.color || 'gray'}-500`}></div>
                              {folderItem.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </div>
          )}

          {previewMode === "preview" && csvFile && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Preview</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {csvRows.length} scenarios found in {csvFile.name}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2 flex items-center gap-1"
                    onClick={() => setScenariosCollapsed(!scenariosCollapsed)}
                  >
                    {scenariosCollapsed ? (
                      <>
                        <ChevronDown className="h-4 w-4" />
                        Show Scenarios
                      </>
                    ) : (
                      <>
                        <ChevronUp className="h-4 w-4" />
                        Hide Scenarios
                      </>
                    )}
                  </Button>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    {csvFile.name}
                  </Badge>
                </div>
              </div>

              {!scenariosCollapsed && (
                <div className="border border-indigo-100 dark:border-indigo-800/50 rounded-md overflow-hidden shadow-sm max-h-[400px] overflow-y-auto">
                  <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectAll}
                          onCheckedChange={toggleSelectAll}
                          aria-label="Select all scenarios"
                        />
                      </TableHead>
                      <TableHead className="w-1/6">
                        Name
                        <span className="ml-1 text-red-500">*</span>
                      </TableHead>
                      <TableHead className="w-1/6">
                        Description
                      </TableHead>
                      <TableHead className="w-1/12">
                        Tags
                      </TableHead>
                      <TableHead className="w-7/12">
                        Steps
                        <span className="ml-1 text-red-500">*</span>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {csvRows.map((row, rowIndex) => (
                      <TableRow
                        key={rowIndex}
                        className={`
                          ${selectedScenarios.includes(rowIndex) ? "bg-blue-50 dark:bg-blue-900/10" : ""}
                          ${invalidRows.includes(rowIndex) ? "bg-red-50 dark:bg-red-900/10" : ""}
                        `}
                      >
                        <TableCell className="w-12">
                          <Checkbox
                            checked={selectedScenarios.includes(rowIndex)}
                            onCheckedChange={() => toggleScenarioSelection(rowIndex)}
                            aria-label={`Select scenario ${rowIndex + 1}`}
                            disabled={invalidRows.includes(rowIndex)}
                          />
                        </TableCell>
                        <TableCell className="max-w-[150px] truncate">
                          {getValueByHeader(row, csvHeaders, 'name') || "-"}
                        </TableCell>
                        <TableCell className="max-w-[150px] truncate">
                          {getValueByHeader(row, csvHeaders, 'description') || "-"}
                        </TableCell>
                        <TableCell className="max-w-[100px] truncate">
                          {getValueByHeader(row, csvHeaders, 'tags') || "-"}
                        </TableCell>
                        <TableCell className="overflow-hidden">
                          <div className="flex items-center justify-between">
                            <div className="bg-gray-50 dark:bg-gray-800 py-1 px-3 rounded-full border border-gray-200 dark:border-gray-700">
                              {getStepsCount(getValueByHeader(row, csvHeaders, 'steps'))}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 w-7 p-0 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                              title="View JSON"
                              onClick={() => {
                                // JSON içeriğini görmek için bir popup gösterilebilir
                                // TODO: Implement JSON viewer modal
                              }}
                            >
                              <FileText className="h-3.5 w-3.5 text-gray-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              )}

              <div className="flex justify-between items-center mt-2 text-sm text-gray-500 dark:text-gray-400">
                <div>
                  {selectedScenarios.length} of {csvRows.length - invalidRows.length} valid scenarios selected
                  {invalidRows.length > 0 && (
                    <span className="ml-2 text-red-500">
                      ({invalidRows.length} invalid scenarios found)
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {invalidRows.length > 0 && (
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-100 dark:bg-red-900/30 mr-1"></div>
                      <span>Missing required fields</span>
                    </div>
                  )}
                  <Button
                    variant="link"
                    onClick={toggleSelectAll}
                    className="h-auto p-0"
                  >
                    {selectAll ? "Deselect All" : "Select All Valid"}
                  </Button>
                </div>
              </div>

              {invalidRows.length > 0 && (
                <Alert className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border-red-200 dark:border-red-800 shadow-sm mt-2">
                  <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                  <AlertTitle className="text-red-800 dark:text-red-300">CSV Format Issue</AlertTitle>
                  <AlertDescription className="text-red-700 dark:text-red-400">
                    <div>Some scenarios are missing required fields (name or steps). Please check your CSV file format.</div>
                    <div className="mt-1">Make sure column names are exactly <strong>Name</strong> and <strong>Steps</strong> (case-insensitive).</div>
                    <div className="mt-1">If you exported this file from the system, it should be compatible. The steps field must be in valid JSON format.</div>
                    <div className="mt-1">Try selecting only valid scenarios or check the console for more details.</div>
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="useDefaultFolderPreview"
                    checked={useDefaultFolder}
                    onCheckedChange={(checked) => {
                      setUseDefaultFolder(!!checked)
                      if (checked) {
                        setSelectedFolder("uncategorized")
                      }
                    }}
                    disabled={isImporting}
                  />
                  <Label htmlFor="useDefaultFolderPreview" className="text-sm font-medium">
                    Import to default folder (Uncategorized)
                  </Label>
                </div>

                {!useDefaultFolder && (
                  <div className="space-y-2">
                    <Label htmlFor="folderPreview">Select Folder</Label>
                    <Select
                      value={selectedFolder}
                      onValueChange={setSelectedFolder}
                      disabled={foldersLoading || isImporting}
                    >
                      <SelectTrigger id="folderPreview" className="w-full">
                        <SelectValue placeholder="Select a folder" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="uncategorized">
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                            Uncategorized
                          </div>
                        </SelectItem>
                        {folders.map(folderItem => (
                          <SelectItem key={folderItem.id} value={folderItem.id}>
                            <div className="flex items-center gap-2">
                              <div className={`h-2 w-2 rounded-full bg-${folderItem.color || 'gray'}-500`}></div>
                              {folderItem.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <Alert className="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 border-amber-200 dark:border-amber-800 shadow-sm">
                <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                <AlertTitle className="text-amber-800 dark:text-amber-300">Important</AlertTitle>
                <AlertDescription className="text-amber-700 dark:text-amber-400">
                  <div>Make sure your CSV file has the correct format. The first row should contain headers, and each scenario must have the following required fields:</div>
                  <ul className="list-disc list-inside mt-2 ml-2 space-y-1">
                    <li className="font-medium">name - The name of the scenario</li>
                    <li className="font-medium">steps - The steps of the scenario (in JSON format)</li>
                  </ul>
                  <div className="mt-2">Other fields like description and tags are optional. System fields like createdAt, updatedAt, id will be ignored.</div>
                </AlertDescription>
              </Alert>
            </div>
          )}

          {previewMode === "results" && importResults && (
            <div className="space-y-4">
              <div className="flex items-center justify-center p-6 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="text-center">
                  <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <Check className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Import Successful</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {importResults.message || `${importResults.scenarios?.length || 0} scenarios imported successfully`}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    {selectedScenarios.length} scenarios were selected from the CSV file
                  </p>
                </div>
              </div>

              {importResults.scenarios && importResults.scenarios.length > 0 && (
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Imported Scenarios</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-2 flex items-center gap-1"
                      onClick={() => setScenariosCollapsed(!scenariosCollapsed)}
                    >
                      {scenariosCollapsed ? (
                        <>
                          <ChevronDown className="h-4 w-4" />
                          Show Details
                        </>
                      ) : (
                        <>
                          <ChevronUp className="h-4 w-4" />
                          Hide Details
                        </>
                      )}
                    </Button>
                  </div>

                  {!scenariosCollapsed && (
                    <div className="border border-green-100 dark:border-green-800/50 rounded-md overflow-hidden shadow-sm max-h-[400px] overflow-y-auto">
                      <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-1/6">Name</TableHead>
                        <TableHead className="w-1/6">Description</TableHead>
                        <TableHead className="w-1/12">Status</TableHead>
                        <TableHead className="w-7/12">Steps</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {importResults.scenarios.slice(0, 5).map((scenario: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium max-w-[150px] truncate">{scenario.name}</TableCell>
                          <TableCell className="max-w-[150px] truncate">{scenario.description || "-"}</TableCell>
                          <TableCell>
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                              {scenario.status || "active"}
                            </Badge>
                          </TableCell>
                          <TableCell className="overflow-hidden">
                            <div className="flex items-center justify-between">
                              <div className="bg-gray-50 dark:bg-gray-800 py-1 px-3 rounded-full border border-gray-200 dark:border-gray-700">
                                {scenario.steps && Array.isArray(scenario.steps) ? (
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{scenario.steps.length}</span>
                                    <span className="text-gray-500">steps</span>
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">0</span>
                                    <span className="text-gray-500">steps</span>
                                  </div>
                                )}
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-7 w-7 p-0 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                                title="View JSON"
                                onClick={() => {
                                  // JSON içeriğini görmek için bir popup gösterilebilir
                                  // TODO: Implement JSON viewer modal
                                }}
                              >
                                <FileText className="h-3.5 w-3.5 text-gray-500" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                      {importResults.scenarios.length > 5 && (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center text-sm text-gray-500">
                            {importResults.scenarios.length - 5} more scenarios imported...
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                      </Table>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="border-t border-gray-100 dark:border-gray-800 pt-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/50 dark:to-gray-800/50">
          {previewMode === "file" && (
            <Button
              variant="outline"
              onClick={onOpenChange.bind(null, false)}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
          )}

          {previewMode === "preview" && (
            <>
              <Button
                variant="outline"
                onClick={() => {
                  setCsvFile(null)
                  setCsvContent("")
                  setCsvHeaders([])
                  setCsvRows([])
                  setPreviewMode("file")
                }}
                className="w-full sm:w-auto"
                disabled={isImporting}
              >
                Back
              </Button>
              <Button
                onClick={handleImport}
                className="w-full sm:w-auto bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200 border-0"
                disabled={isImporting || selectedScenarios.length === 0}
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Importing...
                  </>
                ) : (
                  <>
                    <FileUp className="mr-2 h-4 w-4" />
                    Import Scenarios
                  </>
                )}
              </Button>
            </>
          )}

          {previewMode === "importing" && (
            <Button
              variant="outline"
              onClick={() => {
                if (confirm("Are you sure you want to cancel the import process? All progress will be lost.")) {
                  setIsImporting(false)
                  setPreviewMode("preview")
                }
              }}
              className="w-full sm:w-auto"
              disabled={false}
            >
              Cancel Import
            </Button>
          )}

          {previewMode === "results" && (
            <Button
              onClick={onOpenChange.bind(null, false)}
              className="w-full sm:w-auto bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-md hover:shadow-lg transition-all duration-200 border-0"
            >
              Done
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
