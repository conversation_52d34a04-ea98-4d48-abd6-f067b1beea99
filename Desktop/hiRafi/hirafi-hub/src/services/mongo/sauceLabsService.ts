/**
 * SauceLabs service module
 * Handles operations related to SauceLabs integration
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import FormData from 'form-data';
import {
  isMongoDBInitialized,
  pluginsCollection,
  companiesCollection,
  teamsCollection
} from './dbConnection.js';
import { ObjectId } from 'mongodb';
import {
  createSauceLabsError,
  createSauceLabsSuccess,
  validateSauceLabsParams,
  logSauceLabsOperation,
  SauceLabsResult
} from '../../utils/saucelabs-error-handler.js';

/**
 * Sanitize filename for SauceLabs Storage
 * Removes Turkish characters and special characters that might cause issues
 * @param fileName Original filename
 * @returns Sanitized filename
 */
function sanitizeFileName(fileName: string): string {
  // Turkish character mapping
  const turkishCharMap: { [key: string]: string } = {
    'ç': 'c', 'Ç': 'C',
    'ğ': 'g', 'Ğ': 'G',
    'ı': 'i', 'I': 'I',
    'İ': 'I', 'i': 'i',
    'ö': 'o', 'Ö': 'O',
    'ş': 's', 'Ş': 'S',
    'ü': 'u', 'Ü': 'U'
  };

  let sanitized = fileName;

  // Replace Turkish characters
  Object.keys(turkishCharMap).forEach(turkishChar => {
    const regex = new RegExp(turkishChar, 'g');
    sanitized = sanitized.replace(regex, turkishCharMap[turkishChar]);
  });

  // Remove or replace other special characters, keep only alphanumeric, dots, hyphens, and underscores
  sanitized = sanitized.replace(/[^a-zA-Z0-9.\-_]/g, '_');

  // Remove multiple consecutive underscores
  sanitized = sanitized.replace(/_+/g, '_');

  // Remove leading/trailing underscores
  sanitized = sanitized.replace(/^_+|_+$/g, '');

  // Ensure the file has an extension
  if (!sanitized.includes('.')) {
    const originalExt = fileName.split('.').pop();
    if (originalExt) {
      sanitized += '.' + originalExt;
    }
  }

  return sanitized;
}

/**
 * Verify SauceLabs connection
 * @param username SauceLabs username
 * @param accessKey SauceLabs access key
 * @param region SauceLabs region
 * @returns Promise resolving to verification result
 */
export async function verifySauceLabsConnection(
  username: string,
  accessKey: string,
  region: string = 'us-west-1'
): Promise<SauceLabsResult> {
  // Validate input parameters
  const validationError = validateSauceLabsParams(username, accessKey, region);
  if (validationError) {
    logSauceLabsOperation('connection_verification', username, region, false, validationError.details);
    return validationError;
  }

  try {
    const baseUrl = `https://api.${region}.saucelabs.com`;
    const response = await axios.get(`${baseUrl}/team-management/v1/users?username=${username}`, {
      auth: {
        username,
        password: accessKey
      },
      timeout: 30000 // 30 seconds timeout
    });

    if (response.status === 200 && response.data && response.data.results && response.data.results.length > 0) {
      logSauceLabsOperation('connection_verification', username, region, true, 'Connection verified successfully');
      return createSauceLabsSuccess('SauceLabs bağlantısı başarıyla doğrulandı', {
        username,
        region,
        userCount: response.data.results.length
      });
    } else {
      logSauceLabsOperation('connection_verification', username, region, false, 'No user data found in response');
      return createSauceLabsError(
        new Error('No valid user data found in SauceLabs response'),
        'connection_verification',
        region
      );
    }
  } catch (error: any) {
    logSauceLabsOperation('connection_verification', username, region, false, error.message);
    return createSauceLabsError(error, 'connection_verification', region);
  }
}

/**
 * Update SauceLabs configuration
 * @param companyId Company ID
 * @param teamId Team ID
 * @param config SauceLabs configuration
 * @param userId Optional, only for tracking who made the change
 * @returns Promise resolving to update result
 */
export async function updateSauceLabsConfig(
  companyId: string,
  teamId: string,
  config: {
    username: string;
    accessKey: string;
    region?: string;
  },
  userId?: string // Optional, only for tracking who made the change
) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!companyId) {
      return { 
        success: false, 
        message: 'Company ID is required' 
      };
    }

    logger.info(`Updating SauceLabs configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Verify the connection first
    const verificationResult = await verifySauceLabsConnection(
      config.username,
      config.accessKey,
      config.region
    );

    if (!verificationResult.success) {
      return {
        success: false,
        message: 'Failed to verify SauceLabs connection with provided credentials',
        error: verificationResult.error
      };
    }

    const now = new Date();

    // Create the SauceLabs plugin object
    const sauceLabsPlugin = {
      id: 'saucelabs',
      name: 'SauceLabs Integration',
      description: 'Integrate with SauceLabs for mobile device testing',
      active: true,
      config: {
        username: config.username,
        accessKey: config.accessKey,
        region: config.region || 'us-west-1',
        updatedAt: now,
        ...(userId && { configuredBy: userId }) // Track who configured this plugin
      },
      createdAt: now,
      updatedAt: now
    };

    // Look for existing plugin document with companyId and teamId
    const queryFilter: any = { companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    const existingPluginDoc = await pluginsCollection.findOne(queryFilter);

    if (!existingPluginDoc) {
      // Create new plugins entry for company/team
      const newPluginDoc: any = {
        companyId,
        plugins: [sauceLabsPlugin],
        createdAt: now,
        updatedAt: now
      };

      if (teamId) {
        newPluginDoc.teamId = teamId;
      }

      if (userId) {
        newPluginDoc.createdBy = userId; // Track who created this document
      }

      await pluginsCollection.insertOne(newPluginDoc);

      logger.info(`Created SauceLabs plugin for company: ${companyId}, team: ${teamId || 'N/A'}`);
      return {
        success: true,
        message: 'SauceLabs configuration created successfully'
      };
    }

    // Check if SauceLabs plugin already exists in the document
    const sauceLabsExists = existingPluginDoc.plugins.some((plugin: any) => plugin.id === 'saucelabs');

    if (sauceLabsExists) {
      // Update existing SauceLabs plugin
      const updateFilter: any = { 
        companyId,
        'plugins.id': 'saucelabs' 
      };
      if (teamId) {
        updateFilter.teamId = teamId;
      }

      const updateData: any = {
        'plugins.$.name': sauceLabsPlugin.name,
        'plugins.$.description': sauceLabsPlugin.description,
        'plugins.$.active': sauceLabsPlugin.active,
        'plugins.$.config': sauceLabsPlugin.config,
        'plugins.$.updatedAt': now,
        'updatedAt': now
      };

      if (userId) {
        updateData.lastUpdatedBy = userId; // Track who last updated this
      }

      const result = await pluginsCollection.updateOne(updateFilter, { $set: updateData });

      if (result.matchedCount === 0) {
        return {
          success: false,
          message: 'Failed to update SauceLabs plugin configuration'
        };
      }

      logger.info(`Updated SauceLabs plugin for company: ${companyId}, team: ${teamId || 'N/A'}`);
      return {
        success: true,
        message: 'SauceLabs configuration updated successfully'
      };
    }

    // Add SauceLabs plugin to existing plugins array
    const updateFilter: any = { companyId };
    if (teamId) {
      updateFilter.teamId = teamId;
    }

    const updateData: any = {
      'updatedAt': now
    };

    if (userId) {
      updateData.lastUpdatedBy = userId;
    }

    const result = await pluginsCollection.updateOne(
      updateFilter,
      {
        $push: { plugins: sauceLabsPlugin as any },
        $set: updateData
      } as any
    );

    if (!result.acknowledged) {
      return {
        success: false,
        message: 'Failed to add SauceLabs plugin'
      };
    }

    logger.info(`Added SauceLabs plugin for company: ${companyId}, team: ${teamId || 'N/A'}`);
    return {
      success: true,
      message: 'SauceLabs plugin added successfully'
    };
  } catch (error: any) {
    logger.error(`Error updating SauceLabs configuration: ${error.message}`);
    return {
      success: false,
      message: `Error updating SauceLabs configuration: ${error.message}`
    };
  }
}

/**
 * Get SauceLabs configuration
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to configuration result
 */
export async function getSauceLabsConfig(companyId: string, teamId?: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established',
        config: null,
        plugin: null
      };
    }

    if (!companyId) {
      return {
        success: false,
        message: 'Company ID is required',
        config: null,
        plugin: null
      };
    }

    logger.info(`Getting SauceLabs configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // First, try to find plugin document with companyId and teamId (if provided)
    const queryFilter: any = { companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    const pluginData = await pluginsCollection.findOne(queryFilter);

    if (pluginData && pluginData.plugins) {
      const sauceLabsPlugin = pluginData.plugins.find((plugin: any) => plugin.id === 'saucelabs');
      
      if (sauceLabsPlugin) {
        logger.info(`Found SauceLabs configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);
        return {
          success: true,
          message: 'SauceLabs configuration retrieved successfully',
          config: sauceLabsPlugin.config,
          plugin: sauceLabsPlugin
        };
      }
    }

    // If no team-specific config found and teamId was provided, try company-only
    if (teamId) {
      const companyOnlyPluginData = await pluginsCollection.findOne({ 
        companyId,
        teamId: { $exists: false }
      });

      if (companyOnlyPluginData && companyOnlyPluginData.plugins) {
        const sauceLabsPlugin = companyOnlyPluginData.plugins.find((plugin: any) => plugin.id === 'saucelabs');
        
        if (sauceLabsPlugin) {
          logger.info(`Found SauceLabs configuration for company: ${companyId} (company-level)`);
          return {
            success: true,
            message: 'SauceLabs configuration retrieved successfully',
            config: sauceLabsPlugin.config,
            plugin: sauceLabsPlugin
          };
        }
      }
    }

    logger.info(`No SauceLabs configuration found for company: ${companyId}, team: ${teamId || 'N/A'}`);
    return {
      success: true,
      message: 'No SauceLabs configuration found',
      config: null,
      plugin: null
    };
  } catch (error: any) {
    logger.error(`Error getting SauceLabs configuration: ${error.message}`);
    return {
      success: false,
      message: `Error getting SauceLabs configuration: ${error.message}`,
      config: null,
      plugin: null
    };
  }
}

/**
 * Delete SauceLabs configuration
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to delete result
 */
export async function deleteSauceLabsConfig(companyId: string, teamId?: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    if (!companyId) {
      return {
        success: false,
        message: 'Company ID is required'
      };
    }

    logger.info(`Deleting SauceLabs configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Build query filter
    const queryFilter: any = { companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    // Remove the saucelabs plugin from the plugins array
    const result = await pluginsCollection.updateOne(
      queryFilter,
      {
        $pull: { plugins: { id: 'saucelabs' } } as any,
        $set: { updatedAt: new Date() }
      }
    );

    if (result.modifiedCount > 0) {
      logger.info(`Successfully deleted SauceLabs configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);
      return {
        success: true,
        message: 'SauceLabs configuration deleted successfully'
      };
    } else {
      logger.warn(`No SauceLabs configuration found to delete for company: ${companyId}, team: ${teamId || 'N/A'}`);
      return {
        success: false,
        message: 'No SauceLabs configuration found to delete'
      };
    }
  } catch (error: any) {
    logger.error(`Error deleting SauceLabs configuration: ${error.message}`);
    return {
      success: false,
      message: `Error deleting SauceLabs configuration: ${error.message}`
    };
  }
}

/**
 * Get SauceLabs devices
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to devices result
 */
export async function getSauceLabsDevices(companyId: string, teamId?: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established',
        devices: []
      };
    }

    // Get the SauceLabs configuration
    const configResult = await getSauceLabsConfig(companyId, teamId);

    if (!configResult.success) {
      return {
        success: false,
        message: configResult.message || 'Failed to get SauceLabs configuration',
        devices: []
      };
    }

    if (!configResult.config) {
      return {
        success: false,
        message: 'SauceLabs configuration not found',
        devices: []
      };
    }

    const { username, accessKey, region } = configResult.config;
    // Use the updated API endpoint format
    const baseUrl = `https://api.${region || 'us-west-1'}.saucelabs.com`;

    // Get available devices from SauceLabs using the correct endpoint
    const response = await axios.get(`${baseUrl}/v1/rdc/devices`, {
      auth: {
        username,
        password: accessKey
      }
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: 'Failed to get devices from SauceLabs',
        devices: []
      };
    }

    // Also get device status information
    const statusResponse = await axios.get(`${baseUrl}/v1/rdc/devices/status`, {
      auth: {
        username,
        password: accessKey
      }
    });

    // Create a map of device descriptors to their availability status
    const deviceStatusMap = new Map();

    if (statusResponse.status === 200 && statusResponse.data && statusResponse.data.devices) {
      statusResponse.data.devices.forEach((device: any) => {
        if (device.descriptor) {
          deviceStatusMap.set(device.descriptor, {
            state: device.state,
            isAvailable: device.state === 'AVAILABLE'
          });
        }
      });

    } else {
      logger.warn(`Failed to get device status information from SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);
    }

    // Filter for Android devices only
    const androidDevices = response.data.filter((device: any) =>
      device.os && device.os.toLowerCase() === 'android'
    );

    // Enhance device objects with availability information
    const enhancedDevices = androidDevices.map((device: any) => {
      const statusInfo = deviceStatusMap.get(device.id);
      return {
        ...device,
        state: statusInfo?.state || 'UNKNOWN',
        is_available: statusInfo?.isAvailable || false,
        available: statusInfo?.isAvailable || false
      };
    });

    return {
      success: true,
      message: 'SauceLabs devices retrieved successfully',
      devices: enhancedDevices
    };
  } catch (error: any) {
    logger.error(`Error getting SauceLabs devices: ${error.message}`);
    return {
      success: false,
      message: `Error getting SauceLabs devices: ${error.message}`,
      devices: []
    };
  }
}

/**
 * Get SauceLabs account information
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to account information result
 */
export async function getSauceLabsAccountInfo(companyId: string, teamId?: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established',
        data: null
      };
    }

    // Get the SauceLabs configuration
    const configResult = await getSauceLabsConfig(companyId, teamId);

    if (!configResult.success) {
      return {
        success: false,
        message: configResult.message || 'Failed to get SauceLabs configuration',
        data: null
      };
    }

    if (!configResult.config) {
      return {
        success: false,
        message: 'SauceLabs configuration not found',
        data: null
      };
    }

    const { username, accessKey, region } = configResult.config;
    const baseUrl = `https://api.${region || 'us-west-1'}.saucelabs.com`;

    // Get user information from SauceLabs using the updated team management API endpoint
    const response = await axios.get(`${baseUrl}/team-management/v1/users/me/`, {
      auth: {
        username,
        password: accessKey
      }
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: 'Failed to get account information from SauceLabs',
        data: null
      };
    }

    return {
      success: true,
      message: 'SauceLabs account information retrieved successfully',
      data: response.data
    };
  } catch (error: any) {
    logger.error(`Error getting SauceLabs account information: ${error.message}`);
    return {
      success: false,
      message: `Error getting SauceLabs account information: ${error.message}`,
      data: null
    };
  }
}

/**
 * Get SauceLabs apps
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to apps result
 */
export async function getSauceLabsApps(companyId: string, teamId?: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established',
        apps: []
      };
    }

    // Get the SauceLabs configuration
    const configResult = await getSauceLabsConfig(companyId, teamId);

    if (!configResult.success) {
      return {
        success: false,
        message: configResult.message || 'Failed to get SauceLabs configuration',
        apps: []
      };
    }

    if (!configResult.config) {
      return {
        success: true,
        message: 'No SauceLabs configuration found',
        apps: []
      };
    }

    const { username, accessKey, region } = configResult.config;
    const baseUrl = `https://api.${region || 'us-west-1'}.saucelabs.com`;

    // Get apps from SauceLabs
    const response = await axios.get(`${baseUrl}/v1/storage/files`, {
      auth: {
        username,
        password: accessKey
      }
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: 'Failed to get apps from SauceLabs',
        apps: []
      };
    }

    return {
      success: true,
      message: 'SauceLabs apps retrieved successfully',
      apps: response.data.items
    };
  } catch (error: any) {
    logger.error(`Error getting SauceLabs apps: ${error.message}`);
    return {
      success: false,
      message: `Error getting SauceLabs apps: ${error.message}`,
      apps: []
    };
  }
}

/**
 * Get SauceLabs app groups
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to app groups result
 */
export async function getSauceLabsAppGroups(companyId: string, teamId?: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established',
        groups: []
      };
    }

    // Get the SauceLabs configuration
    const configResult = await getSauceLabsConfig(companyId, teamId);

    if (!configResult.success) {
      return {
        success: false,
        message: configResult.message || 'Failed to get SauceLabs configuration',
        groups: []
      };
    }

    if (!configResult.config) {
      return {
        success: true,
        message: 'No SauceLabs configuration found',
        groups: []
      };
    }

    const { username, accessKey, region } = configResult.config;
    const baseUrl = `https://api.${region || 'us-west-1'}.saucelabs.com`;

    // Get app groups from SauceLabs
    const response = await axios.get(`${baseUrl}/v1/storage/groups`, {
      auth: {
        username,
        password: accessKey
      }
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: 'Failed to get app groups from SauceLabs',
        groups: []
      };
    }

    return {
      success: true,
      message: 'SauceLabs app groups retrieved successfully',
      groups: response.data.items
    };
  } catch (error: any) {
    logger.error(`Error getting SauceLabs app groups: ${error.message}`);
    return {
      success: false,
      message: `Error getting SauceLabs app groups: ${error.message}`,
      groups: []
    };
  }
}

/**
 * Upload app to SauceLabs
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @param fileBuffer File buffer
 * @param fileName File name
 * @param description File description
 * @returns Promise resolving to upload result
 */
export async function uploadSauceLabsApp(
  companyId: string,
  teamId: string | undefined,
  fileBuffer: Buffer,
  fileName: string,
  description?: string
) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    // Get the SauceLabs configuration
    const configResult = await getSauceLabsConfig(companyId, teamId);

    if (!configResult.success) {
      return {
        success: false,
        message: configResult.message || 'Failed to get SauceLabs configuration'
      };
    }

    if (!configResult.config) {
      return {
        success: false,
        message: 'SauceLabs configuration not found'
      };
    }

    const { username, accessKey, region } = configResult.config;
    const baseUrl = `https://api.${region || 'us-west-1'}.saucelabs.com`;

    // Create form data
    const form = new FormData();

    // Sanitize filename to avoid issues with Turkish characters and special characters
    const sanitizedFileName = sanitizeFileName(fileName);

    // Directly append the buffer to the form data
    // Node.js form-data doesn't support Blob objects like browsers do
    form.append('payload', fileBuffer, {
      filename: sanitizedFileName,
      contentType: 'application/octet-stream'
    });
    form.append('name', sanitizedFileName);

    if (description) {
      form.append('description', description);
    }

    // Upload app to SauceLabs
    const response = await axios.post(`${baseUrl}/v1/storage/upload`, form, {
      auth: {
        username,
        password: accessKey
      },
      headers: {
        ...form.getHeaders(),
        'Content-Type': 'multipart/form-data'
      }
    });

    if (response.status !== 201) {
      return {
        success: false,
        message: 'Failed to upload app to SauceLabs'
      };
    }

    return {
      success: true,
      message: 'App uploaded to SauceLabs successfully',
      app: response.data.item
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Error uploading app to SauceLabs: ${error.message}`
    };
  }
}

/**
 * Delete app from SauceLabs
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @param fileId File ID
 * @returns Promise resolving to delete result
 */
export async function deleteSauceLabsApp(companyId: string, teamId: string | undefined, fileId: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    // Get the SauceLabs configuration
    const configResult = await getSauceLabsConfig(companyId, teamId);

    if (!configResult.success) {
      return {
        success: false,
        message: configResult.message || 'Failed to get SauceLabs configuration'
      };
    }

    if (!configResult.config) {
      return {
        success: false,
        message: 'SauceLabs configuration not found'
      };
    }

    const { username, accessKey, region } = configResult.config;
    const baseUrl = `https://api.${region || 'us-west-1'}.saucelabs.com`;

    // Delete app from SauceLabs
    const response = await axios.delete(`${baseUrl}/v1/storage/files/${fileId}`, {
      auth: {
        username,
        password: accessKey
      }
    });

    if (response.status !== 200) {
      return {
        success: false,
        message: 'Failed to delete app from SauceLabs'
      };
    }

    logger.info(`Deleted app ${fileId} from SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);

    return {
      success: true,
      message: 'App deleted from SauceLabs successfully'
    };
  } catch (error: any) {
    logger.error(`Error deleting app from SauceLabs: ${error.message}`);
    return {
      success: false,
      message: `Error deleting app from SauceLabs: ${error.message}`
    };
  }
}

/**
 * Update SauceLabs app settings
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @param groupId Group ID
 * @param settings Settings object
 * @returns Promise resolving to update result
 */
export async function updateSauceLabsAppSettings(
  companyId: string,
  teamId: string | undefined,
  groupId: string,
  settings: any
) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    // Get the SauceLabs configuration
    const configResult = await getSauceLabsConfig(companyId, teamId);

    if (!configResult.success) {
      return {
        success: false,
        message: configResult.message || 'Failed to get SauceLabs configuration'
      };
    }

    if (!configResult.config) {
      return {
        success: false,
        message: 'SauceLabs configuration not found'
      };
    }

    const { username, accessKey, region } = configResult.config;
    const baseUrl = `https://api.${region || 'us-west-1'}.saucelabs.com`;

    // Update app settings in SauceLabs
    const response = await axios.put(
      `${baseUrl}/v1/storage/groups/${groupId}/settings`,
      { settings },
      {
        auth: {
          username,
          password: accessKey
        }
      }
    );

    if (response.status !== 201) {
      return {
        success: false,
        message: 'Failed to update app settings in SauceLabs'
      };
    }

    logger.info(`Updated app settings for group ${groupId} in SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);

    return {
      success: true,
      message: 'App settings updated in SauceLabs successfully',
      settings: response.data.settings
    };
  } catch (error: any) {
    logger.error(`Error updating app settings in SauceLabs: ${error.message}`);
    return {
      success: false,
      message: `Error updating app settings in SauceLabs: ${error.message}`
    };
  }
}

/**
 * Get SauceLabs credentials by company ID and team ID
 * This function retrieves SauceLabs credentials for a company or team
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to SauceLabs credentials
 */
export async function getSauceLabsCredentialsByCompanyAndTeam(
  companyId: string,
  teamId?: string | null
) {
  try {
    // Validate input parameters
    if (!companyId) {
      logger.warn('getSauceLabsCredentialsByCompanyAndTeam: companyId is required');
      return {
        success: false,
        message: 'Company ID is required',
        credentials: null
      };
    }

    // Check MongoDB connection
    if (!isMongoDBInitialized()) {
      logger.error('getSauceLabsCredentialsByCompanyAndTeam: MongoDB is not initialized');
      return {
        success: false,
        message: 'Database connection not established',
        credentials: null
      };
    }

    // Check if collections are available
    if (!pluginsCollection || !teamsCollection || !companiesCollection) {
      logger.error('getSauceLabsCredentialsByCompanyAndTeam: Required collections are not available');
      return {
        success: false,
        message: 'Required database collections are not available',
        credentials: null
      };
    }

    logger.info(`Getting SauceLabs credentials for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // First, try to find a plugin document that directly matches both companyId and teamId
    if (teamId) {
      try {
        // Look for a plugin document with matching companyId and teamId
        const pluginDoc = await pluginsCollection.findOne({
          companyId: companyId,
          teamId: teamId
        });

        if (pluginDoc) {


          // Find the SauceLabs plugin in the plugins array
          const sauceLabsPlugin = pluginDoc.plugins.find((plugin: any) => plugin.id === 'saucelabs');

          if (sauceLabsPlugin && sauceLabsPlugin.config) {
            // Validate the credentials
            if (!sauceLabsPlugin.config.username || !sauceLabsPlugin.config.accessKey) {
              logger.warn(`Incomplete SauceLabs credentials found in plugin document for companyId: ${companyId}, teamId: ${teamId}`);
            } else {

              return {
                success: true,
                message: 'SauceLabs credentials found in plugin document',
                credentials: {
                  username: sauceLabsPlugin.config.username,
                  accessKey: sauceLabsPlugin.config.accessKey,
                  region: sauceLabsPlugin.config.region || 'us-west-1'
                }
              };
            }
          } else {
            logger.info(`Plugin document found but no SauceLabs plugin for companyId: ${companyId}, teamId: ${teamId}`);
          }
        } else {
          logger.info(`No plugin document found with matching companyId: ${companyId} and teamId: ${teamId}`);
        }
      } catch (pluginError: any) {
        // Log the error but continue to try other methods
        logger.error(`Error finding plugin document: ${pluginError.message}`, pluginError);
      }

      // If no direct plugin document found, try to get team-specific credentials
      try {
        // Try to find the team by ID
        let team = await teamsCollection.findOne({ id: teamId });

        // If not found by id, try by _id (with ObjectId conversion)
        if (!team) {
          try {
            // Try with ObjectId conversion directly
            try {
              team = await teamsCollection.findOne({ _id: new ObjectId(teamId) });
            } catch (objectIdError: any) {
              logger.debug(`Error converting teamId to ObjectId: ${objectIdError.message}`);
              // Continue with null team
            }
          } catch (error: any) {
            logger.debug(`Error finding team: ${error.message}`);
            // Continue with null team
          }
        }

        if (!team) {
          logger.warn(`Team not found with ID: ${teamId}`);
        } else if (!team.ownerId) {
          logger.warn(`Team found but has no ownerId: ${teamId}`);
        } else {
          // Try to get SauceLabs config for the team owner
          const teamOwnerConfig = await getSauceLabsConfig(team.ownerId);
          if (teamOwnerConfig.success && teamOwnerConfig.config) {
            // Validate the credentials
            if (!teamOwnerConfig.config.username || !teamOwnerConfig.config.accessKey) {
              logger.warn(`Incomplete SauceLabs credentials found for team owner: ${team.ownerId}`);
            } else {

              return {
                success: true,
                message: 'SauceLabs credentials found for team owner',
                credentials: {
                  username: teamOwnerConfig.config.username,
                  accessKey: teamOwnerConfig.config.accessKey,
                  region: teamOwnerConfig.config.region || 'us-west-1'
                }
              };
            }
          } else {
            logger.info(`No SauceLabs config found for team owner: ${team.ownerId}`);
          }
        }
      } catch (teamError: any) {
        // Log the error but continue to try company credentials
        logger.error(`Error finding team: ${teamError.message}`, teamError);
        // Don't return here, continue to try company credentials
      }
    }

    // If no team credentials found or no teamId provided, try company credentials

    // First, try to find a plugin document that directly matches companyId
    try {
      // Look for a plugin document with matching companyId
      const pluginDoc = await pluginsCollection.findOne({
        companyId: companyId
      });

      if (pluginDoc) {


        // Find the SauceLabs plugin in the plugins array
        const sauceLabsPlugin = pluginDoc.plugins.find((plugin: any) => plugin.id === 'saucelabs');

        if (sauceLabsPlugin && sauceLabsPlugin.config) {
          // Validate the credentials
          if (!sauceLabsPlugin.config.username || !sauceLabsPlugin.config.accessKey) {
            logger.warn(`Incomplete SauceLabs credentials found in plugin document for companyId: ${companyId}`);
          } else {

            return {
              success: true,
              message: 'SauceLabs credentials found in plugin document',
              credentials: {
                username: sauceLabsPlugin.config.username,
                accessKey: sauceLabsPlugin.config.accessKey,
                region: sauceLabsPlugin.config.region || 'us-west-1'
              }
            };
          }
        } else {
          logger.info(`Plugin document found but no SauceLabs plugin for companyId: ${companyId}`);
        }
      } else {
        logger.info(`No plugin document found with matching companyId: ${companyId}`);
      }
    } catch (pluginError: any) {
      // Log the error but continue to try other methods
      logger.error(`Error finding plugin document: ${pluginError.message}`, pluginError);
    }

    // If no direct plugin document found, try to get company owner credentials
    try {
      // Try to find the company by ID
      let company = await companiesCollection.findOne({ id: companyId });

      // If not found by id, try by _id (with ObjectId conversion)
      if (!company) {
        try {
          // Try with ObjectId conversion directly
          try {
            company = await companiesCollection.findOne({ _id: new ObjectId(companyId) });
          } catch (objectIdError: any) {
            logger.debug(`Error converting companyId to ObjectId: ${objectIdError.message}`);
            // Continue with null company
          }
        } catch (error: any) {
          logger.debug(`Error finding company: ${error.message}`);
          // Continue with null company
        }
      }

      if (!company) {
        logger.warn(`Company not found with ID: ${companyId}`);
      } else if (!company.ownerId) {
        logger.warn(`Company found but has no ownerId: ${companyId}`);
      } else {
        // Try to get SauceLabs config for the company owner
        const companyOwnerConfig = await getSauceLabsConfig(company.ownerId);
        if (companyOwnerConfig.success && companyOwnerConfig.config) {
          // Validate the credentials
          if (!companyOwnerConfig.config.username || !companyOwnerConfig.config.accessKey) {
            logger.warn(`Incomplete SauceLabs credentials found for company owner: ${company.ownerId}`);
          } else {

            return {
              success: true,
              message: 'SauceLabs credentials found for company owner',
              credentials: {
                username: companyOwnerConfig.config.username,
                accessKey: companyOwnerConfig.config.accessKey,
                region: companyOwnerConfig.config.region || 'us-west-1'
              }
            };
          }
        } else {
          logger.info(`No SauceLabs config found for company owner: ${company.ownerId}`);
        }
      }
    } catch (companyError: any) {
      // Log the error but continue to the final return
      logger.error(`Error finding company: ${companyError.message}`, companyError);
    }

    // If no credentials found for team or company, return failure with detailed message
    logger.warn(`No SauceLabs credentials found for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Log detailed diagnostic information
    logger.info(`SauceLabs credential search summary:
      - Searched for plugin document with companyId: ${companyId} and teamId: ${teamId || 'N/A'}
      - Searched for team owner's credentials (if teamId provided)
      - Searched for plugin document with only companyId: ${companyId}
      - Searched for company owner's credentials
      - No valid credentials found in any location
    `);

    return {
      success: false,
      message: `No SauceLabs credentials found for company: ${companyId}, team: ${teamId || 'N/A'}. Please configure SauceLabs credentials in the plugins section.`,
      credentials: null
    };
  } catch (error: any) {
    // Log detailed error information
    logger.error(`Error getting SauceLabs credentials: ${error.message}`, {
      error: error,
      stack: error.stack,
      companyId: companyId,
      teamId: teamId
    });

    return {
      success: false,
      message: `Error getting SauceLabs credentials: ${error.message}`,
      credentials: null
    };
  }
}
