"use client"

import { useEffect, useRef } from "react"
import { motion } from "framer-motion"

interface AnimatedBackgroundProps {
  className?: string
}

export function AnimatedBackground({ className }: AnimatedBackgroundProps) {
  return (
    <div className={`relative w-full h-full overflow-hidden ${className}`}>
      {/* Animated gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/90 via-violet-600/90 to-purple-700/90 z-10"></div>

      {/* Animated shapes */}
      <div className="absolute inset-0 z-0">
        <AnimatedShapes />
      </div>
    </div>
  )
}

function AnimatedShapes() {
  // Generate shapes with different sizes, positions, and animations
  const shapes = [
    { size: "w-64 h-64", x: -20, y: -20, delay: 0, duration: 20 },
    { size: "w-96 h-96", x: 60, y: 40, delay: 5, duration: 25 },
    { size: "w-80 h-80", x: 30, y: 60, delay: 2, duration: 22 },
    { size: "w-72 h-72", x: 70, y: 10, delay: 8, duration: 18 },
    { size: "w-48 h-48", x: 10, y: 80, delay: 4, duration: 15 },
  ]

  return (
    <>
      {shapes.map((shape, index) => (
        <motion.div
          key={index}
          className={`absolute rounded-full ${shape.size} bg-white/10 backdrop-blur-md`}
          style={{ x: `${shape.x}%`, y: `${shape.y}%` }}
          animate={{
            x: [`${shape.x}%`, `${shape.x + 5}%`, `${shape.x}%`],
            y: [`${shape.y}%`, `${shape.y - 8}%`, `${shape.y}%`],
          }}
          transition={{
            duration: shape.duration,
            delay: shape.delay,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      ))}
    </>
  )
}

export function AnimatedWaves() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId: number
    let time = 0

    // Set canvas dimensions
    const resizeCanvas = () => {
      const { width, height } = canvas.getBoundingClientRect()
      const dpr = window.devicePixelRatio || 1
      canvas.width = width * dpr
      canvas.height = height * dpr
      ctx.scale(dpr, dpr)
    }

    window.addEventListener("resize", resizeCanvas)
    resizeCanvas()

    // Animation parameters
    const waves = [
      { amplitude: 25, frequency: 0.02, speed: 0.01, color: "rgba(255, 255, 255, 0.1)" },
      { amplitude: 15, frequency: 0.04, speed: 0.02, color: "rgba(255, 255, 255, 0.15)" },
      { amplitude: 10, frequency: 0.03, speed: 0.015, color: "rgba(255, 255, 255, 0.2)" },
    ]

    // Animation loop
    const animate = () => {
      const { width, height } = canvas.getBoundingClientRect()

      ctx.clearRect(0, 0, width, height)

      waves.forEach((wave) => {
        ctx.fillStyle = wave.color
        ctx.beginPath()

        // Start at the left edge
        ctx.moveTo(0, height / 2)

        // Draw the wave
        for (let x = 0; x < width; x++) {
          const y = Math.sin(x * wave.frequency + time * wave.speed) * wave.amplitude + height / 2
          ctx.lineTo(x, y)
        }

        // Complete the path
        ctx.lineTo(width, height)
        ctx.lineTo(0, height)
        ctx.closePath()
        ctx.fill()
      })

      time += 0.05
      animationFrameId = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      window.removeEventListener("resize", resizeCanvas)
      cancelAnimationFrame(animationFrameId)
    }
  }, [])

  return <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" />
}

