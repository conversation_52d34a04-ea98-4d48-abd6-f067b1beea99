/**
 * SauceLabs Error Handler
 * Provides meaningful error messages and troubleshooting suggestions for SauceLabs API errors
 */

import { logger } from './logger.js';

export interface SauceLabsErrorResult {
  success: false;
  error: string;
  errorCode: string;
  details?: string;
  troubleshooting?: string[];
}

export interface SauceLabsSuccessResult<T = any> {
  success: true;
  message: string;
  data?: T;
}

export type SauceLabsResult<T = any> = SauceLabsSuccessResult<T> | SauceLabsErrorResult;

/**
 * Error codes for different SauceLabs scenarios
 */
export enum SauceLabsErrorCode {
  AUTHENTICATION_FAILED = 'SAUCELABS_AUTH_FAILED',
  INVALID_CREDENTIALS = 'SAUCELABS_INVALID_CREDENTIALS',
  ACCOUNT_SUSPENDED = 'SAUCELABS_ACCOUNT_SUSPENDED',
  INSUFFICIENT_PERMISSIONS = 'SAUCELABS_INSUFFICIENT_PERMISSIONS',
  RATE_LIMITED = 'SAUCELABS_RATE_LIMITED',
  INVALID_REGION = 'SAUCELABS_INVALID_REGION',
  SERVICE_UNAVAILABLE = 'SAUCELABS_SERVICE_UNAVAILABLE',
  NETWORK_ERROR = 'SAUCELABS_NETWORK_ERROR',
  INVALID_REQUEST = 'SAUCELABS_INVALID_REQUEST',
  RESOURCE_NOT_FOUND = 'SAUCELABS_RESOURCE_NOT_FOUND',
  INTERNAL_ERROR = 'SAUCELABS_INTERNAL_ERROR',
  UNKNOWN_ERROR = 'SAUCELABS_UNKNOWN_ERROR'
}

/**
 * Get user-friendly error message and troubleshooting steps based on HTTP status code and response
 */
function getErrorDetails(statusCode: number, responseData?: any, region?: string): {
  errorCode: SauceLabsErrorCode;
  message: string;
  details?: string;
  troubleshooting: string[];
} {
  switch (statusCode) {
    case 401:
      return {
        errorCode: SauceLabsErrorCode.AUTHENTICATION_FAILED,
        message: 'SauceLabs kimlik doğrulama başarısız oldu',
        details: 'Kullanıcı adı veya Access Key hatalı',
        troubleshooting: [
          'SauceLabs kullanıcı adınızı ve Access Key\'inizi kontrol edin',
          'SauceLabs web sitesinden yeni bir Access Key oluşturmayı deneyin',
          'Hesabınızın aktif olduğundan emin olun',
          'Büyük/küçük harf duyarlılığına dikkat edin'
        ]
      };

    case 403:
      if (responseData?.message?.includes('suspended') || responseData?.error?.includes('suspended')) {
        return {
          errorCode: SauceLabsErrorCode.ACCOUNT_SUSPENDED,
          message: 'SauceLabs hesabınız askıya alınmış',
          details: 'Hesap durumu ile ilgili sorun var',
          troubleshooting: [
            'SauceLabs destek ekibi ile iletişime geçin',
            'Hesap durumunuzu SauceLabs dashboard\'ından kontrol edin',
            'Ödeme durumunuzu kontrol edin',
            'Hesap limitlerinizi gözden geçirin'
          ]
        };
      }
      return {
        errorCode: SauceLabsErrorCode.INSUFFICIENT_PERMISSIONS,
        message: 'SauceLabs erişim izniniz yetersiz',
        details: 'Bu işlemi gerçekleştirmek için yeterli yetkiye sahip değilsiniz',
        troubleshooting: [
          'Hesap yöneticinizden gerekli izinleri talep edin',
          'Team üyeliğinizi kontrol edin',
          'Enterprise hesap ayarlarınızı gözden geçirin'
        ]
      };

    case 404:
      return {
        errorCode: SauceLabsErrorCode.RESOURCE_NOT_FOUND,
        message: 'SauceLabs kaynağı bulunamadı',
        details: 'İstenen endpoint veya kaynak mevcut değil',
        troubleshooting: [
          `Seçili region (${region || 'bilinmiyor'}) doğru mu kontrol edin`,
          'SauceLabs API endpoint\'lerinin güncel olduğundan emin olun',
          'Kaynak ID\'lerini doğrulayın'
        ]
      };

    case 429:
      return {
        errorCode: SauceLabsErrorCode.RATE_LIMITED,
        message: 'SauceLabs istek limiti aşıldı',
        details: 'Çok fazla istek gönderildi, lütfen bekleyin',
        troubleshooting: [
          'Birkaç dakika bekleyip tekrar deneyin',
          'Eş zamanlı istek sayısını azaltın',
          'SauceLabs plan limitlerini kontrol edin'
        ]
      };

    case 500:
      return {
        errorCode: SauceLabsErrorCode.INTERNAL_ERROR,
        message: 'SauceLabs sunucu hatası',
        details: 'SauceLabs tarafında geçici bir sorun var',
        troubleshooting: [
          'Birkaç dakika bekleyip tekrar deneyin',
          'SauceLabs status sayfasını kontrol edin',
          'Sorun devam ederse SauceLabs desteği ile iletişime geçin'
        ]
      };

    case 502:
    case 503:
    case 504:
      return {
        errorCode: SauceLabsErrorCode.SERVICE_UNAVAILABLE,
        message: 'SauceLabs servisi geçici olarak kullanılamıyor',
        details: 'SauceLabs API servisleri şu anda erişilebilir değil',
        troubleshooting: [
          'Birkaç dakika bekleyip tekrar deneyin',
          'SauceLabs status sayfasını kontrol edin (status.saucelabs.com)',
          'Farklı bir region deneyebilirsiniz'
        ]
      };

    case 400:
      return {
        errorCode: SauceLabsErrorCode.INVALID_REQUEST,
        message: 'SauceLabs istek formatı hatalı',
        details: 'Gönderilen veri formatı veya parametreler geçersiz',
        troubleshooting: [
          'Gönderilen parametreleri kontrol edin',
          'JSON formatının doğru olduğundan emin olun',
          'Zorunlu alanların dolu olduğunu kontrol edin'
        ]
      };

    default:
      if (statusCode >= 500) {
        return {
          errorCode: SauceLabsErrorCode.INTERNAL_ERROR,
          message: 'SauceLabs sunucu hatası',
          details: `HTTP ${statusCode} hatası alındı`,
          troubleshooting: [
            'Birkaç dakika bekleyip tekrar deneyin',
            'SauceLabs status sayfasını kontrol edin'
          ]
        };
      }
      
      return {
        errorCode: SauceLabsErrorCode.UNKNOWN_ERROR,
        message: 'Bilinmeyen SauceLabs hatası',
        details: `HTTP ${statusCode} hatası alındı`,
        troubleshooting: [
          'Bağlantınızı kontrol edin',
          'Birkaç dakika bekleyip tekrar deneyin',
          'Sorun devam ederse destek ekibi ile iletişime geçin'
        ]
      };
  }
}

/**
 * Handle network errors (connection issues, timeouts, etc.)
 */
function getNetworkErrorDetails(error: any): {
  errorCode: SauceLabsErrorCode;
  message: string;
  details: string;
  troubleshooting: string[];
} {
  const errorMessage = error.message?.toLowerCase() || '';

  if (errorMessage.includes('timeout') || errorMessage.includes('etimedout')) {
    return {
      errorCode: SauceLabsErrorCode.NETWORK_ERROR,
      message: 'SauceLabs bağlantı zaman aşımı',
      details: 'SauceLabs sunucularına bağlanırken zaman aşımı oluştu',
      troubleshooting: [
        'İnternet bağlantınızı kontrol edin',
        'Firewall ayarlarını gözden geçirin',
        'Proxy ayarlarınızı kontrol edin',
        'Birkaç dakika bekleyip tekrar deneyin'
      ]
    };
  }

  if (errorMessage.includes('enotfound') || errorMessage.includes('getaddrinfo')) {
    return {
      errorCode: SauceLabsErrorCode.INVALID_REGION,
      message: 'SauceLabs sunucusuna ulaşılamıyor',
      details: 'DNS çözümlemesi başarısız oldu',
      troubleshooting: [
        'Seçili region ayarını kontrol edin',
        'İnternet bağlantınızı kontrol edin',
        'DNS ayarlarınızı kontrol edin',
        'Farklı bir region deneyin (us-west-1, us-east-1, eu-central-1)'
      ]
    };
  }

  if (errorMessage.includes('econnrefused') || errorMessage.includes('connect')) {
    return {
      errorCode: SauceLabsErrorCode.NETWORK_ERROR,
      message: 'SauceLabs bağlantısı reddedildi',
      details: 'SauceLabs sunucularına bağlanılamıyor',
      troubleshooting: [
        'İnternet bağlantınızı kontrol edin',
        'Firewall ve proxy ayarlarını kontrol edin',
        'SauceLabs status sayfasını kontrol edin',
        'Birkaç dakika bekleyip tekrar deneyin'
      ]
    };
  }

  return {
    errorCode: SauceLabsErrorCode.NETWORK_ERROR,
    message: 'SauceLabs ağ bağlantı hatası',
    details: error.message || 'Ağ bağlantısında sorun oluştu',
    troubleshooting: [
      'İnternet bağlantınızı kontrol edin',
      'Firewall ve proxy ayarlarını kontrol edin',
      'Birkaç dakika bekleyip tekrar deneyin'
    ]
  };
}

/**
 * Create a standardized SauceLabs error result
 */
export function createSauceLabsError(
  error: any,
  context: string,
  region?: string
): SauceLabsErrorResult {
  logger.error(`SauceLabs Error in ${context}:`, error);

  // Handle Axios errors with response data
  if (error.response) {
    const statusCode = error.response.status;
    const responseData = error.response.data;
    
    const errorDetails = getErrorDetails(statusCode, responseData, region);
    
    return {
      success: false,
      error: errorDetails.message,
      errorCode: errorDetails.errorCode,
      details: errorDetails.details,
      troubleshooting: errorDetails.troubleshooting
    };
  }

  // Handle network errors
  if (error.code || error.message) {
    const errorDetails = getNetworkErrorDetails(error);
    
    return {
      success: false,
      error: errorDetails.message,
      errorCode: errorDetails.errorCode,
      details: errorDetails.details,
      troubleshooting: errorDetails.troubleshooting
    };
  }

  // Handle generic errors
  const message = error.message || error.toString() || 'Bilinmeyen hata oluştu';
  
  return {
    success: false,
    error: 'SauceLabs bağlantı hatası',
    errorCode: SauceLabsErrorCode.UNKNOWN_ERROR,
    details: message,
    troubleshooting: [
      'Bağlantı bilgilerinizi kontrol edin',
      'Birkaç dakika bekleyip tekrar deneyin',
      'Sorun devam ederse destek ekibi ile iletişime geçin'
    ]
  };
}

/**
 * Create a standardized SauceLabs success result
 */
export function createSauceLabsSuccess<T>(
  message: string,
  data?: T
): SauceLabsSuccessResult<T> {
  return {
    success: true,
    message,
    data
  };
}

/**
 * Validate SauceLabs connection parameters
 */
export function validateSauceLabsParams(
  username?: string,
  accessKey?: string,
  region?: string
): SauceLabsErrorResult | null {
  if (!username || username.trim() === '') {
    return {
      success: false,
      error: 'SauceLabs kullanıcı adı gerekli',
      errorCode: SauceLabsErrorCode.INVALID_REQUEST,
      details: 'Kullanıcı adı boş olamaz',
      troubleshooting: [
        'SauceLabs kullanıcı adınızı girin',
        'SauceLabs hesabınızın aktif olduğundan emin olun'
      ]
    };
  }

  if (!accessKey || accessKey.trim() === '') {
    return {
      success: false,
      error: 'SauceLabs Access Key gerekli',
      errorCode: SauceLabsErrorCode.INVALID_REQUEST,
      details: 'Access Key boş olamaz',
      troubleshooting: [
        'SauceLabs Access Key\'inizi girin',
        'SauceLabs dashboard\'ından yeni bir Access Key oluşturun'
      ]
    };
  }

  // Validate region if provided
  if (region && !['us-west-1', 'us-east-1', 'eu-central-1'].includes(region)) {
    return {
      success: false,
      error: 'Geçersiz SauceLabs region',
      errorCode: SauceLabsErrorCode.INVALID_REGION,
      details: `Region '${region}' desteklenmiyor`,
      troubleshooting: [
        'Geçerli region seçin: us-west-1, us-east-1, eu-central-1',
        'Hesabınızın seçili region\'da aktif olduğundan emin olun'
      ]
    };
  }

  return null; // No validation errors
}

/**
 * Log SauceLabs operation with context
 */
export function logSauceLabsOperation(
  operation: string,
  username: string,
  region: string,
  success: boolean,
  details?: string
): void {
  const logData = {
    operation,
    username,
    region,
    success,
    timestamp: new Date().toISOString(),
    details
  };

  if (success) {
    logger.info(`SauceLabs ${operation} successful:`, logData);
  } else {
    logger.error(`SauceLabs ${operation} failed:`, logData);
  }
} 