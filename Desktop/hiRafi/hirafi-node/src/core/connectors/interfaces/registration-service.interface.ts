/**
 * Registration Service Interface
 * Defines the contract for registration services
 */

import { IConnectorService } from './connector-service.interface.js';

/**
 * Registration service interface
 */
export interface IRegistrationService extends IConnectorService {
  /**
   * Log registration attempt
   * @param name Node name
   * @param capabilities Node capabilities
   * @param connectorType Connector type
   */
  logRegistrationAttempt(
    name: string,
    capabilities: string[],
    connectorType: string
  ): void;

  /**
   * Prepare registration data
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Client ID
   * @param additionalData Additional registration data
   */
  prepareRegistrationData(
    name: string,
    capabilities: string[],
    clientId?: string,
    additionalData?: Record<string, any>
  ): Record<string, any>;

  /**
   * Create registration headers
   * @param secretKey Node secret key
   * @param additionalHeaders Additional headers
   */
  createRegistrationHeaders(
    secretKey: string,
    additionalHeaders?: Record<string, string>
  ): Record<string, string>;

  /**
   * Validate registration response
   * @param response Response data
   * @param validator Validation function
   */
  validateRegistrationResponse(
    response: any,
    validator: (data: any) => boolean
  ): boolean;

  /**
   * Determine node ID from response
   * @param existingNodeId Existing node ID
   * @param responseNodeId Node ID from response
   */
  determineNodeId(
    existingNodeId: string | undefined,
    responseNodeId: string
  ): string | null;

  /**
   * Handle registration success
   * @param nodeId Assigned node ID
   */
  handleRegistrationSuccess(nodeId: string): void;

  /**
   * Handle registration failure
   * @param error Error that occurred
   * @param connectorType Connector type
   */
  handleRegistrationFailure(error: any, connectorType: string): void;
}

/**
 * Registration configuration interface
 */
export interface IRegistrationConfig {
  enableLogging?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  validateResponse?: boolean;
}

/**
 * Registration data interface
 */
export interface IRegistrationData {
  name: string;
  capabilities: string[];
  clientId?: string;
  nodeId?: string;
  version?: string;
  vncUrl?: string;
  vncPort?: string;
  timestamp: string;
  [key: string]: any;
}

/**
 * Registration response interface
 */
export interface IRegistrationResponse {
  nodeId: string;
  success: boolean;
  message?: string;
  timestamp: string;
  [key: string]: any;
}

/**
 * Registration statistics interface
 */
export interface IRegistrationStats {
  totalAttempts: number;
  successfulRegistrations: number;
  failedRegistrations: number;
  lastAttemptTime?: string;
  lastSuccessTime?: string;
  lastFailureTime?: string;
  averageResponseTime: number;
  connectorStats: Record<string, {
    attempts: number;
    successes: number;
    failures: number;
    lastAttempt?: string;
  }>;
}
