/**
 * Dashboard API Routes
 * MongoDB üzerinden dashboard istatistikleri için API rotaları
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import {
  ensureMongoDBConnection,
  isMongoDBInitialized,
  db,
  reportsCollection,
  runReportsCollection
} from '../../services/mongo/dbConnection.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { getRunReportsByUserId } from '../../services/mongo/atomicReportService.js';

const router = Router();

interface TestStats {
  totalTests: number;
  totalSuccess: number;
  totalFailure: number;
  successRate: number;
}

interface DailyActivity {
  date: string;
  passed: number;
  failed: number;
}
/**
 * GET /api/dashboard/activity
 * Son 24 saat içindeki test aktivitelerini getirir
 * Hem test_reports hem de run_reports koleksiyonlarından veri çeker
 */
router.get('/activity', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kontrolü
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı bilgileri bulunamadı. Lütfen tekrar giriş yapın.'
      });
    }

    const userId = req.user.id;
    const teamId = req.user.teamId;
    const companyId = req.user.companyId;

    // Son 24 saatin başlangıç zamanını hesapla
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - (24 * 60 * 60 * 1000));

    // MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();

    if (!isMongoDBInitialized() || !db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not established'
      });
    }

    // Koleksiyonları al
    if (!reportsCollection || !runReportsCollection) {
      return res.status(500).json({
        success: false,
        error: 'Database collections not available'
      });
    }

    // Son 24 saatteki test raporlarını getir
    const testReports = await reportsCollection.find({
      $or: [
        { "date": { $gte: startDate, $lte: endDate } },
        { "createdAt": { $gte: startDate, $lte: endDate } }
      ],
      $and: [
        {
          $or: [
            { "userId": userId },
            { "executedUser": userId },
            { "teamId": teamId },
            { "companyId": companyId }
          ]
        }
      ]
    }, {
      projection: {
        status: 1,
        result: 1,
        date: 1,
        createdAt: 1
      }
    }).toArray();

    // Son 24 saatteki run raporlarını getir
    const runReports = await runReportsCollection.find({
      $or: [
        { "createdAt": { $gte: startDate, $lte: endDate } }
      ],
      $and: [
        {
          $or: [
            { "userId": userId },
            { "executedUser": userId },
            { "teamId": teamId },
            { "companyId": companyId }
          ]
        }
      ]
    }, {
      projection: {
        status: 1,
        createdAt: 1,
        completedAt: 1
      }
    }).toArray();

    logger.info(`Fetched ${testReports.length} test reports and ${runReports.length} run reports for user ${userId} in the last 24 hours`);

    // Saatlik aktivite verilerini hazırla
    const activity: DailyActivity[] = [];

    // Test raporlarını işle
    testReports.forEach(report => {
      const reportDate = new Date(report.date || report.createdAt);
      const hour = reportDate.toISOString().split('T')[0] + ' ' + reportDate.getHours() + ':00';

      const existingHour = activity.find(a => a.date === hour);

      // Başarılı test durumlarını kontrol et
      const isPassed =
        report.status === 'passed' ||
        report.status === 'success' ||
        report.status === 'Passed' ||
        report.status === 'Success' ||
        (report.result && (
          report.result === 'success' ||
          report.result === 'Passed' ||
          report.result.status === 'success' ||
          report.result.status === 'Passed'
        ));

      if (existingHour) {
        if (isPassed) existingHour.passed++;
        else existingHour.failed++;
      } else {
        activity.push({
          date: hour,
          passed: isPassed ? 1 : 0,
          failed: isPassed ? 0 : 1
        });
      }
    });

    // Run raporlarını işle
    runReports.forEach(report => {
      const reportDate = new Date(report.createdAt);
      const hour = reportDate.toISOString().split('T')[0] + ' ' + reportDate.getHours() + ':00';

      const existingHour = activity.find(a => a.date === hour);

      // Başarılı run durumlarını kontrol et
      const isPassed =
        report.status === 'completed' ||
        report.status === 'success' ||
        report.status === 'Completed' ||
        report.status === 'Success';

      if (existingHour) {
        if (isPassed) existingHour.passed++;
        else existingHour.failed++;
      } else {
        activity.push({
          date: hour,
          passed: isPassed ? 1 : 0,
          failed: isPassed ? 0 : 1
        });
      }
    });

    // Saatlere göre sırala
    activity.sort((a, b) => a.date.localeCompare(b.date));

    res.json({
      success: true,
      data: {
        activity: activity
      }
    });

  } catch (error: any) {
    logger.error(`API Error in GET /dashboard/activity: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/dashboard/stats
 * Belirli bir zaman aralığına göre giriş yapmış kullanıcının test istatistiklerini getirir
 * timeframe parametresi: day, week, month
 */
router.get('/stats', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kontrol et (authMiddleware tarafından eklenir)
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı bilgileri bulunamadı. Lütfen tekrar giriş yapın.'
      });
    }

    const userId = req.user.id;
    const teamId = req.user.teamId;
    const companyId = req.user.companyId;
    const timeframe = req.query.timeframe?.toString() || 'week';

    // Zaman aralığını hesapla
    const endDate = new Date();
    const startDate = new Date();

    switch (timeframe) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7); // varsayılan olarak haftalık
    }

    // MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();

    if (!isMongoDBInitialized() || !db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not established'
      });
    }

    // Test raporları koleksiyonunu al
    if (!reportsCollection || !runReportsCollection) {
      return res.status(500).json({
        success: false,
        error: 'Database collections not available'
      });
    }

    // Test raporlarını getir
    const testReports = await reportsCollection.find({
      $or: [
        { "date": { $gte: startDate, $lte: endDate } },
        { "createdAt": { $gte: startDate, $lte: endDate } }
      ],
      $and: [
        {
          $or: [
            { "userId": userId },
            { "executedUser": userId },
            { "teamId": teamId },
            { "companyId": companyId }
          ]
        }
      ]
    }, {
      projection: {
        status: 1,
        result: 1,
        date: 1,
        createdAt: 1,
        userId: 1,
        executedUser: 1,
        teamId: 1,
        companyId: 1,
        summary: 1,
        _id: 1
      }
    }).toArray();

    // Run raporlarını getir
    const runReports = await runReportsCollection.find({
      $or: [
        { "createdAt": { $gte: startDate, $lte: endDate } }
      ],
      $and: [
        {
          $or: [
            { "userId": userId },
            { "executedUser": userId },
            { "teamId": teamId },
            { "companyId": companyId }
          ]
        }
      ]
    }, {
      projection: {
        status: 1,
        createdAt: 1,
        testResults: 1,
        userId: 1,
        executedUser: 1,
        teamId: 1,
        companyId: 1,
        _id: 1
      }
    }).toArray();

    logger.info(`Fetched ${testReports.length} test reports and ${runReports.length} run reports for user ${userId} in the ${timeframe} timeframe`);

    // Test raporlarından istatistikleri hesapla
    const totalTestReports = testReports.length;
    const totalTestSuccess = testReports.filter(r =>
      r.status === 'success' || r.status === 'Passed' ||
      (r.result && (r.result === 'success' || r.result === 'Passed' || r.result.status === 'success' || r.result.status === 'Passed'))
    ).length;
    const totalTestFailure = testReports.filter(r =>
      r.status === 'failure' || r.status === 'Failed' || r.status === 'error' || r.status === 'Error' ||
      (r.result && (r.result === 'failure' || r.result === 'Failed' || r.result === 'error' || r.result === 'Error' ||
                   r.result.status === 'failure' || r.result.status === 'Failed' || r.result.status === 'error' || r.result.status === 'Error'))
    ).length;

    // Adım istatistiklerini hesapla
    let totalSteps = 0;
    let passedSteps = 0;
    let failedSteps = 0;

    testReports.forEach(report => {
      if (report.summary) {
        totalSteps += report.summary.total || 0;
        passedSteps += report.summary.passed || 0;
        failedSteps += report.summary.failed || 0;
      }
    });

    // Run raporlarından istatistikleri hesapla
    const totalRuns = runReports.length;
    const completedRuns = runReports.filter(r =>
      r.status === 'completed' || r.status === 'success' || r.status === 'Completed' || r.status === 'Success'
    ).length;
    const failedRuns = runReports.filter(r =>
      r.status === 'failed' || r.status === 'failure' || r.status === 'Failed' || r.status === 'Failure' || r.status === 'error' || r.status === 'Error'
    ).length;

    // Run'lardaki test sonuçlarını topla
    let totalRunTests = 0;
    let passedRunTests = 0;
    let failedRunTests = 0;

    runReports.forEach(report => {
      if (report.testResults) {
        totalRunTests += report.testResults.total || 0;
        passedRunTests += report.testResults.completed || 0;
        failedRunTests += report.testResults.failed || 0;
      }
    });

    // Başarı oranlarını hesapla
    const testSuccessRate = totalTestReports > 0 ? (totalTestSuccess / totalTestReports) * 100 : 0;
    const runSuccessRate = totalRuns > 0 ? (completedRuns / totalRuns) * 100 : 0;
    const stepSuccessRate = totalSteps > 0 ? (passedSteps / totalSteps) * 100 : 0;

    // İstatistikleri döndür
    const stats = {
      tests: {
        total: totalTestReports,
        success: totalTestSuccess,
        failure: totalTestFailure,
        successRate: Math.round(testSuccessRate * 100) / 100, // 2 ondalık basamağa yuvarla
      },
      steps: {
        total: totalSteps,
        passed: passedSteps,
        failed: failedSteps,
        successRate: Math.round(stepSuccessRate * 100) / 100,
      },
      runs: {
        total: totalRuns,
        completed: completedRuns,
        failed: failedRuns,
        successRate: Math.round(runSuccessRate * 100) / 100,
        totalTests: totalRunTests,
        passedTests: passedRunTests,
        failedTests: failedRunTests
      }
    };

    // Eğer timeframe 'week' ise, günlük aktivite verilerini de ekle
    let dailyActivity: DailyActivity[] = [];

    if (timeframe === 'week') {
      // Son 7 günü hesapla
      const weekEndDate = new Date(endDate);
      const weekStartDate = new Date(startDate);

      // Günleri hazırla (son 7 gün)
      const days: DailyActivity[] = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date(weekStartDate);
        date.setDate(date.getDate() + i);
        days.push({
          date: date.toISOString().split('T')[0], // YYYY-MM-DD formatı
          passed: 0,
          failed: 0
        });
      }

      // Her test raporu için günlük adım istatistiklerini hesapla
      testReports.forEach(report => {
        if (!report.summary) return;

        // Tarih olarak date veya createdAt alanını kullan
        const reportDate = new Date(report.date || report.createdAt);
        const dateStr = reportDate.toISOString().split('T')[0];

        const dayIndex = days.findIndex(d => d.date === dateStr);
        if (dayIndex !== -1) {
          days[dayIndex].passed += report.summary.passed || 0;
          days[dayIndex].failed += report.summary.failed || 0;
        }
      });

      dailyActivity = days;
    }

    res.json({
      success: true,
      data: {
        timeframe,
        startDate,
        endDate,
        stats,
        dailyActivity: timeframe === 'week' ? dailyActivity : [],
        userId
      }
    });

  } catch (error: any) {
    logger.error(`API Error in GET /dashboard/stats: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/dashboard/weekly-activity
 * Son 7 günün günlük test aktivite verilerini getirir
 * Hem test_reports hem de run_reports koleksiyonlarından veri çeker
 */
router.get('/weekly-activity', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kontrol et
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı bilgileri bulunamadı. Lütfen tekrar giriş yapın.'
      });
    }

    const userId = req.user.id;
    const teamId = req.user.teamId;
    const companyId = req.user.companyId;

    // Son 7 günü hesapla
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 6); // 7 gün (bugün dahil)

    // Günleri hazırla (son 7 gün)
    const days: DailyActivity[] = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      days.push({
        date: date.toISOString().split('T')[0], // YYYY-MM-DD formatı
        passed: 0,
        failed: 0
      });
    }

    // MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();

    if (!isMongoDBInitialized() || !db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not established'
      });
    }

    // Koleksiyonları al
    if (!reportsCollection || !runReportsCollection) {
      return res.status(500).json({
        success: false,
        error: 'Database collections not available'
      });
    }

    // Kullanıcıya, takıma veya şirkete ait son 7 günün test raporlarını çek
    const testReports = await reportsCollection.find({
      $or: [
        { "date": { $gte: startDate, $lte: endDate } },
        { "createdAt": { $gte: startDate, $lte: endDate } }
      ],
      $and: [
        {
          $or: [
            { "userId": userId },
            { "executedUser": userId },
            { "teamId": teamId },
            { "companyId": companyId }
          ]
        }
      ]
    }, {
      projection: {
        status: 1,
        result: 1,
        date: 1,
        createdAt: 1
      }
    }).toArray();

    // Kullanıcıya, takıma veya şirkete ait son 7 günün run raporlarını çek
    const runReports = await runReportsCollection.find({
      $or: [
        { "createdAt": { $gte: startDate, $lte: endDate } }
      ],
      $and: [
        {
          $or: [
            { "userId": userId },
            { "executedUser": userId },
            { "teamId": teamId },
            { "companyId": companyId }
          ]
        }
      ]
    }, {
      projection: {
        status: 1,
        createdAt: 1,
        completedAt: 1
      }
    }).toArray();

    logger.info(`Fetched ${testReports.length} test reports and ${runReports.length} run reports for user ${userId} in the last 7 days`);

    // Test raporlarını işle
    testReports.forEach(report => {
      // Tarih olarak date veya createdAt alanını kullan
      const reportDate = new Date(report.date || report.createdAt);
      const dateStr = reportDate.toISOString().split('T')[0];

      const dayIndex = days.findIndex(d => d.date === dateStr);
      if (dayIndex !== -1) {
        // Başarılı test durumlarını kontrol et
        const isPassed =
          report.status === 'passed' ||
          report.status === 'success' ||
          report.status === 'Passed' ||
          report.status === 'Success' ||
          (report.result && (
            report.result === 'success' ||
            report.result === 'Passed' ||
            report.result.status === 'success' ||
            report.result.status === 'Passed'
          ));

        if (isPassed) {
          days[dayIndex].passed++;
        } else {
          days[dayIndex].failed++;
        }
      }
    });

    // Run raporlarını işle
    runReports.forEach(report => {
      // Tarih olarak createdAt alanını kullan
      const reportDate = new Date(report.createdAt);
      const dateStr = reportDate.toISOString().split('T')[0];

      const dayIndex = days.findIndex(d => d.date === dateStr);
      if (dayIndex !== -1) {
        // Başarılı run durumlarını kontrol et
        const isPassed =
          report.status === 'completed' ||
          report.status === 'success' ||
          report.status === 'Completed' ||
          report.status === 'Success';

        if (isPassed) {
          days[dayIndex].passed++;
        } else {
          days[dayIndex].failed++;
        }
      }
    });

    res.json({
      success: true,
      data: {
        startDate,
        endDate,
        activity: days,
        userId // Kullanıcı kimliğini de ekle
      }
    });

  } catch (error: any) {
    logger.error(`API Error in GET /dashboard/weekly-activity: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/dashboard/recent-runs
 * Kullanıcının son çalıştırdığı testleri getirir
 */
router.get('/recent-runs', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kontrol et
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı bilgileri bulunamadı. Lütfen tekrar giriş yapın.'
      });
    }

    const userId = req.user.id;
    const teamId = req.user.teamId;
    const companyId = req.user.companyId;
    const limit = parseInt(req.query.limit?.toString() || '5');

    logger.info(`Fetching recent runs for user ${userId}, teamId ${teamId}, companyId ${companyId} with limit ${limit}`);

    // MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();

    if (!isMongoDBInitialized() || !db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not established'
      });
    }

    // Kullanıcının, takımın veya şirketin son çalıştırdığı testleri getir
    const result = await getRunReportsByUserId(userId, limit, 0, teamId, companyId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to fetch recent runs'
      });
    }

    // Raporları döndür
    res.json({
      success: true,
      data: {
        runs: result.reports,
        total: result.total || 0
      }
    });

  } catch (error: any) {
    logger.error(`API Error in GET /dashboard/recent-runs: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/dashboard/recent-scenarios
 * Kullanıcının takımına ve şirketine ait son oluşturulan senaryoları getirir
 */
router.get('/recent-scenarios', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kontrol et
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı bilgileri bulunamadı. Lütfen tekrar giriş yapın.'
      });
    }

    const userId = req.user.id;
    const teamId = req.user.teamId;
    const companyId = req.user.companyId;
    const limit = parseInt(req.query.limit?.toString() || '5');

    logger.info(`Fetching recent scenarios for user ${userId}, teamId ${teamId}, companyId ${companyId} with limit ${limit}`);

    // MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();

    if (!isMongoDBInitialized() || !db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not established'
      });
    }

    // Scenarios koleksiyonunu al
    const scenariosCollection = db.collection('scenarios');

    // Takım ve şirkete ait son oluşturulan senaryoları getir
    const scenarios = await scenariosCollection.find(
      {
        $or: [
          { teamId: teamId, companyId: companyId },
          { userId: userId } // Geriye dönük uyumluluk için userId ile de kontrol et
        ]
      },
      {
        projection: {
          _id: 0,
          id: 1,
          name: 1,
          description: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          folderId: 1,
          steps: { $slice: 0 } // Sadece step sayısını al, içeriğini alma
        },
        sort: { createdAt: -1 },
        limit: limit
      }
    ).toArray();

    // Senaryo sayısını hesapla
    const scenariosWithStepCount = scenarios.map(scenario => {
      // steps alanı varsa uzunluğunu al, yoksa 0 olarak işaretle
      const stepCount = scenario.steps ? Array.isArray(scenario.steps) ? scenario.steps.length : 0 : 0;

      // steps alanını kaldır ve yerine stepCount ekle
      const { steps, ...rest } = scenario;
      return {
        ...rest,
        stepCount
      };
    });

    // Senaryoları döndür
    res.json({
      success: true,
      data: {
        scenarios: scenariosWithStepCount,
        total: scenariosWithStepCount.length
      }
    });

  } catch (error: any) {
    logger.error(`API Error in GET /dashboard/recent-scenarios: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;