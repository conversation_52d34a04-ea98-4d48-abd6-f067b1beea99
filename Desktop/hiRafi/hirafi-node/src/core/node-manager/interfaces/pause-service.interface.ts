/**
 * Pause Service Interface
 * Defines the contract for node pause management
 * Refactored to use dependency injection instead of singleton pattern
 */

import { EventEmitter } from 'events';

export interface IPauseService extends EventEmitter {
  /**
   * Initialize the pause service
   */
  initialize(nodeId: string): Promise<boolean>;

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean;

  /**
   * Check if the system is paused
   */
  isSystemPaused(): Promise<boolean>;

  /**
   * Check if this specific node is paused
   */
  isNodePaused(): Promise<boolean>;

  /**
   * Check if either the system or this node is paused
   */
  isPaused(): Promise<boolean>;

  /**
   * Set system pause status
   */
  setSystemPaused(paused: boolean): Promise<boolean>;

  /**
   * Set node pause status
   */
  setNodePaused(paused: boolean): Promise<boolean>;

  /**
   * Get the current node ID
   */
  getNodeId(): string | null;

  /**
   * Close the service
   */
  close(): Promise<void>;
}

export interface PauseServiceEvents {
  'effectivePauseStatusChanged': (isPaused: boolean) => void;
}

export interface PauseServiceConfig {
  nodeId: string;
  enableLogging?: boolean;
  checkInterval?: number;
}

export interface PauseServiceStats {
  nodeId: string | null;
  isInitialized: boolean;
  systemPaused: boolean;
  nodePaused: boolean;
  effectivelyPaused: boolean;
  lastCheck: string | null;
  checkCount: number;
}
