/**
 * Step Handler Registry Interface
 * Defines the contract for registering and retrieving step handlers
 */

import { IStepHandler } from './step-handler.interface.js';

/**
 * Step Handler Registry Interface
 * Manages a collection of step handlers
 */
export interface IStepHandlerRegistry {
  /**
   * Register a step handler for a specific step type
   * @param type Step type
   * @param handler Step handler
   */
  registerHandler(type: string, handler: IStepHandler): void;

  /**
   * Get a step handler for a specific step type
   * @param type Step type
   * @returns Step handler or undefined if not found
   */
  getHandler(type: string): IStepHandler | undefined;

  /**
   * Check if a handler exists for a specific step type
   * @param type Step type
   * @returns True if a handler exists, false otherwise
   */
  hasHandler(type: string): boolean;

  /**
   * Execute a step using the appropriate handler
   * @param target Target to execute the step on
   * @param step Step to execute
   * @param agent Agent to use for AI-powered steps
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  executeStep(
    target: any,
    step: any,
    agent: any,
    logs: string[]
  ): Promise<{ success: boolean; error: string | null; data?: any }>;

  /**
   * Set progress reporter for nested step reporting
   * @param progressReporter Progress reporter instance
   * @param testId Current test ID
   * @param testContext Test context with runId, executionId, scenarioId
   */
  setProgressReporter(progressReporter: any, testId: string, testContext?: {
    runId?: string;
    executionId?: string;
    scenarioId?: string;
  }): void;

  /**
   * Set screenshot provider for nested step screenshots
   * @param screenshotProvider Screenshot provider instance
   */
  setScreenshotProvider(screenshotProvider: any): void;

  /**
   * Set current test request for variable resolution
   * @param testRequest Current test request
   */
  setCurrentTestRequest(testRequest: any): void;

  /**
   * Destroy the registry and cleanup all resources
   * Essential for garbage collection
   */
  destroy(): void;
}
