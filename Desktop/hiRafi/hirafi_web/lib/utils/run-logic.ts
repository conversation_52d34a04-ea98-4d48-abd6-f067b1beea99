/**
 * Run logic utilities - centralized business logic for runs
 */

export interface TestResults {
  total: number;
  completed: number;
  failed: number;
  running: number;
  queued: number;
  stopped: number;
}

export interface Run {
  id: string;
  name: string;
  runName?: string;
  description?: string;
  status: 'created' | 'queued' | 'running' | 'completed' | 'failed' | 'stopped' | 'partial';
  testResults?: TestResults;
  scenarioIds: string[];
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  userId?: string;
  executedUser?: string;
  executedUserName?: string;
  platform?: 'web' | 'android';
  environment?: any;
  deviceProvider?: 'sauceLabs' | 'testinium'; // Mutually exclusive device provider selection
  reportSettings?: {
    takeScreenshots?: boolean;
    takeVideos?: boolean;
    pageMetrics?: boolean;
    networkData?: boolean;
    tracingData?: boolean;
    accessibilityData?: boolean;
  };
  testIds?: string[];
  tags?: string[];
  options?: Record<string, any>;
  latestRunReportId?: string;
  testDataEnvironmentId?: string;
  testDataEnvironmentName?: string;
}

/**
 * Normalize API status to consistent format
 */
export function normalizeStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'created': 'created',
    'queued': 'queued',
    'running': 'running',
    'completed': 'completed',
    'passed': 'completed',
    'failed': 'failed',
    'stopped': 'stopped',
    'partial': 'partial'
  };
  
  return statusMap[status.toLowerCase()] || status;
}

/**
 * Check if a run is finished (no longer active)
 */
export function isRunFinished(run: Run, tests?: any[]): boolean {
  if (!run) return true;
  
  const finishedStatuses = ['completed', 'failed', 'stopped', 'partial'];
  
  // Check run status first
  if (finishedStatuses.includes(run.status)) {
    return true;
  }
  
  // If we have test results, check if all tests are finished
  if (run.testResults) {
    const { total, completed, failed, stopped } = run.testResults;
    const finishedTests = completed + failed + stopped;
    return total > 0 && finishedTests >= total;
  }
  
  // If we have tests array, check individual test statuses
  if (tests && tests.length > 0) {
    return tests.every(test => 
      finishedStatuses.includes(test.status) || 
      finishedStatuses.includes(test.testStatus)
    );
  }
  
  return false;
}

/**
 * Check if a run is currently active (running or queued)
 */
export function isRunActive(run: Run, tests?: any[]): boolean {
  return !isRunFinished(run, tests);
}

/**
 * Count tests by status from test results
 */
export function countTestsByStatus(testResults: TestResults): {
  total: number;
  finished: number;
  active: number;
  pending: number;
} {
  const { total, completed, failed, stopped, running, queued } = testResults;
  
  return {
    total,
    finished: completed + failed + stopped,
    active: running,
    pending: queued
  };
}

/**
 * Generate default test results based on run status and scenario count
 */
export function generateDefaultTestResults(status: string, scenarioCount: number): TestResults {
  const normalizedStatus = normalizeStatus(status);
  
  switch (normalizedStatus) {
    case 'created':
      return {
        total: scenarioCount,
        completed: 0,
        failed: 0,
        running: 0,
        queued: 0,
        stopped: 0
      };
    case 'queued':
      return {
        total: scenarioCount,
        completed: 0,
        failed: 0,
        running: 0,
        queued: scenarioCount,
        stopped: 0
      };
    case 'running':
      return {
        total: scenarioCount,
        completed: 0,
        failed: 0,
        running: scenarioCount,
        queued: 0,
        stopped: 0
      };
    case 'completed':
      return {
        total: scenarioCount,
        completed: scenarioCount,
        failed: 0,
        running: 0,
        queued: 0,
        stopped: 0
      };
    case 'failed':
      return {
        total: scenarioCount,
        completed: 0,
        failed: scenarioCount,
        running: 0,
        queued: 0,
        stopped: 0
      };
    case 'stopped':
      return {
        total: scenarioCount,
        completed: 0,
        failed: 0,
        running: 0,
        queued: 0,
        stopped: scenarioCount
      };
    default:
      return {
        total: scenarioCount,
        completed: 0,
        failed: 0,
        running: 0,
        queued: 0,
        stopped: 0
      };
  }
}

/**
 * Determine if SWR should refresh based on run activity
 */
export function shouldRefresh(data: { run: Run; tests?: any[] } | undefined): number {
  if (!data) return 0; // Don't refresh if no data
  
  const isActive = isRunActive(data.run, data.tests);
  return isActive ? 5000 : 0; // Refresh every 5 seconds if active, otherwise don't refresh
}

/**
 * Get run duration in milliseconds
 */
export function getRunDuration(run: Run): number | undefined {
  if (!run.startedAt) return undefined;
  
  const startTime = new Date(run.startedAt).getTime();
  const endTime = run.completedAt ? new Date(run.completedAt).getTime() : Date.now();
  
  return endTime - startTime;
}

/**
 * Check if a run can be started
 */
export function canStartRun(run: Run): boolean {
  return ['created', 'stopped', 'failed', 'completed'].includes(run.status);
}

/**
 * Check if a run can be stopped
 */
export function canStopRun(run: Run): boolean {
  return ['running', 'queued'].includes(run.status);
}
