import { <PERSON><PERSON>, Position } from 'reactflow';
import { Button } from '@/components/ui/button';
import { X, Plus, FilePlus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState, useEffect } from 'react';
import { getDbColumns } from '@/lib/api/test-data'; // Bu importu ekleyeceğiz

function InsertNode({ data, id }: { data: any, id: string }) {
  const [columns, setColumns] = useState<string[]>([]);
  const [isFetchingColumns, setIsFetchingColumns] = useState(false);

  useEffect(() => {
    console.log('InsertNode useEffect:', { 
      table: data.table, 
      dbTables: data.dbTables, 
      dbTablesLength: data.dbTables?.length,
      connectionString: data.connectionString,
      version: data.version 
    });
    
    // Eğer tablo seçili ve connection string mevcut ise sütunları getir
    if (data.table && data.connectionString) {
      console.log('Fetching columns for table:', data.table, 'with connection:', data.connectionString);
      setIsFetchingColumns(true);
      
      // Doğrudan getDbColumns API'sini çağır
      import('@/lib/api/test-data').then(({ getDbColumns }) => {
        return getDbColumns(data.connectionString, data.table);
      }).then((result: any) => {
        console.log('Direct getDbColumns result:', result);
        if (result.success && result.data?.columns) {
          console.log('Setting columns:', result.data.columns);
          setColumns(result.data.columns);
        } else {
          console.log('getDbColumns failed:', result.error);
          setColumns([]);
        }
        setIsFetchingColumns(false);
      }).catch((error: any) => {
        console.error("Failed to fetch columns directly", error);
        setColumns([]);
        setIsFetchingColumns(false);
      });
    } else {
      console.log('Conditions not met for fetching columns - no table or connectionString');
    }
  }, [data.version, data.dbTables, data.table, data.connectionString]);

  const handleTableChange = async (tableName: string, resetState = true) => {
    if(resetState) {
        data.updateData({ table: tableName, fields: [{ col: '', val: '' }] });
    } else {
        data.updateData({ table: tableName });
    }

    setIsFetchingColumns(true);
    try {
      const fetchedColumns = await data.onTableSelect(tableName);
      setColumns(fetchedColumns || []);
    } catch (error) {
      console.error("Failed to fetch columns", error);
    } finally {
        setIsFetchingColumns(false);
    }
  };

  const handleFieldChange = (index: number, field: 'col' | 'val', value: string) => {
    const newFields = [...data.fields];
    newFields[index][field] = value;
    data.updateData({ fields: newFields });
  };
  
  const addField = () => {
      data.updateData({ fields: [...data.fields, { col: '', val: '' }] });
  };

  const removeField = (index: number) => {
      const newFields = data.fields.filter((_: any, i: number) => i !== index);
      data.updateData({ fields: newFields.length > 0 ? newFields : [{ col: '', val: '' }] });
  };

  return (
    <div className="query-builder-node">
      <div className="node-header">
        <div className="node-title">
            <FilePlus className="h-4 w-4 text-blue-500" />
            <span>INSERT Data</span>
        </div>
        <Button variant="ghost" size="icon" className="h-6 w-6 btn" onClick={data.onDelete}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="node-body">
        <div className="node-section">
            <Label className="node-section-label">TABLE</Label>
            <Select onValueChange={handleTableChange} value={data.table}>
                <SelectTrigger className="react-select-trigger"><SelectValue placeholder="Select a table..." /></SelectTrigger>
                <SelectContent>{data.dbTables.map((t: string) => <SelectItem key={t} value={t}>{t}</SelectItem>)}</SelectContent>
            </Select>
        </div>

        {isFetchingColumns && <div>Loading...</div>}

        {data.table && !isFetchingColumns && (
            <div className="node-section">
                 <Label className="node-section-label">COLUMNS</Label>
                 {data.fields.map((field: any, index: number) => (
                    <div key={index} className="node-row">
                        <Select value={field.col} onValueChange={(val) => handleFieldChange(index, 'col', val)}><SelectTrigger className="react-select-trigger"><SelectValue placeholder="Column" /></SelectTrigger><SelectContent>{columns.map(c => <SelectItem key={c} value={c}>{c}</SelectItem>)}</SelectContent></Select>
                        <Input className="react-input" placeholder="Value" value={field.val} onChange={(e) => handleFieldChange(index, 'val', e.target.value)} />
                        <Button variant="ghost" size="icon" className="btn" onClick={() => removeField(index)}><X className="h-4 w-4 text-slate-400" /></Button>
                    </div>
                 ))}
                 <Button variant="outline" size="sm" className="btn mt-1" onClick={addField}><Plus className="h-4 w-4 mr-2" />Add Field</Button>
            </div>
        )}
      </div>
      <Handle type="source" position={Position.Right} />
    </div>
  );
}

export default InsertNode; 