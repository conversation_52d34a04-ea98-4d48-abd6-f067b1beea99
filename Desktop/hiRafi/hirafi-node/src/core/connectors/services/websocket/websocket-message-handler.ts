/**
 * WebSocket Message Handler
 * 
 * Handles WebSocket message processing with dependency injection.
 * Replaces the singleton WebSocketMessageHandler with a proper service implementation.
 */

import { BaseService } from '../base/base-service.js';
import { IWebSocketMessageHandler } from '../../interfaces/websocket-connector.interface.js';
import { IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';

/**
 * WebSocket Message Handler Class
 * Processes incoming WebSocket messages with dependency injection
 */
export class WebSocketMessageHandler extends BaseService implements IWebSocketMessageHandler {
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private messageStats: Map<string, number> = new Map();
  private totalMessages = 0;
  private validMessages = 0;
  private invalidMessages = 0;
  private enableValidation = true;
  private messageValidator?: (message: any) => boolean;

  constructor(logger: any, config: IServiceConfig) {
    super('WebSocketMessageHandler', logger, config);
    this.enableValidation = config.enableValidation !== false;
    this.messageValidator = config.messageValidator;
  }

  /**
   * Initialize the message handler
   * @param config Service configuration
   */
  protected async doInitialize(config: IServiceConfig): Promise<void> {
    this.setupDefaultHandlers();
    this.logger.debug('WebSocketMessageHandler: Initialized with validation:', this.enableValidation);
  }

  /**
   * Close the message handler
   */
  protected async doClose(): Promise<void> {
    this.messageHandlers.clear();
    this.messageStats.clear();
    this.logger.debug('WebSocketMessageHandler: Closed successfully');
  }

  /**
   * Handle incoming message
   * @param message Message data
   */
  handleMessage(message: any): void {
    try {
      this.totalMessages++;
      this.logOperation('messageReceived', { type: message?.type });

      // Validate message if validation is enabled
      if (this.enableValidation && !this.validateMessage(message)) {
        this.invalidMessages++;
        this.handleError(new Error('Invalid message format'), 'handleMessage');
        return;
      }

      this.validMessages++;

      // Update message type statistics
      const messageType = message.type || 'unknown';
      this.messageStats.set(messageType, (this.messageStats.get(messageType) || 0) + 1);

      // Find and execute handler
      const handler = this.messageHandlers.get(messageType);
      if (handler) {
        const startTime = Date.now();
        handler(message.data || message);
        const processingTime = Date.now() - startTime;

        this.logOperation('messageProcessed', {
          type: messageType,
          processingTime
        });

        this.emit('messageProcessed', {
          messageType,
          processingTime,
          success: true
        });
      } else {
        this.logOperation('messageUnhandled', { type: messageType });
        this.emit('messageUnhandled', { messageType });
      }

    } catch (error) {
      this.handleError(error, 'handleMessage');
      this.emit('messageError', { error, message });
    }
  }

  /**
   * Register message handler for specific type
   * @param messageType Message type
   * @param handler Handler function
   */
  registerHandler(messageType: string, handler: (data: any) => void): void {
    this.messageHandlers.set(messageType, handler);
    this.logOperation('handlerRegistered', { messageType });
    this.emit('handlerRegistered', { messageType });
  }

  /**
   * Unregister message handler for specific type
   * @param messageType Message type
   */
  unregisterHandler(messageType: string): void {
    this.messageHandlers.delete(messageType);
    this.logOperation('handlerUnregistered', { messageType });
    this.emit('handlerUnregistered', { messageType });
  }

  /**
   * Get registered message types
   */
  getRegisteredTypes(): string[] {
    return Array.from(this.messageHandlers.keys());
  }

  /**
   * Enable/disable message validation
   * @param enabled Whether to enable validation
   */
  setValidationEnabled(enabled: boolean): void {
    this.enableValidation = enabled;
    this.logOperation('validationToggled', { enabled });
  }

  /**
   * Set message validator
   * @param validator Validation function
   */
  setMessageValidator(validator: (message: any) => boolean): void {
    this.messageValidator = validator;
    this.logOperation('validatorSet');
  }

  /**
   * Validate message format
   * @param message Message to validate
   */
  private validateMessage(message: any): boolean {
    if (!message) {
      return false;
    }

    // Use custom validator if provided
    if (this.messageValidator) {
      return this.messageValidator(message);
    }

    // Default validation: message should have a type
    return typeof message === 'object' && typeof message.type === 'string';
  }

  /**
   * Setup default message handlers
   */
  private setupDefaultHandlers(): void {
    // Registration response handler
    this.registerHandler('registered', (data: any) => {
      this.emit('registered', data.nodeId || data);
    });

    // Node ID correction handler
    this.registerHandler('nodeIdCorrected', (data: any) => {
      this.emit('nodeIdCorrected', data.nodeId || data);
    });

    // Registration required handler
    this.registerHandler('registrationRequired', (data: any) => {
      this.emit('registrationRequired', data);
    });

    // Pong handler
    this.registerHandler('pong', (data: any) => {
      this.emit('pong', data);
    });

    // Test assignment handler
    this.registerHandler('test-assignment', (data: any) => {
      this.emit('test-assignment', data);
    });

    // Next test handler
    this.registerHandler('next-test', (data: any) => {
      this.emit('next-test', data);
    });

    // Stop test handler
    this.registerHandler('stop-test', (data: any) => {
      this.emit('stop-test', data);
    });

    this.logger.debug('WebSocketMessageHandler: Default handlers registered');
  }

  /**
   * Get handler statistics
   */
  getStats(): IServiceStats {
    return super.getStats();
  }

  /**
   * Get message handler statistics
   */
  getMessageHandlerStats(): Record<string, any> {
    const baseStats = super.getStats();
    
    const messagesByType = Object.fromEntries(this.messageStats);
    const handledMessages = this.validMessages;
    const unhandledMessages = this.totalMessages - this.validMessages - this.invalidMessages;
    const averageProcessingTime = this.totalMessages > 0 
      ? Math.round((this.stats.operationCount / this.totalMessages) * 100) / 100 
      : 0;

    return {
      ...baseStats,
      totalMessages: this.totalMessages,
      messagesByType,
      validMessages: this.validMessages,
      invalidMessages: this.invalidMessages,
      handledMessages,
      unhandledMessages,
      averageProcessingTime,
      registeredHandlers: this.messageHandlers.size,
      enableValidation: this.enableValidation,
      hasCustomValidator: !!this.messageValidator
    };
  }
}
