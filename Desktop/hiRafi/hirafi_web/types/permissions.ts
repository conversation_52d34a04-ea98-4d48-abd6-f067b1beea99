// Rol izinleri için tip tanımları
export interface Permission {
  id?: string;
  resource: string;
  action: string;
  description?: string;
  name?: string;
  module?: string;
}

export interface RolePermission {
  id: string;
  name: string;
  permissions: Permission[];
  color?: string;
  isDefault?: boolean;
  teamId?: string;
  companyId?: string;
}

// Klasör izinleri
export const FOLDER_PERMISSIONS = {
  CREATE: { resource: 'Folder', action: 'create', description: 'Klasör oluşturma' },
  EDIT: { resource: 'Folder', action: 'edit', description: 'Klasör düzenleme' },
  DELETE: { resource: 'Folder', action: 'delete', description: 'Klasör silme' },
};

// Senaryo izinleri
export const SCENARIO_PERMISSIONS = {
  CREATE: { resource: 'Scenario', action: 'create', description: 'Senaryo oluşturma' },
  EDIT: { resource: 'Scenario', action: 'edit', description: 'Senaryo düzenleme' },
  DELETE: { resource: 'Scenario', action: 'delete', description: 'Senaryo silme' },
  VIEW: { resource: 'Scenario', action: 'view', description: 'Senaryo görüntüleme' },
};

// Çalıştırma izinleri
export const RUN_PERMISSIONS = {
  CREATE: { resource: 'Run', action: 'create', description: 'Çalıştırma oluşturma' },
  EDIT: { resource: 'Run', action: 'edit', description: 'Çalıştırma düzenleme' },
  DELETE: { resource: 'Run', action: 'delete', description: 'Çalıştırma silme' },
  VIEW: { resource: 'Run', action: 'read', description: 'Çalıştırma görüntüleme' },
  EXECUTE: { resource: 'Run', action: 'execute', description: 'Çalıştırma başlatma' },
};

// Zamanlama izinleri
export const SCHEDULE_PERMISSIONS = {
  CREATE: { resource: 'Schedule', action: 'create', description: 'Zamanlama oluşturma' },
  EDIT: { resource: 'Schedule', action: 'edit', description: 'Zamanlama düzenleme' },
  DELETE: { resource: 'Schedule', action: 'delete', description: 'Zamanlama silme' },
};

// Varsayılan roller
export const DEFAULT_ROLES: RolePermission[] = [
  {
    id: 'company_owner',
    name: 'Şirket Sahibi',
    permissions: [
      ...Object.values(FOLDER_PERMISSIONS),
      ...Object.values(SCENARIO_PERMISSIONS),
      ...Object.values(RUN_PERMISSIONS),
      ...Object.values(SCHEDULE_PERMISSIONS),
    ],
    color: 'red',
    isDefault: true,
  },
  {
    id: 'team_admin',
    name: 'Takım Yöneticisi',
    permissions: [
      ...Object.values(FOLDER_PERMISSIONS),
      ...Object.values(SCENARIO_PERMISSIONS),
      ...Object.values(RUN_PERMISSIONS),
      ...Object.values(SCHEDULE_PERMISSIONS),
    ],
    color: 'blue',
    isDefault: true,
  },
  {
    id: 'tester',
    name: 'Test Uzmanı',
    permissions: [
      FOLDER_PERMISSIONS.CREATE,
      SCENARIO_PERMISSIONS.CREATE,
      SCENARIO_PERMISSIONS.EDIT,
      SCENARIO_PERMISSIONS.VIEW,
      RUN_PERMISSIONS.CREATE,
      RUN_PERMISSIONS.EDIT,
      RUN_PERMISSIONS.VIEW,
      RUN_PERMISSIONS.EXECUTE,
      SCHEDULE_PERMISSIONS.CREATE,
      SCHEDULE_PERMISSIONS.EDIT,
    ],
    color: 'green',
    isDefault: true,
  },
  {
    id: 'viewer',
    name: 'İzleyici',
    permissions: [],
    color: 'gray',
    isDefault: true,
  },
];

// Kullanıcının belirli bir izne sahip olup olmadığını kontrol eden yardımcı fonksiyon
export function hasPermission(userPermissions: Permission[], permission: Permission): boolean {
  return userPermissions.some(
    p => p.resource === permission.resource && p.action === permission.action
  );
}

// Kullanıcının izinlerini localStorage'dan alma
export function getUserPermissions(): Permission[] {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    return user.permissions || [];
  } catch (e) {
    console.error('Kullanıcı izinleri alınamadı:', e);
    return [];
  }
}

// UI için önceden tanımlanmış izinler
export const predefinedPermissions: Record<string, Permission[]> = {
  folders: [
    {
      id: "folders-create",
      name: "Klasör Oluşturma",
      description: "Yeni klasörler oluşturabilir",
      module: "folders",
      resource: "Folder",
      action: "create",
    },
    {
      id: "folders-update",
      name: "Klasör Düzenleme",
      description: "Mevcut klasörleri düzenleyebilir",
      module: "folders",
      resource: "Folder",
      action: "edit",
    },
    {
      id: "folders-delete",
      name: "Klasör Silme",
      description: "Klasörleri silebilir",
      module: "folders",
      resource: "Folder",
      action: "delete",
    },
  ],
  scenarios: [
    {
      id: "scenarios-create",
      name: "Senaryo Oluşturma",
      description: "Yeni senaryolar oluşturabilir",
      module: "scenarios",
      resource: "Scenario",
      action: "create",
    },
    {
      id: "scenarios-read",
      name: "Senaryo Görüntüleme",
      description: "Senaryoları görüntüleyebilir",
      module: "scenarios",
      resource: "Scenario",
      action: "view",
    },
    {
      id: "scenarios-update",
      name: "Senaryo Düzenleme",
      description: "Mevcut senaryoları düzenleyebilir",
      module: "scenarios",
      resource: "Scenario",
      action: "edit",
    },
    {
      id: "scenarios-delete",
      name: "Senaryo Silme",
      description: "Senaryoları silebilir",
      module: "scenarios",
      resource: "Scenario",
      action: "delete",
    },
  ],
  runs: [
    {
      id: "runs-create",
      name: "Koşum Oluşturma",
      description: "Yeni koşumlar oluşturabilir",
      module: "runs",
      resource: "Run",
      action: "create",
    },
    {
      id: "runs-read",
      name: "Koşum Görüntüleme",
      description: "Koşumları görüntüleyebilir",
      module: "runs",
      resource: "Run",
      action: "read",
    },
    {
      id: "runs-update",
      name: "Koşum Düzenleme",
      description: "Mevcut koşumları düzenleyebilir",
      module: "runs",
      resource: "Run",
      action: "edit",
    },
    {
      id: "runs-delete",
      name: "Koşum Silme",
      description: "Koşumları silebilir",
      module: "runs",
      resource: "Run",
      action: "delete"
    },
    {
      id: "runs-execute",
      name: "Koşum Başlatma",
      description: "Koşumları çalıştırabilir",
      module: "runs",
      resource: "Run",
      action: "execute",
    },
  ],
  reports: [
    {
      id: "reports-read",
      name: "Rapor Görüntüleme",
      description: "Raporları görüntüleyebilir",
      module: "reports",
      resource: "Report",
      action: "view",
    },
    {
      id: "reports-create",
      name: "Rapor Oluşturma",
      description: "Özel raporlar oluşturabilir",
      module: "reports",
      resource: "Report",
      action: "create",
    },
    {
      id: "reports-delete",
      name: "Rapor Silme",
      description: "Raporları silebilir",
      module: "reports",
      resource: "Report",
      action: "delete",
    },
  ],
  Schedule: [
    {
      id: "schedule-create",
      name: "Schedule Oluşturma",
      description: "Yeni schedule görevleri oluşturabilir",
      module: "Schedule",
      resource: "Schedule",
      action: "create",
    },
    {
      id: "schedule-read",
      name: "Schedule Görüntüleme",
      description: "Schedule görevleri görüntüleyebilir",
      module: "Schedule",
      resource: "Schedule",
      action: "view",
    },
    {
      id: "schedule-update",
      name: "Schedule Düzenleme",
      description: "Schedule görevleri düzenleyebilir",
      module: "Schedule",
      resource: "Schedule",
      action: "edit",
    },
    {
      id: "schedule-delete",
      name: "Schedule Silme",
      description: "Schedule görevleri silebilir",
      module: "Schedule",
      resource: "Schedule",
      action: "delete",
    },
  ],
};
