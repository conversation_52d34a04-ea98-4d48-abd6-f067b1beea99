"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, Loader2, Shuffle, Layers, ChevronDown } from "lucide-react"
import { EnvironmentSelectionModal } from "./environment-selection-modal"
import { CreativityControl } from "@/components/ui/creativity-control"
import type { DataEnvironment } from "@/store/testDataSetsStore"

interface AiPromptSectionProps {
  prompt: string
  onPromptChange: (value: string) => void
  onGenerate: (selectedEnvironmentIds: string[], creativityLevel?: number) => void
  isGenerating: boolean
  // Environment props
  environments: DataEnvironment[]
  selectedEnvironments: string[]
  isLoadingEnvironments: boolean
  onToggleEnvironment: (environmentId: string) => void
}

export function AiPromptSection({
  prompt,
  onPromptChange,
  onGenerate,
  isGenerating,
  environments,
  selectedEnvironments,
  isLoadingEnvironments,
  onToggleEnvironment,
}: AiPromptSectionProps) {
  const [showEnvironmentModal, setShowEnvironmentModal] = useState(false)
  const [showGenerateEnvironmentModal, setShowGenerateEnvironmentModal] = useState(false)
  const [creativityLevel, setCreativityLevel] = useState(1) // Default to "Balanced"

  // Ensure selectedEnvironments is always an array
  const safeSelectedEnvironments = Array.isArray(selectedEnvironments)
    ? selectedEnvironments
    : []

  const activeEnvironments = environments.filter(env => env.isActive)

  const handleGenerateClick = () => {
    if (!prompt.trim()) {
      return
    }
    setShowGenerateEnvironmentModal(true)
  }

  const handleModalGenerate = (selectedEnvIds: string[]) => {
    setShowGenerateEnvironmentModal(false)
    onGenerate(selectedEnvIds, creativityLevel)
  }

  const handleEnvironmentSelection = (selectedEnvIds: string[]) => {
    setShowEnvironmentModal(false)
    // Update the selected environments through the parent component
    selectedEnvIds.forEach(envId => {
      if (!safeSelectedEnvironments.includes(envId)) {
        onToggleEnvironment(envId)
      }
    })
    // Remove unselected environments
    safeSelectedEnvironments.forEach(envId => {
      if (!selectedEnvIds.includes(envId)) {
        onToggleEnvironment(envId)
      }
    })
  }


  return (
    <>
    <Card>
      <CardHeader className="bg-gradient-to-r from-indigo-50 to-violet-50 dark:from-indigo-950/30 dark:to-violet-950/30 border-b">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Sparkles className="h-5 w-5 text-indigo-500" />
              AI Prompt
            </CardTitle>
            <CardDescription>Describe the data you want to generate</CardDescription>
          </div>

          {/* Environment Selection Dropdown */}
          <div className="flex items-center gap-2">
            <Label className="text-sm font-medium text-muted-foreground">Environments:</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowEnvironmentModal(true)}
              className="h-8 px-3 text-xs border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 dark:border-gray-700 dark:hover:border-indigo-700 dark:hover:bg-indigo-900/30"
            >
              <div className="flex items-center gap-2">
                <Layers className="h-3 w-3 text-indigo-500" />
                <span>
                  {isLoadingEnvironments
                    ? "Loading..."
                    : safeSelectedEnvironments.length === 0
                      ? "Select environments"
                      : `${safeSelectedEnvironments.length} selected`
                  }
                </span>
                <ChevronDown className="h-3 w-3 opacity-50" />
              </div>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 p-6">
        <Textarea
          placeholder="Describe the test data you need..."
          value={prompt}
          onChange={(e) => onPromptChange(e.target.value)}
          className="min-h-[150px] border-gray-200 focus:border-indigo-500 transition-all resize-none"
        />

        {/* Warning when no environments selected */}
        {safeSelectedEnvironments.length === 0 && activeEnvironments.length > 0 && (
          <div className="text-center py-3 text-sm text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
            ⚠️ No environments selected. Please select environments from the dropdown above.
          </div>
        )}

        {/* Creativity Control */}
        <div className="space-y-3">
          <CreativityControl
            value={creativityLevel}
            onChange={setCreativityLevel}
            disabled={isGenerating}
            compact={true}
          />
        </div>

        <div className="flex justify-center">
          <Button
            onClick={handleGenerateClick}
            disabled={isGenerating || !prompt.trim()}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 transition-all"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate with AI
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>

    {/* Environment Selection Modal for Environment Management */}
    <EnvironmentSelectionModal
      open={showEnvironmentModal}
      onOpenChange={setShowEnvironmentModal}
      environments={environments}
      selectedEnvironments={selectedEnvironments}
      onGenerate={handleEnvironmentSelection}
      isGenerating={false}
      title="Select Environments"
      description="Choose which environments to include in this data set. Selected environments will be used for data generation and randomization."
      buttonText="Update Selection"
    />

    {/* Environment Selection Modal for Generate */}
    <EnvironmentSelectionModal
      open={showGenerateEnvironmentModal}
      onOpenChange={setShowGenerateEnvironmentModal}
      environments={environments}
      selectedEnvironments={selectedEnvironments}
      onGenerate={handleModalGenerate}
      isGenerating={isGenerating}
      title="Generate Variables with AI"
      description="Select the environments where you want to generate test data variables. AI will create variable definitions and populate values for the selected environments."
      buttonText="Generate Variables"
    />
  </>
  )
}
