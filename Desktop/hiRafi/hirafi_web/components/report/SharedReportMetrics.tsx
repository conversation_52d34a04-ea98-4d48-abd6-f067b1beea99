import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  BarChart2,
  Server,
  Accessibility,
  Network,
  Info,
  AlertCircle,
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger
} from '@/components/ui/tooltip';

// Helper function for safe counting
const safeCount = (count: unknown): number => {
  return typeof count === 'number' ? count : 0;
};

function WebVitals({ metrics }: { metrics: any }) {
  // Check for performance metrics in multiple possible locations
  const performanceData = metrics?.tracingData?.performanceMetrics ||
                         metrics?.performanceMetrics ||
                         metrics?.pageMetrics;

  if (!performanceData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <BarChart2 className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Web Vitals</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <AlertCircle className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">No Web Vitals data available</p>
            <p className="text-sm text-gray-400 max-w-md mx-auto">
              Web performance metrics may not be enabled for this test or no data was collected during execution.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Convert existing performance metrics to Web Vitals format
  const webVitals = {
    // Use firstContentfulPaint as FCP (First Contentful Paint) - close to LCP for simple pages
    LCP: performanceData.firstContentfulPaint || performanceData.firstPaint || null,
    // Use domContentLoaded as a proxy for interactivity timing
    FID: performanceData.domContentLoaded && performanceData.domContentLoaded < 100 ? performanceData.domContentLoaded : null,
    // CLS is not available from basic metrics, set to 0 as default
    CLS: 0,
    // Use loadComplete as TTFB proxy
    TTFB: performanceData.loadComplete || null,
    // INP is not available from basic metrics
    INP: null
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <BarChart2 className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">Web Vitals</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  FCP
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>First Contentful Paint (FCP) measures loading performance. Should be below 1800ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!webVitals.LCP ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : webVitals.LCP > 1800 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{webVitals.LCP ? webVitals.LCP.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">First Contentful Paint (ms)</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  DCL
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>DOM Content Loaded measures when initial HTML is loaded. Should be below 100ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!webVitals.FID ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : webVitals.FID > 100 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{webVitals.FID ? webVitals.FID.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">DOM Content Loaded (ms)</div>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  FP
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>First Paint measures when first pixel is rendered. Should be below 1000ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!performanceData.firstPaint ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : performanceData.firstPaint > 1000 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{performanceData.firstPaint ? performanceData.firstPaint.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">First Paint (ms)</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  LC
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>Load Complete measures when page is fully loaded. Should be below 50ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!webVitals.TTFB ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : webVitals.TTFB > 50 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{webVitals.TTFB ? webVitals.TTFB.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">Load Complete (ms)</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  CT
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>Collection Time measures how long metrics collection took. Lower is better.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!performanceData.collectionTime ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : performanceData.collectionTime > 1000 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Slow</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Fast</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{performanceData.collectionTime ? performanceData.collectionTime.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">Collection Time (ms)</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function NetworkStatistics({ metrics }: { metrics: any }) {
  if (!metrics?.networkData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Server className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Network Statistics</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Network className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">No Network Statistics available</p>
            <p className="text-sm text-gray-400 max-w-md mx-auto">
              Network tracking may not be enabled for this test or no network activity was recorded during execution.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const networkData = metrics.networkData;
  const totalRequests = networkData.requests?.total || 1;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Server className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">Network Statistics</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">Request Types</h4>
            {Object.entries(networkData.requests?.byType || {}).map(([type, count]) => (
              <div key={type} className="flex items-center mb-2">
                <div className="w-24 capitalize">{type}</div>
                <div className="flex-1 mx-2">
                  <div className="h-2 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-indigo-500"
                      style={{
                        width: `${Math.min(100, (Number(count) / totalRequests) * 100)}%`,
                      }}
                    ></div>
                  </div>
                </div>
                <div className="w-20 text-right text-sm">
                  {String(count)} ({Math.round((Number(count) / totalRequests) * 100)}%)
                </div>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="text-sm font-medium mb-1">Total Transfers</div>
              <div className="text-2xl font-bold">{networkData.transferred?.total?.toFixed(2) || 'N/A'}</div>
              <div className="text-xs text-gray-500">{networkData.transferred?.unit || 'Bytes'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="text-sm font-medium mb-1">Request Status</div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <div className="text-sm">Success: {networkData.requests?.successful || 0}</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-rose-500"></div>
                <div className="text-sm">Failed: {networkData.requests?.failed || 0}</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function AccessibilityMetrics({ report }: { report: any }) {
  // Check for accessibility data in either enhancedMetrics or metrics field
  const violationsData = report?.enhancedMetrics?.accessibilityData?.violations ||
                         report?.metrics?.accessibilityData?.violations;

  if (!violationsData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Accessibility className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Accessibility Metrics</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Accessibility className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500">No accessibility data available for this test</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Accessibility className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">Accessibility Metrics</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/60 dark:to-gray-900/40 p-4 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Total Violations</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                  safeCount(violationsData.count) > 50
                  ? "bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300"
                  : safeCount(violationsData.count) > 20
                  ? "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300"
                  : "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300"
                }`}>
                  {safeCount(violationsData.count) > 50 ? "Critical" : safeCount(violationsData.count) > 20 ? "Warning" : "Good"}
                </span>
              </div>
              <div className="text-3xl font-bold mb-1">{safeCount(violationsData.count).toString()}</div>
              <div className="text-xs text-gray-500">Found during test execution</div>
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/60 dark:to-gray-900/40 p-4 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
              <h3 className="text-sm font-medium mb-3">Severity Breakdown</h3>
              {violationsData.bySeverity ? (
                <div className="space-y-2">
                  {Object.entries(violationsData.bySeverity).map(([severity, count]) => (
                    <div key={severity} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className={`w-3 h-3 rounded-full ${
                          severity === 'critical' ? 'bg-rose-500' :
                          severity === 'serious' ? 'bg-amber-500' :
                          severity === 'moderate' ? 'bg-yellow-500' : 'bg-blue-500'
                        }`}></span>
                        <span className="text-sm capitalize">{severity}</span>
                      </div>
                      <span className="text-sm font-bold">{count as React.ReactNode}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-500">No severity data available</div>
              )}
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/60 dark:to-gray-900/40 p-4 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
              <h3 className="text-sm font-medium mb-3">Top Issue Types</h3>
              <div className="space-y-2">
                {violationsData.byType ? (
                  Object.entries(violationsData.byType)
                    .sort((a, b) => (b[1] as number) - (a[1] as number))
                    .slice(0, 4)
                    .map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="text-sm truncate max-w-[180px]">{type}</div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 h-1.5 rounded-full mt-1">
                            <div
                              className="bg-indigo-500 h-1.5 rounded-full"
                              style={{ width: `${Math.min(100, (count as number) / (violationsData.count || 1) * 100)}%` }}>
                            </div>
                          </div>
                        </div>
                        <span className="text-sm font-bold ml-2">{count as React.ReactNode}</span>
                      </div>
                    ))
                ) : (
                  <div className="text-sm text-gray-500">No issue type data available</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SharedReportMetrics({ report }: { report: any }) {
  // Check for metrics data in either enhancedMetrics or metrics field
  const metricsData = report?.enhancedMetrics || report?.metrics;

  if (!metricsData) {
    return (
      <div className="text-center py-12">
        <Info className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <p className="text-gray-500">No advanced metrics data available for this test.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <WebVitals metrics={metricsData} />
        <NetworkStatistics metrics={metricsData} />
      </div>
      <AccessibilityMetrics report={{...report, enhancedMetrics: metricsData}} />
    </div>
  );
}
