import React from 'react';
import { Star } from 'lucide-react';
import Image from 'next/image';

const TestimonialSection = () => {
  const testimonials = [
    {
      quote: "HiRafi cut our test maintenance effort by 65%. Tests that used to break every sprint now fix themselves.",
      author: "<PERSON>",
      role: "QA Lead at TechCorp",
      company: "TechCorp",
      avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      stars: 5
    },
    {
      quote: "We eliminated our test automation backlog in weeks instead of months. The AI-generated tests found bugs our manual team missed.",
      author: "<PERSON>",
      role: "Director of Engineering",
      company: "DataFlow Systems",
      avatar: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      stars: 5
    },
    {
      quote: "As a non-technical product manager, I can now create and run tests myself. Game changer for our team dynamics.",
      author: "<PERSON>",
      role: "Product Manager",
      company: "SaaS Innovate",
      avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      stars: 4
    }
  ];

  return (
    <section id="testimonials" className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Clients Say</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Teams across industries are transforming their testing processes with HiRafi.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-gray-50 p-8 rounded-xl shadow-md hover:shadow-xl transition-shadow duration-300 flex flex-col"
            >
              <div className="flex mb-4">
                {[...Array(testimonial.stars)].map((_, i) => (
                  <Star key={i} size={20} fill="#FBBF24" color="#FBBF24" />
                ))}
                {[...Array(5 - testimonial.stars)].map((_, i) => (
                  <Star key={i} size={20} color="#E5E7EB" />
                ))}
              </div>

              <p className="text-gray-700 italic mb-6 flex-grow">"{testimonial.quote}"</p>

              <div className="flex items-center">
                <Image
                  src={testimonial.avatar}
                  alt={testimonial.author}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full mr-4 object-cover"
                />
                <div>
                  <p className="font-semibold">{testimonial.author}</p>
                  <p className="text-sm text-gray-500">{testimonial.role}, {testimonial.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <p className="text-xl text-gray-600 mb-6">Join hundreds of satisfied companies using HiRafi</p>
          <div className="flex flex-wrap justify-center gap-8 opacity-70">
            <div className="w-32 h-12 bg-gray-300 rounded flex items-center justify-center">Company 1</div>
            <div className="w-32 h-12 bg-gray-300 rounded flex items-center justify-center">Company 2</div>
            <div className="w-32 h-12 bg-gray-300 rounded flex items-center justify-center">Company 3</div>
            <div className="w-32 h-12 bg-gray-300 rounded flex items-center justify-center">Company 4</div>
            <div className="w-32 h-12 bg-gray-300 rounded flex items-center justify-center">Company 5</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;