/**
 * Base Connector Class
 * 
 * Provides common functionality for all connector implementations with dependency injection.
 * Replaces the singleton-based BaseConnector with a proper service implementation.
 */

import { EventEmitter } from 'events';
import { IBaseConnector, IConnectorConfig, IConnectorStats, ConnectorEventType } from '../interfaces/base-connector.interface.js';

/**
 * Abstract base class for all connectors
 * Implements common functionality and lifecycle management with dependency injection
 */
export abstract class BaseConnector extends EventEmitter implements IBaseConnector {
  protected nodeId: string | null = null;
  protected isInitialized = false;
  protected config: IConnectorConfig = {};
  protected stats: IConnectorStats;
  protected logger: any;

  /**
   * Constructor
   * @param connectorType Type of connector (http, websocket, etc.)
   * @param logger Logger instance
   * @param config Connector configuration
   */
  protected constructor(
    protected connectorType: string,
    logger: any,
    config: IConnectorConfig = {}
  ) {
    super();
    this.logger = logger;
    this.config = { ...this.config, ...config };
    this.stats = {
      connectorType,
      nodeId: null,
      isInitialized: false,
      isConnected: false,
      uptime: 0,
      connectionAttempts: 0,
      successfulConnections: 0,
      errors: 0,
      createdAt: new Date().toISOString()
    };

    if (config.enableLogging) {
      this.logger.debug(`${connectorType}: Connector created`);
    }
  }

  /**
   * Initialize the connector
   * @param config Configuration object
   */
  async initialize(config: IConnectorConfig = {}): Promise<void> {
    if (this.isInitialized) {
      this.logger.debug(`${this.connectorType}: Already initialized`);
      return;
    }

    this.config = { ...this.config, ...config };

    // Priority order for node ID:
    // 1. Explicitly configured nodeId in config
    // 2. Keep existing nodeId if already set
    if (config.nodeId) {
      this.nodeId = config.nodeId;
      this.logger.info(`${this.connectorType}: Using configured node ID: ${this.nodeId}`);
    }

    await this.doInitialize(this.config);
    this.isInitialized = true;
    this.stats.isInitialized = true;

    this.emit(ConnectorEventType.CONNECTED);
    this.logger.info(`${this.connectorType}: Initialized successfully`);
  }

  /**
   * Abstract method for connector-specific initialization
   * @param config Configuration object
   */
  protected abstract doInitialize(config: IConnectorConfig): Promise<void>;

  /**
   * Close the connector and cleanup resources
   */
  async close(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    await this.doClose();
    this.isInitialized = false;
    this.stats.isInitialized = false;
    this.removeAllListeners();

    this.emit(ConnectorEventType.DISCONNECTED);
    this.logger.info(`${this.connectorType}: Closed successfully`);
  }

  /**
   * Abstract method for connector-specific cleanup
   */
  protected abstract doClose(): Promise<void>;

  /**
   * Check if the connector is connected
   */
  abstract isConnected(): boolean;

  /**
   * Get the current node ID
   */
  getNodeId(): string | null {
    return this.nodeId;
  }

  /**
   * Update the node ID
   * @param nodeId New node ID
   */
  updateNodeId(nodeId: string): void {
    const oldNodeId = this.nodeId;
    this.nodeId = nodeId;
    this.stats.nodeId = nodeId;
    
    this.logger.info(`${this.connectorType}: Updated node ID from ${oldNodeId} to ${nodeId}`);
    this.emit(ConnectorEventType.NODE_ID_UPDATED, { oldNodeId, newNodeId: nodeId });
  }

  /**
   * Get connector type
   */
  getConnectorType(): string {
    return this.connectorType;
  }

  /**
   * Get connector statistics
   */
  getStats(): IConnectorStats {
    return {
      ...this.stats,
      nodeId: this.nodeId,
      isInitialized: this.isInitialized,
      isConnected: this.isConnected(),
      uptime: this.getUptime()
    };
  }

  /**
   * Get uptime in milliseconds
   */
  protected getUptime(): number {
    const createdAt = new Date(this.stats.createdAt).getTime();
    return Date.now() - createdAt;
  }

  /**
   * Increment connection attempts counter
   */
  protected incrementConnectionAttempts(): void {
    this.stats.connectionAttempts++;
  }

  /**
   * Increment successful connections counter
   */
  protected incrementSuccessfulConnections(): void {
    this.stats.successfulConnections++;
  }

  /**
   * Increment errors counter
   */
  protected incrementErrors(): void {
    this.stats.errors++;
  }

  /**
   * Get hub URL from configuration
   */
  protected getHubUrl(): string {
    return this.config.hubUrl || process.env.HUB_URL || 'http://localhost:3000';
  }

  /**
   * Get node secret key from configuration
   */
  protected getNodeSecretKey(): string {
    return this.config.secretKey || process.env.NODE_SECRET_KEY || 'test-node-secret-key-for-secure-communication';
  }

  /**
   * Handle errors consistently
   * @param error Error object
   * @param context Error context
   */
  protected handleError(error: any, context: string): void {
    this.incrementErrors();
    this.logger.error(`${this.connectorType}: ${context}: ${error}`);
    this.emit(ConnectorEventType.ERROR, { 
      error, 
      context, 
      connectorType: this.connectorType,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log operation success
   * @param operation Operation name
   * @param details Optional details
   */
  protected logOperation(operation: string, details?: any): void {
    if (this.config.enableLogging) {
      this.logger.debug(`${this.connectorType}: ${operation}`, details);
    }
  }

  /**
   * Validate configuration
   * @param config Configuration to validate
   * @param requiredFields Required configuration fields
   */
  protected validateConfig(config: IConnectorConfig, requiredFields: string[] = []): void {
    for (const field of requiredFields) {
      if (!config[field as keyof IConnectorConfig]) {
        throw new Error(`${this.connectorType}: Missing required configuration field: ${field}`);
      }
    }
  }

  /**
   * Get configuration value
   * @param key Configuration key
   * @param defaultValue Default value if key not found
   */
  protected getConfigValue<T>(key: keyof IConnectorConfig, defaultValue?: T): T {
    return (this.config[key] as T) !== undefined ? (this.config[key] as T) : defaultValue!;
  }

  /**
   * Update configuration
   * @param updates Configuration updates
   */
  protected updateConfig(updates: Partial<IConnectorConfig>): void {
    this.config = { ...this.config, ...updates };
    
    if (this.config.enableLogging) {
      this.logger.debug(`${this.connectorType}: Configuration updated`, updates);
    }
  }
}
