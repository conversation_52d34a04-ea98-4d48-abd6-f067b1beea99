/**
 * Centralized AI Method Configuration
 * Defines icons, colors, and styling for all AI method types
 */

import {
  CheckCircle2,
  XCircle,
  Terminal,
  Play,
  MousePointer,
  Clock,
  ArrowRight,
  Search,
  Keyboard,
  Zap,
  MessageSquare,
  Eye,
  Hash,
  Type,
  MousePointer2,
  ScrollText,
  Target,
  ToggleLeft
} from 'lucide-react';

export interface AIMethodConfig {
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  borderColor: string;
  badgeColor: string;
  displayName: string;
  category: 'action' | 'query' | 'assertion' | 'input' | 'navigation' | 'wait';
}

export const AI_METHOD_CONFIGS: Record<string, AIMethodConfig> = {
  // Action Methods
  aiAction: {
    icon: Zap,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
    borderColor: 'border-indigo-200 dark:border-indigo-800',
    badgeColor: 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/20 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800',
    displayName: 'AI Action',
    category: 'action'
  },
  aiTap: {
    icon: MousePointer,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    badgeColor: 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800',
    displayName: 'AI Tap',
    category: 'action'
  },
  aiRightClick: {
    icon: MousePointer2,
    color: 'text-cyan-600',
    bgColor: 'bg-cyan-50 dark:bg-cyan-900/20',
    borderColor: 'border-cyan-200 dark:border-cyan-800',
    badgeColor: 'bg-cyan-50 text-cyan-700 dark:bg-cyan-900/20 dark:text-cyan-300 border-cyan-200 dark:border-cyan-800',
    displayName: 'AI Right Click',
    category: 'action'
  },
  aiHover: {
    icon: Target,
    color: 'text-teal-600',
    bgColor: 'bg-teal-50 dark:bg-teal-900/20',
    borderColor: 'border-teal-200 dark:border-teal-800',
    badgeColor: 'bg-teal-50 text-teal-700 dark:bg-teal-900/20 dark:text-teal-300 border-teal-200 dark:border-teal-800',
    displayName: 'AI Hover',
    category: 'action'
  },
  aiScroll: {
    icon: ScrollText,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    borderColor: 'border-orange-200 dark:border-orange-800',
    badgeColor: 'bg-orange-50 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300 border-orange-200 dark:border-orange-800',
    displayName: 'AI Scroll',
    category: 'action'
  },

  // Input Methods
  aiInput: {
    icon: Type,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-800',
    badgeColor: 'bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300 border-purple-200 dark:border-purple-800',
    displayName: 'AI Input',
    category: 'input'
  },
  aiKeyboardPress: {
    icon: Keyboard,
    color: 'text-violet-600',
    bgColor: 'bg-violet-50 dark:bg-violet-900/20',
    borderColor: 'border-violet-200 dark:border-violet-800',
    badgeColor: 'bg-violet-50 text-violet-700 dark:bg-violet-900/20 dark:text-violet-300 border-violet-200 dark:border-violet-800',
    displayName: 'AI Keyboard',
    category: 'input'
  },

  // Query Methods
  aiQuery: {
    icon: MessageSquare,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    badgeColor: 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800',
    displayName: 'AI Query',
    category: 'query'
  },
  aiString: {
    icon: Type,
    color: 'text-sky-600',
    bgColor: 'bg-sky-50 dark:bg-sky-900/20',
    borderColor: 'border-sky-200 dark:border-sky-800',
    badgeColor: 'bg-sky-50 text-sky-700 dark:bg-sky-900/20 dark:text-sky-300 border-sky-200 dark:border-sky-800',
    displayName: 'AI String',
    category: 'query'
  },
  aiNumber: {
    icon: Hash,
    color: 'text-emerald-600',
    bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
    borderColor: 'border-emerald-200 dark:border-emerald-800',
    badgeColor: 'bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800',
    displayName: 'AI Number',
    category: 'query'
  },
  aiBoolean: {
    icon: ToggleLeft,
    color: 'text-lime-600',
    bgColor: 'bg-lime-50 dark:bg-lime-900/20',
    borderColor: 'border-lime-200 dark:border-lime-800',
    badgeColor: 'bg-lime-50 text-lime-700 dark:bg-lime-900/20 dark:text-lime-300 border-lime-200 dark:border-lime-800',
    displayName: 'AI Boolean',
    category: 'query'
  },

  // Assertion Methods
  aiAssertion: {
    icon: CheckCircle2,
    color: 'text-emerald-600',
    bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
    borderColor: 'border-emerald-200 dark:border-emerald-800',
    badgeColor: 'bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800',
    displayName: 'AI Assertion',
    category: 'assertion'
  },

  // Wait Methods
  aiWaitElement: {
    icon: Eye,
    color: 'text-amber-600',
    bgColor: 'bg-amber-50 dark:bg-amber-900/20',
    borderColor: 'border-amber-200 dark:border-amber-800',
    badgeColor: 'bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300 border-amber-200 dark:border-amber-800',
    displayName: 'AI Wait Element',
    category: 'wait'
  },
  aiLocate: {
    icon: Search,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    borderColor: 'border-yellow-200 dark:border-yellow-800',
    badgeColor: 'bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800',
    displayName: 'AI Locate',
    category: 'wait'
  },

  // Navigation Methods
  goto: {
    icon: ArrowRight,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    badgeColor: 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800',
    displayName: 'Navigation',
    category: 'navigation'
  },

  // Wait Methods
  sleep: {
    icon: Clock,
    color: 'text-amber-600',
    bgColor: 'bg-amber-50 dark:bg-amber-900/20',
    borderColor: 'border-amber-200 dark:border-amber-800',
    badgeColor: 'bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300 border-amber-200 dark:border-amber-800',
    displayName: 'Wait',
    category: 'wait'
  }
};

/**
 * Get AI method configuration by step type
 */
export function getAIMethodConfig(stepType: string): AIMethodConfig {
  return AI_METHOD_CONFIGS[stepType] || {
    icon: Terminal,
    color: 'text-gray-600',
    bgColor: 'bg-gray-50 dark:bg-gray-900/20',
    borderColor: 'border-gray-200 dark:border-gray-800',
    badgeColor: 'bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300 border-gray-200 dark:border-gray-800',
    displayName: stepType || 'Unknown',
    category: 'action'
  };
}

/**
 * Check if a step type is an AI method
 */
export function isAIMethod(stepType: string): boolean {
  return stepType.startsWith('ai') || stepType === 'goto' || stepType === 'sleep' || 
         stepType === 'ifElse' || stepType === 'forLoop' || stepType === 'whileLoop';
}

/**
 * Get card border styling for AI methods
 */
export function getAIMethodCardBorder(stepType: string, success: boolean): string {
  if (!success) {
    return "border-rose-200 dark:border-rose-800";
  }

  // Define specific border colors for each AI method type
  const borderColors: Record<string, string> = {
    aiAction: "border-l-4 border-l-indigo-400 dark:border-l-indigo-600",
    aiTap: "border-l-4 border-l-blue-400 dark:border-l-blue-600",
    aiRightClick: "border-l-4 border-l-cyan-400 dark:border-l-cyan-600",
    aiHover: "border-l-4 border-l-teal-400 dark:border-l-teal-600",
    aiScroll: "border-l-4 border-l-orange-400 dark:border-l-orange-600",
    aiInput: "border-l-4 border-l-purple-400 dark:border-l-purple-600",
    aiKeyboardPress: "border-l-4 border-l-violet-400 dark:border-l-violet-600",
    aiWaitElement: "border-l-4 border-l-amber-400 dark:border-l-amber-600",
    aiLocate: "border-l-4 border-l-yellow-400 dark:border-l-yellow-600",
    aiAssertion: "border-l-4 border-l-emerald-400 dark:border-l-emerald-600",
    aiQuery: "border-l-4 border-l-blue-400 dark:border-l-blue-600",
    aiString: "border-l-4 border-l-sky-400 dark:border-l-sky-600",
    aiNumber: "border-l-4 border-l-emerald-400 dark:border-l-emerald-600",
    aiBoolean: "border-l-4 border-l-lime-400 dark:border-l-lime-600",
    goto: "border-l-4 border-l-blue-400 dark:border-l-blue-600",
    sleep: "border-l-4 border-l-amber-400 dark:border-l-amber-600"
  };

  return borderColors[stepType] || "border-l-4 border-l-gray-400 dark:border-l-gray-600";
}
