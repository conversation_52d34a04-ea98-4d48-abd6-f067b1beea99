#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { glob } from "glob";
import { execSync } from "child_process";
import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import os from 'os';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  distDir: path.join(__dirname, "../dist"),
  obfuscatedDir: path.join(__dirname, "../obfuscated"),
  // Daha basit obfuscation ayarları
  maxWorkers: os.cpus().length - 1 || 1 // Kullanılabilir CPU sayısına göre worker sayısı
};

// Worker thread kodu
if (!isMainThread) {
  const { file, targetPath, configPath } = workerData;
  
  try {
    // Ensure the file exists
    if (!fs.existsSync(file)) {
      parentPort.postMessage({ success: false, file, error: 'File not found' });
      return;
    }

    // Run javascript-obfuscator CLI tool with config file
    execSync(`javascript-obfuscator ${file} --output ${targetPath} --config ${configPath}`);
    parentPort.postMessage({ success: true, file });
  } catch (err) {
    // Copy the original file as fallback
    try {
      fs.copyFileSync(file, targetPath);
      parentPort.postMessage({ success: false, file, error: err.message, fallback: true });
    } catch (copyErr) {
      parentPort.postMessage({ success: false, file, error: err.message, fallbackError: copyErr.message });
    }
  }
}
// Ana thread kodu
else {
  // Ensure directories exist
  if (!fs.existsSync(config.obfuscatedDir)) {
    fs.mkdirSync(config.obfuscatedDir, { recursive: true });
  }

  // Step 1: Obfuscate JavaScript files using CLI tool
  console.log("Obfuscating JavaScript files with parallel processing...");

  async function obfuscateFiles() {
    const jsFiles = await glob(`${config.distDir}/**/*.js`);
    const configPath = path.join(__dirname, "../obfuscator-config.json");
    
    // Dosyaları işlemek için worker'ları oluştur
    const batchSize = Math.ceil(jsFiles.length / config.maxWorkers);
    const batches = [];
    
    // Dosyaları batch'lere böl
    for (let i = 0; i < jsFiles.length; i += batchSize) {
      batches.push(jsFiles.slice(i, i + batchSize));
    }
    
    console.log(`Processing ${jsFiles.length} files with ${config.maxWorkers} workers in ${batches.length} batches`);
    
    // Her batch için bir promise oluştur
    const batchPromises = batches.map((batch, batchIndex) => {
      return new Promise((resolve) => {
        let completed = 0;
        const results = [];
        
        // Her dosya için bir worker oluştur
        batch.forEach(file => {
          const relativePath = path.relative(config.distDir, file);
          const targetPath = path.join(config.obfuscatedDir, relativePath);
          
          // Ensure directory exists
          const targetDir = path.dirname(targetPath);
          if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
          }
          
          const worker = new Worker(__filename, {
            workerData: { file, targetPath, configPath }
          });
          
          worker.on('message', (message) => {
            completed++;
            results.push(message);
            
            if (message.success) {
              console.log(`[Batch ${batchIndex + 1}] Obfuscated: ${relativePath}`);
            } else if (message.fallback) {
              console.log(`[Batch ${batchIndex + 1}] Copied original (obfuscation failed): ${relativePath}`);
              console.error(`[Batch ${batchIndex + 1}] Error obfuscating ${file}:`, message.error);
            } else {
              console.error(`[Batch ${batchIndex + 1}] Failed to process ${file}:`, message.error);
            }
            
            if (completed === batch.length) {
              resolve(results);
            }
          });
          
          worker.on('error', (err) => {
            completed++;
            results.push({ success: false, file, error: err.message });
            console.error(`[Batch ${batchIndex + 1}] Worker error for ${file}:`, err);
            
            if (completed === batch.length) {
              resolve(results);
            }
          });
        });
      });
    });
    
    // Tüm batch'lerin tamamlanmasını bekle
    const batchResults = await Promise.all(batchPromises);
    const allResults = batchResults.flat();
    
    const successCount = allResults.filter(r => r.success).length;
    const fallbackCount = allResults.filter(r => !r.success && r.fallback).length;
    const failCount = allResults.filter(r => !r.success && !r.fallback).length;
    
    console.log("\nObfuscation Summary:");
    console.log(`- Successfully obfuscated: ${successCount} files`);
    console.log(`- Fallback to original: ${fallbackCount} files`);
    console.log(`- Failed completely: ${failCount} files`);
    console.log("\nParallel obfuscation completed!");
  }

  obfuscateFiles().catch(err => {
    console.error("Obfuscation failed:", err);
    process.exit(1);
  });
}
