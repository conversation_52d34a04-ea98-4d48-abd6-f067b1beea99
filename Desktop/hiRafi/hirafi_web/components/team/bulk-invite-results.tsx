"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Check, X, AlertCircle, Copy } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

interface InvitationResult {
  email: string
  success: boolean
  message?: string
  inviteId?: string
}

interface BulkInviteResultsProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  results: InvitationResult[]
}

export function BulkInviteResults({ open, onOpenChange, results }: BulkInviteResultsProps) {
  const successCount = results.filter(result => result.success).length
  const errorCount = results.length - successCount
  
  const copyResults = () => {
    const text = results.map(result => {
      return `${result.email}: ${result.success ? '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' : '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'} - ${result.message || ''}`
    }).join('\n')
    
    navigator.clipboard.writeText(text)
      .then(() => {
        // Could use toast here to inform copied successfully
      })
      .catch(err => {
        // Clipboard write failed silently
      })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Davet Sonuçları
            <div className="flex items-center gap-2 ml-auto">
              <span className="text-sm font-normal text-green-600 flex items-center">
                <Check className="w-4 h-4 mr-1" /> {successCount} Başarılı
              </span>
              {errorCount > 0 && (
                <span className="text-sm font-normal text-red-600 flex items-center">
                  <X className="w-4 h-4 mr-1" /> {errorCount} Başarısız
                </span>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <Button
            variant="outline"
            size="sm"
            className="mb-2 text-xs"
            onClick={copyResults}
          >
            <Copy className="w-3 h-3 mr-1" /> Sonuçları Kopyala
          </Button>
          
          <ScrollArea className="h-[250px] rounded-md border">
            <div className="p-4 space-y-2">
              {results.map((result, index) => (
                <div 
                  key={index}
                  className={cn(
                    "p-3 rounded-md text-sm flex items-start",
                    result.success 
                      ? "bg-green-50 border border-green-100 dark:bg-green-900/20 dark:border-green-800" 
                      : "bg-red-50 border border-red-100 dark:bg-red-900/20 dark:border-red-800"
                  )}
                >
                  <div className="mr-2 mt-0.5">
                    {result.success ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    )}
                  </div>
                  <div>
                    <div className="font-medium">{result.email}</div>
                    <div className={cn(
                      "text-xs mt-1",
                      result.success ? "text-green-700 dark:text-green-400" : "text-red-700 dark:text-red-400"
                    )}>
                      {result.message || (result.success ? 'Davet başarıyla gönderildi' : 'Davet gönderilemedi')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
        
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Kapat
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 