/**
 * Draft Status Component
 * 
 * Shows the current draft status and provides manual controls for testing
 * This component can be added to the scenario creation page for debugging
 */

"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useScenarioCreationStore } from '@/store/scenarioCreationStore'
import { Clock, Save, Trash2, Download, Upload, RefreshCw } from 'lucide-react'

interface DraftStatusProps {
  className?: string
  showControls?: boolean
  onClearDraft: () => void
}

export function DraftStatus({ className = "", showControls = false, onClearDraft }: DraftStatusProps) {
  const {
    currentDraft,
    lastSaveTime,
    hasUnsavedChanges,
    autoSaveEnabled,
    isExpired,
    getDraftAge,
    exportDraft,
    importDraft,
    cleanupExpiredDrafts
  } = useScenarioCreationStore()

  const [importText, setImportText] = useState('')
  const [showImport, setShowImport] = useState(false)

  const formatTime = (timestamp: number | null) => {
    if (!timestamp) return 'Never'
    return new Date(timestamp).toLocaleTimeString()
  }

  const formatAge = (minutes: number) => {
    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${Math.floor(minutes)} min ago`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}h ${Math.floor(remainingMinutes)}m ago`
  }

  const handleExport = () => {
    const draftJson = exportDraft()
    navigator.clipboard.writeText(draftJson)
    alert('Draft exported to clipboard!')
  }

  const handleImport = () => {
    if (importDraft(importText)) {
      alert('Draft imported successfully!')
      setImportText('')
      setShowImport(false)
    } else {
      alert('Failed to import draft. Please check the format.')
    }
  }

  const handleCleanup = () => {
    const cleaned = cleanupExpiredDrafts()
    alert(`Cleaned up ${cleaned} expired drafts`)
  }

  if (!currentDraft) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Draft Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-500">No draft available</div>
          {showControls && (
            <div className="mt-3 space-y-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleCleanup}
                className="w-full"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Cleanup Expired
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  const draftAge = getDraftAge()
  const expired = isExpired()

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          Draft Status
          <div className="flex gap-1">
            {autoSaveEnabled && (
              <Badge variant="secondary" className="text-xs">
                Auto-save
              </Badge>
            )}
            {expired && (
              <Badge variant="destructive" className="text-xs">
                Expired
              </Badge>
            )}
            {hasUnsavedChanges && (
              <Badge variant="outline" className="text-xs">
                Unsaved
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">Created:</span>
            <span className="flex items-center">
              <Clock className="w-3 h-3 mr-1" />
              {formatAge(draftAge)}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-500">Last saved:</span>
            <span>{formatTime(lastSaveTime)}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-500">Draft ID:</span>
            <span className="font-mono text-xs">{currentDraft.id.slice(-8)}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-500">Steps:</span>
            <span>
              {currentDraft.formData.steps?.length || 0}
            </span>
          </div>
        </div>

        {showControls && (
          <div className="space-y-2 pt-2 border-t">
            <div className="grid grid-cols-2 gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleExport}
                className="text-xs"
              >
                <Download className="w-3 h-3 mr-1" />
                Export
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowImport(!showImport)}
                className="text-xs"
              >
                <Upload className="w-3 h-3 mr-1" />
                Import
              </Button>
            </div>
            
            <Button
              size="sm"
              variant="destructive"
              onClick={onClearDraft}
              className="w-full text-xs"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Clear Draft
            </Button>
            
            {showImport && (
              <div className="space-y-2">
                <textarea
                  value={importText}
                  onChange={(e) => setImportText(e.target.value)}
                  placeholder="Paste draft JSON here..."
                  className="w-full h-20 text-xs p-2 border rounded resize-none"
                />
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowImport(false)}
                    className="text-xs"
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleImport}
                    className="text-xs"
                  >
                    Import
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Export a debug version with all controls
export function DraftStatusDebug(props: Omit<DraftStatusProps, 'showControls'>) {
  return <DraftStatus {...props} showControls={true} />
}
