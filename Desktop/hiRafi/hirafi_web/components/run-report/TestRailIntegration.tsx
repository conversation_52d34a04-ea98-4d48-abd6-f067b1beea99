"use client"

import React from 'react';
import { RunReport } from '@/hooks/useRunReports';
import { TestManagementReportIntegration } from '@/components/shared/test-management-report-integration';

interface TestRailIntegrationProps {
  report: RunReport | null;
}

export function RunReportTestRailIntegration({ report }: TestRailIntegrationProps) {
  return <TestManagementReportIntegration report={report} provider="testrail" />;
}
