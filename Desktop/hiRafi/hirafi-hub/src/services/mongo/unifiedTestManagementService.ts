/**
 * Unified Test Management Service
 * Handles test result submission and test case creation for multiple providers
 */

import { logger } from '../../utils/logger.js';
import {
  sendResultToTestRail,
  getTestRailConfig
} from './testRailService.js';
import {
  createZephyrScaleExecution,
  getZephyrScaleConfig
} from './zephyrScaleService.js';
import {
  isMongoDBInitialized,
  db,
  scenariosCollection,
  reportsCollection
} from './dbConnection.js';

export type TestManagementProvider = 'testrail' | 'zephyrscale';

export interface UnifiedTestResult {
  success: boolean;
  message: string;
  data?: any;
  provider: TestManagementProvider;
  runId?: number | string;
  runUrl?: string;
  executionId?: string;
  executionUrl?: string;
}

export interface ScenarioTestManagementConfig {
  provider: TestManagementProvider;
  testCases: string[];
  sync: boolean;
}

export interface TestResultSubmissionRequest {
  reportData: any;
  testCaseId: string;
  provider: TestManagementProvider;
  teamId: string;
  companyId: string;
  runId?: number | string;
  scenarioName?: string;
}

/**
 * Submit test result to the appropriate test management provider
 */
export async function submitTestResult(request: TestResultSubmissionRequest): Promise<UnifiedTestResult> {
  try {
    const { reportData, testCaseId, provider, teamId, companyId, runId, scenarioName } = request;

    logger.info(`Submitting test result to ${provider} for test case ${testCaseId}`);

    switch (provider) {
      case 'testrail':
        return await submitTestRailResult(reportData, testCaseId, teamId, companyId, runId);

      case 'zephyrscale':
        return await submitZephyrScaleResult(reportData, testCaseId, teamId, companyId, scenarioName);
      
      default:
        return {
          success: false,
          message: `Unsupported test management provider: ${provider}`,
          provider
        };
    }
  } catch (error: any) {
    logger.error(`Error submitting test result: ${error.message}`);
    return {
      success: false,
      message: `Failed to submit test result: ${error.message}`,
      provider: request.provider
    };
  }
}

/**
 * Submit test result to TestRail
 */
async function submitTestRailResult(
  reportData: any,
  testCaseId: string,
  teamId: string,
  companyId: string,
  runId?: number | string
): Promise<UnifiedTestResult> {
  try {
    // Get TestRail configuration
    const configResult = await getTestRailConfig(teamId, companyId);
    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        message: "TestRail configuration not found",
        provider: 'testrail'
      };
    }

    // Use existing TestRail service
    const result = await sendResultToTestRail(
      reportData,
      configResult.config,
      testCaseId,
      runId ? Number(runId) : undefined
    );

    if (result.success) {
      return {
        success: true,
        message: "Test result submitted to TestRail successfully",
        data: result.data,
        provider: 'testrail'
      };
    } else {
      return {
        success: false,
        message: result.message,
        provider: 'testrail'
      };
    }
  } catch (error: any) {
    logger.error(`Error submitting TestRail result: ${error.message}`);
    return {
      success: false,
      message: `Failed to submit TestRail result: ${error.message}`,
      provider: 'testrail'
    };
  }
}

/**
 * Submit test result to Zephyr Scale
 */
async function submitZephyrScaleResult(
  reportData: any,
  testCaseKey: string,
  teamId: string,
  companyId: string,
  scenarioName?: string
): Promise<UnifiedTestResult> {
  try {
    // Get Zephyr Scale configuration
    const configResult = await getZephyrScaleConfig(teamId, companyId);
    if (!configResult.success || !configResult.data) {
      return {
        success: false,
        message: "Zephyr Scale configuration not found",
        provider: 'zephyrscale'
      };
    }

    const config = configResult.data;
    
    // Determine project key from test case key or configuration
    let projectKey = '';
    if (config.projectsData && config.projectsData.length > 0) {
      projectKey = config.projectsData[0].key || config.projectsData[0].id;
    }

    if (!projectKey) {
      return {
        success: false,
        message: "No project configured in Zephyr Scale settings",
        provider: 'zephyrscale'
      };
    }

    // Map test status to Zephyr Scale status
    const statusName = mapTestStatusToZephyrScale(reportData.status);
    
    // Create execution comment
    const comment = createExecutionComment(reportData, scenarioName);

    // Create test execution in Zephyr Scale
    const result = await createZephyrScaleExecution(
      config.apiToken,
      testCaseKey,
      projectKey,
      statusName,
      comment
    );

    if (result.success) {
      // Generate execution URL if possible
      const executionUrl = result.data?.id 
        ? `https://smartbear.atlassian.net/projects/${projectKey}/testexecutions/${result.data.id}`
        : undefined;

      return {
        success: true,
        message: "Test execution created successfully in Zephyr Scale",
        data: result.data,
        provider: 'zephyrscale',
        executionId: result.data?.id,
        executionUrl
      };
    } else {
      return {
        success: false,
        message: result.message,
        provider: 'zephyrscale'
      };
    }
  } catch (error: any) {
    logger.error(`Error submitting Zephyr Scale result: ${error.message}`);
    return {
      success: false,
      message: `Failed to submit Zephyr Scale result: ${error.message}`,
      provider: 'zephyrscale'
    };
  }
}

/**
 * Map test status to Zephyr Scale status
 */
function mapTestStatusToZephyrScale(status: string): string {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'passed':
    case 'success':
      return 'Pass';
    case 'failed':
    case 'error':
      return 'Fail';
    case 'stopped':
    case 'cancelled':
      return 'Not Executed';
    default:
      return 'Not Executed';
  }
}

/**
 * Create execution comment for Zephyr Scale
 */
function createExecutionComment(reportData: any, scenarioName?: string): string {
  let comment = `Automated test execution`;
  
  if (scenarioName) {
    comment += ` for scenario: ${scenarioName}`;
  }
  
  comment += `\n\nExecution Details:`;
  comment += `\n- Status: ${reportData.status || 'Unknown'}`;
  comment += `\n- Duration: ${reportData.duration || 'N/A'}`;
  comment += `\n- Executed at: ${new Date().toISOString()}`;
  
  if (reportData.error) {
    comment += `\n- Error: ${reportData.error}`;
  }
  
  return comment;
}

/**
 * Detect test management provider from scenario data
 */
export function detectProviderFromScenario(scenarioData: any): TestManagementProvider | null {
  if (scenarioData.testrailIntegration?.sync && scenarioData.testrailIntegration?.caseIds?.length > 0) {
    return 'testrail';
  }

  if (scenarioData.zephyrscaleIntegration?.sync && scenarioData.zephyrscaleIntegration?.caseIds?.length > 0) {
    return 'zephyrscale';
  }

  // Legacy support for old format
  if (scenarioData.testrailCases && scenarioData.testrailCases.length > 0) {
    return 'testrail';
  }

  if (scenarioData.zephyrscaleCases && scenarioData.zephyrscaleCases.length > 0) {
    return 'zephyrscale';
  }

  return null;
}

/**
 * Get test cases for a specific provider from scenario data
 */
export function getTestCasesForProvider(scenarioData: any, provider: TestManagementProvider): string[] {
  switch (provider) {
    case 'testrail':
      return scenarioData.testrailIntegration?.caseIds || scenarioData.testrailCases || [];
    case 'zephyrscale':
      return scenarioData.zephyrscaleIntegration?.caseIds || scenarioData.zephyrscaleCases || [];
    default:
      return [];
  }
}

/**
 * Submit test results for all test cases in a scenario
 */
export async function submitTestResultsForScenario(
  reportData: any,
  scenarioId: string,
  teamId: string,
  companyId: string,
  runId?: number | string
): Promise<UnifiedTestResult[]> {
  try {
    if (!isMongoDBInitialized() || !db || !scenariosCollection) {
      throw new Error("Database connection not established");
    }

    // Get scenario data
    const scenario = await scenariosCollection.findOne({
      _id: scenarioId as any,
      teamId,
      companyId
    });

    if (!scenario) {
      throw new Error(`Scenario ${scenarioId} not found`);
    }

    // Detect provider and get test cases
    const provider = detectProviderFromScenario(scenario);
    if (!provider) {
      logger.info(`No test management integration found for scenario ${scenarioId}`);
      return [];
    }

    const testCases = getTestCasesForProvider(scenario, provider);
    if (testCases.length === 0) {
      logger.info(`No test cases found for provider ${provider} in scenario ${scenarioId}`);
      return [];
    }

    logger.info(`Submitting results for ${testCases.length} test cases to ${provider}`);

    // Submit results for each test case
    const results: UnifiedTestResult[] = [];
    for (const testCaseId of testCases) {
      const result = await submitTestResult({
        reportData,
        testCaseId,
        provider,
        teamId,
        companyId,
        runId,
        scenarioName: scenario.name
      });

      results.push(result);
    }

    return results;
  } catch (error: any) {
    logger.error(`Error submitting test results for scenario: ${error.message}`);
    throw error;
  }
}

/**
 * Send report to unified test management system
 * This is the main entry point for automatic result synchronization
 */
export async function sendReportToUnifiedTestManagement(
  reportId: string,
  runId?: string
): Promise<UnifiedTestResult[]> {
  try {
    if (!isMongoDBInitialized() || !db || !reportsCollection || !scenariosCollection) {
      throw new Error("Database connection not established");
    }

    // Get the report
    const report = await reportsCollection.findOne({ id: reportId });
    if (!report) {
      throw new Error(`Report ${reportId} not found`);
    }

    // Get scenario data to detect test management integration
    const scenario = await scenariosCollection.findOne({
      _id: report.scenarioId as any,
      teamId: report.teamId,
      companyId: report.companyId
    });

    if (!scenario) {
      throw new Error(`Scenario ${report.scenarioId} not found`);
    }

    // Detect provider and check if sync is enabled
    const provider = detectProviderFromScenario(scenario);
    if (!provider) {
      logger.info(`No test management integration found for scenario ${report.scenarioId}`);
      return [];
    }

    // Check if sync is enabled for the detected provider
    const syncEnabled = provider === 'testrail'
      ? scenario.testrailIntegration?.sync
      : scenario.zephyrscaleIntegration?.sync;

    if (!syncEnabled) {
      logger.info(`Test management sync is disabled for scenario ${report.scenarioId}`);
      return [];
    }

    // Submit results for all test cases in the scenario
    return await submitTestResultsForScenario(
      report,
      report.scenarioId,
      report.teamId,
      report.companyId,
      runId
    );
  } catch (error: any) {
    logger.error(`Error sending report to unified test management: ${error.message}`);
    throw error;
  }
}

/**
 * Get scenario test management configuration
 */
export function getScenarioTestManagementConfig(scenarioData: any): ScenarioTestManagementConfig | null {
  const provider = detectProviderFromScenario(scenarioData);
  if (!provider) {
    return null;
  }

  const testCases = getTestCasesForProvider(scenarioData, provider);
  const sync = provider === 'testrail'
    ? scenarioData.testrailIntegration?.sync || false
    : scenarioData.zephyrscaleIntegration?.sync || false;

  return {
    provider,
    testCases,
    sync
  };
}
