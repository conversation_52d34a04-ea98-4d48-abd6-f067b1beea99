/**
 * Configuration Service - Dependency Injection Implementation
 * 
 * Replaces the ConfigurationManager singleton with a proper service
 * that can be injected and managed through DI containers.
 */

import { NodeConfig } from '../../models/types.js';
import { ConfigChangeEvent, ConfigWatcher, ConfigSource } from '../types/config-types.js';
import { ILoggerService, LoggerServiceFactory } from '../../utils/logger-service.js';

/**
 * Configuration service options
 */
export interface ConfigurationServiceOptions {
  enableWatchers?: boolean;
  enableHistory?: boolean;
  maxHistorySize?: number;
  enableLogging?: boolean;
}

/**
 * Configuration Service Interface
 */
export interface IConfigurationService {
  initialize(config: NodeConfig): void;
  getConfig(): NodeConfig;
  get<T>(key: string): T | undefined;
  set<T>(key: string, value: T, source?: ConfigSource): void;
  updateMultiple(updates: Record<string, any>, source?: ConfigSource): void;
  watch<T>(key: string, callback: ConfigWatcher<T>): void;
  unwatch<T>(key: string, callback: ConfigWatcher<T>): void;
  getHistory(limit?: number): ConfigChangeEvent[];
  clearHistory(): void;
  validate(): { isValid: boolean; errors: string[] };
  reset(): void;
}

/**
 * Configuration Service Implementation
 * 
 * Provides centralized configuration management with dependency injection support.
 * Eliminates singleton pattern while maintaining all functionality.
 */
export class ConfigurationService implements IConfigurationService {
  private config: NodeConfig | null = null;
  private watchers: Map<string, ConfigWatcher[]> = new Map();
  private configHistory: ConfigChangeEvent[] = [];
  private readonly options: ConfigurationServiceOptions;
  private readonly logger: ILoggerService;

  constructor(options: ConfigurationServiceOptions = {}) {
    this.options = {
      enableWatchers: true,
      enableHistory: true,
      maxHistorySize: 100,
      enableLogging: true,
      ...options
    };
    this.logger = LoggerServiceFactory.createServiceLogger('ConfigurationService');
  }

  /**
   * Initialize the configuration service with a configuration object
   * @param config Initial configuration
   */
  public initialize(config: NodeConfig): void {
    this.config = { ...config };
    
    if (this.options.enableLogging) {
      this.logger.serviceInit('ConfigurationService', 'Initialized with configuration');
    }
  }

  /**
   * Get the current configuration
   * @returns Current configuration object
   * @throws Error if configuration is not initialized
   */
  public getConfig(): NodeConfig {
    if (!this.config) {
      throw new Error('Configuration not initialized. Call initialize() first.');
    }
    return { ...this.config };
  }

  /**
   * Get a specific configuration value
   * @param key Configuration key (supports dot notation)
   * @returns Configuration value
   */
  public get<T>(key: string): T | undefined {
    if (!this.config) {
      return undefined;
    }

    return this.getNestedValue(this.config, key) as T;
  }

  /**
   * Set a configuration value
   * @param key Configuration key (supports dot notation)
   * @param value New value
   * @param source Source of the configuration change
   */
  public set<T>(key: string, value: T, source: ConfigSource = 'runtime'): void {
    if (!this.config) {
      throw new Error('Configuration not initialized. Call initialize() first.');
    }

    const oldValue = this.get<T>(key);
    this.setNestedValue(this.config, key, value);

    // Notify watchers if enabled
    if (this.options.enableWatchers) {
      this.notifyWatchers(key, oldValue, value, source);
    }

    // Add to history if enabled
    if (this.options.enableHistory) {
      this.addToHistory(key, oldValue, value, source);
    }
  }

  /**
   * Update multiple configuration values
   * @param updates Object with key-value pairs to update
   * @param source Source of the configuration changes
   */
  public updateMultiple(updates: Record<string, any>, source: ConfigSource = 'runtime'): void {
    Object.entries(updates).forEach(([key, value]) => {
      this.set(key, value, source);
    });
  }

  /**
   * Watch for changes to a specific configuration key
   * @param key Configuration key to watch
   * @param callback Callback function to call when value changes
   */
  public watch<T>(key: string, callback: ConfigWatcher<T>): void {
    if (!this.options.enableWatchers) {
      if (this.options.enableLogging) {
        this.logger.warn('ConfigurationService: Watchers are disabled');
      }
      return;
    }

    if (!this.watchers.has(key)) {
      this.watchers.set(key, []);
    }
    this.watchers.get(key)!.push(callback as ConfigWatcher);
  }

  /**
   * Remove a watcher for a specific configuration key
   * @param key Configuration key
   * @param callback Callback function to remove
   */
  public unwatch<T>(key: string, callback: ConfigWatcher<T>): void {
    const keyWatchers = this.watchers.get(key);
    if (keyWatchers) {
      const index = keyWatchers.indexOf(callback as ConfigWatcher);
      if (index > -1) {
        keyWatchers.splice(index, 1);
      }
    }
  }

  /**
   * Get configuration change history
   * @param limit Maximum number of history entries to return
   * @returns Array of configuration change events
   */
  public getHistory(limit?: number): ConfigChangeEvent[] {
    if (!this.options.enableHistory) {
      return [];
    }

    const history = [...this.configHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Clear configuration change history
   */
  public clearHistory(): void {
    this.configHistory = [];
  }

  /**
   * Validate the current configuration
   * @returns Validation result
   */
  public validate(): { isValid: boolean; errors: string[] } {
    if (!this.config) {
      return { isValid: false, errors: ['Configuration not initialized'] };
    }

    const errors: string[] = [];

    if (!this.config.name) {
      errors.push('Node name is required');
    }

    if (!this.config.hubUrl) {
      errors.push('Hub URL is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Reset configuration to uninitialized state
   */
  public reset(): void {
    this.config = null;
    this.watchers.clear();
    this.configHistory = [];
    
    if (this.options.enableLogging) {
      this.logger.serviceInit('ConfigurationService', 'Reset to uninitialized state');
    }
  }

  /**
   * Get nested value from object using dot notation
   * @param obj Object to get value from
   * @param path Dot-separated path
   * @returns Value at the specified path
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Set nested value in object using dot notation
   * @param obj Object to set value in
   * @param path Dot-separated path
   * @param value Value to set
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!(key in current)) {
        current[key] = {};
      }
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * Notify watchers of configuration changes
   * @param key Configuration key that changed
   * @param oldValue Previous value
   * @param newValue New value
   * @param source Source of the change
   */
  private notifyWatchers<T>(key: string, oldValue: T, newValue: T, source: ConfigSource): void {
    const keyWatchers = this.watchers.get(key);
    if (keyWatchers) {
      const event: ConfigChangeEvent<T> = {
        key,
        oldValue,
        newValue,
        source,
        timestamp: new Date()
      };

      keyWatchers.forEach(watcher => {
        try {
          watcher(event);
        } catch (error) {
          if (this.options.enableLogging) {
            this.logger.error(`ConfigurationService: Error in watcher for key ${key}:`, error);
          }
        }
      });
    }
  }

  /**
   * Add configuration change to history
   * @param key Configuration key that changed
   * @param oldValue Previous value
   * @param newValue New value
   * @param source Source of the change
   */
  private addToHistory<T>(key: string, oldValue: T, newValue: T, source: ConfigSource): void {
    const event: ConfigChangeEvent<T> = {
      key,
      oldValue,
      newValue,
      source,
      timestamp: new Date()
    };

    this.configHistory.push(event);

    // Limit history size
    if (this.configHistory.length > this.options.maxHistorySize!) {
      this.configHistory.shift();
    }
  }
}

/**
 * Configuration Service Factory
 *
 * Provides factory methods to create instances of the ConfigurationService.
 */
export class ConfigurationServiceFactory {
  /**
   * Create a new configuration service with specific options
   */
  static createService(options: ConfigurationServiceOptions = {}): IConfigurationService {
    return new ConfigurationService(options);
  }

  /**
   * Create a new configuration service with default options
   */
  static createDefaultService(): IConfigurationService {
    return new ConfigurationService({
      enableWatchers: true,
      enableHistory: true,
      maxHistorySize: 100,
      enableLogging: true
    });
  }
}
