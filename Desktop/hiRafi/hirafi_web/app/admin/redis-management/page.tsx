"use client"

import { useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminRouteGuard } from "@/lib/api/admin-auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  Database,
  Trash2,
  AlertTriangle,
  Shield,
  RefreshCw,
  CheckCircle,
  XCircle,
  Eye,
  Server,
  Key,
  Clock,
  Hash,
  List,
  FileText,
  ChevronDown,
  ChevronRight,
  X
} from "lucide-react"
import { toast } from "@/lib/utils/toast-utils"
import { purgeAllRedisData, getAllRedisData, deleteRedisKey } from "@/lib/api/admin-api"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function RedisManagementPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(false)
  const [redisData, setRedisData] = useState<any>(null)
  const [lastPurgeResult, setLastPurgeResult] = useState<{
    success: boolean
    message?: string
    timestamp: Date
  } | null>(null)
  const [expandedDatabases, setExpandedDatabases] = useState<Set<number>>(new Set())
  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(new Set())
  const [deletingKeys, setDeletingKeys] = useState<Set<string>>(new Set())

  const handleLoadRedisData = async () => {
    setIsLoadingData(true)
    try {
      const result = await getAllRedisData()

      if (result.success && result.data) {
        const redisDataPayload = result.data

        // Validate the data structure
        if (redisDataPayload?.databases && Array.isArray(redisDataPayload.databases)) {
          setRedisData(redisDataPayload)
          toast.success("Redis verileri başarıyla yüklendi!")
        } else {
          setRedisData({
            databases: [],
            totalDatabases: 0,
            totalKeys: 0,
            serverInfo: redisDataPayload?.serverInfo || {},
            timestamp: redisDataPayload?.timestamp || new Date().toISOString()
          })
          toast.success("Redis verileri yüklendi (veri bulunamadı)")
        }
      } else {
        toast.error(`Redis verileri yüklenirken hata: ${result.error || 'Bilinmeyen hata'}`)
        setRedisData(null)
      }
    } catch (error) {
      toast.error("Redis verileri yüklenirken beklenmeyen bir hata oluştu")
      setRedisData(null)
    } finally {
      setIsLoadingData(false)
    }
  }

  const handlePurgeAllRedis = async () => {
    setIsLoading(true)
    try {
      const result = await purgeAllRedisData()

      setLastPurgeResult({
        success: result.success,
        message: result.message || result.error,
        timestamp: new Date()
      })

      if (result.success) {
        toast.success("Tüm Redis verileri başarıyla temizlendi!")
        // Clear the displayed data since it's been purged
        setRedisData(null)
      } else {
        toast.error(`Redis temizleme hatası: ${result.error}`)
      }
    } catch (error) {
      toast.error("Redis temizleme sırasında beklenmeyen bir hata oluştu")
      setLastPurgeResult({
        success: false,
        message: "Beklenmeyen hata oluştu",
        timestamp: new Date()
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteKey = async (database: number, keyName: string) => {
    const keyId = `${database}:${keyName}`
    setDeletingKeys(prev => new Set(prev).add(keyId))

    try {
      const result = await deleteRedisKey(database, keyName)

      if (result.success) {
        toast.success(`Key "${keyName}" başarıyla silindi!`)
        // Redis verilerini yeniden yükle
        await handleLoadRedisData()
      } else {
        toast.error(`Key silme hatası: ${result.error || 'Bilinmeyen hata'}`)
      }
    } catch (error) {
      toast.error("Key silinirken beklenmeyen bir hata oluştu")
    } finally {
      setDeletingKeys(prev => {
        const newSet = new Set(prev)
        newSet.delete(keyId)
        return newSet
      })
    }
  }

  const toggleDatabaseExpansion = (dbNumber: number) => {
    setExpandedDatabases(prev => {
      const newSet = new Set(prev)
      if (newSet.has(dbNumber)) {
        newSet.delete(dbNumber)
      } else {
        newSet.add(dbNumber)
      }
      return newSet
    })
  }

  const toggleKeyExpansion = (keyId: string) => {
    setExpandedKeys(prev => {
      const newSet = new Set(prev)
      if (newSet.has(keyId)) {
        newSet.delete(keyId)
      } else {
        newSet.add(keyId)
      }
      return newSet
    })
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'string': return <FileText className="h-4 w-4" />
      case 'list': return <List className="h-4 w-4" />
      case 'set': return <Hash className="h-4 w-4" />
      case 'zset': return <Hash className="h-4 w-4" />
      case 'hash': return <Hash className="h-4 w-4" />
      default: return <Key className="h-4 w-4" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'string': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'list': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'set': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'zset': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'hash': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'error': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      case 'info': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  return (
    <AdminRouteGuard>
      <AdminLayout>
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Redis Management</h1>
            <p className="text-muted-foreground">
              Redis veritabanı yönetimi ve temizleme işlemleri
            </p>
          </div>

          {/* Warning Card */}
          <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
                <AlertTriangle className="h-5 w-5" />
                Dikkat
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-orange-700 dark:text-orange-300">
                Bu sayfadaki işlemler Redis veritabanındaki tüm verileri kalıcı olarak siler.
                Bu işlemler geri alınamaz ve sistem performansını etkileyebilir.
              </p>
            </CardContent>
          </Card>

          {/* Redis Data Viewer Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Redis Data Viewer
              </CardTitle>
              <CardDescription>
                Redis'teki tüm verileri görüntüleyin (ilk 100 key ile sınırlı)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <Button
                  onClick={handleLoadRedisData}
                  disabled={isLoadingData}
                  className="flex items-center gap-2"
                >
                  {isLoadingData ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  {isLoadingData ? 'Yükleniyor...' : 'Redis Verilerini Yükle'}
                </Button>

                {redisData && (
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Son güncelleme: {redisData?.timestamp ? new Date(redisData.timestamp).toLocaleString('tr-TR') : 'Bilinmiyor'}
                  </div>
                )}
              </div>

              {redisData && (
                <div className="space-y-4">
                  {/* Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                      <div className="flex items-center gap-2">
                        <Server className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        <div>
                          <p className="text-sm font-medium text-blue-800 dark:text-blue-200">Toplam Database</p>
                          <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{redisData?.totalDatabases || 0}</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg p-4">
                      <div className="flex items-center gap-2">
                        <Key className="h-5 w-5 text-green-600 dark:text-green-400" />
                        <div>
                          <p className="text-sm font-medium text-green-800 dark:text-green-200">Toplam Key</p>
                          <p className="text-2xl font-bold text-green-900 dark:text-green-100">{redisData?.totalKeys || 0}</p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-purple-50 dark:bg-purple-950 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                      <div className="flex items-center gap-2">
                        <Database className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                        <div>
                          <p className="text-sm font-medium text-purple-800 dark:text-purple-200">Aktif DB</p>
                          <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                            {redisData?.databases && Array.isArray(redisData.databases) && redisData.databases.length > 0
                              ? redisData.databases
                                  .filter((db: any) => db && typeof db.database !== 'undefined')
                                  .map((db: any) => db.database)
                                  .join(', ') || 'Yok'
                              : 'Yok'
                            }
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Databases */}
                  {redisData?.databases && Array.isArray(redisData.databases) && redisData.databases.length > 0 && redisData.databases
                    .filter((database: any) => database && typeof database.database !== 'undefined')
                    .map((database: any, dbIndex: number) => (
                    <Collapsible
                      key={database.database || dbIndex}
                      open={expandedDatabases.has(database.database)}
                      onOpenChange={() => toggleDatabaseExpansion(database.database)}
                    >
                      <Card className="border-l-4 border-l-blue-500">
                        <CollapsibleTrigger asChild>
                          <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <CardTitle className="flex items-center justify-between text-lg">
                              <div className="flex items-center gap-2">
                                <Database className="h-5 w-5" />
                                Database {database.database}
                                <Badge variant="secondary">{database.keyCount || 0} keys</Badge>
                              </div>
                              <div className="flex items-center gap-2">
                                {expandedDatabases.has(database.database) ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </div>
                            </CardTitle>
                          </CardHeader>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <CardContent className="pt-0">
                            <div className="space-y-2 max-h-96 overflow-y-auto">
                              {database?.keys && Array.isArray(database.keys) && database.keys.length > 0 && database.keys
                                .filter((keyData: any) => keyData && keyData.key)
                                .map((keyData: any, keyIndex: number) => {
                                  const keyId = `${database.database}:${keyData.key}`
                                  const isKeyExpanded = expandedKeys.has(keyId)
                                  const isDeletingKey = deletingKeys.has(keyId)

                                  return (
                                    <Collapsible
                                      key={`${database.database}-${keyIndex}`}
                                      open={isKeyExpanded}
                                      onOpenChange={() => toggleKeyExpansion(keyId)}
                                    >
                                      <div className="border rounded-lg overflow-hidden">
                                        <CollapsibleTrigger asChild>
                                          <div className="p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                            <div className="flex items-center justify-between">
                                              <div className="flex items-center gap-2">
                                                {getTypeIcon(keyData?.type || 'unknown')}
                                                <code className="text-sm font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                                  {keyData?.key || 'Unknown Key'}
                                                </code>
                                                <Badge className={`text-xs ${getTypeColor(keyData?.type || 'unknown')}`}>
                                                  {keyData?.type || 'unknown'}
                                                </Badge>
                                                {isKeyExpanded ? (
                                                  <ChevronDown className="h-3 w-3" />
                                                ) : (
                                                  <ChevronRight className="h-3 w-3" />
                                                )}
                                              </div>
                                              <div className="flex items-center gap-2">
                                                <div className="flex items-center gap-2 text-xs text-gray-500">
                                                  <Clock className="h-3 w-3" />
                                                  TTL: {keyData?.ttl || 'unknown'}
                                                  {(keyData?.size || 0) > 0 && (
                                                    <>
                                                      <span>•</span>
                                                      Size: {keyData.size}
                                                    </>
                                                  )}
                                                </div>
                                                <AlertDialog>
                                                  <AlertDialogTrigger asChild>
                                                    <Button
                                                      variant="ghost"
                                                      size="sm"
                                                      className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                                      disabled={isDeletingKey}
                                                      onClick={(e) => e.stopPropagation()}
                                                    >
                                                      {isDeletingKey ? (
                                                        <RefreshCw className="h-3 w-3 animate-spin" />
                                                      ) : (
                                                        <X className="h-3 w-3" />
                                                      )}
                                                    </Button>
                                                  </AlertDialogTrigger>
                                                  <AlertDialogContent>
                                                    <AlertDialogHeader>
                                                      <AlertDialogTitle className="flex items-center gap-2 text-red-600">
                                                        <AlertTriangle className="h-5 w-5" />
                                                        Key Silme Onayı
                                                      </AlertDialogTitle>
                                                      <AlertDialogDescription>
                                                        <strong>"{keyData?.key}"</strong> key'ini silmek istediğinizden emin misiniz?
                                                        <br />
                                                        <br />
                                                        Bu işlem geri alınamaz.
                                                      </AlertDialogDescription>
                                                    </AlertDialogHeader>
                                                    <AlertDialogFooter>
                                                      <AlertDialogCancel>İptal</AlertDialogCancel>
                                                      <AlertDialogAction
                                                        onClick={() => handleDeleteKey(database.database, keyData.key)}
                                                        className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                                                      >
                                                        Evet, Sil
                                                      </AlertDialogAction>
                                                    </AlertDialogFooter>
                                                  </AlertDialogContent>
                                                </AlertDialog>
                                              </div>
                                            </div>
                                          </div>
                                        </CollapsibleTrigger>
                                        <CollapsibleContent>
                                          <div className="px-3 pb-3 space-y-2">
                                            {keyData?.value && keyData.type !== 'info' && keyData.type !== 'error' && (
                                              <div className="bg-gray-50 dark:bg-gray-900 rounded p-3">
                                                <pre className="text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap break-all">
                                                  {typeof keyData.value === 'object'
                                                    ? JSON.stringify(keyData.value, null, 2)
                                                    : String(keyData.value)
                                                  }
                                                </pre>
                                              </div>
                                            )}

                                            {keyData?.type === 'info' && (
                                              <div className="text-sm text-yellow-700 dark:text-yellow-300 italic p-3 bg-yellow-50 dark:bg-yellow-950 rounded">
                                                {keyData?.value || 'No info available'}
                                              </div>
                                            )}

                                            {keyData?.type === 'error' && (
                                              <div className="text-sm text-red-700 dark:text-red-300 italic p-3 bg-red-50 dark:bg-red-950 rounded">
                                                {keyData?.value || 'Error occurred'}
                                              </div>
                                            )}
                                          </div>
                                        </CollapsibleContent>
                                      </div>
                                    </Collapsible>
                                  )
                                })}
                              </div>
                            </CardContent>
                          </CollapsibleContent>
                        </Card>
                      </Collapsible>
                    ))}

                  {(!redisData?.databases || !Array.isArray(redisData.databases) || redisData.databases.length === 0) && (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Redis'te hiç veri bulunamadı</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Redis Purge All Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Complete Redis Purge
              </CardTitle>
              <CardDescription>
                Tüm Redis veritabanlarındaki tüm verileri temizler (FLUSHALL komutu)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Shield className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
                  <div className="space-y-2">
                    <h4 className="font-semibold text-red-800 dark:text-red-200">
                      Kritik Uyarı - Bu İşlem:
                    </h4>
                    <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                      <li>• Tüm Redis veritabanlarındaki TÜM verileri siler</li>
                      <li>• BullMQ kuyruklarını temizler</li>
                      <li>• Aktif test verilerini siler</li>
                      <li>• Önbellek verilerini siler</li>
                      <li>• Session verilerini siler</li>
                      <li>• Bu işlem GERİ ALINAMAZ</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Last Operation Result */}
              {lastPurgeResult && (
                <div className={`border rounded-lg p-4 ${
                  lastPurgeResult.success 
                    ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800' 
                    : 'bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800'
                }`}>
                  <div className="flex items-start gap-3">
                    {lastPurgeResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
                    )}
                    <div>
                      <h4 className={`font-semibold ${
                        lastPurgeResult.success 
                          ? 'text-green-800 dark:text-green-200' 
                          : 'text-red-800 dark:text-red-200'
                      }`}>
                        Son İşlem Sonucu
                      </h4>
                      <p className={`text-sm ${
                        lastPurgeResult.success 
                          ? 'text-green-700 dark:text-green-300' 
                          : 'text-red-700 dark:text-red-300'
                      }`}>
                        {lastPurgeResult.message}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {lastPurgeResult.timestamp.toLocaleString('tr-TR')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Button */}
              <div className="flex justify-end">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button 
                      variant="destructive" 
                      disabled={isLoading}
                      className="flex items-center gap-2"
                    >
                      {isLoading ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                      {isLoading ? 'Temizleniyor...' : 'Tüm Redis Verilerini Temizle'}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle className="flex items-center gap-2 text-red-600">
                        <AlertTriangle className="h-5 w-5" />
                        Kritik İşlem Onayı
                      </AlertDialogTitle>
                      <AlertDialogDescription className="space-y-3">
                        <p>
                          <strong>TÜM Redis veritabanlarındaki TÜM verileri silmek</strong> üzeresiniz.
                        </p>
                        <p>
                          Bu işlem şunları etkileyecek:
                        </p>
                        <ul className="list-disc list-inside space-y-1 text-sm">
                          <li>Tüm BullMQ kuyrukları</li>
                          <li>Aktif test verileri</li>
                          <li>Önbellek verileri</li>
                          <li>Session verileri</li>
                          <li>Geçici sistem verileri</li>
                        </ul>
                        <p className="font-semibold text-red-600">
                          Bu işlem geri alınamaz. Devam etmek istediğinizden emin misiniz?
                        </p>
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>İptal</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handlePurgeAllRedis}
                        className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                      >
                        Evet, Tüm Verileri Sil
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>

          {/* Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Redis Hakkında
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                <p>
                  Redis, sistemde önbellek, kuyruk yönetimi ve geçici veri depolama için kullanılır.
                </p>
                <p>
                  <strong>Normal durumda</strong> Redis verilerini manuel olarak temizlemeniz gerekmez. 
                  Sistem otomatik olarak eski verileri temizler.
                </p>
                <p>
                  <strong>Bu işlemi sadece şu durumlarda kullanın:</strong>
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Sistem tamamen sıfırlanması gerektiğinde</li>
                  <li>Kritik hata durumlarında</li>
                  <li>Geliştirme/test ortamında temiz başlangıç için</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  )
}
