/**
 * Run Status Management Utilities
 * Centralized status fetching and management logic
 */

import * as runApi from '@/lib/api/run-api';

/**
 * Status request manager for handling concurrent requests
 */
class StatusRequestManager {
  private activeRequests = new Map<string, AbortController>();
  private lastUpdateTime = 0;
  private readonly MIN_UPDATE_INTERVAL = 1000; // 1 second

  /**
   * Fetches status for a single run
   * Automatically cancels previous requests for the same run
   */
  async fetchRunStatus(runId: string): Promise<{
    success: boolean;
    status?: any;
    error?: string;
    aborted?: boolean;
  }> {
    try {
      // Cancel previous request for this run
      this.cancelRequest(runId);

      // Create new controller
      const controller = new AbortController();
      this.activeRequests.set(runId, controller);

      // Make API call
      const response = await runApi.getRunStatus(runId, controller.signal);

      // Clean up
      this.activeRequests.delete(runId);

      if (!response.success) {
        if (response.aborted) {
          return { success: false, aborted: true };
        }
        return {
          success: false,
          error: response.error || `Failed to fetch status for run ${runId}`
        };
      }

      return {
        success: true,
        status: response.data
      };
    } catch (err: any) {
      // Handle abort errors
      if (err.name === 'AbortError' || err.message?.includes('aborted')) {
        return { success: false, aborted: true };
      }

      return {
        success: false,
        error: err.message || `Failed to fetch status for run ${runId}`
      };
    }
  }

  /**
   * Fetches all active runs
   * Implements rate limiting to prevent excessive requests
   */
  async fetchActiveRuns(bypassRateLimit: boolean = false): Promise<{
    success: boolean;
    runs?: any[];
    error?: string;
    aborted?: boolean;
  }> {
    try {
      // Rate limiting check (can be bypassed for critical operations)
      const now = Date.now();
      if (!bypassRateLimit && now - this.lastUpdateTime < this.MIN_UPDATE_INTERVAL) {
        return { success: false, error: 'Rate limited' };
      }

      // Cancel all previous requests
      this.cancelAllRequests();

      // Create new controller
      const controller = new AbortController();
      this.lastUpdateTime = now;

      // Make API call
      const response = await runApi.getActiveRuns(controller.signal);

      if (!response.success) {
        if (response.aborted) {
          return { success: false, aborted: true };
        }
        return {
          success: false,
          error: response.error || 'Failed to fetch active runs'
        };
      }

      // Validate response format
      const runs = response.runs || response.data?.runs;
      if (!runs || !Array.isArray(runs)) {
        return {
          success: false,
          error: 'Invalid response format from server'
        };
      }

      return {
        success: true,
        runs: runs
      };
    } catch (err: any) {
      // Handle abort errors
      if (err.name === 'AbortError' || err.message?.includes('aborted')) {
        return { success: false, aborted: true };
      }

      return {
        success: false,
        error: err.message || 'Failed to fetch active runs'
      };
    }
  }

  /**
   * Cancels a specific request
   */
  private cancelRequest(runId: string): void {
    const controller = this.activeRequests.get(runId);
    if (controller) {
      controller.abort();
      this.activeRequests.delete(runId);
    }
  }

  /**
   * Cancels all active requests
   */
  private cancelAllRequests(): void {
    this.activeRequests.forEach((controller, runId) => {
      controller.abort();
    });
    this.activeRequests.clear();
  }

  /**
   * Cleanup method for component unmount
   */
  cleanup(): void {
    this.cancelAllRequests();
    this.lastUpdateTime = 0;
  }
}

// Export singleton instance
export const statusManager = new StatusRequestManager();

/**
 * Hook-friendly wrapper for single run status
 */
export async function getRunStatus(runId: string) {
  return statusManager.fetchRunStatus(runId);
}

/**
 * Hook-friendly wrapper for active runs
 */
export async function getActiveRuns(bypassRateLimit: boolean = false) {
  return statusManager.fetchActiveRuns(bypassRateLimit);
}

/**
 * Cleanup function for hooks
 */
export function cleanupStatusRequests() {
  statusManager.cleanup();
}
