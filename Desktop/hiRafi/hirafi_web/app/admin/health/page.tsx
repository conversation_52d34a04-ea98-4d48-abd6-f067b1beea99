"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { RefreshCw, Server, Activity, Clock } from "lucide-react"
import { adminApi } from "@/lib/api"

export default function HealthPage() {
  const [healthData, setHealthData] = useState<any>(null)
  const [nodes, setNodes] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchHealthData = async () => {
    try {
      setRefreshing(true)
      setError(null) // Clear previous errors
      const response = await adminApi.getHealthData()

      if (response.success) {
        setHealthData(response.data)
      } else {
        const errorMessage = response.error || "Unknown error occurred while fetching health data"
        setError(errorMessage)
      }
      setRefreshing(false)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      setError(errorMessage)
      setRefreshing(false)
    }
  }

  const fetchNodes = async () => {
    try {
      // We'll use the health data to get node information instead of a separate API call
      // This is because the admin health endpoint now includes detailed node information
      if (healthData && healthData.nodes && healthData.nodes.details) {
        setNodes(healthData.nodes.details)
      } else {
        // Fallback to the old API if needed
        const response = await adminApi.getNodes()

        if (response.success && (response.nodes || response.data?.nodes)) {
          setNodes(response.nodes || response.data.nodes)
        } else {
          const errorMessage = response.error || "Unknown error occurred while fetching nodes"
          // Don't set error state here to avoid overriding health data errors
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      // Don't set error state here to avoid overriding health data errors
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      await fetchHealthData()
      // Call fetchNodes after health data is loaded since it may use that data
      await fetchNodes()
      setIsLoading(false)
    }

    loadData()

    // Her 30 saniyede bir otomatik yenile
    const interval = setInterval(() => {
      fetchHealthData().then(() => fetchNodes())
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  const handleRefresh = () => {
    fetchHealthData().then(() => fetchNodes())
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-500"
      case "busy":
        return "bg-yellow-500"
      case "inactive":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-[50vh] items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">Sistem durumu yükleniyor...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Hub-Node Sağlık Durumu</h1>
            <p className="text-muted-foreground">
              Test Hub ve Node'ların durumunu izleyin
            </p>
          </div>
          <Button onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Yenileniyor...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Yenile
              </>
            )}
          </Button>
        </div>

        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
            <TabsTrigger value="nodes">Node'lar</TabsTrigger>
            <TabsTrigger value="tests">Test Durumu</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Hub Durumu</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2">
                    <div className={`h-3 w-3 rounded-full ${healthData?.status === "UP" ? "bg-green-500" : "bg-red-500"}`}></div>
                    <div className="text-2xl font-bold">{healthData?.status || "Bilinmiyor"}</div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Son güncelleme: {healthData?.timestamp ? new Date(healthData.timestamp).toLocaleString() : "Bilinmiyor"}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Node Durumu</CardTitle>
                  <Server className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{healthData?.nodes?.available || 0} / {healthData?.nodes?.total || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Aktif Node / Toplam Node
                  </p>
                  <div className="mt-2 flex items-center space-x-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span className="text-xs">Aktif: {healthData?.nodes?.available || 0}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                    <span className="text-xs">Meşgul: {healthData?.nodes?.busy || 0}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Test Durumu</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{healthData?.tests?.running || 0} / {healthData?.tests?.queued || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Çalışan Test / Sıradaki Test
                  </p>
                  <div className="mt-2 flex items-center space-x-2">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span className="text-xs">Çalışan: {healthData?.tests?.running || 0}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                    <span className="text-xs">Sırada: {healthData?.tests?.queued || 0}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Sistem Bilgileri</CardTitle>
                  <CardDescription>
                    Test Hub sistem bilgileri ve yapılandırması
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="mb-2 text-sm font-medium">Genel Bilgiler</h3>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Versiyon:</span>
                          <span className="text-sm font-medium">{healthData?.version || "Bilinmiyor"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Ortam:</span>
                          <span className="text-sm font-medium">{healthData?.environment || "Bilinmiyor"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Port:</span>
                          <span className="text-sm font-medium">{healthData?.config?.port || "Bilinmiyor"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Çalışma Süresi:</span>
                          <span className="text-sm font-medium">{healthData?.system?.uptime ? `${Math.floor(healthData.system.uptime / 60 / 60)} saat ${Math.floor((healthData.system.uptime / 60) % 60)} dakika` : "Bilinmiyor"}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="mb-2 text-sm font-medium">Bağlantı Durumu</h3>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">WebSocket:</span>
                          <Badge variant={healthData?.config?.websocketEnabled ? "default" : "outline"}>
                            {healthData?.config?.websocketEnabled ? "Aktif" : "Pasif"}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Redis:</span>
                          <Badge variant={healthData?.config?.redisEnabled ? "default" : "outline"}>
                            {healthData?.config?.redisEnabled ? "Aktif" : "Pasif"}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">MongoDB:</span>
                          <Badge variant={healthData?.connections?.mongodb?.initialized ? "default" : "outline"}>
                            {healthData?.connections?.mongodb?.initialized ? "Bağlı" : "Bağlantı Yok"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Redis Kuyruk Durumu</CardTitle>
                  <CardDescription>
                    Redis kuyruk bilgileri ve istatistikleri
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {healthData?.connections?.redis?.connected ? (
                    <div className="space-y-4">
                      {Object.entries(healthData.connections.redis.queues).map(([queueName, queueData]: [string, any]) => (
                        <div key={queueName} className="border-b pb-2 last:border-0">
                          <h3 className="text-sm font-medium">{queueName}</h3>
                          <div className="mt-1 grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="text-muted-foreground">Bekleyen:</span> {queueData.waiting || 0}
                            </div>
                            <div>
                              <span className="text-muted-foreground">Aktif:</span> {queueData.active || 0}
                            </div>
                            <div>
                              <span className="text-muted-foreground">Tamamlanan:</span> {queueData.completed || 0}
                            </div>
                            <div>
                              <span className="text-muted-foreground">Başarısız:</span> {queueData.failed || 0}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex h-24 items-center justify-center">
                      <p className="text-sm text-muted-foreground">Redis bağlantısı yok veya kuyruk bilgisi alınamadı</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Sistem Kaynakları</CardTitle>
                <CardDescription>
                  Bellek ve işlemci kullanımı
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h3 className="mb-2 text-sm font-medium">Bellek Kullanımı</h3>
                    <div className="space-y-1">
                      {healthData?.system?.memory && Object.entries(healthData.system.memory).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <span className="text-sm text-muted-foreground">{key}:</span>
                          <span className="text-sm font-medium">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className="mb-2 text-sm font-medium">Sistem Bilgileri</h3>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Node.js:</span>
                        <span className="text-sm font-medium">{healthData?.system?.nodeVersion || "Bilinmiyor"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Platform:</span>
                        <span className="text-sm font-medium">{healthData?.system?.platform || "Bilinmiyor"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Mimari:</span>
                        <span className="text-sm font-medium">{healthData?.system?.arch || "Bilinmiyor"}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="nodes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Node Listesi</CardTitle>
                <CardDescription>
                  Sistemdeki tüm test node'ları ve durumları
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-5 border-b bg-muted/50 p-2 font-medium">
                    <div>Node ID</div>
                    <div>İsim</div>
                    <div>Durum</div>
                    <div>Yetenekler</div>
                    <div>Son Güncelleme</div>
                  </div>
                  <div className="divide-y">
                    {nodes.length > 0 ? (
                      nodes.map((node) => (
                        <div key={node.id} className="grid grid-cols-5 p-2">
                          <div className="truncate font-mono text-xs">{node.id}</div>
                          <div>{node.name}</div>
                          <div>
                            <Badge className={getStatusColor(node.status)}>
                              {node.status === "available" ? "Aktif" :
                               node.status === "busy" ? "Meşgul" :
                               node.status === "inactive" ? "Pasif" : node.status}
                            </Badge>
                          </div>
                          <div>
                            <div className="flex flex-wrap gap-1">
                              {node.capabilities && node.capabilities.length > 0 ? (
                                node.capabilities.map((cap: string, index: number) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {cap}
                                  </Badge>
                                ))
                              ) : (
                                <span className="text-xs text-muted-foreground">Yok</span>
                              )}
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {node.lastHeartbeat ? new Date(node.lastHeartbeat).toLocaleString() : "Bilinmiyor"}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-muted-foreground">
                        Sistemde kayıtlı node bulunamadı
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tests" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Test Durumu</CardTitle>
                <CardDescription>
                  Çalışan ve sıradaki testlerin durumu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="mb-2 text-sm font-medium">Özet</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="pt-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold">{healthData?.tests?.running || 0}</div>
                            <p className="text-xs text-muted-foreground">Çalışan Test</p>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="pt-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold">{healthData?.tests?.queued || 0}</div>
                            <p className="text-xs text-muted-foreground">Sıradaki Test</p>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="pt-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold">{healthData?.tests?.maxConcurrent || 0}</div>
                            <p className="text-xs text-muted-foreground">Maksimum Eşzamanlı</p>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
