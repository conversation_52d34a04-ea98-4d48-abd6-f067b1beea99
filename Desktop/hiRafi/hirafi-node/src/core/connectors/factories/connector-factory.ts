/**
 * Connector Factory
 * 
 * Factory for creating and configuring connectors with proper
 * dependency injection. Replaces the singleton-based pattern.
 */

import { logger } from '../../../utils/logger.js';
import { ConnectorServiceRegistry } from '../registry/connector-service-registry.js';
import { IBaseConnector } from '../interfaces/base-connector.interface.js';
import { IConnectorFactory, IConnectorFactoryConfig } from '../interfaces/connector-factory.interface.js';
import { IConnectorManager } from '../interfaces/connector-manager.interface.js';
import { ConnectorType } from '../types/connector-types.js';

/**
 * Connector Factory
 * 
 * Creates connectors with proper dependency injection
 * and service registry management.
 */
export class ConnectorFactory implements IConnectorFactory {
  private static serviceRegistries: Map<string, ConnectorServiceRegistry> = new Map();
  private static connectorManagers: Map<string, IConnectorManager> = new Map();

  /**
   * Create a connector manager with all dependencies
   */
  static async createConnectorManager(config: IConnectorFactoryConfig): Promise<IConnectorManager> {
    const managerId = config.nodeId || 'default';

    logger.info(`ConnectorFactory: Creating connector manager for node ${managerId}`);

    try {
      // Check if manager already exists
      if (ConnectorFactory.connectorManagers.has(managerId)) {
        logger.debug(`ConnectorFactory: Reusing existing connector manager for node ${managerId}`);
        return ConnectorFactory.connectorManagers.get(managerId)!;
      }

      // Create service registry for this manager
      const serviceRegistry = new ConnectorServiceRegistry({
        nodeId: managerId,
        enableLogging: config.enableLogging || false,
        enableCircularDependencyDetection: config.enableCircularDependencyDetection !== false,
        hubUrl: config.hubUrl,
        secretKey: config.secretKey
      });

      // Initialize the service registry
      await serviceRegistry.initialize();

      // Register external services if provided
      if (config.nodeId) {
        serviceRegistry.registerExternalService('nodeId', config.nodeId);
      }
      
      if (config.clientId) {
        serviceRegistry.registerExternalService('clientId', config.clientId);
      }

      // Get the connector manager from the registry
      const connectorManager = await serviceRegistry.getConnectorManager();

      // Initialize the connector manager (CRITICAL FIX: This was missing!)
      await connectorManager.initialize({
        enableLogging: config.enableLogging || false,
        defaultConnectorType: config.defaultConnectorType,
        hubUrl: config.hubUrl,
        secretKey: config.secretKey,
        nodeId: config.nodeId,
        clientId: config.clientId
      });

      // Store references for management
      ConnectorFactory.serviceRegistries.set(managerId, serviceRegistry);
      ConnectorFactory.connectorManagers.set(managerId, connectorManager);

      logger.info(`ConnectorFactory: Successfully created connector manager for node ${managerId}`);
      return connectorManager;

    } catch (error: any) {
      logger.error(`ConnectorFactory: Failed to create connector manager for node ${managerId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a connector by type
   * @param type Connector type
   * @param config Connector configuration
   */
  async createConnector(type: string, config: any = {}): Promise<IBaseConnector> {
    // Get or create default connector manager
    const managerId = config.nodeId || 'default';
    let connectorManager = ConnectorFactory.connectorManagers.get(managerId);

    if (!connectorManager) {
      connectorManager = await ConnectorFactory.createConnectorManager({
        nodeId: managerId,
        enableLogging: config.enableLogging || false,
        hubUrl: config.hubUrl,
        secretKey: config.secretKey,
        clientId: config.clientId
      });
    }

    return connectorManager.createConnector(type, config);
  }

  /**
   * Create HTTP connector
   * @param config Connector configuration
   */
  async createHttpConnector(config: any = {}): Promise<IBaseConnector> {
    return this.createConnector(ConnectorType.HTTP, config);
  }

  /**
   * Create WebSocket connector
   * @param config Connector configuration
   */
  async createWebSocketConnector(config: any = {}): Promise<IBaseConnector> {
    return this.createConnector(ConnectorType.WEBSOCKET, config);
  }

  /**
   * Create Redis connector
   * @param config Connector configuration
   */
  async createRedisConnector(config: any = {}): Promise<IBaseConnector> {
    return this.createConnector(ConnectorType.REDIS, config);
  }

  /**
   * Get available connector types
   */
  getAvailableTypes(): string[] {
    return [ConnectorType.HTTP, ConnectorType.REDIS, ConnectorType.WEBSOCKET];
  }

  /**
   * Validate connector type
   * @param type Connector type
   */
  isValidType(type: string): boolean {
    return this.getAvailableTypes().includes(type.toLowerCase() as ConnectorType);
  }

  /**
   * Create connector manager (instance method)
   * @param config Manager configuration
   */
  async createConnectorManager(config: IConnectorFactoryConfig = {}): Promise<IConnectorManager> {
    return ConnectorFactory.createConnectorManager(config);
  }

  /**
   * Get an existing connector manager by ID
   */
  static getConnectorManager(managerId: string): IConnectorManager | null {
    return ConnectorFactory.connectorManagers.get(managerId) || null;
  }

  /**
   * Dispose a connector manager and its services
   */
  static async disposeConnectorManager(managerId: string): Promise<void> {
    logger.info(`ConnectorFactory: Disposing connector manager for node ${managerId}`);

    try {
      // Get and stop the connector manager
      const connectorManager = ConnectorFactory.connectorManagers.get(managerId);
      if (connectorManager) {
        await connectorManager.dispose();
        ConnectorFactory.connectorManagers.delete(managerId);
      }

      // Dispose the service registry
      const serviceRegistry = ConnectorFactory.serviceRegistries.get(managerId);
      if (serviceRegistry) {
        await serviceRegistry.dispose();
        ConnectorFactory.serviceRegistries.delete(managerId);
      }

      logger.info(`ConnectorFactory: Successfully disposed connector manager for node ${managerId}`);

    } catch (error: any) {
      logger.error(`ConnectorFactory: Error disposing connector manager for node ${managerId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Dispose all connector managers
   */
  static async disposeAll(): Promise<void> {
    logger.info('ConnectorFactory: Disposing all connector managers');

    const managerIds = Array.from(ConnectorFactory.connectorManagers.keys());
    
    for (const managerId of managerIds) {
      try {
        await ConnectorFactory.disposeConnectorManager(managerId);
      } catch (error: any) {
        logger.error(`ConnectorFactory: Error disposing connector manager ${managerId}: ${error.message}`);
      }
    }

    logger.info('ConnectorFactory: All connector managers disposed');
  }

  /**
   * Get all active connector manager IDs
   */
  static getActiveManagerIds(): string[] {
    return Array.from(ConnectorFactory.connectorManagers.keys());
  }

  /**
   * Check if a connector manager exists
   */
  static hasConnectorManager(managerId: string): boolean {
    return ConnectorFactory.connectorManagers.has(managerId);
  }
}

/**
 * Convenience function for creating a connector
 * Maintains backward compatibility with existing code
 */
export async function createConnector(
  type: string,
  config?: any
): Promise<IBaseConnector> {
  const factory = new ConnectorFactory();
  return factory.createConnector(type, config);
}
