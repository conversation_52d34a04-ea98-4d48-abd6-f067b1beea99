export type ScenarioStatus = "passed" | "failed" | "unknown" | "running" | "queued" | "stopped" | "new" | "active" | "inactive" | "passive"

export interface Scenario {
  id: string
  name: string
  description: string
  status: ScenarioStatus
  lastRun?: string
  folderId: string
  tags?: string[]
  steps: {
    total: number
    completed: number
    failed?: number
  }
  duration?: number
  createdAt?: string
  updatedAt?: string
  testrailIntegration?: {
    caseIds: string[] | number[]
    sync?: boolean
  }
  url?: string
}

export interface FolderType {
  id: string
  name: string
  isOpen?: boolean
  color?: string
  teamId?: string
  companyId?: string
}

