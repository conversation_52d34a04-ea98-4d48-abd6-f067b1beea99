/**
 * Cache Utilities
 * 
 * Global cache invalidation utilities for SWR-based data management
 * Provides consistent cache invalidation across all hooks and components
 */

import { mutate } from 'swr'
import { toast } from '@/lib/utils/toast-utils'

/**
 * Cache key patterns for different data types
 */
export const CACHE_PATTERNS = {
  SCENARIOS: 'scenarios',
  FOLDERS: 'folders',
  TEST_DATA: 'test-data',
  RUNS: 'runs',
  REPORTS: 'reports'
} as const

/**
 * Global cache invalidation utility
 * Uses SWR's global mutate function with pattern matching
 */
export class CacheManager {
  /**
   * Invalidate all caches that match a specific pattern
   */
  static async invalidatePattern(pattern: string, options?: {
    revalidate?: boolean
    showNotification?: boolean
    notificationMessage?: string
  }): Promise<void> {
    const { 
      revalidate = true, 
      showNotification = false, 
      notificationMessage = 'Data refreshed' 
    } = options || {}

    try {
      await mutate(
        (key) => typeof key === 'string' && key.includes(pattern),
        undefined,
        { revalidate }
      )

      if (showNotification) {
        toast.success(notificationMessage)
      }
    } catch (error) {
      console.error(`Cache invalidation failed for pattern "${pattern}":`, error)
      if (showNotification) {
        toast.error('Failed to refresh data')
      }
    }
  }

  /**
   * Invalidate scenarios cache
   * Includes both scenarios and folders since they're often fetched together
   */
  static async invalidateScenarios(options?: {
    includeFolders?: boolean
    showNotification?: boolean
  }): Promise<void> {
    const { includeFolders = true, showNotification = false } = options || {}

    try {
      // Invalidate scenarios cache
      await this.invalidatePattern(CACHE_PATTERNS.SCENARIOS, {
        revalidate: true,
        showNotification: false
      })

      // Also invalidate folders cache if requested
      if (includeFolders) {
        await this.invalidatePattern(CACHE_PATTERNS.FOLDERS, {
          revalidate: true,
          showNotification: false
        })
      }

      if (showNotification) {
        toast.success('Scenarios updated')
      }
    } catch (error) {
      console.error('Failed to invalidate scenarios cache:', error)
      if (showNotification) {
        toast.error('Failed to refresh scenarios')
      }
    }
  }

  /**
   * Invalidate folders cache
   */
  static async invalidateFolders(options?: {
    showNotification?: boolean
  }): Promise<void> {
    const { showNotification = false } = options || {}

    await this.invalidatePattern(CACHE_PATTERNS.FOLDERS, {
      revalidate: true,
      showNotification,
      notificationMessage: 'Folders updated'
    })
  }

  /**
   * Invalidate all scenario-related caches
   * Use this after operations that affect both scenarios and folders
   */
  static async invalidateScenarioData(options?: {
    showNotification?: boolean
  }): Promise<void> {
    const { showNotification = false } = options || {}

    try {
      // Invalidate both scenarios and folders in parallel
      await Promise.all([
        this.invalidatePattern(CACHE_PATTERNS.SCENARIOS, { revalidate: true }),
        this.invalidatePattern(CACHE_PATTERNS.FOLDERS, { revalidate: true })
      ])

      if (showNotification) {
        toast.success('Data refreshed')
      }
    } catch (error) {
      console.error('Failed to invalidate scenario data:', error)
      if (showNotification) {
        toast.error('Failed to refresh data')
      }
    }
  }

  /**
   * Invalidate specific cache key
   */
  static async invalidateKey(key: string, options?: {
    revalidate?: boolean
    showNotification?: boolean
  }): Promise<void> {
    const { revalidate = true, showNotification = false } = options || {}

    try {
      await mutate(key, undefined, { revalidate })

      if (showNotification) {
        toast.success('Data refreshed')
      }
    } catch (error) {
      console.error(`Failed to invalidate cache key "${key}":`, error)
      if (showNotification) {
        toast.error('Failed to refresh data')
      }
    }
  }

  /**
   * Invalidate multiple cache keys
   */
  static async invalidateKeys(keys: string[], options?: {
    revalidate?: boolean
    showNotification?: boolean
  }): Promise<void> {
    const { revalidate = true, showNotification = false } = options || {}

    try {
      await Promise.all(
        keys.map(key => mutate(key, undefined, { revalidate }))
      )

      if (showNotification) {
        toast.success('Data refreshed')
      }
    } catch (error) {
      console.error('Failed to invalidate cache keys:', error)
      if (showNotification) {
        toast.error('Failed to refresh data')
      }
    }
  }

  /**
   * Clear all caches (nuclear option)
   * Use with caution - this will clear all SWR caches
   */
  static async clearAllCaches(options?: {
    showNotification?: boolean
  }): Promise<void> {
    const { showNotification = false } = options || {}

    try {
      await mutate(() => true, undefined, { revalidate: true })

      if (showNotification) {
        toast.success('All data refreshed')
      }
    } catch (error) {
      console.error('Failed to clear all caches:', error)
      if (showNotification) {
        toast.error('Failed to refresh data')
      }
    }
  }
}

/**
 * Hook-based cache invalidation utilities
 * For use within React components and hooks
 */
export function useCacheInvalidation() {
  return {
    invalidateScenarios: (options?: Parameters<typeof CacheManager.invalidateScenarios>[0]) =>
      CacheManager.invalidateScenarios(options),
    
    invalidateFolders: (options?: Parameters<typeof CacheManager.invalidateFolders>[0]) =>
      CacheManager.invalidateFolders(options),
    
    invalidateScenarioData: (options?: Parameters<typeof CacheManager.invalidateScenarioData>[0]) =>
      CacheManager.invalidateScenarioData(options),
    
    invalidatePattern: (pattern: string, options?: Parameters<typeof CacheManager.invalidatePattern>[1]) =>
      CacheManager.invalidatePattern(pattern, options),
    
    invalidateKey: (key: string, options?: Parameters<typeof CacheManager.invalidateKey>[1]) =>
      CacheManager.invalidateKey(key, options),
    
    invalidateKeys: (keys: string[], options?: Parameters<typeof CacheManager.invalidateKeys>[1]) =>
      CacheManager.invalidateKeys(keys, options),
    
    clearAllCaches: (options?: Parameters<typeof CacheManager.clearAllCaches>[0]) =>
      CacheManager.clearAllCaches(options)
  }
}

/**
 * Optimistic update helper
 * Provides optimistic updates with automatic rollback on failure
 */
export async function withOptimisticUpdate<T>(
  cacheKey: string,
  optimisticData: T,
  asyncOperation: () => Promise<any>,
  options?: {
    showNotification?: boolean
    successMessage?: string
    errorMessage?: string
  }
): Promise<{ success: boolean; data?: any; error?: string }> {
  const { 
    showNotification = false, 
    successMessage = 'Operation completed',
    errorMessage = 'Operation failed'
  } = options || {}

  try {
    // Apply optimistic update
    await mutate(cacheKey, optimisticData, false)

    // Perform the actual operation
    const result = await asyncOperation()

    // Revalidate to get fresh data
    await mutate(cacheKey)

    if (showNotification && result.success) {
      toast.success(successMessage)
    }

    return result
  } catch (error) {
    // Rollback optimistic update by revalidating
    await mutate(cacheKey)

    const errorMsg = error instanceof Error ? error.message : errorMessage
    
    if (showNotification) {
      toast.error(errorMsg)
    }

    return { success: false, error: errorMsg }
  }
}
