/**
 * Node Timeout Guardian
 * 
 * Provides aggressive local timeout monitoring and emergency termination.
 * Works independently from hub-side monitoring as the first line of defense.
 * Features hub notification and coordination to prevent duplicate terminations.
 */

import { EventEmitter } from 'events';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';
import { TestResult, TestRequest } from '../../../../models/types.js';
import { IExecutionCoordinator } from '../../interfaces/execution-coordinator.interface.js';
import { ITestExecutionContainer } from '../../../common/di/application-container.js';
import { ITestProgressReporter } from '../../interfaces/test-progress-reporter.interface.js';

export interface NodeGuardianStatistics {
  activeTimeouts: number;
  guardedTests: number;
  emergencyTerminations: number;
  stepTimeouts: number;
  testTimeouts: number;
  hubNotificationsSent: number;
  hubNotificationFailures: number;
}

export interface GuardianConfiguration {
  stepTimeoutMs: number;
  testTimeoutMs: number;
  emergencyCleanupTimeoutMs: number;
  hubNotificationRetries: number;
}

/**
 * Node Timeout Guardian Service
 * 
 * First line of defense against stuck tests with aggressive timeouts.
 * Coordinates with hub-side monitoring to prevent duplicate terminations.
 */
export class NodeTimeoutGuardian extends EventEmitter {
  private logger: ILoggerService;
  private executionCoordinator: IExecutionCoordinator;
  private container: ITestExecutionContainer;
  private progressReporter: ITestProgressReporter;

  // Timeout tracking
  private activeStepTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private activeTestTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private testStartTimes: Map<string, number> = new Map();
  private emergencyCleanupInProgress = new Set<string>();
  private testMetadata: Map<string, Partial<TestRequest>> = new Map();

  // Statistics and metrics
  private statistics: NodeGuardianStatistics = {
    activeTimeouts: 0,
    guardedTests: 0,
    emergencyTerminations: 0,
    stepTimeouts: 0,
    testTimeouts: 0,
    hubNotificationsSent: 0,
    hubNotificationFailures: 0
  };

  // Configuration - aggressive timeline to beat hub-side monitoring
  private config: GuardianConfiguration = {
    stepTimeoutMs: 15 * 60 * 1000,     // 15 minutes - increased for longer test steps
    testTimeoutMs: 40 * 60 * 1000,     // 40 minutes - increased for longer tests with many steps
    emergencyCleanupTimeoutMs: 15000,  // 15 seconds max cleanup
    hubNotificationRetries: 3          // Retry hub notifications
  };

  constructor(
    executionCoordinator: IExecutionCoordinator,
    container: ITestExecutionContainer,
    progressReporter: ITestProgressReporter
  ) {
    super();
    this.logger = LoggerServiceFactory.createServiceLogger('NodeTimeoutGuardian');
    this.executionCoordinator = executionCoordinator;
    this.container = container;
    this.progressReporter = progressReporter;

    this.logger.info('NodeTimeoutGuardian: Initialized with aggressive timeout monitoring');
  }

  /**
   * Start guarding a test with comprehensive timeout monitoring
   */
  startGuarding(testId: string, testRequest: Partial<TestRequest>, totalSteps: number): void {
    if (this.testStartTimes.has(testId)) {
      this.logger.warn(`NodeTimeoutGuardian: Already guarding test ${testId}, skipping duplicate start`);
      return;
    }

    this.testStartTimes.set(testId, Date.now());
    this.testMetadata.set(testId, testRequest);
    this.statistics.guardedTests++;

    this.logger.warn(`🛡️ NodeTimeoutGuardian: Started guarding test ${testId} (${totalSteps} steps) - Step timeout: ${this.config.stepTimeoutMs/1000}s, Test timeout: ${this.config.testTimeoutMs/1000}s`);

    // Start test-level timeout
    const testTimeout = setTimeout(() => {
      this.handleTestTimeout(testId, 'Test exceeded maximum duration');
    }, this.config.testTimeoutMs);

    this.activeTestTimeouts.set(testId, testTimeout);
    this.updateActiveTimeoutsCount();

    // Notify hub that guardian is active for this test
    this.notifyHubGuardianActive(testId).catch(error => {
      this.logger.warn(`NodeTimeoutGuardian: Failed to notify hub of guardian activation: ${error.message}`);
    });

    // Emit guardian started event
    this.emit('guardian:started', { testId, totalSteps, config: this.config });
  }

  /**
   * Start step-level timeout monitoring
   */
  startStepGuarding(testId: string, stepIndex: number, stepType: string): void {
    // Clear any previous step timeout for this test
    this.clearStepTimeout(testId, stepIndex - 1);

    const timeoutKey = `${testId}-step-${stepIndex}`;
    const stepTimeout = setTimeout(() => {
      this.handleStepTimeout(testId, stepIndex, stepType);
    }, this.config.stepTimeoutMs);

    this.activeStepTimeouts.set(timeoutKey, stepTimeout);
    this.updateActiveTimeoutsCount();

    this.logger.debug(`🛡️ NodeTimeoutGuardian: Guarding step ${stepIndex} (${stepType}) for test ${testId} - timeout in ${this.config.stepTimeoutMs/1000}s`);

    // Emit step guard started event
    this.emit('step:guard:started', { testId, stepIndex, stepType });
  }

  /**
   * Clear step timeout when step completes successfully
   */
  clearStepTimeout(testId: string, stepIndex: number): void {
    const timeoutKey = `${testId}-step-${stepIndex}`;
    const timeout = this.activeStepTimeouts.get(timeoutKey);

    if (timeout) {
      clearTimeout(timeout);
      this.activeStepTimeouts.delete(timeoutKey);
      this.updateActiveTimeoutsCount();
      this.logger.debug(`🛡️ NodeTimeoutGuardian: Cleared step timeout for test ${testId} step ${stepIndex}`);
    }
  }

  /**
   * Stop guarding a test (test completed normally)
   */
  stopGuarding(testId: string): void {
    if (!this.testStartTimes.has(testId)) {
      this.logger.debug(`NodeTimeoutGuardian: Test ${testId} was not being guarded, skipping stop`);
      return;
    }

    // Clear all timeouts for this test
    const keysToDelete: string[] = [];
    
    // Clear step timeouts
    for (const [key, timeout] of this.activeStepTimeouts) {
      if (key.startsWith(testId)) {
        clearTimeout(timeout);
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.activeStepTimeouts.delete(key));

    // Clear test timeout
    const testTimeout = this.activeTestTimeouts.get(testId);
    if (testTimeout) {
      clearTimeout(testTimeout);
      this.activeTestTimeouts.delete(testId);
    }

    // Clean up tracking data
    this.testStartTimes.delete(testId);
    this.testMetadata.delete(testId);
    this.emergencyCleanupInProgress.delete(testId);
    
    this.updateActiveTimeoutsCount();

    this.logger.info(`🛡️ NodeTimeoutGuardian: Stopped guarding test ${testId} - normal completion`);

    // Notify hub that guardian is no longer active
    this.notifyHubGuardianInactive(testId).catch(error => {
      this.logger.warn(`NodeTimeoutGuardian: Failed to notify hub of guardian deactivation: ${error.message}`);
    });

    // Emit guardian stopped event
    this.emit('guardian:stopped', { testId, reason: 'normal_completion' });
  }

  /**
   * Handle step timeout - emergency termination
   */
  private async handleStepTimeout(testId: string, stepIndex: number, stepType: string): Promise<void> {
    if (this.emergencyCleanupInProgress.has(testId)) {
      this.logger.debug(`NodeTimeoutGuardian: Emergency cleanup already in progress for test ${testId}, skipping step timeout`);
      return;
    }

    this.emergencyCleanupInProgress.add(testId);
    this.statistics.stepTimeouts++;

    const testStartTime = this.testStartTimes.get(testId) || Date.now();
    const testDuration = Date.now() - testStartTime;

    this.logger.error(`🚨 NodeTimeoutGuardian: STEP TIMEOUT - Test ${testId}, Step ${stepIndex} (${stepType}), Duration: ${Math.round(testDuration/1000)}s, Timeout: ${this.config.stepTimeoutMs/1000}s`);

    // Emit step timeout event
    this.emit('step:timeout', { testId, stepIndex, stepType, duration: testDuration });

    await this.executeEmergencyTermination(
      testId, 
      `Step ${stepIndex} (${stepType}) timeout after ${Math.round(this.config.stepTimeoutMs/1000)}s`
    );
  }

  /**
   * Handle test timeout - emergency termination
   */
  private async handleTestTimeout(testId: string, reason: string): Promise<void> {
    if (this.emergencyCleanupInProgress.has(testId)) {
      this.logger.debug(`NodeTimeoutGuardian: Emergency cleanup already in progress for test ${testId}, skipping test timeout`);
      return;
    }

    this.emergencyCleanupInProgress.add(testId);
    this.statistics.testTimeouts++;

    const testStartTime = this.testStartTimes.get(testId) || Date.now();
    const testDuration = Date.now() - testStartTime;

    this.logger.error(`🚨 NodeTimeoutGuardian: TEST TIMEOUT - Test ${testId}, Duration: ${Math.round(testDuration/1000)}s, Reason: ${reason}`);

    // Emit test timeout event
    this.emit('test:timeout', { testId, reason, duration: testDuration });

    await this.executeEmergencyTermination(testId, reason);
  }

  /**
   * Execute emergency termination with hub notification
   */
  private async executeEmergencyTermination(testId: string, reason: string): Promise<void> {
    const startTime = Date.now();
    this.statistics.emergencyTerminations++;

    this.logger.error(`🔥 NodeTimeoutGuardian: EMERGENCY TERMINATION started for test ${testId}: ${reason}`);

    // Emit termination started event
    this.emit('termination:started', { testId, reason, startTime });

    try {
      // Step 1: Notify hub immediately that we're handling this test
      await this.notifyHubEmergencyTermination(testId, reason);

      // Step 2: Send failed result to hub
      await this.sendFailedResultToHub(testId, reason);

      // Step 3: Force stop execution coordinator
      await this.forceStopExecutionCoordinator(testId, reason);

      // Step 4: Force dispose DI container scope
      await this.forceDisposeContainerScope(testId);

      // Step 5: Force cleanup resources
      await this.forceCleanupResources(testId);

      const duration = Date.now() - startTime;
      this.logger.error(`🔥 NodeTimeoutGuardian: EMERGENCY TERMINATION completed for test ${testId} in ${duration}ms`);

      // Emit termination completed event
      this.emit('termination:completed', { testId, reason, duration, success: true });

    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.logger.error(`🔥 NodeTimeoutGuardian: Emergency termination failed for test ${testId}: ${error.message}`);

      // Last resort: Try to notify hub of failure
      await this.notifyHubTerminationFailure(testId, error.message).catch(() => {});

      // Emit termination failed event
      this.emit('termination:failed', { testId, reason, duration, error: error.message });

      // If cleanup took too long, consider process exit
      if (duration > this.config.emergencyCleanupTimeoutMs) {
        this.logger.error(`🔥 NodeTimeoutGuardian: EMERGENCY CLEANUP TIMEOUT - Cleanup took ${duration}ms, considering process exit`);
        this.emit('cleanup:timeout', { testId, duration });
        
        // Don't exit immediately, but warn about potential issues
        this.logger.error(`🔥 NodeTimeoutGuardian: Node may be in unstable state, monitor closely`);
      }
    } finally {
      this.stopGuarding(testId);
    }
  }

  /**
   * Sanitize environment settings to remove sensitive data before sending to hub
   * @param environmentSettings Raw environment settings that may contain sensitive data
   * @returns Sanitized environment settings safe for result transmission
   */
  private sanitizeEnvironmentSettingsForResult(environmentSettings: any): any {
    if (!environmentSettings || typeof environmentSettings !== 'object') {
      return environmentSettings;
    }

    // Deep clone to avoid modifying the original object
    const sanitized = JSON.parse(JSON.stringify(environmentSettings));

    // Remove sensitive SauceLabs credentials
    if (sanitized.sauceLabs) {
      delete sanitized.sauceLabs.accessKey;
      delete sanitized.sauceLabs.username;
      // Keep device information for context in reports
    }

    // Remove sensitive Testinium credentials
    if (sanitized.testinium) {
      delete sanitized.testinium.clientId;
      delete sanitized.testinium.clientSecret;
      delete sanitized.testinium.apiUrl;
      delete sanitized.testinium.issuerUri;
      // Keep device information for context in reports
    }

    // Remove sensitive AI model configuration
    if (sanitized.aiModelConfig) {
      delete sanitized.aiModelConfig.OPENAI_API_KEY;
      delete sanitized.aiModelConfig.OPENAI_BASE_URL;
      // Keep only non-sensitive fields like model name
      if (sanitized.aiModelConfig.MIDSCENE_MODEL_NAME) {
        sanitized.aiModelConfig = {
          MIDSCENE_MODEL_NAME: sanitized.aiModelConfig.MIDSCENE_MODEL_NAME
        };
      } else {
        delete sanitized.aiModelConfig;
      }
    }

    // Remove sensitive proxy credentials
    if (sanitized.proxy) {
      delete sanitized.proxy.username;
      delete sanitized.proxy.password;
    }

    this.logger.debug('NodeTimeoutGuardian: Sanitized environment settings for result transmission by removing sensitive credentials');
    
    return sanitized;
  }

  /**
   * Send failed result to hub with proper test metadata
   */
  private async sendFailedResultToHub(testId: string, reason: string): Promise<void> {
    try {
      const testStartTime = this.testStartTimes.get(testId) || Date.now();
      const duration = Date.now() - testStartTime;
      const testMetadata = this.testMetadata.get(testId) || {};

      // Sanitize environment settings if present
      const sanitizedEnvironmentSettings = testMetadata.environmentSettings ? 
        this.sanitizeEnvironmentSettingsForResult(testMetadata.environmentSettings) : 
        undefined;

      // Create comprehensive failed result
      const failedResult: TestResult = {
        id: testId,
        scenarioId: testMetadata.scenarioId || 'unknown',
        scenarioName: testMetadata.scenarioName || 'Unknown Scenario',
        name: testMetadata.scenario?.name || 'NodeTimeoutGuardian Terminated Test',
        url: testMetadata.scenario?.url || '',
        date: new Date().toISOString(),
        status: 'failed',
        success: false,
        duration,
        steps: [],
        summary: { 
          passed: 0, 
          failed: 1, 
          total: testMetadata.scenario?.steps?.length || 1, 
          errors: 1 
        },
        error: `NodeTimeoutGuardian: ${reason}`,
        testId,
        nodeId: this.getNodeId(),
        runId: testMetadata.runId,
        executionId: testMetadata.executionId || `guardian-${Date.now()}`,
        executedUser: testMetadata.executedUser || 'system',
        executedUserName: testMetadata.executedUserName || 'NodeTimeoutGuardian',
        teamId: testMetadata.teamId,
        companyId: testMetadata.companyId,
        platform: testMetadata.platform,
        environmentSettings: sanitizedEnvironmentSettings, // SANITIZED environment settings
        transactionId: `guardian-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      };

      // Send via progress reporter (Redis queue)
      await this.progressReporter.reportTestCompletion(testId, failedResult);
      
      this.logger.error(`🔥 NodeTimeoutGuardian: FAILED RESULT SENT TO HUB for test ${testId}`);
      
      // Log sanitization for security audit
      if (testMetadata.environmentSettings) {
        this.logger.info(`NodeTimeoutGuardian: Sanitized environment settings for failed test result ${testId} - removed sensitive credentials before transmission to hub`);
      }
      
      this.statistics.hubNotificationsSent++;

    } catch (error: any) {
      this.logger.error(`🔥 NodeTimeoutGuardian: FAILED TO SEND RESULT TO HUB for test ${testId}: ${error.message}`);
      this.statistics.hubNotificationFailures++;
      throw error;
    }
  }

  /**
   * Force stop execution coordinator with timeout
   */
  private async forceStopExecutionCoordinator(testId: string, reason: string): Promise<void> {
    try {
      await Promise.race([
        this.executionCoordinator.stopSpecificTest(testId, `NodeTimeoutGuardian: ${reason}`),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('ExecutionCoordinator stop timeout')), 5000)
        )
      ]);
      this.logger.info(`🔥 NodeTimeoutGuardian: ExecutionCoordinator stopped for test ${testId}`);
    } catch (error: any) {
      this.logger.error(`🔥 NodeTimeoutGuardian: ExecutionCoordinator stop failed for test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Force dispose container scope with timeout
   */
  private async forceDisposeContainerScope(testId: string): Promise<void> {
    try {
      await Promise.race([
        this.container.disposeScope(testId),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Container disposal timeout')), 8000)
        )
      ]);
      this.logger.info(`🔥 NodeTimeoutGuardian: Container scope disposed for test ${testId}`);
    } catch (error: any) {
      this.logger.error(`🔥 NodeTimeoutGuardian: Container disposal failed for test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Force cleanup all resources
   */
  private async forceCleanupResources(testId: string): Promise<void> {
    try {
      // Import services dynamically to avoid circular dependencies
      const promises = [
        this.forceCleanupRedisLocks(testId),
        this.forceCleanupProcesses(),
        this.forceCleanupNetworkConnections()
      ];

      await Promise.allSettled(promises);
      this.logger.info(`🔥 NodeTimeoutGuardian: Resource cleanup completed for test ${testId}`);
    } catch (error: any) {
      this.logger.error(`🔥 NodeTimeoutGuardian: Resource cleanup failed for test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Force cleanup Redis locks
   */
  private async forceCleanupRedisLocks(testId: string): Promise<void> {
    try {
      // Use config to get Redis connection info and create a simple client
      const lockKeys = [
        `test:lock:${testId}`,
        `test:claim:${testId}`,
        `test:reservation:${testId}`,
        `test:status:${testId}`,
        `test:node:${testId}`,
        `lock:test:${testId}`,
        `lock:${testId}`
      ];

      // Try to cleanup via a simple method - this is a fallback cleanup
      this.logger.info(`🔥 NodeTimeoutGuardian: Attempted Redis lock cleanup for test ${testId} (${lockKeys.length} keys)`);
    } catch (error: any) {
      this.logger.error(`🔥 NodeTimeoutGuardian: Redis lock cleanup failed: ${error.message}`);
    }
  }

  /**
   * Force cleanup browser/chrome processes
   */
  private async forceCleanupProcesses(): Promise<void> {
    try {
      if (process.platform === 'linux') {
        const { exec } = await import('child_process');
        const util = await import('util');
        const execPromise = util.promisify(exec);

        // Kill all Chrome/Chromium processes
        await Promise.allSettled([
          execPromise('pkill -f chrome'),
          execPromise('pkill -f chromium'),
          execPromise('pkill -f google-chrome')
        ]);
        
        this.logger.info(`🔥 NodeTimeoutGuardian: Browser processes force killed`);
      }
    } catch (error: any) {
      this.logger.error(`🔥 NodeTimeoutGuardian: Process cleanup failed: ${error.message}`);
    }
  }

  /**
   * Force cleanup network connections
   */
  private async forceCleanupNetworkConnections(): Promise<void> {
    try {
      // This would force close WebSocket and other network connections
      // Implementation depends on the specific connection managers
      this.logger.debug(`🔥 NodeTimeoutGuardian: Network connections cleanup attempted`);
    } catch (error: any) {
      this.logger.error(`🔥 NodeTimeoutGuardian: Network cleanup failed: ${error.message}`);
    }
  }

  /**
   * Notify hub that guardian is active for a test
   */
  private async notifyHubGuardianActive(testId: string): Promise<void> {
    try {
      const message = {
        type: 'node-guardian-active',
        data: { 
          testId, 
          nodeId: this.getNodeId(), 
          timestamp: Date.now(),
          guardianConfig: this.config
        }
      };
      
      await this.sendMessageToHub(message);
      this.statistics.hubNotificationsSent++;
      this.logger.debug(`🔥 NodeTimeoutGuardian: Notified hub of guardian activation for test ${testId}`);
    } catch (error: any) {
      this.statistics.hubNotificationFailures++;
      this.logger.warn(`NodeTimeoutGuardian: Failed to notify hub of guardian activation: ${error.message}`);
    }
  }

  /**
   * Notify hub that guardian is no longer active
   */
  private async notifyHubGuardianInactive(testId: string): Promise<void> {
    try {
      const message = {
        type: 'node-guardian-inactive',
        data: { 
          testId, 
          nodeId: this.getNodeId(), 
          timestamp: Date.now()
        }
      };
      
      await this.sendMessageToHub(message);
      this.statistics.hubNotificationsSent++;
    } catch (error: any) {
      this.statistics.hubNotificationFailures++;
      // Don't log warning for inactive notification failures
    }
  }

  /**
   * Notify hub of emergency termination
   */
  private async notifyHubEmergencyTermination(testId: string, reason: string): Promise<void> {
    try {
      const message = {
        type: 'node-emergency-termination',
        data: {
          testId,
          nodeId: this.getNodeId(),
          reason: `NodeTimeoutGuardian: ${reason}`,
          timestamp: Date.now(),
          terminatedBy: 'NodeTimeoutGuardian'
        }
      };

      await this.sendMessageToHub(message);
      this.statistics.hubNotificationsSent++;
      this.logger.info(`🔥 NodeTimeoutGuardian: Notified hub of emergency termination for test ${testId}`);
    } catch (error: any) {
      this.statistics.hubNotificationFailures++;
      this.logger.error(`🔥 NodeTimeoutGuardian: Failed to notify hub of emergency termination: ${error.message}`);
    }
  }

  /**
   * Notify hub of termination failure
   */
  private async notifyHubTerminationFailure(testId: string, errorMessage: string): Promise<void> {
    try {
      const message = {
        type: 'node-termination-failure',
        data: {
          testId,
          nodeId: this.getNodeId(),
          error: errorMessage,
          timestamp: Date.now()
        }
      };

      await this.sendMessageToHub(message);
      this.statistics.hubNotificationsSent++;
    } catch (error: any) {
      this.statistics.hubNotificationFailures++;
      // Silent failure for failure notifications
    }
  }

  /**
   * Send message to hub via WebSocket
   */
  private async sendMessageToHub(message: any): Promise<void> {
    // Implementation would use the WebSocket connector to send to hub
    // This is a placeholder that would be implemented based on the connection service
    if (this.progressReporter && typeof (this.progressReporter as any).sendMessageToHub === 'function') {
      await (this.progressReporter as any).sendMessageToHub(message);
    } else {
      this.logger.debug('NodeTimeoutGuardian: No hub communication method available');
    }
  }

  /**
   * Get current node ID
   */
  private getNodeId(): string {
    // Try to get node ID from execution coordinator or fallback to environment
    try {
      const nodeId = process.env.NODE_ID || 'unknown-node';
      return nodeId;
    } catch (error) {
      return 'unknown-node';
    }
  }

  /**
   * Update active timeouts count
   */
  private updateActiveTimeoutsCount(): void {
    this.statistics.activeTimeouts = this.activeStepTimeouts.size + this.activeTestTimeouts.size;
  }

  /**
   * Get guardian statistics
   */
  getStatistics(): NodeGuardianStatistics {
    return { ...this.statistics };
  }

  /**
   * Get configuration
   */
  getConfiguration(): GuardianConfiguration {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfiguration(newConfig: Partial<GuardianConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info(`NodeTimeoutGuardian: Configuration updated`, this.config);
  }

  /**
   * Get active guardian status
   */
  getActiveStatus(): {
    guardedTests: string[];
    activeStepTimeouts: string[];
    activeTestTimeouts: string[];
    emergencyCleanupInProgress: string[];
  } {
    return {
      guardedTests: Array.from(this.testStartTimes.keys()),
      activeStepTimeouts: Array.from(this.activeStepTimeouts.keys()),
      activeTestTimeouts: Array.from(this.activeTestTimeouts.keys()),
      emergencyCleanupInProgress: Array.from(this.emergencyCleanupInProgress)
    };
  }

  /**
   * Dispose guardian and cleanup all resources
   */
  async dispose(): Promise<void> {
    try {
      // Clear all active timeouts
      for (const timeout of this.activeStepTimeouts.values()) {
        clearTimeout(timeout);
      }
      for (const timeout of this.activeTestTimeouts.values()) {
        clearTimeout(timeout);
      }

      // Clear all tracking data
      this.activeStepTimeouts.clear();
      this.activeTestTimeouts.clear();
      this.testStartTimes.clear();
      this.testMetadata.clear();
      this.emergencyCleanupInProgress.clear();

      this.logger.info('NodeTimeoutGuardian: Disposed successfully');
    } catch (error: any) {
      this.logger.error(`NodeTimeoutGuardian: Error during disposal: ${error.message}`);
      throw error;
    }
  }
} 