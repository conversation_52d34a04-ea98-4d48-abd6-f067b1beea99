"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>L<PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Database, Sparkles } from "lucide-react"

// Import modular components
import { NewDataSetHeader } from "@/components/data-sets/new-data-set-header"
import { BasicInfoForm } from "@/components/data-sets/basic-info-form"
import { AiPromptSection } from "@/components/data-sets/ai-prompt-section"
import { VariablesTable } from "@/components/data-sets/variables-table"
import { ConstraintEditorDialog } from "@/components/data-sets/constraint-editor-dialog"
import { ImportFromSourceDialog } from "@/components/data-sets/import-from-source-dialog"

// Import custom hook
import { useTestDataSets } from "@/hooks/useTestDataSets"

export default function NewDataSetPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)

  const {
    // Form state
    formData,
    newTag,
    setNewTag,

    // UI state
    isGenerating,
    isSaving,
    activeTab,
    setActiveTab,
    showVariableValues,
    setShowVariableValues,
    activeEnvironmentId,
    showEnvironmentSelector,
    setShowEnvironmentSelector,

    // Environment state
    userEnvironments,
    selectedEnvironments,
    isLoadingEnvironments,

    // Variables state
    variables,

    // Editing state
    editingVariable,
    tempConstraints,

    // Handlers
    handleFormChange,
    handleAddTag,
    handleRemoveTag,
    handleUpdateVariable,
    handleAddVariable,
    handleRemoveVariable,
    handleDuplicateVariable,
    handleGenerateData,
    handleGenerateDataSetStructure,
    handleRandomizeData,
    handleEditConstraints,
    handleUpdateConstraint,
    handleSaveConstraints,
    handleCancelConstraints,
    handleCopyValue,
    handleCopyAllEnvironmentsAsJson,
    handleSaveDataSet,
    handleBack,
    handleEnvironmentChange,
    handleToggleEnvironment,
    handleClearAll,
    canSave,
    importVariablesFromSource,
    resetFormAndVariables,
  } = useTestDataSets()

  // Reset the form and variables when the page loads (for new data set)
  useEffect(() => {
    resetFormAndVariables()
    setActiveTab("basic-info")
  }, [resetFormAndVariables, setActiveTab])

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <NewDataSetHeader onBack={handleBack} onSave={handleSaveDataSet} isSaving={isSaving} canSave={canSave} />

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900/20 p-6">
          <div className="max-w-7xl mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full max-w-md grid-cols-2">
                <TabsTrigger value="basic-info" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  <span>Basic Information</span>
                </TabsTrigger>
                <TabsTrigger value="generate-data" className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  <span>Generate Data</span>
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic-info" className="space-y-6">
                <BasicInfoForm
                  formData={formData}
                  onFormChange={handleFormChange}
                  newTag={newTag}
                  onNewTagChange={setNewTag}
                  onAddTag={handleAddTag}
                  onRemoveTag={handleRemoveTag}
                />
              </TabsContent>

              {/* Generate Data Tab */}
              <TabsContent value="generate-data" className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <div className="space-y-6">
                    {/* AI Prompt Section */}
                    <AiPromptSection
                      prompt={formData.aiPrompt}
                      onPromptChange={(value) => handleFormChange("aiPrompt", value)}
                      onGenerate={handleGenerateDataSetStructure}
                      isGenerating={isGenerating}
                      environments={userEnvironments}
                      selectedEnvironments={selectedEnvironments}
                      isLoadingEnvironments={isLoadingEnvironments}
                      onToggleEnvironment={handleToggleEnvironment}
                    />

                    {/* Variables Table */}
                    <VariablesTable
                      variables={variables}
                      showValues={showVariableValues}
                      onShowValuesChange={setShowVariableValues}
                      activeEnvironment={activeEnvironmentId}
                      onEnvironmentChange={handleEnvironmentChange}
                      showEnvironmentSelector={showEnvironmentSelector}
                      onShowEnvironmentSelectorChange={setShowEnvironmentSelector}
                      onUpdateVariable={handleUpdateVariable}
                      onCopyValue={handleCopyValue}
                      onDuplicateVariable={handleDuplicateVariable}
                      onRemoveVariable={handleRemoveVariable}
                      onEditConstraints={handleEditConstraints}
                      onAddVariable={handleAddVariable}
                      onCopyAllAsJson={handleCopyAllEnvironmentsAsJson}
                      userEnvironments={userEnvironments}
                      isLoadingEnvironments={isLoadingEnvironments}
                      onRandomizeData={handleRandomizeData}
                      isGenerating={isGenerating}
                      onClearAll={handleClearAll}
                      onImportFromSource={() => setShowImportDialog(true)}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>

      {/* Constraint Editor Dialog */}
      <ConstraintEditorDialog
        variable={editingVariable}
        constraints={tempConstraints}
        onConstraintChange={handleUpdateConstraint}
        onSave={handleSaveConstraints}
        onCancel={handleCancelConstraints}
      />

      {/* Import from Source Dialog */}
      <ImportFromSourceDialog
        isOpen={showImportDialog}
        onClose={() => setShowImportDialog(false)}
        onImport={importVariablesFromSource}
        currentVariables={variables}
      />
    </div>
  )
}
