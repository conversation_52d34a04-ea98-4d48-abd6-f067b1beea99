/**
 * Sleep Step Handler
 * Handles sleep/wait steps in tests (consolidated from web-specific handler)
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';

/**
 * Sleep Step Handler
 * Handles sleep/wait steps in tests
 */
export class SleepStepHandler implements IStepHandler {
  /**
   * Execute a sleep step
   * @param _target Unused target parameter (works for both web and android)
   * @param step Step to execute
   * @param _agent Unused agent parameter
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(
    _target: any,
    step: ScenarioStep,
    _agent: any,
    logs: string[]
  ): Promise<{ success: boolean; error: string | null; data?: any }> {
    try {
      // Extract wait time from step
      const waitTime = parseFloat(step.duration || step.value || '1000');

      // Format wait time for logging
      let formattedWaitMsg;
      if (waitTime < 1000) {
        formattedWaitMsg = `${waitTime}ms`;
      } else {
        const waitTimeSec = waitTime / 1000;
        const displayTime = waitTimeSec % 1 === 0 ? waitTimeSec : waitTimeSec.toFixed(1);
        formattedWaitMsg = `${displayTime} Second${displayTime !== '1' && displayTime !== 1 ? 's' : ''}`;
      }

      logs.push(`⏱️ Wait for ${formattedWaitMsg}`);
      logger.debug(`SleepStepHandler: Waiting for ${formattedWaitMsg}`);

      // Sleep for the specified time
      await this.sleep(waitTime);

      logs.push(`✅ Wait completed`);
      logger.debug(`SleepStepHandler: Wait completed`);

      return { success: true, error: null };
    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error during sleep';
      logs.push(`❌ Wait failed: ${errorMessage}`);
      logger.error(`SleepStepHandler: Wait failed: ${errorMessage}`);
      return { success: false, error: `Wait failed: ${errorMessage}` };
    }
  }

  /**
   * Sleep for the specified time
   * @param ms Time to sleep in milliseconds
   * @returns Promise that resolves after the specified time
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step, false otherwise
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'sleep';
  }

  /**
   * Get the name of this step handler
   * @returns The name of the step handler
   */
  getName(): string {
    return 'sleep';
  }
}
