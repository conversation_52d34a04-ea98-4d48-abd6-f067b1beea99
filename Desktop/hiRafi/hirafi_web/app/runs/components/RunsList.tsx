import React from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Eye, Edit, Trash2, Play, Square, MoreHorizontal,
  LoaderCircle, CheckCircle, X, Clock, Laptop, Smartphone, Database, XCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { RunStatusBadge } from '@/lib/utils/run-ui';
import { canStartRun, canStopRun } from '@/lib/utils/run-logic';
import { useRunUpdating, useRunSimulatedState } from '@/store/runStore';

export interface RunsListProps {
  runs: any[];
  onStart: (runId: string) => void;
  onStop: (runId: string) => void;
  onDelete: (runId: string) => void;
}

export function RunsList({ runs, onStart, onStop, onDelete }: RunsListProps) {
  const router = useRouter();

  const handleViewDetails = (runId: string) => router.push(`/runs/${runId}`);
  const handleEdit = (runId: string) => router.push(`/runs/edit/${runId}`);

  return (
    <div className="bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 rounded-lg shadow-sm overflow-hidden">
      <div className="divide-y divide-gray-200 dark:divide-gray-800">
        {runs.map(run => {
          return <RunListItem
            key={run.id}
            run={run}
            onStart={onStart}
            onStop={onStop}
            onDelete={onDelete}
            onViewDetails={handleViewDetails}
            onEdit={handleEdit}
          />;
        })}
      </div>
    </div>
  );
}

function RunListItem({
  run,
  onStart,
  onStop,
  onDelete,
  onViewDetails,
  onEdit
}: {
  run: any;
  onStart: (runId: string) => void;
  onStop: (runId: string) => void;
  onDelete: (runId: string) => void;
  onViewDetails: (runId: string) => void;
  onEdit: (runId: string) => void;
}) {
  const isUpdating = useRunUpdating(run.id);
  const simulatedState = useRunSimulatedState(run.id);


  return (
    <div
      className={`
        p-4 hover:bg-gray-50 dark:hover:bg-gray-900/60 transition-all duration-200
        ${simulatedState === "running" ? "border-l-4 border-blue-400 dark:border-blue-600" :
          simulatedState === "passed" ? "border-l-4 border-emerald-400 dark:border-emerald-600" :
          simulatedState === "failed" ? "border-l-4 border-red-400 dark:border-red-600" :
          simulatedState === "stopped" ? "border-l-4 border-amber-400 dark:border-amber-600" : "border-l-4 border-transparent"}
      `}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Status Badge */}
          <RunStatusBadge
            status={run.status}
            simulatedStatus={simulatedState}
            className="px-2 py-1 text-xs font-medium rounded-full"
          />

          {/* Run name */}
          <div
            className="cursor-pointer"
            onClick={() => onViewDetails(run.id)}
          >
            <h3 className="text-base font-medium text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
              {run.name}
            </h3>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Test Status Icons */}
          {run.testResults && (
            <div className="flex items-center gap-2">
              {run.testResults.completed > 0 && (
                <div className="flex items-center text-emerald-600 dark:text-emerald-400">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">{run.testResults.completed}</span>
                </div>
              )}

              {run.testResults.failed > 0 && (
                <div className="flex items-center text-red-600 dark:text-red-400">
                  <X className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">{run.testResults.failed}</span>
                </div>
              )}

              {run.testResults.stopped > 0 && (
                <div className="flex items-center text-gray-600 dark:text-gray-400">
                  <XCircle className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">{run.testResults.stopped}</span>
                </div>
              )}

              {run.testResults.running > 0 && (
                <div className="flex items-center text-blue-600 dark:text-blue-400">
                  <LoaderCircle className="h-4 w-4 mr-1 animate-spin" />
                  <span className="text-sm font-medium">{run.testResults.running}</span>
                </div>
              )}

              {run.testResults.queued > 0 && (
                <div className="flex items-center text-amber-600 dark:text-amber-400">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">{run.testResults.queued}</span>
                </div>
              )}
            </div>
          )}

          {/* Platform badge */}
          <Badge
            variant="outline"
            className={`${
              run.platform === "web"
                ? "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800"
                : run.platform === "android"
                ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800"
                : "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900 dark:text-gray-400 dark:border-gray-800"
            }`}
          >
            {run.platform === "web" ? (
              <>
                <Laptop className="h-3 w-3 mr-1" />
                Web
              </>
            ) : run.platform === "android" ? (
              <>
                <Smartphone className="h-3 w-3 mr-1" />
                Android
              </>
            ) : (
              run.platform || "Unknown"
            )}
          </Badge>

          {/* Test Data Environment badge */}
          {run.testDataEnvironmentName && (
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800">
              <Database className="h-3 w-3 mr-1" />
              {run.testDataEnvironmentName}
            </Badge>
          )}

          {/* Date */}
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {format(new Date(run.createdAt), "MMM d, yyyy")}
          </span>

          {/* Actions */}
          <div className="flex items-center space-x-1">
            {/* Run actions */}
            {canStopRun(run) || simulatedState === "running" ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full hover:bg-red-50 dark:hover:bg-red-950/30"
                onClick={() => onStop(run.id)}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <LoaderCircle className="h-4 w-4 text-red-600 dark:text-red-400 animate-spin" />
                ) : (
                  <Square className="h-4 w-4 text-red-600 dark:text-red-400" />
                )}
              </Button>
            ) : canStartRun(run) ? (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full hover:bg-indigo-50 dark:hover:bg-indigo-950/30"
                onClick={() => onStart(run.id)}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <LoaderCircle className="h-4 w-4 text-indigo-600 dark:text-indigo-400 animate-spin" />
                ) : (
                  <Play className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                )}
              </Button>
            ) : null}

            {/* View details */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => onViewDetails(run.id)}
            >
              <Eye className="h-4 w-4 text-gray-500" />
            </Button>
          </div>
        </div>
      </div>


    </div>
  );
}
