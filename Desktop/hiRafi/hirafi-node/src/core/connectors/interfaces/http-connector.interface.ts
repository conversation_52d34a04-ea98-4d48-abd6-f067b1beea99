/**
 * HTTP Connector Interface
 * Defines the contract for HTTP-based connectors
 */

import { IBaseConnector } from './base-connector.interface.js';

/**
 * HTTP connector interface
 */
export interface IHttpConnector extends IBaseConnector {
  /**
   * Register node with hub
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Optional client ID
   */
  registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null>;

  /**
   * Send heartbeat to hub
   */
  sendHeartbeat(): Promise<boolean>;

  /**
   * Start heartbeat interval
   */
  startHeartbeat(): void;

  /**
   * Stop heartbeat interval
   */
  stopHeartbeat(): void;

  /**
   * Get test by ID
   * @param testId Test ID
   */
  getTest(testId: string): Promise<any>;

  /**
   * Update test status
   * @param testId Test ID
   * @param status New status
   * @param result Test result (optional)
   */
  updateTestStatus(testId: string, status: string, result?: any): Promise<boolean>;

  /**
   * Get next test from hub
   */
  getNextTest(): Promise<any>;

  /**
   * Generic POST method
   * @param url URL to POST to
   * @param data Data to send
   */
  post<T>(url: string, data: any): Promise<T>;
}

/**
 * HTTP registration service interface
 */
export interface IHttpRegistrationService {
  /**
   * Initialize the service
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  initialize(hubUrl: string, nodeSecretKey: string): Promise<void>;

  /**
   * Register node with hub
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Optional client ID
   */
  registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null>;

  /**
   * Close the service
   */
  close(): Promise<void>;

  /**
   * Get service statistics
   */
  getStats(): Record<string, any>;
}

/**
 * HTTP heartbeat service interface
 */
export interface IHttpHeartbeatService {
  /**
   * Initialize the service
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  initialize(hubUrl: string, nodeSecretKey: string): Promise<void>;

  /**
   * Send heartbeat
   * @param nodeId Node ID
   */
  sendHeartbeat(nodeId: string): Promise<boolean>;

  /**
   * Start heartbeat interval
   * @param nodeId Node ID
   */
  startHeartbeat(nodeId: string): void;

  /**
   * Stop heartbeat interval
   */
  stopHeartbeat(): void;

  /**
   * Close the service
   */
  close(): Promise<void>;

  /**
   * Get service statistics
   */
  getStats(): Record<string, any>;
}

/**
 * HTTP test service interface
 */
export interface IHttpTestService {
  /**
   * Initialize the service
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  initialize(hubUrl: string, nodeSecretKey: string): Promise<void>;

  /**
   * Get test by ID
   * @param testId Test ID
   */
  getTest(testId: string): Promise<any>;

  /**
   * Update test status
   * @param testId Test ID
   * @param status New status
   * @param result Test result
   * @param nodeId Node ID
   */
  updateTestStatus(testId: string, status: string, result: any, nodeId: string): Promise<boolean>;

  /**
   * Get next test
   * @param nodeId Node ID
   */
  getNextTest(nodeId: string): Promise<any>;

  /**
   * Close the service
   */
  close(): Promise<void>;

  /**
   * Get service statistics
   */
  getStats(): Record<string, any>;
}
