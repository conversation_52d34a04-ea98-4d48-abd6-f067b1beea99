/**
 * TestRail service module
 * Handles TestRail integration operations
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import {
  isMongoDBInitialized,
  pluginsCollection,
  db,
  reportsCollection,
  runsCollection,
} from './dbConnection.js';
import { getTestRailRunIdForRun, createTestRailRunForRun } from './runTestRailService.js';
import { getRunReportByRunId, updateRunReportTestRail } from './atomicReportService.js';

// TestRail API endpoints
export const TESTRAIL_CONFIG = {
  apiAddResult: process.env.TESTRAIL_API_ADD_RESULT || '/add_result_for_case/',
  apiGetProjects: process.env.TESTRAIL_API_GET_PROJECTS || '/get_projects',
  apiGetSuites: process.env.TESTRAIL_API_GET_SUITES || '/get_suites/'
};

// Constants for API endpoints
const TESTRAIL_API_ADD_RUN = '/add_run/';

/**
 * Create a new TestRail run
 * @param pluginConfig TestRail plugin configuration
 * @param testCaseId TestRail test case ID
 * @param scenarioName Name of the scenario (used for run name)
 * @returns Result of the operation with runId if successful
 */
export async function createTestRailRun(
  pluginConfig: any,
  testCaseId: string,
  scenarioName: string
) {
  try {
    if (!pluginConfig || !pluginConfig.url || !pluginConfig.username || !pluginConfig.apiKey) {
      return {
        success: false,
        message: "Missing TestRail configuration",
        data: null
      };
    }

    // Get the first project from the configuration
    if (!pluginConfig.projectsData || !pluginConfig.projectsData.length) {
      return {
        success: false,
        message: "No projects configured in TestRail settings",
        data: null
      };
    }

    // Get the first suite from the configuration
    if (!pluginConfig.suitesData || !pluginConfig.suitesData.length) {
      return {
        success: false,
        message: "No suites configured in TestRail settings",
        data: null
      };
    }

    const projectId = pluginConfig.projectsData[0].id;
    const suiteId = pluginConfig.suitesData[0].id;

    // Setup TestRail auth
    const auth = {
      username: pluginConfig.username,
      password: pluginConfig.apiKey
    };

    // Prepare API data for creating a run
    const runName = `Automated Test Run for ${scenarioName} - ${new Date().toISOString().split('T')[0]}`;

    // API verilerini hazırla - varsayılan olarak tüm test case'leri dahil et
    let apiData = {
      suite_id: suiteId,
      name: runName,
      description: `Automated test run created for test case ${testCaseId}`,
      include_all: true
    };

    // TestRail'den mevcut case ID'leri al
    try {
      const getCasesUrl = `${pluginConfig.url}/index.php?/api/v2/get_cases/${projectId}&suite_id=${suiteId}`;

      const casesResponse = await axios.get(getCasesUrl, { auth });
      const availableCases = casesResponse.data.cases || [];
      const availableCaseIds = availableCases.map((c: any) => c.id);

      // Sayıya çevir
      const numericTestCaseId = parseInt(testCaseId, 10);

      // Case ID'nin TestRail'de var olup olmadığını kontrol et
      if (!isNaN(numericTestCaseId) && availableCaseIds.includes(numericTestCaseId)) {

        // Case ID varsa, sadece bu case ID'yi dahil et
        apiData = {
          suite_id: suiteId,
          name: runName,
          description: `Automated test run created for test case ${testCaseId}`,
          include_all: false
        };

        // Add case_ids as a separate property to avoid TypeScript error
        (apiData as any).case_ids = [numericTestCaseId];
      } else {
        logger.warn(`Test case ID ${testCaseId} does not exist in TestRail project ${projectId}, suite ${suiteId}`);
        // Varsayılan apiData kullanılacak (include_all: true)
      }
    } catch (error: any) {
      logger.error(`Error validating TestRail case ID: ${error.message}`);
      logger.info(`Falling back to include_all: true due to case ID validation error`);
      // Varsayılan apiData kullanılacak (include_all: true)
    }

    // Build TestRail API URL for creating a run
    const url = `${pluginConfig.url}/index.php?/api/v2${TESTRAIL_API_ADD_RUN}${projectId}`;

    logger.info(`Creating TestRail run: ${url}`);
    logger.info(`Run data: ${JSON.stringify(apiData)}`);

    // Send request to TestRail API
    const response = await axios.post(url, apiData, { auth });

    logger.info(`TestRail run created successfully with ID: ${response.data.id}`);

    // Generate the TestRail run URL
    const runUrl = `${pluginConfig.url}/index.php?/runs/view/${response.data.id}`;
    logger.info(`TestRail run URL: ${runUrl}`);

    // response.data.id'nin sayı olduğundan emin ol
    let numericRunId: number;
    if (typeof response.data.id === 'number') {
      numericRunId = response.data.id;
    } else {
      numericRunId = parseInt(String(response.data.id), 10);
      if (isNaN(numericRunId)) {
        logger.error(`Invalid TestRail run ID returned from API: ${response.data.id} is not a valid number`);
        return {
          success: false,
          message: `Invalid TestRail run ID returned from API: ${response.data.id} is not a valid number`,
          data: null
        };
      }
    }

    logger.info(`Converted TestRail run ID to number: ${numericRunId} (original: ${response.data.id}, type: ${typeof response.data.id})`);

    return {
      success: true,
      message: "TestRail run created successfully",
      data: response.data,
      runId: numericRunId,
      runUrl: runUrl
    };
  } catch (error: any) {
    logger.error(`Error creating TestRail run: ${error.message}`);
    return {
      success: false,
      message: `Error creating TestRail run: ${error.message}`,
      data: error.response?.data
    };
  }
}

/**
 * Send test result to TestRail
 * @param reportData Test report data
 * @param pluginConfig TestRail plugin configuration
 * @param testCaseId TestRail test case ID
 * @param runId Optional TestRail run ID
 * @returns Result of the operation
 */
export async function sendResultToTestRail(
  reportData: any,
  pluginConfig: any,
  testCaseId: string,
  runId?: number
) {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: "Database connection not established", data: null };
    }

    if (!pluginConfig || !pluginConfig.url || !pluginConfig.username || !pluginConfig.apiKey) {
      return {
        success: false,
        message: "Missing TestRail configuration",
        data: null
      };
    }

    // Setup TestRail auth
    const auth = {
      username: pluginConfig.username,
      password: pluginConfig.apiKey
    };

    // Map status codes
    // 1: Passed, 2: Blocked, 3: Untested, 4: Retest, 5: Failed
    let status_id = 1; // Default to passed
    if (reportData.status === 'failed') {
      status_id = 5; // Failed
    } else if (reportData.status === 'skipped') {
      status_id = 2; // Blocked
    }

    // Prepare API data with a more structured report
    const formattedDate = new Date().toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    // Create a structured comment with test details
    let comment = `# Test Execution Report\n\n`;
    comment += `**Execution Date:** ${formattedDate}\n`;
    comment += `**Status:** ${reportData.status === 'failed' ? '❌ Failed' : reportData.status === 'skipped' ? '⚠️ Skipped' : '✅ Passed'}\n`;
    comment += `**Report ID:** ${reportData.id}\n`;

    // Add scenario information if available
    if (reportData.scenarioName) {
      comment += `**Scenario:** ${reportData.scenarioName}\n`;
    }

    // Add duration information if available
    if (reportData.duration) {
      // Duration is already in milliseconds, convert to a more readable format
      const totalSeconds = reportData.duration / 1000;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = Math.floor(totalSeconds % 60);

      if (minutes > 0) {
        comment += `**Duration:** ${minutes}m ${seconds}s (${totalSeconds.toFixed(2)} seconds)\n`;
      } else {
        comment += `**Duration:** ${totalSeconds.toFixed(2)} seconds\n`;
      }
    }

    // Add summary information if available
    if (reportData.summary) {
      comment += `\n## Summary\n`;
      comment += `- Total Steps: ${reportData.summary.total || 0}\n`;
      comment += `- Passed: ${reportData.summary.passed || 0}\n`;
      comment += `- Failed: ${reportData.summary.failed || 0}\n`;
      comment += `- Errors: ${reportData.summary.errors || 0}\n`;
    }

    // Add step details if available
    if (reportData.steps && reportData.steps.length > 0) {
      comment += `\n## Test Steps\n`;

      // Create a table header
      comment += `| # | Step | Status | Duration |\n`;
      comment += `| --- | --- | --- | --- |\n`;

      // Add each step to the table
      const failedSteps: any[] = [];

      reportData.steps.forEach((step: any, index: number) => {
        const isSuccess = step.success === true;
        const stepStatus = isSuccess ? '✅ Passed' : step.error ? '❌ Failed' : '⚠️ Unknown';
        const stepDuration = step.duration ? `${(step.duration / 1000).toFixed(2)}s` : 'N/A';

        // Sanitize step name for markdown table (escape pipe characters)
        let stepName = step.name || `Step ${index + 1}`;
        stepName = stepName.replace(/\|/g, '\\|').replace(/\n/g, ' ');

        // Truncate very long step names
        if (stepName.length > 80) {
          stepName = stepName.substring(0, 77) + '...';
        }

        comment += `| ${index + 1} | ${stepName} | ${stepStatus} | ${stepDuration} |\n`;

        // Collect failed steps for detailed error section
        if (!isSuccess) {
          failedSteps.push({
            index: index + 1,
            name: stepName,
            error: step.error
          });
        }
      });

      // Add detailed information about failed steps if any
      if (failedSteps.length > 0) {
        comment += `\n## Failed Steps Details\n`;

        failedSteps.forEach(step => {
          comment += `### Step ${step.index}: ${step.name}\n`;
          if (step.error) {
            comment += `\`\`\`\n${step.error}\n\`\`\`\n`;
          } else {
            comment += `No detailed error information available.\n`;
          }
        });
      }
    }

    // Add error information if available
    if (reportData.error) {
      comment += `\n## Error\n`;
      comment += `\`\`\`\n${reportData.error}\n\`\`\`\n`;
    }

    // Add link to the test report - always use hirafi.ai for TestRail links
    const reportUrl = `https://hirafi.ai/reports/${reportData.id}`;
    comment += `\n## Links\n`;
    comment += `[View Detailed Report](${reportUrl})\n`;

    const apiData = {
      status_id: status_id,
      comment: comment
    };

    // Build TestRail API URL
    // TestRail API'si run_id'nin bir sayı olmasını bekliyor, string değil
    let numericRunId: number | null = null;

    if (runId) {
      // Önce runId'nin bir sayı olup olmadığını kontrol et
      if (typeof runId === 'number') {
        numericRunId = runId;
      } else {
        // String veya başka bir tip ise sayıya çevirmeyi dene
        numericRunId = parseInt(String(runId), 10);
      }

      if (isNaN(numericRunId)) {
        logger.error(`Invalid TestRail run ID: ${runId} is not a valid number`);
        numericRunId = null;
      } else {
        logger.info(`Using TestRail run ID: ${numericRunId} (converted from ${runId})`);
      }
    }

    // Eğer runId geçerli değilse, pluginConfig'den al
    if (!numericRunId && pluginConfig.runId) {
      if (typeof pluginConfig.runId === 'number') {
        numericRunId = pluginConfig.runId;
      } else {
        numericRunId = parseInt(String(pluginConfig.runId), 10);
      }

      if (isNaN(numericRunId!)) {
        logger.error(`Invalid TestRail run ID from config: ${pluginConfig.runId} is not a valid number`);
        numericRunId = null;
      } else {
        logger.info(`Using TestRail run ID from config: ${numericRunId}`);
      }
    }

    // Hala geçerli bir run ID yoksa, hata döndür
    if (!numericRunId) {
      logger.error(`No valid TestRail run ID found`);
      return {
        success: false,
        message: `No valid TestRail run ID found`,
        data: null
      };
    }

    const effectiveRunId = numericRunId || 0;
    logger.info(`Using effective TestRail run ID: ${effectiveRunId}`);

    // TestRail API'si case_id'nin bir sayı olmasını bekliyor, string değil
    // Eğer testCaseId bir string ise, sayıya çevirelim
    const numericTestCaseId = parseInt(testCaseId, 10);

    if (isNaN(numericTestCaseId)) {
      logger.error(`Invalid TestRail case ID: ${testCaseId} is not a valid number`);
      return {
        success: false,
        message: `Invalid TestRail case ID: ${testCaseId} is not a valid number`,
        data: null
      };
    }

    const url = `${pluginConfig.url}/index.php?/api/v2${TESTRAIL_CONFIG.apiAddResult}/${effectiveRunId}/${numericTestCaseId}`;

    logger.info(`Sending result to TestRail: ${url}`);

    // Send request to TestRail API
    try {
      const response = await axios.post(url, apiData, { auth });

      return {
        success: true,
        message: "Result sent to TestRail successfully",
        data: response.data
      };
    } catch (apiError: any) {
      // Detaylı hata mesajını logla
      logger.error(`TestRail API error: ${apiError.message}`);
      if (apiError.response) {
        logger.error(`TestRail API response status: ${apiError.response.status}`);
        logger.error(`TestRail API response data: ${JSON.stringify(apiError.response.data)}`);

        // "No (active) test found for the run/case combination" hatası için özel işlem
        if (apiError.response.status === 400 &&
            apiError.response.data &&
            apiError.response.data.error &&
            apiError.response.data.error.includes("No (active) test found for the run/case combination")) {

          logger.warn(`TestRail run ${effectiveRunId} does not contain test case ${numericTestCaseId}. Attempting to add the test case to the run...`);

          // Bu durumda, test case'i run'a eklemek için bir çözüm önerebiliriz
          return {
            success: false,
            message: `TestRail run ${effectiveRunId} does not contain test case ${numericTestCaseId}. The test case needs to be added to the run first.`,
            data: apiError.response.data,
            errorCode: "NO_TEST_IN_RUN",
            runId: effectiveRunId,
            caseId: numericTestCaseId
          };
        }
      }

      // Hatayı yukarı fırlat
      throw apiError;
    }
  } catch (error: any) {
    logger.error(`Error sending result to TestRail: ${error.message}`);
    return {
      success: false,
      message: `Error sending result to TestRail: ${error.message}`,
      data: error.response?.data
    };
  }
}

/**
 * Send report to TestRail
 * @param reportId Report ID
 * @param userId User ID
 * @param testCaseId TestRail test case ID
 * @returns Result of the operation
 */
export async function sendReportToTestRail(
  reportId: string,
  userId: string,
  testCaseId: string,
  runId?: string // Run ID (optional)
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection || !reportsCollection || !runsCollection) {
      return { success: false, message: "Database connection not established", data: null };
    }

    // Get user's TestRail configuration
    const plugin = await pluginsCollection.findOne({
      userId: userId,
      "plugins.id": "testrail"
    });

    if (!plugin) {
      return {
        success: false,
        message: "TestRail plugin not configured for this user",
        data: null
      };
    }

    // Find the testrail plugin from plugins array
    const testrailPlugin = plugin.plugins.find((p: any) => p.id === "testrail");

    if (!testrailPlugin || !testrailPlugin.config) {
      return {
        success: false,
        message: "TestRail plugin configuration not found",
        data: null
      };
    }

    // Get report
    const report = await reportsCollection.findOne({ id: reportId });

    if (!report) {
      return {
        success: false,
        message: `Report with ID ${reportId} not found`,
        data: null
      };
    }

    // Get scenario name for the run
    let scenarioName = "Automated Test";
    if (report.scenarioName) {
      scenarioName = report.scenarioName;
    } else if (report.name) {
      scenarioName = report.name;
    }

    // Önce run ID'si varsa, bu run için TestRail run ID'sini kontrol et
    let runResult: any = { success: false };

    if (runId) {
      logger.info(`Checking if run ${runId} has a TestRail run ID`);

      // Sadece bir kere dene, retry mekanizması yok
      const testrailRunResult = await getTestRailRunIdForRun(runId);

      if (testrailRunResult.success && testrailRunResult.data) {

        runResult = {
          success: true,
          message: "Using existing TestRail run",
          runId: testrailRunResult.data.testrailRunId,
          runUrl: testrailRunResult.data.testrailRunUrl
        };
      } else {
        logger.warn(`No TestRail run ID found for run ${runId}: ${testrailRunResult.message}`);

        // Run için TestRail run ID'si bulunamadı, yeni bir run oluşturmak için gerekli bilgileri al
        try {
          // Run'ın takım ve şirket bilgilerini al
          const run = await runsCollection.findOne({ id: runId });

          if (run && run.teamId && run.companyId) {
            logger.info(`Attempting to create TestRail run for run ${runId}`);

            // TestRail run oluştur
            const createResult = await createTestRailRunForRun(
              runId,
              run.teamId,
              run.companyId,
              run.name || `Run ${runId}`
            );

            if (createResult.success) {

              runResult = {
                success: true,
                message: "Created new TestRail run for this run",
                runId: createResult.runId,
                runUrl: createResult.runUrl
              };
            } else {
              logger.warn(`Failed to create TestRail run for run ${runId}: ${createResult.message}`);
            }
          } else {
            logger.warn(`Run ${runId} not found or missing teamId/companyId`);
          }
        } catch (error: any) {
          logger.error(`Error creating TestRail run for run ${runId}: ${error.message}`);
        }
      }
    }

    // Eğer run ID'si yoksa veya bu run için TestRail run ID'si bulunamadıysa, yeni bir run oluştur
    if (!runResult.success) {
      // Create a TestRail run first
      logger.info(`Creating TestRail run for test case ${testCaseId}`);
      runResult = await createTestRailRun(testrailPlugin.config, testCaseId, scenarioName);
    }

    if (!runResult.success) {
      logger.error(`Failed to create TestRail run: ${runResult.message}`);
      return runResult;
    }

    logger.info(`TestRail run created with ID: ${runResult.runId}`);

    // Send result to TestRail using the newly created run
    logger.info(`Sending result to TestRail using run ID: ${runResult.runId} (type: ${typeof runResult.runId})`);

    // runResult.runId'nin sayı olduğundan emin ol
    let numericRunId: number;
    if (typeof runResult.runId === 'number') {
      numericRunId = runResult.runId;
    } else {
      numericRunId = parseInt(String(runResult.runId), 10);
      if (isNaN(numericRunId)) {
        logger.error(`Invalid TestRail run ID from runResult: ${runResult.runId} is not a valid number`);
        return {
          success: false,
          message: `Invalid TestRail run ID: ${runResult.runId} is not a valid number`,
          data: null
        };
      }
    }

    const result = await sendResultToTestRail(report, testrailPlugin.config, testCaseId, numericRunId);

    // Eğer "No (active) test found for the run/case combination" hatası alındıysa
    if (!result.success && result.errorCode === "NO_TEST_IN_RUN") {
      logger.warn(`TestRail run ${result.runId} does not contain test case ${result.caseId}. Attempting to update the run...`);

      try {
        // TestRail API'sine istek göndererek run'ı güncelle ve test case'i ekle
        const updateRunUrl = `${testrailPlugin.config.url}/index.php?/api/v2/update_run/${result.runId}`;

        // TestRail auth bilgilerini ayarla
        const auth = {
          username: testrailPlugin.config.username,
          password: testrailPlugin.config.apiKey
        };

        // API verilerini hazırla - sadece case_ids'yi güncelle
        const updateData = {
          case_ids: [result.caseId] // Eklenecek test case ID'si
        };

        logger.info(`Updating TestRail run ${result.runId} to add test case ${result.caseId}: ${updateRunUrl}`);
        logger.info(`Update data: ${JSON.stringify(updateData)}`);

        // TestRail API'ye istek gönder
        await axios.post(updateRunUrl, updateData, { auth });

        logger.info(`TestRail run updated successfully. Retrying to send result...`);

        // Tekrar sonuç göndermeyi dene
        const retryResult = await sendResultToTestRail(report, testrailPlugin.config, testCaseId, runResult.runId);

        if (retryResult.success) {

          // Başarılı sonucu kullan
          Object.assign(result, retryResult);
        } else {
          logger.error(`Failed to send result to TestRail after adding test case to run: ${retryResult.message}`);
        }
      } catch (updateError: any) {
        logger.error(`Error updating TestRail run: ${updateError.message}`);
        if (updateError.response) {
          logger.error(`TestRail API response status: ${updateError.response.status}`);
          logger.error(`TestRail API response data: ${JSON.stringify(updateError.response.data)}`);
        }
      }
    }

    // Update report with TestRail status if successful
    if (result.success) {
      await updateReportTestRailStatus(reportId, {
        sentAt: new Date(),
        testCaseId: testCaseId,
        runId: runResult.runId,
        runUrl: runResult.runUrl,
        status: result.success ? 'success' : 'failed'
      });

      // Also update the report with the TestRail run URL
      if (runResult.runUrl) {
        await reportsCollection.updateOne(
          { id: reportId },
          {
            $set: {
              testrailRunLink: runResult.runUrl,
              updatedAt: new Date()
            }
          }
        );
        logger.info(`Updated report ${reportId} with TestRail run URL: ${runResult.runUrl}`);

        // If this is part of a run, also update the run report with TestRail information
        if (runId && report.runId) {
          try {
            const runReportResult = await getRunReportByRunId(report.runId);

            if (runReportResult.success && runReportResult.report) {
              await updateRunReportTestRail(
                runReportResult.report.id,
                numericRunId,
                runResult.runUrl
              );
              logger.info(`Updated run report ${runReportResult.report.id} for run ${report.runId} with TestRail run ID: ${numericRunId} and URL: ${runResult.runUrl}`);
            }
          } catch (runReportError: any) {
            logger.warn(`Error updating run report with TestRail information: ${runReportError.message}`);
          }
        }
      }
    }

    return result;
  } catch (error: any) {
    logger.error(`Error sending report to TestRail: ${error.message}`);
    return {
      success: false,
      message: `Error sending report to TestRail: ${error.message}`,
      data: null
    };
  }
}

/**
 * Verify TestRail connection
 * @param url TestRail URL
 * @param username TestRail username/email
 * @param apiKey TestRail API key
 * @returns Result of the operation
 */
export async function verifyTestRailConnection(url: string, username: string, apiKey: string) {
  try {
    // Input validation and sanitization
    const trimmedUrl = url?.trim();
    const trimmedUsername = username?.trim();
    const trimmedApiKey = apiKey?.trim();

    if (!trimmedUrl || !trimmedUsername || !trimmedApiKey) {
      return {
        success: false,
        message: "Gerekli parametreler eksik: URL, kullanıcı adı veya API anahtarı",
        errorCode: "MISSING_PARAMETERS",
        data: null
      };
    }

    // URL format validation
    if (!trimmedUrl.includes('.')) {
      return {
        success: false,
        message: "Geçersiz URL formatı. Lütfen doğru TestRail URL'ini girin.",
        errorCode: "INVALID_URL_FORMAT",
        data: null
      };
    }

    // Clean up URL if needed (remove trailing slash)
    const cleanUrl = trimmedUrl.endsWith('/') ? trimmedUrl.slice(0, -1) : trimmedUrl;

    // Add https:// if not present
    const fullUrl = cleanUrl.startsWith('http') ? cleanUrl : `https://${cleanUrl}`;

    // Setup TestRail auth
    const auth = {
      username: trimmedUsername,
      password: trimmedApiKey
    };

    // Build TestRail API URL
    const apiUrl = `${fullUrl}/index.php?/api/v2/get_projects`;

    logger.info(`Verifying TestRail connection: ${apiUrl}`);

    // Send request to TestRail API with timeout
    const response = await axios.get(apiUrl, { 
      auth,
      timeout: 10000, // 10 second timeout
      headers: {
        'User-Agent': 'TestRail-Integration/1.0'
      }
    });

    logger.info(`TestRail connection verified successfully`);

    return {
      success: true,
      message: "TestRail bağlantısı başarıyla doğrulandı",
      errorCode: null,
      data: response.data
    };
  } catch (error: any) {
    logger.error(`Error verifying TestRail connection: ${error.message}`);
    
    // Detailed error handling based on axios error response
    if (error.response) {
      const status = error.response.status;
      const statusText = error.response.statusText;
      const responseData = error.response.data;

      switch (status) {
        case 401:
          return {
            success: false,
            message: "Kimlik doğrulama hatası. Email adresinizi ve API anahtarınızı kontrol edin.",
            errorCode: "AUTHENTICATION_FAILED",
            data: { status, statusText, responseData }
          };
        
        case 403:
          return {
            success: false,
            message: "Erişim reddedildi. Kullanıcınızın TestRail'e erişim yetkisi olmayabilir.",
            errorCode: "ACCESS_DENIED",
            data: { status, statusText, responseData }
          };
        
        case 404:
          return {
            success: false,
            message: "TestRail instance bulunamadı. URL'yi kontrol edin.",
            errorCode: "INSTANCE_NOT_FOUND",
            data: { status, statusText, responseData }
          };
        
        case 429:
          return {
            success: false,
            message: "Çok fazla istek gönderildi. Lütfen birkaç dakika bekleyip tekrar deneyin.",
            errorCode: "RATE_LIMITED",
            data: { status, statusText, responseData }
          };
        
        case 500:
        case 502:
        case 503:
        case 504:
          return {
            success: false,
            message: "TestRail sunucusunda bir sorun var. Lütfen daha sonra tekrar deneyin.",
            errorCode: "SERVER_ERROR",
            data: { status, statusText, responseData }
          };
        
        default:
          return {
            success: false,
            message: `TestRail API hatası: ${status} ${statusText}`,
            errorCode: "API_ERROR",
            data: { status, statusText, responseData }
          };
      }
    } else if (error.request) {
      // Network error
      if (error.code === 'ENOTFOUND') {
        return {
          success: false,
          message: "TestRail sunucusuna ulaşılamıyor. URL'yi kontrol edin veya internet bağlantınızı kontrol edin.",
          errorCode: "DNS_RESOLUTION_FAILED",
          data: { code: error.code, message: error.message }
        };
      } else if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          message: "TestRail sunucusu bağlantıları reddediyor. URL ve port bilgilerini kontrol edin.",
          errorCode: "CONNECTION_REFUSED",
          data: { code: error.code, message: error.message }
        };
      } else if (error.code === 'ETIMEDOUT') {
        return {
          success: false,
          message: "Bağlantı zaman aşımına uğradı. TestRail sunucunuz yavaş yanıt veriyor olabilir.",
          errorCode: "TIMEOUT",
          data: { code: error.code, message: error.message }
        };
      } else {
        return {
          success: false,
          message: "Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.",
          errorCode: "NETWORK_ERROR",
          data: { code: error.code, message: error.message }
        };
      }
    } else {
      // Other error
      return {
        success: false,
        message: `Beklenmeyen hata: ${error.message || 'Bilinmeyen hata'}`,
        errorCode: "UNKNOWN_ERROR",
        data: { message: error.message }
      };
    }
  }
}

/**
 * Verify TestRail connection using a settings object
 * @param settings Object containing TestRail settings (url, username, apiKey)
 * @returns Result of the operation
 */
export async function verifyTestRailConnectionWithSettings(settings: { url: string, username: string, apiKey: string }) {
  try {
    // Extract settings
    const { url, username, apiKey } = settings;

    // Call the original function with extracted parameters
    return await verifyTestRailConnection(url, username, apiKey);
  } catch (error: any) {
    logger.error(`Error verifying TestRail connection with settings: ${error.message}`);
    return {
      success: false,
      message: `Error verifying TestRail connection: ${error.message || 'Unknown error'}`,
      data: null
    };
  }
}

/**
 * Get TestRail projects
 * @param url TestRail URL
 * @param username TestRail username/email
 * @param apiKey TestRail API key
 * @returns List of TestRail projects
 */
export async function getTestRailProjects(url: string, username: string, apiKey: string) {
  try {
    if (!url || !username || !apiKey) {
      return {
        success: false,
        message: "Missing TestRail configuration",
        data: null,
        projects: []
      };
    }

    // Setup TestRail auth
    const auth = {
      username: username,
      password: apiKey
    };

    // Build TestRail API URL
    const apiUrl = `${url}/index.php?/api/v2${TESTRAIL_CONFIG.apiGetProjects}`;

    // Send request to TestRail API
    const response = await axios.get(apiUrl, { auth });

    return {
      success: true,
      message: "TestRail projects retrieved successfully",
      data: response.data,
      projects: response.data
    };
  } catch (error: any) {
    logger.error(`Error getting TestRail projects: ${error.message}`);
    return {
      success: false,
      message: `Error getting TestRail projects: ${error.message}`,
      data: error.response?.data,
      projects: []
    };
  }
}

/**
 * Get TestRail suites for one or more projects
 * @param url TestRail URL
 * @param username TestRail username/email
 * @param apiKey TestRail API key
 * @param projectIds Array of TestRail project IDs or a single project ID
 * @returns Test suites grouped by project ID
 */
export async function getTestRailSuites(url: string, username: string, apiKey: string, projectIds: number[] | number) {
  try {
    if (!url || !username || !apiKey) {
      return {
        success: false,
        message: "Missing TestRail configuration",
        data: null,
        suitesByProject: {},
        suites: []
      };
    }

    // Convert single project ID to array if needed
    const projectIdArray = Array.isArray(projectIds) ? projectIds : [projectIds];

    if (projectIdArray.length === 0) {
      return {
        success: false,
        message: "No project IDs provided",
        data: null,
        suitesByProject: {},
        suites: []
      };
    }

    // Setup TestRail auth
    const auth = {
      username: username,
      password: apiKey
    };

    // Fetch suites for all projects in parallel
    const results = await Promise.all(
      projectIdArray.map(async (projectId) => {
        try {
          // Build TestRail API URL
          const apiUrl = `${url}/index.php?/api/v2${TESTRAIL_CONFIG.apiGetSuites}${projectId}`;

          // Send request to TestRail API
          const response = await axios.get(apiUrl, { auth });

          return {
            projectId,
            success: true,
            suites: response.data
          };
        } catch (error: any) {
          logger.error(`Error getting TestRail suites for project ${projectId}: ${error.message}`);
          return {
            projectId,
            success: false,
            error: error.message,
            suites: []
          };
        }
      })
    );

    // Combine results
    const allSuites: any[] = [];
    const suitesByProject: Record<number, any[]> = {};

    results.forEach(result => {
      if (result.success) {
        // Add project_id to each suite for easier filtering on frontend
        const suitesWithProjectId = result.suites.map((suite: any) => ({
          ...suite,
          project_id: result.projectId
        }));

        // Add to all suites array
        allSuites.push(...suitesWithProjectId);

        // Add to project-specific collection
        suitesByProject[result.projectId] = suitesWithProjectId;
      }
    });

    // Check if we got any suites
    if (allSuites.length === 0) {
      // If we have no suites, check if all requests failed
      const allFailed = results.every(result => !result.success);
      if (allFailed) {
        return {
          success: false,
          message: "Failed to retrieve suites for all projects",
          data: results,
          suitesByProject: {},
          suites: []
        };
      }
    }

    return {
      success: true,
      message: "TestRail suites retrieved successfully",
      data: results,
      suitesByProject,
      suites: allSuites
    };
  } catch (error: any) {
    logger.error(`Error getting TestRail suites: ${error.message}`);
    return {
      success: false,
      message: `Error getting TestRail suites: ${error.message}`,
      data: null,
      suitesByProject: {},
      suites: []
    };
  }
}

/**
 * Update TestRail configuration
 * @param teamId team ID
 * @param companyId company ID
 * @param url TestRail URL
 * @param username TestRail username
 * @param apiKey TestRail API key
 * @param projectsData Array of projects with names {id: string|number, name: string}
 * @param suitesData Array of suites with names {id: string|number, name: string}
 * @param skipProjectValidation Skip project and suite validation (for initial config save)
 * @returns Object with success status
 */
export async function updateTestRailConfig(
  teamId: string,
  companyId: string,
  url: string,
  username: string,
  apiKey: string,
  projectsData: Array<{id: string|number, name: string}> = [],
  suitesData: Array<{id: string|number, name: string}> = [],
  skipProjectValidation: boolean = false
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return {
        success: false,
        message: "Database connection not established"
      };
    }

    // Temel doğrulama kontrolleri
    if (!url || !username || !apiKey) {
      return {
        success: false,
        message: "Missing required TestRail credentials (url, username, apiKey)"
      };
    }

    // Skip project and suite validation if this is an initial config save
    if (!skipProjectValidation) {
      if (!projectsData.length) {
        return {
          success: false,
          message: "At least one project must be selected"
        };
      }

      if (!suitesData.length) {
        return {
          success: false,
          message: "At least one suite must be selected"
        };
      }
    }

    // Zaman damgası
    const now = new Date();

    // Config nesnesini oluştur
    const config = {
      url,
      username,
      apiKey,
      projectsData,
      suitesData,
      updatedAt: now
    };

    // Plugin nesnesini oluştur
    const testrailPlugin = {
      id: "testrail",
      name: "TestRail Integration",
      description: "Integrate with TestRail to sync test cases and results",
      active: true,
      config,
      createdAt: now,
      updatedAt: now
    };

    logger.info(`Updating TestRail configuration for team: ${teamId}, company: ${companyId}`);

    // TeamId ve companyId ile plugin dokümanını kontrol et
    const existingPlugins = await pluginsCollection.findOne({ teamId, companyId });

    if (!existingPlugins) {
      // Yeni plugin dokümanı oluştur
      logger.info(`Creating new plugins document for team: ${teamId}, company: ${companyId}`);
      await pluginsCollection.insertOne({
        teamId,
        companyId,
        plugins: [testrailPlugin]
      });

      return {
        success: true,
        message: "TestRail configuration created successfully"
      };
    }

    // TestRail plugin var mı kontrol et
    const testrailExists = existingPlugins.plugins.some((plugin: any) => plugin.id === "testrail");

    if (testrailExists) {
      // Mevcut TestRail pluginini güncelle
      logger.info(`Updating existing TestRail plugin for team: ${teamId}, company: ${companyId}`);
      const result = await pluginsCollection.updateOne(
        { teamId, companyId, "plugins.id": "testrail" },
        {
          $set: {
            "plugins.$.name": testrailPlugin.name,
            "plugins.$.description": testrailPlugin.description,
            "plugins.$.active": testrailPlugin.active,
            "plugins.$.config": testrailPlugin.config,
            "plugins.$.updatedAt": now
          }
        }
      );

      if (result.matchedCount === 0) {
        return {
          success: false,
          message: "Failed to update TestRail plugin configuration"
        };
      }

      return {
        success: true,
        message: "TestRail configuration updated successfully"
      };
    }

    // Mevcut plugin listesine TestRail ekle
    logger.info(`Adding TestRail plugin to existing plugins for team: ${teamId}, company: ${companyId}`);

    // $push operatörü kullanarak update
    const result = await pluginsCollection.updateOne(
      { teamId, companyId },
      {
        $push: {
          plugins: testrailPlugin as any // Type hatası çözümü
        }
      }
    );

    if (!result.acknowledged) {
      return {
        success: false,
        message: "Failed to add TestRail plugin to team's plugins"
      };
    }

    return {
      success: true,
      message: "TestRail plugin added successfully"
    };
  } catch (error: any) {
    logger.error(`Error updating TestRail configuration: ${error.message}`);
    return {
      success: false,
      message: `Error updating TestRail configuration: ${error.message}`
    };
  }
}

/**
 * Get TestRail status for a report
 * @param reportId Report ID
 * @returns TestRail status
 */
export async function getTestRailStatus(reportId: string) {
  try {
    if (!isMongoDBInitialized() || !db || !reportsCollection) {
      return { success: false, message: "Database connection not established", data: null };
    }

    // Get report
    const report = await reportsCollection.findOne(
      { id: reportId },
      { projection: { testRailStatus: 1 } }
    );

    if (!report) {
      return {
        success: false,
        message: `Report with ID ${reportId} not found`,
        data: null
      };
    }

    return {
      success: true,
      message: "TestRail status retrieved successfully",
      data: report.testRailStatus || null
    };
  } catch (error: any) {
    logger.error(`Error getting TestRail status for report ${reportId}: ${error.message}`);
    return {
      success: false,
      message: `Error getting TestRail status: ${error.message}`,
      data: null
    };
  }
}

/**
 * Update TestRail project and suite for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @param projectId TestRail project ID
 * @param suiteId TestRail suite ID
 * @returns Result of the operation
 */
export async function updateTestRailProjectAndSuite(
  teamId: string,
  companyId: string,
  projectId: number,
  suiteId: number
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return {
        success: false,
        message: "Database connection not established"
      };
    }

    if (!teamId || !companyId) {
      return {
        success: false,
        message: "Team ID and Company ID are required"
      };
    }

    if (!projectId || !suiteId) {
      return {
        success: false,
        message: "Project ID and Suite ID are required"
      };
    }

    logger.info(`Updating TestRail project: ${projectId} and suite: ${suiteId} for team: ${teamId}, company: ${companyId}`);

    // Get team's TestRail plugin configuration
    const pluginData = await pluginsCollection.findOne({
      teamId: teamId,
      companyId: companyId,
      "plugins.id": "testrail"
    });

    if (!pluginData) {
      return {
        success: false,
        message: "TestRail plugin not configured for this team"
      };
    }

    // Find the testrail plugin from plugins array
    const pluginIndex = pluginData.plugins.findIndex((p: any) => p.id === "testrail");

    if (pluginIndex === -1) {
      return {
        success: false,
        message: "TestRail plugin not found for this team"
      };
    }

    // Get the current plugin configuration
    const testrailPlugin = pluginData.plugins[pluginIndex];
    const config = testrailPlugin.config || {};

    // Create project and suite data objects
    const projectData = { id: projectId, name: `Project ${projectId}` };
    const suiteData = { id: suiteId, name: `Suite ${suiteId}` };

    // Update the configuration with the new project and suite
    const updatedConfig = {
      ...config,
      projectsData: [projectData],
      suitesData: [suiteData],
      updatedAt: new Date()
    };

    // Update the plugin configuration
    const result = await pluginsCollection.updateOne(
      { teamId, companyId, "plugins.id": "testrail" },
      {
        $set: {
          "plugins.$.config": updatedConfig,
          "plugins.$.updatedAt": new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: "Failed to update TestRail project and suite configuration"
      };
    }

    return {
      success: true,
      message: "TestRail project and suite updated successfully"
    };
  } catch (error: any) {
    logger.error(`Error updating TestRail project and suite: ${error.message}`);
    return {
      success: false,
      message: `Error updating TestRail project and suite: ${error.message}`
    };
  }
}

/**
 * Update TestRail status for a report
 * @param reportId Report ID
 * @param testRailStatus TestRail status object with testCaseId, runId, status, etc.
 * @returns Result of the operation
 */
export async function updateReportTestRailStatus(reportId: string, testRailStatus: any) {
  try {
    if (!isMongoDBInitialized() || !db || !reportsCollection) {
      return { success: false, message: "Database connection not established", data: null };
    }

    // Update report
    const result = await reportsCollection.updateOne(
      { id: reportId },
      { $set: {
        testRailStatus: testRailStatus,
        updatedAt: new Date()
      } }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: `Report with ID ${reportId} not found`,
        data: null
      };
    }

    logger.info(`Updated TestRail status for report ${reportId}`);

    return {
      success: true,
      message: "TestRail status updated successfully",
      data: null
    };
  } catch (error: any) {
    logger.error(`Error updating TestRail status for report ${reportId}: ${error.message}`);
    return {
      success: false,
      message: `Error updating TestRail status: ${error.message}`,
      data: null
    };
  }
}

/**
 * Delete TestRail plugin configuration for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @returns Result of the operation
 */
export async function deleteTestRailConfig(teamId: string, companyId: string) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return {
        success: false,
        message: "Database connection not established"
      };
    }

    if (!teamId || !companyId) {
      return {
        success: false,
        message: "Team ID and Company ID are required"
      };
    }

    logger.info(`Deleting TestRail configuration for team: ${teamId}, company: ${companyId}`);

    // TestRail plugin'ini plugins array'inden kaldır
    const result = await pluginsCollection.updateOne(
      { teamId, companyId },
      {
        $pull: {
          plugins: { id: "testrail" } as any
        }
      }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: "TestRail plugin configuration not found for this team"
      };
    }

    if (result.modifiedCount === 0) {
      return {
        success: false,
        message: "TestRail plugin was not found in the team's configuration"
      };
    }

    logger.info(`TestRail configuration deleted successfully for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: "TestRail integration has been disconnected successfully"
    };
  } catch (error: any) {
    logger.error(`Error deleting TestRail configuration: ${error.message}`);
    return {
      success: false,
      message: `Error deleting TestRail configuration: ${error.message}`
    };
  }
}

/**
 * Get TestRail plugin configuration for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @param userId User ID (optional for backward compatibility)
 * @returns TestRail plugin configuration
 */
export async function getTestRailConfig(teamId?: string, companyId?: string, userId?: string) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return {
        success: false,
        message: "Database connection not established",
        data: null,
        config: null,
        plugin: null
      };
    }

    // Öncelikle teamId ve companyId ile arama yap
    if (teamId && companyId) {


      // TeamId ve companyId ile plugin dokümanını ara
      const pluginData = await pluginsCollection.findOne({
        teamId,
        companyId,
        "plugins.id": "testrail"
      });

      if (pluginData) {
        // Find the testrail plugin from plugins array
        const testrailPlugin = pluginData.plugins.find((p: any) => p.id === "testrail");

        if (testrailPlugin) {
          if (testrailPlugin.config) {
            // Return the plugin configuration
            return {
              success: true,
              message: "TestRail configuration retrieved successfully",
              data: testrailPlugin,
              config: testrailPlugin.config,
              plugin: testrailPlugin
            };
          } else {
            return {
              success: false,
              message: "TestRail plugin configuration not found",
              data: null,
              config: null,
              plugin: testrailPlugin
            };
          }
        }
      }
    }

    // TeamId ve companyId ile bulunamadıysa veya parametreler verilmediyse, geriye dönük uyumluluk için userId ile dene
    if (userId) {


      // Get user's TestRail plugin configuration
      const pluginData = await pluginsCollection.findOne({
        userId: userId,
        "plugins.id": "testrail"
      });

      if (pluginData) {
        // Find the testrail plugin from plugins array
        const testrailPlugin = pluginData.plugins.find((p: any) => p.id === "testrail");

        if (testrailPlugin) {
          if (testrailPlugin.config) {
            // Return the plugin configuration
            return {
              success: true,
              message: "TestRail configuration retrieved successfully",
              data: testrailPlugin,
              config: testrailPlugin.config,
              plugin: testrailPlugin
            };
          } else {
            return {
              success: false,
              message: "TestRail plugin configuration not found",
              data: null,
              config: null,
              plugin: testrailPlugin
            };
          }
        }
      }
    }

    // Eğer hiçbir konfigürasyon bulunamadıysa, güvenlik nedeniyle başka kullanıcıların config'lerini döndürme
    logger.warn(`TestRail configuration not found for teamId: ${teamId}, companyId: ${companyId}, userId: ${userId}`);
    
    return {
      success: false,
      message: "TestRail plugin not configured for this user",
      data: null,
      config: null,
      plugin: null
    };
  } catch (error: any) {
    logger.error(`Error retrieving TestRail configuration: ${error.message}`);
    return {
      success: false,
      message: `Error retrieving TestRail configuration: ${error.message}`,
      data: null,
      config: null,
      plugin: null
    };
  }
}

/**
 * Get TestRail test cases for a specific suite with pagination support
 * @param url TestRail URL
 * @param username TestRail username
 * @param apiKey TestRail API key
 * @param suiteId Test suite ID
 * @param projectId Project ID
 * @returns Test cases from the specified suite
 */
export async function getTestRailCases(
  url: string,
  username: string,
  apiKey: string,
  suiteId: number,
  projectId: number
) {
  try {


    if (!url || !username || !apiKey) {
      logger.error('Missing required TestRail credentials');
      return {
        success: false,
        message: "Missing required TestRail credentials (url, username, apiKey)",
        cases: []
      };
    }

    if (!suiteId) {
      logger.error('Suite ID is required for getTestRailCases');
      return {
        success: false,
        message: "Suite ID is required",
        cases: []
      };
    }

    if (!projectId) {
      logger.error('Project ID is required for getTestRailCases');
      return {
        success: false,
        message: "Project ID is required",
        cases: []
      };
    }

    // Normalize URL (remove trailing slash if present)
    const baseUrl = url.endsWith('/') ? url.slice(0, -1) : url;

    // TestRail URL'yi düzgün şekilde oluştur
    // Önce projectId ile endpoint
    const apiUrl = `${baseUrl}/index.php?/api/v2/get_cases/${projectId}`;



    // Pagination için değişkenler
    const limit = 250; // TestRail API'nin maksimum limit değeri
    let offset = 0;
    let allCases: any[] = [];
    let hasMoreCases = true;

    // Tüm sayfaları getirmek için döngü
    while (hasMoreCases) {


      // Axios ile API çağrısı - query parametrelerini ayrı obje olarak belirt
      const response = await axios.get(apiUrl, {
        auth: {
          username: username,
          password: apiKey
        },
        params: {
          suite_id: suiteId,
          limit: limit,
          offset: offset
        },
        headers: {
          'Content-Type': 'application/json'
        }
      });



      // Response verisi kontrolü
      if (!response.data) {
        break;
      }

      // Yanıt içindeki case'leri çıkar
      let casesArray: any[] = [];

      if (Array.isArray(response.data)) {
        casesArray = response.data;
      }
      else if (typeof response.data === 'object' && response.data !== null) {
        // 'cases' alanı varsa kontrol et
        if (Array.isArray(response.data.cases)) {
          casesArray = response.data.cases;
        } else {
          // Eğer 'cases' alanı yoksa veya dizi değilse, direkt nesneyi kullanmaya çalışalım
          if (response.data.id && response.data.title) {
            casesArray = [response.data];
          } else {
            break;
          }
        }
      } else {
        break;
      }

      // Alınan case'leri toplam listeye ekle
      allCases = [...allCases, ...casesArray];

      // Eğer dönen case sayısı limit değerinden azsa, tüm case'leri almışız demektir
      if (casesArray.length < limit) {
        hasMoreCases = false;
      } else {
        // Sonraki sayfa için offset'i güncelle
        offset += limit;
      }
    }



    // Process cases to extract relevant information and add suite_id and project_id
    const cases = allCases.map((c: any) => ({
      id: c.id,
      title: c.title,
      section_id: c.section_id,
      priority_id: c.priority_id,
      estimate: c.estimate,
      estimate_forecast: c.estimate_forecast,
      refs: c.refs,
      custom_steps: c.custom_steps,
      custom_expected: c.custom_expected,
      type_id: c.type_id,
      milestone_id: c.milestone_id,
      created_by: c.created_by,
      created_on: c.created_on,
      suite_id: suiteId,  // Add suite_id to each test case
      project_id: projectId  // Add project_id to each test case
    }));



    return {
      success: true,
      message: `Successfully fetched ${cases.length} test cases from suite ${suiteId}`,
      cases: cases
    };
  } catch (error: any) {
    logger.error(`Error fetching TestRail cases: ${error.message}`);

    // Hata detaylarını loglayalım
    if (error.response) {
      logger.error(`TestRail API error response status: ${error.response.status}`);
      logger.error(`TestRail API error response data: ${JSON.stringify(error.response.data)}`);
      logger.error(`TestRail API error response headers: ${JSON.stringify(error.response.headers)}`);
    } else if (error.request) {
      logger.error('TestRail API error: No response received from server');
      logger.error(`Request details: ${JSON.stringify(error.request)}`);
    } else {
      logger.error('TestRail API error occurred before making the request');
      logger.error(`Error details: ${JSON.stringify(error)}`);
    }

    return {
      success: false,
      message: `Error fetching TestRail cases: ${error.message}`,
      cases: []
    };
  }
}

/**
 * Get TestRail test cases for multiple suites
 * @param url TestRail URL
 * @param username TestRail username
 * @param apiKey TestRail API key
 * @param projectId Project ID
 * @param suiteIds Array of Test suite IDs
 * @returns Test cases from the specified suites
 */
export async function getTestRailCasesMultipleSuites(
  url: string,
  username: string,
  apiKey: string,
  projectId: number,
  suiteIds: number[]
) {
  try {
    // Parametre loglaması
    logger.info(`getTestRailCasesMultipleSuites called with url: ${url}, username: ${username}, apiKey: *****, projectId: ${projectId}, suiteIds: ${JSON.stringify(suiteIds)}`);

    if (!url || !username || !apiKey) {
      logger.error('Missing required TestRail credentials for getTestRailCasesMultipleSuites');
      return {
        success: false,
        message: "Missing required TestRail credentials (url, username, apiKey)",
        cases: [],
        casesBySuite: {}
      };
    }

    if (!projectId) {
      logger.error('Project ID is required for getTestRailCasesMultipleSuites');
      return {
        success: false,
        message: "Project ID is required",
        cases: [],
        casesBySuite: {}
      };
    }

    if (!suiteIds || !Array.isArray(suiteIds) || suiteIds.length === 0) {
      logger.error('At least one suite ID is required for getTestRailCasesMultipleSuites');
      return {
        success: false,
        message: "At least one suite ID is required",
        cases: [],
        casesBySuite: {}
      };
    }

    logger.info(`Fetching TestRail cases for project ${projectId}, suites: ${suiteIds.join(', ')}`);

    // Fetch cases for each suite in parallel
    logger.info(`Starting parallel requests for ${suiteIds.length} suites`);
    const suitePromises = suiteIds.map(suiteId => getTestRailCases(url, username, apiKey, suiteId, projectId));
    const results = await Promise.all(suitePromises);
    logger.info(`Completed parallel requests for all suites`);

    // Collect all cases
    let allCases: any[] = [];
    const casesBySuite: Record<string, any[]> = {};
    const successfulSuites: number[] = [];
    const failedSuites: number[] = [];

    results.forEach((result, index) => {
      const suiteId = suiteIds[index];

      if (result.success && result.cases.length > 0) {
        successfulSuites.push(suiteId);
        // Add suite ID to each case for reference
        const casesWithSuiteId = result.cases.map((c: any) => ({
          ...c,
          suite_id: suiteId,
          project_id: projectId
        }));

        allCases = [...allCases, ...casesWithSuiteId];
        casesBySuite[suiteId.toString()] = casesWithSuiteId;
        logger.info(`Added ${casesWithSuiteId.length} cases from suite ${suiteId}`);
      } else if (!result.success) {
        failedSuites.push(suiteId);
        logger.warn(`Failed to fetch cases for suite ${suiteId}: ${result.message}`);
      } else {
        logger.info(`Suite ${suiteId} returned 0 test cases`);
      }
    });

    // Özet loglama
    logger.info(`Fetching summary: Successful suites: ${successfulSuites.length}, Failed suites: ${failedSuites.length}`);
    logger.info(`Total cases retrieved: ${allCases.length}`);

    // Detaylı suite bazlı loglama
    Object.entries(casesBySuite).forEach(([suiteId, cases]) => {

    });

    if (failedSuites.length > 0) {
      logger.warn(`Failed suite IDs: ${failedSuites.join(', ')}`);
    }

    return {
      success: true,
      message: `Successfully fetched ${allCases.length} test cases from ${Object.keys(casesBySuite).length} suites`,
      cases: allCases,
      casesBySuite: casesBySuite
    };
  } catch (error: any) {
    logger.error(`Error fetching TestRail cases from multiple suites: ${error.message}`);

    if (error.stack) {
      logger.error(`Stack trace: ${error.stack}`);
    }

    return {
      success: false,
      message: `Error fetching TestRail cases: ${error.message}`,
      cases: [],
      casesBySuite: {}
    };
  }
}

/**
 * Get detailed steps for a specific TestRail case
 * @param url TestRail URL
 * @param username TestRail username
 * @param apiKey TestRail API key
 * @param caseId Test case ID
 * @returns Test case details including steps
 */
export async function getTestRailCaseSteps(
  url: string,
  username: string,
  apiKey: string,
  caseId: number | string
) {
  try {
    // Parametre loglaması
    logger.info(`getTestRailCaseSteps called with url: ${url}, username: ${username}, apiKey: *****, caseId: ${caseId}`);

    if (!url || !username || !apiKey) {
      logger.error('Missing required TestRail credentials');
      return {
        success: false,
        message: "Missing required TestRail credentials (url, username, apiKey)",
        case: null
      };
    }

    if (!caseId) {
      logger.error('Case ID is required for getTestRailCaseSteps');
      return {
        success: false,
        message: "Case ID is required",
        case: null
      };
    }

    // Normalize URL (remove trailing slash if present)
    const baseUrl = url.endsWith('/') ? url.slice(0, -1) : url;

    // TestRail URL'yi düzgün şekilde oluştur
    const apiUrl = `${baseUrl}/index.php?/api/v2/get_case/${caseId}`;

    // API isteği detayları
    logger.info(`TestRail API Request Details for case ${caseId}:`);
    logger.info(`- URL: ${apiUrl}`);
    logger.info(`- Method: GET`);
    logger.info(`- Auth Username: ${username}`);
    logger.info(`- Case ID: ${caseId}`);

    // Axios ile API çağrısı
    logger.info(`Sending API request to TestRail for case ${caseId}...`);
    const response = await axios.get(apiUrl, {
      auth: {
        username: username,
        password: apiKey
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });

    logger.info(`TestRail API response received for case ${caseId}. Status: ${response.status}`);

    // Response verisi kontrolü
    if (!response.data) {
      logger.warn(`TestRail API returned empty response for case ${caseId}`);
      return {
        success: false,
        message: "TestRail API returned empty response",
        case: null
      };
    }

    // API yanıt formatını loglayalım
    logger.info(`TestRail API response type: ${typeof response.data}`);

    // TestRail API'den dönen case verilerini zenginleştirme
    const caseDetails = response.data;

    // Response verisi formatını loglayalım
    logger.info(`Case details contains custom_steps: ${caseDetails.custom_steps ? 'Yes' : 'No'}`);
    logger.info(`Case details contains custom_steps_separated: ${caseDetails.custom_steps_separated ? 'Yes' : 'No'}`);

    // Eğer custom_steps_separated varsa (ayrı adım ve beklentiler içeren dizi formatı)
    if (caseDetails.custom_steps_separated && Array.isArray(caseDetails.custom_steps_separated)) {
      try {
        logger.info(`Processing custom_steps_separated with ${caseDetails.custom_steps_separated.length} steps`);

        // Adımları biçimlendirilmiş stringe dönüştürelim
        let formattedSteps = '';
        let formattedExpected = '';

        caseDetails.custom_steps_separated.forEach((step: any, index: number) => {
          // Adım indeksini ve içeriğini ekle
          formattedSteps += `Step ${index + 1}: ${step.content || ''}\n\n`;

          // Beklenen sonucu varsa ekle
          if (step.expected) {
            formattedExpected += `Step ${index + 1} Expected: ${step.expected || ''}\n\n`;
          }
        });

        // Biçimlendirilmiş adımları ve beklenen sonuçları ekleyelim
        caseDetails.custom_steps_formatted = formattedSteps.trim();
        caseDetails.custom_expected_formatted = formattedExpected.trim();

        // Orijinal adımları ve beklenen sonuçları da text olarak ekleyelim
        // Bu, API'den direkt dönen HTML yok ise frontend'in kullanabileceği bir alan olacak
        if (!caseDetails.custom_steps) {
          caseDetails.custom_steps = formattedSteps.trim();
        }

        if (!caseDetails.custom_expected) {
          caseDetails.custom_expected = formattedExpected.trim();
        }


      } catch (error) {
        logger.warn(`Error processing custom_steps_separated for case ${caseId}: ${error}`);
      }
    }
    // Custom_steps varsa (HTML içerikli metin formatı)
    else if (caseDetails.custom_steps) {
      try {
        // HTML içeriğini temizlemeye çalış - basit bir temizleme
        caseDetails.custom_steps_formatted = caseDetails.custom_steps
          .replace(/<br\s*\/?>/gi, '\n')  // <br> etiketlerini satır sonlarına çevir
          .replace(/<\/p>/gi, '\n')       // </p> etiketlerini satır sonlarına çevir
          .replace(/<[^>]*>/g, '')        // Diğer tüm HTML etiketlerini kaldır
          .replace(/&nbsp;/g, ' ')        // HTML special karakterleri değiştir
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .trim();                        // Başlangıç ve sonundaki boşlukları temizle


      } catch (error) {
        logger.warn(`Could not format steps for case ${caseId}: ${error}`);
        caseDetails.custom_steps_formatted = caseDetails.custom_steps;
      }
    }
    else {
      logger.warn(`Case ${caseId} does not have steps in any recognized format`);
      // Adımlar yoksa boş bir string ekleyelim
      caseDetails.custom_steps_formatted = '';
    }

    // Beklenen sonuç formatlaması - eğer yukarıda custom_steps_separated'den işlemediyse
    if (caseDetails.custom_expected && !caseDetails.custom_expected_formatted) {
      try {
        // HTML içeriğini temizlemeye çalış - basit bir temizleme
        caseDetails.custom_expected_formatted = caseDetails.custom_expected
          .replace(/<br\s*\/?>/gi, '\n')  // <br> etiketlerini satır sonlarına çevir
          .replace(/<\/p>/gi, '\n')       // </p> etiketlerini satır sonlarına çevir
          .replace(/<[^>]*>/g, '')        // Diğer tüm HTML etiketlerini kaldır
          .replace(/&nbsp;/g, ' ')        // HTML special karakterleri değiştir
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .trim();                        // Başlangıç ve sonundaki boşlukları temizle


      } catch (error) {
        logger.warn(`Could not format expected results for case ${caseId}: ${error}`);
        caseDetails.custom_expected_formatted = caseDetails.custom_expected;
      }
    }
    else if (!caseDetails.custom_expected_formatted) {
      // Beklenen sonuç yoksa boş bir string ekleyelim
      caseDetails.custom_expected_formatted = '';
    }



    return {
      success: true,
      message: `Successfully fetched details for case ${caseId}`,
      case: caseDetails
    };
  } catch (error: any) {
    logger.error(`Error fetching TestRail case details: ${error.message}`);

    // Hata detaylarını loglayalım
    if (error.response) {
      logger.error(`TestRail API error response status: ${error.response.status}`);
      logger.error(`TestRail API error response data: ${JSON.stringify(error.response.data)}`);
      logger.error(`TestRail API error response headers: ${JSON.stringify(error.response.headers)}`);
    } else if (error.request) {
      logger.error('TestRail API error: No response received from server');
      logger.error(`Request details: ${JSON.stringify(error.request)}`);
    } else {
      logger.error('TestRail API error occurred before making the request');
      logger.error(`Error details: ${JSON.stringify(error)}`);
    }

    return {
      success: false,
      message: `Error fetching TestRail case details: ${error.message}`,
      case: null
    };
  }
}