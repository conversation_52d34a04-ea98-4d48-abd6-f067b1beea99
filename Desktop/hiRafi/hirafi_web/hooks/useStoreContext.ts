/**
 * Store Context Management Hook
 * Ensures stores are properly managed when user/company context changes
 */

import { useEffect, useRef } from "react";
import { useAuth } from "@/lib/api/auth";
import { storeManager } from "@/lib/utils/store-management";
import { clearCurrentCompanyData } from "@/lib/utils/company-aware-storage";

/**
 * Hook to manage store context automatically
 * Should be called at app root level or in layout
 */
export function useStoreContext() {
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      // Update store manager with current user context
      storeManager.setUserContext({
        id: user.id,
        companyId: user.companyId,
        teamId: user.teamId,
      });
    }
  }, [user?.id, user?.companyId, user?.teamId]);

  // Clear stores on unmount (logout)
  useEffect(() => {
    return () => {
      if (!user) {
        storeManager.clearAllStores();
      }
    };
  }, [user]);
}

/**
 * Manual function to clear all data for company switch
 * Can be called from company selection UI
 */
export function clearForCompanySwitch() {
  storeManager.clearAllStores();
  storeManager.forceCleanLocalStorage();
  clearCurrentCompanyData();

  console.log("[StoreContext] Cleared all data for company switch");
}

/**
 * Hook specifically for scenario-related components
 * Automatically clears scenario stores when company changes
 */
export function useScenarioStoreContext() {
  const { user } = useAuth();
  const prevCompanyIdRef = useRef<string | undefined>(undefined);

  useEffect(() => {
    if (user?.companyId) {
      const prevCompanyId = prevCompanyIdRef.current;

      // Only set context if this is the initial load or if there's no previous company
      // or if we're switching between different companies
      if (!prevCompanyId) {
        // Initial load - set context but don't clear stores
        storeManager.setUserContext({
          id: user.id,
          companyId: user.companyId,
          teamId: user.teamId,
        });
        console.log(
          "[useScenarioStoreContext] Initial context set, preserving drafts",
        );
      } else if (prevCompanyId !== user.companyId) {
        // Actual company change - clear stores
        console.log(
          "[useScenarioStoreContext] Company changed, clearing stores",
          {
            from: prevCompanyId,
            to: user.companyId,
          },
        );
        storeManager.setUserContext({
          id: user.id,
          companyId: user.companyId,
          teamId: user.teamId,
        });
      }

      // Update the ref
      prevCompanyIdRef.current = user.companyId;
    }
  }, [user?.id, user?.companyId, user?.teamId]);
}
