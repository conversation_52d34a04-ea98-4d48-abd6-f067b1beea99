/**
 * Shared Patterns and Utilities for Hooks
 *
 * Common patterns, helpers, and utilities used across multiple hooks
 */

import { useCallback, useRef, useState, useEffect } from 'react'
import { toast } from '@/lib/utils/toast-utils'
import type {
  ApiResponse,
  OperationResult,
  LoadingState,
  QueryParams,
  ValidationState
} from './types'

/**
 * Creates a stable SWR key from query parameters
 */
export function createSWRKey(baseKey: string, params?: QueryParams): string | null {
  if (!params) return baseKey

  // Remove undefined values and sort keys for consistency
  const cleanParams = Object.fromEntries(
    Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .sort(([a], [b]) => a.localeCompare(b))
  )

  if (Object.keys(cleanParams).length === 0) return baseKey

  return `${baseKey}-${JSON.stringify(cleanParams)}`
}

/**
 * Standard error handler for hooks
 */
export function handleHookError(error: any, operation: string): string {
  const message = error?.message || error?.error || `Failed to ${operation}`
  console.error(`Hook error [${operation}]:`, error)
  return message
}

/**
 * Standard success handler for hooks
 */
export function handleHookSuccess<T>(
  response: ApiResponse<T>,
  operation: string,
  showToast = true
): OperationResult<T> {
  if (response.success) {
    if (showToast && response.message) {
      toast.success(response.message)
    }
    return {
      success: true,
      data: response.data,
      message: response.message
    }
  } else {
    const error = response.error || `Failed to ${operation}`
    if (showToast) {
      toast.error(error)
    }
    return {
      success: false,
      error
    }
  }
}

/**
 * Loading state manager for multiple operations
 */
export function useOperationStates(initialOperations: string[] = []) {
  const [operations, setOperations] = useState<Set<string>>(new Set())

  const setOperationLoading = useCallback((operation: string, loading: boolean) => {
    setOperations(prev => {
      const newSet = new Set(prev)
      if (loading) {
        newSet.add(operation)
      } else {
        newSet.delete(operation)
      }
      return newSet
    })
  }, [])

  const isOperationLoading = useCallback((operation: string) => {
    return operations.has(operation)
  }, [operations])

  const clearAllOperations = useCallback(() => {
    setOperations(new Set())
  }, [])

  return {
    operations,
    setOperationLoading,
    isOperationLoading,
    clearAllOperations,
    hasActiveOperations: operations.size > 0
  }
}

/**
 * Enhanced debounce hook with cancel functionality
 */
export function useEnhancedDebounce<T>(
  value: T,
  delay: number,
  options: {
    immediate?: boolean
    maxWait?: number
  } = {}
) {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  const timeoutRef = useRef<NodeJS.Timeout>()
  const maxTimeoutRef = useRef<NodeJS.Timeout>()
  const lastCallTimeRef = useRef<number>()

  const { immediate = false, maxWait } = options

  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    if (maxTimeoutRef.current) {
      clearTimeout(maxTimeoutRef.current)
    }
  }, [])

  const flush = useCallback(() => {
    cancel()
    setDebouncedValue(value)
  }, [cancel, value])

  useEffect(() => {
    // If immediate and first call, update immediately
    if (immediate && lastCallTimeRef.current === undefined) {
      setDebouncedValue(value)
      lastCallTimeRef.current = Date.now()
      return
    }

    lastCallTimeRef.current = Date.now()

    // Set up debounced update
    timeoutRef.current = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    // Set up max wait timeout if specified
    if (maxWait && !maxTimeoutRef.current) {
      maxTimeoutRef.current = setTimeout(() => {
        setDebouncedValue(value)
        cancel()
      }, maxWait)
    }

    return cancel
  }, [value, delay, immediate, maxWait, cancel])

  return [debouncedValue, { cancel, flush }] as const
}

/**
 * Optimistic update manager
 */
export function useOptimisticUpdates<T>() {
  const [optimisticUpdates, setOptimisticUpdates] = useState<Map<string, T>>(new Map())

  const addOptimisticUpdate = useCallback((key: string, data: T) => {
    setOptimisticUpdates(prev => new Map(prev).set(key, data))
  }, [])

  const removeOptimisticUpdate = useCallback((key: string) => {
    setOptimisticUpdates(prev => {
      const newMap = new Map(prev)
      newMap.delete(key)
      return newMap
    })
  }, [])

  const clearOptimisticUpdates = useCallback(() => {
    setOptimisticUpdates(new Map())
  }, [])

  const applyOptimisticUpdates = useCallback((baseData: T[], keyExtractor: (item: T) => string): T[] => {
    if (optimisticUpdates.size === 0) return baseData

    return baseData.map(item => {
      const key = keyExtractor(item)
      const optimisticUpdate = optimisticUpdates.get(key)
      return optimisticUpdate ? { ...item, ...optimisticUpdate } : item
    })
  }, [optimisticUpdates])

  return {
    optimisticUpdates,
    addOptimisticUpdate,
    removeOptimisticUpdate,
    clearOptimisticUpdates,
    applyOptimisticUpdates,
    hasOptimisticUpdates: optimisticUpdates.size > 0
  }
}

/**
 * Cache invalidation helper
 */
export function useCacheInvalidation() {
  const invalidationCallbacks = useRef<Map<string, (() => void)[]>>(new Map())

  const registerInvalidation = useCallback((key: string, callback: () => void) => {
    const callbacks = invalidationCallbacks.current.get(key) || []
    callbacks.push(callback)
    invalidationCallbacks.current.set(key, callbacks)

    // Return cleanup function
    return () => {
      const currentCallbacks = invalidationCallbacks.current.get(key) || []
      const filteredCallbacks = currentCallbacks.filter(cb => cb !== callback)
      if (filteredCallbacks.length === 0) {
        invalidationCallbacks.current.delete(key)
      } else {
        invalidationCallbacks.current.set(key, filteredCallbacks)
      }
    }
  }, [])

  const invalidateCache = useCallback((key: string) => {
    const callbacks = invalidationCallbacks.current.get(key) || []
    callbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.warn(`Cache invalidation callback failed for key "${key}":`, error)
      }
    })
  }, [])

  const invalidateMultiple = useCallback((keys: string[]) => {
    keys.forEach(invalidateCache)
  }, [invalidateCache])

  const invalidateAll = useCallback(() => {
    Array.from(invalidationCallbacks.current.keys()).forEach(invalidateCache)
  }, [invalidateCache])

  return {
    registerInvalidation,
    invalidateCache,
    invalidateMultiple,
    invalidateAll
  }
}

/**
 * Form validation utilities
 */
export function useFormValidation<T extends Record<string, any>>(
  initialData: T,
  validators: Partial<Record<keyof T, (value: any, formData: T) => string | undefined>>
) {
  const [validation, setValidation] = useState<ValidationState>({
    isValid: true,
    errors: {},
    touched: {}
  })

  const validateField = useCallback((field: keyof T, value: any, formData: T) => {
    const validator = validators[field]
    if (!validator) return undefined

    return validator(value, formData)
  }, [validators])

  const validateForm = useCallback((formData: T) => {
    const errors: Record<string, string | undefined> = {}
    let isValid = true

    Object.keys(validators).forEach(field => {
      const error = validateField(field, formData[field], formData)
      if (error) {
        errors[field] = error
        isValid = false
      }
    })

    const validationState: ValidationState = {
      isValid,
      errors,
      touched: validation.touched
    }

    setValidation(validationState)
    return validationState
  }, [validators, validateField, validation.touched])

  const touchField = useCallback((field: keyof T) => {
    setValidation(prev => ({
      ...prev,
      touched: { ...prev.touched, [field]: true }
    }))
  }, [])

  const resetValidation = useCallback(() => {
    setValidation({
      isValid: true,
      errors: {},
      touched: {}
    })
  }, [])

  return {
    validation,
    validateField,
    validateForm,
    touchField,
    resetValidation,
    setValidation
  }
}

/**
 * Data transformer utilities
 */
export const dataTransformers = {
  /**
   * Transform API response to internal format
   */
  transformApiResponse<T, R>(
    response: ApiResponse<T>,
    transformer: (data: T) => R
  ): ApiResponse<R> {
    if (!response.success || !response.data) {
      return response as ApiResponse<R>
    }

    try {
      return {
        ...response,
        data: transformer(response.data)
      }
    } catch (error) {
      return {
        success: false,
        error: `Data transformation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  },

  /**
   * Normalize array data by ID
   */
  normalizeById<T extends { id: string }>(items: T[]): Record<string, T> {
    return items.reduce((acc, item) => {
      acc[item.id] = item
      return acc
    }, {} as Record<string, T>)
  },

  /**
   * Denormalize data by IDs
   */
  denormalizeByIds<T>(normalized: Record<string, T>, ids: string[]): T[] {
    return ids.map(id => normalized[id]).filter(Boolean)
  },

  /**
   * Transform form data for API submission
   */
  transformForSubmission<T extends Record<string, any>>(
    formData: T,
    fieldMap?: Partial<Record<keyof T, string>>
  ): Record<string, any> {
    const transformed: Record<string, any> = {}

    Object.entries(formData).forEach(([key, value]) => {
      const mappedKey = fieldMap?.[key as keyof T] || key

      // Skip undefined and empty string values
      if (value !== undefined && value !== '') {
        transformed[mappedKey] = value
      }
    })

    return transformed
  }
}

/**
 * Retry mechanism for operations
 */
export function useRetry() {
  const [retryCount, setRetryCount] = useState(0)
  const [isRetrying, setIsRetrying] = useState(false)

  const retry = useCallback(async <T>(
    operation: () => Promise<T>,
    options: {
      maxRetries?: number
      delay?: number
      backoff?: boolean
    } = {}
  ): Promise<T> => {
    const { maxRetries = 3, delay = 1000, backoff = true } = options

    let attempts = 0
    let lastError: any

    while (attempts <= maxRetries) {
      try {
        setIsRetrying(attempts > 0)
        const result = await operation()
        setRetryCount(attempts)
        setIsRetrying(false)
        return result
      } catch (error) {
        lastError = error
        attempts++

        if (attempts <= maxRetries) {
          const retryDelay = backoff ? delay * Math.pow(2, attempts - 1) : delay
          await new Promise(resolve => setTimeout(resolve, retryDelay))
        }
      }
    }

    setRetryCount(attempts)
    setIsRetrying(false)
    throw lastError
  }, [])

  const resetRetry = useCallback(() => {
    setRetryCount(0)
    setIsRetrying(false)
  }, [])

  return {
    retry,
    retryCount,
    isRetrying,
    resetRetry
  }
}

/**
 * Local storage hook with SSR safety
 */
export function useLocalStorage<T>(key: string, defaultValue: T) {
  const [value, setValue] = useState<T>(defaultValue)
  const [isLoaded, setIsLoaded] = useState(false)

  // Load from localStorage on mount (client-side only)
  useEffect(() => {
    try {
      const stored = localStorage.getItem(key)
      if (stored !== null) {
        setValue(JSON.parse(stored))
      }
    } catch (error) {
      console.warn(`Failed to load from localStorage key "${key}":`, error)
    } finally {
      setIsLoaded(true)
    }
  }, [key])

  // Save to localStorage when value changes
  useEffect(() => {
    if (!isLoaded) return

    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.warn(`Failed to save to localStorage key "${key}":`, error)
    }
  }, [key, value, isLoaded])

  return [value, setValue, isLoaded] as const
}
