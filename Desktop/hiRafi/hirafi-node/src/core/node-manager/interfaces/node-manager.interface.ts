/**
 * Node Manager Interface
 * 
 * Main interface for the node manager that coordinates all node operations.
 * This replaces the singleton NodeManager with a proper interface-based design.
 */

import { EventEmitter } from 'events';

/**
 * Node Manager Interface
 * 
 * Defines the contract for the main node manager that coordinates
 * test node operations and communication with the test hub.
 */
export interface INodeManager extends EventEmitter {
  /**
   * Start the node manager
   * @returns Promise resolving to true if started successfully
   */
  start(): Promise<boolean>;

  /**
   * Stop the node manager
   * @returns Promise that resolves when stopped
   */
  stop(): Promise<void>;

  /**
   * Stop a running test
   * @param testId Test ID to stop
   * @returns Promise resolving to true if stopped successfully
   */
  stopTest(testId: string): Promise<boolean>;

  /**
   * Get current node status
   * @returns Current status string
   */
  getStatus(): string;

  /**
   * Get node ID
   * @returns Node ID or null if not assigned
   */
  getNodeId(): string | null;

  /**
   * Check if node is running a test
   * @returns True if running a test
   */
  isRunningTest(): boolean;

  /**
   * Get test statistics
   * @returns Test execution statistics
   */
  getTestStatistics(): {
    executed: number;
    succeeded: number;
    failed: number;
    uptime: number;
  };

  /**
   * Get the test execution manager instance
   * @returns Test execution manager or null
   */
  getTestExecutionManager(): any | null;
}
