import { useState, useEffect, useCallback } from "react"
import { useToast } from "@/components/ui/use-toast"
import { reportApi } from "@/lib/api"
import { TestResultCounts } from "@/lib/utils/run-utils"

// Run Report türü tanımlaması
export interface RunReport {
  id: string
  name: string
  status: 'created' | 'queued' | 'running' | 'completed' | 'failed' | 'stopped' | 'partial'
  createdAt: string
  startedAt?: string
  completedAt?: string
  // Report-specific timestamps that may differ from run timestamps
  reportStartedAt?: string
  reportCompletedAt?: string
  // Report status that may differ from run status
  reportStatus?: string
  // Duration in milliseconds (may be provided directly by the API)
  duration?: number
  userId: string
  executedUser?: string
  executedUserName?: string
  executionId?: string
  runId: string
  runName?: string
  // testResults is now optional and will be calculated from scenarioStatuses
  testResults?: TestResultCounts
  // Added scenarioStatuses field
  scenarioStatuses?: Array<{
    scenarioId: string
    testId?: string
    status: 'queued' | 'running' | 'passed' | 'failed' | 'stopped'
    startedAt?: string
    completedAt?: string
  }>
  scenarioIds?: string[]
  testrailRunId?: number
  testrailRunLink?: string
}

// Senaryo türü tanımlaması
export interface TestScenario {
  id: string
  title: string
  status: string
  createdAt: string
  executionId: string
  runId: string
  scenarioId: string
  result?: any
  duration?: number
  steps?: Array<{
    title: string
    status: string
    duration?: number
    startTime?: string
    endTime?: string
  }>
  enhancedMetrics?: {
    pageMetrics?: any
    networkStatistics?: any
    totalViolations?: any
  }
}

// Hook sonucu için interface
export interface RunReportsHookResult {
  reports: RunReport[]
  setReports: React.Dispatch<React.SetStateAction<RunReport[]>>
  totalCount: number
  isLoading: boolean
  error: string | null
  fetchReports: (searchQuery?: string) => Promise<void>
  fetchScenariosByExecutionId: (executionId: string) => Promise<{
    success: boolean;
    scenarios?: TestScenario[];
    error?: string;
  }>
  deleteReport: (reportId: string) => Promise<{
    success: boolean;
    error?: string;
  }>
  deleteMultipleReports: (reportIds: string[]) => Promise<{
    success: boolean;
    deletedCount?: number;
    error?: string;
  }>
}

// Hook için opsiyonlar
export interface UseRunReportsOptions {
  autoFetch?: boolean;
}

export function useRunReports(options: UseRunReportsOptions = { autoFetch: true }): RunReportsHookResult {
  const [reports, setReports] = useState<RunReport[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Kullanıcıya ait tüm run reportları getir
  const fetchReports = useCallback(async (searchQuery?: string) => {
    setError(null)
    setIsLoading(true)

    try {
      // Yeni API yapısını kullan
      const params: any = { limit: 100 }

      if (searchQuery) {
        params.search = searchQuery
      }

      const response = await reportApi.getRunReports(params)

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch run reports')
      }

      // Yanıtın doğru formatta olduğunu kontrol et
      const reports = response.reports || response.data?.reports;
      if (!reports || !Array.isArray(reports)) {
        console.error('Invalid API response format: missing or invalid reports array', response.data)
        throw new Error('Invalid response format from server')
      }

      // Report verilerini formatlayarak hazırla
      const formattedReports = reports.map((report: any) => {
        if (!report) return null; // Boş öğeleri atla

        return {
          ...report,
          // Gerekli alanlar için varsayılan değerler atama
          id: report.id || `unknown-${Date.now()}`,
          // Önce runName alanını kontrol et, yoksa name alanını kullan, o da yoksa 'Unnamed Run' kullan
          name: report.runName || report.name || 'Unnamed Run',
          status: report.status || 'completed',
          // Tarihleri string olarak standardize et
          createdAt: report.createdAt ? new Date(report.createdAt).toISOString() : new Date().toISOString(),
          startedAt: report.startedAt ? new Date(report.startedAt).toISOString() : undefined,
          completedAt: report.completedAt ? new Date(report.completedAt).toISOString() : undefined,
          // Include report-specific fields if available
          reportStartedAt: report.reportStartedAt ? new Date(report.reportStartedAt).toISOString() : undefined,
          reportCompletedAt: report.reportCompletedAt ? new Date(report.reportCompletedAt).toISOString() : undefined,
          reportStatus: report.reportStatus || report.status || 'completed',
          // Ensure executedUserName is properly set
          executedUserName: report.executedUserName || (report.executedUser ? report.executedUser : 'System'),
        }
      }).filter(Boolean) // Boş öğeleri filtrele

      setReports(formattedReports)
      setTotalCount(response.total || response.data?.total || formattedReports.length)
    } catch (err: any) {
      console.error('Error fetching run reports:', err)
      setError(err.message || 'Failed to fetch run reports')
      toast({
        title: "Error",
        description: err.message || "Failed to fetch run reports",
        variant: "destructive"
      })
      // Hata durumunda boş dizi ata
      setReports([])
      setTotalCount(0)
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  // Tek bir raporu sil
  const deleteReport = useCallback(async (reportId: string) => {
    try {
      // Silinecek raporu bul
      const reportToDelete = reports.find(report => report.id === reportId);

      if (!reportToDelete) {
        throw new Error(`Report with ID ${reportId} not found`);
      }

      // API isteği için gerekli verileri hazırla
      const requestData = {
        runId: reportToDelete.runId,
        executionId: reportToDelete.executionId
      };

      // API'ye silme isteği gönder
      const response = await reportApi.deleteRunReport(reportId, requestData);

      if (!response.success) {
        throw new Error(response.error || `Failed to delete report with ID ${reportId}`);
      }

      // UI'daki rapor listesini güncelle
      setReports(prevReports => prevReports.filter(report => report.id !== reportId));
      setTotalCount(prev => prev - 1);

      // Başarılı bildirim göster
      toast({
        title: "Success",
        description: "Report deleted successfully",
        variant: "default"
      });

      return {
        success: true
      };
    } catch (err: any) {
      console.error(`Error deleting report ${reportId}:`, err);

      // Hata bildirimini göster
      toast({
        title: "Error",
        description: err.message || `Failed to delete report`,
        variant: "destructive"
      });

      return {
        success: false,
        error: err.message
      };
    }
  }, [reports, toast]);

  // Birden fazla raporu toplu sil - asenkron olarak çalışır
  const deleteMultipleReports = useCallback(async (reportIds: string[]) => {
    try {
      if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
        throw new Error('At least one report ID is required');
      }

      // API'ye toplu silme isteği gönder
      const response = await reportApi.deleteMultipleRunReports(reportIds);

      if (!response.success) {
        throw new Error(response.error || 'Failed to delete reports');
      }

      // UI'daki rapor listesini hemen güncelle - silinen raporları kaldır
      setReports(prevReports => prevReports.filter(report => !reportIds.includes(report.id)));
      setTotalCount(prev => prev - reportIds.length);

      // Başarılı bildirim göster
      toast({
        title: "Success",
        description: `Silme işlemi başlatıldı: ${reportIds.length} rapor`,
        variant: "default"
      });

      // Kısa bir süre sonra raporları yeniden yükle (silme işlemi arka planda devam ederken)
      setTimeout(() => {
        fetchReports();
      }, 2000);

      return {
        success: true,
        deletedCount: reportIds.length,
        status: 'processing'
      };
    } catch (err: any) {
      console.error('Error deleting multiple reports:', err);

      // Hata bildirimini göster
      toast({
        title: "Error",
        description: err.message || 'Failed to delete reports',
        variant: "destructive"
      });

      return {
        success: false,
        error: err.message
      };
    }
  }, [toast, fetchReports]);

  // Belirli bir executionId'ye ait senaryoları getir
  const fetchScenariosByExecutionId = useCallback(async (executionId: string) => {
    try {
      if (!executionId) {
        return {
          success: false,
          error: 'Execution ID is required'
        }
      }

      const response = await reportApi.getScenariosByExecutionId(executionId)

      if (!response.success) {
        return {
          success: false,
          error: response.error || `Failed to fetch scenarios for execution ID ${executionId}`
        }
      }

      const scenarios = response.scenarios || response.data?.scenarios;
      if (!scenarios || !Array.isArray(scenarios)) {
        console.error('Invalid API response format: missing or invalid scenarios array', response.data)
        return {
          success: false,
          error: 'Invalid response format from server'
        }
      }

      return {
        success: true,
        scenarios: scenarios
      }
    } catch (err: any) {
      return {
        success: false,
        error: err.message || `Failed to fetch scenarios for execution ID ${executionId}`
      }
    }
  }, [])

  // Component mount olduğunda ve autoFetch true ise raporları getir
  useEffect(() => {
    if (options.autoFetch) {
      fetchReports()
    }
  }, [fetchReports, options.autoFetch])

  return {
    reports,
    setReports,
    totalCount,
    isLoading,
    error,
    fetchReports,
    fetchScenariosByExecutionId,
    deleteReport,
    deleteMultipleReports
  }
}