"use client"

import { <PERSON>actNode, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/api/auth"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

interface CompanyOwnerRouteProps {
  children: ReactNode
}

export function CompanyOwnerRoute({ children }: CompanyOwnerRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth")
    } else if (!isLoading && isAuthenticated && user?.accountType !== 'company_owner') {
      // Kullanıcı company_owner değilse ana sayfaya yönlendir
      router.push("/")
    }
  }, [isLoading, isAuthenticated, user, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  if (user?.accountType !== 'company_owner') {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erişim Reddedildi</AlertTitle>
          <AlertDescription>
            Bu sayfaya erişmek için şirket sahibi (company owner) yetkisine sahip olmanız gerekmektedir.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return <>{children}</>
}
