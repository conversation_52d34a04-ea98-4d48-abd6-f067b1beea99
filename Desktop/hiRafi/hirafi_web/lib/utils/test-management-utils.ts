/**
 * Test Management Utilities
 * 
 * Utility functions for test management provider operations
 */

import { TestManagementProvider } from '@/store/testManagementStore'

/**
 * Maps test management providers to their corresponding form field names
 */
export function getProviderFormFieldName(provider: TestManagementProvider): string {
  switch (provider) {
    case 'testrail':
      return 'testrailCases'
    case 'zephyrscale':
      return 'zephyrscaleCases'
    default:
      return 'selectedTestCases'
  }
}

/**
 * Maps test management providers to their sync field names
 */
export function getProviderSyncFieldName(provider: TestManagementProvider): string {
  switch (provider) {
    case 'testrail':
      return 'testrailSync'
    case 'zephyrscale':
      return 'zephyrscaleSync'
    default:
      return 'testManagementSync'
  }
}

/**
 * Gets the display name for a test management provider
 */
export function getProviderDisplayName(provider: TestManagementProvider): string {
  switch (provider) {
    case 'testrail':
      return 'TestRail'
    case 'zephyrscale':
      return 'Zephyr Scale'
    default:
      return provider
  }
}

/**
 * Gets the configuration URL for a test management provider
 */
export function getProviderConfigUrl(provider: TestManagementProvider): string {
  switch (provider) {
    case 'testrail':
      return '/plugins/testrail'
    case 'zephyrscale':
      return '/plugins/zephyrscale'
    default:
      return '/plugins'
  }
}

/**
 * Updates form data with provider-specific test case selection
 */
export function updateFormDataWithProvider(
  updateFormField: (field: string, value: any) => void,
  provider: TestManagementProvider,
  caseIds: string[]
): void {
  const fieldName = getProviderFormFieldName(provider)
  updateFormField(fieldName, caseIds)
}

/**
 * Gets selected test cases for a specific provider from form data
 */
export function getSelectedCasesForProvider(
  formData: any,
  provider: TestManagementProvider
): string[] {
  const fieldName = getProviderFormFieldName(provider)
  return formData[fieldName] || []
}

/**
 * Determines the active provider based on which test cases are selected in form data
 */
export function determineActiveProviderFromFormData(formData: any): TestManagementProvider | null {
  if (formData.testrailCases?.length > 0) {
    return 'testrail'
  }
  if (formData.zephyrscaleCases?.length > 0) {
    return 'zephyrscale'
  }
  return null
}

/**
 * Clears all provider-specific test case selections from form data
 */
export function clearAllProviderSelections(
  updateFormField: (field: string, value: any) => void
): void {
  updateFormField('testrailCases', [])
  updateFormField('zephyrscaleCases', [])
  updateFormField('testrailSync', false)
  updateFormField('zephyrscaleSync', false)
}
