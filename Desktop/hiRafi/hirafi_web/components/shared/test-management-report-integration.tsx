"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ExternalLink, TestTube, Check } from 'lucide-react';

interface TestManagementReportIntegrationProps {
  report: {
    testrailRunLink?: string;
    zephyrscaleRunLink?: string;
    zephyrscaleTestCycleLink?: string;
    [key: string]: any;
  } | null;
  provider?: 'testrail' | 'zephyrscale';
}

export function TestManagementReportIntegration({
  report,
  provider
}: TestManagementReportIntegrationProps) {
  // Auto-detect provider if not specified
  const detectedProvider = provider ||
    (report?.testrailRunLink ? 'testrail' :
     (report?.zephyrscaleRunLink || report?.zephyrscaleTestCycleLink) ? 'zephyrscale' : null);

  const runLink = detectedProvider === 'testrail'
    ? report?.testrailRunLink
    : (report?.zephyrscaleTestCycleLink || report?.zephyrscaleRunLink);

  if (!report || !runLink || !detectedProvider) return null;

  const getProviderConfig = () => {
    switch (detectedProvider) {
      case 'testrail':
        return {
          name: 'TestRail',
          color: 'blue',
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-700',
          borderColor: 'border-blue-200',
          hoverBg: 'hover:bg-blue-100',
          hoverText: 'hover:text-blue-800',
          darkBg: 'dark:bg-blue-900/20',
          darkText: 'dark:text-blue-300',
          darkBorder: 'dark:border-blue-800',
          darkHover: 'dark:hover:bg-blue-900/40'
        };
      case 'zephyrscale':
        return {
          name: 'Zephyr Scale',
          color: 'green',
          bgColor: 'bg-green-50',
          textColor: 'text-green-700',
          borderColor: 'border-green-200',
          hoverBg: 'hover:bg-green-100',
          hoverText: 'hover:text-green-800',
          darkBg: 'dark:bg-green-900/20',
          darkText: 'dark:text-green-300',
          darkBorder: 'dark:border-green-800',
          darkHover: 'dark:hover:bg-green-900/40'
        };
      default:
        return {
          name: 'Test Management',
          color: 'gray',
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-700',
          borderColor: 'border-gray-200',
          hoverBg: 'hover:bg-gray-100',
          hoverText: 'hover:text-gray-800',
          darkBg: 'dark:bg-gray-900/20',
          darkText: 'dark:text-gray-300',
          darkBorder: 'dark:border-gray-800',
          darkHover: 'dark:hover:bg-gray-900/40'
        };
    }
  };

  const config = getProviderConfig();

  return (
    <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all shadow-sm hover:shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <TestTube className={`h-5 w-5 mr-2 text-${config.color}-500`} />
          {config.name} Integration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2 mb-3">
          <div className="bg-green-100 dark:bg-green-900/30 p-1 rounded-full">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
          </div>
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Test results synced with {config.name}
          </span>
        </div>
        
        <Button 
          variant="outline" 
          size="sm" 
          className={`w-full ${config.bgColor} ${config.textColor} ${config.borderColor} ${config.hoverBg} ${config.hoverText} ${config.darkBg} ${config.darkText} ${config.darkBorder} ${config.darkHover}`}
          onClick={() => window.open(runLink, '_blank')}
        >
          <ExternalLink className="h-4 w-4 mr-1" />
          View in {config.name}
        </Button>
      </CardContent>
    </Card>
  );
}

// Legacy component exports for backward compatibility
export function TestRailIntegration({ report }: { report: any }) {
  return <TestManagementReportIntegration report={report} provider="testrail" />;
}

export function RunReportTestRailIntegration({ report }: { report: any }) {
  return <TestManagementReportIntegration report={report} provider="testrail" />;
}
