/**
 * Fetch Wrapper
 *
 * Bu dosya, API isteklerini yönetmek için bir wrapper sağlar.
 * Tüm API istekleri bu wrapper üzerinden yapılmalıdır.
 */

import { toast } from "@/lib/utils/toast-utils";
import { API_BASE_URL } from "./constants";

// İstek tipleri
export type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// İstek seçenekleri
export interface RequestOptions extends Omit<RequestInit, 'method' | 'body'> {
  method?: RequestMethod;
  body?: any;
  params?: Record<string, string | number | boolean | undefined | null>;
  withAuth?: boolean;
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
  signal?: AbortSignal; // AbortController için signal
  responseType?: 'json' | 'blob' | 'text'; // Yanıt tipini belirtmek için
  isFormData?: boolean; // FormData olup olmadığını belirtmek için
}

// Yanıt tipi
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
  aborted?: boolean; // İstek iptal edildi mi?
  // Geriye dönük uyumluluk için spread edilen alanlar
  runs?: any[];
  schedules?: any[];
  users?: any[];
  companies?: any[];
  teams?: any[];
  folders?: any[];
  scenarios?: any[];
  reports?: any[];
  dataSets?: any[];
  dataSources?: any[];
  dataEnvironments?: any[];
  environments?: any[];
  activeTests?: any[];
  tests?: any[];
  nodes?: any[];
  stats?: any;
  report?: any;
  total?: number;
  count?: number;
  timestamp?: string;
  inUseByRuns?: any[];
}

/**
 * API isteği yapmak için kullanılan temel fonksiyon
 *
 * @param endpoint API endpoint'i
 * @param options İstek seçenekleri
 * @returns API yanıtı
 */
export async function fetchApi<T = any>(
  endpoint: string,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> {
  try {
    // Varsayılan seçenekler
    const {
      method = 'GET',
      body,
      params,
      withAuth = true,
      showErrorToast = true,
      showSuccessToast = false,
      successMessage = 'İşlem başarıyla tamamlandı',
      responseType = 'json',
      ...fetchOptions
    } = options;

    // URL oluştur
    let url = `${API_BASE_URL}${endpoint}`;

    // URL parametrelerini ekle
    if (params) {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });
      const queryString = queryParams.toString();
      if (queryString) {
        url += `?${queryString}`;
      }
    }

    // İstek seçeneklerini oluştur
    const requestOptions: RequestInit = {
      method,
      ...fetchOptions,
      headers: {
        ...fetchOptions.headers,
      },
      signal: options.signal, // AbortController signal'ını ekle
    };

    // FormData kontrolü
    const isFormData = options.isFormData || body instanceof FormData;

    // Content-Type header'ını ayarla (FormData değilse)
    if (!isFormData) {
      requestOptions.headers = {
        'Content-Type': 'application/json',
        ...requestOptions.headers,
      };
    }

    // Body ekle (GET istekleri hariç)
    if (body && method !== 'GET') {
      if (isFormData) {
        // FormData ise doğrudan gönder
        requestOptions.body = body;
        // FormData için Content-Type header'ını kaldır (browser otomatik ekler)
        if (requestOptions.headers && 'Content-Type' in requestOptions.headers) {
          delete requestOptions.headers['Content-Type'];
        }
      } else {
        // Normal JSON body
        requestOptions.body = JSON.stringify(body);
      }
    }

    // Auth token ekle
    if (withAuth) {
      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      if (token) {
        requestOptions.headers = {
          ...requestOptions.headers,
          Authorization: `Bearer ${token}`,
        };
      }
    }

    // İsteği yap
    const response = await fetch(url, requestOptions);

    // 401 Unauthorized durumunu kontrol et
    if (response.status === 401) {
      // Token'ları temizle
      localStorage.removeItem('authToken');
      sessionStorage.removeItem('authToken');
      localStorage.removeItem('user');
      sessionStorage.removeItem('user');

      // Login sayfasına yönlendir
      if (typeof window !== 'undefined') {
        window.location.href = '/auth?session_expired=true';
      }

      // Hata döndürerek işlemi durdur
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error || 'Oturum süresi doldu veya geçersiz. Lütfen tekrar giriş yapın.';
      
      toast.error('Oturum Hatası', {
        description: errorMessage,
      });

      return {
        success: false,
        error: errorMessage,
        statusCode: 401,
      };
    }

    // Yanıt tipine göre işleme
    let data;

    // Blob yanıtı için
    if (responseType === 'blob') {
      if (response.ok) {
        data = await response.blob();

        if (showSuccessToast) {
          toast.success(successMessage);
        }

        return {
          success: true,
          data: data as T, // Blob verisi
          statusCode: response.status,
        };
      } else {
        // Hata durumunda JSON yanıtı almaya çalış
        const text = await response.text();
        try {
          const errorData = JSON.parse(text);
          const errorMessage = errorData.error || errorData.message || 'Bir hata oluştu';

          if (showErrorToast) {
            toast.error(errorMessage);
          }

          return {
            success: false,
            error: errorMessage,
            statusCode: response.status,
          };
        } catch (e) {
          // JSON parse edilemezse
          const errorMessage = 'Bir hata oluştu';

          if (showErrorToast) {
            toast.error(errorMessage);
          }

          return {
            success: false,
            error: errorMessage,
            statusCode: response.status,
          };
        }
      }
    }

    // Text yanıtı için
    if (responseType === 'text') {
      const text = await response.text();

      if (response.ok) {
        if (showSuccessToast) {
          toast.success(successMessage);
        }

        return {
          success: true,
          data: text as T,
          statusCode: response.status,
        };
      } else {
        const errorMessage = text || 'Bir hata oluştu';

        if (showErrorToast) {
          toast.error(errorMessage);
        }

        return {
          success: false,
          error: errorMessage,
          statusCode: response.status,
        };
      }
    }

    // JSON yanıtı için (varsayılan)
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      // JSON olmayan yanıtlar için (örn. text)
      const text = await response.text();
      try {
        data = JSON.parse(text);
      } catch (e) {
        data = { success: response.ok, message: text };
      }
    }



    // Başarılı yanıt
    if (response.ok) {
      if (showSuccessToast) {
        toast.success(successMessage);
      }

      // Eğer API response'u zaten success alanı içeriyorsa, doğrudan döndür
      // Nested data problemi yaratmamak için data alanını tekrar ekleme
      if (data && typeof data === 'object' && 'success' in data) {
        return {
          ...data,
          statusCode: response.status,
        };
      }

      // API yanıtını doğrudan döndür, ancak success ve statusCode ekle
      // Circular reference'ı önlemek için data'yı spread etmiyoruz
      return {
        success: true,
        data: data, // API'den gelen veriyi data alanına koy
        message: data.message,
        statusCode: response.status,
        // Geriye dönük uyumluluk için bazı yaygın alanları doğrudan ekle
        ...(data.runs && { runs: data.runs }),
        ...(data.schedules && { schedules: data.schedules }),
        ...(data.users && { users: data.users }),
        ...(data.companies && { companies: data.companies }),
        ...(data.teams && { teams: data.teams }),
        ...(data.folders && { folders: data.folders }),
        ...(data.scenarios && { scenarios: data.scenarios }),
        ...(data.reports && { reports: data.reports }),
        ...(data.dataSets && { dataSets: data.dataSets }),
        ...(data.dataSources && { dataSources: data.dataSources }),
        ...(data.dataEnvironments && { dataEnvironments: data.dataEnvironments }),
        ...(data.environments && { environments: data.environments }),
        ...(data.activeTests && { activeTests: data.activeTests }),
        ...(data.tests && { tests: data.tests }),
        ...(data.nodes && { nodes: data.nodes }),
        ...(data.stats && { stats: data.stats }),
        ...(data.report && { report: data.report }),
        ...(data.total !== undefined && { total: data.total }),
        ...(data.count !== undefined && { count: data.count }),
        ...(data.timestamp && { timestamp: data.timestamp }),
      };
    }

    // Hata yanıtı
    const errorMessage = data.error || data.message || 'Bir hata oluştu';
    if (showErrorToast) {
      toast.error(errorMessage);
    }

    // Include specific fields from the API response in the error response
    // Avoid circular reference by not spreading the entire data object
    return {
      success: false,
      error: errorMessage,
      statusCode: response.status,
      data: data, // Include the original data for debugging
      // Include specific commonly used fields for backward compatibility
      ...(data.inUseByRuns && { inUseByRuns: data.inUseByRuns }),
    };
  } catch (error: any) {
    // AbortError durumunda sessizce çık
    if (error.name === 'AbortError') {
      return {
        success: false,
        error: 'Request aborted',
        aborted: true,
        data: undefined // undefined kullan, null değil
      };
    }

    // Diğer istek hataları
    const errorMessage = error.message || 'Bir hata oluştu';
    if (options.showErrorToast !== false) {
      toast.error(errorMessage);
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Admin API isteği yapmak için kullanılan fonksiyon
 *
 * @param endpoint API endpoint'i
 * @param options İstek seçenekleri
 * @returns API yanıtı
 */
export async function fetchAdminApi<T = any>(
  endpoint: string,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> {
  // Admin token'ı kullan
  const adminToken = localStorage.getItem('adminAuthToken') || sessionStorage.getItem('adminAuthToken');

  return fetchApi<T>(endpoint, {
    ...options,
    headers: {
      ...options.headers,
      Authorization: `Bearer ${adminToken}`,
    },
    withAuth: false, // Zaten manuel olarak token ekliyoruz
  });
}

/**
 * GET isteği yapmak için kısayol
 */
export function get<T = any>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}) {
  return fetchApi<T>(endpoint, { ...options, method: 'GET' });
}

/**
 * POST isteği yapmak için kısayol
 */
export function post<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}) {
  // FormData kontrolü
  const isFormData = body instanceof FormData;

  return fetchApi<T>(endpoint, {
    ...options,
    method: 'POST',
    body,
    isFormData: isFormData || options.isFormData
  });
}

/**
 * PUT isteği yapmak için kısayol
 */
export function put<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}) {
  return fetchApi<T>(endpoint, { ...options, method: 'PUT', body });
}

/**
 * DELETE isteği yapmak için kısayol
 */
export function del<T = any>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}) {
  return fetchApi<T>(endpoint, { ...options, method: 'DELETE' });
}

/**
 * PATCH isteği yapmak için kısayol
 */
export function patch<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}) {
  return fetchApi<T>(endpoint, { ...options, method: 'PATCH', body });
}

/**
 * Admin GET isteği yapmak için kısayol
 */
export function adminGet<T = any>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}) {
  return fetchAdminApi<T>(endpoint, { ...options, method: 'GET' });
}

/**
 * Admin POST isteği yapmak için kısayol
 */
export function adminPost<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}) {
  return fetchAdminApi<T>(endpoint, { ...options, method: 'POST', body });
}

/**
 * Admin PUT isteği yapmak için kısayol
 */
export function adminPut<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}) {
  return fetchAdminApi<T>(endpoint, { ...options, method: 'PUT', body });
}

/**
 * Admin DELETE isteği yapmak için kısayol
 */
export function adminDel<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}) {
  return fetchAdminApi<T>(endpoint, { ...options, method: 'DELETE', body });
}

/**
 * Admin PATCH isteği yapmak için kısayol
 */
export function adminPatch<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}) {
  return fetchAdminApi<T>(endpoint, { ...options, method: 'PATCH', body });
}
