/**
 * Operation Queue Manager
 * Asynchronous operation queue for database operations
 */

import { logger } from './logger.js';

// Queue item interface
interface QueueItem {
  id: string;
  operation: () => Promise<any>;
  priority: number;
  timestamp: number;
  retries: number;
  maxRetries: number;
  backoffFactor: number;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
}

// Queue configuration
interface QueueConfig {
  concurrency: number;
  retryDelay: number;
  maxRetries: number;
  backoffFactor: number;
  priorityLevels: {
    HIGH: number;
    NORMAL: number;
    LOW: number;
  };
}

/**
 * Operation Queue Manager
 * Manages asynchronous operations with priority, concurrency control, and retry logic
 */
class OperationQueueManager {
  private queue: QueueItem[] = [];
  private running: Set<string> = new Set();
  private config: QueueConfig;
  private processing: boolean = false;
  private stats = {
    enqueued: 0,
    completed: 0,
    failed: 0,
    retried: 0
  };

  constructor(config?: Partial<QueueConfig>) {
    // Default configuration
    this.config = {
      concurrency: 50,
      retryDelay: 1000,
      maxRetries: 3,
      backoffFactor: 2,
      priorityLevels: {
        HIGH: 1,
        NORMAL: 5,
        LOW: 10
      },
      ...config
    };

    // Start the queue processor
    this.startProcessor();

    logger.info(`OperationQueueManager: Initialized with concurrency=${this.config.concurrency}, maxRetries=${this.config.maxRetries}`);
  }

  /**
   * Enqueue an operation with priority
   * @param id Unique operation ID
   * @param operation Async function to execute
   * @param options Operation options
   * @returns Promise that resolves when the operation completes
   */
  enqueue<T>(
    id: string,
    operation: () => Promise<T>,
    options: {
      priority?: 'HIGH' | 'NORMAL' | 'LOW';
      maxRetries?: number;
      backoffFactor?: number;
    } = {}
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      // Generate a unique ID if not provided
      const operationId = id || `op-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      // Get priority level
      const priorityLevel = options.priority || 'NORMAL';
      const priority = this.config.priorityLevels[priorityLevel];

      // Create queue item
      const queueItem: QueueItem = {
        id: operationId,
        operation,
        priority,
        timestamp: Date.now(),
        retries: 0,
        maxRetries: options.maxRetries !== undefined ? options.maxRetries : this.config.maxRetries,
        backoffFactor: options.backoffFactor || this.config.backoffFactor,
        onSuccess: (result) => resolve(result),
        onError: (error) => reject(error)
      };

      // Add to queue
      this.queue.push(queueItem);
      this.stats.enqueued++;

      // Sort queue by priority and timestamp
      this.sortQueue();

      // Trigger processing
      this.processQueue();

      logger.debug(`OperationQueueManager: Enqueued operation ${operationId} with priority ${priorityLevel}`);
    });
  }

  /**
   * Sort the queue by priority and timestamp
   */
  private sortQueue(): void {
    this.queue.sort((a, b) => {
      // First sort by priority (lower number = higher priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      // Then sort by timestamp (older first)
      return a.timestamp - b.timestamp;
    });
  }

  /**
   * Start the queue processor
   */
  private startProcessor(): void {
    // Process queue every 50ms
    setInterval(() => {
      this.processQueue();
    }, 50);

    // Log stats every minute
    setInterval(() => {
      this.logStats();
    }, 60000);
  }

  /**
   * Process the queue
   */
  private processQueue(): void {
    // Prevent concurrent processing
    if (this.processing) {
      return;
    }

    this.processing = true;

    try {
      // Check if we can run more operations
      while (this.queue.length > 0 && this.running.size < this.config.concurrency) {
        const item = this.queue.shift();
        if (!item) continue;

        // Mark as running
        this.running.add(item.id);

        // Execute operation
        this.executeOperation(item);
      }
    } finally {
      this.processing = false;
    }
  }

  /**
   * Execute an operation
   * @param item Queue item to execute
   */
  private async executeOperation(item: QueueItem): Promise<void> {
    const startTime = Date.now();

    try {
      logger.debug(`OperationQueueManager: Starting operation ${item.id} (queue: ${this.queue.length}, running: ${this.running.size})`);

      // Execute the operation with timeout
      const timeoutMs = 300000; // 5 minutes timeout
      const result = await Promise.race([
        item.operation(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Operation timeout after ${timeoutMs}ms`)), timeoutMs)
        )
      ]);

      // Handle success
      const duration = Date.now() - startTime;
      this.stats.completed++;
      this.running.delete(item.id);

      if (item.onSuccess) {
        item.onSuccess(result);
      }

      logger.debug(`OperationQueueManager: Operation ${item.id} completed successfully in ${duration}ms`);
    } catch (error: unknown) {
      // Handle error
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      if (item.retries < item.maxRetries) {
        // Check if this is a retryable error
        const isRetryable = this.isRetryableError(error);

        if (isRetryable) {
          // Retry with exponential backoff
          const delay = this.config.retryDelay * Math.pow(item.backoffFactor, item.retries);
          item.retries++;
          this.stats.retried++;

          logger.warn(`OperationQueueManager: Operation ${item.id} failed after ${duration}ms, retrying in ${delay}ms (${item.retries}/${item.maxRetries}): ${errorMessage}`);

          // Re-queue after delay
          setTimeout(() => {
            this.running.delete(item.id);
            this.queue.push(item);
            this.sortQueue();
            this.processQueue();
          }, delay);
        } else {
          // Non-retryable error, fail immediately
          this.stats.failed++;
          this.running.delete(item.id);

          logger.error(`OperationQueueManager: Operation ${item.id} failed with non-retryable error after ${duration}ms: ${errorMessage}`, {
            error: errorStack,
            operationId: item.id
          });

          if (item.onError) {
            item.onError(error);
          }
        }
      } else {
        // Max retries reached, fail permanently
        this.stats.failed++;
        this.running.delete(item.id);

        logger.error(`OperationQueueManager: Operation ${item.id} failed permanently after ${item.maxRetries} retries and ${duration}ms: ${errorMessage}`, {
          error: errorStack,
          operationId: item.id,
          totalRetries: item.retries
        });

        if (item.onError) {
          item.onError(error);
        }
      }
    } finally {
      // Ensure the operation is removed from running set
      this.running.delete(item.id);

      // Trigger queue processing
      this.processQueue();
    }
  }

  /**
   * Check if an error is retryable
   * @param error The error to check
   * @returns True if the error is retryable
   */
  private isRetryableError(error: unknown): boolean {
    if (!(error instanceof Error)) {
      return false;
    }

    const message = error.message.toLowerCase();

    // MongoDB connection/timeout errors are retryable
    if (message.includes('timeout') ||
        message.includes('connection') ||
        message.includes('network') ||
        message.includes('econnreset') ||
        message.includes('enotfound') ||
        message.includes('writeconflict') ||
        message.includes('server selection timeout')) {
      return true;
    }

    // MongoDB write concern errors are retryable
    if (message.includes('write concern') ||
        message.includes('write conflict') ||
        message.includes('duplicate key') && message.includes('transactionid')) {
      return true;
    }

    // Operation queue timeout is retryable
    if (message.includes('operation timeout')) {
      return true;
    }

    return false;
  }

  /**
   * Log queue statistics
   */
  private logStats(): void {
    const queueLength = this.queue.length;
    const runningCount = this.running.size;

    logger.info(`OperationQueueManager: Stats - Queue: ${queueLength}, Running: ${runningCount}, Completed: ${this.stats.completed}, Failed: ${this.stats.failed}, Retried: ${this.stats.retried}`);
  }

  /**
   * Get current queue statistics
   */
  getStats() {
    return {
      queueLength: this.queue.length,
      runningCount: this.running.size,
      ...this.stats
    };
  }
}

// Create singleton instance
export const operationQueue = new OperationQueueManager();
export default operationQueue;
