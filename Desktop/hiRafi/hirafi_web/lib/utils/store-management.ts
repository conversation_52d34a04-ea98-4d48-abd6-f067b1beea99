/**
 * Store Management Utility
 * Centralizes store cleanup and context management
 */

import { useEffect } from "react";
import { CompanyExpiryStorage } from "./expiry-storage";

interface StoreWithClear {
  clearPersistedState?: () => void;
  resetForm?: () => void;
  resetFormAndVariables?: () => void;
  resetScenario?: () => void;
  clearFilters?: () => void;
  clearSelectedScenarios?: () => void;
  reset?: () => void;
}

interface UserContext {
  id?: string;
  companyId?: string;
  teamId?: string;
}

class StoreManager {
  private registeredStores: StoreWithClear[] = [];
  private currentUserContext: UserContext | null = null;

  /**
   * Register a store for automatic cleanup
   */
  registerStore(store: StoreWithClear) {
    this.registeredStores.push(store);
  }

  /**
   * Set current user context
   */
  setUserContext(context: UserContext) {
    const prevContext = this.currentUserContext;
    this.currentUserContext = context;

    // Only clear stores if there's a meaningful context change
    // Don't clear on initial context set or if contexts are effectively the same
    if (
      prevContext &&
      context &&
      prevContext.companyId &&
      context.companyId &&
      (prevContext.companyId !== context.companyId ||
        prevContext.id !== context.id)
    ) {
      console.log("[StoreManager] User context changed, clearing stores", {
        prev: prevContext,
        new: context,
      });
      this.clearAllStores();
    } else if (prevContext && context) {
      console.log("[StoreManager] User context set but no significant change", {
        prev: prevContext,
        new: context,
      });
    } else {
      console.log(
        "[StoreManager] Initial user context set, preserving stores",
        {
          context,
        },
      );
    }
  }

  /**
   * Clear all registered stores
   */
  clearAllStores() {
    console.log("[StoreManager] Clearing all stores due to context change");

    this.registeredStores.forEach((store) => {
      try {
        // All stores now have clearPersistedState method
        if (store.clearPersistedState) {
          store.clearPersistedState();
        }
      } catch (error) {
        console.warn("[StoreManager] Error clearing store:", error);
      }
    });

    // Also clear UI data and expired data
    this.clearUIData();
    this.cleanupExpiredData();
  }

  /**
   * Force clear all localStorage store data
   */
  forceCleanLocalStorage() {
    const storeKeys = [
      "scenario-creation-store",
      "scenario-store",
      "test-data-sets-store",
    ];

    storeKeys.forEach((key) => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`[StoreManager] Error removing ${key}:`, error);
      }
    });
  }

  /**
   * Clean up expired scenario creation data
   */
  cleanupExpiredData() {
    console.log("[StoreManager] Cleaning up expired scenario creation data");

    try {
      const cleanedCount = CompanyExpiryStorage.cleanupCompanyExpiredItems();
      if (cleanedCount > 0) {
        console.log(`[StoreManager] Cleaned up ${cleanedCount} expired items`);
      }
    } catch (error) {
      console.warn("[StoreManager] Error during expired data cleanup:", error);
    }
  }

  /**
   * Clear company-specific UI data
   */
  clearUIData() {
    const userInfo = this.getCurrentUserInfo();
    if (!userInfo?.companyId) return;

    const companyPrefix = `-company-${userInfo.companyId}`;
    const uiKeys = ["theme", "feedbackButtonPosition", "hideAppPopup"];

    uiKeys.forEach((baseKey) => {
      try {
        const companyKey = `${baseKey}${companyPrefix}`;
        localStorage.removeItem(companyKey);
        // Also clear non-company specific keys for clean slate
        localStorage.removeItem(baseKey);
      } catch (error) {
        console.warn(
          `[StoreManager] Error clearing UI data ${baseKey}:`,
          error,
        );
      }
    });
  }

  /**
   * Get current user info from localStorage
   */
  private getCurrentUserInfo() {
    if (typeof window === "undefined") return null;

    try {
      const userStr = localStorage.getItem("user");
      return userStr ? JSON.parse(userStr) : null;
    } catch {
      return null;
    }
  }

  /**
   * Get current user context
   */
  getUserContext(): UserContext | null {
    return this.currentUserContext;
  }
}

// Singleton instance
export const storeManager = new StoreManager();

/**
 * Hook to register store for automatic cleanup
 */
export function useStoreRegistration(store: StoreWithClear) {
  useEffect(() => {
    storeManager.registerStore(store);
  }, [store]);
}

/**
 * Enhanced logout function that clears all stores
 */
export function enhancedLogout(originalLogout: () => void) {
  // Clear all stores first
  storeManager.clearAllStores();
  storeManager.forceCleanLocalStorage();

  // Then perform original logout
  originalLogout();
}
