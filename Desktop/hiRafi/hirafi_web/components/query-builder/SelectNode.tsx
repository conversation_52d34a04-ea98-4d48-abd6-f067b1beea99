import { <PERSON><PERSON>, Position } from 'reactflow';
import { But<PERSON> } from '@/components/ui/button';
import { X, Plus, Search, Filter } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useState, useEffect } from 'react';

function SelectNode({ data, id }: { data: any, id: string }) {
  const [columns, setColumns] = useState<string[]>([]);
  const [isFetchingColumns, setIsFetchingColumns] = useState(false);

  useEffect(() => {
    console.log('SelectNode useEffect:', { 
      table: data.table, 
      dbTables: data.dbTables, 
      dbTablesLength: data.dbTables?.length,
      connectionString: data.connectionString,
      version: data.version 
    });
    
    // Eğer tablo seçili ve connection string mevcut ise sütunları getir
    if (data.table && data.connectionString) {
      console.log('Fetching columns for table:', data.table, 'with connection:', data.connectionString);
      setIsFetchingColumns(true);
      
      // Doğrudan getDbColumns API'sini çağır
      import('@/lib/api/test-data').then(({ getDbColumns }) => {
        return getDbColumns(data.connectionString, data.table);
      }).then((result: any) => {
        console.log('Direct getDbColumns result:', result);
        if (result.success && result.data?.columns) {
          console.log('Setting columns:', result.data.columns);
          setColumns(result.data.columns);
        } else {
          console.log('getDbColumns failed:', result.error);
          setColumns([]);
        }
        setIsFetchingColumns(false);
      }).catch((error: any) => {
        console.error("Failed to fetch columns directly", error);
        setColumns([]);
        setIsFetchingColumns(false);
      });
    } else {
      console.log('Conditions not met for fetching columns - no table or connectionString');
    }
  }, [data.version, data.dbTables, data.table, data.connectionString]);

  const handleTableChange = async (tableName: string, resetState = true) => {
    if(resetState) {
        data.updateData({ 
          table: tableName, 
          selectedColumns: ['*'], 
          whereConditions: [{ col: '', operator: '=', val: '' }],
          orderBy: '',
          orderDirection: 'ASC',
          limitValue: ''
        });
    } else {
        data.updateData({ table: tableName });
    }

    setIsFetchingColumns(true);
    try {
      const fetchedColumns = await data.onTableSelect(tableName);
      setColumns(fetchedColumns || []);
    } catch (error) {
      console.error("Failed to fetch columns", error);
    } finally {
        setIsFetchingColumns(false);
    }
  };

  const handleColumnToggle = (column: string, checked: boolean) => {
    const newSelectedColumns = [...(data.selectedColumns || ['*'])];
    
    if (column === '*') {
      // If selecting *, clear all other selections
      data.updateData({ selectedColumns: checked ? ['*'] : [] });
    } else {
      // If selecting specific column, remove * if it exists
      const filteredColumns = newSelectedColumns.filter(col => col !== '*');
      
      if (checked) {
        filteredColumns.push(column);
      } else {
        const index = filteredColumns.indexOf(column);
        if (index > -1) filteredColumns.splice(index, 1);
      }
      
      // If no columns selected, default to *
      data.updateData({ selectedColumns: filteredColumns.length > 0 ? filteredColumns : ['*'] });
    }
  };

  const handleWhereChange = (index: number, field: 'col' | 'operator' | 'val', value: string) => {
    const newConditions = [...(data.whereConditions || [])];
    newConditions[index][field] = value;
    data.updateData({ whereConditions: newConditions });
  };
  
  const addWhereCondition = () => {
    const newConditions = [...(data.whereConditions || []), { col: '', operator: '=', val: '' }];
    data.updateData({ whereConditions: newConditions });
  };

  const removeWhereCondition = (index: number) => {
    const newConditions = (data.whereConditions || []).filter((_: any, i: number) => i !== index);
    data.updateData({ whereConditions: newConditions.length > 0 ? newConditions : [{ col: '', operator: '=', val: '' }] });
  };

  const operators = ['=', '!=', '>', '<', '>=', '<=', 'LIKE', 'NOT LIKE', 'IN', 'NOT IN', 'IS NULL', 'IS NOT NULL'];

  return (
    <div className="query-builder-node">
      <div className="node-header">
        <div className="node-title">
            <Search className="h-4 w-4 text-green-500" />
            <span>SELECT Data</span>
        </div>
        <Button variant="ghost" size="icon" className="h-6 w-6 btn" onClick={data.onDelete}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="node-body">
        <div className="node-section">
            <Label className="node-section-label">TABLE</Label>
            <Select onValueChange={handleTableChange} value={data.table}>
                <SelectTrigger className="react-select-trigger">
                  <SelectValue placeholder="Select a table..." />
                </SelectTrigger>
                <SelectContent>
                  {data.dbTables.map((t: string) => (
                    <SelectItem key={t} value={t}>{t}</SelectItem>
                  ))}
                </SelectContent>
            </Select>
        </div>

        {isFetchingColumns && <div className="text-center py-2 text-sm text-gray-500">Loading columns...</div>}

        {data.table && !isFetchingColumns && (
          <>
            {/* Column Selection */}
            <div className="node-section">
              <Label className="node-section-label">SELECT COLUMNS</Label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={(data.selectedColumns || ['*']).includes('*')}
                    onCheckedChange={(checked) => handleColumnToggle('*', checked as boolean)}
                  />
                  <Label htmlFor="select-all" className="text-sm font-medium">* (All Columns)</Label>
                </div>
                {columns.map((column, index) => (
                  <div key={column} className="flex items-center space-x-2">
                    <Checkbox
                      id={`col-${index}`}
                      checked={(data.selectedColumns || []).includes(column)}
                      onCheckedChange={(checked) => handleColumnToggle(column, checked as boolean)}
                    />
                    <Label htmlFor={`col-${index}`} className="text-sm">{column}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* WHERE Conditions */}
            <div className="node-section">
              <div className="flex items-center gap-2 mb-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <Label className="node-section-label">WHERE CONDITIONS (Optional)</Label>
              </div>
              {(data.whereConditions || [{ col: '', operator: '=', val: '' }]).map((condition: any, index: number) => (
                <div key={index} className="node-row space-y-1">
                  <div className="flex gap-1">
                    <Select value={condition.col} onValueChange={(val) => handleWhereChange(index, 'col', val)}>
                      <SelectTrigger className="react-select-trigger flex-1">
                        <SelectValue placeholder="Column" />
                      </SelectTrigger>
                      <SelectContent>
                        {columns.map(c => (
                          <SelectItem key={c} value={c}>{c}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Select value={condition.operator} onValueChange={(val) => handleWhereChange(index, 'operator', val)}>
                      <SelectTrigger className="react-select-trigger w-24">
                        <SelectValue placeholder="=" />
                      </SelectTrigger>
                      <SelectContent>
                        {operators.map(op => (
                          <SelectItem key={op} value={op}>{op}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Input 
                      className="react-input flex-1" 
                      placeholder="Value" 
                      value={condition.val} 
                      onChange={(e) => handleWhereChange(index, 'val', e.target.value)} 
                    />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="btn h-8 w-8" 
                      onClick={() => removeWhereCondition(index)}
                    >
                      <X className="h-4 w-4 text-slate-400" />
                    </Button>
                  </div>
                </div>
              ))}
              <Button variant="outline" size="sm" className="btn mt-1" onClick={addWhereCondition}>
                <Plus className="h-4 w-4 mr-2" />Add Condition
              </Button>
            </div>

            {/* ORDER BY */}
            <div className="node-section">
              <Label className="node-section-label">ORDER BY (Optional)</Label>
              <div className="flex gap-2">
                <Select value={data.orderBy || 'none'} onValueChange={(val) => data.updateData({ orderBy: val === 'none' ? '' : val })}>
                  <SelectTrigger className="react-select-trigger flex-1">
                    <SelectValue placeholder="Select column..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {columns.map(c => (
                      <SelectItem key={c} value={c}>{c}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={data.orderDirection || 'ASC'} onValueChange={(val) => data.updateData({ orderDirection: val })}>
                  <SelectTrigger className="react-select-trigger w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ASC">ASC</SelectItem>
                    <SelectItem value="DESC">DESC</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* LIMIT */}
            <div className="node-section">
              <Label className="node-section-label">LIMIT (Optional)</Label>
              <Input 
                className="react-input" 
                placeholder="Number of rows" 
                type="number"
                value={data.limitValue || ''} 
                onChange={(e) => data.updateData({ limitValue: e.target.value })} 
              />
            </div>
          </>
        )}
      </div>
      <Handle type="source" position={Position.Right} />
    </div>
  );
}

export default SelectNode; 