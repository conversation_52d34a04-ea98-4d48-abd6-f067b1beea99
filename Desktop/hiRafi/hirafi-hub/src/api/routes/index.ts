/**
 * API Routes Index
 * Combines all routes and exports a single router
 */

import { Router } from 'express';
import testRoutes from './test-routes.js';
import nodeRoutes from './node-routes.js';
import healthRoutes from './health-routes.js';
import scenarioRoutes from './scenario-routes.js';
import scenarioExportRoutes from './scenario-export-routes.js';
import reportRoutes from './report-routes.js';
import sharedReportRoutes from './shared-report-routes.js';
import dashboardRoutes from './dashboard-routes.js';
import pluginRoutes from './plugin-routes.js';
import authRoutes from './auth-routes.js';
import vncRoutes from './vnc-routes.js';
import generateStepsRoutes from './generate-steps-routes.js';
import generateDataRoutes from './generate-data-routes.js';
import feedbackRoutes from './feedback-routes.js';
import runRoutes from './run-routes.js';
import teamRoutes from './team-routes.js';
// Company routes are now part of admin routes
import adminRoutes from './admin-routes.js';
import systemRoutes from './system-routes.js';
import redisRoutes from './redis.routes.js';
import statusRoutes from './status-routes.js';
import { authenticate } from '../middleware/authMiddleware.js';
import { authorizeCompanyOwner } from '../middleware/companyOwnerMiddleware.js';
import userRoutes from './user-routes.js';
import companyRoutes from './company-routes.js';
import settingsRoutes from './settings-routes.js';
import scheduleRoutes from './schedule-routes.js';
import landingPageRoutes from './landing-page-routes.js';
import stepProgressRoutes from './step-progress-routes.js';
import testDataRoutes from './test-data-routes.js';
import excelRoutes from './excel.routes.js';

const router = Router();

// Public routes - no authentication required
router.use('/', healthRoutes);
router.use('/auth', authRoutes);
router.use('/admin', adminRoutes);
router.use('/system', systemRoutes);
router.use('/shared-reports', sharedReportRoutes);
router.use('/landing-page', landingPageRoutes);

// VNC viewer endpoint'i herkese açık olmalı (token doğrulaması route içinde yapılıyor)
router.use('/vnc', vncRoutes);

// Protected routes - require authentication
router.use('/test-hub', authenticate, testRoutes);
router.use('/scenarios', authenticate, scenarioRoutes);
router.use('/scenario-export', authenticate, scenarioExportRoutes);
router.use('/nodes', authenticate, nodeRoutes);
router.use('/reports', authenticate, reportRoutes);
router.use('/dashboard', authenticate, dashboardRoutes);
// Plugin route'ları sadece company owner'lar için erişilebilir
router.use('/plugins', authenticate, authorizeCompanyOwner, pluginRoutes);
router.use('/generate-steps', authenticate, generateStepsRoutes);
router.use('/generate-data', authenticate, generateDataRoutes);
router.use('/runs', authenticate, runRoutes);
router.use('/teams', authenticate, teamRoutes);
router.use('/users', authenticate, userRoutes);
router.use('/feedback', authenticate, feedbackRoutes);
router.use('/companies', authenticate, companyRoutes);
router.use('/settings', authenticate, settingsRoutes);
router.use('/schedules', authenticate, scheduleRoutes);
// Admin company routes are accessed via /admin/companies
router.use('/redis', authenticate, redisRoutes);
router.use('/status', authenticate, statusRoutes);
router.use('/step-progress', authenticate, stepProgressRoutes);
router.use('/test-data', authenticate, testDataRoutes);
router.use('/excel', authenticate, excelRoutes);

export default router;