/**
 * Plugin service module
 * Handles operations related to plugins, their activation, and configuration
 */

import logger from '../../utils/logger.js';
import {
  isMongoDBInitialized,
  pluginsCollection
} from './dbConnection.js';
import { Document, OptionalId } from 'mongodb';

/**
 * Plugin interface
 */
interface Plugin {
  id: string;
  active: boolean;
  config: any;
  createdAt?: Date;
}

/**
 * Plugins data interface
 */
interface PluginsData extends Document {
  teamId: string;
  companyId: string;
  plugins: Plugin[];
}

/**
 * Get all plugins for a team and company
 * @param teamId Team ID to get plugins for
 * @param companyId Company ID to get plugins for
 * @returns Array of plugins
 */
export async function getUserPlugins(teamId: string, companyId: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', plugins: [] };
    }

    logger.info(`Fetching plugins for team: ${teamId}, company: ${companyId}`);
    const pluginsData = await pluginsCollection.findOne({ teamId, companyId });

    if (!pluginsData) {
      // Return default empty plugins data if none exists
      return {
        success: true,
        message: 'No plugins data found',
        plugins: []
      };
    }

    return {
      success: true,
      message: 'Plugins retrieved successfully',
      plugins: pluginsData.plugins || []
    };
  } catch (error) {
    logger.error(`Error fetching plugins: ${error}`);
    return { success: false, message: `Error fetching plugins: ${error}`, plugins: [] };
  }
}

/**
 * Get all active plugins for a team and company
 * @param teamId Team ID to get plugins for
 * @param companyId Company ID to get plugins for
 * @returns Array of active plugins
 */
export async function getActivePlugins(teamId: string, companyId: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    logger.info(`Fetching active plugins for team: ${teamId}, company: ${companyId}`);
    const pluginsData = await pluginsCollection.findOne({ teamId, companyId });

    if (!pluginsData) {
      // Return default empty plugins data if none exists
      return {
        success: true,
        message: 'No plugins data found',
        data: { teamId, companyId, plugins: [] }
      };
    }

    return { success: true, message: 'Plugins retrieved successfully', data: pluginsData };
  } catch (error) {
    logger.error(`Error fetching plugins: ${error}`);
    return { success: false, message: `Error fetching plugins: ${error}`, data: null };
  }
}

/**
 * Create a new plugin for a team and company
 * @param pluginData Plugin data including teamId, companyId and pluginName
 * @returns Created plugin
 */
export async function createPlugin(pluginData: any) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', plugin: null };
    }

    if (!pluginData.teamId || !pluginData.companyId || !pluginData.pluginName) {
      return { success: false, message: 'Missing required fields: teamId, companyId or pluginName', plugin: null };
    }

    logger.info(`Creating ${pluginData.pluginName} plugin for team: ${pluginData.teamId}, company: ${pluginData.companyId}`);

    // Get current plugins configuration
    let teamPlugins = await pluginsCollection.findOne({ teamId: pluginData.teamId, companyId: pluginData.companyId });

    const newPlugin = {
      id: pluginData.pluginName,
      active: pluginData.active || false,
      config: pluginData.config || {},
      createdAt: new Date()
    };

    if (!teamPlugins) {
      // Create new plugins entry
      const pluginsEntry = {
        teamId: pluginData.teamId,
        companyId: pluginData.companyId,
        plugins: [newPlugin]
      };

      await pluginsCollection.insertOne(pluginsEntry);
      logger.info(`Created first plugin for team: ${pluginData.teamId}, company: ${pluginData.companyId}`);

      return {
        success: true,
        message: 'Plugin created successfully',
        plugin: newPlugin
      };
    } else {
      // Check if plugin already exists
      const existingPluginIndex = teamPlugins.plugins.findIndex((p: any) => p.id === pluginData.pluginName);

      if (existingPluginIndex >= 0) {
        return {
          success: false,
          message: 'Plugin already exists for this team',
          plugin: teamPlugins.plugins[existingPluginIndex]
        };
      }

      // Add new plugin to existing plugins
      teamPlugins.plugins.push(newPlugin);

      await pluginsCollection.updateOne(
        { teamId: pluginData.teamId, companyId: pluginData.companyId },
        { $set: { plugins: teamPlugins.plugins } }
      );

      logger.info(`Added plugin ${pluginData.pluginName} for team: ${pluginData.teamId}, company: ${pluginData.companyId}`);

      return {
        success: true,
        message: 'Plugin created successfully',
        plugin: newPlugin
      };
    }
  } catch (error) {
    logger.error(`Error creating plugin: ${error}`);
    return { success: false, message: `Error creating plugin: ${error}`, plugin: null };
  }
}

/**
 * Get a plugin by name for team and company
 * @param pluginName Plugin name
 * @param teamId Team ID
 * @param companyId Company ID
 * @returns Plugin if found
 */
export async function getPluginByNameAndUser(pluginName: string, teamId: string, companyId: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', plugin: null };
    }

    logger.info(`Fetching ${pluginName} plugin for team: ${teamId}, company: ${companyId}`);

    const teamPlugins = await pluginsCollection.findOne({ teamId, companyId });

    if (!teamPlugins) {
      return { success: false, message: 'No plugins found for team', plugin: null };
    }

    const plugin = teamPlugins.plugins.find((p: any) => p.id === pluginName);

    if (!plugin) {
      return { success: false, message: 'Plugin not found for team', plugin: null };
    }

    return {
      success: true,
      message: 'Plugin found',
      plugin
    };
  } catch (error) {
    logger.error(`Error fetching plugin: ${error}`);
    return { success: false, message: `Error fetching plugin: ${error}`, plugin: null };
  }
}

/**
 * Update plugin active status
 * @param pluginName Plugin name
 * @param teamId Team ID
 * @param companyId Company ID
 * @param enabled Enabled status
 * @returns Success status
 */
export async function updatePluginStatus(pluginName: string, teamId: string, companyId: string, enabled: boolean) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    logger.info(`${enabled ? 'Activating' : 'Deactivating'} ${pluginName} plugin for team: ${teamId}, company: ${companyId}`);

    const teamPlugins = await pluginsCollection.findOne({ teamId, companyId });

    if (!teamPlugins) {
      // Create new plugins entry with this plugin
      const pluginsEntry = {
        teamId,
        companyId,
        plugins: [{
          id: pluginName,
          active: enabled,
          config: {}
        }]
      };

      await pluginsCollection.insertOne(pluginsEntry);

      return {
        success: true,
        message: `Plugin ${enabled ? 'activated' : 'deactivated'} successfully`
      };
    } else {
      // Find and update the plugin
      const existingPluginIndex = teamPlugins.plugins.findIndex((p: any) => p.id === pluginName);

      if (existingPluginIndex >= 0) {
        // Update existing plugin
        teamPlugins.plugins[existingPluginIndex].active = enabled;
      } else {
        // Add new plugin
        teamPlugins.plugins.push({
          id: pluginName,
          active: enabled,
          config: {}
        });
      }

      await pluginsCollection.updateOne(
        { teamId, companyId },
        { $set: { plugins: teamPlugins.plugins } }
      );

      return {
        success: true,
        message: `Plugin ${enabled ? 'activated' : 'deactivated'} successfully`
      };
    }
  } catch (error) {
    logger.error(`Error updating plugin status: ${error}`);
    return { success: false, message: `Error updating plugin status: ${error}` };
  }
}

/**
 * Delete a plugin
 * @param pluginName Plugin name
 * @param teamId Team ID
 * @param companyId Company ID
 * @returns Success status
 */
export async function deletePlugin(pluginName: string, teamId: string, companyId: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    logger.info(`Deleting ${pluginName} plugin for team: ${teamId}, company: ${companyId}`);

    const teamPlugins = await pluginsCollection.findOne({ teamId, companyId });

    if (!teamPlugins) {
      return { success: false, message: 'No plugins found for team' };
    }

    // Filter out the plugin to delete
    const newPlugins = teamPlugins.plugins.filter((p: any) => p.id !== pluginName);

    // If no plugins were removed, it didn't exist
    if (newPlugins.length === teamPlugins.plugins.length) {
      return { success: false, message: 'Plugin not found for team' };
    }

    await pluginsCollection.updateOne(
      { teamId, companyId },
      { $set: { plugins: newPlugins } }
    );

    return {
      success: true,
      message: 'Plugin deleted successfully'
    };
  } catch (error) {
    logger.error(`Error deleting plugin: ${error}`);
    return { success: false, message: `Error deleting plugin: ${error}` };
  }
}

/**
 * Activate or deactivate a plugin for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @param pluginId Plugin ID to activate
 * @param active Activation status
 * @returns Updated plugins data
 */
export async function setPluginActivation(teamId: string, companyId: string, pluginId: string, active: boolean) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!teamId || !companyId || !pluginId) {
      return { success: false, message: 'Missing required fields: teamId, companyId or pluginId', data: null };
    }

    logger.info(`${active ? 'Activating' : 'Deactivating'} plugin ${pluginId} for team: ${teamId}, company: ${companyId}`);

    // Get current plugins configuration
    let pluginsData = await pluginsCollection.findOne({ teamId, companyId }) as PluginsData | null;

    if (!pluginsData) {
      // Create new plugins data if none exists
      const newPluginsData: OptionalId<PluginsData> = {
        teamId,
        companyId,
        plugins: [{
          id: pluginId,
          active,
          config: {}
        }]
      };

      await pluginsCollection.insertOne(newPluginsData);
      logger.info(`Created new plugins data for team: ${teamId}, company: ${companyId}`);

      // Fetch the newly created document to get the _id field
      const createdPluginsData = await pluginsCollection.findOne({ teamId, companyId }) as unknown as PluginsData;

      return {
        success: true,
        message: `Plugin ${active ? 'activated' : 'deactivated'} successfully`,
        data: createdPluginsData
      };
    } else {
      // Update existing plugins data
      const existingPluginIndex = pluginsData.plugins.findIndex((p: any) => p.id === pluginId);

      if (existingPluginIndex >= 0) {
        // Update existing plugin
        pluginsData.plugins[existingPluginIndex].active = active;
      } else {
        // Add new plugin
        pluginsData.plugins.push({
          id: pluginId,
          active,
          config: {}
        });
      }

      await pluginsCollection.updateOne(
        { teamId, companyId },
        { $set: { plugins: pluginsData.plugins } }
      );

      logger.info(`Updated plugin status for team: ${teamId}, company: ${companyId}`);
      return {
        success: true,
        message: `Plugin ${active ? 'activated' : 'deactivated'} successfully`,
        data: pluginsData
      };
    }
  } catch (error) {
    logger.error(`Error setting plugin activation: ${error}`);
    return { success: false, message: `Error setting plugin activation: ${error}`, data: null };
  }
}

/**
 * Update plugin configuration for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @param pluginId Plugin ID to configure
 * @param config Configuration object
 * @returns Updated plugins data
 */
export async function updatePluginConfig(teamId: string, companyId: string, pluginId: string, config: any) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!teamId || !companyId || !pluginId) {
      return { success: false, message: 'Missing required fields: teamId, companyId or pluginId', data: null };
    }

    logger.info(`Updating config for plugin ${pluginId} for team: ${teamId}, company: ${companyId}`);

    // Get current plugins configuration
    let pluginsData = await pluginsCollection.findOne({ teamId, companyId }) as PluginsData | null;

    if (!pluginsData) {
      // Create new plugins data with config if none exists
      const newPluginsData: OptionalId<PluginsData> = {
        teamId,
        companyId,
        plugins: [{
          id: pluginId,
          active: true, // Activate by default when configuring
          config
        }]
      };

      await pluginsCollection.insertOne(newPluginsData);
      logger.info(`Created new plugins data with config for team: ${teamId}, company: ${companyId}`);

      // Fetch the newly created document to get the _id field
      const createdPluginsData = await pluginsCollection.findOne({ teamId, companyId }) as unknown as PluginsData;

      return {
        success: true,
        message: 'Plugin configuration updated successfully',
        data: createdPluginsData
      };
    } else {
      // Update existing plugins data
      const existingPluginIndex = pluginsData.plugins.findIndex((p: any) => p.id === pluginId);

      if (existingPluginIndex >= 0) {
        // Update existing plugin config
        pluginsData.plugins[existingPluginIndex].config = config;
      } else {
        // Add new plugin with config
        pluginsData.plugins.push({
          id: pluginId,
          active: true, // Activate by default when configuring
          config
        });
      }

      await pluginsCollection.updateOne(
        { teamId, companyId },
        { $set: { plugins: pluginsData.plugins } }
      );

      logger.info(`Updated plugin config for team: ${teamId}, company: ${companyId}`);
      return {
        success: true,
        message: 'Plugin configuration updated successfully',
        data: pluginsData
      };
    }
  } catch (error) {
    logger.error(`Error updating plugin config: ${error}`);
    return { success: false, message: `Error updating plugin config: ${error}`, data: null };
  }
}

/**
 * Get plugin configuration for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @param pluginId Plugin ID
 * @returns Plugin configuration or null if not found
 */
export async function getPluginConfig(teamId: string, companyId: string, pluginId: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!teamId || !companyId || !pluginId) {
      return { success: false, message: 'Missing required fields: teamId, companyId or pluginId', data: null };
    }

    logger.info(`Fetching config for plugin ${pluginId} for team: ${teamId}, company: ${companyId}`);

    const pluginsData = await pluginsCollection.findOne({ teamId, companyId });

    if (!pluginsData) {
      return { success: false, message: 'No plugins data found for team', data: null };
    }

    const plugin = pluginsData.plugins.find((p: any) => p.id === pluginId);

    if (!plugin) {
      return { success: false, message: 'Plugin not found for team', data: null };
    }

    return {
      success: true,
      message: 'Plugin configuration retrieved successfully',
      data: plugin.config
    };
  } catch (error) {
    logger.error(`Error fetching plugin config: ${error}`);
    return { success: false, message: `Error fetching plugin config: ${error}`, data: null };
  }
}

/**
 * Check if a specific plugin exists for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @param pluginName Plugin name to check
 * @returns Object with success status and plugin data if found
 */
export async function checkPluginExists(teamId: string, companyId: string, pluginName: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return { success: false, message: 'Database connection not established', exists: false, plugin: null };
    }

    if (!teamId || !companyId || !pluginName) {
      return { success: false, message: 'Missing required fields: teamId, companyId or pluginName', exists: false, plugin: null };
    }

    logger.info(`Checking if plugin ${pluginName} exists for team: ${teamId}, company: ${companyId}`);

    const pluginsData = await pluginsCollection.findOne({ teamId, companyId });

    if (!pluginsData) {
      return { success: true, message: 'No plugins data found for team', exists: false, plugin: null };
    }

    const plugin = pluginsData.plugins.find((p: any) => p.id === pluginName);

    if (!plugin) {
      return { success: true, message: 'Plugin not found for team', exists: false, plugin: null };
    }

    return {
      success: true,
      message: 'Plugin found for team',
      exists: true,
      plugin: plugin
    };
  } catch (error) {
    logger.error(`Error checking plugin existence: ${error}`);
    return { success: false, message: `Error checking plugin existence: ${error}`, exists: false, plugin: null };
  }
}