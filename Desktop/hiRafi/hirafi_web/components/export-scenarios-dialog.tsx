"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/lib/utils/toast-utils"
import { Download, FileDown, Loader2 } from "lucide-react"
import { useScenarioManager } from "@/hooks/useScenarioManager"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { exportScenarios } from "@/lib/api/scenario-api"
import { useAuth } from "@/lib/api/auth"
import { isScenarioUncategorized } from "@/lib/utils/scenario-utils"

interface ExportScenariosDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedScenarios?: string[]
  scenarios?: any[]
  folders?: any[]
}

export function ExportScenariosDialog({
  open,
  onOpenChange,
  selectedScenarios = [],
  scenarios = [],
  folders = []
}: ExportScenariosDialogProps) {
  const { user } = useAuth()
  const [isExporting, setIsExporting] = useState(false)
  const [selectedFolder, setSelectedFolder] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [selectedFormat, setSelectedFormat] = useState<"csv" | "pdf">("csv")
  const [exportMode, setExportMode] = useState<"all" | "selected">(
    selectedScenarios.length > 0 ? "selected" : "all"
  )
  const [includeSteps, setIncludeSteps] = useState(true)
  const [includeDescription, setIncludeDescription] = useState(true)
  const [includeTags, setIncludeTags] = useState(true)
  const [includeStatus, setIncludeStatus] = useState(true)
  const [includeLastRun, setIncludeLastRun] = useState(false)
  const [includeDuration, setIncludeDuration] = useState(false)
  const [fileName, setFileName] = useState(`scenarios-export-${format(new Date(), "yyyy-MM-dd")}`)

  // Props'tan gelen veriler varsa onları kullan, yoksa hook'tan çek
  const {
    folders: hookFolders,
    foldersLoading,
    scenarios: hookScenarios,
    scenariosLoading
  } = useScenarioManager({
    folderLimit: 50,
    scenarioLimit: 100,
    // Don't set scenarioStatus here to avoid state updates during render
    autoFetch: folders.length === 0 && scenarios.length === 0 // Sadece props'ta veri yoksa fetch et
  })

  // Props'tan gelen veriler varsa onları kullan, yoksa hook'tan gelen verileri kullan
  const finalFolders = folders.length > 0 ? folders : hookFolders
  const finalScenarios = scenarios.length > 0 ? scenarios : hookScenarios

  // Filtrelenmiş senaryoları hesapla
  const filteredScenarios = exportMode === "selected"
    ? finalScenarios.filter(scenario => selectedScenarios.includes(scenario.id))
    : finalScenarios.filter(scenario => {
        // Handle folder filtering
        let matchesFolder = selectedFolder === "all";
        if (!matchesFolder) {
          if (selectedFolder === "uncategorized") {
            // If "uncategorized" folder is selected, use the consistent utility function
            matchesFolder = isScenarioUncategorized(scenario.folderId);
          } else {
            // Normal folder matching
            matchesFolder = scenario.folderId === selectedFolder;
          }
        }

        const matchesStatus = selectedStatus === "all" || scenario.status === selectedStatus
        return matchesFolder && matchesStatus
      })

  // Export işlemi
  const handleExport = async () => {
    setIsExporting(true)

    try {
      // API'ye gönderilecek verileri hazırla
      const exportOptions = {
        format: selectedFormat,
        folderId: exportMode === "selected" ? undefined : selectedFolder,
        status: exportMode === "selected" ? undefined : selectedStatus,
        selectedScenarioIds: exportMode === "selected" ? selectedScenarios : undefined,
        fileName: fileName || `scenarios-export-${format(new Date(), "yyyy-MM-dd")}`,
        includeFields: {
          steps: includeSteps,
          description: includeDescription,
          tags: includeTags,
          status: includeStatus,
          lastRun: includeLastRun,
          duration: includeDuration
        },
        // Kullanıcının takım ve şirket bilgilerini ekle
        teamId: user?.teamId,
        companyId: user?.companyId
      }

      // API'yi çağır
      const result = await exportScenarios(exportOptions)

      if (result.success) {
        toast.success("Export successful", {
          description: `${filteredScenarios.length} scenarios exported as ${selectedFormat.toUpperCase()}`
        })
        onOpenChange(false)
      } else {
        toast.error("Export failed", {
          description: result.error || "An error occurred while exporting scenarios"
        })
      }
    } catch (error) {
      console.error("Export error:", error)
      toast.error("Export failed", {
        description: "An error occurred while exporting scenarios"
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px] border-0 shadow-lg bg-white dark:bg-gray-900 rounded-xl">
        <DialogHeader className="pb-4 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
              <FileDown className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Export Scenarios</DialogTitle>
              <DialogDescription className="text-gray-500 dark:text-gray-400">
                Export your scenarios as CSV or PDF file with customizable options.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-5 py-5">
          {/* Export Mode Selection */}
          <div className="space-y-3">
            <Label className="text-gray-700 dark:text-gray-300 font-medium">Export Mode</Label>
            <div className="grid grid-cols-2 gap-3">
              <div
                className={`p-3 rounded-lg border cursor-pointer transition-all ${
                  exportMode === "all"
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                    : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                }`}
                onClick={() => setExportMode("all")}
              >
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={exportMode === "all"}
                    onChange={() => setExportMode("all")}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">All Scenarios</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Export based on folder/status filters</div>
                  </div>
                </div>
              </div>

              <div
                className={`p-3 rounded-lg border cursor-pointer transition-all ${
                  exportMode === "selected"
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                    : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                }`}
                onClick={() => setExportMode("selected")}
              >
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={exportMode === "selected"}
                    onChange={() => setExportMode("selected")}
                    className="text-blue-600"
                    disabled={selectedScenarios.length === 0}
                  />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">Selected Scenarios</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Export only selected scenarios ({selectedScenarios.length})
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Folder and Status filters - only show when export mode is "all" */}
          {exportMode === "all" && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="folder" className="text-gray-700 dark:text-gray-300 font-medium">Folder</Label>
              <Select
                value={selectedFolder}
                onValueChange={setSelectedFolder}
                disabled={foldersLoading}
              >
                <SelectTrigger id="folder" className="border-gray-200 dark:border-gray-700 rounded-md">
                  <SelectValue placeholder="Select folder" />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
                  <SelectItem value="all">All Folders</SelectItem>
                  <SelectItem value="uncategorized">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                      Uncategorized
                    </div>
                  </SelectItem>
                  {finalFolders.map(folderItem => (
                    <SelectItem key={folderItem.id} value={folderItem.id}>
                      <div className="flex items-center gap-2">
                        <div className={`h-2 w-2 rounded-full bg-${folderItem.color || 'gray'}-500`}></div>
                        {folderItem.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-gray-700 dark:text-gray-300 font-medium">Status</Label>
              <Select
                value={selectedStatus}
                onValueChange={setSelectedStatus}
              >
                <SelectTrigger id="status" className="border-gray-200 dark:border-gray-700 rounded-md">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-teal-500"></div>
                      Active
                    </div>
                  </SelectItem>
                  <SelectItem value="passive">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                      Passive
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="format" className="text-gray-700 dark:text-gray-300 font-medium">Export Format</Label>
              <Select
                value={selectedFormat}
                onValueChange={(value) => setSelectedFormat(value as "csv" | "pdf")}
              >
                <SelectTrigger id="format" className="border-gray-200 dark:border-gray-700 rounded-md">
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
                  <SelectItem value="csv">
                    <div className="flex items-center gap-2">
                      <div className="p-1 rounded bg-blue-100 dark:bg-blue-900/30">
                        <FileDown className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                      </div>
                      CSV File
                    </div>
                  </SelectItem>
                  <SelectItem value="pdf">
                    <div className="flex items-center gap-2">
                      <div className="p-1 rounded bg-red-100 dark:bg-red-900/30">
                        <FileDown className="h-3 w-3 text-red-600 dark:text-red-400" />
                      </div>
                      PDF Document
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fileName" className="text-gray-700 dark:text-gray-300 font-medium">File Name</Label>
              <Input
                id="fileName"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                placeholder="Enter file name without extension"
                className="border-gray-200 dark:border-gray-700 rounded-md"
              />
            </div>
          </div>

          <div className="space-y-3">
            <Label className="text-gray-700 dark:text-gray-300 font-medium">Include Fields</Label>
            <div className="grid grid-cols-3 gap-3 mt-2 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-100 dark:border-gray-800">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeSteps"
                  checked={includeSteps}
                  onCheckedChange={(checked) => setIncludeSteps(!!checked)}
                  className="border-gray-300 dark:border-gray-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
                <div className="flex items-center">
                  <label
                    htmlFor="includeSteps"
                    className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Steps
                  </label>
                  <span className="ml-2 text-xs text-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-300 px-2 py-0.5 rounded-full">
                    JSON Format
                  </span>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeDescription"
                  checked={includeDescription}
                  onCheckedChange={(checked) => setIncludeDescription(!!checked)}
                  className="border-gray-300 dark:border-gray-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
                <label
                  htmlFor="includeDescription"
                  className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Description
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeTags"
                  checked={includeTags}
                  onCheckedChange={(checked) => setIncludeTags(!!checked)}
                  className="border-gray-300 dark:border-gray-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
                <label
                  htmlFor="includeTags"
                  className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Tags
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeStatus"
                  checked={includeStatus}
                  onCheckedChange={(checked) => setIncludeStatus(!!checked)}
                  className="border-gray-300 dark:border-gray-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
                <label
                  htmlFor="includeStatus"
                  className="text-sm font-medium text-gray-700 dark:text-gray-300 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Status
                </label>
              </div>

              {/* Last Run ve Duration alanları kaldırıldı */}
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800/30 mt-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30">
                  <FileDown className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Scenarios to export
                </span>
              </div>
              <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300 border-0 px-2.5 py-0.5 rounded-full">
                {scenariosLoading ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  filteredScenarios.length
                )}
              </Badge>
            </div>
          </div>
        </div>

        <DialogFooter className="border-t border-gray-100 dark:border-gray-800 pt-4 mt-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || scenariosLoading || filteredScenarios.length === 0}
            className="gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 border-none"
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <FileDown className="h-4 w-4" />
                Export {filteredScenarios.length} Scenarios
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
