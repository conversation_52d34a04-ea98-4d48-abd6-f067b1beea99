import { Response, NextFunction } from 'express';
import { logger } from '../../utils/logger.js';
import { AuthRequest } from './authMiddleware.js';

/**
 * Company Owner authorization middleware
 * Sadece company_owner hesap tür<PERSON><PERSON> kullanıcıların erişimine izin verir
 */
export function authorizeCompanyOwner(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    // Kullanıcı bilgilerini kontrol et
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Kullanıcının accountType'ını kontrol et
    if (req.user.accountType !== 'company_owner') {
      logger.warn(`User ${req.user.id} with accountType ${req.user.accountType} attempted to access a company owner only resource`);
      return res.status(403).json({
        success: false,
        error: 'This resource is only accessible to company owners'
      });
    }

    // Yetkilendirme başarılı, sonraki middleware'e geç
    next();
  } catch (error: any) {
    logger.error(`Company owner authorization error: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Authorization error'
    });
  }
}
