#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { glob } from "glob";
import { execSync } from "child_process";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  distDir: path.join(__dirname, "../dist"),
  obfuscatedDir: path.join(__dirname, "../obfuscated"),
  // Daha basit obfuscation ayarları
  obfuscationOptions: {
    compact: true,
    controlFlowFlattening: false,
    deadCodeInjection: false,
    debugProtection: false,
    disableConsoleOutput: false,
    identifierNamesGenerator: "hexadecimal",
    numbersToExpressions: true,
    selfDefending: false,
    simplify: true,
    splitStrings: true,
    stringArray: true,
    stringArrayEncoding: ["base64"],
    stringArrayIndexShift: true,
    transformObjectKeys: false
  }
};

// Ensure directories exist
if (!fs.existsSync(config.obfuscatedDir)) {
  fs.mkdirSync(config.obfuscatedDir, { recursive: true });
}

// Step 1: Obfuscate JavaScript files using CLI tool
console.log("Obfuscating JavaScript files with simple settings...");

async function obfuscateFiles() {
  const jsFiles = await glob(`${config.distDir}/**/*.js`);

  for (const file of jsFiles) {
    // Create the same directory structure in obfuscated dir
    const relativePath = path.relative(config.distDir, file);
    const targetPath = path.join(config.obfuscatedDir, relativePath);

    // Ensure directory exists
    const targetDir = path.dirname(targetPath);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // Ensure the file exists
    if (!fs.existsSync(file)) {
      console.error(`File not found: ${file}`);
      continue;
    }

    try {
      // Run javascript-obfuscator CLI tool with config file
      const configPath = path.join(__dirname, "../obfuscator-config.json");
      execSync(`javascript-obfuscator ${file} --output ${targetPath} --config ${configPath}`);
      console.log(`Obfuscated: ${relativePath}`);
    } catch (err) {
      console.error(`Error obfuscating ${file}:`, err);
      // Copy the original file as fallback
      fs.copyFileSync(file, targetPath);
      console.log(`Copied original (obfuscation failed): ${relativePath}`);
    }
  }

  console.log("Simple obfuscation completed successfully!");
}

obfuscateFiles().catch(err => {
  console.error("Obfuscation failed:", err);
  process.exit(1);
});
