"use client"

import type React from "react"

import { useState } from "react"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { HelpCircle, Book, MessageCircle, FileQuestion, CheckCircle } from "lucide-react"
import { ProtectedRoute } from "@/components/protected-route"

function HelpPageContent() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [contactSubmitted, setContactSubmitted] = useState(false)

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Simulate form submission
    setTimeout(() => {
      setContactSubmitted(true)
    }, 1000)
  }

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5 text-blue-500" />
            <h1 className="text-2xl font-bold">Help & Support</h1>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-6xl mx-auto">
            <Tabs defaultValue="faq" className="w-full">
              <TabsList className="mb-6">
                <TabsTrigger value="faq" className="flex items-center gap-1.5">
                  <FileQuestion className="h-4 w-4" />
                  FAQ
                </TabsTrigger>
                <TabsTrigger value="documentation" className="flex items-center gap-1.5">
                  <Book className="h-4 w-4" />
                  Documentation
                </TabsTrigger>
                <TabsTrigger value="contact" className="flex items-center gap-1.5">
                  <MessageCircle className="h-4 w-4" />
                  Contact Us
                </TabsTrigger>
              </TabsList>

              <TabsContent value="faq">
                <Card>
                  <CardHeader>
                    <CardTitle>Frequently Asked Questions</CardTitle>
                    <CardDescription>Find answers to common questions about using our platform</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="item-1">
                        <AccordionTrigger>How do I create my first API test?</AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            <p>Creating your first API test is easy:</p>
                            <ol className="list-decimal pl-5 space-y-1">
                              <li>Navigate to the API Test section from the sidebar</li>
                              <li>Click on "New Test Flow" to create a new test</li>
                              <li>Drag endpoints from the left panel to your test flow</li>
                              <li>Add components to each endpoint to validate responses</li>
                              <li>Click "Run Test Flow" to execute your test</li>
                            </ol>
                            <p>You can also check our detailed documentation for step-by-step guides.</p>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="item-2">
                        <AccordionTrigger>What components are available for API testing?</AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            <p>We offer a variety of components for comprehensive API testing:</p>
                            <ul className="list-disc pl-5 space-y-1">
                              <li>
                                <strong>Status Code Check:</strong> Verify HTTP status codes
                              </li>
                              <li>
                                <strong>JSON Path Extractor:</strong> Extract and validate JSON values
                              </li>
                              <li>
                                <strong>Response Assertion:</strong> Write custom validation logic
                              </li>
                              <li>
                                <strong>Variable Setter:</strong> Store values for use in subsequent requests
                              </li>
                              <li>
                                <strong>Header Validator:</strong> Check response headers
                              </li>
                              <li>
                                <strong>Response Logger:</strong> Log information during test execution
                              </li>
                              <li>
                                <strong>Custom Script:</strong> Execute JavaScript for advanced scenarios
                              </li>
                              <li>
                                <strong>Delay:</strong> Add pauses between test steps
                              </li>
                            </ul>
                            <p>Each component has detailed configuration options to suit your testing needs.</p>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="item-3">
                        <AccordionTrigger>How do I use variables across multiple API requests?</AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            <p>To use variables across multiple requests:</p>
                            <ol className="list-decimal pl-5 space-y-1">
                              <li>Add a "Variable Setter" component to the endpoint that returns the value you need</li>
                              <li>
                                Configure the component with a name, JSONPath to extract the value, and appropriate
                                scope:
                                <ul className="list-disc pl-5 mt-1">
                                  <li>
                                    <strong>Request scope:</strong> Available only in the current request
                                  </li>
                                  <li>
                                    <strong>Test scope:</strong> Available throughout the current test flow
                                  </li>
                                  <li>
                                    <strong>Global scope:</strong> Available across all test flows
                                  </li>
                                </ul>
                              </li>
                              <li>
                                Reference the variable in subsequent requests using the syntax{" "}
                                <code>{"{{variableName}}"}</code> in URLs, headers, or body
                              </li>
                            </ol>
                            <p>
                              Example: If you extract an auth token with the name "authToken", you can use it in a
                              header as <code>{"{{authToken}}"}</code>
                            </p>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="item-4">
                        <AccordionTrigger>How do I validate complex JSON responses?</AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            <p>For complex JSON validation, you have several options:</p>
                            <ol className="list-decimal pl-5 space-y-1">
                              <li>
                                <strong>JSON Path Extractor:</strong> Use JSONPath syntax to target specific elements in
                                the response
                              </li>
                              <li>
                                <strong>Response Assertion:</strong> Write JavaScript expressions for custom validation
                                logic
                              </li>
                              <li>
                                <strong>Custom Script:</strong> For the most complex scenarios, write JavaScript code to
                                perform detailed validation
                              </li>
                            </ol>
                            <p>
                              The JSON Helper tool can assist you in exploring the response structure and generating
                              JSONPath expressions.
                            </p>
                            <p>Example JSONPath expressions:</p>
                            <ul className="list-disc pl-5 space-y-1">
                              <li>
                                <code>$.data.items[*].id</code> - All IDs in an items array
                              </li>
                              <li>
                                <code>$.data.items[?(@.price {">"} 50)]</code> - Items with price greater than 50
                              </li>
                              <li>
                                <code>$.data.items.length()</code> - Count of items in the array
                              </li>
                            </ul>
                          </div>
                        </AccordionContent>
                      </AccordionItem>

                      <AccordionItem value="item-5">
                        <AccordionTrigger>Can I schedule API tests to run automatically?</AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            <p>Yes, you can schedule API tests to run automatically:</p>
                            <ol className="list-decimal pl-5 space-y-1">
                              <li>Navigate to the Schedule section in the sidebar</li>
                              <li>Click "New Schedule" to create a new schedule</li>
                              <li>Select the test flow you want to run</li>
                              <li>Configure the schedule frequency (hourly, daily, weekly, etc.)</li>
                              <li>Set notification preferences for test results</li>
                              <li>Save the schedule</li>
                            </ol>
                            <p>You can view and manage all your scheduled tests from the Schedule dashboard.</p>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="documentation">
                <Card>
                  <CardHeader>
                    <CardTitle>Documentation</CardTitle>
                    <CardDescription>Comprehensive guides and reference materials for our platform</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <Card className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center gap-2">
                            <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full mb-2">
                              <Book className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <h3 className="font-medium">Getting Started Guide</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Learn the basics of our platform and create your first test
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center gap-2">
                            <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full mb-2">
                              <Book className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                            </div>
                            <h3 className="font-medium">API Testing Guide</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Detailed documentation on API testing features
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center gap-2">
                            <div className="bg-emerald-100 dark:bg-emerald-900/30 p-3 rounded-full mb-2">
                              <Book className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <h3 className="font-medium">Component Reference</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Complete reference for all available test components
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center gap-2">
                            <div className="bg-amber-100 dark:bg-amber-900/30 p-3 rounded-full mb-2">
                              <Book className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                            </div>
                            <h3 className="font-medium">Advanced Techniques</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Advanced testing strategies and best practices
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center gap-2">
                            <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-full mb-2">
                              <Book className="h-6 w-6 text-red-600 dark:text-red-400" />
                            </div>
                            <h3 className="font-medium">Integrations Guide</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              How to integrate with other tools and services
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center gap-2">
                            <div className="bg-indigo-100 dark:bg-indigo-900/30 p-3 rounded-full mb-2">
                              <Book className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                            </div>
                            <h3 className="font-medium">API Reference</h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Technical reference for our platform's API
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contact">
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Support</CardTitle>
                    <CardDescription>Get in touch with our support team for assistance</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {contactSubmitted ? (
                      <div className="flex flex-col items-center justify-center py-8 text-center">
                        <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-full mb-4">
                          <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                        </div>
                        <h3 className="text-xl font-medium mb-2">Message Sent Successfully</h3>
                        <p className="text-gray-500 dark:text-gray-400 max-w-md">
                          Thank you for contacting us. Our support team will get back to you within 24 hours.
                        </p>
                        <Button className="mt-6" onClick={() => setContactSubmitted(false)}>
                          Send Another Message
                        </Button>
                      </div>
                    ) : (
                      <form onSubmit={handleContactSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label htmlFor="name">Name</Label>
                            <Input id="name" placeholder="Your name" required />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="email">Email</Label>
                            <Input id="email" type="email" placeholder="<EMAIL>" required />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="subject">Subject</Label>
                          <Select defaultValue="general">
                            <SelectTrigger>
                              <SelectValue placeholder="Select a subject" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="general">General Inquiry</SelectItem>
                              <SelectItem value="technical">Technical Support</SelectItem>
                              <SelectItem value="billing">Billing Question</SelectItem>
                              <SelectItem value="feature">Feature Request</SelectItem>
                              <SelectItem value="bug">Bug Report</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="message">Message</Label>
                          <Textarea
                            id="message"
                            placeholder="Please describe your issue or question in detail..."
                            className="min-h-[150px]"
                            required
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button type="submit">Submit Support Request</Button>
                        </div>
                      </form>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  )
}

export default function HelpPage() {
  return (
    <ProtectedRoute>
      <HelpPageContent />
    </ProtectedRoute>
  )
}

