/**
 * Manual Race Condition Verification Script
 * 
 * This script manually tests the race condition fixes in the StateManager
 * to verify that the implementation works correctly.
 */

import { StateManager } from './state-manager.js';
import { TestExecutionState } from '../../node-manager/types/node-manager-types.js';
import type { StateManagerConfig } from '../interfaces/state-manager.interface.js';

async function testRaceConditionFix() {
  console.log('🔧 Starting Race Condition Verification Tests...\n');

  const stateManager = new StateManager();
  const config: StateManagerConfig = {
    nodeId: 'verification-node',
    enableStateLogging: true,
    transitionTimeout: 5000
  };

  try {
    await stateManager.initialize(config);
    console.log('✅ StateManager initialized successfully');

    // Test 1: Verify concurrent transitions are properly serialized
    console.log('\n📋 Test 2: Concurrent Transition Serialization');
    const concurrentPromises = [];
    const results: any[] = [];

    for (let i = 0; i < 10; i++) {
      const promise = stateManager.tryTransitionState(
        TestExecutionState.IDLE,
        TestExecutionState.PREPARING,
        `test-${i}`
      ).then(result => {
        results.push({ testId: `test-${i}`, success: result.success });
        return result;
      });
      concurrentPromises.push(promise);
    }

    await Promise.all(concurrentPromises);
    
    const successfulTransitions = results.filter(r => r.success);
    if (successfulTransitions.length === 1) {
      console.log('✅ Concurrent transitions properly serialized - only 1 succeeded');
      console.log(`   Winner: ${successfulTransitions[0].testId}`);
    } else {
      console.log(`❌ Race condition detected - ${successfulTransitions.length} transitions succeeded`);
    }

    // Reset state for next test
    await stateManager.forceTransitionState(TestExecutionState.IDLE, undefined, 'Reset for next test');

    // Test 3: Verify force transitions get priority
    console.log('\n📋 Test 3: Force Transition Priority');
    const normalPromises = [];
    for (let i = 0; i < 5; i++) {
      normalPromises.push(
        stateManager.tryTransitionState(
          TestExecutionState.IDLE,
          TestExecutionState.PREPARING,
          `normal-${i}`
        )
      );
    }

    // Add force transition after normal ones
    const forcePromise = stateManager.forceTransitionState(
      TestExecutionState.RUNNING,
      'force-test',
      'Priority test'
    );

    const [forceResult, ...normalResults] = await Promise.all([forcePromise, ...normalPromises]);
    
    if (forceResult.success && stateManager.getCurrentState() === TestExecutionState.RUNNING) {
      console.log('✅ Force transition executed with priority');
    } else {
      console.log('❌ Force transition priority failed');
    }

    // Test 4: Verify state consistency
    console.log('\n📋 Test 4: State Consistency Check');
    const currentState = stateManager.getCurrentState();
    const currentTestId = stateManager.getCurrentTestId();
    
    console.log(`   Current State: ${currentState}`);
    console.log(`   Current Test ID: ${currentTestId}`);
    
    if (currentState === TestExecutionState.RUNNING && currentTestId === 'force-test') {
      console.log('✅ State consistency maintained');
    } else {
      console.log('❌ State consistency check failed');
    }

    // Test 5: Verify concurrent test prevention
    console.log('\n📋 Test 5: Concurrent Test Prevention');
    const result1 = await stateManager.tryTransitionState(
      TestExecutionState.RUNNING,
      TestExecutionState.IDLE
    );
    
    const result2 = await stateManager.tryTransitionState(
      TestExecutionState.IDLE,
      TestExecutionState.PREPARING,
      'test-1'
    );
    
    const result3 = await stateManager.tryTransitionState(
      TestExecutionState.PREPARING,
      TestExecutionState.RUNNING,
      'test-2' // Different test ID
    );
    
    if (result2.success && !result3.success && result3.message?.includes('another test')) {
      console.log('✅ Concurrent test prevention working correctly');
    } else {
      console.log('❌ Concurrent test prevention failed');
    }

    console.log('\n🎉 Race Condition Verification Tests Completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    await stateManager.dispose();
    console.log('🧹 StateManager disposed');
  }
}

// Run the verification if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testRaceConditionFix().catch(console.error);
}

export { testRaceConditionFix };
