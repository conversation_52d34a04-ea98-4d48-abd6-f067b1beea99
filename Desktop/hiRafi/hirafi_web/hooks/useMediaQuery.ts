"use client"

import { useEffect, useState } from "react"

/**
 * Custom hook for media query matching
 * @param query The media query string to match against
 * @returns boolean indicating if the media query matches
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(false)

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    const media = window.matchMedia(query)

    // Initial check
    setMatches(media.matches)

    // Update matches when the media query changes
    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    // Add event listener
    media.addEventListener("change", listener)

    // Cleanup
    return () => {
      media.removeEventListener("change", listener)
    }
  }, [query])

  return matches
}

/**
 * Hook to detect if the current viewport is mobile size
 * Uses 768px as the mobile breakpoint
 * @returns boolean indicating if viewport is mobile size
 */
export function useIsMobile(): boolean {
  const MOBILE_BREAKPOINT = 768
  return useMediaQuery(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
}

/**
 * Hook to detect if the current viewport is tablet size
 * @returns boolean indicating if viewport is tablet size (768px - 1024px)
 */
export function useIsTablet(): boolean {
  return useMediaQuery("(min-width: 768px) and (max-width: 1024px)")
}

/**
 * Hook to detect if the current viewport is desktop size
 * @returns boolean indicating if viewport is desktop size (>1024px)
 */
export function useIsDesktop(): boolean {
  return useMediaQuery("(min-width: 1025px)")
}

/**
 * Hook that returns the current breakpoint name
 * @returns 'mobile' | 'tablet' | 'desktop'
 */
export function useBreakpoint(): 'mobile' | 'tablet' | 'desktop' {
  const isMobile = useIsMobile()
  const isTablet = useIsTablet()

  if (isMobile) return 'mobile'
  if (isTablet) return 'tablet'
  return 'desktop'
}
