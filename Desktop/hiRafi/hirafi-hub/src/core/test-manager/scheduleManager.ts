/**
 * Schedule Manager
 * Manages scheduled test runs
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import {
  getPendingSchedules,
  updateNextRunTime,
  createScheduleRun
} from '../../services/mongo/scheduleService.js';
import { getRunById } from '../../services/mongo/runService.js';
import { v4 as uuidv4 } from 'uuid';
import { testManager } from './index.js';
import { sendScheduledRunNotification } from '../../services/notificationService.js';

class ScheduleManager extends EventEmitter {
  private static instance: ScheduleManager;
  private checkInterval: number = 60000; // Check every minute
  private isProcessing: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    logger.info('ScheduleManager: Initializing');
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): ScheduleManager {
    if (!ScheduleManager.instance) {
      ScheduleManager.instance = new ScheduleManager();
    }
    return ScheduleManager.instance;
  }

  /**
   * Start the schedule manager
   */
  public start(): void {
    logger.info('ScheduleManager: Starting schedule manager');

    // Clear any existing interval
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    // Start checking for schedules to run
    this.intervalId = setInterval(() => this.checkSchedules(), this.checkInterval);

    // Do an initial check
    this.checkSchedules();
  }

  /**
   * Stop the schedule manager
   */
  public stop(): void {
    logger.info('ScheduleManager: Stopping schedule manager');

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Check for schedules that need to be executed
   */
  private async checkSchedules(): Promise<void> {
    // Prevent concurrent processing
    if (this.isProcessing) {
      logger.debug('ScheduleManager: Already processing schedules, skipping this check');
      return;
    }

    this.isProcessing = true;

    try {
      const now = new Date();
      logger.debug(`ScheduleManager: Checking for pending schedules at ${now.toISOString()}`);

      // Get pending schedules
      const result = await getPendingSchedules();

      if (!result.success || !result.schedules || result.schedules.length === 0) {
        logger.debug('ScheduleManager: No pending schedules found');
        this.isProcessing = false;
        return;
      }



      // Process each schedule
      for (const schedule of result.schedules) {
        try {
          logger.info(`ScheduleManager: Processing schedule ${schedule.id} (${schedule.name}), Type: ${schedule.scheduleType}, NextRunAt: ${schedule.nextRunAt?.toISOString()}`);

          // Execute the schedule
          await this.executeSchedule(schedule);

          // Update next run time
          logger.info(`ScheduleManager: Updating next run time for schedule ${schedule.id}`);
          const updateResult = await updateNextRunTime(schedule.id);

          if (updateResult.success) {

          } else {
            logger.error(`ScheduleManager: Failed to update next run time for schedule ${schedule.id}: ${updateResult.message}`);
          }
        } catch (error: any) {
          logger.error(`ScheduleManager: Error processing schedule ${schedule.id}: ${error.message}`, error);

          // Still try to update the next run time even if execution failed
          try {
            await updateNextRunTime(schedule.id);
          } catch (updateError: any) {
            logger.error(`ScheduleManager: Error updating next run time for schedule ${schedule.id} after execution failure: ${updateError.message}`, updateError);
          }
        }
      }
    } catch (error: any) {
      logger.error(`ScheduleManager: Error checking schedules: ${error.message}`, error);
    } finally {
      this.isProcessing = false;
      logger.debug('ScheduleManager: Finished processing schedules');
    }
  }

  /**
   * Execute a scheduled run
   */
  private async executeSchedule(schedule: any): Promise<void> {
    logger.info(`ScheduleManager: Executing schedule ${schedule.id} - ${schedule.name}`);

    try {
      // Check if schedule has runIds array
      if (!schedule.runIds || !Array.isArray(schedule.runIds) || schedule.runIds.length === 0) {
        logger.error(`ScheduleManager: No runIds found for schedule ${schedule.id}`);
        return;
      }

      // Use the first run ID from the array
      const runId = schedule.runIds[0];
      logger.info(`ScheduleManager: Using run ID ${runId} from schedule ${schedule.id}`);

      // Get run details
      const runResult = await getRunById(runId);

      if (!runResult.success || !runResult.run) {
        logger.error(`ScheduleManager: Run ${runId} not found for schedule ${schedule.id}`);
        return;
      }

      const run = runResult.run;


      // Generate execution ID for this run
      const executionId = uuidv4();
      logger.info(`ScheduleManager: Generated execution ID ${executionId} for run ${run.id}`);

      // Create schedule run record with execution ID
      logger.info(`ScheduleManager: Creating schedule run record for schedule ${schedule.id} and run ${run.id}`);
      const scheduleRunResult = await createScheduleRun(schedule.id, run.id, executionId);

      if (!scheduleRunResult.success) {
        logger.error(`ScheduleManager: Failed to create schedule run record: ${scheduleRunResult.message}`);
        return;
      }

      const scheduleRunId = scheduleRunResult.scheduleRunId;
      logger.info(`ScheduleManager: Created schedule run with ID ${scheduleRunId} linked to execution ${executionId}`);

      // Prepare execution options with company and team IDs from the schedule
      const executionOptions = {
        userId: schedule.userId,
        executionId: executionId,
        isScheduled: true,
        scheduleId: schedule.id,
        scheduleRunId: scheduleRunId,
        // Include company and team IDs from the schedule for atomic reports
        companyId: schedule.companyId,
        teamId: schedule.teamId
      };

      logger.info(`ScheduleManager: Executing run ${run.id} with options: ${JSON.stringify(executionOptions)}`);

      // Execute the run
      const result = await testManager.executeRun(run.id, executionOptions);

      if (!result.success) {
        logger.error(`ScheduleManager: Failed to execute run ${run.id}: ${result.message}`);
        return;
      }



      // The run is now queued and will be processed by the test manager
      // The test manager will update the run status as tests complete

      // We'll handle completion in the test manager's run completion event
      // Get event listeners from the atomic report service
      const { runStatusEvents } = await import('../../services/mongo/atomicReportService.js');

      // Create a completion handler for both event systems
      const completionHandler = async (runResult: any) => {
        logger.info(`ScheduleManager: Run ${run.id} completed for schedule ${schedule.id} with result: ${JSON.stringify(runResult)}`);

        // Send notifications if configured
        if (schedule.notifications) {
          logger.info(`ScheduleManager: Sending notifications for completed run ${run.id}`);
          this.sendNotifications(schedule, runResult);
        } else {
          logger.info(`ScheduleManager: Notifications not configured for schedule ${schedule.id}, skipping`);
        }
      };

      // Register event handlers for both old and new event systems
      logger.info(`ScheduleManager: Registering completion handlers for run ${run.id} with executionId ${executionId}`);

      // New method (runStatusEvents from atomic report service)
      runStatusEvents.once(`run:${run.id}:${executionId}:completed`, completionHandler);
      logger.info(`ScheduleManager: Registered atomic report completion handler for run ${run.id}`);

      // Old method (testManager events) - keep for backward compatibility
      testManager.once(`run:${run.id}:${executionId}:completed`, completionHandler);
      logger.info(`ScheduleManager: Registered legacy completion handler for run ${run.id}`);
    } catch (error: any) {
      logger.error(`ScheduleManager: Error executing schedule ${schedule.id}: ${error.message}`, error);
    }
  }

  /**
   * Send notifications for completed runs
   */
  private async sendNotifications(schedule: any, runResult: any): Promise<void> {
    logger.info(`ScheduleManager: Sending notifications for schedule ${schedule.id} - ${schedule.name}`);

    try {
      const { notifications } = schedule;

      if (!notifications) {
        logger.info(`ScheduleManager: No notification settings found for schedule ${schedule.id}, skipping notifications`);
        return;
      }

      // Log notification settings
      logger.info(`ScheduleManager: Notification settings for schedule ${schedule.id}:
        Email: ${notifications.email ? 'Enabled' : 'Disabled'}
        Slack: ${notifications.slack ? 'Enabled' : 'Disabled'}
        In-App: ${notifications.inApp ? 'Enabled' : 'Disabled'}
        On Success: ${notifications.onSuccess ? 'Enabled' : 'Disabled'}
        On Failure: ${notifications.onFailure ? 'Enabled' : 'Disabled'}
        Email Recipients: ${schedule.emailRecipients || 'None'}
        Slack Channel: ${schedule.slackChannel || 'None'}
      `);

      // Determine if we should send notification based on success/failure settings
      const shouldSend =
        (runResult.success && notifications.onSuccess) ||
        (!runResult.success && notifications.onFailure);

      if (!shouldSend) {
        logger.info(`ScheduleManager: Skipping notifications for schedule ${schedule.id} based on success/failure settings (success=${runResult.success})`);
        return;
      }

      // Prepare notification data
      const runIds = Array.isArray(schedule.runIds) ? schedule.runIds : [schedule.runIds];

      // For atomic reports, use the report URL format instead of the run ID
      const baseUrl = process.env.WEB_APP_URL || 'https://hirafi.ai';

      // Generate report URLs for atomic reports
      // Format: /run-reports/{runId} instead of /reports/{runId}
      const reportUrls = runIds.map((id: string) => `${baseUrl}/run-reports/${id}`);

      logger.info(`ScheduleManager: Preparing to send notifications for ${runIds.length} run(s) with report URLs: ${reportUrls.join(', ')}`);

      // Send notifications using the notification service
      const notificationResult = await sendScheduledRunNotification(
        schedule.name,
        runIds,
        runResult.success,
        reportUrls,
        notifications.email ? schedule.emailRecipients : undefined,
        notifications.slack ? schedule.slackChannel : undefined,
        // Add company and team IDs for proper notification routing
        schedule.companyId,
        schedule.teamId
      );

      // Log notification results
      if (notifications.email) {
        if (notificationResult.emailSent) {
          logger.info(`ScheduleManager: Email notification successfully sent to ${schedule.emailRecipients}`);
        } else {
          logger.warn(`ScheduleManager: Failed to send email notification to ${schedule.emailRecipients}`);
        }
      }

      if (notifications.slack) {
        if (notificationResult.slackSent) {
          logger.info(`ScheduleManager: Slack notification successfully sent to channel ${schedule.slackChannel}`);
        } else {
          logger.warn(`ScheduleManager: Failed to send Slack notification to channel ${schedule.slackChannel}`);
        }
      }

      // In-app notifications are handled by the frontend
      if (notifications.inApp) {
        logger.info(`ScheduleManager: In-app notifications will be handled by the frontend`);
      }
    } catch (error: any) {
      logger.error(`ScheduleManager: Error sending notifications for schedule ${schedule.id}: ${error.message}`, error);
    }
  }
}

// Export singleton instance
export const scheduleManager = ScheduleManager.getInstance();
