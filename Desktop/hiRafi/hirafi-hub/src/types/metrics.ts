/**
 * Type definitions for metrics processing and storage
 * 
 * This file contains all the type definitions related to metrics collection,
 * processing, and storage to ensure type safety across the application.
 */

/**
 * Web Vitals metrics structure
 */
export interface WebVitalsMetrics {
  lcp?: number | undefined;
  cls?: number | undefined;
  fid?: number | undefined;
  ttfb?: number | undefined;
  inp?: number | undefined;
  fcp?: number | undefined;
  [key: string]: number | undefined;
}

/**
 * Page performance metrics structure
 */
export interface PageMetrics {
  documents?: number | undefined;
  nodes?: number | undefined;
  jsHeapUsedSize?: number | undefined;
  jsHeapTotalSize?: number | undefined;
  scriptDuration?: number | undefined;
  layoutCount?: number | undefined;
  recalcStyleCount?: number | undefined;
  [key: string]: number | undefined;
}

/**
 * Network data metrics structure
 */
export interface NetworkDataMetrics {
  requests?: {
    total?: number | undefined;
    successful?: number | undefined;
    failed?: number | undefined;
    byType?: Record<string, number>;
  };
  transferred?: {
    total?: number | undefined;
    unit?: string;
  };
  logs?: any[];
}

/**
 * Accessibility data metrics structure
 */
export interface AccessibilityDataMetrics {
  violations?: {
    count?: number | undefined;
    items?: any[];
    byType?: Record<string, number>;
    bySeverity?: Record<string, number>;
  };
  passes?: any[];
  summary?: {
    totalElements?: number | undefined;
    accessibilityScore?: number | undefined;
    [key: string]: any;
  };
}

/**
 * Collection performance metrics structure
 */
export interface CollectionPerformanceMetrics {
  totalTime?: number | undefined;
  pageMetricsCollection?: number | undefined;
  networkDataCollection?: number | undefined;
  accessibilityDataCollection?: number | undefined;
  [key: string]: number | undefined;
}

/**
 * Processed metrics structure for consistent storage and retrieval
 */
export interface ProcessedMetrics {
  webVitals?: WebVitalsMetrics;
  pageMetrics?: PageMetrics;
  networkData?: NetworkDataMetrics;
  accessibilityData?: AccessibilityDataMetrics;
  performanceMetrics?: {
    [key: string]: any;
  };
  tracingData?: {
    [key: string]: any;
  };
  timestamp?: string;
  collectionPerformance?: CollectionPerformanceMetrics;
}

/**
 * Raw metrics input structure (what we receive from test nodes)
 * Simplified to avoid race conditions and duplicate field handling
 */
export interface RawMetricsInput {
  // Use any for raw input since we validate and transform in centralResultProcessor
  [key: string]: any;
}

/**
 * Type guard to check if a value is a valid number
 */
export function isValidNumber(value: any): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

/**
 * Type guard to check if a value can be converted to a valid number
 */
export function isConvertibleToNumber(value: any): boolean {
  if (value === null || value === undefined) {
    return false;
  }
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return isValidNumber(numValue);
}
