/**
 * Device Provider Registry Interface
 * Defines the contract for device provider registration and management
 */

import { IDeviceManager } from '../device-manager.interface.js';
import { ProviderConfiguration } from '../services/device-provider-config.service.js';

/**
 * Device provider factory function type
 */
export type DeviceProviderFactory = (
  config: ProviderConfiguration,
  dependencies: DeviceProviderDependencies
) => IDeviceManager;

/**
 * Dependencies required for device provider creation
 */
export interface DeviceProviderDependencies {
  logger: import('../../../../utils/logger-service.js').ILoggerService;
  utils?: import('../shared/device-manager-utils.js').DeviceManagerUtils;
}

/**
 * Device provider registration information
 */
export interface DeviceProviderRegistration {
  name: string;
  factory: DeviceProviderFactory;
  supportedCapabilities: string[];
  description?: string;
}

/**
 * Device Provider Registry Interface
 * Manages registration and creation of device providers
 */
export interface IDeviceProviderRegistry {
  /**
   * Register a device provider
   * @param registration Provider registration information
   */
  registerProvider(registration: DeviceProviderRegistration): void;

  /**
   * Unregister a device provider
   * @param name Provider name
   */
  unregisterProvider(name: string): void;

  /**
   * Check if a provider is registered
   * @param name Provider name
   * @returns True if provider is registered
   */
  isProviderRegistered(name: string): boolean;

  /**
   * Get all registered provider names
   * @returns Array of provider names
   */
  getRegisteredProviders(): string[];

  /**
   * Create a device manager for the specified provider
   * @param providerName Provider name
   * @param config Provider configuration
   * @param dependencies Required dependencies
   * @returns Device manager instance
   */
  createDeviceManager(
    providerName: string,
    config: ProviderConfiguration,
    dependencies: DeviceProviderDependencies
  ): IDeviceManager;

  /**
   * Get provider registration information
   * @param name Provider name
   * @returns Provider registration or undefined
   */
  getProviderRegistration(name: string): DeviceProviderRegistration | undefined;

  /**
   * Validate provider configuration
   * @param providerName Provider name
   * @param config Configuration to validate
   * @returns Validation result
   */
  validateProviderConfig(providerName: string, config: ProviderConfiguration): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  };
}
