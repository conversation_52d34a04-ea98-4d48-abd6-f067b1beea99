/**
 * AI Log Formatter
 * Utility functions for formatting AI operation results for logs
 */

import { logger } from './logger.js';

/**
 * AI Task Result Interface
 * Represents the result of an AI operation
 */
export interface AITaskResult<T = any> {
  /** The actual result of the operation */
  result: T;

  /** <PERSON><PERSON><PERSON> about the task execution */
  metadata: {
    /** Status of the task (pending, running, finished, failed, cancelled) */
    status?: string;

    /** Timestamp when the task started */
    start?: number;

    /** Timestamp when the task ended */
    end?: number;

    /** Total time taken to execute the task in milliseconds */
    totalTime?: number;

    /** Cache information */
    cache?: { hit: boolean };

    /** Token usage information */
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
      [key: string]: any;
    };

    /** AI's thought process */
    thought?: string;

    /** Element location information */
    locate?: any;

    /** Action plans */
    plan?: any;
  };
}

/**
 * AI Operation Log Entry
 * Standardized format for AI operation logs
 */
export interface AIOperationLogEntry {
  /** Type of AI operation (aiAction, aiAssertion, aiQuery, aiWaitFor, aiBoolean, aiKeyboardPress, aiHover, aiScroll, aiTap, aiInput, aiRightClick, aiNumber, aiString, aiLocate) */
  operation: string;
  
  /** Description of the operation */
  description?: string;
  
  /** Whether the operation was successful */
  success: boolean;
  
  /** Error message if the operation failed */
  error?: string;
  
  /** Additional metadata about the operation */
  metadata: {
    /** Status of the operation */
    status?: string;
    
    /** Duration of the operation in milliseconds */
    duration?: number;
    
    /** Token usage information */
    tokenUsage?: {
      prompt_tokens?: number;
      completion_tokens?: number;
      total_tokens?: number;
    };
    
    /** AI's thought process */
    thought?: string;
    
    /** Element location information */
    locate?: any;
    
    /** Whether the operation was cached */
    cacheHit?: boolean;
    
    /** For assertions, whether the assertion passed */
    pass?: boolean;
    
    /** Action plans */
    plan?: any;
    
    /** Additional details about the operation */
    [key: string]: any;
  };
}

/**
 * Format AI result for logging
 * Converts an AI task result to a standardized log format
 * 
 * @param operation Type of AI operation (aiAction, aiAssertion, aiQuery, aiWaitFor, aiBoolean, aiKeyboardPress, aiHover, aiScroll, aiTap, aiInput, aiRightClick, aiNumber, aiString, aiLocate)
 * @param description Description of the operation
 * @param result Result of the AI operation
 * @returns Formatted log entry
 */
export function formatAIResultForLog(operation: string, description?: string, result?: AITaskResult<any>): AIOperationLogEntry {
  try {
    // Extract thought from either metadata or result object
    let thought = result?.metadata?.thought;

    // If thought is not in metadata, check if it's in the result object
    if (!thought && typeof result?.result === 'object' && result?.result?.thought) {
      thought = result.result.thought;
    }

    // Replace midscene/midscenejs references with "hirafi"
    if (thought) {
      thought = thought.replace(/midscene(js)?/gi, 'hirafi');
    }

    // Extract token usage from either metadata or result object
    let tokenUsage = result?.metadata?.usage;

    // If token usage is not in metadata, check if it's in the result object
    if (!tokenUsage && typeof result?.result === 'object' && result?.result?.usage) {
      tokenUsage = result.result.usage;
    }

    // Extract cache hit information
    const cacheHit = result?.metadata?.cache?.hit === true;

    // Replace midscene/midscenejs references in other metadata fields
    let locate = result?.metadata?.locate;
    if (locate && typeof locate === 'string') {
      locate = locate.replace(/midscene(js)?/gi, 'hirafi');
    }

    let plan = result?.metadata?.plan;
    if (plan) {
      if (typeof plan === 'string') {
        plan = plan.replace(/midscene(js)?/gi, 'hirafi');
      } else if (Array.isArray(plan)) {
        // If plan is an array, check each item
        plan = plan.map(item => {
          if (typeof item === 'string') {
            return item.replace(/midscene(js)?/gi, 'hirafi');
          } else if (typeof item === 'object' && item !== null) {
            // If the item is an object, check the description field
            if (item.description && typeof item.description === 'string') {
              item.description = item.description.replace(/midscene(js)?/gi, 'hirafi');
            }
          }
          return item;
        });
      }
    }

    // Create the base log entry
    const logEntry: AIOperationLogEntry = {
      operation,
      description,
      success: true, // Default to true, will be updated based on operation type
      metadata: {
        status: result?.metadata?.status || "finished",
        duration: result?.metadata?.totalTime,
        tokenUsage: tokenUsage,
        thought: thought,
        locate: locate,
        plan: plan,
        cacheHit: cacheHit
      }
    };
    
    // Handle operation-specific logic
    if (operation === 'aiAssertion') {
      // For assertions, check if the result is a boolean or an object with a pass property
      const isPassed = typeof result?.result === 'object' ?
                      result.result?.pass === true :
                      result?.result === true;
      
      logEntry.success = isPassed;
      logEntry.metadata.pass = isPassed;
    }
    
    return logEntry;
  } catch (error) {
    // Log the error but don't throw, as this is a utility function
    logger.error(`Error formatting AI result for log: ${error instanceof Error ? error.message : String(error)}`);
    
    // Return a basic log entry with error information
    return {
      operation,
      description,
      success: false,
      error: `Error formatting log: ${error instanceof Error ? error.message : String(error)}`,
      metadata: {
        status: 'error'
      }
    };
  }
}
