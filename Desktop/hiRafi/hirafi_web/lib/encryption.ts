/**
 * Simple encryption utilities for test data
 * Note: This is for demonstration purposes only
 * In production, use proper encryption libraries
 */

const ENCRYPTION_PREFIX = "ENC:"

/**
 * Simple base64 encoding for demonstration
 * In production, use proper encryption
 */
export function encryptValue(value: string): string {
  if (!value) return value
  
  try {
    const encoded = btoa(value)
    return `${ENCRYPTION_PREFIX}${encoded}`
  } catch (error) {
    console.error("Encryption error:", error)
    return value
  }
}

/**
 * Simple base64 decoding for demonstration
 * In production, use proper decryption
 */
export function decryptValue(encryptedValue: string): string {
  if (!encryptedValue || !isEncrypted(encryptedValue)) {
    return encryptedValue
  }
  
  try {
    const encoded = encryptedValue.replace(ENCRYPTION_PREFIX, "")
    return atob(encoded)
  } catch (error) {
    console.error("Decryption error:", error)
    return encryptedValue
  }
}

/**
 * Check if a value is encrypted
 */
export function isEncrypted(value: string): boolean {
  return typeof value === "string" && value.startsWith(ENCRYPTION_PREFIX)
}

/**
 * Toggle encryption status of a value
 */
export function toggleEncryption(value: string): string {
  if (isEncrypted(value)) {
    return decryptValue(value)
  } else {
    return encryptValue(value)
  }
}
