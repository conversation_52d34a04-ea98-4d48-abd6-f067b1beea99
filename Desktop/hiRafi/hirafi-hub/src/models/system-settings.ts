/**
 * System Settings Model
 * Defines the data structure for system-wide settings
 */

import { ObjectId } from 'mongodb';

/**
 * System setting types
 */
export enum SystemSettingType {
  SMTP = 'smtp',
  SLACK = 'slack',
  AI_MODEL = 'ai_model',
  LANDING_PAGE = 'landing_page'
}

/**
 * Base system setting interface
 */
export interface SystemSetting {
  _id?: ObjectId;
  id: string;
  type: SystemSettingType;
  name: string;
  config: any;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * SMTP configuration interface
 */
export interface SMTPConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: string;
  fromName: string;
}

/**
 * SMTP system setting
 */
export interface SMTPSetting extends SystemSetting {
  type: SystemSettingType.SMTP;
  config: SMTPConfig;
}

/**
 * Slack configuration interface
 */
export interface SlackConfig {
  botToken: string;
  signingSecret: string;
  defaultChannel: string;
}

/**
 * Slack system setting
 */
export interface SlackSetting extends SystemSetting {
  type: SystemSettingType.SLACK;
  config: SlackConfig;
}

/**
 * AI Model configuration interface
 */
export interface AIModelConfig {
  name: string;
  api: string;
  apiKey: string;
  isActive: boolean;
  supportsImageProcessing: boolean;
}

/**
 * AI Model system setting
 */
export interface AIModelSetting extends SystemSetting {
  type: SystemSettingType.AI_MODEL;
  config: AIModelConfig;
}

/**
 * Landing Page configuration interface
 */
export interface LandingPageConfig {
  adminEmails: string; // Comma-separated email addresses for admin notifications
  emailSubject: string; // Email subject for notifications
  emailTemplate: string; // Email template for notifications
  thankYouMessage: string; // Thank you message to display after form submission
}

/**
 * Landing Page system setting
 */
export interface LandingPageSetting extends SystemSetting {
  type: SystemSettingType.LANDING_PAGE;
  config: LandingPageConfig;
}

/**
 * System settings service response
 */
export interface SystemSettingsServiceResponse {
  success: boolean;
  message?: string;
  setting?: SystemSetting;
  settings?: SystemSetting[];
  error?: string;
}

/**
 * Test SMTP configuration request
 */
export interface TestSMTPRequest {
  settingId?: string;
  config?: SMTPConfig;
  testEmail: string;
}

/**
 * Test Slack configuration request
 */
export interface TestSlackRequest {
  settingId?: string;
  config?: SlackConfig;
  testMessage?: string;
}
