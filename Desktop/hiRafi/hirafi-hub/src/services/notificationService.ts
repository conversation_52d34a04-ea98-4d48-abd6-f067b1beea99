/**
 * Notification Service
 * Handles sending notifications via email and Slack
 */

import nodemailer from 'nodemailer';
import { WebClient } from '@slack/web-api';
import { logger } from '../utils/logger.js';
import { getSystemSettingByType } from './mongo/systemSettingsService.js';
import { SystemSettingType, SMTPConfig, SlackConfig } from '../models/system-settings.js';

/**
 * Send an email notification
 * @param to Recipient email address
 * @param subject Email subject
 * @param text Plain text content
 * @param html HTML content
 */
export async function sendEmailNotification(
  to: string,
  subject: string,
  text: string,
  html?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Get SMTP configuration from system settings
    const result = await getSystemSettingByType(SystemSettingType.SMTP);

    if (!result.success || !result.setting) {
      logger.error('Failed to get SMTP configuration from system settings');
      return {
        success: false,
        error: 'SMTP configuration not found'
      };
    }

    const smtpConfig = result.setting.config as SMTPConfig;

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      auth: {
        user: smtpConfig.auth.user,
        pass: smtpConfig.auth.pass
      }
    });

    // Send email
    const info = await transporter.sendMail({
      from: `"${smtpConfig.fromName}" <${smtpConfig.from}>`,
      to,
      subject,
      text,
      html: html || text
    });

    logger.info(`Email sent: ${info.messageId}`);

    return {
      success: true,
      message: `Email sent to ${to}`
    };
  } catch (error: any) {
    logger.error(`Error sending email notification: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Send a Slack notification
 * @param channel Slack channel (defaults to the configured default channel)
 * @param text Message text
 * @param blocks Message blocks (optional)
 */
export async function sendSlackNotification(
  text: string,
  channel?: string,
  blocks?: any[]
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Get Slack configuration from system settings
    const result = await getSystemSettingByType(SystemSettingType.SLACK);

    if (!result.success || !result.setting) {
      logger.error('Failed to get Slack configuration from system settings');
      return {
        success: false,
        error: 'Slack configuration not found'
      };
    }

    const slackConfig = result.setting.config as SlackConfig;

    // Create Slack client
    const slack = new WebClient(slackConfig.botToken);

    // Use provided channel or default
    const targetChannel = channel || slackConfig.defaultChannel;

    // Send message
    await slack.chat.postMessage({
      channel: targetChannel,
      text,
      blocks: blocks || undefined
    });

    logger.info(`Slack message sent to ${targetChannel}`);

    return {
      success: true,
      message: `Slack message sent to ${targetChannel}`
    };
  } catch (error: any) {
    logger.error(`Error sending Slack notification: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Send a test completion notification
 * @param testId Test ID
 * @param testName Test name
 * @param success Whether the test passed
 * @param reportUrl URL to the test report
 * @param recipients Email recipients
 * @param slackChannel Slack channel
 */
export async function sendTestCompletionNotification(
  testId: string,
  testName: string,
  success: boolean,
  reportUrl: string,
  recipients?: string,
  slackChannel?: string
): Promise<{ emailSent: boolean; slackSent: boolean }> {
  const result = {
    emailSent: false,
    slackSent: false
  };

  // Send email notification if recipients are provided
  if (recipients) {
    const emailSubject = `Test ${success ? 'Passed' : 'Failed'}: ${testName}`;
    const emailText = `
      Test ${testId} (${testName}) has ${success ? 'passed' : 'failed'}.

      View the full report at: ${reportUrl}
    `;
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${success ? '#10b981' : '#ef4444'};">
          Test ${success ? 'Passed' : 'Failed'}: ${testName}
        </h2>
        <p>
          Test ${testId} (${testName}) has ${success ? 'passed' : 'failed'}.
        </p>
        <p>
          <a href="${reportUrl}" style="display: inline-block; background-color: #4f46e5; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">
            View Full Report
          </a>
        </p>
        <hr style="border: 1px solid #e5e7eb; margin: 20px 0;" />
        <p style="color: #6b7280; font-size: 14px;">
          This is an automated notification from HiRafi AI.
        </p>
      </div>
    `;

    const emailResult = await sendEmailNotification(
      recipients,
      emailSubject,
      emailText,
      emailHtml
    );

    result.emailSent = emailResult.success;
  }

  // Send Slack notification if channel is provided
  if (slackChannel) {
    const slackText = `Test ${success ? 'Passed' : 'Failed'}: ${testName}`;
    const slackBlocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: `Test ${success ? 'Passed ✅' : 'Failed ❌'}: ${testName}`,
          emoji: true
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `Test *${testId}* (${testName}) has ${success ? 'passed' : 'failed'}.`
        }
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View Report',
              emoji: true
            },
            url: reportUrl,
            style: 'primary'
          }
        ]
      },
      {
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: 'This is an automated notification from HiRafi AI.'
          }
        ]
      }
    ];

    const slackResult = await sendSlackNotification(
      slackText,
      slackChannel,
      slackBlocks
    );

    result.slackSent = slackResult.success;
  }

  return result;
}

/**
 * Send a scheduled run notification
 * @param scheduleName Schedule name
 * @param runIds Run IDs
 * @param success Whether all runs passed
 * @param reportUrls URLs to the test reports
 * @param recipients Email recipients
 * @param slackChannel Slack channel
 * @param companyId Company ID for the notification
 * @param teamId Team ID for the notification
 */
export async function sendScheduledRunNotification(
  scheduleName: string,
  runIds: string[],
  success: boolean,
  reportUrls: string[],
  recipients?: string,
  slackChannel?: string,
  companyId?: string,
  teamId?: string
): Promise<{ emailSent: boolean; slackSent: boolean }> {
  const result = {
    emailSent: false,
    slackSent: false
  };

  logger.info(`[SCHEDULE NOTIFICATION] Preparing notifications for schedule "${scheduleName}"`);
  logger.info(`[SCHEDULE NOTIFICATION] Status: ${success ? 'Success' : 'Failed'}, Recipients: ${recipients || 'none'}, Slack: ${slackChannel || 'none'}`);
  logger.info(`[SCHEDULE NOTIFICATION] Company ID: ${companyId || 'none'}, Team ID: ${teamId || 'none'}, Run IDs: ${runIds.join(', ')}`);
  logger.info(`[SCHEDULE NOTIFICATION] Report URLs: ${reportUrls.join(', ')}`);

  // Send email notification if recipients are provided
  if (recipients) {
    try {
      logger.info(`[SCHEDULE NOTIFICATION] Sending email notification to: ${recipients}`);

      // Get SMTP configuration from system settings
      const smtpResult = await getSystemSettingByType(SystemSettingType.SMTP);

      if (!smtpResult.success || !smtpResult.setting) {
        logger.error('[SCHEDULE NOTIFICATION] SMTP settings not found, cannot send email notification');
        return result;
      }

      const smtpConfig = smtpResult.setting.config as SMTPConfig;

      logger.info(`[SCHEDULE NOTIFICATION] Using SMTP config: ${smtpConfig.host}:${smtpConfig.port}`);

      const emailSubject = `Scheduled Run ${success ? 'Completed' : 'Failed'}: ${scheduleName}`;
      const emailText = `
        Scheduled run "${scheduleName}" has ${success ? 'completed successfully' : 'failed'}.

        ${runIds.length} test(s) were executed.

        View the reports at:
        ${reportUrls.join('\n')}
      `;
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: ${success ? '#10b981' : '#ef4444'};">
            Scheduled Run ${success ? 'Completed' : 'Failed'}: ${scheduleName}
          </h2>
          <p>
            Scheduled run "${scheduleName}" has ${success ? 'completed successfully' : 'failed'}.
          </p>
          <p>
            ${runIds.length} test(s) were executed.
          </p>
          <h3>Reports:</h3>
          <ul>
            ${reportUrls.map(url => `<li><a href="${url}">${url}</a></li>`).join('')}
          </ul>
          <hr style="border: 1px solid #e5e7eb; margin: 20px 0;" />
          <p style="color: #6b7280; font-size: 14px;">
            This is an automated notification from HiRafi AI.
          </p>
        </div>
      `;

      const emailResult = await sendEmailNotification(
        recipients,
        emailSubject,
        emailText,
        emailHtml
      );

      if (emailResult.success) {
        logger.info(`[SCHEDULE NOTIFICATION] Email notification sent successfully to ${recipients}`);
        result.emailSent = true;
      } else {
        logger.error(`[SCHEDULE NOTIFICATION] Failed to send email notification: ${emailResult.error}`);
      }
    } catch (error: any) {
      logger.error(`[SCHEDULE NOTIFICATION] Error sending email notification: ${error.message}`);
    }
  } else {
    logger.info('[SCHEDULE NOTIFICATION] No email recipients specified, skipping email notification');
  }

  // Send Slack notification if channel is provided
  if (slackChannel) {
    try {
      logger.info(`[SCHEDULE NOTIFICATION] Sending Slack notification to channel: ${slackChannel}`);

      // Get Slack configuration from system settings
      const slackSettingResult = await getSystemSettingByType(SystemSettingType.SLACK);

      if (!slackSettingResult.success || !slackSettingResult.setting) {
        logger.error('[SCHEDULE NOTIFICATION] Slack settings not found, cannot send Slack notification');
        return result;
      }

      const slackConfig = slackSettingResult.setting.config as SlackConfig;

      logger.info(`[SCHEDULE NOTIFICATION] Using Slack config with default channel: ${slackConfig.defaultChannel}`);

      const slackText = `Scheduled Run ${success ? 'Completed' : 'Failed'}: ${scheduleName}`;
      const slackBlocks: any[] = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: `Scheduled Run ${success ? 'Completed ✅' : 'Failed ❌'}: ${scheduleName}`,
            emoji: true
          }
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `Scheduled run *${scheduleName}* has ${success ? 'completed successfully' : 'failed'}.\n${runIds.length} test(s) were executed.`
          }
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*Reports:*'
          }
        }
      ];

      // Add report links
      reportUrls.forEach((url, index) => {
        slackBlocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `<${url}|View Report ${index + 1}>`
          }
        });
      });

      // Add footer
      slackBlocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: 'This is an automated notification from HiRafi AI.'
          }
        ]
      } as any);

      const slackNotificationResult = await sendSlackNotification(
        slackText,
        slackChannel,
        slackBlocks
      );

      if (slackNotificationResult.success) {
        logger.info(`[SCHEDULE NOTIFICATION] Slack notification sent successfully to channel ${slackChannel}`);
        result.slackSent = true;
      } else {
        logger.error(`[SCHEDULE NOTIFICATION] Failed to send Slack notification: ${slackNotificationResult.error}`);
      }
    } catch (error: any) {
      logger.error(`[SCHEDULE NOTIFICATION] Error sending Slack notification: ${error.message}`);
    }
  } else {
    logger.info('[SCHEDULE NOTIFICATION] No Slack channel specified, skipping Slack notification');
  }

  return result;
}
