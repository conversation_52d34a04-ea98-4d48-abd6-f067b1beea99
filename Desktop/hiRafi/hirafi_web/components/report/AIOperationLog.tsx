"use client"

import React from 'react';
import {
  CheckCircle2,
  XCircle,
  Terminal,
  AlertCircle,
  Search,
  MousePointer,
  Clock,
  Brain,
  Cpu,
  Zap,
  MessageSquare,
  Type,
  Keyboard,
  MousePointer2,
  ScrollText,
  Target,
  Eye,
  Hash,
  ToggleLeft
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { formatTimelineDuration } from '@/lib/utils/duration-utils';

interface AIOperationLogProps {
  log: string;
  compact?: boolean; // Timeline view için kompakt görünüm
}

interface AIOperationData {
  operation: string;
  description: string;
  success: boolean;
  error?: string;
  metadata?: {
    status?: string;
    duration?: number;
    tokenUsage?: {
      prompt_tokens?: number;
      completion_tokens?: number;
      total_tokens?: number;
    };
    thought?: string;
    locate?: any | Array<{id?: string; prompt?: string; [key: string]: any}>;
    actionDetails?: any[];
    pass?: boolean;
    explanation?: string;
    elements?: any[];
    result?: any;
    cacheHit?: boolean;
  };
}

export function AIOperationLog({ log, compact = false }: AIOperationLogProps) {
  try {
    // Try to parse the log as JSON
    if (!log || typeof log !== 'string' || !log.trim()) {
      throw new Error('Invalid log data');
    }

    const data: AIOperationData = JSON.parse(log);

    // If it's not an AI operation log, show a simplified message
    if (!data.operation) {
      return (
        <div className="flex items-start p-2 rounded-md border border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900/20">
          <Terminal className="h-4 w-4 text-gray-500 mt-0.5" />
          <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
            System log entry
          </span>
        </div>
      );
    }

    // Determine the icon and color based on the operation type
    let icon;
    let colorClass;
    let bgClass;
    let borderClass;

    switch (data.operation) {
      case 'aiAction':
        icon = <Zap className="h-5 w-5" />;
        colorClass = data.success ? "text-indigo-600" : "text-rose-600";
        bgClass = data.success ? "bg-indigo-50 dark:bg-indigo-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-indigo-200 dark:border-indigo-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiTap':
        icon = <MousePointer className="h-5 w-5" />;
        colorClass = data.success ? "text-blue-600" : "text-rose-600";
        bgClass = data.success ? "bg-blue-50 dark:bg-blue-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-blue-200 dark:border-blue-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiRightClick':
        icon = <MousePointer2 className="h-5 w-5" />;
        colorClass = data.success ? "text-cyan-600" : "text-rose-600";
        bgClass = data.success ? "bg-cyan-50 dark:bg-cyan-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-cyan-200 dark:border-cyan-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiHover':
        icon = <Target className="h-5 w-5" />;
        colorClass = data.success ? "text-teal-600" : "text-rose-600";
        bgClass = data.success ? "bg-teal-50 dark:bg-teal-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-teal-200 dark:border-teal-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiScroll':
        icon = <ScrollText className="h-5 w-5" />;
        colorClass = data.success ? "text-orange-600" : "text-rose-600";
        bgClass = data.success ? "bg-orange-50 dark:bg-orange-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-orange-200 dark:border-orange-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiInput':
        icon = <Type className="h-5 w-5" />;
        colorClass = data.success ? "text-purple-600" : "text-rose-600";
        bgClass = data.success ? "bg-purple-50 dark:bg-purple-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-purple-200 dark:border-purple-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiKeyboardPress':
        icon = <Keyboard className="h-5 w-5" />;
        colorClass = data.success ? "text-violet-600" : "text-rose-600";
        bgClass = data.success ? "bg-violet-50 dark:bg-violet-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-violet-200 dark:border-violet-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiWaitElement':
      case 'aiWaitFor':
        icon = <Eye className="h-5 w-5" />;
        colorClass = data.success ? "text-amber-600" : "text-rose-600";
        bgClass = data.success ? "bg-amber-50 dark:bg-amber-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-amber-200 dark:border-amber-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiLocate':
        icon = <Search className="h-5 w-5" />;
        colorClass = data.success ? "text-yellow-600" : "text-rose-600";
        bgClass = data.success ? "bg-yellow-50 dark:bg-yellow-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-yellow-200 dark:border-yellow-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiAssertion':
        icon = data.success ? <CheckCircle2 className="h-5 w-5" /> : <XCircle className="h-5 w-5" />;
        colorClass = data.success ? "text-emerald-600" : "text-rose-600";
        bgClass = data.success ? "bg-emerald-50 dark:bg-emerald-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-emerald-200 dark:border-emerald-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiQuery':
        icon = <MessageSquare className="h-5 w-5" />;
        colorClass = data.success ? "text-blue-600" : "text-rose-600";
        bgClass = data.success ? "bg-blue-50 dark:bg-blue-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-blue-200 dark:border-blue-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiString':
        icon = <Type className="h-5 w-5" />;
        colorClass = data.success ? "text-sky-600" : "text-rose-600";
        bgClass = data.success ? "bg-sky-50 dark:bg-sky-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-sky-200 dark:border-sky-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiNumber':
        icon = <Hash className="h-5 w-5" />;
        colorClass = data.success ? "text-emerald-600" : "text-rose-600";
        bgClass = data.success ? "bg-emerald-50 dark:bg-emerald-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-emerald-200 dark:border-emerald-800" : "border-rose-200 dark:border-rose-800";
        break;
      case 'aiBoolean':
        icon = <ToggleLeft className="h-5 w-5" />;
        colorClass = data.success ? "text-lime-600" : "text-rose-600";
        bgClass = data.success ? "bg-lime-50 dark:bg-lime-900/20" : "bg-rose-50 dark:bg-rose-900/20";
        borderClass = data.success ? "border-lime-200 dark:border-lime-800" : "border-rose-200 dark:border-rose-800";
        break;
      default:
        icon = <Terminal className="h-5 w-5" />;
        colorClass = "text-gray-600";
        bgClass = "bg-gray-50 dark:bg-gray-900/20";
        borderClass = "border-gray-200 dark:border-gray-800";
    }

    // Format duration using centralized utility
    const formattedDuration = data.metadata?.duration !== undefined
      ? formatTimelineDuration(data.metadata.duration)
      : null;

    // Format token usage
    const tokenUsage = data.metadata?.tokenUsage;
    const cacheHit = data.metadata?.cacheHit === true;

    // If cache hit is true, show "No token used" instead of token count
    const formattedTokenUsage = cacheHit
      ? "No token used (cached)"
      : (tokenUsage
          ? `${tokenUsage.total_tokens || 0} tokens (${tokenUsage.prompt_tokens || 0} prompt, ${tokenUsage.completion_tokens || 0} completion)`
          : null);

    // Timeline view için kompakt görünüm
    if (compact) {
      return (
        <div className={`rounded-md border ${borderClass} ${bgClass} p-2`}>
          <div className="flex items-center gap-1.5">
            <div className={colorClass}>{icon}</div>
            <div className="flex-1">
              <div className="flex items-center gap-1.5 flex-wrap">
                <h4 className={`text-xs font-medium ${colorClass}`}>
                  {data.operation === 'aiAction' && 'AI Action'}
                  {data.operation === 'aiTap' && 'AI Tap'}
                  {data.operation === 'aiRightClick' && 'AI Right Click'}
                  {data.operation === 'aiHover' && 'AI Hover'}
                  {data.operation === 'aiScroll' && 'AI Scroll'}
                  {data.operation === 'aiInput' && 'AI Input'}
                  {data.operation === 'aiKeyboardPress' && 'AI Keyboard'}
                  {data.operation === 'aiWaitElement' && 'AI Wait Element'}
                  {data.operation === 'aiWaitFor' && 'AI Wait For'}
                  {data.operation === 'aiLocate' && 'AI Locate'}
                  {data.operation === 'aiAssertion' && 'AI Assertion'}
                  {data.operation === 'aiQuery' && 'AI Query'}
                  {data.operation === 'aiString' && 'AI String'}
                  {data.operation === 'aiNumber' && 'AI Number'}
                  {data.operation === 'aiBoolean' && 'AI Boolean'}
                </h4>
                {data.success ? (
                  <Badge variant="outline" className="text-xs py-0 h-5 bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800">
                    Success
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="text-xs py-0 h-5">Failed</Badge>
                )}
                {cacheHit && (
                  <Badge variant="outline" className="text-xs py-0 h-5 bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800 font-bold">
                    No Token Used (Cached)
                  </Badge>
                )}
              </div>
              <p className="text-xs text-gray-700 dark:text-gray-300 mt-0.5">
                {data.description}
              </p>
            </div>
          </div>

          {/* Error message (compact) */}
          {data.error && (
            <div className="mt-1.5 p-1.5 bg-rose-50 dark:bg-rose-900/20 rounded-md border border-rose-200 dark:border-rose-800 text-xs">
              <div className="flex items-start gap-1">
                <AlertCircle className="h-3 w-3 text-rose-600 mt-0.5" />
                <span className="text-rose-700 dark:text-rose-300">
                  {typeof data.error === 'string' ?
                    (data.error.startsWith('AI Assertion error:') ?
                      data.error.replace('AI Assertion error:', '').trim() :
                      data.error) :
                    'Error occurred'}
                </span>
              </div>
              {data.reason && (
                <div className="mt-1 pl-4">
                  <span className="text-rose-700 dark:text-rose-300">
                    {typeof data.reason === 'string' ? data.reason : 'Unknown reason'}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      );
    }

    // Normal görünüm (detaylı)
    return (
      <div className={`rounded-md border ${borderClass} ${bgClass} p-3 mb-3`}>
        <div className="flex items-center gap-2 mb-2">
          <div className={colorClass}>{icon}</div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h4 className={`text-sm font-medium ${colorClass}`}>
                {data.operation === 'aiAction' && 'AI Action'}
                {data.operation === 'aiTap' && 'AI Tap'}
                {data.operation === 'aiRightClick' && 'AI Right Click'}
                {data.operation === 'aiHover' && 'AI Hover'}
                {data.operation === 'aiScroll' && 'AI Scroll'}
                {data.operation === 'aiInput' && 'AI Input'}
                {data.operation === 'aiKeyboardPress' && 'AI Keyboard'}
                {data.operation === 'aiWaitElement' && 'AI Wait Element'}
                {data.operation === 'aiWaitFor' && 'AI Wait For'}
                {data.operation === 'aiLocate' && 'AI Locate'}
                {data.operation === 'aiAssertion' && 'AI Assertion'}
                {data.operation === 'aiQuery' && 'AI Query'}
                {data.operation === 'aiString' && 'AI String'}
                {data.operation === 'aiNumber' && 'AI Number'}
                {data.operation === 'aiBoolean' && 'AI Boolean'}
              </h4>
              {data.success ? (
                <Badge variant="outline" className="bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800">
                  Success
                </Badge>
              ) : (
                <Badge variant="destructive">Failed</Badge>
              )}
              {cacheHit && (
                <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                  Cached
                </Badge>
              )}
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">
              {data.description}
            </p>
          </div>
        </div>

        {/* Metadata section */}
        {data.metadata && (
          <div className="mt-3 space-y-2 text-xs">
            {/* Status and Duration */}
            <div className="flex flex-wrap gap-4">
              {data.metadata.status && (
                <div className="flex items-center gap-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                  <span className={data.success ? "text-emerald-600 dark:text-emerald-400" : "text-rose-600 dark:text-rose-400"}>
                    {data.metadata.status}
                  </span>
                </div>
              )}
              {formattedDuration && (
                <div className="flex items-center gap-1">
                  <Clock className="h-3.5 w-3.5 text-gray-500" />
                  <span className="font-medium text-gray-700 dark:text-gray-300">Duration:</span>
                  <span>{formattedDuration}</span>
                </div>
              )}
              {formattedTokenUsage && (
                <div className="flex items-center gap-1">
                  <Cpu className="h-3.5 w-3.5 text-gray-500" />
                  <span className="font-medium text-gray-700 dark:text-gray-300">Tokens:</span>
                  {cacheHit ? (
                    <span className="text-blue-600 dark:text-blue-400 font-medium">{formattedTokenUsage}</span>
                  ) : (
                    <span>{formattedTokenUsage}</span>
                  )}
                </div>
              )}


            </div>

            {/* Thought process */}
            {data.metadata.thought && (
              <div className="mt-3 p-2 bg-purple-50 dark:bg-purple-900/20 rounded-md border border-purple-200 dark:border-purple-800">
                <div className="flex items-start gap-1 mb-1">
                  <Brain className="h-3.5 w-3.5 text-purple-600 mt-0.5" />
                  <span className="font-medium text-purple-700 dark:text-purple-300">AI Reasoning:</span>
                </div>
                <div className="text-gray-700 dark:text-gray-300 pl-5">
                  {data.metadata.thought.split('\n').map((line, i) => (
                    <p key={i} className={i > 0 ? 'mt-1' : ''}>
                      {line}
                    </p>
                  ))}
                </div>
              </div>
            )}

            {/* Explanation for assertions */}
            {data.metadata.explanation && (
              <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
                <div className="flex items-start gap-1 mb-1">
                  <MessageSquare className="h-3.5 w-3.5 text-blue-600 mt-0.5" />
                  <span className="font-medium text-blue-700 dark:text-blue-300">Explanation:</span>
                </div>
                <p className="text-gray-700 dark:text-gray-300 pl-5">
                  {data.metadata.explanation}
                </p>
              </div>
            )}

            {/* Location data for aiWaitFor */}
            {data.metadata.locate && (
              <div className="mt-2 p-2 bg-amber-50 dark:bg-amber-900/20 rounded-md border border-amber-200 dark:border-amber-800">
                <div className="flex items-start gap-1 mb-1">
                  <Search className="h-3.5 w-3.5 text-amber-600 mt-0.5" />
                  <span className="font-medium text-amber-700 dark:text-amber-300">Element Location:</span>
                </div>
                <div className="pl-5 space-y-1">
                  {Array.isArray(data.metadata.locate) ? (
                    // If locate is an array, display each item
                    <div className="space-y-3">
                      {data.metadata.locate.map((item, index) => (
                        <div key={index} className="border-l-2 border-amber-300 pl-2 py-1">
                          <div className="text-xs font-medium text-amber-700 dark:text-amber-300 mb-1">Element {index + 1}:</div>
                          <div className="space-y-1">
                            {Object.entries(item).map(([key, value]) => (
                              <div key={key} className="grid grid-cols-4 gap-2">
                                <span className="font-medium col-span-1">{key}:</span>
                                <span className="col-span-3 font-mono">{String(value)}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    // If locate is an object, display its properties
                    Object.entries(data.metadata.locate).map(([key, value]) => (
                      <div key={key} className="grid grid-cols-4 gap-2">
                        <span className="font-medium col-span-1">{key}:</span>
                        <span className="col-span-3 font-mono">{String(value)}</span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            {/* Action details for aiAction */}
            {data.metadata.actionDetails && data.metadata.actionDetails.length > 0 && (
              <div className="mt-2 p-2 bg-indigo-50 dark:bg-indigo-900/20 rounded-md border border-indigo-200 dark:border-indigo-800">
                <div className="flex items-start gap-1 mb-1">
                  <Zap className="h-3.5 w-3.5 text-indigo-600 mt-0.5" />
                  <span className="font-medium text-indigo-700 dark:text-indigo-300">Action Details:</span>
                </div>
                <div className="pl-5 space-y-2">
                  {data.metadata.actionDetails.map((detail, idx) => (
                    <div key={idx} className="text-gray-700 dark:text-gray-300">
                      {typeof detail === 'object' ? (
                        <pre className="text-xs font-mono overflow-auto">{JSON.stringify(detail, null, 2)}</pre>
                      ) : (
                        <span>{String(detail)}</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Result for aiQuery */}
            {data.metadata.result && (
              <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
                <div className="flex items-start gap-1 mb-1">
                  <MessageSquare className="h-3.5 w-3.5 text-blue-600 mt-0.5" />
                  <span className="font-medium text-blue-700 dark:text-blue-300">Query Result:</span>
                </div>
                <div className="pl-5">
                  {typeof data.metadata.result === 'object' ? (
                    <pre className="text-xs font-mono overflow-auto">{JSON.stringify(data.metadata.result, null, 2)}</pre>
                  ) : (
                    <span className="text-gray-700 dark:text-gray-300">{String(data.metadata.result)}</span>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error message */}
        {data.error && (
          <div className="mt-3 p-2 bg-rose-50 dark:bg-rose-900/20 rounded-md border border-rose-200 dark:border-rose-800">
            <div className="flex items-start gap-1">
              <AlertCircle className="h-3.5 w-3.5 text-rose-600 mt-0.5" />
              <span className="font-medium text-rose-700 dark:text-rose-300">Error:</span>
              <span className="text-rose-700 dark:text-rose-300">
                {typeof data.error === 'string' ?
                  (data.error.startsWith('AI Assertion error:') ?
                    data.error.replace('AI Assertion error:', '').trim() :
                    data.error) :
                  'Error occurred'}
              </span>
            </div>
            {/* Reason bilgisini sadece error ile aynı değilse göster */}
            {data.reason && data.reason !== data.error && data.reason !== 'Bilinmeyen hata' && (
              <div className="mt-1 pl-5">
                <span className="text-rose-700 dark:text-rose-300">
                  {typeof data.reason === 'string' ? data.reason : 'Unknown reason'}
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    );
  } catch (error) {
    // If the log is not valid JSON or doesn't match our expected format,
    // display a more user-friendly message instead of raw JSON
    return (
      <div className="flex items-start p-2 rounded-md border border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900/20">
        <AlertCircle className="h-4 w-4 text-amber-500 mt-0.5" />
        <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
          Log data could not be parsed properly
        </span>
      </div>
    );
  }
}
