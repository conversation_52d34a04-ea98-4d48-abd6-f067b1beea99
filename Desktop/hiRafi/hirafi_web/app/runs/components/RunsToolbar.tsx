import React from 'react';
import { FilterBar } from '@/components/runs/filter-bar';
import { useRunsFilter } from '@/hooks/useRunsFilter';

export function RunsToolbar() {
  const {
    searchQuery,
    activeFilter,
    activePlatform,
    dateFilter,
    customDateRange,
    sortBy,
    sortOrder,
    itemsPerPage,
    displayView,
    setSearchQuery,
    setStatusFilter,
    setPlatformFilter,
    setDateFilter,
    setCustomDateRange,
    setSort,
    setItemsPerPage,
    setDisplayView,
    resetFilters
  } = useRunsFilter();

  return (
    <FilterBar
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      activeFilter={activeFilter}
      onStatusFilterChange={setStatusFilter}
      activePlatform={activePlatform}
      onPlatformFilterChange={setPlatformFilter}
      dateFilter={dateFilter}
      customDateRange={customDateRange}
      onDateFilterChange={(filter, customRange) => {
        setDateFilter(filter);
        if (customRange) {
          setCustomDateRange(customRange);
        }
      }}
      sortBy={sortBy}
      sortOrder={sortOrder}
      onSortChange={setSort}
      itemsPerPage={itemsPerPage}
      onItemsPerPageChange={setItemsPerPage}
      displayView={displayView}
      onDisplayViewChange={setDisplayView}
      onClearFilters={resetFilters}
      isSearching={false} // Will be handled by SWR loading state
    />
  );
}
