"use client"

import { useState } from "react"
import { <PERSON><PERSON>pD<PERSON>, Check, ArrowUp, ArrowDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface SortControlProps {
  sortBy: string
  sortOrder: string
  onSortChange: (by: string, order: string) => void
}

// Sort options
const sortOptions = [
  { by: "priority", order: "desc", label: "Priority (Running First)" },
  { by: "date", order: "desc", label: "Date (Newest First)" },
  { by: "date", order: "asc", label: "Date (Oldest First)" },
  { by: "name", order: "asc", label: "Name (A-Z)" },
  { by: "name", order: "desc", label: "Name (Z-A)" },
  { by: "status", order: "asc", label: "Status (A-Z)" },
  { by: "status", order: "desc", label: "Status (Z-A)" },
]

export function SortControl({ sortBy, sortOrder, onSortChange }: SortControlProps) {
  const [open, setOpen] = useState(false)

  // Get the currently selected sort option
  const selectedSort = sortOptions.find(
    option => option.by === sortBy && option.order === sortOrder
  ) || sortOptions[0]

  // Handle sort selection
  const handleSortSelect = (by: string, order: string) => {
    onSortChange(by, order)
    setOpen(false)
  }

  // Get sort icon based on current sort
  const getSortIcon = () => {
    if (sortOrder === "asc") {
      return <ArrowUp className="h-3.5 w-3.5 ml-1" />
    }
    return <ArrowDown className="h-3.5 w-3.5 ml-1" />
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-9 border-gray-200 dark:border-gray-700 min-w-[120px] justify-between"
        >
          <div className="flex items-center">
            <ArrowUpDown className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
            <span className="text-sm font-medium flex items-center">
              {sortBy === "priority" ? "Priority" :
               sortBy === "date" ? "Date" :
               sortBy === "name" ? "Name" : "Status"}
              {sortBy !== "priority" && getSortIcon()}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        {sortOptions.map((option) => (
          <DropdownMenuItem
            key={`${option.by}-${option.order}`}
            className={cn(
              "flex items-center px-3 py-2 cursor-pointer",
              sortBy === option.by && sortOrder === option.order ? "bg-gray-100 dark:bg-gray-800" : ""
            )}
            onClick={() => handleSortSelect(option.by, option.order)}
          >
            <div className="flex items-center flex-1">
              <span>{option.label}</span>
            </div>
            {sortBy === option.by && sortOrder === option.order && (
              <Check className="h-4 w-4 ml-2 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
