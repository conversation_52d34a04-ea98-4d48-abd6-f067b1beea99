"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Check, ChevronRight, Puzzle } from "lucide-react"
import { CompanyOwnerRoute } from "@/components/company-owner-route"

export default function GitHubPluginPage() {
  const router = useRouter()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [configStep, setConfigStep] = useState(1)
  const [configData, setConfigData] = useState({
    token: "",
    selectedRepo: "",
    selectedBranch: "",
  })
  const [isActive, setIsActive] = useState(false)

  // Mock repos and branches for GitHub
  const mockRepos = [
    { id: "repo1", name: "testinium/e-commerce-tests" },
    { id: "repo2", name: "testinium/mobile-app-tests" },
    { id: "repo3", name: "testinium/api-tests" },
  ]

  const mockBranches = [
    { id: "main", name: "main" },
    { id: "develop", name: "develop" },
    { id: "feature-123", name: "feature/new-tests" },
  ]

  const handleNextStep = () => {
    if (configStep < 3) {
      setConfigStep(configStep + 1)
    } else {
      // Save configuration
      setIsActive(true)
    }
  }

  const handlePrevStep = () => {
    if (configStep > 1) {
      setConfigStep(configStep - 1)
    }
  }

  const getStepTitle = () => {
    switch (configStep) {
      case 1:
        return "Authentication"
      case 2:
        return "Select Repository"
      case 3:
        return "Select Branch"
      default:
        return "Configure GitHub"
    }
  }

  return (
    <CompanyOwnerRoute>
      <div className="flex h-screen bg-white dark:bg-gray-950">
        {/* Sidebar */}
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
            <div className="flex items-center gap-3">
              <Puzzle className="h-6 w-6 text-purple-500" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">GitHub Integration</h1>
                <p className="text-gray-500 dark:text-gray-400">
                  Connect your GitHub account to link test cases to repositories
                </p>
              </div>
            </div>
          </header>

          <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
            <div className="max-w-3xl mx-auto">
              {isActive ? (
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Check className="h-6 w-6 text-emerald-500" />
                      <CardTitle>GitHub Integration Active</CardTitle>
                    </div>
                    <CardDescription>Your GitHub integration is set up and working correctly.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Repository</h3>
                          <p className="text-sm font-medium">
                            {mockRepos.find((r) => r.id === configData.selectedRepo)?.name ||
                              "testinium/e-commerce-tests"}
                          </p>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Branch</h3>
                          <p className="text-sm font-medium">
                            {mockBranches.find((b) => b.id === configData.selectedBranch)?.name || "main"}
                          </p>
                        </div>
                      </div>

                      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-800">
                        <h3 className="text-sm font-medium mb-2">Sync Status</h3>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-500">Last synced 15 minutes ago</span>
                          <span className="text-xs font-medium">32 test files</span>
                        </div>
                        <Progress value={100} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t border-gray-200 dark:border-gray-800 px-6 py-4">
                    <Button variant="outline" onClick={() => setIsActive(false)}>
                      Reconfigure
                    </Button>
                    <Button variant="destructive">Disconnect</Button>
                  </CardFooter>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>{getStepTitle()}</CardTitle>
                    <CardDescription>
                      {configStep === 1 && "Enter your GitHub personal access token to authenticate."}
                      {configStep === 2 && "Select the repository you want to connect with."}
                      {configStep === 3 && "Select the branch you want to use."}
                    </CardDescription>
                  </CardHeader>

                  <CardContent>
                    <div className="mb-6">
                      <Progress value={(configStep / 3) * 100} className="h-2" />
                      <div className="flex justify-between mt-2">
                        <div
                          className={`flex flex-col items-center ${configStep >= 1 ? "text-purple-600" : "text-gray-400"}`}
                        >
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center ${configStep >= 1 ? "bg-purple-100 text-purple-600" : "bg-gray-100 text-gray-400"}`}
                          >
                            1
                          </div>
                          <span className="text-xs mt-1">Authentication</span>
                        </div>
                        <div
                          className={`flex flex-col items-center ${configStep >= 2 ? "text-purple-600" : "text-gray-400"}`}
                        >
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center ${configStep >= 2 ? "bg-purple-100 text-purple-600" : "bg-gray-100 text-gray-400"}`}
                          >
                            2
                          </div>
                          <span className="text-xs mt-1">Repository</span>
                        </div>
                        <div
                          className={`flex flex-col items-center ${configStep >= 3 ? "text-purple-600" : "text-gray-400"}`}
                        >
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center ${configStep >= 3 ? "bg-purple-100 text-purple-600" : "bg-gray-100 text-gray-400"}`}
                          >
                            3
                          </div>
                          <span className="text-xs mt-1">Branch</span>
                        </div>
                      </div>
                    </div>

                    {configStep === 1 && (
                      <div className="space-y-4">
                        <div className="grid gap-2">
                          <Label htmlFor="token">GitHub Personal Access Token</Label>
                          <Input
                            id="token"
                            type="password"
                            placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                            value={configData.token}
                            onChange={(e) => setConfigData({ ...configData, token: e.target.value })}
                          />
                          <p className="text-xs text-gray-500">
                            Your token needs the following permissions: repo, read:user
                          </p>
                        </div>
                      </div>
                    )}

                    {configStep === 2 && (
                      <div className="space-y-4">
                        <Label>Select Repository</Label>
                        <div className="space-y-2">
                          {mockRepos.map((repo) => (
                            <div
                              key={repo.id}
                              className={cn(
                                "p-3 border rounded-md cursor-pointer transition-colors",
                                configData.selectedRepo === repo.id
                                  ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                                  : "border-gray-200 dark:border-gray-800 hover:border-purple-300 dark:hover:border-purple-700",
                              )}
                              onClick={() => setConfigData({ ...configData, selectedRepo: repo.id })}
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">{repo.name}</span>
                                {configData.selectedRepo === repo.id && <Check className="h-5 w-5 text-purple-500" />}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {configStep === 3 && (
                      <div className="space-y-4">
                        <Label>Select Branch</Label>
                        <div className="space-y-2">
                          {mockBranches.map((branch) => (
                            <div
                              key={branch.id}
                              className={cn(
                                "p-3 border rounded-md cursor-pointer transition-colors",
                                configData.selectedBranch === branch.id
                                  ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                                  : "border-gray-200 dark:border-gray-800 hover:border-purple-300 dark:hover:border-purple-700",
                              )}
                              onClick={() => setConfigData({ ...configData, selectedBranch: branch.id })}
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">{branch.name}</span>
                                {configData.selectedBranch === branch.id && <Check className="h-5 w-5 text-purple-500" />}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>

                  <CardFooter className="flex justify-between border-t border-gray-200 dark:border-gray-800 px-6 py-4">
                    {configStep > 1 ? (
                      <Button variant="outline" onClick={handlePrevStep}>
                        Back
                      </Button>
                    ) : (
                      <Button variant="outline" onClick={() => router.push("/")}>
                        Cancel
                      </Button>
                    )}
                    <Button
                      onClick={handleNextStep}
                      disabled={
                        (configStep === 1 && !configData.token) ||
                        (configStep === 2 && !configData.selectedRepo) ||
                        (configStep === 3 && !configData.selectedBranch)
                      }
                      className="gap-1"
                    >
                      {configStep < 3 ? (
                        <>
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </>
                      ) : (
                        "Save & Activate"
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              )}
            </div>
          </main>
        </div>
      </div>
    </CompanyOwnerRoute>
  )
}

function cn(...classes: any[]) {
  return classes.filter(Boolean).join(" ")
}

