"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, MoreHorizontal, Edit, Trash2, ShieldPlus, Users, Shield } from "lucide-react"
import type { Role, Permission } from "@/types/team"
import { useAuth } from "@/lib/api/auth"
import { AddRoleDialog } from "./add-role-dialog"
import { EditRoleDialog } from "./edit-role-dialog"
import { ConfirmDialog } from "./confirm-dialog"

interface RoleManagementProps {
  roles: Role[]
  onAddRole: (role: Role) => void
  onUpdateRole: (role: Role) => void
  onDeleteRole: (id: string) => void
}

export function RoleManagement({ roles, onAddRole, onUpdateRole, onDeleteRole }: RoleManagementProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [roleToEdit, setRoleToEdit] = useState<Role | null>(null)
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null)
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Role
    direction: "asc" | "desc"
  }>({ key: "name", direction: "asc" })

  // Kullanıcının company_owner olup olmadığını kontrol et
  const { user } = useAuth()
  const isCompanyOwner = user?.accountType === 'company_owner'

  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      role.description.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const sortedRoles = [...filteredRoles].sort((a, b) => {
    const aValue = a[sortConfig.key] || '';
    const bValue = b[sortConfig.key] || '';

    if (aValue < bValue) {
      return sortConfig.direction === "asc" ? -1 : 1
    }
    if (aValue > bValue) {
      return sortConfig.direction === "asc" ? 1 : -1
    }
    return 0
  })

  const handleSort = (key: keyof Role) => {
    setSortConfig({
      key,
      direction: sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc",
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rol ara..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        {/* Sadece company owner yeni rol ekleyebilir */}
        {isCompanyOwner && (
          <Button
            onClick={() => setShowAddDialog(true)}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            <ShieldPlus className="mr-2 h-4 w-4" />
            Yeni Rol Ekle
          </Button>
        )}
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px] cursor-pointer" onClick={() => handleSort("name")}>
                Rol Adı
                {sortConfig.key === "name" && (
                  <span className="ml-1">{sortConfig.direction === "asc" ? "↑" : "↓"}</span>
                )}
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("description")}>
                Açıklama
                {sortConfig.key === "description" && (
                  <span className="ml-1">{sortConfig.direction === "asc" ? "↑" : "↓"}</span>
                )}
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort("memberCount")}>
                Üye Sayısı
                {sortConfig.key === "memberCount" && (
                  <span className="ml-1">{sortConfig.direction === "asc" ? "↑" : "↓"}</span>
                )}
              </TableHead>
              <TableHead>Yetkiler</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedRoles.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                  Arama kriterlerine uygun rol bulunamadı.
                </TableCell>
              </TableRow>
            ) : (
              sortedRoles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={`bg-${role.color}-100 text-${role.color}-800 dark:bg-${role.color}-900 dark:text-${role.color}-300 border-none`}>
                        {role.name}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{role.description}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      {role.memberCount}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-2">
                      {role.permissions && renderPermissions(role.permissions)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    {isCompanyOwner ? (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">İşlemler</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setRoleToEdit(role)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Düzenle
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => {
                              if ((role.memberCount || 0) > 0) {
                                alert(`Bu rol ${role.memberCount} kullanıcı tarafından kullanılıyor. Önce bu kullanıcıların rolünü değiştirmelisiniz.`);
                                return;
                              }
                              setRoleToDelete(role.id);
                            }}
                            disabled={(role.memberCount || 0) > 0}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Sil
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    ) : (
                      <span className="text-gray-400 text-sm">-</span>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Add Role Dialog */}
      <AddRoleDialog open={showAddDialog} onOpenChange={setShowAddDialog} onAddRole={onAddRole} />

      {/* Edit Role Dialog */}
      {roleToEdit && (
        <EditRoleDialog
          open={!!roleToEdit}
          onOpenChange={() => setRoleToEdit(null)}
          role={roleToEdit}
          onUpdateRole={onUpdateRole}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={!!roleToDelete}
        onOpenChange={() => setRoleToDelete(null)}
        title="Rolü Sil"
        description="Bu rolü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."
        confirmLabel="Sil"
        cancelLabel="İptal"
        onConfirm={() => {
          if (roleToDelete) {
            onDeleteRole(roleToDelete)
            setRoleToDelete(null)
          }
        }}
        variant="destructive"
      />
    </div>
  )
}

function renderPermissions(permissions: Permission[]) {
  // Group permissions by resource
  const resourceGroups: Record<string, string[]> = {};

  permissions.forEach(permission => {
    if (!resourceGroups[permission.resource]) {
      resourceGroups[permission.resource] = [];
    }
    resourceGroups[permission.resource].push(permission.action);
  });

  // Function to get the display name for a resource
  const getResourceName = (resource: string) => {
    const resourceMap: Record<string, string> = {
      'scenario': 'Senaryo',
      'run': 'Koşum',
      'report': 'Rapor',
      'settings': 'Ayarlar',
      'team': 'Takım',
      'api': 'API',
      '*': 'Tümü'
    };
    return resourceMap[resource] || resource;
  };

  // Function to get color for an action
  const getActionColor = (action: string) => {
    const actionMap: Record<string, string> = {
      'read': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'write': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'update': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'create': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'delete': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'execute': 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300',
      'manage': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
      '*': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
    };
    return actionMap[action] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
  };

  // Function to get short display for an action
  const getActionDisplay = (action: string) => {
    const actionMap: Record<string, string> = {
      'read': 'G',
      'write': 'Y',
      'update': 'D',
      'create': 'O',
      'delete': 'S',
      'execute': 'Ç',
      'manage': 'Y',
      '*': 'T'
    };
    return actionMap[action] || action.charAt(0).toUpperCase();
  };

  // Check if this is the '*' resource with 'manage' action (admin)
  if (resourceGroups['*'] && resourceGroups['*'].includes('manage')) {
    return (
      <Badge variant="outline" className="bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300 px-3 py-1 font-medium">
        Tam Yetki
      </Badge>
    );
  }

  return Object.entries(resourceGroups).map(([resource, actions]) => (
    <div key={resource} className="inline-flex items-center rounded-md border border-gray-200 bg-white px-2 py-1 dark:border-gray-700 dark:bg-gray-800">
      <span className="mr-1 font-medium">{getResourceName(resource)}:</span>
      <div className="flex gap-1">
        {actions.map(action => (
          <span
            key={`${resource}-${action}`}
            className={`inline-flex items-center justify-center rounded-full ${getActionColor(action)} h-5 w-5 text-xs font-bold`}
            title={action === 'read' ? 'Görüntüleme' :
                  action === 'write' ? 'Yazma' :
                  action === 'update' ? 'Düzenleme' :
                  action === 'create' ? 'Oluşturma' :
                  action === 'delete' ? 'Silme' :
                  action === 'execute' ? 'Çalıştırma' :
                  action === 'manage' ? 'Yönetme' :
                  action === '*' ? 'Tümü' : action}
          >
            {getActionDisplay(action)}
          </span>
        ))}
      </div>
    </div>
  ));
}
