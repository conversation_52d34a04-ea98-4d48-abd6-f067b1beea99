/**
 * Base Service Interfaces
 * 
 * Consolidated service interfaces that can be shared across all modules
 * to reduce duplication and ensure consistency.
 */

/**
 * Base service interface that all services should implement
 */
export interface IBaseService {
  /**
   * Initialize the service
   * @param config Service configuration
   */
  initialize(config?: any): Promise<void>;

  /**
   * Close the service and cleanup resources
   */
  close(): Promise<void>;

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean;

  /**
   * Get service statistics
   */
  getStats(): IServiceStats;

  /**
   * Get service name
   */
  getServiceName(): string;
}

/**
 * Service configuration interface
 */
export interface IServiceConfig {
  enableLogging?: boolean;
  [key: string]: any;
}

/**
 * Service statistics interface
 */
export interface IServiceStats {
  serviceName: string;
  isInitialized: boolean;
  createdAt: string;
  uptime: number;
  operationCount: number;
  errorCount: number;
  lastOperation?: string;
  lastError?: string;
}

/**
 * Service lifecycle interface
 */
export interface IServiceLifecycle {
  /**
   * Called before service initialization
   */
  beforeInitialize?(): Promise<void>;

  /**
   * Called after service initialization
   */
  afterInitialize?(): Promise<void>;

  /**
   * Called before service closure
   */
  beforeClose?(): Promise<void>;

  /**
   * Called after service closure
   */
  afterClose?(): Promise<void>;
}

/**
 * Service dependency interface
 */
export interface IServiceDependency {
  name: string;
  required: boolean;
  version?: string;
}

/**
 * Service registration interface for DI containers
 */
export interface IServiceRegistration<T = any> {
  name: string;
  factory: (...args: any[]) => Promise<T> | T;
  dependencies: string[];
  lifecycle: 'singleton' | 'transient' | 'scoped';
  instance?: T;
  initialized: boolean;
  scope?: string;
}

/**
 * Service health status interface
 */
export interface IServiceHealthStatus {
  healthy: boolean;
  message?: string;
  details?: Record<string, any>;
  timestamp: string;
}

/**
 * Service event interface
 */
export interface IServiceEvent {
  type: string;
  serviceName: string;
  timestamp: string;
  data?: any;
}

/**
 * Factory configuration interface
 */
export interface IFactoryConfig {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  maxResolutionDepth?: number;
  [key: string]: any;
}

/**
 * Factory statistics interface
 */
export interface IFactoryStats {
  totalCreations: number;
  creationsByType: Record<string, number>;
  successfulCreations: number;
  failedCreations: number;
  averageCreationTime: number;
  registeredTypes: string[];
  singletonInstances: number;
  transientInstances: number;
}

/**
 * Factory error interface
 */
export interface IFactoryError {
  type: string;
  message: string;
  timestamp: string;
  context?: any;
}

/**
 * Factory event interface
 */
export interface IFactoryEvent {
  type: string;
  factoryType: string;
  timestamp: string;
  data?: any;
}

/**
 * Factory lifecycle interface
 */
export interface IFactoryLifecycle {
  beforeCreate?: (type: string, config?: any) => Promise<void>;
  afterCreate?: (type: string, instance: any, config?: any) => Promise<void>;
  beforeDestroy?: (type: string, instance: any) => Promise<void>;
  afterDestroy?: (type: string) => Promise<void>;
}

/**
 * Service registry configuration interface
 */
export interface IServiceRegistryConfig {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  maxResolutionDepth?: number;
  nodeId?: string;
  [key: string]: any;
}

/**
 * Base service registry interface
 */
export interface IServiceRegistry {
  /**
   * Initialize the service registry
   */
  initialize(): Promise<void>;

  /**
   * Register an external service
   */
  registerExternalService<T>(name: string, instance: T): void;

  /**
   * Check if service is registered
   */
  isRegistered(name: string): boolean;

  /**
   * Get registry statistics
   */
  getStatistics(): Record<string, any>;

  /**
   * Dispose the registry
   */
  dispose(): Promise<void>;
}

/**
 * Disposable interface for resources that need cleanup
 */
export interface IDisposable {
  /**
   * Dispose the resource and cleanup
   */
  dispose(): Promise<void>;
}

/**
 * Configurable interface for services that accept configuration
 */
export interface IConfigurable<T = any> {
  /**
   * Configure the service
   */
  configure(config: T): void;

  /**
   * Get current configuration
   */
  getConfiguration(): T;
}

/**
 * Observable interface for services that emit events
 */
export interface IObservable<T = any> {
  /**
   * Subscribe to events
   */
  subscribe(callback: (event: T) => void): () => void;

  /**
   * Emit an event
   */
  emit(event: T): void;
}

/**
 * Validatable interface for services that can validate their state
 */
export interface IValidatable {
  /**
   * Validate the service state
   */
  validate(): Promise<{ isValid: boolean; errors: string[] }>;
}

/**
 * Monitorable interface for services that provide health monitoring
 */
export interface IMonitorable {
  /**
   * Get health status
   */
  getHealthStatus(): Promise<IServiceHealthStatus>;

  /**
   * Perform health check
   */
  healthCheck(): Promise<boolean>;
}

/**
 * Resettable interface for services that can be reset
 */
export interface IResettable {
  /**
   * Reset the service to initial state
   */
  reset(): Promise<void>;
}

/**
 * Pausable interface for services that can be paused/resumed
 */
export interface IPausable {
  /**
   * Pause the service
   */
  pause(): Promise<void>;

  /**
   * Resume the service
   */
  resume(): Promise<void>;

  /**
   * Check if service is paused
   */
  isPaused(): boolean;
}
