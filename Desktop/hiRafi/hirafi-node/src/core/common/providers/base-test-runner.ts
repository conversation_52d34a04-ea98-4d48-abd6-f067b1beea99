/**
 * Unified Test Runner
 * Consolidates AndroidTestRunner and WebTestRunner into a single implementation
 * Eliminates duplicate code and provides platform-agnostic test execution
 */

import { EventEmitter } from 'events';
import { ITestRunner } from '../../test-execution/interfaces/test-runner.interface.js';
import { IScreenshotProvider } from '../interfaces/screenshot-provider.interface.js';
import { IVideoRecorder } from '../interfaces/video-recorder.interface.js';
import { IMetricsCollector } from '../interfaces/metrics-collector.interface.js';
import { ITestProgressReporter } from '../../test-execution/interfaces/test-progress-reporter.interface.js';
import { IStepHandlerRegistry } from '../interfaces/step-handler-registry.interface.js';
import {
  ScenarioStep,
  TestRequest,
  TestResult,
  StepResult,
  StepStatus,
  EnvironmentSettings,
  defaultEnvironmentSettings
} from '../../../models/types.js';
import { ILoggerService, LoggerServiceFactory } from '../../../utils/logger-service.js';
import { ConfigurationManager } from '../../test-execution/services/configuration/ai-configuration-service.js';

// Interface for step monitoring guardian (minimal interface to avoid circular dependencies)
interface IStepGuardian {
  startStepGuarding(testId: string, stepIndex: number, stepType: string): void;
  clearStepTimeout(testId: string, stepIndex: number): void;
}

/**
 * Unified Test Runner
 * Implements test execution logic for all platforms (Android and Web)
 */
export class UnifiedTestRunner extends EventEmitter implements ITestRunner {
  protected screenshotProvider: IScreenshotProvider;
  protected videoRecorder: IVideoRecorder<any>;
  protected metricsCollector: IMetricsCollector;
  protected progressReporter: ITestProgressReporter;
  protected stepHandlerRegistry: IStepHandlerRegistry;
  protected platformManager: any; // Device manager or browser manager
  protected platform: 'android' | 'web';
  protected logger: ILoggerService;
  protected stepGuardian: IStepGuardian | null = null; // Guardian for step timeout monitoring

  protected isRunning: boolean = false;
  protected abortRequested: boolean = false;
  protected cleanupInProgress: boolean = false;
  protected _currentTestId: string | null = null;
  protected testLogs: string[] = [];
  protected _nodeId: string = '';
  protected config: any = {};
  protected currentTarget: any = null; // Current device or page
  protected currentTestRequest: TestRequest | null = null; // Current test request for accessing scenarioId
  protected testiniumSessionId: string | null = null; // Testinium session ID for tracking

  /**
   * Create a new UnifiedTestRunner
   * @param platformManager Platform manager (device manager or browser manager)
   * @param screenshotProvider Screenshot provider
   * @param videoRecorder Video recorder
   * @param metricsCollector Metrics collector
   * @param progressReporter Progress reporter
   * @param stepHandlerRegistry Step handler registry
   * @param config Configuration object
   * @param stepGuardian Optional guardian for step monitoring
   */
  constructor(
    platformManager: any,
    screenshotProvider: IScreenshotProvider,
    videoRecorder: IVideoRecorder<any>,
    metricsCollector: IMetricsCollector,
    progressReporter: ITestProgressReporter,
    stepHandlerRegistry: IStepHandlerRegistry,
    config: any = {},
    stepGuardian?: IStepGuardian
  ) {
    super();
    this.platformManager = platformManager;
    this.screenshotProvider = screenshotProvider;
    this.videoRecorder = videoRecorder;
    this.metricsCollector = metricsCollector;
    this.progressReporter = progressReporter;
    this.stepHandlerRegistry = stepHandlerRegistry;
    this.stepGuardian = stepGuardian || null;
    this.config = config;
    this.logger = LoggerServiceFactory.createServiceLogger('UnifiedTestRunner');

    // Detect platform based on manager type
    this.platform = this.detectPlatform(platformManager);

    this.logger.info(`UnifiedTestRunner: Initialized for ${this.platform} platform with${this.stepGuardian ? '' : 'out'} step guardian monitoring`);
  }

  /**
   * Detect platform based on manager type
   * @param manager Platform manager
   * @returns Platform type
   */
  private detectPlatform(manager: any): 'android' | 'web' {
    if (manager && typeof manager.createBrowser === 'function') {
      return 'web';
    }
    return 'android';
  }

  /**
   * Initialize the test runner
   * @param nodeId Node identifier
   * @param config Configuration object
   */
  async initialize(nodeId: string, config: any): Promise<void> {
    this._nodeId = nodeId;
    this.config = { ...this.config, ...config };
    this.logger.info(`BaseTestRunner: Initialized for node ${nodeId}`);
  }

  /**
   * Run a test - template method that calls platform-specific implementations
   * @param testRequest Test request
   * @returns Promise resolving to the test result
   */
  async runTest(testRequest: TestRequest): Promise<TestResult> {
    if (this.isRunning) {
      throw new Error('Test runner is already running a test');
    }

    this.isRunning = true;
    this.abortRequested = false;
    this.cleanupInProgress = false; // Reset cleanup flag for new test
    this._currentTestId = testRequest.id;
    this.currentTestRequest = testRequest; // Store current test request for accessing scenarioId
    this.testLogs = [];

    const testId = testRequest.id;
    const startTime = Date.now();

    let result: Partial<TestResult> = {
      id: testId,
      success: false,
      status: 'failed',
      startTime: new Date().toISOString(),
      endTime: '',
      duration: 0,
      steps: [],
      logs: [],
      error: null
    };

    // Initialize stepResults outside try block so it's accessible in catch
    let stepResults: StepResult[] = [];

    try {
      this.logger.info(`UnifiedTestRunner: Starting test ${testId}`);

      // Platform-specific setup
      const target = await this.setupTestEnvironment(testRequest);

      // Validate target before proceeding
      if (!target) {
        throw new Error('Failed to create test environment target');
      }

      // For web platform, validate page is still connected
      if (this.platform === 'web') {
        if (typeof target.isClosed === 'function' && target.isClosed()) {
          throw new Error('Browser page was closed during setup');
        }
        const browser = target.browser && target.browser();
        if (browser && !browser.connected) {
          throw new Error('Browser disconnected during setup');
        }
      }

      // Apply environment settings
      await this.applyEnvironmentSettings(target, testRequest.environmentSettings || defaultEnvironmentSettings);

      // Set up metrics collection with validation
      try {
        await this.metricsCollector.setup(target, testId, testRequest.reportSettings);
        // Start metrics collection after successful setup
        await this.metricsCollector.startCollection(target);
      } catch (metricsError: any) {
        this.logger.warn(`UnifiedTestRunner: Failed to setup or start metrics collection: ${metricsError.message}`);
        // Continue with test execution even if metrics setup/start fails
      }

      // Execute test steps
      const steps = testRequest.scenario?.steps || [];

      // ENHANCED SAFETY: Additional validation before starting expensive operations
      if (steps.length === 0) {
        this.logger.warn(`UnifiedTestRunner: Test ${testId} has no steps - this should have been caught by validation. Stopping execution immediately.`);
        throw new Error('Test execution halted: No steps to execute');
      }

      // Validate that we have meaningful steps before starting video recording
      const validSteps = steps.filter((step: any) => 
        step && 
        typeof step === 'object' && 
        step.type && 
        (step.value || step.name || step.prompt)
      );

      if (validSteps.length === 0) {
        this.logger.warn(`UnifiedTestRunner: Test ${testId} has no valid steps - all steps are empty or malformed. Stopping execution immediately.`);
        throw new Error('Test execution halted: No valid steps to execute');
      }

      this.logger.info(`UnifiedTestRunner: Test ${testId} validation passed - proceeding with ${validSteps.length} valid steps out of ${steps.length} total steps`);

      // Start video recording if enabled (only after validation)
      if (testRequest.recordVideo !== false) {
        this.logger.info(`UnifiedTestRunner: Starting video recording for test ${testId} with ${validSteps.length} valid steps`);
        await this.videoRecorder.startRecording(target, testId);
      }

      // Notify test started
      this.notifyTestStarted(testRequest, steps.length);

      stepResults = await this.executeTestSteps(target, testRequest, steps, steps.length);

      // Build basic result immediately for fast transmission
      const success = stepResults.length > 0 && stepResults.every(step => step.success);

      // Calculate summary from step results - use actual total steps from scenario
      const actualTotalSteps = testRequest.scenario?.totalSteps || testRequest.scenario?.steps?.length || stepResults.length;
      const summary = {
        total: actualTotalSteps,
        passed: stepResults.filter(step => step.success).length,
        failed: stepResults.filter(step => !step.success).length,
        errors: stepResults.filter(step => !step.success && step.error).length
      };

      // Collect final metrics synchronously before result construction
      let collectedMetrics: any = {};
      try {
        this.logger.info(`UnifiedTestRunner: Collecting final metrics synchronously for test ${testId}`);
        const finalMetrics = await this.collectFinalMetrics({
          ensureCompletion: true,
          maxTimeout: 8000,
          waitForAllOperations: true
        });

        if (finalMetrics && typeof finalMetrics === 'object') {
          collectedMetrics = finalMetrics;
          this.logger.info(`UnifiedTestRunner: Successfully collected metrics for test ${testId} with ${Object.keys(finalMetrics).length} metric categories`);
        } else {
          this.logger.debug(`UnifiedTestRunner: No metrics collected for test ${testId}`);
        }
      } catch (metricsError: any) {
        this.logger.warn(`UnifiedTestRunner: Error collecting final metrics for test ${testId}: ${metricsError.message}`);
        // Continue with empty metrics - don't fail the test due to metrics collection issues
      }

      // Create result with collected metrics included
      result = {
        ...result,
        success,
        status: success ? 'passed' : 'failed',
        endTime: new Date().toISOString(),
        duration: Date.now() - startTime,
        steps: stepResults,
        summary,
        logs: this.testLogs,
        metrics: collectedMetrics, // Include collected metrics in result
        enhancedMetrics: collectedMetrics, // Also include in enhancedMetrics field for compatibility
        videoUrl: undefined, // Will be updated when video upload completes
        testiniumSessionId: this.testiniumSessionId ?? undefined // Include Testinium session ID if available
      };

      // Notify test completed immediately (this triggers result transmission)
      this.notifyTestCompleted(testRequest, result as TestResult);

      this.logger.info(`UnifiedTestRunner: Test ${testId} completed with success: ${result.success} - Result with metrics transmitted immediately`);

      // Start async operations that don't block result transmission
      this.performAsyncCleanupOperations(testId, testRequest, target);
    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error during test execution';
      this.logger.error(`UnifiedTestRunner: Error running test ${testId}: ${errorMessage}`);

      // Calculate summary for error case - use actual total steps from scenario
      const actualTotalSteps = testRequest.scenario?.totalSteps || testRequest.scenario?.steps?.length || stepResults.length || 1;
      const summary = {
        total: actualTotalSteps,
        passed: stepResults.filter(step => step.success).length,
        failed: stepResults.filter(step => !step.success).length + (stepResults.length === 0 ? 1 : 0),
        errors: 1
      };

      result = {
        ...result,
        success: false,
        error: errorMessage,
        status: 'failed',
        endTime: new Date().toISOString(),
        duration: Date.now() - startTime,
        summary,
        testiniumSessionId: this.testiniumSessionId ?? undefined // Include Testinium session ID even in error cases
      };
    } finally {
      // Platform-specific cleanup
      await this.cleanupTestEnvironment();
      this.isRunning = false;
      this._currentTestId = null;
      this.currentTestRequest = null; // Clear current test request
      this.testiniumSessionId = null; // Clear Testinium session ID
    }

    return result as TestResult;
  }

  /**
   * Execute test steps - consolidated logic from both Android and Web runners
   * @param target Platform-specific target (Page, AndroidAgent, etc.)
   * @param testRequest Test request
   * @param steps Steps to execute
   * @param totalSteps Total number of steps
   * @returns Promise resolving to step results
   */
  async executeTestSteps(target: any, testRequest: TestRequest, steps: ScenarioStep[], totalSteps: number): Promise<StepResult[]> {
    const stepResults: StepResult[] = [];
    const testId = testRequest.id;

    this.logger.info(`UnifiedTestRunner: Starting execution of ${totalSteps} steps for test ${testId}`);

    for (let i = 0; i < steps.length; i++) {
      if (this.abortRequested) {
        this.logger.warn(`UnifiedTestRunner: Test aborted, stopping step execution at step ${i + 1}`);
        break;
      }

      const step = steps[i];
      const stepIndex = i + 1;

      try {
        this.logger.info(`UnifiedTestRunner: Executing step ${stepIndex}/${totalSteps} (${step.type}) for test ${testId}`);

        // 🛡️ START GUARDIAN STEP MONITORING before step execution
        if (this.stepGuardian) {
          this.stepGuardian.startStepGuarding(testId, stepIndex, step.type);
          this.logger.debug(`🛡️ Guardian: Started monitoring step ${stepIndex} (${step.type}) for test ${testId}`);
        }

        // Send step progress - starting
        this.sendStepProgress(testId, step, stepIndex, totalSteps, StepStatus.STARTED);

        // Execute the step
        const stepResult = await this.executeTestStep(target, step, testId, stepIndex);
        stepResults.push(stepResult);

        // 🛡️ CLEAR GUARDIAN STEP MONITORING after successful completion
        if (this.stepGuardian) {
          this.stepGuardian.clearStepTimeout(testId, stepIndex);
          this.logger.debug(`🛡️ Guardian: Cleared step timeout for test ${testId} step ${stepIndex} - completed successfully`);
        }

        // Send step progress - completed
        this.sendStepProgress(testId, step, stepIndex, totalSteps, stepResult.success ? StepStatus.COMPLETED : StepStatus.FAILED, stepResult);

        this.logger.info(`UnifiedTestRunner: Step ${stepIndex}/${totalSteps} ${stepResult.success ? 'passed' : 'failed'} for test ${testId}`);

        // Always stop on any step failure
        if (!stepResult.success) {
          this.logger.warn(`UnifiedTestRunner: Step failed - terminating test execution`);
          break;
        }

      } catch (error: any) {
        this.logger.error(`UnifiedTestRunner: Error executing step ${stepIndex} for test ${testId}: ${error.message}`);

        // 🛡️ CLEAR GUARDIAN STEP MONITORING on error
        if (this.stepGuardian) {
          this.stepGuardian.clearStepTimeout(testId, stepIndex);
          this.logger.debug(`🛡️ Guardian: Cleared step timeout for test ${testId} step ${stepIndex} - failed with error`);
        }

        const failedStepResult: StepResult = {
          id: step.id || `step-${stepIndex}`,
          name: this.generateStepName(step),
          type: step.type,
          status: 'failed',
          success: false,
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          duration: 0,
          error: error.message || 'Unknown error',
          data: null,
          logs: []
        };

        stepResults.push(failedStepResult);
        this.sendStepProgress(testId, step, stepIndex, totalSteps, StepStatus.FAILED, failedStepResult);

        // Always stop on any step failure
        this.logger.warn(`UnifiedTestRunner: Step error - terminating test execution`);
        break;
      }
    }

    this.logger.info(`UnifiedTestRunner: Completed execution of ${stepResults.length} steps for test ${testId}`);
    return stepResults;
  }

  /**
   * Execute a single test step - consolidated logic
   * @param target Platform-specific target
   * @param step Step to execute
   * @param testId Test identifier
   * @param stepIndex Step index
   * @returns Promise resolving to the step result
   */
  async executeTestStep(target: any, step: ScenarioStep, testId: string, stepIndex: number): Promise<StepResult> {
    const startTime = new Date();
    const startTimeISO = startTime.toISOString();

    try {
      this.logger.debug(`UnifiedTestRunner: Executing step ${stepIndex} (${step.type}) for test ${testId}`);

      // Create logs array to collect step logs
      const stepLogs: string[] = [];

      // Pre-step health check for web platform
      if (this.platform === 'web') {
        await this.performWebHealthCheck(target, testId, stepIndex);
      }

      // Get platform-specific agent for AI steps
      const agent = await this.getAgentForStep(target, step);

      // Take before screenshot (immediate capture, async upload)
      let beforeScreenshotUrl: string | null = null;
      try {
        if (this.screenshotProvider) {
          // CRITICAL FIX: Screenshot buffer is captured immediately while page is open
          // The upload happens asynchronously but the image data is already captured
          beforeScreenshotUrl = await this.screenshotProvider.takeScreenshot(
            target,
            testId,
            step.id || `step-${stepIndex}`,
            'before',
            stepIndex
          );
          this.logger.debug(`UnifiedTestRunner: Before screenshot captured and queued for step ${stepIndex}: ${beforeScreenshotUrl}`);
        }
      } catch (screenshotError: any) {
        // Provide more context about screenshot failures
        const errorContext = this.getScreenshotErrorContext(screenshotError);
        this.logger.warn(`UnifiedTestRunner: Error taking before screenshot for step ${stepIndex}: ${screenshotError.message} ${errorContext}`);

        // Check if screenshot failure indicates browser crash
        if (this.isScreenshotErrorIndicatingCrash(screenshotError)) {
          this.logger.error(`UnifiedTestRunner: Screenshot error indicates browser crash for test ${testId} step ${stepIndex}`);
          throw new Error(`Browser appears to have crashed: ${screenshotError.message}`);
        }
      }

      // Set current test request for variable resolution
      if (typeof this.stepHandlerRegistry.setCurrentTestRequest === 'function') {
        this.stepHandlerRegistry.setCurrentTestRequest(this.currentTestRequest);
      }

      // Set progress reporter for nested step reporting with test context
      this.stepHandlerRegistry.setProgressReporter(this.progressReporter, testId, {
        runId: this.currentTestRequest?.runId,
        executionId: this.currentTestRequest?.executionId,
        scenarioId: this.currentTestRequest?.scenarioId
      });

      // Set screenshot provider for nested step screenshots
      if (typeof this.stepHandlerRegistry.setScreenshotProvider === 'function') {
        this.stepHandlerRegistry.setScreenshotProvider(this.screenshotProvider);
      }

      // Execute step with logs collection
      const result = await this.stepHandlerRegistry.executeStep(target, step, agent, stepLogs);

      // Take after screenshot (immediate capture, async upload)
      let afterScreenshotUrl: string | null = null;
      try {
        if (this.screenshotProvider) {
          // CRITICAL FIX: Screenshot buffer is captured immediately while page is open
          // This is especially important for the last step's after screenshot
          afterScreenshotUrl = await this.screenshotProvider.takeScreenshot(
            target,
            testId,
            step.id || `step-${stepIndex}`,
            'after',
            stepIndex
          );
          this.logger.debug(`UnifiedTestRunner: After screenshot captured and queued for step ${stepIndex}: ${afterScreenshotUrl}`);
        }
      } catch (screenshotError: any) {
        // Provide more context about screenshot failures
        const errorContext = this.getScreenshotErrorContext(screenshotError);
        this.logger.warn(`UnifiedTestRunner: Error taking after screenshot for step ${stepIndex}: ${screenshotError.message} ${errorContext}`);
      }

      const endTime = new Date();
      const endTimeISO = endTime.toISOString();

      // Use AI duration if available, otherwise calculate from timestamps
      let duration: number;
      if (step.type.startsWith('ai') && result.data?.aiDuration) {
        // Use AI-provided duration for AI steps
        duration = result.data.aiDuration;
        this.logger.debug(`UnifiedTestRunner: Using AI duration for step ${stepIndex}: ${duration}ms`);
      } else {
        // Calculate duration from timestamps for non-AI steps
        duration = endTime.getTime() - startTime.getTime();
        this.logger.debug(`UnifiedTestRunner: Calculated duration for step ${stepIndex}: ${duration}ms`);
      }

      const stepResult: StepResult = {
        id: step.id || `step-${stepIndex}`,
        name: this.generateStepName(step),
        type: step.type,
        status: result.success ? 'passed' : 'failed',
        success: result.success,
        startTime: startTimeISO,
        endTime: endTimeISO,
        duration: duration,
        error: result.error || null,
        data: result.data || null,
        logs: stepLogs,
        beforeScreenshotUrl: beforeScreenshotUrl,
        afterScreenshotUrl: afterScreenshotUrl,
        
        // Map control flow data from handler result
        controlFlowData: this.mapControlFlowData(step.type, result.data)
      };

      this.logger.debug(`UnifiedTestRunner: Step ${stepIndex} completed in ${duration}ms with status: ${stepResult.status}, collected ${stepLogs.length} logs`);
      return stepResult;

    } catch (error: any) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      this.logger.error(`UnifiedTestRunner: Error executing step ${stepIndex}: ${error.message}`);

      return {
        id: step.id || `step-${stepIndex}`,
        name: this.generateStepName(step),
        type: step.type,
        status: 'failed',
        success: false,
        startTime: startTimeISO,
        endTime: endTime.toISOString(),
        duration: duration,
        error: error.message || 'Unknown error',
        data: null,
        logs: [],
        beforeScreenshotUrl: null,
        afterScreenshotUrl: null
      };
    }
  }

  // Platform-specific implementations consolidated
  protected async setupTestEnvironment(testRequest: TestRequest): Promise<any> {
    if (this.platform === 'web') {
      return this.setupWebEnvironment(testRequest);
    } else {
      return this.setupAndroidEnvironment(testRequest);
    }
  }

  protected async cleanupTestEnvironment(): Promise<void> {
    // Idempotency guard - prevent duplicate cleanup calls
    if (this.cleanupInProgress) {
      this.logger.debug(`UnifiedTestRunner: Cleanup already in progress for ${this.platform} platform, skipping duplicate call`);
      return;
    }

    this.cleanupInProgress = true;
    this.logger.info(`UnifiedTestRunner: Starting comprehensive test environment cleanup for ${this.platform} platform`);

    try {
      // Make cleanup idempotent and robust - each step wrapped in try-catch
      const cleanupSteps = [
        { name: 'Video Recording', action: () => this.cleanupVideoRecording() },
        { name: 'Metrics Collection', action: () => this.cleanupMetricsCollection() },
        { name: 'Platform Environment', action: () => this.cleanupPlatformEnvironment() },
        { name: 'Step Handler Registry', action: () => this.cleanupStepHandlerRegistry() },
        { name: 'AI Configuration', action: () => this.cleanupAIConfiguration() }
      ];

      for (const step of cleanupSteps) {
        try {
          await step.action();
          this.logger.debug(`UnifiedTestRunner: Successfully completed ${step.name} cleanup`);
        } catch (error: any) {
          this.logger.error(`UnifiedTestRunner: Error during ${step.name} cleanup: ${error.message}`);
          // Continue with other cleanup steps even if one fails
        }
      }

      this.logger.info(`UnifiedTestRunner: Completed comprehensive test environment cleanup for ${this.platform} platform`);
    } catch (error: any) {
      this.logger.error(`UnifiedTestRunner: Error during cleanup: ${error.message}`);
      throw error;
    }
    // Note: cleanupInProgress flag is NOT reset here to maintain idempotency
    // It will be reset when a new test starts or the runner is disposed
  }

  /**
   * Cleanup video recording with enhanced error handling
   */
  private async cleanupVideoRecording(): Promise<void> {
    if (this.videoRecorder) {
      try {
        // Check if cleanup method exists, otherwise use stopRecording
        if (typeof (this.videoRecorder as any).cleanup === 'function') {
          await (this.videoRecorder as any).cleanup();
        } else if (typeof this.videoRecorder.stopRecording === 'function') {
          await this.videoRecorder.stopRecording();
        }
        this.logger.debug('UnifiedTestRunner: Video recorder cleanup completed');
      } catch (error: any) {
        this.logger.error(`UnifiedTestRunner: Error cleaning up video recorder: ${error.message}`);
        // Force reset video recorder reference
        this.videoRecorder = null as any;
      }
    }
  }

  /**
   * Collect final metrics before cleanup to prevent race conditions
   * This method is called by TestExecutionManager before browser cleanup
   * Enhanced with coordinated promise handling and comprehensive error management
   */
  async collectFinalMetrics(options: any = {}): Promise<any> {
    try {
      this.logger.info(`UnifiedTestRunner: Starting coordinated final metrics collection for ${this.platform} platform`);

      if (!this.metricsCollector || !this.currentTarget) {
        this.logger.debug('UnifiedTestRunner: No metrics collector or target available for final collection');
        return null;
      }

      // Extract options with defaults
      const {
        ensureCompletion = false,
        maxTimeout = 12000,
        waitForAllOperations = false
      } = options;

      // Phase 1: Pre-flight checks to prevent race conditions
      if (this.platform === 'web') {
        // Check if page is still valid
        if (typeof this.currentTarget.isClosed === 'function' && this.currentTarget.isClosed()) {
          this.logger.warn(`UnifiedTestRunner: Page is already closed for ${this.platform} platform, skipping metrics collection`);
          return null;
        }

        // Check if browser is still connected
        const browser = this.currentTarget.browser();
        if (!browser || !browser.connected) {
          this.logger.warn(`UnifiedTestRunner: Browser is disconnected for ${this.platform} platform, skipping metrics collection`);
          return null;
        }
      }

      // Phase 2: Coordinated metrics collection with enhanced options
      this.logger.info(`UnifiedTestRunner: Phase 2 - Collecting final metrics with coordination for ${this.platform} platform`);

      const metricsOptions = {
        includeNetworkData: true,
        includeAccessibilityData: true,
        timeout: maxTimeout,
        ensureCompletion,
        waitForAllOperations
      };

      let finalMetrics;
      if (ensureCompletion) {
        // Use the enhanced coordinated collection for web platform
        if (this.platform === 'web' && typeof this.metricsCollector.collectWithCoordination === 'function') {
          finalMetrics = await this.metricsCollector.collectWithCoordination(this.currentTarget, metricsOptions);
        } else {
          finalMetrics = await this.metricsCollector.collectMetrics(this.currentTarget, metricsOptions);
        }
      } else {
        finalMetrics = await this.metricsCollector.collectMetrics(this.currentTarget, metricsOptions);
      }

      this.logger.info(`UnifiedTestRunner: Coordinated final metrics collection completed for ${this.platform} platform`);

      // Store final metrics for later use if needed
      if (finalMetrics && typeof finalMetrics === 'object') {
        const metricCategories = Object.keys(finalMetrics).length;
        this.logger.debug(`UnifiedTestRunner: Final metrics collected successfully with ${metricCategories} metric categories`);
        return finalMetrics;
      }

      return null;

    } catch (error: any) {
      this.logger.warn(`UnifiedTestRunner: Error during coordinated final metrics collection: ${error.message}`);

      // Log specific patterns that indicate race conditions have been prevented
      if (error.message.includes('Session closed') ||
          error.message.includes('Target closed') ||
          error.message.includes('Protocol error')) {
        this.logger.info(`UnifiedTestRunner: Successfully prevented race condition - coordinated metrics collection attempted before browser closure`);
      }

      // Don't throw - this is a best-effort collection
      return null;
    }
  }

  /**
   * Cleanup metrics collection with enhanced error handling
   */
  private async cleanupMetricsCollection(): Promise<void> {
    if (this.metricsCollector) {
      try {
        await this.metricsCollector.stopCollection();
        // Check if cleanup method exists, otherwise just stop collection
        if (typeof (this.metricsCollector as any).cleanup === 'function') {
          await (this.metricsCollector as any).cleanup();
        }
        this.logger.debug('UnifiedTestRunner: Metrics collector cleanup completed');
      } catch (error: any) {
        this.logger.error(`UnifiedTestRunner: Error cleaning up metrics collector: ${error.message}`);
        // Force reset metrics collector reference
        this.metricsCollector = null as any;
      }
    }
  }

  /**
   * Cleanup platform-specific environment
   */
  private async cleanupPlatformEnvironment(): Promise<void> {
    if (this.platform === 'web') {
      await this.cleanupWebEnvironment();
    } else {
      await this.cleanupAndroidEnvironment();
    }
  }

  /**
   * Cleanup step handler registry
   * Note: Don't destroy registry as it's now created per-test (TRANSIENT lifecycle)
   * Just clear the reference to allow garbage collection
   */
  private async cleanupStepHandlerRegistry(): Promise<void> {
    if (this.stepHandlerRegistry) {
      try {
        // Don't destroy the registry - it's a fresh instance per test
        // Just clear the reference to allow garbage collection
        this.stepHandlerRegistry = null as any;
        this.logger.debug('UnifiedTestRunner: Cleared step handler registry reference (per-test instance)');
      } catch (error: any) {
        this.logger.error(`UnifiedTestRunner: Error clearing step handler registry reference: ${error.message}`);
        // Force reset reference
        this.stepHandlerRegistry = null as any;
      }
    }
  }

  /**
   * Cleanup AI configuration
   */
  private async cleanupAIConfiguration(): Promise<void> {
    try {
      const aiManager = new ConfigurationManager();
      await aiManager.resetConfiguration();
      this.logger.info('UnifiedTestRunner: AI configuration reset successfully');
    } catch (error: any) {
      this.logger.warn(`UnifiedTestRunner: Failed to reset AI configuration: ${error.message}`);
    }
  }

  public async applyEnvironmentSettings(target: any, settings: EnvironmentSettings): Promise<void> {
    if (this.platform === 'web') {
      await this.applyWebEnvironmentSettings(target, settings);
    } else {
      await this.applyAndroidEnvironmentSettings(target, settings);
    }
  }

  protected async getAgentForStep(target: any, step: ScenarioStep): Promise<any> {
    if (this.platform === 'web') {
      return this.getWebAgentForStep(target, step);
    } else {
      return this.getAndroidAgentForStep(target, step);
    }
  }

  // Web-specific implementations
  private async setupWebEnvironment(testRequest: TestRequest): Promise<any> {
    try {
      this.logger.info(`UnifiedTestRunner: Starting web test ${testRequest.id}`);

              // Create browser and page (using new headless mode)
      const browser = await this.platformManager.createBrowser();
      const page = await this.platformManager.createPage(browser, {
        testId: testRequest.id,
        scenarioId: testRequest.scenarioId  // Pass scenarioId for caching
      });

      this.currentTarget = page;
      return page;
    } catch (error: any) {
      this.logger.error(`UnifiedTestRunner: Error setting up web test environment: ${error.message}`);
      throw error;
    }
  }

  private async cleanupWebEnvironment(): Promise<void> {
    try {
      // Close the current page first
      if (this.currentTarget) {
        await this.currentTarget.close();
        this.logger.debug('UnifiedTestRunner: Closed web page');
      }

      // Delegate browser cleanup to BrowserManager (single source of truth)
      if (this.platformManager && typeof this.platformManager.forceCleanupAll === 'function') {
        await this.platformManager.forceCleanupAll();
        this.logger.debug('UnifiedTestRunner: Delegated browser cleanup to BrowserManager');
      }
    } catch (error: any) {
      this.logger.error(`UnifiedTestRunner: Error during web cleanup: ${error.message}`);
    } finally {
      // Always clear the current target reference
      this.currentTarget = null;
    }
  }

  private async applyWebEnvironmentSettings(target: any, settings: EnvironmentSettings): Promise<void> {
    if (!target) {
      throw new Error('Browser page not initialized');
    }

    try {
      // Cast to web settings to access viewport and userAgent
      const webSettings = settings as any;

      // Always set viewport with deviceScaleFactor to ensure it's applied
      const viewportConfig = {
        deviceScaleFactor: webSettings.deviceScaleFactor || 2,
        width: webSettings.viewport?.width || 1600,
        height: webSettings.viewport?.height || 900
      };

      await target.setViewport(viewportConfig);
      this.logger.debug(`UnifiedTestRunner: Set viewport to ${viewportConfig.width}x${viewportConfig.height} with deviceScaleFactor: ${viewportConfig.deviceScaleFactor}`);

      // Set user agent if specified
      if (webSettings.userAgent) {
        await target.setUserAgent(webSettings.userAgent);
        this.logger.debug(`UnifiedTestRunner: Set user agent to ${webSettings.userAgent}`);
      }

      this.logger.debug('UnifiedTestRunner: Applied web environment settings');
    } catch (error: any) {
      this.logger.warn(`UnifiedTestRunner: Error applying web environment settings: ${error.message}`);
    }
  }

  private async getWebAgentForStep(target: any, step: ScenarioStep): Promise<any> {
    // Create or get AI agent for AI-powered steps and control flow steps that need AI evaluation
    const needsAgent = step.type.startsWith('ai') || 
                      step.type === 'ifElse' || 
                      step.type === 'whileLoop' || 
                      step.type === 'forLoop';
    
    if (needsAgent) {
      try {
        // Get existing agent or create one with scenarioId if available
        const existingAgent = this.platformManager.getPageAgent(target);
        if (existingAgent) {
          return existingAgent;
        }

        // Use scenarioId from current test request if available
        const scenarioId = this.currentTestRequest?.scenarioId || 'web-agent';
        return await this.platformManager.createAgent(target, scenarioId);
      } catch (error: any) {
        this.logger.warn(`UnifiedTestRunner: Error getting web agent for step: ${error.message}`);
        return null;
      }
    }
    return null;
  }

  /**
   * Get context information for screenshot errors
   * @param error Screenshot error
   * @returns Context string for logging
   */
  private getScreenshotErrorContext(error: any): string {
    const errorMessage = error.message || '';

    if (errorMessage.includes('Protocol error')) {
      return '(Browser protocol communication issue - may be transient)';
    } else if (errorMessage.includes('Page is closed')) {
      return '(Browser page was closed during screenshot)';
    } else if (errorMessage.includes('Browser is disconnected')) {
      return '(Browser connection lost)';
    } else if (errorMessage.includes('Target closed')) {
      return '(Browser target was destroyed)';
    } else if (errorMessage.includes('Session closed')) {
      return '(Browser session terminated)';
    } else {
      return '(Unknown screenshot error)';
    }
  }

  /**
   * Check if screenshot error indicates browser crash
   * @param error Screenshot error
   * @returns True if error indicates browser crash
   */
  private isScreenshotErrorIndicatingCrash(error: any): boolean {
    const errorMessage = error.message || '';

    // Critical errors that indicate browser crash
    const crashIndicators = [
      'Page is closed',
      'Browser is disconnected',
      'Target closed',
      'Session closed',
      'Connection terminated',
      'Browser process exited',
      'Protocol error (Runtime.callFunctionOn): Session closed',
      'Protocol error (Page.captureScreenshot): Session closed'
    ];

    return crashIndicators.some(indicator => errorMessage.includes(indicator));
  }

  /**
   * Perform health check for web platform before step execution
   * @param target Browser page target
   * @param testId Test identifier
   * @param stepIndex Step index
   */
  private async performWebHealthCheck(target: any, testId: string, stepIndex: number): Promise<void> {
    try {
      this.logger.debug(`UnifiedTestRunner: Performing web health check for test ${testId} step ${stepIndex}`);

      // Quick check if page is still open
      if (typeof target.isClosed === 'function' && target.isClosed()) {
        this.logger.error(`UnifiedTestRunner: Page is closed for test ${testId} step ${stepIndex}`);
        throw new Error('Browser page was closed unexpectedly');
      }

      // Quick check browser connection
      const browser = target.browser && target.browser();
      if (browser) {
        if (!browser.connected) {
          this.logger.error(`UnifiedTestRunner: Browser disconnected for test ${testId} step ${stepIndex}`);
          throw new Error('Browser connection lost');
        }

        // Quick check browser process
        const process = browser.process();
        if (process && process.killed) {
          this.logger.error(`UnifiedTestRunner: Browser process killed for test ${testId} step ${stepIndex}`);
          throw new Error('Browser process was terminated');
        }
      }

      // Lightweight responsiveness check with short timeout
      try {
        await Promise.race([
          target.evaluate(() => document.readyState),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), 1500)
          )
        ]);
        this.logger.debug(`UnifiedTestRunner: Page responsiveness check passed for test ${testId} step ${stepIndex}`);
      } catch (evalError: any) {
        // Only fail for genuine crashes, not timeouts
        if (this.isScreenshotErrorIndicatingCrash(evalError) && !evalError.message.includes('timeout')) {
          throw new Error(`Page is unresponsive: ${evalError.message}`);
        }
        
        // Log timeout but don't fail
        if (evalError.message.includes('timeout')) {
          this.logger.warn(`UnifiedTestRunner: Health check timeout for test ${testId} step ${stepIndex}, proceeding anyway`);
        } else {
          this.logger.warn(`UnifiedTestRunner: Page responsiveness check failed for test ${testId} step ${stepIndex}: ${evalError.message}`);
        }
      }

    } catch (error: any) {
      this.logger.error(`UnifiedTestRunner: Web health check failed for test ${testId} step ${stepIndex}: ${error.message}`);
      throw error;
    }
  }

  // Android-specific implementations
  private async setupAndroidEnvironment(testRequest: TestRequest): Promise<any> {
    try {
      const environmentSettings = testRequest.environmentSettings;

      // Check if we have Android environment settings
      if (environmentSettings && (environmentSettings as any).platform === 'android') {
        const androidSettings = environmentSettings as any;

        // Enhanced device provider detection logic
        let deviceProvider = androidSettings.deviceProvider;

        if (!deviceProvider) {
          // Auto-detect provider based on configuration and device selection
          const sauceLabsDevices = androidSettings.sauceLabs?.selectedDevices || [];
          const testiniumDevices = androidSettings.testinium?.selectedDevices || [];
          const hasSauceLabsCredentials = androidSettings.sauceLabs?.username && androidSettings.sauceLabs?.accessKey;
          const hasTestiniumCredentials = androidSettings.testinium?.apiUrl && androidSettings.testinium?.clientId;

          if (testiniumDevices.length > 0 && sauceLabsDevices.length === 0) {
            deviceProvider = 'testinium';
            this.logger.info(`UnifiedTestRunner: Auto-detected Testinium provider based on selected devices`);
          } else if (sauceLabsDevices.length > 0 && testiniumDevices.length === 0) {
            deviceProvider = 'sauceLabs';
            this.logger.info(`UnifiedTestRunner: Auto-detected SauceLabs provider based on selected devices`);
          } else if (hasTestiniumCredentials && !hasSauceLabsCredentials) {
            deviceProvider = 'testinium';
            this.logger.info(`UnifiedTestRunner: Auto-detected Testinium provider based on credentials`);
          } else if (hasSauceLabsCredentials && !hasTestiniumCredentials) {
            deviceProvider = 'sauceLabs';
            this.logger.info(`UnifiedTestRunner: Auto-detected SauceLabs provider based on credentials`);
          } else if (sauceLabsDevices.length > 0 && testiniumDevices.length > 0) {
            this.logger.warn(`UnifiedTestRunner: Both SauceLabs and Testinium devices selected, defaulting to SauceLabs`);
            deviceProvider = 'sauceLabs';
          } else {
            // Default to SauceLabs for backward compatibility
            deviceProvider = 'sauceLabs';
            this.logger.info(`UnifiedTestRunner: No provider detected, defaulting to SauceLabs for backward compatibility`);
          }
        }

        this.logger.info(`UnifiedTestRunner: Setting up Android environment with provider: ${deviceProvider}`);

        if (deviceProvider === 'sauceLabs' && androidSettings.sauceLabs) {
          const sauceLabsConfig = androidSettings.sauceLabs;

          // Create SauceLabs configuration object
          const androidConfig = {
            android: {
              sauceLabs: {
                enabled: true,
                user: sauceLabsConfig.username,
                key: sauceLabsConfig.accessKey,
                region: sauceLabsConfig.region || 'us-west-1',
                username: sauceLabsConfig.username,
                accessKey: sauceLabsConfig.accessKey,
                options: {
                  appiumVersion: 'stable'
                }
              }
            }
          };

          try {
            // Initialize the device manager with SauceLabs config
            await this.platformManager.initialize(androidConfig);

            // Connect to SauceLabs device
            this.currentTarget = await this.platformManager.connectToAppiumHub(testRequest);

            return this.currentTarget;
          } catch (sauceLabsError: any) {
            this.logger.error(`UnifiedTestRunner: Error connecting to SauceLabs device: ${sauceLabsError.message}`);
            throw sauceLabsError;
          }
        } else if (deviceProvider === 'testinium' && androidSettings.testinium) {
          const testiniumConfig = androidSettings.testinium;

          // Create Testinium configuration object
          const androidConfig = {
            android: {
              testinium: {
                enabled: true,
                apiUrl: testiniumConfig.apiUrl,
                clientId: testiniumConfig.clientId,
                clientSecret: testiniumConfig.clientSecret,
                issuerUri: testiniumConfig.issuerUri,
                options: {
                  appiumVersion: 'stable'
                }
              }
            }
          };

          try {
            // Initialize the device manager with Testinium config
            await this.platformManager.initialize(androidConfig);

            // Connect to Testinium device
            this.currentTarget = await this.platformManager.connectToAppiumHub(testRequest);

            // Capture Testinium session ID for reporting
            if (this.platformManager.getCurrentSessionId) {
              this.testiniumSessionId = this.platformManager.getCurrentSessionId();
              this.logger.info(`UnifiedTestRunner: Captured Testinium session ID: ${this.testiniumSessionId}`);
            }

            return this.currentTarget;
          } catch (testiniumError: any) {
            this.logger.error(`UnifiedTestRunner: Error connecting to Testinium device: ${testiniumError.message}`);
            throw testiniumError;
          }
        } else {
          this.logger.warn(`UnifiedTestRunner: No valid provider configuration found for ${deviceProvider}, falling back to local device`);
          // Local device connection
          this.currentTarget = await this.platformManager.connectToDevice();
          return this.currentTarget;
        }
      }
      // Legacy support: Check for SauceLabs configuration in environment settings
      else if (environmentSettings && (environmentSettings as any).sauceLabs) {
        this.logger.info(`UnifiedTestRunner: Using legacy SauceLabs configuration`);
        const sauceLabsConfig = (environmentSettings as any).sauceLabs;

        // Create SauceLabs configuration object
        const androidConfig = {
          android: {
            sauceLabs: {
              enabled: true,
              user: sauceLabsConfig.username,
              key: sauceLabsConfig.accessKey,
              region: sauceLabsConfig.region || 'us-west-1',
              username: sauceLabsConfig.username,
              accessKey: sauceLabsConfig.accessKey,
              options: {
                appiumVersion: 'stable'
              }
            }
          }
        };

        try {
          // Initialize the device manager
          await this.platformManager.initialize(androidConfig);

          // Connect to SauceLabs device
          this.currentTarget = await this.platformManager.connectToAppiumHub(testRequest);

          return this.currentTarget;
        } catch (sauceLabsError: any) {
          this.logger.error(`UnifiedTestRunner: Error connecting to SauceLabs device: ${sauceLabsError.message}`);
          throw sauceLabsError;
        }
      }
      // Legacy support: Check for Testinium configuration in environment settings
      else if (environmentSettings && (environmentSettings as any).testinium) {
        this.logger.info(`UnifiedTestRunner: Using legacy Testinium configuration`);
        const testiniumConfig = (environmentSettings as any).testinium;

        // Create Testinium configuration object
        const androidConfig = {
          android: {
            testinium: {
              enabled: true,
              apiUrl: testiniumConfig.apiUrl,
              clientId: testiniumConfig.clientId,
              clientSecret: testiniumConfig.clientSecret,
              issuerUri: testiniumConfig.issuerUri,
              options: {
                appiumVersion: 'stable'
              }
            }
          }
        };

        try {
          // Initialize the device manager with Testinium config
          await this.platformManager.initialize(androidConfig);

          // Connect to Testinium device
          this.currentTarget = await this.platformManager.connectToAppiumHub(testRequest);

          // Capture Testinium session ID for reporting
          if (this.platformManager.getCurrentSessionId) {
            this.testiniumSessionId = this.platformManager.getCurrentSessionId();
            this.logger.info(`UnifiedTestRunner: Captured Testinium session ID: ${this.testiniumSessionId}`);
          }

          return this.currentTarget;
        } catch (testiniumError: any) {
          this.logger.error(`UnifiedTestRunner: Error connecting to Testinium device: ${testiniumError.message}`);
          throw testiniumError;
        }
      }
      else {
        // Local device connection
        this.currentTarget = await this.platformManager.connectToDevice();
        return this.currentTarget;
      }
    } catch (error: any) {
      this.logger.error(`UnifiedTestRunner: Error setting up Android test environment: ${error.message}`);
      throw error;
    }
  }

  private async cleanupAndroidEnvironment(): Promise<void> {
    if (this.currentTarget) {
      try {
        this.logger.info('UnifiedTestRunner: Starting Android environment cleanup');

        // Enhanced cleanup for SauceLabs sessions
        await this.performEnhancedAndroidCleanup();

        // Disconnect from device
        await this.platformManager.disconnectFromDevice(this.currentTarget);

        this.logger.info('UnifiedTestRunner: Android environment cleanup completed');
      } catch (error: any) {
        this.logger.error(`UnifiedTestRunner: Error during Android cleanup: ${error.message}`);
        // Continue with cleanup even if there are errors
      } finally {
        this.currentTarget = null;
      }
    }
  }

  /**
   * Perform enhanced cleanup for Android sessions, especially SauceLabs and Testinium
   */
  private async performEnhancedAndroidCleanup(): Promise<void> {
    if (!this.currentTarget || !this.currentTarget.page) {
      return;
    }

    try {
      // Check if this is a SauceLabs session
      const isSauceLabs = this.currentTarget.page.serverConfig?.hostname?.includes('saucelabs.com');
      // Check if this is a Testinium session
      const isTestinium = this.currentTarget.page.serverConfig?.hostname?.includes('testinium.io') ||
                         this.currentTarget.page.serverConfig?.hostname?.includes('devicepark');

      if (isSauceLabs) {
        this.logger.info('UnifiedTestRunner: Performing enhanced SauceLabs cleanup');

        // Send a final command to ensure session is still active
        try {
          await this.currentTarget.page.getCurrentActivity();
          this.logger.debug('UnifiedTestRunner: Sent final activity check command');
        } catch (activityError: any) {
          this.logger.warn(`UnifiedTestRunner: Failed to get current activity: ${activityError.message}`);
        }

        // Set test result if not already set
        try {
          if (typeof this.currentTarget.page.execute === 'function') {
            await this.currentTarget.page.execute('sauce:job-result=passed');
            this.logger.debug('UnifiedTestRunner: Set SauceLabs job result to passed');
          }
        } catch (resultError: any) {
          this.logger.warn(`UnifiedTestRunner: Failed to set job result: ${resultError.message}`);
        }
      } else if (isTestinium) {
        this.logger.info('UnifiedTestRunner: Performing enhanced Testinium cleanup');

        // Send a final command to ensure session is still active
        try {
          await this.currentTarget.page.getCurrentActivity();
          this.logger.debug('UnifiedTestRunner: Sent final activity check command for Testinium');
        } catch (activityError: any) {
          this.logger.warn(`UnifiedTestRunner: Failed to get current activity on Testinium: ${activityError.message}`);
        }

        // Testinium-specific cleanup if needed
        try {
          // Add any Testinium-specific cleanup logic here
          this.logger.debug('UnifiedTestRunner: Completed Testinium-specific cleanup');
        } catch (testiniumError: any) {
          this.logger.warn(`UnifiedTestRunner: Failed Testinium cleanup: ${testiniumError.message}`);
        }
      }
    } catch (error: any) {
      this.logger.warn(`UnifiedTestRunner: Enhanced cleanup failed: ${error.message}`);
    }
  }

  private async applyAndroidEnvironmentSettings(_target: any, _settings: EnvironmentSettings): Promise<void> {
    // Android-specific environment settings application
  }

  private async getAndroidAgentForStep(target: any, step: ScenarioStep): Promise<any> {
    // For Android, the target (AndroidAgent) is already the AI agent for AI-powered steps and control flow steps
    const needsAgent = step.type.startsWith('ai') || 
                      step.type === 'ifElse' || 
                      step.type === 'whileLoop' || 
                      step.type === 'forLoop';
    
    if (needsAgent) {
      return target; // AndroidAgent is already the AI agent
    }
    return null;
  }

  // Common utility methods

  /**
   * Generate meaningful step name based on step type
   * @param step The scenario step
   * @param parentStepId Optional parent step ID for nested steps
   * @param nestingLevel Optional nesting level (0 for top level, 1 for first nested, etc.)
   * @returns Formatted step name
   */
  private generateStepName(step: ScenarioStep, parentStepId?: string, nestingLevel?: number): string {
    let stepName = step.name || step.type;

    // Handle nested steps with prefix
    const isNested = nestingLevel !== undefined && nestingLevel > 0;
    const prefix = isNested ? `  ${'  '.repeat(nestingLevel - 1)}└─ ` : '';

    // Generate human-readable step names based on step type
    switch (step.type) {
      case 'goto':
        const url = step.url || step.value || (step.name && step.name.startsWith('http') ? step.name : null);
        if (isNested) {
          stepName = url ? `Go to ${url}` : 'Go to page';
        } else {
          if (url) {
            stepName = `Navigate to ${url}`;
          } else {
            stepName = 'Navigate to page';
          }
        }
        break;

      case 'aiInput':
        const textToInput = step.value || step.prompt;
        const inputTarget = step.target || step.name || step.description;
        if (isNested) {
          // Shorter names for nested steps
          stepName = textToInput ? `Input "${textToInput}"` : 'Input text';
        } else {
          if (textToInput && inputTarget && inputTarget !== textToInput) {
            stepName = `Input "${textToInput}" into ${inputTarget}`;
          } else if (textToInput) {
            stepName = `Input "${textToInput}"`;
          } else {
            stepName = 'Input text';
          }
        }
        break;

      case 'aiKeyboardPress':
        const keyToPress = step.value || step.prompt || step.name || step.description;
        const keyTarget = step.target;
        if (isNested) {
          stepName = keyToPress ? `Press "${keyToPress}"` : 'Press key';
        } else {
          if (keyToPress && keyTarget && keyTarget !== keyToPress) {
            stepName = `Press "${keyToPress}" on ${keyTarget}`;
          } else if (keyToPress) {
            stepName = `Press "${keyToPress}"`;
          } else {
            stepName = 'Press key';
          }
        }
        break;

      case 'aiTap':
        const tapTarget = step.prompt || step.value || step.name || step.description;
        if (isNested) {
          stepName = tapTarget ? `Click ${tapTarget}` : 'Click element';
        } else {
          if (tapTarget) {
            stepName = `Click ${tapTarget}`;
          } else {
            stepName = 'Click element';
          }
        }
        break;

      case 'aiHover':
        const hoverTarget = step.prompt || step.value || step.name || step.description;
        if (hoverTarget) {
          stepName = `Hover over ${hoverTarget}`;
        } else {
          stepName = 'Hover over element';
        }
        break;

      case 'aiScroll':
        const scrollDetails = step.prompt || step.value || step.name || step.description;
        if (scrollDetails) {
          stepName = `Scroll ${scrollDetails}`;
        } else {
          stepName = 'Scroll page';
        }
        break;

      case 'aiRightClick':
        const rightClickTarget = step.prompt || step.value || step.name || step.description;
        if (rightClickTarget) {
          stepName = `Right-click ${rightClickTarget}`;
        } else {
          stepName = 'Right-click element';
        }
        break;

      case 'aiAction':
        const actionText = step.prompt || step.value || step.name || step.description;
        if (isNested) {
          stepName = actionText || 'Action';
        } else {
          if (actionText) {
            stepName = actionText;
          } else {
            stepName = 'Perform action';
          }
        }
        break;

      case 'aiAssertion':
        const assertionText = step.prompt || step.value || step.name || step.description;
        if (isNested) {
          stepName = assertionText ? `Verify ${assertionText}` : 'Verify condition';
        } else {
          if (assertionText) {
            stepName = `Verify: ${assertionText}`;
          } else {
            stepName = 'Verify condition';
          }
        }
        break;

      case 'aiWaitElement':
        const elementDescription = step.prompt || step.value || step.name || step.description;
        if (elementDescription) {
          stepName = `Wait for ${elementDescription}`;
        } else {
          stepName = 'Wait for element';
        }
        break;

      case 'aiLocate':
        const locateQuery = step.prompt || step.value || step.name || step.description;
        if (locateQuery) {
          stepName = `Locate ${locateQuery}`;
        } else {
          stepName = 'Locate element';
        }
        break;

      case 'aiQuery':
        const queryText = step.prompt || step.value || step.name || step.description;
        if (queryText) {
          stepName = `Query: ${queryText}`;
        } else {
          stepName = 'Query page';
        }
        break;

      case 'aiString':
        const stringQuery = step.prompt || step.value || step.name || step.description;
        if (stringQuery) {
          stepName = `Extract text: ${stringQuery}`;
        } else {
          stepName = 'Extract text';
        }
        break;

      case 'aiNumber':
        const numberQuery = step.prompt || step.value || step.name || step.description;
        if (numberQuery) {
          stepName = `Extract number: ${numberQuery}`;
        } else {
          stepName = 'Extract number';
        }
        break;

      case 'aiBoolean':
        const booleanQuestion = step.prompt || step.value || step.name || step.description;
        if (booleanQuestion) {
          stepName = `Check: ${booleanQuestion}`;
        } else {
          stepName = 'Check condition';
        }
        break;

      case 'sleep':
        const duration = step.duration || (step.value ? Math.max(1, Math.floor(parseInt(step.value) / 1000)) : 1);
        stepName = `Wait ${duration} second${duration !== 1 ? 's' : ''}`;
        break;

      // Control Flow Steps
      case 'ifElse':
        const condition = step.condition || step.value || step.prompt;
        if (condition) {
          stepName = `If: ${condition}`;
        } else {
          stepName = 'If-Else condition';
        }
        break;

      case 'forLoop':
        const iterationQuery = step.iterationCount || step.value || step.prompt;
        const loopStepsCount = step.loopSteps ? step.loopSteps.length : 0;
        if (iterationQuery) {
          stepName = `For each: ${iterationQuery} (${loopStepsCount} steps)`;
        } else {
          stepName = `For loop (${loopStepsCount} steps)`;
        }
        break;

      case 'whileLoop':
        const whileCondition = step.condition || step.value || step.prompt;
        const whileStepsCount = step.loopSteps ? step.loopSteps.length : 0;
        if (whileCondition) {
          stepName = `While: ${whileCondition} (${whileStepsCount} steps)`;
        } else {
          stepName = `While loop (${whileStepsCount} steps)`;
        }
        break;

      default:
        // For any other step types, use the existing logic
        stepName = step.name || step.type;
        break;
    }

    // Add prefix for nested steps
    return prefix + stepName;
  }

  public sendStepProgress(testId: string, step: ScenarioStep, stepIndex: number, totalSteps: number, status: StepStatus, additionalData?: any, parentStepId?: string, nestingLevel?: number): void {
    // Get test request context for additional metadata
    const testRequest = this.currentTestRequest;

    this.logger.info(`UnifiedTestRunner: Sending step progress for test ${testId} - Step ${stepIndex}/${totalSteps}: ${this.generateStepName(step, parentStepId, nestingLevel)} (${status})`);

    // Report to progress reporter (WebSocket)
    this.progressReporter.reportStepProgress(testId, {
      type: 'step',
      stepIndex,
      totalSteps,
      stepId: step.id || `step-${stepIndex}`,
      stepName: this.generateStepName(step, parentStepId, nestingLevel),
      stepType: step.type,
      status: status as any,
      timestamp: new Date().toISOString(),
      result: additionalData,
      // Add test context metadata
      runId: testRequest?.runId,
      executionId: testRequest?.executionId,
      scenarioId: testRequest?.scenarioId,
      // Add nested step metadata
      parentStepId,
      nestingLevel
    }).catch(error => {
      this.logger.warn(`UnifiedTestRunner: Error reporting step progress: ${error.message}`);
    });

    // Note: Step monitoring is now handled centrally by the Hub via step progress service
  }

  /**
   * Map control flow data from handler result to StepResult format
   * @param stepType The type of step
   * @param handlerData Data returned from the step handler
   * @returns Mapped control flow data or undefined
   */
  private mapControlFlowData(stepType: string, handlerData: any): any {
    if (!handlerData || typeof handlerData !== 'object') {
      return undefined;
    }

    // Handle IF-ELSE control flow
    if (stepType === 'ifElse') {
      return {
        conditionResult: handlerData.conditionResult,
        executedBranch: handlerData.executedBranch,
        trueSteps: handlerData.trueSteps || [],
        falseSteps: handlerData.falseSteps || []
      };
    }

    // Handle FOR LOOP control flow
    if (stepType === 'forLoop') {
      return {
        iterationCount: handlerData.iterationCount,
        completedIterations: handlerData.completedIterations,
        totalStepsExecuted: handlerData.totalStepsExecuted,
        failedIteration: handlerData.failedIteration,
        failedStepIndex: handlerData.failedStepIndex
      };
    }

    // Handle WHILE LOOP control flow
    if (stepType === 'whileLoop') {
      return {
        iterationCount: handlerData.maxIterations,
        completedIterations: handlerData.completedIterations,
        totalStepsExecuted: handlerData.totalStepsExecuted,
        hitMaxIterations: handlerData.hitMaxIterations,
        failedIteration: handlerData.failedIteration,
        failedStepIndex: handlerData.failedStepIndex
      };
    }

    return undefined;
  }

  public notifyTestStarted(testRequest: TestRequest, totalSteps: number): void {
    this.emit('testStarted', { testId: testRequest.id, totalSteps });
  }

  public notifyTestCompleted(testRequest: TestRequest, result: TestResult): void {
    this.emit('testCompleted', { testId: testRequest.id, result });
  }

  /**
   * Perform async cleanup operations that don't block result transmission
   * @param testId Test ID
   * @param testRequest Test request
   * @param target Platform target
   */
  private performAsyncCleanupOperations(testId: string, testRequest: TestRequest, target: any): void {
    // Run async operations in background without blocking
    (async () => {
      try {
        this.logger.debug(`UnifiedTestRunner: Starting async cleanup operations for test ${testId}`);

        // 1. Generate memory report from AI agent (web platform only)
        if (this.platform === 'web') {
          try {
            await this.generateMemoryReport(target, testId);
          } catch (memoryError: any) {
            this.logger.warn(`UnifiedTestRunner: Failed to generate memory report for test ${testId}: ${memoryError.message}`);
          }
        }

        // 2. Stop video recording asynchronously
        if (testRequest.recordVideo !== false) {
          try {
            // Stop recording without waiting for upload
            if (this.videoRecorder.stopRecordingAsync) {
              await this.videoRecorder.stopRecordingAsync((videoTestId: string, uploadedVideoUrl: string, error?: Error) => {
                if (error) {
                  this.logger.error(`UnifiedTestRunner: Error uploading video for test ${videoTestId}: ${error.message}`);
                } else {
                  this.logger.info(`UnifiedTestRunner: Video uploaded successfully for test ${videoTestId}: ${uploadedVideoUrl}`);
                  // Report video upload completion to hub via progressReporter (consistent with other notifications)
                  this.progressReporter.reportVideoUploadCompleted(videoTestId, uploadedVideoUrl).catch((notifyError: any) => {
                    this.logger.error(`UnifiedTestRunner: Error notifying hub of video upload for test ${videoTestId}: ${notifyError.message}`);
                  });
                }
              });
            } else {
              // Fallback to synchronous recording
              const videoUrl = await this.videoRecorder.stopRecording();
              if (videoUrl) {
                this.logger.info(`UnifiedTestRunner: Video recorded synchronously for test ${testId}: ${videoUrl}`);
                // Report video upload completion to hub via progressReporter (consistent with other notifications)
                try {
                  await this.progressReporter.reportVideoUploadCompleted(testId, videoUrl);
                } catch (notifyError: any) {
                  this.logger.error(`UnifiedTestRunner: Error notifying hub of video upload for test ${testId}: ${notifyError.message}`);
                }
              }
            }
            this.logger.debug(`UnifiedTestRunner: Video recording stopped for test ${testId}`);
          } catch (videoError: any) {
            this.logger.warn(`UnifiedTestRunner: Error stopping video recording for test ${testId}: ${videoError.message}`);
          }
        }

        this.logger.debug(`UnifiedTestRunner: Completed async cleanup operations for test ${testId}`);
      } catch (error: any) {
        this.logger.error(`UnifiedTestRunner: Error in async cleanup operations for test ${testId}: ${error.message}`);
      }
    })().catch(error => {
      this.logger.error(`UnifiedTestRunner: Unhandled error in async cleanup for test ${testId}: ${error}`);
    });
  }

  /**
   * Generate memory report from AI agent
   * @param target Platform target (Page for web)
   * @param testId Test ID
   */
  private async generateMemoryReport(target: any, testId: string): Promise<void> {
    try {
      // Get the AI agent from the browser manager
      const agent = this.platformManager.getPageAgent(target);

      if (!agent) {
        this.logger.debug(`UnifiedTestRunner: No AI agent found for memory report generation for test ${testId}`);
        return;
      }

      // Check if agent has memory reporting methods
      if (typeof agent.getMemoryReport !== 'function' || typeof agent.getMemorySummary !== 'function') {
        this.logger.debug(`UnifiedTestRunner: AI agent does not support memory reporting for test ${testId}`);
        return;
      }

      // Get memory report and summary
      const report = agent.getMemoryReport();
      const summary = agent.getMemorySummary();

      if (!report || !summary) {
        this.logger.debug(`UnifiedTestRunner: No memory data available from AI agent for test ${testId}`);
        return;
      }

      // Log memory report in the specified format
      this.logger.info('🎯 Test Results:');
      this.logger.info(`   Total Steps: ${report.summary?.totalItems || 0}`);
      this.logger.info(`   Success Rate: ${report.analytics?.successRate || 0}%`);
      this.logger.info(`   Memory Effectiveness: ${report.summary?.memoryEffectiveness || 0}%`);
      this.logger.info(`   Data Extracted: ${report.analytics?.dataExtractionCount || 0} items`);

      this.logger.info('\n📊 Workflow Steps:');
      if (summary.recentSteps && Array.isArray(summary.recentSteps)) {
        summary.recentSteps.forEach((step: any, i: number) => {
          const stepInfo = step.step || 'Unknown step';
          const stepType = step.type || 'unknown';
          const success = step.success ? '✅' : '❌';
          this.logger.info(`   ${i + 1}. ${stepInfo} (${stepType}) - ${success}`);
        });
      } else {
        this.logger.info('   No workflow steps available');
      }

      this.logger.info('\n📋 Extracted Data:');
      if (summary.dataExtracted) {
        try {
          this.logger.info(JSON.stringify(summary.dataExtracted, null, 2));
        } catch (jsonError) {
          this.logger.info('   Unable to serialize extracted data');
        }
      } else {
        this.logger.info('   No data extracted');
      }

      this.logger.debug(`UnifiedTestRunner: Memory report generated successfully for test ${testId}`);
    } catch (error: any) {
      this.logger.error(`UnifiedTestRunner: Error generating memory report for test ${testId}: ${error.message}`);
    }
  }

  // ITestRunner interface methods
  async abortTest(): Promise<void> {
    this.abortRequested = true;
    this.logger.info(`UnifiedTestRunner: Test abort requested`);

    // Only perform cleanup if not already in progress or completed
    // The CleanupOrchestrator will handle the actual cleanup coordination
    if (!this.cleanupInProgress) {
      try {
        await this.cleanupTestEnvironment();
        this.logger.info(`UnifiedTestRunner: Cleanup completed during abort`);
      } catch (cleanupError: any) {
        this.logger.error(`UnifiedTestRunner: Error during cleanup on abort: ${cleanupError.message}`);
      }
    } else {
      this.logger.debug(`UnifiedTestRunner: Cleanup already in progress, abort will not trigger duplicate cleanup`);
    }
  }

  /**
   * Dispose method for complete cleanup
   */
  async dispose(): Promise<void> {
    try {
      this.logger.info(`UnifiedTestRunner: Starting disposal cleanup`);
      this.abortRequested = true;
      this.isRunning = false;

      // Only perform cleanup if not already in progress or completed
      // The CleanupOrchestrator will handle the actual cleanup coordination
      if (!this.cleanupInProgress) {
        await this.cleanupTestEnvironment();
        this.logger.info(`UnifiedTestRunner: Disposal cleanup completed`);
      } else {
        this.logger.debug(`UnifiedTestRunner: Cleanup already in progress, dispose will not trigger duplicate cleanup`);
      }
    } catch (error: any) {
      this.logger.error(`UnifiedTestRunner: Error during disposal: ${error.message}`);
      throw error;
    }
  }

  isTestRunning(): boolean {
    return this.isRunning;
  }

  getCurrentTestId(): string | null {
    return this._currentTestId;
  }

  getNodeId(): string {
    return this._nodeId;
  }

  /**
   * Set step guardian for timeout monitoring
   * This method allows runtime injection of guardian from ExecutionCoordinator
   */
  setStepGuardian(stepGuardian: IStepGuardian): void {
    this.stepGuardian = stepGuardian;
    this.logger.info(`UnifiedTestRunner: Step guardian ${stepGuardian ? 'enabled' : 'disabled'} for timeout monitoring`);
  }

  /**
   * Get step guardian status
   */
  getStepGuardianStatus(): { enabled: boolean; guardian?: IStepGuardian } {
    return {
      enabled: this.stepGuardian !== null,
      guardian: this.stepGuardian || undefined
    };
  }
}

// Backward compatibility export
export { UnifiedTestRunner as BaseTestRunner };
