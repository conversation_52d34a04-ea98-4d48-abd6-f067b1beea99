"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/lib/utils/toast-utils"
import { Copy, Loader2, FolderIcon, Files, Hash } from "lucide-react"
import { useScenarioManager } from "@/hooks/useScenarioManager"
import { useScenariosActions } from "@/hooks/useScenariosActions"

interface BulkDuplicateScenarioDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  scenarioIds: string[]
  onSuccess?: (duplicatedCount: number) => void
}

export function BulkDuplicateScenarioDialog({
  open,
  onOpenChange,
  scenarioIds,
  onSuccess
}: BulkDuplicateScenarioDialogProps) {
  const [isDuplicating, setIsDuplicating] = useState(false)
  const [namePrefix, setNamePrefix] = useState("Copy")
  const [selectedFolder, setSelectedFolder] = useState<string>("keep-original")
  const [useCustomNaming, setUseCustomNaming] = useState(false)
  const [addNumbers, setAddNumbers] = useState(true)

  // Get bulk duplicate function
  const { bulkDuplicateScenarios } = useScenariosActions()

  // Fetch folders
  const {
    folders,
    foldersLoading
  } = useScenarioManager({
    folderLimit: 50,
    autoFetch: true
  })

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setNamePrefix("Copy")
      setSelectedFolder("keep-original")
      setUseCustomNaming(false)
      setAddNumbers(true)
    }
  }, [open])

  // Generate preview names
  const getPreviewExample = () => {
    if (!useCustomNaming) {
      return addNumbers 
        ? [`${namePrefix} Original Scenario 1`, `${namePrefix} Original Scenario 2`, "..."]
        : [`${namePrefix} Original Scenario`, `${namePrefix} Original Scenario`, "..."]
    }
    
    return addNumbers
      ? [`${namePrefix} 1`, `${namePrefix} 2`, `${namePrefix} 3`, "..."]
      : [`${namePrefix}`, `${namePrefix}`, `${namePrefix}`, "..."]
  }

  // Handle bulk duplication
  const handleBulkDuplicate = async () => {
    if (scenarioIds.length === 0) return

    setIsDuplicating(true)

    try {
      const options: any = {}

      // Set naming options
      if (namePrefix.trim()) {
        if (useCustomNaming) {
          // For custom naming, we'll use namePrefix as the base name
          options.customNamePattern = namePrefix.trim()
          options.addNumbers = addNumbers
        } else {
          // For prefix mode, we'll add prefix to original names
          options.namePrefix = namePrefix.trim()
        }
      }

      // Set folder options
      if (selectedFolder !== "keep-original") {
        options.folderId = selectedFolder === "uncategorized" ? null : selectedFolder
      }

      // Call bulk duplicate API
      const result = await bulkDuplicateScenarios(scenarioIds, options)

      if (result.success) {
        toast.success("Senaryolar Kopyalandı", {
          description: `${result.duplicatedCount} senaryo başarıyla kopyalandı`
        })

        // Call success callback
        if (onSuccess) {
          onSuccess(result.duplicatedCount)
        }

        // Close dialog
        onOpenChange(false)
      } else {
        toast.error("Senaryo Kopyalama Hatası", {
          description: result.error || "Senaryolar kopyalanırken bir hata oluştu"
        })
      }
    } catch (error) {
      console.error("Bulk duplication error:", error)
      toast.error("Senaryo Kopyalama Hatası", {
        description: "Senaryolar kopyalanırken bir hata oluştu"
      })
    } finally {
      setIsDuplicating(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] border-0 shadow-lg bg-white dark:bg-gray-900 rounded-xl">
        <DialogHeader className="pb-4 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
              <Files className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Bulk Duplicate Scenarios
              </DialogTitle>
              <DialogDescription className="text-gray-500 dark:text-gray-400">
                Create copies of {scenarioIds.length} selected scenarios with custom settings.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-5 py-5">
          {/* Scenario Count Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                {scenarioIds.length} scenarios selected for duplication
              </span>
            </div>
          </div>

          {/* Naming Strategy */}
          <div className="space-y-3">
            <Label className="text-gray-700 dark:text-gray-300 font-medium">Naming Strategy</Label>
            
            {/* Custom Naming Toggle */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="useCustomNaming"
                checked={useCustomNaming}
                onChange={(e) => setUseCustomNaming(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Label htmlFor="useCustomNaming" className="text-sm text-gray-600 dark:text-gray-400">
                Use custom naming pattern instead of prefix
              </Label>
            </div>

            {/* Name Prefix/Pattern Input */}
            <div className="space-y-2">
              <Label htmlFor="namePrefix" className="text-sm font-medium">
                {useCustomNaming ? "Custom Name Pattern" : "Name Prefix"}
              </Label>
              <Input
                id="namePrefix"
                value={namePrefix}
                onChange={(e) => setNamePrefix(e.target.value)}
                placeholder={useCustomNaming ? "e.g., 'Test Scenario'" : "e.g., 'Copy', 'Test', 'V2'"}
                className="border-gray-200 dark:border-gray-700 rounded-md"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {useCustomNaming 
                  ? "Each scenario will be named with this pattern (numbers will be appended if enabled)"
                  : "This prefix will be added to the beginning of each original scenario name"
                }
              </p>
            </div>

            {/* Add Numbers Toggle */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="addNumbers"
                checked={addNumbers}
                onChange={(e) => setAddNumbers(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Label htmlFor="addNumbers" className="text-sm text-gray-600 dark:text-gray-400">
                Add sequential numbers to duplicated scenarios
              </Label>
            </div>
          </div>

          {/* Folder Selection */}
          <div className="space-y-3">
            <Label className="text-gray-700 dark:text-gray-300 font-medium">Destination Folder</Label>
            <Select
              value={selectedFolder}
              onValueChange={setSelectedFolder}
              disabled={foldersLoading}
            >
              <SelectTrigger className="border-gray-200 dark:border-gray-700 rounded-md">
                <SelectValue placeholder="Select destination folder" />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
                <SelectItem value="keep-original">
                  <div className="flex items-center gap-2">
                    <FolderIcon className="h-4 w-4 text-gray-500" />
                    Keep Original Folders
                  </div>
                </SelectItem>
                <SelectItem value="uncategorized">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                    Uncategorized
                  </div>
                </SelectItem>
                {folders.map(folder => (
                  <SelectItem key={folder.id} value={folder.id}>
                    <div className="flex items-center gap-2">
                      <div 
                        className="h-2 w-2 rounded-full" 
                        style={{ backgroundColor: folder.color ? `var(--${folder.color}-500)` : '#6b7280' }}
                      ></div>
                      {folder.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Preview */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30">
                  <Copy className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Preview
                </span>
              </div>
              <div className="pl-8 space-y-2">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Naming examples:</span>
                </p>
                <div className="bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-700">
                  {getPreviewExample().map((example, index) => (
                    <div key={index} className="text-xs text-gray-600 dark:text-gray-400 font-mono">
                      → {example}
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Destination:</span> {
                    selectedFolder === "keep-original" ? "Original folders" :
                    selectedFolder === "uncategorized" ? "Uncategorized" :
                    folders.find(f => f.id === selectedFolder)?.name || "Selected folder"
                  }
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Total scenarios to create:</span> {scenarioIds.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="border-t border-gray-100 dark:border-gray-800 pt-4 mt-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            Cancel
          </Button>
          <Button
            onClick={handleBulkDuplicate}
            disabled={isDuplicating || scenarioIds.length === 0 || !namePrefix.trim()}
            className="gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 border-none"
          >
            {isDuplicating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Duplicating {scenarioIds.length} scenarios...
              </>
            ) : (
              <>
                <Files className="h-4 w-4" />
                Duplicate {scenarioIds.length} Scenarios
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 