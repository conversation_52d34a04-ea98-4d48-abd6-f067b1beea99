import type { VariableType, EnvironmentType, ValueConstraints } from "./test-data"

export type DataTemplateType =
  | "user_profiles"
  | "product_catalog"
  | "payment_data"
  | "login_credentials"
  | "api_responses"
  | "test_scenarios"
  | "custom"

export interface EnhancedDataVariable {
  id: string
  name: string
  value: string // Keep for backward compatibility, will be computed from environmentValues
  type: VariableType
  description: string
  isRequired?: boolean
  format?: string
  constraints?: ValueConstraints
  environmentValues?: Record<string, string> // Environment ID -> Value mapping
  // API Source integration
  sourceType?: 'api' | 'database' | 'csv' | 'excel'
  sourceId?: string
  jsonPath?: string
  // Database Source integration
  dbMode?: string
  needsColumnMapping?: boolean
  nameColumn?: string
  valueColumn?: string
}

export interface DataSetFormData {
  name: string
  description: string
  variables?: EnhancedDataVariable[]
  tags: string[]
  environment: EnvironmentType
  aiPrompt?: string
}

export interface DataTemplate {
  id: DataTemplateType
  name: string
  description: string
  icon: React.ReactNode | null
  examplePrompt: string
  variables: Array<{
    name: string
    type: VariableType
    description: string
  }>
}

// Re-export ValueConstraints for convenience
export type { ValueConstraints, VariableType, EnvironmentType }
