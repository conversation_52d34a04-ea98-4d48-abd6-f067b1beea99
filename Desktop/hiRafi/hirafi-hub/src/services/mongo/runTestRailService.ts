/**
 * Run TestRail Service
 * Run için TestRail entegrasyonu işlemlerini yönetir
 */

import { logger } from '../../utils/logger.js';
import { db, isMongoDBInitialized, pluginsCollection, runsCollection, scenariosCollection } from './dbConnection.js';
import axios from 'axios';
import { config } from '../../config/index.js';
import { getRunReportByRunId, updateRunReportTestRail, getRunReportTestRailInfo } from './atomicReportService.js';

// TestRail API endpoints from config
const TESTRAIL_API_ADD_RUN = '/add_run/';

/**
 * Run için TestRail run oluşturur
 * @param runId Run ID
 * @param teamId Team ID
 * @param companyId Company ID
 * @param runName Run adı
 * @returns TestRail run ID ve URL'si
 */
export async function createTestRailRunForRun(
  runId: string,
  teamId: string,
  companyId: string,
  runName: string
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection || !runsCollection || !scenariosCollection) {
      return {
        success: false,
        message: "Database connection not established",
        data: null
      };
    }

    // TestRail run ID'si var mı kontrol et - run report'tan kontrol et
    const runReportResult = await getRunReportTestRailInfo(runId);

    if (runReportResult.success && runReportResult.testrailRunId) {
      logger.info(`Run ${runId} already has a TestRail run ID in run report: ${runReportResult.testrailRunId}`);
      return {
        success: true,
        message: "TestRail run already exists for this run",
        data: null,
        runId: runReportResult.testrailRunId,
        runUrl: runReportResult.testrailRunLink
      };
    }

    // Takım için TestRail konfigürasyonunu al
    const plugin = await pluginsCollection.findOne({
      teamId: teamId,
      companyId: companyId,
      "plugins.id": "testrail"
    });

    if (!plugin) {
      return {
        success: false,
        message: "TestRail plugin not configured for this team",
        data: null
      };
    }

    // TestRail plugin'ini bul
    const testrailPlugin = plugin.plugins.find((p: any) => p.id === "testrail");

    if (!testrailPlugin || !testrailPlugin.config) {
      return {
        success: false,
        message: "TestRail plugin configuration not found",
        data: null
      };
    }

    const pluginConfig = testrailPlugin.config;

    // Gerekli alanları kontrol et
    if (!pluginConfig.url || !pluginConfig.username || !pluginConfig.apiKey) {
      return {
        success: false,
        message: "Missing TestRail configuration",
        data: null
      };
    }

    // İlk projeyi al
    if (!pluginConfig.projectsData || !pluginConfig.projectsData.length) {
      return {
        success: false,
        message: "No projects configured in TestRail settings",
        data: null
      };
    }

    // İlk suite'i al
    if (!pluginConfig.suitesData || !pluginConfig.suitesData.length) {
      return {
        success: false,
        message: "No suites configured in TestRail settings",
        data: null
      };
    }

    const projectId = pluginConfig.projectsData[0].id;
    const suiteId = pluginConfig.suitesData[0].id;

    // TestRail auth bilgilerini ayarla
    const auth = {
      username: pluginConfig.username,
      password: pluginConfig.apiKey
    };

    // Run için tarih formatı
    const formattedDate = new Date().toISOString().split('T')[0];

    // Bu run için TestRail case ID'lerini topla
    let caseIds: number[] = [];

    // Runs koleksiyonundan run'ı al
    const run = await runsCollection.findOne({ id: runId });

    if (run && run.scenarioIds && run.scenarioIds.length > 0) {


      // Senaryoları al
      const scenarios = await scenariosCollection.find({
        id: { $in: run.scenarioIds },
        "testrailIntegration.sync": true // Sadece TestRail entegrasyonu olan senaryoları al
      }).toArray();



      // TestRail case ID'lerini topla
      for (const scenario of scenarios) {
        if (scenario.testrailIntegration &&
            scenario.testrailIntegration.caseIds &&
            scenario.testrailIntegration.caseIds.length > 0) {

          // Her bir case ID'sini sayıya çevir ve ekle
          for (const caseId of scenario.testrailIntegration.caseIds) {
            const numericCaseId = parseInt(caseId, 10);
            if (!isNaN(numericCaseId)) {
              caseIds.push(numericCaseId);
            }
          }
        }
      }

      logger.info(`Collected ${caseIds.length} TestRail case IDs for run ${runId}: ${caseIds.join(', ')}`);
    } else {
      logger.warn(`Run ${runId} has no scenarios or could not be found`);
    }

    // Eğer hiç TestRail case ID'si yoksa, TestRail run oluşturmaya gerek yok
    if (caseIds.length === 0) {
      logger.info(`No TestRail case IDs found for run ${runId}, skipping TestRail run creation`);
      return {
        success: false,
        message: "No TestRail case IDs found for this run",
        data: null
      };
    }

    // Case ID'leri doğrula - TestRail'de var olan case ID'leri kontrol et
    let apiData;
    try {
      // TestRail'den mevcut case ID'leri al
      const getCasesUrl = `${pluginConfig.url}/index.php?/api/v2/get_cases/${projectId}&suite_id=${suiteId}`;
      logger.info(`Fetching available TestRail cases from: ${getCasesUrl}`);

      const casesResponse = await axios.get(getCasesUrl, { auth });
      const availableCases = casesResponse.data.cases || [];
      const availableCaseIds = availableCases.map((c: any) => c.id);

      // Sadece TestRail'de var olan case ID'lerini kullan
      const validCaseIds = caseIds.filter(id => availableCaseIds.includes(id));

      if (validCaseIds.length === 0) {
        logger.warn(`None of the case IDs ${caseIds.join(', ')} exist in TestRail project ${projectId}, suite ${suiteId}`);
        return {
          success: false,
          message: "No valid TestRail case IDs found for this run",
          data: null
        };
      }

      // API verilerini hazırla
      apiData = {
        suite_id: suiteId,
        name: `${runName} - ${formattedDate}`,
        description: `Automated test run created for run ID: ${runId}`,
        include_all: false,
        case_ids: validCaseIds // Sadece geçerli case ID'leri ekle
      };
    } catch (error: any) {
      logger.error(`Error validating TestRail case IDs: ${error.message}`);

      // Hata durumunda, include_all: true ile devam et
      logger.info(`Falling back to include_all: true due to case ID validation error`);

      // API verilerini hazırla - tüm case'leri dahil et
      apiData = {
        suite_id: suiteId,
        name: `${runName} - ${formattedDate}`,
        description: `Automated test run created for run ID: ${runId}`,
        include_all: true
      };
    }

    // TestRail API URL'sini oluştur
    const url = `${pluginConfig.url}/index.php?/api/v2${TESTRAIL_API_ADD_RUN}${projectId}`;

    logger.info(`Creating TestRail run for run ID ${runId}: ${url}`);
    logger.info(`Run data: ${JSON.stringify(apiData)}`);

    try {
      // TestRail API'ye istek gönder
      let response;
      try {
        response = await axios.post(url, apiData, { auth });
      } catch (apiError: any) {
        // Eğer aynı ID ile bir run zaten varsa veya başka bir hata oluştuysa
        if (apiError.response && apiError.response.status === 400) {
          logger.warn(`Error creating TestRail run: ${apiError.message}`);
          logger.warn(`TestRail API response: ${JSON.stringify(apiError.response.data)}`);

          // Run ID'sine rastgele bir sayı ekleyerek tekrar deneyelim
          const randomSuffix = Math.floor(Math.random() * 10000);
          // Eğer apiData.name bir sayı içeriyorsa, o sayıyı çıkarıp yeni bir sayı ekleyelim
          let baseName = apiData.name;
          const idMatch = baseName.match(/\((\d+)\)$/);
          if (idMatch) {
            // Eğer isim zaten (123) gibi bir sayı içeriyorsa, bu kısmı çıkaralım
            baseName = baseName.replace(/\s*\(\d+\)$/, '');
          }

          const newRunName = `${baseName} (${randomSuffix})`;

          logger.info(`Retrying with a different run ID: ${newRunName}`);

          // API verilerini güncelle
          const newApiData = {
            ...apiData,
            name: newRunName
          };

          // Tekrar dene
          response = await axios.post(url, newApiData, { auth });
          logger.info(`TestRail run created successfully with random ID suffix after retry`);
        } else {
          // Başka bir hata ise, hatayı yukarı fırlat
          throw apiError;
        }
      }

      logger.info(`TestRail run created successfully with ID: ${response.data.id}`);

      // TestRail run URL'sini oluştur
      const runUrl = `${pluginConfig.url}/index.php?/runs/view/${response.data.id}`;
      logger.info(`TestRail run URL: ${runUrl}`);

      // Run'ı güncelle - response.data.id bir sayı olmalı
      let numericRunId: number;
      if (typeof response.data.id === 'number') {
        numericRunId = response.data.id;
      } else {
        numericRunId = parseInt(String(response.data.id), 10);
        if (isNaN(numericRunId)) {
          logger.error(`Invalid TestRail run ID returned from API: ${response.data.id} is not a valid number`);
          return {
            success: false,
            message: `Invalid TestRail run ID returned from API: ${response.data.id} is not a valid number`,
            data: null
          };
        }
      }

      logger.info(`Converted TestRail run ID to number: ${numericRunId} (original: ${response.data.id}, type: ${typeof response.data.id})`);

      // Run report'u güncelle
      const runReportResult = await getRunReportByRunId(runId);

      if (runReportResult.success && runReportResult.report) {
        // Run report'a TestRail bilgilerini ekle
        await updateRunReportTestRail(
          runReportResult.report.id,
          numericRunId,
          runUrl
        );

        logger.info(`Updated run report ${runReportResult.report.id} for run ${runId} with TestRail run ID: ${numericRunId} and URL: ${runUrl}`);
      } else {
        logger.warn(`No run report found for run ${runId}, cannot update TestRail information in run report`);
      }

      logger.info(`TestRail run created for run ${runId} with ID: ${numericRunId} and URL: ${runUrl}`);

      return {
        success: true,
        message: "TestRail run created successfully",
        data: response.data,
        runId: numericRunId,
        runUrl: runUrl
      };
    } catch (error: any) {
      logger.error(`Error creating TestRail run: ${error.message}`);
      if (error.response) {
        logger.error(`TestRail API response status: ${error.response.status}`);
        logger.error(`TestRail API response data: ${JSON.stringify(error.response.data)}`);
      }

      return {
        success: false,
        message: `Error creating TestRail run: ${error.message}`,
        data: error.response?.data
      };
    }
  } catch (error: any) {
    logger.error(`Error creating TestRail run for run ${runId}: ${error.message}`);
    return {
      success: false,
      message: `Error creating TestRail run: ${error.message}`,
      data: error.response?.data
    };
  }
}

/**
 * Run için TestRail run ID'sini alır
 * @param runId Run ID
 * @returns TestRail run ID
 */
export async function getTestRailRunIdForRun(runId: string) {
  try {
    if (!isMongoDBInitialized() || !db) {
      return {
        success: false,
        message: "Database connection not established",
        data: null
      };
    }

    // Run report'tan TestRail bilgilerini kontrol et
    const runReportResult = await getRunReportTestRailInfo(runId);

    if (runReportResult.success && runReportResult.testrailRunId) {


      return {
        success: true,
        message: "TestRail run ID found in run report",
        data: {
          testrailRunId: runReportResult.testrailRunId,
          testrailRunUrl: runReportResult.testrailRunLink
        }
      };
    }

    // TestRail run ID bulunamadı
    logger.info(`No TestRail run ID found for run ${runId}`);

    return {
      success: false,
      message: `No TestRail run ID found for run ${runId}`,
      data: null
    };
  } catch (error: any) {
    logger.error(`Error getting TestRail run ID for run ${runId}: ${error.message}`);
    return {
      success: false,
      message: `Error getting TestRail run ID: ${error.message}`,
      data: null
    };
  }
}
