/**
 * Log Formatter - Cleans and aggregates log messages
 * 
 * This utility helps reduce log noise by:
 * - Grouping similar messages
 * - Suppressing repetitive startup messages
 * - Formatting structured summaries
 */

export interface LogSummary {
  category: string;
  count: number;
  samples: string[];
  level: 'info' | 'warn' | 'error' | 'debug';
}

export interface ServiceStartupSummary {
  totalServices: number;
  successfulServices: string[];
  failedServices: string[];
  criticalServices: string[];
  timeElapsed: number;
}

export class LogFormatter {
  private static startupMessages: Map<string, number> = new Map();
  private static startupStartTime: number = Date.now();
  private static startupComplete: boolean = false;

  /**
   * Mark startup as complete and log summary
   */
  public static markStartupComplete(services: string[]): void {
    if (this.startupComplete) return;
    
    this.startupComplete = true;
    const timeElapsed = Date.now() - this.startupStartTime;
    
    console.log(`\n🎉 STARTUP COMPLETE`);
    console.log(`📊 Summary: ${services.length} services initialized in ${timeElapsed}ms`);
    console.log(`✅ Services: ${services.join(', ')}\n`);
  }

  /**
   * Group and display service initialization messages
   */
  public static logServiceGroup(category: string, services: string[]): void {
    if (services.length === 0) return;

    if (services.length === 1) {
      console.log(`[INFO] ${category}: ${services[0]} initialized`);
    } else {
      console.log(`[INFO] ${category}: ${services.length} services initialized`);
      console.log(`       └─ ${services.join(', ')}`);
    }
  }

  /**
   * Log connection summary in a clean format
   */
  public static logConnectionSummary(nodeId: string, connections: {
    websocket: { status: boolean; details?: string };
    redis: { status: boolean; details?: string };
    hub: { status: boolean; url?: string };
  }): void {
    console.log(`\n🔗 CONNECTION STATUS: ${nodeId}`);
    
    Object.entries(connections).forEach(([type, config]) => {
      const icon = config.status ? '✅' : '❌';
      const status = config.status ? 'Connected' : 'Failed';
      let line = `   ${type.toUpperCase()}: ${icon} ${status}`;
      
      // Handle different property types for different connection types
      if (type === 'hub' && 'url' in config && config.url) {
        line += ` - ${config.url}`;
      } else if ('details' in config && config.details) {
        line += ` - ${config.details}`;
      }
      
      console.log(line);
    });
    
    console.log('');
  }

  /**
   * Filter and clean fluxbox startup messages
   */
  public static filterFluxboxLogs(message: string): boolean {
    const ignoredPatterns = [
      'Failed to read:',
      'Setting default value',
      'Warning: Failed to open file',
      'for translation, using default messages'
    ];
    
    return !ignoredPatterns.some(pattern => message.includes(pattern));
  }

  /**
   * Group similar log messages to reduce noise
   */
  public static groupSimilarMessages(messages: string[]): LogSummary[] {
    const groups = new Map<string, { count: number; samples: string[]; level: string }>();
    
    messages.forEach(message => {
      // Extract pattern from message
      const pattern = this.extractPattern(message);
      const level = this.extractLogLevel(message);
      
      if (!groups.has(pattern)) {
        groups.set(pattern, { count: 0, samples: [], level });
      }
      
      const group = groups.get(pattern)!;
      group.count++;
      
      if (group.samples.length < 3) {
        group.samples.push(message);
      }
    });

    return Array.from(groups.entries()).map(([category, data]) => ({
      category,
      count: data.count,
      samples: data.samples,
      level: data.level as any
    }));
  }

  /**
   * Extract pattern from log message for grouping
   */
  private static extractPattern(message: string): string {
    // Remove timestamps
    let pattern = message.replace(/\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}[.,]\d{3}/, '[TIMESTAMP]');
    
    // Remove specific IDs and replace with placeholders
    pattern = pattern.replace(/node-[a-f0-9]+/g, '[NODE-ID]');
    pattern = pattern.replace(/client-[a-f0-9]+/g, '[CLIENT-ID]');
    pattern = pattern.replace(/websocket-\d+-[a-z0-9]+/g, '[WEBSOCKET-ID]');
    pattern = pattern.replace(/redis-\d+-[a-z0-9]+/g, '[REDIS-ID]');
    pattern = pattern.replace(/\d+ms/g, '[TIME]');
    pattern = pattern.replace(/:\d+/g, ':[PORT]');
    
    // Group by service initialization patterns
    if (pattern.includes('initialized')) {
      return 'Service Initialization';
    }
    if (pattern.includes('Connected') || pattern.includes('connection')) {
      return 'Connection Events';
    }
    if (pattern.includes('Started') || pattern.includes('starting')) {
      return 'Service Startup';
    }
    
    return pattern.slice(0, 100); // Limit pattern length
  }

  /**
   * Extract log level from message
   */
  private static extractLogLevel(message: string): string {
    if (message.includes('[ERROR]') || message.includes('ERROR')) return 'error';
    if (message.includes('[WARN]') || message.includes('WARN')) return 'warn';
    if (message.includes('[DEBUG]') || message.includes('DEBUG')) return 'debug';
    return 'info';
  }

  /**
   * Format startup sequence in a clean, structured way
   */
  public static formatStartupSequence(steps: Array<{
    name: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    duration?: number;
    details?: string;
  }>): void {
    console.log('\n🚀 STARTUP SEQUENCE');
    console.log('══════════════════');
    
    steps.forEach((step, index) => {
      const stepNumber = `[${index + 1}/${steps.length}]`;
      let icon = '';
      let status = '';
      
      switch (step.status) {
        case 'pending':
          icon = '⏳';
          status = 'Pending';
          break;
        case 'running':
          icon = '🔄';
          status = 'Running';
          break;
        case 'completed':
          icon = '✅';
          status = step.duration ? `Completed (${step.duration}ms)` : 'Completed';
          break;
        case 'failed':
          icon = '❌';
          status = 'Failed';
          break;
      }
      
      console.log(`${stepNumber} ${icon} ${step.name}: ${status}`);
      
      if (step.details) {
        console.log(`     └─ ${step.details}`);
      }
    });
    
    console.log('');
  }

  /**
   * Create clean, condensed startup summary
   */
  public static createStartupSummary(services: ServiceStartupSummary): void {
    const total = services.totalServices;
    const successful = services.successfulServices.length;
    const failed = services.failedServices.length;
    const critical = services.criticalServices.length;
    
    console.log('\n📋 STARTUP SUMMARY');
    console.log('═══════════════════');
    console.log(`⏱️  Time: ${services.timeElapsed}ms`);
    console.log(`📊 Services: ${successful}/${total} successful`);
    
    if (critical > 0) {
      console.log(`🎯 Critical: ${services.criticalServices.join(', ')}`);
    }
    
    if (failed > 0) {
      console.log(`❌ Failed: ${services.failedServices.join(', ')}`);
    }
    
    console.log('');
  }
}

/**
 * Pre-configured log patterns for common services
 */
export const LOG_PATTERNS = {
  REDIS_INIT: /RedisConnector.*Initialized \d+ queues/,
  WEBSOCKET_CONNECT: /WebSocket.*connected/i,
  SERVICE_INIT: /.*Service.*initialized/i,
  NODE_REGISTRATION: /Registration.*successful.*Node ID/,
  FLUXBOX_WARNING: /Failed to read:|Setting default value/,
  HEARTBEAT: /heartbeat.*started/i
};

/**
 * Service categories for grouping
 */
export const SERVICE_CATEGORIES = {
  CORE: ['NodeManager', 'ApplicationContainer', 'NodeIdentityService'],
  CONNECTIVITY: ['WebSocket', 'RedisConnector', 'ConnectionService'],
  PROCESSING: ['TestQueueWorker', 'TestExecutionManager'],
  MONITORING: ['HeartbeatService', 'EventService', 'PeriodicTaskService'],
  STORAGE: ['S3ArtifactStorage', 'MinioArtifactStorage']
}; 