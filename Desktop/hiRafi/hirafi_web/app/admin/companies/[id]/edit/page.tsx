"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { use } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { getCompanyById, updateCompany } from "@/lib/api/admin-api"
import { toast } from "@/components/ui/use-toast"
import { ArrowLeft, Save } from "lucide-react"
import Link from "next/link"

// Form şeması
const companyFormSchema = z.object({
  name: z.string().min(2, {
    message: "Şirket adı en az 2 karakter olmalıdır.",
  }),
  status: z.enum(["active", "inactive", "suspended"], {
    required_error: "Lütfen bir durum seçin.",
  }),
  accountType: z.enum(["trial", "basic", "standard", "premium", "enterprise"], {
    required_error: "Lütfen bir hesap türü seçin.",
  }),
  runLimit: z.coerce.number().int().min(0).optional(),
  runMinuteLimit: z.coerce.number().int().min(0).optional(),
  concurrentRunLimit: z.coerce.number().int().min(1).optional(),
  scenarioLimit: z.coerce.number().int().min(0).optional(),
  generationLimit: z.coerce.number().int().min(0).optional(),
  maxUsers: z.coerce.number().int().min(1).optional(),
  maxTeams: z.coerce.number().int().min(0).optional(),
  // Kalan kullanım alanları
  remainingRuns: z.coerce.number().int().min(0).optional(),
  remainingRunMinutes: z.coerce.number().int().min(0).optional(),
  remainingGenerations: z.coerce.number().int().min(0).optional()
})

type CompanyFormValues = z.infer<typeof companyFormSchema>

export default function EditCompanyPage({ params }: { params: { id: string } }) {
  // Unwrap params using React.use
  const unwrappedParams = use(params);
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Form tanımı
  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      name: "",
      status: "active",
      accountType: "trial",
      runLimit: 100,
      runMinuteLimit: 500,
      concurrentRunLimit: 2,
      scenarioLimit: 50,
      generationLimit: 200,
      maxUsers: 5,
      maxTeams: 2
    },
    mode: "onChange",
  })

  // Şirket verilerini yükle
  useEffect(() => {
    const fetchCompany = async () => {
      setIsLoading(true)
      try {
        const response = await getCompanyById(unwrappedParams.id)

        if (response.success && (response.data?.company || response.company)) {
          const company = response.data?.company || response.company

          // Form değerlerini ayarla
          form.reset({
            name: company.name,
            status: company.status,
            accountType: company.settings?.accountType || "trial",
            runLimit: company.settings?.runLimit ?? 100,
            runMinuteLimit: company.settings?.runMinuteLimit ?? 500,
            concurrentRunLimit: company.settings?.concurrentRunLimit ?? 2,
            scenarioLimit: company.settings?.scenarioLimit ?? 50,
            generationLimit: company.settings?.generationLimit ?? 200,
            maxUsers: company.settings?.maxUsers ?? 5,
            maxTeams: company.settings?.maxTeams ?? 2,
            // Kalan kullanım değerleri
            remainingRuns: company.settings?.remaining?.runs ?? company.settings?.runLimit ?? 100,
            remainingRunMinutes: company.settings?.remaining?.runMinutes ?? company.settings?.runMinuteLimit ?? 500,
            remainingGenerations: company.settings?.remaining?.generations ?? company.settings?.generationLimit ?? 200
          })
        } else {
          toast({
            title: "Hata",
            description: response.error || "Şirket bilgileri alınamadı.",
            variant: "destructive",
          })
          router.push('/admin/companies')
        }
      } catch (error) {
        toast({
          title: "Hata",
          description: "Şirket bilgileri alınamadı.",
          variant: "destructive",
        })
        router.push('/admin/companies')
      } finally {
        setIsLoading(false)
      }
    }

    fetchCompany()
  }, [unwrappedParams.id, router, form])

  // Form gönderme
  const onSubmit = async (data: CompanyFormValues) => {
    setIsSubmitting(true)
    try {
      // Ayarları hazırla
      const {
        accountType, runLimit, runMinuteLimit, concurrentRunLimit,
        scenarioLimit, generationLimit, maxUsers, maxTeams,
        remainingRuns, remainingRunMinutes, remainingGenerations,
        ...companyData
      } = data

      const settings = {
        accountType,
        runLimit,
        runMinuteLimit,
        concurrentRunLimit,
        scenarioLimit,
        generationLimit,
        maxUsers,
        maxTeams,
        remaining: {
          runs: remainingRuns,
          runMinutes: remainingRunMinutes,
          generations: remainingGenerations
        }
      }

      // API'ye gönder
      const response = await updateCompany(unwrappedParams.id, {
        ...companyData,
        settings
      })

      if (response.success) {
        toast({
          title: "Şirket güncellendi",
          description: "Şirket bilgileri başarıyla güncellendi.",
        })
        router.push(`/admin/companies/${unwrappedParams.id}`)
      } else {
        toast({
          title: "Hata",
          description: response.data?.error || response.error || "Şirket güncellenirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Şirket güncellenirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex h-[50vh] items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">Şirket bilgileri yükleniyor...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Şirket Düzenle</h1>
            <p className="text-muted-foreground">
              Şirket bilgilerini ve ayarlarını düzenleyin
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href={`/admin/companies/${unwrappedParams.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Şirket Detaylarına Dön
            </Link>
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Şirket Bilgileri</CardTitle>
                <CardDescription>
                  Şirketin temel bilgilerini düzenleyin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Şirket Adı *</FormLabel>
                        <FormControl>
                          <Input placeholder="Şirket adını girin" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Durum *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Durum seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="active">Aktif</SelectItem>
                            <SelectItem value="inactive">Pasif</SelectItem>
                            <SelectItem value="suspended">Askıya Alınmış</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Hesap Türü ve Limitler</CardTitle>
                <CardDescription>
                  Şirket hesap türünü ve limitlerini düzenleyin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="accountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hesap Türü *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Hesap türü seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="trial">Deneme</SelectItem>
                          <SelectItem value="basic">Temel</SelectItem>
                          <SelectItem value="standard">Standart</SelectItem>
                          <SelectItem value="premium">Premium</SelectItem>
                          <SelectItem value="enterprise">Kurumsal</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Hesap türü, şirketin kullanabileceği özellikleri ve limitleri belirler
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="runLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Koşum Sayısı Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aylık toplam koşum sayısı limiti (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="runMinuteLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Koşum Dakikası Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aylık toplam koşum dakikası limiti (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="concurrentRunLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Eşzamanlı Koşum Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aynı anda çalışabilecek koşum sayısı
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="scenarioLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Senaryo Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Oluşturulabilecek maksimum senaryo sayısı (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="generationLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Generation Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aylık toplam senaryo generation limiti (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="maxUsers"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maksimum Kullanıcı Sayısı</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormDescription>
                          Şirketin sahip olabileceği maksimum kullanıcı sayısı
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maxTeams"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maksimum Takım Sayısı</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Şirketin oluşturabileceği maksimum takım sayısı (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Card className="border-dashed border-yellow-500">
                  <CardHeader>
                    <CardTitle className="text-lg">Kalan Kullanım Bilgileri</CardTitle>
                    <CardDescription>
                      Şirketin kalan kullanım haklarını düzenleyin
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <FormField
                        control={form.control}
                        name="remainingRuns"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Kalan Koşum Sayısı</FormLabel>
                            <FormControl>
                              <Input type="number" min="0" {...field} />
                            </FormControl>
                            <FormDescription>
                              Kalan koşum hakkı sayısı
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="remainingRunMinutes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Kalan Koşum Dakikası</FormLabel>
                            <FormControl>
                              <Input type="number" min="0" {...field} />
                            </FormControl>
                            <FormDescription>
                              Kalan koşum dakikası sayısı
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="remainingGenerations"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Kalan Generation Sayısı</FormLabel>
                            <FormControl>
                              <Input type="number" min="0" {...field} />
                            </FormControl>
                            <FormDescription>
                              Kalan generation hakkı sayısı
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>

            <CardFooter className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/admin/companies/${unwrappedParams.id}`)}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></div>
                    Kaydediliyor...
                  </div>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Değişiklikleri Kaydet
                  </>
                )}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </div>
    </AdminLayout>
  )
}
