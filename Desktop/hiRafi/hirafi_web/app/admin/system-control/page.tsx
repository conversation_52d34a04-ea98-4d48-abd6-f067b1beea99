"use client"

import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminRouteGuard } from "@/lib/api/admin-auth"
import SystemPauseControl from "@/components/admin/SystemPauseControl"

export default function AdminSystemControlPage() {
  return (
    <AdminRouteGuard>
      <AdminLayout>
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">System Control</h1>
            <p className="text-muted-foreground">
              Pause and resume test execution across the system
            </p>
          </div>
          
          <SystemPauseControl />
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  )
}
