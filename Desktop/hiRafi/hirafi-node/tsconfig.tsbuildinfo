{"root": ["./src/index.ts", "./src/test-di-improvements.ts", "./src/test-di-integration.ts", "./src/test-di.ts", "./src/config/index.ts", "./src/config/core/config-factory.ts", "./src/config/core/config-manager.ts", "./src/config/core/config-validator.ts", "./src/config/domains/bullmq-config.ts", "./src/config/domains/filesystem-config.ts", "./src/config/domains/network-collector-config.ts", "./src/config/domains/node-config.ts", "./src/config/domains/platform-config.ts", "./src/config/domains/redis-config.ts", "./src/config/domains/storage-config.ts", "./src/config/domains/test-config.ts", "./src/config/services/configuration-service.ts", "./src/config/services/index.ts", "./src/config/types/config-types.ts", "./src/config/utils/directory-utils.ts", "./src/config/utils/env-utils.ts", "./src/core/index.ts", "./src/core/android/index.ts", "./src/core/android/device/base-device-manager.ts", "./src/core/android/device/device-manager-factory.ts", "./src/core/android/device/device-manager.interface.ts", "./src/core/android/device/index.ts", "./src/core/android/device/sauce-labs-device-manager.ts", "./src/core/android/providers/index.ts", "./src/core/android/step-handlers/index.ts", "./src/core/common/index.ts", "./src/core/common/di/application-container.ts", "./src/core/common/di/dependency-container.ts", "./src/core/common/di/index.ts", "./src/core/common/factories/index.ts", "./src/core/common/interfaces/base-service.interface.ts", "./src/core/common/interfaces/index.ts", "./src/core/common/interfaces/metrics-collector.interface.ts", "./src/core/common/interfaces/screenshot-provider.interface.ts", "./src/core/common/interfaces/step-handler-registry.interface.ts", "./src/core/common/interfaces/step-handler.interface.ts", "./src/core/common/interfaces/video-recorder.interface.ts", "./src/core/common/providers/base-metrics-collector.ts", "./src/core/common/providers/base-screenshot-provider.ts", "./src/core/common/providers/base-test-runner.ts", "./src/core/common/providers/base-video-recorder.ts", "./src/core/common/providers/index.ts", "./src/core/common/step-handlers/ai-action-handler.ts", "./src/core/common/step-handlers/ai-assertion-handler.ts", "./src/core/common/step-handlers/ai-boolean-handler.ts", "./src/core/common/step-handlers/ai-hover-handler.ts", "./src/core/common/step-handlers/ai-input-handler.ts", "./src/core/common/step-handlers/ai-keyboard-press-handler.ts", "./src/core/common/step-handlers/ai-locate-handler.ts", "./src/core/common/step-handlers/ai-number-handler.ts", "./src/core/common/step-handlers/ai-query-handler.ts", "./src/core/common/step-handlers/ai-right-click-handler.ts", "./src/core/common/step-handlers/ai-scroll-handler.ts", "./src/core/common/step-handlers/ai-string-handler.ts", "./src/core/common/step-handlers/ai-tap-handler.ts", "./src/core/common/step-handlers/ai-wait-element-handler.ts", "./src/core/common/step-handlers/goto-handler.ts", "./src/core/common/step-handlers/index.ts", "./src/core/common/step-handlers/sleep-handler.ts", "./src/core/common/step-handlers/step-handler-registry.ts", "./src/core/common/storage/artifact-storage.interface.ts", "./src/core/common/storage/index.ts", "./src/core/common/storage/minio-artifact-storage.ts", "./src/core/common/storage/s3-artifact-storage.ts", "./src/core/common/storage/screenshot-queue-service.ts", "./src/core/common/storage/storage-factory.ts", "./src/core/common/storage/storage-manager.ts", "./src/core/common/utils/artifact-utils.ts", "./src/core/common/utils/error-handling.ts", "./src/core/common/utils/file-utils.ts", "./src/core/common/utils/index.ts", "./src/core/connectors/index.ts", "./src/core/connectors/core/base-connector.ts", "./src/core/connectors/core/connector-manager.ts", "./src/core/connectors/core/index.ts", "./src/core/connectors/factories/connector-factory.ts", "./src/core/connectors/factories/connector-manager-factory.ts", "./src/core/connectors/factories/index.ts", "./src/core/connectors/http/http-connector.ts", "./src/core/connectors/http/index.ts", "./src/core/connectors/interfaces/base-connector.interface.ts", "./src/core/connectors/interfaces/connection-manager.interface.ts", "./src/core/connectors/interfaces/connector-factory.interface.ts", "./src/core/connectors/interfaces/connector-manager.interface.ts", "./src/core/connectors/interfaces/connector-service-registry.interface.ts", "./src/core/connectors/interfaces/connector-service.interface.ts", "./src/core/connectors/interfaces/heartbeat-service.interface.ts", "./src/core/connectors/interfaces/http-connector.interface.ts", "./src/core/connectors/interfaces/index.ts", "./src/core/connectors/interfaces/message-handler.interface.ts", "./src/core/connectors/interfaces/redis-connector.interface.ts", "./src/core/connectors/interfaces/registration-service.interface.ts", "./src/core/connectors/interfaces/websocket-connector.interface.ts", "./src/core/connectors/redis/index.ts", "./src/core/connectors/redis/redis-connector.ts", "./src/core/connectors/registry/connector-service-registry.ts", "./src/core/connectors/registry/index.ts", "./src/core/connectors/registry/service-dependency-graph.ts", "./src/core/connectors/services/index.ts", "./src/core/connectors/services/base/base-service.ts", "./src/core/connectors/services/base/index.ts", "./src/core/connectors/services/heartbeat/heartbeat-service.ts", "./src/core/connectors/services/heartbeat/index.ts", "./src/core/connectors/services/http/http-heartbeat-service.ts", "./src/core/connectors/services/http/http-registration-service.ts", "./src/core/connectors/services/http/http-test-service.ts", "./src/core/connectors/services/http/index.ts", "./src/core/connectors/services/redis/index.ts", "./src/core/connectors/services/redis/redis-connection-manager.ts", "./src/core/connectors/services/registration/index.ts", "./src/core/connectors/services/registration/registration-service.ts", "./src/core/connectors/services/websocket/index.ts", "./src/core/connectors/services/websocket/websocket-connection-manager.ts", "./src/core/connectors/services/websocket/websocket-message-handler.ts", "./src/core/connectors/types/connector-types.ts", "./src/core/connectors/types/factory-types.ts", "./src/core/connectors/types/index.ts", "./src/core/connectors/types/registry-types.ts", "./src/core/connectors/types/service-types.ts", "./src/core/connectors/websocket/index.ts", "./src/core/connectors/websocket/websocket-connector.ts", "./src/core/node-manager/index.ts", "./src/core/node-manager/core/index.ts", "./src/core/node-manager/core/node-manager.ts", "./src/core/node-manager/factories/index.ts", "./src/core/node-manager/factories/node-manager-factory.ts", "./src/core/node-manager/factories/pause-service-factory.ts", "./src/core/node-manager/interfaces/connection-service.interface.ts", "./src/core/node-manager/interfaces/event-service.interface.ts", "./src/core/node-manager/interfaces/heartbeat-service.interface.ts", "./src/core/node-manager/interfaces/index.ts", "./src/core/node-manager/interfaces/node-identity-service.interface.ts", "./src/core/node-manager/interfaces/node-manager-factory.interface.ts", "./src/core/node-manager/interfaces/node-manager.interface.ts", "./src/core/node-manager/interfaces/node-state-service.interface.ts", "./src/core/node-manager/interfaces/pause-service.interface.ts", "./src/core/node-manager/interfaces/periodic-task-service.interface.ts", "./src/core/node-manager/interfaces/service-registry.interface.ts", "./src/core/node-manager/registry/dependency-container.ts", "./src/core/node-manager/registry/index.ts", "./src/core/node-manager/registry/service-registry.ts", "./src/core/node-manager/services/index.ts", "./src/core/node-manager/services/connection/connection-service.ts", "./src/core/node-manager/services/events/event-service.ts", "./src/core/node-manager/services/heartbeat/heartbeat-service.ts", "./src/core/node-manager/services/identity/node-identity-service.ts", "./src/core/node-manager/services/pause/index.ts", "./src/core/node-manager/services/pause/pause-service.ts", "./src/core/node-manager/services/state/atomic-state-manager.ts", "./src/core/node-manager/services/state/node-state-service.ts", "./src/core/node-manager/services/tasks/periodic-task-service.ts", "./src/core/node-manager/types/index.ts", "./src/core/node-manager/types/node-manager-types.ts", "./src/core/test-execution/index.ts", "./src/core/test-execution/core/execution-coordinator.ts", "./src/core/test-execution/core/index.ts", "./src/core/test-execution/core/state-manager.ts", "./src/core/test-execution/factories/execution-coordinator-factory.ts", "./src/core/test-execution/factories/index.ts", "./src/core/test-execution/factories/test-communication-factory.ts", "./src/core/test-execution/factories/test-queue-worker-factory.ts", "./src/core/test-execution/factories/test-runner-factory.ts", "./src/core/test-execution/interfaces/cleanup-orchestrator.interface.ts", "./src/core/test-execution/interfaces/configuration-manager.interface.ts", "./src/core/test-execution/interfaces/execution-coordinator.interface.ts", "./src/core/test-execution/interfaces/index.ts", "./src/core/test-execution/interfaces/resource-manager.interface.ts", "./src/core/test-execution/interfaces/state-manager.interface.ts", "./src/core/test-execution/interfaces/test-communication.interface.ts", "./src/core/test-execution/interfaces/test-progress-reporter.interface.ts", "./src/core/test-execution/interfaces/test-queue-worker.interface.ts", "./src/core/test-execution/interfaces/test-runner.interface.ts", "./src/core/test-execution/registry/index.ts", "./src/core/test-execution/registry/service-registry.ts", "./src/core/test-execution/services/index.ts", "./src/core/test-execution/services/cleanup/cleanup-orchestrator.ts", "./src/core/test-execution/services/cleanup/index.ts", "./src/core/test-execution/services/communication/index.ts", "./src/core/test-execution/services/communication/test-communication-service.ts", "./src/core/test-execution/services/configuration/ai-configuration-service.ts", "./src/core/test-execution/services/configuration/index.ts", "./src/core/test-execution/services/progress/index.ts", "./src/core/test-execution/services/progress/test-progress-service.ts", "./src/core/test-execution/services/queue/index.ts", "./src/core/test-execution/services/queue/test-queue-worker.ts", "./src/core/test-execution/services/resource-management/index.ts", "./src/core/test-execution/services/resource-management/resource-cleanup-service.ts", "./src/core/web/collector-base.ts", "./src/core/web/index.ts", "./src/core/web/metrics-collector.ts", "./src/core/web/network-temp-storage.ts", "./src/core/web/browser/browser-manager.interface.ts", "./src/core/web/browser/browser-manager.ts", "./src/core/web/browser/index.ts", "./src/core/web/providers/index.ts", "./src/core/web/step-handlers/index.ts", "./src/models/android-environment.ts", "./src/models/message-types.ts", "./src/models/platform-types.ts", "./src/models/types.ts", "./src/types/amqplib.d.ts", "./src/types/sharp.d.ts", "./src/utils/ai-log-formatter.ts", "./src/utils/logger-service.ts", "./src/utils/logger.ts", "./src/utils/type-guards.ts"], "version": "5.8.3"}