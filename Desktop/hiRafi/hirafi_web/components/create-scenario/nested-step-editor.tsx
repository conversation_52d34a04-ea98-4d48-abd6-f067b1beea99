"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Plus,
  Trash2,
  GripVertical,
  MousePointer,
  Sparkles,
  Clock,
  ArrowRight,
  Type as TypeIcon,
  Search,
  Binary,
  Keyboard,
  ChevronsUpDown,
  Locate,
  Hash,
  ClipboardCheck,
  GitBranch,
  RotateCcw,
  Repeat,
  BrainCircuit,
  Target,
  Info,
} from "lucide-react"
import { VariableAutocomplete } from "./variable-autocomplete"
import { TestStep, ActionType } from "@/models/scenario"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"
import { createNewStep, updateStepField, updateStepType as updateStepTypeUtil } from '@/lib/utils/step-management'
import { Switch } from "@/components/ui/switch"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ip<PERSON>ontent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Action configuration (detailed for nested editor)
const actionConfig: {
  [key: string]: {
    label: string;
    icon: React.ElementType;
    supportsDeepThink?: boolean;
    valueLabel?: string;
    valuePlaceholder?: string;
    valueAsTextarea?: boolean;
    info?: string;
  };
} = {
  aiTap: { 
    label: "AI Tap/Click", 
    icon: MousePointer,
    supportsDeepThink: true,
    valueLabel: "Element to Tap",
    valuePlaceholder: "e.g., The 'Submit' button",
    info: "Click or tap on a specific element."
  },
  aiInput: { 
    label: "AI Input", 
    icon: TypeIcon,
    supportsDeepThink: true,
    valueLabel: "Text to Input",
    valuePlaceholder: "e.g., my-secret-password",
    info: "Type text into an input field."
  },
  aiAction: { 
    label: "AI Action", 
    icon: Sparkles,
    supportsDeepThink: false,
    valueLabel: "Action Description",
    valuePlaceholder: "e.g., Type 'hello' into the search box and click search",
    valueAsTextarea: true,
    info: "Perform multi-step actions using natural language."
  },
  aiAssertion: { 
    label: "AI Assertion", 
    icon: ClipboardCheck,
    supportsDeepThink: false,
    valueLabel: "Assertion to Verify",
    valuePlaceholder: "e.g., The login button should be visible",
    valueAsTextarea: true,
    info: "Verify a condition on the page is true."
  },
  aiWaitElement: { 
    label: "AI Wait", 
    icon: Clock,
    supportsDeepThink: true,
    valueLabel: "Condition to Wait For",
    valuePlaceholder: "e.g., The success message appears",
    valueAsTextarea: true,
    info: "Wait for a condition to be true."
  },
  aiQuery: { 
    label: "AI Query", 
    icon: Search,
    supportsDeepThink: false,
    valueLabel: "Query Object Definition",
    valuePlaceholder: 'e.g., { "title": "The page title" }',
    info: "Extract structured data (JSON) from the UI."
  },
  aiString: { 
    label: "AI String", 
    icon: TypeIcon,
    supportsDeepThink: false,
    valueLabel: "Text to Extract",
    valuePlaceholder: "e.g., The welcome message text",
    info: "Extract a single text value from the UI."
  },
  aiNumber: { 
    label: "AI Number", 
    icon: Hash,
    supportsDeepThink: false,
    valueLabel: "Number to Extract",
    valuePlaceholder: "e.g., The number of items in the cart",
    info: "Extract a single numerical value from the UI."
  },
  aiBoolean: { 
    label: "AI Boolean", 
    icon: Binary,
    supportsDeepThink: false,
    valueLabel: "Question to Ask",
    valuePlaceholder: "e.g., Is the 'Save' button disabled?",
    info: "Ask a yes/no question about the UI."
  },
  aiHover: { 
    label: "AI Hover", 
    icon: MousePointer,
    supportsDeepThink: true,
    valueLabel: "Element to Hover Over",
    valuePlaceholder: "e.g., The user profile picture",
    info: "Move the mouse cursor over an element."
  },
  aiKeyboardPress: { 
    label: "AI Keyboard Press", 
    icon: Keyboard,
    supportsDeepThink: true,
    valueLabel: "Key to Press",
    valuePlaceholder: "e.g., Enter",
    info: "Simulate pressing a single keyboard key."
  },
  aiScroll: { 
    label: "AI Scroll", 
    icon: ChevronsUpDown,
    supportsDeepThink: true,
    valueLabel: "Direction & Element (optional)",
    valuePlaceholder: 'e.g., down, or "the user list" down',
    info: "Scroll the page or an element."
  },
  aiRightClick: { 
    label: "AI Right Click", 
    icon: MousePointer,
    supportsDeepThink: true,
    valueLabel: "Element to Right-Click",
    valuePlaceholder: "e.g., The file icon",
    info: "Perform a right-click on an element."
  },
  aiLocate: { 
    label: "AI Locate", 
    icon: Locate,
    supportsDeepThink: true,
    valueLabel: "Element to Locate",
    valuePlaceholder: "e.g., The logo in the header",
    info: "Find element coordinates (position/size)."
  },
  ifElse: { 
    label: "If-Else", 
    icon: GitBranch,
    supportsDeepThink: false,
    valueLabel: "Condition to Check",
    valuePlaceholder: "e.g., Is the user already logged in to the system?",
    info: "Execute different steps based on a boolean condition evaluated by AI."
  },
  forLoop: { 
    label: "For Loop", 
    icon: RotateCcw,
    supportsDeepThink: false,
    valueLabel: "List to Iterate Over",
    valuePlaceholder: "e.g., List of product cards on the page",
    info: "Repeat steps for each item in a list retrieved by AI."
  },
  whileLoop: { 
    label: "While Loop", 
    icon: Repeat,
    supportsDeepThink: false,
    valueLabel: "Condition to Check",
    valuePlaceholder: "e.g., Is the 'Load More' button still visible and clickable?",
    info: "Repeat steps while a boolean condition evaluated by AI remains true."
  },
  goto: { 
    label: "Goto URL", 
    icon: ArrowRight,
    supportsDeepThink: false,
    valueLabel: "URL to Navigate To",
    valuePlaceholder: "e.g., https://hirafi.com",
    info: "Navigate the browser to a specific URL."
  },
  sleep: { 
    label: "Sleep", 
    icon: Clock,
    supportsDeepThink: false,
    valueLabel: "Duration (seconds)",
    valuePlaceholder: "e.g., 5",
    info: "Pause execution for a set time."
  },
}

const actionGroups = [
  {
    label: "Instant Actions",
    actions: ["aiTap", "aiInput", "aiHover", "aiKeyboardPress", "aiScroll", "aiRightClick", "aiAssertion", "aiWaitElement"] as ActionType[],
  },
  {
    label: "Data Extraction",
    actions: ["aiQuery", "aiString", "aiNumber", "aiBoolean", "aiLocate"] as ActionType[],
  },
  {
    label: "Control Flow",
    actions: ["ifElse", "forLoop", "whileLoop"] as ActionType[],
  },
  {
    label: "High-Level Goal",
    actions: ["aiAction"] as ActionType[],
  },
  {
    label: "Browser Control",
    actions: ["goto", "sleep"] as ActionType[],
  },
]

interface NestedStepEditorProps {
  steps: TestStep[]
  onStepsChange: (steps: TestStep[]) => void
  title: string
  emptyMessage: string
  depth?: number
  maxDepth?: number
}

export function NestedStepEditor({ 
  steps, 
  onStepsChange, 
  title, 
  emptyMessage, 
  depth = 0,
  maxDepth = 3 
}: NestedStepEditorProps) {
  
  // IF-ELSE toggle state for each step
  const [ifElseToggleStates, setIfElseToggleStates] = React.useState<{[stepId: string]: boolean}>({})
  
  // Generate unique step ID
  const generateStepId = () => `step_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

  // Optimized addStep with useCallback for performance
  const addStep = React.useCallback((position?: number) => {
    const newStep = createNewStep("aiTap");

    const newSteps = [...steps]
    if (position !== undefined) {
      newSteps.splice(position, 0, newStep)
    } else {
      newSteps.push(newStep)
    }
    onStepsChange(newSteps)
  }, [steps, onStepsChange])

  // Optimized removeStep with useCallback for performance
  const removeStep = React.useCallback((stepId: string) => {
    const newSteps = steps.filter(step => step.id !== stepId)
    onStepsChange(newSteps)
  }, [steps, onStepsChange])

  // Optimized updateStep with useCallback for performance
  const updateStep = React.useCallback((stepId: string, field: keyof TestStep, value: any) => {
    const newSteps = steps.map(step =>
      step.id === stepId ? updateStepField(step, field, value) : step
    )
    onStepsChange(newSteps)
  }, [steps, onStepsChange])

  // Optimized updateStepType with useCallback for performance
  const updateStepType = React.useCallback((stepId: string, newType: ActionType) => {
    const newSteps = steps.map(step =>
      step.id === stepId ? updateStepTypeUtil(step, newType) : step
    )
    onStepsChange(newSteps)
  }, [steps, onStepsChange])

  // Optimized handleDragEnd with useCallback for performance
  const handleDragEnd = React.useCallback((result: any) => {
    if (!result.destination) return

    const newSteps = Array.from(steps)
    const [reorderedStep] = newSteps.splice(result.source.index, 1)
    newSteps.splice(result.destination.index, 0, reorderedStep)
    onStepsChange(newSteps)
  }, [steps, onStepsChange])

  // IF-ELSE toggle helper functions
  const getIfElseToggleState = (stepId: string) => {
    return ifElseToggleStates[stepId] ?? true // Default to true (show TRUE branch)
  }

  const setIfElseToggleState = (stepId: string, showTrueBranch: boolean) => {
    setIfElseToggleStates(prev => ({
      ...prev,
      [stepId]: showTrueBranch
    }))
  }

  // Get step type label
  const getStepTypeLabel = (type: ActionType) => {
    return actionConfig[type]?.label || "Unknown Action"
  }

  // Get step type icon
  const getStepTypeIcon = (type: ActionType) => {
    const Icon = actionConfig[type]?.icon || Sparkles
    return <Icon className="h-4 w-4 mr-2" />
  }

  return (
    <Card className={`border ${depth > 0 ? 'border-dashed border-gray-300 dark:border-gray-600' : 'border-gray-200 dark:border-gray-700'}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center justify-between">
          <span>{title}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => addStep()}
            className="h-7 text-xs"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Step
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {steps.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-lg">
            <Sparkles className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{emptyMessage}</p>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => addStep()}
              className="mt-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add First Step
            </Button>
          </div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId={`nested-steps-${title}-${depth}`}>
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-3">
                  {steps.map((step, index) => (
                    <Draggable key={step.id} draggableId={step.id} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className="bg-white dark:bg-gray-800/30 border border-gray-200 dark:border-gray-700 rounded-lg p-3"
                        >
                          <div className="flex items-start space-x-3">
                            <div 
                              {...provided.dragHandleProps}
                              className="flex items-center justify-center w-6 h-6 rounded bg-gray-100 dark:bg-gray-700 text-xs font-medium cursor-grab"
                            >
                              {index + 1}
                            </div>

                            <div className="flex-1 space-y-3">
                              {/* Step Type Selector */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Select
                                    value={step.type}
                                    onValueChange={(newType) => updateStepType(step.id, newType as ActionType)}
                                  >
                                    <SelectTrigger className="w-auto min-w-[180px] h-8 text-sm">
                                      <SelectValue>
                                        <div className="flex items-center">
                                          {getStepTypeIcon(step.type)}
                                          <span className="truncate">{getStepTypeLabel(step.type)}</span>
                                        </div>
                                      </SelectValue>
                                    </SelectTrigger>
                                    <SelectContent>
                                      {actionGroups.map(group => (
                                        <React.Fragment key={group.label}>
                                          <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 dark:text-gray-400">
                                            {group.label}
                                          </div>
                                          {group.actions
                                            .filter(actionType => {
                                              // Prevent nested if-else (no if-else inside if-else)
                                              if (depth > 0 && actionType === 'ifElse') {
                                                return false
                                              }
                                              // Limit other control flow depth
                                              if (depth >= maxDepth && ['forLoop', 'whileLoop'].includes(actionType)) {
                                                return false
                                              }
                                              return true
                                            })
                                            .map(actionType => {
                                              const config = actionConfig[actionType]
                                              const Icon = config.icon
                                              return (
                                                <SelectItem key={actionType} value={actionType} className="text-sm">
                                                  <div className="flex items-center">
                                                    <Icon className="h-4 w-4 mr-2" />
                                                    <span>{config.label}</span>
                                                  </div>
                                                </SelectItem>
                                              )
                                            })}
                                        </React.Fragment>
                                      ))}
                                    </SelectContent>
                                  </Select>

                                  {actionConfig[step.type]?.info && (
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Info className="h-4 w-4 text-gray-400 dark:text-gray-500 cursor-help" />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p className="max-w-xs">{actionConfig[step.type].info}</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  )}
                                </div>

                                <div className="flex items-center space-x-2">
                                  {actionConfig[step.type]?.supportsDeepThink && (
                                    <div className="flex items-center space-x-1.5 p-1.5 rounded bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/30 dark:to-purple-950/30 border border-indigo-100 dark:border-indigo-800/50">
                                      <BrainCircuit className="h-3 w-3 text-indigo-600 dark:text-indigo-400" />
                                      <Switch
                                        checked={!!step.deepThink}
                                        onCheckedChange={(checked) => updateStep(step.id, 'deepThink', checked)}
                                        id={`deep-think-${step.id}`}
                                        className="scale-75"
                                      />
                                      <Label htmlFor={`deep-think-${step.id}`} className="text-xs font-medium text-indigo-700 dark:text-indigo-300 cursor-pointer">
                                        Deep Focus
                                      </Label>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Target className="h-3 w-3 text-indigo-500 dark:text-indigo-400 cursor-help" />
                                          </TooltipTrigger>
                                          <TooltipContent className="max-w-sm">
                                            <div className="space-y-2">
                                              <p className="font-medium text-indigo-600 dark:text-indigo-400">Enhanced Element Detection</p>
                                              <p className="text-sm">
                                                Enables advanced AI analysis for precise targeting.
                                              </p>
                                            </div>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  )}

                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeStep(step.id)}
                                    className="h-8 w-8 p-0 text-gray-500 hover:text-red-500"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>

                              {/* Step Content Based on Type */}
                              {step.type === 'ifElse' ? (
                                <div className="space-y-4">
                                  <div>
                                    <Label className="text-sm font-medium">Condition</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., Is the user already logged in to the system?"
                                      value={step.condition || step.value || ""}
                                      onChange={(value: string) => {
                                        updateStep(step.id, 'condition', value)
                                        updateStep(step.id, 'value', value)
                                      }}
                                    />
                                  </div>
                                  
                                  {/* Branch Toggle */}
                                  <div className="flex items-center justify-center py-2">
                                    <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                                      <Button
                                        variant={getIfElseToggleState(step.id) ? "default" : "ghost"}
                                        size="sm"
                                        onClick={() => setIfElseToggleState(step.id, true)}
                                        className={`
                                          h-8 px-3 text-xs font-medium transition-all
                                          ${getIfElseToggleState(step.id)
                                            ? 'bg-green-500 hover:bg-green-600 text-white shadow-sm' 
                                            : 'text-gray-600 hover:text-green-600 hover:bg-green-50 dark:text-gray-400 dark:hover:text-green-400 dark:hover:bg-green-900/20'
                                          }
                                        `}
                                      >
                                        <div className="w-2 h-2 bg-current rounded-full mr-2"></div>
                                        TRUE ({(step.trueSteps || []).length} steps)
                                      </Button>
                                      <Button
                                        variant={!getIfElseToggleState(step.id) ? "default" : "ghost"}
                                        size="sm"
                                        onClick={() => setIfElseToggleState(step.id, false)}
                                        className={`
                                          h-8 px-3 text-xs font-medium transition-all ml-1
                                          ${!getIfElseToggleState(step.id)
                                            ? 'bg-red-500 hover:bg-red-600 text-white shadow-sm' 
                                            : 'text-gray-600 hover:text-red-600 hover:bg-red-50 dark:text-gray-400 dark:hover:text-red-400 dark:hover:bg-red-900/20'
                                          }
                                        `}
                                      >
                                        <div className="w-2 h-2 bg-current rounded-full mr-2"></div>
                                        FALSE ({(step.falseSteps || []).length} steps)
                                      </Button>
                                    </div>
                                  </div>

                                  {/* Conditional Branch Display */}
                                  <div className="w-full">
                                    {getIfElseToggleState(step.id) ? (
                                      <NestedStepEditor
                                        steps={step.trueSteps || []}
                                        onStepsChange={(newSteps) => updateStep(step.id, 'trueSteps', newSteps)}
                                        title="✅ If TRUE - Execute These Steps"
                                        emptyMessage="No steps for TRUE condition"
                                        depth={depth + 1}
                                        maxDepth={maxDepth}
                                      />
                                    ) : (
                                      <NestedStepEditor
                                        steps={step.falseSteps || []}
                                        onStepsChange={(newSteps) => updateStep(step.id, 'falseSteps', newSteps)}
                                        title="❌ If FALSE - Execute These Steps"
                                        emptyMessage="No steps for FALSE condition"
                                        depth={depth + 1}
                                        maxDepth={maxDepth}
                                      />
                                    )}
                                  </div>
                                </div>
                              ) : step.type === 'forLoop' ? (
                                <div className="space-y-4">
                                  <div>
                                    <Label className="text-sm font-medium">Number of Iterations</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., 3 or 'How many products are shown?'"
                                      value={step.iterationCount || step.value || ""}
                                      onChange={(value: string) => {
                                        updateStep(step.id, 'iterationCount', value)
                                        updateStep(step.id, 'value', value)
                                      }}
                                    />
                                  </div>
                                  
                                  <NestedStepEditor
                                    steps={step.loopSteps || []}
                                    onStepsChange={(newSteps) => updateStep(step.id, 'loopSteps', newSteps)}
                                    title="🔄 Loop Steps - Execute For Each Iteration"
                                    emptyMessage="No steps in the loop"
                                    depth={depth + 1}
                                    maxDepth={maxDepth}
                                  />
                                </div>
                              ) : step.type === 'whileLoop' ? (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-3 gap-3">
                                    <div className="col-span-2">
                                      <Label className="text-sm font-medium">Condition</Label>
                                      <VariableAutocomplete
                                        placeholder="e.g., Is the 'Load More' button still visible and clickable?"
                                        value={step.condition || step.value || ""}
                                        onChange={(value: string) => {
                                          updateStep(step.id, 'condition', value)
                                          updateStep(step.id, 'value', value)
                                        }}
                                      />
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium">Max Iterations</Label>
                                      <Input
                                        type="number"
                                        placeholder="50"
                                        value={step.maxIterations || 50}
                                        onChange={(e) => updateStep(step.id, 'maxIterations', parseInt(e.target.value) || 50)}
                                      />
                                    </div>
                                  </div>
                                  
                                  <NestedStepEditor
                                    steps={step.loopSteps || []}
                                    onStepsChange={(newSteps) => updateStep(step.id, 'loopSteps', newSteps)}
                                    title="⚪ While Steps - Execute While Condition is TRUE"
                                    emptyMessage="No steps in the while loop"
                                    depth={depth + 1}
                                    maxDepth={maxDepth}
                                  />
                                </div>
                              ) : step.type === 'goto' ? (
                                <div className="space-y-3">
                                  <div>
                                    <Label className="text-sm font-medium">URL to Navigate To</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., https://hirafi.com"
                                      value={step.url || ""}
                                      onChange={(value: string) => updateStep(step.id, 'url', value)}
                                    />
                                  </div>
                                </div>
                              ) : step.type === 'aiInput' ? (
                                <div className="space-y-3">
                                  <div>
                                    <Label className="text-sm font-medium">Text to Input</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., my-secret-password"
                                      value={step.value || ""}
                                      onChange={(value: string) => updateStep(step.id, 'value', value)}
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium">Target Element</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., The password field"
                                      value={step.target || ""}
                                      onChange={(value: string) => updateStep(step.id, 'target', value)}
                                    />
                                  </div>
                                </div>
                              ) : step.type === 'aiKeyboardPress' ? (
                                <div className="space-y-3">
                                  <div>
                                    <Label className="text-sm font-medium">Key to Press</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., Press Enter key"
                                      value={step.prompt || ""}
                                      onChange={(value: string) => updateStep(step.id, 'prompt', value)}
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium">Target Element (optional)</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., The search input field"
                                      value={step.target || ""}
                                      onChange={(value: string) => updateStep(step.id, 'target', value)}
                                    />
                                  </div>
                                </div>
                              ) : step.type === 'aiWaitElement' ? (
                                <div className="space-y-3">
                                  <div>
                                    <Label className="text-sm font-medium">Condition to Wait For</Label>
                                    <VariableAutocomplete
                                      placeholder="e.g., The success message appears"
                                      value={step.prompt || ""}
                                      onChange={(value: string) => updateStep(step.id, 'prompt', value)}
                                      rows={actionConfig[step.type]?.valueAsTextarea ? 3 : 1}
                                    />
                                  </div>
                                  
                                  <div className="grid grid-cols-2 gap-3">
                                    <div>
                                      <Label className="text-sm font-medium">Timeout (ms)</Label>
                                      <Input
                                        type="number"
                                        placeholder="15000"
                                        value={step.timeoutMs || ""}
                                        onChange={(e) => updateStep(step.id, 'timeoutMs', e.target.value ? parseInt(e.target.value) : undefined)}
                                      />
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium">Check Interval (ms)</Label>
                                      <Input
                                        type="number"
                                        placeholder="3000"
                                        value={step.checkIntervalMs || ""}
                                        onChange={(e) => updateStep(step.id, 'checkIntervalMs', e.target.value ? parseInt(e.target.value) : undefined)}
                                      />
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div>
                                  {actionConfig[step.type]?.valueLabel && (
                                    <div>
                                      <Label className="text-sm font-medium">
                                        {actionConfig[step.type].valueLabel}
                                      </Label>
                                      <VariableAutocomplete
                                        placeholder={actionConfig[step.type].valuePlaceholder || ""}
                                        value={step.prompt || ""}
                                        onChange={(value: string) => updateStep(step.id, 'prompt', value)}
                                        rows={actionConfig[step.type]?.valueAsTextarea ? 3 : 1}
                                      />
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        )}
      </CardContent>
    </Card>
  )
} 