/**
 * System Settings Service
 * Manages system-wide settings in the database
 */

import { ObjectId } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';
import { systemSettingsCollection, ensureMongoDBConnection } from './dbConnection.js';
import logger from '../../utils/logger.js';
import {
  SystemSetting,
  SystemSettingType,
  SMTPConfig,
  SlackConfig,
  AIModelConfig,
  SystemSettingsServiceResponse,
  TestSMTPRequest,
  TestSlackRequest
} from '../../models/system-settings.js';
import nodemailer from 'nodemailer';
import { WebClient } from '@slack/web-api';

/**
 * Get all system settings
 * @param type Optional filter by setting type
 */
export async function getAllSystemSettings(type?: SystemSettingType): Promise<SystemSettingsServiceResponse> {
  try {
    const query = type ? { type } : {};

    // Ensure MongoDB connection and systemSettingsCollection is available
    await ensureMongoDBConnection();
    if (!systemSettingsCollection) {
      return {
        success: false,
        message: 'System settings collection not available'
      };
    }

    const settings = await systemSettingsCollection.find(query).toArray();

    return {
      success: true,
      settings: settings.map(setting => {
        const systemSetting: SystemSetting = {
          id: setting.id || setting._id.toString(),
          type: setting.type,
          name: setting.name,
          config: setting.config,
          isActive: setting.isActive,
          createdAt: setting.createdAt,
          updatedAt: setting.updatedAt
        };
        return systemSetting;
      })
    };
  } catch (error: any) {
    logger.error(`Error getting system settings: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get a system setting by ID
 * @param id Setting ID
 */
export async function getSystemSettingById(id: string): Promise<SystemSettingsServiceResponse> {
  try {
    // Ensure MongoDB connection and systemSettingsCollection is available
    await ensureMongoDBConnection();
    if (!systemSettingsCollection) {
      return {
        success: false,
        message: 'System settings collection not available'
      };
    }

    let setting;
    try {
      setting = await systemSettingsCollection.findOne({
        $or: [
          { id },
          { _id: new ObjectId(id) }
        ]
      });
    } catch (error) {
      // If ObjectId conversion fails, try with just the id
      setting = await systemSettingsCollection.findOne({ id });
    }

    if (!setting) {
      return {
        success: false,
        message: `System setting with ID ${id} not found`
      };
    }

    return {
      success: true,
      setting: {
        id: setting.id || setting._id.toString(),
        type: setting.type,
        name: setting.name,
        config: setting.config,
        isActive: setting.isActive,
        createdAt: setting.createdAt,
        updatedAt: setting.updatedAt
      } as SystemSetting
    };
  } catch (error: any) {
    logger.error(`Error getting system setting by ID: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get a system setting by type
 * @param type Setting type
 */
export async function getSystemSettingByType(type: SystemSettingType): Promise<SystemSettingsServiceResponse> {
  try {
    // Ensure MongoDB connection and systemSettingsCollection is available
    await ensureMongoDBConnection();
    if (!systemSettingsCollection) {
      return {
        success: false,
        message: 'System settings collection not available'
      };
    }

    const setting = await systemSettingsCollection.findOne({ type, isActive: true });

    if (!setting) {
      return {
        success: false,
        message: `Active system setting of type ${type} not found`
      };
    }

    return {
      success: true,
      setting: {
        id: setting.id || setting._id.toString(),
        type: setting.type,
        name: setting.name,
        config: setting.config,
        isActive: setting.isActive,
        createdAt: setting.createdAt,
        updatedAt: setting.updatedAt
      } as SystemSetting
    };
  } catch (error: any) {
    logger.error(`Error getting system setting by type: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Create a new system setting
 * @param setting Setting data
 */
export async function createSystemSetting(setting: Partial<SystemSetting>): Promise<SystemSettingsServiceResponse> {
  try {
    // Ensure MongoDB connection and systemSettingsCollection is available
    await ensureMongoDBConnection();
    if (!systemSettingsCollection) {
      return {
        success: false,
        message: 'System settings collection not available'
      };
    }

    // Validate required fields
    if (!setting.type) {
      return {
        success: false,
        message: 'Setting type is required'
      };
    }

    if (!setting.name) {
      return {
        success: false,
        message: 'Setting name is required'
      };
    }

    if (!setting.config) {
      return {
        success: false,
        message: 'Setting configuration is required'
      };
    }

    // Validate configuration based on type
    const validationResult = validateConfig(setting.type, setting.config);
    if (!validationResult.valid) {
      return {
        success: false,
        message: validationResult.message
      };
    }

    // If this is set to active, deactivate other settings of the same type
    if (setting.isActive) {
      await systemSettingsCollection.updateMany(
        { type: setting.type },
        { $set: { isActive: false, updatedAt: new Date() } }
      );
    }

    // Create new setting
    const newSetting: SystemSetting = {
      id: uuidv4(),
      type: setting.type,
      name: setting.name,
      config: setting.config,
      isActive: setting.isActive ?? false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await systemSettingsCollection.insertOne(newSetting);

    if (!result.acknowledged) {
      return {
        success: false,
        message: 'Failed to create system setting'
      };
    }

    return {
      success: true,
      message: 'System setting created successfully',
      setting: newSetting
    };
  } catch (error: any) {
    logger.error(`Error creating system setting: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Update a system setting
 * @param id Setting ID
 * @param updates Setting updates
 */
export async function updateSystemSetting(
  id: string,
  updates: Partial<SystemSetting>
): Promise<SystemSettingsServiceResponse> {
  try {
    // Ensure MongoDB connection and systemSettingsCollection is available
    await ensureMongoDBConnection();
    if (!systemSettingsCollection) {
      return {
        success: false,
        message: 'System settings collection not available'
      };
    }

    // Get the current setting
    let currentSetting;
    try {
      currentSetting = await systemSettingsCollection.findOne({
        $or: [
          { id },
          { _id: new ObjectId(id) }
        ]
      });
    } catch (error) {
      // If ObjectId conversion fails, try with just the id
      currentSetting = await systemSettingsCollection.findOne({ id });
    }

    if (!currentSetting) {
      return {
        success: false,
        message: `System setting with ID ${id} not found`
      };
    }

    // If config is being updated, validate it
    if (updates.config) {
      const type = updates.type || currentSetting.type;
      const validationResult = validateConfig(type, updates.config);
      if (!validationResult.valid) {
        return {
          success: false,
          message: validationResult.message
        };
      }
    }

    // If this is being set to active, deactivate other settings of the same type
    if (updates.isActive) {
      const type = updates.type || currentSetting.type;
      try {
        await systemSettingsCollection.updateMany(
          {
            type,
            $or: [
              { id: { $ne: id } },
              { _id: { $ne: new ObjectId(id) } }
            ]
          },
          { $set: { isActive: false, updatedAt: new Date() } }
        );
      } catch (error) {
        // If ObjectId conversion fails, use only the id condition
        await systemSettingsCollection.updateMany(
          {
            type,
            id: { $ne: id }
          },
          { $set: { isActive: false, updatedAt: new Date() } }
        );
      }
    }

    // Update the setting
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };

    let result;
    try {
      result = await systemSettingsCollection.updateOne(
        {
          $or: [
            { id },
            { _id: new ObjectId(id) }
          ]
        },
        { $set: updateData }
      );
    } catch (error) {
      // If ObjectId conversion fails, use only the id condition
      result = await systemSettingsCollection.updateOne(
        { id },
        { $set: updateData }
      );
    }

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: `System setting with ID ${id} not found`
      };
    }

    // Get the updated setting
    let updatedSetting;
    try {
      updatedSetting = await systemSettingsCollection.findOne({
        $or: [
          { id },
          { _id: new ObjectId(id) }
        ]
      });
    } catch (error) {
      // If ObjectId conversion fails, try with just the id
      updatedSetting = await systemSettingsCollection.findOne({ id });
    }

    if (!updatedSetting) {
      return {
        success: true,
        message: 'System setting updated successfully, but could not retrieve the updated setting'
      };
    }

    return {
      success: true,
      message: 'System setting updated successfully',
      setting: {
        id: updatedSetting.id || updatedSetting._id.toString(),
        type: updatedSetting.type,
        name: updatedSetting.name,
        config: updatedSetting.config,
        isActive: updatedSetting.isActive,
        createdAt: updatedSetting.createdAt,
        updatedAt: updatedSetting.updatedAt
      } as SystemSetting
    };
  } catch (error: any) {
    logger.error(`Error updating system setting: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Delete a system setting
 * @param id Setting ID
 */
export async function deleteSystemSetting(id: string): Promise<SystemSettingsServiceResponse> {
  try {
    // Ensure MongoDB connection and systemSettingsCollection is available
    await ensureMongoDBConnection();
    if (!systemSettingsCollection) {
      return {
        success: false,
        message: 'System settings collection not available'
      };
    }

    let result;
    try {
      result = await systemSettingsCollection.deleteOne({
        $or: [
          { id },
          { _id: new ObjectId(id) }
        ]
      });
    } catch (error) {
      // If ObjectId conversion fails, use only the id condition
      result = await systemSettingsCollection.deleteOne({ id });
    }

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: `System setting with ID ${id} not found`
      };
    }

    return {
      success: true,
      message: 'System setting deleted successfully'
    };
  } catch (error: any) {
    logger.error(`Error deleting system setting: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Validate configuration based on type
 * @param type Setting type
 * @param config Configuration object
 */
function validateConfig(type: SystemSettingType, config: any): { valid: boolean; message?: string } {
  switch (type) {
    case SystemSettingType.SMTP:
      return validateSMTPConfig(config);
    case SystemSettingType.SLACK:
      return validateSlackConfig(config);
    case SystemSettingType.AI_MODEL:
      return validateAIModelConfig(config);
    case SystemSettingType.LANDING_PAGE:
      return validateLandingPageConfig(config);
    default:
      return { valid: false, message: `Unknown setting type: ${type}` };
  }
}

/**
 * Validate SMTP configuration
 * @param config SMTP configuration
 */
function validateSMTPConfig(config: any): { valid: boolean; message?: string } {
  if (!config.host) {
    return { valid: false, message: 'SMTP host is required' };
  }

  if (!config.port) {
    return { valid: false, message: 'SMTP port is required' };
  }

  if (typeof config.secure !== 'boolean') {
    return { valid: false, message: 'SMTP secure flag must be a boolean' };
  }

  if (!config.auth || !config.auth.user) {
    return { valid: false, message: 'SMTP username is required' };
  }

  if (!config.auth || !config.auth.pass) {
    return { valid: false, message: 'SMTP password is required' };
  }

  if (!config.from) {
    return { valid: false, message: 'SMTP from address is required' };
  }

  if (!config.fromName) {
    return { valid: false, message: 'SMTP from name is required' };
  }

  return { valid: true };
}

/**
 * Validate Slack configuration
 * @param config Slack configuration
 */
function validateSlackConfig(config: any): { valid: boolean; message?: string } {
  if (!config.botToken) {
    return { valid: false, message: 'Slack bot token is required' };
  }

  if (!config.signingSecret) {
    return { valid: false, message: 'Slack signing secret is required' };
  }

  if (!config.defaultChannel) {
    return { valid: false, message: 'Slack default channel is required' };
  }

  return { valid: true };
}

/**
 * Validate AI Model configuration
 * @param config AI Model configuration
 */
function validateAIModelConfig(config: any): { valid: boolean; message?: string } {
  if (!config.name) {
    return { valid: false, message: 'AI Model name is required' };
  }

  if (!config.api) {
    return { valid: false, message: 'AI Model API endpoint is required' };
  }

  if (!config.apiKey) {
    return { valid: false, message: 'AI Model API key is required' };
  }

  if (typeof config.isActive !== 'boolean') {
    return { valid: false, message: 'AI Model isActive flag must be a boolean' };
  }

  if (typeof config.supportsImageProcessing !== 'boolean') {
    return { valid: false, message: 'AI Model supportsImageProcessing flag must be a boolean' };
  }

  return { valid: true };
}

/**
 * Validate Landing Page configuration
 * @param config Landing Page configuration
 */
function validateLandingPageConfig(config: any): { valid: boolean; message?: string } {
  if (!config.adminEmails) {
    return { valid: false, message: 'Admin emails are required' };
  }

  if (!config.emailSubject) {
    return { valid: false, message: 'Email subject is required' };
  }

  if (!config.emailTemplate) {
    return { valid: false, message: 'Email template is required' };
  }

  if (!config.thankYouMessage) {
    return { valid: false, message: 'Thank you message is required' };
  }

  return { valid: true };
}

/**
 * Test SMTP configuration
 * @param request Test request
 */
export async function testSMTPConfig(request: TestSMTPRequest): Promise<SystemSettingsServiceResponse> {
  try {
    let config: SMTPConfig;

    // Get config from setting ID or use provided config
    if (request.settingId) {
      const result = await getSystemSettingById(request.settingId);
      if (!result.success || !result.setting) {
        return {
          success: false,
          message: `System setting with ID ${request.settingId} not found`
        };
      }
      config = result.setting.config as SMTPConfig;
    } else if (request.config) {
      config = request.config;
    } else {
      return {
        success: false,
        message: 'Either settingId or config must be provided'
      };
    }

    // Validate config
    const validationResult = validateSMTPConfig(config);
    if (!validationResult.valid) {
      return {
        success: false,
        message: validationResult.message
      };
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: {
        user: config.auth.user,
        pass: config.auth.pass
      },
      // AWS SES için TLS seçenekleri
      tls: {
        // TLS sorunlarını gidermek için
        rejectUnauthorized: false,
        minVersion: 'TLSv1.2'
      },
      debug: true // Hata ayıklama için
    });

    logger.info(`Testing SMTP with: host=${config.host}, port=${config.port}, secure=${config.secure}, user=${config.auth.user}`);


    // Verify SMTP connection
    try {
      logger.info('Verifying SMTP connection...');
      await transporter.verify();
      logger.info('SMTP connection verified successfully');
    } catch (verifyError: any) {
      logger.error(`SMTP connection verification failed: ${verifyError.message}`);
      return {
        success: false,
        error: `SMTP connection verification failed: ${verifyError.message}`
      };
    }

    // Send test email
    try {
      logger.info(`Sending test email to ${request.testEmail}...`);
      await transporter.sendMail({
        from: `"${config.fromName}" <${config.from}>`,
        to: request.testEmail,
        subject: 'HiRafi AI - SMTP Configuration Test',
        text: 'This is a test email to verify your SMTP configuration.',
        html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4f46e5;">HiRafi AI - SMTP Configuration Test</h2>
          <p>This is a test email to verify your SMTP configuration.</p>
          <p>If you're seeing this email, your SMTP configuration is working correctly!</p>
          <hr style="border: 1px solid #e5e7eb; margin: 20px 0;" />
          <p style="color: #6b7280; font-size: 14px;">
            This is an automated message, please do not reply.
          </p>
        </div>
      `
      });

      logger.info('Test email sent successfully');

      return {
        success: true,
        message: `Test email sent successfully to ${request.testEmail}`,
        setting: {
          id: 'test',
          type: SystemSettingType.SMTP,
          name: 'Test SMTP Configuration',
          config,
          isActive: false,
          createdAt: new Date()
        }
      };
    } catch (sendError: any) {
      logger.error(`Error sending test email: ${sendError.message}`);
      return {
        success: false,
        error: `Error sending test email: ${sendError.message}`
      };
    }
  } catch (error: any) {
    logger.error(`Error testing SMTP configuration: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test Slack configuration
 * @param request Test request
 */
export async function testSlackConfig(request: TestSlackRequest): Promise<SystemSettingsServiceResponse> {
  try {
    let config: SlackConfig;

    // Get config from setting ID or use provided config
    if (request.settingId) {
      const result = await getSystemSettingById(request.settingId);
      if (!result.success || !result.setting) {
        return {
          success: false,
          message: `System setting with ID ${request.settingId} not found`
        };
      }
      config = result.setting.config as SlackConfig;
    } else if (request.config) {
      config = request.config;
    } else {
      return {
        success: false,
        message: 'Either settingId or config must be provided'
      };
    }

    // Validate config
    const validationResult = validateSlackConfig(config);
    if (!validationResult.valid) {
      return {
        success: false,
        message: validationResult.message
      };
    }

    // Create Slack client
    const slack = new WebClient(config.botToken);

    // Send test message
    const message = request.testMessage || 'This is a test message to verify your Slack configuration.';
    const channel = config.defaultChannel;

    await slack.chat.postMessage({
      channel,
      text: message,
      blocks: [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: 'HiRafi AI - Slack Configuration Test',
            emoji: true
          }
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: message
          }
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: 'This is an automated test message.'
            }
          ]
        }
      ]
    });

    return {
      success: true,
      message: `Test message sent successfully to ${channel}`,
      setting: {
        id: 'test',
        type: SystemSettingType.SLACK,
        name: 'Test Slack Configuration',
        config,
        isActive: false,
        createdAt: new Date()
      }
    };
  } catch (error: any) {
    logger.error(`Error testing Slack configuration: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}
