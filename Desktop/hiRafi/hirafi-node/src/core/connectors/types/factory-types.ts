/**
 * Factory Type Definitions
 * Defines types for factory patterns
 */

import { IBaseConnector } from '../interfaces/base-connector.interface.js';

/**
 * Factory configuration type
 */
export interface FactoryConfig {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  maxResolutionDepth?: number;
  defaultConnectorType?: string;
  hubUrl?: string;
  secretKey?: string;
  nodeId?: string;
  clientId?: string;
}

/**
 * Connector creation options type
 */
export interface ConnectorCreationOptions {
  type: string;
  config?: any;
  dependencies?: Record<string, any>;
  lifecycle?: 'singleton' | 'transient';
}

/**
 * Factory method type
 */
export type FactoryMethod<T> = (...args: any[]) => Promise<T> | T;

/**
 * Connector factory method type
 */
export type ConnectorFactoryMethod = FactoryMethod<IBaseConnector>;

/**
 * Factory registration type
 */
export interface FactoryRegistration<T = any> {
  type: string;
  factory: FactoryMethod<T>;
  dependencies?: string[];
  singleton?: boolean;
}

/**
 * Factory statistics type
 */
export interface FactoryStats {
  totalCreations: number;
  creationsByType: Record<string, number>;
  successfulCreations: number;
  failedCreations: number;
  averageCreationTime: number;
  registeredTypes: string[];
  singletonInstances: number;
  transientInstances: number;
}

/**
 * Factory error type
 */
export interface FactoryError {
  type: string;
  message: string;
  timestamp: string;
  context?: any;
}

/**
 * Factory event type
 */
export interface FactoryEvent {
  type: string;
  factoryType: string;
  timestamp: string;
  data?: any;
}

/**
 * Factory lifecycle type
 */
export interface FactoryLifecycle {
  beforeCreate?: (type: string, config?: any) => Promise<void>;
  afterCreate?: (type: string, instance: any, config?: any) => Promise<void>;
  beforeDestroy?: (type: string, instance: any) => Promise<void>;
  afterDestroy?: (type: string) => Promise<void>;
}
