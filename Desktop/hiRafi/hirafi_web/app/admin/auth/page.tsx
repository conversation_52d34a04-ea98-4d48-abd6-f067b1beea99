"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { AuthLayout } from "@/components/auth/auth-layout"
import { AdminLoginForm } from "@/components/auth/admin-login-form"
import { useAdminAuth } from "@/lib/api/admin-auth"

export default function AdminAuthPage() {
  const router = useRouter()
  const { isAuthenticated, isLoading } = useAdminAuth()

  useEffect(() => {
    // If already authenticated, redirect to admin dashboard
    if (!isLoading && isAuthenticated) {
      router.push("/admin/dashboard")
    }
  }, [isLoading, isAuthenticated, router])

  const handleAuthSuccess = () => {
    router.push("/admin/dashboard")
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  // If already authenticated, don't render the login form
  if (isAuthenticated) {
    return null
  }

  return (
    <AuthLayout backgroundImage="/placeholder.svg?height=1080&width=1920&text=Admin%20Panel">
      <AdminLoginForm onSuccess={handleAuthSuccess} />
    </AuthLayout>
  )
}
