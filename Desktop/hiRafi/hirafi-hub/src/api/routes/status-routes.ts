/**
 * Status Routes
 * API endpoints for system status
 */

import { Router, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { nodeManager } from '../../core/node-manager/index.js';
import { testManager } from '../../core/test-manager/index.js';
import { queueService } from '../../services/redis/queueService.js';
import { pauseService } from '../../services/redis/pauseService.js';
import { authenticateAdmin, AdminRequest } from '../middleware/adminAuthMiddleware.js';

const router = Router();

/**
 * @route   GET /api/status/system
 * @desc    Get system status including pause status
 * @access  Admin
 */
router.get('/system', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Get system pause status
    const systemPaused = await pauseService.isSystemPaused();

    // Get all nodes
    const nodes = nodeManager.getAllNodes();

    // Get node pause status
    const nodeIds = nodes.map(node => node.id);
    const nodePauseStatus = await pauseService.getNodesPauseStatus(nodeIds);

    // Get queue status
    const queueStatus = await queueService.getQueueStatus();

    // Combine node info with pause status
    const nodeStatuses = nodes.map(node => ({
      id: node.id,
      name: node.name,
      status: node.status,
      isPaused: nodePauseStatus[node.id] || false,
      lastSeen: node.lastSeen,
      capabilities: node.capabilities || {}
    }));

    // Get test queue status
    const testQueueStatus = await testManager.getQueueStatus();

    return res.json({
      success: true,
      system: {
        paused: systemPaused
      },
      nodes: nodeStatuses,
      queues: {
        ...queueStatus,
        tests: testQueueStatus
      }
    });
  } catch (error: any) {
    logger.error(`API: Error getting system status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/status/nodes
 * @desc    Get all nodes status
 * @access  Admin
 */
router.get('/nodes', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Get all nodes
    const nodes = nodeManager.getAllNodes();

    // Get node pause status
    const nodeIds = nodes.map(node => node.id);
    const nodePauseStatus = await pauseService.getNodesPauseStatus(nodeIds);

    // Combine node info with pause status
    const nodeStatuses = nodes.map(node => ({
      id: node.id,
      name: node.name,
      status: node.status,
      isPaused: nodePauseStatus[node.id] || false,
      lastSeen: node.lastSeen,
      capabilities: node.capabilities || {}
    }));

    return res.json({
      success: true,
      nodes: nodeStatuses
    });
  } catch (error: any) {
    logger.error(`API: Error getting nodes status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/status/queues
 * @desc    Get queue status
 * @access  Admin
 */
router.get('/queues', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Get queue status from Redis
    const queueStatus = await queueService.getQueueStatus();

    // Get test queue status from test manager
    const testQueueStatus = await testManager.getQueueStatus();

    return res.json({
      success: true,
      queues: {
        ...queueStatus,
        tests: testQueueStatus
      }
    });
  } catch (error: any) {
    logger.error(`API: Error getting queue status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
