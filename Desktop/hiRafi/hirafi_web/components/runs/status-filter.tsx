"use client"

import { useState } from "react"
import { Filter, Check } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { REPORT_STATUS_OPTIONS } from "@/lib/constants/report-status"

interface StatusFilterProps {
  activeFilter: string
  onStatusFilterChange: (status: string) => void
}

// Use standardized status options
const statusOptions = REPORT_STATUS_OPTIONS

export function StatusFilter({ activeFilter, onStatusFilterChange }: StatusFilterProps) {
  const [open, setOpen] = useState(false)

  // Get the currently selected status option
  const selectedStatus = statusOptions.find(option => option.value === activeFilter) || statusOptions[0]

  // Handle status selection
  const handleStatusSelect = (value: string) => {
    onStatusFilterChange(value)
    setOpen(false)
  }

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      case "stopped":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
      case "partial":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
      case "created":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
      case "queued":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400"
    }
  }

  // Get status icon color
  const getIconColor = (status: string) => {
    switch (status) {
      case "running":
      case "completed":
        return "text-green-600 dark:text-green-400"
      case "failed":
        return "text-red-600 dark:text-red-400"
      case "stopped":
        return "text-yellow-600 dark:text-yellow-400"
      case "partial":
        return "text-orange-600 dark:text-orange-400"
      case "created":
      case "queued":
        return "text-blue-600 dark:text-blue-400"
      default:
        return "text-gray-600 dark:text-gray-400"
    }
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-9 border-gray-200 dark:border-gray-700 min-w-[120px] justify-between",
            activeFilter !== "all" ? "bg-gray-50 dark:bg-gray-800" : ""
          )}
        >
          <div className="flex items-center">
            <Filter className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
            <span className="text-sm font-medium">
              {activeFilter !== "all" ? (
                <span className="flex items-center">
                  <span className={cn("mr-1.5", getIconColor(activeFilter))}>
                    {statusOptions.find(s => s.value === activeFilter)?.icon}
                  </span>
                  {selectedStatus.label}
                </span>
              ) : (
                "Status"
              )}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[200px]">
        {statusOptions.map((status) => (
          <DropdownMenuItem
            key={status.value}
            className={cn(
              "flex items-center px-3 py-2 cursor-pointer",
              activeFilter === status.value ? "bg-gray-100 dark:bg-gray-800" : ""
            )}
            onClick={() => handleStatusSelect(status.value)}
          >
            <div className="flex items-center flex-1">
              {status.value !== "all" && (
                <span className={cn("mr-2 text-lg", getIconColor(status.value))}>
                  {status.icon}
                </span>
              )}
              <span>{status.label}</span>
            </div>
            {activeFilter === status.value && (
              <Check className="h-4 w-4 ml-2 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
