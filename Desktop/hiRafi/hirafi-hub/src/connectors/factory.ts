/**
 * Connector Factory
 * Creates appropriate connector instances based on type
 */

import { MessageConnector, QueueConnector, RealTimeConnector } from './interfaces.js';
import { redisAdapter } from './redis/adapter.js';
import { webSocketConnector as websocketConnector } from './index.js';

export class ConnectorFactory {
  /**
   * Create a message connector based on type
   * @param type Type of connector to create
   * @param config Configuration for the connector
   */
  static createMessageConnector(type: string, config?: any): MessageConnector {
    switch (type.toLowerCase()) {
      case 'websocket':
        // X-Core WebSocket connector'ı kullan
        return websocketConnector;
      case 'redis':
        return redisAdapter;
      default:
        throw new Error(`Unknown connector type: ${type}`);
    }
  }

  /**
   * Create a queue connector (for message queues)
   * @param type Type of queue connector
   * @param config Configuration for the connector
   */
  static createQueueConnector(type: string = 'redis', config?: any): QueueConnector {
    switch (type.toLowerCase()) {
      case 'redis':
        return redisAdapter;
      default:
        throw new Error(`Unsupported queue connector type: ${type}`);
    }
  }

  /**
   * Create a real-time connector (for websockets, etc)
   * @param type Type of real-time connector
   * @param config Configuration for the connector
   */
  static createRealTimeConnector(type: string = 'websocket', config?: any): RealTimeConnector {
    if (type.toLowerCase() !== 'websocket') {
      throw new Error(`Unsupported real-time connector type: ${type}`);
    }

    // X-Core WebSocket connector'ı kullan
    return websocketConnector;
  }
}