"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

import {
  Cloud,
  Server,
  Smartphone,
  Tablet,
  X
} from "lucide-react"
import { DeviceSelectionModal } from "../device-selection-modal"
import { EnvironmentSettings } from "../../run-context"
import {
  AndroidEnvironmentSettings,
  AndroidDevice,
  SauceLabsConfig,
  TestiniumConfig
} from "@/types/android-environment"
import { DeviceProvider, DeviceProviderUtils } from "@/types/device-provider"

interface DevicesTabProps {
  environmentSettings: EnvironmentSettings;
  updateEnvironmentSettings: (settings: EnvironmentSettings) => void;
  activeProviders: {
    sauceLabs: boolean;
    testinium: boolean;
  };
  handleProviderToggle: (provider: 'sauceLabs' | 'testinium', checked: boolean) => void;
}

// Type guard to check if environmentSettings is AndroidEnvironmentSettings
const isAndroidSettings = (settings: EnvironmentSettings): settings is AndroidEnvironmentSettings => {
  return settings.platform === 'android';
}

export function DevicesTab({
  environmentSettings,
  updateEnvironmentSettings,
  activeProviders,
  handleProviderToggle
}: DevicesTabProps) {
  const [isDeviceModalOpen, setIsDeviceModalOpen] = useState(false)

  // Open device selection modal
  const openDeviceSelectionModal = () => {
    setIsDeviceModalOpen(true)
  }

  // Handle device selection from modal with mutual exclusivity enforcement
  const handleDeviceSelection = (selectedDevices: any[]) => {
    if (selectedDevices.length === 0) return

    // Get current Android settings or create default ones
    const androidSettings = isAndroidSettings(environmentSettings)
      ? environmentSettings
      : {
          platform: 'android' as const,
          deviceProvider: 'sauceLabs' as DeviceProvider,
          sauceLabs: {
            username: '',
            accessKey: '',
            selectedDevices: []
          },
          testinium: {
            apiUrl: '',
            clientId: '',
            clientSecret: '',
            issuerUri: '',
            selectedDevices: []
          }
        };

    // Convert selected devices to AndroidDevice type
    const typedSelectedDevices: AndroidDevice[] = selectedDevices.map(d => ({
      id: d.id,
      name: d.name,
      serial: d.serial, // Include serial field for DevicePark API
      osVersion: d.osVersion,
      provider: d.provider || 'sauceLabs',
      deviceType: d.deviceType || 'phone',
      manufacturer: d.manufacturer,
      // Include additional device details if available
      apiLevel: d.apiLevel,
      screenResolution: d.screenResolution,
      screenSize: d.screenSize,
      ram: d.ram,
      modelNumber: d.modelNumber
    }));

    // Determine the selected provider from active providers
    const selectedProvider = DeviceProviderUtils.getActiveProvider(
      activeProviders.sauceLabs,
      activeProviders.testinium
    );

    if (!selectedProvider) {
      console.error("No active device provider selected.");
      return;
    }

    // Validate device selection against the selected provider
    const validation = DeviceProviderUtils.validateDeviceSelection(
      typedSelectedDevices,
      selectedProvider
    );

    if (!validation.isValid) {
      console.error('Device selection validation failed:', validation.error);
      return;
    }

    // For mutual exclusivity, only store devices for the selected provider
    const sauceLabsDevices = selectedProvider === 'sauceLabs' ? typedSelectedDevices : [];
    const testiniumDevices = selectedProvider === 'testinium' ? typedSelectedDevices : [];

    // Create updated SauceLabs config
    const updatedSauceLabsConfig: SauceLabsConfig = {
      ...androidSettings.sauceLabs,
      selectedDevices: sauceLabsDevices
    };

    // Create updated Testinium config
    const updatedTestiniumConfig = {
      ...androidSettings.testinium,
      selectedDevices: testiniumDevices
    };

    // Create new Android settings with deviceProvider field
    const newSettings: AndroidEnvironmentSettings = {
      ...androidSettings,
      platform: 'android',
      deviceProvider: selectedProvider,
      sauceLabs: updatedSauceLabsConfig,
      testinium: updatedTestiniumConfig
    };

    // Update the environment settings
    updateEnvironmentSettings(newSettings);

    // Log device selection for debugging
    console.log(`Selected ${selectedDevices.length} devices for provider ${selectedProvider}`);
  }

  // Function to get device icon based on type
  const getDeviceIcon = (device: AndroidDevice) => {
    return device.deviceType === 'tablet' ? (
      <Tablet className="h-4 w-4 text-purple-600 dark:text-purple-400" />
    ) : (
      <Smartphone className="h-4 w-4 text-green-600 dark:text-green-400" />
    )
  }

  // Handle removing a device from selection
  const handleRemoveDevice = (deviceId: string) => {
    if (!isAndroidSettings(environmentSettings)) return;

    const androidSettings = environmentSettings;

    // Determine the active provider
    const activeProvider = activeProviders.sauceLabs ? 'sauceLabs' : 'testinium';

    if (activeProvider === 'sauceLabs') {
      const updatedDevices = androidSettings.sauceLabs.selectedDevices?.filter(
        device => device.id !== deviceId
      ) || [];

      const newSettings: AndroidEnvironmentSettings = {
        ...androidSettings,
        sauceLabs: {
          ...androidSettings.sauceLabs,
          selectedDevices: updatedDevices
        }
      };

      updateEnvironmentSettings(newSettings);
    } else if (activeProvider === 'testinium') {
      const updatedDevices = androidSettings.testinium.selectedDevices?.filter(
        device => device.id !== deviceId
      ) || [];

      const newSettings: AndroidEnvironmentSettings = {
        ...androidSettings,
        testinium: {
          ...androidSettings.testinium,
          selectedDevices: updatedDevices
        }
      };

      updateEnvironmentSettings(newSettings);
    }
  }

  return (
    <div className="space-y-4">
      {/* Device Farm Providers Section */}
      <div className="space-y-4">
        <div className="flex items-center">
          <div className="bg-blue-100 dark:bg-blue-900/50 p-1.5 rounded-full mr-2">
            <Cloud className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-base font-medium text-blue-700 dark:text-blue-300">
              Device Farm Providers
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Select where to run your Android tests
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* SauceLabs Provider Card */}
          <Card
            className={`border shadow-sm transition-all duration-200 hover:shadow-md cursor-pointer ${
              activeProviders.sauceLabs
                ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700'
            }`}
            onClick={() => handleProviderToggle('sauceLabs', true)}
          >
            <CardContent className="p-4 flex flex-row items-center justify-between space-y-0">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full ${
                  activeProviders.sauceLabs
                    ? 'bg-blue-100 dark:bg-blue-800'
                    : 'bg-gray-100 dark:bg-gray-800'
                }`}>
                  <Server className={`h-5 w-5 ${
                    activeProviders.sauceLabs
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`} />
                </div>
                <div>
                  <h4 className="text-base flex items-center font-medium">
                    SauceLabs
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Run tests on real devices in the SauceLabs cloud
                  </p>
                </div>
              </div>
              <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                activeProviders.sauceLabs
                  ? 'border-blue-500 bg-blue-100 dark:border-blue-400 dark:bg-blue-900/30'
                  : 'border-gray-300 dark:border-gray-600'
              }`}>
                {activeProviders.sauceLabs && (
                  <div className="w-2.5 h-2.5 rounded-full bg-blue-500 dark:bg-blue-400" />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Testinium Provider Card */}
          <Card
            className={`border shadow-sm transition-all duration-200 hover:shadow-md cursor-pointer ${
              activeProviders.testinium
                ? 'border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700'
            }`}
            onClick={() => handleProviderToggle('testinium', true)}
          >
            <CardContent className="p-4 flex flex-row items-center justify-between space-y-0">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full ${
                  activeProviders.testinium
                    ? 'bg-blue-100 dark:bg-blue-800'
                    : 'bg-gray-100 dark:bg-gray-800'
                }`}>
                  <Smartphone className={`h-5 w-5 ${
                    activeProviders.testinium
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`} />
                </div>
                <div>
                  <h4 className="text-base flex items-center font-medium">
                    Testinium
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Run tests on Testinium device farm using DevicePark SDK
                  </p>
                </div>
              </div>
              <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                activeProviders.testinium
                  ? 'border-blue-500 bg-blue-100 dark:border-blue-400 dark:bg-blue-900/30'
                  : 'border-gray-300 dark:border-gray-600'
              }`}>
                {activeProviders.testinium && (
                  <div className="w-2.5 h-2.5 rounded-full bg-blue-500 dark:bg-blue-400" />
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Provider selection message with mutual exclusivity info */}
        {activeProviders.sauceLabs && (!isAndroidSettings(environmentSettings) ||
          !environmentSettings.sauceLabs.selectedDevices ||
          environmentSettings.sauceLabs.selectedDevices.length === 0) && (
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400 flex items-center">
            <div className="bg-amber-100 dark:bg-amber-900/30 p-1 rounded-full mr-2">
              <Smartphone className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            </div>
            Please select devices from SauceLabs (only one provider can be used per test)
          </div>
        )}

        {activeProviders.testinium && (!isAndroidSettings(environmentSettings) ||
          !environmentSettings.testinium.selectedDevices ||
          environmentSettings.testinium.selectedDevices.length === 0) && (
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400 flex items-center">
            <div className="bg-amber-100 dark:bg-amber-900/30 p-1 rounded-full mr-2">
              <Smartphone className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            </div>
            Please select devices from Testinium (only one provider can be used per test)
          </div>
        )}

        {/* Device Selection Button */}
        {(activeProviders.sauceLabs || activeProviders.testinium) && (
          <Button
            variant="default"
            className="w-full justify-center mt-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-md transition-all duration-200"
            onClick={openDeviceSelectionModal}
          >
            <div className="bg-white/20 p-1 rounded-full mr-2">
              <Smartphone className="h-4 w-4" />
            </div>
            Select Android Devices
          </Button>
        )}

        {/* Device Selection Modal */}
        <DeviceSelectionModal
          isOpen={isDeviceModalOpen}
          onClose={() => setIsDeviceModalOpen(false)}
          onSave={handleDeviceSelection}
          initialSelectedDevices={
            isAndroidSettings(environmentSettings)
              ? activeProviders.sauceLabs
                ? environmentSettings.sauceLabs.selectedDevices || []
                : activeProviders.testinium
                ? environmentSettings.testinium.selectedDevices || []
                : []
              : []
          }
          activeProvider={activeProviders.sauceLabs ? 'sauceLabs' : 'testinium'}
          onProviderChange={(newProvider) => {
            // Provider değiştiğinde parent'ı güncelle
            handleProviderToggle(newProvider, true);
          }}
          updateEnvironmentSettings={updateEnvironmentSettings}
        />

        {/* Selected Devices Display - Only show devices from active provider */}
        {isAndroidSettings(environmentSettings) && (
          (activeProviders.sauceLabs && environmentSettings.sauceLabs.selectedDevices && environmentSettings.sauceLabs.selectedDevices.length > 0) ||
          (activeProviders.testinium && environmentSettings.testinium.selectedDevices && environmentSettings.testinium.selectedDevices.length > 0)
        ) && (
          <>
            <div className="mt-4 p-4 border rounded-md bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-blue-200 dark:border-blue-800 shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium flex items-center text-blue-700 dark:text-blue-300">
                  <div className="bg-.blue-100 dark:bg-blue-900/50 p-1 rounded-full mr-2">
                    <Smartphone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  Selected Devices ({
                    activeProviders.sauceLabs ? (environmentSettings.sauceLabs.selectedDevices?.length || 0) :
                    activeProviders.testinium ? (environmentSettings.testinium.selectedDevices?.length || 0) : 0
                  }) - {activeProviders.sauceLabs ? 'SauceLabs' : 'Testinium'}
                </h4>
              </div>
              <div className="divide-y divide-gray-100 dark:divide-gray-800">
                {(activeProviders.sauceLabs ? (environmentSettings.sauceLabs.selectedDevices || []) :
                  activeProviders.testinium ? (environmentSettings.testinium.selectedDevices || []) : []
                ).map((device: AndroidDevice) => (
                  <div
                    key={`${device.provider}-${device.id}`}
                    className="py-2 first:pt-0 last:pb-0 flex items-center justify-between hover:bg-white/50 dark:hover:bg-gray-800/50 rounded-md px-2 transition-colors duration-200"
                  >
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <div className={`p-1.5 rounded-full ${device.deviceType === 'tablet' ? 'bg-purple-100 dark:bg-purple-900/50' : 'bg-green-100 dark:bg-green-900/50'}`}>
                        {getDeviceIcon(device)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center">
                          <p className="font-medium text-sm truncate">{device.name}</p>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            {getDeviceIcon(device)} {device.deviceType ? device.deviceType.charAt(0).toUpperCase() + device.deviceType.slice(1) : 'Device'}
                          </span>
                          <span className="text-gray-300 dark:text-gray-600">|</span>
                          <span>{device.osVersion}</span>
                          {device.apiLevel && (
                            <>
                              <span className="text-gray-300 dark:text-gray-600">|</span>
                              <span>API {device.apiLevel}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                      onClick={() => handleRemoveDevice(device.id)}
                      title={`Remove ${device.name}`}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
