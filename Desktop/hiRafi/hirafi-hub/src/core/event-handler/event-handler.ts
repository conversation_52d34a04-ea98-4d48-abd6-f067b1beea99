/**
 * Event Handler for hirafi-hub
 * 
 * Consolidates event handling responsibilities from multiple classes
 * into a single, clear event handling architecture. Eliminates responsibility
 * ambiguity and provides a single source of truth for all event coordination.
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { config } from '../../config/index.js';
import { IEventCoordinator } from './interfaces/event-coordinator.interface.js';

/**
 * Event types emitted by EventHandler
 */
export interface EventHandlerEvents {
  // Redis events
  'test:queued': (testData: any) => void;
  'test:completed': (testId: string, result: any) => void;
  'test:failed': (testId: string, error: any) => void;
  
  // WebSocket events
  'node:connected': (nodeId: string) => void;
  'node:disconnected': (nodeId: string) => void;
  'node:registered': (nodeId: string, nodeData: any) => void;
  
  // Node registry events
  'node:statusChanged': (nodeId: string, newStatus: string, oldStatus: string) => void;
  'node:testClaimed': (nodeId: string, testId: string) => void;
  'node:testReleased': (nodeId: string, testId: string) => void;
  
  // Test management events
  'test:assigned': (testId: string, nodeId: string) => void;
  'test:recovered': (testId: string, recoveryMethod: string) => void;
  'test:stuck': (testId: string, nodeId: string, details: any) => void;
}

/**
 * Event Handler
 * 
 * Single responsibility class that handles:
 * - Redis event communication
 * - WebSocket event coordination
 * - Node registry event coordination
 * - Test management event coordination
 * - Event deduplication and mutex management
 */
export class EventHandler extends EventEmitter implements IEventCoordinator {
  private static instance: EventHandler;

  // Event setup state
  private eventsSetup: boolean = false;
  
  // Component references - will be injected during initialization
  private webSocketConnector: any = null;
  private nodeRegistry: any = null;
  private testManager: any = null;
  private testQueueService: any = null;
  private testDistributor: any = null;
  private resultQueueService: any = null;

  // Event deduplication tracking
  private processedEvents: Set<string> = new Set();
  private eventProcessingMutex: Map<string, boolean> = new Map();

  // Bound method references for proper cleanup
  private boundMethods: Map<string, Function> = new Map();

  private constructor() {
    super();
    this.setupBoundMethods();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): EventHandler {
    if (!EventHandler.instance) {
      EventHandler.instance = new EventHandler();
    }
    return EventHandler.instance;
  }

  /**
   * Initialize EventHandler with component dependencies
   */
  public async initialize(dependencies: {
    webSocketConnector?: any;
    nodeRegistry?: any;
    testManager?: any;
    testQueueService?: any;
    testDistributor?: any;
    resultQueueService?: any;
  }): Promise<void> {
    logger.info('EventHandler: Initializing with dependencies');

    // Store component references
    this.webSocketConnector = dependencies.webSocketConnector;
    this.nodeRegistry = dependencies.nodeRegistry;
    this.testManager = dependencies.testManager;
    this.testQueueService = dependencies.testQueueService;
    this.testDistributor = dependencies.testDistributor;
    this.resultQueueService = dependencies.resultQueueService;

    // If webSocketConnector is not provided, try to import it
    if (!this.webSocketConnector) {
      try {
        const { webSocketConnector } = await import('../../connectors/index.js');
        this.webSocketConnector = webSocketConnector;
        logger.debug('EventHandler: WebSocket connector imported dynamically');
      } catch (error) {
        logger.warn(`EventHandler: Could not import WebSocket connector: ${error}`);
      }
    }

    logger.info('EventHandler: Dependencies injected successfully');
  }

  /**
   * Setup bound method references for proper cleanup
   */
  private setupBoundMethods(): void {
    // WebSocket event handlers
    this.boundMethods.set('handleNodeConnected', this.handleNodeConnected.bind(this));
    this.boundMethods.set('handleNodeDisconnected', this.handleNodeDisconnected.bind(this));
    this.boundMethods.set('handleNodeRegistered', this.handleNodeRegistered.bind(this));
    this.boundMethods.set('handleNodeTestClaimed', this.handleNodeTestClaimed.bind(this));
    this.boundMethods.set('handleNodeTestReleased', this.handleNodeTestReleased.bind(this));
    this.boundMethods.set('handleNodeStatusResponse', this.handleNodeStatusResponse.bind(this));

    // Node registry event handlers
    this.boundMethods.set('handleNodeStatusChanged', this.handleNodeStatusChanged.bind(this));
    this.boundMethods.set('handleNodeTestStuck', this.handleNodeTestStuck.bind(this));

    // Test management event handlers
    this.boundMethods.set('handleTestAdded', this.handleTestAdded.bind(this));
    this.boundMethods.set('handleTestAssigned', this.handleTestAssigned.bind(this));
    this.boundMethods.set('handleTestRecovered', this.handleTestRecovered.bind(this));
    this.boundMethods.set('handleTestRecoveryFailed', this.handleTestRecoveryFailed.bind(this));
    this.boundMethods.set('handleResultAdded', this.handleResultAdded.bind(this));
  }

  /**
   * Setup all event handlers (IEventCoordinator interface)
   */
  public setupAllEvents(): void {
    if (this.eventsSetup) {
      logger.debug('EventHandler: Events already setup, skipping');
      return;
    }

    logger.info('EventHandler: Setting up all event handlers');

    // Clean up any existing listeners first to prevent duplicates
    this.teardownAllEvents();

    this.setupWebSocketEvents();
    this.setupRedisEvents();
    this.setupTestManagementEvents();
    this.setupNodeRegistryEvents();

    this.eventsSetup = true;
    logger.info('EventHandler: All event handlers setup completed');
  }

  /**
   * Setup WebSocket event handlers
   */
  public setupWebSocketEvents(): void {
    if (!this.webSocketConnector) {
      logger.debug('EventHandler: WebSocket connector not available, skipping WebSocket event setup');
      return;
    }

    logger.debug('EventHandler: Setting up WebSocket event handlers');

    // Node connection events
    this.webSocketConnector.on('node:connected', this.boundMethods.get('handleNodeConnected'));
    this.webSocketConnector.on('node:registered', this.boundMethods.get('handleNodeRegistered'));

    // Node disconnection events - handle immediate disconnection
    this.webSocketConnector.on('node:disconnected', this.boundMethods.get('handleNodeDisconnected'));

    // Test lifecycle events from nodes
    this.webSocketConnector.on('node:testClaimed', this.boundMethods.get('handleNodeTestClaimed'));
    this.webSocketConnector.on('node:testReleased', this.boundMethods.get('handleNodeTestReleased'));

    // Node status coordination events
    this.webSocketConnector.on('node:status-response', this.boundMethods.get('handleNodeStatusResponse'));

    logger.debug('EventHandler: WebSocket event handlers setup completed');
  }

  /**
   * Setup Redis event handlers
   */
  public setupRedisEvents(): void {
    if (!config.connections?.redis?.enabled) {
      logger.debug('EventHandler: Redis disabled, skipping Redis event setup');
      return;
    }

    logger.debug('EventHandler: Setting up Redis event handlers');

    // Redis events will be handled through the queue services
    // No direct Redis event listeners needed here as services handle their own events

    logger.debug('EventHandler: Redis event handlers setup completed');
  }

  /**
   * Setup test management event handlers
   */
  public setupTestManagementEvents(): void {
    logger.debug('EventHandler: Setting up test management event handlers');

    if (this.testQueueService) {
      this.testQueueService.on('test:added', this.boundMethods.get('handleTestAdded'));
      this.testQueueService.on('test:recovered', this.boundMethods.get('handleTestRecovered'));
      this.testQueueService.on('test:recovery:failed', this.boundMethods.get('handleTestRecoveryFailed'));
    }

    if (this.testDistributor) {
      this.testDistributor.on('test:assigned', this.boundMethods.get('handleTestAssigned'));
    }

    if (this.resultQueueService) {
      this.resultQueueService.on('result:added', this.boundMethods.get('handleResultAdded'));
    }

    logger.debug('EventHandler: Test management event handlers setup completed');
  }

  /**
   * Setup node registry event handlers
   */
  public setupNodeRegistryEvents(): void {
    if (!this.nodeRegistry) {
      logger.debug('EventHandler: Node registry not available, skipping node registry event setup');
      return;
    }

    logger.debug('EventHandler: Setting up node registry event handlers');

    this.nodeRegistry.on('node:statusChanged', this.boundMethods.get('handleNodeStatusChanged'));
    this.nodeRegistry.on('node:testStuck', this.boundMethods.get('handleNodeTestStuck'));

    logger.debug('EventHandler: Node registry event handlers setup completed');
  }

  /**
   * Teardown all event handlers (IEventCoordinator interface)
   */
  public teardownAllEvents(): void {
    if (!this.eventsSetup) {
      return; // Already torn down
    }

    logger.info('EventHandler: Tearing down all event handlers');

    this.teardownWebSocketEvents();
    this.teardownRedisEvents();
    this.teardownTestManagementEvents();
    this.teardownNodeRegistryEvents();

    // Clear processed events tracking
    this.processedEvents.clear();
    this.eventProcessingMutex.clear();

    this.eventsSetup = false;
    logger.info('EventHandler: All event handlers teardown completed');
  }

  /**
   * Teardown WebSocket event handlers
   */
  public teardownWebSocketEvents(): void {
    if (!this.webSocketConnector) {
      return;
    }

    logger.debug('EventHandler: Tearing down WebSocket event handlers');

    this.webSocketConnector.removeListener('node:connected', this.boundMethods.get('handleNodeConnected'));
    this.webSocketConnector.removeListener('node:disconnected', this.boundMethods.get('handleNodeDisconnected'));
    this.webSocketConnector.removeListener('node:registered', this.boundMethods.get('handleNodeRegistered'));
    this.webSocketConnector.removeListener('node:testClaimed', this.boundMethods.get('handleNodeTestClaimed'));
    this.webSocketConnector.removeListener('node:testReleased', this.boundMethods.get('handleNodeTestReleased'));
  }

  /**
   * Teardown Redis event handlers
   */
  public teardownRedisEvents(): void {
    logger.debug('EventHandler: Tearing down Redis event handlers');
    // No direct Redis listeners to remove as services handle their own events
  }

  /**
   * Teardown test management event handlers
   */
  public teardownTestManagementEvents(): void {
    logger.debug('EventHandler: Tearing down test management event handlers');

    if (this.testQueueService) {
      this.testQueueService.removeListener('test:added', this.boundMethods.get('handleTestAdded'));
      this.testQueueService.removeListener('test:recovered', this.boundMethods.get('handleTestRecovered'));
      this.testQueueService.removeListener('test:recovery:failed', this.boundMethods.get('handleTestRecoveryFailed'));
    }

    if (this.testDistributor) {
      this.testDistributor.removeListener('test:assigned', this.boundMethods.get('handleTestAssigned'));
    }

    if (this.resultQueueService) {
      this.resultQueueService.removeListener('result:added', this.boundMethods.get('handleResultAdded'));
    }
  }

  /**
   * Teardown node registry event handlers
   */
  public teardownNodeRegistryEvents(): void {
    if (!this.nodeRegistry) {
      return;
    }

    logger.debug('EventHandler: Tearing down node registry event handlers');

    this.nodeRegistry.removeListener('node:statusChanged', this.boundMethods.get('handleNodeStatusChanged'));
    this.nodeRegistry.removeListener('node:testStuck', this.boundMethods.get('handleNodeTestStuck'));
  }

  /**
   * Check if events are setup
   */
  public areEventsSetup(): boolean {
    return this.eventsSetup;
  }

  // ===== EVENT HANDLERS =====

  /**
   * Handle node connected event
   */
  private handleNodeConnected = (nodeId: string): void => {
    logger.info(`EventHandler: Node ${nodeId} connected`);
    this.emit('node:connected', nodeId);
  }

  /**
   * Handle node disconnected event
   */
  private handleNodeDisconnected = (disconnectionData: any): void => {
    const { nodeId, code, reason, disconnectionType } = disconnectionData;
    logger.info(`EventHandler: Node ${nodeId} disconnected (type: ${disconnectionType}, code: ${code})`);

    // Call NodeRegistry to handle immediate disconnection
    if (this.nodeRegistry) {
      this.nodeRegistry.handleImmediateNodeDisconnection(nodeId, disconnectionType, code, reason);
    }

    this.emit('node:disconnected', nodeId);
  }

  /**
   * Handle node registered event
   */
  private handleNodeRegistered = (nodeId: string, nodeData: any): void => {
    logger.info(`EventHandler: Node ${nodeId} registered`);
    this.emit('node:registered', nodeId, nodeData);
  }

  /**
   * Handle node test claimed event with deduplication
   */
  private handleNodeTestClaimed = (data: { nodeId: string; testId: string; jobId?: string; success: boolean }): void => {
    const eventKey = `node:testClaimed:${data.nodeId}:${data.testId}`;

    // Check if this event has already been processed
    if (this.processedEvents.has(eventKey)) {
      logger.warn(`EventHandler: Node test claimed event already processed for node ${data.nodeId}, test ${data.testId}, skipping duplicate`);
      return;
    }

    if (data.success) {
      logger.info(`EventHandler: Node ${data.nodeId} successfully claimed test ${data.testId}`);

      // Call NodeRegistry to handle the test claim
      if (this.nodeRegistry) {
        this.nodeRegistry.handleNodeClaimedTest(data.nodeId, data.testId);
      }

      this.emit('node:testClaimed', data.nodeId, data.testId);
    } else {
      logger.warn(`EventHandler: Node ${data.nodeId} failed to claim test ${data.testId}`);
    }

    // Mark event as processed
    this.processedEvents.add(eventKey);
  }

  /**
   * Handle node test released event with deduplication
   */
  private handleNodeTestReleased = (data: { nodeId: string; testId: string; status?: string }): void => {
    const eventKey = `node:testReleased:${data.nodeId}:${data.testId}`;

    // Check if this event has already been processed
    if (this.processedEvents.has(eventKey)) {
      logger.warn(`EventHandler: Node test released event already processed for node ${data.nodeId}, test ${data.testId}, skipping duplicate`);
      return;
    }

    logger.info(`EventHandler: Node ${data.nodeId} released test ${data.testId} with status ${data.status || 'unknown'}`);

    // Call NodeRegistry to handle the test release
    if (this.nodeRegistry) {
      this.nodeRegistry.handleNodeReleasedTest(data.nodeId, data.testId);
    }

    this.emit('node:testReleased', data.nodeId, data.testId);

    // Mark event as processed
    this.processedEvents.add(eventKey);
  }

  /**
   * Handle node status changed event
   */
  private handleNodeStatusChanged = (nodeId: string, newStatus: string, oldStatus: string): void => {
    logger.debug(`EventHandler: Node ${nodeId} status changed from ${oldStatus} to ${newStatus}`);
    this.emit('node:statusChanged', nodeId, newStatus, oldStatus);
  }

  /**
   * Handle node test stuck event
   */
  private handleNodeTestStuck = (nodeId: string, testId: string, details: any): void => {
    logger.error(`EventHandler: Test ${testId} stuck on node ${nodeId}: ${details.reason}`);
    this.emit('test:stuck', testId, nodeId, details);
  }

  /**
   * Handle test added event
   */
  private handleTestAdded = (testId: string, data: any): void => {
    logger.debug(`EventHandler: Test ${testId} added to queue`);
    this.emit('test:queued', data);
  }

  /**
   * Handle test assigned event
   */
  private handleTestAssigned = (testId: string, nodeId: string): void => {
    logger.info(`EventHandler: Test ${testId} assigned to node ${nodeId}`);
    this.emit('test:assigned', testId, nodeId);
  }

  /**
   * Handle test recovered event
   */
  private handleTestRecovered = (testId: string, recoveryMethod: string): void => {
    logger.info(`EventHandler: Test ${testId} recovered using method: ${recoveryMethod}`);
    this.emit('test:recovered', testId, recoveryMethod);
  }

  /**
   * Handle test recovery failed event
   */
  private handleTestRecoveryFailed = (testId: string, error: any): void => {
    logger.error(`EventHandler: Test ${testId} recovery failed: ${error}`);
    this.emit('test:failed', testId, error);
  }

  /**
   * Handle result added event
   */
  private handleResultAdded = (testId: string, data: any): void => {
    logger.debug(`EventHandler: Result added for test ${testId}`);
    this.emit('test:completed', testId, data);
  }

  /**
   * Handle node status response event
   */
  private handleNodeStatusResponse = (data: { nodeId: string; isRunning: boolean; testId: string | null; timestamp: number }): void => {
    const { nodeId, isRunning, testId } = data;
    
    logger.debug(`EventHandler: Received node status response from ${nodeId} - running: ${isRunning}, testId: ${testId || 'none'}`);

    // Update NodeRegistry with current test status
    if (this.nodeRegistry) {
      const node = this.nodeRegistry.getNode(nodeId);
      if (node) {
        // Update node's current test ID if it has changed
        if (node.currentTestId !== testId) {
          logger.info(`EventHandler: Updating node ${nodeId} currentTestId from ${node.currentTestId || 'none'} to ${testId || 'none'}`);
          
          // Update the node directly in the registry
          node.currentTestId = testId;
          node.lastSeen = new Date();
          
          // Update status based on test running state
          const newStatus = isRunning && testId ? 'busy' : 'available';
          if (node.status !== newStatus) {
            const previousStatus = node.status;
            node.status = newStatus;
            logger.info(`EventHandler: Updated node ${nodeId} status from ${previousStatus} to ${newStatus} based on status response`);
            this.nodeRegistry.emit('node:statusChanged', nodeId, newStatus, previousStatus);
          }
        }
      } else {
        logger.warn(`EventHandler: Received status response for unknown node ${nodeId}`);
      }
    }
  }
}
