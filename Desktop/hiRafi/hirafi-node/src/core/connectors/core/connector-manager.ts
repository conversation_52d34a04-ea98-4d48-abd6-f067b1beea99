/**
 * Connector Manager
 * 
 * Manages connector instances and provides centralized connector operations.
 * Implements dependency injection and proper lifecycle management.
 */

import { IConnectorManager, IConnectorManagerConfig, IConnectorRegistrationInfo, IConnectorManagerStats } from '../interfaces/connector-manager.interface.js';
import { IBaseConnector } from '../interfaces/base-connector.interface.js';
import { IHeartbeatService } from '../interfaces/heartbeat-service.interface.js';
import { IRegistrationService } from '../interfaces/registration-service.interface.js';
import { ConnectorType } from '../types/connector-types.js';

/**
 * Connector Manager Class
 * 
 * Manages connector instances with dependency injection and proper lifecycle management.
 */
export class ConnectorManager implements IConnectorManager {
  private connectors: Map<string, IConnectorRegistrationInfo> = new Map();
  private isManagerInitialized = false;
  private config: IConnectorManagerConfig = {};
  private stats: IConnectorManagerStats;

  constructor(
    private logger: any,
    config: IConnectorManagerConfig,
    private heartbeatService: IHeartbeatService,
    private registrationService: IRegistrationService
  ) {
    this.config = {
      enableLogging: false,
      maxConnectors: 10,
      defaultConnectorType: ConnectorType.WEBSOCKET,
      autoCleanup: true,
      cleanupInterval: 300000, // 5 minutes
      ...config
    };

    this.stats = {
      totalConnectors: 0,
      connectorsByType: {},
      activeConnections: 0,
      totalConnectionAttempts: 0,
      successfulConnections: 0,
      failedConnections: 0,
      uptime: 0,
      memoryUsage: { used: 0, total: 0, percentage: 0 },
      connectorStats: {}
    };

    if (this.config.enableLogging) {
      this.logger.debug('ConnectorManager: Initialized with config', this.config);
    }
  }

  /**
   * Initialize the connector manager
   * @param config Manager configuration
   */
  async initialize(config: IConnectorManagerConfig = {}): Promise<void> {
    if (this.isManagerInitialized) {
      this.logger.debug('ConnectorManager: Already initialized');
      return;
    }

    this.config = { ...this.config, ...config };

    // Initialize services
    if (!this.heartbeatService.isInitialized()) {
      await this.heartbeatService.initialize(this.config);
    }

    if (!this.registrationService.isInitialized()) {
      await this.registrationService.initialize(this.config);
    }

    // Start cleanup interval if enabled
    if (this.config.autoCleanup) {
      this.startCleanupInterval();
    }

    this.isManagerInitialized = true;
    this.logger.info('ConnectorManager: Initialized successfully');
  }

  /**
   * Create a connector by type
   * @param type Connector type
   * @param config Connector configuration
   */
  async createConnector(type: string, config: any = {}): Promise<IBaseConnector> {
    if (!this.isManagerInitialized) {
      throw new Error('ConnectorManager: Manager not initialized');
    }

    if (this.connectors.size >= (this.config.maxConnectors || 10)) {
      throw new Error('ConnectorManager: Maximum number of connectors reached');
    }

    const connectorId = this.generateConnectorId(type);
    
    try {
      let connector: IBaseConnector;

      switch (type.toLowerCase()) {
        case ConnectorType.HTTP:
          connector = await this.createHttpConnector(config);
          break;
        case ConnectorType.REDIS:
          connector = await this.createRedisConnector(config);
          break;
        case ConnectorType.WEBSOCKET:
        case ConnectorType.WEBSOCKET_SHORT:
          connector = await this.createWebSocketConnector(config);
          break;
        default:
          throw new Error(`Unknown connector type: ${type}`);
      }

      // Register the connector
      this.registerConnector(connectorId, connector);

      this.logger.info(`ConnectorManager: Created ${type} connector with ID: ${connectorId}`);
      return connector;

    } catch (error) {
      this.stats.failedConnections++;
      this.logger.error(`ConnectorManager: Failed to create ${type} connector: ${error}`);
      throw error;
    }
  }

  /**
   * Get an existing connector by ID
   * @param connectorId Connector ID
   */
  getConnector(connectorId: string): IBaseConnector | null {
    const registration = this.connectors.get(connectorId);
    return registration ? registration.connector : null;
  }

  /**
   * Register a connector
   * @param connectorId Connector ID
   * @param connector Connector instance
   */
  registerConnector(connectorId: string, connector: IBaseConnector): void {
    const registration: IConnectorRegistrationInfo = {
      id: connectorId,
      type: connector.getConnectorType(),
      connector,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
      usageCount: 0
    };

    this.connectors.set(connectorId, registration);
    this.updateStats();

    if (this.config.enableLogging) {
      this.logger.debug(`ConnectorManager: Registered connector ${connectorId} of type ${registration.type}`);
    }
  }

  /**
   * Unregister a connector
   * @param connectorId Connector ID
   */
  async unregisterConnector(connectorId: string): Promise<void> {
    const registration = this.connectors.get(connectorId);
    if (!registration) {
      return;
    }

    try {
      await registration.connector.close();
    } catch (error) {
      this.logger.warn(`ConnectorManager: Error closing connector ${connectorId}: ${error}`);
    }

    this.connectors.delete(connectorId);
    this.updateStats();

    if (this.config.enableLogging) {
      this.logger.debug(`ConnectorManager: Unregistered connector ${connectorId}`);
    }
  }

  /**
   * Get all registered connectors
   */
  getAllConnectors(): Map<string, IBaseConnector> {
    const result = new Map<string, IBaseConnector>();
    for (const [id, registration] of this.connectors) {
      result.set(id, registration.connector);
    }
    return result;
  }

  /**
   * Get connectors by type
   * @param type Connector type
   */
  getConnectorsByType(type: string): IBaseConnector[] {
    const result: IBaseConnector[] = [];
    for (const registration of this.connectors.values()) {
      if (registration.type.toLowerCase() === type.toLowerCase()) {
        result.push(registration.connector);
      }
    }
    return result;
  }

  /**
   * Close all connectors
   */
  async closeAllConnectors(): Promise<void> {
    const connectorIds = Array.from(this.connectors.keys());
    
    for (const connectorId of connectorIds) {
      await this.unregisterConnector(connectorId);
    }

    this.logger.info('ConnectorManager: Closed all connectors');
  }

  /**
   * Get manager statistics
   */
  getStats(): IConnectorManagerStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Check if manager is initialized
   */
  isInitialized(): boolean {
    return this.isManagerInitialized;
  }

  /**
   * Dispose the manager and all resources
   */
  async dispose(): Promise<void> {
    await this.closeAllConnectors();
    
    if (this.heartbeatService) {
      await this.heartbeatService.close();
    }

    if (this.registrationService) {
      await this.registrationService.close();
    }

    this.isManagerInitialized = false;
    this.logger.info('ConnectorManager: Disposed successfully');
  }

  /**
   * Create HTTP connector
   */
  private async createHttpConnector(config: any): Promise<IBaseConnector> {
    // Import HTTP services
    const { HttpRegistrationService } = await import('../services/http/http-registration-service.js');
    const { HttpHeartbeatService } = await import('../services/http/http-heartbeat-service.js');
    const { HttpTestService } = await import('../services/http/http-test-service.js');
    const { HttpConnector } = await import('../http/http-connector.js');

    // Create service instances
    const httpRegistrationService = new HttpRegistrationService(this.logger, config, this.registrationService);
    const httpHeartbeatService = new HttpHeartbeatService(this.logger, config, this.heartbeatService);
    const httpTestService = new HttpTestService(this.logger, config);

    // Initialize services
    await httpRegistrationService.initialize(config.hubUrl || '', config.nodeSecretKey || '');
    await httpHeartbeatService.initialize(config.hubUrl || '', config.nodeSecretKey || '');
    await httpTestService.initialize(config.hubUrl || '', config.nodeSecretKey || '');

    // Create connector with dependencies
    const connector = new HttpConnector(
      this.logger,
      config,
      httpRegistrationService,
      httpHeartbeatService,
      httpTestService
    );

    await connector.initialize(config);
    return connector;
  }

  /**
   * Create Redis connector
   */
  private async createRedisConnector(config: any): Promise<IBaseConnector> {
    // Import Redis connector
    const { RedisConnector } = await import('../redis/redis-connector.js');

    // Create connector instance
    const connector = new RedisConnector();

    // Initialize connector
    await connector.initialize(config);
    return connector;
  }

  /**
   * Create WebSocket connector
   */
  private async createWebSocketConnector(config: any): Promise<IBaseConnector> {
    // Import WebSocket services
    const { WebSocketConnectionManager } = await import('../services/websocket/websocket-connection-manager.js');
    const { WebSocketMessageHandler } = await import('../services/websocket/websocket-message-handler.js');
    const { WebSocketConnector } = await import('../websocket/websocket-connector.js');

    // Create service instances
    const connectionManager = new WebSocketConnectionManager(this.logger, config);
    const messageHandler = new WebSocketMessageHandler(this.logger, config);

    // Initialize services
    await connectionManager.initialize(config);
    await messageHandler.initialize(config);

    // Create connector with dependencies
    const connector = new WebSocketConnector(
      this.logger,
      config,
      connectionManager,
      messageHandler,
      this.registrationService,
      this.heartbeatService
    );

    await connector.initialize(config);
    return connector;
  }

  /**
   * Generate unique connector ID
   */
  private generateConnectorId(type: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${type.toLowerCase()}-${timestamp}-${random}`;
  }

  /**
   * Update statistics
   */
  private updateStats(): void {
    this.stats.totalConnectors = this.connectors.size;
    this.stats.connectorsByType = {};
    this.stats.activeConnections = 0;
    this.stats.connectorStats = {};

    for (const [id, registration] of this.connectors) {
      const type = registration.type;
      this.stats.connectorsByType[type] = (this.stats.connectorsByType[type] || 0) + 1;
      
      if (registration.connector.isConnected()) {
        this.stats.activeConnections++;
      }

      this.stats.connectorStats[id] = registration.connector.getStats();
    }

    // Update memory usage
    const memUsage = process.memoryUsage();
    this.stats.memoryUsage = {
      used: memUsage.heapUsed,
      total: memUsage.heapTotal,
      percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    };
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval(): void {
    setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval || 300000);
  }

  /**
   * Perform cleanup of unused connectors
   */
  private performCleanup(): void {
    const now = Date.now();
    const maxAge = 3600000; // 1 hour
    const connectorsToRemove: string[] = [];

    for (const [id, registration] of this.connectors) {
      const age = now - new Date(registration.lastUsed).getTime();
      
      if (age > maxAge && !registration.connector.isConnected()) {
        connectorsToRemove.push(id);
      }
    }

    for (const id of connectorsToRemove) {
      this.unregisterConnector(id);
    }

    if (connectorsToRemove.length > 0 && this.config.enableLogging) {
      this.logger.debug(`ConnectorManager: Cleaned up ${connectorsToRemove.length} unused connectors`);
    }
  }
}
