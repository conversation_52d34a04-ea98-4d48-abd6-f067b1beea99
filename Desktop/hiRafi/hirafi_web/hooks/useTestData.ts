"use client"

import { useState, useCallback, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import type {
  DataSource,
  DataSet,
  CustomEnvironment,
  DataVariable,
  VariableType,
  EnvironmentType,
  CreateDataSetRequest,
  UpdateDataSetRequest,
  CreateDataSourceRequest,
  UpdateDataSourceRequest,
  CreateDataEnvironmentRequest,
  UpdateDataEnvironmentRequest,
  DataSetQueryOptions,
  DataSourceQueryOptions,
  DataEnvironmentQueryOptions,
} from "../types/test-data"

import { encryptValue, decryptValue, isEncrypted } from "@/lib/encryption"
import { dataSetApi, dataSourceApi, dataEnvironmentApi } from "@/lib/api/test-data"

export function useTestData() {
  const { toast } = useToast()

  // State
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [dataSets, setDataSets] = useState<DataSet[]>([])
  const [customEnvironments, setCustomEnvironments] = useState<CustomEnvironment[]>([])
  const [visibleSecrets, setVisibleSecrets] = useState<Record<string, boolean>>({})

  // Loading states
  const [isLoadingDataSets, setIsLoadingDataSets] = useState(false)
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(false)
  const [isLoadingEnvironments, setIsLoadingEnvironments] = useState(false)
  const [isUpdatingEnvironment, setIsUpdatingEnvironment] = useState<string | null>(null)

  // Error states
  const [dataSetError, setDataSetError] = useState<string | null>(null)
  const [dataSourceError, setDataSourceError] = useState<string | null>(null)
  const [environmentError, setEnvironmentError] = useState<string | null>(null)

  // Data loading functions
  const loadDataSets = useCallback(async (options?: DataSetQueryOptions) => {
    try {
      setIsLoadingDataSets(true)
      setDataSetError(null)
      const result = await dataSetApi.getAll(options)
      if (result.success) {
        // Extract data sets from response
        const dataSets = result.dataSets || result.data?.dataSets || []
        setDataSets(dataSets)
      } else {
        throw new Error(result.error || 'Failed to load data sets')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load data sets'
      setDataSetError(errorMessage)
      toast({
        title: "Error loading data sets",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoadingDataSets(false)
    }
  }, [toast])

  const loadDataSources = useCallback(async (options?: DataSourceQueryOptions) => {
    try {
      setIsLoadingDataSources(true)
      setDataSourceError(null)
      const result = await dataSourceApi.getAll(options)
      if (result.success) {
        // Extract data sources from response
        const dataSources = result.data?.dataSources || []
        setDataSources(dataSources)
      } else {
        throw new Error(result.error || 'Failed to load data sources')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load data sources'
      setDataSourceError(errorMessage)
      toast({
        title: "Error loading data sources",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoadingDataSources(false)
    }
  }, [toast])

  const loadEnvironments = useCallback(async (options?: DataEnvironmentQueryOptions) => {
    try {
      setIsLoadingEnvironments(true)
      setEnvironmentError(null)
      const result = await dataEnvironmentApi.getAll(options)

      if (result.success) {
        // Extract data environments from response
        const environments = result.data?.dataEnvironments || []
        setCustomEnvironments(environments)
      } else {
        throw new Error(result.error || 'Failed to load environments')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load environments'
      setEnvironmentError(errorMessage)
      toast({
        title: "Error loading environments",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoadingEnvironments(false)
    }
  }, [toast])

  // Load data on mount
  useEffect(() => {
    loadDataSets()
    loadDataSources()
    loadEnvironments()
  }, [loadDataSets, loadDataSources, loadEnvironments])

  // Environment management
  const handleAddEnvironment = useCallback(
    async (newEnvironment: Partial<CustomEnvironment>) => {
      if (!newEnvironment.name?.trim()) {
        toast({
          title: "Environment name required",
          description: "Please enter a name for the environment.",
          variant: "destructive",
        })
        return false
      }

      try {
        const request: CreateDataEnvironmentRequest = {
          name: newEnvironment.name,
          description: newEnvironment.description || "",
          color: newEnvironment.color || "blue",
          type: "custom",
          isActive: newEnvironment.isActive ?? true,
        }

        const result = await dataEnvironmentApi.create(request)
        if (result.success) {
          const newEnvironment = result.data?.data || result.data || (result as any).dataEnvironment
          setCustomEnvironments((prev) => [...prev, newEnvironment])

          toast({
            title: "Environment created",
            description: `"${newEnvironment.name}" environment has been created successfully.`,
          })
        } else {
          throw new Error(result.error || 'Failed to create environment')
        }

        return true
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create environment'
        toast({
          title: "Error creating environment",
          description: errorMessage,
          variant: "destructive",
        })
        return false
      }
    },
    [toast],
  )

  const handleEditEnvironment = useCallback(
    async (selectedEnvironment: CustomEnvironment) => {
      if (!selectedEnvironment.name?.trim()) {
        toast({
          title: "Environment name required",
          description: "Please enter a name for the environment.",
          variant: "destructive",
        })
        return false
      }

      // Prevent multiple simultaneous updates
      if (isUpdatingEnvironment === selectedEnvironment.id) return false

      try {
        setIsUpdatingEnvironment(selectedEnvironment.id)

        const request: UpdateDataEnvironmentRequest = {
          name: selectedEnvironment.name,
          description: selectedEnvironment.description,
          color: selectedEnvironment.color,
          type: selectedEnvironment.type,
          isActive: selectedEnvironment.isActive,
        }

        const result = await dataEnvironmentApi.update(selectedEnvironment.id, request)
        if (result.success) {
          const updatedEnvironment = result.data?.data || result.data || (result as any).dataEnvironment
          setCustomEnvironments((prev) =>
            prev.map((env) =>
              env.id === selectedEnvironment.id ? updatedEnvironment : env,
            ),
          )

          toast({
            title: "Environment updated",
            description: `"${updatedEnvironment.name}" environment has been updated successfully.`,
          })
        } else {
          throw new Error(result.error || 'Failed to update environment')
        }

        return true
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update environment'
        toast({
          title: "Error updating environment",
          description: errorMessage,
          variant: "destructive",
        })
        return false
      } finally {
        setIsUpdatingEnvironment(null)
      }
    },
    [isUpdatingEnvironment, toast],
  )

  const handleDeleteEnvironment = useCallback(
    async (id: string) => {
      const environment = customEnvironments.find((env) => env.id === id)
      if (environment?.type === "default") {
        toast({
          title: "Cannot delete default environment",
          description: "Default environments cannot be deleted.",
          variant: "destructive",
        })
        return
      }

      try {
        await dataEnvironmentApi.delete(id)
        setCustomEnvironments((prev) => prev.filter((env) => env.id !== id))

        toast({
          title: "Environment deleted",
          description: `"${environment?.name}" environment has been deleted.`,
        })
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete environment'
        toast({
          title: "Error deleting environment",
          description: errorMessage,
          variant: "destructive",
        })
      }
    },
    [customEnvironments, toast],
  )

  const handleToggleEnvironmentStatus = useCallback(async (id: string) => {
    const environment = customEnvironments.find((env) => env.id === id)
    if (!environment) return

    // Prevent multiple simultaneous updates
    if (isUpdatingEnvironment === id) return

    try {
      setIsUpdatingEnvironment(id)

      const request: UpdateDataEnvironmentRequest = {
        isActive: !environment.isActive,
      }

      const result = await dataEnvironmentApi.update(id, request)
      if (result.success) {
        const updatedEnvironment = result.data?.data || result.data || (result as any).dataEnvironment
        setCustomEnvironments((prev) =>
          prev.map((env) =>
            env.id === id ? updatedEnvironment : env,
          ),
        )

        toast({
          title: "Environment updated",
          description: `Environment "${updatedEnvironment.name}" has been ${updatedEnvironment.isActive ? 'activated' : 'deactivated'}.`,
        })
      } else {
        throw new Error(result.error || 'Failed to update environment status')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update environment status'
      toast({
        title: "Error updating environment",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsUpdatingEnvironment(null)
    }
  }, [customEnvironments, isUpdatingEnvironment, toast])

  // Data source management
  const handleAddDataSource = useCallback(async (dataSource: Partial<DataSource>) => {
    try {
      const request: CreateDataSourceRequest = {
        name: dataSource.name || "New Data Source",
        type: dataSource.type || "manual",
        description: dataSource.description || "",
        connectionString: dataSource.connectionString,
        filePath: dataSource.filePath,
        isActive: true,
      }

      const result = await dataSourceApi.create(request)
      if (result.success) {
        const newDataSource = result.data?.data || result.data || (result as any).dataSource
        setDataSources((prev) => [...prev, newDataSource])

        toast({
          title: "Data source created",
          description: `"${newDataSource.name}" data source has been created successfully.`,
        })
      } else {
        throw new Error(result.error || 'Failed to create data source')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create data source'
      toast({
        title: "Error creating data source",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }, [toast])

  const handleDeleteDataSource = useCallback(async (id: string) => {
    try {
      await dataSourceApi.delete(id)
      setDataSources((prev) => prev.filter((ds) => ds.id !== id))

      toast({
        title: "Data source deleted",
        description: "Data source has been deleted successfully.",
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete data source'
      toast({
        title: "Error deleting data source",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }, [toast])

  const toggleDataSourceStatus = useCallback(async (id: string) => {
    const dataSource = dataSources.find((ds) => ds.id === id)
    if (!dataSource) return

    try {
      const request: UpdateDataSourceRequest = {
        isActive: !dataSource.isActive,
      }

      const result = await dataSourceApi.update(id, request)
      if (result.success) {
        const updatedDataSource = result.data?.data || result.data || (result as any).dataSource
        setDataSources((prev) =>
          prev.map((ds) => ds.id === id ? updatedDataSource : ds),
        )
      } else {
        throw new Error(result.error || 'Failed to update data source status')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update data source status'
      toast({
        title: "Error updating data source",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }, [dataSources, toast])

  // Data set management
  const handleAddDataSet = useCallback(
    async (dataSet: Partial<DataSet>) => {
      try {
        const request: CreateDataSetRequest = {
          name: dataSet.name || "New Data Set",
          description: dataSet.description || "",
          variables: [],
          tags: dataSet.tags || [],
          environment: dataSet.environment || "development",
        }

        const result = await dataSetApi.create(request)
        if (result.success) {
          const newDataSet = result.data?.data || result.data || (result as any).dataSet
          setDataSets((prev) => [...prev, newDataSet])

          toast({
            title: "Data set created",
            description: `"${newDataSet.name}" data set has been created successfully.`,
          })
        } else {
          throw new Error(result.error || 'Failed to create data set')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create data set'
        toast({
          title: "Error creating data set",
          description: errorMessage,
          variant: "destructive",
        })
      }
    },
    [dataSources, toast],
  )

  const handleDeleteDataSet = useCallback(async (id: string) => {
    try {
      await dataSetApi.delete(id)
      setDataSets((prev) => prev.filter((ds) => ds.id !== id))

      toast({
        title: "Data set deleted",
        description: "Data set has been deleted successfully.",
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete data set'
      toast({
        title: "Error deleting data set",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }, [toast])

  // Variable management
  const handleAddVariable = useCallback(async (dataSetId: string, newVariable: Partial<DataVariable>) => {
    if (!newVariable.name || !newVariable.value) return

    const dataSet = dataSets.find(ds => ds.id === dataSetId)
    if (!dataSet) return

    try {
      const newVar: DataVariable = {
        id: `var-${Date.now()}`,
        name: newVariable.name as string,
        value: newVariable.type === "secret" ? encryptValue(newVariable.value as string) : (newVariable.value as string),
        type: newVariable.type as VariableType,
        description: newVariable.description || "",
      }

      const updatedVariables = [...dataSet.variables, newVar]
      const request: UpdateDataSetRequest = {
        variables: updatedVariables,
      }

      const result = await dataSetApi.update(dataSetId, request)
      if (result.success) {
        const updatedDataSet = result.data?.data || result.data || (result as any).dataSet
        setDataSets((prev) =>
          prev.map((ds) => ds.id === dataSetId ? updatedDataSet : ds),
        )

        toast({
          title: "Variable added",
          description: `Variable "${newVar.name}" has been added successfully.`,
        })
      } else {
        throw new Error(result.error || 'Failed to add variable')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add variable'
      toast({
        title: "Error adding variable",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }, [dataSets, toast])

  const handleDeleteVariable = useCallback(async (dataSetId: string, variableId: string) => {
    const dataSet = dataSets.find(ds => ds.id === dataSetId)
    if (!dataSet) return

    try {
      const updatedVariables = dataSet.variables.filter((v: DataVariable) => v.id !== variableId)
      const request: UpdateDataSetRequest = {
        variables: updatedVariables,
      }

      const result = await dataSetApi.update(dataSetId, request)
      if (result.success) {
        const updatedDataSet = result.data
        setDataSets((prev) =>
          prev.map((ds) => ds.id === dataSetId ? updatedDataSet : ds),
        )

        toast({
          title: "Variable deleted",
          description: "Variable has been deleted successfully.",
        })
      } else {
        throw new Error(result.error || 'Failed to delete variable')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete variable'
      toast({
        title: "Error deleting variable",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }, [dataSets, toast])

  const toggleEncryption = useCallback(async (dataSetId: string, variableId: string) => {
    const dataSet = dataSets.find(ds => ds.id === dataSetId)
    if (!dataSet) return

    try {
      const updatedVariables = dataSet.variables.map((v: DataVariable) => {
        if (v.id === variableId) {
          if (v.type === "secret" && isEncrypted(v.value)) {
            return {
              ...v,
              type: "string" as VariableType,
              value: decryptValue(v.value),
            }
          } else {
            return {
              ...v,
              type: "secret" as VariableType,
              value: encryptValue(v.value),
            }
          }
        }
        return v
      })

      const request: UpdateDataSetRequest = {
        variables: updatedVariables,
      }

      const result = await dataSetApi.update(dataSetId, request)
      if (result.success) {
        const updatedDataSet = result.data
        setDataSets((prev) =>
          prev.map((ds) => ds.id === dataSetId ? updatedDataSet : ds),
        )
      } else {
        throw new Error(result.error || 'Failed to toggle encryption')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to toggle encryption'
      toast({
        title: "Error updating variable",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }, [dataSets, toast])

  const toggleSecretVisibility = useCallback((variableId: string) => {
    setVisibleSecrets((prev) => ({
      ...prev,
      [variableId]: !prev[variableId],
    }))
  }, [])

  const copyVariableValue = useCallback(
    (variable: DataVariable) => {
      let valueToCopy = variable.value

      if (variable.type === "secret" && isEncrypted(valueToCopy)) {
        valueToCopy = decryptValue(valueToCopy)
      }

      navigator.clipboard.writeText(valueToCopy).then(() => {
        toast({
          title: "Value copied",
          description: `"${variable.name}" variable value has been copied to clipboard.`,
        })
      })
    },
    [toast],
  )

  // Filtering functions
  const getFilteredDataSets = useCallback(
    (searchQuery: string, environmentFilter: EnvironmentType) => {
      return dataSets.filter(
        (ds) =>
          (environmentFilter === "all" || ds.environment === environmentFilter) &&
          ((ds.name?.toLowerCase() || "").includes(searchQuery.toLowerCase()) ||
            (ds.description?.toLowerCase() || "").includes(searchQuery.toLowerCase()) ||
            ds.tags?.some((tag: string) => tag?.toLowerCase()?.includes(searchQuery.toLowerCase()))),
      )
    },
    [dataSets],
  )

  const getFilteredDataSources = useCallback(
    (searchQuery: string) => {
      return dataSources.filter(
        (ds) =>
          (ds.name?.toLowerCase() || "").includes(searchQuery.toLowerCase()) ||
          (ds.description?.toLowerCase() || "").includes(searchQuery.toLowerCase()) ||
          (ds.type?.toLowerCase() || "").includes(searchQuery.toLowerCase()),
      )
    },
    [dataSources],
  )

  const getFilteredEnvironments = useCallback(
    (searchQuery: string) => {
      return customEnvironments.filter(
        (env) =>
          (env.name?.toLowerCase() || "").includes(searchQuery.toLowerCase()) ||
          (env.description?.toLowerCase() || "").includes(searchQuery.toLowerCase()),
      )
    },
    [customEnvironments],
  )

  return {
    // State
    dataSources,
    dataSets,
    customEnvironments,
    visibleSecrets,

    // Loading states
    isLoadingDataSets,
    isLoadingDataSources,
    isLoadingEnvironments,
    isUpdatingEnvironment,

    // Error states
    dataSetError,
    dataSourceError,
    environmentError,

    // Data loading functions
    loadDataSets,
    loadDataSources,
    loadEnvironments,

    // Environment functions
    handleAddEnvironment,
    handleEditEnvironment,
    handleDeleteEnvironment,
    handleToggleEnvironmentStatus,

    // Data source functions
    handleAddDataSource,
    handleDeleteDataSource,
    toggleDataSourceStatus,

    // Data set functions
    handleAddDataSet,
    handleDeleteDataSet,

    // Variable functions
    handleAddVariable,
    handleDeleteVariable,
    toggleEncryption,
    toggleSecretVisibility,
    copyVariableValue,

    // Filtering functions
    getFilteredDataSets,
    getFilteredDataSources,
    getFilteredEnvironments,
  }
}
