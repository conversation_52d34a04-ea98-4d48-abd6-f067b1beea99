/**
 * Scenario Service
 * Test senaryoları için servis modülü
 */

import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import {
  db,
  scenariosCollection,
  scenarioFoldersCollection,
  runsCollection,
  ensureMongoDBConnection,
  isMongoDBInitialized,
  runStatusCollection
} from './dbConnection.js';
import { operationQueue } from '../../utils/operationQueue.js';
import { ObjectId } from 'mongodb';

// Senaryo tipi
export interface Scenario {
  id: string;
  name: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  userId?: string;
  nodeConfig?: {
    url: string;
    enabled: boolean;
    [key: string]: any;
  };
  tags?: string[];
  repositoryUrl?: string;
  branch?: string;
  status?: 'active' | 'inactive' | 'archived';
  lastRunDate?: string;
  lastRunStatus?: 'passed' | 'failed' | 'skipped' | 'unknown';
  testDataSetId?: string;    // Dataset ID for variable resolution
  metadata?: {               // Dataset variables metadata
    variables?: any[];
  };
  [key: string]: any;
}

// Klasör tipi
export interface ScenarioFolder {
  id: string;
  name: string;
  description?: string;
  color?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  teamId?: string;
  companyId?: string;
}

/**
 * Yeni bir klasör oluşturur - asenkron kuyruk kullanarak
 */
export async function createFolder(folderData: {
  name: string;
  userId: string;
  description?: string;
  color?: string;
  teamId?: string;
  companyId?: string;
}): Promise<{
  success: boolean;
  message: string;
  folderId?: string;
  folder?: ScenarioFolder;
}> {
  try {
    // Validate required fields
    if (!folderData.name) {
      return { success: false, message: "Folder name is required" };
    }

    if (!folderData.userId) {
      return { success: false, message: "User ID is required" };
    }

    // Generate a unique ID for the folder
    const folderId = uuidv4();

    // Queue the database operation
    return await operationQueue.enqueue(
      `create-folder-${folderId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure scenarioFolders collection is initialized
        if (!scenarioFoldersCollection) {
          return { success: false, message: "Scenario folders collection not initialized" };
        }

        const now = new Date();

        // Renk belirtilmemişse varsayılan bir renk seç
        const colors = ["indigo", "emerald", "amber", "rose", "sky", "purple", "blue", "orange", "teal"];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];

        // Prepare folder object
        const folder: ScenarioFolder = {
          id: folderId,
          name: folderData.name,
          color: folderData.color || randomColor,
          createdAt: now,
          updatedAt: now,
          userId: folderData.userId,
          teamId: folderData.teamId,
          companyId: folderData.companyId
        };

        // Insert the folder
        await scenarioFoldersCollection.insertOne(folder);

        return {
          success: true,
          message: "Folder created successfully",
          folderId,
          folder
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error creating folder:`, error);
    return {
      success: false,
      message: `Error creating folder: ${error.message}`
    };
  }
}

/**
 * Kullanıcıya ait klasörleri getirir
 */
export async function getUserFolders(userId: string, options: {
  limit?: number;
  skip?: number;
  teamId?: string;
  companyId?: string;
} = {}): Promise<{
  success: boolean;
  folders?: ScenarioFolder[];
  count?: number;
  message?: string;
}> {
  try {
    if (!userId) {
      return { success: false, message: "User ID is required" };
    }

    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure scenarioFolders collection is initialized
    if (!scenarioFoldersCollection) {
      return { success: false, message: "Scenario folders collection not initialized" };
    }

    const { limit = 50, skip = 0, teamId, companyId } = options;

    // Build query for user's folders
    const query: any = {};

    // Eğer teamId ve companyId varsa, bunları kullan
    if (teamId && companyId) {
      query.teamId = teamId;
      query.companyId = companyId;
    } else {
      // Geriye dönük uyumluluk için userId ile filtreleme
      query.userId = userId;
    }

    // Count total matching folders for this user/team
    const total = await scenarioFoldersCollection.countDocuments(query);

    // Get user folders with pagination
    const folders = await scenarioFoldersCollection.find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray() as unknown as ScenarioFolder[];

    return {
      success: true,
      folders,
      count: total
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching folders for user ${userId}:`, error);
    return {
      success: false,
      message: `Error fetching user folders: ${error.message}`
    };
  }
}

/**
 * Kullanıcıya ait senaryoları getirir
 */
export async function getUserScenarios(userId: string, options: {
  limit?: number;
  skip?: number;
  status?: string | string[];
  tags?: string[];
  excludeSteps?: boolean;
  teamId?: string;
  companyId?: string;
  folderId?: string;
  scenarioIds?: string[];
} = {}): Promise<{
  success: boolean;
  scenarios?: any[];
  count?: number;
  message?: string;
}> {
  try {
    if (!userId) {
      return { success: false, message: "User ID is required" };
    }

    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure scenarios collection is initialized
    if (!scenariosCollection) {
      return { success: false, message: "Scenarios collection not initialized" };
    }

    const { limit = 10, skip = 0, status, tags, excludeSteps = false, teamId, companyId, folderId, scenarioIds } = options;

    // Build query for team's scenarios
    const query: any = {};

    // Eğer teamId ve companyId varsa, bunları kullan
    if (teamId && companyId) {
      query.teamId = teamId;
      query.companyId = companyId;
    } else {
      // Geriye dönük uyumluluk için userId ile filtreleme
      query.userId = userId;
    }

    if (tags && tags.length > 0) {
      // Use case-insensitive regex for tag matching
      const tagRegexes = tags.map(tag => new RegExp(`^${tag}$`, 'i'));
      query.tags = { $in: tagRegexes };
    }

    // Add status filter if specified
    if (status) {
      if (Array.isArray(status)) {
        // If status is an array, use $in operator
        if (status.length > 0) {
          query.status = { $in: status };
          logger.info(`[MONGODB] Adding status filter with $in operator: ${JSON.stringify(status)}`);
        }
      } else {
        // If status is a string, use direct equality
        query.status = status;
      }
    }

    // Handle folder filtering
    if (folderId) {
      // If "uncategorized" folder is selected, find scenarios with null, empty, or "uncategorized" folderId
      if (folderId === "uncategorized") {
        query.$or = [
          { folderId: { $exists: false } },
          { folderId: null },
          { folderId: "" },
          { folderId: "uncategorized" },
          { folderId: "1" } // Legacy support
        ];
      } else {
        // Normal folder filtering
        query.folderId = folderId;
      }
    }

    // Handle specific scenario IDs filtering
    if (scenarioIds && Array.isArray(scenarioIds) && scenarioIds.length > 0) {
      query.id = { $in: scenarioIds };
    }

    // Count total matching scenarios for this user
    const total = await scenariosCollection.countDocuments(query);

    // Get user scenarios with pagination
    let findQuery = scenariosCollection.find(query);

    // Exclude steps from results if requested
    if (excludeSteps) {
      findQuery = findQuery.project({ steps: 0 });
    }

    const scenarios = await findQuery
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    return {
      success: true,
      scenarios,
      count: total
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching scenarios for user ${userId}:`, error);
    return {
      success: false,
      message: `Error fetching user scenarios: ${error.message}`
    };
  }
}

/**
 * Get all scenarios with optional filtering
 * @param options Filtering options
 * @returns Scenarios matching the criteria
 */
export async function getAllScenarios(options: {
  limit?: number;
  status?: 'active' | 'inactive' | 'archived' | string[];
  tags?: string[];
  excludeSteps?: boolean;
  userId: string; // userId is now required
}): Promise<{
  success: boolean;
  scenarios?: Scenario[];
  count?: number;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !scenariosCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!options.userId) {
      return { success: false, message: 'User ID is required' };
    }

    const query: any = {
      userId: options.userId // Only get scenarios for the specified user
    };

    if (options.status) {
      if (Array.isArray(options.status)) {
        // If status is an array, use $in operator
        if (options.status.length > 0) {
          query.status = { $in: options.status };
          logger.info(`[MONGODB] Adding status filter with $in operator: ${JSON.stringify(options.status)}`);
        }
      } else {
        // If status is a string, use direct equality
        query.status = options.status;
        logger.info(`[MONGODB] Adding status filter: "${options.status}"`);
      }
    }

    if (options.tags && options.tags.length > 0) {
      // Use case-insensitive regex for tag matching
      const tagRegexes = options.tags.map(tag => new RegExp(`^${tag}$`, 'i'));
      query.tags = { $in: tagRegexes };
      logger.info(`[MONGODB] Adding tags filter with case-insensitive regex for ${options.tags.length} tags in getAllScenarios`);
    }

    const projection: any = {};
    if (options.excludeSteps) {
      projection.steps = 0;
    }

    const scenarios = await scenariosCollection
      .find(query)
      .project(projection)
      .limit(options.limit || 100)
      .toArray();

    const count = await scenariosCollection.countDocuments(query);

    return {
      success: true,
      scenarios: scenarios as Scenario[],
      count
    };
  } catch (error: any) {
    logger.error(`Error in getAllScenarios: ${error.message}`);
    return { success: false, message: error.message };
  }
}

/**
 * ID'ye göre senaryo getirir - GÜÇLÜ CONSISTENCY (Race Condition Çözümü)
 */
export async function getScenarioById(scenarioId: string): Promise<{
  success: boolean;
  scenario?: any;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure scenarios collection is initialized
    if (!scenariosCollection) {
      return { success: false, message: "Scenarios collection not initialized" };
    }

    // Find the scenario with strong consistency to prevent race conditions
    const scenario = await scenariosCollection.findOne(
      { id: scenarioId },
      { 
        // CRITICAL: Primary read preference to ensure we get the latest data
        readPreference: 'primary',
        // Force read from primary to avoid stale data from secondaries
        readConcern: { level: 'majority' }
      }
    );

    if (!scenario) {
      logger.info(`[MONGODB] Scenario with ID ${scenarioId} not found`);
      return {
        success: false,
        message: `Scenario with ID ${scenarioId} not found`
      };
    }

    logger.debug(`[MONGODB] Retrieved scenario ${scenarioId} with strong consistency - updated: ${scenario.updatedAt}`);

    return {
      success: true,
      scenario
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching scenario with ID ${scenarioId}:`, error);
    return {
      success: false,
      message: `Error fetching scenario: ${error.message}`
    };
  }
}

/**
 * Yeni senaryo oluşturur - SENKRON İŞLEM (Race Condition Çözümü)
 * SORUN: Operation queue asenkron çalıştığı için kullanıcı oluşturma sonrası hemen run sayfasına geçerse
 * senaryo görünmüyor. Bu critical operation için direkt senkron yapıyoruz.
 */
export async function createScenario(scenarioData: any): Promise<{
  success: boolean;
  message: string;
  scenarioId?: string;
}> {
  try {
    // Validate required fields
    if (!scenarioData.name) {
      return { success: false, message: "Scenario name is required" };
    }

    if (!scenarioData.userId) {
      return { success: false, message: "User ID is required" };
    }

    // Generate a unique ID for the scenario
    const scenarioId = uuidv4();

    // SENKRON DATABASE OPERATION - Race condition çözümü için
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure scenarios collection is initialized
    if (!scenariosCollection) {
      return { success: false, message: "Scenarios collection not initialized" };
    }

    logger.info(`[MONGODB] Starting SYNCHRONOUS scenario creation for ID: ${scenarioId}`);

    const now = new Date();

    // Gereksiz alanları kaldır
    if (scenarioData.environmentSettings) {
      delete scenarioData.environmentSettings;
    }
    if (scenarioData.reportSettings) {
      delete scenarioData.reportSettings;
    }

    // Prepare scenario object
    const scenario = {
      id: scenarioId,
      createdAt: now,
      updatedAt: now,
      status: scenarioData.status || 'active',
      tags: scenarioData.tags || [],
      ...scenarioData
    };

    // Direct synchronous database insert
    await scenariosCollection.insertOne(scenario);

    logger.info(`[MONGODB] SYNCHRONOUS scenario creation completed for ID: ${scenarioId}`);

    return {
      success: true,
      message: "Scenario created successfully",
      scenarioId
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error creating scenario:`, error);
    return {
      success: false,
      message: `Error creating scenario: ${error.message}`
    };
  }
}

/**
 * Senaryo günceller - SENKRON İŞLEM (Race Condition Çözümü)
 * SORUN: Operation queue asenkron çalıştığı için kullanıcı güncelleme sonrası hemen test koşarsa
 * eski içerik çalışıyor. Bu critical operation için direkt senkron yapıyoruz.
 */
export async function updateScenario(
  scenarioId: string,
  updateData: any
): Promise<{
  success: boolean;
  message: string;
  scenario?: any;
}> {
  try {
    // Validate scenario ID
    if (!scenarioId) {
      return { success: false, message: "Scenario ID is required" };
    }

    // Process updateData before updating
    // Prevent updating the ID and creation date
    if (updateData.id) {
      delete updateData.id;
    }
    if (updateData.createdAt) {
      delete updateData.createdAt;
    }

    // Gereksiz alanları kaldır
    if (updateData.environmentSettings) {
      delete updateData.environmentSettings;
    }
    if (updateData.reportSettings) {
      delete updateData.reportSettings;
    }
    if (updateData.environment) {
      delete updateData.environment;
    }
    if (updateData.reporting) {
      delete updateData.reporting;
    }

    // SENKRON DATABASE OPERATION - Race condition çözümü için
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure scenarios collection is initialized
    if (!scenariosCollection) {
      return { success: false, message: "Scenarios collection not initialized" };
    }

    logger.info(`[MONGODB] Starting SYNCHRONOUS scenario update for ID: ${scenarioId}`);

    // Ensure consistent field names in steps
    if (updateData.steps && Array.isArray(updateData.steps)) {
      updateData.steps = updateData.steps.map((step: any) => {
        // Create a normalized step object
        const normalizedStep: any = {
          id: step.id,
          type: step.type
        };

        // Ensure name field is present (might be in description or name)
        normalizedStep.name = step.name || step.description || '';

        // Handle different step type fields based on optimized structure
        if (step.type === 'goto') {
          // For goto steps, use url field
          normalizedStep.url = step.url || step.value || '';
        } else if (step.type === 'sleep') {
          // For sleep steps, use duration field
          normalizedStep.duration = typeof step.duration !== 'undefined' ?
            step.duration :
            (typeof step.value !== 'undefined' ?
              (typeof step.value === 'string' ? parseInt(step.value) || 1 : step.value) :
              1);
        } else if (step.type === 'aiInput') {
          // For aiInput steps, preserve value (input text) and target fields
          if (typeof step.value !== 'undefined') {
            normalizedStep.value = step.value;
          }
          if (typeof step.target !== 'undefined') {
            normalizedStep.target = step.target;
          }
        } else {
          // For other AI steps, use prompt field
          if (typeof step.prompt !== 'undefined') {
            normalizedStep.prompt = step.prompt;
          } else if (typeof step.value !== 'undefined') {
            // Fallback for legacy data
            normalizedStep.prompt = step.value;
          }
        }

        // Copy any other fields
        Object.keys(step).forEach(key => {
          if (!['id', 'type', 'name', 'description', 'value', 'url', 'duration'].includes(key)) {
            normalizedStep[key] = step[key];
          }
        });

        return normalizedStep;
      });
    }

    // Update the scenario with retry logic for write conflicts
    let result;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        result = await scenariosCollection.findOneAndUpdate(
          { id: scenarioId },
          {
            $set: {
              ...updateData,
              updatedAt: new Date()
            }
          },
          { 
            returnDocument: 'after',
            // CRITICAL: Ensure strong consistency to prevent race conditions
            writeConcern: { w: 'majority', j: true }
          }
        );
        break; // Success, exit retry loop
      } catch (error: any) {
        retryCount++;
        if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
          // Duplicate key error or write conflict, or max retries reached
          logger.error(`Scenario update failed for scenarioId ${scenarioId} after ${retryCount} attempts: ${error.message}`);
          throw error;
        }
        // Wait before retry with exponential backoff
        const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
        logger.warn(`Retrying scenario update for scenarioId ${scenarioId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!result) {
      return {
        success: false,
        message: `Scenario with ID ${scenarioId} not found`
      };
    }

    logger.info(`[MONGODB] SYNCHRONOUS scenario update completed successfully for ID: ${scenarioId}`);

    return {
      success: true,
      message: "Scenario updated successfully",
      scenario: result
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error updating scenario with ID ${scenarioId}:`, error);
    return {
      success: false,
      message: `Error updating scenario: ${error.message}`
    };
  }
}

/**
 * Senaryo siler - asenkron kuyruk kullanarak
 * Aktif run'larda kullanılan senaryoları silmeyi engeller
 */
export async function deleteScenario(scenarioId: string): Promise<{
  success: boolean;
  message: string;
  inUseByRuns?: Array<{id: string, name: string}>;
}> {
  try {
    // Validate scenarioId
    if (!scenarioId) {
      return { success: false, message: "Scenario ID is required" };
    }

    // Queue the database operation
    return await operationQueue.enqueue(
      `delete-scenario-${scenarioId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure scenarios collection is initialized
        if (!scenariosCollection) {
          return { success: false, message: "Scenarios collection not initialized" };
        }

        // Ensure runs collection is initialized
        if (!runsCollection) {
          return { success: false, message: "Runs collection not initialized" };
        }



        // Check if the scenario is used in any active runs
        // Active runs are those with status: 'created', 'queued', or 'running'
        const activeRuns = await runsCollection.find({
          scenarioIds: scenarioId,
          status: { $in: ['created', 'queued', 'running'] }
        }, {
          projection: { id: 1, name: 1 }
        }).toArray();

        // If the scenario is used in active runs, prevent deletion
        if (activeRuns.length > 0) {


          // Create a properly typed array of runs
          const inUseByRuns = activeRuns.map(run => {
            const typedRun = run as unknown as { id: string, name?: string };
            return {
              id: typedRun.id,
              name: typedRun.name || 'Unnamed Run'
            };
          });

          return {
            success: false,
            message: "Cannot delete scenario that is used in active runs",
            inUseByRuns
          };
        }



        // Delete the scenario
        const result = await scenariosCollection.deleteOne({ id: scenarioId });

        if (result.deletedCount === 0) {

          return {
            success: false,
            message: `Scenario with ID ${scenarioId} not found`
          };
        }



        return {
          success: true,
          message: "Scenario deleted successfully"
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error deleting scenario ${scenarioId}:`, error);
    return {
      success: false,
      message: `Error deleting scenario: ${error.message}`
    };
  }
}

/**
 * Toplu senaryo silme - asenkron kuyruk kullanarak
 * Aktif run'larda kullanılan senaryoları silmeyi engeller
 */
export async function bulkDeleteScenarios(scenarioIds: string[]): Promise<{
  success: boolean;
  message: string;
  deletedCount?: number;
  inUseScenarios?: Array<{
    scenarioId: string;
    inUseByRuns: Array<{id: string, name: string}>
  }>;
}> {
  try {
    // Validate scenarioIds
    if (!scenarioIds || !Array.isArray(scenarioIds) || scenarioIds.length === 0) {
      return { success: false, message: "No scenario IDs provided" };
    }

    // Queue the database operation
    return await operationQueue.enqueue(
      `bulk-delete-scenarios-${Date.now()}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure scenarios collection is initialized
        if (!scenariosCollection) {
          return { success: false, message: "Scenarios collection not initialized" };
        }

        // Ensure runs collection is initialized
        if (!runsCollection) {
          return { success: false, message: "Runs collection not initialized" };
        }



        // Check if any of the scenarios are used in active runs
        const activeRuns = await runsCollection.find({
          scenarioIds: { $in: scenarioIds },
          status: { $in: ['created', 'queued', 'running'] }
        }, {
          projection: { id: 1, name: 1, scenarioIds: 1 }
        }).toArray();

        // If there are active runs, identify which scenarios are in use
        if (activeRuns.length > 0) {
          // Create a map of scenarioId -> runs that use it
          const scenarioUsageMap = new Map<string, Array<{id: string, name: string}>>();

          // Populate the map
          for (const run of activeRuns) {
            const typedRun = run as unknown as { id: string, name?: string, scenarioIds: string[] };
            for (const scenarioId of typedRun.scenarioIds) {
              if (scenarioIds.includes(scenarioId)) {
                if (!scenarioUsageMap.has(scenarioId)) {
                  scenarioUsageMap.set(scenarioId, []);
                }
                scenarioUsageMap.get(scenarioId)!.push({ id: typedRun.id, name: typedRun.name || 'Unnamed Run' });
              }
            }
          }

          // If any scenarios are in use, only delete the ones that aren't
          if (scenarioUsageMap.size > 0) {
            const inUseScenarioIds = Array.from(scenarioUsageMap.keys());
            const availableScenarioIds = scenarioIds.filter(id => !inUseScenarioIds.includes(id));



            // If there are no available scenarios to delete, return without deleting anything
            if (availableScenarioIds.length === 0) {
              return {
                success: false,
                message: "All selected scenarios are in use by active runs and cannot be deleted",
                deletedCount: 0,
                inUseScenarios: Array.from(scenarioUsageMap.entries()).map(([scenarioId, runs]) => ({
                  scenarioId,
                  inUseByRuns: runs
                }))
              };
            }

            // Delete only the available scenarios
            const result = await scenariosCollection.deleteMany({ id: { $in: availableScenarioIds } });



            return {
              success: true,
              message: `${result.deletedCount} scenarios deleted successfully. ${inUseScenarioIds.length} scenarios were skipped because they are in use by active runs.`,
              deletedCount: result.deletedCount,
              inUseScenarios: Array.from(scenarioUsageMap.entries()).map(([scenarioId, runs]) => ({
                scenarioId,
                inUseByRuns: runs
              }))
            };
          }
        }

        // If no scenarios are in use, delete all of them

        // Delete the scenarios
        const result = await scenariosCollection.deleteMany({ id: { $in: scenarioIds } });



        return {
          success: true,
          message: `${result.deletedCount} scenarios deleted successfully`,
          deletedCount: result.deletedCount
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error bulk deleting scenarios:`, error);
    return {
      success: false,
      message: `Error bulk deleting scenarios: ${error.message}`
    };
  }
}



/**
 * Senaryo çalışma durumunu günceller - asenkron kuyruk kullanarak
 */
export async function updateScenarioRunStatus(
  scenarioId: string,
  status: 'running' | 'idle' | 'completed' | 'failed' | 'queued' | 'stopped'
): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Validate scenarioId and status
    if (!scenarioId) {
      return { success: false, message: "Scenario ID is required" };
    }

    if (!status) {
      return { success: false, message: "Status is required" };
    }

    // Queue the database operation
    return await operationQueue.enqueue(
      `update-scenario-status-${scenarioId}-${Date.now()}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure scenarios collection is initialized
        if (!scenariosCollection) {
          return { success: false, message: "Scenarios collection not initialized" };
        }

        logger.info(`[MONGODB] Updating run status for scenario ${scenarioId} to ${status}`);

        // Update the scenario status with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await scenariosCollection.updateOne(
              { id: scenarioId },
              {
                $set: {
                  runStatus: status,
                  updatedAt: new Date()
                }
              }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`Scenario status update failed for scenarioId ${scenarioId} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`Retrying scenario status update for scenarioId ${scenarioId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!result || result.matchedCount === 0) {
          logger.info(`[MONGODB] Scenario with ID ${scenarioId} not found`);
          return {
            success: false,
            message: `Scenario with ID ${scenarioId} not found`
          };
        }

        logger.info(`[MONGODB] Run status for scenario ${scenarioId} updated to ${status}`);

        return {
          success: true,
          message: `Scenario run status updated to ${status}`
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error updating run status for scenario ${scenarioId}:`, error);
    return {
      success: false,
      message: `Error updating scenario run status: ${error.message}`
    };
  }
}

// updateScenarioLastRun fonksiyonu kaldırıldı - senaryoların lastRun bilgilerini güncellemiyoruz

/**
 * Klasör adını günceller - asenkron kuyruk kullanarak
 */
export async function renameFolder(
  folderId: string,
  newName: string,
  userId: string
): Promise<{
  success: boolean;
  message: string;
  folder?: ScenarioFolder;
}> {
  try {
    // Validate required fields
    if (!folderId) {
      return { success: false, message: "Folder ID is required" };
    }

    if (!newName) {
      return { success: false, message: "New folder name is required" };
    }

    if (!userId) {
      return { success: false, message: "User ID is required" };
    }

    // Queue the database operation
    return await operationQueue.enqueue(
      `rename-folder-${folderId}-${Date.now()}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure folder collection is initialized
        if (!scenarioFoldersCollection) {
          return { success: false, message: "Scenario folders collection not initialized" };
        }

        logger.info(`[MONGODB] Renaming folder ${folderId} to "${newName}" for user ${userId}`);

        // First check if the folder exists and belongs to the user
        const folder = await scenarioFoldersCollection.findOne({ id: folderId });

        if (!folder) {
          logger.info(`[MONGODB] Folder with ID ${folderId} not found`);
          return {
            success: false,
            message: `Folder with ID ${folderId} not found`
          };
        }

        if (folder.userId !== userId) {
          logger.info(`[MONGODB] User ${userId} does not own folder ${folderId}`);
          return {
            success: false,
            message: `You don't have permission to update this folder`
          };
        }

        // Update the folder name
        const result = await scenarioFoldersCollection.findOneAndUpdate(
          { id: folderId },
          {
            $set: {
              name: newName,
              updatedAt: new Date()
            }
          },
          { returnDocument: 'after' }
        );

        // Debug için sonucu logla
        logger.info(`[MONGODB] Rename result: ${JSON.stringify(result, null, 2)}`);

        // Kontrolü düzelt - MongoDB farklı yapılar döndürebilir sürüme göre
        if (!result) {
          return {
            success: false,
            message: `Failed to update folder name`
          };
        }

        logger.info(`[MONGODB] Folder ${folderId} renamed successfully to "${newName}"`);

        return {
          success: true,
          message: "Folder renamed successfully",
          folder: result as unknown as ScenarioFolder
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error renaming folder ${folderId}:`, error);
    return {
      success: false,
      message: `Error renaming folder: ${error.message}`
    };
  }
}

/**
 * Klasörü siler ve ilişkili senaryoların folder referansını kaldırır - asenkron kuyruk kullanarak
 */
export async function deleteFolder(
  folderId: string,
  userId: string
): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Validate required fields
    if (!folderId) {
      return { success: false, message: "Folder ID is required" };
    }

    if (!userId) {
      return { success: false, message: "User ID is required" };
    }

    // Queue the database operation
    return await operationQueue.enqueue(
      `delete-folder-${folderId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure collections are initialized
        if (!scenarioFoldersCollection) {
          return { success: false, message: "Scenario folders collection not initialized" };
        }

        if (!scenariosCollection) {
          return { success: false, message: "Scenarios collection not initialized" };
        }

        logger.info(`[MONGODB] Deleting folder ${folderId} for user ${userId}`);

        // First check if the folder exists and belongs to the user
        const folder = await scenarioFoldersCollection.findOne({ id: folderId });

        if (!folder) {
          logger.info(`[MONGODB] Folder with ID ${folderId} not found`);
          return {
            success: false,
            message: `Folder with ID ${folderId} not found`
          };
        }

        if (folder.userId !== userId) {
          logger.info(`[MONGODB] User ${userId} does not own folder ${folderId}`);
          return {
            success: false,
            message: `You don't have permission to delete this folder`
          };
        }

        // Delete the folder
        const deleteResult = await scenarioFoldersCollection.deleteOne({ id: folderId });

        if (deleteResult.deletedCount === 0) {
          return {
            success: false,
            message: `Failed to delete folder`
          };
        }

        // Update all scenarios that were in this folder to remove the folder reference
        await scenariosCollection.updateMany(
          { folderId: folderId },
          { $unset: { folderId: "" } }
        );

        logger.info(`[MONGODB] Folder ${folderId} deleted successfully and associated scenarios updated`);

        return {
          success: true,
          message: "Folder deleted successfully and associated scenarios updated"
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error deleting folder ${folderId}:`, error);
    return {
      success: false,
      message: `Error deleting folder: ${error.message}`
    };
  }
}

/**
 * Senaryoları ve klasörleri birlikte getiren aggregation metodu
 * CompanyId ve TeamId'ye göre filtreleme yapar ve klasörleri senaryolarla eşleştirir
 */
export async function getScenariosWithFolders(options: {
  teamId: string;
  companyId: string;
  userId: string;
  limit?: number;
  skip?: number;
  status?: string | string[];
  search?: string;
  excludeSteps?: boolean;
  tag?: string;
  testType?: string;
  startDate?: string;
  endDate?: string;
  testManagementStatus?: string;
}): Promise<{
  success: boolean;
  data?: {
    folders: ScenarioFolder[];
    scenarios: any[];
  };
  message?: string;
}> {
  try {
    // Validate required fields
    if (!options.teamId || !options.companyId || !options.userId) {
      return {
        success: false,
        message: "TeamId, CompanyId and UserId are required"
      };
    }

    await ensureMongoDBConnection();
    if (!db || !scenariosCollection || !scenarioFoldersCollection) {
      return {
        success: false,
        message: "MongoDB not initialized or collections not available"
      };
    }

    const {
      teamId,
      companyId,
      // userId is required for validation but not used in the query
      limit = 1000,
      skip = 0,
      status,
      search,
      excludeSteps = true,
      tag,
      testType,
      startDate,
      endDate,
      testManagementStatus
    } = options;

    logger.info(`[MONGODB] Fetching scenarios and folders with aggregation for teamId: ${teamId}, companyId: ${companyId}`);
    logger.info(`[MONGODB] Filter options: ${JSON.stringify({
      status, search, tag, testType, startDate, endDate, testManagementStatus, limit, skip
    })}`);

    // 1. Önce klasörleri getir
    const folderQuery = {
      teamId,
      companyId
    };

    const folders = await scenarioFoldersCollection
      .find(folderQuery)
      .sort({ updatedAt: -1 })
      .toArray() as unknown as ScenarioFolder[];



    // 2. Senaryolar için sorgu oluştur
    const scenarioQuery: any = {
      teamId,
      companyId
    };

    // Status filtresi ekle - varsayılan olarak inactive senaryoları hariç tut
    if (status && status !== 'all') {
      if (Array.isArray(status)) {
        // If status is an array, use $in operator
        if (status.length > 0) {
          scenarioQuery.status = { $in: status };
        }
      } else {
        // If status is a string, use direct equality
        scenarioQuery.status = status;
      }
    } else {
      // Varsayılan olarak inactive senaryoları hariç tut
      scenarioQuery.status = { $ne: 'inactive' };
    }

    // Arama filtresi ekle
    if (search && search.trim() !== '') {
      const searchRegex = new RegExp(search, 'i');
      scenarioQuery.$or = [
        { name: searchRegex },
        { description: searchRegex },
        { tags: searchRegex }
      ];
    }

    // Tag filtresi ekle - case-insensitive regex kullanarak
    if (tag && tag.trim() !== '') {
      const tagValue = tag.trim();
      // Use $elemMatch with case-insensitive regex to match whole tag strings
      scenarioQuery.tags = { $elemMatch: { $regex: new RegExp(`^${tagValue}$`, 'i') } };
    }

    // Test tipi filtresi ekle
    if (testType && testType.trim() !== '') {
      scenarioQuery.testType = testType.trim();
    }

    // Date range filtresi ekle
    if (startDate || endDate) {
      const dateFilter: any = {};

      if (startDate) {
        try {
          const startDateObj = new Date(startDate);
          if (!isNaN(startDateObj.getTime())) {
            dateFilter.$gte = startDateObj;
          }
        } catch (error) {
          logger.warn(`[MONGODB] Invalid startDate format: ${startDate}`);
        }
      }

      if (endDate) {
        try {
          const endDateObj = new Date(endDate);
          if (!isNaN(endDateObj.getTime())) {
            // Set to end of day for inclusive filtering
            endDateObj.setHours(23, 59, 59, 999);
            dateFilter.$lte = endDateObj;
          }
        } catch (error) {
          logger.warn(`[MONGODB] Invalid endDate format: ${endDate}`);
        }
      }

      if (Object.keys(dateFilter).length > 0) {
        // Apply date filter to both createdAt and updatedAt fields
        scenarioQuery.$and = scenarioQuery.$and || [];
        scenarioQuery.$and.push({
          $or: [
            { createdAt: dateFilter },
            { updatedAt: dateFilter }
          ]
        });
      }
    }

    // Test Management Integration status filtresi ekle
    if (testManagementStatus && testManagementStatus.trim() !== '') {
      const status = testManagementStatus.trim();
      if (status === 'active') {
        // Scenarios with TestRail or Zephyr Scale integration
        scenarioQuery.$or = scenarioQuery.$or || [];
        scenarioQuery.$or.push(
          { 'testrailIntegration.caseIds': { $exists: true, $ne: [], $not: { $size: 0 } } },
          { 'zephyrIntegration.caseIds': { $exists: true, $ne: [], $not: { $size: 0 } } }
        );
      } else if (status === 'inactive') {
        // Scenarios without any test management integration
        scenarioQuery.$and = scenarioQuery.$and || [];
        scenarioQuery.$and.push({
          $and: [
            {
              $or: [
                { 'testrailIntegration.caseIds': { $exists: false } },
                { 'testrailIntegration.caseIds': { $size: 0 } },
                { 'testrailIntegration.caseIds': [] }
              ]
            },
            {
              $or: [
                { 'zephyrIntegration.caseIds': { $exists: false } },
                { 'zephyrIntegration.caseIds': { $size: 0 } },
                { 'zephyrIntegration.caseIds': [] }
              ]
            }
          ]
        });
      }
    }

    // Projection oluştur - steps alanını hariç tut
    const projection: any = {};
    if (excludeSteps) {
      projection.steps = 0;
    }

    // 3. Tüm senaryoları getir
    const allScenarios = await scenariosCollection
      .find(scenarioQuery)
      .project(projection)
      .sort({ updatedAt: -1 })
      .toArray();



    // 4. Normalize all scenarios to have proper folder assignment
    // Set uncategorized scenarios to have folderId as "uncategorized"
    const normalizedScenarios = allScenarios.map(scenario => {
      // If scenario has no folder or invalid folder, assign it to uncategorized
      if (!scenario.folderId ||
          scenario.folderId === "" ||
          scenario.folderId === "uncategorized" ||
          scenario.folderId === "1" ||
          scenario.folderId === "none") {
        return {
          ...scenario,
          folderId: "uncategorized"
        };
      }

      // Keep existing folder assignment for categorized scenarios
      return scenario;
    });

    return {
      success: true,
      data: {
        folders,
        scenarios: normalizedScenarios
      }
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error in getScenariosWithFolders:`, error);
    return {
      success: false,
      message: `Error fetching scenarios and folders: ${error.message}`
    };
  }
}

/**
 * Toplu senaryo kopyalama (bulk duplicate) fonksiyonu
 * Birden fazla senaryoyu aynı anda kopyalar
 */
export async function bulkDuplicateScenarios(
  scenarioIds: string[],
  teamId?: string,
  companyId?: string,
  options?: {
    namePrefix?: string;
    folderId?: string;
    customNamePattern?: string;
    addNumbers?: boolean;
  }
): Promise<{
  success: boolean;
  message: string;
  duplicatedCount?: number;
  duplicatedScenarios?: Array<{
    originalId: string;
    newId: string;
    name: string;
  }>;
}> {
  try {
    // Validate scenarioIds
    if (!scenarioIds || !Array.isArray(scenarioIds) || scenarioIds.length === 0) {
      return { success: false, message: "No scenario IDs provided" };
    }

    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure scenarios collection is initialized
    if (!scenariosCollection) {
      return { success: false, message: "Scenarios collection not initialized" };
    }

    const duplicatedScenarios: Array<{
      originalId: string;
      newId: string;
      name: string;
    }> = [];

    let successCount = 0;
    let errorCount = 0;

    // Process each scenario
    for (const scenarioId of scenarioIds) {
      try {
        // Get the original scenario
        const originalScenario = await scenariosCollection.findOne({ id: scenarioId });

        if (!originalScenario) {
          logger.warn(`Scenario with ID ${scenarioId} not found during bulk duplicate`);
          errorCount++;
          continue;
        }

        // Generate a new ID for the duplicate
        const newScenarioId = uuidv4();
        const now = new Date();

        // Determine the new scenario name
        let newScenarioName: string;
        if (options?.customNamePattern) {
          // Use custom naming pattern
          newScenarioName = options.customNamePattern.replace('{original}', originalScenario.title || originalScenario.name || "Senaryo");
          if (options.addNumbers) {
            newScenarioName += ` ${successCount + 1}`;
          }
        } else {
          const prefix = options?.namePrefix || 'Copy';
          const originalName = originalScenario.title || originalScenario.name || "Senaryo";
          newScenarioName = options?.addNumbers
            ? `${prefix} ${originalName} ${successCount + 1}`
            : `${prefix} ${originalName}`;
        }

        // Create a copy of the original scenario with a new ID
        const duplicatedScenario: Record<string, any> = {
          ...originalScenario,
          id: newScenarioId,
          title: newScenarioName,
          name: newScenarioName,
          createdAt: now,
          updatedAt: now,
          // Clear last run data
          lastRunDate: null,
          lastRunStatus: null,
          lastRun: null
        };

        // Set folder if specified in options
        if (options?.folderId !== undefined) {
          duplicatedScenario.folderId = options.folderId === 'uncategorized' ? null : options.folderId;
        }

        // Set team and company if provided
        if (teamId && companyId) {
          duplicatedScenario.teamId = teamId;
          duplicatedScenario.companyId = companyId;
        }

        // Clean up unnecessary fields
        ['lastRun', 'environmentSettings', 'reportSettings', 'environment', 'reporting', '_id'].forEach(field => {
          if (field in duplicatedScenario) {
            delete duplicatedScenario[field];
          }
        });

        // Insert the duplicated scenario
        await scenariosCollection.insertOne(duplicatedScenario);

        duplicatedScenarios.push({
          originalId: scenarioId,
          newId: newScenarioId,
          name: newScenarioName
        });

        successCount++;
        logger.info(`Successfully duplicated scenario ${scenarioId} to ${newScenarioId}`);

      } catch (error: any) {
        logger.error(`Error duplicating scenario ${scenarioId}:`, error);
        errorCount++;
      }
    }

    if (successCount === 0) {
      return {
        success: false,
        message: "No scenarios were duplicated successfully",
        duplicatedCount: 0
      };
    }

    const message = errorCount > 0
      ? `${successCount} scenarios duplicated successfully, ${errorCount} failed`
      : `${successCount} scenarios duplicated successfully`;

    return {
      success: true,
      message,
      duplicatedCount: successCount,
      duplicatedScenarios
    };

  } catch (error: any) {
    logger.error(`[MONGODB] Error in bulk duplicate scenarios:`, error);
    return {
      success: false,
      message: `Error duplicating scenarios: ${error.message}`
    };
  }
}

/**
 * Senaryo kopyalama (duplicate) fonksiyonu - asenkron kuyruk kullanarak
 * Belirtilen senaryo ID'sine göre senaryoyu çoğaltır
 */
export async function duplicateScenario(
  scenarioId: string,
  teamId?: string,
  companyId?: string,
  options?: {
    namePrefix?: string;
    folderId?: string;
    newName?: string;
  }
): Promise<{
  success: boolean;
  message: string;
  newScenarioId?: string;
  scenario?: any;
}> {
  try {
    // Validate scenarioId
    if (!scenarioId) {
      return { success: false, message: "Scenario ID is required" };
    }

    // Generate a new ID for the duplicate
    const newScenarioId = uuidv4();

    // Queue the database operation
    return await operationQueue.enqueue(
      `duplicate-scenario-${scenarioId}-${newScenarioId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure scenarios collection is initialized
        if (!scenariosCollection) {
          return { success: false, message: "Scenarios collection not initialized" };
        }



        // First, get the original scenario
        const originalScenario = await scenariosCollection.findOne({ id: scenarioId });

        if (!originalScenario) {
          return {
            success: false,
            message: `Scenario with ID ${scenarioId} not found`
          };
        }

        const now = new Date();

        // Determine the new scenario name
        let newScenarioName: string;
        if (options?.newName) {
          newScenarioName = options.newName;
        } else {
          const prefix = options?.namePrefix || '';
          const originalName = originalScenario.title || originalScenario.name || "Senaryo";
          newScenarioName = prefix ? `${prefix} ${originalName}` : `${originalName} (Copy)`;
        }

        // Create a copy of the original scenario with a new ID
        const duplicatedScenario: Record<string, any> = {
          ...originalScenario,
          id: newScenarioId,
          title: newScenarioName,
          name: newScenarioName, // Ensure both title and name are updated
          createdAt: now,
          updatedAt: now,
          // lastRun alanlarını tamamen kaldır
          lastRunDate: null,
          lastRunStatus: null,
          lastRun: null
        };

        // Set folder if specified in options
        if (options?.folderId !== undefined) {
          duplicatedScenario.folderId = options.folderId === 'uncategorized' ? null : options.folderId;
        }

        // Eğer teamId ve companyId parametreleri verilmişse, bunları kullan
        if (teamId && companyId) {
          duplicatedScenario.teamId = teamId;
          duplicatedScenario.companyId = companyId;
        }

        // lastRun ile ilgili tüm alanları temizle
        if ('lastRun' in duplicatedScenario) {
          delete duplicatedScenario.lastRun;
        }

        // Gereksiz alanları kaldır
        if ('environmentSettings' in duplicatedScenario) {
          delete duplicatedScenario.environmentSettings;
        }
        if ('reportSettings' in duplicatedScenario) {
          delete duplicatedScenario.reportSettings;
        }
        if ('environment' in duplicatedScenario) {
          delete duplicatedScenario.environment;
        }
        if ('reporting' in duplicatedScenario) {
          delete duplicatedScenario.reporting;
        }

        // Remove _id field as MongoDB will generate a new one
        if ('_id' in duplicatedScenario) {
          delete duplicatedScenario._id;
        }

        // Insert the duplicated scenario
        await scenariosCollection.insertOne(duplicatedScenario);



        return {
          success: true,
          message: "Scenario duplicated successfully",
          newScenarioId,
          scenario: duplicatedScenario
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error duplicating scenario ${scenarioId}:`, error);
    return {
      success: false,
      message: `Error duplicating scenario: ${error.message}`
    };
  }
}

/**
 * Run ID'ye göre senaryoları getirir
 * @param runId Run ID
 * @returns Senaryolar ve başarı durumu
 */
export async function getScenariosByRunId(runId: string): Promise<{
  success: boolean;
  scenarios?: any[];
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db || !runStatusCollection) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure scenarios collection is initialized
    if (!scenariosCollection) {
      return { success: false, message: "Scenarios collection not initialized" };
    }



    // Get run status collection
    // Find the run status document
    const runStatus = await runStatusCollection.findOne({ runId });

    if (!runStatus) {
      logger.warn(`[MONGODB] Run status for run ${runId} not found`);
      return {
        success: false,
        message: `Run status for run ${runId} not found`
      };
    }

    // Extract scenario statuses from the run status
    const scenarioStatuses = runStatus.scenarios || {};

    // Create an array of scenario objects with their status
    const scenarios = Object.entries(scenarioStatuses).map(([scenarioId, status]) => ({
      id: scenarioId,
      status
    }));



    return {
      success: true,
      scenarios
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching scenarios for run ${runId}:`, error);
    return {
      success: false,
      message: `Error fetching scenarios for run: ${error.message}`
    };
  }
}