/**
 * Heartbeat Service Interface
 * Defines the contract for heartbeat services
 */

import { IConnectorService } from './connector-service.interface.js';

/**
 * Heartbeat service interface
 */
export interface IHeartbeatService extends IConnectorService {
  /**
   * Start heartbeat for a specific connector
   * @param connectorId Connector identifier
   * @param heartbeatFunction Function to call for heartbeat
   * @param interval Heartbeat interval in milliseconds
   */
  startHeartbeat(
    connectorId: string,
    heartbeatFunction: () => Promise<boolean>,
    interval?: number
  ): void;

  /**
   * Stop heartbeat for a specific connector
   * @param connectorId Connector identifier
   */
  stopHeartbeat(connectorId: string): void;

  /**
   * Check if heartbeat is active for a connector
   * @param connectorId Connector identifier
   */
  isHeartbeatActive(connectorId: string): boolean;

  /**
   * Get heartbeat interval for a connector
   * @param connectorId Connector identifier
   */
  getHeartbeatInterval(connectorId: string): number | null;

  /**
   * Prepare heartbeat data
   * @param nodeId Node ID
   * @param status Current node status
   * @param additionalData Additional data to include
   */
  prepareHeartbeatData(
    nodeId: string,
    status: string,
    additionalData?: Record<string, any>
  ): Record<string, any>;

  /**
   * Stop all heartbeats
   */
  stopAllHeartbeats(): void;

  /**
   * Get active heartbeat connectors
   */
  getActiveHeartbeats(): string[];
}

/**
 * Heartbeat configuration interface
 */
export interface IHeartbeatConfig {
  defaultInterval?: number;
  maxRetries?: number;
  retryDelay?: number;
  enableLogging?: boolean;
}

/**
 * Heartbeat data interface
 */
export interface IHeartbeatData {
  nodeId: string;
  status: string;
  timestamp: string;
  uptime: number;
  capabilities?: string[];
  currentTestId?: string;
  [key: string]: any;
}

/**
 * Heartbeat statistics interface
 */
export interface IHeartbeatStats {
  activeHeartbeats: number;
  totalHeartbeatsSent: number;
  successfulHeartbeats: number;
  failedHeartbeats: number;
  averageResponseTime: number;
  lastHeartbeatTime?: string;
  connectorStats: Record<string, {
    interval: number;
    lastSent: string;
    successCount: number;
    failureCount: number;
  }>;
}
