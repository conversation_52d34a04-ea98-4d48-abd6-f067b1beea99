/**
 * Goto Step Handler
 * Handles navigation steps in web tests (consolidated from web-specific handler)
 */

import { Page } from 'rebrowser-puppeteer';
import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';

/**
 * Goto Step Handler
 * Handles navigation to URLs in web tests
 */
export class GotoStepHandler implements IStepHandler {
  /**
   * Execute a goto step
   * @param page Puppeteer page (for web platform)
   * @param step Step to execute
   * @param _agent Unused agent parameter
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(
    page: Page,
    step: ScenarioStep,
    _agent: any,
    logs: string[]
  ): Promise<{ success: boolean; error: string | null; data?: any }> {
    try {
      // Extract URL from step - prioritize url field, fallback to value for backward compatibility, then name if it's a URL
      const url = step.url || step.value || (step.name && step.name.startsWith('http') ? step.name : null);

      if (!url) {
        return { success: false, error: 'Missing URL for navigate step. Please provide a valid URL in the url field.' };
      }

      logs.push(`🌐 Navigating to: "${url}"`);
      logger.info(`GotoStepHandler: Navigating to ${url}`);

      // Check page state before navigation
      if (page.isClosed()) {
        throw new Error('Page is closed - cannot navigate');
      }

      // Navigate to URL with timeout and fallback waitUntil strategy
      try {
        await page.goto(url, { 
          waitUntil: 'networkidle2', // Wait for network to be mostly idle (better for screenshots)
          timeout: 45000 // Increased timeout for network idle
        });
      } catch (error: any) {
        // If networkidle2 fails, try with domcontentloaded
        if (error.message.includes('timeout') || error.message.includes('Navigation')) {
          logger.warn(`GotoStepHandler: First navigation attempt with networkidle2 failed, trying with domcontentloaded: ${error.message}`);
          await page.goto(url, { 
            waitUntil: 'domcontentloaded',
            timeout: 30000 // Standard timeout for fallback
          });
        } else {
          throw error;
        }
      }

      // Verify navigation was successful
      const currentUrl = page.url();
      logs.push(`✅ Navigation completed - Current URL: ${currentUrl}`);
      logger.info(`GotoStepHandler: Navigation to ${url} completed successfully, current URL: ${currentUrl}`);

      return { 
        success: true, 
        error: null,
        data: {
          targetUrl: url,
          actualUrl: currentUrl,
          navigationTime: Date.now()
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error during navigation';
      logs.push(`❌ Navigation failed: ${errorMessage}`);
      logger.error(`GotoStepHandler: Navigation failed: ${errorMessage}`);
      
      // Provide more context for common errors
      if (errorMessage.includes('timeout')) {
        logs.push(`⏱️ Navigation timed out - page may be loading slowly`);
      } else if (errorMessage.includes('net::ERR_')) {
        logs.push(`🌐 Network error - check URL accessibility`);
      }
      
      return { success: false, error: `Navigation failed: ${errorMessage}` };
    }
  }

  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step, false otherwise
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'goto';
  }

  /**
   * Get the name of this step handler
   * @returns The name of the step handler
   */
  getName(): string {
    return 'goto';
  }
}
