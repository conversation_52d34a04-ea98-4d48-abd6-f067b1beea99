"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Loader2, Info, Check, X, Plus, Trash, AlertTriangle, Edit, Eye, EyeOff } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useAuth } from "@/lib/api/auth"
import { settingsApi } from "@/lib/api"
import { ConfirmDialog } from "@/components/team/confirm-dialog"

// AI Model tipi
interface AIModel {
  id: string;
  name: string;
  api: string;
  apiKey: string;
  isActive: boolean;
  supportsImageProcessing: boolean;
  createdAt: string; // API'den string olarak gelecek
  updatedAt: string; // API'den string olarak gelecek
}

interface AIModelSettings {
  preferredModel: string;
}

interface AIModelSettingsProps {
  aiSettings: AIModelSettings;
  onUpdateSettings: (settings: Partial<AIModelSettings>) => void;
}

export function AIModelSettings({ aiSettings, onUpdateSettings }: AIModelSettingsProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [models, setModels] = useState<AIModel[]>([])
  const [formOpen, setFormOpen] = useState(false)
  const [editFormOpen, setEditFormOpen] = useState(false)
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [modelToDelete, setModelToDelete] = useState<string | null>(null)
  const [modelToEdit, setModelToEdit] = useState<AIModel | null>(null)
  const [showApiKeys, setShowApiKeys] = useState<{[key: string]: boolean}>({})
  const { user } = useAuth()
  const isCompanyOwner = user?.accountType === 'company_owner'
  const [newModel, setNewModel] = useState<Omit<AIModel, "id" | "createdAt" | "updatedAt">>({
    name: "",
    api: "",
    apiKey: "",
    isActive: true,
    supportsImageProcessing: true
  })
  const [editModel, setEditModel] = useState<Omit<AIModel, "id" | "createdAt" | "updatedAt">>({
    name: "",
    api: "",
    apiKey: "",
    isActive: true,
    supportsImageProcessing: true
  })
  const { toast } = useToast()

  // Modelleri API'den al
  useEffect(() => {
    fetchModels()
  }, [])

  // Modelleri getir
  const fetchModels = async () => {
    setIsLoading(true)
    try {
      const response = await settingsApi.getAIModels()

      if (response.success) {
        setModels(response.data?.models || [])
      } else {
        toast({
          title: "Hata",
          description: response.error || "Modeller alınırken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Failed to fetch AI models:", error)
      toast({
        title: "Hata",
        description: "Modeller alınırken bir hata oluştu.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }



  // Yeni model ekleme
  const handleAddModel = async () => {
    setIsLoading(true)
    try {
      // settingsApi.createAIModel fonksiyonunu kullan
      const response = await settingsApi.createAIModel({
        name: newModel.name,
        provider: "custom", // Varsayılan olarak "custom" kullan
        apiKey: newModel.apiKey,
        endpoint: newModel.api,
        isActive: newModel.isActive,
        parameters: {
          supportsImageProcessing: newModel.supportsImageProcessing
        }
      })

      if (response.success) {
        toast({
          title: "Model eklendi",
          description: "Yeni AI model başarıyla eklendi."
        })

        // Modelleri yeniden yükle
        await fetchModels()

        // Formu sıfırla ve kapat
        setNewModel({
          name: "",
          api: "",
          apiKey: "",
          isActive: true,
          supportsImageProcessing: true
        })
        setFormOpen(false)
      } else {
        toast({
          title: "Hata",
          description: response.error || "Model eklenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Failed to add AI model:", error)
      toast({
        title: "Hata",
        description: "Model eklenirken bir hata oluştu.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Model silme modal açma
  const openDeleteModal = (modelId: string) => {
    setModelToDelete(modelId)
    setDeleteConfirmOpen(true)
  }

  // Model silme
  const handleDeleteModel = async () => {
    if (!modelToDelete) return

    setIsLoading(true)
    try {
      // settingsApi.deleteAIModel fonksiyonunu kullan
      const response = await settingsApi.deleteAIModel(modelToDelete)

      if (response.success) {
        toast({
          title: "Model silindi",
          description: "AI model başarıyla silindi."
        })

        // Modelleri yeniden yükle
        await fetchModels()
      } else {
        toast({
          title: "Hata",
          description: response.error || "Model silinirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Failed to delete AI model:", error)
      toast({
        title: "Hata",
        description: "Model silinirken bir hata oluştu.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
      setDeleteConfirmOpen(false)
      setModelToDelete(null)
    }
  }

  // Model edit modal açma
  const openEditModal = (model: AIModel) => {
    setModelToEdit(model)
    setEditModel({
      name: model.name,
      api: model.api,
      apiKey: model.apiKey,
      isActive: model.isActive,
      supportsImageProcessing: model.supportsImageProcessing
    })
    setEditFormOpen(true)
  }

  // Model güncelleme
  const handleUpdateModel = async () => {
    if (!modelToEdit) return

    setIsLoading(true)
    try {
      const response = await settingsApi.updateAIModel(modelToEdit.id, {
        name: editModel.name,
        endpoint: editModel.api,
        apiKey: editModel.apiKey,
        isActive: editModel.isActive,
        parameters: {
          supportsImageProcessing: editModel.supportsImageProcessing
        }
      })

      if (response.success) {
        toast({
          title: "Model güncellendi",
          description: "AI model başarıyla güncellendi."
        })

        // Modelleri yeniden yükle
        await fetchModels()

        // Formu sıfırla ve kapat
        setEditFormOpen(false)
        setModelToEdit(null)
        setEditModel({
          name: "",
          api: "",
          apiKey: "",
          isActive: true,
          supportsImageProcessing: true
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Model güncellenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Failed to update AI model:", error)
      toast({
        title: "Hata",
        description: "Model güncellenirken bir hata oluştu.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // API Key görünürlüğünü değiştirme
  const toggleApiKeyVisibility = (modelId: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }))
  }

  // Model durumunu değiştirme
  const handleToggleModel = async (modelId: string, isActive: boolean) => {
    setIsLoading(true)
    try {
      // Aktifleştirme veya devre dışı bırakma için uygun fonksiyonu kullan
      const response = isActive
        ? await settingsApi.activateAIModel(modelId)
        : await settingsApi.deactivateAIModel(modelId);

      if (response.success) {
        toast({
          title: "Model güncellendi",
          description: `Model ${isActive ? 'aktif' : 'devre dışı'} duruma getirildi.`
        })

        // Modelleri yeniden yükle
        await fetchModels()
      } else {
        toast({
          title: "Hata",
          description: response.error || "Model güncellenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Failed to update AI model:", error)
      toast({
        title: "Hata",
        description: "Model güncellenirken bir hata oluştu.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>AI Model Ayarları</CardTitle>
            <CardDescription>AI modellerinizi ekleyin ve yönetin</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-6 bg-amber-50 border-amber-500">
              <Info className="h-5 w-5 text-amber-500" />
              <AlertTitle className="text-amber-700">Dikkat</AlertTitle>
              <AlertDescription className="text-amber-700">
                Eklediğiniz AI modellerin görüntü işleme (Image Processing) özelliğini desteklemesi gerekmektedir.
              </AlertDescription>
            </Alert>

            {isCompanyOwner && (
              <Alert className="mb-6 bg-blue-50 border-blue-500">
                <AlertTriangle className="h-5 w-5 text-blue-500" />
                <AlertTitle className="text-blue-700">Bilgi</AlertTitle>
                <AlertDescription className="text-blue-700">
                  Burada eklediğiniz AI modelleri tüm kullanıcılar kullanabilir.
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              {!isCompanyOwner && models.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p className="mb-4">Henüz eklenmiş AI modeliniz bulunmuyor.</p>
                  <p>AI modelleri sadece şirket sahibi (company owner) hesapları tarafından eklenebilir.</p>
                </div>
              ) : models.length === 0 && isCompanyOwner ? (
                <div className="text-center py-8 text-gray-500">
                  <p className="mb-4">Henüz eklenmiş AI modeliniz bulunmuyor.</p>
                  <Dialog open={formOpen} onOpenChange={setFormOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700">
                        <Plus className="h-4 w-4 mr-2" />
                        Model Ekle
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Yeni AI Model Ekle</DialogTitle>
                        <DialogDescription>
                          AI modelinizin bilgilerini girin. Model görüntü işleme özelliğini desteklemelidir.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <Alert className="mb-4 bg-amber-50 border-amber-500">
                          <Info className="h-5 w-5 text-amber-500" />
                          <AlertDescription className="text-amber-700">
                            Ekleyeceğiniz AI model görüntü işleme (Image Processing) özelliğini desteklemelidir.
                          </AlertDescription>
                        </Alert>
                        <div className="space-y-2">
                          <Label htmlFor="name">Model Adı</Label>
                          <Input
                            id="name"
                            value={newModel.name}
                            onChange={e => setNewModel({...newModel, name: e.target.value})}
                            placeholder="Örn: GPT-4o"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="api">API Endpoint</Label>
                          <Input
                            id="api"
                            value={newModel.api}
                            onChange={e => setNewModel({...newModel, api: e.target.value})}
                            placeholder="Örn: https://api.openai.com/v1/chat/completions"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="apiKey">API Key</Label>
                          <Input
                            id="apiKey"
                            type="password"
                            value={newModel.apiKey}
                            onChange={e => setNewModel({...newModel, apiKey: e.target.value})}
                            placeholder="API Key girin"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <DialogClose asChild>
                          <Button variant="outline">İptal</Button>
                        </DialogClose>
                        <Button
                          onClick={handleAddModel}
                          disabled={isLoading || !newModel.name || !newModel.api || !newModel.apiKey}
                          className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                        >
                          {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Plus className="h-4 w-4 mr-2" />}
                          Ekle
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Kayıtlı Modelleriniz</h3>
                    {isCompanyOwner && (
                      <Dialog open={formOpen} onOpenChange={setFormOpen}>
                        <DialogTrigger asChild>
                          <Button className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700">
                            <Plus className="h-4 w-4 mr-2" />
                            Model Ekle
                          </Button>
                        </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Yeni AI Model Ekle</DialogTitle>
                          <DialogDescription>
                            AI modelinizin bilgilerini girin. Model görüntü işleme özelliğini desteklemelidir.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                          <Alert className="mb-4 bg-amber-50 border-amber-500">
                            <Info className="h-5 w-5 text-amber-500" />
                            <AlertDescription className="text-amber-700">
                              Ekleyeceğiniz AI model görüntü işleme (Image Processing) özelliğini desteklemelidir.
                            </AlertDescription>
                          </Alert>
                          <div className="space-y-2">
                            <Label htmlFor="name">Model Adı</Label>
                            <Input
                              id="name"
                              value={newModel.name}
                              onChange={e => setNewModel({...newModel, name: e.target.value})}
                              placeholder="Örn: GPT-4o"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="api">API Endpoint</Label>
                            <Input
                              id="api"
                              value={newModel.api}
                              onChange={e => setNewModel({...newModel, api: e.target.value})}
                              placeholder="Örn: https://api.openai.com/v1/chat/completions"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="apiKey">API Key</Label>
                            <Input
                              id="apiKey"
                              type="password"
                              value={newModel.apiKey}
                              onChange={e => setNewModel({...newModel, apiKey: e.target.value})}
                              placeholder="API Key girin"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <DialogClose asChild>
                            <Button variant="outline">İptal</Button>
                          </DialogClose>
                          <Button
                            onClick={handleAddModel}
                            disabled={isLoading || !newModel.name || !newModel.api || !newModel.apiKey}
                            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                          >
                            {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Plus className="h-4 w-4 mr-2" />}
                            Ekle
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                    )}
                  </div>

                  <div className="space-y-4">
                    {models.map(model => (
                      <div key={model.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
              <div>
                            <h3 className="font-medium flex items-center gap-2">
                              {model.name}
                              {model.isActive ? (
                                <span className="flex items-center text-green-600 dark:text-green-400 text-xs">
                                  <Check className="h-3 w-3 mr-1" />
                                  Aktif
                                </span>
                              ) : (
                                <span className="flex items-center text-gray-400 dark:text-gray-600 text-xs">
                                  <X className="h-3 w-3 mr-1" />
                                  Devre dışı
                                </span>
                              )}
                              {model.supportsImageProcessing ? (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Info className="h-4 w-4 text-blue-500" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Bu model görüntü işlemeyi destekler</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              ) : (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Info className="h-4 w-4 text-amber-500" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Bu model görüntü işlemeyi desteklemiyor</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                            </h3>
                          </div>
                          <div className="flex items-center gap-2">
                            {isCompanyOwner ? (
                              <>
                                <Switch
                                  checked={model.isActive}
                                  onCheckedChange={(checked) => handleToggleModel(model.id, checked)}
                                  disabled={isLoading}
                                />
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => openEditModal(model)}
                                  disabled={isLoading}
                                  title="Modeli düzenle"
                                >
                                  <Edit className="h-4 w-4 text-blue-500" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => openDeleteModal(model.id)}
                                  disabled={isLoading}
                                  title="Modeli sil"
                                >
                                  <Trash className="h-4 w-4 text-red-500" />
                                </Button>
                              </>
                            ) : null}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm">
                            <span className="font-medium">API Endpoint:</span>{" "}
                            <span className="text-gray-600">{model.api}</span>
                          </div>
                          <div className="text-sm flex items-center">
                            <span className="font-medium">API Key:</span>{" "}
                            <span className="text-gray-600 ml-1">
                              {showApiKeys[model.id] ? model.apiKey : "•••••••••••••••••••••"}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleApiKeyVisibility(model.id)}
                              className="h-5 w-5 p-0 ml-1"
                              title={showApiKeys[model.id] ? "API Key'i gizle" : "API Key'i göster"}
                            >
                              {showApiKeys[model.id] ? (
                                <EyeOff className="h-3 w-3" />
                              ) : (
                                <Eye className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                        </div>
                    </div>
                  ))}
              </div>
                </>
              )}
            </div>
          </CardContent>
          <CardFooter className="border-t px-6 py-4">
            <div className="text-sm text-gray-500">
              Son güncelleme: {new Date().toLocaleDateString()}
            </div>
          </CardFooter>
        </Card>

        {/* Edit Model Dialog */}
        <Dialog open={editFormOpen} onOpenChange={setEditFormOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>AI Model Düzenle</DialogTitle>
              <DialogDescription>
                AI modelinizin bilgilerini güncelleyin.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="editName">Model Adı</Label>
                <Input
                  id="editName"
                  value={editModel.name}
                  onChange={e => setEditModel({...editModel, name: e.target.value})}
                  placeholder="Örn: GPT-4o"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="editApi">API Endpoint</Label>
                <Input
                  id="editApi"
                  value={editModel.api}
                  onChange={e => setEditModel({...editModel, api: e.target.value})}
                  placeholder="Örn: https://api.openai.com/v1/chat/completions"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="editApiKey">API Key</Label>
                <Input
                  id="editApiKey"
                  type="password"
                  value={editModel.apiKey}
                  onChange={e => setEditModel({...editModel, apiKey: e.target.value})}
                  placeholder="API Key girin"
                />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">İptal</Button>
              </DialogClose>
              <Button
                onClick={handleUpdateModel}
                disabled={isLoading || !editModel.name || !editModel.api || !editModel.apiKey}
                className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
              >
                {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Edit className="h-4 w-4 mr-2" />}
                Güncelle
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <ConfirmDialog
          open={deleteConfirmOpen}
          onOpenChange={setDeleteConfirmOpen}
          title="Modeli Sil"
          description="Bu AI modelini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."
          confirmLabel="Evet, Sil"
          cancelLabel="İptal"
          onConfirm={handleDeleteModel}
          variant="destructive"
        />
    </div>
  );
}

