/**
 * Ba<PERSON>t bir logger modülü
 * <PERSON>ha kapsamlı bir çözüm için winston gibi kütüphaneler kullanılabilir
 */

// Log seviyeleri
const LOG_LEVELS = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
}

// Geçerli log seviyesi (environment'e göre ayarlanabilir)
const CURRENT_LOG_LEVEL = process.env.NODE_ENV === 'production' 
  ? LOG_LEVELS.ERROR  // Prodüksiyonda sadece hataları logla 
  : LOG_LEVELS.DEBUG  // Geliştirme ortamında tüm logları göster

// Renk kodları
const COLORS = {
  [LOG_LEVELS.ERROR]: '\x1b[31m', // kırmızı
  [LOG_LEVELS.WARN]: '\x1b[33m',  // sarı
  [LOG_LEVELS.INFO]: '\x1b[36m',  // turkuaz
  [LOG_LEVELS.DEBUG]: '\x1b[90m', // gri
  RESET: '\x1b[0m'                // rengi sıfırla
}

// Log mesajını biçimlendir
function formatMessage(level: string, message: string): string {
  const timestamp = new Date().toISOString()
  return `[${timestamp}] [${level}] ${message}`
}

// Belirli bir seviyede log oluştur
function log(level: string, message: string, data?: any): void {
  // Seviye kontrolü yap
  const levels = Object.values(LOG_LEVELS)
  const currentLevelIndex = levels.indexOf(CURRENT_LOG_LEVEL)
  const messageLevelIndex = levels.indexOf(level)
  
  // Sadece mevcut seviyeye eşit veya daha yüksek önemdeki mesajları göster
  if (messageLevelIndex > currentLevelIndex) {
    return
  }
  
  const formattedMessage = formatMessage(level, message)
  
  // Tarayıcıda mı, Node.js'de mi çalışıyor kontrolü
  if (typeof window !== 'undefined') {
    // Tarayıcı
    switch(level) {
      case LOG_LEVELS.ERROR:
        console.error(formattedMessage, data || '')
        break
      case LOG_LEVELS.WARN:
        console.warn(formattedMessage, data || '')
        break
      case LOG_LEVELS.INFO:
        console.info(formattedMessage, data || '')
        break
      default:

    }
  } else {
    // Node.js
    const color = COLORS[level] || COLORS.RESET

  }
}

// API
export const logger = {
  error: (message: string, data?: any) => log(LOG_LEVELS.ERROR, message, data),
  warn: (message: string, data?: any) => log(LOG_LEVELS.WARN, message, data),
  info: (message: string, data?: any) => log(LOG_LEVELS.INFO, message, data),
  debug: (message: string, data?: any) => log(LOG_LEVELS.DEBUG, message, data)
} 
