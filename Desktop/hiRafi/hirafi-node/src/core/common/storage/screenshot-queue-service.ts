/**
 * Screenshot Queue Service
 * Migrated to core common storage with improved integration
 * Handles asynchronous uploading of screenshots to storage using the unified storage system
 * Uses a queue to manage uploads in the background
 */

import { logger } from '#utils/logger.js';
import { config } from '#config/index.js';
import { StorageManager } from './storage-manager.js';
import { IArtifactStorage, IArtifact } from './artifact-storage.interface.js';

/**
 * Screenshot Queue Service Interface
 * Defines the contract for screenshot queue management with dependency injection support
 */
export interface IScreenshotQueueService {
  initialize(): Promise<boolean>;
  queueScreenshot(
    testId: string,
    stepId: string,
    stepIndex: number,
    buffer: Buffer,
    type: 'before' | 'after',
    platform?: string
  ): Promise<string>;
  getQueueStatus(): {
    queueLength: number;
    isProcessing: boolean;
    isInitialized: boolean;
  };
  clearQueue(): void;
  close(): Promise<void>;
}

// Screenshot upload queue item interface
interface ScreenshotQueueItem {
  testId: string;
  stepId: string;
  stepIndex: number;
  buffer: Buffer;
  type: 'before' | 'after';
  platform?: string;
  timestamp: number;
  retryCount: number;
}

/**
 * Screenshot Queue Service Configuration
 */
export interface ScreenshotQueueServiceConfig {
  maxRetries?: number;
  maxQueueSize?: number;
  storageProvider?: IArtifactStorage;
  enableLogging?: boolean;
}

/**
 * Screenshot Queue Service Implementation
 * Supports both dependency injection and backward compatibility
 */
export class ScreenshotQueueService implements IScreenshotQueueService {
  private static instance: ScreenshotQueueService;
  private queue: ScreenshotQueueItem[] = [];
  private isProcessing: boolean = false;
  private maxRetries: number = 3;
  private maxQueueSize: number = 1000;
  private uploadPromises: Map<string, Promise<string>> = new Map();
  private uploadResults: Map<string, string> = new Map();
  private storageProvider: IArtifactStorage | null = null;
  private isInitialized: boolean = false;
  private enableLogging: boolean = true;

  /**
   * Constructor for dependency injection
   * @param config Service configuration
   * @param injectedStorageProvider Optional injected storage provider
   */
  constructor(config?: ScreenshotQueueServiceConfig, injectedStorageProvider?: IArtifactStorage) {
    if (config) {
      this.maxRetries = config.maxRetries ?? 3;
      this.maxQueueSize = config.maxQueueSize ?? 1000;
      this.enableLogging = config.enableLogging ?? true;
      this.storageProvider = config.storageProvider || injectedStorageProvider || null;
    }

    if (this.enableLogging) {
      logger.debug('ScreenshotQueueService: Instance created with dependency injection');
    }
  }

  /**
   * Get singleton instance (DEPRECATED - Use dependency injection instead)
   * @deprecated Use ScreenshotQueueServiceFactory.createService() or dependency injection instead
   */
  public static getInstance(): ScreenshotQueueService {
    logger.warn('ScreenshotQueueService.getInstance() is deprecated. Use ScreenshotQueueServiceFactory.createService() or dependency injection instead.');

    if (!ScreenshotQueueService.instance) {
      ScreenshotQueueService.instance = new ScreenshotQueueService();
    }
    return ScreenshotQueueService.instance;
  }

  /**
   * Initialize the service
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      logger.debug('ScreenshotQueueService: Already initialized');
      return true;
    }

    try {
      await this.initializeStorageProvider();
      this.isInitialized = true;
      logger.info('ScreenshotQueueService: Initialized successfully');
      return true;
    } catch (error: any) {
      logger.error(`ScreenshotQueueService: Initialization failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Initialize the storage provider using StorageFactory (DI pattern)
   */
  private async initializeStorageProvider(): Promise<void> {
    // If storage provider was already injected or initialized, use it
    if (this.storageProvider) {
      logger.info(`ScreenshotQueueService: Using existing storage provider: ${this.storageProvider.getProviderName()}`);
      return;
    }

    try {

      // Create configuration based on app config
      const storageConfig: any = {
        // S3 configuration
        bucket: config.s3?.screenshotBucket || 'test-artifacts',
        region: config.s3?.region || process.env.AWS_REGION || 'eu-west-1',
        accessKey: config.s3?.accessKey,
        secretKey: config.s3?.secretKey,
        publicBaseUrl: config.s3?.publicBaseUrl,
        // MinIO configuration
        endPoint: config.minio?.endPoint || 'localhost',
        port: config.minio?.port || 9000,
        useSSL: config.minio?.useSSL || false
      };

      // Determine provider preference from config
      if (config.s3Enabled) {
        storageConfig.provider = 's3';
      } else if (config.minioEnabled) {
        storageConfig.provider = 'minio';
      }

      // Use StorageFactory for new DI pattern
      try {
        const { StorageFactory } = await import('./storage-factory.js');
        this.storageProvider = await StorageFactory.createStorageProvider(storageConfig);
        logger.info(`ScreenshotQueueService: Initialized storage provider via StorageFactory: ${this.storageProvider.getProviderName()}`);
      } catch (factoryError: any) {
        // If StorageFactory fails, try creating a basic factory instance
        logger.warn(`ScreenshotQueueService: StorageFactory static method failed, trying factory instance: ${factoryError.message}`);
        try {
          const { StorageFactoryInstance } = await import('./storage-factory.js');
          const factory = new StorageFactoryInstance({
            enableLogging: true,
            defaultProvider: 's3',
            fallbackToNoOp: true
          });
          this.storageProvider = await factory.createStorageProvider(storageConfig);
          logger.info(`ScreenshotQueueService: Initialized storage provider via StorageFactoryInstance: ${this.storageProvider.getProviderName()}`);
        } catch (instanceError: any) {
          logger.error(`ScreenshotQueueService: All storage initialization methods failed: ${instanceError.message}`);
          this.storageProvider = null;
        }
      }
    } catch (error: any) {
      logger.error(`ScreenshotQueueService: Error initializing storage provider: ${error.message}`);
      this.storageProvider = null;
    }
  }

  /**
   * Queue a screenshot for upload (buffer already captured)
   * @param testId Test ID
   * @param stepId Step ID
   * @param stepIndex Step index
   * @param buffer Screenshot buffer (already captured from live page)
   * @param type Screenshot type (before or after)
   * @param platform Platform type (web or android)
   * @returns Promise resolving to the URL of the uploaded screenshot
   */
  public async queueScreenshot(
    testId: string,
    stepId: string,
    stepIndex: number,
    buffer: Buffer,
    type: 'before' | 'after',
    platform?: string
  ): Promise<string> {
    // Ensure service is initialized
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Validate buffer is not empty (critical check for timing issues)
    if (!buffer || buffer.length === 0) {
      logger.error(`ScreenshotQueueService: Empty buffer received for test ${testId}, step ${stepId} - this indicates a timing issue`);
      return `empty-buffer://test-${testId}/step-${stepId}/${type}`;
    }

    // Don't queue if max size reached
    if (this.queue.length >= this.maxQueueSize) {
      logger.warn(`ScreenshotQueueService: Queue full (${this.queue.length} items), dropping screenshot for test ${testId}, step ${stepId}`);
      return `queue-full://test-${testId}/step-${stepId}/${type}`;
    }

    // Create a unique key for this screenshot
    const key = `${testId}_${stepId}_${type}`;

    // Check if we already have a promise for this screenshot
    if (this.uploadPromises.has(key)) {
      return this.uploadPromises.get(key)!;
    }

    logger.debug(`ScreenshotQueueService: Queuing screenshot with buffer size ${buffer.length} bytes for test ${testId}, step ${stepId}, type ${type}`);

    // Create a promise for this screenshot upload
    const promise = new Promise<string>((resolve) => {
      // Add to queue with the already-captured buffer
      this.queue.push({
        testId,
        stepId,
        stepIndex,
        buffer, // Buffer is already captured from live page
        type,
        platform: platform || 'web',
        timestamp: Date.now(),
        retryCount: 0
      });

      // Start processing if not already
      this.ensureProcessing();

      // When processed, this promise will be resolved with the URL
      // We'll check periodically until the result is available
      const checkResult = () => {
        if (this.uploadResults.has(key)) {
          resolve(this.uploadResults.get(key)!);
        } else {
          setTimeout(checkResult, 100);
        }
      };

      checkResult();
    });

    // Store the promise
    this.uploadPromises.set(key, promise);

    return promise;
  }

  /**
   * Ensure the queue is being processed
   * @private
   */
  private ensureProcessing(): void {
    if (!this.isProcessing) {
      // Start processing asynchronously
      this.processQueue().catch((error) => {
        logger.error(`ScreenshotQueueService: Error processing queue: ${error.message}`);
        this.isProcessing = false;
      });
    }
  }

  /**
   * Process the screenshot queue
   * @private
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      // Ensure service is initialized before processing
      if (!this.isInitialized) {
        await this.initialize();
      }

      while (this.queue.length > 0) {
        const item = this.queue.shift()!;

        try {
          let url: string;

          // If we have a storage provider, use it
          if (this.storageProvider) {
            url = await this.uploadScreenshot(item);
            logger.debug(`ScreenshotQueueService: Uploaded screenshot using unified storage for test ${item.testId}, step ${item.stepId}`);
          } else {
            // No storage provider available, return a placeholder URL
            logger.warn(`ScreenshotQueueService: No storage provider available. Screenshot not uploaded for test ${item.testId}`);
            url = `no-storage-provider://test-${item.testId}/step-${item.stepId}/${item.type}`;
          }

          // Store the URL for retrieval
          const key = `${item.testId}_${item.stepId}_${item.type}`;
          this.uploadResults.set(key, url);
          this.uploadPromises.set(key, Promise.resolve(url));
        } catch (error: any) {
          await this.handleUploadError(item, error);
        }
      }
    } finally {
      this.isProcessing = false;

      // If there are more items in the queue, continue processing
      if (this.queue.length > 0) {
        this.ensureProcessing();
      }
    }
  }

  /**
   * Upload a screenshot using the storage provider
   */
  private async uploadScreenshot(item: ScreenshotQueueItem): Promise<string> {
    if (!this.storageProvider) {
      throw new Error('No storage provider available');
    }

    // Detect content type from buffer
    const contentType = this.detectImageContentType(item.buffer);
    const fileExtension = contentType === 'image/jpeg' ? 'jpg' : 'png';

    // Create an artifact with proper metadata
    const artifact: IArtifact = {
      metadata: {
        testId: item.testId,
        stepName: item.stepId,
        timestamp: new Date(item.timestamp),
        platform: (item.platform === 'mobile' || item.platform === 'api') ? item.platform : 'web',
        artifactType: 'screenshot',
        contentType: contentType,
        customTags: {
          type: item.type,
          stepIndex: item.stepIndex.toString(),
          optimized: 'true',
          compression: contentType === 'image/jpeg' ? 'sharp-mozjpeg' : 'none'
        }
      },
      fileName: `${item.stepId}_${item.type}.${fileExtension}`,
      contentBuffer: item.buffer
    };

    // Upload using the unified storage system
    const destinationPath = `screenshots/${item.testId}`;
    const result = await this.storageProvider.uploadArtifact(artifact, destinationPath);
    return result.url || result.storagePath;
  }

  /**
   * Detect image content type from buffer
   * @param buffer Image buffer
   * @returns Content type string
   */
  private detectImageContentType(buffer: Buffer): string {
    if (!buffer || buffer.length < 4) {
      return 'image/png'; // Default fallback
    }

    // Check for JPEG magic bytes (FF D8 FF)
    if (buffer[0] === 0xFF && buffer[1] === 0xD8 && buffer[2] === 0xFF) {
      return 'image/jpeg';
    }

    // Check for PNG magic bytes (89 50 4E 47)
    if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
      return 'image/png';
    }

    // Check for WebP magic bytes (52 49 46 46)
    if (buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46) {
      return 'image/webp';
    }

    // Default to PNG if detection fails
    logger.warn('ScreenshotQueueService: Could not detect image format, defaulting to PNG');
    return 'image/png';
  }

  /**
   * Handle upload errors with retry logic
   */
  private async handleUploadError(item: ScreenshotQueueItem, error: any): Promise<void> {
    const key = `${item.testId}_${item.stepId}_${item.type}`;

    // Check if this is a connection error
    if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
      logger.warn(`ScreenshotQueueService: Connection refused to storage server. Test will continue without uploading screenshot: ${error.message}`);

      // Set a placeholder URL so tests can continue
      const placeholderUrl = `storage-connection-error://test-${item.testId}/step-${item.stepId}/${item.type}`;
      this.uploadResults.set(key, placeholderUrl);
      this.uploadPromises.set(key, Promise.resolve(placeholderUrl));
    } else if (item.retryCount < this.maxRetries) {
      // Retry this item
      item.retryCount++;
      logger.warn(`ScreenshotQueueService: Error uploading screenshot, will retry (${item.retryCount}/${this.maxRetries}): ${error.message}`);
      this.queue.push(item);
    } else {
      // Max retries reached, log error and set error URL
      logger.error(`ScreenshotQueueService: Failed to upload screenshot after ${this.maxRetries} retries: ${error.message}`);

      const errorUrl = `upload-error://test-${item.testId}/step-${item.stepId}/${item.type}`;
      this.uploadResults.set(key, errorUrl);
      this.uploadPromises.set(key, Promise.resolve(errorUrl));
    }
  }

  /**
   * Get queue status
   */
  public getQueueStatus(): { queueLength: number; isProcessing: boolean; isInitialized: boolean } {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Clear the queue
   */
  public clearQueue(): void {
    logger.info(`ScreenshotQueueService: Clearing queue with ${this.queue.length} items`);
    this.queue = [];
    this.uploadPromises.clear();
    this.uploadResults.clear();
  }

  /**
   * Close the service
   */
  public async close(): Promise<void> {
    try {
      this.clearQueue();
      this.isInitialized = false;
      logger.info('ScreenshotQueueService: Closed successfully');
    } catch (error: any) {
      logger.error(`ScreenshotQueueService: Error during close: ${error.message}`);
    }
  }
}

/**
 * Screenshot Queue Service Factory
 * Provides dependency injection support for ScreenshotQueueService
 */
export class ScreenshotQueueServiceFactory {
  private static services = new Map<string, IScreenshotQueueService>();

  /**
   * Create a screenshot queue service with dependency injection
   * @param config Service configuration
   * @param storageProvider Optional storage provider
   * @returns Promise resolving to screenshot queue service
   */
  static async createService(
    config?: ScreenshotQueueServiceConfig,
    storageProvider?: IArtifactStorage
  ): Promise<IScreenshotQueueService> {
    const service = new ScreenshotQueueService(config, storageProvider);
    await service.initialize();
    return service;
  }

  /**
   * Create a managed screenshot queue service (singleton per key)
   * @param key Service key for management
   * @param config Service configuration
   * @param storageProvider Optional storage provider
   * @returns Promise resolving to screenshot queue service
   */
  static async createManagedService(
    key: string,
    config?: ScreenshotQueueServiceConfig,
    storageProvider?: IArtifactStorage
  ): Promise<IScreenshotQueueService> {
    if (!ScreenshotQueueServiceFactory.services.has(key)) {
      const service = await ScreenshotQueueServiceFactory.createService(config, storageProvider);
      ScreenshotQueueServiceFactory.services.set(key, service);
    }
    return ScreenshotQueueServiceFactory.services.get(key)!;
  }

  /**
   * Dispose a managed service
   * @param key Service key
   */
  static async disposeService(key: string): Promise<void> {
    const service = ScreenshotQueueServiceFactory.services.get(key);
    if (service) {
      await service.close();
      ScreenshotQueueServiceFactory.services.delete(key);
    }
  }

  /**
   * Dispose all managed services
   */
  static async disposeAll(): Promise<void> {
    const promises = Array.from(ScreenshotQueueServiceFactory.services.values()).map(service => service.close());
    await Promise.all(promises);
    ScreenshotQueueServiceFactory.services.clear();
  }
}

