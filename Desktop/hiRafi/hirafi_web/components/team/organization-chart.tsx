"use client"

import { useMemo } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, Too<PERSON>ip<PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import type { TeamMember, Role } from "@/types/team"
import dynamic from "next/dynamic"
import { Calendar, Mail, CheckCheck, XCircle } from "lucide-react"

// Dynamically import the organizational chart component with no SSR
const Tree = dynamic(() => import("react-organizational-chart").then((mod) => mod.Tree), { ssr: false })
const TreeNode = dynamic(() => import("react-organizational-chart").then((mod) => mod.TreeNode), { ssr: false })

// Custom CSS styles for the organizational chart
const chartStyles = {
  tree: `w-full flex justify-center`,
  nodeWrapper: `flex flex-col items-center my-2`,
  verticalLine: `w-0.5 h-6 bg-gray-300 dark:bg-gray-600`,
  horizontalLine: `h-0.5 w-8 bg-gray-300 dark:bg-gray-600`,
  childrenContainer: `flex flex-col items-center gap-4 mt-2`,
  siblingContainer: `flex flex-col items-center gap-6 mt-2`,
  lineContainer: `flex items-center`,
}

interface OrganizationChartProps {
  members: TeamMember[]
  roles: Role[]
}

export function OrganizationChart({ members, roles }: OrganizationChartProps) {
  // Get effective role name from member
  const getMemberRole = (member: TeamMember) => {
    // If member has role object, use role.name
    if (member.role && typeof member.role === 'object' && 'name' in member.role) {
      return member.role.name;
    }
    // If member has role as string, use it directly
    if (member.role && typeof member.role === 'string') {
      return member.role;
    }
    // Otherwise use roleId
    return member.roleId || 'Belirsiz';
  }

  // Find team admin or company leader
  const adminMember = useMemo(() => {
    return members.find(member => {
      const roleName = getMemberRole(member);
      return roleName === "company_leader" || 
             roleName === "CompanyLeader" ||
             roleName === "team_admin" || 
             roleName === "Team Admin";
    })
  }, [members])

  // Group members by role
  const membersByRole = useMemo(() => {
    const result: Record<string, TeamMember[]> = {}

    members.forEach(member => {
      if (member.status !== "active") return // Skip inactive members
      
      const roleName = getMemberRole(member);
      if (!roleName) return; // Skip members without role
      
      if (!result[roleName]) {
        result[roleName] = []
      }
      result[roleName].push(member)
    })

    return result
  }, [members])

  // Sort roles by hierarchy
  const sortedRoles = useMemo(() => {
    return [...roles].sort((a, b) => {
      // Admin/Company Leader always at the top
      if (a.name === "Team Admin" || a.name === "CompanyLeader") return -1
      if (b.name === "Team Admin" || b.name === "CompanyLeader") return 1

      // Then by permission count
      return b.permissions.length - a.permissions.length
    })
  }, [roles])

  // Get a placeholder member if no admin found - select the member with highest permissions
  const placeholderMember = useMemo(() => {
    if (adminMember) return null

    // If no admin found, find the role with highest permissions
    const topRole = sortedRoles[0]
    if (!topRole) return null

    // Find members with this role
    const topRoleMembers = membersByRole[topRole.name] || []
    return topRoleMembers[0] || null
  }, [adminMember, sortedRoles, membersByRole])

  // Get all unique roles that have active members
  const activeRoles = useMemo(() => {
    return Object.keys(membersByRole).filter(roleName => membersByRole[roleName].length > 0)
  }, [membersByRole])

  // Create a hierarchical structure for roles
  const roleHierarchy = useMemo(() => {
    const adminRoleNames = ["team_admin", "Team Admin", "company_leader", "CompanyLeader"];
    
    // Start with admin roles at the top level
    const topLevelRoles = sortedRoles.filter(role => 
      adminRoleNames.includes(role.name)
    );
    
    // Then add other roles based on permission count (higher count = higher in hierarchy)
    const otherRoles = sortedRoles
      .filter(role => !adminRoleNames.includes(role.name))
      .sort((a, b) => b.permissions.length - a.permissions.length);
    
    return [...topLevelRoles, ...otherRoles];
  }, [sortedRoles]);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(part => part[0])
      .join("")
      .toUpperCase()
  }

  const getRoleColor = (roleName: string) => {
    // Default color mapping
    const colorMap: Record<string, string> = {
      "Team Admin": "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100",
      "team_admin": "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100",
      "CompanyLeader": "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100",
      "company_leader": "bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100",
      "Developer": "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100",
      "developer": "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100",
      "QA": "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100",
      "qa": "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100",
      "Viewer": "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100",
      "viewer": "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100",
      "Tester": "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100",
      "tester": "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
    }
    
    const role = roles.find(r => r.name === roleName)
    return colorMap[roleName] || (role?.color || "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100")
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'Belirtilmemiş'
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const renderMemberNode = (member: TeamMember) => (
    <TooltipProvider key={member.id}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="p-1 cursor-pointer">
            <Card className={`w-[220px] shadow-md transition-all duration-300 hover:shadow-lg ${member.status !== "active" ? "opacity-60" : ""}`}>
              <CardContent className="p-4 flex flex-col items-center">
                <Avatar className="h-16 w-16 mb-2">
                  <AvatarImage src={member.avatar || "/placeholder.svg?height=40&width=40"} alt={member.name || (member.user?.name || "")} />
                  <AvatarFallback className="text-lg">{getInitials(member.name || (member.user?.name || ""))}</AvatarFallback>
                </Avatar>
                <div className="text-center">
                  <p className="font-medium truncate w-full">{member.name || (member.user?.name || "")}</p>
                  <p className="text-sm text-gray-500 mb-2 truncate w-full">{member.email || (member.user?.email || "")}</p>
                  <Badge variant="outline" className={`${getRoleColor(getMemberRole(member))} border-none`}>
                    {getMemberRole(member)}
                  </Badge>
                  {member.status !== "active" && (
                    <Badge variant="outline" className="ml-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100 border-none">
                      Pasif
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TooltipTrigger>
        <TooltipContent side="right" className="p-0 overflow-hidden">
          <div className="w-72 p-0">
            <div className="bg-primary py-2 px-4">
              <h3 className="text-primary-foreground font-medium">{member.name || (member.user?.name || "")}</h3>
              <p className="text-primary-foreground/80 text-xs">{member.email || (member.user?.email || "")}</p>
            </div>
            <div className="p-4 space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="outline" className={`${getRoleColor(getMemberRole(member))} border-none`}>
                  {getMemberRole(member)}
                </Badge>
                {member.status === "active" ? (
                  <span className="flex items-center text-xs text-green-600 dark:text-green-400">
                    <CheckCheck className="h-3 w-3 mr-1" /> Aktif
                  </span>
                ) : (
                  <span className="flex items-center text-xs text-red-500">
                    <XCircle className="h-3 w-3 mr-1" /> Pasif
                  </span>
                )}
              </div>
              
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span>Oluşturulma: {formatDate(member.createdAt || member.addedAt)}</span>
              </div>
              
              {(member.lastLogin || member.lastActive) && (
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>Son Giriş: {formatDate(member.lastLogin || member.lastActive)}</span>
                </div>
              )}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )

  const renderRoleNode = (role: Role, members: TeamMember[]) => (
    <div className="p-1">
      <Card className="w-[180px] shadow-sm">
        <CardContent className="p-3 flex flex-col items-center">
          <Badge variant="outline" className={`${role.color || getRoleColor(role.name)} border-none mb-2`}>
            {role.name}
          </Badge>
          <p className="text-sm text-gray-500">{members.length} üye</p>
        </CardContent>
      </Card>
    </div>
  )

  // Render a custom vertical organizational chart
  const renderVerticalHierarchy = () => {
    return (
      <div className="flex flex-col items-center gap-8 w-full">
        {/* Top level - Admin node */}
        <div className={chartStyles.nodeWrapper}>
          {adminMember ? renderMemberNode(adminMember) : 
           placeholderMember ? renderMemberNode(placeholderMember) : 
           <div>Yönetici Bulunamadı</div>}
        </div>
        
        {/* Vertical line connecting to other roles */}
        <div className={chartStyles.verticalLine}></div>
        
        {/* Lower level roles */}
        <div className={chartStyles.siblingContainer}>
          {roleHierarchy.map(role => {
            // Skip the admin role as it's already at the top
            if ((adminMember && (role.name === "Team Admin" || role.name === "CompanyLeader" || 
                role.name === "team_admin" || role.name === "company_leader")) ||
                (placeholderMember && placeholderMember.role === role.name)) {
              return null;
            }

            const roleMembers = membersByRole[role.name] || [];
            if (roleMembers.length === 0) return null;

            return (
              <div key={role.id} className="mb-12">
                {/* Role heading */}
                <div className="flex flex-col items-center mb-4">
                  {renderRoleNode(role, roleMembers)}
                  <div className={chartStyles.verticalLine}></div>
                </div>
                
                {/* Members in this role */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 justify-items-center">
                  {roleMembers.map(member => (
                    <div key={member.id}>{renderMemberNode(member)}</div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="p-4 bg-white dark:bg-gray-900 rounded-lg border" style={{ minHeight: "800px", overflowX: "auto" }}>
      <div className="flex justify-center mb-8">
        <h3 className="text-lg font-medium">Organizasyon Şeması</h3>
      </div>

      <div className="flex justify-center min-w-[1200px]">
        {activeRoles.length > 0 ? (
          <div className="w-full">
            {renderVerticalHierarchy()}
          </div>
        ) : (
          <div className="text-center p-8">
            <p>Takımda aktif üye bulunamadı. Lütfen takıma üye ekleyiniz.</p>
          </div>
        )}
      </div>
    </div>
  )
}
