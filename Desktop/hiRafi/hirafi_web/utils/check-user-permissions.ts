"use client"

import { Permission } from "@/types/permissions"

// JWT token'dan kullanıc<PERSON> bilgilerini al
function getUserFromToken(): any {
  if (typeof window === 'undefined') return null

  const token = localStorage.getItem('token')
  if (!token) return null

  try {
    // JWT token'ı decode et
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )

    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('Error decoding token:', error)
    return null
  }
}

/**
 * Kullanıcının izinlerini localStorage'dan alır
 * @returns Kullanıcı izinleri dizisi
 */
export function getUserPermissionsFromStorage(): Permission[] {
  if (typeof window === 'undefined') {
    return []
  }
  
  try {
    const userStr = localStorage.getItem('user')
    if (!userStr) return []
    
    const user = JSON.parse(userStr)
    return user.permissions || []
  } catch (error) {
    console.error('Kullanıcı izinleri alınamadı:', error)
    return []
  }
}

/**
 * Kullanıcının belirli bir izne sahip olup olmadığını kontrol eder
 * @param permission Kontrol edilecek izin
 * @returns İzin varsa true, yoksa false
 */
export function checkUserPermission(resource: string, action: string): boolean {
  const user = getUserFromToken()

  if (!user) {
    return false
  }

  // Admin ve company_owner her zaman erişebilir
  if (user.accountType === 'admin' || user.accountType === 'company_owner') {
    return true
  }

  // Permissions array'ini kontrol et
  const permissions = user.permissions || []
  
  // Exact match kontrolü
  const hasExactPermission = permissions.some((permission) =>
    permission.resource === resource && permission.action === action
  )

  if (hasExactPermission) {
    return true
  }

  // Wildcard (*) action kontrolü - eğer resource için * action'ı varsa
  const hasWildcardPermission = permissions.some((permission) =>
    permission.resource === resource && permission.action === '*'
  )

  if (hasWildcardPermission) {
    return true
  }

  // Manage action'ı varsa, diğer tüm action'lara izin ver
  const hasManagePermission = permissions.some((permission) =>
    permission.resource === resource && permission.action === 'manage'
  )

  if (hasManagePermission) {
    return true
  }

  return false
}
