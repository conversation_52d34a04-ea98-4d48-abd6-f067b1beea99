/**
 * Unified Step Handler Registry
 * Single registry implementation for all platforms
 */

import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { IStepHandlerRegistry } from '../interfaces/step-handler-registry.interface.js';
import { logger } from '../../../utils/logger.js';
import { handleAsyncOperation, Result } from '../utils/error-handling.js';
import { VariableResolutionService } from '../../../services/variable-resolution-service.js';

/**
 * Unified Step Handler Registry
 * Manages a collection of step handlers for all platforms
 */
export class StepHandlerRegistry implements IStepHandlerRegistry {
  private handlers: Map<string, IStepHandler> = new Map();
  private platform: string;
  private progressReporter: any = null;
  private currentTestId: string | null = null;
  private currentTestContext: {
    runId?: string;
    executionId?: string;
    scenarioId?: string;
  } | null = null;
  private screenshotProvider: any = null; // Screenshot provider for nested steps
  private currentTestRequest: any = null; // Current test request for variable resolution
  private variableResolutionService: VariableResolutionService;

  /**
   * Create a new StepHandlerRegistry
   * @param platform Platform identifier for logging
   */
  constructor(platform: string = 'common') {
    this.platform = platform;
    this.variableResolutionService = new VariableResolutionService();
    logger.info(`StepHandlerRegistry: Created registry for ${platform} platform`);
  }

  /**
   * Set progress reporter for nested step reporting
   * @param progressReporter Progress reporter instance
   * @param testId Current test ID
   * @param testContext Test context with runId, executionId, scenarioId
   */
  setProgressReporter(progressReporter: any, testId: string, testContext?: {
    runId?: string;
    executionId?: string;
    scenarioId?: string;
  }): void {
    this.progressReporter = progressReporter;
    this.currentTestId = testId;
    this.currentTestContext = testContext || null;
    logger.debug(`StepHandlerRegistry: Progress reporter set for test ${testId} with context: ${JSON.stringify(testContext)}`);
  }

  /**
   * Set screenshot provider for nested step screenshots
   * @param screenshotProvider Screenshot provider instance
   */
  setScreenshotProvider(screenshotProvider: any): void {
    this.screenshotProvider = screenshotProvider;
    logger.debug(`StepHandlerRegistry: Set screenshot provider for ${this.platform} platform`);
  }

  /**
   * Set current test request for variable resolution
   * @param testRequest Current test request
   */
  setCurrentTestRequest(testRequest: any): void {
    this.currentTestRequest = testRequest;
    
    // ENHANCED DEBUG: Log test request details
    logger.info(`StepHandlerRegistry: DEBUG - Setting test request for ${this.platform} platform`);
    logger.info(`StepHandlerRegistry: DEBUG - testRequest exists: ${!!testRequest}`);
    logger.info(`StepHandlerRegistry: DEBUG - testRequest.scenario exists: ${!!testRequest?.scenario}`);
    logger.info(`StepHandlerRegistry: DEBUG - testRequest.scenario.testDataSetId: ${testRequest?.scenario?.testDataSetId || 'UNDEFINED'}`);
    logger.info(`StepHandlerRegistry: DEBUG - testRequest.scenario.metadata exists: ${!!testRequest?.scenario?.metadata}`);
    
    if (testRequest?.scenario?.metadata?.variables) {
      logger.info(`StepHandlerRegistry: DEBUG - testRequest.scenario.metadata.variables count: ${testRequest.scenario.metadata.variables.length}`);
      logger.info(`StepHandlerRegistry: DEBUG - First few variables: ${JSON.stringify(testRequest.scenario.metadata.variables.slice(0, 3).map((v: any) => ({ name: v.name, sourceType: v.sourceType })), null, 2)}`);
    }
    
    logger.debug(`StepHandlerRegistry: Set current test request for ${this.platform} platform`);
  }

  /**
   * Register a step handler for a specific step type
   * @param type Step type
   * @param handler Step handler
   */
  registerHandler(type: string, handler: IStepHandler): void {
    if (this.handlers.has(type)) {
      logger.warn(`StepHandlerRegistry: Overriding existing handler for step type: ${type} on ${this.platform} platform`);
    }

    this.handlers.set(type, handler);
    
    // Inject step executor for control flow handlers
    this.injectStepExecutorForControlFlow(handler);
    
    logger.debug(`StepHandlerRegistry: Registered handler for step type: ${type} on ${this.platform} platform`);
  }

  /**
   * Get a step handler for a specific step type
   * @param type Step type
   * @returns Step handler or undefined if not found
   */
  getHandler(type: string): IStepHandler | undefined {
    const handler = this.handlers.get(type);
    if (!handler) {
      logger.warn(`StepHandlerRegistry: No handler found for step type: ${type} on ${this.platform} platform`);
    }
    return handler;
  }

  /**
   * Check if a handler is registered for a specific step type
   * @param type Step type
   * @returns True if handler is registered, false otherwise
   */
  hasHandler(type: string): boolean {
    return this.handlers.has(type);
  }

  /**
   * Get all registered step types
   * @returns Array of registered step types
   */
  getRegisteredTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Get all registered handlers
   * @returns Map of step type to handler
   */
  getAllHandlers(): Map<string, IStepHandler> {
    return new Map(this.handlers);
  }

  /**
   * Unregister a step handler
   * @param type Step type to unregister
   * @returns True if handler was removed, false if not found
   */
  unregisterHandler(type: string): boolean {
    const removed = this.handlers.delete(type);
    if (removed) {
      logger.debug(`StepHandlerRegistry: Unregistered handler for step type: ${type} on ${this.platform} platform`);
    } else {
      logger.warn(`StepHandlerRegistry: Attempted to unregister non-existent handler for step type: ${type} on ${this.platform} platform`);
    }
    return removed;
  }

  /**
   * Clear all registered handlers
   */
  clear(): void {
    const count = this.handlers.size;
    this.handlers.clear();
    logger.info(`StepHandlerRegistry: Cleared ${count} handlers on ${this.platform} platform`);
  }

  /**
   * Register multiple handlers at once
   * @param handlers Array of handlers to register
   */
  registerHandlers(handlers: IStepHandler[]): void {
    for (const handler of handlers) {
      const stepType = this.getStepTypeFromHandler(handler);
      if (stepType) {
        this.registerHandler(stepType, handler);
        
        // Inject step executor for control flow handlers
        this.injectStepExecutorForControlFlow(handler);
      } else {
        logger.warn(`StepHandlerRegistry: Could not determine step type for handler ${handler.getName()} on ${this.platform} platform`);
      }
    }
    logger.info(`StepHandlerRegistry: Registered ${handlers.length} handlers on ${this.platform} platform`);
  }

  /**
   * Inject step executor for control flow handlers
   * @param handler Handler to potentially inject step executor into
   */
  private injectStepExecutorForControlFlow(handler: IStepHandler): void {
    // Check if handler needs step executor (control flow handlers)
    const handlerName = handler.getName().toLowerCase();
    const needsStepExecutor = handlerName.includes('ifelse') || 
                             handlerName.includes('forloop') || 
                             handlerName.includes('whileloop');

    if (needsStepExecutor && typeof (handler as any).setStepExecutor === 'function') {
      // Create a bound step executor function with progress reporting
      const stepExecutor = this.executeStepWithProgress.bind(this);
      (handler as any).setStepExecutor(stepExecutor);
      
      // Also inject progress reporter if available
      if (typeof (handler as any).setProgressReporter === 'function') {
        (handler as any).setProgressReporter(this.progressReporter);
      }
      
      logger.debug(`StepHandlerRegistry: Injected step executor and progress reporter for ${handler.getName()} on ${this.platform} platform`);
    }
  }

  /**
   * Extract the step type that a handler can handle
   * @param handler Step handler
   * @returns Step type or null if cannot be determined
   */
  private getStepTypeFromHandler(handler: IStepHandler): string | null {
    // Common step types to test
    const commonStepTypes = [
      'aiAction', 'aiAssertion', 'aiWaitElement', 'aiQuery',
      'aiBoolean', 'aiKeyboardPress', 'aiHover', 'aiScroll',
      'aiTap', 'aiInput', 'aiRightClick', 'aiNumber', 'aiString', 'aiLocate',
      'ifElse', 'forLoop', 'whileLoop',
      'goto', 'sleep', 'click', 'type', 'wait', 'scroll',
      'screenshot', 'assert', 'navigate'
    ];

    // Test each common step type to see which one the handler can handle
    for (const stepType of commonStepTypes) {
      const testStep = {
        id: 'test',
        name: 'test',
        type: stepType,
        value: 'test'
      };

      try {
        if (handler.canHandle(testStep)) {
          return stepType;
        }
      } catch (error) {
        // Ignore errors during testing
        continue;
      }
    }

    // If no common step type matches, try to extract from handler name
    const handlerName = handler.getName().toLowerCase();
    if (handlerName.includes('action')) return 'aiAction';
    if (handlerName.includes('assertion')) return 'aiAssertion';
    if (handlerName.includes('waitelement')) return 'aiWaitElement';
    if (handlerName.includes('query')) return 'aiQuery';
    if (handlerName.includes('boolean')) return 'aiBoolean';
    if (handlerName.includes('keyboardpress')) return 'aiKeyboardPress';
    if (handlerName.includes('hover')) return 'aiHover';
    if (handlerName.includes('scroll')) return 'aiScroll';
    if (handlerName.includes('tap')) return 'aiTap';
    if (handlerName.includes('input')) return 'aiInput';
    if (handlerName.includes('rightclick')) return 'aiRightClick';
    if (handlerName.includes('number')) return 'aiNumber';
    if (handlerName.includes('string')) return 'aiString';
    if (handlerName.includes('locate')) return 'aiLocate';
    if (handlerName.includes('ifelse')) return 'ifElse';
    if (handlerName.includes('forloop')) return 'forLoop';
    if (handlerName.includes('whileloop')) return 'whileLoop';
    if (handlerName.includes('goto')) return 'goto';
    if (handlerName.includes('sleep')) return 'sleep';

    return null;
  }

  /**
   * Validate all registered handlers
   * @returns Validation results
   */
  validateHandlers(): { valid: string[]; invalid: string[] } {
    const valid: string[] = [];
    const invalid: string[] = [];

    for (const [type, handler] of this.handlers) {
      try {
        // Check if handler has required methods
        if (typeof handler.execute !== 'function') {
          invalid.push(`${type}: Missing execute method`);
          continue;
        }

        if (typeof handler.canHandle !== 'function') {
          invalid.push(`${type}: Missing canHandle method`);
          continue;
        }

        if (typeof handler.getName !== 'function') {
          invalid.push(`${type}: Missing getName method`);
          continue;
        }

        // Check if getName returns the expected type
        const handlerName = handler.getName();
        if (handlerName !== type) {
          invalid.push(`${type}: Handler name mismatch (expected: ${type}, got: ${handlerName})`);
          continue;
        }

        valid.push(type);
      } catch (error: any) {
        invalid.push(`${type}: Validation error - ${error.message}`);
      }
    }

    if (invalid.length > 0) {
      logger.warn(`StepHandlerRegistry: Found ${invalid.length} invalid handlers on ${this.platform} platform`);
    }

    return { valid, invalid };
  }

  /**
   * Get registry statistics
   * @returns Registry statistics
   */
  getStats(): Record<string, any> {
    const validation = this.validateHandlers();

    return {
      platform: this.platform,
      totalHandlers: this.handlers.size,
      registeredTypes: this.getRegisteredTypes(),
      validHandlers: validation.valid.length,
      invalidHandlers: validation.invalid.length,
      invalidHandlerDetails: validation.invalid
    };
  }

  /**
   * Export registry configuration
   * @returns Registry configuration object
   */
  exportConfig(): Record<string, string> {
    const config: Record<string, string> = {};

    for (const [type, handler] of this.handlers) {
      config[type] = handler.constructor.name;
    }

    return config;
  }

  /**
   * Get handler by step object (convenience method)
   * @param step Step object with type property
   * @returns Step handler or undefined if not found
   */
  getHandlerForStep(step: { type: string }): IStepHandler | undefined {
    return this.getHandler(step.type);
  }

  /**
   * Check if registry can handle a step
   * @param step Step object with type property
   * @returns True if registry has a handler for the step type
   */
  canHandleStep(step: { type: string }): boolean {
    const handler = this.getHandler(step.type);
    if (!handler) {
      return false;
    }

    // Create a minimal ScenarioStep object for validation
    const scenarioStep = {
      id: step.type,
      name: step.type,
      ...step,
      type: step.type  // Ensure type is preserved after spread
    };

    return handler.canHandle(scenarioStep);
  }

  /**
   * Get platform identifier
   * @returns Platform identifier
   */
  getPlatform(): string {
    return this.platform;
  }

  /**
   * Destroy the registry and cleanup all resources
   * Essential for garbage collection
   */
  destroy(): void {
    try {
      const handlerCount = this.handlers.size;

      // Clear all handlers
      this.handlers.clear();

      // Clear references
      this.progressReporter = null;
      this.currentTestId = null;
      this.currentTestContext = null;
      this.screenshotProvider = null;
      this.currentTestRequest = null;

      // Reset platform reference
      this.platform = '';

      logger.info(`StepHandlerRegistry: Destroyed registry with ${handlerCount} handlers`);
    } catch (error: any) {
      logger.error(`StepHandlerRegistry: Error during destroy: ${error.message}`);
    }
  }

  /**
   * Dispose method for DI container compatibility
   * Calls destroy() internally
   */
  async dispose(): Promise<void> {
    this.destroy();
  }

  /**
   * Check if registry is destroyed
   * @returns True if destroyed, false otherwise
   */
  isDestroyed(): boolean {
    return this.handlers.size === 0 && this.platform === '';
  }

  /**
   * Execute a step with progress reporting (for nested steps)
   * @param target Target to execute the step on
   * @param step Step to execute  
   * @param agent Agent to use for AI-powered steps
   * @param logs Array to append log messages to
   * @param parentStepId Parent step ID for nested step tracking
   * @param nestingLevel Nesting level (0 for top level, 1 for first nested, etc.)
   * @returns Promise resolving to the result of the step execution
   */
  async executeStepWithProgress(
    target: any,
    step: any,
    agent: any,
    logs: string[],
    parentStepId?: string,
    nestingLevel: number = 1
  ): Promise<{ success: boolean; error: string | null; data?: any; beforeScreenshotUrl?: string | null; afterScreenshotUrl?: string | null }> {
    const stepId = step.id || `nested-${Date.now()}`;
    
    // Report nested step start if progress reporter is available
    if (this.progressReporter && this.currentTestId) {
      try {
        await this.progressReporter.reportStepProgress(this.currentTestId, {
          type: 'nestedStep',
          stepId: stepId,
          stepName: this.generateNestedStepName(step, parentStepId, nestingLevel),
          stepType: step.type,
          status: 'started',
          timestamp: new Date().toISOString(),
          parentStepId,
          nestingLevel,
          // Include test context to preserve scenarioId, runId, executionId
          runId: this.currentTestContext?.runId,
          executionId: this.currentTestContext?.executionId,
          scenarioId: this.currentTestContext?.scenarioId
        });
      } catch (error: any) {
        logger.warn(`StepHandlerRegistry: Failed to report nested step start: ${error.message}`);
      }
    }

    // Take before screenshot for nested steps
    let beforeScreenshotUrl: string | null = null;
    if (this.screenshotProvider && this.currentTestId) {
      try {
        beforeScreenshotUrl = await this.screenshotProvider.takeScreenshot(
          target,
          this.currentTestId,
          stepId,
          'before',
          nestingLevel // Use nesting level as step index for nested steps
        );
        logger.debug(`StepHandlerRegistry: Before screenshot captured for nested step ${stepId}: ${beforeScreenshotUrl}`);
      } catch (screenshotError: any) {
        logger.warn(`StepHandlerRegistry: Error taking before screenshot for nested step ${stepId}: ${screenshotError.message}`);
      }
    }

    // Execute the step using the standard execution logic
    const result = await this.executeStep(target, step, agent, logs);

    // Take after screenshot for nested steps
    let afterScreenshotUrl: string | null = null;
    if (this.screenshotProvider && this.currentTestId) {
      try {
        afterScreenshotUrl = await this.screenshotProvider.takeScreenshot(
          target,
          this.currentTestId,
          stepId,
          'after',
          nestingLevel // Use nesting level as step index for nested steps
        );
        logger.debug(`StepHandlerRegistry: After screenshot captured for nested step ${stepId}: ${afterScreenshotUrl}`);
      } catch (screenshotError: any) {
        logger.warn(`StepHandlerRegistry: Error taking after screenshot for nested step ${stepId}: ${screenshotError.message}`);
      }
    }

    // Report nested step completion
    if (this.progressReporter && this.currentTestId) {
      try {
        await this.progressReporter.reportStepProgress(this.currentTestId, {
          type: 'nestedStep',
          stepId: stepId,
          stepName: this.generateNestedStepName(step, parentStepId, nestingLevel),
          stepType: step.type,
          status: result.success ? 'completed' : 'failed',
          timestamp: new Date().toISOString(),
          error: result.error,
          result: result.data,
          parentStepId,
          nestingLevel,
          // Include test context to preserve scenarioId, runId, executionId
          runId: this.currentTestContext?.runId,
          executionId: this.currentTestContext?.executionId,
          scenarioId: this.currentTestContext?.scenarioId,
          beforeScreenshotUrl,
          afterScreenshotUrl
        });
      } catch (error: any) {
        logger.warn(`StepHandlerRegistry: Failed to report nested step completion: ${error.message}`);
      }
    }

    // Include screenshot URLs in the result
    return {
      ...result,
      beforeScreenshotUrl,
      afterScreenshotUrl
    };
  }

  /**
   * Generate step name for nested steps
   * @param step Step to generate name for
   * @param parentStepId Parent step ID for nested steps
   * @param nestingLevel Nesting level
   * @returns Generated step name
   */
  private generateNestedStepName(step: any, parentStepId?: string, nestingLevel: number = 1): string {
    // Use the same generateStepName logic as the test runner
    // but with nested step parameters
    return this.generateStepName(step, parentStepId, nestingLevel);
  }

  /**
   * Generate meaningful step name based on step type (matches base-test-runner.ts)
   * @param step The scenario step
   * @param parentStepId Optional parent step ID for nested steps
   * @param nestingLevel Optional nesting level (0 for top level, 1 for first nested, etc.)
   * @returns Formatted step name
   */
  private generateStepName(step: any, parentStepId?: string, nestingLevel?: number): string {
    let stepName = step.name || step.type;

    // Handle nested steps with prefix
    const isNested = nestingLevel !== undefined && nestingLevel > 0;
    const prefix = isNested ? `  ${'  '.repeat(nestingLevel - 1)}└─ ` : '';

    // Generate human-readable step names based on step type
    switch (step.type) {
      case 'aiTap':
        const tapTarget = step.prompt || step.value || step.name || step.description;
        if (isNested) {
          stepName = tapTarget ? `Click ${tapTarget}` : 'Click element';
        } else {
          if (tapTarget) {
            stepName = `Click ${tapTarget}`;
          } else {
            stepName = 'Click element';
          }
        }
        break;

      case 'aiInput':
        const textToInput = step.value || step.prompt;
        const inputTarget = step.target || step.name || step.description;
        if (isNested) {
          stepName = textToInput ? `Input "${textToInput}"` : 'Input text';
        } else {
          if (textToInput && inputTarget && inputTarget !== textToInput) {
            stepName = `Input "${textToInput}" into ${inputTarget}`;
          } else if (textToInput) {
            stepName = `Input "${textToInput}"`;
          } else {
            stepName = 'Input text';
          }
        }
        break;

      case 'aiAssertion':
        const assertionText = step.prompt || step.value || step.name || step.description;
        if (isNested) {
          stepName = assertionText ? `Verify ${assertionText}` : 'Verify condition';
        } else {
          if (assertionText) {
            stepName = `Verify: ${assertionText}`;
          } else {
            stepName = 'Verify condition';
          }
        }
        break;

      case 'goto':
        const url = step.url || step.value || (step.name && step.name.startsWith('http') ? step.name : null);
        if (isNested) {
          stepName = url ? `Go to ${url}` : 'Go to page';
        } else {
          if (url) {
            stepName = `Navigate to ${url}`;
          } else {
            stepName = 'Navigate to page';
          }
        }
        break;

      case 'aiKeyboardPress':
        const keyToPress = step.value || step.prompt || step.name || step.description;
        if (isNested) {
          stepName = keyToPress ? `Press "${keyToPress}"` : 'Press key';
        } else {
          const keyTarget = step.target;
          if (keyToPress && keyTarget && keyTarget !== keyToPress) {
            stepName = `Press "${keyToPress}" on ${keyTarget}`;
          } else if (keyToPress) {
            stepName = `Press "${keyToPress}"`;
          } else {
            stepName = 'Press key';
          }
        }
        break;

      case 'aiAction':
        const actionText = step.prompt || step.value || step.name || step.description;
        if (isNested) {
          stepName = actionText || 'Action';
        } else {
          if (actionText) {
            stepName = actionText;
          } else {
            stepName = 'Perform action';
          }
        }
        break;

      default:
        stepName = step.name || step.type || 'Unknown step';
        break;
    }

    // Add prefix for nested steps
    return prefix + stepName;
  }

  /**
   * Execute a step using the appropriate handler
   * @param target Target to execute the step on
   * @param step Step to execute
   * @param agent Agent to use for AI-powered steps
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async executeStep(
    target: any,
    step: any,
    agent: any,
    logs: string[]
  ): Promise<{ success: boolean; error: string | null; data?: any }> {
    try {
      // Resolve variables in the step before execution
      const resolvedStep = await this.resolveVariablesInStep(step);
      
      // Execute the step with resolved variables
      return await this.originalExecuteStep(target, resolvedStep, agent, logs);
    } catch (error: any) {
      const errorMessage = `Error executing step ${step.type}: ${error.message}`;
      logger.error(`StepHandlerRegistry: ${errorMessage} on ${this.platform} platform`);

      // Log additional context for crash-related errors
      if (this.isCrashRelatedError(error)) {
        logger.error(`StepHandlerRegistry: Crash-related error detected for step ${step.type}: ${error.message}`);
        logger.error(`StepHandlerRegistry: Error stack: ${error.stack}`);
      }

      return {
        success: false,
        error: errorMessage,
        data: null
      };
    }
  }

  /**
   * Resolve variables in a step
   * @param step Step to resolve variables in
   * @returns Promise resolving to the step with resolved variables
   */
  private async resolveVariablesInStep(step: any): Promise<any> {
    try {
      const resolvedStep = { ...step };
      
      // Build test request context from current test request
      const testRequest = {
        scenarioData: this.currentTestRequest?.scenario ? {
          id: this.currentTestRequest.scenario.id || this.currentTestContext?.scenarioId,
          testDataSetId: this.currentTestRequest.scenario.testDataSetId,
          metadata: this.currentTestRequest.scenario.metadata || { variables: [] }
        } : (this.currentTestContext?.scenarioId ? { 
          id: this.currentTestContext.scenarioId,
          metadata: { variables: [] }
        } : undefined),
        teamId: this.currentTestRequest?.teamId || null,
        companyId: this.currentTestRequest?.companyId || null,
        environmentSettings: this.currentTestRequest?.environmentSettings || { activeEnvironmentId: 'production' }
      };
      
      // Resolve variables in common step properties
      const fieldsToResolve = ['name', 'value', 'prompt', 'text', 'url', 'xpath', 'selector', 'description', 'target'];
      
      for (const field of fieldsToResolve) {
        if (resolvedStep[field] && typeof resolvedStep[field] === 'string') {
          const resolvedValue = await this.variableResolutionService.resolveVariables(resolvedStep[field], testRequest);
          if (resolvedValue !== resolvedStep[field]) {
            logger.debug(`StepHandlerRegistry: Resolved variable in ${field}: ${resolvedStep[field]} -> ${resolvedValue}`);
            resolvedStep[field] = resolvedValue;
          }
        }
      }
      
      return resolvedStep;
    } catch (error: any) {
      logger.warn(`StepHandlerRegistry: Error resolving variables in step ${step.type}: ${error.message}`);
      // Return original step if variable resolution fails
      return step;
    }
  }

  /**
   * Original execute step method without variable resolution
   * @param target Target to execute the step on
   * @param step Step to execute
   * @param agent Agent to use for AI-powered steps
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  private async originalExecuteStep(
    target: any,
    step: any,
    agent: any,
    logs: string[]
  ): Promise<{ success: boolean; error: string | null; data?: any }> {
    try {
      const handler = this.getHandler(step.type);

      if (!handler) {
        const errorMessage = `No handler found for step type: ${step.type}`;
        logger.error(`StepHandlerRegistry: ${errorMessage} on ${this.platform} platform`);
        return {
          success: false,
          error: errorMessage,
          data: null
        };
      }

      // Check if handler can handle this step
      if (!handler.canHandle(step)) {
        const errorMessage = `Handler for ${step.type} cannot handle this step`;
        logger.error(`StepHandlerRegistry: ${errorMessage} on ${this.platform} platform`);
        return {
          success: false,
          error: errorMessage,
          data: null
        };
      }

      // Pre-execution health check for web platform
      if (this.platform === 'web' && target) {
        await this.performWebTargetHealthCheck(target, step);
      }

      // Execute the step
      logger.debug(`StepHandlerRegistry: Executing step ${step.type} using ${handler.constructor.name} on ${this.platform} platform`);
      const result = await handler.execute(target, step, agent, logs);

      logger.debug(`StepHandlerRegistry: Step ${step.type} executed with success: ${result.success} on ${this.platform} platform`);
      return result;
    } catch (error: any) {
      const errorMessage = `Error executing step ${step.type}: ${error.message}`;
      logger.error(`StepHandlerRegistry: ${errorMessage} on ${this.platform} platform`);

      // Log additional context for crash-related errors
      if (this.isCrashRelatedError(error)) {
        logger.error(`StepHandlerRegistry: Crash-related error detected for step ${step.type}: ${error.message}`);
        logger.error(`StepHandlerRegistry: Error stack: ${error.stack}`);
      }

      return {
        success: false,
        error: errorMessage,
        data: null
      };
    }
  }

  /**
   * Perform health check for web target before step execution
   * @param target Browser page target
   * @param step Step to execute
   */
  private async performWebTargetHealthCheck(target: any, step: any): Promise<void> {
    try {
      // Quick check if page is still open
      if (typeof target.isClosed === 'function' && target.isClosed()) {
        throw new Error('Browser page was closed unexpectedly');
      }

      // Quick check browser connection
      const browser = target.browser && target.browser();
      if (browser && !browser.connected) {
        throw new Error('Browser connection lost');
      }

      // For critical steps, perform a lightweight responsiveness check with timeout
      if (step.type.startsWith('ai') || step.type === 'goto') {
        try {
          // Use a race condition with timeout to avoid hanging
          await Promise.race([
            target.evaluate(() => document.readyState),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Health check timeout')), 2000)
            )
          ]);
        } catch (evalError: any) {
          // Only fail health check for genuine crashes, not timeouts
          if (this.isCrashRelatedError(evalError) && !evalError.message.includes('timeout')) {
            throw new Error(`Page is unresponsive: ${evalError.message}`);
          }
          // Log timeout but don't fail the health check
          if (evalError.message.includes('timeout')) {
            logger.warn(`StepHandlerRegistry: Health check timeout for step ${step.type}, proceeding anyway`);
          }
        }
      }
    } catch (error: any) {
      logger.error(`StepHandlerRegistry: Web target health check failed for step ${step.type}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if error indicates browser/page crash
   * @param error Error to check
   * @returns True if error indicates crash
   */
  private isCrashRelatedError(error: any): boolean {
    const errorMessage = error.message || '';

    const crashIndicators = [
      'Page is closed',
      'Browser is disconnected',
      'Target closed',
      'Session closed',
      'Connection terminated',
      'Browser process exited',
      'Protocol error',
      'Page was closed',
      'Browser connection lost'
    ];

    return crashIndicators.some(indicator => errorMessage.includes(indicator));
  }

  /**
   * Clone registry with same handlers
   * @param newPlatform Platform identifier for the cloned registry
   * @returns New registry with same handlers
   */
  clone(newPlatform: string): StepHandlerRegistry {
    const cloned = new StepHandlerRegistry(newPlatform);

    for (const [type, handler] of this.handlers) {
      cloned.registerHandler(type, handler);
    }

    logger.debug(`StepHandlerRegistry: Cloned registry from ${this.platform} to ${newPlatform} with ${this.handlers.size} handlers`);
    return cloned;
  }
}
