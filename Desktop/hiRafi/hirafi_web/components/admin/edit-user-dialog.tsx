"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { updateUser, getCompanies, getCompanyById } from "@/lib/api/admin-api"

interface EditUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
  user: any
}

// Form şeması
const userFormSchema = z.object({
  email: z.string().email({
    message: "Geçerli bir e-posta adresi girin.",
  }).default(""),
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }).default(""),
  accountType: z.enum(["user", "admin", "company_owner"], {
    required_error: "Lütfen bir hesap türü seçin.",
  }).default("user"),
  teamRole: z.enum(["team_admin", "team_member", "team_leader", "team_viewer", "developer", "tester"], {
    required_error: "Lütfen bir takım rolü seçin.",
  }).default("team_member"),
  companyId: z.string().optional(),
  active: z.boolean().default(true),
})

type UserFormValues = z.infer<typeof userFormSchema>

export function EditUserDialog({
  open,
  onOpenChange,
  onSuccess,
  user
}: EditUserDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [companies, setCompanies] = useState<any[]>([])
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(true)

  // Form tanımı
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      email: user?.email || "",
      name: user?.name || "",
      accountType: (user?.accountType as any) || "user",
      teamRole: (user?.teamRole as any) || "team_member",
      companyId: user?.companyId || undefined,
      active: user?.active !== false,
    },
  })

  // Kullanıcı değiştiğinde form değerlerini güncelle
  useEffect(() => {
    if (user) {
      form.reset({
        email: user.email || "",
        name: user.name || "",
        accountType: (user.accountType as any) || "user",
        teamRole: (user.teamRole as any) || "team_member",
        companyId: user.companyId || undefined,
        active: user.active !== false,
      })
    }
  }, [user, form])

  // accountType değiştiğinde teamRole'u otomatik ayarla
  useEffect(() => {
    const accountType = form.watch('accountType');
    if (accountType === 'company_owner') {
      form.setValue('teamRole', 'team_admin');
    }
  }, [form.watch('accountType')])

  // Şirketleri getir
  useEffect(() => {
    const fetchCompanies = async () => {
      setIsLoadingCompanies(true)
      try {
        const response = await getCompanies({
          page: 1,
          limit: 100
        }) // Tüm şirketleri getir

        if (response.success && (response.data?.companies || response.companies)) {
          setCompanies(response.data?.companies || response.companies)
        } else {
          setCompanies([])
          console.error("Failed to fetch companies:", response.data?.error || response.error)
        }
      } catch (error) {
        console.error("Failed to fetch companies:", error)
        setCompanies([])
      } finally {
        setIsLoadingCompanies(false)
      }
    }

    if (open) {
      fetchCompanies()
    }
  }, [open])

  // Form gönderme
  const onSubmit = async (data: UserFormValues) => {
    if (!user?.id) {
      toast({
        title: "Hata",
        description: "Kullanıcı ID'si bulunamadı.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      // Company owner için team_admin rolünü zorla
      if (data.accountType === 'company_owner') {
        data.teamRole = 'team_admin';
      }

      // Şirket değiştiyse, şirketin takım ID'sini al
      let teamId = user.teamId;

      if (data.companyId && data.companyId !== user.companyId) {
        try {
          // Şirket bilgilerini getir
          const companyResponse = await getCompanies({
            page: 1,
            limit: 1
          });

          if (companyResponse.success && companyResponse.companies) {
            // Seçilen şirketi bul
            const selectedCompany = companyResponse.companies.find(
              (company: any) => company.id === data.companyId
            );

            if (selectedCompany && selectedCompany.teamId) {
              teamId = selectedCompany.teamId;
              console.log(`Found teamId ${teamId} for company ${data.companyId}`);
            } else {
              // Şirket detaylarını getir
              const companyDetailResponse = await getCompanyById(data.companyId);
              if (companyDetailResponse.success && companyDetailResponse.company) {
                teamId = companyDetailResponse.company.teamId;
                console.log(`Found teamId ${teamId} from company details for company ${data.companyId}`);
              }
            }
          }
        } catch (error) {
          console.error("Error fetching company details:", error);
        }
      } else if (data.companyId === undefined || data.companyId === null) {
        // Şirket kaldırıldıysa takım ID'sini de kaldır
        teamId = null;
      }

      // Gönderilecek verileri logla
      console.log("Updating user with data:", {
        id: user.id,
        ...data,
        teamId
      })

      // API'ye gönder
      const response = await updateUser(user.id, {
        ...data,
        teamId
      })

      if (response.success) {
        toast({
          title: "Kullanıcı güncellendi",
          description: "Kullanıcı bilgileri başarıyla güncellendi.",
        })
        onOpenChange(false)

        // Başarılı olduğunda callback'i çağır
        if (onSuccess) {
          onSuccess()
        }
      } else {
        toast({
          title: "Hata",
          description: response.data?.error || response.error || "Kullanıcı güncellenirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating user:", error)
      toast({
        title: "Hata",
        description: "Kullanıcı güncellenirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Kullanıcı Düzenle</DialogTitle>
          <DialogDescription>
            Kullanıcı bilgilerini güncelleyin.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>E-posta *</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ad Soyad *</FormLabel>
                  <FormControl>
                    <Input placeholder="Ad Soyad" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="accountType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hesap Türü *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Hesap türü seçin" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="user">Kullanıcı</SelectItem>
                        <SelectItem value="admin">Yönetici</SelectItem>
                        <SelectItem value="company_owner">Şirket Sahibi</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="teamRole"
                render={({ field }) => {
                  const accountType = form.watch('accountType');
                  const isCompanyOwner = accountType === 'company_owner';

                  return (
                    <FormItem className="w-full">
                      <FormLabel>Takım Rolü *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || "team_member"}
                        disabled={isCompanyOwner}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Takım rolü seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="team_admin">Takım Yöneticisi</SelectItem>
                          <SelectItem value="team_leader">Takım Lideri</SelectItem>
                          <SelectItem value="team_member">Takım Üyesi</SelectItem>
                          <SelectItem value="team_viewer">Takım Gözlemcisi</SelectItem>
                          <SelectItem value="developer">Geliştirici</SelectItem>
                          <SelectItem value="tester">Test Uzmanı</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {isCompanyOwner
                          ? "Şirket Sahibi için Takım Yöneticisi rolü otomatik atanır"
                          : "Kullanıcının takım içindeki rolü"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>

            <FormField
              control={form.control}
              name="companyId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Şirket</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Şirket seçin (opsiyonel)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {isLoadingCompanies ? (
                        <SelectItem value="loading" disabled>Şirketler yükleniyor...</SelectItem>
                      ) : companies.length === 0 ? (
                        <SelectItem value="none" disabled>Şirket bulunamadı</SelectItem>
                      ) : (
                        companies.map((company) => (
                          <SelectItem key={company.id} value={company.id}>
                            {company.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Kullanıcının bağlı olacağı şirket (opsiyonel)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Aktif</FormLabel>
                    <FormDescription>
                      Kullanıcı hesabı aktif mi?
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></div>
                    Güncelleniyor...
                  </div>
                ) : (
                  "Kaydet"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
