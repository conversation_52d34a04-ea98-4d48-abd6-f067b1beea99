import { redisConnection } from './redisConnection.js';
import { logger } from '../../utils/logger.js';

interface RedisKeyInfo {
  key: string;
  type: string;
  value: any;
  size: number;
  ttl: string;
}

interface DatabaseInfo {
  database: number;
  keyCount: number;
  keys: RedisKeyInfo[];
}

class RedisAdminService {
  /**
   * Gets all Redis data including keys, values, and database information.
   * @returns {Promise<{ success: boolean; data?: any; message?: string }>}
   */
  public async getAllRedisData(): Promise<{ success: boolean; data?: any; message?: string }> {
    logger.info('[RedisAdminService] Fetching all Redis data for admin view.');
    const redisClient = redisConnection.getClient();

    if (!redisClient) {
      const errorMessage = '[RedisAdminService] Redis client not available. Cannot fetch data.';
      logger.error(errorMessage);
      return { success: false, message: errorMessage };
    }

    try {
      // Get all databases (Redis typically has 16 databases by default: 0-15)
      const databases: DatabaseInfo[] = [];

      for (let dbIndex = 0; dbIndex < 16; dbIndex++) {
        try {
          // Select database
          await redisClient.select(dbIndex);

          // Get all keys in this database
          const keys = await redisClient.keys('*');

          if (keys.length > 0) {
            const dbData: DatabaseInfo = {
              database: dbIndex,
              keyCount: keys.length,
              keys: []
            };

            // Limit to first 100 keys to prevent overwhelming response
            const limitedKeys = keys.slice(0, 100);

            for (const key of limitedKeys) {
              try {
                // Get key type
                const type = await redisClient.type(key);

                // Get TTL
                const ttl = await redisClient.ttl(key);

                let value = null;
                let size = 0;

                // Get value based on type (limit size for large values)
                switch (type) {
                  case 'string':
                    const stringValue = await redisClient.get(key);
                    value = stringValue && stringValue.length > 500
                      ? stringValue.substring(0, 500) + '...[truncated]'
                      : stringValue;
                    size = stringValue ? stringValue.length : 0;
                    break;

                  case 'list':
                    const listLength = await redisClient.llen(key);
                    const listSample = await redisClient.lrange(key, 0, 4); // First 5 items
                    value = listSample;
                    size = listLength;
                    break;

                  case 'set':
                    const setSize = await redisClient.scard(key);
                    const setSample = await redisClient.smembers(key);
                    value = setSample.slice(0, 5); // First 5 members
                    size = setSize;
                    break;

                  case 'zset':
                    const zsetSize = await redisClient.zcard(key);
                    const zsetSample = await redisClient.zrange(key, 0, 4, 'WITHSCORES');
                    value = zsetSample;
                    size = zsetSize;
                    break;

                  case 'hash':
                    const hashSize = await redisClient.hlen(key);
                    const hashKeys = await redisClient.hkeys(key);
                    const hashSample: { [key: string]: string | null } = {};
                    for (let i = 0; i < Math.min(5, hashKeys.length); i++) {
                      hashSample[hashKeys[i]] = await redisClient.hget(key, hashKeys[i]);
                    }
                    value = hashSample;
                    size = hashSize;
                    break;

                  default:
                    value = `[${type} type]`;
                    size = 0;
                }

                dbData.keys.push({
                  key,
                  type,
                  value,
                  size,
                  ttl: ttl === -1 ? 'no expiry' : ttl === -2 ? 'expired' : `${ttl}s`
                });
              } catch (keyError) {
                logger.warn(`[RedisAdminService] Error processing key ${key}: ${keyError}`);
                dbData.keys.push({
                  key,
                  type: 'error',
                  value: 'Error reading key',
                  size: 0,
                  ttl: 'unknown'
                });
              }
            }

            // Add truncation info if there are more keys
            if (keys.length > 100) {
              dbData.keys.push({
                key: '...',
                type: 'info',
                value: `... and ${keys.length - 100} more keys (showing first 100)`,
                size: 0,
                ttl: 'n/a'
              });
            }

            databases.push(dbData);
          }
        } catch (dbError) {
          logger.warn(`[RedisAdminService] Error accessing database ${dbIndex}: ${dbError}`);
        }
      }

      // Switch back to database 0
      await redisClient.select(0);

      // Get Redis info
      const info = await redisClient.info();
      const memory = await redisClient.info('memory');

      const result = {
        databases,
        totalDatabases: databases.length,
        totalKeys: databases.reduce((sum, db) => sum + db.keyCount, 0),
        serverInfo: {
          info: info.split('\r\n').filter(line => line && !line.startsWith('#')),
          memory: memory.split('\r\n').filter(line => line && !line.startsWith('#'))
        },
        timestamp: new Date().toISOString()
      };

      const successMessage = `[RedisAdminService] Successfully fetched Redis data. Found ${result.totalKeys} keys across ${result.totalDatabases} databases.`;
      logger.info(successMessage);

      return {
        success: true,
        data: result,
        message: successMessage
      };
    } catch (error: any) {
      const errorMessage = `[RedisAdminService] Error fetching Redis data: ${error.message || error}`;
      logger.error(errorMessage);
      return { success: false, message: errorMessage };
    }
  }

  /**
   * Deletes a specific key from a Redis database.
   * @param database - The database number (0-15)
   * @param key - The key name to delete
   * @returns {Promise<{ success: boolean; message: string }>}
   */
  public async deleteKey(database: number, key: string): Promise<{ success: boolean; message: string }> {
    logger.info(`[RedisAdminService] Attempting to delete key "${key}" from database ${database}.`);
    const redisClient = redisConnection.getClient();

    if (!redisClient) {
      const errorMessage = '[RedisAdminService] Redis client not available. Cannot delete key.';
      logger.error(errorMessage);
      return { success: false, message: errorMessage };
    }

    try {
      // Select the specified database
      await redisClient.select(database);

      // Check if key exists
      const exists = await redisClient.exists(key);
      if (!exists) {
        const message = `[RedisAdminService] Key "${key}" does not exist in database ${database}.`;
        logger.warn(message);
        return { success: false, message: message };
      }

      // Delete the key
      const result = await redisClient.del(key);

      // Switch back to database 0
      await redisClient.select(0);

      if (result > 0) {
        const successMessage = `[RedisAdminService] Successfully deleted key "${key}" from database ${database}.`;
        logger.info(successMessage);
        return { success: true, message: successMessage };
      } else {
        const errorMessage = `[RedisAdminService] Failed to delete key "${key}" from database ${database}. Key may not exist.`;
        logger.error(errorMessage);
        return { success: false, message: errorMessage };
      }
    } catch (error: any) {
      const errorMessage = `[RedisAdminService] Error deleting key "${key}" from database ${database}: ${error.message || error}`;
      logger.error(errorMessage);

      // Ensure we switch back to database 0 even on error
      try {
        await redisClient.select(0);
      } catch (selectError) {
        logger.error(`[RedisAdminService] Error switching back to database 0: ${selectError}`);
      }

      return { success: false, message: errorMessage };
    }
  }

  /**
   * Purges all data from all Redis databases.
   * This is a destructive operation and should be used with extreme caution.
   * @returns {Promise<{ success: boolean; message: string }>}
   */
  public async purgeAllData(): Promise<{ success: boolean; message: string }> {
    logger.warn('[RedisAdminService] Attempting to PURGE ALL DATA from Redis (FLUSHALL). This is a highly destructive operation.');
    const redisClient = redisConnection.getClient();

    if (!redisClient) {
      const errorMessage = '[RedisAdminService] Redis client not available. Cannot purge data.';
      logger.error(errorMessage);
      return { success: false, message: errorMessage };
    }

    try {
      await redisClient.flushall();
      const successMessage = '[RedisAdminService] Successfully purged all data from Redis (FLUSHALL).';
      logger.info(successMessage);
      return { success: true, message: successMessage };
    } catch (error: any) {
      const errorMessage = `[RedisAdminService] Error during Redis FLUSHALL operation: ${error.message || error}`;
      logger.error(errorMessage);
      return { success: false, message: errorMessage };
    }
  }
}

// Export an instance of the service
export const redisAdminService = new RedisAdminService(); 