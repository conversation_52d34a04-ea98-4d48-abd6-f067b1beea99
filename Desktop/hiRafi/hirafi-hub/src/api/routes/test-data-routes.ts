/**
 * Test Data Routes
 * API endpoints for test data management
 */

import { Router, Response } from 'express';
import multer from 'multer';
import path from 'path';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { logger } from '../../utils/logger.js';
import {
  createDataSet,
  getDataSets,
  getDataSetKeys,
  getDataSetById,
  updateDataSet,
  deleteDataSet,
  createDataSource,
  getDataSources,
  getDataSourceById,
  updateDataSource,
  deleteDataSource,
  createDataEnvironment,
  getDataEnvironments,
  getDataEnvironmentById,
  updateDataEnvironment,
  deleteDataEnvironment
} from '../../services/mongo/testDataService.js';
import {
  CreateDataSetRequest,
  UpdateDataSetRequest,
  CreateDataSourceRequest,
  UpdateDataSourceRequest,
  CreateDataEnvironmentRequest,
  UpdateDataEnvironmentRequest,
  DataSetQueryOptions,
  DataSourceQueryOptions,
  DataEnvironmentQueryOptions
} from '../../models/test-data.js';
import {
  testDatabaseConnection,
  getTables,
  getColumns,
  getPreviewData,
  executeFlow,
} from '../../services/dbConnectionService.js';
import axios from 'axios';

const router = Router();

// CSV upload configuration - store in memory, don't save to disk
const csvUpload = multer({
  storage: multer.memoryStorage(), // Store in memory only
  fileFilter: (req, file, cb) => {
    // Only accept CSV files
    if (file.mimetype === 'text/csv' || path.extname(file.originalname).toLowerCase() === '.csv') {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files are allowed!') as any, false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Apply authentication to all routes
router.use(authenticate);

/**
 * Data Sets Routes
 */

/**
 * @route   GET /api/test-data/data-sets/keys
 * @desc    Get lightweight data set information with only variable names for dropdown performance
 * @access  Authenticated
 */
router.get('/data-sets/keys', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // Parse query options
    const options: DataSetQueryOptions = {
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      skip: req.query.skip ? parseInt(req.query.skip as string) : undefined,
      search: req.query.search as string,
      environment: req.query.environment as any,
      tags: req.query.tags ? (req.query.tags as string).split(',') : undefined
    };

    const result = await getDataSetKeys(teamId, companyId, options);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      ...result.data
    });
  } catch (error: any) {
    logger.error(`[API] Error in GET /test-data/data-sets/keys: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/test-data/data-sets
 * @desc    Get data sets for the user's team
 * @access  Authenticated
 */
router.get('/data-sets', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // Parse query options
    const options: DataSetQueryOptions = {
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      skip: req.query.skip ? parseInt(req.query.skip as string) : undefined,
      search: req.query.search as string,
      environment: req.query.environment as any,
      tags: req.query.tags ? (req.query.tags as string).split(',') : undefined
    };

    const result = await getDataSets(teamId, companyId, options);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      ...result.data // Spread data directly to avoid nested structure
    });
  } catch (error: any) {
    logger.error(`[API] Error in GET /test-data/data-sets: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/test-data/data-sets/:id
 * @desc    Get a specific data set
 * @access  Authenticated
 */
router.get('/data-sets/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const result = await getDataSetById(id, teamId, companyId);

    if (!result.success) {
      return res.status(result.data ? 500 : 404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`[API] Error in GET /test-data/data-sets/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/test-data/data-sets
 * @desc    Create a new data set
 * @access  Authenticated
 */
router.post('/data-sets', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId, id: userId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const requestData: CreateDataSetRequest = req.body;

    // Validate required fields
    if (!requestData.name) {
      return res.status(400).json({
        success: false,
        error: 'Name is required'
      });
    }

    const result = await createDataSet(requestData, teamId, companyId, userId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    return res.status(201).json({
      success: true,
      data: result.data,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error in POST /test-data/data-sets: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   PUT /api/test-data/data-sets/:id
 * @desc    Update a data set
 * @access  Authenticated
 */
router.put('/data-sets/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const requestData: UpdateDataSetRequest = req.body;

    const result = await updateDataSet(id, requestData, teamId, companyId);

    if (!result.success) {
      return res.status(result.data ? 500 : 404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      data: result.data,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error in PUT /test-data/data-sets/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   DELETE /api/test-data/data-sets/:id
 * @desc    Delete a data set
 * @access  Authenticated
 */
router.delete('/data-sets/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const result = await deleteDataSet(id, teamId, companyId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error in DELETE /test-data/data-sets/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Data Sources Routes
 */

/**
 * @route   GET /api/test-data/data-sources
 * @desc    Get data sources for the user's team
 * @access  Authenticated
 */
router.get('/data-sources', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // Parse query options
    const options: DataSourceQueryOptions = {
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      skip: req.query.skip ? parseInt(req.query.skip as string) : undefined,
      search: req.query.search as string,
      type: req.query.type as any,
      isActive: req.query.isActive ? req.query.isActive === 'true' : undefined
    };

    const result = await getDataSources(teamId, companyId, options);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      ...result.data // Spread data directly to avoid nested structure
    });
  } catch (error: any) {
    logger.error(`[API] Error in GET /test-data/data-sources: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/test-data/data-sources/:id
 * @desc    Get a specific data source
 * @access  Authenticated
 */
router.get('/data-sources/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    // 🚀 NODE AUTHENTICATION BYPASS - Node system user can access any datasource
    const isNodeUser = req.user?.id === 'node-system';
    
    if (!isNodeUser && (!teamId || !companyId)) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // 🚀 For node users, we need to fetch without team/company restrictions
    let result;
    if (isNodeUser) {
      // Temporarily pass empty strings for node users - service should handle this
      result = await getDataSourceById(id, '', '');
    } else {
      result = await getDataSourceById(id, teamId!, companyId!);
    }

    if (!result.success) {
      return res.status(result.data ? 500 : 404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`[API] Error in GET /test-data/data-sources/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/test-data/data-sources
 * @desc    Create a new data source
 * @access  Authenticated
 */
router.post('/data-sources', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId, id: userId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const requestData: CreateDataSourceRequest = req.body;

    // Validate required fields
    if (!requestData.name || !requestData.type) {
      return res.status(400).json({
        success: false,
        error: 'Name and type are required'
      });
    }

    const result = await createDataSource(requestData, teamId, companyId, userId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    return res.status(201).json({
      success: true,
      data: result.data,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error in POST /test-data/data-sources: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   PUT /api/test-data/data-sources/:id
 * @desc    Update a data source
 * @access  Authenticated
 */
router.put('/data-sources/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const requestData: UpdateDataSourceRequest = req.body;
    const result = await updateDataSource(id, requestData, teamId, companyId);

    if (!result.success) {
      // If the service layer indicates not found, return 404
      if (result.message?.includes("not found")) {
        return res.status(404).json({ success: false, error: result.message });
      }
      return res.status(500).json({ success: false, error: result.message });
    }

    return res.json({ success: true, data: result.data, message: result.message });

  } catch (error: any) {
    logger.error(`[API] Error in PUT /test-data/data-sources/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   DELETE /api/test-data/data-sources/:id
 * @desc    Delete a data source
 * @access  Authenticated
 */
router.delete('/data-sources/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const result = await deleteDataSource(id, teamId, companyId);

    if (!result.success) {
       // If the service layer indicates not found, return 404
       if (result.message?.includes("not found")) {
        return res.status(404).json({ success: false, error: result.message });
    }
      return res.status(500).json({ success: false, error: result.message });
    }

    return res.status(204).send();

  } catch (error: any) {
    logger.error(`[API] Error in DELETE /test-data/data-sources/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/test-data/data-sources/csv-upload
 * @desc    Upload and parse CSV file for a data source
 * @access  Authenticated
 */
router.post('/data-sources/csv-upload', csvUpload.single('file'), async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({ success: false, error: 'Team ID and Company ID are required' });
    }

    if (!req.file) {
      return res.status(400).json({ success: false, error: 'No CSV file uploaded' });
    }

    const csvContent = req.file.buffer.toString('utf-8');
    const lines = csvContent.split('\n').filter(line => line.trim());

    if (lines.length === 0) {
      return res.status(400).json({ success: false, error: 'CSV file is empty' });
    }

    const rows = lines.map(line => 
      line.split(',').map(cell => cell.trim().replace(/"/g, ''))
    );

    const validRows = rows.filter(row => row.length >= 2 && row[0] && row[1]);
    
    if (validRows.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid name-value pairs found in CSV. Each row must have at least a name and a value.'
      });
    }

    const variables = validRows.map((row, index) => ({
      id: `csv-var-${Date.now()}-${index}`,
      name: row[0],
      value: row[1]
    }));

    logger.info(`[API] CSV file parsed for data source. Found ${variables.length} variables.`);

    return res.json({
      success: true,
      data: {
        filename: req.file.originalname,
        variables,
        totalRows: variables.length,
      },
      message: 'CSV file parsed successfully'
    });

  } catch (error: any) {
    logger.error(`[API] Error in POST /test-data/data-sources/csv-upload: ${error.message}`);
    return res.status(500).json({ success: false, error: error.message || 'Internal server error' });
  }
});

/**
 * @route   POST /api/test-data/data-sources/db/test-connection
 * @desc    Test database connection
 * @access  Authenticated
 */
router.post('/data-sources/db/test-connection', async (req: AuthRequest, res: Response) => {
  const { connectionString } = req.body;

  if (!connectionString) {
    return res.status(400).json({ success: false, message: 'Bağlantı dizesi gereklidir.' });
  }

  try {
    const result = await testDatabaseConnection(connectionString);
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error: any) {
    logger.error(`[API] Error in POST /test-data/data-sources/db/test-connection: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Sunucuda beklenmeyen bir hata oluştu.' });
  }
});

/**
 * @route   POST /api/test-data/data-sources/db/tables
 * @desc    Get all tables from a database connection
 * @access  Authenticated
 */
router.post('/data-sources/db/tables', async (req: AuthRequest, res: Response) => {
  const { connectionString } = req.body;

  if (!connectionString) {
    return res.status(400).json({ success: false, message: 'Bağlantı dizesi gereklidir.' });
  }

  try {
    const result = await getTables(connectionString);
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error: any) {
    logger.error(`[API] Error in POST /test-data/data-sources/db/tables: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Sunucuda beklenmeyen bir hata oluştu.' });
  }
});

/**
 * @route   POST /api/test-data/data-sources/db/columns
 * @desc    Get all columns from a database table
 * @access  Authenticated
 */
router.post('/data-sources/db/columns', async (req: AuthRequest, res: Response) => {
  const { connectionString, table } = req.body;

  if (!connectionString || !table) {
    return res.status(400).json({ success: false, message: 'Bağlantı dizesi ve tablo adı gereklidir.' });
  }

  try {
    const result = await getColumns(connectionString, table);
    if (result.success) {
      return res.status(200).json(result);
    } else {
      return res.status(400).json(result);
    }
  } catch (error: any) {
    logger.error(`[API] Error in POST /test-data/data-sources/db/columns: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Sunucuda beklenmeyen bir hata oluştu.' });
  }
});

/**
 * @route   POST /api/test-data/data-sources/db/preview
 * @desc    Get preview data from a database table
 * @access  Authenticated
 */
router.post('/data-sources/db/preview', async (req: AuthRequest, res: Response) => {
    const { connectionString, table, nameColumn, valueColumn } = req.body;

    if (!connectionString || !table || !nameColumn || !valueColumn) {
        return res.status(400).json({ success: false, message: 'Bağlantı dizesi, tablo adı ve sütun adları gereklidir.' });
    }

    try {
        const result = await getPreviewData(connectionString, table, nameColumn, valueColumn);
        if (result.success) {
            return res.status(200).json(result);
        } else {
            return res.status(400).json(result);
        }
    } catch (error: any) {
        logger.error(`[API] Error in POST /test-data/data-sources/db/preview: ${error.message}`);
        return res.status(500).json({ success: false, message: 'Sunucuda beklenmeyen bir hata oluştu.' });
    }
});

/**
 * @route   POST /api/test-data/data-sources/db/execute-flow
 * @desc    Execute a dynamic query flow on a database
 * @access  Authenticated
 */
router.post('/data-sources/db/execute-flow', async (req: AuthRequest, res: Response) => {
    const { connectionString, flow } = req.body;
    
    logger.info(`[API] Received /execute-flow request. Body: ${JSON.stringify(req.body, null, 2)}`);

    if (!connectionString || !flow) {
        logger.error('[API] Missing connectionString or flow in /execute-flow request.');
        return res.status(400).json({ success: false, message: 'Bağlantı dizesi ve akış reçetesi gereklidir.' });
    }

    try {
        const result = await executeFlow(connectionString, flow);
        if (result.success) {
            return res.status(200).json(result);
        } else {
            return res.status(400).json(result);
        }
    } catch (error: any) {
        logger.error(`[API] Error in POST /data-sources/db/execute-flow: ${error.message}`);
        return res.status(500).json({ success: false, message: 'Sunucuda beklenmeyen bir hata oluştu.' });
    }
});

/**
 * @route   POST /api/test-data/node/resolve-manual-variable
 * @desc    Resolve a manual variable value for node execution
 * @access  Node (X-Node-Key authentication)
 */
router.post('/node/resolve-manual-variable', async (req: AuthRequest, res: Response) => {
  try {
    // Node authentication kontrolü
    const nodeKeyHeader = req.headers['x-node-key'] as string;
    if (!nodeKeyHeader || nodeKeyHeader !== process.env.NODE_SECRET_KEY) {
      logger.warn(`Unauthorized node key used for manual variable resolution: ${nodeKeyHeader}`);
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    const { variableName, datasetId, environmentId, teamId, companyId } = req.body as {
      variableName: string;
      datasetId: string;
      environmentId: string;
      teamId?: string;
      companyId?: string;
    };

    if (!variableName || !datasetId || !environmentId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Variable name, dataset ID, and environment ID are required' 
      });
    }

    logger.debug(`[NODE] Resolving manual variable: ${variableName} from dataset: ${datasetId} for environment: ${environmentId}`);

    // Dataset'i al - team ve company kontrolü ile
    const datasetResult = await getDataSetById(datasetId, teamId || '', companyId || '');
    if (!datasetResult.success || !datasetResult.data) {
      return res.status(404).json({ 
        success: false, 
        error: 'Dataset not found' 
      });
    }

    const dataset = datasetResult.data;
    
    // Variable'ı bul
    const variable = dataset.metadata?.variables?.find((v: any) => v.name === variableName);
    if (!variable) {
      return res.status(404).json({ 
        success: false, 
        error: `Variable '${variableName}' not found in dataset` 
      });
    }

    // Environment value'yu al
    let value = null;
    
    // Environment ID ile direkt lookup
    if (variable.environmentValues && variable.environmentValues[environmentId] !== undefined) {
      const envValue = variable.environmentValues[environmentId];
      // Handle nested value structure
      if (typeof envValue === 'object' && envValue !== null && envValue.value !== undefined) {
        value = envValue.value;
      } else {
        value = envValue;
      }
    }

    if (value === null || value === undefined) {
      return res.status(404).json({ 
        success: false, 
        error: `Value not found for variable '${variableName}' in environment '${environmentId}'` 
      });
    }

    logger.debug(`[NODE] Resolved manual variable '${variableName}' to: ${value}`);

    return res.json({ 
      success: true, 
      value: value,
      variableName,
      environmentId
    });

  } catch (error: any) {
    logger.error(`[NODE] Error resolving manual variable: ${error.message}`);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

/**
 * @route   POST /api/test-data/data-sources/api/test
 * @desc    Test an API request
 * @access  Authenticated
 */
router.post('/data-sources/api/test', async (req: AuthRequest, res: Response) => {
    const { method, url, headers, body, bodyType = 'json', params } = req.body;

    if (!url || !method) {
        return res.status(400).json({ success: false, message: 'URL and method are required.' });
    }

    try {
        // Build final URL with query parameters
        let finalUrl = url;
        if (params && Array.isArray(params)) {
            const urlObj = new URL(url);
            params.forEach((param: any) => {
                if (param.enabled && param.key) {
                    urlObj.searchParams.set(param.key, param.value || '');
                }
            });
            finalUrl = urlObj.toString();
        }

        // Convert headers array to object
        const headersObject = headers.reduce((acc: any, header: any) => {
            if (header.key) {
                acc[header.key] = header.value;
            }
            return acc;
        }, {});

        // Build axios options
        const options: any = {
            method,
            url: finalUrl,
            headers: headersObject,
            timeout: 30000, // 30 second timeout
        };

        // Handle different body types
        if ((method === 'POST' || method === 'PUT' || method === 'PATCH') && body && bodyType !== 'none') {
            switch (bodyType) {
                case 'json':
                    try {
                        options.data = JSON.parse(body);
                        options.headers['Content-Type'] = 'application/json';
                    } catch (e) {
                        return res.status(400).json({ 
                            success: false, 
                            message: 'Invalid JSON body.'
                        });
                    }
                    break;
                    
                case 'formdata':
                    // For form-data, the frontend should send the data as FormData
                    // But since we're dealing with a proxy, we'll handle it as JSON for now
                    try {
                        const formFields = JSON.parse(body);
                        const FormData = require('form-data');
                        const form = new FormData();
                        
                        if (Array.isArray(formFields)) {
                            formFields.forEach((field: any) => {
                                if (field.enabled && field.key) {
                                    form.append(field.key, field.value || '');
                                }
                            });
                        }
                        
                        options.data = form;
                        options.headers = {
                            ...options.headers,
                            ...form.getHeaders()
                        };
                    } catch (e) {
                        return res.status(400).json({ 
                            success: false, 
                            message: 'Invalid form data.'
                        });
                    }
                    break;
                    
                case 'urlencoded':
                    try {
                        const urlEncodedFields = JSON.parse(body);
                        const params = new URLSearchParams();
                        
                        if (Array.isArray(urlEncodedFields)) {
                            urlEncodedFields.forEach((field: any) => {
                                if (field.enabled && field.key) {
                                    params.append(field.key, field.value || '');
                                }
                            });
                        }
                        
                        options.data = params.toString();
                        options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
                    } catch (e) {
                        return res.status(400).json({ 
                            success: false, 
                            message: 'Invalid URL encoded data.'
                        });
                    }
                    break;
                    
                case 'raw':
                    options.data = body;
                    break;
                    
                default:
                    // For backwards compatibility, treat as JSON
                    try {
                        options.data = JSON.parse(body);
                        options.headers['Content-Type'] = 'application/json';
                    } catch (e) {
                        // If not valid JSON, send as raw
                        options.data = body;
                    }
            }
        }

        // Make the actual API request
        const result = await axios(options);
        
        return res.status(200).json({
            success: true,
            data: {
                status: result.status,
                statusText: result.statusText,
                headers: result.headers,
                data: result.data, // API test result - keep nested for test response structure
            }
        });
    } catch (error: any) {
        return res.status(400).json({
             success: false,
             message: `API request failed: ${error.message}`,
             error: {
                status: error.response?.status,
                statusText: error.response?.statusText,
                headers: error.response?.headers,
                data: error.response?.data
             }
        });
    }
});

/**
 * Data Environments Routes
 */

/**
 * @route   GET /api/test-data/environments
 * @desc    Get data environments for the user's team
 * @access  Authenticated
 */
router.get('/environments', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // Parse query options
    const options: DataEnvironmentQueryOptions = {
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      skip: req.query.skip ? parseInt(req.query.skip as string) : undefined,
      search: req.query.search as string,
      type: req.query.type as any,
      isActive: req.query.isActive ? req.query.isActive === 'true' : undefined
    };

    const result = await getDataEnvironments(teamId, companyId, options);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      ...result.data // Spread the data directly to avoid nested data.data structure
    });
  } catch (error: any) {
    logger.error(`[API] Error in GET /test-data/environments: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/test-data/environments/:id
 * @desc    Get a specific data environment
 * @access  Authenticated
 */
router.get('/environments/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    logger.info(`[API] GET /test-data/environments/${id} - User: ${req.user?.id}, TeamId: ${teamId}, CompanyId: ${companyId}`);

    if (!teamId || !companyId) {
      logger.warn(`[API] Missing teamId or companyId for user: ${req.user?.id}`);
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const result = await getDataEnvironmentById(id, teamId, companyId);

    if (!result.success) {
      logger.warn(`[API] Environment not found: ${id} for team: ${teamId}, company: ${companyId}`);
      return res.status(result.data ? 500 : 404).json({
        success: false,
        error: result.message
      });
    }


    return res.json({
      success: true,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`[API] Error in GET /test-data/environments/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/test-data/environments
 * @desc    Create a new data environment
 * @access  Authenticated
 */
router.post('/environments', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, companyId, id: userId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const requestData: CreateDataEnvironmentRequest = req.body;

    // Validate required fields
    if (!requestData.name || !requestData.color) {
      return res.status(400).json({
        success: false,
        error: 'Name and color are required'
      });
    }

    const result = await createDataEnvironment(requestData, teamId, companyId, userId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    return res.status(201).json({
      success: true,
      data: result.data,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error in POST /test-data/environments: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   PUT /api/test-data/environments/:id
 * @desc    Update a data environment
 * @access  Authenticated
 */
router.put('/environments/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;



    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      logger.warn(`[API] PUT /test-data/environments/${id} - Missing teamId or companyId. TeamId: ${teamId}, CompanyId: ${companyId}`);
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const requestData: UpdateDataEnvironmentRequest = req.body;

    const result = await updateDataEnvironment(id, requestData, teamId, companyId);

    if (!result.success) {
      logger.warn(`[API] PUT /test-data/environments/${id} - Update failed: ${result.message}`);
      return res.status(result.data ? 500 : 404).json({
        success: false,
        error: result.message
      });
    }


    return res.json({
      success: true,
      data: result.data,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error in PUT /test-data/environments/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   DELETE /api/test-data/environments/:id
 * @desc    Delete a data environment
 * @access  Authenticated
 */
router.delete('/environments/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { teamId, companyId } = req.user!;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const result = await deleteDataEnvironment(id, teamId, companyId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error in DELETE /test-data/environments/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
