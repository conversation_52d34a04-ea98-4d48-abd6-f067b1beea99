/**
 * Company Model
 * Şirket yönetimi için veri modeli
 */

import { ObjectId } from 'mongodb';

/**
 * Şirket durumları
 */
export enum CompanyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

/**
 * AI Model yapısı
 */
export interface AIModel {
  id: string;
  name: string;
  api: string;
  apiKey: string;
  isActive: boolean;
  supportsImageProcessing: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
}

/**
 * Hesap türleri
 */
export enum AccountType {
  TRIAL = 'trial',
  BASIC = 'basic',
  STANDARD = 'standard',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise'
}

/**
 * Plugin ayarları
 */
export interface PluginSettings {
  enabled: boolean;
  config?: any;
}

/**
 * Şirket ayarları
 */
export interface CompanySettings {
  // Hesap türü ve süre
  accountType: AccountType;
  trialEndsAt?: Date;

  // Koşum limitleri
  runLimit?: number;         // Toplam koşum sayısı limiti
  runMinuteLimit?: number;   // Toplam koşum dakikası limiti
  concurrentRunLimit?: number; // Eşzamanlı koşum sayısı limiti
  generationLimit?: number;  // Senaryo generation limiti

  // Kalan kullanım
  remaining?: {
    runs?: number;           // Kalan koşum sayısı
    runMinutes?: number;     // Kalan koşum dakikası
    generations?: number;    // Kalan generation sayısı
  }

  // Kullanıcı ve takım limitleri
  maxUsers?: number;
  maxTeams?: number;

  // Plugin ayarları
  plugins?: {
    jira?: PluginSettings;
    xray?: PluginSettings;
    testrail?: PluginSettings;
  };

  // Eski ayarlar (geriye dönük uyumluluk için)
  customDomain?: string;
  logoUrl?: string;
  [key: string]: any;
}

/**
 * Şirket modeli
 */
export interface Company {
  _id?: ObjectId;
  id?: string;
  name: string;
  description?: string;
  status: CompanyStatus;
  settings?: CompanySettings;
  createdAt: Date;
  updatedAt?: Date;
  createdBy: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  website?: string;
  teamId?: string | null; // Tek bir takım ID'si
  aiModels?: AIModel[]; // Şirket AI modelleri
}

/**
 * Şirket oluşturma sonucu
 */
export interface CreateCompanyResult {
  success: boolean;
  companyId?: string;
  message?: string;
}

/**
 * Şirket getirme sonucu
 */
export interface GetCompanyResult {
  success: boolean;
  company?: Company;
  message?: string;
}

/**
 * Şirketleri getirme sonucu
 */
export interface GetCompaniesResult {
  success: boolean;
  companies?: Company[];
  total?: number;
  message?: string;
}

/**
 * Şirket silme sonucu
 */
export interface DeleteCompanyResult {
  success: boolean;
  message?: string;
}

/**
 * Şirket güncelleme sonucu
 */
export interface UpdateCompanyResult {
  success: boolean;
  company?: Company;
  message?: string;
}

/**
 * Sayfalama seçenekleri
 */
export interface CompanyPaginationOptions {
  limit?: number;
  skip?: number;
  includeInactive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
