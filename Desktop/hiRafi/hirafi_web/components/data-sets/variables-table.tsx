"use client"

import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Sparkles,
  Plus,
  Eye,
  EyeOff,
  Layers,
  ChevronDown,
  ChevronUp,
  Shuffle,
  Copy,
  Settings,
  Trash2,
  Lock,
  Globe,
  Loader2,
  Database,
} from "lucide-react"
import type { EnhancedDataVariable } from "@/types/data-generation"
import type { EnvironmentType, VariableType } from "@/types/test-data"
import type { DataEnvironment } from "@/store/testDataSetsStore"
import { VariableRow } from "./variable-row"
import { EnvironmentSelectionModal } from "./environment-selection-modal"

interface VariablesTableProps {
  variables: EnhancedDataVariable[]
  showValues: boolean
  onShowValuesChange: (show: boolean) => void
  activeEnvironment: string
  onEnvironmentChange: (envId: string) => void
  showEnvironmentSelector: boolean
  onShowEnvironmentSelectorChange: (show: boolean) => void
  onUpdateVariable: (id: string, field: keyof EnhancedDataVariable, value: any) => void
  onCopyValue: (value: string) => void
  onDuplicateVariable: (variable: EnhancedDataVariable) => void
  onRemoveVariable: (id: string) => void
  onEditConstraints: (variable: EnhancedDataVariable) => void
  onAddVariable: () => void
  onCopyAllAsJson: () => void
  userEnvironments: DataEnvironment[]
  isLoadingEnvironments: boolean
  onRandomizeData: (selectedEnvironmentIds: string[]) => void
  isGenerating: boolean
  onClearAll: () => void
  onImportFromSource: () => void
}









export function VariablesTable({
  variables,
  showValues,
  onShowValuesChange,
  activeEnvironment,
  onEnvironmentChange,
  showEnvironmentSelector,
  onShowEnvironmentSelectorChange,
  onUpdateVariable,
  onCopyValue,
  onDuplicateVariable,
  onRemoveVariable,
  onEditConstraints,
  onAddVariable,
  onCopyAllAsJson,
  userEnvironments,
  isLoadingEnvironments,
  onRandomizeData,
  isGenerating,
  onClearAll,
  onImportFromSource,
}: VariablesTableProps) {
  const [showRandomizeModal, setShowRandomizeModal] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Memoized environments list to prevent unnecessary re-renders
  const environments = useMemo(() => {
    if (!Array.isArray(userEnvironments)) return []
    return userEnvironments.map(env => ({
      id: env.id,
      name: env.name,
      icon: <Globe className={`h-4 w-4 text-${env.color}-500`} />
    }))
  }, [userEnvironments])





  // Get the current environment name
  const currentEnvironmentName = useMemo(() => {
    if (!isClient) return "Select environment"
    if (isLoadingEnvironments) return "Loading..."
    if (environments.length === 0) return "No environments"

    const foundEnv = environments.find((e) => e.id === activeEnvironment)
    if (foundEnv) {
      return foundEnv.name
    }

    // If no active environment is set but we have environments, show the first one
    if (environments.length > 0 && !activeEnvironment) {
      return environments[0].name
    }

    return "Select environment"
  }, [isClient, isLoadingEnvironments, environments, activeEnvironment])

  const handleRandomizeClick = () => {
    if (!variables || variables.length === 0) {
      return
    }
    setShowRandomizeModal(true)
  }

  const handleModalRandomize = (selectedEnvIds: string[]) => {
    setShowRandomizeModal(false)
    onRandomizeData(selectedEnvIds)
  }


  // Optimized environment change handler
  const handleEnvironmentClick = useCallback((envId: string) => {
    // Direct environment change without requestAnimationFrame
    onEnvironmentChange(envId)
    onShowEnvironmentSelectorChange(false)
  }, [onEnvironmentChange, onShowEnvironmentSelectorChange])

  // Memoized variable rows using optimized VariableRow component
  const variableRows = useMemo(() => {
    // Ensure variables is an array before mapping
    const safeVariables = Array.isArray(variables) ? variables : [];
    return safeVariables.map((variable) => (
      <VariableRow
        key={`${variable.id}-${activeEnvironment}`}
        variable={variable}
        showValues={showValues}
        onUpdateVariable={onUpdateVariable}
        onCopyValue={onCopyValue}
        onDuplicateVariable={onDuplicateVariable}
        onRemoveVariable={onRemoveVariable}
      />
    ))
  }, [
    variables,
    activeEnvironment,
    showValues,
    onUpdateVariable,
    onCopyValue,
    onDuplicateVariable,
    onRemoveVariable,
  ])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-indigo-50 to-violet-50 dark:from-indigo-950/30 dark:to-violet-950/30 border-b">
        <div>
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-indigo-500" />
            Generated Variables
          </CardTitle>
          <CardDescription>Edit and manage your test data variables</CardDescription>
        </div>
        <div className="flex items-center gap-3">
          {/* Clear All Button */}
          {(variables?.length ?? 0) > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearAll}
              disabled={isGenerating || (variables?.length ?? 0) === 0}
              className="h-8 gap-1 border-red-200 hover:border-red-300 hover:bg-red-50 dark:border-red-800 dark:hover:border-red-700 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
            >
              <Trash2 className="h-3.5 w-3.5" />
              <span>Clear All</span>
            </Button>
          )}

          {/* Randomize Values Button */}
          {(variables?.length ?? 0) > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRandomizeClick}
              disabled={isGenerating || (variables?.length ?? 0) === 0}
              className="h-8 gap-1 border-indigo-200 hover:border-indigo-300 hover:bg-indigo-50 dark:border-indigo-800 dark:hover:border-indigo-700 dark:hover:bg-indigo-900/30"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-3.5 w-3.5 animate-spin" />
                  <span>Randomizing...</span>
                </>
              ) : (
                <>
                  <Shuffle className="h-3.5 w-3.5" />
                  <span>Randomize Values</span>
                </>
              )}
            </Button>
          )}
          <div className="flex items-center space-x-2">
            <Switch id="show-values" checked={showValues} onCheckedChange={onShowValuesChange} />
            <Label htmlFor="show-values" className="text-sm">
              {showValues ? (
                <span className="flex items-center gap-1">
                  <Eye className="h-3.5 w-3.5" />
                  Show Values
                </span>
              ) : (
                <span className="flex items-center gap-1">
                  <EyeOff className="h-3.5 w-3.5" />
                  Hide Values
                </span>
              )}
            </Label>
          </div>

          <Popover open={showEnvironmentSelector} onOpenChange={onShowEnvironmentSelectorChange}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-8 gap-1 border-indigo-200 hover:border-indigo-300 hover:bg-indigo-50 dark:border-indigo-800 dark:hover:border-indigo-700 dark:hover:bg-indigo-900/30"
              >
                <Layers className="h-3.5 w-3.5" />
                <span>Environment: {currentEnvironmentName}</span>
                {showEnvironmentSelector ? (
                  <ChevronUp className="h-3.5 w-3.5 ml-1" />
                ) : (
                  <ChevronDown className="h-3.5 w-3.5 ml-1" />
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-0" align="end">
              <div className="p-2 border-b bg-gradient-to-r from-indigo-50 to-violet-50 dark:from-indigo-950/30 dark:to-violet-950/30">
                <h3 className="text-sm font-medium">Select Environment</h3>
                <p className="text-xs text-muted-foreground">View data for specific environment</p>
              </div>
              <div className="p-2">
                {isLoadingEnvironments ? (
                  <div className="text-center py-2 text-sm text-muted-foreground">
                    Loading environments...
                  </div>
                ) : environments.length === 0 ? (
                  <div className="text-center py-4 text-sm text-muted-foreground">
                    <p>No environments found</p>
                    <p className="text-xs mt-1">Create environments in Test Data settings</p>
                  </div>
                ) : (
                  environments.map((env) => (
                    <Button
                      key={env.id}
                      variant={activeEnvironment === env.id ? "secondary" : "ghost"}
                      className="w-full justify-start text-left mb-1 last:mb-0"
                      onClick={() => handleEnvironmentClick(env.id)}
                    >
                      <div className="flex items-center gap-2">
                        <span>{env.icon}</span>
                        <span>{env.name}</span>
                      </div>
                    </Button>
                  ))
                )}
              </div>

            </PopoverContent>
          </Popover>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {(variables?.length ?? 0) === 0 ? (
          <div className="text-center py-16 px-4 border-b border-dashed border-gray-200 dark:border-gray-800 bg-gray-50/50 dark:bg-gray-900/20">
            <div className="w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="h-8 w-8 text-indigo-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">No Variables Yet</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 mb-6 max-w-md mx-auto">
              Describe the data you want to generate using the AI prompt, then click "Generate with AI", or import from a data source, or add variables manually.
            </p>
            <div className="flex justify-center gap-3">
              <Button variant="outline" className="gap-1" onClick={onImportFromSource}>
                <Database className="h-4 w-4" />
                Import from Source
              </Button>
              <Button className="gap-1 bg-indigo-600 hover:bg-indigo-700 text-white" onClick={onAddVariable}>
                <Plus className="h-4 w-4" />
                Add Variable Manually
              </Button>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader className="bg-gray-50 dark:bg-gray-900/50">
                <TableRow>
                  <TableHead className="font-medium">Name</TableHead>
                  {showValues && <TableHead className="font-medium">Value</TableHead>}
                  <TableHead className="font-medium">Description</TableHead>
                  <TableHead className="text-right font-medium">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>{variableRows}</TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between py-4 px-6 bg-gray-50 dark:bg-gray-900/20 border-t">
        <div className="text-sm text-muted-foreground flex items-center">
          <Badge
            variant="outline"
            className="mr-2 bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-800"
          >
            {(variables?.length ?? 0)}
          </Badge>
          variable{(variables?.length ?? 0) !== 1 ? "s" : ""} defined
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="gap-1 border-indigo-200 hover:border-indigo-300 hover:bg-indigo-50 dark:border-indigo-800 dark:hover:border-indigo-700 dark:hover:bg-indigo-900/30"
            onClick={onCopyAllAsJson}
            disabled={(variables?.length ?? 0) === 0}
          >
            <Copy className="h-3.5 w-3.5" />
            Copy All Environments
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="gap-1 border-indigo-200 hover:border-indigo-300 hover:bg-indigo-50 dark:border-indigo-800 dark:hover:border-indigo-700 dark:hover:bg-indigo-900/30"
            onClick={onImportFromSource}
          >
            <Database className="h-3.5 w-3.5" />
            Import from Source
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="gap-1 border-indigo-200 hover:border-indigo-300 hover:bg-indigo-50 dark:border-indigo-800 dark:hover:border-indigo-700 dark:hover:bg-indigo-900/30"
            onClick={onAddVariable}
          >
            <Plus className="h-3.5 w-3.5" />
            Add Variable
          </Button>
        </div>
      </CardFooter>

      {/* Environment Selection Modal for Randomize */}
      <EnvironmentSelectionModal
        open={showRandomizeModal}
        onOpenChange={setShowRandomizeModal}
        environments={userEnvironments}
        selectedEnvironments={userEnvironments.map(env => env.id)} // Select all environments by default
        onGenerate={handleModalRandomize}
        isGenerating={isGenerating}
        title="Select Environments for Randomization"
        description="Choose which environments to randomize test data for. This will update values for existing variables in the selected environments."
        buttonText="Randomize Data"
      />
    </Card>
  )
}
