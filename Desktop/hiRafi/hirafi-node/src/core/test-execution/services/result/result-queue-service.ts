/**
 * Node-side Result Queue Service
 * Sends test results directly to Redis BullMQ result_queue instead of using WebSocket
 * This provides more reliable test result transmission
 */

import { Queue, QueueOptions } from 'bullmq';
import { EventEmitter } from 'events';
import { randomBytes } from 'crypto';
import { logger } from '../../../../utils/logger.js';
import { config } from '../../../../config/index.js';
import { REDIS_QUEUE_NAMES, REDIS_JOB_TYPES } from '../../../connectors/interfaces/redis-connector.interface.js';
import { TestResult } from '../../../../models/types.js';

export interface ResultQueueServiceEvents {
  'result:added': (testId: string, data: any) => void;
  'result:error': (testId: string, error: Error) => void;
}

/**
 * Node-side Result Queue Service
 * Handles direct transmission of test results to Redis BullMQ result_queue
 */
export class NodeResultQueueService extends EventEmitter {
  private static instance: NodeResultQueueService | null = null;
  private resultQueue: Queue | null = null;
  private isInitialized: boolean = false;

  constructor() {
    super();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): NodeResultQueueService {
    if (!NodeResultQueueService.instance) {
      NodeResultQueueService.instance = new NodeResultQueueService();
    }
    return NodeResultQueueService.instance;
  }

  /**
   * Initialize the result queue service
   */
  public async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) {
        logger.debug('NodeResultQueueService: Already initialized');
        return true;
      }

      if (!config.redisEnabled) {
        logger.error('NodeResultQueueService: Redis is not enabled, cannot initialize result queue service');
        return false;
      }

      // Create Redis connection options for BullMQ
      const connectionOptions = {
        host: config.redis?.host || 'localhost',
        port: config.redis?.port || 6379,
        password: config.redis?.password || undefined,
        db: config.redis?.db || 0,
        maxRetriesPerRequest: null, // Required for BullMQ
      };

      // Create result queue
      this.resultQueue = new Queue(REDIS_QUEUE_NAMES.RESULT_QUEUE, {
        connection: connectionOptions,
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 100,
          attempts: 3,
          backoff: {
            type: 'fixed',
            delay: 500
          }
        }
      });

      // Set up error handling
      this.resultQueue.on('error', (error) => {
        logger.error(`NodeResultQueueService: Queue error: ${error}`);
      });

      this.isInitialized = true;
      logger.info('NodeResultQueueService: Initialized successfully');
      return true;
    } catch (error) {
      logger.error(`NodeResultQueueService: Initialization error: ${error}`);
      return false;
    }
  }

  /**
   * Add a test result to the queue
   * @param testId Test ID
   * @param result Test result data
   * @param nodeId Node ID that processed the test
   * @returns Promise resolving to the job ID if successful, null otherwise
   */
  public async addResult(testId: string, result: TestResult, nodeId: string): Promise<string | null> {
    try {
      if (!this.isInitialized || !this.resultQueue) {
        logger.error('NodeResultQueueService: Not initialized, cannot add result');
        return null;
      }

      // Generate transaction ID if not provided
      let finalTransactionId = result.transactionId;
      let idSource = 'provided';

      if (!finalTransactionId) {
        finalTransactionId = `node-result-${Date.now()}-${randomBytes(3).toString('hex')}`;
        idSource = 'generated';
        logger.debug(`NodeResultQueueService: No transactionId provided for test ${testId}, executionId: ${result.executionId || 'N/A'}. Generated new one: ${finalTransactionId}`);
      } else {
        logger.debug(`NodeResultQueueService: Using provided transactionId for test ${testId}, executionId: ${result.executionId || 'N/A'}. Provided ID: ${finalTransactionId}`);
      }

      // Prepare result data in the format expected by hub's ResultQueueWorker
      const resultData = {
        testId: testId,
        nodeId: nodeId,
        status: result.status || 'unknown',
        result: result,
        timestamp: Date.now(),
        source: 'node-redis-queue',
        runId: result.runId,
        executionId: result.executionId,
        scenarioId: result.scenarioId,
        executedUser: result.executedUser,
        executedUserName: result.executedUserName,
        teamId: result.teamId,
        companyId: result.companyId,
        transactionId: finalTransactionId
      };

      logger.info(`NodeResultQueueService: Adding result to queue for test ${testId}. TransactionId (${idSource}): ${finalTransactionId}, executionId: ${result.executionId || 'N/A'}`);

      // Add job to queue with high priority
      const job = await this.resultQueue.add(
        REDIS_JOB_TYPES.TEST_RESULT,
        resultData,
        {
          // HIGH PRIORITY: Use priority to ensure test results are processed immediately
          priority: 10, // Higher priority for faster processing
          attempts: 3,
          backoff: {
            type: 'fixed',
            delay: 500 // Reduced delay for faster retries
          },
          removeOnComplete: 50,
          removeOnFail: 100,
          // Add delay: 0 to process immediately
          delay: 0
        }
      );

      if (job) {
        logger.info(`NodeResultQueueService: Added result for test ${testId} to queue with transactionId ${finalTransactionId}, job ID: ${job.id}`);
        this.emit('result:added', testId, resultData);
        return job.id || null;
      } else {
        logger.error(`NodeResultQueueService: Failed to add result for test ${testId} to queue`);
        return null;
      }
    } catch (error) {
      logger.error(`NodeResultQueueService: Error adding result for test ${testId} to queue: ${error}`);
      this.emit('result:error', testId, error as Error);
      return null;
    }
  }

  /**
   * Get queue statistics
   */
  public async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    try {
      if (!this.isInitialized || !this.resultQueue) {
        return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
      }

      const waiting = await this.resultQueue.getWaiting();
      const active = await this.resultQueue.getActive();
      const completed = await this.resultQueue.getCompleted();
      const failed = await this.resultQueue.getFailed();
      const delayed = await this.resultQueue.getDelayed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length
      };
    } catch (error) {
      logger.error(`NodeResultQueueService: Error getting queue stats: ${error}`);
      return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
    }
  }

  /**
   * Check if the service is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.resultQueue !== null;
  }

  /**
   * Close the result queue service
   */
  public async close(): Promise<void> {
    try {
      if (this.resultQueue) {
        await this.resultQueue.close();
        this.resultQueue = null;
      }
      this.isInitialized = false;
      logger.info('NodeResultQueueService: Closed');
    } catch (error) {
      logger.error(`NodeResultQueueService: Error closing: ${error}`);
    }
  }
}

// Export singleton instance
export const nodeResultQueueService = NodeResultQueueService.getInstance();
export default nodeResultQueueService;
