/**
 * Node Registry
 * Manages node registration and status
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { TestNode } from '../../models/test-types.js';
import { config } from '../../config/index.js';
// Redis queue imports removed - using WebSocket-only registration

export interface NodeRegistryEvents {
  'node:registered': (nodeId: string, node: TestNode) => void;
  'node:unregistered': (nodeId: string) => void;
  'node:statusChanged': (nodeId: string, status: string, previousStatus: string) => void;
  'node:error': (nodeId: string, error: Error) => void;
  'node:testOrphaned': (nodeId: string, testId: string) => void;
  'node:testClaimProcessed': (nodeId: string, testId: string, success: boolean) => void;
  'node:testCompleted': (nodeId: string, testId: string, result: any) => void;
}

/**
 * Service for managing node registration and status
 */
class NodeRegistry extends EventEmitter {
  private static instance: NodeRegistry;
  private nodes: Map<string, TestNode> = new Map();
  private isInitialized: boolean = false;
  private nodeHealthCheckInterval: NodeJS.Timeout | null = null;
  private activeNodesLogInterval: NodeJS.Timeout | null = null;

  // Prevent duplicate registrations - SINGLE TRUTH SOURCE
  private registrationInProgress: Set<string> = new Set();
  private registrationTimestamps: Map<string, number> = new Map();
  private readonly REGISTRATION_COOLDOWN = 5000; // 5 seconds cooldown between registrations

  private constructor() {
    super();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): NodeRegistry {
    if (!NodeRegistry.instance) {
      NodeRegistry.instance = new NodeRegistry();
    }
    return NodeRegistry.instance;
  }

  /**
   * Initialize the node registry
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Start periodic health check using configured interval
      this.nodeHealthCheckInterval = setInterval(() => this.checkNodeHealth(), config.node.healthCheckInterval);

      // Start active nodes logging using configured interval
      this.activeNodesLogInterval = setInterval(() => this.logActiveNodes(), config.node.logInterval);

      // Redis queue workers removed - using WebSocket-only registration

      // Set up WebSocket disconnection event listener for immediate node removal
      this.setupWebSocketEventListeners();

      this.isInitialized = true;
      logger.info('NodeRegistry: Initialized successfully');
      return true;
    } catch (error) {
      logger.error(`NodeRegistry: Initialization error: ${error}`);
      return false;
    }
  }

  // Redis queue workers removed - using WebSocket-only registration

  /**
   * Set up WebSocket event listeners for immediate node disconnection handling
   * NOTE: Direct WebSocket event handling has been moved to centralized EventHandler
   * This method is kept for backward compatibility but events are now handled centrally
   */
  private setupWebSocketEventListeners(): void {
    logger.info('NodeRegistry: WebSocket event handling moved to centralized EventHandler');
    // Events are now handled through the centralized EventHandler
    // The EventHandler will call the appropriate methods on NodeRegistry when needed
  }

  /**
   * Handle immediate node disconnection (called when WebSocket disconnects)
   * 
   * RESPONSIBILITY: Registry-specific cleanup only
   * - Node state management (status, test assignments)
   * - Registration state cleanup
   * - Event emission for downstream services
   * 
   * NOTE: WebSocket cleanup is already handled by WebSocketConnector
   * This method should NOT attempt any WebSocket-related cleanup operations.
   * 
   * Made public so EventHandler can call it
   * @param nodeId Node ID that disconnected
   * @param disconnectionType Type of disconnection
   * @param code WebSocket close code
   * @param reason Disconnection reason
   */
  public handleImmediateNodeDisconnection(nodeId: string, disconnectionType: string, code: number, reason: string): void {
    try {
      const node = this.nodes.get(nodeId);
      if (!node) {
        logger.debug(`NodeRegistry: Node ${nodeId} not found in registry during disconnection handling`);
        return;
      }

      logger.info(`NodeRegistry: Handling immediate disconnection for node ${nodeId} (${node.name}) - Type: ${disconnectionType}, Code: ${code}, Reason: ${reason || 'none'}`);

      // For abnormal disconnections, mark the node as inactive immediately
      // For normal disconnections, remove the node immediately
      if (disconnectionType === 'abnormal' || code === 1006) {
        // Mark as inactive for abnormal disconnections - will be removed by health check if not reconnected
        const previousStatus = node.status;
        node.status = 'inactive';
        node.lastSeen = new Date(); // Update lastSeen to current time for proper timeout calculation

        logger.warn(`NodeRegistry: Node ${nodeId} marked as inactive due to abnormal disconnection (code: ${code})`);
        this.emit('node:statusChanged', nodeId, 'inactive', previousStatus);
      } else {
        // For normal disconnections, remove immediately
        logger.info(`NodeRegistry: Removing node ${nodeId} due to normal disconnection`);
        this.removeDisconnectedNode(nodeId);
      }
    } catch (error) {
      logger.error(`NodeRegistry: Error handling immediate node disconnection for ${nodeId}: ${error}`);
    }
  }

  /**
   * Register a node - SINGLE TRUTH SOURCE WITH DUPLICATE PREVENTION
   * @param nodeData Node data
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async registerNode(nodeData: any): Promise<boolean> {
    try {
      const { id, name, capabilities, vncUrl, vncPort, version, clientId, status: initialStatus } = nodeData;

      if (!id) {
        logger.error('NodeRegistry: Cannot register node without ID');
        return false;
      }

      // Prevent duplicate registrations
      /*
      if (this.registrationInProgress.has(id)) {
        logger.warn(`NodeRegistry: Registration already in progress for node ${id}, ignoring duplicate`);
        return false;
      }
      */

      // Check registration cooldown - but allow re-registration if clientId matches
      /*
      const lastRegistration = this.registrationTimestamps.get(id);
      const now = Date.now();
      if (lastRegistration && (now - lastRegistration) < this.REGISTRATION_COOLDOWN) {
        // Allow re-registration if this is the same client (by clientId)
        const existingNode = this.nodes.get(id);
        if (!existingNode || existingNode.clientId !== clientId) {
          logger.warn(`NodeRegistry: Registration cooldown active for node ${id}, ignoring duplicate`);
          return false;
        }
        logger.info(`NodeRegistry: Allowing re-registration for node ${id} with matching clientId ${clientId}`);
      }
      */

      // Mark registration as in progress
      this.registrationInProgress.add(id);
      this.registrationTimestamps.set(id, Date.now());

      try {
        // Check if the node is already registered
        const existingNode = this.nodes.get(id);
        if (existingNode) {
          // Update the existing node with complete property set
          existingNode.name = name || existingNode.name;
          existingNode.capabilities = capabilities || existingNode.capabilities;
          existingNode.vncUrl = vncUrl || existingNode.vncUrl;
          existingNode.vncPort = vncPort || existingNode.vncPort;
          existingNode.version = version || existingNode.version;
          existingNode.lastSeen = new Date();
          existingNode.status = initialStatus || existingNode.status || 'available'; // Update status if provided, else keep existing
          existingNode.clientId = clientId || existingNode.clientId;
          // existingNode.lastHeartbeat should be updated by heartbeat mechanism, not registration

          logger.info(`NodeRegistry: Node ${id} re-registered/updated. Current status: ${existingNode.status}`);
          this.emit('node:statusChanged', id, existingNode.status, existingNode.status); // Emitting status change even if same for heartbeat purposes
        } else {
          const newNode: TestNode = {
          id,
            name: name || `Node ${id.substring(0, 6)}`,
            status: initialStatus || 'available', // Default to available
          capabilities: capabilities || [],
            lastHeartbeat: new Date(), // Initialize lastHeartbeat for new node
            lastSeen: new Date(),
          currentTestId: null,
            vncUrl: vncUrl || undefined,
            vncPort: vncPort || undefined,
            version: version || undefined,
            clientId: clientId || undefined,
            registrationTime: new Date(),
            metrics: { totalTestsProcessed: 0, totalFailures: 0, totalSuccesses: 0, averageProcessingTime: 0 }
          };
        this.nodes.set(id, newNode);
          logger.info(`NodeRegistry: Registered new node ${id} (${newNode.name}) with status ${newNode.status})`);
        this.emit('node:registered', id, newNode);
        }

        // Return true for successful registration or update
        return true;
      } catch (error) {
        logger.error(`NodeRegistry: Error during node registration/update for ${id}: ${error}`);
        return false;
      } finally {
        this.clearRegistrationState(id);
      }
    } catch (error) {
      // This outer catch is for errors before registrationInProgress is set (e.g., missing id)
      logger.error(`NodeRegistry: Pre-registration error: ${error}`);
      return false;
    }
  }

  /**
   * Unregister a node
   * @param nodeId Node ID
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async unregisterNode(nodeId: string): Promise<boolean> {
    try {
      // Check if the node is registered
      if (!this.nodes.has(nodeId)) {
        logger.warn(`NodeRegistry: Node ${nodeId} not found, cannot unregister`);
        return false;
      }

      // Use the same cleanup logic as disconnected nodes
      await this.removeDisconnectedNode(nodeId);
      return true;
    } catch (error) {
      logger.error(`NodeRegistry: Error unregistering node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Remove a node from the registry (public method for manual removal)
   * @param nodeId Node ID
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async removeNode(nodeId: string): Promise<boolean> {
    try {
      // Check if the node is registered
      if (!this.nodes.has(nodeId)) {
        logger.warn(`NodeRegistry: Node ${nodeId} not found, cannot remove`);
        return false;
      }

      logger.info(`NodeRegistry: Manually removing node ${nodeId}`);
      await this.removeDisconnectedNode(nodeId);
      return true;
    } catch (error) {
      logger.error(`NodeRegistry: Error removing node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Mark a node as busy with a test
   * @param nodeId Node ID
   * @param testId Test ID
   * @param source Optional source of the call for logging (e.g., 'admin-api', 'event-handler')
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async markNodeAsBusy(nodeId: string, testId: string, source?: string): Promise<boolean> {
    // Only warn if this is not from an administrative source
    if (!source || !['admin-api', 'event-handler', 'internal'].includes(source)) {
      logger.warn(`NodeRegistry: markNodeAsBusy(${nodeId}, ${testId}) called directly. This should ideally be triggered by a node's successful claim event.`);
    } else {
      logger.debug(`NodeRegistry: markNodeAsBusy(${nodeId}, ${testId}) called from ${source}`);
    }

    const node = this.nodes.get(nodeId);

    if (!node) {
      logger.warn(`NodeRegistry: Node ${nodeId} not found, cannot mark as busy`);
      return false;
    }

    // Update the node status
    const previousStatus = node.status;
    node.status = 'busy';
    node.currentTestId = testId;
    node.lastSeen = new Date();

    logger.info(`NodeRegistry: Marked node ${nodeId} as busy with test ${testId}`);
    this.emit('node:statusChanged', nodeId, 'busy', previousStatus);
    return true;
  }

  /**
   * Mark a node as available
   * @param nodeId Node ID
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async markNodeAsAvailable(nodeId: string): Promise<boolean> {
    // This method is now primarily for internal calls or specific hub-driven scenarios.
    // The primary way a node becomes available in the registry should be via the 'node:testReleased' event.
    logger.warn(`NodeRegistry: markNodeAsAvailable(${nodeId}) called directly. This should ideally be triggered by a node's test release/completion event.`);
      const node = this.nodes.get(nodeId);

      if (!node) {
        logger.warn(`NodeRegistry: Node ${nodeId} not found, cannot mark as available`);
        return false;
      }

      // Update the node status
      const previousStatus = node.status;
      node.status = 'available';
      node.currentTestId = null;
      node.lastSeen = new Date();

      logger.info(`NodeRegistry: Marked node ${nodeId} as available`);
      this.emit('node:statusChanged', nodeId, 'available', previousStatus);
      return true;
  }

  /**
   * Get a node by ID
   * @param nodeId Node ID
   * @returns Node if found, null otherwise
   */
  public getNode(nodeId: string): TestNode | null {
    return this.nodes.get(nodeId) || null;
  }

  /**
   * Update a node's heartbeat timestamp
   * @param nodeId Node ID
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async updateNodeHeartbeat(nodeId: string): Promise<boolean> {
    try {
      // Check if the node is registered
      const node = this.nodes.get(nodeId);
      if (!node) {
        logger.warn(`NodeRegistry: Node ${nodeId} not found, cannot update heartbeat`);
        return false;
      }

      // Update the node's last seen and heartbeat timestamps
      node.lastSeen = new Date();
      node.lastHeartbeat = new Date();

      // Log at debug level to avoid flooding logs
      logger.debug(`NodeRegistry: Updated heartbeat for node ${nodeId}`);
      return true;
    } catch (error) {
      logger.error(`NodeRegistry: Error updating heartbeat for node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Get all nodes
   * @returns Map of all nodes
   */
  public getAllNodes(): Map<string, TestNode> {
    return new Map(this.nodes);
  }

  /**
   * Get all available nodes
   * @returns Array of available nodes
   */
  public getAvailableNodes(): TestNode[] {
    return Array.from(this.nodes.values()).filter(node => node.status === 'available');
  }

  /**
   * Find an available node with the required capabilities
   * @param requiredCapabilities Array of required capabilities
   * @param platform Platform type ('web' or 'android')
   * @returns Node if found, null otherwise
   */
  public findAvailableNode(requiredCapabilities: string[] = [], platform: 'web' | 'android' = 'web'): TestNode | null {
    // Get all available nodes
    const availableNodes = this.getAvailableNodes();

    if (availableNodes.length === 0) {
      logger.warn('NodeRegistry: No available nodes found');
      return null;
    }

    // Add platform as a required capability
    const allRequiredCapabilities = [...requiredCapabilities];
    if (platform) {
      allRequiredCapabilities.push(platform);
    }

    // If no specific capabilities required, return any available node
    if (!allRequiredCapabilities || allRequiredCapabilities.length === 0) {
      logger.info(`NodeRegistry: No specific capabilities required, returning first available node: ${availableNodes[0].id}`);
      return availableNodes[0];
    }

    // Find nodes that have all required capabilities
    const matchingNodes = availableNodes.filter(node => {
      // Check if the node has all required capabilities
      return allRequiredCapabilities.every(capability =>
        node.capabilities.includes(capability)
      );
    });

    if (matchingNodes.length === 0) {
      logger.warn(`NodeRegistry: No nodes found with all required capabilities: ${allRequiredCapabilities.join(', ')}`);
      return null;
    }

    // Return the first matching node

    return matchingNodes[0];
  }

  /**
   * Check node health
   * This is called periodically to check if nodes are still alive
   * Enhanced with stuck BUSY state detection and recovery
   */
  private checkNodeHealth(): void {
    const now = new Date();
    const inactiveThreshold = config.node.inactiveTimeout; // Use configured inactive timeout
    const removalThreshold = config.node.removalTimeout; // Use configured removal timeout
    const busyTimeoutThreshold = 3 * 60 * 1000; // 3 minutes - timeout for BUSY nodes without heartbeat

    const nodesToRemove: string[] = [];
    const stuckBusyNodes: string[] = [];

    for (const [nodeId, node] of this.nodes.entries()) {
      // Use lastHeartbeat if lastSeen is not available
      const lastActivity = node.lastSeen || node.lastHeartbeat;

      if (!lastActivity) {
        logger.warn(`NodeRegistry: Node ${nodeId} has no lastSeen or lastHeartbeat timestamp, removing from registry`);
        nodesToRemove.push(nodeId);
        continue;
      }

      const timeSinceLastSeen = now.getTime() - lastActivity.getTime();

      // CRITICAL FIX: Detect stuck BUSY nodes
      if (node.status === 'busy' && timeSinceLastSeen > busyTimeoutThreshold) {
        logger.error(`NodeRegistry: Node ${nodeId} has been BUSY for ${Math.round(timeSinceLastSeen / 1000)}s without heartbeat - marking as stuck`);
        stuckBusyNodes.push(nodeId);

        // If node has been unresponsive for too long, mark as inactive and release test
        if (timeSinceLastSeen > inactiveThreshold) {
          const previousStatus = node.status;
          node.status = 'inactive';

          // Release the test if node was processing one
          if (node.currentTestId) {
            logger.error(`NodeRegistry: Releasing stuck test ${node.currentTestId} from unresponsive node ${nodeId}`);
            this.emit('node:testStuck', nodeId, node.currentTestId, {
              reason: 'Node unresponsive during test execution',
              timeSinceLastSeen: timeSinceLastSeen,
              lastActivity: lastActivity
            });
            node.currentTestId = null;
          }

          this.emit('node:statusChanged', nodeId, 'inactive', previousStatus);
        }
        continue;
      }

      // Remove nodes that have been disconnected for more than the removal threshold
      if (timeSinceLastSeen > removalThreshold) {
        logger.warn(`NodeRegistry: Node ${nodeId} has not been seen for ${Math.round(timeSinceLastSeen / 1000)}s, removing from registry`);

        // If removing a busy node, emit stuck test event
        if (node.status === 'busy' && node.currentTestId) {
          logger.error(`NodeRegistry: Removing busy node ${nodeId} with active test ${node.currentTestId}`);
          this.emit('node:testStuck', nodeId, node.currentTestId, {
            reason: 'Node removed due to prolonged inactivity',
            timeSinceLastSeen: timeSinceLastSeen,
            lastActivity: lastActivity
          });
        }

        nodesToRemove.push(nodeId);
      }
      // Mark nodes as inactive if they haven't been seen for the inactive threshold
      else if (timeSinceLastSeen > inactiveThreshold && node.status !== 'inactive') {
        logger.warn(`NodeRegistry: Node ${nodeId} has not been seen for ${Math.round(timeSinceLastSeen / 1000)}s, marking as inactive`);

        // Mark the node as inactive
        const previousStatus = node.status;
        node.status = 'inactive';

        // If node was busy with a test, emit stuck test event
        if (previousStatus === 'busy' && node.currentTestId) {
          logger.error(`NodeRegistry: Node ${nodeId} marked inactive while processing test ${node.currentTestId}`);
          this.emit('node:testStuck', nodeId, node.currentTestId, {
            reason: 'Node marked inactive during test execution',
            timeSinceLastSeen: timeSinceLastSeen,
            lastActivity: lastActivity
          });
          node.currentTestId = null;
        }

        this.emit('node:statusChanged', nodeId, 'inactive', previousStatus);
      }
    }

    // Log stuck BUSY nodes for monitoring
    if (stuckBusyNodes.length > 0) {
      logger.warn(`NodeRegistry: Detected ${stuckBusyNodes.length} stuck BUSY nodes: ${stuckBusyNodes.join(', ')}`);
    }

    // Remove disconnected nodes
    for (const nodeId of nodesToRemove) {
      this.removeDisconnectedNode(nodeId);
    }
  }

  /**
   * Remove a disconnected node from the registry
   * This method handles cleanup of all associated resources
   * @param nodeId Node ID to remove
   */
  private async removeDisconnectedNode(nodeId: string): Promise<void> {
    try {
      const node = this.nodes.get(nodeId);
      if (!node) {
        logger.warn(`NodeRegistry: Cannot remove node ${nodeId} - not found in registry`);
        return;
      }

      // Log the removal with node details
      logger.info(`NodeRegistry: Removing disconnected node ${nodeId} (${node.name}) - Last seen: ${node.lastSeen ? node.lastSeen.toISOString() : 'never'}`);

      // If the node was running a test, we should handle that
      if (node.currentTestId) {
        logger.warn(`NodeRegistry: Node ${nodeId} was running test ${node.currentTestId} when disconnected`);
        // Emit an event so other services can handle the orphaned test
        this.emit('node:testOrphaned', nodeId, node.currentTestId);
      }

      // Clear registration state to allow clean reconnection
      this.clearRegistrationState(nodeId);

      // Remove the node from the registry
      this.nodes.delete(nodeId);

      // NOTE: WebSocket cleanup is handled by WebSocketConnector when the connection closes
      // No need to call webSocketConnector.disconnectClient() here as it creates duplicate cleanup
      // and results in "Cannot disconnect client, not found" warnings

      // Emit unregistered event
      this.emit('node:unregistered', nodeId);

    } catch (error) {
      logger.error(`NodeRegistry: Error removing disconnected node ${nodeId}: ${error}`);
    }
  }

  /**
   * Clear registration state for a node to allow clean reconnection
   * @param nodeId Node ID to clear state for
   */
  private clearRegistrationState(nodeId: string): void {
    try {
      // Remove from in-progress registrations
      this.registrationInProgress.delete(nodeId);

      // Clear registration timestamp to allow immediate re-registration
      this.registrationTimestamps.delete(nodeId);

      logger.debug(`NodeRegistry: Cleared registration state for node ${nodeId}`);
    } catch (error) {
      logger.error(`NodeRegistry: Error clearing registration state for node ${nodeId}: ${error}`);
    }
  }

  /**
   * Get node statistics
   * @returns Object containing node statistics
   */
  public getNodeStatistics() {
    const totalNodes = this.nodes.size;
    const availableNodes = Array.from(this.nodes.values()).filter(node => node.status === 'available').length;
    const busyNodes = Array.from(this.nodes.values()).filter(node => node.status === 'busy').length;
    const inactiveNodes = Array.from(this.nodes.values()).filter(node => node.status === 'inactive').length;

    return {
      total: totalNodes,
      available: availableNodes,
      busy: busyNodes,
      inactive: inactiveNodes,
      timestamp: new Date()
    };
  }

  /**
   * Log active nodes
   * This is called periodically to log the number of active nodes
   */
  private logActiveNodes(): void {
    const stats = this.getNodeStatistics();
    logger.info(`NodeRegistry: Active Nodes Summary - Total: ${stats.total} | Available: ${stats.available} | Busy: ${stats.busy} | Inactive: ${stats.inactive}`);

    // Detailed dump of all nodes
    if (this.nodes.size > 0) {
      let detailedLog = 'NodeRegistry: Detailed Node List -\n';
      this.nodes.forEach(node => {
        detailedLog += `  Node ID: ${node.id}, Name: ${node.name}, Status: ${node.status}, LastSeen: ${node.lastSeen ? node.lastSeen.toISOString() : 'N/A'}, Capabilities: ${node.capabilities.join(',')}, CurrentTestID: ${node.currentTestId || 'None'}\n`;
      });
      logger.info(detailedLog);
    } else {
      logger.info('NodeRegistry: Detailed Node List - No nodes currently in registry.');
    }
  }

  /**
   * Send a stop test command to a node
   * @param nodeId Node ID
   * @param testId Test ID to stop
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async stopTestOnNode(nodeId: string, testId: string): Promise<boolean> {
    try {
      // Check if the node is registered
      const node = this.getNode(nodeId);
      if (!node) {
        logger.warn(`NodeRegistry: Node ${nodeId} not found, cannot stop test ${testId}`);
        return false;
      }

      // Check if the node is running this test
      if (node.currentTestId !== testId) {
        logger.warn(`NodeRegistry: Node ${nodeId} is not running test ${testId} (current test: ${node.currentTestId || 'none'})`);
        // We'll still try to send the stop command in case the node registry is out of sync
      }

      // Use WebSocketConnector to send the stop command
      // This is imported from the connectors module
      const webSocketConnector = (await import('../../connectors/index.js')).webSocketConnector;
      const success = webSocketConnector.sendStopTestToNode(nodeId, testId);

      if (success) {
        logger.info(`NodeRegistry: Sent stop command for test ${testId} to node ${nodeId}`);
        return true;
      } else {
        logger.error(`NodeRegistry: Failed to send stop command for test ${testId} to node ${nodeId}`);
        return false;
      }
    } catch (error) {
      logger.error(`NodeRegistry: Error stopping test ${testId} on node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Close the node registry
   */
  public async close(): Promise<void> {
    if (this.nodeHealthCheckInterval) {
      clearInterval(this.nodeHealthCheckInterval);
      this.nodeHealthCheckInterval = null;
    }

    if (this.activeNodesLogInterval) {
      clearInterval(this.activeNodesLogInterval);
      this.activeNodesLogInterval = null;
    }

    this.isInitialized = false;
    logger.info('NodeRegistry: Closed');
  }

  // Method to handle when a node successfully claims a test
  // Made public so EventHandler can call it
  public handleNodeClaimedTest(nodeId: string, testId: string): void {
    const node = this.nodes.get(nodeId);
    if (node) {
      const previousStatus = node.status;
      node.status = 'busy';
      node.currentTestId = testId;
      node.lastSeen = new Date();
      this.nodes.set(nodeId, node);
      logger.info(`NodeRegistry: Node ${nodeId} marked as BUSY with test ${testId} (via event).`);
      this.emit('node:statusChanged', nodeId, 'busy', previousStatus);
    } else {
      logger.warn(`NodeRegistry: Received testClaimed event for unknown or removed node ${nodeId}.`);
    }
  }

  // Method to handle when a node releases a test (completed, failed, stopped)
  // Made public so EventHandler can call it
  public handleNodeReleasedTest(nodeId: string, testId: string): void {
    const node = this.nodes.get(nodeId);
    if (node) {
      // Only mark as available if this is the test it was busy with
      if (node.currentTestId === testId) {
        const previousStatus = node.status;
        node.status = 'available';
        node.currentTestId = null;
        node.lastSeen = new Date();
        // Potentially update metrics here if applicable
        if (node.metrics && previousStatus === 'busy') {
            node.metrics.totalTestsProcessed = (node.metrics.totalTestsProcessed || 0) + 1;
        }

        this.nodes.set(nodeId, node);
        logger.info(`NodeRegistry: Node ${nodeId} marked as AVAILABLE as it released test ${testId} (via event).`);
        this.emit('node:statusChanged', nodeId, 'available', previousStatus);
      } else if (node.currentTestId) {
        logger.warn(`NodeRegistry: Node ${nodeId} released test ${testId}, but it was marked busy with ${node.currentTestId}. Status not changed to available based on this event alone for safety.`);
      } else {
        // Node was already available or in another state, ensure it's seen
        node.lastSeen = new Date();
        this.nodes.set(nodeId, node);
        logger.info(`NodeRegistry: Node ${nodeId} released test ${testId}, but was not marked as busy with it. Updated lastSeen.`);
      }
    } else {
      logger.warn(`NodeRegistry: Received testReleased event for unknown or removed node ${nodeId}.`);
    }
  }
}

// Export singleton instance
export const nodeRegistry = NodeRegistry.getInstance();
export default nodeRegistry;
