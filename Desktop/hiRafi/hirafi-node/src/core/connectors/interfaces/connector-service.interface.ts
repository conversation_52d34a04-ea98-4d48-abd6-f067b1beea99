/**
 * Connector Service Interfaces
 * Defines interfaces for connector-related services
 */

/**
 * Base service interface that all connector services must implement
 */
export interface IConnectorService {
  /**
   * Initialize the service
   * @param config Service configuration
   */
  initialize(config?: any): Promise<void>;

  /**
   * Close the service and cleanup resources
   */
  close(): Promise<void>;

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean;

  /**
   * Get service statistics
   */
  getStats(): Record<string, any>;

  /**
   * Get service name
   */
  getServiceName(): string;
}

/**
 * Service configuration interface
 */
export interface IServiceConfig {
  enableLogging?: boolean;
  hubUrl?: string;
  secretKey?: string;
  nodeId?: string;
  [key: string]: any;
}

/**
 * Service statistics interface
 */
export interface IServiceStats {
  serviceName: string;
  isInitialized: boolean;
  createdAt: string;
  uptime: number;
  operationCount: number;
  errorCount: number;
  lastOperation?: string;
  lastError?: string;
}

/**
 * Service lifecycle interface
 */
export interface IServiceLifecycle {
  /**
   * Called before service initialization
   */
  beforeInitialize?(): Promise<void>;

  /**
   * Called after service initialization
   */
  afterInitialize?(): Promise<void>;

  /**
   * Called before service closure
   */
  beforeClose?(): Promise<void>;

  /**
   * Called after service closure
   */
  afterClose?(): Promise<void>;
}

/**
 * Service dependency interface
 */
export interface IServiceDependency {
  name: string;
  required: boolean;
  version?: string;
}

/**
 * Service health interface
 */
export interface IServiceHealth {
  /**
   * Check service health
   */
  checkHealth(): Promise<boolean>;

  /**
   * Get detailed health information
   */
  getHealthDetails(): Promise<Record<string, any>>;
}

/**
 * Service event types
 */
export enum ServiceEventType {
  INITIALIZED = 'initialized',
  CLOSED = 'closed',
  ERROR = 'error',
  OPERATION_COMPLETED = 'operationCompleted',
  HEALTH_CHECK = 'healthCheck'
}
