/**
 * Type Definitions
 * Centralizes all type definitions used throughout the application
 */

// ------------------------------
// Test and Scenario Definitions
// ------------------------------

// Basic test statuses
export type TestStatus = 'queued' | 'running' | 'completed' | 'failed' | 'stopped';

/**
 * Test Status Enum
 * Standardized test status values for consistent communication
 */
export enum TestStatusEnum {
  PENDING = 'pending',
  QUEUED = 'queued',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  STOPPED = 'stopped',
  ABORTED = 'aborted'
}

// Test Request Data
export interface TestRequest {
  id: string;
  scenarioId: string;
  scenario: any;
  scenarioData?: any; // Test hub'dan gelen senaryo verisi
  scenarioName?: string;
  status: TestStatus;
  nodeId?: string;
  runId?: string; // Run identifier - which run this test belongs to
  executionId?: string; // Execution identifier - specific execution of a run
  userId?: string; // Kullanıcı kimliği - hangi kullanıcıya ait olduğunu belirtir
  executedUser?: string; // Testi çalıştıran kullanıcı kimliği (userId ile aynı olabilir)
  executedUserName?: string; // Testi çalıştıran kullanıcının adı
  teamId?: string | null; // Team ID for report attribution
  companyId?: string | null; // Company ID for report attribution
  deviceProvider?: 'sauceLabs' | 'testinium'; // Mutually exclusive device provider selection
  platform?: 'web' | 'android'; // Platform to run the test on (web or android)
  queuedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  reportId?: string;
  priority: number;
  fileConfig?: FileConfig;
  environmentSettings?: EnvironmentSettings;
  reportSettings?: ReportSettings;
  options?: {
    sequentialRun?: boolean;
    [key: string]: any;
  }; // Ek seçenekler
  totalSteps?: number; // Total number of steps in the scenario
  steps?: ScenarioStep[]; // Steps to execute
  recordVideo?: boolean; // Whether to record video

  // Internal properties for lock management
  _cleanupLock?: () => Promise<void>; // Function to clean up Redis lock
  _lockInfo?: any; // Lock information
}

export interface FileConfig {
  saveToTemp: boolean;
  tempDir: string;
  filename: string;
  deleteAfterExecution: boolean;
}

// Network Collector Memory Management Configuration
export interface NetworkCollectorConfig {
  // Memory management settings
  maxInMemoryEntries: number;        // Maximum entries to keep in memory (default: 1000)
  enableTemporaryStorage: boolean;   // Whether to use temporary file storage (default: true)
  tempFilePrefix: string;            // Prefix for temporary files (default: 'network-data')
  cleanupOnDestroy: boolean;         // Whether to cleanup temp files on destroy (default: true)

  // Performance settings
  flushThreshold: number;            // When to flush to temp storage (default: 800)
  compressionEnabled: boolean;       // Whether to compress temp files (default: true)

  // Monitoring settings
  memoryWarningThreshold: number;    // Warn when memory usage exceeds this (default: 500)
  enableMemoryMonitoring: boolean;   // Whether to monitor memory usage (default: true)
}

// Scenario Structure
export interface ScenarioStep {
  id: string;
  type: string;
  name?: string;
  description?: string;
  deepThink?: boolean; // For AI methods supporting deep think

  // Step type specific fields
  // For goto steps
  url?: string;

  // For sleep steps
  duration?: number;

  // For aiInput steps
  value?: any; // Text to input
  target?: string; // Target element to input into

  // For other AI steps (aiTap, aiAssertion, aiWaitElement, etc.)
  prompt?: string; // Main instruction/prompt

  // AI Wait specific parameters
  timeoutMs?: number; // For aiWaitElement: timeout in milliseconds
  checkIntervalMs?: number; // For aiWaitElement: check interval in milliseconds

  // Control Flow specific fields
  condition?: string; // For if-else and while: the condition to evaluate
  trueSteps?: ScenarioStep[]; // For if-else: steps to execute when condition is true
  falseSteps?: ScenarioStep[]; // For if-else: steps to execute when condition is false
  loopSteps?: ScenarioStep[]; // For for/while: steps to execute in the loop
  maxIterations?: number; // For while: maximum number of iterations to prevent infinite loops
  iterationCount?: string; // For for: the number or query to determine iteration count
}

export interface Scenario {
  id: string;
  name: string;
  title?: string;
  url: string;
  steps: ScenarioStep[];
  totalSteps?: number; // Total number of steps in the scenario
}

// ------------------------------
// Test Result Definitions
// ------------------------------

// Test Step Result
export interface StepResult {
  id: string;
  name: string;
  type: string;
  duration: number;
  success: boolean;
  error: string | null;
  beforeScreenshotUrl?: string | null;
  afterScreenshotUrl?: string | null;
  logs: string[];
  status?: string;
  queryData?: any;
  description?: string;
  data?: any;
  startTime?: string;
  endTime?: string;
  
  // Control Flow Results
  controlFlowData?: {
    // IF-ELSE specific
    conditionResult?: boolean;
    executedBranch?: 'true' | 'false';
    trueSteps?: StepResult[];
    falseSteps?: StepResult[];
    
    // FOR/WHILE LOOP specific  
    iterationCount?: number;
    completedIterations?: number;
    totalStepsExecuted?: number;
    loopSteps?: StepResult[][];
    hitMaxIterations?: boolean;
    failedIteration?: number;
    failedStepIndex?: number;
  };
}

// Test Result Summary
export interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  errors: number;
}

// Enhanced Performance Metrics - Simplified interface that matches ProcessedMetrics
export interface EnhancedMetrics {
  webVitals?: {
    lcp?: number;
    cls?: number;
    fid?: number;
    ttfb?: number;
    inp?: number;
    fcp?: number;
  };
  pageMetrics?: {
    documents?: number;
    nodes?: number;
    jsHeapUsedSize?: number;
    jsHeapTotalSize?: number;
    scriptDuration?: number;
    layoutCount?: number;
    recalcStyleCount?: number;
  };
  networkData?: {
    requests?: {
      total?: number;
      successful?: number;
      failed?: number;
      byType?: Record<string, number>;
    };
    transferred?: {
      total?: number;
      unit?: string;
    };
    logs?: any[];
  };
  accessibilityData?: {
    violations?: {
      count?: number;
      items?: any[];
      byType?: Record<string, number>;
      bySeverity?: Record<string, number>;
    };
    passes?: any[];
    summary?: {
      totalElements?: number;
      accessibilityScore?: number;
    };
  };
  tracingData?: any;
  performanceMetrics?: any;
  collectionPerformance?: {
    totalTime?: number;
    pageMetricsCollection?: number;
    networkDataCollection?: number;
    accessibilityDataCollection?: number;
  };
  timestamp?: string;
  error?: string;
  platform?: string;
}

// Complete Test Result
export interface TestResult {
  id: string;
  scenarioId: string;
  scenarioName: string;
  name: string;
  url: string;
  date: string;
  status: string;
  success: boolean;
  duration: number;
  steps: StepResult[];
  summary: TestSummary;
  error?: string | null;
  logs?: string[];
  reportId?: string;
  reportUrl?: string;
  videoUrl?: string;
  startTime?: string;            // ISO string of test start time
  endTime?: string;              // ISO string of test end time
  metrics?: any;                 // Metrics data

  // Fields for result queue processing
  testId?: string;                // Duplicate of id for compatibility with result queue
  nodeId?: string;                // ID of the node that executed the test
  runId?: string;                 // ID of the run this test belongs to
  executionId?: string;           // Execution identifier
  executedUser?: string;          // User who executed the test
  executedUserName?: string;      // Name of the user who executed the test
  teamId?: string | null;         // Team ID for report attribution
  companyId?: string | null;      // Company ID for report attribution
  transactionId?: string;         // Unique transaction ID for tracking

  // Environment settings including device information for Android tests
  environmentSettings?: EnvironmentSettings;  // Environment settings with device info
  platform?: 'web' | 'android';               // Platform type for proper processing

  // Testinium session information for Android tests using Testinium provider
  testiniumSessionId?: string;                 // Testinium session ID for tracking and debugging

  // Consolidated metrics structure
  enhancedMetrics?: EnhancedMetrics;
}

export interface QueryResult {
  stepId: string;
  stepName: string;
  data: any;
}

export interface Screenshot {
  id: string;
  testReportId: string;
  stepId: string;
  path: string;
  timestamp: Date;
  type?: 'before' | 'after';
}

// ------------------------------
// API Communication Interfaces
// ------------------------------

export interface NodeRegistrationResponse {
  nodeId: string;
}

export interface TestStatusResponse {
  status: string;
  id: string;
  scenarioId: string;
  scenario: any;
  queuedAt: Date;
  priority: number;
  runId?: string; // Run identifier
  executionId?: string; // Execution identifier
  nodeId?: string;
  userId?: string; // Kullanıcı kimliği
  executedUser?: string; // Testi çalıştıran kullanıcı kimliği
  executedUserName?: string; // Testi çalıştıran kullanıcının adı
  teamId?: string | null; // Team ID for report attribution
  companyId?: string | null; // Company ID for report attribution
  platform?: 'web' | 'android'; // Platform to run the test on (web or android)
  fileConfig?: FileConfig;
  environmentSettings?: EnvironmentSettings;
  reportSettings?: ReportSettings;
  options?: {
    sequentialRun?: boolean;
    [key: string]: any;
  };
}

export interface NextTestResponse {
  test: {
    id: string;
    scenarioId: string;
  };
  scenario: any;
  fileConfig?: FileConfig;
  environmentSettings?: EnvironmentSettings;
  reportSettings?: ReportSettings;
}

// ------------------------------
// Configuration Definitions
// ------------------------------

// Minio configuration
export interface MinioConfig {
  endPoint: string;
  port: number;
  useSSL: boolean;
  accessKey: string;
  secretKey: string;
  region?: string;
  screenshotBucket: string;
  reportBucket: string;
  publicBaseUrl: string;
}

// AWS S3 configuration
export interface S3Config {
  region: string;
  accessKey: string;
  secretKey: string;
  endpoint?: string;
  forcePathStyle?: boolean;
  screenshotBucket: string;
  reportBucket: string;
  publicBaseUrl: string;
}

// Test options configuration
export interface TestOptions {
  maxRetries?: number;
  timeout?: number;
}

// Appium server configuration
export interface AppiumServerConfig {
  hostname: string;
  port: number;
  path: string;
  protocol: 'http' | 'https';
}

// Appium capabilities configuration
export interface AppiumCapabilities {
  platformName: string;
  'appium:automationName': string;
  'appium:deviceName': string;
  'appium:platformVersion'?: string;
  'appium:udid'?: string;
  'appium:appPackage'?: string;
  'appium:appActivity'?: string;
  [key: string]: any;
}



// Android configuration
// Note: appium and capabilities are provided dynamically in test requests
export interface AndroidConfig {
  enabled: boolean;
  appium?: AppiumServerConfig; // Optional - provided in test requests
  capabilities?: AppiumCapabilities; // Optional - provided in test requests
  sauceLabs?: any; // Optional - provided in test requests
}

// Platform configuration
export interface PlatformConfig {
  web: {
    enabled: boolean;
  };
  android: AndroidConfig;
}

// Complete node configuration
export interface NodeConfig {
  // Basic node configuration
  name: string;
  hubUrl: string;
  capabilities: string[];
  version: string;
  environment: string;
  nodeId?: string; // Persistent node ID

  // Feature flags
  websocketEnabled: boolean;
  redisEnabled: boolean;
  minioEnabled?: boolean;
  s3Enabled?: boolean;
  useSystemInfo?: boolean;

  // Connection settings
  redisUrl: string;
  port?: number;
  nodeSecretKey?: string; // Node secret key for authentication
  heartbeatInterval?: number; // Heartbeat interval in milliseconds

  // Redis configuration
  redis?: {
    host: string;
    port: number;
    password: string;
    url: string;
    db: number;
    prefix: string;
  };

  // BullMQ configuration
  bullmq?: {
    jobLockRefreshInterval: number;
    stalledJobLockExtension: number;
    lockDuration: number;
    stalledInterval: number;
    maxStalledCount: number;
    activeJobMonitoringTimeout: number;
    activeJobLockExtension: number;
    concurrency: number;
    drainDelay: number;
  };

  // File paths
  screenshotsDir: string;
  nodeScenarioDir: string;

  // Extended configurations
  minio?: MinioConfig;
  s3?: S3Config;
  testOptions?: TestOptions;

  // Platform configurations
  platforms?: PlatformConfig;
}

// Import standardized Android environment settings
import { AndroidEnvironmentSettings } from './android-environment.js';

// Web-specific environment settings
export interface WebEnvironmentSettings {
  // Platform identifier
  platform: 'web';

  // Web browser settings
  browserName: string;  // e.g., "Chrome" | "Firefox" | "Safari" | "Edge"
  browserVersion?: string;
  browser: string;      // e.g., "chrome" | "firefox"
  browserMode?: 'headless' | 'headed'; // Browser display mode
  viewportSize: string; // e.g., "Mobile" | "Tablet" | "Desktop" | "Custom"
  viewport?: {
    width: number;
    height: number;
  };
  customViewport?: {
    width: number;
    height: number;
  };
  networkSpeed: string; // e.g., "Slow 3G" | "Fast 3G" | "Slow 4G" | "Fast 4G" | "Normal"

  // AI model settings
  aiModel: string;      // e.g., "GPT-3.5" | "GPT-4" | "Claude" | "Gemini" | "Auto" (ID of the model)
  aiModelName?: string; // Display name of the AI model

  // Proxy settings
  proxy?: {
    enabled: boolean;
    type: string;       // e.g., "SOCKS5" | "HTTP" | "HTTPS"
    host: string;
    port: number;
    username: string;
    password: string;
  };

  // AI model configuration (for node execution)
  aiModelConfig?: {
    OPENAI_BASE_URL: string;
    OPENAI_API_KEY: string;
    MIDSCENE_MODEL_NAME: string;
    [key: string]: string;
  };

  // Additional web-specific properties
  userAgent?: string;           // Custom user agent string
  device?: string;              // Device name (e.g., "iPhone 12 Pro", "iPad Air", "Desktop")
  cookies?: Array<{             // Browser cookies
    name: string;
    value: string;
    domain: string;
    path?: string;
    expires?: number;
    httpOnly?: boolean;
    secure?: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
  }>;
  headers?: Record<string, string>; // Custom HTTP headers
}

// Combined environment settings type - use discriminated union
export type EnvironmentSettings = WebEnvironmentSettings | AndroidEnvironmentSettings;

// Base Report Settings Interface
export interface BaseReportSettings {
  format?: string;      // e.g., "HTML" | "PDF" | "JSON"
  takeScreenshots: boolean;
  takeVideos: boolean;
}

// Web-specific Report Settings Interface
export interface WebReportSettings extends BaseReportSettings {
  pageMetrics: boolean;
  networkData: boolean;
  tracingData: boolean;
  accessibilityData: boolean;

  // Web-specific metrics settings
  enableSyntheticInteractions?: boolean; // Enable synthetic interactions for metrics
  safeInteractions?: boolean; // Use safe interactions mode
  metricsWaitTime?: number; // Wait time for metrics collection in milliseconds
}

// Android-specific Report Settings Interface
export interface AndroidReportSettings extends BaseReportSettings {
  collectMetrics?: boolean; // Whether to collect performance metrics
  videoQuality?: 'low' | 'medium' | 'high'; // Video recording quality
  metricsInterval?: number; // Interval for collecting metrics in milliseconds
}

// Combined Report Settings type - use union to prevent field contamination
export type ReportSettings = WebReportSettings | AndroidReportSettings;

// Default settings
export const defaultEnvironmentSettings: WebEnvironmentSettings = {
  platform: "web", // Default platform is web
  browserName: "Chrome",
  browserVersion: "Latest",
  browser: "chrome",
  browserMode: "headless", // Default to headless for better performance
  viewportSize: "Desktop",
  viewport: {
    width: 1280,
    height: 720
  },
  networkSpeed: "Normal",
  aiModel: "Gemini",
  aiModelName: "Gemini Pro", // Default AI model name
  proxy: {
    enabled: false,
    type: "HTTP",
    host: "",
    port: 0,
    username: "",
    password: ""
  }
};

// Default Web Report Settings
export const defaultWebReportSettings: WebReportSettings = {
  format: "HTML",
  pageMetrics: true,
  networkData: true,
  tracingData: false,
  accessibilityData: false,
  takeScreenshots: true,
  takeVideos: false
};

// Default Android Report Settings
export const defaultAndroidReportSettings: AndroidReportSettings = {
  format: "HTML",
  takeScreenshots: true,
  takeVideos: false,
  collectMetrics: false,
  videoQuality: 'medium',
  metricsInterval: 10000 // 10 seconds
};

// Default Report Settings (defaults to web)
export const defaultReportSettings: WebReportSettings = defaultWebReportSettings;

// ------------------------------
// Type Guards
// ------------------------------

export function isNodeRegistrationResponse(data: unknown): data is NodeRegistrationResponse {
  return (
    typeof data === 'object' &&
    data !== null &&
    'nodeId' in data &&
    typeof data.nodeId === 'string'
  );
}

export function isTestStatusResponse(data: unknown): data is TestStatusResponse {
  return (
    typeof data === 'object' &&
    data !== null &&
    'status' in data &&
    'id' in data &&
    'scenarioId' in data
  );
}

export function isNextTestResponse(data: unknown): data is NextTestResponse {
  const basicCheck = (
    typeof data === 'object' &&
    data !== null &&
    'test' in data &&
    typeof data.test === 'object' &&
    data.test !== null &&
    'id' in data.test &&
    'scenarioId' in data.test
  );

  if (!basicCheck) return false;

  // Check environment settings if present
  if ('environmentSettings' in data) {
    const envSettings = (data as any).environmentSettings;
    if (typeof envSettings !== 'object' || envSettings === null) {
      return false;
    }
  }

  // Check report settings if present
  if ('reportSettings' in data) {
    const reportSettings = (data as any).reportSettings;
    if (typeof reportSettings !== 'object' || reportSettings === null) {
      return false;
    }
  }

  return true;
}

// ------------------------------
// Utility Functions
// ------------------------------

// Test adım durum izleme için enum tanımlaması
export enum StepStatus {
  QUEUED = 'queued',
  STARTED = 'started',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped'
}

// Test adımı ilerleme verisi
export interface StepProgressData {
  type: string;
  status: StepStatus | string;
  timestamp: string;
  duration?: number;
  error?: string | null;
}