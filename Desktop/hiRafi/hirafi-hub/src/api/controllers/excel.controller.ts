import { Request, Response } from 'express';
import multer from 'multer';
import xlsx from 'xlsx';
import { logger } from '../../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

// Use memory storage to avoid writing files to disk
const storage = multer.memoryStorage();
const upload = multer({ storage });

/**
 * @description Inspects an uploaded Excel file and returns the names of its sheets.
 */
export const inspect = (req: Request, res: Response) => {
  logger.info('Excel inspect request received');
  if (!req.file) {
    logger.warn('No file uploaded for inspect');
    return res.status(400).json({ message: 'Dosya yüklenmedi.' });
  }

  try {
    logger.info(`Inspecting file: ${req.file.originalname}, size: ${req.file.size}`);
    const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
    const sheetNames = workbook.SheetNames;
    logger.info(`Found sheet names: ${sheetNames.join(', ')}`);
    return res.status(200).json({ sheetNames });
  } catch (error) {
    logger.error('Error inspecting Excel file:', error);
    return res.status(500).json({ message: 'Excel dosyası incelenirken bir hata oluştu.' });
  }
};

/**
 * @description Parses a specific sheet from an uploaded Excel file and returns its content as JSON.
 * The first row is assumed to be the header.
 */
export const parse = (req: Request, res: Response) => {
  logger.info('Excel parse request received');
  const { sheetName } = req.body;

  if (!req.file) {
    logger.warn('No file uploaded for parse');
    return res.status(400).json({ message: 'Dosya yüklenmedi.' });
  }

  if (!sheetName) {
    logger.warn('No sheetName provided for parse');
    return res.status(400).json({ message: 'Sayfa adı belirtilmedi.' });
  }

  try {
    logger.info(`Parsing file: ${req.file.originalname}, sheet: ${sheetName}`);
    const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });

    if (!workbook.SheetNames.includes(sheetName)) {
      logger.warn(`Sheet "${sheetName}" not found in workbook.`);
      return res.status(404).json({ message: `"${sheetName}" adında bir sayfa bulunamadı.` });
    }

    const worksheet = workbook.Sheets[sheetName];
    const data: any[][] = xlsx.utils.sheet_to_json(worksheet, { header: 1, defval: "" });

    if (!data || data.length === 0) {
      logger.info(`Sheet "${sheetName}" is empty.`);
      return res.status(200).json({ variables: [], totalRows: 0 });
    }

    const variables = data.map(row => ({
      id: uuidv4(),
      name: row[0] || '',
      value: row[1] || '',
    })).filter(v => v.name || v.value); // Exclude completely empty rows

    logger.info(`Successfully parsed ${variables.length} variable pairs from sheet "${sheetName}".`);
    return res.status(200).json({ variables, totalRows: variables.length });
  } catch (error) {
    logger.error('Error parsing Excel file:', error);
    return res.status(500).json({ message: 'Excel dosyası işlenirken bir hata oluştu.' });
  }
}; 