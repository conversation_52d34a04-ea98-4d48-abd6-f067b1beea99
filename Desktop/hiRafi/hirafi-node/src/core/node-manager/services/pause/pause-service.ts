/**
 * Pause Service
 * Refactored to use dependency injection instead of singleton pattern
 * Manages system and node pause status through Redis
 */

import { EventEmitter } from 'events';
import { logger } from '../../../../utils/logger.js';
import { IPauseService, PauseServiceEvents, PauseServiceConfig, PauseServiceStats } from '../../interfaces/pause-service.interface.js';
import { IRedisConnectionManager } from '../../../connectors/interfaces/redis-connector.interface.js';

// Redis key prefixes
const SYSTEM_PAUSE_KEY = 'system:paused';
const NODE_PAUSE_KEY_PREFIX = 'node:';
const NODE_PAUSE_KEY_SUFFIX = ':paused';

export class PauseService extends EventEmitter implements IPauseService {
  private nodeId: string | null = null;
  private _isInitialized: boolean = false;
  private lastEffectivePauseStatus: boolean | null = null;
  private checkCount: number = 0;
  private lastCheck: string | null = null;

  constructor(
    private connectionManager: IRedisConnectionManager,
    private config?: PauseServiceConfig
  ) {
    super();
    logger.debug('PauseService: Instance created with dependency injection');
  }

  /**
   * Initialize the pause service
   */
  public async initialize(nodeId: string): Promise<boolean> {
    if (this._isInitialized && this.nodeId === nodeId) {
      logger.debug('PauseService: Already initialized with the same node ID');
      return true;
    }

    this.nodeId = nodeId;

    try {
      // Ensure Redis connection is available
      if (!this.connectionManager.isConnected()) {
        const connected = await this.connectionManager.connect();
        if (!connected) {
          logger.error('PauseService: Failed to connect to Redis via connection manager');
          return false;
        }
      }

      this._isInitialized = true;
      logger.info(`PauseService: Initialized for node ${nodeId}`);
      return true;

    } catch (error: any) {
      logger.error(`PauseService: Initialization failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Check if the service is initialized
   */
  public isInitialized(): boolean {
    return this._isInitialized;
  }

  /**
   * Check if the system is paused
   */
  public async isSystemPaused(): Promise<boolean> {
    const client = this.connectionManager.getClient();
    if (!client) {
      logger.warn('PauseService: No Redis connection available for system pause check');
      return false;
    }

    try {
      const result = await client.get(SYSTEM_PAUSE_KEY);
      return result === 'true';
    } catch (error: any) {
      logger.error(`PauseService: Error checking system pause status: ${error.message}`);
      return false;
    }
  }

  /**
   * Check if this specific node is paused
   */
  public async isNodePaused(): Promise<boolean> {
    if (!this.nodeId) {
      logger.warn('PauseService: No node ID available for node pause check');
      return false;
    }

    const client = this.connectionManager.getClient();
    if (!client) {
      logger.warn('PauseService: No Redis connection available for node pause check');
      return false;
    }

    try {
      const nodeKey = `${NODE_PAUSE_KEY_PREFIX}${this.nodeId}${NODE_PAUSE_KEY_SUFFIX}`;
      const result = await client.get(nodeKey);
      return result === 'true';
    } catch (error: any) {
      logger.error(`PauseService: Error checking node pause status for ${this.nodeId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Check if either the system or this node is paused
   */
  public async isPaused(): Promise<boolean> {
    this.checkCount++;
    this.lastCheck = new Date().toISOString();

    const systemPaused = await this.isSystemPaused();
    const nodePaused = await this.isNodePaused();
    const effectivelyPaused = systemPaused || nodePaused;

    if (effectivelyPaused) {
      logger.debug(`PauseService: Pause status - System: ${systemPaused}, Node: ${nodePaused}, Effective: ${effectivelyPaused}`);
    }

    // Emit event if effective pause status changed
    if (this.lastEffectivePauseStatus !== null && this.lastEffectivePauseStatus !== effectivelyPaused) {
      logger.info(`PauseService: Effective pause status changed from ${this.lastEffectivePauseStatus} to ${effectivelyPaused}`);
      this.emit('effectivePauseStatusChanged', effectivelyPaused);
    }

    this.lastEffectivePauseStatus = effectivelyPaused;
    return effectivelyPaused;
  }

  /**
   * Set system pause status
   */
  public async setSystemPaused(paused: boolean): Promise<boolean> {
    const client = this.connectionManager.getClient();
    if (!client) {
      logger.error('PauseService: No Redis connection available for setting system pause');
      return false;
    }

    try {
      if (paused) {
        await client.set(SYSTEM_PAUSE_KEY, 'true');
        logger.info('PauseService: System pause enabled');
      } else {
        await client.del(SYSTEM_PAUSE_KEY);
        logger.info('PauseService: System pause disabled');
      }
      // Trigger a check and potential event emission after changing state
      await this.isPaused();
      return true;
    } catch (error: any) {
      logger.error(`PauseService: Error setting system pause status: ${error.message}`);
      return false;
    }
  }

  /**
   * Set node pause status
   */
  public async setNodePaused(paused: boolean): Promise<boolean> {
    if (!this.nodeId) {
      logger.error('PauseService: No node ID available for setting node pause');
      return false;
    }

    const client = this.connectionManager.getClient();
    if (!client) {
      logger.error('PauseService: No Redis connection available for setting node pause');
      return false;
    }

    try {
      const nodeKey = `${NODE_PAUSE_KEY_PREFIX}${this.nodeId}${NODE_PAUSE_KEY_SUFFIX}`;

      if (paused) {
        await client.set(nodeKey, 'true');
        logger.info(`PauseService: Node ${this.nodeId} pause enabled`);
      } else {
        await client.del(nodeKey);
        logger.info(`PauseService: Node ${this.nodeId} pause disabled`);
      }
      // Trigger a check and potential event emission after changing state
      await this.isPaused();
      return true;
    } catch (error: any) {
      logger.error(`PauseService: Error setting node pause status for ${this.nodeId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get the current node ID
   */
  public getNodeId(): string | null {
    return this.nodeId;
  }

  /**
   * Get service statistics
   */
  public getStats(): PauseServiceStats {
    return {
      nodeId: this.nodeId,
      isInitialized: this._isInitialized,
      systemPaused: false, // Would need async call to get real value
      nodePaused: false,   // Would need async call to get real value
      effectivelyPaused: this.lastEffectivePauseStatus || false,
      lastCheck: this.lastCheck,
      checkCount: this.checkCount
    };
  }

  /**
   * Close the service
   */
  public async close(): Promise<void> {
    try {
      // Note: We don't close the connection manager here since it's shared
      // The connection manager will be closed when the main Redis connector is closed
      this._isInitialized = false;
      this.nodeId = null;
      this.lastEffectivePauseStatus = null;
      logger.info('PauseService: Closed successfully');
    } catch (error: any) {
      logger.error(`PauseService: Error during close: ${error.message}`);
    }
  }
}
