/**
 * Queue Service
 * Manages Redis-based queues using Bull
 */

import { Queue, QueueOptions, Job, JobsOptions, Worker, QueueEvents } from 'bullmq';
import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { redisConnection } from './redisConnection.js';
import { config } from '../../config/index.js';

// Queue names
export const QUEUE_NAMES = {
  TEST_QUEUE: 'test-queue',
  RESULT_QUEUE: 'result-queue',
  STEP_PROGRESS_QUEUE: 'step-progress-queue',
  NODE_STATUS_QUEUE: 'node-status-queue',
  // Keep NODE_SPECIFIC_PREFIX for backward compatibility with existing code
  NODE_SPECIFIC_PREFIX: 'node-queue-'
};

// Job types
export const JOB_TYPES = {
  TEST_REQUEST: 'test-request',
  TEST_RESULT: 'test-result',
  STEP_PROGRESS: 'step-progress',
  NODE_STATUS: 'node-status'
};

// Queue events
export interface QueueServiceEvents {
  'job:completed': (jobId: string, result: any, queue: string) => void;
  'job:failed': (jobId: string, error: Error, queue: string) => void;
  'job:progress': (jobId: string, progress: number, queue: string) => void;
  'job:added': (jobId: string, data: any, queue: string) => void;
  'queue:error': (error: Error, queue: string) => void;
  'failed:retry': (jobId: string, attemptsMade: number, err: string, queue: string) => void;
  'test:failed:permanent': (testId: string, reason: string, queue: string) => void;
  'test:stalled': (testId: string, queue: string) => void;
  // New event for specific test claim failures that might be recoverable by TestQueueService
  'test:claim:failed_validation': (job: Job, testId: string, failedReason: string) => void;
}

class QueueService extends EventEmitter {
  private static instance: QueueService;
  private queues: Map<string, Queue> = new Map();
  private queueEvents: Map<string, QueueEvents> = new Map();
  private workers: Map<string, Worker> = new Map();
  private isInitialized: boolean = false;

  private constructor() {
    super();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): QueueService {
    if (!QueueService.instance) {
      QueueService.instance = new QueueService();
    }
    return QueueService.instance;
  }

  /**
   * Initialize the queue service
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Ensure Redis connection is established
      const redisConnected = await redisConnection.initialize();
      if (!redisConnected) {
        logger.error('QueueService: Cannot initialize - Redis connection failed');
        return false;
      }

      // Create main queues
      await this.createQueue(QUEUE_NAMES.TEST_QUEUE);
      await this.createQueue(QUEUE_NAMES.RESULT_QUEUE);
      await this.createQueue(QUEUE_NAMES.STEP_PROGRESS_QUEUE);

      // Set up queue events
      await this.setupQueueEvents(QUEUE_NAMES.TEST_QUEUE);
      await this.setupQueueEvents(QUEUE_NAMES.RESULT_QUEUE);
      await this.setupQueueEvents(QUEUE_NAMES.STEP_PROGRESS_QUEUE);

      // In the Pull Model, we don't create a worker for the test queue
      // Tests are pulled directly by the test nodes
      // logger.info('QueueService: Not creating worker for test queue - using Pull Model where test nodes claim tests directly');
      // Revised understanding: Nodes (hirafi-node) do implement a standard BullMQ worker for TEST_QUEUE.
      logger.info('QueueService: hirafi-node instances implement their own BullMQ workers for the test-queue.');

      this.isInitialized = true;

      return true;
    } catch (error) {
      logger.error(`QueueService: Initialization error: ${error}`);
      return false;
    }
  }

  /**
   * Create a queue
   * @param queueName Name of the queue
   * @param options Queue options
   */
  public async createQueue(queueName: string, options?: QueueOptions): Promise<Queue> {
    if (this.queues.has(queueName)) {
      return this.queues.get(queueName)!;
    }

    // Use existing Redis connection instead of creating new one
    const redisClient = redisConnection.getClient();
    if (!redisClient) {
      throw new Error('Redis client not available');
    }

    // Standardized options for all queues
    const defaultOptions: QueueOptions = {
      connection: redisClient,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        },
        removeOnComplete: 100,  // Keep last 100 completed jobs
        removeOnFail: 200       // Keep last 200 failed jobs
      }
    };

    // Test queue specific configuration
    if (queueName === QUEUE_NAMES.TEST_QUEUE) {
      defaultOptions.defaultJobOptions = {
        attempts: 3, // Reduced from 10 to prevent excessive retries
        backoff: {
          type: 'fixed',  // Fixed delay instead of exponential
          delay: 2000     // Increased to 2 seconds to allow proper cleanup
        },
        removeOnComplete: { count: 50 }, // Increased to keep more completed jobs for debugging
        removeOnFail: { count: 100 }     // Increased to keep more failed jobs for debugging
      };
      logger.info('QueueService: Using optimized retry configuration for test queue (reduced attempts to prevent retry loops)');
    }

    const queue = new Queue(queueName, {
      ...defaultOptions,
      ...options
    });

    this.queues.set(queueName, queue);
    logger.info(`QueueService: Created queue "${queueName}"`);
    return queue;
  }

  /**
   * Get a queue by name
   * @param queueName Name of the queue
   */
  public getQueue(queueName: string): Queue | undefined {
    return this.queues.get(queueName);
  }

  /**
   * Get all queues
   * @returns Map of all queues
   */
  public getAllQueues(): Map<string, Queue> {
    return this.queues;
  }

  /**
   * Get or create the central test queue
   * This replaces the node-specific queues in the Pull Model
   * @returns The central test queue
   */
  public async getCentralTestQueue(): Promise<Queue> {
    return this.getOrCreateQueue(QUEUE_NAMES.TEST_QUEUE);
  }

  /**
   * Add a job to a queue
   * @param queueName Name of the queue
   * @param jobType Type of job
   * @param data Job data
   * @param options Job options
   */
  public async addJob(
    queueName: string,
    jobType: string,
    data: any,
    options?: JobsOptions
  ): Promise<Job | null> {
    try {
      const queue = await this.getOrCreateQueue(queueName);

      // Simplified default options. Specifics (like SauceLabs attempts/backoff)
      // should be passed in the 'options' parameter by the caller.
      const trulyDefaultOptions: JobsOptions = {
        attempts: 3, // General default
        backoff: {
          type: 'exponential', // General default
          delay: 1000
        },
        removeOnComplete: 50,
        removeOnFail: 100
      };

      const job = await queue.add(jobType, data, {
        ...trulyDefaultOptions, // Apply true defaults first
        ...options          // Then apply caller-provided options, which can override defaults
      });

      logger.debug(`QueueService: Added job ${job.id} to queue "${queueName}"`);
      this.emit('job:added', job.id, data, queueName);
      return job;
    } catch (error) {
      logger.error(`QueueService: Error adding job to queue "${queueName}": ${error}`);
      this.emit('queue:error', error, queueName);
      return null;
    }
  }

  /**
   * Get or create a queue
   * @param queueName Name of the queue
   */
  private async getOrCreateQueue(queueName: string): Promise<Queue> {
    if (this.queues.has(queueName)) {
      return this.queues.get(queueName)!;
    }
    return this.createQueue(queueName);
  }

  /**
   * Set up queue events
   * @param queueName Name of the queue
   */
  private async setupQueueEvents(queueName: string): Promise<void> {
    if (this.queueEvents.has(queueName)) {
      return;
    }

    // Use existing Redis connection instead of creating new one
    const redisClient = redisConnection.getClient();
    if (!redisClient) {
      throw new Error('Redis client not available for queue events');
    }

    const queueEvents = new QueueEvents(queueName, {
      connection: redisClient
    });

    queueEvents.on('completed', ({ jobId, returnvalue }: { jobId: string; returnvalue: any }) => {
      logger.debug(`QueueService: Job ${jobId} completed in queue "${queueName}"`);
      this.emit('job:completed', jobId, returnvalue, queueName);

      // Enhanced logging and handling for test queue
      if (queueName === QUEUE_NAMES.TEST_QUEUE) {
        logger.info(`QueueService: Test job ${jobId} completed successfully.`);

        // If the job has a testId, we can use it to update the database
        if (returnvalue && returnvalue.testId) {
          logger.info(`QueueService: Test ${returnvalue.testId} completed successfully after ${returnvalue.attemptsMade || 0} attempts.`);
          // The actual database update would be handled by listeners to the 'job:completed' event
        }
      }
    });

    queueEvents.on('failed', ({ jobId, failedReason }: { jobId: string; failedReason: string }) => {
      logger.error(`QueueService: Job ${jobId} failed in queue "${queueName}": ${failedReason}`);
      this.emit('job:failed', jobId, new Error(failedReason), queueName);

      // Enhanced logging and handling for test queue
      if (queueName === QUEUE_NAMES.TEST_QUEUE) {
        // Check if this is a final failure (not a retry)
        if (failedReason.includes('NodeBusyFinal') ||
            failedReason.includes('TestClaimFailedFinal') ||
            failedReason.includes('PreClaimValidationFailedFinal') ||
            failedReason.includes('NonRetryableError')) {

          // Extract testId from job data for recovery handling
          this.getQueue(queueName)?.getJob(jobId).then(async (job: Job | undefined) => {
            if (job && job.data && job.data.id) {
              const testId = job.data.id;

              // Special handling for PreClaimValidationFailedFinal - these should be recoverable
              if (failedReason.includes('PreClaimValidationFailedFinal')) {
                logger.warn(`QueueService: Test ${testId} (job ${jobId}) failed due to claim validation issues. Emitting 'test:claim:failed_validation'.`);
                // Emit an event for TestQueueService to handle
                this.emit('test:claim:failed_validation', job, testId, failedReason);
              } else {
                logger.error(`QueueService: Test ${testId} failed permanently after ${job.attemptsMade || 0} attempts. Reason: ${failedReason}`);
                // Emit event for permanent test failure handling
                this.emit('test:failed:permanent', testId, failedReason, queueName);
              }
            }
          }).catch((error: Error) => {
            logger.error(`QueueService: Error getting job data for failed job ${jobId}: ${error}`);
          });
        } else {
          logger.error(`QueueService: Test job ${jobId} failed after all attempts. Reason: ${failedReason}`);
        }
      }
    });

    queueEvents.on('progress', ({ jobId, data }: { jobId: string; data: any }) => {
      logger.debug(`QueueService: Job ${jobId} progress in queue "${queueName}": ${data}`);
      this.emit('job:progress', jobId, data, queueName);
    });

    // Use the standard 'failed' event to track retries
    queueEvents.on('failed', ({ jobId, failedReason }: { jobId: string; failedReason: string }) => {
      // Check if this job will be retried (we already have a 'failed' handler above, but we need to add retry logic)
      // Get the job to check if it will be retried
      this.getQueue(queueName)?.getJob(jobId).then((job: Job | undefined) => {
        if (job && job.opts.attempts && (job.attemptsMade || 0) < job.opts.attempts) {
          // This job will be retried
          const currentAttempt = job.attemptsMade || 0;
          logger.warn(`QueueService: Job ${jobId} failed but will be retried (attempt ${currentAttempt}/${job.opts.attempts}). Error: ${failedReason}`);

          // Emit our custom event with the queue name as the last parameter
          this.emit('failed:retry', jobId, currentAttempt, failedReason, queueName);

          if (queueName === QUEUE_NAMES.TEST_QUEUE) {
            // Check if the error is related to SauceLabs device unavailability
            if (failedReason && typeof failedReason === 'string' && failedReason.includes('SauceLabsDeviceUnavailable')) {
              logger.warn(`QueueService: Job ${jobId} failed due to SauceLabs device unavailability. Will retry.`);
            }
          }
        }
      }).catch((error: Error) => {
        logger.error(`QueueService: Error checking job ${jobId} for retry status: ${error}`);
      });
    });

    this.queueEvents.set(queueName, queueEvents);
    logger.info(`QueueService: Set up events for queue "${queueName}"`);
  }

  /**
   * Create a worker to process jobs in a queue
   * @param queueName Name of the queue
   * @param processor Job processor function
   * @param options Additional worker options
   */
  public async createWorker(
    queueName: string,
    processor: (job: Job) => Promise<any>,
    options?: {
      removeOnComplete?: boolean | number | { count: number; age?: number };
      removeOnFail?: boolean | number | { count: number; age?: number };
      concurrency?: number;
      stalledInterval?: number;
      maxStalledCount?: number;
    }
  ): Promise<Worker> {
    if (this.workers.has(queueName)) {
      return this.workers.get(queueName)!;
    }

    // Use existing Redis connection instead of creating new one
    const redisClient = redisConnection.getClient();
    if (!redisClient) {
      throw new Error('Redis client not available for worker');
    }

    // Create worker options
    const workerOptions: any = {
      connection: redisClient,
      concurrency: options?.concurrency || 10,  // Process up to 10 jobs concurrently by default
      stalledInterval: options?.stalledInterval || 30000,  // Check for stalled jobs every 30 seconds
      maxStalledCount: options?.maxStalledCount || 3      // Allow a job to be stalled up to 3 times before marking as failed
    };

    // Add removal options if provided
    if (options?.removeOnComplete !== undefined) {
      workerOptions.removeOnComplete = options.removeOnComplete;
    } else {
      workerOptions.removeOnComplete = {
        count: 100,            // Keep last 100 completed jobs
        age: 60 * 60 * 1000    // Remove completed jobs after 1 hour
      };
    }

    if (options?.removeOnFail !== undefined) {
      workerOptions.removeOnFail = options.removeOnFail;
    } else {
      workerOptions.removeOnFail = {
        count: 200,            // Keep last 200 failed jobs
        age: 24 * 60 * 60 * 1000  // Remove failed jobs after 24 hours
      };
    }

    const worker = new Worker(queueName, async (job: Job) => {
      try {
        return await processor(job);
      } catch (error) {
        logger.error(`QueueService: Error processing job ${job.id} in queue "${queueName}": ${error}`);
        throw error;
      }
    }, workerOptions);

    worker.on('completed', (job: Job) => {
      logger.debug(`QueueService: Worker completed job ${job.id} in queue "${queueName}"`);
    });

    worker.on('failed', (job: Job | undefined, error: Error) => {
      logger.error(`QueueService: Worker failed job ${job?.id} in queue "${queueName}": ${error}`);
    });

    this.workers.set(queueName, worker);
    logger.info(`QueueService: Created worker for queue "${queueName}"`);
    return worker;
  }

  /**
   * Get queue status
   * @param queueName Optional name of the queue. If not provided, returns status for all main queues.
   */
  public async getQueueStatus(queueName?: string): Promise<{
    [key: string]: {
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed: number;
    }
  }> {
    // If a specific queue name is provided, return status for just that queue
    if (queueName) {
      try {
        const queue = this.getQueue(queueName);
        if (!queue) {
          throw new Error(`Queue "${queueName}" not found`);
        }

        const [waiting, active, completed, failed, delayed] = await Promise.all([
          queue.getWaitingCount(),
          queue.getActiveCount(),
          queue.getCompletedCount(),
          queue.getFailedCount(),
          queue.getDelayedCount()
        ]);

        return {
          [queueName]: {
            waiting,
            active,
            completed,
            failed,
            delayed
          }
        };
      } catch (error) {
        logger.error(`QueueService: Error getting queue status for "${queueName}": ${error}`);
        return {
          [queueName]: {
            waiting: 0,
            active: 0,
            completed: 0,
            failed: 0,
            delayed: 0
          }
        };
      }
    }

    // If no queue name is provided, return status for all main queues
    try {
      const result: {
        [key: string]: {
          waiting: number;
          active: number;
          completed: number;
          failed: number;
          delayed: number;
        }
      } = {};

      // Get status for main queues
      const mainQueueNames = [
        QUEUE_NAMES.TEST_QUEUE,
        QUEUE_NAMES.RESULT_QUEUE,
        QUEUE_NAMES.STEP_PROGRESS_QUEUE
      ];

      for (const name of mainQueueNames) {
        try {
          const queue = this.getQueue(name);
          if (queue) {
            const [waiting, active, completed, failed, delayed] = await Promise.all([
              queue.getWaitingCount(),
              queue.getActiveCount(),
              queue.getCompletedCount(),
              queue.getFailedCount(),
              queue.getDelayedCount()
            ]);

            result[name] = {
              waiting,
              active,
              completed,
              failed,
              delayed
            };
          } else {
            logger.warn(`QueueService: Queue "${name}" not found when getting all queue statuses`);
            result[name] = {
              waiting: 0,
              active: 0,
              completed: 0,
              failed: 0,
              delayed: 0
            };
          }
        } catch (error) {
          logger.error(`QueueService: Error getting status for queue "${name}": ${error}`);
          result[name] = {
            waiting: 0,
            active: 0,
            completed: 0,
            failed: 0,
            delayed: 0
          };
        }
      }

      return result;
    } catch (error) {
      logger.error(`QueueService: Error getting all queue statuses: ${error}`);
      return {
        [QUEUE_NAMES.TEST_QUEUE]: {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0
        },
        [QUEUE_NAMES.RESULT_QUEUE]: {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0
        },
        [QUEUE_NAMES.STEP_PROGRESS_QUEUE]: {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0
        },
        // Node registration queue removed - using WebSocket-only registration
      };
    }
  }

  /**
   * Check if connected to Redis
   */
  public isConnected(): boolean {
    return this.isInitialized && redisConnection.isConnected();
  }

  /**
   * Get queue information for monitoring
   * @param queueName Name of the queue to get info for
   */
  public async getQueueInfo(queueName: string): Promise<{
    waiting: number;
    active: number;
    delayed: number;
    completed: number;
    failed: number;
  }> {
    try {
      const queue = this.getQueue(queueName);
      if (!queue) {
        return { waiting: 0, active: 0, delayed: 0, completed: 0, failed: 0 };
      }

      const [waiting, active, delayed, completed, failed] = await Promise.all([
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getDelayedCount(),
        queue.getCompletedCount(),
        queue.getFailedCount()
      ]);

      return { waiting, active, delayed, completed, failed };
    } catch (error) {
      logger.error(`QueueService: Error getting queue info for ${queueName}: ${error}`);
      return { waiting: 0, active: 0, delayed: 0, completed: 0, failed: 0 };
    }
  }

  /**
   * Close all queues and workers
   */
  public async close(): Promise<void> {
    try {
      // Close all workers
      for (const [name, worker] of this.workers.entries()) {
        await worker.close();
        logger.info(`QueueService: Closed worker for queue "${name}"`);
      }
      this.workers.clear();

      // Close all queue events
      for (const [name, events] of this.queueEvents.entries()) {
        await events.close();
        logger.info(`QueueService: Closed events for queue "${name}"`);
      }
      this.queueEvents.clear();

      // Close all queues
      for (const [name, queue] of this.queues.entries()) {
        await queue.close();
        logger.info(`QueueService: Closed queue "${name}"`);
      }
      this.queues.clear();

      this.isInitialized = false;

    } catch (error) {
      logger.error(`QueueService: Error closing queues and workers: ${error}`);
    }
  }
}

// Export singleton instance
export const queueService = QueueService.getInstance();
export default queueService;
