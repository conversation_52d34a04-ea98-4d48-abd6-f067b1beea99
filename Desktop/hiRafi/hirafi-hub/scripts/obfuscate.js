#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { glob } from "glob";
import { execSync } from "child_process";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  distDir: path.join(__dirname, "../dist"),
  obfuscatedDir: path.join(__dirname, "../obfuscated"),
  obfuscationOptions: {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.75,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.4,
    debugProtection: true,
    debugProtectionInterval: 1000,
    disableConsoleOutput: false,
    identifierNamesGenerator: "hexadecimal",
    numbersToExpressions: true,
    selfDefending: true,
    simplify: true,
    splitStrings: true,
    stringArray: true,
    stringArrayCallsTransform: true,
    stringArrayEncoding: ["base64"],
    stringArrayIndexShift: true,
    stringArrayRotate: true,
    stringArrayShuffle: true,
    transformObjectKeys: true
  }
};

// Ensure directories exist
if (!fs.existsSync(config.obfuscatedDir)) {
  fs.mkdirSync(config.obfuscatedDir, { recursive: true });
}

// Step 1: Obfuscate JavaScript files using CLI tool
console.log("Obfuscating JavaScript files...");

async function obfuscateFiles() {
  const jsFiles = await glob(`${config.distDir}/**/*.js`);

  for (const file of jsFiles) {
    // Create the same directory structure in obfuscated dir
    const relativePath = path.relative(config.distDir, file);
    const targetPath = path.join(config.obfuscatedDir, relativePath);

    // Ensure directory exists
    const targetDir = path.dirname(targetPath);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // Ensure the file exists
    if (!fs.existsSync(file)) {
      console.error(`File not found: ${file}`);
      continue;
    }

    // Run javascript-obfuscator CLI tool with aggressive config file
    const configPath = path.join(__dirname, "../obfuscator-config-aggressive.json");

    try {
      execSync(`javascript-obfuscator ${file} --output ${targetPath} --config ${configPath}`);
      console.log(`Obfuscated: ${relativePath}`);
    } catch (err) {
      console.error(`Error obfuscating ${file}:`, err);
      // Copy the original file as fallback
      fs.copyFileSync(file, targetPath);
      console.log(`Copied original (obfuscation failed): ${relativePath}`);
    }
  }

  console.log("Obfuscation completed successfully!");
}

obfuscateFiles().catch(err => {
  console.error("Obfuscation failed:", err);
  process.exit(1);
});
