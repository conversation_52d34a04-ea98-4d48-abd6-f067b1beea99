"use client"

import { useRunForm } from "../run-context"
import {
  Monitor,
  Smartphone,
  Tablet,
  Camera,
  Video,
  LineChart,
  Network,
  Zap,
  Accessibility,
  Bot,
  Database
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"

export function WebRunPreview() {
  const {
    formData,
    environmentSettings,
    reportSettings,
    selectedTestDataEnvironment
  } = useRunForm()

  // Get browser icon
  const getBrowserIcon = () => {
    switch (environmentSettings.browser) {
      case 'chrome':
        return '🌐'
      case 'firefox':
        return '🦊'
      case 'safari':
        return '🧭'
      default:
        return '🌐'
    }
  }

  // Get device type icon
  const getDeviceIcon = () => {
    const viewport = environmentSettings.viewport
    if (!viewport) return <Monitor className="h-4 w-4 text-indigo-500" />
    
    if (viewport.width <= 480) return <Smartphone className="h-4 w-4 text-green-500" />
    if (viewport.width <= 1024) return <Tablet className="h-4 w-4 text-purple-500" />
    return <Monitor className="h-4 w-4 text-indigo-500" />
  }

  return (
    <div className="space-y-4">
      {/* Web-specific settings */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.2,
          type: "spring",
          stiffness: 450,
          damping: 20
        }}
        className="flex justify-between items-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 p-1.5 rounded-md border border-blue-100 dark:border-blue-900/50 hover:shadow-md hover:scale-[1.02] transition-all"
        whileHover={{
          y: -2,
          transition: { duration: 0.2 }
        }}
      >
        <div className="flex items-center p-2">
          <Monitor className="h-5 w-5 mr-2 text-indigo-500" />
          <div>
            <p className="text-sm font-medium">Browser</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {getBrowserIcon()} {environmentSettings.browser?.charAt(0).toUpperCase() + environmentSettings.browser?.slice(1)}
            </p>
          </div>
        </div>
        <div className="flex items-center p-2">
          {getDeviceIcon()}
          <div className="ml-2">
            <p className="text-sm font-medium">Device</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {environmentSettings.device || 'Desktop'} ({environmentSettings.viewport?.width}x{environmentSettings.viewport?.height})
            </p>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.3,
          type: "spring",
          stiffness: 450,
          damping: 20
        }}
        className="flex justify-between items-center bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/30 dark:to-teal-950/30 p-1.5 rounded-md border border-emerald-100 dark:border-emerald-900/50 hover:shadow-md hover:scale-[1.02] transition-all"
        whileHover={{
          y: -2,
          transition: { duration: 0.2 }
        }}
      >
        <div className="flex items-center p-2">
          <Bot className="h-5 w-5 mr-2 text-emerald-500" />
          <div>
            <p className="text-sm font-medium">AI Model</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {environmentSettings.aiModelName || environmentSettings.aiModel || 'Default AI Model'}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Test Data Environment */}
      {selectedTestDataEnvironment && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 0.4,
            type: "spring",
            stiffness: 450,
            damping: 20
          }}
          className="flex justify-between items-center bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 p-1.5 rounded-md border border-green-100 dark:border-green-900/50 hover:shadow-md hover:scale-[1.02] transition-all"
          whileHover={{
            y: -2,
            transition: { duration: 0.2 }
          }}
        >
          <div className="flex items-center p-2">
            <Database className="h-5 w-5 mr-2 text-green-500" />
            <div>
              <p className="text-sm font-medium">Test Data Environment</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {selectedTestDataEnvironment.name}
                {selectedTestDataEnvironment.description && (
                  <span className="ml-1">- {selectedTestDataEnvironment.description}</span>
                )}
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Report settings */}
      <div className="mt-6">
        <p className="text-sm font-medium mb-2 text-indigo-700 dark:text-indigo-300">Report Settings</p>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.1, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.takeScreenshots
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <Camera className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.takeScreenshots ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.takeScreenshots ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Screenshots
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.15, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.takeVideos
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <Video className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.takeVideos ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.takeVideos ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Videos
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.2, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.pageMetrics
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <LineChart className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.pageMetrics ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.pageMetrics ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Metrics
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.25, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.networkData
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <Network className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.networkData ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.networkData ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Network
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.3, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.tracingData
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <Zap className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.tracingData ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.tracingData ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Tracing
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.35, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.accessibilityData
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <Accessibility className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.accessibilityData ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.accessibilityData ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              A11y
            </span>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
