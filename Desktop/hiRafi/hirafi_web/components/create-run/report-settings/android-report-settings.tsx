"use client"

import { useRunForm } from "../run-context"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { HelpCircle, ListOrdered, Activity, Settings2 } from "lucide-react"
import { motion } from "framer-motion"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"

export function AndroidReportSettings() {
  const { formData, updateFormData, reportSettings, updateReportSettings } = useRunForm()

  // Sequential run handler
  const handleToggleSequentialRun = (checked: boolean) => {
    const currentOptions = formData.options || {}
    updateFormData({
      options: {
        ...currentOptions,
        sequentialRun: checked
      }
    })
  }

  // Performance metrics collection handler
  const handleToggleMetricsCollection = (checked: boolean) => {
    updateReportSettings({ collectMetrics: checked })
  }

  // Video quality handler
  const handleVideoQualityChange = (value: string) => {
    updateReportSettings({ videoQuality: value as 'low' | 'medium' | 'high' })
  }

  // Metrics interval handler
  const handleMetricsIntervalChange = (value: number[]) => {
    updateReportSettings({ metricsInterval: value[0] * 1000 }) // Convert to milliseconds
  }

  return (
    <TooltipProvider>
      {/* Sequential Run */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
            <ListOrdered className="h-5 w-5 text-blue-500" />
          </div>
          <div>
            <Label htmlFor="sequential-run" className="text-sm font-medium">
              Sequential Test Execution
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Run all tests sequentially on the same node (instead of in parallel)
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="sequential-run"
            checked={formData.options?.sequentialRun || false}
            onCheckedChange={handleToggleSequentialRun}
            className="data-[state=checked]:bg-blue-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">When enabled, all tests will be run sequentially on the same test node. This is useful to prevent tests from interfering with each other.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>

      {/* Performance Metrics Collection */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-purple-100 dark:bg-purple-900/30 p-2 rounded-full">
            <Activity className="h-5 w-5 text-purple-500" />
          </div>
          <div>
            <Label htmlFor="collect-metrics" className="text-sm font-medium">
              Collect Performance Metrics
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Monitor CPU, memory, battery, and network usage during test execution
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="collect-metrics"
            checked={reportSettings.collectMetrics || false}
            onCheckedChange={handleToggleMetricsCollection}
            className="data-[state=checked]:bg-purple-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">When enabled, performance metrics will be collected during test execution, providing insights into app performance.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>

      {/* Advanced Settings */}
      {(reportSettings.takeVideos || reportSettings.collectMetrics) && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
        >
          <div className="flex items-center gap-2 mb-4">
            <Settings2 className="h-5 w-5 text-amber-500" />
            <h3 className="text-sm font-medium">Advanced Settings</h3>
          </div>

          {/* Video Quality Settings - only show if video recording is enabled */}
          {reportSettings.takeVideos && (
            <div className="mb-4">
              <Label htmlFor="video-quality" className="text-xs font-medium mb-2 block">
                Video Quality
              </Label>
              <Select
                value={reportSettings.videoQuality || 'medium'}
                onValueChange={handleVideoQualityChange}
              >
                <SelectTrigger id="video-quality" className="w-full">
                  <SelectValue placeholder="Select video quality" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low (480p, 2 Mbps)</SelectItem>
                  <SelectItem value="medium">Medium (720p, 4 Mbps)</SelectItem>
                  <SelectItem value="high">High (1080p, 6 Mbps)</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">Higher quality results in larger video files</p>
            </div>
          )}

          {/* Metrics Collection Interval - only show if metrics collection is enabled */}
          {reportSettings.collectMetrics && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <Label htmlFor="metrics-interval" className="text-xs font-medium">
                  Metrics Collection Interval
                </Label>
                <span className="text-xs font-medium">
                  {Math.round((reportSettings.metricsInterval || 10000) / 1000)} seconds
                </span>
              </div>
              <Slider
                id="metrics-interval"
                defaultValue={[(reportSettings.metricsInterval || 10000) / 1000]}
                max={30}
                min={1}
                step={1}
                onValueChange={handleMetricsIntervalChange}
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                How frequently to collect metrics (1-30 seconds)
              </p>
            </div>
          )}
        </motion.div>
      )}
    </TooltipProvider>
  )
}
