/**
 * Zephyr Scale service module
 * Handles Zephyr Scale Cloud integration operations
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import {
  isMongoDBInitialized,
  pluginsCollection,
  db,
  reportsCollection,
  runsCollection,
} from './dbConnection.js';

// Zephyr Scale API endpoints
export const ZEPHYR_SCALE_CONFIG = {
  baseUrl: 'https://api.zephyrscale.smartbear.com/v2',
  apiGetProjects: '/projects',
  apiGetTestCases: '/testcases',
  apiGetTestCase: '/testcases/{testCaseKey}',
  apiCreateExecution: '/testexecutions',
  apiGetFolders: '/folders'
};

/**
 * Verify Zephyr Scale connection
 * @param apiToken Zephyr Scale API token
 * @returns Result of the operation
 */
export async function verifyZephyrScaleConnection(apiToken: string) {
  try {
    // Input validation and sanitization
    const trimmedApiToken = apiToken?.trim();

    if (!trimmedApiToken) {
      return {
        success: false,
        message: "API token is required",
        data: null
      };
    }

    // Test connection by getting projects
    const headers = {
      'Authorization': `Bearer ${trimmedApiToken}`,
      'Content-Type': 'application/json'
    };

    const response = await axios.get(`${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiGetProjects}`, {
      headers,
      timeout: 30000
    });

    if (response.status === 200) {
      logger.info('Zephyr Scale connection verified successfully');
      return {
        success: true,
        message: "Connection verified successfully",
        data: {
          projects: response.data.values || response.data || [],
          totalProjects: response.data.values?.length || response.data?.length || 0
        }
      };
    } else {
      logger.warn(`Zephyr Scale connection failed with status: ${response.status}`);
      return {
        success: false,
        message: `Connection failed with status: ${response.status}`,
        data: null
      };
    }
  } catch (error: any) {
    logger.error(`Zephyr Scale connection verification failed: ${error.message}`);
    
    if (error.response) {
      const status = error.response.status;
      const statusText = error.response.statusText;
      
      if (status === 401) {
        return {
          success: false,
          message: "Authentication failed. Please check your API token.",
          data: null
        };
      } else if (status === 403) {
        return {
          success: false,
          message: "Access denied. Please check your permissions.",
          data: null
        };
      } else if (status === 404) {
        return {
          success: false,
          message: "Zephyr Scale API endpoint not found.",
          data: null
        };
      } else {
        return {
          success: false,
          message: `Connection failed: ${status} ${statusText}`,
          data: null
        };
      }
    } else if (error.code === 'ECONNREFUSED') {
      return {
        success: false,
        message: "Connection refused. Please check the Zephyr Scale service availability.",
        data: null
      };
    } else if (error.code === 'ENOTFOUND') {
      return {
        success: false,
        message: "Zephyr Scale API endpoint not found. Please check your network connection.",
        data: null
      };
    } else if (error.code === 'ETIMEDOUT') {
      return {
        success: false,
        message: "Connection timeout. Please try again later.",
        data: null
      };
    } else {
      return {
        success: false,
        message: `Connection failed: ${error.message}`,
        data: null
      };
    }
  }
}

/**
 * Get Zephyr Scale projects
 * @param apiToken Zephyr Scale API token
 * @returns List of Zephyr Scale projects
 */
export async function getZephyrScaleProjects(apiToken: string) {
  try {
    if (!apiToken) {
      return {
        success: false,
        message: "Missing Zephyr Scale API token",
        data: null
      };
    }

    const headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };

    const response = await axios.get(`${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiGetProjects}`, {
      headers,
      timeout: 30000
    });

    if (response.status === 200) {
      const projects = response.data.values || response.data || [];
      
      logger.info(`Retrieved ${projects.length} Zephyr Scale projects`);
      
      return {
        success: true,
        message: "Projects retrieved successfully",
        data: projects.map((project: any) => ({
          id: project.key || project.id,
          name: project.key || project.id, // Zephyr Scale API doesn't provide name, use key instead
          key: project.key || project.id,
          description: project.description || ''
        }))
      };
    } else {
      return {
        success: false,
        message: `Failed to retrieve projects: ${response.status}`,
        data: null
      };
    }
  } catch (error: any) {
    logger.error(`Failed to get Zephyr Scale projects: ${error.message}`);
    return {
      success: false,
      message: `Failed to retrieve projects: ${error.message}`,
      data: null
    };
  }
}

/**
 * Get Zephyr Scale test cases for a project
 * @param apiToken Zephyr Scale API token
 * @param projectKey Project key
 * @param folderId Optional folder ID to filter test cases
 * @returns Test cases from the specified project
 */
export async function getZephyrScaleTestCases(
  apiToken: string,
  projectKey: string,
  folderId?: string
) {
  try {
    if (!apiToken || !projectKey) {
      return {
        success: false,
        message: "Missing Zephyr Scale API token or project key",
        data: null
      };
    }

    const headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };

    // Build query parameters
    const params = new URLSearchParams();
    params.append('projectKey', projectKey);
    params.append('maxResults', '1000'); // Zephyr Scale default max

    if (folderId) {
      params.append('folderId', folderId);
    }

    const url = `${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiGetTestCases}?${params.toString()}`;

    // Enhanced logging for debugging
    logger.info(`[ZephyrScale] Making API request to: ${url}`);
    logger.info(`[ZephyrScale] Request parameters: projectKey=${projectKey}, folderId=${folderId || 'undefined'}, maxResults=1000`);

    const response = await axios.get(url, {
      headers,
      timeout: 30000
    });

    logger.info(`[ZephyrScale] API response status: ${response.status}`);
    logger.info(`[ZephyrScale] API response data structure: ${JSON.stringify({
      hasValues: !!response.data.values,
      valuesLength: response.data.values?.length || 0,
      dataLength: Array.isArray(response.data) ? response.data.length : 'not array',
      responseKeys: Object.keys(response.data || {})
    })}`);

    if (response.status === 200) {
      const testCases = response.data.values || response.data || [];

      logger.info(`[ZephyrScale] Retrieved ${testCases.length} test cases for project ${projectKey}${folderId ? ` in folder ${folderId}` : ' (all folders)'}`);

      // Log first test case structure for debugging
      if (testCases.length > 0) {
        logger.info(`[ZephyrScale] Sample test case structure: ${JSON.stringify({
          key: testCases[0].key,
          name: testCases[0].name,
          hasFolder: !!testCases[0].folder,
          folderId: testCases[0].folder?.id,
          folderName: testCases[0].folder?.name
        })}`);
      }

      // Enhanced response with additional metadata for better user experience
      const responseData = {
        success: true,
        message: "Test cases retrieved successfully",
        data: testCases.map((testCase: any) => ({
          id: testCase.key || testCase.id,
          key: testCase.key,
          name: testCase.name,
          objective: testCase.objective || '',
          precondition: testCase.precondition || '',
          priority: testCase.priority?.name || 'Normal',
          status: testCase.status?.name || 'Draft',
          folder: testCase.folder?.name || '',
          folderId: testCase.folder?.id || null,
          projectKey: testCase.project?.key || projectKey,
          labels: testCase.labels || [],
          customFields: testCase.customFields || {}
        })),
        metadata: {
          totalCount: testCases.length,
          filteredByFolder: !!folderId,
          folderId: folderId || null,
          projectKey: projectKey
        }
      };

      // If filtering by folder and no results, provide helpful context
      if (folderId && testCases.length === 0) {
        logger.info(`[ZephyrScale] No test cases found in folder ${folderId}. This may indicate test cases are not assigned to folders.`);
        responseData.message = "No test cases found in the selected folder. Test cases may not be assigned to folders in this project.";
        (responseData.metadata as any).suggestion = "Try selecting 'All Folders' to see unassigned test cases, or check if test cases need to be moved to folders in Zephyr Scale.";
      }

      return responseData;
    } else {
      logger.error(`[ZephyrScale] Unexpected response status: ${response.status}`);
      return {
        success: false,
        message: `Failed to retrieve test cases: ${response.status}`,
        data: null
      };
    }
  } catch (error: any) {
    logger.error(`[ZephyrScale] Failed to get test cases: ${error.message}`);
    if (error.response) {
      logger.error(`[ZephyrScale] Error response status: ${error.response.status}`);
      logger.error(`[ZephyrScale] Error response data: ${JSON.stringify(error.response.data)}`);
    }
    return {
      success: false,
      message: `Failed to retrieve test cases: ${error.message}`,
      data: null
    };
  }
}

/**
 * Get Zephyr Scale folders for a project
 * @param apiToken Zephyr Scale API token
 * @param projectKey Project key
 * @returns Folders from the specified project
 */
export async function getZephyrScaleFolders(apiToken: string, projectKey: string) {
  try {
    if (!apiToken || !projectKey) {
      return {
        success: false,
        message: "Missing Zephyr Scale API token or project key",
        data: null
      };
    }

    const headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };

    const params = new URLSearchParams();
    params.append('projectKey', projectKey);
    params.append('folderType', 'TEST_CASE');

    const url = `${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiGetFolders}?${params.toString()}`;

    const response = await axios.get(url, {
      headers,
      timeout: 30000
    });

    if (response.status === 200) {
      const folders = response.data.values || response.data || [];

      logger.info(`Retrieved ${folders.length} Zephyr Scale folders for project ${projectKey}`);

      return {
        success: true,
        message: "Folders retrieved successfully",
        data: folders.map((folder: any) => ({
          id: folder.id,
          name: folder.name,
          type: folder.type,
          projectKey: folder.project?.key || projectKey
        }))
      };
    } else {
      return {
        success: false,
        message: `Failed to retrieve folders: ${response.status}`,
        data: null
      };
    }
  } catch (error: any) {
    logger.error(`Failed to get Zephyr Scale folders: ${error.message}`);
    return {
      success: false,
      message: `Failed to retrieve folders: ${error.message}`,
      data: null
    };
  }
}

/**
 * Update Zephyr Scale configuration for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @param apiToken Zephyr Scale API token
 * @param projectsData Array of projects with names {id: string, name: string, key: string}
 * @param foldersData Array of folders with names {id: string, name: string}
 * @returns Object with success status
 */
export async function updateZephyrScaleConfig(
  teamId: string,
  companyId: string,
  apiToken: string,
  projectsData: any[],
  foldersData: any[]
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return {
        success: false,
        message: "Database connection not established",
        data: null
      };
    }

    if (!teamId || !companyId) {
      return {
        success: false,
        message: "Team ID and Company ID are required",
        data: null
      };
    }

    // Validate API token
    if (!apiToken || apiToken.trim() === '') {
      return {
        success: false,
        message: "API token is required",
        data: null
      };
    }

    // Verify connection before saving
    const connectionResult = await verifyZephyrScaleConnection(apiToken);
    if (!connectionResult.success) {
      return {
        success: false,
        message: `Connection verification failed: ${connectionResult.message}`,
        data: null
      };
    }

    // Prepare configuration data
    const configData = {
      apiToken: apiToken.trim(),
      projectsData: projectsData || [],
      foldersData: foldersData || [],
      updatedAt: new Date().toISOString()
    };

    // Find existing plugins document for this team and company
    const existingPlugins = await pluginsCollection.findOne({ teamId, companyId });

    if (existingPlugins) {
      // Update existing document
      const pluginIndex = existingPlugins.plugins.findIndex((p: any) => p.id === 'zephyrscale');

      if (pluginIndex >= 0) {
        // Update existing Zephyr Scale plugin
        existingPlugins.plugins[pluginIndex].config = configData;
        existingPlugins.plugins[pluginIndex].active = true;
        existingPlugins.plugins[pluginIndex].updatedAt = new Date().toISOString();
      } else {
        // Add new Zephyr Scale plugin
        existingPlugins.plugins.push({
          id: 'zephyrscale',
          name: 'Zephyr Scale',
          description: 'Zephyr Scale Cloud integration for test management',
          active: true,
          config: configData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }

      await pluginsCollection.updateOne(
        { teamId, companyId },
        { $set: { plugins: existingPlugins.plugins } }
      );
    } else {
      // Create new plugins document
      const newPluginsDoc = {
        teamId,
        companyId,
        plugins: [{
          id: 'zephyrscale',
          name: 'Zephyr Scale',
          description: 'Zephyr Scale Cloud integration for test management',
          active: true,
          config: configData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }]
      };

      await pluginsCollection.insertOne(newPluginsDoc);
    }

    logger.info(`Zephyr Scale configuration updated for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: "Zephyr Scale configuration saved successfully",
      data: configData
    };
  } catch (error: any) {
    logger.error(`Failed to update Zephyr Scale configuration: ${error.message}`);
    return {
      success: false,
      message: `Failed to save configuration: ${error.message}`,
      data: null
    };
  }
}

/**
 * Get Zephyr Scale configuration for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @returns Zephyr Scale plugin configuration
 */
export async function getZephyrScaleConfig(teamId: string, companyId: string) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return {
        success: false,
        message: "Database connection not established",
        data: null,
        plugin: null
      };
    }

    if (!teamId || !companyId) {
      return {
        success: false,
        message: "Team ID and Company ID are required",
        data: null,
        plugin: null
      };
    }

    // Find plugins document for this team and company
    const pluginsDoc = await pluginsCollection.findOne({ teamId, companyId });

    if (!pluginsDoc) {
      return {
        success: false,
        message: "No plugins configuration found for this team",
        data: null,
        plugin: null
      };
    }

    // Find Zephyr Scale plugin
    const zephyrScalePlugin = pluginsDoc.plugins.find((p: any) => p.id === 'zephyrscale');

    if (!zephyrScalePlugin) {
      return {
        success: false,
        message: "Zephyr Scale plugin not configured for this team",
        data: null,
        plugin: null
      };
    }

    // Return configuration without sensitive data in logs
    logger.info(`Retrieved Zephyr Scale configuration for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: "Configuration retrieved successfully",
      data: zephyrScalePlugin.config,
      plugin: {
        id: zephyrScalePlugin.id,
        name: zephyrScalePlugin.name,
        description: zephyrScalePlugin.description,
        active: zephyrScalePlugin.active,
        createdAt: zephyrScalePlugin.createdAt,
        updatedAt: zephyrScalePlugin.updatedAt
      }
    };
  } catch (error: any) {
    logger.error(`Failed to get Zephyr Scale configuration: ${error.message}`);
    return {
      success: false,
      message: `Failed to retrieve configuration: ${error.message}`,
      data: null,
      plugin: null
    };
  }
}

/**
 * Delete Zephyr Scale plugin configuration for a team and company
 * @param teamId Team ID
 * @param companyId Company ID
 * @returns Result of the operation
 */
export async function deleteZephyrScaleConfig(teamId: string, companyId: string) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return {
        success: false,
        message: "Database connection not established"
      };
    }

    if (!teamId || !companyId) {
      return {
        success: false,
        message: "Team ID and Company ID are required"
      };
    }

    // Find existing plugins document
    const existingPlugins = await pluginsCollection.findOne({ teamId, companyId });

    if (!existingPlugins) {
      return {
        success: false,
        message: "No plugins configuration found for this team"
      };
    }

    // Remove Zephyr Scale plugin from the plugins array
    const updatedPlugins = existingPlugins.plugins.filter((p: any) => p.id !== 'zephyrscale');

    if (updatedPlugins.length === existingPlugins.plugins.length) {
      return {
        success: false,
        message: "Zephyr Scale plugin not found in configuration"
      };
    }

    // Update the document with the filtered plugins array
    await pluginsCollection.updateOne(
      { teamId, companyId },
      { $set: { plugins: updatedPlugins } }
    );

    logger.info(`Zephyr Scale configuration deleted for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: "Zephyr Scale configuration deleted successfully"
    };
  } catch (error: any) {
    logger.error(`Failed to delete Zephyr Scale configuration: ${error.message}`);
    return {
      success: false,
      message: `Failed to delete configuration: ${error.message}`
    };
  }
}

/**
 * Get detailed information for a specific test case
 * @param apiToken Zephyr Scale API token
 * @param testCaseKey Test case key
 * @returns Test case details including steps
 */
export async function getZephyrScaleTestCaseDetails(
  apiToken: string,
  testCaseKey: string
) {
  try {
    if (!apiToken || !testCaseKey) {
      return {
        success: false,
        message: "Missing Zephyr Scale API token or test case key",
        data: null
      };
    }

    const headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };

    const url = `${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiGetTestCase.replace('{testCaseKey}', testCaseKey)}`;

    const response = await axios.get(url, {
      headers,
      timeout: 30000
    });

    if (response.status === 200) {
      const testCase = response.data;
      let testSteps = testCase.testScript?.steps || [];

      // If steps array is empty but testScript.self URL exists, fetch steps from the URL
      if (testSteps.length === 0 && testCase.testScript?.self) {
        try {
          logger.info(`Fetching test steps from testScript URL: ${testCase.testScript.self}`);

          const stepsResponse = await axios.get(testCase.testScript.self, {
            headers,
            timeout: 30000
          });

          if (stepsResponse.status === 200 && stepsResponse.data) {
            // The response might be an array of steps or an object containing steps
            let rawSteps = [];
            if (Array.isArray(stepsResponse.data)) {
              rawSteps = stepsResponse.data;
            } else if (stepsResponse.data.values && Array.isArray(stepsResponse.data.values)) {
              rawSteps = stepsResponse.data.values;
            } else if (stepsResponse.data.steps && Array.isArray(stepsResponse.data.steps)) {
              rawSteps = stepsResponse.data.steps;
            }

            // Transform Zephyr Scale step format to a more usable format
            testSteps = rawSteps.map((step: any, index: number) => {
              if (step.inline) {
                return {
                  id: index + 1,
                  description: step.inline.description || '',
                  testData: step.inline.testData || '',
                  expectedResult: step.inline.expectedResult || '',
                  customFields: step.inline.customFields || {}
                };
              } else if (step.testCase) {
                // Handle test case reference steps
                return {
                  id: index + 1,
                  description: `Reference to test case: ${step.testCase.key || 'Unknown'}`,
                  testData: '',
                  expectedResult: '',
                  testCaseRef: step.testCase
                };
              } else {
                // Fallback for unknown step format
                return {
                  id: index + 1,
                  description: step.description || JSON.stringify(step),
                  testData: step.testData || '',
                  expectedResult: step.expectedResult || ''
                };
              }
            });

            logger.info(`Successfully fetched and transformed ${testSteps.length} test steps from testScript URL`);
          }
        } catch (stepsError: any) {
          logger.warn(`Failed to fetch test steps from testScript URL: ${stepsError.message}`);
          // Continue with empty steps array rather than failing the entire request
        }
      }

      logger.info(`Retrieved Zephyr Scale test case details for: ${testCaseKey} with ${testSteps.length} steps`);

      return {
        success: true,
        message: "Test case details retrieved successfully",
        data: {
          key: testCase.key,
          name: testCase.name,
          objective: testCase.objective || '',
          precondition: testCase.precondition || '',
          priority: testCase.priority?.name || 'Normal',
          status: testCase.status?.name || 'Draft',
          folder: testCase.folder?.name || '',
          folderId: testCase.folder?.id || null,
          projectKey: testCase.project?.key || '',
          labels: testCase.labels || [],
          customFields: testCase.customFields || {},
          testScript: testCase.testScript || {},
          steps: testSteps
        }
      };
    } else {
      return {
        success: false,
        message: `Failed to retrieve test case details: ${response.status}`,
        data: null
      };
    }
  } catch (error: any) {
    logger.error(`Failed to get Zephyr Scale test case details: ${error.message}`);
    return {
      success: false,
      message: `Failed to retrieve test case details: ${error.message}`,
      data: null
    };
  }
}

/**
 * Create a test execution in Zephyr Scale
 * @param apiToken Zephyr Scale API token
 * @param testCaseKey Test case key
 * @param projectKey Project key
 * @param statusName Execution status (Pass, Fail, etc.)
 * @param comment Optional comment
 * @returns Result of the operation
 */
export async function createZephyrScaleExecution(
  apiToken: string,
  testCaseKey: string,
  projectKey: string,
  statusName: string,
  comment?: string
) {
  try {
    if (!apiToken || !testCaseKey || !projectKey || !statusName) {
      return {
        success: false,
        message: "Missing required parameters for test execution",
        data: null
      };
    }

    const headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };

    const executionData = {
      projectKey,
      testCaseKey,
      statusName,
      comment: comment || `Automated execution - ${new Date().toISOString()}`,
      executedOn: new Date().toISOString()
    };

    const response = await axios.post(
      `${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiCreateExecution}`,
      executionData,
      { headers, timeout: 30000 }
    );

    if (response.status === 201 || response.status === 200) {
      logger.info(`Created Zephyr Scale execution for test case: ${testCaseKey}`);

      return {
        success: true,
        message: "Test execution created successfully",
        data: response.data
      };
    } else {
      return {
        success: false,
        message: `Failed to create test execution: ${response.status}`,
        data: null
      };
    }
  } catch (error: any) {
    logger.error(`Failed to create Zephyr Scale execution: ${error.message}`);
    return {
      success: false,
      message: `Failed to create test execution: ${error.message}`,
      data: null
    };
  }
}
