/**
 * Feedback service module
 * Handles operations related to user feedback
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../utils/logger.js';
import { 
  isMongoDBInitialized, 
  feedbackCollection,
  mongoTimestamp
} from './dbConnection.js';

/**
 * Interface representing a Feedback document
 */
export interface Feedback {
  id: string;
  userId: string;
  email: string;
  subject: string;
  feedback: string;
  screenshot?: string;
  screenshotData?: string;
  createdAt: Date;
  status: 'new' | 'in-progress' | 'resolved' | 'closed';
}

/**
 * Create a new feedback entry
 * @param feedback Feedback data
 * @returns Created feedback
 */
export async function createFeedback(feedback: Partial<Feedback>) {
  try {
    if (!isMongoDBInitialized() || !feedbackCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    // Validate required fields
    if (!feedback || !feedback.userId || !feedback.feedback) {
      return { success: false, message: 'Missing required fields: userId, feedback', data: null };
    }

    // Generate ID if not provided
    if (!feedback.id) {
      feedback.id = uuidv4();
    }

    // Set defaults
    const newFeedback: Feedback = {
      id: feedback.id,
      userId: feedback.userId,
      email: feedback.email || '' ,
      subject: feedback.subject || 'Feedback',
      feedback: feedback.feedback,
      createdAt: mongoTimestamp(),
      status: 'new',
      ...(feedback.screenshot && { screenshot: feedback.screenshot }),
      ...(feedback.screenshotData && { screenshotData: feedback.screenshotData })
    };
    
    logger.info(`Creating feedback with ID: ${newFeedback.id} from user: ${newFeedback.userId}`);
    const result = await feedbackCollection.insertOne(newFeedback);
    
    return { 
      success: true, 
      message: 'Feedback created successfully', 
      data: newFeedback 
    };
  } catch (error) {
    logger.error(`Error creating feedback: ${error}`);
    return { success: false, message: `Error creating feedback: ${error}`, data: null };
  }
}

/**
 * Get all feedback entries with optional filtering
 * @param options Optional parameters for pagination and filtering
 * @returns Array of feedback entries
 */
export async function getAllFeedback(options: {
  limit?: number,
  offset?: number,
  status?: string,
  userId?: string,
  excludeScreenshotData?: boolean
} = {}) {
  try {
    if (!isMongoDBInitialized() || !feedbackCollection) {
      return { success: false, message: 'Database connection not established', data: null, count: 0 };
    }

    const {
      limit = 50,
      offset = 0,
      status,
      userId,
      excludeScreenshotData = true
    } = options;

    // Build query
    const query: any = {};
    if (status) query.status = status;
    if (userId) query.userId = userId;

    // Projection to exclude screenshot data if needed
    const projection: any = {};
    if (excludeScreenshotData) {
      projection.screenshotData = 0;
    }

    logger.info(`Fetching feedback with options: ${JSON.stringify(options)}`);
    
    // Get total count for pagination
    const totalCount = await feedbackCollection.countDocuments(query);
    
    // Get feedback entries
    const feedback = await feedbackCollection
      .find(query, { projection })
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .toArray();
    
    return { 
      success: true, 
      message: 'Feedback retrieved successfully', 
      data: feedback, 
      count: feedback.length,
      totalCount,
      page: Math.floor(offset / limit) + 1,
      totalPages: Math.ceil(totalCount / limit)
    };
  } catch (error) {
    logger.error(`Error fetching feedback: ${error}`);
    return { success: false, message: `Error fetching feedback: ${error}`, data: null, count: 0 };
  }
}

/**
 * Get feedback by ID
 * @param feedbackId Feedback ID
 * @param excludeScreenshotData Whether to exclude screenshot data
 * @returns Feedback data
 */
export async function getFeedbackById(feedbackId: string, excludeScreenshotData: boolean = false) {
  try {
    if (!isMongoDBInitialized() || !feedbackCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    // Projection to exclude screenshot data if needed
    const projection: any = {};
    if (excludeScreenshotData) {
      projection.screenshotData = 0;
    }

    logger.info(`Fetching feedback with ID: ${feedbackId}`);
    const feedback = await feedbackCollection.findOne({ id: feedbackId }, { projection });
    
    if (!feedback) {
      return { success: false, message: 'Feedback not found', data: null };
    }
    
    return { 
      success: true, 
      message: 'Feedback retrieved successfully', 
      data: feedback 
    };
  } catch (error) {
    logger.error(`Error fetching feedback: ${error}`);
    return { success: false, message: `Error fetching feedback: ${error}`, data: null };
  }
}

/**
 * Update feedback status
 * @param feedbackId Feedback ID
 * @param status New status
 * @returns Updated feedback
 */
export async function updateFeedbackStatus(feedbackId: string, status: 'new' | 'in-progress' | 'resolved' | 'closed') {
  try {
    if (!isMongoDBInitialized() || !feedbackCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!feedbackId) {
      return { success: false, message: 'Missing required field: feedbackId', data: null };
    }

    logger.info(`Updating feedback status for ID: ${feedbackId} to: ${status}`);
    
    const result = await feedbackCollection.findOneAndUpdate(
      { id: feedbackId },
      { $set: { status, updatedAt: mongoTimestamp() } },
      { returnDocument: 'after' }
    );
    
    if (!result) {
      return { success: false, message: 'Feedback not found', data: null };
    }
    
    return { 
      success: true, 
      message: 'Feedback status updated successfully', 
      data: result 
    };
  } catch (error) {
    logger.error(`Error updating feedback status: ${error}`);
    return { success: false, message: `Error updating feedback status: ${error}`, data: null };
  }
} 