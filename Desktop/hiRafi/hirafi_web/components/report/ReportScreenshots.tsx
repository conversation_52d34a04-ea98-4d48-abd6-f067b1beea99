"use client"

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageIcon, ZoomIn } from 'lucide-react';
import { ReportDetail } from '@/hooks/useReport';
import { Lightbox } from '@/components/ui/lightbox';
import { detectReportPlatform } from '@/lib/utils/platform-utils';

type Step = ReportDetail['steps'][0];

interface ReportScreenshotsProps {
  steps?: Step[] | null; // Make steps optional to handle undefined/null cases
  report?: ReportDetail | null; // Add report prop for platform detection
}

export function ReportScreenshots({ steps, report }: ReportScreenshotsProps) {
  // Collect all steps with screenshots including nested steps
  const allStepsWithScreenshots: Array<{step: any, isNested?: boolean, parentStep?: any}> = [];

  // Add null check for steps to prevent forEach error
  if (!steps || !Array.isArray(steps)) {
    return (
      <div className="col-span-3 flex flex-col items-center justify-center py-12 text-center">
        <ImageIcon className="h-12 w-12 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No steps available</h3>
        <p className="text-gray-500 dark:text-gray-400 max-w-md">
          No test steps data is available for this report.
        </p>
      </div>
    );
  }

  steps.forEach(step => {
    // Add main step if it has screenshots
    if (step.beforeScreenshotUrl || step.afterScreenshotUrl) {
      allStepsWithScreenshots.push({ step });
    }
    
    // Add nested steps from control flow data
    if (step.controlFlowData) {
      // IF-ELSE nested steps
      if (step.controlFlowData.trueSteps) {
        step.controlFlowData.trueSteps.forEach((nestedStep: any) => {
          if (nestedStep.beforeScreenshotUrl || nestedStep.afterScreenshotUrl) {
            allStepsWithScreenshots.push({ 
              step: nestedStep, 
              isNested: true, 
              parentStep: step 
            });
          }
        });
      }
      if (step.controlFlowData.falseSteps) {
        step.controlFlowData.falseSteps.forEach((nestedStep: any) => {
          if (nestedStep.beforeScreenshotUrl || nestedStep.afterScreenshotUrl) {
            allStepsWithScreenshots.push({ 
              step: nestedStep, 
              isNested: true, 
              parentStep: step 
            });
          }
        });
      }
    }
  });

  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Detect platform for responsive sizing
  const platform = detectReportPlatform(report || null);
  const isAndroid = platform === 'android';

  // Extract all screenshot URLs for the lightbox (prioritize afterScreenshotUrl, fallback to beforeScreenshotUrl)
  const allScreenshots = allStepsWithScreenshots
    .map(({step}) => step.afterScreenshotUrl || step.beforeScreenshotUrl)
    .filter(Boolean) as string[];

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index);
    setLightboxOpen(true);
  };

  if (allStepsWithScreenshots.length === 0) {
    return (
      <div className="col-span-3 flex flex-col items-center justify-center py-12 text-center">
        <ImageIcon className="h-12 w-12 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No screenshots available</h3>
        <p className="text-gray-500 dark:text-gray-400 max-w-md">
          This test run doesn't include any screenshots.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className={`grid gap-4 ${
        isAndroid
          ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5' // More columns for smaller Android screenshots
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' // Original layout for web
      }`}>
        {allStepsWithScreenshots.map(({step, isNested, parentStep}, index) => (
          <Card key={step.id || index} className="overflow-hidden">
            <div
              className={`relative bg-gray-100 dark:bg-gray-800 cursor-pointer group ${
                isAndroid
                  ? 'aspect-[9/16]' // Mobile aspect ratio for Android screenshots
                  : 'aspect-video' // Original video aspect ratio for web
              }`}
              onClick={() => handleImageClick(index)}
            >
              {(step.afterScreenshotUrl || step.beforeScreenshotUrl) ? (
                <img
                  src={step.afterScreenshotUrl || step.beforeScreenshotUrl!} // Prioritize afterScreenshotUrl, fallback to beforeScreenshotUrl
                  alt={`Screenshot for ${step.name}`}
                  className={`absolute inset-0 w-full h-full transition-transform group-hover:scale-105 ${
                    isAndroid
                      ? 'object-contain' // Contain for mobile screenshots to show full screen
                      : 'object-cover' // Cover for web screenshots
                  }`}
                  loading="lazy"
                />
              ) : (
                <div className="absolute inset-0 bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                  <div className="text-center">
                    <ImageIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">No screenshot available</p>
                  </div>
                </div>
              )}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 flex items-center justify-center transition-all opacity-0 group-hover:opacity-100">
                <div className="bg-white/90 dark:bg-black/80 p-2 rounded-full">
                  <ZoomIn className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </div>
            <CardContent className="p-3">
              <p className="text-sm font-medium truncate">
                {isNested && parentStep ? (
                  <span>
                    <span className="text-gray-500 text-xs">({parentStep.type}) </span>
                    {step.name || step.type}
                  </span>
                ) : (
                  step.name
                )}
              </p>
              <div className="flex items-center gap-2 mt-1">
                {isNested ? (
                  <p className="text-xs text-gray-500">Nested Step</p>
                ) : (
                  <p className="text-xs text-gray-500">Step {steps ? steps.findIndex(s => s.id === step.id) + 1 : 'N/A'}</p>
                )}
                {step.afterScreenshotUrl ? (
                  <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300">After</Badge>
                ) : step.beforeScreenshotUrl ? (
                  <Badge variant="outline" className="text-xs bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300">Before</Badge>
                ) : null}
                {!step.success && (
                  <Badge variant="destructive" className="text-xs">Failed</Badge>
                )}
                {isNested && (
                  <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300">Nested</Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Lightbox component */}
      <Lightbox
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        images={allScreenshots}
        initialIndex={selectedImageIndex}
      />
    </>
  );
}