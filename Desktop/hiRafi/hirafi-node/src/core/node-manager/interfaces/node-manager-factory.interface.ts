/**
 * Node Manager Factory Interface
 * 
 * Defines the contract for creating and managing node manager instances
 * with proper dependency injection and lifecycle management.
 */

import { INodeManager } from './node-manager.interface.js';
import { ApplicationContainer } from '../../common/di/application-container.js';

/**
 * Node Manager Factory Configuration
 */
export interface NodeManagerFactoryConfig {
  nodeId?: string;
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  websocketConnector?: any;
  config?: any;
}

/**
 * Node Manager Factory Interface
 * 
 * Creates and manages node manager instances with proper dependency
 * injection and service registry management. Replaces singleton
 * patterns with factory-based object creation.
 */
export interface INodeManagerFactory {
  /**
   * Create a node manager with all dependencies
   * @param config Factory configuration
   * @param container Application container
   * @returns Promise resolving to the node manager
   */
  createNodeManager(config: NodeManagerFactoryConfig, container: ApplicationContainer): Promise<INodeManager>;
}
