/**
 * Base Collector Utilities
 * Shared utilities and patterns for CDP-based collectors to reduce code duplication
 */

import { Page, CDPSession } from 'rebrowser-puppeteer';
import { logger } from '../../utils/logger.js';

// State management for collectors
export enum CollectorState {
  IDLE = 'idle',
  COLLECTING = 'collecting',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  DESTROYING = 'destroying',
  DESTROYED = 'destroyed'
}

// Utility function to add timeout to async operations (deprecated - use coordinated collection)
export function withTimeout<T>(promise: Promise<T>, timeoutMs: number, operation: string): Promise<T> {
  logger.warn(`withTimeout: Using deprecated timeout utility for ${operation}. Consider using coordinated collection.`);
  return Promise.race([
    promise,
    new Promise<T>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`${operation} timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    })
  ]);
}

/**
 * Base class for CDP-based collectors
 * Provides common state management and cleanup patterns
 */
export abstract class BaseCDPCollector {
  protected page: Page;
  protected cdpSession: CDPSession | null = null;
  protected state: CollectorState = CollectorState.IDLE;
  protected stateLock: Promise<void> = Promise.resolve();
  protected collectorName: string;

  constructor(page: Page, collectorName: string) {
    this.page = page;
    this.collectorName = collectorName;

    // Listen for page close/disconnect events for automatic cleanup
    // Note: These handlers coordinate with BrowserManager's state machine
    this.page.on('close', () => {
      // Coordinate with BrowserManager's cleanup instead of "fire and forget"
      this.handlePageClose().catch((error: any) => {
        logger.warn(`${this.collectorName}: Error during coordinated cleanup on page close: ${error.message}`);
      });
    });

    // Listen for browser disconnect
    if (this.page.browser()) {
      this.page.browser().on('disconnected', () => {
        // Coordinate with BrowserManager's cleanup instead of "fire and forget"
        this.handleBrowserDisconnect().catch((error: any) => {
          logger.warn(`${this.collectorName}: Error during coordinated cleanup on browser disconnect: ${error.message}`);
        });
      });
    }
  }

  /**
   * Handle page close with coordinated cleanup
   * @private
   */
  private async handlePageClose(): Promise<void> {
    try {
      logger.debug(`${this.collectorName}: Handling page close with coordinated cleanup`);
      await this.destroy();
    } catch (error: any) {
      logger.error(`${this.collectorName}: Error during page close cleanup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Handle browser disconnect with coordinated cleanup
   * @private
   */
  private async handleBrowserDisconnect(): Promise<void> {
    try {
      logger.debug(`${this.collectorName}: Handling browser disconnect with coordinated cleanup`);
      await this.destroy();
    } catch (error: any) {
      logger.error(`${this.collectorName}: Error during browser disconnect cleanup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Safely transition state with proper locking
   */
  protected async transitionState(newState: CollectorState): Promise<boolean> {
    return new Promise((resolve) => {
      this.stateLock = this.stateLock.then(async () => {
        const currentState = this.state;

        // Validate state transition
        const validTransitions: Record<CollectorState, CollectorState[]> = {
          [CollectorState.IDLE]: [CollectorState.COLLECTING, CollectorState.DESTROYING],
          [CollectorState.COLLECTING]: [CollectorState.STOPPING, CollectorState.DESTROYING],
          [CollectorState.STOPPING]: [CollectorState.STOPPED, CollectorState.DESTROYING],
          [CollectorState.STOPPED]: [CollectorState.COLLECTING, CollectorState.DESTROYING],
          [CollectorState.DESTROYING]: [CollectorState.DESTROYED],
          [CollectorState.DESTROYED]: []
        };

        if (validTransitions[currentState].includes(newState)) {
          this.state = newState;
          logger.debug(`${this.collectorName}: State transition ${currentState} -> ${newState}`);
          resolve(true);
        } else {
          logger.warn(`${this.collectorName}: Invalid state transition ${currentState} -> ${newState}`);
          resolve(false);
        }
      });
    });
  }

  /**
   * Get current state
   */
  public getState(): CollectorState {
    return this.state;
  }

  /**
   * Check if collector is destroyed or being destroyed
   */
  protected isDestroyed(): boolean {
    return this.state === CollectorState.DESTROYED || this.state === CollectorState.DESTROYING;
  }

  /**
   * Create or get CDP session
   */
  protected async ensureCDPSession(): Promise<CDPSession> {
    if (!this.cdpSession) {
      this.cdpSession = await this.page.createCDPSession();
    }
    return this.cdpSession;
  }

  /**
   * Cleanup CDP session with timeout protection
   */
  protected async cleanupCDPSession(domains: string[] = []): Promise<void> {
    if (!this.cdpSession) {
      return;
    }

    try {
      // Disable specified domains
      for (const domain of domains) {
        try {
          await withTimeout(
            this.cdpSession.send(`${domain}.disable` as any),
            5000,
            `${domain}.disable`
          );
        } catch (error: any) {
          logger.warn(`${this.collectorName}: Error disabling ${domain} domain: ${error.message}`);
        }
      }

      // Detach session
      try {
        await withTimeout(
          this.cdpSession.detach(),
          5000,
          'CDP session detach'
        );
      } catch (error: any) {
        logger.warn(`${this.collectorName}: Error detaching CDP session: ${error.message}`);
      }

      this.cdpSession = null;
    } catch (error: any) {
      logger.warn(`${this.collectorName}: Error during CDP cleanup: ${error.message}`);
    }
  }

  /**
   * Remove page event listeners
   */
  protected cleanupPageListeners(): void {
    try {
      this.page.removeAllListeners('close');
      if (this.page.browser()) {
        this.page.browser().removeAllListeners('disconnected');
      }
    } catch (error: any) {
      logger.warn(`${this.collectorName}: Error removing page listeners: ${error.message}`);
    }
  }

  /**
   * Abstract method for collector-specific cleanup
   */
  protected abstract performSpecificCleanup(): Promise<void>;

  /**
   * Destroy the collector and free all resources
   */
  public async destroy(): Promise<void> {
    if (this.isDestroyed()) {
      return;
    }

    // Transition to destroying state
    const canTransition = await this.transitionState(CollectorState.DESTROYING);
    if (!canTransition) {
      logger.warn(`${this.collectorName}: Failed to transition to destroying state`);
      return;
    }

    try {
      // Perform collector-specific cleanup
      await this.performSpecificCleanup();

      // Remove page event listeners
      this.cleanupPageListeners();

      // Final state transition
      await this.transitionState(CollectorState.DESTROYED);
      logger.debug(`${this.collectorName}: Destroyed and cleaned up all resources`);
    } catch (error: any) {
      logger.error(`${this.collectorName}: Error during destroy: ${error.message}`);
      // Force transition to destroyed state even on error
      await this.transitionState(CollectorState.DESTROYED);
      throw error;
    }
  }
}

/**
 * Utility function to safely execute CDP operations with error handling
 */
export async function safeCDPOperation<T>(
  operation: () => Promise<T>,
  fallback: T,
  operationName: string,
  collectorName: string
): Promise<T> {
  try {
    return await operation();
  } catch (error: any) {
    logger.warn(`${collectorName}: Error in ${operationName}: ${error.message}`);
    return fallback;
  }
}
