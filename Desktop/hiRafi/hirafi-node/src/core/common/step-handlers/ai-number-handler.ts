/**
 * Common AI Number Handler
 * Handles executing AI number steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Number Handler
 * Handles executing AI number steps using the AI agent
 */
export class CommonAiNumberHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiNumber';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiNumberHandler';
  }

  /**
   * Execute an AI number step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiNumber, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const numberQuery = step.prompt || step.value || step.name || step.description;

    if (!numberQuery) {
      return { success: false, error: 'Missing number query for aiNumber step. Please provide a prompt field.' };
    }

    try {
      // Note: aiNumber does not support deepThink parameter according to Midscene.js API
      // Only domIncluded and screenshotIncluded options are supported

      logger.debug(`CommonAiNumberHandler: Executing number query "${numberQuery}"`);

      const result = await agent.aiNumber(numberQuery || '');
      logger.debug(`aiNumber result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error && typeof result.result === 'number';

      const logEntry = formatAIResultForLog('aiNumber', numberQuery, result);
      logEntry.success = isSuccess;
      logEntry.metadata.result = result.result;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Number query failed: ${numberQuery}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime,
          result: result.result
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Number Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiNumber',
        description: numberQuery,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiNumberHandler: Number query failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 