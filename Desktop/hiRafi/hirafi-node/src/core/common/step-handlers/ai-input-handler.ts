/**
 * Common AI Input Handler
 * Handles executing AI input steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Input Handler
 * Handles executing AI input steps using the AI agent
 */
export class CommonAiInputHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiInput';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiInputHandler';
  }

  /**
   * Execute an AI input step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiInput, we expect:
    // - value: text to input (preferred) or fallback to prompt for backward compatibility
    // - target: target element (preferred) or fallback to name/description
    const textToInput = step.value || step.prompt;
    const inputTarget = step.target || step.name || step.description;

    if (!textToInput) {
      return { success: false, error: 'Missing value or prompt (text to input) for aiInput step' };
    }
    if (!inputTarget) {
      return { success: false, error: 'Missing target element for aiInput step' };
    }

    try {
      const description = `Input "${textToInput}" into "${inputTarget}"`;
      const options = step.deepThink ? { deepThink: true } : {};
      logger.debug(`CommonAiInputHandler: Executing: ${description} with options: ${JSON.stringify(options)}`);

      const result = await agent.aiInput(textToInput, inputTarget, options);
      logger.debug(`aiInput result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error;

      const logEntry = formatAIResultForLog('aiInput', description, result);
      logEntry.success = isSuccess;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Input failed: ${description}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      const description = `Input "${textToInput}" into "${inputTarget}"`;
      const errorMessage = error.message || error.toString();
      logger.info('AI Input Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiInput',
        description: `Input "${textToInput}" into "${inputTarget}"`,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiInputHandler: Input failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 