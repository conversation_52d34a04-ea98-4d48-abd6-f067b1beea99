/**
 * Node Manager Core Service
 * 
 * PHASE 5 FIX: Extracted core node management logic to reduce over-dependencies
 * Handles core node lifecycle operations with minimal dependencies
 */

import { EventEmitter } from 'events';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';
import { INodeIdentityService } from '../../interfaces/node-identity-service.interface.js';
import { INodeStateService } from '../../interfaces/node-state-service.interface.js';
import { ITestQueueWorker } from '../../../test-execution/interfaces/test-queue-worker.interface.js';
import { NodeStatus } from '../../types/node-manager-types.js';
import { TestResult } from '../../../../models/types.js';

/**
 * Core node management interface
 */
export interface INodeManagerCore {
  /**
   * Get current node status
   */
  getStatus(): string;

  /**
   * Get node ID
   */
  getNodeId(): string | null;

  /**
   * Check if node is running a test
   */
  isRunningTest(): boolean;

  /**
   * Get test statistics
   */
  getTestStatistics(): {
    executed: number;
    succeeded: number;
    failed: number;
    uptime: number;
  };

  /**
   * Handle test lifecycle events
   */
  handleTestStarted(testId: string): void;
  handleTestCompleted(testId: string, result: TestResult): void;
  handleTestFailed(testId: string, error: Error): void;
  handleTestReleased(testId: string): void;

  /**
   * Stop a specific test
   */
  stopTest(testId: string): Promise<boolean>;
}

/**
 * Node Manager Core Service
 * 
 * Handles core node management responsibilities with reduced dependencies.
 * This service focuses purely on node state and identity management.
 */
export class NodeManagerCore implements INodeManagerCore {
  private logger: ILoggerService;
  private identityService: INodeIdentityService;
  private stateService: INodeStateService;
  private testQueueWorker: ITestQueueWorker;

  constructor(
    identityService: INodeIdentityService,
    stateService: INodeStateService,
    testQueueWorker: ITestQueueWorker
  ) {
    this.logger = LoggerServiceFactory.createServiceLogger('NodeManagerCore');
    this.identityService = identityService;
    this.stateService = stateService;
    this.testQueueWorker = testQueueWorker;

    this.logger.serviceInit('NodeManagerCore', 'Initialized with TestQueueWorker for test stop coordination');
  }

  /**
   * Get current node status
   */
  getStatus(): string {
    return this.stateService.getDetailedStatus();
  }

  /**
   * Get node ID
   */
  getNodeId(): string | null {
    return this.identityService.getNodeId();
  }

  /**
   * Check if node is running a test
   */
  isRunningTest(): boolean {
    return this.stateService.isRunningTest();
  }

  /**
   * Get test statistics
   */
  getTestStatistics(): {
    executed: number;
    succeeded: number;
    failed: number;
    uptime: number;
  } {
    return this.stateService.getTestStatistics();
  }

  /**
   * Handle test started events
   */
  handleTestStarted(testId: string): void {
    this.logger.test(`Test started: ${testId}`);

    // Simple transition to BUSY state when test starts
    const transitionSuccess = this.stateService.trySetState(
      NodeStatus.AVAILABLE,
      NodeStatus.BUSY,
      testId
    );

    if (!transitionSuccess) {
      this.logger.error(`NodeManagerCore: Could not transition to BUSY for test ${testId}. Current state: ${this.stateService.getStatus()}`);
      return;
    }

    this.logger.debug(`NodeManagerCore: Node state updated to BUSY for test ${testId}`);
  }

  /**
   * Handle test completed events
   */
  handleTestCompleted(testId: string, result: TestResult): void {
    this.logger.test(`✅ Test completed: ${testId}`);

    this.stateService.updateTestCounters(result);

    // Transition back to AVAILABLE state
    const transitionSuccess = this.stateService.trySetState(
      NodeStatus.BUSY,
      NodeStatus.AVAILABLE
    );

    if (!transitionSuccess) {
      this.logger.error(`NodeManagerCore: Could not transition to AVAILABLE after test ${testId} completion. Current state: ${this.stateService.getStatus()}`);
      // Force reset state if atomic transition fails
      this.stateService.reset();
      this.logger.warn(`NodeManagerCore: Reset state service after failed transition for test ${testId} completion`);
    } else {
      this.logger.debug(`NodeManagerCore: Successfully transitioned to AVAILABLE state after test ${testId} completion`);
    }
  }

  /**
   * Handle test failed events
   */
  handleTestFailed(testId: string, error: Error): void {
    this.logger.test(`❌ Test failed: ${testId} - ${error.message}`);

    // Create a simple failed test result for counter updates
    const failedResultForCounters = { status: 'failed', success: false, id: testId } as TestResult;
    this.stateService.updateTestCounters(failedResultForCounters);

    // Transition back to AVAILABLE state
    const transitionSuccess = this.stateService.trySetState(
      NodeStatus.BUSY,
      NodeStatus.AVAILABLE
    );

    if (!transitionSuccess) {
      this.logger.error(`NodeManagerCore: Could not transition to AVAILABLE after test ${testId} failure. Current state: ${this.stateService.getStatus()}`);
      // Force reset state if atomic transition fails
      this.stateService.reset();
      this.logger.warn(`NodeManagerCore: Reset state service after failed transition for test ${testId} failure`);
    } else {
      this.logger.debug(`NodeManagerCore: Successfully transitioned to AVAILABLE state after test ${testId} failure`);
    }
  }

  /**
   * Handle test released events
   */
  handleTestReleased(testId: string): void {
    this.logger.test(`NodeManagerCore: Test ${testId} released`);

    // Ensure node is in AVAILABLE state
    const currentStatus = this.stateService.getStatus();

    if (currentStatus === NodeStatus.STOPPING) {
      // Transition from STOPPING to AVAILABLE
      this.stateService.setStopping(false);
      const transitionSuccess = this.stateService.trySetState(
        NodeStatus.STOPPING,
        NodeStatus.AVAILABLE
      );

      if (!transitionSuccess) {
        this.logger.error(`NodeManagerCore: Could not transition from STOPPING to AVAILABLE after test ${testId} release`);
        this.stateService.reset();
      } else {
        this.logger.info(`NodeManagerCore: Transitioned from STOPPING to AVAILABLE after test ${testId} release`);
      }
    } else if (currentStatus === NodeStatus.BUSY) {
      // Transition from BUSY to AVAILABLE
      const transitionSuccess = this.stateService.trySetState(
        NodeStatus.BUSY,
        NodeStatus.AVAILABLE
      );

      if (!transitionSuccess) {
        this.logger.error(`NodeManagerCore: Could not transition to AVAILABLE after test ${testId} release`);
        this.stateService.reset();
      } else {
        this.logger.debug(`NodeManagerCore: Transitioned to AVAILABLE after test ${testId} release`);
      }
    }
    // If already AVAILABLE, no action needed
  }

  /**
   * Stop a specific test
   */
  async stopTest(testId: string): Promise<boolean> {
    this.logger.test(`NodeManagerCore: Received stop request for test ${testId}`);

    const currentTestId = this.stateService.getCurrentTestId();
    const isRunning = this.stateService.isRunningTest();

    // Flexible state validation - trust the hub as source of truth
    if (currentTestId !== testId) {
      this.logger.warn(`NodeManagerCore: Stop request for test ${testId} received, but current running test is ${currentTestId || 'none'}. Proceeding with stop request from hub as it is the source of truth.`);
    }

    // If node thinks it's not running any test but hub says to stop a test,
    // it might be a state synchronization issue - proceed anyway
    if (!isRunning && !currentTestId) {
      this.logger.warn(`NodeManagerCore: Stop request for test ${testId} received, but node is not running any test. This might be a state synchronization issue. Attempting to stop anyway.`);
    }

    try {
      this.logger.info(`NodeManagerCore: Initiating stop workflow for test ${testId}`);

      // Transition to STOPPING state immediately to prevent new tests
      const transitionToStopping = this.stateService.trySetState(
        NodeStatus.BUSY,
        NodeStatus.STOPPING
      );

      if (!transitionToStopping) {
        // If we can't transition from BUSY to STOPPING, the node might be in an unexpected state
        this.logger.warn(`NodeManagerCore: Could not transition from BUSY to STOPPING for test ${testId}. Current state: ${this.stateService.getDetailedStatus()}. Attempting to force stop anyway.`);

        // Set stopping flag manually as fallback
        this.stateService.setStopping(true);
      } else {
        this.logger.info(`NodeManagerCore: Successfully transitioned to STOPPING state for test ${testId}`);
      }

      // CRITICAL FIX: Send stop command to ExecutionCoordinator via TestQueueWorker
      try {
        const stopResult = await this.testQueueWorker.stopTest(testId);
        if (stopResult) {
          this.logger.info(`NodeManagerCore: Successfully sent stop command to ExecutionCoordinator for test ${testId}`);
        } else {
          this.logger.warn(`NodeManagerCore: Failed to send stop command to ExecutionCoordinator for test ${testId} - test may not be active on this node`);
        }
      } catch (workerError: any) {
        this.logger.error(`NodeManagerCore: Error sending stop command via TestQueueWorker for test ${testId}: ${workerError.message}`);
        // Continue with state management even if worker communication fails
      }

      this.logger.info(`NodeManagerCore: Stop workflow initiated for test ${testId}. Test execution will handle cleanup.`);
      return true;
    } catch (error) {
      this.logger.error(`NodeManagerCore: Error in stop workflow for test ${testId}: ${error}`);

      // Use atomic state transition to return to AVAILABLE state even on error
      const transitionSuccess = this.stateService.trySetState(
        NodeStatus.BUSY,
        NodeStatus.AVAILABLE
      );

      if (!transitionSuccess) {
        this.logger.error(`NodeManagerCore: Could not transition to AVAILABLE after stop error for test ${testId}. Current state: ${this.stateService.getStatus()}`);
        // Force reset state if atomic transition fails
        this.stateService.reset();
        this.logger.warn(`NodeManagerCore: Reset state service after stop error for test ${testId} due to failed transition`);
      } else {
        this.logger.warn(`NodeManagerCore: Successfully transitioned to AVAILABLE state after stop error for test ${testId}`);
      }

      return false;
    }
  }
} 