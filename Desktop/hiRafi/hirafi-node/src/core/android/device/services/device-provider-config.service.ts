/**
 * Device Provider Configuration Service
 * Handles configuration management for different device providers
 */

import { AppiumBaseCapabilities, SauceLabsConfig } from '../device-manager.interface.js';
import { TestiniumConfig } from '../testinium-device-manager.js';
import { ILoggerService } from '../../../../utils/logger-service.js';
import { DeviceManagerUtils } from '../shared/device-manager-utils.js';

/**
 * Provider configuration interface
 */
export interface ProviderConfiguration {
  provider: 'sauceLabs' | 'testinium';
  enabled: boolean;
  config: SauceLabsConfig | TestiniumConfig;
  capabilities: AppiumBaseCapabilities;
}

/**
 * Configuration validation result
 */
export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Device Provider Configuration Service
 * Manages configuration for different device providers with validation and normalization
 */
export class DeviceProviderConfigService {
  private logger: ILoggerService;
  private utils: DeviceManagerUtils;

  constructor(logger: ILoggerService) {
    this.logger = logger;
    this.utils = new DeviceManagerUtils(logger);
  }

  /**
   * Create SauceLabs configuration
   * @param config Raw configuration object
   * @returns Normalized SauceLabs configuration
   */
  createSauceLabsConfig(config: any): ProviderConfiguration {
    // Support both config.android?.sauceLabs and config.sauceLabs paths
    const sauceLabsConfig = config.android?.sauceLabs || config.sauceLabs || {};
    
    // Normalize configuration
    const normalizedConfig: SauceLabsConfig = {
      user: sauceLabsConfig.user || sauceLabsConfig.username || '',
      key: sauceLabsConfig.key || sauceLabsConfig.accessKey || '',
      region: sauceLabsConfig.region || 'us-west-1'
    };

    // Create capabilities with Appium 2 support
    const capabilities: AppiumBaseCapabilities = {
      platformName: 'Android',
      'appium:automationName': 'UiAutomator2',
      'appium:deviceName': 'Android Device',
      'appium:platformVersion': config.android?.capabilities?.['appium:platformVersion'],
      // Auto-accept alerts and permissions for Android
      'appium:autoAcceptAlerts': true,
      'appium:autoGrantPermissions': true,
      'sauce:options': {
        ...sauceLabsConfig.options,
        appiumVersion: sauceLabsConfig.options?.appiumVersion || 'stable'
      }
    };

    return {
      provider: 'sauceLabs',
      enabled: sauceLabsConfig.enabled || this.hasValidCredentials(normalizedConfig, 'sauceLabs'),
      config: normalizedConfig,
      capabilities
    };
  }

  /**
   * Create Testinium configuration
   * @param config Raw configuration object
   * @returns Normalized Testinium configuration
   */
  createTestiniumConfig(config: any): ProviderConfiguration {
    // Support both config.android?.testinium and config.testinium paths
    const testiniumConfig = config.android?.testinium || config.testinium || {};
    
    // Normalize configuration
    const normalizedConfig: TestiniumConfig = {
      apiUrl: testiniumConfig.apiUrl || '',
      clientId: testiniumConfig.clientId || '',
      clientSecret: testiniumConfig.clientSecret || '',
      issuerUri: testiniumConfig.issuerUri || ''
    };

    // Create capabilities with DevicePark support
    const capabilities: AppiumBaseCapabilities = {
      platformName: 'Android',
      'appium:automationName': 'UiAutomator2',
      'appium:deviceName': 'Android Device',
      'appium:platformVersion': config.android?.capabilities?.['appium:platformVersion'],
      // Auto-accept alerts and permissions for Android
      'appium:autoAcceptAlerts': true,
      'appium:autoGrantPermissions': true
    };

    return {
      provider: 'testinium',
      enabled: testiniumConfig.enabled || this.hasValidCredentials(normalizedConfig, 'testinium'),
      config: normalizedConfig,
      capabilities
    };
  }

  /**
   * Validate provider configuration
   * @param providerConfig Provider configuration to validate
   * @returns Validation result
   */
  validateConfiguration(providerConfig: ProviderConfiguration): ConfigValidationResult {
    const result: ConfigValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    // Use shared validation logic
    const validation = this.utils.validateDeviceConfig(providerConfig.config, providerConfig.provider);
    result.errors = validation.errors;
    result.valid = validation.valid;

    // Additional provider-specific validations
    if (providerConfig.provider === 'sauceLabs') {
      this.validateSauceLabsSpecific(providerConfig, result);
    } else if (providerConfig.provider === 'testinium') {
      this.validateTestiniumSpecific(providerConfig, result);
    }

    // Validate capabilities
    this.validateCapabilities(providerConfig.capabilities, result);

    return result;
  }

  /**
   * Auto-detect provider from configuration
   * @param config Raw configuration object
   * @returns Detected provider or null
   */
  autoDetectProvider(config: any): 'sauceLabs' | 'testinium' | null {
    const android = config.android || {};

    // Check explicit provider setting (support both paths)
    if (android.deviceProvider || config.deviceProvider) {
      return android.deviceProvider || config.deviceProvider;
    }

    // Check enabled flags (support both paths)
    if (android.testinium?.enabled || config.testinium?.enabled) {
      return 'testinium';
    }
    if (android.sauceLabs?.enabled || config.sauceLabs?.enabled) {
      return 'sauceLabs';
    }

    // Check for valid credentials
    const testiniumConfig = this.createTestiniumConfig(config);
    const sauceLabsConfig = this.createSauceLabsConfig(config);

    if (testiniumConfig.enabled && !sauceLabsConfig.enabled) {
      return 'testinium';
    }
    if (sauceLabsConfig.enabled && !testiniumConfig.enabled) {
      return 'sauceLabs';
    }

    // Default to SauceLabs for backward compatibility
    return 'sauceLabs';
  }

  /**
   * Get configuration for specific provider
   * @param config Raw configuration object
   * @param provider Provider name
   * @returns Provider configuration
   */
  getProviderConfiguration(config: any, provider: 'sauceLabs' | 'testinium'): ProviderConfiguration {
    if (provider === 'sauceLabs') {
      return this.createSauceLabsConfig(config);
    } else {
      return this.createTestiniumConfig(config);
    }
  }

  /**
   * Check if configuration has valid credentials
   * @param config Configuration object
   * @param provider Provider name
   * @returns True if credentials are valid
   */
  private hasValidCredentials(config: SauceLabsConfig | TestiniumConfig, provider: string): boolean {
    if (provider === 'sauceLabs') {
      const sauceConfig = config as SauceLabsConfig;
      return !!(sauceConfig.user && sauceConfig.key);
    } else if (provider === 'testinium') {
      const testiniumConfig = config as TestiniumConfig;
      return !!(testiniumConfig.apiUrl && testiniumConfig.clientId && 
                testiniumConfig.clientSecret && testiniumConfig.issuerUri);
    }
    return false;
  }

  /**
   * Validate SauceLabs-specific configuration
   * @param providerConfig Provider configuration
   * @param result Validation result to update
   */
  private validateSauceLabsSpecific(providerConfig: ProviderConfiguration, result: ConfigValidationResult): void {
    const config = providerConfig.config as SauceLabsConfig;

    // Check region validity
    const validRegions = ['us-west-1', 'us-east-1', 'eu-central-1', 'apac-southeast-1'];
    if (config.region && !validRegions.includes(config.region)) {
      result.warnings.push(`SauceLabs region '${config.region}' may not be valid. Valid regions: ${validRegions.join(', ')}`);
    }

    // Check capabilities
    const capabilities = providerConfig.capabilities;
    if (capabilities['sauce:options']) {
      const sauceOptions = capabilities['sauce:options'] as any;
      if (sauceOptions.appiumVersion && !['stable', 'latest', 'beta'].includes(sauceOptions.appiumVersion)) {
        // Check if it's a version number pattern
        if (!/^\d+\.\d+\.\d+$/.test(sauceOptions.appiumVersion)) {
          result.warnings.push(`SauceLabs Appium version '${sauceOptions.appiumVersion}' may not be valid`);
        }
      }
    }
  }

  /**
   * Validate Testinium-specific configuration
   * @param providerConfig Provider configuration
   * @param result Validation result to update
   */
  private validateTestiniumSpecific(providerConfig: ProviderConfiguration, result: ConfigValidationResult): void {
    const config = providerConfig.config as TestiniumConfig;

    // Validate URL format
    if (config.apiUrl && !this.isValidUrl(config.apiUrl)) {
      result.errors.push('Testinium API URL is not a valid URL');
    }

    if (config.issuerUri && !this.isValidUrl(config.issuerUri)) {
      result.errors.push('Testinium issuer URI is not a valid URL');
    }

    // Check for HTTPS
    if (config.apiUrl && !config.apiUrl.startsWith('https://')) {
      result.warnings.push('Testinium API URL should use HTTPS for security');
    }
  }

  /**
   * Validate capabilities
   * @param capabilities Capabilities to validate
   * @param result Validation result to update
   */
  private validateCapabilities(capabilities: AppiumBaseCapabilities, result: ConfigValidationResult): void {
    if (!capabilities.platformName) {
      result.errors.push('Platform name is required in capabilities');
    }

    if (capabilities.platformName !== 'Android') {
      result.warnings.push(`Platform name '${capabilities.platformName}' is not Android`);
    }

    if (!capabilities['appium:automationName']) {
      result.warnings.push('Automation name is not specified in capabilities');
    }
  }

  /**
   * Check if string is a valid URL
   * @param url URL to validate
   * @returns True if valid URL
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
