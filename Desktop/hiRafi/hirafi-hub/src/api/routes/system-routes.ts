/**
 * System Routes
 * System monitoring and management endpoints
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { operationQueue } from '../../utils/operationQueue.js';
import { isMongoDBInitialized, getMongoDBInitError } from '../../services/mongo/dbConnection.js';
import { authenticateAdmin, AdminRequest } from '../middleware/adminAuthMiddleware.js';

const router = Router();

/**
 * Get system status
 * @route GET /api/system/status
 * @access Admin
 */
router.get('/status', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Get MongoDB status
    const mongoDBStatus = {
      initialized: isMongoDBInitialized(),
      error: getMongoDBInitError()?.message
    };

    // Get operation queue stats
    const queueStats = operationQueue.getStats();

    // Return system status
    res.json({
      success: true,
      status: {
        mongodb: mongoDBStatus,
        operationQueue: queueStats,
        uptime: process.uptime()
      }
    });
  } catch (error: any) {
    logger.error(`Error getting system status: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Failed to get system status: ${error.message}`
    });
  }
});

export default router;
