/**
 * WebSocket Connector
 * 
 * Refactored WebSocket connector with dependency injection and single responsibility principle.
 * Replaces the singleton WebSocketConnector with a proper service implementation.
 */

import { BaseConnector } from '../core/base-connector.js';
import { IWebSocketConnector } from '../interfaces/websocket-connector.interface.js';
import { IWebSocketConnectionManager, IWebSocketMessageHandler } from '../interfaces/websocket-connector.interface.js';
import { IRegistrationService } from '../interfaces/registration-service.interface.js';
import { IHeartbeatService } from '../interfaces/heartbeat-service.interface.js';
import { IConnectorConfig, IConnectorStats } from '../interfaces/base-connector.interface.js';

/**
 * WebSocket Connector Class
 * Implements WebSocket-based communication with the test hub using dependency injection
 */
export class WebSocketConnector extends BaseConnector implements IWebSocketConnector {
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectInterval = 5000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private messageQueue: any[] = [];
  private defaultHeartbeatInterval = 30000; // 30 seconds
  private clientId: string | null = null;
  private messageQueueTimeout: NodeJS.Timeout | null = null;
  private readonly MESSAGE_QUEUE_TIMEOUT = 60000; // 60 seconds timeout for queued messages

  constructor(
    logger: any,
    config: IConnectorConfig,
    private connectionManager: IWebSocketConnectionManager,
    private messageHandler: IWebSocketMessageHandler,
    private registrationService: IRegistrationService,
    private heartbeatService: IHeartbeatService
  ) {
    super('WebSocket', logger, config);
    this.maxReconnectAttempts = config.reconnectAttempts || 10;
    this.reconnectInterval = config.reconnectInterval || 5000;
    this.clientId = config.clientId || null;
  }

  /**
   * Initialize the WebSocket connector
   * @param config Configuration object
   */
  protected async doInitialize(config: IConnectorConfig): Promise<void> {
    // Update clientId from config if provided
    if (config.clientId) {
      this.clientId = config.clientId;
    }

    // Initialize services
    await this.registrationService.initialize(config);
    await this.heartbeatService.initialize(config);

    // Initialize optional services if they have initialize method
    if (this.connectionManager.initialize) {
      await this.connectionManager.initialize(config);
    }
    if (this.messageHandler.initialize) {
      await this.messageHandler.initialize(config);
    }

    // Setup message handlers
    this.setupMessageHandlers();

    this.logOperation('websocketConnectorInitialized');
  }

  /**
   * Close the WebSocket connector
   */
  protected async doClose(): Promise<void> {
    this.stopHeartbeat();
    
    // CRITICAL FIX: Clear message queue timeout on close
    this.clearMessageQueueTimeout();
    
    await this.connectionManager.close();

    // Close services
    await this.registrationService.close();
    await this.heartbeatService.close();

    // Close optional services if they have close method
    if (this.messageHandler.close) {
      await this.messageHandler.close();
    }

    this.logOperation('websocketConnectorClosed');
  }

  /**
   * Check if the connector is connected
   */
  isConnected(): boolean {
    return this.connectionManager.isConnected();
  }

  /**
   * Connect to WebSocket server
   */
  async connect(): Promise<void> {
    if (this.isConnecting || this.isConnected()) {
      return;
    }

    this.isConnecting = true;
    this.incrementConnectionAttempts();

    try {
      const hubUrl = this.getHubUrl();
      const wsUrl = hubUrl.replace(/^http/, 'ws') + '/ws';

      this.logOperation('websocketConnectAttempt', { wsUrl });

      // Prepare headers with node ID and client ID for proper identification
      const headers: Record<string, string> = {};

      if (this.nodeId) {
        headers['X-Node-ID'] = String(this.nodeId);
        this.logOperation('websocketConnectWithNodeId', { nodeId: this.nodeId });
      }

      if (this.clientId) {
        headers['X-Client-ID'] = String(this.clientId);
        this.logOperation('websocketConnectWithClientId', { clientId: this.clientId });
      }

      await this.connectionManager.connect(wsUrl, headers);

      this.incrementSuccessfulConnections();
      this.isConnecting = false;
      this.reconnectAttempts = 0;

      this.logOperation('websocketConnected', { wsUrl, nodeId: this.nodeId, clientId: this.clientId });

      // Send registration after connection
      await this.sendRegistration();

      // Process any queued messages
      this.processMessageQueue();

    } catch (error) {
      this.isConnecting = false;
      this.handleError(error, 'connect');
      throw error;
    }
  }

  /**
   * Send a message to the hub
   * @param message Message to send
   */
  async sendMessage(message: any): Promise<boolean> {
    if (!this.isConnected()) {
      this.logger.warn('WebSocket: Connection not ready, cannot send message. Queuing message.');
      this.messageQueue.push(message);
      
      // CRITICAL FIX: Start timeout to prevent messages from staying in queue forever
      this.startMessageQueueTimeout();
      
      return false; 
    }

    try {
      const messageString = JSON.stringify(message);
      const success = await this.connectionManager.send(messageString);
      
      if (success) {
        this.logOperation('websocketMessageSent', { type: message.type });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendMessage');
      return false;
    }
  }

  /**
   * Register node with hub
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Optional client ID
   */
  async registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null> {
    try {
      this.registrationService.logRegistrationAttempt(name, capabilities, 'WebSocket');

      const registrationData = this.registrationService.prepareRegistrationData(
        name,
        capabilities,
        clientId
      );

      // Include the current nodeId in the registration data if available
      if (this.nodeId) {
        registrationData.nodeId = String(this.nodeId);
      }

      const message = {
        type: 'register',
        data: registrationData
      };

      const success = await this.sendMessage(message);

      if (success) {
        this.logOperation('websocketRegistrationSent', { name, capabilities, nodeId: this.nodeId });
        // Node ID will be set when we receive the registration response
        return 'pending'; // Indicate registration was sent
      }

      throw new Error('Failed to send registration message');
    } catch (error) {
      this.registrationService.handleRegistrationFailure(error, 'WebSocket');
      this.handleError(error, 'registerNode');
      return null;
    }
  }

  /**
   * Send heartbeat to hub
   */
  async sendHeartbeat(): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot send heartbeat: No node ID');
      return false;
    }

          try {
        // Get current test ID from NodeManager to include in heartbeat
        let currentTestId: string | null = null;
        let nodeStatus = 'available';
        
        try {
          // Import NodeManager to get current test status
          const nodeManagerModule = await import('../../node-manager/index.js');
          
          // Check if node is running a test
          const isRunning = nodeManagerModule.default.isRunningTest();
          if (isRunning) {
            nodeStatus = 'busy';
            // For now, we'll set currentTestId to a placeholder
            // This will be properly implemented when we add getCurrentTestId to the facade
            currentTestId = 'test-running'; // Temporary placeholder
          }
        } catch (error) {
          this.logger.warn(`WebSocket: Could not get current test ID for heartbeat: ${error}`);
        }

        const heartbeatData = this.heartbeatService.prepareHeartbeatData(
          String(this.nodeId),
          nodeStatus,
          { currentTestId } // Include currentTestId in additional data
        );

      const message = {
        type: 'heartbeat',
        data: heartbeatData
      };

      const success = await this.sendMessage(message);
      
      if (success) {
        this.logOperation('websocketHeartbeatSent', { nodeId: this.nodeId, currentTestId });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendHeartbeat');
      return false;
    }
  }

  /**
   * Start heartbeat interval
   */
  startHeartbeat(): void {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot start heartbeat: No node ID');
      return;
    }

    // Use the injected heartbeat service to manage the interval
    const heartbeatFunction = async () => {
      return await this.sendHeartbeat();
    };

    this.heartbeatService.startHeartbeat(
      `websocket-${this.nodeId}`,
      heartbeatFunction,
      this.defaultHeartbeatInterval
    );

    this.logOperation('websocketHeartbeatStarted', { nodeId: this.nodeId });
  }

  /**
   * Stop heartbeat interval
   */
  stopHeartbeat(): void {
    if (this.nodeId) {
      this.heartbeatService.stopHeartbeat(`websocket-${this.nodeId}`);
    }
    this.logOperation('websocketHeartbeatStopped');
  }

  /**
   * Get next test from hub
   */
  async getNextTest(): Promise<any> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot get next test: No node ID');
      return null;
    }

    try {
      const message = {
        type: 'get-next-test',
        data: { nodeId: String(this.nodeId) }
      };

      const success = await this.sendMessage(message);
      
      if (success) {
        this.logOperation('websocketGetNextTestRequested', { nodeId: this.nodeId });
        // Test will be received via message handler
        return 'requested';
      }

      return null;
    } catch (error) {
      this.handleError(error, 'getNextTest');
      return null;
    }
  }

  /**
   * Request next test from hub
   */
  async requestNextTest(): Promise<boolean> {
    const result = await this.getNextTest();
    return result !== null;
  }

  /**
   * Update test status
   * @param testId Test ID
   * @param status New status
   * @param result Test result (optional)
   * @param runReportId Run report ID (optional)
   */
  async updateTestStatus(testId: string, status: string, result?: any, runReportId?: string): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot update test status: No node ID');
      return false;
    }

    try {
      const message = {
        type: 'test-status-update',
        data: {
          testId,
          status,
          result,
          nodeId: String(this.nodeId),
          timestamp: new Date().toISOString(),
          ...(runReportId && { runReportId })
        }
      };

      const success = await this.sendMessage(message);

      if (success) {
        this.logOperation('websocketTestStatusUpdated', { testId, status, runReportId });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'updateTestStatus');
      return false;
    }
  }

  /**
   * Send test claimed notification
   * @param testId Test ID
   * @param nodeId Node ID
   */
  async sendTestClaimed(testId: string, nodeId?: string): Promise<boolean> {
    const actualNodeId = nodeId || this.nodeId;
    
    if (!actualNodeId) {
      this.logger.warn('WebSocket: Cannot send test claimed: No node ID');
      return false;
    }

    try {
      const message = {
        type: 'test-claimed',
        data: {
          testId,
          nodeId: String(actualNodeId),
          timestamp: new Date().toISOString()
        }
      };

      const success = await this.sendMessage(message);
      
      if (success) {
        this.logOperation('websocketTestClaimed', { testId, nodeId: actualNodeId });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendTestClaimed');
      return false;
    }
  }

  /**
   * Send test started notification
   * @param testId Test ID
   * @param testData Test data
   */
  async sendTestStarted(testId: string, testData?: any): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot send test started: No node ID');
      return false;
    }

    try {
      const message = {
        type: 'test-started',
        data: {
          testId,
          nodeId: String(this.nodeId),
          testData,
          timestamp: new Date().toISOString()
        }
      };

      const success = await this.sendMessage(message);
      
      if (success) {
        this.logOperation('websocketTestStarted', { testId });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendTestStarted');
      return false;
    }
  }

  /**
   * Send test completed notification
   * @param testId Test ID
   * @param result Test result
   */
  async sendTestCompleted(testId: string, result?: any): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot send test completed: No node ID');
      return false;
    }

    try {
      const message = {
        type: 'test-completed',
        data: {
          testId,
          nodeId: String(this.nodeId),
          result,
          timestamp: new Date().toISOString()
        }
      };

      const success = await this.sendMessage(message);

      if (success) {
        this.logOperation('websocketTestCompleted', { testId });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendTestCompleted');
      return false;
    }
  }

  /**
   * Send test failed notification
   * @param testId Test ID
   * @param error Error details
   */
  async sendTestFailed(testId: string, error?: any): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot send test failed: No node ID');
      return false;
    }

    try {
      const message = {
        type: 'test-failed',
        data: {
          testId,
          nodeId: String(this.nodeId),
          error: error?.message || error,
          timestamp: new Date().toISOString()
        }
      };

      const success = await this.sendMessage(message);

      if (success) {
        this.logOperation('websocketTestFailed', { testId });
      }

      return success;
    } catch (sendError) {
      this.handleError(sendError, 'sendTestFailed');
      return false;
    }
  }

  /**
   * Derive run report ID from test data
   * @param testData Test data containing runId and executionId
   * @returns Run report ID or undefined
   */
  private deriveRunReportId(testData: any): string | undefined {
    // Run report ID is typically derived from runId and executionId
    // For now, we'll use a simple format, but this could be enhanced
    // to actually query the hub for the correct run report ID
    if (testData?.runId && testData?.executionId) {
      return `${testData.runId}-${testData.executionId}`;
    }
    return undefined;
  }

  /**
   * Send test running notification
   * @param testId Test ID
   * @param runReportId Run report ID (optional)
   */
  async sendTestRunning(testId: string, runReportId?: string): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot send test running: No node ID');
      return false;
    }
    this.logOperation('sendTestRunning', { testId, runReportId });
    try {
      const message = {
        type: 'test-status-update',
        data: {
          testId: testId,
          status: 'running',
          nodeId: String(this.nodeId),
          timestamp: new Date().toISOString(),
          ...(runReportId && { runReportId })
        }
      };
      const success = await this.sendMessage(message);
      if (success) {
        this.logOperation('sendTestRunningSuccess', { testId, runReportId });
      } else {
        this.logOperation('sendTestRunningFailed', { testId, runReportId });
      }
      return success;
    } catch (error) {
      this.handleError(error, 'sendTestRunning');
      return false;
    }
  }

  /**
   * Send video upload completed notification
   * @param testId Test ID
   * @param videoUrl Video URL
   */
  async sendVideoUploadCompleted(testId: string, videoUrl: string): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('WebSocket: Cannot send video upload completed: No node ID');
      return false;
    }

    try {
      const message = {
        type: 'video-upload-completed',
        data: {
          testId,
          nodeId: String(this.nodeId),
          videoUrl,
          timestamp: new Date().toISOString()
        }
      };

      const success = await this.sendMessage(message);

      if (success) {
        this.logOperation('websocketVideoUploadCompleted', { testId, videoUrl });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendVideoUploadCompleted');
      return false;
    }
  }



  /**
   * Send registration message
   */
  async sendRegistration(): Promise<void> {
    // CRITICAL FIX: Actually send registration message on reconnection
    if (!this.isConnected()) {
      this.logger.warn('WebSocket: Cannot send registration, not connected');
      return;
    }

    try {
      // This will trigger the connection service to handle registration
      this.emit('registration-required');
      this.logOperation('websocketRegistrationReady');
    } catch (error) {
      this.logger.error(`WebSocket: Error triggering registration: ${error}`);
      throw error;
    }
  }

  /**
   * Reset reconnection state
   */
  resetReconnectionState(): void {
    this.reconnectAttempts = 0;
    this.connectionManager.resetReconnectionState();
    this.logOperation('websocketReconnectionStateReset');
  }

  /**
   * Send node status response to hub
   * @param isRunning Whether node is running a test
   * @param testId Current test ID (if any)
   */
  async sendNodeStatusResponse(isRunning: boolean, testId?: string): Promise<boolean> {
    const message = {
      type: 'node-status-response',
      data: {
        nodeId: this.nodeId,
        isRunning,
        testId: testId || null,
        timestamp: Date.now()
      }
    };

    const success = await this.sendMessage(message);
    if (success) {
      this.logOperation('websocketNodeStatusResponse', { isRunning, testId });
    }
    return success;
  }

  /**
   * Send current test ID response to hub
   * @param testId Current test ID or 'none'
   */
  async sendCurrentTestIdResponse(testId: string): Promise<boolean> {
    const message = {
      type: 'current-test-id-response',
      data: {
        nodeId: this.nodeId,
        testId,
        timestamp: Date.now()
      }
    };

    const success = await this.sendMessage(message);
    if (success) {
      this.logOperation('websocketCurrentTestIdResponse', { testId });
    }
    return success;
  }

  /**
   * Setup message handlers for WebSocket events
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift();
      this.logger.info('Sending queued message.');
      this.sendMessage(message);
    }
    
    // CRITICAL FIX: Clear timeout when queue is processed
    this.clearMessageQueueTimeout();
  }

  /**
   * Start timeout for message queue (CRITICAL FIX)
   */
  private startMessageQueueTimeout(): void {
    // Don't start multiple timeouts
    if (this.messageQueueTimeout) {
      return;
    }

    this.messageQueueTimeout = setTimeout(() => {
      if (this.messageQueue.length > 0) {
        this.logger.warn(`WebSocket: Message queue timeout reached (${this.MESSAGE_QUEUE_TIMEOUT}ms). Processing ${this.messageQueue.length} queued messages forcefully.`);
        
        // Try to process messages even without full registration
        this.processBasicMessageQueue();
        
        // If there are still messages after basic processing, log warning
        if (this.messageQueue.length > 0) {
          this.logger.error(`WebSocket: ${this.messageQueue.length} messages still stuck in queue after timeout. Possible connection or registration issues.`);
        }
      }
      
      this.messageQueueTimeout = null;
    }, this.MESSAGE_QUEUE_TIMEOUT);
  }

  /**
   * Clear message queue timeout (CRITICAL FIX)
   */
  private clearMessageQueueTimeout(): void {
    if (this.messageQueueTimeout) {
      clearTimeout(this.messageQueueTimeout);
      this.messageQueueTimeout = null;
    }
  }

  /**
   * Process basic messages that don't require registration (CRITICAL FIX)
   */
  private processBasicMessageQueue(): void {
    if (!this.isConnected()) {
      return;
    }

    const processableMessages: any[] = [];
    const remainingMessages: any[] = [];

    // Separate messages that can be sent without registration
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      
      // Messages that can be sent without registration
      const basicMessageTypes = ['register', 'heartbeat', 'ping', 'pong'];
      
      if (basicMessageTypes.includes(message?.type)) {
        processableMessages.push(message);
      } else {
        remainingMessages.push(message);
      }
    }

    // Put back messages that need registration
    this.messageQueue.push(...remainingMessages);

    // Send processable messages immediately
    processableMessages.forEach(message => {
      this.logger.info('Sending basic queued message without waiting for registration.');
      this.connectionManager.send(JSON.stringify(message)).catch(error => {
        this.logger.error(`Failed to send basic message: ${error}`);
        // Put failed message back to queue
        this.messageQueue.unshift(message);
      });
    });
  }

  private setupMessageHandlers(): void {
    // Handle registration response
    this.messageHandler.registerHandler('registered', (data: any) => {
      const nodeId = data.nodeId || data;
      if (nodeId) {
        this.registrationService.handleRegistrationSuccess(nodeId);
        this.updateNodeId(nodeId); // Set the nodeId in the connector

        // Emit BOTH events for compatibility
        this.emit('registration-success', { nodeId });
        this.emit('registered', nodeId); // This is what ConnectionService is listening for

        this.startHeartbeat();
        this.logOperation('websocketRegistrationSuccess', { nodeId });

        // Now that registration is successful, process any queued messages
        this.processMessageQueue();
        
        // CRITICAL FIX: Clear timeout since registration is successful
        this.clearMessageQueueTimeout();
      }
    });

    // Handle node ID correction
    this.messageHandler.registerHandler('nodeIdCorrected', (data: any) => {
      const nodeId = data.nodeId || data;
      if (nodeId) {
        // The registration service is the source of truth for the node ID
        this.registrationService.handleRegistrationSuccess(nodeId);
        this.updateNodeId(nodeId); // Update the nodeId in the connector
        this.logOperation('websocketNodeIdCorrected', { nodeId });
      }
    });

    // Handle registration required
    this.messageHandler.registerHandler('registrationRequired', (data: any) => {
      this.logOperation('websocketRegistrationRequired');
      this.emit('registrationRequired', data);
    });

    // Handle pong messages
    this.messageHandler.registerHandler('pong', (_data: any) => {
      this.connectionManager.updatePongTimestamp(Date.now());
      this.logOperation('websocketPongReceived');
    });

    // Handle test assignment
    this.messageHandler.registerHandler('test-assignment', (data: any) => {
      this.logOperation('websocketTestAssignment', { testId: data?.testId });
      this.emit('test-assignment', data);
    });

    // Handle next test response
    this.messageHandler.registerHandler('next-test', (data: any) => {
      this.logOperation('websocketNextTest', { testId: data?.testId });
      this.emit('next-test', data);
    });

    // Handle stop test
    this.messageHandler.registerHandler('stop-test', (data: any) => {
      this.logOperation('websocketStopTest', { testId: data?.testId });
      this.emit('stop-test', data);
    });

    // Handle test released (replaces Redis 'test:released')
    this.messageHandler.registerHandler('test-released', (data: any) => {
      this.logOperation('websocketTestReleased', { testId: data?.testId });
      this.emit('test-released', data);
    });

    // Handle node status query (replaces Redis 'check:running')
    this.messageHandler.registerHandler('node-status-query', (data: any) => {
      this.logOperation('websocketNodeStatusQuery');
      this.emit('node-status-query', data);
    });

    // Handle get current test ID query (replaces Redis 'get:current-test-id')
    this.messageHandler.registerHandler('get-current-test-id', (data: any) => {
      this.logOperation('websocketGetCurrentTestId');
      this.emit('get-current-test-id', data);
    });

    // Setup connection manager event handlers
    this.connectionManager.on('connected', () => {
      this.emit('connected');
      // CRITICAL FIX: Process basic messages immediately on connection
      // Only registration-dependent messages should wait for registration
      this.processBasicMessageQueue();
    });

    this.connectionManager.on('disconnected', (data: any) => {
      this.stopHeartbeat();
      this.emit('disconnected', data);
    });

    // CRITICAL FIX: Handle reconnection events
    this.connectionManager.on('reconnected', async () => {
      this.logger.info('WebSocket: Reconnection detected, initiating automatic re-registration');
      
      try {
        // Trigger re-registration after successful reconnection
        await this.sendRegistration();
        this.logger.info('WebSocket: Re-registration initiated after reconnection');
      } catch (error) {
        this.logger.error(`WebSocket: Failed to initiate re-registration after reconnection: ${error}`);
      }
    });

    this.connectionManager.on('error', (error: any) => {
      this.handleError(error, 'connection');
    });

    this.connectionManager.on('message', (data: any) => {
      try {
        const message = JSON.parse(data.toString());
        this.messageHandler.handleMessage(message);
      } catch (error) {
        this.handleError(error, 'message parsing');
      }
    });

    this.logOperation('websocketMessageHandlersSetup');
  }

  /**
   * Get connector statistics
   */
  getStats(): IConnectorStats {
    const baseStats = super.getStats();
    return {
      ...baseStats,
      // Additional WebSocket-specific stats can be added as needed
    };
  }

  /**
   * Get detailed statistics including service stats
   */
  getDetailedStats(): Record<string, any> {
    const baseStats = super.getStats();
    return {
      ...baseStats,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      isConnecting: this.isConnecting,
      services: {
        connectionManager: this.connectionManager.getStats(),
        messageHandler: this.messageHandler.getStats(),
        registration: this.registrationService.getStats(),
        heartbeat: this.heartbeatService.getStats()
      }
    };
  }
}
