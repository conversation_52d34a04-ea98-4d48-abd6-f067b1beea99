"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Plus, AlertTriangle, Trash2 } from 'lucide-react';
import { toast } from '@/lib/utils/toast-utils';
import { Sidebar } from '@/components/sidebar/sidebar';
import { ProtectedRoute } from '@/components/protected-route';
import { PermissionGuard } from '@/components/permission-guard';
import { useScenarioStoreContext } from '@/hooks/useStoreContext';
import { useRuns } from '@/hooks/useRuns';
import { useRunsFilter } from '@/hooks/useRunsFilter';
import { RunsToolbar } from './components/RunsToolbar';
import { RunsGrid } from './components/RunsGrid';
import { RunsList } from './components/RunsList';
import { RunsPagination } from './components/RunsPagination';

function RunsPageContent() {
  // Store context management for company changes
  useScenarioStoreContext();
  
  const router = useRouter();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [runToDelete, setRunToDelete] = useState<string | null>(null);

  // Get filter state
  const { displayView } = useRunsFilter();

  // Get runs data and actions from SWR hook
  // Backend pagination is handled through useRunsFilter hook via API parameters
  const {
    runs,
    totalCount,
    isLoading,
    error,
    executeRun,
    stopRun,
    deleteRun,
    refresh
  } = useRuns();

  // Action handlers
  const handleCreateRun = () => {
    router.push("/runs/create");
  };

  const handleStartRun = async (runId: string) => {
    const result = await executeRun(runId);
    if (result.success) {
      toast.success('Run started successfully');
    }
  };

  const handleStopRun = async (runId: string) => {
    const result = await stopRun(runId);
    if (result.success) {
      toast.success('Run stopped successfully');
    }
  };

  const handleDeleteRun = (runId: string) => {
    setRunToDelete(runId);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!runToDelete) return;

    try {
      const result = await deleteRun(runToDelete);
      if (result.success) {
        toast.success("Run deleted successfully");
      }
    } catch (error) {
      // Error handled by useRuns hook
    } finally {
      setDeleteConfirmOpen(false);
      setRunToDelete(null);
    }
  };

  const handleRetry = () => {
    refresh();
  };

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Confirm Deletion
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this run? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2 sm:justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setDeleteConfirmOpen(false);
                setRunToDelete(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Run
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Sidebar */}
      <Sidebar 
        collapsed={sidebarCollapsed} 
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} 
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Test Runs</h1>
              <p className="text-gray-500 dark:text-gray-400">
                View, create, and manage your automated test runs
              </p>
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={handleCreateRun}
                className="bg-indigo-600 hover:bg-indigo-700 text-white transition-all duration-200 transform hover:scale-105"
              >
                <Plus className="mr-2 h-4 w-4" />
                New Run
              </Button>
            </div>
          </div>

          {/* Filter Toolbar */}
          <div className="mt-4">
            <RunsToolbar />
          </div>
        </header>

        <main className="flex-1 p-4 overflow-auto bg-gray-50 dark:bg-gray-950">
          {/* Conditional rendering based on displayView */}
          {displayView === "grid" ? (
            <RunsGrid
              runs={runs}
              loading={isLoading}
              error={error}
              onStart={handleStartRun}
              onStop={handleStopRun}
              onDelete={handleDeleteRun}
              onCreateRun={handleCreateRun}
              onRetry={handleRetry}
            />
          ) : (
            <RunsList
              runs={runs}
              onStart={handleStartRun}
              onStop={handleStopRun}
              onDelete={handleDeleteRun}
            />
          )}

          {/* Pagination - Now using backend pagination */}
          <RunsPagination 
            totalCount={totalCount} 
            currentRuns={runs}
          />
        </main>
      </div>
    </div>
  );
}

export default function RunsPage() {
  return (
    <ProtectedRoute>
      <PermissionGuard resource="Run" action="view">
        <RunsPageContent />
      </PermissionGuard>
    </ProtectedRoute>
  );
}
