/**
 * Test Types for Test Hub
 * Consolidated type definitions for the Test Hub system
 */

// Test Status Enumeration
export enum TestStatus {
  QUEUED = 'queued',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  STOPPED = 'stopped',
  STOPPING = 'stopping', // Added intermediate state for tests in the process of being stopped
  TIMEOUT = 'timeout',
  ERROR = 'error',
  ASSIGNING = 'assigning',
  ASSIGNED = 'assigned', // Added for tests that have been assigned to a node
  UNKNOWN = 'unknown'
}

// Node Status Types
export type NodeStatus = 'available' | 'busy' | 'stopping' | 'inactive' | 'offline' | 'error';
export type ActiveNodeStatus = 'IDLE' | 'BUSY';

// Test Process Interface
export interface TestProcess {
  id: string;
  scenarioId: string;
  scenarioName?: string;
  scenarioData?: any;
  status: TestStatus;
  queuedAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  nodeId?: string;
  logs: string[];
  result?: any;
  reportId?: string;
  userId?: string;
  executedUser?: string;
  runId?: string;
  executionId?: string;
  stepProgress?: StepProgress;
  process?: any; // Internal process object, not exposed in API
  pid?: number | null;
  startTime?: number;
  endTime?: number | null;
  error?: string | null;
  platform?: 'web' | 'android'; // Platform to run the test on (web or android)
  priority?: number; // Priority of the test (higher number = higher priority)
  progress?: {
    currentStep?: number;
    totalSteps?: number;
    percentage?: number;
    stepData?: any[];
  };
  // BullMQ retry tracking
  retryCount?: number; // Number of retry attempts by BullMQ
}

// Test Request Interface
export interface TestRequest {
  id: string;
  scenarioId: string;
  scenarioName?: string;
  scenarioData?: any;
  stuckTimeSeconds?: number;
  // Add scenario field for direct use by test_node
  scenario?: {
    id: string;
    name: string;
    title?: string;
    steps: any[];
    capabilities?: string[];
    totalSteps?: number;
    url?: string;
    testDataSetId?: string;    // Dataset ID for variable resolution
    metadata?: {               // Dataset variables metadata
      variables?: any[];
    };
  };
  priority: number;
  runId?: string;
  executionId?: string;
  userId?: string;
  executedUser?: string;
  executedUserName?: string;
  teamId?: string | null;    // Team ID for report attribution
  companyId?: string | null; // Company ID for report attribution
  status?: TestStatus;
  queuedAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  nodeId?: string;
  result?: any;
  platform?: 'web' | 'android'; // Platform to run the test on (web or android)
  environmentSettings?: EnvironmentSettings;
  reportSettings?: ReportSettings;
  options?: Record<string, any>;
}

// Test Report Interface
export interface TestReport {
  id: string;
  scenarioId: string;
  status: TestStatus;
  startTime: number;
  endTime: number | null;
  logs?: string[];
  error: string | null;
  result?: any;
  scenarioTitle?: string;
  assignedNode?: string;
  userId?: string;
  executedUser?: string;
  teamId?: string | null;    // Team ID for report attribution
  companyId?: string | null; // Company ID for report attribution
  platform?: 'web' | 'android'; // Platform the test was run on (web or android)
  name?: string;
  date?: Date;
  url?: string;
  duration?: number;
  steps?: any[];
  summary?: any;
  // Legacy fields removed - use enhancedMetrics from ProcessedMetrics interface instead
  lastUpdated?: number;
  options?: Record<string, any>;
  processInfo?: {
    pid: number;
    childProcess: any;
  };
  pageMetrics?: any;
}

// Node Interfaces
export interface TestNode {
  id: string;
  name: string;
  capabilities: string[];
  status: NodeStatus;
  lastHeartbeat: Date;
  lastSeen?: Date;
  currentTestId?: string | null;
  vncUrl?: string;
  vncPort?: string | number;
  version?: string;
  clientId?: string;
  registrationTime?: Date;
  metrics?: NodeMetrics;
}

export interface NodeMetrics {
  totalTestsProcessed?: number;
  totalFailures?: number;
  totalSuccesses?: number;
  averageProcessingTime?: number; // in milliseconds
  uptime?: number; // in seconds or as a timestamp
  lastTestStartedAt?: Date;
  lastTestEndedAt?: Date;
}

export interface ActiveNode {
  id: string;
  name: string;
  capabilities: string[];
  status: ActiveNodeStatus;
  lastHeartbeat: number;
}

// Queue Status Interfaces
export interface QueueStatus {
  queued: number;
  running: number;
  completed: number;
  failed: number;
  total: number;
  maxConcurrent: number;
}

export interface QueueStatusDetailed {
  queuedTests: number;
  runningTests: number;
  tests: {
    queued: TestRequest[];
    running: TestRequest[];
  };
}

// Step Progress Interface for tracking test execution
export interface StepProgress {
  stepName: string;
  stepIndex: number;
  totalSteps: number;
  additionalData?: any;
  timestamp: Date;
}

// Step Result Interface
export interface StepResult {
  id: string;
  name: string;
  type: string;
  success: boolean;
  error: string | null;
  duration: number;
  timestamp: string;
}

// Scenario Last Run Status
export interface ScenarioLastRunStatus {
  status: ReportStatus;
  date: string;
  duration: number;
  reportId: string;
}

// Report Status Type
export type ReportStatus = 'success' | 'failure' | 'error' | 'stopped';

// WebSocket Client Types
export interface WebSocketClient {
  isAlive: boolean;
  id?: string;
  type?: string;
}

export interface NodeWebSocketClient extends WebSocketClient {
  nodeId?: string;
  nodeInfo?: {
    name: string;
    capabilities: string[];
    lastHeartbeat: number;
    status: 'available' | 'busy' | 'stopping';
  };
}

// Config Types
export interface TestHubConfig {
  testTimeout: number;
  port: number;
  websocketEnabled: boolean;
  redisEnabled: boolean;
  redisUrl: string;
}

// MongoDB-specific types for test reports
export interface MongoDBReport {
  id: string;
  name: string;
  date: Date;
  scenarioId: string;
  scenarioTitle?: string;
  status: string;
  startTime: number;
  endTime: number;
  duration: number;
  error?: string | null;
  result: any;
  logs?: string[];
  steps: any[];
  url: string;
  userId?: string;
  runId?: string;
  executionId?: string;
  teamId?: string | null;    // Team ID for report attribution
  companyId?: string | null; // Company ID for report attribution
  platform?: 'web' | 'android'; // Platform the test was run on (web or android)
  // Legacy fields removed - use enhancedMetrics from ProcessedMetrics interface instead
  enhancedMetrics?: any;
  sharing?: {
    enabled: boolean;
    token: string;
    expiresAt?: Date;
    password?: string;
    allowComments?: boolean;
    createdAt: Date;
    lastAccessedAt?: Date;
    accessCount?: number;
  };
}

import type { AndroidEnvironmentSettings } from './android-environment.js';
// Re-export AndroidEnvironmentSettings
export type { AndroidEnvironmentSettings };

// Web-specific environment settings
export interface WebEnvironmentSettings {
  // Platform identifier
  platform: 'web';

  // Web browser settings
  browserName: string;  // e.g., "Chrome" | "Firefox" | "Safari" | "Edge"
  browserVersion?: string;
  browser: string;      // e.g., "chrome" | "firefox"
  browserMode?: 'headless' | 'headed'; // Browser display mode
  viewportSize: string; // e.g., "Mobile" | "Tablet" | "Desktop" | "Custom"
  viewport?: {
    width: number;
    height: number;
  };
  customViewport?: {
    width: number;
    height: number;
  };
  networkSpeed: string; // e.g., "Slow 3G" | "Fast 3G" | "Slow 4G" | "Fast 4G" | "Normal"

  // AI model settings
  aiModel: string;      // e.g., "GPT-3.5" | "GPT-4" | "Claude" | "Gemini" | "Auto" (ID of the model)
  aiModelName?: string; // Display name of the AI model

  // Proxy settings
  proxy?: {
    enabled: boolean;
    type: string;       // e.g., "SOCKS5" | "HTTP" | "HTTPS"
    host: string;
    port: number;
    username: string;
    password: string;
  };

  // AI model configuration (for node execution)
  aiModelConfig?: {
    OPENAI_BASE_URL: string;
    OPENAI_API_KEY: string;
    MIDSCENE_MODEL_NAME: string;
    [key: string]: string;
  };
}

// Combined environment settings type - use discriminated union
export type EnvironmentSettings = WebEnvironmentSettings | AndroidEnvironmentSettings;

// Base Report Settings Interface
export interface BaseReportSettings {
  format?: string;      // e.g., "HTML" | "PDF" | "JSON"
  takeScreenshots: boolean;
  takeVideos: boolean;
}

// Web-specific Report Settings Interface
export interface WebReportSettings extends BaseReportSettings {
  pageMetrics: boolean;
  networkData: boolean;
  tracingData: boolean;
  accessibilityData: boolean;
}

// Android-specific Report Settings Interface
export interface AndroidReportSettings extends BaseReportSettings {
  collectMetrics?: boolean; // Whether to collect performance metrics
  videoQuality?: 'low' | 'medium' | 'high'; // Video recording quality
  metricsInterval?: number; // Interval for collecting metrics in milliseconds
}

// Combined Report Settings type - use union to prevent field contamination
export type ReportSettings = WebReportSettings | AndroidReportSettings;

// Default settings
export const defaultEnvironmentSettings: WebEnvironmentSettings = {
  platform: "web", // Default platform is web
  browserName: "Chrome",
  browserVersion: "Latest",
  browser: "chrome",
  browserMode: "headless", // Default to headless for better performance
  viewportSize: "Desktop",
  viewport: {
    width: 1280,
    height: 720
  },
  networkSpeed: "Normal",
  aiModel: "Gemini",
  aiModelName: "Gemini Pro", // Default AI model name
  proxy: {
    enabled: false,
    type: "HTTP",
    host: "",
    port: 0,
    username: "",
    password: ""
  }
};

// Default Web Report Settings
export const defaultWebReportSettings: WebReportSettings = {
  format: "HTML",
  pageMetrics: true,
  networkData: true,
  tracingData: false,
  accessibilityData: false,
  takeScreenshots: true,
  takeVideos: false
};

// Default Android Report Settings
export const defaultAndroidReportSettings: AndroidReportSettings = {
  format: "HTML",
  takeScreenshots: true,
  takeVideos: false,
  collectMetrics: false,
  videoQuality: 'medium',
  metricsInterval: 10000 // 10 seconds
};

// Default Report Settings (defaults to web)
export const defaultReportSettings: WebReportSettings = defaultWebReportSettings;

// Run Status Enum - Run durumlarını tanımlar
export enum RunStatus {
  CREATED = 'created',   // Run oluşturuldu ama henüz başlatılmadı
  QUEUED = 'queued',     // Run kuyruğa alındı
  RUNNING = 'running',   // Run çalışıyor
  COMPLETED = 'completed', // Run tamamlandı
  FAILED = 'failed',     // Run başarısız oldu
  STOPPED = 'stopped',   // Run durduruldu
  PARTIAL = 'partial'    // Run kısmen tamamlandı (bazı testler başarılı, bazıları başarısız)
}

// Test Run Interface - Çoklu test çalıştırma işlemi
export interface TestRun {
  id: string;             // Run ID
  name: string;           // Run ismi (kullanıcı tarafından belirlenebilir)
  description?: string;   // Run açıklaması
  status: RunStatus;      // Run durumu
  createdAt: Date;        // Oluşturulma zamanı
  startedAt?: Date;       // Başlama zamanı
  completedAt?: Date;     // Tamamlanma zamanı
  userId: string;         // Oluşturan kullanıcı ID'si
  companyId?: string | null; // Şirket ID'si
  teamId?: string | null;    // Takım ID'si
  platform?: 'web' | 'android'; // Platform to run the test on (web or android)
  deviceProvider?: 'sauceLabs' | 'testinium'; // Mutually exclusive device provider selection
  scenarioIds: string[];  // Run içindeki senaryo ID'leri listesi
  environment?: EnvironmentSettings; // Çalışma ortamı ayarları
  testDataEnvironmentId?: string; // Test data environment ID
  testDataEnvironmentName?: string; // Test data environment name
  reportSettings?: ReportSettings;   // Rapor ayarları
  lastExecutionId?: string;  // Son çalıştırma execution ID'si
  reports?: TestReport[]; // Run içindeki testlerin raporları
  tags?: string[];        // Etiketler
  options?: Record<string, any>; // Ek seçenekler
  scenarioStatuses?: ScenarioStatus[]; // Enhanced with device info support
}

export interface Run {
  id: string;
  name: string;
  description?: string;
  status: RunStatus;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  userId: string;
  companyId?: string | null;
  teamId?: string | null;
  platform?: 'web' | 'android'; // Platform to run the test on (web or android)
  deviceProvider?: 'sauceLabs' | 'testinium'; // Mutually exclusive device provider selection
  scenarioIds: string[];
  environment?: EnvironmentSettings;
  reportSettings?: ReportSettings;
  lastExecutionId?: string;  // Son çalıştırma execution ID'si
  reports?: string[];
  tags?: string[];
  options?: Record<string, any>;
  testDataEnvironmentId?: string; // Test data environment ID
  testDataEnvironmentName?: string; // Test data environment name
  scenarioStatuses?: ScenarioStatus[]; // Enhanced with device info support
}

// Helper type for scenario status counts
export interface ScenarioStatusCounts {
  total: number;
  queued: number;
  running: number;
  completed: number;
  failed: number;
  stopped: number;
}

// Enhanced scenario status interface for multiple device support
export interface ScenarioStatus {
  scenarioId: string;
  testId: string;        // Unique test identifier
  deviceInfo?: {         // Device information for Android tests
    id: string;
    name: string;
    osVersion?: string;
    provider?: 'sauceLabs' | 'testinium';
  };
  status: 'queued' | 'running' | 'passed' | 'failed' | 'stopped';
  startedAt?: Date;
  completedAt?: Date;
  duration?: number;     // Test duration in milliseconds
}

/**
 * Calculate test result statistics from scenarioStatuses array
 * Multiple device aware: counts actual tests, not just scenarios
 * @param run Run object containing scenarioStatuses and environment settings
 * @returns Object with counts for different statuses
 */
export function calculateTestResults(run: Run): ScenarioStatusCounts {
  const scenarioStatuses = run.scenarioStatuses || [];
  
  // For multiple device runs, total should be the actual number of tests (scenarioStatuses.length)
  // For single device/scenario runs, use the scenario count as fallback
  let total = scenarioStatuses.length;
  
  // If no scenarioStatuses yet, estimate total based on run configuration
  if (total === 0 && run.scenarioIds?.length) {
    // Check if this is a multiple device Android run
    const isAndroid = run.platform === 'android';
    if (isAndroid && run.environment && 'sauceLabs' in run.environment) {
      const androidEnv = run.environment as any; // AndroidEnvironmentSettings
      const selectedDevices = androidEnv.sauceLabs?.selectedDevices || [];
      const testDistributionStrategy = androidEnv.testDistribution?.strategy || 'all-on-all';
      
      if (selectedDevices.length > 1) {
        if (testDistributionStrategy === 'all-on-all') {
          // Each scenario runs on each device
          total = run.scenarioIds.length * selectedDevices.length;
        } else if (testDistributionStrategy === 'distribute') {
          // Scenarios are distributed across devices (each scenario runs on one device)
          total = run.scenarioIds.length;
        }
      } else {
        // Single device or no devices selected
        total = run.scenarioIds.length;
      }
    } else {
      // Web platform or single device
      total = run.scenarioIds.length;
    }
  }

  // Initialize counts
  const counts: ScenarioStatusCounts = {
    total,
    queued: 0,
    running: 0,
    completed: 0,
    failed: 0,
    stopped: 0
  };

  // Count individual tests by status (each scenarioStatus represents one test)
  scenarioStatuses.forEach(scenario => {
    switch (scenario.status) {
      case 'queued':
        counts.queued++;
        break;
      case 'running':
        counts.running++;
        break;
      case 'passed':
        counts.completed++;
        break;
      case 'failed':
        counts.failed++;
        break;
      case 'stopped':
        counts.stopped++;
        break;
    }
  });

  return counts;
}

// Helper function to group scenario results by scenarioId for display
export function groupScenarioResultsByScenario(scenarioStatuses: ScenarioStatus[]) {
  const grouped = scenarioStatuses.reduce((acc, status) => {
    const scenarioId = status.scenarioId;
    if (!acc[scenarioId]) {
      acc[scenarioId] = [];
    }
    acc[scenarioId].push(status);
    return acc;
  }, {} as Record<string, ScenarioStatus[]>);

  return Object.entries(grouped).map(([scenarioId, tests]) => ({
    scenarioId,
    totalTests: tests.length,
    tests,
    // Calculate overall status for the scenario
    overallStatus: calculateOverallScenarioStatus(tests),
    devices: tests.map(t => t.deviceInfo).filter(Boolean)
  }));
}

// Helper function to calculate overall status for a scenario with multiple device tests
function calculateOverallScenarioStatus(tests: ScenarioStatus[]): 'queued' | 'running' | 'passed' | 'failed' | 'stopped' {
  if (tests.some(t => t.status === 'running')) return 'running';
  if (tests.some(t => t.status === 'failed')) return 'failed';
  if (tests.some(t => t.status === 'stopped')) return 'stopped';
  if (tests.every(t => t.status === 'passed')) return 'passed';
  if (tests.every(t => t.status === 'queued')) return 'queued';
  return 'failed'; // Mixed state defaults to failed
}