/**
 * Test Progress Service Implementation
 *
 * Provides comprehensive test progress reporting with proper error handling
 * and integration with the test execution architecture.
 */

import { EventEmitter } from 'events';
import type {
  ITestProgressReporter,
  StepProgressData
} from '../../interfaces/test-progress-reporter.interface.js';
import { TestResult } from '../../../../models/types.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';
import { IConnectionService } from '../../../node-manager/interfaces/connection-service.interface.js';
import { nodeResultQueueService } from '../result/result-queue-service.js';
import { NodeStepProgressQueueService } from './step-progress-queue-service.js';

/**
 * Test Progress Service Configuration
 */
export interface TestProgressServiceConfig {
  enableLogging?: boolean;
  batchReporting?: boolean;
  reportingInterval?: number;
  enableResultQueue?: boolean;
  enableStepProgressQueue?: boolean;
}

/**
 * Test Progress Service Implementation
 *
 * Provides comprehensive test progress reporting with proper error handling
 * and integration with the test execution architecture.
 */
export class TestProgressService extends EventEmitter implements ITestProgressReporter {
  private config: TestProgressServiceConfig;
  private initialized = false;
  private logger: ILoggerService;
  private connectionService: IConnectionService;
  private stepProgressQueueService: NodeStepProgressQueueService | null = null;
  private activeTests: Map<string, { executionId: string, runId: string }> = new Map();

  // Batching support
  private pendingReports: Map<string, StepProgressData[]> = new Map();
  private reportingTimer: NodeJS.Timeout | null = null;

  constructor(
    connectionService: IConnectionService,
    stepProgressQueueService?: NodeStepProgressQueueService
  ) {
    super();
    this.logger = LoggerServiceFactory.createServiceLogger('TestProgressService');
    this.connectionService = connectionService;
    this.stepProgressQueueService = stepProgressQueueService || null;
    this.config = {
      enableLogging: false,
      batchReporting: false,
      reportingInterval: 1000,
      enableResultQueue: true,
      enableStepProgressQueue: true
    };
  }

  /**
   * Initialize the progress reporter with configuration
   */
  async initialize(config: TestProgressServiceConfig): Promise<void> {
    this.config = { ...this.config, ...config };
    
    // ENHANCED DEBUG: Log ConnectionService status during initialization
    this.logger.info(`TestProgressService: Initializing with ConnectionService: ${this.connectionService ? 'AVAILABLE' : 'NULL'}`);
    
    if (this.connectionService) {
      const nodeId = this.connectionService.getNodeId();
      const wsConnector = this.connectionService.getWebSocketConnector();
      const wsConnected = this.connectionService.isWebSocketConnected();
      
      this.logger.info(`TestProgressService: ConnectionService status - NodeID: ${nodeId || 'NULL'}, WebSocket Connector: ${wsConnector ? 'AVAILABLE' : 'NULL'}, WebSocket Connected: ${wsConnected}`);
    }
    
    // Initialize result queue service if enabled
    if (this.config.enableResultQueue) {
      try {
        const queueInitialized = await nodeResultQueueService.initialize();
        if (!queueInitialized) {
          this.logger.warn('TestProgressService: Failed to initialize result queue service, will use WebSocket only for results');
          this.config.enableResultQueue = false;
        } else {
          this.logger.info('TestProgressService: Result queue service initialized successfully');
        }
      } catch (error: any) {
        this.logger.error(`TestProgressService: Error initializing result queue service: ${error.message}`);
        this.config.enableResultQueue = false;
      }
    }

    // Initialize step progress queue service if enabled
    if (this.config.enableStepProgressQueue && this.stepProgressQueueService) {
      try {
        const stepProgressQueueInitialized = await this.stepProgressQueueService.initialize();
        if (!stepProgressQueueInitialized) {
          this.logger.warn('TestProgressService: Failed to initialize step progress queue service, will use WebSocket for step progress');
          this.config.enableStepProgressQueue = false;
        } else {
          this.logger.info('TestProgressService: Step progress queue service initialized successfully');
        }
      } catch (error: any) {
        this.logger.error(`TestProgressService: Error initializing step progress queue service: ${error.message}`);
        this.config.enableStepProgressQueue = false;
      }
    } else if (this.config.enableStepProgressQueue && !this.stepProgressQueueService) {
      this.logger.warn('TestProgressService: Step progress queue service not provided, disabling step progress queue');
      this.config.enableStepProgressQueue = false;
    }

    this.initialized = true;

    if (this.config.batchReporting) {
      this.startBatchReporting();
    }

    if (this.config.enableLogging) {
      this.logger.info('TestProgressService: Initialized with config', {
        batchReporting: this.config.batchReporting,
        reportingInterval: this.config.reportingInterval,
        enableResultQueue: this.config.enableResultQueue,
        enableStepProgressQueue: this.config.enableStepProgressQueue
      });
    }
  }

  /**
   * Caches the execution and run IDs for a given test
   */
  public startTest(testId: string, executionId: string, runId: string): void {
    this.activeTests.set(testId, { executionId, runId });
    this.logger.info(`TestProgressService: Started tracking test ${testId} with executionId ${executionId} and runId ${runId}`);
  }

  /**
   * Report step progress
   */
  async reportStepProgress(testId: string, data: StepProgressData): Promise<void> {
    if (!this.initialized) {
      throw new Error('TestProgressService not initialized');
    }

    try {
      if (this.config.batchReporting) {
        this.addToBatch(testId, data);
      } else {
        await this.sendStepProgress(testId, data);
      }

      if (this.config.enableLogging) {
        this.logger.info(`TestProgressService: Reported step progress for test ${testId}`, {
          stepType: data.stepType,
          status: data.status,
          stepIndex: data.stepIndex
        });
      }
    } catch (error: any) {
      this.logger.error(`TestProgressService: Error reporting step progress for test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Report test completion
   */
  async reportTestCompletion(testId: string, data: TestResult): Promise<void> {
    if (!this.initialized) {
      throw new Error('TestProgressService not initialized');
    }

    try {
      // Flush any pending batch reports for this test first
      if (this.config.batchReporting) {
        await this.flushBatchForTest(testId);
      }

      // Send test completion to Redis result queue
      // Hub will automatically update run status when processing results
      await this.sendTestCompletion(testId, data);

      // Clear cached IDs on test completion
      this.activeTests.delete(testId);

      if (this.config.enableLogging) {
        this.logger.info(`TestProgressService: Reported test completion for test ${testId}`, {
          success: data.success,
          status: data.status,
          duration: data.duration
        });
      }
    } catch (error: any) {
      this.logger.error(`TestProgressService: Error reporting test completion for test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Report a log message
   */
  async reportLog(testId: string, message: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('TestProgressService not initialized');
    }

    try {
      await this.sendLogMessage(testId, message);

      if (this.config.enableLogging) {
        this.logger.debug(`TestProgressService: Reported log message for test ${testId}`);
      }
    } catch (error: any) {
      this.logger.error(`TestProgressService: Error reporting log for test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Report test running.
   * Re-enabled with proper race condition protection. Individual test status updates are now
   * handled separately from run-level status updates to prevent database conflicts.
   * The hub will use locking mechanisms to ensure atomic test status updates.
   */
  async reportTestRunning(testId: string): Promise<void> {
    if (!this.initialized) {
      this.logger.error('TestProgressService.reportTestRunning called before initialization.');
      return;
    }

    if (this.config.enableLogging) {
      this.logger.info(`TestProgressService: Reporting test running for test ${testId}.`);
    }

    try {
      // Send the test running status update to the hub with proper error handling
      await this.sendTestRunning(testId);

      if (this.config.enableLogging) {
        this.logger.debug(`TestProgressService: Successfully reported test running for test ${testId}`);
      }
    } catch (error: any) {
      this.logger.error(`TestProgressService: Failed to report test running for test ${testId}: ${error.message}`);
      // Don't throw - this is a status update, test execution should continue
    }
  }

  /**
   * Report that a test has been claimed by a node.
   * @param testId The ID of the test that has been claimed.
   * @param nodeId The ID of the node that claimed the test.
   */
  async reportTestClaimed(testId: string, nodeId: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('TestProgressService not initialized');
    }

    try {
      await this.sendTestClaimed(testId, nodeId);
      this.logger.info(`TestProgressService: Reported test claimed for test ${testId} by node ${nodeId}`);
    } catch (error: any) {
      this.logger.error(`TestProgressService: Error reporting test claimed for test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Report video upload completion to hub
   * @param testId The ID of the test
   * @param videoUrl The URL of the uploaded video
   */
  async reportVideoUploadCompleted(testId: string, videoUrl: string): Promise<void> {
    if (!this.initialized) {
      this.logger.error('TestProgressService.reportVideoUploadCompleted called before initialization.');
      return;
    }

    if (this.config.enableLogging) {
      this.logger.info(`TestProgressService: Reporting video upload completed for test ${testId}: ${videoUrl}`);
    }

    try {
      await this.sendVideoUploadCompleted(testId, videoUrl);
      this.logger.info(`TestProgressService: Successfully reported video upload completed for test ${testId}`);
    } catch (error: any) {
      this.logger.error(`TestProgressService: Failed to report video upload completed for test ${testId}: ${error.message}`);
      // Don't throw - this is a notification, test execution should continue
    }
  }

  /**
   * Send test claimed notification via WebSocket.
   * @param testId The ID of the test.
   * @param nodeId The ID of the node.
   */
  private async sendTestClaimed(testId: string, nodeId: string): Promise<void> {
    this.logger.info(`TestProgressService: Attempting to send test claimed for test ${testId} by node ${nodeId}`);
    
    const websocketConnector = this.connectionService.getWebSocketConnector();
    this.logger.info(`TestProgressService: WebSocket connector availability: ${websocketConnector ? 'AVAILABLE' : 'NULL'}`);
    
    if (websocketConnector) {
      this.logger.info(`TestProgressService: WebSocket connector connected: ${websocketConnector.isConnected ? websocketConnector.isConnected() : 'UNKNOWN'}`);
      
      const success = await websocketConnector.sendTestClaimed(testId, nodeId);
      if (!success) {
        this.logger.warn(`TestProgressService: Failed to send test claimed status for test ${testId} via WebSocket - sendTestClaimed returned false`);
      } else {
        this.logger.info(`TestProgressService: Successfully sent test claimed for test ${testId} via WebSocket`);
      }
    } else {
      this.logger.error('TestProgressService: WebSocket connector is NULL - cannot send test claimed status. This indicates a connection service initialization issue.');
    }
  }

  /**
   * Send video upload completed notification via WebSocket.
   * @param testId The ID of the test.
   * @param videoUrl The URL of the uploaded video.
   */
  private async sendVideoUploadCompleted(testId: string, videoUrl: string): Promise<void> {
    this.logger.debug(`TestProgressService: Attempting to send video upload completed for test ${testId}: ${videoUrl}`);
    
    const websocketConnector = this.connectionService.getWebSocketConnector();
    if (websocketConnector && typeof websocketConnector.sendVideoUploadCompleted === 'function') {
      try {
        const success = await websocketConnector.sendVideoUploadCompleted(testId, videoUrl);
        if (success) {
          this.logger.debug(`TestProgressService: Successfully sent video upload completed for test ${testId} via WebSocket`);
        } else {
          this.logger.warn(`TestProgressService: Failed to send video upload completed for test ${testId} via WebSocket - sendVideoUploadCompleted returned false`);
        }
      } catch (error: any) {
        this.logger.error(`TestProgressService: Failed to send video upload completed for test ${testId}: ${error.message}`);
        throw error;
      }
    } else {
      this.logger.warn(`TestProgressService: WebSocket connector not available or sendVideoUploadCompleted method missing for test ${testId}`);
    }
  }

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Dispose of the progress service
   */
  async dispose(): Promise<void> {
    // Note: Event Bus unsubscription should be handled by the creator of this service

    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
      this.reportingTimer = null;
    }

    // Flush any remaining batch reports
    if (this.config.batchReporting) {
      await this.flushAllBatches();
    }

    // Close result queue service if it was initialized
    if (this.config.enableResultQueue && nodeResultQueueService.isReady()) {
      try {
        await nodeResultQueueService.close();
        this.logger.debug('TestProgressService: Result queue service closed');
      } catch (error: any) {
        this.logger.error(`TestProgressService: Error closing result queue service: ${error.message}`);
      }
    }

    // Close step progress queue service if it was initialized
    if (this.config.enableStepProgressQueue && this.stepProgressQueueService?.isServiceInitialized()) {
      try {
        await this.stepProgressQueueService.close();
        this.logger.debug('TestProgressService: Step progress queue service closed');
      } catch (error: any) {
        this.logger.error(`TestProgressService: Error closing step progress queue service: ${error.message}`);
      }
    }

    this.pendingReports.clear();
    this.activeTests.clear();
    this.initialized = false;

    this.logger.debug('TestProgressService: Disposed');
  }

  private async sendTestRunning(testId: string): Promise<void> {
    const websocketConnector = this.connectionService.getWebSocketConnector();
    if (websocketConnector && typeof websocketConnector.sendTestRunning === 'function') {
      try {
        // Get the run report ID from active test data
        const testData = this.activeTests.get(testId);
        let runReportId: string | undefined;

        if (testData?.runId && testData?.executionId) {
          // Derive run report ID from runId and executionId
          runReportId = `${testData.runId}-${testData.executionId}`;
          this.logger.debug(`TestProgressService: Sending test running for test ${testId} with runReportId ${runReportId}`);
          await websocketConnector.sendTestRunning(testId, runReportId);
          this.logger.debug(`TestProgressService: Successfully sent test running for test ${testId} with runReportId ${runReportId}`);
        } else {
          this.logger.warn(`TestProgressService: Cannot send test running - missing runId or executionId for test ${testId}`);
        }
      } catch (error: any) {
        this.logger.error(`TestProgressService: Failed to send test running for test ${testId}: ${error.message}`);
      }
    } else {
      this.logger.warn(`TestProgressService: Cannot send test running - WebSocket connector or sendTestRunning function is not available for test ${testId}`);
    }
  }

  /**
   * Send step progress via Redis queue for reliable delivery
   * Step progress uses Redis queue for consistent architecture with test results
   */
  private async sendStepProgress(testId: string, data: StepProgressData): Promise<void> {
    const nodeId = this.connectionService.getNodeId();
    if (!nodeId) {
      this.logger.warn('TestProgressService: Node ID not available for step progress transmission');
      return;
    }

    // Send via Redis queue (primary method)
    if (this.config.enableStepProgressQueue && this.stepProgressQueueService?.isServiceInitialized()) {
      try {
        const jobId = await this.stepProgressQueueService.addStepProgress(testId, data, nodeId);
        if (jobId) {
          if (this.config.enableLogging) {
            const stepInfo = data.stepIndex !== undefined ? data.stepIndex : 'nested';
        this.logger.info(`TestProgressService: Successfully sent step progress via Redis queue for test ${testId}, step ${stepInfo}, job ID: ${jobId}`);
          }
          return; // Successfully sent via Redis queue
        } else {
          this.logger.error(`TestProgressService: Failed to add step progress to Redis queue for test ${testId} - no job ID returned`);
        }
      } catch (error: any) {
        this.logger.error(`TestProgressService: Redis queue error sending step progress for test ${testId}: ${error.message}`);
      }
    } else {
      this.logger.warn('TestProgressService: Step progress queue service not available or not enabled');
    }

    // Step progress transmission failed - log but don't fail the test
              const stepInfo = data.stepIndex !== undefined ? data.stepIndex : 'nested';
          this.logger.warn(`TestProgressService: Step progress transmission failed for test ${testId}, step ${stepInfo}. Test will continue.`);
  }

  /**
   * Send test completion directly to Redis result queue
   */
  private async sendTestCompletion(testId: string, data: TestResult): Promise<void> {
    const nodeId = this.connectionService.getNodeId();
    if (!nodeId) {
      throw new Error('Node ID not available for test result transmission');
    }

    if (!this.config.enableResultQueue || !nodeResultQueueService.isReady()) {
      this.logger.error(`TestProgressService: Result queue not available for test ${testId}. This should not happen in production.`);
      throw new Error('Result queue service not available');
    }

    // Send via Redis queue
    try {
      const jobId = await nodeResultQueueService.addResult(testId, data, nodeId);
      if (!jobId) {
        throw new Error('Failed to add result to queue');
      }

      if (this.config.enableLogging) {
        this.logger.info(`TestProgressService: Successfully sent test result via queue for test ${testId}, job ID: ${jobId}`);
      }
    } catch (error: any) {
      this.logger.error(`TestProgressService: Queue error sending test completion: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send log message to websocket connector
   */
  private async sendLogMessage(testId: string, message: string): Promise<void> {
    const websocketConnector = this.connectionService.getWebSocketConnector();
    if (!websocketConnector || typeof websocketConnector.sendLogMessage !== 'function') {
      this.logger.warn('TestProgressService: WebSocket connector not available or missing sendLogMessage method');
      return;
    }

    try {
      await websocketConnector.sendLogMessage(testId, message);
    } catch (error: any) {
      this.logger.error(`TestProgressService: WebSocket error sending log message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add step progress to batch
   */
  private addToBatch(testId: string, data: StepProgressData): void {
    if (!this.pendingReports.has(testId)) {
      this.pendingReports.set(testId, []);
    }
    this.pendingReports.get(testId)!.push(data);
  }

  /**
   * Start batch reporting timer
   */
  private startBatchReporting(): void {
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
    }

    this.reportingTimer = setInterval(async () => {
      await this.flushAllBatches();
    }, this.config.reportingInterval);
  }

  /**
   * Flush batch reports for a specific test
   */
  private async flushBatchForTest(testId: string): Promise<void> {
    const reports = this.pendingReports.get(testId);
    if (!reports || reports.length === 0) {
      return;
    }

    for (const report of reports) {
      try {
        await this.sendStepProgress(testId, report);
      } catch (error: any) {
        this.logger.error(`TestProgressService: Error flushing batch report for test ${testId}: ${error.message}`);
      }
    }

    this.pendingReports.delete(testId);
  }

  /**
   * Flush all pending batch reports
   */
  async flushAllBatches(): Promise<void> {
    const testIds = Array.from(this.pendingReports.keys());

    for (const testId of testIds) {
      await this.flushBatchForTest(testId);
    }
  }
}
