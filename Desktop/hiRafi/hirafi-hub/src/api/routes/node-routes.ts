/**
 * Node API Routes
 * Handles all test node-related endpoints
 */

import { Router, Request, Response } from 'express';
import { nodeManager } from '../../core/node-manager/index.js';
import { nodeRegistry } from '../../services/node/index.js';
import { testManager } from '../../core/test-manager/index.js';
import { logger } from '../../utils/logger.js';
import { webSocketConnector as websocketConnector } from '../../connectors/index.js';
import { authenticateAdmin, AdminRequest } from '../middleware/adminAuthMiddleware.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';

const router = Router();

/**
 * GET /api/nodes
 * Get all nodes
 * @access Admin
 */
router.get('/', authenticateAdmin, (req: AdminRequest, res: Response) => {
  try {
    const nodes = nodeManager.getAllNodes();

    res.json({
      success: true,
      nodes,
      count: nodes.length
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/nodes/stats
 * Get node statistics
 * @access Admin
 */
router.get('/stats', authenticateAdmin, (req: AdminRequest, res: Response) => {
  try {
    const stats = nodeRegistry.getNodeStatistics();
    res.json({
      success: true,
      statistics: stats
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/nodes/:nodeId
 * Get a specific node by ID
 * @access Admin
 */
router.get('/:nodeId', authenticateAdmin, (req: AdminRequest, res: Response) => {
  try {
    const { nodeId } = req.params;
    const node = nodeManager.getNode(nodeId);

    if (!node) {
      return res.status(404).json({
        success: false,
        error: `Node with ID ${nodeId} not found`
      });
    }

    // Check WebSocket connection status
    const isConnected = websocketConnector.isClientConnected(nodeId);

    res.json({
      success: true,
      data: {
        node: {
          ...node,
          isConnected
        }
      }
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/nodes
 * Register a new node - DEPRECATED: Use WebSocket registration only
 * @access Node
 */
router.post('/', (req: Request, res: Response) => {
  try {
    logger.warn('API: HTTP node registration is deprecated - use WebSocket registration only');
    logger.warn('API: This prevents duplicate registrations and maintains single truth source');

    res.status(400).json({
      success: false,
      error: 'HTTP node registration is deprecated. Please use WebSocket connection for node registration.',
      message: 'Nodes should connect via WebSocket to maintain single registration authority'
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * PUT /api/nodes/:nodeId/heartbeat
 * Update node heartbeat
 * @access Node
 */
router.put('/:nodeId/heartbeat', (req: Request, res: Response) => {
  try {
    const { nodeId } = req.params;
    const { vncUrl, vncPort } = req.body;

    // Önce node'u alalım
    const node = nodeManager.getNode(nodeId);

    if (!node) {
      return res.status(404).json({
        success: false,
        error: `Node with ID ${nodeId} not found`
      });
    }

    // vncUrl veya vncPort gönderilmişse, node bilgilerini güncelleyelim
    if (vncUrl && node.vncUrl !== vncUrl) {
      node.vncUrl = vncUrl;
      logger.debug(`Node ${nodeId} VNC URL updated: ${vncUrl}`);
    }

    if (vncPort && node.vncPort !== vncPort) {
      node.vncPort = vncPort;
      logger.debug(`Node ${nodeId} VNC Port updated: ${vncPort}`);
    }

    // Heartbeat'i güncelleyelim
    const success = nodeManager.updateNodeHeartbeat(nodeId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: `Node with ID ${nodeId} not found`
      });
    }

    res.json({
      success: true,
      timestamp: Date.now()
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * PUT /api/nodes/:nodeId/status
 * Update node status
 * @access Node
 */
router.put('/:nodeId/status', async (req: Request, res: Response) => {
  try {
    const { nodeId } = req.params;
    const { status, testId } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        error: 'Status is required'
      });
    }

    let success = false;

    switch (status) {
      case 'available':
        success = await nodeManager.markNodeAsAvailable(nodeId);
        break;
      case 'busy':
        if (!testId) {
          return res.status(400).json({
            success: false,
            error: 'testId is required when status is busy'
          });
        }
        success = await nodeManager.markNodeAsBusy(nodeId, testId);
        break;
      case 'inactive':
        success = await nodeManager.markNodeAsInactive(nodeId);
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid status. Must be available, busy, or inactive'
        });
    }

    if (!success) {
      return res.status(404).json({
        success: false,
        error: `Node with ID ${nodeId} not found or status could not be updated`
      });
    }

    res.json({
      success: true,
      message: `Node ${nodeId} status updated to ${status}`
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/nodes/:nodeId/tests
 * Get all tests assigned to a node
 * @access Admin
 */
router.get('/:nodeId/tests', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { nodeId } = req.params;
    const node = nodeManager.getNode(nodeId);

    if (!node) {
      return res.status(404).json({
        success: false,
        error: `Node with ID ${nodeId} not found`
      });
    }

    // Get all tests where nodeId matches
    const allTests = await testManager.getAllTests();
    const nodeTests = allTests.filter((test: any) => test.nodeId === nodeId);

    res.json({
      success: true,
      tests: nodeTests,
      count: nodeTests.length
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * DELETE /api/nodes/:nodeId
 * Remove a node
 * @access Admin
 */
router.delete('/:nodeId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { nodeId } = req.params;
    const success = await nodeManager.removeNode(nodeId);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: `Node with ID ${nodeId} not found or could not be removed`
      });
    }

    // WebSocket connection is already handled by the NodeRegistry.removeNode method
    // No need to manually disconnect here

    res.json({
      success: true,
      message: `Node ${nodeId} removed successfully`
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

export default router;