/**
 * Node Manager Factory
 * 
 * Factory for creating and configuring node managers with proper
 * dependency injection. Replaces the singleton-based pattern.
 */

import { INodeManager } from '../interfaces/node-manager.interface.js';
import { INodeManagerFactory, NodeManagerFactoryConfig } from '../interfaces/node-manager-factory.interface.js';
import { ApplicationContainer } from '../../common/di/application-container.js';
import { ServiceNames } from '../types/node-manager-types.js';

/**
 * Node Manager Factory
 * 
 * Creates node managers with proper dependency injection
 * and service registry management.
 */
export class NodeManagerFactory implements INodeManagerFactory {
  /**
   * Create a node manager with all dependencies
   */
  static async createNodeManager(
    config: NodeManagerFactoryConfig,
    container: ApplicationContainer
  ): Promise<INodeManager> {
    const nodeId = config.nodeId || 'default';

    if (config.enableLogging) {
      console.info(`[NodeManagerFactory] Creating node manager for node ${nodeId}`);
    }

    try {
      // Register external services if provided
      if (config.websocketConnector) {
        container.registerService(ServiceNames.WEBSOCKET_CONNECTOR, () => config.websocketConnector);
      }
      
      if (config.config) {
        container.registerService(ServiceNames.CONFIG, () => config.config);
      }

      // Get the node manager from the container
      const nodeManager = await container.resolveService<INodeManager>(ServiceNames.NODE_MANAGER);

      if (config.enableLogging) {
        console.info(`[NodeManagerFactory] Successfully created node manager for node ${nodeId}`);
      }
      return nodeManager;

    } catch (error: any) {
      if (config.enableLogging) {
        console.error(`[NodeManagerFactory] Failed to create node manager for node ${nodeId}: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Create a node manager with all dependencies (instance method)
   */
  async createNodeManager(
    config: NodeManagerFactoryConfig,
    container: ApplicationContainer
  ): Promise<INodeManager> {
    return NodeManagerFactory.createNodeManager(config, container);
  }
}

/**
 * Convenience function for creating a node manager
 * Maintains backward compatibility with existing code
 */
export async function createNodeManager(
  container: ApplicationContainer,
  nodeId?: string,
  websocketConnector?: any,
  config?: any
): Promise<INodeManager> {
  return NodeManagerFactory.createNodeManager({
    nodeId,
    websocketConnector,
    config,
    enableLogging: false
  }, container);
}
