"use client"

import { useState } from "react"
import { toast } from "@/lib/utils/toast-utils"
import html2canvas from "html2canvas"
import { feedbackApi } from "@/lib/api"

interface FeedbackData {
  feedback: string
  subject: string
  email: string
  screenshot: string | null
  userId?: string
}

interface UseFeedbackReturn {
  feedback: string
  setFeedback: (value: string) => void
  subject: string
  setSubject: (value: string) => void
  email: string
  setEmail: (value: string) => void
  screenshot: string | null
  setScreenshot: (value: string | null) => void
  loading: boolean
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  captureScreenshot: () => Promise<void>
  handleSubmit: (e: React.FormEvent) => Promise<void>
  resetForm: () => void
}

export function useFeedback(userId?: string): UseFeedbackReturn {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [feedback, setFeedback] = useState("")
  const [subject, setSubject] = useState("")
  const [email, setEmail] = useState("")
  const [screenshot, setScreenshot] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  // Reset form fields
  const resetForm = () => {
    setFeedback("")
    setSubject("")
    setEmail("")
    setScreenshot(null)
  }

  // Capture screenshot
  const captureScreenshot = async () => {
    setLoading(true)

    try {
      // Temporarily close dialog while taking screenshot
      setIsDialogOpen(false)

      // Add a small delay to ensure dialog is fully closed
      await new Promise(resolve => setTimeout(resolve, 100))

      // Capture screenshot
      const canvas = await html2canvas(document.body, {
        allowTaint: true,
        useCORS: true,
        logging: false,
        scale: window.devicePixelRatio
      })

      // Convert to base64
      const dataUrl = canvas.toDataURL("image/png")
      setScreenshot(dataUrl)

      // Reopen dialog
      setIsDialogOpen(true)
      toast.success("Ekran görüntüsü alındı")
    } catch (error) {
      console.error("Ekran görüntüsü alınırken hata oluştu:", error)
      toast.error("Ekran görüntüsü alınamadı")
      setIsDialogOpen(true)
    } finally {
      setLoading(false)
    }
  }

  // Submit feedback
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validation
    if (!feedback.trim()) {
      toast.error("Lütfen geri bildiriminizi yazın")
      return
    }

    if (!subject) {
      toast.error("Lütfen bir konu seçin")
      return
    }

    setLoading(true)

    try {
      const feedbackData: FeedbackData = {
        feedback,
        subject,
        email,
        screenshot,
        ...(userId && { userId })
      }

      // Yeni API yapısını kullan
      const response = await feedbackApi.sendFeedback(feedbackData)

      if (response.success) {
        toast.success("Geri bildiriminiz için teşekkürler!")
        setIsDialogOpen(false)
        resetForm()
      } else {
        toast.error(response.error || "Geri bildirim gönderilirken bir hata oluştu")
      }
    } catch (error) {
      toast.error("Bağlantı hatası, lütfen daha sonra tekrar deneyin")
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  return {
    feedback,
    setFeedback,
    subject,
    setSubject,
    email,
    setEmail,
    screenshot,
    setScreenshot,
    loading,
    isDialogOpen,
    setIsDialogOpen,
    captureScreenshot,
    handleSubmit,
    resetForm
  }
}