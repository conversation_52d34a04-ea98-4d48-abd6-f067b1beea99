import { useState, useEffect } from 'react'
import { toast } from "@/lib/utils/toast-utils"
import { getDashboardStats, getRecentRuns, getRecentScenarios, getTestActivity } from "@/lib/api/dashboard-api"
import { getBestTestResults, calculatePassRate, TestResultCounts } from "@/lib/utils/run-utils"

// API'den dönen dashboard istatistikleri için interface
interface DashboardStats {
  tests: {
    total: number
    success: number
    failure: number
    successRate: number
  }
  steps: {
    total: number
    passed: number
    failed: number
    successRate: number
  }
  runs: {
    total: number
    completed: number
    failed: number
    successRate: number
    totalTests: number
    passedTests: number
    failedTests: number
  }
  // Geriye dönük uyumluluk için eski alanlar
  totalTests: number
  totalSuccess: number
  totalFailure: number
  successRate: number
}

// API'den dönen haftalık aktivite için interface
interface DailyActivity {
  date: string
  passed: number
  failed: number
}

// API'den dönen 24 saatlik aktivite için interface
interface HourlyActivity {
  date: string
  passed: number
  failed: number
}

// Son çalıştırılan testler için interface
export interface RecentRun {
  id: string
  name: string
  status: string
  result: string
  date: string
  duration: string
  passRate: number
  totalTests: number
  passed: number
  failed: number
  skipped?: number
  environment: string
  triggeredBy: string
  createdAt?: string
  startedAt?: string
  completedAt?: string
  testResults?: TestResultCounts
}

// Hook'un geri döndüreceği tüm veriler için interface
export interface DashboardData {
  stats: DashboardStats
  weeklyActivity: DailyActivity[]
  hourlyActivity: HourlyActivity[]
  recentRuns: RecentRun[]
  isLoading: boolean
  isError: boolean
  error?: string
  refetch: () => Promise<void>
}

export function useDashboard(timeframe: 'day' | 'week' | 'month' = 'week'): DashboardData {
  const [stats, setStats] = useState<DashboardStats>({
    tests: {
      total: 0,
      success: 0,
      failure: 0,
      successRate: 0
    },
    steps: {
      total: 0,
      passed: 0,
      failed: 0,
      successRate: 0
    },
    runs: {
      total: 0,
      completed: 0,
      failed: 0,
      successRate: 0,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0
    },
    // Geriye dönük uyumluluk için
    totalTests: 0,
    totalSuccess: 0,
    totalFailure: 0,
    successRate: 0
  })
  const [weeklyActivity, setWeeklyActivity] = useState<DailyActivity[]>([])
  const [hourlyActivity, setHourlyActivity] = useState<HourlyActivity[]>([])
  const [recentRuns, setRecentRuns] = useState<RecentRun[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [isError, setIsError] = useState<boolean>(false)
  const [error, setError] = useState<string | undefined>()

  // API'yi çağıracak fonksiyon
  const fetchDashboardData = async () => {
    setIsLoading(true)
    setIsError(false)
    setError(undefined)

    try {
      // İstatistikler için API çağrısı
      const statsResult = await getDashboardStats(timeframe)

      if (!statsResult.success) {
        throw new Error(statsResult.error || 'Failed to fetch dashboard statistics')
      }

      // Son çalıştırılan testler için API çağrısı
      const recentRunsResult = await getRecentRuns(5)

      if (!recentRunsResult.success) {
        throw new Error(recentRunsResult.error || 'Failed to fetch recent runs')
      }

      // İstatistikleri kaydet
      const apiStats = statsResult.data?.stats || {};

      // Yeni API formatını kullan
      setStats({
        tests: {
          total: apiStats.tests?.total || 0,
          success: apiStats.tests?.success || 0,
          failure: apiStats.tests?.failure || 0,
          successRate: apiStats.tests?.successRate || 0
        },
        steps: {
          total: apiStats.steps?.total || 0,
          passed: apiStats.steps?.passed || 0,
          failed: apiStats.steps?.failed || 0,
          successRate: apiStats.steps?.successRate || 0
        },
        runs: {
          total: apiStats.runs?.total || 0,
          completed: apiStats.runs?.completed || 0,
          failed: apiStats.runs?.failed || 0,
          successRate: apiStats.runs?.successRate || 0,
          totalTests: apiStats.runs?.totalTests || 0,
          passedTests: apiStats.runs?.passedTests || 0,
          failedTests: apiStats.runs?.failedTests || 0
        },
        // Geriye dönük uyumluluk için
        totalTests: apiStats.tests?.total || 0,
        totalSuccess: apiStats.tests?.success || 0,
        totalFailure: apiStats.tests?.failure || 0,
        successRate: apiStats.tests?.successRate || 0
      })

      // Son çalıştırılan testleri formatlayıp kaydet
      const runs = recentRunsResult.data?.runs || []

      // Format reports for UI
      const formattedRuns = runs.map((run: any) => {
        if (!run) return null;

        try {
          // Store original date for proper formatting in UI
          const dateValue = run.createdAt || run.date;

          // Format display date
          let formattedDate = 'N/A';
          try {
            if (dateValue) {
              const reportDate = new Date(dateValue);

              // Validate the date
              if (isNaN(reportDate.getTime())) {
                console.warn('Invalid date value received:', dateValue);
                formattedDate = 'Invalid date';
              } else {
                formattedDate = reportDate.toLocaleDateString('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric'
                });
              }
            }
          } catch (e) {
            console.error('Date formatting error:', e, 'Input:', dateValue);
          }

          // Get the run status from the API
          const runStatus = typeof run.status === 'string' ? run.status.toLowerCase() : '';

          // Initialize status and result
          let status = runStatus;
          let result = 'pending';

          // If the run is still running, keep that status
          if (runStatus === 'running' || runStatus === 'pending') {
            status = 'running';
            result = 'pending';
          } else if (runStatus === 'scheduled') {
            status = 'scheduled';
            result = 'pending';
          } else {
            // For completed runs, determine the result based on test results using utility functions
            const testResults = getBestTestResults(run);
            const { total, completed, failed, stopped } = testResults;

            // If all tests passed, it's a success
            if (total > 0 && completed === total && failed === 0 && stopped === 0) {
              status = 'completed';
              result = 'passed';
            }
            // If all tests failed, it's a failure
            else if (total > 0 && failed === total) {
              status = 'failed';
              result = 'failed';
            }
            // If some tests passed and some failed, it's partial
            else if (total > 0 && (completed > 0 || failed > 0 || stopped > 0)) {
              status = 'partial';
              result = 'partial';
            }
            // Default to the original status if we can't determine
            else if (
              runStatus === 'failed' ||
              runStatus === 'error' ||
              runStatus === 'failure'
            ) {
              result = 'failed';
            } else if (runStatus === 'warning') {
              result = 'warning';
            }
          }

          // Format duration for display
          let duration = 'N/A';
          if (run.completedAt && run.startedAt) {
            try {
              const start = new Date(run.startedAt).getTime();
              const end = new Date(run.completedAt).getTime();
              const durationMs = end - start;
              const durationSec = durationMs / 1000;

              const minutes = Math.floor(durationSec / 60);
              const seconds = Math.round(durationSec % 60);
              duration = minutes > 0 ? `${minutes}m ${seconds}s` : `${durationSec.toFixed(2)}s`;
            } catch (e) {
              console.error('Duration calculation error:', e);
            }
          }

          // Calculate test counts and pass rate using utility functions
          const finalTestResults = getBestTestResults(run);
          const passRate = calculatePassRate(finalTestResults);
          const { total: totalTests, completed, failed, stopped, queued, running } = finalTestResults;

          return {
            id: run.id,
            name: run.runName || run.name || 'Unnamed Run',
            status,
            result,
            date: formattedDate,
            duration,
            passRate,
            totalTests,
            passed: completed,
            failed,
            stopped,
            environment: run.environment || 'Production',
            triggeredBy: run.executedUserName || run.userName || run.userId || 'System',
            createdAt: run.createdAt,
            startedAt: run.startedAt,
            completedAt: run.completedAt,
            testResults: finalTestResults // Use calculated finalTestResults instead of raw run.testResults
          };
        } catch (err) {
          console.error('Run formatting error:', err);
          // Return minimum valid run object in error case
          return {
            id: run.id || Date.now().toString(),
            name: 'Error formatting run',
            date: new Date().toLocaleDateString(),
            status: 'completed',
            result: 'failed',
            duration: 'N/A',
            passRate: 0,
            totalTests: 0,
            passed: 0,
            failed: 0,
            environment: 'Unknown',
            triggeredBy: 'System'
          };
        }
      }).filter(Boolean); // Filter out null values

      setRecentRuns(formattedRuns)

      // API'den gelen günlük aktivite verilerini kullan
      if (statsResult.data?.dailyActivity && Array.isArray(statsResult.data.dailyActivity) && statsResult.data.dailyActivity.length > 0) {
        // API'den gelen günlük aktivite verilerini kullan
        setWeeklyActivity(statsResult.data.dailyActivity);
      } else {
        // API'den günlük aktivite verileri gelmezse, boş bir array oluştur
        // Son 7 günü hesapla
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 6); // 7 gün (bugün dahil)

        // Günleri hazırla (son 7 gün)
        const days: DailyActivity[] = [];
        for (let i = 0; i < 7; i++) {
          const date = new Date(startDate);
          date.setDate(date.getDate() + i);
          days.push({
            date: date.toISOString().split('T')[0], // YYYY-MM-DD formatı
            passed: 0,
            failed: 0
          });
        }

        setWeeklyActivity(days);
      }

      // 24 saatlik aktiviteyi ayarla - API'den getir
      try {
        const activityResult = await getTestActivity();

        if (activityResult.success && activityResult.data?.activity) {
          setHourlyActivity(activityResult.data.activity);
        } else {
          // API'den veri gelmezse boş array kullan
          setHourlyActivity([]);
        }
      } catch (activityError) {
        console.error('Failed to fetch activity data:', activityError);
        setHourlyActivity([]);
      }

    } catch (err: any) {
      console.error('Dashboard data fetch error:', err)
      setIsError(true)
      setError(err.message)
      toast.error(`Failed to load dashboard data: ${err.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  // Component mount olduğunda ve timeframe değiştiğinde verileri çek
  useEffect(() => {
    fetchDashboardData()
  }, [timeframe])

  // Hook'un döndüreceği veriler
  return {
    stats,
    weeklyActivity,
    hourlyActivity,
    recentRuns,
    isLoading,
    isError,
    error,
    refetch: fetchDashboardData
  }
}