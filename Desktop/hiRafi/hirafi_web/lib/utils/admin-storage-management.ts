/**
 * Admin Storage Management Utility
 * Separate storage management for admin authentication and data
 */

class AdminStorageManager {
  private adminStoreKeys = [
    'adminAuthToken',
    'adminUser'
  ];

  /**
   * Clear all admin-related storage data
   */
  clearAdminStorage() {
    console.log('[AdminStorageManager] Clearing all admin storage');
    
    this.adminStoreKeys.forEach(key => {
      try {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      } catch (error) {
        console.warn(`[AdminStorageManager] Error removing ${key}:`, error);
      }
    });
  }

  /**
   * Clear admin storage on logout
   */
  clearOnLogout() {
    this.clearAdminStorage();
  }

  /**
   * Clear admin storage on session switch
   */
  clearOnSessionSwitch() {
    this.clearAdminStorage();
  }
}

// Singleton instance
export const adminStorageManager = new AdminStorageManager();

/**
 * Enhanced admin logout function
 */
export function enhancedAdminLogout(originalLogout: () => void) {
  // Clear admin storage first
  adminStorageManager.clearOnLogout();
  
  // Then perform original logout
  originalLogout();
} 