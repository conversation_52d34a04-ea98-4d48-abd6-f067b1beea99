/**
 * Device Provider Validation Utilities for Backend
 * Simplified validation for device provider selection
 */

import { TestRequest } from '../models/types.js';
import { AndroidEnvironmentSettings } from '../models/android-environment.js';
import { logger } from './logger.js';

/**
 * Device provider types
 */
export type DeviceProvider = 'sauceLabs' | 'testinium';

/**
 * Device provider validation result
 */
export interface DeviceProviderValidationResult {
  isValid: boolean;
  error?: string;
  warning?: string;
  selectedProvider?: DeviceProvider;
}

/**
 * Simplified Device Provider Validator
 * Core validation for device provider selection
 */
export class DeviceProviderValidator {
  /**
   * Validate device provider selection in test request
   * @param testRequest Test request to validate
   * @returns Validation result
   */
  static validateTestRequest(testRequest: TestRequest): DeviceProviderValidationResult {
    try {
      // Check if platform is Android
      if (!testRequest.environmentSettings ||
          !('platform' in testRequest.environmentSettings) ||
          testRequest.environmentSettings.platform !== 'android') {
        return { isValid: true }; // Not Android, no validation needed
      }

      const androidSettings = testRequest.environmentSettings as AndroidEnvironmentSettings;
      const deviceProvider = testRequest.deviceProvider || androidSettings.deviceProvider;

      if (!deviceProvider) {
        return {
          isValid: false,
          error: 'Device provider must be specified for Android tests'
        };
      }

      if (!this.isValidProvider(deviceProvider)) {
        return {
          isValid: false,
          error: `Invalid device provider: ${deviceProvider}. Must be 'sauceLabs' or 'testinium'`
        };
      }

      // Basic device validation
      const hasDevices = this.hasSelectedDevices(androidSettings, deviceProvider);
      if (!hasDevices) {
        return {
          isValid: false,
          error: `No devices selected for ${deviceProvider} provider`
        };
      }

      return {
        isValid: true,
        selectedProvider: deviceProvider
      };

    } catch (error: any) {
      logger.error(`DeviceProviderValidator: Error validating test request: ${error.message}`);
      return {
        isValid: false,
        error: `Validation error: ${error.message}`
      };
    }
  }

  /**
   * Check if provider is valid
   */
  private static isValidProvider(provider: string): provider is DeviceProvider {
    return provider === 'sauceLabs' || provider === 'testinium';
  }

  /**
   * Check if devices are selected for the provider
   */
  private static hasSelectedDevices(
    androidSettings: AndroidEnvironmentSettings,
    deviceProvider: DeviceProvider
  ): boolean {
    if (deviceProvider === 'sauceLabs') {
      return (androidSettings.sauceLabs?.selectedDevices || []).length > 0;
    } else if (deviceProvider === 'testinium') {
      return (androidSettings.testinium?.selectedDevices || []).length > 0;
    }
    return false;
  }

  /**
   * Get the active device provider from environment settings
   * @param androidSettings Android environment settings
   * @returns Active device provider or null
   */
  static getActiveProvider(androidSettings: AndroidEnvironmentSettings): DeviceProvider | null {
    // Check explicit deviceProvider field first
    if (androidSettings.deviceProvider && this.isValidProvider(androidSettings.deviceProvider)) {
      return androidSettings.deviceProvider;
    }

    // Fallback to checking device counts
    const sauceLabsDevices = androidSettings.sauceLabs?.selectedDevices || [];
    const testiniumDevices = androidSettings.testinium?.selectedDevices || [];

    if (sauceLabsDevices.length > 0 && testiniumDevices.length === 0) {
      return 'sauceLabs';
    }

    if (testiniumDevices.length > 0 && sauceLabsDevices.length === 0) {
      return 'testinium';
    }

    return null;
  }
}
