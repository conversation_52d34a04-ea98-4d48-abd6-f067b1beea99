"use client"

import { useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>, User<PERSON><PERSON><PERSON>, ClockIcon as User<PERSON><PERSON>, ShieldChe<PERSON> } from "lucide-react"
import type { TeamMember, Role } from "@/types/team"

interface TeamStatsProps {
  teamMembers: TeamMember[]
  roles: Role[]
}

export function TeamStats({ teamMembers, roles }: TeamStatsProps) {
  const stats = useMemo(() => {
    const activeMembers = teamMembers.filter((m) => m.status === "active").length
    const pendingMembers = teamMembers.filter((m) => m.status === "pending").length

    const roleStats = roles
      .map((role) => ({
        name: role.name,
        count: teamMembers.filter((m) => m.role === role.name).length,
        color: role.color,
      }))
      .sort((a, b) => b.count - a.count)

    return {
      totalMembers: teamMembers.length,
      activeMembers,
      pendingInvitations: pendingMembers,
      roles: roleStats,
    }
  }, [teamMembers, roles])

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Toplam Üye</p>
            <h3 className="text-2xl font-bold">{stats.totalMembers}</h3>
          </div>
          <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
            <Users className="h-6 w-6 text-blue-600 dark:text-blue-300" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Aktif Üye</p>
            <h3 className="text-2xl font-bold">{stats.activeMembers}</h3>
          </div>
          <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
            <UserCheck className="h-6 w-6 text-green-600 dark:text-green-300" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Bekleyen Davet</p>
            <h3 className="text-2xl font-bold">{stats.pendingInvitations}</h3>
          </div>
          <div className="h-12 w-12 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center">
            <UserClock className="h-6 w-6 text-amber-600 dark:text-amber-300" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Rol Sayısı</p>
            <h3 className="text-2xl font-bold">{stats.roles.length}</h3>
          </div>
          <div className="h-12 w-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
            <ShieldCheck className="h-6 w-6 text-purple-600 dark:text-purple-300" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
