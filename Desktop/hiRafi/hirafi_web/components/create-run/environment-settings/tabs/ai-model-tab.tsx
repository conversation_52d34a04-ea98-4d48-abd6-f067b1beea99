"use client"

import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  RefreshCw,
  AlertCircle,
  Bot,
  Database
} from "lucide-react"
import { useAIModels } from "@/hooks/useAIModels"
import { useRunForm } from "../../run-context"

interface AIModelTabProps {
  environmentSettings: any;
  updateEnvironmentSettings: (settings: any) => void;
  errors?: Record<string, string>;
}

export function AIModelTab({
  environmentSettings,
  updateEnvironmentSettings,
  errors
}: AIModelTabProps) {
  const { models, isLoading } = useAIModels()
  const {
    testDataEnvironments,
    selectedTestDataEnvironment,
    isLoadingTestDataEnvironments,
    selectTestDataEnvironment
  } = useRunForm()

  // Function to handle AI model selection
  const handleAIModelSelect = (modelId: string) => {
    // Find the selected model to get its name
    const selectedModel = models.find(model => model.id === modelId);

    // Update both the model ID and name
    updateEnvironmentSettings({
      aiModel: modelId,
      aiModelName: selectedModel ? selectedModel.name : undefined
    });
  }

  return (
    <div className="space-y-6">
      {/* AI Model */}
      <div className="grid gap-2">
        <div className="flex items-center">
          <div className="bg-indigo-100 dark:bg-indigo-900/50 p-1.5 rounded-full mr-2">
            <Bot className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
          </div>
          <Label htmlFor="aiModel" className="font-medium text-indigo-700 dark:text-indigo-300">
            AI Model <span className="text-red-500">*</span>
          </Label>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-md p-2 border border-indigo-200 dark:border-indigo-800 shadow-sm">
          <Select
            value={environmentSettings.aiModel || "default"}
            onValueChange={handleAIModelSelect}
          >
            <SelectTrigger id="aiModel" className="border-gray-200 dark:border-gray-700 bg-transparent">
              <SelectValue placeholder="Select AI model" />
            </SelectTrigger>
            <SelectContent>
              {isLoading ? (
                <SelectItem value="loading" disabled>
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading models...
                  </div>
                </SelectItem>
              ) : (
                models.map(model => (
                  <SelectItem key={model.id} value={model.id} className="flex items-center" disabled={!model.isActive}>
                    <div className="flex items-center">
                      <Bot className="h-4 w-4 mr-2 text-indigo-500" />
                      {model.name} {!model.isActive && '(Devre Dışı)'}
                    </div>
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>
        {errors?.aiModel && (
          <p className="text-sm text-red-500 flex items-center">
            <AlertCircle className="h-4 w-4 mr-1" />
            {errors.aiModel}
          </p>
        )}
      </div>

      {/* Test Data Environment */}
      <div className="grid gap-2">
        <div className="flex items-center">
          <div className="bg-green-100 dark:bg-green-900/50 p-1.5 rounded-full mr-2">
            <Database className="h-4 w-4 text-green-600 dark:text-green-400" />
          </div>
          <Label htmlFor="testDataEnvironment" className="font-medium text-green-700 dark:text-green-300">
            Test Data Environment
          </Label>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-md p-2 border border-green-200 dark:border-green-800 shadow-sm">
          <Select
            value={selectedTestDataEnvironment?.id || "none"}
            onValueChange={(value: string) => {
              if (value === "none") {
                selectTestDataEnvironment(null);
              } else {
                const environment = testDataEnvironments.find(env => env.id === value);
                selectTestDataEnvironment(environment || null);
              }
            }}
          >
            <SelectTrigger id="testDataEnvironment" className="border-gray-200 dark:border-gray-700 bg-transparent">
              <SelectValue placeholder="Select test data environment (optional)" />
            </SelectTrigger>
            <SelectContent>
              {isLoadingTestDataEnvironments ? (
                <SelectItem value="loading" disabled>
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading environments...
                  </div>
                </SelectItem>
              ) : (
                <>
                  <SelectItem value="none">No environment selected</SelectItem>
                  {testDataEnvironments.map(env => (
                    <SelectItem key={env.id} value={env.id}>
                      <div className="flex items-center">
                        <Database className="h-4 w-4 mr-2 text-green-500" />
                        <span>{env.name}</span>
                        {env.description && (
                          <span className="text-xs text-gray-500 ml-2">- {env.description}</span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </>
              )}
            </SelectContent>
          </Select>
        </div>
        <p className="text-xs text-gray-500">
          Select a test data environment to use specific test data values during test execution.
        </p>
      </div>

      {/* AI Model Information */}
      <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-md border border-indigo-200 dark:border-indigo-800 shadow-sm">
        <h4 className="text-sm font-medium text-indigo-700 dark:text-indigo-300 mb-2">
          About AI Models
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          The AI model is used to Run and analyze your test results and provide intelligent insights.
          Different models have different capabilities and performance characteristics.
        </p>
      </div>
    </div>
  )
}
