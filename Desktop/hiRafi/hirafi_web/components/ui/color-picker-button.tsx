import React, { useRef, useEffect, useState } from 'react'

interface ColorPickerButtonProps {
  currentColor: string
  onColorChange: (color: string) => void
}

export function ColorPickerButton({ currentColor = '#3b82f6', onColorChange }: ColorPickerButtonProps) {
  const colorPickerRef = useRef<HTMLInputElement>(null)
  const colorPreviewRef = useRef<HTMLDivElement>(null)
  const [tempColor, setTempColor] = useState(currentColor?.startsWith('#') ? currentColor : '#3b82f6')

  // Dışarıdan gelen renk değiştiğinde geçici renk state'ini güncelle
  useEffect(() => {
    if (currentColor?.startsWith('#')) {
      setTempColor(currentColor)
    } else {
      setTempColor('#3b82f6') // Varsayılan mavi
    }
  }, [currentColor])

  // HTML renk seçici elementini hazırla
  useEffect(() => {
    const colorPicker = colorPickerRef.current
    const colorPreview = colorPreviewRef.current

    if (!colorPicker || !colorPreview) return

    // Renk değişimlerini yakala
    const handleInput = (e: Event) => {
      const value = (e.target as HTMLInputElement).value

      // Geçici state'i güncelle
      setTempColor(value)

      // DOM önizlemesini güncelle
      if (colorPreview) {
        colorPreview.style.backgroundColor = value
      }
    }

    // Renk seçimi tamamlandığında
    const handleChange = (e: Event) => {
      const value = (e.target as HTMLInputElement).value

      // State'i güncelle
      onColorChange(value)
    }

    // Click event handler (mobil cihazlar için)
    const handleClick = () => {
      // Sadece bilgi için
    }

    // Event listenerları ekle
    colorPicker.addEventListener('input', handleInput)
    colorPicker.addEventListener('change', handleChange)
    colorPicker.addEventListener('click', handleClick)

    // Temizlik fonksiyonu
    return () => {
      colorPicker.removeEventListener('input', handleInput)
      colorPicker.removeEventListener('change', handleChange)
      colorPicker.removeEventListener('click', handleClick)
    }
  }, [onColorChange])

  // İlk render'da ve yeni bir renk seçildiğinde DOM'u güncelle
  useEffect(() => {
    const colorPreview = colorPreviewRef.current

    if (colorPreview && colorPickerRef.current) {
      // Eğer hex renk kodu ise önizlemeyi güncelle
      if (currentColor?.startsWith('#')) {
        colorPreview.style.backgroundColor = currentColor
        colorPreview.style.borderColor = 'white'
        colorPreview.style.boxShadow = '0 0 0 2px black'
        colorPickerRef.current.value = currentColor
      } else {
        // Önceden tanımlı renklerden biri ise
        colorPreview.style.backgroundColor = '#3b82f6' // Varsayılan mavi
        colorPreview.style.borderColor = '#e2e8f0'
        colorPreview.style.boxShadow = 'none'
        colorPickerRef.current.value = '#3b82f6'
      }
    }
  }, [currentColor])

  return (
    <div
      ref={colorPreviewRef}
      className="w-10 h-10 rounded-full border-2 border-gray-200 flex-shrink-0 relative overflow-hidden cursor-pointer"
      style={{
        backgroundColor: tempColor,
        borderColor: tempColor !== '#3b82f6' ? 'white' : '#e2e8f0',
        boxShadow: tempColor !== '#3b82f6' ? '0 0 0 2px black' : 'none'
      }}
    >
      <input
        ref={colorPickerRef}
        type="color"
        value={tempColor}
        onChange={(e) => {
          // Handle the change event to prevent React warnings
          // This is a controlled input, so we need to update the state
          setTempColor(e.target.value);
        }}
        className="absolute inset-0 opacity-0 w-full h-full cursor-pointer"
        title="Select custom color"
      />
    </div>
  )
}