"use client"

import { useAuth } from "@/lib/api/auth"
import { checkUserPermission } from "@/utils/check-user-permissions"

export interface NavigationItem {
  href: string
  label: string
  resource: string
  action: string
  icon?: React.ReactNode
  children?: NavigationItem[]
}

// Say<PERSON> erişim izinleri tanımları
export const PAGE_PERMISSIONS = {
  DASHBOARD: { resource: 'Dashboard', action: 'view' },
  SCENARIOS: { resource: 'Scenario', action: 'view' },
  RUNS: { resource: 'Run', action: 'view' },
  REPORTS: { resource: 'Report', action: 'view' },
  SCHEDULE: { resource: 'Schedule', action: 'view' },
  TEST_DATA: { resource: 'TestData', action: 'view' },
  TEAMS: { resource: 'Team', action: 'view' },
  PLUGINS: { resource: 'Plugin', action: 'view' },
  SETTINGS: { resource: 'Settings', action: 'view' },
  HELP: { resource: 'Help', action: 'view' }
} as const

export function usePermissions() {
  const { user } = useAuth()

  // Kullanıcının belirli bir izne sahip olup olmadığını kontrol et
  const hasPermission = (resource: string, action: string): boolean => {
    // Admin ve company_owner her zaman erişebilir
    if (user?.accountType === 'admin' || user?.accountType === 'company_owner') {
      return true
    }

    return checkUserPermission(resource, action)
  }

  // Kullanıcının erişebileceği navigation item'ları filtrele
  const filterNavigationItems = (items: NavigationItem[]): NavigationItem[] => {
    return items.filter(item => {
      // Eğer item'ın children'ı varsa, onları da filtrele
      if (item.children) {
        const filteredChildren = filterNavigationItems(item.children)
        // Eğer hiç child kalmadıysa, parent'ı da gösterme
        if (filteredChildren.length === 0) {
          return false
        }
        // Children'ları güncelle
        item.children = filteredChildren
      }

      // Item'ın permission'ını kontrol et
      return hasPermission(item.resource, item.action)
    })
  }

  // Kullanıcının belirli bir sayfaya erişip erişemeyeceğini kontrol et
  const canAccessPage = (pageKey: keyof typeof PAGE_PERMISSIONS): boolean => {
    // Dashboard, Settings ve Help'e herkes erişebilir
    if (pageKey === 'DASHBOARD' || pageKey === 'SETTINGS' || pageKey === 'HELP') {
      return true
    }

    const permission = PAGE_PERMISSIONS[pageKey]
    return hasPermission(permission.resource, permission.action)
  }

  // Kullanıcının permissions'larını al
  const getUserPermissions = () => {
    return user?.permissions || []
  }

  // Kullanıcının role'ünü al
  const getUserRole = () => {
    return user?.roleId || user?.teamRole || 'viewer'
  }

  // Kullanıcının account type'ını al
  const getUserAccountType = () => {
    return user?.accountType || 'user'
  }

  // Admin veya company owner olup olmadığını kontrol et
  const isAdminOrOwner = () => {
    return user?.accountType === 'admin' || user?.accountType === 'company_owner'
  }

  return {
    hasPermission,
    filterNavigationItems,
    canAccessPage,
    getUserPermissions,
    getUserRole,
    getUserAccountType,
    isAdminOrOwner
  }
}
