/**
 * Test Progress Reporter Interface
 * Defines the contract for reporting test progress
 */

import { TestResult } from '../../../models/types.js';
import { EventEmitter } from 'events';

/**
 * Step Progress Data Interface
 * Represents data about a step's progress
 */
export interface StepProgressData {
  type: string;
  stepIndex?: number;
  totalSteps?: number;
  stepId?: string;
  stepName?: string;
  stepType?: string;
  status: 'started' | 'completed' | 'failed';
  timestamp: string;
  error?: string;
  duration?: number;
  result?: any;
  runId?: string;
  executionId?: string;
  scenarioId?: string;
  steps?: Array<{
    id: string;
    name: string;
    type: string;
    success: boolean | null;
    error?: string;
    duration?: number;
    timestamp: string;
  }>;
  // Nested step progress for control flow steps
  nestedSteps?: StepProgressData[];
  parentStepId?: string;
  nestingLevel?: number; // 0 for top level, 1 for first nested, etc.
  [key: string]: any;
}

/**
 * Test Progress Reporter Interface
 * Handles reporting test progress to the test hub
 */
export interface ITestProgressReporter extends EventEmitter {
  /**
   * Initialize the progress reporter
   */
  initialize(config?: any): Promise<void>;

  /**
   * Cache test metadata for later use
   */
  startTest(testId: string, executionId: string, runId: string): void;

  /**
   * Report step progress
   * @param testId Test ID
   * @param data Step progress data
   */
  reportStepProgress(testId: string, data: StepProgressData): Promise<void>;

  /**
   * Report test completion
   * @param testId Test identifier
   * @param data Test result data
   */
  reportTestCompletion(testId: string, data: TestResult): Promise<void>;

  /**
   * Report a log message
   * @param testId Test identifier
   * @param message Log message
   */
  reportLog(testId: string, message: string): Promise<void>;

  /**
   * Report that a test has started running.
   * @param testId The ID of the test that has started.
   */
  reportTestRunning(testId: string): Promise<void>;

  /**
   * Report that a test has been claimed by a node.
   * @param testId The ID of the test that has been claimed.
   * @param nodeId The ID of the node that claimed the test.
   */
  reportTestClaimed(testId: string, nodeId: string): Promise<void>;

  /**
   * Report video upload completion to hub
   * @param testId The ID of the test
   * @param videoUrl The URL of the uploaded video
   */
  reportVideoUploadCompleted(testId: string, videoUrl: string): Promise<void>;
}
