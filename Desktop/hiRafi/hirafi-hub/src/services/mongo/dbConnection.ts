/**
 * MongoDB Connection Management
 * MongoDB bağlantısını ve koleksiyon referanslarını yöneten temel modül
 */

import { MongoClient, Db, Collection, ServerApiVersion } from 'mongodb';
import { logger } from '../../utils/logger.js';
import { config } from '../../config/index.js';
import { AdminRole } from '../../models/admin.js';
import { v4 as uuidv4 } from 'uuid';

// MongoDB yapılandırması
let client: MongoClient | null = null;
let db: Db | null = null;
let isInitialized = false;
let initError: Error | null = null;

// Koleksiyon referansları
let reportsCollection: Collection | null = null;
let scenariosCollection: Collection | null = null;
let usersCollection: Collection | null = null;
let pluginsCollection: Collection | null = null;
let scenarioFoldersCollection: Collection | null = null;
let feedbackCollection: Collection | null = null;
let runsCollection: Collection | null = null;
let runReportsCollection: Collection | null = null;
let companiesCollection: Collection | null = null;
let adminsCollection: Collection | null = null;
let systemSettingsCollection: Collection | null = null;
let teamsCollection: Collection | null = null;
let schedulesCollection: Collection | null = null;
let scheduleRunsCollection: Collection | null = null;
let dataSetsCollection: Collection | null = null;
let dataSourcesCollection: Collection | null = null;
let dataEnvironmentsCollection: Collection | null = null;
let testsCollection: Collection | null = null;
let runStatusCollection: Collection | null = null;
let rolesCollection: Collection | null = null;
let teamInvitesCollection: Collection | null = null;
let testiniumAppsCollection: Collection | null = null;

// Varsayılan veritabanı adı
const dbName = config.connections.mongodb.database;

/**
 * MongoDB bağlantısını başlat
 */
export async function initialize() {
  // Zaten başlatılmışsa tekrar başlatma
  if (isInitialized && client && db) {
    return;
  }

  // Bağlantıyı başlat
  try {
    // Get connection parameters from config
    const { host, port, database, username, password, useSSL, authSource } = config.connections.mongodb;

    // Construct the connection URI
    const auth = username && password ? `${username}:${encodeURIComponent(password)}@` : '';
    const authSourceParam = `authSource=${authSource}`;
    const sslOption = useSSL ? '&ssl=true' : '';
    const uri = `mongodb://${auth}${host}:${port}/${database}?${authSourceParam}${sslOption}`;

    logger.info(`[MONGODB] Initializing MongoDB connection to ${host}:${port}/${database}`);

    // MongoDB bağlantı seçenekleri - Optimize edilmiş yüksek eşzamanlılık için
    const options = {
      serverApi: {
        version: ServerApiVersion.v1,
        strict: false, // Text indeksleri kullanabilmek için strict modu devre dışı bırak
        deprecationErrors: true,
      },
      connectTimeoutMS: 30000, // 30 saniye bağlantı zaman aşımı
      socketTimeoutMS: 120000, // 120 saniye soket zaman aşımı (test processing için)
      maxPoolSize: 150, // Maksimum bağlantı havuzu boyutu
      minPoolSize: 15, // Minimum bağlantı havuzu boyutu
      maxIdleTimeMS: 300000, // 5 dakika maksimum boşta kalma süresi
      waitQueueTimeoutMS: 60000, // 60 saniye kuyruk bekleme süresi
      retryWrites: true, // Write retry'ları etkinleştir
      retryReads: true, // Read retry'ları etkinleştir
      maxConnecting: 10, // Maksimum eşzamanlı bağlantı sayısı
      heartbeatFrequencyMS: 10000, // 10 saniye heartbeat
      serverSelectionTimeoutMS: 30000, // 30 saniye server seçim timeout
    };

    // Bağlantıyı oluştur
    client = new MongoClient(uri, options);

    // Bağlantıyı test et
    await client.connect();
    await client.db("admin").command({ ping: 1 });

    logger.info('[MONGODB] MongoDB connection initialized successfully');

    // Veritabanı referansını al
    db = client.db(dbName);

    // Koleksiyon referanslarını oluştur - temel koleksiyonlar
    // Diğer koleksiyonlar initializeCollections() fonksiyonu tarafından başlatılacak
    reportsCollection = db.collection('test_reports');
    scenariosCollection = db.collection('scenarios');
    usersCollection = db.collection('users');
    pluginsCollection = db.collection('plugins');
    scenarioFoldersCollection = db.collection('scenario_folders');
    feedbackCollection = db.collection('feedback');
    runsCollection = db.collection('runs');
    adminsCollection = db.collection('admins');
    companiesCollection = db.collection('companies');
    systemSettingsCollection = db.collection('system_settings');
    teamsCollection = db.collection('teams');
    dataSetsCollection = db.collection('data_sets');
    dataSourcesCollection = db.collection('data_sources');
    dataEnvironmentsCollection = db.collection('data_environments');
    schedulesCollection = db.collection('schedules');
    scheduleRunsCollection = db.collection('schedule_runs');
    testsCollection = db.collection('tests');
    runStatusCollection = db.collection('runStatus');
    rolesCollection = db.collection('roles');
    teamInvitesCollection = db.collection('teamInvites');
    testiniumAppsCollection = db.collection('testinium_apps');

    // Temel indeksleri oluştur
    await createIndexes();

    isInitialized = true;
    initError = null; // Hata durumunu sıfırla
  } catch (error: any) {
    logger.error('[MONGODB] MongoDB initialization error:', error);
    initError = error instanceof Error ? error : new Error(String(error));
    isInitialized = false;

    // Alternatif bağlantı denemesi
    try {
      logger.info('[MONGODB] Attempting alternative connection...');
      // Get connection parameters from config
      const { host, port, database, username, password, useSSL, authSource } = config.connections.mongodb;

      // Construct the alternative connection URI
      const auth = username && password ? `${username}:${encodeURIComponent(password)}@` : '';
      const authSourceParam = `authSource=${authSource}`;
      const sslOption = useSSL ? '&ssl=true' : '';
      const alternativeUri = `mongodb://${auth}${host}:${port}/${database}?${authSourceParam}${sslOption}`;

      logger.info(`[MONGODB] Trying alternative URI: ${host}:${port}/${database}`);

      // Alternatif bağlantı için de optimize edilmiş ayarları kullan
      client = new MongoClient(alternativeUri, {
        serverApi: {
          version: ServerApiVersion.v1,
          strict: false,
          deprecationErrors: true,
        },
        connectTimeoutMS: 30000, // 30 saniye bağlantı zaman aşımı
        socketTimeoutMS: 120000, // 120 saniye soket zaman aşımı (result processing için)
        maxPoolSize: 100, // Maksimum bağlantı havuzu boyutu
        minPoolSize: 10, // Minimum bağlantı havuzu boyutu
        maxIdleTimeMS: 300000, // 5 dakika maksimum boşta kalma süresi
        waitQueueTimeoutMS: 60000, // 60 saniye kuyruk bekleme süresi
        retryWrites: true, // Write retry'ları etkinleştir
        retryReads: true, // Read retry'ları etkinleştir
        maxConnecting: 10, // Maksimum eşzamanlı bağlantı sayısı
        heartbeatFrequencyMS: 10000, // 10 saniye heartbeat
        serverSelectionTimeoutMS: 30000, // 30 saniye server seçim timeout
      });
      await client.connect();
      db = client.db(dbName);

      // Koleksiyon referanslarını oluştur - temel koleksiyonlar
      // Diğer koleksiyonlar initializeCollections() fonksiyonu tarafından başlatılacak
      reportsCollection = db.collection('test_reports');
      scenariosCollection = db.collection('scenarios');
      usersCollection = db.collection('users');
      pluginsCollection = db.collection('plugins');
      scenarioFoldersCollection = db.collection('scenario_folders');
      feedbackCollection = db.collection('feedback');
      runsCollection = db.collection('runs');
      adminsCollection = db.collection('admins');
      companiesCollection = db.collection('companies');
      systemSettingsCollection = db.collection('system_settings');
      teamsCollection = db.collection('teams');
      dataSetsCollection = db.collection('data_sets');
      dataSourcesCollection = db.collection('data_sources');
      dataEnvironmentsCollection = db.collection('data_environments');
      schedulesCollection = db.collection('schedules');
      scheduleRunsCollection = db.collection('schedule_runs');
      testsCollection = db.collection('tests');
      runStatusCollection = db.collection('runStatus');
      testiniumAppsCollection = db.collection('testinium_apps');

      // Temel indeksleri oluştur
      await createIndexes();

      isInitialized = true;
      initError = null; // Hata durumunu sıfırla
      logger.info('[MONGODB] Alternative MongoDB connection initialized successfully');
    } catch (altError: any) {
      logger.error('[MONGODB] Alternative connection also failed:', altError);
      initError = altError instanceof Error ? altError : new Error(String(altError));
      isInitialized = false;
      client = null;
      db = null;
    }
  }
}

/**
 * Güvenli bir şekilde indeks oluşturan yardımcı fonksiyon
 * Eğer indeks zaten varsa, hata vermek yerine sessizce geçer
 */
async function createIndexSafely(collection: Collection, key: Record<string, number | string>, options: any = {}) {
  if (!collection) return;

  try {
    // Mevcut indeksleri kontrol et
    const indexName = options.name || Object.entries(key).map(([field, dir]) => `${field}_${dir}`).join('_');
    const indexes = await collection.listIndexes().toArray();

    // Bu alanlar için zaten bir indeks var mı kontrol et
    const keyFields = Object.keys(key);
    const existingIndex = indexes.find(idx => {
      if (!idx.key) return false;

      // Tüm alanlar ve yönleri eşleşiyor mu kontrol et
      return keyFields.every(field => {
        // TypeScript için tip güvenliği sağla
        const idxKey = idx.key as Record<string, number | string>;
        return idxKey[field] === key[field];
      });
    });

    if (existingIndex) {
      // İndeks zaten var, debug seviyesinde logla
      logger.debug(`[MONGODB] Index for ${JSON.stringify(key)} already exists on ${collection.collectionName} as ${existingIndex.name}`);
      return;
    }

    // İndeks yoksa oluştur
    await collection.createIndex(key as any, options);
    logger.debug(`[MONGODB] Created index ${indexName} on ${collection.collectionName}`);
  } catch (error: any) {
    // Eğer indeks zaten varsa ve isim çakışması varsa, sadece uyarı ver
    if (error.message && error.message.includes('already exists')) {
      logger.debug(`[MONGODB] Index for ${JSON.stringify(key)} already exists on ${collection.collectionName} with a different name`);
    } else {
      // Diğer hataları warn seviyesinde logla
      logger.warn(`[MONGODB] Failed to create index for ${JSON.stringify(key)} on ${collection.collectionName}: ${error.message}`);
    }
  }
}

/**
 * Eski problematic plugin index'lerini temizle
 */
async function cleanupLegacyPluginIndexes(collection: Collection) {
  try {
    logger.info('[MONGODB] Cleaning up legacy plugin indexes...');
    
    // Mevcut index'leri listele
    const existingIndexes = await collection.indexes();
    
    // Temizlenecek eski index'ler
    const legacyIndexesToDrop = [
      'userId_1_pluginName_1',  // Ana problematic index
      'pluginName_1_userId_1',  // Ters sıralı versiyonu
      'userId_pluginName',      // Underscore olmayan versiyon
      'plugins_userId_pluginName' // Farklı naming ile oluşturulmuş versiyon
    ];
    
    // Her legacy index'i kontrol et ve kaldır
    for (const indexName of legacyIndexesToDrop) {
      const foundIndex = existingIndexes.find(idx => idx.name === indexName);
      
      if (foundIndex) {
        try {
          await collection.dropIndex(indexName);
          logger.info(`[MONGODB] ✅ Dropped legacy index: ${indexName}`);
        } catch (error: any) {
          if (error.codeName === 'IndexNotFound') {
            logger.debug(`[MONGODB] Legacy index ${indexName} already removed`);
          } else {
            logger.warn(`[MONGODB] ⚠️  Failed to drop legacy index ${indexName}: ${error.message}`);
          }
        }
      }
    }
    
    // userId + pluginName kombinasyonuna sahip index'leri de kontrol et
    for (const index of existingIndexes) {
      if (index.key && index.key.userId && index.key.pluginName && index.name) {
        try {
          await collection.dropIndex(index.name);
          logger.info(`[MONGODB] ✅ Dropped legacy index with userId+pluginName combination: ${index.name}`);
        } catch (error: any) {
          logger.warn(`[MONGODB] ⚠️  Failed to drop index ${index.name}: ${error.message}`);
        }
      }
    }
    
    logger.info('[MONGODB] Legacy plugin indexes cleanup completed');
  } catch (error: any) {
    logger.error('[MONGODB] Error during legacy index cleanup:', error);
    // Index cleanup hatası uygulamanın çalışmasını engellememelidir
  }
}

/**
 * Koleksiyonlar için gerekli indexleri oluştur
 */
async function createIndexes() {
  try {
    if (!db) {
      return;
    }

    // Scenarios koleksiyonu için indexler
    if (scenariosCollection) {
      await createIndexSafely(scenariosCollection, { id: 1 }, { unique: true });
      await createIndexSafely(scenariosCollection, { createdAt: -1 });
      await createIndexSafely(scenariosCollection, { "lastRun.date": -1 });
      await createIndexSafely(scenariosCollection, { tags: 1 });
    }

    // Reports koleksiyonu için indexler
    if (reportsCollection) {
      await createIndexSafely(reportsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(reportsCollection, { scenarioId: 1 });
      await createIndexSafely(reportsCollection, { createdAt: -1 });
      await createIndexSafely(reportsCollection, { status: 1 });
    }

    // Users koleksiyonu için indexler
    if (usersCollection) {
      await createIndexSafely(usersCollection, { email: 1 }, { unique: true });
      await createIndexSafely(usersCollection, { id: 1 }, { unique: true });
      await createIndexSafely(usersCollection, { companyId: 1 }); // Şirket bazlı sorgular için
      await createIndexSafely(usersCollection, { teamId: 1 }); // Takım bazlı sorgular için
      await createIndexSafely(usersCollection, { accountType: 1 }); // Hesap türü bazlı sorgular için
      await createIndexSafely(usersCollection, { active: 1 }); // Aktif/pasif kullanıcı sorguları için
      // Text indeksi apiStrict modunda çalışmıyor, bu yüzden kaldırıldı
      // await createIndexSafely(usersCollection, { name: 'text', email: 'text' }); // Metin araması için
    }

    // Plugins koleksiyonu için indexler
    if (pluginsCollection) {
      // ÖNCE: Eski problematic index'leri temizle
      await cleanupLegacyPluginIndexes(pluginsCollection);
      
      // SONRA: Yeni sistem için indexler (teamId + companyId tabanlı)
      await createIndexSafely(pluginsCollection, { teamId: 1, companyId: 1 }, { unique: true });
      await createIndexSafely(pluginsCollection, { userId: 1 }); // Geriye dönük uyumluluk için
      await createIndexSafely(pluginsCollection, { "plugins.id": 1 }); // Plugin ID aramaları için
    }

    // Scenario Folders koleksiyonu için indexler
    if (scenarioFoldersCollection) {
      await createIndexSafely(scenarioFoldersCollection, { id: 1 }, { unique: true });
      await createIndexSafely(scenarioFoldersCollection, { userId: 1 });
    }

    // Feedback koleksiyonu için indexler
    if (feedbackCollection) {
      await createIndexSafely(feedbackCollection, { userId: 1 });
    }

    // Run Reports koleksiyonu için indexler
    if (runReportsCollection) {
      await createIndexSafely(runReportsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(runReportsCollection, { runId: 1 });
      await createIndexSafely(runReportsCollection, { executionId: 1 });
      await createIndexSafely(runReportsCollection, { createdAt: -1 });
    }

    // Companies koleksiyonu için indexler
    if (companiesCollection) {
      await createIndexSafely(companiesCollection, { id: 1 }, { unique: true });
      await createIndexSafely(companiesCollection, { name: 1 });
      await createIndexSafely(companiesCollection, { createdAt: -1 });
      await createIndexSafely(companiesCollection, { status: 1 });
    }

    // Admins koleksiyonu için indexler
    if (adminsCollection) {
      await createIndexSafely(adminsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(adminsCollection, { email: 1 }, { unique: true });
      await createIndexSafely(adminsCollection, { role: 1 });
      // Not: Super admin kontrolü artık initAdminCollection tarafından yapılıyor
    }

    // System Settings koleksiyonu için indexler
    if (systemSettingsCollection) {
      await createIndexSafely(systemSettingsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(systemSettingsCollection, { type: 1 });
      await createIndexSafely(systemSettingsCollection, { isActive: 1 });
    }

    // Teams koleksiyonu için indexler
    if (teamsCollection) {
      await createIndexSafely(teamsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(teamsCollection, { createdBy: 1 });
      await createIndexSafely(teamsCollection, { status: 1 });
      await createIndexSafely(teamsCollection, { companyId: 1 });
      await createIndexSafely(teamsCollection, { 'tags': 1 });
      await createIndexSafely(teamsCollection, { createdAt: -1 });
    }

    // role_permissions koleksiyonu için indexler (team membership + roles)
    const rolePermissionsCollection = db.collection('role_permissions');
    if (rolePermissionsCollection) {
      await createIndexSafely(rolePermissionsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(rolePermissionsCollection, { user_id: 1 });
      await createIndexSafely(rolePermissionsCollection, { team_id: 1 });
      await createIndexSafely(rolePermissionsCollection, { role_id: 1 });
      await createIndexSafely(rolePermissionsCollection, { 'user_id': 1, 'team_id': 1 });
      await createIndexSafely(rolePermissionsCollection, { status: 1 });
      await createIndexSafely(rolePermissionsCollection, { is_system: 1 });
    }

    // Schedules koleksiyonu için indexler
    if (schedulesCollection) {
      await createIndexSafely(schedulesCollection, { id: 1 }, { unique: true });
      await createIndexSafely(schedulesCollection, { userId: 1 });
      await createIndexSafely(schedulesCollection, { teamId: 1 });
      await createIndexSafely(schedulesCollection, { companyId: 1 });
      await createIndexSafely(schedulesCollection, { status: 1 });
      await createIndexSafely(schedulesCollection, { nextRunAt: 1 });
    }

    // Schedule Runs koleksiyonu için indexler
    if (scheduleRunsCollection) {
      await createIndexSafely(scheduleRunsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(scheduleRunsCollection, { scheduleId: 1 });
      await createIndexSafely(scheduleRunsCollection, { createdAt: -1 });
      await createIndexSafely(scheduleRunsCollection, { executionId: 1 });
    }

    // Data Sets koleksiyonu için indexler
    if (dataSetsCollection) {
      await createIndexSafely(dataSetsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(dataSetsCollection, { teamId: 1 });
      await createIndexSafely(dataSetsCollection, { companyId: 1 });
      await createIndexSafely(dataSetsCollection, { environment: 1 });
      await createIndexSafely(dataSetsCollection, { tags: 1 });
      await createIndexSafely(dataSetsCollection, { createdAt: -1 });
      await createIndexSafely(dataSetsCollection, { updatedAt: -1 });
    }

    // Data Sources koleksiyonu için indexler
    if (dataSourcesCollection) {
      await createIndexSafely(dataSourcesCollection, { id: 1 }, { unique: true });
      await createIndexSafely(dataSourcesCollection, { teamId: 1 });
      await createIndexSafely(dataSourcesCollection, { companyId: 1 });
      await createIndexSafely(dataSourcesCollection, { type: 1 });
      await createIndexSafely(dataSourcesCollection, { isActive: 1 });
      await createIndexSafely(dataSourcesCollection, { createdAt: -1 });
      await createIndexSafely(dataSourcesCollection, { updatedAt: -1 });
    }

    // Data Environments koleksiyonu için indexler
    if (dataEnvironmentsCollection) {
      await createIndexSafely(dataEnvironmentsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(dataEnvironmentsCollection, { teamId: 1 });
      await createIndexSafely(dataEnvironmentsCollection, { companyId: 1 });
      await createIndexSafely(dataEnvironmentsCollection, { type: 1 });
      await createIndexSafely(dataEnvironmentsCollection, { isActive: 1 });
      await createIndexSafely(dataEnvironmentsCollection, { createdAt: -1 });
      await createIndexSafely(dataEnvironmentsCollection, { updatedAt: -1 });
    }

    // Tests koleksiyonu için indexler
    if (testsCollection) {
      await createIndexSafely(testsCollection, { id: 1 }, { unique: true });
      await createIndexSafely(testsCollection, { runId: 1 });
      await createIndexSafely(testsCollection, { scenarioId: 1 });
      await createIndexSafely(testsCollection, { status: 1 });
      await createIndexSafely(testsCollection, { nodeId: 1 });
      await createIndexSafely(testsCollection, { userId: 1 });
      await createIndexSafely(testsCollection, { queuedAt: -1 });
    }

    // runStatus koleksiyonu için indexler
    if (runStatusCollection) {
      await createIndexSafely(runStatusCollection, { runId: 1 }, { unique: true });
    }

    logger.info('[MONGODB] Database indexes created successfully');
  } catch (error) {
    logger.error('[MONGODB] Error creating indexes:', error);
  }
}

// Uygulama başladığında MongoDB'yi başlat
// Sadece bir kez çalıştırılacak şekilde ayarla
let initializationStarted = false;
(async () => {
  if (!initializationStarted) {
    initializationStarted = true;
    await initialize();
  }
})();

/**
 * Admin koleksiyonunu başlat ve Super Admin varlığını kontrol et
 */
export async function initAdminCollection(): Promise<boolean> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    if (!db) {
      throw new Error('Database connection not established');
    }

    // Admin koleksiyonunu al
    adminsCollection = db.collection('admins');

    // Gerekli indeksleri güvenli bir şekilde oluştur
    await createIndexSafely(adminsCollection, { id: 1 }, { unique: true });
    await createIndexSafely(adminsCollection, { email: 1 }, { unique: true });
    await createIndexSafely(adminsCollection, { role: 1 });

    // Super admin kontrolü
    await ensureSuperAdmin();

    logger.info('[ADMIN] Admin collection initialized');
    return true;
  } catch (error) {
    logger.error('[ADMIN] Error initializing admin collection:', error);
    return false;
  }
}

/**
 * MongoDB bağlantı durumunu kontrol et
 */
export function isMongoDBInitialized(): boolean {
  return isInitialized;
}

/**
 * MongoDB başlatma hatasını döndür
 */
export function getMongoDBInitError(): Error | null {
  return initError;
}

/**
 * MongoDB bağlantısının açık olduğundan emin ol
 */
export async function ensureMongoDBConnection() {
  // Zaten başlatılmışsa tekrar başlatma
  if (isInitialized && client && db) {
    return;
  }

  // Eğer başlatma işlemi zaten başladıysa, başlatma işleminin tamamlanmasını bekle
  if (initializationStarted) {
    // Bağlantının kurulmasını bekle (en fazla 5 saniye)
    let attempts = 0;
    const maxAttempts = 50; // 5 saniye (her 100ms'de bir kontrol)

    while (!isInitialized && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }

    if (isInitialized && client && db) {
      return;
    }
  }

  // Bağlantıyı başlat
  initializationStarted = true;
  await initialize();

  // Başlatma başarısız olduysa hata fırlat
  if (!isInitialized || !client || !db) {
    logger.error('[MONGODB] Failed to ensure MongoDB connection');
    throw new Error('MongoDB is not initialized');
  }

  // Bağlantı durumunu kontrol et
  try {
    // Ping ile bağlantıyı test et (timeout ile)
    await Promise.race([
      client.db("admin").command({ ping: 1 }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Ping timeout')), 5000)
      )
    ]);
    logger.debug('[MONGODB] MongoDB connection is healthy');
  } catch (error) {
    logger.error('[MONGODB] MongoDB connection is not healthy, attempting to reconnect', error);

    // Bağlantıyı kapat ve yeniden başlat
    try {
      if (client) {
        await client.close();
      }
      isInitialized = false;
      initializationStarted = false; // Reset initialization flag
      await initialize();

      if (!isInitialized || !client || !db) {
        throw new Error('Failed to reconnect to MongoDB');
      }
    } catch (reconnectError) {
      logger.error('[MONGODB] Failed to reconnect to MongoDB', reconnectError);
      throw new Error('Failed to reconnect to MongoDB');
    }
  }
}

/**
 * MongoDB'de timestamp oluştur
 */
export function mongoTimestamp() {
  return new Date();
}

/**
 * ISO string formatındaki tarihi MongoDate'e dönüştür
 */
export function isoStringToMongoDate(dateString: string): Date {
  if (!dateString) {
    return new Date();
  }

  try {
    return new Date(dateString);
  } catch (error) {
    logger.error('[MONGODB] Error converting ISO string to MongoDate:', error);
    return new Date();
  }
}

/**
 * MongoDB kapanırken bağlantıyı kapat
 */
export async function closeMongoDBConnection() {
  if (client) {
    try {
      await client.close();
      logger.info('[MONGODB] MongoDB connection closed');
      isInitialized = false;
    } catch (error) {
      logger.error('[MONGODB] Error closing MongoDB connection:', error);
    }
  }
}

// Process sonlandığında bağlantıyı düzgün şekilde kapat
process.on('SIGINT', async () => {
  await closeMongoDBConnection();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closeMongoDBConnection();
  process.exit(0);
});

/**
 * Super admin kullanıcısının varlığını kontrol et, yoksa oluştur
 */
async function ensureSuperAdmin() {
  try {
    if (!adminsCollection) {
      throw new Error('Admin collection not initialized');
    }

    // Super admin var mı kontrol et
    const superAdmin = await adminsCollection.findOne({ role: AdminRole.SUPER_ADMIN });

    if (!superAdmin) {
      // Default super admin bilgileri
      const defaultEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
      const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';

      logger.info(`[ADMIN] Creating super admin with email: ${defaultEmail}`);
      logger.info(`[ADMIN] Default password: ${defaultPassword}`);

      // Şifreyi hashle
      const bcrypt = await import('bcryptjs');
      const salt = await bcrypt.default.genSalt(10);
      const hashedPassword = await bcrypt.default.hash(defaultPassword, salt);

      logger.info(`[ADMIN] Password hashed successfully`);

      // Super admin oluştur
      const adminId = uuidv4();
      const now = new Date();

      const adminDoc = {
        id: adminId,
        email: defaultEmail,
        password: hashedPassword,
        name: 'Super Admin',
        role: AdminRole.SUPER_ADMIN,
        accountType: 'admin', // Her zaman admin olarak ayarla
        permissions: ['*'],
        createdAt: now,
        active: true,
        updatedAt: now
      };

      logger.info(`[ADMIN] Inserting admin document:`, { ...adminDoc, password: '[HIDDEN]' });

      await adminsCollection.insertOne(adminDoc);

      logger.info(`[ADMIN] Default super admin created with email: ${defaultEmail}`);
      logger.warn('[ADMIN] Please change the default admin password immediately!');

      // Doğrulama: Oluşturulan admin'i tekrar bul
      const createdAdmin = await adminsCollection.findOne({ email: defaultEmail });
      if (createdAdmin) {
        logger.info(`[ADMIN] Verification: Admin created successfully with ID: ${createdAdmin.id}`);
      } else {
        logger.error(`[ADMIN] Verification failed: Admin not found after creation`);
      }
    } else {
      logger.info(`[ADMIN] Super admin already exists with email: ${superAdmin.email}`);
    }
  } catch (error) {
    logger.error('[ADMIN] Error ensuring super admin:', error);
    throw error; // Hatayı yukarı ilet
  }
}

/**
 * Run Reports koleksiyonunu getir
 */
export async function getRunReportsCollection(): Promise<Collection> {
  if (!runReportsCollection) {
    // MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();

    if (!db) {
      throw new Error('MongoDB connection not available');
    }

    runReportsCollection = db.collection('run_reports');

    // Gerekli indeksleri güvenli bir şekilde oluştur
    await createIndexSafely(runReportsCollection, { id: 1 }, { unique: true });
    await createIndexSafely(runReportsCollection, { runId: 1 });
    await createIndexSafely(runReportsCollection, { executionId: 1 });
    await createIndexSafely(runReportsCollection, { status: 1 });
    await createIndexSafely(runReportsCollection, { createdAt: -1 });
    await createIndexSafely(runReportsCollection, { 'scenarioStatuses.scenarioId': 1 });
  }
  return runReportsCollection;
}

/**
 * Tüm koleksiyonları başlat
 */
export async function initializeCollections(): Promise<boolean> {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();

    if (!db) {
      throw new Error('Database connection not established');
    }

    // Koleksiyonları başlat
    const collections = [
      // Admin koleksiyonu ve Super Admin kontrolü
      initAdminCollection(),

      // Run Reports koleksiyonu
      getRunReportsCollection(),

      // Diğer koleksiyonlar için başlatma fonksiyonları buraya eklenebilir
    ];

    // Tüm koleksiyon başlatma işlemlerini paralel olarak çalıştır
    await Promise.all(collections);

    logger.info('[MONGODB] All collections initialized successfully');
    return true;
  } catch (error) {
    logger.error('[MONGODB] Error initializing collections:', error);
    return false;
  }
}

// Modül dışarıya açılan referanslar ve fonksiyonlar
export {
  client,
  db,
  reportsCollection,
  scenariosCollection,
  usersCollection,
  pluginsCollection,
  scenarioFoldersCollection,
  feedbackCollection,
  runsCollection,
  runReportsCollection,
  companiesCollection,
  adminsCollection,
  systemSettingsCollection,
  teamsCollection,
  schedulesCollection,
  scheduleRunsCollection,
  dataSetsCollection,
  dataSourcesCollection,
  dataEnvironmentsCollection,
  testsCollection,
  runStatusCollection,
  rolesCollection,
  teamInvitesCollection,
  testiniumAppsCollection
};