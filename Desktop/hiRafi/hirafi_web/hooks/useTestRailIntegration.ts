import { useState, useEffect, useMemo } from "react";
import { pluginApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { useTestManagementStore } from "@/store/testManagementStore";

export interface TestRailConfig {
  success: boolean;
  config?: {
    url: string;
    username: string;
    apiKey: string;
    projectsData: any[];
    suitesData: any[];
    updatedAt: string;
  };
  plugin?: {
    id: string;
    name: string;
    description: string;
    active: boolean;
    createdAt: string;
    updatedAt: string;
  };
  error?: string | null;
}

export interface TestRailCase {
  id: string;
  title: string;
  suite_id: number;
  project_id: number;
  priority_id?: number;
  [key: string]: any;
}

interface UseTestRailIntegrationOptions {
  autoInitialize?: boolean;
}

export function useTestRailIntegration(
  options: UseTestRailIntegrationOptions = {},
) {
  const { toast } = useToast();
  const { autoInitialize = true } = options;
  const setProviderConfig = useTestManagementStore(state => state.setProviderConfig);

  // Core state
  const [isConnected, setIsConnected] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [testRailConfig, setTestRailConfig] = useState<TestRailConfig | null>(
    null,
  );
  const [testCases, setTestCases] = useState<TestRailCase[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [suites, setSuites] = useState<any[]>([]);

  // Selection state
  const [selectedProject, setSelectedProject] = useState<string>("all");
  const [selectedSuite, setSelectedSuite] = useState<string>("all");
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [selectedCaseDetails, setSelectedCaseDetails] = useState<any>(null);

  // TRUTH OF SOURCE: Single loading state management
  const [loadingState, setLoadingState] = useState({
    initial: true, // Initial config loading
    suites: false, // Suite filtering/loading
    cases: false, // Test cases loading
    caseDetails: false, // Individual case details loading
  });

  // UI state
  const [searchTerm, setSearchTerm] = useState("");

  // Pagination state for chunked loading
  const [allTestCases, setAllTestCases] = useState<TestRailCase[]>([]);
  const [displayedCases, setDisplayedCases] = useState<TestRailCase[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentChunk, setCurrentChunk] = useState(0);
  const CHUNK_SIZE = 25;

  // Derived loading states for backward compatibility
  const isLoading = loadingState.initial;
  const isLoadingSuites = loadingState.suites;
  const isLoadingCases = loadingState.cases;

  // Initialize TestRail configuration on mount (only if autoInitialize is true)
  useEffect(() => {
    if (autoInitialize) {
      initialize();
    }
  }, [autoInitialize]);

  // Helper to update loading state
  const updateLoadingState = (updates: Partial<typeof loadingState>) => {
    setLoadingState((prev) => ({ ...prev, ...updates }));
  };

  // Initialize method for lazy loading - now uses context data
  const initialize = async (configData?: any) => {
    if (isInitialized) return;

    try {
      // If config data is provided (from context), use it directly
      if (configData) {
        console.log('[TestRail] Initializing with provided config:', configData);
        setTestRailConfig({
          success: true,
          config: configData.config,
          plugin: configData.plugin,
        });
        setIsConnected(configData.isConnected || false);

        // Set up available projects
        if (configData.config?.projectsData && Array.isArray(configData.config.projectsData)) {
          setProjects(configData.config.projectsData);
        }

        setIsInitialized(true);
        updateLoadingState({ initial: false });
        return;
      }

      // Fallback to API call if no config provided
      await fetchTestRailConfig();
      setIsInitialized(true);
    } catch (error) {
      console.error("Failed to initialize TestRail integration:", error);
    }
  };

  // Sync isConnected state with store
  useEffect(() => {
    if (isInitialized) {
      setProviderConfig('testrail', {
        isConnected,
        isInitialized: true,
        config: testRailConfig?.config,
        plugin: testRailConfig?.plugin,
      });
      console.log(`[TestRail Hook] Syncing isConnected to store: ${isConnected}`);
    }
  }, [isConnected, isInitialized, testRailConfig, setProviderConfig]);

  // Fetch TestRail configuration using unified endpoint
  const fetchTestRailConfig = async () => {
    try {
      updateLoadingState({ initial: true });

      // Use unified endpoint to check if TestRail is available
      const unifiedResponse = await pluginApi.getAvailableTestManagementIntegrations();

      if (unifiedResponse.success && unifiedResponse.data) {
        const testRailIntegration = unifiedResponse.data.integrations.find(
          (integration: any) => integration.provider === "testrail",
        );

        if (testRailIntegration && testRailIntegration.isConnected) {
          // Fix project_id in suites if needed
          let configData = testRailIntegration.config;
          if (
            configData.suitesData &&
            configData.projectsData &&
            configData.projectsData.length === 1
          ) {
            const projectId = parseInt(configData.projectsData[0].id);
            configData.suitesData = configData.suitesData.map((suite: any) => ({
              ...suite,
              project_id: suite.project_id || projectId,
            }));
          }

          setTestRailConfig({
            success: true,
            config: configData,
            plugin: testRailIntegration.plugin,
          });
          setIsConnected(true);

          // Set up available projects
          if (
            configData.projectsData &&
            Array.isArray(configData.projectsData)
          ) {
            setProjects(configData.projectsData);
          }
        } else {
          // TestRail not configured - this is normal, don't show error
          setIsConnected(false);
          setTestRailConfig({
            success: false,
            error: null, // No error message for missing configuration
          });
        }
      } else {
        // No integrations available - this is normal
        setIsConnected(false);
        setTestRailConfig({
          success: false,
          error: null, // No error message for missing configuration
        });
      }
    } catch (error: any) {
      // Silently handle errors - user might not want TestRail configured
      console.debug("TestRail configuration check failed:", error.message);
      setIsConnected(false);
      setTestRailConfig({
        success: false,
        error: null, // No error message for missing configuration
      });
    } finally {
      updateLoadingState({ initial: false });
    }
  };

  // Fetch test cases for a specific suite with chunked loading
  const fetchTestCasesForSuite = async (projectId: string, suiteId: string) => {
    try {
      updateLoadingState({ cases: true });

      const response = await pluginApi.getTestRailCasesForSuite(
        projectId,
        suiteId,
      );

      // Extract cases from response
      const cases = response.data?.cases || [];

      if (response.success && Array.isArray(cases) && cases.length > 0) {
        // Store all cases
        setAllTestCases(cases);

        // Show first chunk
        const firstChunk = cases.slice(0, CHUNK_SIZE);
        setDisplayedCases(firstChunk);
        setTestCases(firstChunk);
        setCurrentChunk(0);

        toast({
          title: "Test Cases Loaded",
          description: `Showing first ${firstChunk.length} of ${cases.length} test cases`,
          variant: "default",
        });
      } else {
        setAllTestCases([]);
        setDisplayedCases([]);
        setTestCases([]);
        toast({
          title: "No Test Cases Found",
          description: "No test cases were found in the selected suite",
          variant: "default",
        });
      }
    } catch (error: any) {
      setAllTestCases([]);
      setDisplayedCases([]);
      setTestCases([]);
      toast({
        title: "Error",
        description: error.message || "Could not fetch test cases",
        variant: "destructive",
      });
    } finally {
      updateLoadingState({ cases: false });
    }
  };

  // Load more test cases (next chunk)
  const loadMoreCases = async () => {
    if (isLoadingMore || allTestCases.length === 0) return;

    try {
      setIsLoadingMore(true);

      const nextChunk = currentChunk + 1;
      const startIndex = nextChunk * CHUNK_SIZE;
      const endIndex = startIndex + CHUNK_SIZE;

      if (startIndex < allTestCases.length) {
        const newChunk = allTestCases.slice(startIndex, endIndex);
        const updatedDisplayed = [...displayedCases, ...newChunk];

        setDisplayedCases(updatedDisplayed);
        setTestCases(updatedDisplayed);
        setCurrentChunk(nextChunk);

        toast({
          title: "More Cases Loaded",
          description: `Showing ${updatedDisplayed.length} of ${allTestCases.length} test cases`,
          variant: "default",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to load more test cases",
        variant: "destructive",
      });
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Check if there are more cases to load
  const hasMoreCases = () => {
    return displayedCases.length < allTestCases.length;
  };

  // Handle project change
  const handleProjectChange = async (projectId: string) => {
    setSelectedProject(projectId);
    setSelectedSuite("all");
    setTestCases([]);
    setAllTestCases([]);
    setDisplayedCases([]);

    if (!projectId || projectId === "all") {
      // Show all suites when "all" is selected
      const allSuites = testRailConfig?.config?.suitesData || [];
      setSuites(allSuites);
      return;
    }

    try {
      updateLoadingState({ suites: true });

      // Filter suites from configuration data
      const availableSuites = testRailConfig?.config?.suitesData || [];
      let projectSuites = availableSuites.filter(
        (suite: any) => suite.project_id === parseInt(projectId),
      );

      setSuites(projectSuites);

      if (projectSuites.length > 0) {
        toast({
          title: "Suites Available",
          description: `Found ${projectSuites.length} test suites for the selected project`,
          variant: "default",
        });
      } else {
        toast({
          title: "No Test Suites Found",
          description: "No test suites were found for the selected project",
          variant: "default",
        });
      }
    } catch (error) {
      setSuites([]);
      toast({
        title: "Error",
        description: "An error occurred while loading test suites",
        variant: "destructive",
      });
    } finally {
      updateLoadingState({ suites: false });
    }
  };

  // Handle suite change - FIXED: Now properly handles loading states
  const handleSuiteChange = async (suiteId: string) => {
    setSelectedSuite(suiteId);

    // Clear existing cases when changing suite
    setTestCases([]);
    setAllTestCases([]);
    setDisplayedCases([]);

    if (
      !suiteId ||
      suiteId === "all" ||
      !selectedProject ||
      selectedProject === "all"
    ) {
      // If "all" is selected or no specific project/suite, don't load cases
      return;
    }

    // Load cases for the specific suite
    await fetchTestCasesForSuite(selectedProject, suiteId);
  };

  // Handle test case selection
  const handleTestCaseSelection = async (caseId: string) => {
    const isDeselecting = selectedCases.includes(caseId);
    let updatedCases;

    if (isDeselecting) {
      updatedCases = selectedCases.filter((id) => id !== caseId);
      setSelectedCaseDetails(null);
    } else {
      // Only allow one case at a time for now
      updatedCases = [caseId];

      // Note: Case details are not automatically fetched here to prevent unnecessary API calls
      // Users can fetch details manually if needed
      setSelectedCaseDetails(null);
    }

    setSelectedCases(updatedCases);
    return updatedCases;
  };

  // Manual setter for selected cases (for external updates)
  const updateSelectedCases = (cases: string[]) => {
    setSelectedCases(cases);
  };

  // Get available suites for the selected project
  const getAvailableSuites = () => {
    if (!testRailConfig?.config?.suitesData) return [];

    if (!selectedProject || selectedProject === "all")
      return testRailConfig.config.suitesData;

    const projectId = parseInt(selectedProject);
    return testRailConfig.config.suitesData.filter(
      (suite: any) => suite.project_id === projectId,
    );
  };

  // Filtered test cases (only filter displayed cases for performance)
  const filteredTestCases = useMemo(() => {
    if (displayedCases.length === 0) return [];

    let filtered = [...displayedCases];

    // Project filtering
    if (selectedProject && selectedProject !== "all") {
      const projectId = parseInt(selectedProject);
      filtered = filtered.filter(
        (testCase) => testCase.project_id === projectId,
      );
    }

    // Suite filtering
    if (selectedSuite && selectedSuite !== "all") {
      const suiteId = parseInt(selectedSuite);
      filtered = filtered.filter((testCase) => testCase.suite_id === suiteId);
    }

    // Search filtering
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(
        (testCase) =>
          testCase.id.toString().startsWith(search) ||
          testCase.title.toLowerCase().includes(search),
      );
    }

    return filtered;
  }, [displayedCases, searchTerm, selectedProject, selectedSuite]);

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("");
    setSelectedProject("all");
    setSelectedSuite("all");
    setTestCases([]);
    setAllTestCases([]);
    setDisplayedCases([]);
  };

  return {
    // State
    isConnected,
    isInitialized,
    isLoading,
    isLoadingSuites,
    isLoadingCases,
    testRailConfig,
    testCases,
    projects,
    suites,
    filteredTestCases,

    // Chunked loading state
    allTestCases,
    displayedCases,
    isLoadingMore,
    CHUNK_SIZE,

    // Selection state
    selectedProject,
    selectedSuite,
    selectedCases,
    selectedCaseDetails,
    searchTerm,

    // Actions
    initialize,
    fetchTestRailConfig,
    fetchTestCasesForSuite,
    loadMoreCases,
    handleProjectChange,
    handleSuiteChange,
    handleTestCaseSelection,
    getAvailableSuites,
    resetFilters,

    // Setters
    setSearchTerm,
    setSelectedCases,
    updateSelectedCases,

    // Computed properties
    hasMoreCases,

    // Unified interface compatibility (not applicable for TestRail but needed for unified interface)
    isLoadingFolders: false,
    selectedFolder: "all",
    handleFolderChange: () => {},
    getAvailableFolders: () => [],
  };
}
