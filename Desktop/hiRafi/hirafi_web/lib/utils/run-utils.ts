/**
 * Utility functions for run and report data
 */

import { formatReportDuration } from './duration-utils';

// Type definitions for test results
export interface TestResultCounts {
  total: number;
  completed: number;
  failed: number;
  running: number;
  queued: number;
  stopped: number;
}

/**
 * Normalize status values to handle different status formats
 * @param status Raw status string
 * @returns Normalized status
 */
function normalizeStatus(status: string): string {
  if (!status) return 'unknown';

  const statusLower = status.toLowerCase();

  // Handle completed/passed status variations
  if (statusLower === 'completed' || statusLower === 'passed' || statusLower === 'success') {
    return 'passed';
  }

  // Handle other status variations
  if (statusLower === 'failed' || statusLower === 'failure' || statusLower === 'error') {
    return 'failed';
  }

  if (statusLower === 'running' || statusLower === 'in_progress') {
    return 'running';
  }

  if (statusLower === 'queued' || statusLower === 'pending') {
    return 'queued';
  }

  if (statusLower === 'stopped' || statusLower === 'cancelled') {
    return 'stopped';
  }

  return statusLower;
}

/**
 * Calculate test result statistics from scenarioStatuses array
 * Multiple device aware: counts actual tests, not just scenarios
 * @param run Run or RunReport object containing scenarioStatuses
 * @returns Object with counts for different statuses
 */
export function calculateTestResults(run: any): TestResultCounts {
  const scenarioStatuses = run.scenarioStatuses || [];
  
  // For multiple device runs, total should be the actual number of tests (scenarioStatuses.length)
  // For single device/scenario runs, use the scenario count as fallback
  let total = scenarioStatuses.length;
  
  // If no scenarioStatuses yet, estimate total based on run configuration
  if (total === 0 && run.scenarioIds?.length) {
    // Check if this is a multiple device Android run
    const isAndroid = run.platform === 'android';
    if (isAndroid && run.environment && run.environment.sauceLabs) {
      const selectedDevices = run.environment.sauceLabs.selectedDevices || [];
      const testDistributionStrategy = run.environment.testDistribution?.strategy || 'all-on-all';
      
      if (selectedDevices.length > 1) {
        if (testDistributionStrategy === 'all-on-all') {
          // Each scenario runs on each device
          total = run.scenarioIds.length * selectedDevices.length;
        } else if (testDistributionStrategy === 'distribute') {
          // Scenarios are distributed across devices (each scenario runs on one device)
          total = run.scenarioIds.length;
        }
      } else {
        // Single device or no devices selected
        total = run.scenarioIds.length;
      }
    } else {
      // Web platform or single device
      total = run.scenarioIds.length;
    }
  }

  // Initialize counts
  const counts: TestResultCounts = {
    total,
    completed: 0,
    failed: 0,
    running: 0,
    queued: 0,
    stopped: 0
  };

  // Count individual tests by status (each scenarioStatus represents one test)
  scenarioStatuses.forEach((scenario: any) => {
    const normalizedStatus = normalizeStatus(scenario.status);

    switch (normalizedStatus) {
      case 'queued':
        counts.queued++;
        break;
      case 'running':
        counts.running++;
        break;
      case 'passed':
        counts.completed++;
        break;
      case 'failed':
        counts.failed++;
        break;
      case 'stopped':
        counts.stopped++;
        break;
    }
  });

  return counts;
}

/**
 * Calculate test result statistics from scenario reports array
 * Multiple device aware: counts actual test reports
 * @param reports Array of scenario reports
 * @param options Configuration options
 * @returns Object with counts for different statuses
 */
export function calculateTestResultsFromReports(
  reports: any[],
  options: {
    isDistributeStrategy?: boolean;
    scenarioIds?: string[];
    platform?: string;
    environment?: any;
  } = {}
): TestResultCounts {
  if (!reports || !Array.isArray(reports)) {
    // If no reports yet but we know the run configuration, estimate total
    if (options.scenarioIds?.length && options.platform === 'android' && options.environment?.sauceLabs) {
      const selectedDevices = options.environment.sauceLabs.selectedDevices || [];
      const testDistributionStrategy = options.environment.testDistribution?.strategy || 'all-on-all';
      
      let estimatedTotal = options.scenarioIds.length;
      if (selectedDevices.length > 1 && testDistributionStrategy === 'all-on-all') {
        estimatedTotal = options.scenarioIds.length * selectedDevices.length;
      }
      
      return {
        total: estimatedTotal,
        completed: 0,
        failed: 0,
        running: 0,
        queued: estimatedTotal, // All tests start as queued
        stopped: 0
      };
    }
    
    return {
      total: 0,
      completed: 0,
      failed: 0,
      running: 0,
      queued: 0,
      stopped: 0
    };
  }

  // For reports, total is always the number of actual test reports
  // Each report represents one test execution (scenario + device combination)
  const total = reports.length;

  // Initialize counts
  const counts: TestResultCounts = {
    total,
    completed: 0,
    failed: 0,
    running: 0,
    queued: 0,
    stopped: 0
  };

  // Count test reports by status (each report represents one test)
  reports.forEach((report: any) => {
    const normalizedStatus = normalizeStatus(report.status);

    switch (normalizedStatus) {
      case 'queued':
        counts.queued++;
        break;
      case 'running':
        counts.running++;
        break;
      case 'passed':
        counts.completed++;
        break;
      case 'failed':
        counts.failed++;
        break;
      case 'stopped':
        counts.stopped++;
        break;
    }
  });

  return counts;
}

/**
 * Get the best available test results, preferring existing testResults over calculated ones
 * Multiple device aware: uses enhanced calculation functions
 * @param run Run object that may contain testResults and/or scenarioStatuses
 * @param reports Optional scenario reports array
 * @param options Configuration options
 * @returns Test result counts
 */
export function getBestTestResults(
  run: any,
  reports?: any[],
  options: {
    isDistributeStrategy?: boolean;
  } = {}
): TestResultCounts {
  // If we have reports, calculate from reports (most accurate)
  if (reports && reports.length > 0) {
    return calculateTestResultsFromReports(reports, {
      isDistributeStrategy: options.isDistributeStrategy,
      scenarioIds: run.scenarioIds,
      platform: run.platform,
      environment: run.environment
    });
  }

  // If we have scenarioStatuses, calculate from them (second most accurate)
  if (run.scenarioStatuses && run.scenarioStatuses.length > 0) {
    return calculateTestResults(run);
  }

  // If run already has testResults and they seem accurate, use them
  if (run.testResults && typeof run.testResults === 'object') {
    // Check if testResults total makes sense for multiple device runs
    const expectedTotal = getExpectedTestTotal(run);
    
    if (expectedTotal > 0 && run.testResults.total === expectedTotal) {
      return {
        total: run.testResults.total || 0,
        completed: run.testResults.completed || 0,
        failed: run.testResults.failed || 0,
        running: run.testResults.running || 0,
        queued: run.testResults.queued || 0,
        stopped: run.testResults.stopped || 0
      };
    }
  }

  // Fallback: calculate expected total and provide empty results
  const expectedTotal = getExpectedTestTotal(run);
  return {
    total: expectedTotal,
    completed: 0,
    failed: 0,
    running: 0,
    queued: expectedTotal, // Assume all tests start as queued
    stopped: 0
  };
}

/**
 * Get expected total number of tests for a run based on its configuration
 * @param run Run object
 * @returns Expected total test count
 */
function getExpectedTestTotal(run: any): number {
  if (!run.scenarioIds?.length) return 0;
  
  // Check if this is a multiple device Android run
  const isAndroid = run.platform === 'android';
  if (isAndroid && run.environment && run.environment.sauceLabs) {
    const selectedDevices = run.environment.sauceLabs.selectedDevices || [];
    const testDistributionStrategy = run.environment.testDistribution?.strategy || 'all-on-all';
    
    if (selectedDevices.length > 1) {
      if (testDistributionStrategy === 'all-on-all') {
        // Each scenario runs on each device
        return run.scenarioIds.length * selectedDevices.length;
      } else if (testDistributionStrategy === 'distribute') {
        // Scenarios are distributed across devices (each scenario runs on one device)
        return run.scenarioIds.length;
      }
    }
  }
  
  // Web platform or single device
  return run.scenarioIds.length;
}

/**
 * Calculate pass rate from test results
 * @param testResults Test result counts
 * @returns Pass rate as percentage (0-100)
 */
export function calculatePassRate(testResults: TestResultCounts): number {
  const { total, completed } = testResults;
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
}

/**
 * Determine run status based on test results
 * @param run Run object
 * @param testResults Test result counts
 * @returns Determined status
 */
export function determineRunStatus(run: any, testResults: TestResultCounts): string {
  // Get current status for analysis
  const currentStatus = (run.reportStatus || run.status)?.toLowerCase();
  
  // If the run is still running, return that status
  if (currentStatus === "running") {
    return "running";
  }

  // If there are no test results or scenario statuses, return the current status
  if (!testResults && !run.scenarioStatuses?.length) {
    return currentStatus || 'unknown';
  }

  const { total, completed, failed, stopped } = testResults;

  // If all scenarios are stopped, the run status should be stopped
  if (total > 0 && stopped === total) {
    return "stopped";
  }

  // If all tests are completed and none failed, it's a success
  if (total > 0 && completed === total && failed === 0) {
    return "success";
  }

  // If all tests are completed and all failed, it's a failure
  if (total > 0 && failed === total) {
    return "failed";
  }

  // If some tests passed and some failed, it's partial
  if (total > 0 && completed > 0 && failed > 0) {
    return "partial";
  }

  // If some tests are stopped and some are completed/failed, it's partial
  if (total > 0 && stopped > 0 && (completed > 0 || failed > 0)) {
    return "partial";
  }

  // Default to the current status
  const finalStatus = currentStatus || 'completed';
  return finalStatus;
}

/**
 * Check if a report/test is successful based on status
 * @param item Report or test object with status
 * @returns True if successful
 */
export function isSuccessful(item: any): boolean {
  if (!item) return false;

  // Check explicit success field first
  if (typeof item.success === 'boolean') {
    return item.success;
  }

  // Check status
  const normalizedStatus = normalizeStatus(item.status || '');
  return normalizedStatus === 'passed';
}

/**
 * Generate scenario statuses from scenario reports
 * @param reports Array of scenario reports
 * @returns Array of scenario statuses
 */
export function generateScenarioStatuses(reports: any[]): any[] {
  if (!reports || !Array.isArray(reports)) {
    return [];
  }

  return reports.map(report => ({
    scenarioId: report.scenarioId,
    status: normalizeStatus(report.status || ''),
    startedAt: report.startTime || report.startedAt,
    completedAt: report.createdAt || report.completedAt
  }));
}

/**
 * Format a date string to a human-readable format
 * @param dateString ISO date string
 * @returns Formatted date string
 */
export function formatDate(dateString?: string): string {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Calculate the duration between two dates in seconds
 * @param startDate Start date string
 * @param endDate End date string
 * @returns Duration in seconds or undefined if dates are invalid
 */
export function calculateDuration(startDate?: string, endDate?: string): number | undefined {
  if (!startDate || !endDate) return undefined;

  try {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();

    if (isNaN(start) || isNaN(end)) return undefined;

    return Math.round((end - start) / 1000);
  } catch (error) {
    return undefined;
  }
}

/**
 * Format duration in seconds to a human-readable format
 * @param seconds Duration in seconds
 * @returns Formatted duration string
 * @deprecated Use formatReportDuration from duration-utils.ts instead
 */
export function formatDuration(seconds?: number): string {
  if (seconds === undefined || isNaN(seconds)) return 'N/A';

  // Convert seconds to milliseconds for the centralized utility
  const durationMs = seconds * 1000;
  return formatReportDuration(durationMs);
}

/**
 * Get formatted duration for any report/test object with smart handling
 * @param report Report or test object
 * @param options Configuration options
 * @returns Formatted duration string
 */
export function getFormattedDuration(report: any, options: {
  isRunning?: boolean;
  startTimeField?: string;
} = {}): string {
  const { isRunning = false, startTimeField = 'startTime' } = options;

  // For running tests, calculate real-time duration
  if ((isRunning || report.status === 'running') && report[startTimeField]) {
    try {
      const startTime = new Date(report[startTimeField]).getTime();
      const currentTime = Date.now();
      const durationMs = currentTime - startTime;
      return formatReportDuration(durationMs);
    } catch (error) {
      console.warn('Error calculating real-time duration:', error);
    }
  }

  // For completed tests, use the stored duration
  if (report.duration !== undefined) {
    // Check if duration is in milliseconds or seconds and convert to milliseconds
    const durationMs = report.duration > 1000 ? report.duration : report.duration * 1000;
    return formatReportDuration(durationMs);
  }

  // Try to calculate from timestamps using the existing function
  const calculatedDuration = calculateRunDuration(report);
  if (calculatedDuration !== undefined) {
    return formatReportDuration(calculatedDuration);
  }

  return 'N/A';
}

/**
 * Calculate run duration from report data
 * @param report Run report object
 * @returns Duration in milliseconds or undefined if cannot be calculated
 */
export function calculateRunDuration(report: any): number | undefined {
  // First check if the report has a direct duration field
  // This is the most reliable source if available
  if (report.duration !== undefined && typeof report.duration === 'number') {
    // If duration is suspiciously close to 10 minutes and 1 second (601000ms) for stopped/failed tests,
    // we'll try to calculate a more accurate duration from timestamps
    const isNearDefaultTimeout = Math.abs(report.duration - 601000) < 1000;
    const isStoppedOrFailed = report.status === 'stopped' || report.status === 'failed';

    if (!(isNearDefaultTimeout && isStoppedOrFailed)) {
      return report.duration;
    }
    // Otherwise, fall through to timestamp-based calculation
  }

  // Try to use reportStartedAt and reportCompletedAt if available
  let startDate: Date | null = null;
  let endDate: Date | null = null;

  // Try to get start date
  if (report.reportStartedAt) {
    startDate = new Date(report.reportStartedAt);
    if (isNaN(startDate.getTime())) startDate = null;
  }

  // If reportStartedAt is not available or invalid, fall back to startedAt
  if (!startDate && report.startedAt) {
    startDate = new Date(report.startedAt);
    if (isNaN(startDate.getTime())) startDate = null;
  }

  // Try to get end date
  if (report.reportCompletedAt) {
    endDate = new Date(report.reportCompletedAt);
    if (isNaN(endDate.getTime())) endDate = null;
  }

  // If reportCompletedAt is not available or invalid, fall back to completedAt
  if (!endDate && report.completedAt) {
    endDate = new Date(report.completedAt);
    if (isNaN(endDate.getTime())) endDate = null;
  }

  // If we don't have both valid dates, try to use createdAt as a fallback for startDate
  if (!startDate && report.createdAt) {
    startDate = new Date(report.createdAt);
    if (isNaN(startDate.getTime())) startDate = null;
  }

  // If we still don't have both valid dates, we can't calculate duration
  if (!startDate || !endDate) {
    return undefined;
  }

  // Check that completedAt is after startedAt
  if (endDate.getTime() < startDate.getTime()) {
    // If startedAt is after completedAt, something's wrong
    console.info(`Report ${report.id} corrected negative duration to positive value: ${Math.abs(endDate.getTime() - startDate.getTime())}ms`);
    return Math.abs(endDate.getTime() - startDate.getTime());
  }

  // Calculate duration
  const calculatedDuration = endDate.getTime() - startDate.getTime();
  
  // Sanity check - if duration is unreasonably large (> 24 hours), log a warning
  if (calculatedDuration > 24 * 60 * 60 * 1000) {
    console.warn(`Report ${report.id} has unusually large duration: ${calculatedDuration}ms (${Math.round(calculatedDuration / 1000 / 60)} minutes)`);
  }
  
  return calculatedDuration;
}
