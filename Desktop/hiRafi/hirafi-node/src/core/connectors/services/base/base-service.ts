/**
 * Base Service
 * 
 * Provides common functionality for all connector services.
 * Implements dependency injection and proper lifecycle management.
 */

import { EventEmitter } from 'events';
import { IConnectorService, IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';

/**
 * Abstract base class for all connector services
 * Implements common functionality and lifecycle management
 */
export abstract class BaseService extends EventEmitter implements IConnectorService {
  protected isServiceInitialized = false;
  protected config: IServiceConfig = {};
  protected stats: IServiceStats;
  protected logger: any;

  /**
   * Constructor
   * @param serviceName Service name
   * @param logger Logger instance
   * @param config Service configuration
   */
  protected constructor(
    protected serviceName: string,
    logger: any,
    config: IServiceConfig = {}
  ) {
    super();
    this.logger = logger;
    this.config = { ...this.config, ...config };
    this.stats = {
      serviceName,
      isInitialized: false,
      createdAt: new Date().toISOString(),
      uptime: 0,
      operationCount: 0,
      errorCount: 0
    };

    if (config.enableLogging) {
      this.logger.debug(`${serviceName}: Service created`);
    }
  }

  /**
   * Initialize the service
   * @param config Configuration object
   */
  async initialize(config: IServiceConfig = {}): Promise<void> {
    if (this.isServiceInitialized) {
      this.logger.debug(`${this.serviceName}: Already initialized`);
      return;
    }

    this.config = { ...this.config, ...config };

    await this.doInitialize(this.config);
    this.isServiceInitialized = true;
    this.stats.isInitialized = true;

    this.emit('initialized', { serviceName: this.serviceName });
    this.logger.info(`${this.serviceName}: Service initialized successfully`);
  }

  /**
   * Abstract method for service-specific initialization
   * @param config Configuration object
   */
  protected abstract doInitialize(config: IServiceConfig): Promise<void>;

  /**
   * Close the service and cleanup resources
   */
  async close(): Promise<void> {
    if (!this.isServiceInitialized) {
      return;
    }

    await this.doClose();
    this.isServiceInitialized = false;
    this.stats.isInitialized = false;
    this.removeAllListeners();

    this.emit('closed', { serviceName: this.serviceName });
    this.logger.info(`${this.serviceName}: Service closed successfully`);
  }

  /**
   * Abstract method for service-specific cleanup
   */
  protected abstract doClose(): Promise<void>;

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean {
    return this.isServiceInitialized;
  }

  /**
   * Get service statistics
   */
  getStats(): IServiceStats {
    return {
      ...this.stats,
      uptime: this.getUptime()
    };
  }

  /**
   * Get service name
   */
  getServiceName(): string {
    return this.serviceName;
  }

  /**
   * Get uptime in milliseconds
   */
  protected getUptime(): number {
    const createdAt = new Date(this.stats.createdAt).getTime();
    return Date.now() - createdAt;
  }

  /**
   * Increment operation counter
   */
  protected incrementOperationCount(): void {
    this.stats.operationCount++;
    this.stats.lastOperation = new Date().toISOString();
  }

  /**
   * Increment error counter
   */
  protected incrementErrorCount(): void {
    this.stats.errorCount++;
    this.stats.lastError = new Date().toISOString();
  }

  /**
   * Handle errors consistently
   * @param error Error object
   * @param context Error context
   */
  protected handleError(error: any, context: string): void {
    this.incrementErrorCount();
    this.logger.error(`${this.serviceName}: ${context}: ${error}`);
    this.emit('error', { 
      error, 
      context, 
      serviceName: this.serviceName,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log operation success
   * @param operation Operation name
   * @param details Optional details
   */
  protected logOperation(operation: string, details?: any): void {
    this.incrementOperationCount();
    
    if (this.config.enableLogging) {
      this.logger.debug(`${this.serviceName}: ${operation}`, details);
    }

    this.emit('operationCompleted', {
      operation,
      serviceName: this.serviceName,
      timestamp: new Date().toISOString(),
      details
    });
  }

  /**
   * Validate configuration
   * @param config Configuration to validate
   * @param requiredFields Required configuration fields
   */
  protected validateConfig(config: IServiceConfig, requiredFields: string[] = []): void {
    for (const field of requiredFields) {
      if (!config[field]) {
        throw new Error(`${this.serviceName}: Missing required configuration field: ${field}`);
      }
    }
  }

  /**
   * Get configuration value
   * @param key Configuration key
   * @param defaultValue Default value if key not found
   */
  protected getConfigValue<T>(key: string, defaultValue?: T): T {
    return this.config[key] !== undefined ? this.config[key] as T : defaultValue as T;
  }

  /**
   * Update configuration
   * @param updates Configuration updates
   */
  protected updateConfig(updates: Partial<IServiceConfig>): void {
    this.config = { ...this.config, ...updates };
    
    if (this.config.enableLogging) {
      this.logger.debug(`${this.serviceName}: Configuration updated`, updates);
    }
  }
}
