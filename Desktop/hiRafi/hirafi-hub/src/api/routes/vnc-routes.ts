/**
 * VNC Proxy API Routes
 * VNC bağlantıları için API rotaları
 */

import { Router, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import logger from '../../utils/logger.js';
import testManager from '../../core/test-manager/index.js';
import nodeManager from '../../core/node-manager/index.js';
import { TestStatus } from '../../models/test-types.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';

const router = Router();

// Secret key from environment variable or use a fallback (in production, NEVER hardcode)
const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret_key_for_development';

// VNC token verileri için storage
const generatedTokens: Record<string, any> = {};

// Basic auth middleware for VNC routes - currently not used but kept for reference
// const authenticateVnc = (req: AuthRequest, res: Response, next: NextFunction) => {
//   try {
//     // If no user in request, return 401
//     if (!req.user) {
//       return res.status(401).json({
//         success: false,
//         error: 'Authentication required'
//       });
//     }
//     next();
//   } catch (error) {
//     logger.error(`VNC Auth Error: ${error}`);
//     return res.status(401).json({
//       success: false,
//       error: 'Authentication error'
//     });
//   }
// };

/**
 * Token oluşturma işlemi
 * @param testId Test ID
 * @param userId User ID
 * @param nodeId Node ID
 * @returns VNC erişim token
 */
function generateVncToken(testId: string, userId: string, nodeId?: string): string {
  try {
    // Token'ı oluştur (JWT), 30 dakika geçerlilik süresi
    const payload = {
      testId,
      userId,
      nodeId, // Node ID'yi ekle
      exp: Math.floor(Date.now() / 1000) + (30 * 60) // 30 dakika geçerli
    };

    const token = jwt.sign(payload, JWT_SECRET);

    // Token'ı kaydet
    generatedTokens[token] = {
      testId,
      userId,
      nodeId,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + (30 * 60 * 1000)) // 30 dakika sonra
    };

    logger.info(`Generated VNC token for test ${testId} and user ${userId.substring(0, 8)}`);

    return token;
  } catch (error: any) {
    logger.error(`Error generating VNC token: ${error.message}`);
    throw error;
  }
}

/**
 * Verifies a VNC access token
 * @param token The JWT token to verify
 * @returns The decoded token payload if valid
 */
function verifyVncToken(token: string) {
  try {
    // Token'ın doğrulamasını yap
    const decoded = jwt.verify(token, JWT_SECRET);
    logger.info(`VNC token doğrulandı: ${JSON.stringify(decoded)}`);
    return decoded;
  } catch (error: any) {
    if (error.name === 'TokenExpiredError') {
      logger.warn(`VNC token süresi doldu: ${token.substring(0, 15)}...`);
      throw new Error('Token süresi doldu');
    } else if (error.name === 'JsonWebTokenError') {
      logger.warn(`Geçersiz VNC token: ${token.substring(0, 15)}...`);
      throw new Error('Geçersiz token formatı');
    } else {
      logger.error('Token doğrulama hatası:', error);
      throw new Error('Token doğrulama hatası');
    }
  }
}

/**
 * POST /api/vnc/token
 * VNC erişim token'ı oluşturur
 */
router.post('/token', async (req: Request, res: Response) => {
  try {
    const { testId, nodeId, userId = 'anonymous' } = req.body;

    if (!testId) {
      return res.status(400).json({ error: 'Test ID is required' });
    }

    // Test varlığını ve durumunu kontrol et - MongoDB'den al
    try {
      const { getTestById } = await import('../../services/mongo/testService.js');
      const testResult = await getTestById(testId);

      if (!testResult.success || !testResult.test) {
        return res.status(404).json({ error: 'Test not found' });
      }

      const test = testResult.test;

      // Test tamamlandıysa veya çalışmıyorsa, token oluşturmayı reddet
      if (test.status === TestStatus.COMPLETED || test.status === TestStatus.FAILED || test.status === TestStatus.STOPPED) {
        return res.status(400).json({
          error: 'Test is no longer running',
          status: test.status
        });
      }
    } catch (error) {
      logger.error(`Error getting test ${testId} from MongoDB: ${error}`);
      return res.status(500).json({ error: 'Error retrieving test information' });
    }

    // Token oluştur - node ID'yi de parametreye ekle
    const token = generateVncToken(testId, userId, nodeId);

    return res.status(200).json({ token });

  } catch (error: any) {
    logger.error('Error generating VNC token:', error);
    return res.status(500).json({ error: error.message || 'Unknown error' });
  }
});

/**
 * GET /api/vnc/viewer/:token
 * VNC viewer HTML sayfası döndürür
 */
router.get('/viewer/:token', async (req: Request, res: Response) => {
  try {
    const { token } = req.params;
    logger.info(`VNC viewer request received with token: ${token.substring(0, 10)}...`);

    if (!token) {
      logger.error('VNC viewer request without token');
      return res.status(401).send(`
        <html><body style="font-family: Arial; color: white; background: #333; text-align: center; padding-top: 20%;">
          <h2>Hata: Token gerekli</h2>
          <p>VNC erişimi için geçerli bir token gereklidir</p>
          <button onclick="window.close()" style="padding: 10px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer;">Kapat</button>
        </body></html>
      `);
    }

    let payload;
    let testId;
    let nodeId;

    try {
      // Token'ı doğrula
      payload = verifyVncToken(token) as any;
      nodeId = payload.nodeId;
      testId = payload.testId;

      if (!nodeId || !testId) {
        logger.error('Invalid token payload:', payload);
        return res.status(200).send(`
          <html><body style="font-family: Arial; color: white; background: #333; text-align: center; padding-top: 20%;">
            <h2>Hata: Geçersiz token</h2>
            <p>VNC token'ı geçerli bilgileri içermiyor</p>
            <button onclick="window.close()" style="padding: 10px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer;">Kapat</button>
          </body></html>
        `);
      }

      logger.info(`Token verified. Test ID: ${testId}, Node ID: ${nodeId}`);

    } catch (tokenError: any) {
      // Token hatası varsa HTML içinde bu hatayı göster
      logger.warn(`VNC token hatası: ${tokenError.message}`);

      // Hata HTML'i gönder
      return res.status(200).send(`
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VNC Viewer Error</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #1a1a1a;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .error-container {
      text-align: center;
      max-width: 500px;
      padding: 2rem;
      border-radius: 8px;
      background-color: rgba(0,0,0,0.6);
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    .error-icon {
      display: inline-block;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(220, 38, 38, 0.2);
      margin-bottom: 1rem;
      position: relative;
    }
    .error-icon:before, .error-icon:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 30px;
      height: 3px;
      background-color: #ef4444;
      border-radius: 2px;
    }
    .error-icon:before {
      transform: translate(-50%, -50%) rotate(45deg);
    }
    .error-icon:after {
      transform: translate(-50%, -50%) rotate(-45deg);
    }
    h1 {
      color: #ef4444;
      margin-top: 0;
    }
    p {
      color: #d1d5db;
      line-height: 1.6;
    }
    button {
      margin-top: 1.5rem;
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #1d4ed8;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-icon"></div>
    <h1>Bağlantı Hatası</h1>
    <p>VNC görüntüleyicisi başlatılamadı. Token geçersiz veya süresi dolmuş.</p>
    <p>Hata: ${tokenError.message}</p>
    <button onclick="window.close()">Kapat</button>
  </div>
</body>
</html>
      `);
    }

    // Test durumunu kontrol et
    logger.info(`Checking test status for test ID: ${testId}`);
    const test = await testManager.getTest(testId);

    if (!test) {
      logger.error(`Test not found: ${testId}`);
      return res.status(200).send(`
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VNC Viewer Error</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #1a1a1a;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .error-container {
      text-align: center;
      max-width: 500px;
      padding: 2rem;
      border-radius: 8px;
      background-color: rgba(0,0,0,0.6);
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    .error-icon {
      display: inline-block;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(220, 38, 38, 0.2);
      margin-bottom: 1rem;
      position: relative;
    }
    .error-icon:before, .error-icon:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 30px;
      height: 3px;
      background-color: #ef4444;
      border-radius: 2px;
    }
    .error-icon:before {
      transform: translate(-50%, -50%) rotate(45deg);
    }
    .error-icon:after {
      transform: translate(-50%, -50%) rotate(-45deg);
    }
    h1 {
      color: #ef4444;
      margin-top: 0;
    }
    p {
      color: #d1d5db;
      line-height: 1.6;
    }
    button {
      margin-top: 1.5rem;
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #1d4ed8;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-icon"></div>
    <h1>Test Bulunamadı</h1>
    <p>İzlemek istediğiniz test artık mevcut değil.</p>
    <button onclick="window.close()">Kapat</button>
  </div>
</body>
</html>
      `);
    }

    // Test tamamlandıysa veya çalışmıyorsa, token'ı geçersiz kıl
    if (test.status === TestStatus.COMPLETED || test.status === TestStatus.FAILED || test.status === TestStatus.STOPPED) {
      logger.info(`Token access denied: Test ${testId} is no longer running (status: ${test.status})`);
      return res.status(200).send(`
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VNC Viewer - Test Completed</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #1a1a1a;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .error-container {
      text-align: center;
      max-width: 500px;
      padding: 2rem;
      border-radius: 8px;
      background-color: rgba(0,0,0,0.6);
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    .status-icon {
      display: inline-block;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(34, 197, 94, 0.2);
      margin-bottom: 1rem;
      position: relative;
    }
    .status-icon:before, .status-icon:after {
      content: '';
      position: absolute;
    }
    .status-icon:before {
      top: 40%;
      left: 25%;
      width: 15px;
      height: 3px;
      background-color: #22c55e;
      transform: rotate(45deg);
    }
    .status-icon:after {
      top: 45%;
      left: 35%;
      width: 25px;
      height: 3px;
      background-color: #22c55e;
      transform: rotate(-45deg);
    }
    h1 {
      color: #22c55e;
      margin-top: 0;
    }
    p {
      color: #d1d5db;
      line-height: 1.6;
    }
    .status {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      margin: 10px 0;
    }
    .status.completed {
      background-color: rgba(34, 197, 94, 0.2);
      color: #22c55e;
    }
    .status.failed {
      background-color: rgba(239, 68, 68, 0.2);
      color: #ef4444;
    }
    .status.stopped {
      background-color: rgba(245, 158, 11, 0.2);
      color: #f59e0b;
    }
    button {
      margin-top: 1.5rem;
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #1d4ed8;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="status-icon"></div>
    <h1>Test Tamamlandı</h1>
    <p>Bu test artık aktif değil, canlı izleme kullanılamaz.</p>
    <div class="status ${test.status.toLowerCase()}">Durum: ${test.status}</div>
    <button onclick="window.close()">Kapat</button>
  </div>
</body>
</html>
      `);
    }

    // User has valid token and test is still running, generate the noVNC viewer HTML
    logger.info(`Test is running. Generating VNC HTML...`);

    // Node bilgilerini al
    const node = nodeManager.getNode(nodeId);

    // Varsayılan değerler
    const publicHost = process.env.PUBLIC_HOST || 'localhost';
    const defaultVncPort = process.env.NOVNC_PORT || '6080';
    const vncPassword = process.env.VNC_PASSWORD || 'vncpassword';

    // Node'a özel port bilgisi varsa onu kullan, yoksa varsayılan portu kullan
    const novncPort = node?.vncPort || defaultVncPort;

    logger.info(`Using VNC port ${novncPort} for node ${nodeId} (${node?.name || 'unknown'})`);

    // Create a custom HTML page that loads the noVNC viewer with configuration
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Secure VNC Viewer - Test ${testId}</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
      background-color: #0f172a; /* Koyu arka plan */
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: white;
    }
    #vnc-container {
      width: 100%;
      height: 100%;
      position: relative;
    }
    #loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(15, 23, 42, 0.95);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      z-index: 10;
    }
    #loading.hidden {
      display: none;
    }
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(34, 114, 250, 0.2);
      border-top: 4px solid rgba(34, 114, 250, 1);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    #vnc-iframe {
      width: 100%;
      height: 100%;
      border: none;
      opacity: 0;
      transition: opacity 0.5s ease;
    }
    #vnc-iframe.loaded {
      opacity: 1;
    }
    #token-expired {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(15, 23, 42, 0.95);
      color: white;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      z-index: 100;
    }
    #error-message {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(15, 23, 42, 0.95);
      color: white;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      z-index: 100;
    }
    #token-expired h2, #error-message h2 {
      font-size: 24px;
      margin-bottom: 10px;
      color: #f87171;
    }
    #token-expired p, #error-message p {
      font-size: 16px;
      max-width: 80%;
      text-align: center;
      color: #cbd5e1;
      line-height: 1.6;
    }
    .action-button {
      margin-top: 20px;
      padding: 10px 20px;
      background-color: #2563eb;
      border: none;
      border-radius: 5px;
      color: white;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .action-button:hover {
      background-color: #1d4ed8;
    }
    .action-button.danger {
      background-color: #ef4444;
    }
    .action-button.danger:hover {
      background-color: #dc2626;
    }
    .connection-info {
      margin-top: 10px;
      font-size: 14px;
      color: #94a3b8;
    }
    #connection-time {
      font-weight: bold;
    }
    #debug-info {
      position: absolute;
      bottom: 10px;
      left: 10px;
      background: rgba(15, 23, 42, 0.8);
      color: #cbd5e1;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      z-index: 5;
      max-width: 80%;
      max-height: 150px;
      overflow: auto;
      font-family: monospace;
      border: 1px solid #334155;
    }
    .debug-entry {
      margin: 3px 0;
    }
    .debug-time {
      color: #64748b;
    }

    /* Node bilgileri paneli */
    #node-info {
      position: absolute;
      top: 10px;
      right: 10px;
      background: rgba(15, 23, 42, 0.8);
      color: #cbd5e1;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      z-index: 5;
      max-width: 250px;
      overflow: auto;
      font-family: monospace;
      border: 1px solid #334155;
    }
  </style>
</head>
<body>
  <div id="vnc-container">
    <div id="loading">
      <div class="spinner"></div>
      <p style="font-size: 18px;">VNC bağlantısı kuruluyor...</p>
      <p style="font-size: 14px; color: #94a3b8; margin-top: 10px;">Bu işlem birkaç saniye sürebilir</p>
      <div class="connection-info">Bağlantı süresi: <span id="connection-time">0</span> saniye</div>
    </div>

    <!-- Test completion overlay -->
    <div id="token-expired">
      <h2>Test Tamamlandı</h2>
      <p>Bu test tamamlandı ve canlı izleme oturumu artık kullanılamıyor.</p>
      <p id="auto-close-message" style="display: none;">
        Bu pencere <span id="countdown">5</span> saniye içinde otomatik olarak kapanacak.
      </p>
      <p id="manual-close-message" style="display: none; color: #f87171;">
        Otomatik kapatma başarısız oldu. Lütfen pencereyi manuel olarak kapatın.
      </p>
      <button class="action-button danger" onclick="window.close()">İzleyiciyi Kapat</button>
    </div>

    <!-- Error message overlay -->
    <div id="error-message">
      <h2>Bağlantı Hatası</h2>
      <p id="error-text">VNC görüntüleyici yüklenemedi.</p>
      <button class="action-button" onclick="tryReconnect()">Yeniden Bağlan</button>
      <button class="action-button danger" onclick="window.close()">Kapat</button>
    </div>

    <!-- Node bilgileri -->
    <div id="node-info">
      <div>Node: <strong>${node?.name || 'Unknown'}</strong></div>
      <div>TestID: <strong>${testId.substring(0, 8)}...</strong></div>
      <div>Port: <strong>${novncPort}</strong></div>
    </div>

    <!-- Debug info panel (only shown in dev) -->
    <div id="debug-info"></div>

    <!-- Load noVNC viewer in an iframe -->
    <iframe
      id="vnc-iframe"
      src=""
      allowfullscreen
    ></iframe>
  </div>

  <script>
    // Bağlantı zamanını izle
    let connectionTimer = 0;
    let connectionInterval;
    const connectionTimeElement = document.getElementById('connection-time');

    // Debug panel
    const debugPanel = document.getElementById('debug-info');
    const isDev = ${process.env.NODE_ENV === 'development' ? 'true' : 'false'};

    // Debug log
    function debugLog(message) {
      const now = new Date();
      const timeStr = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');

      if (isDev) {
        const debugEntry = document.createElement('div');
        debugEntry.className = 'debug-entry';
        debugEntry.innerHTML = '<span class="debug-time">[' + timeStr + ']</span> ' + message;
        debugPanel.appendChild(debugEntry);
        debugPanel.scrollTop = debugPanel.scrollHeight;
      }
    }

    // Configuration
    const publicHost = '${publicHost}';
    const novncPort = '${novncPort}';
    const vncPassword = '${vncPassword}';
    const testId = '${testId}';
    const loading = document.getElementById('loading');
    const errorMessage = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');
    const vncIframe = document.getElementById('vnc-iframe');

    debugLog(\`Configuration: Host=\${publicHost}, Port=\${novncPort}, TestID=\${testId}\`);

    // Start connection timer
    function startConnectionTimer() {
      connectionTimer = 0;
      connectionTimeElement.textContent = connectionTimer;

      connectionInterval = setInterval(() => {
        connectionTimer++;
        connectionTimeElement.textContent = connectionTimer;

        // 30 saniye bağlantı süresi aşılırsa otomatik yeniden bağlan
        if (connectionTimer >= 30 && loading.style.display !== 'none') {
          debugLog('Connection timeout after 30 seconds, attempting reconnect...');
          clearInterval(connectionInterval);
          tryReconnect();
        }
      }, 1000);
    }

    // Try to connect to VNC
    function connectVNC() {
      debugLog('Starting VNC connection...');
      loading.classList.remove('hidden');
      loading.style.display = 'flex';
      errorMessage.style.display = 'none';
      vncIframe.className = '';

      // Start connection timer
      startConnectionTimer();

      // Build the noVNC URL
      const vncUrl = \`http://\${publicHost}:\${novncPort}/vnc.html?host=\${publicHost}&port=\${novncPort}&path=websockify&autoconnect=true&resize=scale&view_only=1&password=\${vncPassword}&quality=8&compression=6&show_dot=false&bell=false&shared=true\`;
      debugLog(\`VNC connection configured: Using host \${publicHost} and port \${novncPort}\`);

      // Set iframe source
      vncIframe.src = vncUrl;

      // Monitor iframe loading
      vncIframe.onload = function() {
        debugLog('VNC iframe DOM loaded');

        // NoVNC iframe content might load but websocket connection might fail
        // So we need to check if the iframe content is actually showing something
        setTimeout(() => {
          debugLog('Checking if NoVNC is properly connected...');
          loading.style.display = 'none';
          vncIframe.className = 'loaded';
          clearInterval(connectionInterval);
        }, 2000);
      };

      // Handle iframe error
      vncIframe.onerror = function(e) {
        debugLog(\`VNC iframe load error: \${e}\`);
        clearInterval(connectionInterval);
        loading.style.display = 'none';
        errorText.textContent = 'VNC bağlantısı kurulamadı. NoVNC sunucusuna erişilemiyor.';
        errorMessage.style.display = 'flex';
      };

      // If loading takes too long, show error
      setTimeout(() => {
        if (loading.style.display !== 'none') {
          debugLog('VNC connection timeout after 15 seconds');
          clearInterval(connectionInterval);
          loading.style.display = 'none';
          errorText.textContent = 'VNC bağlantısı zaman aşımına uğradı. Lütfen tekrar deneyin.';
          errorMessage.style.display = 'flex';
        }
      }, 15000);
    }

    // Reconnect function
    function tryReconnect() {
      debugLog('Attempting to reconnect...');
      vncIframe.src = '';
      setTimeout(() => {
        connectVNC();
      }, 1000);
    }

    // Function to check if the test is still running
    async function checkTestStatus() {
      try {
        debugLog('Checking test status...');
        const baseUrl = window.location.origin;

        // Yeni anonim endpoint'i kullan
        const response = await fetch(baseUrl + '/api/vnc/check-status/${testId}');

        if (!response.ok) {
          debugLog('Error fetching test status: ' + response.status);
          return;
        }

        const data = await response.json();
        debugLog(\`Test status: \${data.status}, isActive: \${data.isActive}\`);

        // Test aktif değilse overlay'i göster ve otomatik kapat
        if (!data.isActive) {
          debugLog(\`Test is no longer running. Status: \${data.status}\`);
          document.getElementById('token-expired').style.display = 'flex';

          // Test tamamlandığında polling'i durdur
          clearInterval(statusCheckInterval);

          // 5 saniye sonra pencereyi otomatik kapat
          debugLog('Test completed. Window will close automatically in 5 seconds...');
          document.getElementById('auto-close-message').style.display = 'block';

          let countdown = 5;
          const countdownElement = document.getElementById('countdown');
          countdownElement.textContent = countdown.toString();

          const countdownInterval = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown.toString();

            if (countdown <= 0) {
              clearInterval(countdownInterval);
              debugLog('Auto-closing window...');
              window.close();

              // If window.close() doesn't work (which is common in modern browsers),
              // show a message asking the user to close the window manually
              setTimeout(() => {
                document.getElementById('manual-close-message').style.display = 'block';
                document.getElementById('auto-close-message').style.display = 'none';
              }, 1000);
            }
          }, 1000);
        }
      } catch (error) {
        debugLog(\`Error checking test status: \${error}\`);
      }
    }

    // Initialize
    connectVNC();

    // Durumu başlangıçta ve sonra her 5 saniyede bir kontrol et
    debugLog('Starting status check polling...');
    checkTestStatus();
    const statusCheckInterval = setInterval(checkTestStatus, 5000);

    // Show debug panel if in development
    if (isDev) {
      debugPanel.style.display = 'block';
    } else {
      debugPanel.style.display = 'none';
    }

    // Window unload handler
    window.addEventListener('beforeunload', function() {
      debugLog('Window closing, cleaning up...');
      clearInterval(connectionInterval);
      clearInterval(statusCheckInterval);
    });
  </script>
</body>
</html>
      `;

    logger.info(`VNC HTML generated, returning to client.`);
    // HTML içeriğini döndür
    res.setHeader('Content-Type', 'text/html');
    res.setHeader('Cache-Control', 'no-store, max-age=0');
    return res.send(htmlContent);
  } catch (error: any) {
    logger.error('Error in VNC proxy viewer route:', error);
    return res.status(200).send(`
      <html><body style="font-family: Arial; color: white; background: #333; text-align: center; padding-top: 20%;">
        <h2>VNC Görüntüleyici Hatası</h2>
        <p>${error.message || 'Bilinmeyen bir hata oluştu'}</p>
        <button onclick="window.close()" style="padding: 10px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer;">Kapat</button>
      </body></html>
    `);
  }
});

/**
 * GET /api/vnc/info/:testId
 * Test için VNC bağlantı bilgilerini döndürür (Frontend bileşeni için)
 */
router.get('/info/:testId', async (req: AuthRequest, res: Response) => {
  try {
    // Note: Authentication is handled by the middleware
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    // Test varlığını kontrol et
    const test = await testManager.getTest(testId);
    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // Test hala çalışıyor mu kontrol et
    if (test.status === TestStatus.COMPLETED || test.status === TestStatus.FAILED || test.status === TestStatus.STOPPED) {
      return res.status(400).json({
        success: false,
        error: 'Test has completed or is no longer running',
        status: test.status,
        isActive: false
      });
    }

    // Test çalıştığı node'u al
    const nodeId = test.nodeId;
    if (!nodeId) {
      return res.status(400).json({
        success: false,
        error: 'No node found for this test'
      });
    }

    // VNC bilgilerini döndür
    const vncInfo = {
      success: true,
      testId,
      nodeId,
      status: test.status,
      isActive: true,
      // Frontend'in doğrudan token alması için
      getTokenEndpoint: '/api/vnc/token',
      // Gerekli bilgiler
      scenarioName: test.scenarioName
    };

    return res.json(vncInfo);
  } catch (error: any) {
    logger.error(`API Error in GET /vnc/info: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/vnc/check-status/:testId
 * Test durumunu anonim olarak kontrol etmek için endpoint
 * Bu endpoint VNC viewer tarafından periyodik olarak çağrılarak testin hala aktif olup olmadığı kontrol edilir
 */
router.get('/check-status/:testId', async (req: Request, res: Response) => {
  try {
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({ error: 'Test ID is required' });
    }

    // Test durumunu kontrol et
    const test = await testManager.getTest(testId);

    if (!test) {
      return res.json({
        status: 'NOT_FOUND',
        isActive: false,
        message: 'Test not found'
      });
    }

    // Test aktif mi? (tamamlanmış, başarısız, durdurulmuş testler aktif değil)
    const isActive = test.status !== TestStatus.COMPLETED &&
                    test.status !== TestStatus.FAILED &&
                    test.status !== TestStatus.STOPPED;

    return res.json({
      status: test.status,
      isActive,
      message: isActive ? 'Test is active' : 'Test is not active'
    });
  } catch (error: any) {
    logger.error('Error checking test status:', error);
    return res.status(500).json({
      error: error.message || 'Unknown error',
      isActive: false
    });
  }
});

/**
 * GET /api/vnc/proxy/:token
 * VNC bağlantısını proxy olarak yönlendirme - kullanıcıya doğrudan URL göstermeden
 */
router.get('/proxy/:token', async (req: Request, res: Response) => {
  try {
    const { token } = req.params;
    logger.info(`VNC proxy request received with token: ${token.substring(0, 10)}...`);

    if (!token) {
      logger.error('VNC proxy request without token');
      return res.status(401).json({
        success: false,
        error: 'Token is required'
      });
    }

    let payload;
    let testId;
    let nodeId;

    try {
      // Token'ı doğrula
      payload = verifyVncToken(token) as any;
      nodeId = payload.nodeId;
      testId = payload.testId;

      if (!nodeId || !testId) {
        logger.error('Invalid token payload:', payload);
        return res.status(403).json({
          success: false,
          error: 'Invalid token payload'
        });
      }

      logger.info(`Token verified for proxy. Test ID: ${testId}, Node ID: ${nodeId}`);

    } catch (tokenError: any) {
      logger.warn(`VNC token error for proxy: ${tokenError.message}`);
      return res.status(403).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    // Test durumunu kontrol et
    logger.info(`Checking test status for proxy, test ID: ${testId}`);
    const test = await testManager.getTest(testId);

    if (!test) {
      logger.error(`Test not found for proxy: ${testId}`);
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // Test tamamlandıysa veya çalışmıyorsa
    if (test.status === TestStatus.COMPLETED || test.status === TestStatus.FAILED || test.status === TestStatus.STOPPED) {
      logger.info(`Proxy access denied: Test ${testId} is no longer running (status: ${test.status})`);
      return res.status(400).json({
        success: false,
        error: 'Test is no longer active',
        status: test.status
      });
    }

    // Proxy için gerekli bilgileri döndür
    const publicHost = process.env.PUBLIC_HOST || 'localhost';
    const novncPort = process.env.NOVNC_PORT || '6080';

    // Güvenli içerik oluştur - URL doğrudan frontend'e verilmiyor
    return res.json({
      success: true,
      proxy: {
        host: publicHost,
        port: novncPort,
        token: token,
        testId: testId,
        isSecure: false, // HTTP veya HTTPS olup olmadığını belirt
      }
    });

  } catch (error: any) {
    logger.error('Error in VNC proxy route:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error while setting up VNC proxy'
    });
  }
});

/**
 * GET /api/vnc/node-info/:testId
 * Test ve node hakkında VNC bağlantı bilgilerini döndürür
 */
router.get('/node-info/:testId', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    // Note: Authentication is handled by the middleware
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        error: 'Test ID is required'
      });
    }

    // Test varlığını kontrol et
    const test = await testManager.getTest(testId);
    if (!test) {
      return res.status(404).json({
        error: 'Test not found'
      });
    }

    // Test hala çalışıyor mu kontrol et
    const isRunning = test.status !== TestStatus.COMPLETED &&
                     test.status !== TestStatus.FAILED &&
                     test.status !== TestStatus.STOPPED;

    if (!isRunning) {
      return res.json({
        testId,
        testStatus: test.status,
        running: false,
        message: 'Test has completed or is no longer running'
      });
    }

    // Test çalıştığı node'u al
    const nodeId = test.nodeId;
    if (!nodeId) {
      return res.status(400).json({
        error: 'No node found for this test'
      });
    }

    // Node bilgilerini al
    const node = nodeManager.getNode(nodeId);

    if (!node) {
      return res.status(404).json({
        error: `Node ${nodeId} not found`
      });
    }

    // VNC bilgilerini döndür
    const vncInfo = {
      testId,
      nodeId,
      nodeName: node.name,
      testStatus: test.status,
      running: true,
      vncPort: node.vncPort || process.env.NOVNC_PORT || '6080',
      host: process.env.PUBLIC_HOST || 'localhost'
    };

    logger.info(`VNC node info for test ${testId} - Node: ${node.name}, Port: ${vncInfo.vncPort}`);
    return res.json(vncInfo);
  } catch (error: any) {
    logger.error(`API Error in GET /vnc/node-info: ${error.message}`);
    return res.status(500).json({
      error: error.message
    });
  }
});

/**
 * POST /api/vnc/close-session
 * VNC oturumunu sonlandırır
 */
router.post('/close-session', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    const { testId, token } = req.body;

    if (!testId || !token) {
      return res.status(400).json({
        success: false,
        error: 'Test ID and token are required'
      });
    }

    // Token'ı doğrula
    try {
      verifyVncToken(token);
    } catch (error) {
      // Token geçersiz olsa bile işleme devam et, sadece log al
      logger.warn(`Invalid token in close-session request: ${error}`);
    }

    // Token'ı generatedTokens listesinden kaldır
    if (generatedTokens[token]) {
      delete generatedTokens[token];
      logger.info(`VNC token removed for test ${testId}`);
    }

    // Test durumunu kontrol et
    const test = await testManager.getTest(testId);
    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // Test hala çalışıyor mu kontrol et
    const isRunning = test.status !== TestStatus.COMPLETED &&
                     test.status !== TestStatus.FAILED &&
                     test.status !== TestStatus.STOPPED;

    return res.json({
      success: true,
      testId,
      status: test.status,
      isRunning
    });
  } catch (error: any) {
    logger.error(`API Error in POST /vnc/close-session: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;