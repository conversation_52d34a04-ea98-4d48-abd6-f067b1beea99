/**
 * User Model
 * Authentication ve yetkilendirme için kullanıcı modeli
 */

import { Permission } from './team.js';

/**
 * AI Model yapısı
 */
export interface AIModel {
  id: string;
  name: string;
  api: string;
  apiKey: string;
  isActive: boolean;
  supportsImageProcessing: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  _id?: string;
  id?: string;
  email: string;
  password: string;
  name?: string;
  accountType: string;
  teamRole?: string;
  active?: boolean;
  companyId?: string;
  teamId?: string;
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;

  // Takım yönetimi için alanlar
  defaultTeamId?: string;    // Varsayılan takım ID'si
  lastActiveTeamId?: string; // Son aktif olunan takım ID'si
  teams?: string[];          // Kullanıcının dahil olduğu takım ID'leri
  customPermissions?: Permission[]; // <PERSON><PERSON> izinler
  avatar?: string;           // Profil resmi URL'si
  settings?: UserSettings;   // Kullanıcı ayarları
  aiModels?: AIModel[];      // Kullanıcının yapay zeka modelleri
}

/**
 * Kullanıcı ayarları
 */
export interface UserSettings {
  theme?: string;
  language?: string;
  notificationPreferences?: NotificationPreferences;
  timezone?: string;
}

/**
 * Bildirim tercihleri
 */
export interface NotificationPreferences {
  email: boolean;
  inApp: boolean;
  testCompletion: boolean;
  teamInvites: boolean;
  reportGeneration: boolean;
}

// Kullanıcı hesap türleri
export enum AccountType {
  ADMIN = 'admin',
  USER = 'user',
  COMPANY_OWNER = 'company_owner',  // ✅ Frontend'de kullanılan company_owner eklendi
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
  TEAM = 'team'
}

// Takım içi rol bilgisi - yeni enum
export enum TeamRole {
  TEAM_MEMBER = 'team_member',
  TEAM_LEADER = 'team_leader',
  TESTER = 'tester',
  VIEWER = 'viewer',
  TEST_LEAD = 'test_lead',
  QA = 'qa',
  DEVELOPER = 'developer',
  COMPANY_LEADER = 'company_leader'
}



// Kullanıcı girişi için gelen veri
export interface LoginCredentials {
  email: string;
  password: string;
}

// Kullanıcı kaydı için gelen veri
export interface RegisterUserData {
  email: string;
  password: string;
  name?: string;
  accountType?: string;
  teamRole?: string;
  teamId?: string;          // Kayıt sırasında takıma dahil etmek için
  inviteToken?: string;     // Davet tokeni
}

// JWT token ile dönen kullanıcı bilgileri
export interface UserTokenData {
  id: string;
  email: string;
  name?: string;
  accountType: string;
  role: string;             // Role property for backward compatibility
  teamRole?: string;        // Token'a takım rolü bilgisini ekle
  defaultTeamId?: string;   // Token'a takım bilgisi ekle
  teams?: string[];         // Kullanıcının takımları
  customPermissions?: Permission[]; // Özel izinler
  companyId?: string;       // Şirket ID'si
  teamId?: string;          // Aktif takım ID'si
}

// Kullanıcı profil yanıtı
export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  accountType: string;
  teamRole?: string;       // Profil yanıtına takım rolü ekle
  active?: boolean;
  companyId?: string;
  teamId?: string;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
  defaultTeamId?: string;
  lastActiveTeamId?: string;
  teams?: string[];
  avatar?: string;
}