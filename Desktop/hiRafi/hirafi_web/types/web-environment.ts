/**
 * Web Environment Settings Types
 * Defines the structure for web-specific test environment configuration
 */

// Web-specific environment settings
export interface WebEnvironmentSettings {
  // Platform identifier
  platform: 'web';

  // Web browser settings
  browserName?: string;  // e.g., "Chrome" | "Firefox" | "Safari" | "Edge"
  browserVersion?: string;
  browser: string;      // e.g., "chrome" | "firefox"
  browserMode?: 'headless' | 'headed'; // Browser display mode
  viewportSize?: string; // e.g., "Mobile" | "Tablet" | "Desktop" | "Custom"
  viewport: {
    width: number;
    height: number;
  };
  customViewport?: {
    width: number;
    height: number;
  };
  networkSpeed?: string; // e.g., "Slow 3G" | "Fast 3G" | "Slow 4G" | "Fast 4G" | "Normal"

  // AI model settings
  aiModel?: string;      // e.g., "GPT-3.5" | "GPT-4" | "Claude" | "Gemini" | "Auto" (ID of the model)
  aiModelName?: string; // Display name of the AI model

  // Additional web-specific properties
  userAgent?: string;           // Custom user agent string
  device?: string;              // Device name (e.g., "iPhone 12 Pro", "iPad Air", "Desktop")
  geolocation?: {               // Geolocation settings
    latitude: number;
    longitude: number;
  };

  // Proxy settings
  proxy?: {
    enabled: boolean;
    type: string;       // e.g., "SOCKS5" | "HTTP" | "HTTPS"
    host: string;
    port: number;
    username: string;
    password: string;
  };

  // Cookies and headers
  cookies?: Array<{             // Browser cookies
    name: string;
    value: string;
    domain: string;
    path?: string;
    expires?: number;
    httpOnly?: boolean;
    secure?: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
  }>;
  headers?: Record<string, string>; // Custom HTTP headers

  // AI model configuration (for node execution)
  aiModelConfig?: {
    OPENAI_BASE_URL: string;
    OPENAI_API_KEY: string;
    MIDSCENE_MODEL_NAME: string;
    [key: string]: string;
  };
}

// Default web environment settings
export const defaultWebEnvironmentSettings: WebEnvironmentSettings = {
  platform: "web",
  browserName: "Chrome",
  browserVersion: "Latest",
  browser: "chrome",
  browserMode: "headless", // Default to headless for better performance
  viewportSize: "Desktop",
  viewport: {
    width: 1280,
    height: 720
  },
  networkSpeed: "Normal",
  aiModel: "Gemini",
  aiModelName: "Gemini Pro",
  device: "Desktop (1280x720)",
  proxy: {
    enabled: false,
    type: "HTTP",
    host: "",
    port: 0,
    username: "",
    password: ""
  }
};


