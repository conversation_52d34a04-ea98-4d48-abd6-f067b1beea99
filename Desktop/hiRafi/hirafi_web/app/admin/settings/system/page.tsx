"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminRouteGuard } from "@/lib/api/admin-auth"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Loader2, Mail, Send, Slack, Save, Plus, Trash2, Bot, Globe } from "lucide-react"
import { HtmlEditor } from "@/components/ui/html-editor"
import { useToast } from "@/components/ui/use-toast"
import {
  getSystemSettings,
  getSystemSettingByType,
  createSystemSetting,
  updateSystemSetting,
  deleteSystemSetting,
  testSMTPConfig,
  testSlackConfig
} from "@/lib/api/admin-system-settings-api"

// SMTP form schema
const smtpFormSchema = z.object({
  host: z.string().min(1, { message: "SMTP host is required" }),
  port: z.coerce.number().int().min(1).max(65535),
  secure: z.boolean().default(false),
  auth: z.object({
    user: z.string().min(1, { message: "Username is required" }),
    pass: z.string().min(1, { message: "Password is required" })
  }),
  from: z.string().email({ message: "Please enter a valid email address" }),
  fromName: z.string().min(1, { message: "From name is required" })
})

// Slack form schema
const slackFormSchema = z.object({
  botToken: z.string().min(1, { message: "Bot token is required" }),
  signingSecret: z.string().min(1, { message: "Signing secret is required" }),
  defaultChannel: z.string().min(1, { message: "Default channel is required" })
})

// Test SMTP form schema
const testEmailFormSchema = z.object({
  testEmail: z.string().email({ message: "Please enter a valid email address" })
})

// Test Slack form schema
const testSlackFormSchema = z.object({
  testMessage: z.string().min(1, { message: "Test message is required" })
})

// AI Model form schema
const aiModelFormSchema = z.object({
  name: z.string().min(1, { message: "Model name is required" }),
  api: z.string().min(1, { message: "API endpoint is required" }),
  apiKey: z.string().min(1, { message: "API key is required" }),
  isActive: z.boolean().default(true),
  supportsImageProcessing: z.boolean().default(true)
})

// Landing Page form schema
const landingPageFormSchema = z.object({
  adminEmails: z.string().min(1, { message: "Admin emails are required" }),
  emailSubject: z.string().min(1, { message: "Email subject is required" }),
  emailTemplate: z.string().min(1, { message: "Email template is required" }),
  thankYouMessage: z.string().min(1, { message: "Thank you message is required" })
})

type SMTPFormValues = z.infer<typeof smtpFormSchema>
type SlackFormValues = z.infer<typeof slackFormSchema>
type TestEmailFormValues = z.infer<typeof testEmailFormSchema>
type TestSlackFormValues = z.infer<typeof testSlackFormSchema>
type AIModelFormValues = z.infer<typeof aiModelFormSchema>
type LandingPageFormValues = z.infer<typeof landingPageFormSchema>

export default function SystemSettingsPage() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("smtp")

  // Handle tab parameter from URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const tab = params.get('tab')
    if (tab === 'smtp' || tab === 'slack' || tab === 'ai_model' || tab === 'landing_page') {
      setActiveTab(tab)
    }
  }, [])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [smtpSetting, setSmtpSetting] = useState<any>(null)
  const [slackSetting, setSlackSetting] = useState<any>(null)
  const [aiModelSetting, setAiModelSetting] = useState<any>(null)
  const [landingPageSetting, setLandingPageSetting] = useState<any>(null)

  // SMTP form
  const smtpForm = useForm<SMTPFormValues>({
    resolver: zodResolver(smtpFormSchema),
    defaultValues: {
      host: "",
      port: 587,
      secure: false,
      auth: {
        user: "",
        pass: ""
      },
      from: "",
      fromName: ""
    }
  })

  // Slack form
  const slackForm = useForm<SlackFormValues>({
    resolver: zodResolver(slackFormSchema),
    defaultValues: {
      botToken: "",
      signingSecret: "",
      defaultChannel: "#general"
    }
  })

  // Test email form
  const testEmailForm = useForm<TestEmailFormValues>({
    resolver: zodResolver(testEmailFormSchema),
    defaultValues: {
      testEmail: ""
    }
  })

  // Test Slack form
  const testSlackForm = useForm<TestSlackFormValues>({
    resolver: zodResolver(testSlackFormSchema),
    defaultValues: {
      testMessage: "This is a test message from HiRafi AI"
    }
  })

  // AI Model form
  const aiModelForm = useForm<AIModelFormValues>({
    resolver: zodResolver(aiModelFormSchema),
    defaultValues: {
      name: "",
      api: "",
      apiKey: "",
      isActive: true,
      supportsImageProcessing: true
    }
  })

  // Landing Page form
  const landingPageForm = useForm<LandingPageFormValues>({
    resolver: zodResolver(landingPageFormSchema),
    defaultValues: {
      adminEmails: "",
      emailSubject: "New Meeting Request from Landing Page",
      emailTemplate: `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>New Meeting Request</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: linear-gradient(to right, #4f46e5, #7c3aed); padding: 20px; border-radius: 8px 8px 0 0; }
    .header h1 { color: white; margin: 0; font-size: 24px; }
    .content { background: #fff; padding: 20px; border-radius: 0 0 8px 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    .field { margin-bottom: 10px; }
    .field strong { font-weight: bold; color: #4f46e5; }
    .footer { margin-top: 20px; font-size: 12px; color: #6b7280; text-align: center; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>New Meeting Request</h1>
    </div>
    <div class="content">
      <p>You have received a new meeting request with the following details:</p>

      <div class="field"><strong>Name:</strong> {{name}}</div>
      <div class="field"><strong>Email:</strong> {{email}}</div>
      <div class="field"><strong>Company:</strong> {{company}}</div>
      <div class="field"><strong>Phone:</strong> {{phone}}</div>
      <div class="field"><strong>Preferred Date:</strong> {{preferredDate}}</div>
      <div class="field"><strong>Message:</strong> {{message}}</div>

      <div class="footer">
        This is an automated message from HiRafi AI.
      </div>
    </div>
  </div>
</body>
</html>`,
      thankYouMessage: "Thank you for your interest! We will contact you shortly."
    }
  })

  // Load settings
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true)
      try {
        // Load SMTP settings
        const smtpResult = await getSystemSettingByType("smtp")
        if (smtpResult.success && smtpResult.setting) {
          setSmtpSetting(smtpResult.setting)
          smtpForm.reset(smtpResult.setting.config)
        }

        // Load Slack settings
        const slackResult = await getSystemSettingByType("slack")
        if (slackResult.success && slackResult.setting) {
          setSlackSetting(slackResult.setting)
          slackForm.reset(slackResult.setting.config)
        }

        // Load AI Model settings
        const aiModelResult = await getSystemSettingByType("ai_model")
        if (aiModelResult.success && aiModelResult.setting) {
          setAiModelSetting(aiModelResult.setting)
          aiModelForm.reset(aiModelResult.setting.config)
        }

        // Load Landing Page settings
        const landingPageResult = await getSystemSettingByType("landing_page")
        if (landingPageResult.success && landingPageResult.setting) {
          setLandingPageSetting(landingPageResult.setting)
          landingPageForm.reset(landingPageResult.setting.config)
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load system settings",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [])

  // Save SMTP settings
  const onSaveSMTP = async (data: SMTPFormValues) => {
    setIsSaving(true)
    try {
      let result
      if (smtpSetting) {
        // Update existing setting
        result = await updateSystemSetting(smtpSetting.id, {
          name: "SMTP Configuration",
          config: data,
          isActive: true
        })
      } else {
        // Create new setting
        result = await createSystemSetting({
          type: "smtp",
          name: "SMTP Configuration",
          config: data,
          isActive: true
        })
      }

      if (result.success) {
        setSmtpSetting(result.setting || result.data?.setting)
        toast({
          title: "Success",
          description: "SMTP settings saved successfully",
          variant: "success"
        })
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save SMTP settings",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save SMTP settings",
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Save Slack settings
  const onSaveSlack = async (data: SlackFormValues) => {
    setIsSaving(true)
    try {
      let result
      if (slackSetting) {
        // Update existing setting
        result = await updateSystemSetting(slackSetting.id, {
          name: "Slack Bot Configuration",
          config: data,
          isActive: true
        })
      } else {
        // Create new setting
        result = await createSystemSetting({
          type: "slack",
          name: "Slack Bot Configuration",
          config: data,
          isActive: true
        })
      }

      if (result.success) {
        setSlackSetting(result.setting || result.data?.setting)
        toast({
          title: "Success",
          description: "Slack settings saved successfully",
          variant: "success"
        })
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save Slack settings",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save Slack settings",
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Test SMTP configuration
  const onTestSMTP = async (data: TestEmailFormValues) => {
    setIsTesting(true)
    try {
      const testData = {
        testEmail: data.testEmail
      }

      if (smtpSetting) {
        testData.settingId = smtpSetting.id
      } else {
        testData.config = smtpForm.getValues()
      }

      const result = await testSMTPConfig(testData)

      if (result.success) {
        toast({
          title: "Success",
          description: `Test email sent to ${data.testEmail}`,
          variant: "success"
        })
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to send test email",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test SMTP configuration",
        variant: "destructive"
      })
    } finally {
      setIsTesting(false)
    }
  }

  // Test Slack configuration
  const onTestSlack = async (data: TestSlackFormValues) => {
    setIsTesting(true)
    try {
      const testData = {
        testMessage: data.testMessage
      }

      if (slackSetting) {
        testData.settingId = slackSetting.id
      } else {
        testData.config = slackForm.getValues()
      }

      const result = await testSlackConfig(testData)

      if (result.success) {
        toast({
          title: "Success",
          description: "Test message sent to Slack",
          variant: "success"
        })
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to send test message",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test Slack configuration",
        variant: "destructive"
      })
    } finally {
      setIsTesting(false)
    }
  }

  // Save AI Model settings
  const onSaveAIModel = async (data: AIModelFormValues) => {
    setIsSaving(true)
    try {
      let result
      if (aiModelSetting) {
        // Update existing setting
        result = await updateSystemSetting(aiModelSetting.id, {
          name: "Default AI Model",
          config: data,
          isActive: true
        })
      } else {
        // Create new setting
        result = await createSystemSetting({
          type: "ai_model",
          name: "Default AI Model",
          config: data,
          isActive: true
        })
      }

      if (result.success) {
        setAiModelSetting(result.setting || result.data?.setting)
        toast({
          title: "Success",
          description: "AI Model settings saved successfully",
          variant: "success"
        })
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save AI Model settings",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save AI Model settings",
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Save Landing Page settings
  const onSaveLandingPage = async (data: LandingPageFormValues) => {
    setIsSaving(true)
    try {
      let result
      if (landingPageSetting) {
        // Update existing setting
        result = await updateSystemSetting(landingPageSetting.id, {
          name: "Landing Page Settings",
          config: data,
          isActive: true
        })
      } else {
        // Create new setting
        result = await createSystemSetting({
          type: "landing_page",
          name: "Landing Page Settings",
          config: data,
          isActive: true
        })
      }

      if (result.success) {
        setLandingPageSetting(result.setting || result.data?.setting)
        toast({
          title: "Success",
          description: "Landing Page settings saved successfully",
          variant: "success"
        })
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to save Landing Page settings",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save Landing Page settings",
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <AdminRouteGuard>
      <AdminLayout>
        <div className="flex flex-col gap-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
            <p className="text-muted-foreground">
              Configure system-wide settings for notifications and integrations
            </p>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="smtp" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                SMTP Configuration
              </TabsTrigger>
              <TabsTrigger value="slack" className="flex items-center gap-2">
                <Slack className="h-4 w-4" />
                Slack Integration
              </TabsTrigger>
              <TabsTrigger value="ai_model" className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                Default AI Model
              </TabsTrigger>
              <TabsTrigger value="landing_page" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Landing Schedule
              </TabsTrigger>
            </TabsList>

            {/* SMTP Configuration Tab */}
            <TabsContent value="smtp" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Email Server Configuration</CardTitle>
                  <CardDescription>
                    Configure the SMTP server for sending email notifications
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <Form {...smtpForm}>
                      <form onSubmit={smtpForm.handleSubmit(onSaveSMTP)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={smtpForm.control}
                            name="host"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>SMTP Host</FormLabel>
                                <FormControl>
                                  <Input placeholder="smtp.example.com" {...field} />
                                </FormControl>
                                <FormDescription>
                                  The hostname of your SMTP server
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={smtpForm.control}
                            name="port"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>SMTP Port</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    placeholder="587"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value))}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The port of your SMTP server (usually 25, 465, or 587)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={smtpForm.control}
                            name="secure"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Use SSL/TLS</FormLabel>
                                  <FormDescription>
                                    Enable secure connection (usually for port 465)
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <div className="md:col-span-2">
                            <h3 className="text-lg font-medium mb-4">Authentication</h3>
                          </div>

                          <FormField
                            control={smtpForm.control}
                            name="auth.user"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Username</FormLabel>
                                <FormControl>
                                  <Input placeholder="username" {...field} />
                                </FormControl>
                                <FormDescription>
                                  The username for SMTP authentication
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={smtpForm.control}
                            name="auth.pass"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Password</FormLabel>
                                <FormControl>
                                  <Input
                                    type="password"
                                    placeholder="••••••••"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The password for SMTP authentication
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="md:col-span-2">
                            <h3 className="text-lg font-medium mb-4">Sender Information</h3>
                          </div>

                          <FormField
                            control={smtpForm.control}
                            name="from"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>From Email</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="<EMAIL>"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The email address that will appear in the From field
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={smtpForm.control}
                            name="fromName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>From Name</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="HiRafi AI"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The name that will appear in the From field
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end gap-2">
                          <Button
                            type="submit"
                            disabled={isSaving}
                            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                          >
                            {isSaving ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Save Configuration
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Test Email Configuration</CardTitle>
                  <CardDescription>
                    Send a test email to verify your SMTP configuration
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...testEmailForm}>
                    <form onSubmit={testEmailForm.handleSubmit(onTestSMTP)} className="space-y-6">
                      <FormField
                        control={testEmailForm.control}
                        name="testEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Test Email Address</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="<EMAIL>"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Enter an email address to receive the test email
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button
                          type="submit"
                          disabled={isTesting || !smtpForm.formState.isValid}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          {isTesting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Sending...
                            </>
                          ) : (
                            <>
                              <Send className="mr-2 h-4 w-4" />
                              Send Test Email
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Slack Integration Tab */}
            <TabsContent value="slack" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Slack Bot Configuration</CardTitle>
                  <CardDescription>
                    Configure the Slack bot for sending notifications to your Slack workspace
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <Form {...slackForm}>
                      <form onSubmit={slackForm.handleSubmit(onSaveSlack)} className="space-y-6">
                        <div className="grid grid-cols-1 gap-6">
                          <FormField
                            control={slackForm.control}
                            name="botToken"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Bot Token</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="xoxb-your-token"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The bot token from your Slack app (starts with xoxb-)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={slackForm.control}
                            name="signingSecret"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Signing Secret</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="your-signing-secret"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The signing secret from your Slack app
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={slackForm.control}
                            name="defaultChannel"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Default Channel</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="#general"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The default channel for notifications (e.g., #general)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end gap-2">
                          <Button
                            type="submit"
                            disabled={isSaving}
                            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                          >
                            {isSaving ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Save Configuration
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Test Slack Configuration</CardTitle>
                  <CardDescription>
                    Send a test message to verify your Slack configuration
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form {...testSlackForm}>
                    <form onSubmit={testSlackForm.handleSubmit(onTestSlack)} className="space-y-6">
                      <FormField
                        control={testSlackForm.control}
                        name="testMessage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Test Message</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="This is a test message from HiRafi AI"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Enter a message to send to your Slack workspace
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button
                          type="submit"
                          disabled={isTesting || !slackForm.formState.isValid}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          {isTesting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Sending...
                            </>
                          ) : (
                            <>
                              <Send className="mr-2 h-4 w-4" />
                              Send Test Message
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>

            {/* AI Model Configuration Tab */}
            <TabsContent value="ai_model" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Default AI Model Configuration</CardTitle>
                  <CardDescription>
                    Configure the default AI model that will be added to new companies
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <Form {...aiModelForm}>
                      <form onSubmit={aiModelForm.handleSubmit(onSaveAIModel)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={aiModelForm.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Model Name</FormLabel>
                                <FormControl>
                                  <Input placeholder="Gemini Pro" {...field} />
                                </FormControl>
                                <FormDescription>
                                  The name of the AI model
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={aiModelForm.control}
                            name="api"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>API Endpoint</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="https://api.example.com/v1"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The API endpoint for the AI model
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={aiModelForm.control}
                            name="apiKey"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>API Key</FormLabel>
                                <FormControl>
                                  <Input
                                    type="password"
                                    placeholder="••••••••"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The API key for authentication
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="md:col-span-2 space-y-4">
                            <FormField
                              control={aiModelForm.control}
                              name="isActive"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">Active</FormLabel>
                                    <FormDescription>
                                      Enable this AI model as the default
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={aiModelForm.control}
                              name="supportsImageProcessing"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel className="text-base">Supports Image Processing</FormLabel>
                                    <FormDescription>
                                      Enable if this AI model can process images
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        <div className="flex justify-end gap-2">
                          <Button
                            type="submit"
                            disabled={isSaving}
                            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                          >
                            {isSaving ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Save Configuration
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Landing Page Configuration Tab */}
            <TabsContent value="landing_page" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Landing Schedule Configuration</CardTitle>
                  <CardDescription>
                    Configure settings for the landing page meeting schedule form and email notifications
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  ) : (
                    <Form {...landingPageForm}>
                      <form onSubmit={landingPageForm.handleSubmit(onSaveLandingPage)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={landingPageForm.control}
                            name="adminEmails"
                            render={({ field }) => (
                              <FormItem className="md:col-span-2">
                                <FormLabel>Admin Emails</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="<EMAIL>, <EMAIL>"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Comma-separated list of email addresses to receive form submissions
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={landingPageForm.control}
                            name="emailSubject"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email Subject</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="New Meeting Request from Landing Page"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Subject line for notification emails
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={landingPageForm.control}
                            name="thankYouMessage"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Thank You Message</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Thank you for your interest! We will contact you shortly."
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Message to display after form submission
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={landingPageForm.control}
                          name="emailTemplate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email Template</FormLabel>
                              <FormControl>
                                <HtmlEditor
                                  value={field.value}
                                  onChange={field.onChange}
                                  placeholder="HTML email template"
                                />
                              </FormControl>
                              <FormDescription>
                                HTML template for notification emails. Use {`{{name}}, {{email}}, {{company}}, {{phone}}, {{message}}, and {{preferredDate}}`} as placeholders.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="flex justify-end gap-2">
                          <Button
                            type="submit"
                            disabled={isSaving}
                            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                          >
                            {isSaving ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Save Configuration
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  )
}
