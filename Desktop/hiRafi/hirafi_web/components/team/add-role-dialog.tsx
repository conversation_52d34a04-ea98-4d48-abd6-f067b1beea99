"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Loader2 } from "lucide-react"
import type { Role } from "@/types/team"
import type { Permission } from "@/types/permissions"
import { predefinedPermissions } from "@/types/permissions"
import { v4 as uuidv4 } from "uuid"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"

interface AddRoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAddRole: (role: Role) => void
}



// Available colors for roles
const roleColors = [
  "blue",
  "green",
  "purple",
  "amber",
  "red",
  "indigo",
  "pink",
  "teal",
]

export function AddRoleDialog({ open, onOpenChange, onAddRole }: AddRoleDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color: roleColors[0],
    selectedPermissions: [] as string[],
  })
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [activeTab, setActiveTab] = useState("folders") // Varsayılan sekme

  const handleChange = (field: string, value: string | string[]) => {
    setFormData({ ...formData, [field]: value })

    // Clear error when field is edited
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const handlePermissionToggle = (permissionId: string) => {
    const selectedPermissions = [...formData.selectedPermissions]

    if (selectedPermissions.includes(permissionId)) {
      handleChange(
        "selectedPermissions",
        selectedPermissions.filter((id) => id !== permissionId),
      )
    } else {
      handleChange("selectedPermissions", [...selectedPermissions, permissionId])
    }
  }

  const handleSelectAllModulePermissions = (module: string, checked: boolean) => {
    const modulePermissionIds = predefinedPermissions[module]
      .map((p) => p.id)
      .filter((id): id is string => id !== undefined);

    let newSelectedPermissions = [...formData.selectedPermissions]

    if (checked) {
      // Add all module permissions that aren't already selected
      modulePermissionIds.forEach((id) => {
        if (!newSelectedPermissions.includes(id)) {
          newSelectedPermissions.push(id)
        }
      })
    } else {
      // Remove all module permissions
      newSelectedPermissions = newSelectedPermissions.filter((id) => !modulePermissionIds.includes(id))
    }

    handleChange("selectedPermissions", newSelectedPermissions)
  }

  const isAllModulePermissionsSelected = (module: string) => {
    const modulePermissionIds = predefinedPermissions[module]
      .map((p) => p.id)
      .filter((id): id is string => id !== undefined);

    return modulePermissionIds.every((id) => formData.selectedPermissions.includes(id))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Rol adı gereklidir"
    }

    if (formData.selectedPermissions.length === 0) {
      newErrors.permissions = "En az bir yetki seçilmelidir"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Get full permission objects from selected IDs and convert to required format
      const permissions: Permission[] = []
      Object.values(predefinedPermissions).forEach((modulePermissions) => {
        modulePermissions.forEach((permission) => {
          if (permission.id && formData.selectedPermissions.includes(permission.id)) {
            permissions.push({
              resource: permission.resource,
              action: permission.action,
              // Keep original fields for UI display but make them optional
              id: permission.id,
              name: permission.name,
              description: permission.description,
              module: permission.module
            })
          }
        })
      })

      const newRole: Role = {
        id: uuidv4(),
        name: formData.name,
        description: formData.description,
        permissions,
        color: formData.color
      }

      onAddRole(newRole)
      onOpenChange(false)

      // Reset form
      setFormData({
        name: "",
        description: "",
        color: roleColors[0],
        selectedPermissions: [],
      })
    } catch (error) {
      console.error("Failed to add role:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Yeni Rol Ekle</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Rol Adı</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder="Rol adı giriniz"
              disabled={isLoading}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Açıklama</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange("description", e.target.value)}
              placeholder="Rol açıklaması giriniz"
              disabled={isLoading}
            />
          </div>

          <div className="grid gap-2">
            <Label>Renk</Label>
            <div className="flex flex-wrap gap-2">
              {roleColors.map((color, index) => (
                <div
                  key={index}
                  className={`w-8 h-8 rounded-full cursor-pointer border-2 ${
                    formData.color === color ? "border-black dark:border-white" : "border-transparent"
                  }`}
                  style={{ backgroundColor: color.includes("bg-") ? "" : color }}
                  onClick={() => handleChange("color", color)}
                >
                  <span className={`block w-full h-full rounded-full bg-${color}-500`}></span>
                </div>
              ))}
            </div>
          </div>

          <div className="grid gap-2">
            <Label>Yetkiler</Label>
            {errors.permissions && <p className="text-sm text-red-500">{errors.permissions}</p>}

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-5">
                <TabsTrigger value="folders">Klasörler</TabsTrigger>
                <TabsTrigger value="scenarios">Senaryolar</TabsTrigger>
                <TabsTrigger value="runs">Koşumlar</TabsTrigger>
                <TabsTrigger value="reports">Raporlar</TabsTrigger>
                <TabsTrigger value="Schedule">Schedule</TabsTrigger>
              </TabsList>

              {Object.keys(predefinedPermissions).map((module) => (
                <TabsContent key={module} value={module} className="border rounded-md p-4">
                  <div className="flex items-center space-x-2 mb-4">
                    <Checkbox
                      id={`select-all-${module}`}
                      checked={isAllModulePermissionsSelected(module)}
                      onCheckedChange={(checked) => handleSelectAllModulePermissions(module, checked === true)}
                    />
                    <Label htmlFor={`select-all-${module}`} className="font-medium">
                      Tümünü Seç
                    </Label>
                  </div>

                  <ScrollArea className="h-[200px] pr-4">
                    <div className="space-y-4">
                      {predefinedPermissions[module].map((permission) => (
                        <div key={permission.id || `${module}-${permission.action}`} className="flex items-start space-x-2">
                          <Checkbox
                            id={permission.id || `${module}-${permission.action}`}
                            checked={permission.id ? formData.selectedPermissions.includes(permission.id) : false}
                            onCheckedChange={() => permission.id && handlePermissionToggle(permission.id)}
                          />
                          <div className="grid gap-1">
                            <Label htmlFor={permission.id || `${module}-${permission.action}`} className="font-medium">
                              {permission.name}
                            </Label>
                            <p className="text-sm text-gray-500">{permission.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Ekleniyor...
              </>
            ) : (
              "Ekle"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
