/**
 * Configuration Manager Interface
 * 
 * Defines the contract for managing AI and test execution configuration.
 * This interface handles configuration application and management.
 */

import { TestPlatform } from '../../../models/platform-types.js';

/**
 * AI configuration object
 */
export interface AIConfiguration {
  OPENAI_BASE_URL?: string;
  OPENAI_API_KEY?: string;
  MIDSCENE_MODEL_NAME?: string;
  MIDSCENE_CACHE?: string;
  MIDSCENE_USE_VLM_UI_TARS?: string;
  MIDSCENE_USE_QWEN_VL?: string;
  MIDSCENE_USE_GEMINI?: string;
  [key: string]: string | undefined;
}

/**
 * Configuration application result
 */
export interface ConfigurationResult {
  success: boolean;
  appliedConfiguration: AIConfiguration;
  errors: Error[];
  platform: TestPlatform;
}

/**
 * Configuration manager options
 */
export interface ConfigurationManagerOptions {
  enableCaching?: boolean;
  configurationTimeout?: number;
  fallbackToEnvironment?: boolean;
}

/**
 * Interface for managing test execution configuration
 * 
 * This interface provides configuration management for AI models and
 * test execution settings across different platforms.
 */
export interface IConfigurationManager {
  /**
   * Initialize the configuration manager
   * @param options Configuration manager options
   * @returns Promise that resolves when initialization is complete
   */
  initialize(options?: ConfigurationManagerOptions): Promise<void>;

  /**
   * Apply AI configuration for test execution
   * @param testRequest Test request containing environment settings
   * @param platform Platform type (web/android)
   * @returns Promise resolving to configuration result
   */
  applyAIConfiguration(testRequest: any, platform: TestPlatform): Promise<ConfigurationResult>;

  /**
   * Build configuration with model-specific settings
   * @param baseConfig Base AI configuration
   * @returns Enhanced configuration with model-specific flags
   */
  buildConfiguration(baseConfig: any): AIConfiguration;

  /**
   * Apply configuration for web platform
   * @param config Configuration object
   * @returns Promise that resolves when configuration is applied
   */
  applyWebConfiguration(config: AIConfiguration): Promise<void>;

  /**
   * Apply configuration for Android platform
   * @param config Configuration object
   * @returns Promise that resolves when configuration is applied
   */
  applyAndroidConfiguration(config: AIConfiguration): Promise<void>;

  /**
   * Apply configuration using environment variables (fallback)
   * @param config Configuration object
   */
  applyEnvironmentConfiguration(config: AIConfiguration): void;

  /**
   * Get current configuration for a platform
   * @param platform Platform type
   * @returns Current configuration or null if not set
   */
  getCurrentConfiguration(platform: TestPlatform): AIConfiguration | null;

  /**
   * Validate configuration object
   * @param config Configuration to validate
   * @returns True if configuration is valid, false otherwise
   */
  validateConfiguration(config: any): boolean;

  /**
   * Reset configuration for a platform
   * @param platform Platform type
   * @returns Promise that resolves when reset is complete
   */
  resetConfiguration(platform?: TestPlatform): Promise<void>;

  /**
   * Check if configuration manager is initialized
   * @returns True if initialized, false otherwise
   */
  isInitialized(): boolean;

  /**
   * Dispose of the configuration manager
   * @returns Promise that resolves when disposal is complete
   */
  dispose(): Promise<void>;
}
