/**
 * Unified Test Management Utilities
 * 
 * Utility functions for working with unified test management integration patterns
 */

import { TestManagementProvider } from '@/store/testManagementStore'
import { Scenario, TestRailIntegration, ZephyrScaleIntegration } from '@/models/scenario'
import { ExtendedScenarioFormData } from './scenario-form-utils'

/**
 * Unified Test Management Integration Interface
 * Represents a unified view of test management integration regardless of provider
 */
export interface UnifiedTestManagementIntegration {
  provider: TestManagementProvider
  caseIds: string[]
  sync: boolean
  isActive: boolean
}

/**
 * Test Management Configuration for Scenarios
 */
export interface ScenarioTestManagementConfig {
  hasIntegration: boolean
  activeProvider: TestManagementProvider | null
  integrations: UnifiedTestManagementIntegration[]
  totalCases: number
}

/**
 * Get unified test management configuration from scenario data
 */
export function getScenarioTestManagementConfig(scenario: Scenario): ScenarioTestManagementConfig {
  const integrations: UnifiedTestManagementIntegration[] = []
  
  // TestRail integration
  if (scenario.testrailIntegration) {
    integrations.push({
      provider: 'testrail',
      caseIds: (scenario.testrailIntegration.caseIds || []).map(id => String(id)),
      sync: scenario.testrailIntegration.sync,
      isActive: scenario.testrailIntegration.sync && (scenario.testrailIntegration.caseIds || []).length > 0
    })
  }
  
  // Zephyr Scale integration
  if (scenario.zephyrscaleIntegration) {
    integrations.push({
      provider: 'zephyrscale',
      caseIds: scenario.zephyrscaleIntegration.caseIds || [],
      sync: scenario.zephyrscaleIntegration.sync,
      isActive: scenario.zephyrscaleIntegration.sync && (scenario.zephyrscaleIntegration.caseIds || []).length > 0
    })
  }
  
  const activeIntegrations = integrations.filter(i => i.isActive)
  const activeProvider = activeIntegrations.length > 0 ? activeIntegrations[0].provider : null
  const totalCases = integrations.reduce((sum, integration) => sum + integration.caseIds.length, 0)
  
  return {
    hasIntegration: integrations.length > 0,
    activeProvider,
    integrations,
    totalCases
  }
}

/**
 * Get unified test management configuration from form data
 */
export function getFormDataTestManagementConfig(formData: ExtendedScenarioFormData): ScenarioTestManagementConfig {
  const integrations: UnifiedTestManagementIntegration[] = []
  
  // TestRail integration
  integrations.push({
    provider: 'testrail',
    caseIds: formData.testrailCases || [],
    sync: formData.testrailSync || false,
    isActive: (formData.testrailSync || false) && (formData.testrailCases || []).length > 0
  })
  
  // Zephyr Scale integration
  integrations.push({
    provider: 'zephyrscale',
    caseIds: formData.zephyrscaleCases || [],
    sync: formData.zephyrscaleSync || false,
    isActive: (formData.zephyrscaleSync || false) && (formData.zephyrscaleCases || []).length > 0
  })
  
  const activeIntegrations = integrations.filter(i => i.isActive)
  const activeProvider = activeIntegrations.length > 0 ? activeIntegrations[0].provider : null
  const totalCases = integrations.reduce((sum, integration) => sum + integration.caseIds.length, 0)
  
  return {
    hasIntegration: integrations.some(i => i.caseIds.length > 0),
    activeProvider,
    integrations,
    totalCases
  }
}

/**
 * Get test cases for a specific provider from scenario
 */
export function getTestCasesForProvider(scenario: Scenario, provider: TestManagementProvider): string[] {
  switch (provider) {
    case 'testrail':
      return (scenario.testrailIntegration?.caseIds || []).map(id => String(id))
    case 'zephyrscale':
      return scenario.zephyrscaleIntegration?.caseIds || []
    default:
      return []
  }
}

/**
 * Get test cases for a specific provider from form data
 */
export function getTestCasesForProviderFromFormData(formData: ExtendedScenarioFormData, provider: TestManagementProvider): string[] {
  switch (provider) {
    case 'testrail':
      return formData.testrailCases || []
    case 'zephyrscale':
      return formData.zephyrscaleCases || []
    default:
      return []
  }
}

/**
 * Check if sync is enabled for a specific provider in scenario
 */
export function isSyncEnabledForProvider(scenario: Scenario, provider: TestManagementProvider): boolean {
  switch (provider) {
    case 'testrail':
      return scenario.testrailIntegration?.sync || false
    case 'zephyrscale':
      return scenario.zephyrscaleIntegration?.sync || false
    default:
      return false
  }
}

/**
 * Check if sync is enabled for a specific provider in form data
 */
export function isSyncEnabledForProviderInFormData(formData: ExtendedScenarioFormData, provider: TestManagementProvider): boolean {
  switch (provider) {
    case 'testrail':
      return formData.testrailSync || false
    case 'zephyrscale':
      return formData.zephyrscaleSync || false
    default:
      return false
  }
}

/**
 * Get provider display name
 */
export function getProviderDisplayName(provider: TestManagementProvider): string {
  switch (provider) {
    case 'testrail':
      return 'TestRail'
    case 'zephyrscale':
      return 'Zephyr Scale'
    default:
      return 'Unknown Provider'
  }
}

/**
 * Get provider color scheme for UI
 */
export function getProviderColorScheme(provider: TestManagementProvider) {
  switch (provider) {
    case 'testrail':
      return {
        primary: 'blue',
        bg: 'bg-blue-50',
        text: 'text-blue-700',
        border: 'border-blue-200',
        hover: 'hover:bg-blue-100',
        dark: {
          bg: 'dark:bg-blue-900/20',
          text: 'dark:text-blue-300',
          border: 'dark:border-blue-800'
        }
      }
    case 'zephyrscale':
      return {
        primary: 'green',
        bg: 'bg-green-50',
        text: 'text-green-700',
        border: 'border-green-200',
        hover: 'hover:bg-green-100',
        dark: {
          bg: 'dark:bg-green-900/20',
          text: 'dark:text-green-300',
          border: 'dark:border-green-800'
        }
      }
    default:
      return {
        primary: 'gray',
        bg: 'bg-gray-50',
        text: 'text-gray-700',
        border: 'border-gray-200',
        hover: 'hover:bg-gray-100',
        dark: {
          bg: 'dark:bg-gray-900/20',
          text: 'dark:text-gray-300',
          border: 'dark:border-gray-800'
        }
      }
  }
}

/**
 * Validate test management integration configuration
 */
export function validateTestManagementIntegration(config: ScenarioTestManagementConfig): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Check for multiple active integrations
  const activeIntegrations = config.integrations.filter(i => i.isActive)
  if (activeIntegrations.length > 1) {
    warnings.push('Multiple test management integrations are active. Only the first one will be used for result synchronization.')
  }
  
  // Check for sync enabled without test cases
  config.integrations.forEach(integration => {
    if (integration.sync && integration.caseIds.length === 0) {
      warnings.push(`${getProviderDisplayName(integration.provider)} sync is enabled but no test cases are selected.`)
    }
  })
  
  // Check for test cases without sync enabled
  config.integrations.forEach(integration => {
    if (!integration.sync && integration.caseIds.length > 0) {
      warnings.push(`${getProviderDisplayName(integration.provider)} has test cases selected but sync is disabled.`)
    }
  })
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Get integration summary for display
 */
export function getIntegrationSummary(config: ScenarioTestManagementConfig): string {
  if (!config.hasIntegration) {
    return 'No test management integration'
  }
  
  const activeIntegrations = config.integrations.filter(i => i.isActive)
  if (activeIntegrations.length === 0) {
    return 'Test management configured but sync disabled'
  }
  
  if (activeIntegrations.length === 1) {
    const integration = activeIntegrations[0]
    return `${getProviderDisplayName(integration.provider)} (${integration.caseIds.length} test case${integration.caseIds.length !== 1 ? 's' : ''})`
  }
  
  return `Multiple integrations active (${config.totalCases} total test cases)`
}
