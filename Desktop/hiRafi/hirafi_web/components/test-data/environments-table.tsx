"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Globe, Plus, Edit, Settings, Trash2, MoreHorizontal, AlertCircle, RefreshCw, Loader2 } from "lucide-react"
import type { CustomEnvironment } from "@/types/test-data"

interface EnvironmentsTableProps {
  environments: CustomEnvironment[]
  onNewEnvironment: () => void
  onEditEnvironment: (environment: CustomEnvironment) => void
  onDeleteEnvironment: (id: string) => void
  onToggleEnvironmentStatus: (id: string) => void
  isLoading?: boolean
  isUpdatingEnvironment?: string | null
  error?: string | null
  onRetry?: () => void
}

export function EnvironmentsTable({
  environments,
  onNewEnvironment,
  onEditEnvironment,
  onDeleteEnvironment,
  onToggleEnvironmentStatus,
  isLoading = false,
  isUpdatingEnvironment = null,
  error = null,
  onRetry
}: EnvironmentsTableProps) {
  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
          <CardTitle>Environments</CardTitle>
          <CardDescription>Manage your test environments and their configurations</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-[100px]">Type</TableHead>
                  <TableHead className="w-[120px]">Status</TableHead>
                  <TableHead className="w-[100px]">Color</TableHead>
                  <TableHead className="w-[150px]">Created</TableHead>
                  <TableHead className="w-[150px]">Updated</TableHead>
                  <TableHead className="w-[100px] text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {[...Array(3)].map((_, i) => (
                  <TableRow key={i}>
                    <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>{error}</span>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  if (environments.length === 0) {
    return (
      <Card className="border-dashed">
        <CardContent className="pt-6 text-center">
          <Globe className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium">No Environments Found</h3>
          <p className="text-sm text-gray-500 mt-1 mb-4">No environments match your search criteria.</p>
          <Button
            onClick={onNewEnvironment}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Environment
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
        <CardTitle>Environments</CardTitle>
        <CardDescription>Manage your test environments and their configurations</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="w-[100px]">Type</TableHead>
                <TableHead className="w-[120px]">Status</TableHead>
                <TableHead className="w-[100px]">Color</TableHead>
                <TableHead className="w-[150px]">Created</TableHead>
                <TableHead className="w-[150px]">Updated</TableHead>
                <TableHead className="w-[100px] text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {environments.map((environment, index) => (
                <TableRow key={environment.id || `environment-${index}`} className="hover:bg-gray-50 dark:hover:bg-gray-900/50">
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full bg-${environment.color || 'gray'}-500 flex-shrink-0`}></div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">{environment.name || 'Unnamed Environment'}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                        {environment.description || "No description provided"}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={environment.type === "default" ? "secondary" : "outline"} className="text-xs">
                      {environment.type === "default" ? "Default" : "Custom"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-2 w-2 rounded-full ${
                          environment.isActive ? "bg-emerald-500" : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      ></div>
                      <span className="text-sm">{environment.isActive ? "Active" : "Inactive"}</span>
                      <div className="flex items-center gap-1 ml-1">
                        {isUpdatingEnvironment === environment.id ? (
                          <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                        ) : (
                          <Switch
                            checked={environment.isActive}
                            onCheckedChange={() => onToggleEnvironmentStatus(environment.id)}
                            size="sm"
                            disabled={isUpdatingEnvironment === environment.id}
                          />
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className={`w-4 h-4 rounded-full bg-${environment.color || 'gray'}-500`}></div>
                      <span className="text-sm capitalize">{environment.color || 'gray'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {new Date(environment.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-400 dark:text-gray-500">
                      {new Date(environment.createdAt).toLocaleTimeString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {new Date(environment.updatedAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-400 dark:text-gray-500">
                      {new Date(environment.updatedAt).toLocaleTimeString()}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => onEditEnvironment(environment)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => onToggleEnvironmentStatus(environment.id)}
                          disabled={isUpdatingEnvironment === environment.id}
                        >
                          {isUpdatingEnvironment === environment.id ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Settings className="h-4 w-4 mr-2" />
                          )}
                          {environment.isActive ? "Deactivate" : "Activate"}
                        </DropdownMenuItem>
                        {environment.type === "custom" && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => onDeleteEnvironment(environment.id)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
