/**
 * Result Queue Interface
 * Defines the contract for result queue services
 */

import { TestResult } from '../../../models/types.js';

/**
 * Result queue service interface
 */
export interface IResultQueueService {
  /**
   * Initialize the result queue service
   */
  initialize(): Promise<boolean>;

  /**
   * Add a test result to the queue
   * @param testId Test ID
   * @param result Test result data
   * @param nodeId Node ID that processed the test
   */
  addResult(testId: string, result: TestResult, nodeId: string): Promise<string | null>;

  /**
   * Get queue statistics
   */
  getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }>;

  /**
   * Check if the service is ready
   */
  isReady(): boolean;

  /**
   * Close the result queue service
   */
  close(): Promise<void>;
}

/**
 * Result queue configuration
 */
export interface ResultQueueConfig {
  /** Enable result queue */
  enabled: boolean;
  /** Maximum retry attempts */
  maxRetries: number;
  /** Retry delay in milliseconds */
  retryDelay: number;
  /** Enable logging */
  enableLogging: boolean;
}
