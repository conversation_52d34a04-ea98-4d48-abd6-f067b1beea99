/**
 * Expiry Storage Utility
 * Adds automatic expiration to localStorage data
 */

export interface ExpiryData<T = any> {
  data: T;
  timestamp: number;
  expiryMs: number;
}

export class ExpiryStorage {
  private static readonly DEFAULT_EXPIRY_MS = 60 * 60 * 1000; // 1 hour

  /**
   * Set data with expiry
   */
  static setItem<T>(key: string, data: T, expiryMs: number = ExpiryStorage.DEFAULT_EXPIRY_MS): void {
    if (typeof window === 'undefined') return;

    const expiryData: ExpiryData<T> = {
      data,
      timestamp: Date.now(),
      expiryMs
    };

    try {
      localStorage.setItem(key, JSON.stringify(expiryData));
    } catch (error) {
      console.warn(`[ExpiryStorage] Failed to set ${key}:`, error);
    }
  }

  /**
   * Get data and check if expired
   */
  static getItem<T>(key: string): T | null {
    if (typeof window === 'undefined') return null;

    try {
      const item = localStorage.getItem(key);
      if (!item) return null;

      const expiryData: ExpiryData<T> = JSON.parse(item);
      const now = Date.now();
      const isExpired = (now - expiryData.timestamp) > expiryData.expiryMs;

      if (isExpired) {
        // Auto-remove expired data
        ExpiryStorage.removeItem(key);
        console.log(`[ExpiryStorage] Auto-removed expired data: ${key}`);
        return null;
      }

      return expiryData.data;
    } catch (error) {
      console.warn(`[ExpiryStorage] Failed to get ${key}:`, error);
      // Remove corrupted data
      ExpiryStorage.removeItem(key);
      return null;
    }
  }

  /**
   * Remove item
   */
  static removeItem(key: string): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`[ExpiryStorage] Failed to remove ${key}:`, error);
    }
  }

  /**
   * Check if item exists and is not expired
   */
  static hasValidItem(key: string): boolean {
    return ExpiryStorage.getItem(key) !== null;
  }

  /**
   * Get remaining time until expiry in milliseconds
   */
  static getRemainingTime(key: string): number {
    if (typeof window === 'undefined') return 0;

    try {
      const item = localStorage.getItem(key);
      if (!item) return 0;

      const expiryData: ExpiryData = JSON.parse(item);
      const now = Date.now();
      const elapsed = now - expiryData.timestamp;
      const remaining = expiryData.expiryMs - elapsed;

      return Math.max(0, remaining);
    } catch (error) {
      return 0;
    }
  }

  /**
   * Cleanup all expired items with specific prefix
   */
  static cleanupExpiredItems(prefix?: string): number {
    if (typeof window === 'undefined') return 0;

    let cleanedCount = 0;
    const keysToCheck: string[] = [];

    // Collect keys to check
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (!prefix || key.startsWith(prefix))) {
        keysToCheck.push(key);
      }
    }

    // Check and cleanup expired items
    keysToCheck.forEach(key => {
      try {
        const item = localStorage.getItem(key);
        if (!item) return;

        const expiryData: ExpiryData = JSON.parse(item);
        const now = Date.now();
        const isExpired = (now - expiryData.timestamp) > expiryData.expiryMs;

        if (isExpired) {
          localStorage.removeItem(key);
          cleanedCount++;
        }
      } catch (error) {
        // Remove corrupted data
        localStorage.removeItem(key);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`[ExpiryStorage] Cleaned up ${cleanedCount} expired/corrupted items`);
    }

    return cleanedCount;
  }
}

/**
 * Company-aware expiry storage
 */
export class CompanyExpiryStorage extends ExpiryStorage {
  /**
   * Generate company-specific key
   */
  private static getCompanyKey(baseKey: string): string {
    if (typeof window === 'undefined') return baseKey;
    
    try {
      const userStr = localStorage.getItem('user');
      const user = userStr ? JSON.parse(userStr) : null;
      const companyId = user?.companyId;
      
      return companyId ? `${baseKey}-company-${companyId}` : baseKey;
    } catch {
      return baseKey;
    }
  }

  /**
   * Set company-specific data with expiry
   */
  static setItem<T>(baseKey: string, data: T, expiryMs?: number): void {
    const companyKey = CompanyExpiryStorage.getCompanyKey(baseKey);
    super.setItem(companyKey, data, expiryMs);
  }

  /**
   * Get company-specific data
   */
  static getItem<T>(baseKey: string): T | null {
    const companyKey = CompanyExpiryStorage.getCompanyKey(baseKey);
    return super.getItem<T>(companyKey);
  }

  /**
   * Remove company-specific data
   */
  static removeItem(baseKey: string): void {
    const companyKey = CompanyExpiryStorage.getCompanyKey(baseKey);
    super.removeItem(companyKey);
  }

  /**
   * Cleanup all expired company-specific items
   */
  static cleanupCompanyExpiredItems(): number {
    if (typeof window === 'undefined') return 0;
    
    try {
      const userStr = localStorage.getItem('user');
      const user = userStr ? JSON.parse(userStr) : null;
      const companyId = user?.companyId;
      
      if (!companyId) return 0;
      
      return super.cleanupExpiredItems(`-company-${companyId}`);
    } catch {
      return 0;
    }
  }
} 