/**
 * Test Execution Module - Refactored Architecture
 *
 * This module provides a refactored test execution architecture with improved
 * separation of concerns, dependency injection, and modular design.
 *
 * Key improvements:
 * - Single Responsibility Principle: Each class has one clear responsibility
 * - Dependency Injection: No more singleton patterns, proper DI throughout
 * - Interface Segregation: Clear interfaces for each service
 * - Modular Architecture: Logical grouping of related functionality
 * - Testability: Easy to mock and test individual components
 */

// Factories
export * from './factories/index.js';
export { TestRunnerFactory as TestExecutionTestRunnerFactory } from './factories/test-runner-factory.js';

// Core Components
export { ExecutionCoordinator as TestExecutionManager } from './core/execution-coordinator.js';

// Interfaces
export type { IExecutionCoordinator, TestExecutionEvents, TestExecutionOptions, TestExecutionStatus } from './interfaces/execution-coordinator.interface.js';
export type { IStateManager, StateTransitionResult } from './interfaces/state-manager.interface.js';
export type { IResourceManager, ResourceCleanupResult } from './interfaces/resource-manager.interface.js';
export type { IConfigurationManager, ConfigurationResult } from './interfaces/configuration-manager.interface.js';
export type { ICleanupOrchestrator, CleanupResult } from './interfaces/cleanup-orchestrator.interface.js';
export type { ITestRunner } from './interfaces/test-runner.interface.js';
export type { ITestProgressReporter } from './interfaces/test-progress-reporter.interface.js';

// Factory Types
export type { Platform, TestRunnerDependencies } from './factories/index.js';
