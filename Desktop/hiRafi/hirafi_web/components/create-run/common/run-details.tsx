"use client"

import { useState, useEffect, useCallback } from "react"
import { useRunForm } from "../run-context"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { X, AlertCircle, Check, Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { PlatformSelector } from "./platform-selector"

export function RunDetails() {
  const { formData, updateFormData, errors } = useRunForm()
  const [newTag, setNewTag] = useState("")
  const [localName, setLocalName] = useState(formData.name || "")
  const [localDescription, setLocalDescription] = useState(formData.description || "")

  // formData değiştiğinde yerel state'i güncelle (sadece dışarıdan değişiklik olduğunda)
  useEffect(() => {
    // Only update if the change didn't come from this component (initialization or external change)
    if (formData.name !== undefined && formData.name !== localName) {
      setLocalName(formData.name);
    }

    if (formData.description !== undefined && formData.description !== localDescription) {
      setLocalDescription(formData.description);
    }
  // Remove localName and localDescription from dependencies to prevent infinite loops
  }, [formData.name, formData.description]);

  // İsim alanı değiştiğinde sadece lokal state'i güncelle
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalName(e.target.value);
  }

  // İsim alanı odağını kaybettiğinde form state'ini güncelle
  const handleNameBlur = useCallback(() => {
    const trimmedName = localName.trim();
    if (formData.name !== trimmedName) {
      updateFormData({ name: trimmedName });
    }
  }, [localName, formData.name, updateFormData]);

  // Açıklama alanı değiştiğinde sadece lokal state'i güncelle
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setLocalDescription(e.target.value);
  }

  // Açıklama alanı odağını kaybettiğinde form state'ini güncelle
  const handleDescriptionBlur = useCallback(() => {
    if (formData.description !== localDescription) {
      updateFormData({ description: localDescription });
    }
  }, [localDescription, formData.description, updateFormData]);

  // Yeni tag ekle
  const addTag = useCallback(() => {
    if (newTag.trim() && (!formData.tags || !formData.tags.includes(newTag.trim()))) {
      const updatedTags = [...(formData.tags || []), newTag.trim()]
      updateFormData({ tags: updatedTags })
      setNewTag("")
    }
  }, [newTag, formData.tags, updateFormData]);

  // Tag sil
  const removeTag = useCallback((tag: string) => {
    const updatedTags = (formData.tags || []).filter(t => t !== tag)
    updateFormData({ tags: updatedTags })
  }, [formData.tags, updateFormData]);

  // Tag ekleme için Enter tuşu
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addTag()
    }
  }, [addTag]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main content - left column */}
      <div className="lg:col-span-2">
        <Card className="overflow-hidden border-0 shadow-md">
          <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/40 dark:to-purple-950/40 border-b">
            {/* Removed duplicate heading - the parent component already displays the section title */}
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            {/* Run Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className={errors.name ? "text-red-500" : ""}>
                Run Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                placeholder="Enter a descriptive name for your test run"
                value={localName}
                onChange={handleNameChange}
                onBlur={handleNameBlur}
                className={errors.name ? "border-red-500 focus-visible:ring-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            {/* Run Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe the purpose of this test run (optional)"
                value={localDescription}
                onChange={handleDescriptionChange}
                onBlur={handleDescriptionBlur}
                rows={3}
              />
            </div>

            {/* Platform Selection - Now using the modular component */}
            <PlatformSelector />

            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <div className="flex gap-2">
                <Input
                  id="tags"
                  placeholder="Add tags to categorize your run"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
                <Button
                  type="button"
                  onClick={addTag}
                  variant="outline"
                  className="shrink-0"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add
                </Button>
              </div>

              {formData.tags && formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="px-2 py-1 flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tips - right column */}
      <div className="space-y-6">
        <Card className="overflow-hidden border-0 shadow-md p-5">
          <h3 className="font-semibold text-lg mb-4 flex items-center text-indigo-700 dark:text-indigo-300">
            <AlertCircle className="h-5 w-5 mr-2" />
            How to Create a Run
          </h3>
          <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <li className="flex items-start gap-2">
              <div className="mt-0.5 rounded-full bg-emerald-100 p-1 dark:bg-emerald-900/30">
                <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
              </div>
              <span>Give your run a descriptive name and description</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 rounded-full bg-emerald-100 p-1 dark:bg-emerald-900/30">
                <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
              </div>
              <span>Add relevant tags to make runs searchable</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 rounded-full bg-emerald-100 p-1 dark:bg-emerald-900/30">
                <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
              </div>
              <span>In the next step, you'll select scenarios to include in this run</span>
            </li>
          </ul>
        </Card>
      </div>
    </div>
  )
}
