"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Check, Link, Lock, Clock, Share2, X, Loader2 } from 'lucide-react'
import { toast } from '@/lib/utils/toast-utils'
import { enableReportSharing, disableReportSharing, getReportById } from '@/lib/api/report-api'

interface ShareReportModalProps {
  isOpen: boolean
  onClose: () => void
  reportId: string
  reportName: string
}

export function ShareReportModal({ isOpen, onClose, reportId, reportName }: ShareReportModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isInitializing, setIsInitializing] = useState(false)
  const [activeTab, setActiveTab] = useState('settings')
  const [shareEnabled, setShareEnabled] = useState(false)
  const [expiresIn, setExpiresIn] = useState<string | null>('7')
  const [password, setPassword] = useState('')
  const [allowComments, setAllowComments] = useState(false)
  const [shareUrl, setShareUrl] = useState('')
  const [copied, setCopied] = useState(false)

  // Modal açıldığında rapor paylaşım durumunu kontrol et
  useEffect(() => {
    if (isOpen && reportId) {
      checkReportSharingStatus();
    }
  }, [isOpen, reportId]);

  // Rapor paylaşım durumunu kontrol et
  const checkReportSharingStatus = async () => {
    setIsInitializing(true);

    try {
      console.log('ShareReportModal: Fetching report details for ID:', reportId);
      const result = await getReportById(reportId);
      console.log('ShareReportModal: API response:', result);

      // API yanıtı fetch-wrapper tarafından farklı formatlarda gelebilir
      if (result.success) {
        const report = result.report || result.data?.report;

        // Rapor paylaşım bilgilerini kontrol et
        if (report.sharing && report.sharing.enabled) {
          setShareEnabled(true);

          // Paylaşım URL'sini oluştur
          setShareUrl(`${window.location.origin}/reports/share/${report.sharing.token}`);

          // Paylaşım ayarlarını doldur
          if (report.sharing.expiresAt) {
            // Son kullanma tarihinden gün sayısını hesapla
            const expiresAt = new Date(report.sharing.expiresAt);
            const now = new Date();
            const diffTime = expiresAt.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // En yakın değeri seç
            if (diffDays <= 1) setExpiresIn('1');
            else if (diffDays <= 7) setExpiresIn('7');
            else if (diffDays <= 30) setExpiresIn('30');
            else setExpiresIn('90');
          } else {
            setExpiresIn('null'); // Süresiz
          }

          // Şifre ve yorum ayarlarını doldur
          setAllowComments(report.sharing.allowComments || false);
          setPassword(''); // Şifre hash'lenmiş olduğu için boş bırakıyoruz

          // Link sekmesini aktif et
          setActiveTab('link');
        } else {
          // Paylaşım etkin değilse varsayılan değerleri kullan
          resetSharingSettings();
        }
      } else {
        toast.error('Failed to get report details', {
          description: result.error || (result.data && result.data.error) || 'An unexpected error occurred'
        });
        resetSharingSettings();
      }
    } catch (error: any) {
      console.error('Error checking report sharing status:', error);
      toast.error('Failed to check report sharing status', {
        description: error.message || 'An unexpected error occurred'
      });
      resetSharingSettings();
    } finally {
      setIsInitializing(false);
    }
  };

  // Paylaşım ayarlarını sıfırla
  const resetSharingSettings = () => {
    setShareEnabled(false);
    setExpiresIn('7');
    setPassword('');
    setAllowComments(false);
    setShareUrl('');
    setActiveTab('settings');
  };

  // Paylaşım ayarlarını kaydet
  const handleSaveSettings = async () => {
    setIsLoading(true)

    try {
      const result = await enableReportSharing(reportId, {
        expiresIn: expiresIn ? parseInt(expiresIn) : null,
        password: password || undefined,
        allowComments
      })

      if (result.success) {
        setShareEnabled(true)
        setShareUrl(`${window.location.origin}/reports/share/${result.data.shareToken}`)
        setActiveTab('link')
        toast.success('Report sharing enabled successfully')
      } else {
        toast.error('Failed to enable report sharing', {
          description: result.error || 'An unexpected error occurred'
        })
      }
    } catch (error: any) {
      toast.error('Failed to enable report sharing', {
        description: error.message || 'An unexpected error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Paylaşımı devre dışı bırak
  const handleDisableSharing = async () => {
    setIsLoading(true)

    try {
      const result = await disableReportSharing(reportId)

      if (result.success) {
        setShareEnabled(false)
        setShareUrl('')
        setActiveTab('settings')
        toast.success('Report sharing disabled successfully')
      } else {
        toast.error('Failed to disable report sharing', {
          description: result.error || 'An unexpected error occurred'
        })
      }
    } catch (error: any) {
      toast.error('Failed to disable report sharing', {
        description: error.message || 'An unexpected error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Paylaşım linkini kopyala
  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareUrl)
    setCopied(true)
    toast.success('Link copied to clipboard')

    setTimeout(() => {
      setCopied(false)
    }, 2000)
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5 text-indigo-500" />
            Share Report
          </DialogTitle>
          <DialogDescription>
            Share this test report with others via a secure link
          </DialogDescription>
        </DialogHeader>

        {isInitializing ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-500 mb-4" />
            <p className="text-sm text-gray-500">Loading sharing information...</p>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="link" disabled={!shareEnabled}>Share Link</TabsTrigger>
            </TabsList>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-4 py-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Report Details</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                {reportName || `Report ${reportId}`}
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="share-enabled">Enable Sharing</Label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Allow others to access this report via a link
                  </p>
                </div>
                <Switch
                  id="share-enabled"
                  checked={shareEnabled}
                  onCheckedChange={setShareEnabled}
                />
              </div>

              {shareEnabled && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="expires">Expiration</Label>
                    <Select
                      value={expiresIn || ''}
                      onValueChange={setExpiresIn}
                    >
                      <SelectTrigger id="expires" className="w-full">
                        <SelectValue placeholder="Select expiration time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 day</SelectItem>
                        <SelectItem value="7">7 days</SelectItem>
                        <SelectItem value="30">30 days</SelectItem>
                        <SelectItem value="null">No expiration</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">Password Protection (Optional)</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter a password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Leave blank for no password protection
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="allow-comments">Allow Comments</Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Let viewers add comments to the report
                      </p>
                    </div>
                    <Switch
                      id="allow-comments"
                      checked={allowComments}
                      onCheckedChange={setAllowComments}
                    />
                  </div>
                </>
              )}
            </div>
          </TabsContent>

          {/* Share Link Tab */}
          <TabsContent value="link" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="share-url">Share URL</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="share-url"
                  value={shareUrl}
                  readOnly
                  className="flex-1"
                />
                <Button
                  type="button"
                  size="icon"
                  variant="outline"
                  onClick={handleCopyLink}
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Share Settings</h3>
              <div className="rounded-md border p-4 space-y-3">
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Expiration</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {expiresIn === 'null' ? 'No expiration' : `Expires in ${expiresIn} days`}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Lock className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Password Protection</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {password ? 'Password protected' : 'No password protection'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Link className="h-5 w-5 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Access</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Anyone with the link can view this report
                      {allowComments ? ' and add comments' : ''}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-2">
              <Button
                variant="destructive"
                onClick={handleDisableSharing}
                disabled={isLoading}
                className="w-full"
              >
                <X className="mr-2 h-4 w-4" />
                Disable Sharing
              </Button>
            </div>
          </TabsContent>
        </Tabs>
        )}

        <DialogFooter className="sm:justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading || isInitializing}
          >
            Cancel
          </Button>

          {activeTab === 'settings' && shareEnabled && (
            <Button
              type="button"
              onClick={handleSaveSettings}
              disabled={isLoading || isInitializing}
            >
              {isLoading ? 'Saving...' : 'Save & Generate Link'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
