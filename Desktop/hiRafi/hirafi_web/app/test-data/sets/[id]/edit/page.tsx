"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Database, Sparkles, Loader2 } from "lucide-react"

// Import modular components
import { EditDataSetHeader } from "@/components/data-sets/edit-data-set-header"
import { BasicInfoForm } from "@/components/data-sets/basic-info-form"
import { AiPromptSection } from "@/components/data-sets/ai-prompt-section"
import { VariablesTable } from "@/components/data-sets/variables-table"
import { ConstraintEditorDialog } from "@/components/data-sets/constraint-editor-dialog"
import { ImportFromSourceDialog } from "@/components/data-sets/import-from-source-dialog"

// Import custom hook
import { useTestDataSets } from "@/hooks/useTestDataSets"
import type { ValueConstraints } from "@/types/test-data"

export default function EditDataSetPage() {
  const params = useParams()
  const router = useRouter()
  const dataSetId = params.id as string
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)

  const {
    formData,
    newTag,
    setNewTag,
    isGenerating,
    isSaving,
    activeTab,
    setActiveTab,
    showVariableValues,
    setShowVariableValues,
    activeEnvironmentId,
    showEnvironmentSelector,
    setShowEnvironmentSelector,
    userEnvironments,
    selectedEnvironments,
    isLoadingEnvironments,
    variables,
    editingVariable,
    tempConstraints,
    handleFormChange,
    handleAddTag,
    handleRemoveTag,
    handleUpdateVariable,
    handleAddVariable,
    handleRemoveVariable,
    handleDuplicateVariable,
    handleGenerateData,
    handleRandomizeData,
    handleEditConstraints,
    handleUpdateConstraint,
    handleSaveConstraints,
    handleCancelConstraints,
    handleCopyValue,
    handleCopyAllEnvironmentsAsJson,
    handleUpdateDataSet,
    handleBack,
    handleEnvironmentChange,
    handleToggleEnvironment,
    handleClearAll,
    canSave,
    loadDataSetForEditing,
    isLoadingDataSet,
    loadDataSetError,
    importVariablesFromSource,
  } = useTestDataSets()

  // Load data set data on mount using the hook's function
  useEffect(() => {
    if (dataSetId) {
      loadDataSetForEditing(dataSetId)
    }
  }, [dataSetId, loadDataSetForEditing])

  // Show loading state from the hook
  if (isLoadingDataSet) {
    return (
      <div className="flex h-screen bg-white dark:bg-gray-950">
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-indigo-600" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Loading Data Set</h2>
              <p className="text-gray-600 dark:text-gray-400">Please wait while we load the data set for editing...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show error state from the hook
  if (loadDataSetError) {
    return (
      <div className="flex h-screen bg-white dark:bg-gray-950">
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="text-red-500 mb-4"><Database className="h-12 w-12 mx-auto" /></div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Failed to Load Data Set</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{loadDataSetError}</p>
              <button
                onClick={() => router.push("/test-data")}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                Back to Test Data
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Main content once loaded
  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <EditDataSetHeader
          dataSetName={formData.name}
          onSave={() => handleUpdateDataSet(dataSetId)}
          onBack={handleBack}
          isSaving={isSaving}
          canSave={canSave}
        />
        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900/20 p-6">
          <div className="max-w-7xl mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full max-w-md grid-cols-2">
                <TabsTrigger value="basic-info" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  <span>Basic Information</span>
                </TabsTrigger>
                <TabsTrigger value="generate-data" className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  <span>Generate Data</span>
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic-info" className="space-y-6">
                <BasicInfoForm
                  formData={formData}
                  onFormChange={handleFormChange}
                  newTag={newTag}
                  onNewTagChange={setNewTag}
                  onAddTag={handleAddTag}
                  onRemoveTag={handleRemoveTag}
                />
              </TabsContent>

              {/* Generate Data Tab */}
              <TabsContent value="generate-data" className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <div className="space-y-6">
                    {/* AI Prompt Section */}
                    <AiPromptSection
                      prompt={formData.aiPrompt}
                      onPromptChange={(value) => handleFormChange("aiPrompt", value)}
                      onGenerate={handleGenerateData}
                      isGenerating={isGenerating}
                      environments={userEnvironments}
                      selectedEnvironments={selectedEnvironments}
                      isLoadingEnvironments={isLoadingEnvironments}
                      onToggleEnvironment={handleToggleEnvironment}
                    />

                    {/* Variables Table */}
                    <VariablesTable
                      variables={variables}
                      showValues={showVariableValues}
                      onShowValuesChange={setShowVariableValues}
                      activeEnvironment={activeEnvironmentId}
                      showEnvironmentSelector={showEnvironmentSelector}
                      onShowEnvironmentSelectorChange={setShowEnvironmentSelector}
                      onEnvironmentChange={handleEnvironmentChange}
                      onUpdateVariable={handleUpdateVariable}
                      onRemoveVariable={handleRemoveVariable}
                      onDuplicateVariable={handleDuplicateVariable}
                      onCopyValue={handleCopyValue}
                      onEditConstraints={handleEditConstraints}
                      onAddVariable={handleAddVariable}
                      onCopyAllAsJson={handleCopyAllEnvironmentsAsJson}
                      userEnvironments={userEnvironments}
                      isLoadingEnvironments={isLoadingEnvironments}
                      onRandomizeData={handleRandomizeData}
                      isGenerating={isGenerating}
                      onClearAll={handleClearAll}
                      onImportFromSource={() => setShowImportDialog(true)}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </main>

        {/* Constraint Editor Dialog */}
        <ConstraintEditorDialog
          variable={editingVariable}
          constraints={tempConstraints}
          onConstraintChange={handleUpdateConstraint as (key: keyof ValueConstraints, value: any) => void}
          onSave={handleSaveConstraints}
          onCancel={handleCancelConstraints}
        />

        {/* Import from Source Dialog */}
        <ImportFromSourceDialog
          isOpen={showImportDialog}
          onClose={() => setShowImportDialog(false)}
          onImport={importVariablesFromSource}
          currentVariables={variables}
        />
      </div>
    </div>
  )
}
