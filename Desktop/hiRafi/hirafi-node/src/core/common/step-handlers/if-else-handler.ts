/**
 * If-Else Control Flow Handler
 * Handles if-else control flow steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';

/**
 * If-Else Control Flow Handler
 * Executes conditional logic based on AI boolean evaluation
 */
export class IfElseHandler implements IStepHandler {
  private stepExecutor: any; // Will be injected by the registry
  private progressReporter: any = null; // Will be injected by the registry

  /**
   * Set the step executor (used for executing nested steps)
   * @param executor Function to execute nested steps
   */
  setStepExecutor(executor: any): void {
    this.stepExecutor = executor;
  }

  /**
   * Set the progress reporter (used for reporting nested step progress)
   * @param progressReporter Progress reporter instance
   */
  setProgressReporter(progressReporter: any): void {
    this.progressReporter = progressReporter;
  }

  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'ifElse';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'IfElseHandler';
  }

  /**
   * Execute an if-else step
   * @param target The target to execute the step on
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    const condition = step.condition || step.value || step.prompt;
    
    if (!condition) {
      const errorMessage = 'Missing condition for if-else step';
      logs.push(`❌ If-Else Error: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    if (!step.trueSteps && !step.falseSteps) {
      const errorMessage = 'If-else step must have at least one branch (trueSteps or falseSteps)';
      logs.push(`❌ If-Else Error: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    try {
      logs.push(`🔍 Evaluating condition: "${condition}"`);
      logger.debug(`IfElseHandler: Evaluating condition "${condition}"`);

      // Check if agent is available for AI operations
      if (!agent) {
        const errorMessage = 'AI agent is not available for condition evaluation';
        logs.push(`❌ If-Else Error: ${errorMessage}`);
        logger.error(`IfElseHandler: Agent is null - this indicates an issue with agent passing in nested execution`);
        return { success: false, error: errorMessage };
      }

      // Use AI agent to evaluate the boolean condition via aiBoolean
      const conditionEvaluation = await agent.aiBoolean(condition);
      
      if (conditionEvaluation.error) {
        const errorMessage = `Condition evaluation failed: ${conditionEvaluation.error}`;
        logs.push(`❌ If-Else Error: ${errorMessage}`);
        return { success: false, error: errorMessage };
      }

      const isConditionTrue = conditionEvaluation.result === true;
      logs.push(`✅ Condition result: ${isConditionTrue ? 'TRUE' : 'FALSE'}`);
      
      // Determine which steps to execute
      const stepsToExecute = isConditionTrue ? step.trueSteps : step.falseSteps;
      
      if (!stepsToExecute || stepsToExecute.length === 0) {
        logs.push(`ℹ️ No steps to execute for ${isConditionTrue ? 'TRUE' : 'FALSE'} branch`);
        return { success: true, error: null, data: { conditionResult: isConditionTrue, stepsExecuted: 0 } };
      }

      logs.push(`🔄 Executing ${stepsToExecute.length} steps for ${isConditionTrue ? 'TRUE' : 'FALSE'} branch`);

      // Execute the steps in the selected branch and collect results
      let executedSteps = 0;
      const executedStepResults: any[] = [];
      
      for (let i = 0; i < stepsToExecute.length; i++) {
        const nestedStep = stepsToExecute[i];
        
        if (!this.stepExecutor) {
          const errorMessage = 'Step executor not available for nested step execution';
          logs.push(`❌ If-Else Error: ${errorMessage}`);
          return { success: false, error: errorMessage };
        }

        const stepResult = await this.stepExecutor(target, nestedStep, agent, logs, step.id || `if-else-${Date.now()}`, 1);
        executedSteps++;
        
        // Enrich step result with nested step details for proper reporting
        const stepName = nestedStep.name || 
                        (nestedStep.type === 'aiTap' ? `Click ${nestedStep.prompt || 'element'}` :
                         nestedStep.type === 'aiInput' ? `Input "${nestedStep.value || 'text'}"` :
                         nestedStep.type === 'aiAssertion' ? `Verify ${nestedStep.prompt || 'condition'}` :
                         nestedStep.type === 'goto' ? `Navigate to ${nestedStep.url || 'page'}` :
                         nestedStep.type === 'sleep' ? `Wait ${nestedStep.duration || 1000}ms` :
                         nestedStep.type || 'Nested Step');
        
        const enrichedStepResult = {
          ...stepResult,
          id: nestedStep.id || `nested-step-${i + 1}`,
          name: stepName,
          type: nestedStep.type || 'unknown',
          // Include screenshot URLs from nested step execution
          beforeScreenshotUrl: stepResult.beforeScreenshotUrl || null,
          afterScreenshotUrl: stepResult.afterScreenshotUrl || null,
          // Add timing information
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          duration: stepResult.duration || 0,
          status: stepResult.success ? 'passed' : 'failed',
          success: stepResult.success,
          logs: stepResult.logs || [],
          // Preserve original step metadata
          stepMetadata: {
            originalStep: nestedStep,
            executionOrder: i + 1,
            parentStepId: step.id,
            branch: isConditionTrue ? 'true' : 'false'
          }
        };
        
        executedStepResults.push(enrichedStepResult);

        if (!stepResult.success) {
          logs.push(`❌ If-Else: Nested step ${i + 1} failed: ${stepResult.error}`);
          return { 
            success: false, 
            error: `Nested step failed: ${stepResult.error}`,
            data: { 
              conditionResult: isConditionTrue, 
              executedBranch: isConditionTrue ? 'true' : 'false',
              stepsExecuted: executedSteps, 
              failedStepIndex: i,
              [isConditionTrue ? 'trueSteps' : 'falseSteps']: executedStepResults
            }
          };
        }
      }

      logs.push(`✅ If-Else completed successfully (${executedSteps} steps executed)`);
      return { 
        success: true, 
        error: null, 
        data: { 
          conditionResult: isConditionTrue, 
          executedBranch: isConditionTrue ? 'true' : 'false',
          stepsExecuted: executedSteps,
          [isConditionTrue ? 'trueSteps' : 'falseSteps']: executedStepResults
        } 
      };

    } catch (error: any) {
      const errorMessage = `If-else execution failed: ${error.message}`;
      logs.push(`❌ If-Else Error: ${errorMessage}`);
      logger.error(`IfElseHandler: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }
} 