"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"
import { UnifiedScenarioProvider } from "@/contexts/UnifiedScenarioContext"
import { useFolders } from "@/hooks/useFolders"
import { Sidebar } from "@/components/sidebar/sidebar"
import { ScenarioHeader } from "@/components/edit-scenario/scenario-header"
import { TabNavigation } from "@/components/edit-scenario/tab-navigation"
import { DetailsTab } from "@/components/edit-scenario/tabs/details-tab"

import { TestManagementTab } from "@/components/shared/test-management-tab"
import { TestManagementProvider as TestManagementProviderType } from "@/store/testManagementStore"
import { UnifiedTestManagementProvider } from "@/contexts/UnifiedTestManagementContext"
import { updateFormDataWithProvider } from "@/lib/utils/test-management-utils"
import { ScenarioSteps } from "@/components/shared/scenario-steps/ScenarioSteps"

import { Mouse<PERSON>oint<PERSON>, <PERSON>, <PERSON>, <PERSON>, ArrowR<PERSON> } from "lucide-react"
import { TestStep, ActionType, ScenarioFormData } from "@/models/scenario"
import { toast } from "@/lib/utils/toast-utils"
import { ProtectedRoute } from "@/components/protected-route"
import { useScenarioStoreContext } from "@/hooks/useStoreContext"
import { useScenarioExpiryCleanup } from "@/hooks/useExpiryCleanup"
import React from "react"
import { Button } from "@/components/ui/button"


function EditScenarioPageContent() {
  // Store context management for company changes
  useScenarioStoreContext()
  
  // Automatic expiry cleanup for scenario editing drafts (2 hours for editing)
  useScenarioExpiryCleanup({ 
    showNotifications: false, // Don't show notifications during editing
    customExpiryTime: 120 // 120 minutes (2 hours)
  })
  
  const {
    // Data
    formData: scenarioData,
    ui,
    folder,

    // Actions
    updateFormField: updateScenarioField,
    saveScenario: handleSaveScenario,
    deleteScenario: handleDeleteScenario,
    setActiveTab,

    // Step management
    addStep,
    removeStep,
    updateStep: handleUpdateStep,
    updateStepType: handleStepTypeChange,
    reorderSteps: handleDragEnd,

    // Tag management
    addTag: handleAddTag,
    removeTag,

    // Test management
    handleTestManagementSelection,

    // Folder management
    setNewFolderOpen,
    setNewFolderName,
    setNewFolderColor,
    setIsCreatingFolder,

    // UI management
    setSidebarCollapsed
  } = useUnifiedScenarioContext()

  // Fetch folders and actions using unified hook - optimized for edit page
  const {
    folders,
    isLoading: foldersLoading,
    error: foldersError,
    refreshFolders,
    createFolder
  } = useFolders({
    autoFetch: true // Safe to auto-fetch since this doesn't subscribe to scenario store
  })

  // Legacy helpers - these would need to be implemented or imported separately
  const router = useRouter()
  const getFolderColorClass = (color: string) => `bg-${color}-100 text-${color}-800`
  const getStepTypeIcon = (type: string) => '📝'
  const getStepTypeLabel = (type: string) => type
  const getStepTypeColor = (type: string) => 'blue'

  // Create folder handler
  const handleCreateFolder = async (name: string, color: string) => {
    try {
      const result = await createFolder({ name, color })
      if (result?.success) {
        // Refresh folders list to show the new folder
        refreshFolders()

        // Select the newly created folder
        const newFolder = result.data?.folder || result.folder
        if (newFolder?.id) {
          updateScenarioField("folderId", newFolder.id)
        }
      }
    } catch (error) {
      console.error('Failed to create folder:', error)
    }
  }

  // Sync toggle handler (placeholder for test management)
  const handleSyncToggle = (enabled: boolean) => {
    // Implementation would depend on test management requirements
    console.log('Sync toggle:', enabled)
  }

  // Handle scenario error - removed automatic redirect to avoid conflicts with conditional rendering


  // Show loading state while scenario is being loaded
  if (ui.isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-white dark:bg-gray-950">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Loading scenario...</h2>
        </div>
      </div>
    )
  }

  // Show error state if there was an error loading the scenario
  if (ui.scenarioError) {
    return (
      <div className="flex h-screen items-center justify-center bg-white dark:bg-gray-950">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Error Loading Scenario</h2>
          <p className="text-gray-500 mt-2">{ui.scenarioError}</p>
          <div className="mt-4 space-x-2">
            <Button onClick={() => window.location.reload()} variant="outline">
              Retry
            </Button>
            <Button onClick={() => router.push("/scenarios")}>
              Back to Scenarios
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show not found state if scenario data is not available after loading
  if (!scenarioData || !scenarioData.name) {
    return (
      <div className="flex h-screen items-center justify-center bg-white dark:bg-gray-950">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Scenario not found.</h2>
          <p className="text-gray-500 mt-2">It might have been deleted or you may not have permission to view it.</p>
          <Button onClick={() => router.push("/scenarios")} className="mt-4">
            Back to Scenarios
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex h-screen bg-white dark:bg-gray-950 relative">
        {/* Saving Overlay */}
        {ui.isSaving && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 text-center">
              <div className="w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Saving scenario...</h3>
              <p className="text-gray-500 mt-2">Please wait while we save your changes.</p>
            </div>
          </div>
        )}

        {/* Sidebar */}
        <Sidebar collapsed={ui.sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!ui.sidebarCollapsed)} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <ScenarioHeader
            scenarioData={scenarioData}
            isLoading={ui.isLoading}
            isSaving={ui.isSaving}
            onSave={handleSaveScenario}
            onBack={() => router.push("/scenarios")}
            onDelete={handleDeleteScenario}
          />

          <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
            <div className="max-w-7xl mx-auto">
              {/* Tab Navigation */}
              <TabNavigation activeTab={ui.activeTab} onTabChange={setActiveTab} />

              {/* Tab Content */}
              <div className="w-full">
                {ui.activeTab === "details" && (
                  <div className="animate-tab-transition">
                    <DetailsTab
                      scenarioData={scenarioData}
                      updateScenarioField={updateScenarioField}
                      folders={folders}
                      foldersLoading={foldersLoading}
                      getFolderColorClass={getFolderColorClass}
                      newFolderOpen={folder.newFolderOpen}
                      setNewFolderOpen={setNewFolderOpen}
                      newFolderName={folder.newFolderName}
                      setNewFolderName={setNewFolderName}
                      newFolderColor={folder.newFolderColor}
                      setNewFolderColor={setNewFolderColor}
                      isCreatingFolder={folder.isCreatingFolder}
                      handleCreateFolder={handleCreateFolder}
                      handleAddTag={handleAddTag}
                      handleRemoveTag={removeTag}
                    />
                  </div>
                )}

                {ui.activeTab === "steps" && (
                  <div className="animate-tab-transition">
                    <ScenarioSteps
                      steps={scenarioData.steps}
                      onStepsChange={(newSteps) => updateScenarioField("steps", newSteps)}
                    />
                  </div>
                )}



                {ui.activeTab === "testmanagement" && (
                  <div className="animate-tab-transition">
                    <TestManagementTab
                      scenarioData={scenarioData}
                      handleTestCaseSelection={handleTestManagementSelection}
                      handleSyncToggle={handleSyncToggle}
                    />
                  </div>
                )}
              </div>
            </div>
          </main>
        </div>
      </div>

      <style jsx global>{`
        @keyframes tabFadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-tab-transition {
          animation: tabFadeIn 0.3s ease-out forwards;
          will-change: opacity, transform;
        }
      `}</style>
    </>
  )
}

// Wrapper component to access scenario data for UnifiedTestManagementProvider
function EditScenarioPageWrapper() {
  const { formData } = useUnifiedScenarioContext()

  return (
    <UnifiedTestManagementProvider scenarioData={formData} autoInitialize={false}>
      <EditScenarioPageContent />
    </UnifiedTestManagementProvider>
  )
}

export default function EditScenarioPage() {
  const params = useParams()
  if (!params) return null; // Guard against null params
  const scenarioId = params.id as string

  return (
    <ProtectedRoute>
      <UnifiedScenarioProvider options={{ mode: 'edit', scenarioId }}>
        <EditScenarioPageWrapper />
      </UnifiedScenarioProvider>
    </ProtectedRoute>
  )
}
