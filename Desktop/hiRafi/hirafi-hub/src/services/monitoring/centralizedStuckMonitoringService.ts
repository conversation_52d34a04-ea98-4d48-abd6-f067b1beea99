/**
 * Centralized Stuck Monitoring Service
 * 
 * Replaces all Node-side stuck monitoring with a single, optimized Hub-based monitoring system.
 * Monitors ALL tests across ALL nodes from a central location using step progression data.
 * Provides intelligent stuck detection, recovery, and clear debugging information.
 * Enhanced with NodeTimeoutGuardian coordination for hybrid monitoring approach.
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { stepProgressService } from '../step-progress/stepProgressService.js';
import { queueService, QUEUE_NAMES } from '../redis/queueService.js';
import { nodeRegistry } from '../node/NodeRegistry.js';
import { testManager } from '../../core/test-manager/index.js';
import { TestStatus } from '../../models/test-types.js';

export interface StuckTestAnalysis {
  testId: string;
  nodeId?: string;
  isStuck: boolean;
  reason: string;
  stuckDuration: number;
  currentStepDuration: number;
  averageStepDuration: number;
  recommendation: 'continue' | 'investigate' | 'terminate';
  lastStepUpdate: string;
  stepProgressionStalled: boolean;
}

export interface StuckTestMetrics {
  totalStuckTests: number;
  totalRecoveredTests: number;
  totalFailedRecoveries: number;
  averageRecoveryTime: number;
  stuckTestsByReason: Record<string, number>;
  stuckTestsByNode: Record<string, number>;
  recentStuckTests: StuckTestEvent[];
  // Guardian coordination metrics
  nodeGuardianActive: Record<string, boolean>;
  nodeGuardianTerminations: number;
  coordinatedTerminations: number;
  duplicateTerminationsPrevented: number;
}

export interface StuckTestEvent {
  testId: string;
  nodeId: string;
  reason: string;
  detectedAt: Date;
  recoveredAt?: Date;
  recoveryTime?: number;
  recoverySuccess: boolean;
  details: StuckTestAnalysis;
  terminatedBy?: 'hub' | 'node-guardian' | 'coordinated';
}

export interface CentralizedMonitoringEvents {
  'test:stuck:detected': (analysis: StuckTestAnalysis) => void;
  'test:stuck:recovered': (testId: string, method: string) => void;
  'test:stuck:failed': (testId: string, error: Error) => void;
  'node:unresponsive': (nodeId: string, testId: string) => void;
  'stuckTestDetected': (event: StuckTestEvent) => void;
  'stuckTestRecovered': (event: StuckTestEvent) => void;
  'stuckTestRecoveryTimeout': (event: StuckTestEvent) => void;
  // Guardian coordination events
  'node:guardian:active': (testId: string, nodeId: string) => void;
  'node:guardian:inactive': (testId: string, nodeId: string) => void;
  'node:guardian:termination': (testId: string, nodeId: string, reason: string) => void;
  'coordination:duplicate:prevented': (testId: string, by: string) => void;
}

export class CentralizedStuckMonitoringService extends EventEmitter {
  private static instance: CentralizedStuckMonitoringService;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring: boolean = false;

  // Configuration - Conservative timeline to work with node-side guardian (aggressive approach)
  private readonly MONITORING_INTERVAL = 15000; // Check every 15 seconds (faster than before)
  private readonly MAX_STEP_DURATION = 12 * 60 * 1000; // 12 minutes per step (higher than node's 15min)
  private readonly STEP_TIMEOUT_MULTIPLIER = 5; // Allow 5x average step duration
  private readonly MIN_STEPS_FOR_ANALYSIS = 2; // Need at least 2 completed steps for analysis
  private readonly CRITICAL_STUCK_THRESHOLD = 40 * 60 * 1000; // 40 minutes - increased for longer tests with many steps
  private readonly NODE_UNRESPONSIVE_THRESHOLD = 5 * 60 * 1000; // 5 minutes without step updates

  // Guardian coordination tracking
  private activeNodeGuardians = new Map<string, { testId: string; nodeId: string; timestamp: number }>();
  private guardianTerminations = new Set<string>(); // Track tests terminated by node guardians
  private hubTerminations = new Set<string>(); // Track tests terminated by hub

  // Enhanced Statistics and Metrics
  private metrics: StuckTestMetrics;
  private stuckTestEvents: Map<string, StuckTestEvent>;
  private totalStuckDetections = 0;
  private totalRecoveries = 0;
  private totalFailures = 0;
  private nodeGuardianTerminations = 0;
  private coordinatedTerminations = 0;
  private duplicateTerminationsPrevented = 0;

  private constructor() {
    super();

    // Initialize metrics
    this.metrics = {
      totalStuckTests: 0,
      totalRecoveredTests: 0,
      totalFailedRecoveries: 0,
      averageRecoveryTime: 0,
      stuckTestsByReason: {},
      stuckTestsByNode: {},
      recentStuckTests: [],
      nodeGuardianActive: {},
      nodeGuardianTerminations: 0,
      coordinatedTerminations: 0,
      duplicateTerminationsPrevented: 0
    };

    // Initialize stuck test events tracking
    this.stuckTestEvents = new Map();

    // Setup guardian coordination message handlers
    this.setupGuardianCoordinationHandlers();
  }

  public static getInstance(): CentralizedStuckMonitoringService {
    if (!CentralizedStuckMonitoringService.instance) {
      CentralizedStuckMonitoringService.instance = new CentralizedStuckMonitoringService();
    }
    return CentralizedStuckMonitoringService.instance;
  }

  /**
   * Setup handlers for node guardian coordination messages
   */
  private setupGuardianCoordinationHandlers(): void {
    // These handlers would be called by WebSocket message handlers
    this.on('internal:node-guardian-active', (data: { testId: string; nodeId: string; guardianConfig: any }) => {
      this.handleNodeGuardianActive(data.testId, data.nodeId, data.guardianConfig);
    });

    this.on('internal:node-guardian-inactive', (data: { testId: string; nodeId: string }) => {
      this.handleNodeGuardianInactive(data.testId, data.nodeId);
    });

    this.on('internal:node-emergency-termination', (data: { testId: string; nodeId: string; reason: string; terminatedBy: string }) => {
      this.handleNodeGuardianTermination(data.testId, data.nodeId, data.reason);
    });

    this.on('internal:node-termination-failure', (data: { testId: string; nodeId: string; error: string }) => {
      this.handleNodeGuardianTerminationFailure(data.testId, data.nodeId, data.error);
    });

    logger.info('CentralizedStuckMonitoringService: Guardian coordination handlers initialized');
  }

  /**
   * Handle node guardian activation notification
   */
  private handleNodeGuardianActive(testId: string, nodeId: string, guardianConfig: any): void {
    logger.info(`🛡️ Hub: Node guardian activated for test ${testId} on node ${nodeId}`);
    
    this.activeNodeGuardians.set(testId, {
      testId,
      nodeId,
      timestamp: Date.now()
    });

    this.metrics.nodeGuardianActive[testId] = true;
    
    this.emit('node:guardian:active', testId, nodeId);

    // Log configuration differences for debugging
    logger.debug(`🛡️ Hub: Guardian config - Step timeout: ${guardianConfig.stepTimeoutMs}ms, Test timeout: ${guardianConfig.testTimeoutMs}ms`);
    logger.debug(`🛡️ Hub: Hub config - Step timeout: ${this.MAX_STEP_DURATION}ms, Test timeout: ${this.CRITICAL_STUCK_THRESHOLD}ms`);
  }

  /**
   * Handle node guardian deactivation notification  
   */
  private handleNodeGuardianInactive(testId: string, nodeId: string): void {
    logger.info(`🛡️ Hub: Node guardian deactivated for test ${testId} on node ${nodeId}`);
    
    this.activeNodeGuardians.delete(testId);
    this.metrics.nodeGuardianActive[testId] = false;
    
    this.emit('node:guardian:inactive', testId, nodeId);
  }

  /**
   * Handle node guardian emergency termination notification
   */
  private handleNodeGuardianTermination(testId: string, nodeId: string, reason: string): void {
    logger.error(`🔥 Hub: EMERGENCY TERMINATION by node guardian - Test ${testId} on node ${nodeId}: ${reason}`);
    
    // Mark as terminated by node guardian to prevent duplicate processing
    this.guardianTerminations.add(testId);
    this.nodeGuardianTerminations++;
    this.metrics.nodeGuardianTerminations++;
    
    // Clean up tracking
    this.activeNodeGuardians.delete(testId);
    this.metrics.nodeGuardianActive[testId] = false;
    
    // Update stuck test event if it exists
    const stuckEvent = this.stuckTestEvents.get(testId);
    if (stuckEvent) {
      stuckEvent.terminatedBy = 'node-guardian';
      stuckEvent.recoveredAt = new Date();
      stuckEvent.recoveryTime = Date.now() - stuckEvent.detectedAt.getTime();
      stuckEvent.recoverySuccess = true;
      
      this.metrics.recentStuckTests.push(stuckEvent);
      if (this.metrics.recentStuckTests.length > 50) {
        this.metrics.recentStuckTests.shift();
      }
    }
    
    this.emit('node:guardian:termination', testId, nodeId, reason);
    
    // Perform hub-side cleanup coordination
    this.performCoordinatedCleanup(testId, nodeId, reason, 'node-guardian').catch(error => {
      logger.error(`🔥 Hub: Error in coordinated cleanup for test ${testId}: ${(error as Error).message}`);
    });
  }

  /**
   * Handle node guardian termination failure
   */
  private handleNodeGuardianTerminationFailure(testId: string, nodeId: string, error: string): void {
    logger.error(`🔥 Hub: Node guardian termination FAILED for test ${testId} on node ${nodeId}: ${error}`);
    
    // Remove from guardian terminations since it failed
    this.guardianTerminations.delete(testId);
    
    // Hub should take over termination since node guardian failed
    logger.error(`🔥 Hub: Taking over termination for test ${testId} due to node guardian failure`);
    
    // Create analysis for hub-side termination
    const analysis: StuckTestAnalysis = {
      testId,
      nodeId,
      isStuck: true,
      reason: `Node guardian termination failed: ${error}`,
      stuckDuration: 0,
      currentStepDuration: 0,
      averageStepDuration: 0,
      recommendation: 'terminate',
      lastStepUpdate: new Date().toISOString(),
      stepProgressionStalled: true
    };
    
    // Execute hub-side emergency termination
    this.handleStuckTest(analysis).catch(hubError => {
      logger.error(`🔥 Hub: Emergency hub termination also failed for test ${testId}: ${(hubError as Error).message}`);
    });
  }

  /**
   * Perform coordinated cleanup after node guardian termination
   */
  private async performCoordinatedCleanup(testId: string, nodeId: string, reason: string, terminatedBy: string): Promise<void> {
    try {
      logger.info(`🔥 Hub: Starting coordinated cleanup for test ${testId} (terminated by: ${terminatedBy})`);
      
      // 1. Update test status in database
      try {
        await testManager.updateTestStatus(testId, TestStatus.FAILED, {
          error: `NodeGuardian: ${reason}`,
          endedAt: new Date(),
          stuckDetectedBy: 'NodeTimeoutGuardian',
          terminatedBy: terminatedBy
        });
        logger.info(`🔥 Hub: Updated test ${testId} status to failed (coordinated cleanup)`);
      } catch (statusError) {
        logger.error(`🔥 Hub: Error updating test status in coordinated cleanup: ${(statusError as Error).message}`);
      }
      
      // 2. Handle BullMQ job cleanup
      try {
        const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
        if (testQueue) {
          const activeJobs = await testQueue.getActive();
          const job = activeJobs.find(j => j.data?.id === testId);

          if (job) {
            const jobResult = {
              success: false,
              testId: testId,
              message: `Terminated by ${terminatedBy}: ${reason}`,
              terminatedBy: terminatedBy,
              finalOutcome: 'coordinated_termination'
            };

            await job.moveToCompleted(jobResult, job.token || '', true);
            logger.info(`🔥 Hub: Coordinated BullMQ job cleanup for test ${testId}`);
          } else {
            logger.debug(`🔥 Hub: No active BullMQ job found for test ${testId} (already cleaned by guardian)`);
          }
        }
      } catch (jobError) {
        logger.warn(`🔥 Hub: Error in coordinated BullMQ cleanup: ${(jobError as Error).message}`);
      }
      
      // 3. Clean up step progress data
      try {
        await stepProgressService.cleanupTestStepProgress(testId);
        logger.info(`🔥 Hub: Coordinated step progress cleanup for test ${testId}`);
      } catch (cleanupError) {
        logger.warn(`🔥 Hub: Error in coordinated step progress cleanup: ${(cleanupError as Error).message}`);
      }
      
      // 4. Mark node as available
      try {
        await nodeRegistry.markNodeAsAvailable(nodeId);
        logger.info(`🔥 Hub: Coordinated node availability update for node ${nodeId}`);
      } catch (nodeError) {
        logger.warn(`🔥 Hub: Error in coordinated node availability update: ${(nodeError as Error).message}`);
      }
      
      this.coordinatedTerminations++;
      this.metrics.coordinatedTerminations++;
      
      logger.info(`🔥 Hub: Completed coordinated cleanup for test ${testId}`);
      
    } catch (error) {
      logger.error(`🔥 Hub: Error in coordinated cleanup for test ${testId}: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Check if test should be processed by hub (not already handled by guardian)
   */
  private shouldProcessTestForStuck(testId: string): boolean {
    // Skip if already terminated by node guardian
    if (this.guardianTerminations.has(testId)) {
      this.duplicateTerminationsPrevented++;
      this.metrics.duplicateTerminationsPrevented++;
      logger.debug(`🛡️ Hub: Skipping test ${testId} - already terminated by node guardian`);
      this.emit('coordination:duplicate:prevented', testId, 'node-guardian');
      return false;
    }
    
    // Skip if already terminated by hub
    if (this.hubTerminations.has(testId)) {
      logger.debug(`🛡️ Hub: Skipping test ${testId} - already terminated by hub`);
      return false;
    }
    
    return true;
  }

  /**
   * Start centralized monitoring
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      logger.warn('CentralizedStuckMonitoringService: Monitoring already active');
      return;
    }

    this.monitoringInterval = setInterval(() => {
      this.performMonitoringCycle().catch(error => {
        logger.error(`CentralizedStuckMonitoringService: Error in monitoring cycle: ${(error as Error).message}`);
      });
    }, this.MONITORING_INTERVAL);

    this.isMonitoring = true;
    logger.info('CentralizedStuckMonitoringService: Started centralized stuck monitoring');
  }

  /**
   * Stop monitoring
   */
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    logger.info('CentralizedStuckMonitoringService: Stopped centralized stuck monitoring');
  }

  /**
   * Main monitoring cycle - checks all active tests across all nodes
   */
  private async performMonitoringCycle(): Promise<void> {
    try {
      logger.debug('CentralizedStuckMonitoringService: Starting monitoring cycle');

      // Get active tests from both MongoDB and BullMQ
      const [mongoTests, bullmqActiveJobs] = await Promise.all([
        testManager.getAllTests(),
        this.getBullMQActiveTests()
      ]);

      // Combine tests from both sources
      const allActiveTests = new Map<string, any>();

      // Add MongoDB tests
      for (const test of mongoTests) {
        allActiveTests.set(test.id, test);
      }

      // Add BullMQ active jobs that might not be in MongoDB
      for (const job of bullmqActiveJobs) {
        if (job.data?.id && !allActiveTests.has(job.data.id)) {
          // Create a test object from BullMQ job data
          allActiveTests.set(job.data.id, {
            id: job.data.id,
            nodeId: job.data.nodeId,
            status: 'RUNNING', // Assume running if in BullMQ active
            queuedAt: new Date(job.timestamp || Date.now()),
            ...job.data
          });
        }
      }

      const activeTestsArray = Array.from(allActiveTests.values());

      if (activeTestsArray.length === 0) {
        logger.debug('CentralizedStuckMonitoringService: No active tests to monitor');
        return;
      }

      logger.debug(`CentralizedStuckMonitoringService: Monitoring ${activeTestsArray.length} active tests (${mongoTests.length} from MongoDB, ${bullmqActiveJobs.length} from BullMQ)`);

      // Analyze each active test for stuck conditions
      for (const test of activeTestsArray) {
        try {
          await this.analyzeTestForStuckConditions(test);
        } catch (error) {
          logger.error(`CentralizedStuckMonitoringService: Error analyzing test ${test.id}: ${(error as Error).message}`);
        }
      }

      logger.debug('CentralizedStuckMonitoringService: Completed monitoring cycle');
    } catch (error) {
      logger.error(`CentralizedStuckMonitoringService: Error in monitoring cycle: ${(error as Error).message}`);
    }
  }

  /**
   * Get active tests from BullMQ queue
   */
  private async getBullMQActiveTests(): Promise<any[]> {
    try {
      const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (!testQueue) {
        logger.warn('CentralizedStuckMonitoringService: Test queue not available');
        return [];
      }

      const activeJobs = await testQueue.getActive();
      logger.debug(`CentralizedStuckMonitoringService: Found ${activeJobs.length} active jobs in BullMQ`);

      return activeJobs;
    } catch (error) {
      logger.error(`CentralizedStuckMonitoringService: Error getting BullMQ active tests: ${(error as Error).message}`);
      return [];
    }
  }

  /**
   * Analyze a single test for stuck conditions using step progression data
   */
  private async analyzeTestForStuckConditions(test: any): Promise<void> {
    const testId = test.id;
    
    // 🛡️ COORDINATION CHECK: Skip if already handled by node guardian
    if (!this.shouldProcessTestForStuck(testId)) {
      return;
    }
    
    try {
      // Get step progress data from Redis
      const stepProgress = await stepProgressService.getTestStepProgress(testId);
      
      if (!stepProgress) {
        // No step progress data - test might be in early stages or stuck before first step
        await this.handleTestWithoutStepProgress(test);
        return;
      }

      // Analyze step progression for stuck conditions
      const analysis = await this.analyzeStepProgression(testId, stepProgress, test);
      
      if (analysis.isStuck && analysis.recommendation === 'terminate') {
        logger.warn(`CentralizedStuckMonitoringService: Detected stuck test ${testId}: ${analysis.reason}`);
        this.totalStuckDetections++;

        // 🛡️ COORDINATION: Mark as hub termination to prevent guardian interference
        this.hubTerminations.add(testId);

        // ENHANCED: Track stuck test event with detailed metrics
        this.handleStuckTestDetected(testId, analysis.nodeId || 'unknown', analysis);

        this.emit('test:stuck:detected', analysis);

        // Handle stuck test recovery
        await this.handleStuckTest(analysis);
      } else if (analysis.recommendation === 'investigate') {
        logger.info(`CentralizedStuckMonitoringService: Test ${testId} needs investigation: ${analysis.reason}`);
      }

    } catch (error) {
      logger.error(`CentralizedStuckMonitoringService: Error analyzing test ${testId}: ${(error as Error).message}`);
    }
  }

  /**
   * Analyze step progression to determine if test is stuck
   */
  private async analyzeStepProgression(testId: string, stepProgress: any, test: any): Promise<StuckTestAnalysis> {
    const now = Date.now();
    const lastUpdated = new Date(stepProgress.lastUpdated).getTime();
    const timeSinceLastUpdate = now - lastUpdated;
    
    // Get node information
    const nodeId = test.nodeId || stepProgress.steps?.[stepProgress.steps.length - 1]?.nodeId;
    
    // Check if step progression has stalled
    const stepProgressionStalled = timeSinceLastUpdate > this.MAX_STEP_DURATION;
    
    // Calculate average step duration for intelligent analysis
    const completedSteps = stepProgress.steps.filter((step: any) => step.status === 'completed');
    let averageStepDuration = 0;
    
    if (completedSteps.length >= this.MIN_STEPS_FOR_ANALYSIS) {
      const totalDuration = completedSteps.reduce((sum: number, step: any, index: number) => {
        if (index === 0) return sum;
        const prevStep = completedSteps[index - 1];
        const stepDuration = new Date(step.timestamp).getTime() - new Date(prevStep.timestamp).getTime();
        return sum + stepDuration;
      }, 0);
      averageStepDuration = totalDuration / (completedSteps.length - 1);
    }
    
    // Determine if test is stuck and recommendation
    let isStuck = false;
    let reason = '';
    let recommendation: 'continue' | 'investigate' | 'terminate' = 'continue';
    
    // Critical threshold - always terminate
    if (timeSinceLastUpdate > this.CRITICAL_STUCK_THRESHOLD) {
      isStuck = true;
      reason = `No step progression for ${Math.round(timeSinceLastUpdate / 1000)}s (critical threshold exceeded)`;
      recommendation = 'terminate';
    }
    // Step progression stalled
    else if (stepProgressionStalled) {
      isStuck = true;
      reason = `No step progression for ${Math.round(timeSinceLastUpdate / 1000)}s (max step duration exceeded)`;
      recommendation = 'terminate';
    }
    // Current step taking too long based on average
    else if (averageStepDuration > 0 && timeSinceLastUpdate > (averageStepDuration * this.STEP_TIMEOUT_MULTIPLIER)) {
      isStuck = true;
      reason = `Current step duration ${Math.round(timeSinceLastUpdate / 1000)}s exceeds ${this.STEP_TIMEOUT_MULTIPLIER}x average (${Math.round(averageStepDuration / 1000)}s)`;
      recommendation = 'investigate';
    }
    // Node might be unresponsive
    else if (timeSinceLastUpdate > this.NODE_UNRESPONSIVE_THRESHOLD) {
      reason = `No step updates for ${Math.round(timeSinceLastUpdate / 1000)}s (node might be unresponsive)`;
      recommendation = 'investigate';
      
      if (nodeId) {
        this.emit('node:unresponsive', nodeId, testId);
      }
    }

    return {
      testId,
      nodeId,
      isStuck,
      reason,
      stuckDuration: timeSinceLastUpdate,
      currentStepDuration: timeSinceLastUpdate,
      averageStepDuration,
      recommendation,
      lastStepUpdate: stepProgress.lastUpdated,
      stepProgressionStalled
    };
  }

  /**
   * Handle test without step progress data
   */
  private async handleTestWithoutStepProgress(test: any): Promise<void> {
    const testId = test.id;
    const now = Date.now();
    
    // Check how long the test has been running without step progress
    let testStartTime = now;
    if (test.startedAt) {
      testStartTime = new Date(test.startedAt).getTime();
    } else if (test.queuedAt) {
      testStartTime = new Date(test.queuedAt).getTime();
    }
    
    const timeSinceStart = now - testStartTime;
    
    // If test has been running for too long without any step progress, consider it stuck
    if (timeSinceStart > this.CRITICAL_STUCK_THRESHOLD) {
      logger.warn(`CentralizedStuckMonitoringService: Test ${testId} has no step progress after ${Math.round(timeSinceStart / 1000)}s`);
      
      const analysis: StuckTestAnalysis = {
        testId,
        nodeId: test.nodeId,
        isStuck: true,
        reason: `No step progress data after ${Math.round(timeSinceStart / 1000)}s`,
        stuckDuration: timeSinceStart,
        currentStepDuration: timeSinceStart,
        averageStepDuration: 0,
        recommendation: 'terminate',
        lastStepUpdate: 'never',
        stepProgressionStalled: true
      };
      
      this.totalStuckDetections++;

      // ENHANCED: Track stuck test event with detailed metrics
      this.handleStuckTestDetected(testId, analysis.nodeId || 'unknown', analysis);

      this.emit('test:stuck:detected', analysis);
      await this.handleStuckTest(analysis);
    }
  }

  /**
   * Handle stuck test recovery
   */
  private async handleStuckTest(analysis: StuckTestAnalysis): Promise<void> {
    const { testId, nodeId } = analysis;
    
    try {
      logger.error(`CentralizedStuckMonitoringService: Handling stuck test ${testId}: ${analysis.reason}`);
      
      // 1. Mark node as available if we know which node was processing the test
      if (nodeId) {
        try {
          await nodeRegistry.markNodeAsAvailable(nodeId);
          logger.info(`CentralizedStuckMonitoringService: Set node ${nodeId} as available after stuck test ${testId}`);
        } catch (nodeError) {
          logger.error(`CentralizedStuckMonitoringService: Error setting node ${nodeId} as available: ${(nodeError as Error).message}`);
        }
      }
      
      // 2. Update test status to failed in database
      try {
        await testManager.updateTestStatus(testId, TestStatus.FAILED, {
          error: `Test stuck: ${analysis.reason}`,
          endedAt: new Date(),
          stuckDetectedBy: 'CentralizedStuckMonitoringService'
        });
        logger.info(`CentralizedStuckMonitoringService: Updated test ${testId} status to failed`);
      } catch (statusError) {
        logger.error(`CentralizedStuckMonitoringService: Error updating test status: ${(statusError as Error).message}`);
      }
      
      // 3. Handle BullMQ job - move to completed with failure indication
      try {
        const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
        if (testQueue) {
          // Find the job in active state
          const activeJobs = await testQueue.getActive();
          const job = activeJobs.find(j => j.data?.id === testId);

          if (job) {
            const jobResult = {
              success: false,
              testId: testId,
              message: `Terminated by CentralizedStuckMonitoringService: ${analysis.reason}`,
              stuckDetection: analysis,
              finalOutcome: 'centralized_stuck_detection'
            };

            try {
              // Try to move job to completed with force flag
              await job.moveToCompleted(jobResult, job.token || '', true); // true to ignore lock
              logger.info(`CentralizedStuckMonitoringService: Moved stuck job to completed for test ${testId}`);
            } catch (moveError) {
              logger.warn(`CentralizedStuckMonitoringService: Failed to move job to completed normally, trying force fail for test ${testId}: ${(moveError as Error).message}`);

              // If moveToCompleted fails, try to fail the job
              try {
                await job.moveToFailed(new Error(`Stuck test terminated: ${analysis.reason}`), job.token || '', true);
                logger.info(`CentralizedStuckMonitoringService: Force failed stuck job for test ${testId}`);
              } catch (failError) {
                logger.error(`CentralizedStuckMonitoringService: Failed to force fail job for test ${testId}: ${(failError as Error).message}`);

                // Last resort: try to remove the job entirely
                try {
                  await job.remove();
                  logger.warn(`CentralizedStuckMonitoringService: Removed stuck job entirely for test ${testId}`);
                } catch (removeError) {
                  logger.error(`CentralizedStuckMonitoringService: Failed to remove stuck job for test ${testId}: ${(removeError as Error).message}`);
                }
              }
            }
          } else {
            logger.warn(`CentralizedStuckMonitoringService: No active BullMQ job found for stuck test ${testId}`);
          }
        }
      } catch (jobError) {
        logger.error(`CentralizedStuckMonitoringService: Error handling BullMQ job for test ${testId}: ${(jobError as Error).message}`);
      }
      
      // 4. Process failure result through CentralResultProcessor for proper counter updates
      await this.processStuckTestFailureResult(analysis);
      
      // 5. Clean up step progress data
      try {
        await stepProgressService.cleanupTestStepProgress(testId);
        logger.info(`CentralizedStuckMonitoringService: Cleaned up step progress for test ${testId}`);
      } catch (cleanupError) {
        logger.warn(`CentralizedStuckMonitoringService: Error cleaning up step progress: ${(cleanupError as Error).message}`);
      }
      
      this.totalRecoveries++;

      // ENHANCED: Track successful recovery
      this.handleStuckTestRecovered(testId, true);

      this.emit('test:stuck:recovered', testId, 'centralized_monitoring');

    } catch (error) {
      logger.error(`CentralizedStuckMonitoringService: Error handling stuck test ${testId}: ${(error as Error).message}`);
      this.totalFailures++;

      // ENHANCED: Track failed recovery
      this.handleStuckTestRecovered(testId, false);

      this.emit('test:stuck:failed', testId, error as Error);
    }
  }

  /**
   * Process failure result through CentralResultProcessor for proper counter updates
   */
  private async processStuckTestFailureResult(analysis: StuckTestAnalysis): Promise<void> {
    try {
      const { testId } = analysis;

      // Get test data from database to construct proper failure result
      const test = await testManager.getTest(testId);
      if (!test) {
        logger.warn(`CentralizedStuckMonitoringService: Could not find test ${testId} for failure result`);
        return;
      }

      // Create failure result for CentralResultProcessor
      const failureResult = {
        testId: testId,
        status: 'failed',
        result: {
          success: false,
          error: `Test execution failed: ${analysis.reason}`,
          stuckAnalysis: analysis,
          duration: analysis.stuckDuration,
          steps: [],
          summary: {
            passed: 0,
            failed: 1,
            total: 1,
            errors: 1
          }
        },
        runId: test.runId,
        executionId: test.executionId,
        scenarioId: test.scenarioId || 'unknown',
        nodeId: analysis.nodeId,
        userId: test.userId,
        executedUser: test.executedUser,
        executedUserName: test.executedUser,
        teamId: (test as any).teamId,
        companyId: (test as any).companyId,
        platform: test.platform,
        timestamp: new Date().toISOString(),
        source: 'stuck-monitoring'
      };

      // Process through CentralResultProcessor for proper counter updates
      const { centralResultProcessor } = await import('../result/centralResultProcessor.js');

      const processingResult = await centralResultProcessor.processResult(
        failureResult,
        'Stuck-Monitoring-Service',
        `stuck-${testId}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      );

      if (processingResult) {
        logger.info(`CentralizedStuckMonitoringService: Successfully processed stuck test failure through CentralResultProcessor for test ${testId}`);
      } else {
        logger.warn(`CentralizedStuckMonitoringService: Failed to process stuck test failure through CentralResultProcessor for test ${testId}`);
      }
    } catch (error) {
      logger.error(`CentralizedStuckMonitoringService: Error processing stuck test failure result: ${(error as Error).message}`);
    }
  }

  /**
   * Get monitoring statistics
   */
  public getStatistics(): any {
    return {
      isMonitoring: this.isMonitoring,
      monitoringInterval: this.MONITORING_INTERVAL,
      totalStuckDetections: this.totalStuckDetections,
      totalRecoveries: this.totalRecoveries,
      totalFailures: this.totalFailures,
      thresholds: {
        maxStepDuration: this.MAX_STEP_DURATION,
        stepTimeoutMultiplier: this.STEP_TIMEOUT_MULTIPLIER,
        criticalStuckThreshold: this.CRITICAL_STUCK_THRESHOLD,
        nodeUnresponsiveThreshold: this.NODE_UNRESPONSIVE_THRESHOLD,
        minStepsForAnalysis: this.MIN_STEPS_FOR_ANALYSIS
      }
    };
  }

  /**
   * Manually analyze a specific test for stuck conditions
   */
  public async analyzeSpecificTest(testId: string): Promise<StuckTestAnalysis | null> {
    try {
      const test = await testManager.getTest(testId);
      if (!test) {
        logger.warn(`CentralizedStuckMonitoringService: Test ${testId} not found for manual analysis`);
        return null;
      }

      const stepProgress = await stepProgressService.getTestStepProgress(testId);
      if (!stepProgress) {
        logger.warn(`CentralizedStuckMonitoringService: No step progress found for test ${testId}`);
        return null;
      }

      return await this.analyzeStepProgression(testId, stepProgress, test);
    } catch (error) {
      logger.error(`CentralizedStuckMonitoringService: Error analyzing test ${testId}: ${(error as Error).message}`);
      return null;
    }
  }

  /**
   * Manually force recovery of a stuck test
   */
  public async forceRecoverTest(testId: string): Promise<boolean> {
    try {
      logger.info(`CentralizedStuckMonitoringService: Manually forcing recovery of test ${testId}`);

      const analysis = await this.analyzeSpecificTest(testId);
      if (!analysis) {
        logger.warn(`CentralizedStuckMonitoringService: Cannot analyze test ${testId} for manual recovery`);
        return false;
      }

      // Force the analysis to indicate stuck for manual recovery
      analysis.isStuck = true;
      analysis.recommendation = 'terminate';
      analysis.reason = `Manual recovery requested: ${analysis.reason}`;

      await this.handleStuckTest(analysis);

      return true;
    } catch (error) {
      logger.error(`CentralizedStuckMonitoringService: Error forcing recovery of test ${testId}: ${(error as Error).message}`);
      return false;
    }
  }

  /**
   * Handle stuck test detection with enhanced metrics tracking
   */
  private handleStuckTestDetected(testId: string, nodeId: string, analysis: StuckTestAnalysis): void {
    const event: StuckTestEvent = {
      testId,
      nodeId,
      reason: analysis.reason,
      detectedAt: new Date(),
      recoverySuccess: false,
      details: analysis
    };

    // Store the event
    this.stuckTestEvents.set(testId, event);

    // Update metrics
    this.metrics.totalStuckTests++;
    this.metrics.stuckTestsByReason[event.reason] = (this.metrics.stuckTestsByReason[event.reason] || 0) + 1;
    this.metrics.stuckTestsByNode[nodeId] = (this.metrics.stuckTestsByNode[nodeId] || 0) + 1;
    this.metrics.recentStuckTests.unshift(event);

    // Keep only recent events (last 100)
    if (this.metrics.recentStuckTests.length > 100) {
      this.metrics.recentStuckTests = this.metrics.recentStuckTests.slice(0, 100);
    }

    logger.warn(`CentralizedStuckMonitoringService: Stuck test detected - ${testId} on node ${nodeId}: ${event.reason}`);
    this.emit('stuckTestDetected', event);
  }

  /**
   * Handle stuck test recovery with metrics tracking
   */
  private handleStuckTestRecovered(testId: string, success: boolean): void {
    const event = this.stuckTestEvents.get(testId);
    if (!event) {
      logger.warn(`CentralizedStuckMonitoringService: Recovery event for unknown stuck test ${testId}`);
      return;
    }

    // Update the event
    event.recoveredAt = new Date();
    event.recoveryTime = event.recoveredAt.getTime() - event.detectedAt.getTime();
    event.recoverySuccess = success;

    // Update metrics
    if (success) {
      this.metrics.totalRecoveredTests++;

      // Update average recovery time
      const totalRecoveryTime = this.metrics.averageRecoveryTime * (this.metrics.totalRecoveredTests - 1) + event.recoveryTime;
      this.metrics.averageRecoveryTime = totalRecoveryTime / this.metrics.totalRecoveredTests;
    } else {
      this.metrics.totalFailedRecoveries++;
    }

    // Update the event in recent events
    const recentIndex = this.metrics.recentStuckTests.findIndex(e => e.testId === testId);
    if (recentIndex >= 0) {
      this.metrics.recentStuckTests[recentIndex] = event;
    }

    logger.info(`CentralizedStuckMonitoringService: Stuck test recovery ${success ? 'succeeded' : 'failed'} - ${testId} (${event.recoveryTime}ms)`);
    this.emit('stuckTestRecovered', event);
  }

  /**
   * Get current metrics
   */
  public getMetrics(): StuckTestMetrics {
    return { ...this.metrics };
  }

  /**
   * Get stuck test event by ID
   */
  public getStuckTestEvent(testId: string): StuckTestEvent | undefined {
    return this.stuckTestEvents.get(testId);
  }

  /**
   * Get all active stuck tests (not yet recovered)
   */
  public getActiveStuckTests(): StuckTestEvent[] {
    return Array.from(this.stuckTestEvents.values()).filter(event => !event.recoveredAt);
  }

  /**
   * Generate monitoring report
   */
  public generateReport(): string {
    const metrics = this.getMetrics();
    const activeStuckTests = this.getActiveStuckTests();

    const report = [
      '=== Centralized Stuck Monitoring Service Report ===',
      `Total stuck tests detected: ${metrics.totalStuckTests}`,
      `Total recovered tests: ${metrics.totalRecoveredTests}`,
      `Total failed recoveries: ${metrics.totalFailedRecoveries}`,
      `Average recovery time: ${Math.round(metrics.averageRecoveryTime)}ms`,
      `Active stuck tests: ${activeStuckTests.length}`,
      '',
      'Stuck tests by reason:',
      ...Object.entries(metrics.stuckTestsByReason).map(([reason, count]) => `  ${reason}: ${count}`),
      '',
      'Stuck tests by node:',
      ...Object.entries(metrics.stuckTestsByNode).map(([node, count]) => `  ${node}: ${count}`),
      '',
      'Recent stuck tests:',
      ...metrics.recentStuckTests.slice(0, 10).map(event =>
        `  ${event.testId} (${event.nodeId}) - ${event.reason} - ${event.recoverySuccess ? 'Recovered' : 'Pending'}`
      ),
      '======================================================='
    ].join('\n');

    return report;
  }

  /**
   * Reset statistics and metrics
   */
  public resetStatistics(): void {
    this.totalStuckDetections = 0;
    this.totalRecoveries = 0;
    this.totalFailures = 0;

    // Reset enhanced metrics
    this.metrics = {
      totalStuckTests: 0,
      totalRecoveredTests: 0,
      totalFailedRecoveries: 0,
      averageRecoveryTime: 0,
      stuckTestsByReason: {},
      stuckTestsByNode: {},
      recentStuckTests: [],
      nodeGuardianActive: {},
      nodeGuardianTerminations: 0,
      coordinatedTerminations: 0,
      duplicateTerminationsPrevented: 0
    };
    this.stuckTestEvents.clear();

    logger.info('CentralizedStuckMonitoringService: Statistics and metrics reset');
  }
}

// Export singleton instance
export const centralizedStuckMonitoringService = CentralizedStuckMonitoringService.getInstance();
