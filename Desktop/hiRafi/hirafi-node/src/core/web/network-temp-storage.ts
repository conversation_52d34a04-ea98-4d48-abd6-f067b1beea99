/**
 * Network Temporary Storage
 * Handles temporary file storage for NetworkCollector to prevent memory leaks
 */

import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';
import { promisify } from 'util';
import { logger } from '../../utils/logger.js';
import { getTempDirectory } from '../../config/utils/directory-utils.js';
import { NetworkCollectorConfig } from '../../models/types.js';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

/**
 * Temporary storage manager for network data
 * Provides memory-safe storage for network requests and responses
 */
export class NetworkTempStorage {
  private config: NetworkCollectorConfig;
  private tempDir: string;
  private tempFiles: Set<string> = new Set();
  private requestFileCounter: number = 0;
  private responseFileCounter: number = 0;
  private isDestroyed: boolean = false;

  constructor(config: NetworkCollectorConfig, testId?: string) {
    this.config = config;
    
    // Create a unique temp directory for this collector instance
    const subdirectory = testId 
      ? `${this.config.tempFilePrefix}-${testId}-${Date.now()}`
      : `${this.config.tempFilePrefix}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    this.tempDir = getTempDirectory(subdirectory, true);
    
    logger.debug(`NetworkTempStorage: Initialized with temp directory: ${this.tempDir}`);
  }

  /**
   * Store requests data to temporary file
   * @param requests Array of request objects to store
   * @returns File path where data was stored
   */
  async storeRequests(requests: any[]): Promise<string> {
    if (this.isDestroyed) {
      throw new Error('NetworkTempStorage has been destroyed');
    }

    if (!requests || requests.length === 0) {
      return '';
    }

    const fileName = `requests-${this.requestFileCounter++}.json${this.config.compressionEnabled ? '.gz' : ''}`;
    const filePath = path.join(this.tempDir, fileName);

    try {
      const jsonData = JSON.stringify(requests);
      let dataToWrite: Buffer;

      if (this.config.compressionEnabled) {
        dataToWrite = await gzip(Buffer.from(jsonData, 'utf8'));
      } else {
        dataToWrite = Buffer.from(jsonData, 'utf8');
      }

      await fs.promises.writeFile(filePath, dataToWrite);
      this.tempFiles.add(filePath);

      logger.debug(`NetworkTempStorage: Stored ${requests.length} requests to ${fileName}`);
      return filePath;
    } catch (error: any) {
      logger.error(`NetworkTempStorage: Error storing requests: ${error.message}`);
      throw error;
    }
  }

  /**
   * Store responses data to temporary file
   * @param responses Array of response objects to store
   * @returns File path where data was stored
   */
  async storeResponses(responses: any[]): Promise<string> {
    if (this.isDestroyed) {
      throw new Error('NetworkTempStorage has been destroyed');
    }

    if (!responses || responses.length === 0) {
      return '';
    }

    const fileName = `responses-${this.responseFileCounter++}.json${this.config.compressionEnabled ? '.gz' : ''}`;
    const filePath = path.join(this.tempDir, fileName);

    try {
      const jsonData = JSON.stringify(responses);
      let dataToWrite: Buffer;

      if (this.config.compressionEnabled) {
        dataToWrite = await gzip(Buffer.from(jsonData, 'utf8'));
      } else {
        dataToWrite = Buffer.from(jsonData, 'utf8');
      }

      await fs.promises.writeFile(filePath, dataToWrite);
      this.tempFiles.add(filePath);

      logger.debug(`NetworkTempStorage: Stored ${responses.length} responses to ${fileName}`);
      return filePath;
    } catch (error: any) {
      logger.error(`NetworkTempStorage: Error storing responses: ${error.message}`);
      throw error;
    }
  }

  /**
   * Load requests from temporary file
   * @param filePath Path to the temporary file
   * @returns Array of request objects
   */
  async loadRequests(filePath: string): Promise<any[]> {
    if (this.isDestroyed) {
      throw new Error('NetworkTempStorage has been destroyed');
    }

    if (!filePath || !fs.existsSync(filePath)) {
      return [];
    }

    try {
      const fileData = await fs.promises.readFile(filePath);
      let jsonData: string;

      if (this.config.compressionEnabled && filePath.endsWith('.gz')) {
        const decompressed = await gunzip(fileData);
        jsonData = decompressed.toString('utf8');
      } else {
        jsonData = fileData.toString('utf8');
      }

      const requests = JSON.parse(jsonData);
      logger.debug(`NetworkTempStorage: Loaded ${requests.length} requests from ${path.basename(filePath)}`);
      return requests;
    } catch (error: any) {
      logger.error(`NetworkTempStorage: Error loading requests from ${filePath}: ${error.message}`);
      return [];
    }
  }

  /**
   * Load responses from temporary file
   * @param filePath Path to the temporary file
   * @returns Array of response objects
   */
  async loadResponses(filePath: string): Promise<any[]> {
    if (this.isDestroyed) {
      throw new Error('NetworkTempStorage has been destroyed');
    }

    if (!filePath || !fs.existsSync(filePath)) {
      return [];
    }

    try {
      const fileData = await fs.promises.readFile(filePath);
      let jsonData: string;

      if (this.config.compressionEnabled && filePath.endsWith('.gz')) {
        const decompressed = await gunzip(fileData);
        jsonData = decompressed.toString('utf8');
      } else {
        jsonData = fileData.toString('utf8');
      }

      const responses = JSON.parse(jsonData);
      logger.debug(`NetworkTempStorage: Loaded ${responses.length} responses from ${path.basename(filePath)}`);
      return responses;
    } catch (error: any) {
      logger.error(`NetworkTempStorage: Error loading responses from ${filePath}: ${error.message}`);
      return [];
    }
  }

  /**
   * Get all temporary file paths
   * @returns Array of temporary file paths
   */
  getTempFiles(): string[] {
    return Array.from(this.tempFiles);
  }

  /**
   * Get storage statistics
   * @returns Storage statistics object
   */
  getStats(): any {
    const stats = {
      tempDir: this.tempDir,
      totalFiles: this.tempFiles.size,
      requestFiles: this.requestFileCounter,
      responseFiles: this.responseFileCounter,
      compressionEnabled: this.config.compressionEnabled,
      isDestroyed: this.isDestroyed
    };

    // Calculate total size if possible
    try {
      let totalSize = 0;
      for (const filePath of this.tempFiles) {
        if (fs.existsSync(filePath)) {
          const stat = fs.statSync(filePath);
          totalSize += stat.size;
        }
      }
      (stats as any).totalSizeBytes = totalSize;
    } catch (error: any) {
      logger.debug(`NetworkTempStorage: Could not calculate total size: ${error.message}`);
    }

    return stats;
  }

  /**
   * Clean up all temporary files and directories
   */
  async cleanup(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    try {
      // Delete all temporary files
      for (const filePath of this.tempFiles) {
        try {
          if (fs.existsSync(filePath)) {
            await fs.promises.unlink(filePath);
            logger.debug(`NetworkTempStorage: Deleted temp file ${path.basename(filePath)}`);
          }
        } catch (error: any) {
          logger.warn(`NetworkTempStorage: Error deleting temp file ${filePath}: ${error.message}`);
        }
      }

      // Remove the temp directory if it's empty
      try {
        if (fs.existsSync(this.tempDir)) {
          const files = await fs.promises.readdir(this.tempDir);
          if (files.length === 0) {
            await fs.promises.rmdir(this.tempDir);
            logger.debug(`NetworkTempStorage: Removed temp directory ${this.tempDir}`);
          }
        }
      } catch (error: any) {
        logger.debug(`NetworkTempStorage: Could not remove temp directory: ${error.message}`);
      }

      this.tempFiles.clear();
      this.isDestroyed = true;
      
      logger.debug('NetworkTempStorage: Cleanup completed');
    } catch (error: any) {
      logger.error(`NetworkTempStorage: Error during cleanup: ${error.message}`);
      throw error;
    }
  }
}
