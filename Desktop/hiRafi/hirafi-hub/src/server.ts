/**
 * Main server file
 * Sets up HTTP server and initializes all modules
 */

import express, { Application } from 'express';
import cors from 'cors';
import { createServer, Server as HttpServer } from 'http';
import multer from 'multer';
import { config } from './config/index.js';
import { logger } from './utils/logger.js';
import { webSocketConnector } from './connectors/index.js';
import apiRoutes from './api/routes/index.js';
import { initializeCollections } from './services/mongo/dbConnection.js';
import { redisConnection } from './services/redis/redisConnection.js';
import { queueService } from './services/redis/queueService.js';
import { bullBoardService } from './services/redis/bullBoard.js';
// Import the result queue worker for asynchronous result processing
import { resultQueueWorker } from './services/result/resultQueueWorker.js';
// Import the new services
import { nodeRegistry } from './services/node/index.js';
import { testQueueService, testDistributor } from './services/test/index.js';
// Legacy result handler has been removed in favor of the asynchronous result queue worker
import { testManager } from './core/test-manager/index.js';
import { centralizedStuckMonitoringService } from './services/monitoring/centralizedStuckMonitoringService.js';
import { initializeDeviceAvailabilityScheduler } from './services/saucelabs/DeviceAvailabilityScheduler.js';

// Create Express application
const app: Application = express();

// Configure middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Configure multer for file uploads
const storage = multer.memoryStorage(); // Store files in memory as Buffer objects
const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB max file size
  },
  fileFilter: (_req: Express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    // Accept only .apk and .ipa files
    if (file.mimetype === 'application/vnd.android.package-archive' ||
        file.originalname.endsWith('.apk') ||
        file.originalname.endsWith('.ipa')) {
      cb(null, true);
    } else {
      cb(new Error('Only .apk and .ipa files are allowed'));
    }
  }
});

// Make multer available to routes
app.locals.upload = upload;

// Create HTTP server with optimized timeout settings
const server: HttpServer = createServer(app);

// Configure server timeouts for WebSocket stability
server.timeout = 300000; // 5 minutes
server.keepAliveTimeout = 65000; // 65 seconds (slightly higher than typical load balancer timeout)
server.headersTimeout = 66000; // 66 seconds (higher than keepAliveTimeout)

// Basic route for server status
app.get('/', (_req, res) => {
  res.json({ message: 'Test Hub Server Running', status: 'OK' });
});

// Mount API routes
app.use('/api', apiRoutes);

// MongoDB error handler is already added before routes

// Mount Bull Board UI (if Redis is enabled)
if (config.connections.redis.enabled) {
  bullBoardService.initialize().then(serverAdapter => {
    app.use('/admin/queues', serverAdapter.getRouter());
    logger.info('Bull Board UI mounted at /admin/queues');
  }).catch(error => {
    logger.error(`Failed to initialize Bull Board: ${error}`);
  });
}

// Create a specific route for WebSocket connections
app.get('/ws/node', (_req, res) => {
  res.send('WebSocket endpoint for test nodes');
});

// Start the server
async function startServer() {
  try {
    // Initialize WebSocket connector
    webSocketConnector.initialize(server);

    // Initialize Redis connection
    const redisSuccess = await redisConnection.initialize();
    if (redisSuccess) {
      logger.info('Redis connection initialized');

      // Clean up Redis keys, excluding test-queue and result-queue
      try {
        const redisClient = redisConnection.getClient();
        if (redisClient) {
          logger.info('Starting Redis cleanup process...');
          const allKeys = await redisClient.keys('*');
          const keysToDelete: string[] = [];
          const protectedQueues = ['test-queue', 'result-queue'];
          for (const key of allKeys) {
            let isProtected = false;
            for (const protectedQueue of protectedQueues) {
              const bullQueuePrefix = `bull:${protectedQueue}`;
              // Refined protection: Check if key starts with 'bull:queuename:' or is exactly 'bull:queuename'
              if (key.startsWith(bullQueuePrefix + ':') || key === bullQueuePrefix) {
                isProtected = true;
                break;
              }
            }
            // Preserve Bull Board internal keys if Bull Board is enabled
            if (config.connections.redis.enabled && key.startsWith('bull:bull:')) {
                isProtected = true;
            }

            if (!isProtected) {
              keysToDelete.push(key);
            }
          }

          if (keysToDelete.length > 0) {

            // Log a few keys to be deleted for debugging, but not all if the list is very long.
            const sampleKeysToDelete = keysToDelete.slice(0, 10).join(', ');
            logger.debug(`Sample keys to be deleted: ${sampleKeysToDelete}${keysToDelete.length > 10 ? '...' : ''}`);

            let deletedCount = 0;
            for (const key of keysToDelete) {
              try {
                await redisClient.del(key);
                deletedCount++;
              } catch (delError: any) {
                logger.warn(`Error deleting Redis key ${key}: ${delError.message || delError}`);
              }
            }
            logger.info(`Redis cleanup completed. ${deletedCount} keys deleted.`);
          } else {
            logger.info('No Redis keys found to clean up (excluding test-queue and result-queue related keys).');
          }
        } else {
          logger.warn('Redis client not available, skipping Redis cleanup.');
        }
      } catch (cleanupError: any) {
        logger.warn(`Error during Redis cleanup process: ${cleanupError.message || cleanupError}`);
      }

      // Import and initialize LockService
      try {
        const { lockService } = await import('./services/redis/lockService.js');
        const lockServiceSuccess = lockService.initialize();
        if (lockServiceSuccess) {
          logger.info('Lock service initialized successfully');
        } else {
          logger.warn('Lock service failed to initialize');
        }
      } catch (lockServiceError) {
        logger.error(`Error initializing lock service: ${lockServiceError}`);
      }

      // Initialize Queue Service
      const queueServiceSuccess = await queueService.initialize();
      if (queueServiceSuccess) {
        logger.info('Queue service initialized');

        // Initialize Result Queue Worker for asynchronous result processing
        try {
          const resultQueueInitialized = await resultQueueWorker.initialize();
          if (resultQueueInitialized) {
            logger.info('Result Queue Worker initialized successfully - asynchronous result processing enabled');

            // Set up event listeners for the result queue worker
            resultQueueWorker.on('result:processed', (testId, status, source) => {
              logger.info(`Result processed for test ${testId} with status ${status} from ${source}`);
            });

            resultQueueWorker.on('result:failed', (testId, error) => {
              logger.error(`Failed to process result for test ${testId}: ${error}`);
            });
          } else {
            logger.warn('Result Queue Worker failed to initialize - falling back to synchronous processing');
          }
        } catch (resultQueueError) {
          logger.error(`Error initializing Result Queue Worker: ${resultQueueError}`);
        }

        // Initialize Step Progress Queue Worker for asynchronous step progress processing
        try {
          const { createStepProgressQueueWorker } = await import('./services/step-progress/step-progress-queue-worker.js');
          const stepProgressQueueWorker = createStepProgressQueueWorker();
          const stepProgressQueueInitialized = await stepProgressQueueWorker.initialize(webSocketConnector);
          if (stepProgressQueueInitialized) {
            logger.info('Step Progress Queue Worker initialized successfully - asynchronous step progress processing enabled');

            // Set up event listeners for the step progress queue worker
            stepProgressQueueWorker.on('step-progress:processed', (testId, jobId) => {
              logger.debug(`Step progress processed for test ${testId}, job ${jobId}`);
            });

            stepProgressQueueWorker.on('step-progress:failed', (testId, jobId, error) => {
              logger.error(`Failed to process step progress for test ${testId}, job ${jobId}: ${error}`);
            });

            stepProgressQueueWorker.on('step-progress:ui-broadcast', (testId, data) => {
              logger.debug(`Step progress broadcasted to UI for test ${testId}`);
            });
          } else {
            logger.warn('Step Progress Queue Worker failed to initialize - step progress will not be processed');
          }
        } catch (stepProgressQueueError) {
          logger.error(`Error initializing Step Progress Queue Worker: ${stepProgressQueueError}`);
        }

        // Initialize the new services
        try {
          // Initialize Node Registry
          const nodeRegistryInitialized = await nodeRegistry.initialize();
          if (nodeRegistryInitialized) {
            logger.info('Node Registry initialized successfully');
          } else {
            logger.warn('Node Registry failed to initialize');
          }

          // Initialize Test Queue Service
          const testQueueInitialized = await testQueueService.initialize();
          if (testQueueInitialized) {
            logger.info('Test Queue Service initialized successfully');
          } else {
            logger.warn('Test Queue Service failed to initialize');
          }

          // Initialize Test Distributor
          const testDistributorInitialized = await testDistributor.initialize();
          if (testDistributorInitialized) {
            logger.info('Test Distributor initialized successfully - pull-based test distribution enabled');
          } else {
            logger.warn('Test Distributor failed to initialize');
          }
        } catch (newServicesError) {
          logger.error(`Error initializing new services: ${newServicesError}`);
        }
      } else {
        logger.warn('Queue service failed to initialize');
      }
    } else {
      logger.warn('Redis connection failed to initialize');
    }

    // Initialize all database collections
    try {
      const dbSuccess = await initializeCollections();
      if (dbSuccess) {
        logger.info('[MONGODB] Database collections initialized');
      } else {
        logger.warn('[MONGODB] Database collections initialization had issues');
      }
    } catch (dbError) {
      logger.error(`[MONGODB] Error initializing database collections: ${dbError}`);
      // Continue server startup even if MongoDB initialization fails
      // The reconnection mechanism will try to reconnect automatically
      logger.info('[MONGODB] Server will continue startup and attempt to reconnect to MongoDB');
    }

    // Initialize test manager event listeners
    // This must be done after nodeManager is initialized to avoid circular dependencies
    testManager.initialize();
    logger.info('Test manager event listeners initialized');

    // Initialize centralized EventHandler
    try {
      const { eventHandler } = await import('./core/event-handler/index.js');
      await eventHandler.initialize({
        webSocketConnector,
        nodeRegistry,
        testManager
      });
      logger.info('Centralized EventHandler initialized successfully');
    } catch (eventHandlerError) {
      logger.error(`Error initializing EventHandler: ${eventHandlerError}`);
    }

    // Initialize centralized stuck monitoring service (replaces old stuck job recovery service)
    centralizedStuckMonitoringService.startMonitoring();
    logger.info('Centralized stuck monitoring service started');

    // Initialize device availability scheduler for SauceLabs
    try {
      await initializeDeviceAvailabilityScheduler();
      logger.info('Device availability scheduler initialized successfully');
    } catch (schedulerError) {
      logger.error(`Error initializing device availability scheduler: ${schedulerError}`);
    }

    // Initialize Schedule Manager for scheduled test runs
    try {
      const { scheduleManager } = await import('./core/test-manager/scheduleManager.js');
      scheduleManager.start();
      logger.info('Schedule Manager initialized successfully - scheduled test runs enabled');
    } catch (scheduleManagerError) {
      logger.error(`Error initializing Schedule Manager: ${scheduleManagerError}`);
    }

    // Start HTTP server
    server.listen(config.server.port, () => {
      logger.info(`Server started on port ${config.server.port}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });

    // Handle server shutdown
    process.on('SIGTERM', () => shutdown().catch(err => logger.error(`Error during shutdown: ${err}`)));
    process.on('SIGINT', () => shutdown().catch(err => logger.error(`Error during shutdown: ${err}`)));
  } catch (error) {
    logger.error(`Failed to start server: ${error}`);
    process.exit(1);
  }
}

// Graceful shutdown
async function shutdown() {
  logger.info('Shutting down server...');

  // Stop WebSocket
  webSocketConnector.stop().catch((error: Error) => {
    logger.error(`Error stopping WebSocket connector: ${error}`);
  });

  // Stop centralized stuck monitoring service
  centralizedStuckMonitoringService.stopMonitoring();
  logger.info('Centralized stuck monitoring service stopped');

  // Teardown centralized EventHandler
  try {
    const { eventHandler } = await import('./core/event-handler/index.js');
    eventHandler.teardownAllEvents();
    logger.info('Centralized EventHandler teardown completed');
  } catch (eventHandlerError) {
    logger.error(`Error tearing down EventHandler: ${eventHandlerError}`);
  }

  // Shutdown device availability scheduler
  try {
    const { shutdownDeviceAvailabilityScheduler } = await import('./services/saucelabs/DeviceAvailabilityScheduler.js');
    await shutdownDeviceAvailabilityScheduler();
    logger.info('Device availability scheduler shutdown complete');
  } catch (schedulerError) {
    logger.error(`Error shutting down device availability scheduler: ${schedulerError}`);
  }

  // Shutdown Schedule Manager
  try {
    const { scheduleManager } = await import('./core/test-manager/scheduleManager.js');
    scheduleManager.stop();
    logger.info('Schedule Manager shutdown complete');
  } catch (scheduleManagerError) {
    logger.error(`Error shutting down Schedule Manager: ${scheduleManagerError}`);
  }

  // Close HTTP server
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });

  // Close Result Queue Worker
  resultQueueWorker.close().catch(error => {
    logger.error(`Error closing Result Queue Worker: ${error}`);
  });

  // Close the new services
  testDistributor.close().catch(error => {
    logger.error(`Error closing Test Distributor: ${error}`);
  });

  testQueueService.close().catch(error => {
    logger.error(`Error closing Test Queue Service: ${error}`);
  });

  nodeRegistry.close().catch(error => {
    logger.error(`Error closing Node Registry: ${error}`);
  });

  // Legacy Redis Result Handler has been removed

  // Close Queue Service
  queueService.close().catch(error => {
    logger.error(`Error closing queue service: ${error}`);
  });

  // Close Redis connection
  redisConnection.close().catch(error => {
    logger.error(`Error closing Redis connection: ${error}`);
  });

  // Force exit after timeout
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 10000);
}

// Start the server
startServer().catch(error => {
  logger.error(`Server startup error: ${error}`);
  process.exit(1);
});

export { app, server };