{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:prod": "cross-env NODE_ENV=production next build", "start": "next start", "start:prod": "cross-env NODE_ENV=production next start", "lint": "next lint"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hello-pangea/dnd": "latest", "@hookform/resolvers": "^3.9.1", "@next/third-parties": "^15.3.2", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@types/js-cookie": "^3.0.6", "@types/papaparse": "^5.3.15", "antd": "^5.24.9", "autoprefixer": "^10.4.20", "axios": "^1.6.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "combined-stream": "^1.0.8", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.6.3", "html2canvas": "^1.4.1", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.4", "immer": "^10.1.1", "input-otp": "1.4.1", "js-cookie": "^3.0.5", "lucide-react": "^0.454.0", "next": "^15.3.2", "next-themes": "^0.4.4", "papaparse": "^5.5.2", "rc-mentions": "^2.20.0", "react": "^19", "react-day-picker": "^9.7.0", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-organizational-chart": "^2.2.1", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "recharts": "2.15.0", "socks-proxy-agent": "^8.0.2", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^5.0.4"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}