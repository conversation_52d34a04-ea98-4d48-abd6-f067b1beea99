/**
 * Unified Video Recorder
 * Handles video recording for all platforms (Android and Web)
 * Consolidates AndroidVideoRecorder and WebVideoRecorder
 */

import { IVideoRecorder } from '../interfaces/video-recorder.interface.js';
import { IArtifactStorage } from '../storage/artifact-storage.interface.js';
import { logger } from '../../../utils/logger.js';
import {
  generateVideoPath,
  ensureDirectoryExists,
  fileExists,
  deleteFileWithRetry
} from '../utils/file-utils.js';
import {
  createVideoArtifact,
  validateArtifact
} from '../utils/artifact-utils.js';
import {
  handleAsyncOperation,
  Result,
  createErrorResult
} from '../utils/error-handling.js';
import { startVideoRecording, stopVideoRecording } from 'rfi-ai-android';
import { PuppeteerScreenRecorder } from 'puppeteer-screen-recorder';
import { IExecutionCoordinator } from '../../test-execution/interfaces/execution-coordinator.interface.js';

/**
 * Unified Video Recorder
 * Implements video recording logic for all platforms
 */
export class UnifiedVideoRecorder implements IVideoRecorder {
  protected artifactStorage: IArtifactStorage;
  protected videoDir: string;
  protected config: any;
  protected platform: 'web' | 'mobile';
  protected isRecordingActive: boolean = false;
  protected currentTestId: string | null = null;
  protected recordingStartTime: number = 0;

  // Platform-specific properties
  private webRecorder: PuppeteerScreenRecorder | null = null;
  private currentAndroidDevice: any = null;

  // Upload tracking to prevent race conditions
  private pendingUploads: Map<string, Promise<string>> = new Map();
  private uploadInProgress: Set<string> = new Set();

  private executionCoordinator: IExecutionCoordinator;

  /**
   * Create a new BaseVideoRecorder
   * @param artifactStorage Storage service for videos
   * @param platform Platform identifier
   * @param config Configuration object
   */
  constructor(
    artifactStorage: IArtifactStorage,
    platform: 'web' | 'mobile',
    config: any = {},
    executionCoordinator: IExecutionCoordinator
  ) {
    this.artifactStorage = artifactStorage;
    this.platform = platform;
    this.videoDir = config.videoDir || 'videos';
    this.config = config;
    this.executionCoordinator = executionCoordinator;

    logger.debug(`BaseVideoRecorder: Initialized for ${platform} platform`);
  }

  /**
   * Start recording for any platform
   * @param target Platform-specific target (Page, AndroidAgent, etc.)
   * @param testId Test identifier
   * @param options Optional recording options
   * @returns Promise resolving when recording has started
   */
  async startRecording(target: any, testId: string, options?: any): Promise<void> {
    try {
      // ENHANCED SAFETY: Additional validation to prevent unnecessary recording
      if (!testId || testId.trim() === '') {
        throw new Error('Invalid test ID provided for video recording');
      }

      // Log recording initiation for debugging empty scenario issues
      logger.info(`UnifiedVideoRecorder: Initiating recording for test ${testId} on ${this.platform} platform`);

      // Common setup from base class
      await this.startRecordingSetup(testId);

      if (this.platform === 'web') {
        await this.startWebRecording(target, testId);
      } else {
        await this.startAndroidRecording(target, testId, options);
      }

      this.isRecordingActive = true;
      logger.info(`UnifiedVideoRecorder: Started recording for test ${testId} on ${this.platform} platform`);
    } catch (error: any) {
      logger.error(`UnifiedVideoRecorder: Error starting recording for test ${testId}: ${error.message}`);
      this.isRecordingActive = false;
      this.webRecorder = null;
      this.currentAndroidDevice = null;
      throw error;
    }
  }

  /**
   * Stop recording and save the video
   * @returns Promise resolving to the URL of the stored video
   */
  async stopRecording(): Promise<string> {
    try {
      if (!this.isRecordingActive || !this.currentTestId) {
        logger.warn(`UnifiedVideoRecorder: No active recording to stop`);
        return '';
      }

      const testId = this.currentTestId;
      logger.info(`UnifiedVideoRecorder: Stopping recording for test ${testId}`);

      if (this.platform === 'web') {
        await this.stopWebRecording();
      } else {
        await this.stopAndroidRecording();
      }

      // Complete recording using base class with testId parameter
      return await this.completeRecording(testId);
    } catch (error: any) {
      logger.error(`UnifiedVideoRecorder: Error stopping recording: ${error.message}`);
      this.isRecordingActive = false;
      this.currentTestId = null;
      this.webRecorder = null;
      this.currentAndroidDevice = null;
      throw error;
    }
  }

  /**
   * Stop recording and save the video asynchronously
   * @param onVideoUploaded Callback function called when video upload completes
   * @returns Promise resolving immediately after recording stops (before upload)
   */
  async stopRecordingAsync(onVideoUploaded?: (testId: string, videoUrl: string, error?: Error) => void): Promise<void> {
    try {
      if (!this.isRecordingActive || !this.currentTestId) {
        logger.warn(`UnifiedVideoRecorder: No active recording to stop`);
        return;
      }

      const testId = this.currentTestId;
      logger.info(`UnifiedVideoRecorder: Stopping recording for test ${testId} (async mode)`);

      if (this.platform === 'web') {
        await this.stopWebRecording();
      } else {
        await this.stopAndroidRecording();
      }

      this.isRecordingActive = false;
      this.recordingStartTime = 0;

      this.uploadVideoAsync(testId, onVideoUploaded);

    } catch (error: any) {
      logger.error(`UnifiedVideoRecorder: Error stopping recording (async): ${error.message}`);
      if (onVideoUploaded) {
        onVideoUploaded(this.currentTestId || 'unknown', '', error);
      }
      throw error;
    }
  }

  /**
   * Start web recording using Puppeteer
   * @param page Puppeteer page
   * @param testId Test identifier
   */
  private async startWebRecording(page: any, testId: string): Promise<void> {
    const videoPath = this.getVideoPath(testId);

    // Configure recorder with CPU-optimized settings using environment variables
    const config = {
      followNewTab: true,
      fps: this.config.fps || parseInt(process.env.VIDEO_FPS || '8'),  // Reduced from 10 to 8 FPS for smaller files
      ffmpeg_Path: this.config.ffmpegPath || null,
      videoFrame: {
        width: this.config.width || parseInt(process.env.VIDEO_WIDTH || '960'),   // Reduced from 1280 to 960 for smaller files
        height: this.config.height || parseInt(process.env.VIDEO_HEIGHT || '540')   // Reduced from 720 to 540 for smaller files
      },
      aspectRatio: this.config.aspectRatio || '16:9',
      // Additional CPU optimization settings with lower quality for smaller files
      videoCrf: 32,  // Increased from 28 to 32 for lower quality and smaller file size
      videoCodec: 'libx264',
      videoPreset: 'ultrafast',  // Fastest encoding preset
      videoBitrate: this.config.bitrate || parseInt(process.env.VIDEO_BITRATE || '600'),  // Reduced from 1000 to 600 for smaller files
      autopad: {
        color: 'black'
      }
    };

    // Create and start recorder
    this.webRecorder = new PuppeteerScreenRecorder(page, config);
    await this.webRecorder.start(videoPath);
  }

  /**
   * Start Android recording using misoai-android
   * @param agent Android agent
   * @param testId Test identifier
   * @param options Optional recording options
   */
  private async startAndroidRecording(agent: any, testId: string, options?: any): Promise<void> {
    if (!agent || !agent.page) {
      throw new Error('Invalid Android agent or page');
    }

    this.currentAndroidDevice = agent;

    // CPU-optimized recording options with reduced quality for smaller files
    const recordingOptions = {
      timeLimit: this.config.timeLimit || 1800,    // 30 minutes max
      bitRate: this.config.bitRate || 1000000,     // Further reduced from 2 Mbps to 1 Mbps for smaller files
      size: this.config.size || '960x540'          // Reduced from 1280x720 to 960x540 for smaller files
    };

    // Apply custom options if provided
    if (options) {
      Object.assign(recordingOptions, options);
      logger.info(`UnifiedVideoRecorder: Using custom recording options: ${JSON.stringify(recordingOptions)}`);
    }

    // Start recording using misoai-android's startVideoRecording helper
    await startVideoRecording(agent.page, recordingOptions);
  }

  /**
   * Stop web recording with enhanced process management
   */
  private async stopWebRecording(): Promise<void> {
    if (!this.webRecorder) {
      throw new Error('No web recorder to stop');
    }

    try {
      // Enhanced FFmpeg process termination with timeout
      const stopPromise = this.webRecorder.stop();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('FFmpeg stop timeout')), 10000)
      );

      await Promise.race([stopPromise, timeoutPromise]);
      logger.info('UnifiedVideoRecorder: Web recording stopped successfully');
    } catch (error: any) {
      logger.error(`UnifiedVideoRecorder: Error stopping web recording: ${error.message}`);

      // Force terminate FFmpeg process if normal stop fails
      await this.forceTerminateFFmpegProcess();
      throw error;
    } finally {
      // Always clear the recorder reference
      this.webRecorder = null;
    }
  }

  /**
   * Force terminate FFmpeg process using direct process termination
   */
  private async forceTerminateFFmpegProcess(): Promise<void> {
    try {
      if (this.webRecorder && (this.webRecorder as any).ffmpegProcess) {
        const ffmpegProcess = (this.webRecorder as any).ffmpegProcess;
        const pid = ffmpegProcess.pid;

        if (pid) {
          logger.warn(`UnifiedVideoRecorder: Force terminating FFmpeg process ${pid}`);

          // Direct process termination as fallback
          try {
            ffmpegProcess.kill('SIGTERM');

            // Wait a bit for graceful termination
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Force kill if still running
            if (!ffmpegProcess.killed) {
              ffmpegProcess.kill('SIGKILL');
            }

            logger.info(`UnifiedVideoRecorder: Successfully terminated FFmpeg process ${pid}`);
          } catch (killError: any) {
            logger.error(`UnifiedVideoRecorder: Error killing FFmpeg process ${pid}: ${killError.message}`);
          }
        }
      }
    } catch (error: any) {
      logger.error(`UnifiedVideoRecorder: Error force terminating FFmpeg process: ${error.message}`);
    }
  }

  /**
   * Stop Android recording
   */
  private async stopAndroidRecording(): Promise<void> {
    if (!this.currentAndroidDevice) {
      throw new Error('No Android device to stop recording');
    }

    // Use currentTestId while it's still available, or throw error if already null
    if (!this.currentTestId) {
      throw new Error('No test ID available for Android recording');
    }

    const videoPath = this.getVideoPath(this.currentTestId);

    try {
      // Stop recording using misoai-android's stopVideoRecording helper
      await stopVideoRecording(this.currentAndroidDevice.page, {
        filePath: videoPath,
        createDir: true
      });

      logger.info(`UnifiedVideoRecorder: Saved Android video to ${videoPath}`);
    } catch (recordingError: any) {
      logger.error(`UnifiedVideoRecorder: Error with misoai-android helper: ${recordingError.message}`);

      // Try fallback method
      await this.fallbackStopAndroidRecording(videoPath);
    }

    this.currentAndroidDevice = null;
  }

  /**
   * Fallback Android recording stop method
   * @param videoPath Path to save video
   */
  private async fallbackStopAndroidRecording(videoPath: string): Promise<void> {
    if (!this.currentAndroidDevice) {
      throw new Error('No current device for fallback recording');
    }

    if (typeof this.currentAndroidDevice.page.stopRecordingScreen === 'function') {
      logger.info(`UnifiedVideoRecorder: Trying fallback recording method`);

      // This returns base64 encoded video data
      const videoBase64 = await this.currentAndroidDevice.page.stopRecordingScreen();

      // Convert base64 to buffer and save
      const buffer = Buffer.from(videoBase64, 'base64');
      const fs = await import('fs');
      await fs.promises.writeFile(videoPath, buffer);

      logger.info(`UnifiedVideoRecorder: Saved video using fallback method to ${videoPath}`);
    } else {
      throw new Error('No fallback recording method available');
    }
  }

  /**
   * Start the recording process with common setup
   * @param testId Test identifier
   * @returns Promise resolving when setup is complete
   */
  protected async startRecordingSetup(testId: string): Promise<void> {
    const context = `BaseVideoRecorder.startRecordingSetup`;

    if (this.isRecordingActive) {
      logger.warn(`${context}: Recording already in progress, stopping previous recording`);
      await this.stopRecording();
    }

    this.currentTestId = testId;
    this.recordingStartTime = Date.now();

    // Ensure video directory exists
    const videoPath = this.getVideoPath(testId);
    await ensureDirectoryExists(videoPath.substring(0, videoPath.lastIndexOf('/')));

    logger.debug(`${context}: Recording setup completed for test ${testId}`);
  }

  /**
   * Complete the recording process with common cleanup
   * @param testId Test identifier to complete recording for
   * @returns Promise resolving to the URL of the stored video
   */
  protected async completeRecording(testId?: string): Promise<string> {
    const context = `BaseVideoRecorder.completeRecording`;

    // Use provided testId or fall back to currentTestId for backward compatibility
    const recordingTestId = testId || this.currentTestId;
    
    if (!recordingTestId) {
      throw new Error('No active recording to complete');
    }

    const result = await handleAsyncOperation(async () => {
      const videoPath = this.getVideoPath(recordingTestId);

      // Verify video file exists
      if (!(await fileExists(videoPath))) {
        throw new Error(`Video file not found at ${videoPath}`);
      }

      logger.info(`${context}: Video saved to ${videoPath}`);

      // Create artifact
      const artifact = createVideoArtifact(
        recordingTestId,
        this.platform,
        videoPath,
        this.getCustomTags()
      );

      // Validate artifact
      if (!validateArtifact(artifact)) {
        throw new Error('Invalid artifact created');
      }

      // Upload to storage
      const uploadResult = await this.artifactStorage.uploadArtifact(artifact);

      logger.info(`${context}: Video uploaded successfully`);

      // Clean up local video file after successful upload
      try {
        const deleted = await deleteFileWithRetry(videoPath);
        if (deleted) {
          logger.info(`${context}: Local video file deleted: ${videoPath}`);
        } else {
          logger.warn(`${context}: Failed to delete local video file: ${videoPath}`);
        }
      } catch (cleanupError: any) {
        logger.warn(`${context}: Error during local video cleanup: ${cleanupError.message}`);
        // Don't throw error for cleanup failure, just log it
      }

      return uploadResult.url || uploadResult.storagePath;
    }, context);

    // Only reset state after successful upload completion
    if (result.success) {
      this.isRecordingActive = false;
      this.currentTestId = null;
      this.recordingStartTime = 0;
      logger.info(`${context}: State reset after successful upload for test ${recordingTestId}`);
    } else {
      logger.error(`${context}: Upload failed, keeping state for potential retry for test ${recordingTestId}`);
    }

    if (!result.success) {
      throw new Error((result as any).error || 'Video recording operation failed');
    }

    return result.data!;
  }

  private uploadVideoAsync(testId: string, onVideoUploaded?: (testId: string, videoUrl: string, error?: Error) => void): void {
    const uploadPromise = this.completeRecording(testId).then(videoUrl => {
        if (onVideoUploaded) {
            onVideoUploaded(testId, videoUrl, videoUrl ? undefined : new Error('Upload failed'));
        }
        return videoUrl;
    }).catch(error => {
        logger.error(`uploadVideoAsync: Error for test ${testId}: ${error.message}`);
        if (onVideoUploaded) {
            onVideoUploaded(testId, '', error);
        }
        return ''; // Return empty string on failure
    });

    this.pendingUploads.set(testId, uploadPromise);

    uploadPromise.finally(() => {
        this.pendingUploads.delete(testId);
    });
  }

  /**
   * Get the path where a video would be stored
   * @param testId Test identifier
   * @returns Path to the video
   */
  getVideoPath(testId: string): string {
    return generateVideoPath(this.videoDir, testId, this.platform);
  }

  /**
   * Check if recording is currently active
   * @returns True if recording is active
   */
  isRecording(): boolean {
    return this.isRecordingActive;
  }

  /**
   * Check if there are pending video uploads
   * @returns True if uploads are in progress
   */
  hasPendingUploads(): boolean {
    return this.uploadInProgress.size > 0;
  }

  /**
   * Wait for all pending uploads to complete
   * @param timeout Maximum time to wait in milliseconds
   * @returns Promise that resolves when all uploads complete or timeout
   */
  async waitForPendingUploads(timeout: number = 30000): Promise<void> {
    const context = `BaseVideoRecorder.waitForPendingUploads`;

    if (this.pendingUploads.size === 0) {
      logger.debug(`${context}: No pending uploads to wait for`);
      return;
    }

    logger.info(`${context}: Waiting for ${this.pendingUploads.size} pending uploads to complete`);

    try {
      // Create timeout promise
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => reject(new Error(`Upload timeout after ${timeout}ms`)), timeout);
      });

      // Wait for all uploads or timeout
      await Promise.race([
        Promise.allSettled(Array.from(this.pendingUploads.values())),
        timeoutPromise
      ]);

      logger.info(`${context}: All pending uploads completed`);
    } catch (error: any) {
      logger.warn(`${context}: Error waiting for uploads: ${error.message}`);
      // Don't throw error, just log it - cleanup should continue
    }
  }

  /**
   * Get recording duration in milliseconds
   * @returns Recording duration or 0 if not recording
   */
  getRecordingDuration(): number {
    if (!this.isRecordingActive || this.recordingStartTime === 0) {
      return 0;
    }
    return Date.now() - this.recordingStartTime;
  }

  /**
   * Get custom tags for artifacts with platform-specific information
   * @returns Custom tags object
   */
  protected getCustomTags(): Record<string, string> {
    const duration = this.getRecordingDuration();
    const baseTags = {
      platform: this.platform,
      provider: this.constructor.name,
      duration: duration.toString(),
      durationFormatted: this.formatDuration(duration)
    };

    if (this.platform === 'web') {
      return {
        ...baseTags,
        browser: 'chrome',
        resolution: `${this.config.width || 1920}x${this.config.height || 1080}`,
        fps: (this.config.fps || 25).toString(),
        aspectRatio: this.config.aspectRatio || '16:9',
        recorder: 'puppeteer-screen-recorder'
      };
    } else {
      const deviceTags: Record<string, string> = {
        ...baseTags,
        deviceType: 'android',
        bitRate: (this.config.bitRate || 4000000).toString(),
        resolution: this.config.size || '1280x720',
        recorder: 'misoai-android'
      };

      if (this.currentAndroidDevice) {
        deviceTags.deviceId = this.currentAndroidDevice.deviceId || 'unknown';
        deviceTags.osVersion = this.currentAndroidDevice.osVersion || 'unknown';
      }

      return deviceTags;
    }
  }

  /**
   * Format duration in human-readable format
   * @param durationMs Duration in milliseconds
   * @returns Formatted duration string
   */
  protected formatDuration(durationMs: number): string {
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  }

  /**
   * Initialize the video recorder
   * @param config Configuration object
   */
  async initialize(config: any): Promise<void> {
    this.config = { ...this.config, ...config };

    // Ensure video directory exists
    await ensureDirectoryExists(this.videoDir);

    logger.info(`BaseVideoRecorder: Initialized for ${this.platform} platform`);
  }

  /**
   * Notify TestExecutionManager about video upload completion
   * This is used during cleanup when test runner is no longer active
   */
  private async notifyVideoUploadCompleted(testId: string, videoUrl: string): Promise<void> {
    try {
      if (this.executionCoordinator && typeof this.executionCoordinator.emit === 'function') {
        this.executionCoordinator.emit('video:uploaded', testId, videoUrl);
        logger.debug(`UnifiedVideoRecorder: Notified ExecutionCoordinator of video upload for test ${testId}`);
      } else {
        logger.warn(`UnifiedVideoRecorder: Could not get ExecutionCoordinator to notify for video upload`);
      }
    } catch (error: any) {
      logger.error(`UnifiedVideoRecorder: Error notifying ExecutionCoordinator of video upload: ${error.message}`);
    }
  }

  /**
   * Cleanup resources with enhanced process management and idempotent behavior
   */
  async cleanup(): Promise<void> {
    logger.info(`UnifiedVideoRecorder: Starting comprehensive cleanup for ${this.platform} platform`);

    // Wait for pending uploads before cleanup to prevent race conditions
    if (this.hasPendingUploads()) {
      logger.info(`UnifiedVideoRecorder: Waiting for ${this.uploadInProgress.size} pending uploads before cleanup`);
      await this.waitForPendingUploads(15000); // 15 second timeout
    }

    // Make cleanup idempotent - safe to call multiple times
    if (this.isRecordingActive && this.currentTestId) {
      logger.warn('UnifiedVideoRecorder: Stopping active recording during cleanup');

      try {
        // Use stopRecordingAsync to ensure video upload is triggered
        await this.stopRecordingAsync((testId: string, videoUrl: string, error?: Error) => {
          if (error) {
            logger.error(`UnifiedVideoRecorder: Error uploading video during cleanup for test ${testId}: ${error.message}`);
          } else {
            logger.info(`UnifiedVideoRecorder: Video uploaded successfully during cleanup for test ${testId}: ${videoUrl}`);

            // Send video upload notification directly to TestExecutionManager during cleanup
            this.notifyVideoUploadCompleted(testId, videoUrl).catch((notifyError: any) => {
              logger.error(`UnifiedVideoRecorder: Error notifying video upload completion during cleanup for test ${testId}: ${notifyError.message}`);
            });
          }
        });
        logger.info('UnifiedVideoRecorder: Successfully stopped recording and triggered upload during cleanup');
      } catch (error: any) {
        logger.error(`UnifiedVideoRecorder: Error during stopRecordingAsync in cleanup: ${error.message}`);

        // Fallback to platform-specific cleanup if stopRecordingAsync fails
        try {
          if (this.platform === 'web') {
            await this.cleanupWebRecording();
          } else {
            await this.cleanupAndroidRecording();
          }
        } catch (fallbackError: any) {
          logger.error(`UnifiedVideoRecorder: Error during fallback platform-specific cleanup: ${fallbackError.message}`);
        }
      }
    }

    // Force reset all state regardless of errors above
    try {
      this.isRecordingActive = false;
      this.currentTestId = null;
      this.recordingStartTime = 0;
      this.webRecorder = null;
      this.currentAndroidDevice = null;

      // Don't clear upload tracking immediately if we have pending uploads
      // Let them complete naturally to avoid interrupting video uploads
      if (!this.hasPendingUploads()) {
        this.pendingUploads.clear();
        this.uploadInProgress.clear();
        logger.debug(`UnifiedVideoRecorder: Cleared upload tracking as no uploads are pending`);
      } else {
        logger.info(`UnifiedVideoRecorder: Keeping upload tracking active for ${this.uploadInProgress.size} pending uploads`);
      }

      logger.info(`UnifiedVideoRecorder: Cleanup completed successfully for ${this.platform} platform`);
    } catch (error: any) {
      logger.error(`UnifiedVideoRecorder: Error during state reset: ${error.message}`);
    }
  }

  /**
   * Enhanced web recording cleanup with process termination
   */
  private async cleanupWebRecording(): Promise<void> {
    if (this.webRecorder) {
      try {
        // Try graceful stop first
        await this.stopWebRecording();
      } catch (error: any) {
        logger.warn(`UnifiedVideoRecorder: Graceful stop failed during cleanup: ${error.message}`);

        // Force terminate any remaining FFmpeg processes
        await this.forceTerminateFFmpegProcess();
      }
    }
  }

  /**
   * Enhanced Android recording cleanup
   */
  private async cleanupAndroidRecording(): Promise<void> {
    if (this.currentAndroidDevice) {
      try {
        // Try to stop recording gracefully
        await this.stopAndroidRecording();
      } catch (error: any) {
        logger.warn(`UnifiedVideoRecorder: Android recording stop failed during cleanup: ${error.message}`);

        // Force cleanup Android device reference
        this.currentAndroidDevice = null;
      }
    }
  }

  /**
   * Get recorder statistics with platform-specific information
   * @returns Recorder statistics
   */
  getStats(): Record<string, any> {
    const baseStats = {
      platform: this.platform,
      videoDir: this.videoDir,
      isRecording: this.isRecordingActive,
      currentTestId: this.currentTestId,
      recordingDuration: this.getRecordingDuration(),
      recorderType: this.constructor.name
    };

    if (this.platform === 'web') {
      return {
        ...baseStats,
        hasRecorder: !!this.webRecorder,
        recorderConfig: {
          fps: this.config.fps || 8,              // Updated to reflect new default
          width: this.config.width || 960,        // Updated to reflect new default
          height: this.config.height || 540,      // Updated to reflect new default
          aspectRatio: this.config.aspectRatio || '16:9',
          videoCrf: 32,                           // Added CRF info
          videoBitrate: this.config.bitrate || 600 // Updated to reflect new default
        }
      };
    } else {
      return {
        ...baseStats,
        hasCurrentDevice: !!this.currentAndroidDevice,
        deviceInfo: this.currentAndroidDevice ? {
          deviceId: this.currentAndroidDevice.deviceId,
          osVersion: this.currentAndroidDevice.osVersion
        } : null,
        recordingConfig: {
          timeLimit: this.config.timeLimit || 1800,
          bitRate: this.config.bitRate || 1000000,  // Updated to reflect new default (1 Mbps)
          size: this.config.size || '960x540'       // Updated to reflect new default
        }
      };
    }
  }
}

// Backward compatibility export
export { UnifiedVideoRecorder as BaseVideoRecorder };
