/**
 * Status API
 *
 * System status and pause/resume functionality
 */

import { get, post, adminGet, adminPost } from './fetch-wrapper';
import { STATUS_ENDPOINTS } from './constants';

/**
 * Get system status including pause status
 */
export async function getSystemStatus(signal?: AbortSignal) {
  return await adminGet(STATUS_ENDPOINTS.SYSTEM, { signal });
}

/**
 * Get all nodes status
 */
export async function getNodesStatus(signal?: AbortSignal) {
  return await adminGet(STATUS_ENDPOINTS.NODES, { signal });
}

/**
 * Get queue status
 */
export async function getQueueStatus(signal?: AbortSignal) {
  return await adminGet(STATUS_ENDPOINTS.QUEUES, { signal });
}

/**
 * Pause the entire system
 */
export async function pauseSystem() {
  return await adminPost(STATUS_ENDPOINTS.PAUSE_SYSTEM);
}

/**
 * Resume the entire system
 */
export async function resumeSystem() {
  return await adminPost(STATUS_ENDPOINTS.RESUME_SYSTEM);
}

/**
 * Pause a specific node
 * @param nodeId Node ID
 */
export async function pauseNode(nodeId: string) {
  return await adminPost(STATUS_ENDPOINTS.PAUSE_NODE(nodeId));
}

/**
 * Resume a specific node
 * @param nodeId Node ID
 */
export async function resumeNode(nodeId: string) {
  return await adminPost(STATUS_ENDPOINTS.RESUME_NODE(nodeId));
}

/**
 * Get pause status for a specific node
 * @param nodeId Node ID
 */
export async function getNodePauseStatus(nodeId: string, signal?: AbortSignal) {
  return await adminGet(STATUS_ENDPOINTS.NODE_PAUSE_STATUS(nodeId), { signal });
}

/**
 * Get pause status for all nodes
 */
export async function getNodesPauseStatus(signal?: AbortSignal) {
  return await adminGet(STATUS_ENDPOINTS.NODES_PAUSE_STATUS, { signal });
}
