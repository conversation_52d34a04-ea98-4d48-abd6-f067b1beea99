services:
  web_app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_BASE_URL: ${NEXT_PUBLIC_API_BASE_URL:-http://localhost:5000/api}
    container_name: hirafi-web-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:5000/api}
      - COMPOSE_BAKE=true
    restart: unless-stopped
    networks:
      - test-hub-network

networks:
  test-hub-network:
    external: true
    name: test_hub_test-hub-network
