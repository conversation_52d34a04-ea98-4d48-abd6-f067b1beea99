"use client"

import { useState, useMemo, useEffect, useCallback } from "react"
import { useDebounce } from "@/hooks/useDebounce"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  TestTube,
  X,
  CheckCircle2,
  Filter,
  Folder,
  ListChecks,
  LayoutGrid,
  FileText,
  RefreshCw,
  Download,
  Loader2
} from "lucide-react"
import { TestManagementProvider } from "@/store/testManagementStore"
import { useUnifiedTestManagementContext } from "@/contexts/UnifiedTestManagementContext"

interface UnifiedTestCaseSelectorProps {
  onCaseSelect?: (caseId: string) => void
  onImportComplete?: (prompt: string, provider: TestManagementProvider, caseDetails: any) => void
  selectedCaseId?: string | null
  showImportMode?: boolean
  className?: string
}

export function UnifiedTestCaseSelector({
  onCaseSelect,
  onImportComplete,
  selectedCaseId = null,
  showImportMode = false,
  className = ""
}: UnifiedTestCaseSelectorProps) {
  const { store, fetchSuitesOrFolders, fetchTestCases } = useUnifiedTestManagementContext();

  // Extract values from store for easier access
  const {
    activeProvider,
    availableProviders,
    projects,
    suitesOrFolders,
    testCases,
    filters: { selectedProject, selectedSuiteOrFolder, searchTerm },
    loading: { isLoading, isLoadingSuites, isLoadingCases, isLoadingMore },
    providers,
    initializedProviders,
  } = store;

  // Derived values
  const isConnected = activeProvider ? providers[activeProvider]?.isConnected || false : false;
  const displayedCases = testCases; // For now, use all test cases
  const hasMoreCases = false; // Will be implemented with pagination

  // Debug logging (reduced frequency)
  useEffect(() => {
    console.log('[UnifiedTestCaseSelector] Component state:', {
      activeProvider,
      availableProviders,
      isConnected,
      providersState: providers,
      projectsCount: projects?.length || 0,
      testCasesCount: testCases?.length || 0,
      selectedProject,
      selectedSuiteOrFolder,
      initializedProviders: Array.from(initializedProviders)
    });
  }, [activeProvider, isConnected, projects?.length, testCases?.length]);

  // Actions from store
  const switchProvider = store.switchProvider;
  const setSearchTerm = store.setSearchTerm;
  const resetFilters = store.resetFilters;

  // Provider display name helper
  const getProviderDisplayName = (provider: TestManagementProvider) => {
    return provider === 'testrail' ? 'TestRail' : 'Zephyr Scale';
  };

  // Enhanced functions with proper test case fetching
  const handleProjectChange = async (projectId: string) => {
    store.setSelectedProject(projectId);
    store.setSelectedSuiteOrFolder('all'); // Reset folder selection

    // Fetch suites/folders for the new project
    if (projectId !== 'all') {
      await fetchSuitesOrFolders(projectId);
    }

    // Fetch test cases for the project
    await fetchTestCases(projectId);
  };

  const handleSuiteOrFolderChange = async (id: string) => {
    console.log(`[UnifiedTestCaseSelector] Folder/Suite changed to: ${id}`);
    store.setSelectedSuiteOrFolder(id);

    // Fetch test cases for the selected folder/suite
    const projectId = selectedProject || 'all';
    const suiteOrFolderId = id === 'all' ? undefined : id;

    console.log(`[UnifiedTestCaseSelector] Fetching test cases for project: ${projectId}, folder/suite: ${suiteOrFolderId}`);
    await fetchTestCases(projectId, suiteOrFolderId);
  };
  const loadMoreCases = () => {
    // Will be implemented with pagination
  };
  const ensureProviderInitialized = async (provider: TestManagementProvider) => {
    await store.initializeProvider(provider);
  };
  const selectImportCase = (caseId: string | null) => {
    store.setSelectedImportCase(caseId);
  };

  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')

  // Local search state with debounce
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm || "")
  const debouncedSearchTerm = useDebounce(localSearchTerm, 300)

  // Update store when debounced value changes
  useEffect(() => {
    if (debouncedSearchTerm !== searchTerm) {
      setSearchTerm(debouncedSearchTerm)
    }
  }, [debouncedSearchTerm, searchTerm, setSearchTerm])

  // Sync local state when store changes (e.g., reset filters)
  useEffect(() => {
    if (searchTerm !== localSearchTerm) {
      setLocalSearchTerm(searchTerm || "")
    }
  }, [searchTerm])

  // Initialize active provider when component mounts (same logic as TestManagementTab)
  useEffect(() => {
    if (activeProvider && !initializedProviders.has(activeProvider)) {
      console.log(`[UnifiedTestCaseSelector] Provider ${activeProvider} not initialized, but UnifiedTestManagementContext should handle this`);
      // Don't call ensureProviderInitialized here as UnifiedTestManagementContext handles initialization
    }
  }, [activeProvider, initializedProviders]);

  // Fetch suites/folders when a project is selected
  useEffect(() => {
    if (activeProvider && isConnected && selectedProject && selectedProject !== 'all') {
      console.log(`[UnifiedTestCaseSelector] Fetching suites/folders for provider: ${activeProvider}`);
      fetchSuitesOrFolders(selectedProject);
    }
  }, [activeProvider, isConnected, selectedProject, fetchSuitesOrFolders]);

  // Filter test cases based on search and selection
  const filteredTestCases = useMemo(() => {
    if (!testCases) return []

    let filtered = testCases

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(testCase => {
        const name = testCase.name?.toLowerCase() || ""
        const description = testCase.description?.toLowerCase() || ""
        const id = testCase.id?.toString() || ""

        return name.includes(searchLower) ||
               description.includes(searchLower) ||
               id.includes(searchTerm)
      })
    }

    return filtered
  }, [testCases, searchTerm])

  // Handle case selection
  const handleCaseSelect = useCallback(async (caseId: string) => {
    if (showImportMode) {
      await selectImportCase(caseId)
    }
    onCaseSelect?.(caseId)
  }, [showImportMode, selectImportCase, onCaseSelect])



  // No providers available
  if (availableProviders.length === 0) {
    return (
      <Card className={`border-dashed border-gray-300 dark:border-gray-600 ${className}`}>
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-6 mb-6">
            <TestTube className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
            No Test Management Integration
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
            Configure TestRail or Zephyr Scale to browse and select test cases.
          </p>
          <div className="flex gap-3">
            <Button 
              size="lg"
              onClick={() => window.location.href = '/plugins/testrail'}
            >
              Configure TestRail
            </Button>
            <Button 
              size="lg"
              variant="outline"
              onClick={() => window.location.href = '/plugins/zephyrscale'}
            >
              Configure Zephyr Scale
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Provider not connected
  if (!isConnected) {
    console.log('[UnifiedTestCaseSelector] Provider not connected, showing not connected UI', {
      activeProvider,
      isConnected,
      providerState: providers[activeProvider!]
    });
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-6 mb-6">
            <TestTube className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
            {getProviderDisplayName(activeProvider!)} Not Connected
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
            Configure your {getProviderDisplayName(activeProvider!)} integration to browse test cases.
          </p>
          <Button
            size="lg"
            onClick={() => window.location.href = `/plugins/${activeProvider}`}
          >
            Configure {getProviderDisplayName(activeProvider!)}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            {showImportMode ? (
              <>
                <Download className="h-5 w-5 text-blue-500" />
                Import from {getProviderDisplayName(activeProvider!)}
              </>
            ) : (
              <>
                <TestTube className="h-5 w-5 text-blue-500" />
                {getProviderDisplayName(activeProvider!)} Test Cases
              </>
            )}
          </CardTitle>

          {/* Provider Switcher */}
          {availableProviders.length > 1 && (
            <Select value={activeProvider || ""} onValueChange={switchProvider}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableProviders.map((provider) => (
                  <SelectItem key={provider} value={provider}>
                    {getProviderDisplayName(provider)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Import Mode Description */}
        {showImportMode && (
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <Download className="h-4 w-4 inline mr-1" />
              Select a test case below and click "Import" to generate AI steps from its content.
            </p>
          </div>
        )}

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex flex-col items-center">
              <Loader2 className="h-10 w-10 text-blue-500 animate-spin" />
              <p className="mt-4 text-blue-600 dark:text-blue-400">
                Loading {getProviderDisplayName(activeProvider!)} data...
              </p>
            </div>
          </div>
        ) : (
          <>
            {/* Filters */}
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search test cases..."
                  value={localSearchTerm}
                  onChange={(e) => setLocalSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Project and Suite/Folder Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Project Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Project
                  </label>
                  <Select value={selectedProject} onValueChange={handleProjectChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select project" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        <div className="flex items-center">
                          <LayoutGrid className="h-3.5 w-3.5 mr-2 text-blue-500" />
                          All Projects
                        </div>
                      </SelectItem>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          <div className="flex items-center">
                            <Folder className="h-3.5 w-3.5 mr-2 text-blue-500" />
                            {project.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Suite/Folder Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {activeProvider === 'testrail' ? 'Suite' : 'Folder'}
                  </label>
                  <Select value={selectedSuiteOrFolder} onValueChange={handleSuiteOrFolderChange}>
                    <SelectTrigger>
                      <SelectValue placeholder={`Select ${activeProvider === 'testrail' ? 'suite' : 'folder'}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        <div className="flex items-center">
                          <LayoutGrid className="h-3.5 w-3.5 mr-2 text-blue-500" />
                          All {activeProvider === 'testrail' ? 'Suites' : 'Folders'}
                        </div>
                      </SelectItem>
                      {suitesOrFolders.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          <div className="flex items-center">
                            <ListChecks className="h-3.5 w-3.5 mr-2 text-blue-500" />
                            {item.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Filter Stats and Reset */}
              <div className="flex items-center justify-between bg-white/60 dark:bg-gray-900/40 rounded-md p-2 text-xs">
                <span className="flex items-center text-blue-700 dark:text-blue-300 font-medium">
                  <FileText className="h-3.5 w-3.5 mr-1.5 text-blue-500" />
                  Found: <span className="font-bold mx-1">{filteredTestCases.length}</span> of <span className="font-bold mx-1">{testCases.length}</span> cases
                </span>

                {(searchTerm || selectedProject !== "all" || selectedSuiteOrFolder !== "all") && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetFilters}
                    className="h-7 px-2.5 py-0 text-xs"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>

            {/* Test Cases List */}
            {isLoadingCases ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 text-blue-500 animate-spin mr-2" />
                <span className="text-blue-600 dark:text-blue-400">Loading test cases...</span>
              </div>
            ) : filteredTestCases.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />

                {/* Enhanced empty state messaging for Zephyr Scale folder filtering */}
                {activeProvider === 'zephyrscale' && selectedSuiteOrFolder && selectedSuiteOrFolder !== 'all' ? (
                  <div className="space-y-3">
                    <p className="text-gray-900 dark:text-white font-medium">
                      No test cases found in this folder
                    </p>
                    <p className="text-gray-600 dark:text-gray-400 text-sm max-w-md mx-auto">
                      Test cases in Zephyr Scale may not be assigned to folders. Try selecting "All Folders" to see unassigned test cases, or check if test cases need to be moved to folders in Zephyr Scale.
                    </p>
                    <div className="flex gap-2 justify-center mt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSuiteOrFolderChange('all')}
                      >
                        <LayoutGrid className="h-4 w-4 mr-2" />
                        View All Folders
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open('https://support.smartbear.com/zephyr-scale-cloud/docs/test-cases/organize-test-cases.html', '_blank')}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Learn More
                      </Button>
                    </div>
                  </div>
                ) : searchTerm ? (
                  <div className="space-y-3">
                    <p className="text-gray-600 dark:text-gray-400">
                      No test cases found matching "{searchTerm}".
                    </p>
                    <Button variant="outline" onClick={() => setLocalSearchTerm("")} className="mt-2">
                      Clear Search
                    </Button>
                  </div>
                ) : (
                  <p className="text-gray-600 dark:text-gray-400">
                    No test cases found matching your criteria.
                  </p>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <ScrollArea className="h-[400px]">
                  <div className="space-y-2 pr-4">
                    {(displayedCases.length > 0 ? displayedCases : filteredTestCases).map((testCase) => (
                      <TestCaseItem
                        key={testCase.id}
                        testCase={testCase}
                        isSelected={selectedCaseId === testCase.id}
                        onSelect={() => handleCaseSelect(testCase.id)}
                        provider={activeProvider!}
                      />
                    ))}
                  </div>
                </ScrollArea>

                {/* Load More Button */}
                {hasMoreCases && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={loadMoreCases}
                      disabled={isLoadingMore}
                    >
                      {isLoadingMore ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Load More Cases
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}

// Test Case Item Component
interface TestCaseItemProps {
  testCase: any
  isSelected: boolean
  onSelect: () => void
  provider: TestManagementProvider
}

function TestCaseItem({ testCase, isSelected, onSelect, provider }: TestCaseItemProps) {
  return (
    <div
      className={`p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-sm ${
        isSelected
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-1">
          {isSelected ? (
            <CheckCircle2 className="h-5 w-5 text-blue-500" />
          ) : (
            <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {testCase.name}
            </span>
            <Badge variant="secondary" className="text-xs">
              {provider === 'testrail' ? `C${testCase.id}` : testCase.key || testCase.id}
            </Badge>
          </div>

          {testCase.description && (
            <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
              {testCase.description}
            </p>
          )}

          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2">
              {testCase.priority && (
                <Badge variant="outline" className="text-xs">
                  {testCase.priority}
                </Badge>
              )}
              {testCase.status && (
                <Badge variant="outline" className="text-xs">
                  {testCase.status}
                </Badge>
              )}
            </div>


          </div>
        </div>
      </div>
    </div>
  )
}
