import { useState, useEffect, useMemo } from "react";
import { pluginApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { useTestManagementStore } from "@/store/testManagementStore";

interface UseZephyrScaleIntegrationOptions {
  autoInitialize?: boolean;
}

export interface ZephyrScaleConfig {
  success: boolean;
  config?: {
    projectsData: any[];
    foldersData: any[];
    updatedAt: string;
    // apiToken is no longer included in frontend responses for security
  };
  plugin?: {
    id: string;
    name: string;
    description: string;
    active: boolean;
    createdAt: string;
    updatedAt: string;
  };
  error?: string | null;
}

export interface ZephyrScaleTestCase {
  id: string;
  key: string;
  name: string;
  objective?: string;
  precondition?: string;
  priority?: string;
  status?: string;
  folder?: string;
  folderId?: string;
  projectKey?: string;
  labels?: string[];
  customFields?: any;
  [key: string]: any;
}

export function useZephyrScaleIntegration(
  options: UseZephyrScaleIntegrationOptions = {},
) {
  const { toast } = useToast();
  const { autoInitialize = true } = options;
  const {
    setProviderConfig,
    setProjects,
    setSuitesOrFolders,
    setTestCases,
    providers,
  } = useTestManagementStore();

  // Get state from store
  const zephyrScaleState = providers.zephyrscale;
  const isConnected = zephyrScaleState?.isConnected || false;
  const isInitialized = zephyrScaleState?.isInitialized || false;
  const zephyrScaleConfig = zephyrScaleState?.config || null;
  const projects = useTestManagementStore((state) => state.projects);
  const folders = useTestManagementStore((state) => state.suitesOrFolders);
  const testCases = useTestManagementStore((state) => state.testCases);

  // Debug logging (reduced frequency)
  useEffect(() => {
    console.log('[useZephyrScaleIntegration] State check:', {
      zephyrScaleState,
      isConnected,
      isInitialized,
      projectsCount: projects?.length || 0,
      foldersCount: folders?.length || 0,
      testCasesCount: testCases?.length || 0
    });
  }, [isConnected, isInitialized, projects?.length, folders?.length, testCases?.length]);

  // Selection state
  const [selectedProject, setSelectedProject] = useState<string>("all");
  const [selectedFolder, setSelectedFolder] = useState<string>("all");
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [selectedCaseDetails, setSelectedCaseDetails] = useState<any>(null);

  // Loading states
  const [loadingStates, setLoadingStates] = useState({
    initial: true,
    folders: false,
    cases: false,
  });

  // Search and filtering
  const [searchTerm, setSearchTerm] = useState("");

  // Chunked loading state
  const [allTestCases, setAllTestCases] = useState<ZephyrScaleTestCase[]>([]);
  const [displayedCases, setDisplayedCases] = useState<ZephyrScaleTestCase[]>(
    [],
  );
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const CHUNK_SIZE = 50;

  // Helper function to update loading states
  const updateLoadingState = (updates: Partial<typeof loadingStates>) => {
    setLoadingStates((prev) => ({ ...prev, ...updates }));
  };

  // Computed loading states for backward compatibility
  const isLoading = loadingStates.initial;
  const isLoadingFolders = loadingStates.folders;
  const isLoadingCases = loadingStates.cases;

  const initialize = async (config?: ZephyrScaleConfig) => {
    console.log('[useZephyrScaleIntegration] initialize called with config:', config);
    const configToUse = config || zephyrScaleConfig;
    console.log('[useZephyrScaleIntegration] configToUse:', configToUse);

    if (configToUse) {
      console.log('[useZephyrScaleIntegration] Setting provider config:', {
        isInitialized: true,
        isConnected: configToUse.success,
        config: configToUse.config,
        plugin: configToUse.plugin,
      });

      setProviderConfig("zephyrscale", {
        isInitialized: true,
        isConnected: configToUse.success,
        config: configToUse.config,
        plugin: configToUse.plugin,
      });

      if (configToUse.config?.projectsData) {
        console.log('[useZephyrScaleIntegration] Setting projects:', configToUse.config.projectsData);
        setProjects(configToUse.config.projectsData);
      }
    } else {
      console.log('[useZephyrScaleIntegration] No config provided, fetching from API');
      await fetchZephyrScaleConfig();
    }
  };

  // Fetch Zephyr Scale configuration using unified endpoint
  const fetchZephyrScaleConfig = async () => {
    console.log('[useZephyrScaleIntegration] fetchZephyrScaleConfig called');
    try {
      updateLoadingState({ initial: true });

      // Use unified endpoint to check if Zephyr Scale is available
      console.log('[useZephyrScaleIntegration] Calling getAvailableTestManagementIntegrations');
      const unifiedResponse = await pluginApi.getAvailableTestManagementIntegrations();
      console.log('[useZephyrScaleIntegration] Unified response:', unifiedResponse);

      if (unifiedResponse.success && unifiedResponse.data) {
        const zephyrScaleIntegration = unifiedResponse.data.integrations.find(
          (integration: any) => integration.provider === 'zephyrscale'
        );
        console.log('[useZephyrScaleIntegration] Found Zephyr Scale integration:', zephyrScaleIntegration);

        if (zephyrScaleIntegration && zephyrScaleIntegration.isConnected) {
          const configValue = zephyrScaleIntegration.config;
          console.log('[useZephyrScaleIntegration] Zephyr Scale is connected, config:', configValue);

          setProviderConfig("zephyrscale", {
            isInitialized: true,
            isConnected: true,
            config: configValue,
            plugin: zephyrScaleIntegration.plugin,
          });

          // Set up available projects
          if (
            configValue.projectsData &&
            Array.isArray(configValue.projectsData)
          ) {
            console.log('[useZephyrScaleIntegration] Setting projects from config:', configValue.projectsData);
            setProjects(configValue.projectsData);
          }
        } else {
          // Zephyr Scale not configured - this is normal, don't show error
          console.log('[useZephyrScaleIntegration] Zephyr Scale not connected or not found');
          setProviderConfig("zephyrscale", {
            isInitialized: true,
            isConnected: false,
            error: null, // No error message for missing configuration
          });
        }
      } else {
        // No integrations available - this is normal
        setProviderConfig("zephyrscale", {
          isInitialized: true,
          isConnected: false,
          error: null, // No error message for missing configuration
        });
      }
    } catch (error: any) {
      // Silently handle errors - user might not want Zephyr Scale configured
      console.debug('Zephyr Scale configuration check failed:', error.message);
      setProviderConfig("zephyrscale", {
        isInitialized: true,
        isConnected: false,
        error: error.message,
      });
    } finally {
      updateLoadingState({ initial: false });
    }
  };

  // Fetch folders for a specific project
  const fetchFoldersForProject = async (projectKey: string) => {
    try {
      updateLoadingState({ folders: true });

      if (!zephyrScaleConfig?.config) {
        throw new Error("Zephyr Scale configuration not available");
      }

      const response = await pluginApi.getZephyrScaleFolders({
        projectKey,
      });

      const folders = response.data || [];
      setSuitesOrFolders(folders);

      return folders;
    } catch (error: any) {
      console.error("Failed to fetch folders:", error);
      toast({
        title: "Error",
        description: `Failed to fetch folders: ${error.message}`,
        variant: "destructive",
      });
      return [];
    } finally {
      updateLoadingState({ folders: false });
    }
  };

  // Fetch test cases for a project and optional folder
  const fetchTestCasesForProject = async (
    projectKey: string,
    folderId?: string,
  ) => {
    try {
      updateLoadingState({ cases: true });

      if (!zephyrScaleConfig?.config) {
        throw new Error("Zephyr Scale configuration not available");
      }

      const response = await pluginApi.getZephyrScaleTestCases({
        projectKey,
        folderId,
      });

      // Extract test cases from response
      const cases = response.data || [];
      setAllTestCases(cases);
      setTestCases(cases); // Update store

      // Initialize chunked display
      setDisplayedCases(cases.slice(0, CHUNK_SIZE));

      // Show helpful message if no test cases found in specific folder
      if (folderId && cases.length === 0) {
        const metadata = response.data?.metadata;
        if (metadata?.suggestion) {
          toast({
            title: "No test cases in folder",
            description: metadata.suggestion,
            variant: "default",
          });
        }
      }

      return cases;
    } catch (error: any) {
      console.error("Failed to fetch test cases:", error);
      toast({
        title: "Error",
        description: `Failed to fetch test cases: ${error.message}`,
        variant: "destructive",
      });
      return [];
    } finally {
      updateLoadingState({ cases: false });
    }
  };

  // Load more test cases (chunked loading)
  const loadMoreCases = () => {
    if (isLoadingMore) return;

    setIsLoadingMore(true);

    setTimeout(() => {
      const currentLength = displayedCases.length;
      const nextChunk = allTestCases.slice(
        currentLength,
        currentLength + CHUNK_SIZE,
      );
      setDisplayedCases((prev) => [...prev, ...nextChunk]);
      setIsLoadingMore(false);
    }, 500);
  };

  // Check if there are more cases to load
  const hasMoreCases = displayedCases.length < allTestCases.length;

  // Handle project change
  const handleProjectChange = async (projectKey: string) => {
    setSelectedProject(projectKey);
    setSelectedFolder("all");
    setSelectedCases([]);
    setTestCases([]);
    setAllTestCases([]);
    setDisplayedCases([]);
    setSuitesOrFolders([]);

    if (projectKey && projectKey !== "all") {
      // Fetch folders for the selected project
      await fetchFoldersForProject(projectKey);
      // Fetch test cases for the project
      await fetchTestCasesForProject(projectKey);
    }
  };

  // Handle folder change
  const handleFolderChange = async (folderId: string) => {
    setSelectedFolder(folderId);
    setSelectedCases([]);
    setTestCases([]);
    setAllTestCases([]);
    setDisplayedCases([]);

    if (selectedProject && selectedProject !== "all") {
      const folderIdToUse = folderId === "all" ? undefined : folderId;
      await fetchTestCasesForProject(selectedProject, folderIdToUse);
    }
  };

  // Handle test case selection
  const handleTestCaseSelection = (caseKey: string, isSelected: boolean) => {
    setSelectedCases((prev) => {
      if (isSelected) {
        return [...prev, caseKey];
      } else {
        return prev.filter((key) => key !== caseKey);
      }
    });
  };

  // Get available folders for the selected project
  const getAvailableFolders = () => {
    return folders || [];
  };

  // Reset filters
  const resetFilters = () => {
    setSelectedProject("all");
    setSelectedFolder("all");
    setSelectedCases([]);
    setTestCases([]);
    setAllTestCases([]);
    setDisplayedCases([]);
    setSuitesOrFolders([]);
    setSearchTerm("");
  };

  // Fetch test case details for import
  const fetchTestCaseDetails = async (testCaseKey: string) => {
    try {
      console.log('[useZephyrScaleIntegration] Fetching test case details for:', testCaseKey);

      const response = await pluginApi.getZephyrScaleTestCaseDetails({
        testCaseKey: testCaseKey
      });

      if (!response.success) {
        console.error('[useZephyrScaleIntegration] Failed to fetch test case details:', response.error);
        toast({
          title: "Error",
          description: response.error || "Failed to fetch test case details",
          variant: "destructive",
        });
        return null;
      }

      console.log('[useZephyrScaleIntegration] Successfully fetched test case details:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('[useZephyrScaleIntegration] Error fetching test case details:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to fetch test case details",
        variant: "destructive",
      });
      return null;
    }
  };

  // Update selected cases (for external updates)
  const updateSelectedCases = (newSelectedCases: string[]) => {
    setSelectedCases(newSelectedCases);
  };

  // Filter test cases based on search term
  const filteredTestCases = useMemo(() => {
    if (!searchTerm) return displayedCases;

    const searchLower = searchTerm.toLowerCase();
    return displayedCases.filter(
      (testCase) =>
        testCase.name?.toLowerCase().includes(searchLower) ||
        testCase.key?.toLowerCase().includes(searchLower) ||
        testCase.objective?.toLowerCase().includes(searchLower),
    );
  }, [displayedCases, searchTerm]);

  // Initialize configuration on mount (only if autoInitialize is true)
  useEffect(() => {
    console.log('[useZephyrScaleIntegration] useEffect called with autoInitialize:', autoInitialize);
    if (autoInitialize) {
      console.log('[useZephyrScaleIntegration] Auto-initializing...');
      initialize();
    } else {
      console.log('[useZephyrScaleIntegration] Auto-initialization disabled');
    }
  }, [autoInitialize]);



  return {
    // State
    isConnected,
    isInitialized,
    isLoading,
    isLoadingFolders,
    isLoadingCases,
    testCases,
    projects,
    folders,
    filteredTestCases,

    // Chunked loading state
    allTestCases,
    displayedCases,
    isLoadingMore,
    CHUNK_SIZE,

    // Selection state
    selectedProject,
    selectedFolder,
    selectedCases,
    selectedCaseDetails,
    searchTerm,

    // Actions
    initialize,
    fetchZephyrScaleConfig,
    fetchTestCasesForProject,
    fetchFoldersForProject,
    loadMoreCases,
    handleProjectChange,
    handleFolderChange,
    handleTestCaseSelection,
    getAvailableFolders,
    resetFilters,
    fetchTestCaseDetails,

    // Setters
    setSearchTerm,
    setSelectedCases,
    updateSelectedCases,

    // Computed properties
    hasMoreCases,

    // Unified interface compatibility (not applicable for Zephyr Scale but needed for unified interface)
    isLoadingSuites: false,
    selectedSuite: "all",
    handleSuiteChange: () => {},
    getAvailableSuites: () => [],
  };
}
