"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Loader2 } from "lucide-react"
import type { Role } from "@/types/team"
import { predefinedPermissions } from "@/types/permissions"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"

interface EditRoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role: Role
  onUpdateRole: (role: Role) => void
}

// Available colors for roles
const roleColors = [
  "blue",
  "green",
  "purple",
  "amber",
  "red",
  "indigo",
  "pink",
  "teal",
]

export function EditRoleDialog({ open, onOpenChange, role, onUpdateRole }: EditRoleDialogProps) {
  const [formData, setFormData] = useState({
    ...role,
    selectedPermissionIds: role.permissions.map((p) => p.id || `${p.resource}-${p.action}`),
  })
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [activeTab, setActiveTab] = useState("folders")

  useEffect(() => {
    setFormData({
      ...role,
      selectedPermissionIds: role.permissions.map((p) => p.id || `${p.resource}-${p.action}`),
    })
  }, [role])

  const handleChange = (field: string, value: any) => {
    setFormData({ ...formData, [field]: value })

    // Clear error when field is edited
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" })
    }
  }

  const handlePermissionToggle = (permissionId: string) => {
    const selectedPermissionIds = [...formData.selectedPermissionIds]

    if (selectedPermissionIds.includes(permissionId)) {
      handleChange(
        "selectedPermissionIds",
        selectedPermissionIds.filter((id) => id !== permissionId),
      )
    } else {
      handleChange("selectedPermissionIds", [...selectedPermissionIds, permissionId])
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Rol adı gereklidir"
    }

    if (formData.selectedPermissionIds.length === 0) {
      newErrors.permissions = "En az bir yetki seçilmelidir"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) return

    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Update permissions based on selected IDs
      // Tüm modüllerdeki tüm izinleri düz bir diziye dönüştür
      const allPermissions = Object.values(permissionsByModule).flat()
      // Seçili ID'lere göre izinleri filtrele
      const updatedPermissions = allPermissions.filter((p) => formData.selectedPermissionIds.includes(p.id))

      const updatedRole: Role = {
        ...formData,
        permissions: updatedPermissions,
      }

      onUpdateRole(updatedRole)
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to update role:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Group permissions by module using predefinedPermissions
  const permissionsByModule: Record<string, typeof role.permissions> = {}

  // Önce predefinedPermissions'dan tüm izinleri ekleyelim
  Object.keys(predefinedPermissions).forEach(module => {
    if (!permissionsByModule[module]) {
      permissionsByModule[module] = []
    }

    // predefinedPermissions'daki izinleri ekle
    predefinedPermissions[module].forEach(permission => {
      // Rolün mevcut izinlerinde bu izin var mı kontrol et
      const existingPermission = role.permissions.find(p =>
        p.resource === permission.resource && p.action === permission.action
      )

      // Eğer varsa, mevcut izni kullan (id'si ile birlikte)
      if (existingPermission) {
        permissionsByModule[module].push(existingPermission)
      } else {
        // Yoksa, predefined izni kullan
        permissionsByModule[module].push(permission)
      }
    })
  })

  const isAllModulePermissionsSelected = (module: string) => {
    const modulePermissions = permissionsByModule[module] || []
    return modulePermissions.every((p) => {
      const permissionId = p.id || `${p.resource}-${p.action}`
      return formData.selectedPermissionIds.includes(permissionId)
    })
  }

  const handleSelectAllModulePermissions = (module: string, checked: boolean) => {
    const modulePermissions = permissionsByModule[module] || []
    const modulePermissionIds = modulePermissions.map((p) => p.id || `${p.resource}-${p.action}`)
    let newSelectedPermissionIds = [...formData.selectedPermissionIds]

    if (checked) {
      // Add all module permissions that aren't already selected
      modulePermissionIds.forEach((id) => {
        if (!newSelectedPermissionIds.includes(id)) {
          newSelectedPermissionIds.push(id)
        }
      })
    } else {
      // Remove all module permissions
      newSelectedPermissionIds = newSelectedPermissionIds.filter((id) => !modulePermissionIds.includes(id))
    }

    handleChange("selectedPermissionIds", newSelectedPermissionIds)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Rol Düzenle</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Rol Adı</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              placeholder="Rol adı giriniz"
              disabled={isLoading}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Açıklama</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange("description", e.target.value)}
              placeholder="Rol açıklaması giriniz"
              disabled={isLoading}
            />
          </div>

          <div className="grid gap-2">
            <Label>Renk</Label>
            <div className="flex flex-wrap gap-2">
              {roleColors.map((color, index) => (
                <div
                  key={index}
                  className={`w-8 h-8 rounded-full cursor-pointer border-2 ${
                    formData.color === color ? "border-black dark:border-white" : "border-transparent"
                  }`}
                  style={{ backgroundColor: color.includes("bg-") ? "" : color }}
                  onClick={() => handleChange("color", color)}
                >
                  <span className={`block w-full h-full rounded-full bg-${color}-500`}></span>
                </div>
              ))}
            </div>
          </div>

          <div className="grid gap-2">
            <Label>Yetkiler</Label>
            {errors.permissions && <p className="text-sm text-red-500">{errors.permissions}</p>}

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-5">
                {Object.keys(permissionsByModule).map((module) => (
                  <TabsTrigger key={module} value={module}>
                    {module === "folders"
                      ? "Klasörler"
                      : module === "scenarios"
                        ? "Senaryolar"
                        : module === "runs"
                          ? "Koşumlar"
                          : module === "reports"
                            ? "Raporlar"
                            : module === "Schedule"
                              ? "Schedule"
                              : ""}
                  </TabsTrigger>
                ))}
              </TabsList>

              {Object.keys(permissionsByModule).map((module) => (
                <TabsContent key={module} value={module} className="border rounded-md p-4">
                  <div className="flex items-center space-x-2 mb-4">
                    <Checkbox
                      id={`select-all-${module}`}
                      checked={isAllModulePermissionsSelected(module)}
                      onCheckedChange={(checked) => handleSelectAllModulePermissions(module, checked === true)}
                    />
                    <Label htmlFor={`select-all-${module}`} className="font-medium">
                      Tümünü Seç
                    </Label>
                  </div>

                  <ScrollArea className="h-[200px] pr-4">
                    <div className="space-y-4">
                      {permissionsByModule[module].map((permission) => {
                        const permissionId = permission.id || `${permission.resource}-${permission.action}`
                        return (
                        <div key={permissionId} className="flex items-start space-x-2">
                          <Checkbox
                            id={permissionId}
                            checked={formData.selectedPermissionIds.includes(permissionId)}
                            onCheckedChange={() => handlePermissionToggle(permissionId)}
                          />
                          <div className="grid gap-1">
                            <Label htmlFor={permissionId} className="font-medium">
                              {permission.name}
                            </Label>
                            <p className="text-sm text-gray-500">{permission.description}</p>
                          </div>
                        </div>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Güncelleniyor...
              </>
            ) : (
              "Güncelle"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
