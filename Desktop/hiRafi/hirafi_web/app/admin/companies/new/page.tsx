"use client"

import { useState, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { AdminLayout } from "@/components/admin/admin-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { createCompany } from "@/lib/api/admin-api"
import { toast } from "@/components/ui/use-toast"
import { ArrowLeft, Save, Check } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import Link from "next/link"

// Form şeması
const companyFormSchema = z.object({
  name: z.string().min(2, {
    message: "Şirket adı en az 2 karakter olmalıdır.",
  }),
  contactEmail: z.string().email({
    message: "Geçerli bir e-posta adresi girin.",
  }).optional(),
  contactPhone: z.string().optional(),
  status: z.enum(["active", "inactive", "suspended"], {
    required_error: "Lütfen bir durum seçin.",
  }),
  accountType: z.enum(["trial", "basic", "standard", "premium", "enterprise"], {
    required_error: "Lütfen bir hesap türü seçin.",
  }),
  // Plugin ayarları
  enableJira: z.boolean().default(false),
  enableXray: z.boolean().default(false),
  enableTestRail: z.boolean().default(false),
  // Sistem limitleri
  runLimit: z.coerce.number().int().min(0).optional(),
  runMinuteLimit: z.coerce.number().int().min(0).optional(),
  concurrentRunLimit: z.coerce.number().int().min(1).optional(),
  generationLimit: z.coerce.number().int().min(0).optional(),
  maxUsers: z.coerce.number().int().min(1).optional()
  // maxTeams kaldırıldı, her şirketin otomatik olarak bir takımı olacak
})

type CompanyFormValues = z.infer<typeof companyFormSchema>

export default function NewCompanyPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Varsayılan form değerleri - useRef ile sabit tutuyoruz
  const defaultValues = useRef<Partial<CompanyFormValues>>({
    name: "",
    contactEmail: "",
    contactPhone: "",
    status: "active",
    accountType: "trial",
    enableJira: false,
    enableXray: false,
    enableTestRail: false,
    runLimit: 100,
    runMinuteLimit: 500,
    concurrentRunLimit: 2,
    generationLimit: 200,
    maxUsers: 5
    // maxTeams kaldırıldı, her şirketin otomatik olarak bir takımı olacak
  }).current

  // Form tanımı
  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues,
    mode: "onChange",
  })

  // Form yüklenirken tüm alanların tanımlı olduğundan emin ol
  useEffect(() => {
    // Formu varsayılan değerlerle sıfırla
    form.reset(defaultValues)
  }, [form, defaultValues])

  // Form gönderme
  const onSubmit = async (data: CompanyFormValues) => {
    setIsSubmitting(true)
    try {
      // Ayarları hazırla
      const {
        accountType, runLimit, runMinuteLimit, concurrentRunLimit,
        scenarioLimit, generationLimit, maxUsers,
        enableJira, enableXray, enableTestRail,
        ...companyData
      } = data

      const settings = {
        accountType,
        runLimit,
        runMinuteLimit,
        concurrentRunLimit,
        generationLimit,
        maxUsers,
        maxTeams: 1, // Her şirketin sadece bir takımı olacak
        // Kalan kullanım - başlangıçta limitlerle aynı
        remaining: {
          runs: runLimit,
          runMinutes: runMinuteLimit,
          generations: generationLimit
        },
        plugins: {
          jira: { enabled: enableJira },
          xray: { enabled: enableXray },
          testrail: { enabled: enableTestRail }
        }
      }

      // API'ye gönder
      const response = await createCompany({
        ...companyData,
        settings
      })

      if (response.success) {
        toast({
          title: "Şirket oluşturuldu",
          description: "Şirket başarıyla oluşturuldu.",
        })
        router.push(`/admin/companies/${response.companyId}`)
      } else {
        toast({
          title: "Hata",
          description: response.data?.error || response.error || "Şirket oluşturulurken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Şirket oluşturulurken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Yeni Şirket Ekle</h1>
            <p className="text-muted-foreground">
              Sisteme yeni bir şirket ekleyin
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/admin/companies">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Şirketlere Dön
            </Link>
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Şirket Bilgileri</CardTitle>
                <CardDescription>
                  Şirketin temel bilgilerini girin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Şirket Adı *</FormLabel>
                        <FormControl>
                          <Input placeholder="Şirket adını girin" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Durum *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || "active"}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Durum seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="active">Aktif</SelectItem>
                            <SelectItem value="inactive">Pasif</SelectItem>
                            <SelectItem value="suspended">Askıya Alınmış</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="contactEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Şirket Yetkilisi E-posta *</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormDescription>
                          Şirket yetkilisinin e-posta adresi
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contactPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Şirket Yetkilisi Telefon</FormLabel>
                        <FormControl>
                          <Input placeholder="+90 ************" {...field} />
                        </FormControl>
                        <FormDescription>
                          Şirket yetkilisinin telefon numarası (opsiyonel)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Plugin Ayarları</CardTitle>
                <CardDescription>
                  Şirket için aktif olacak pluginleri seçin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="enableJira"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Jira Entegrasyonu
                          </FormLabel>
                          <FormDescription>
                            Jira ile entegrasyon sağlar
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableXray"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Xray Entegrasyonu
                          </FormLabel>
                          <FormDescription>
                            Jira Xray test yönetimi entegrasyonu
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="enableTestRail"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value || false}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            TestRail Entegrasyonu
                          </FormLabel>
                          <FormDescription>
                            TestRail ile entegrasyon sağlar
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Hesap Türü ve Limitler</CardTitle>
                <CardDescription>
                  Şirket hesap türünü ve limitlerini belirleyin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="accountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hesap Türü *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || "trial"}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Hesap türü seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="trial">Deneme</SelectItem>
                          <SelectItem value="basic">Temel</SelectItem>
                          <SelectItem value="standard">Standart</SelectItem>
                          <SelectItem value="premium">Premium</SelectItem>
                          <SelectItem value="enterprise">Kurumsal</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Hesap türü, şirketin kullanabileceği özellikleri ve limitleri belirler
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="runLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Koşum Sayısı Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aylık toplam koşum sayısı limiti (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="runMinuteLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Koşum Dakikası Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aylık toplam koşum dakikası limiti (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="concurrentRunLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Eşzamanlı Koşum Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aynı anda çalışabilecek koşum sayısı
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="generationLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Generation Limiti</FormLabel>
                        <FormControl>
                          <Input type="number" min="0" {...field} />
                        </FormControl>
                        <FormDescription>
                          Aylık toplam senaryo generation limiti (0 = sınırsız)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="maxUsers"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Maksimum Kullanıcı Sayısı</FormLabel>
                        <FormControl>
                          <Input type="number" min="1" {...field} />
                        </FormControl>
                        <FormDescription>
                          Şirketin sahip olabileceği maksimum kullanıcı sayısı
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Maksimum Takım Sayısı alanı kaldırıldı, her şirketin otomatik olarak bir takımı olacak */}
                </div>
              </CardContent>
            </Card>

            <CardFooter className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/admin/companies')}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></div>
                    Kaydediliyor...
                  </div>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Şirketi Kaydet
                  </>
                )}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </div>
    </AdminLayout>
  )
}
