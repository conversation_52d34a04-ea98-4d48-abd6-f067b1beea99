/**
 * Redis Queue Connector Adapter
 * Adapts the Redis queue service to the QueueConnector interface
 */

import { QueueConnector, ConnectionError, MessageError } from '../interfaces.js';
import { queueService, QUEUE_NAMES, JOB_TYPES } from '../../services/redis/queueService.js';
import { logger } from '../../utils/logger.js';
import { testQueueService } from '../../services/test/TestQueueService.js';
import { resultQueueService } from '../../services/result/ResultQueueService.js';

/**
 * Redis Queue Connector Adapter - Implements the QueueConnector interface
 */
class RedisQueueConnectorAdapter implements QueueConnector {
  /**
   * Initialize the connector
   */
  async initialize(config?: any): Promise<boolean> {
    return queueService.initialize();
  }

  /**
   * Close the connection
   */
  async close(): Promise<void> {
    return queueService.close();
  }

  /**
   * Check if connected to Redis
   */
  isConnected(): boolean {
    return queueService.isConnected();
  }

  /**
   * Create a queue
   */
  async createQueue(queueName: string, options?: any): Promise<void> {
    try {
      await queueService.createQueue(queueName, options);
    } catch (error: any) {
      throw new ConnectionError(`Error creating queue ${queueName}: ${error.message}`);
    }
  }

  /**
   * Purge a queue
   */
  async purgeQueue(queueName: string): Promise<void> {
    try {
      const queue = queueService.getQueue(queueName);
      if (queue) {
        await queue.obliterate({ force: true });
        logger.info(`Redis: Purged queue ${queueName}`);
      } else {
        logger.warn(`Redis: Cannot purge queue ${queueName}: Queue not found`);
      }
    } catch (error: any) {
      throw new ConnectionError(`Error purging queue ${queueName}: ${error.message}`);
    }
  }

  /**
   * Consume messages from a queue
   */
  async consumeQueue(queueName: string, callback: (message: any) => void): Promise<void> {
    try {
      await queueService.createWorker(queueName, async (job) => {
        try {
          await callback(job.data);
          return { success: true };
        } catch (error) {
          logger.error(`Redis: Error processing job ${job.id} in queue ${queueName}: ${error}`);
          throw error;
        }
      });
      logger.info(`Redis: Started consuming from queue ${queueName}`);
    } catch (error: any) {
      throw new ConnectionError(`Error consuming queue ${queueName}: ${error.message}`);
    }
  }

  /**
   * Set prefetch count for load balancing
   */
  async setPrefetchCount(count: number): Promise<void> {
    // Not directly applicable to Redis/Bull, but we can log it
    logger.info(`Redis: Prefetch count set to ${count} (note: this is a no-op for Redis/Bull)`);
  }

  /**
   * Send a message to a specific topic
   * @param topic Topic to send to
   * @param data Data to send
   * @returns Promise resolving to success status
   */
  async sendMessage(topic: string, data: any): Promise<boolean> {
    try {
      // Use the topic as the queue name
      const queueName = topic;

      // Add message to the queue
      const job = await queueService.addJob(
        queueName,
        'message',
        {
          ...data,
          timestamp: Date.now()
        },
        {
          attempts: 1,
          removeOnComplete: 100
        }
      );

      if (job) {
        logger.debug(`Redis: Sent message to topic ${topic}`);
        return true;
      } else {
        logger.error(`Redis: Failed to send message to topic ${topic}`);
        return false;
      }
    } catch (error: any) {
      logger.error(`Redis: Error sending message to topic ${topic}: ${error.message}`);
      return false;
    }
  }

  /**
   * Send a test to a node - Implementation for QueueConnector interface
   *
   * In the Pull Model, tests are added to the central queue for nodes to pull.
   *
   * @param _nodeId Node ID (unused in Pull Model)
   * @param test Test to send
   * @returns Promise resolving to success status
   */
  async sendTestToNode(_nodeId: string, test: any): Promise<boolean> {
    try {
      // Initialize the test queue service if needed
      await testQueueService.initialize();

      // Add the test to the queue with default priority
      const jobId = await testQueueService.addTest(test);

      if (jobId) {
        logger.info(`Redis: Added test ${test.id} to central queue for Pull Model (job ID: ${jobId})`);
        return true;
      } else {
        logger.error(`Redis: Failed to add test ${test.id} to central queue`);
        return false;
      }
    } catch (error: any) {
      logger.error(`Redis: Error adding test to central queue: ${error.message}`);
      return false;
    }
  }

  /**
   * Send a stop test command to a node
   * @param nodeId Node ID
   * @param testId Test ID to stop
   * @returns Promise resolving to success status
   */
  async sendStopTestToNode(nodeId: string, testId: string): Promise<boolean> {
    try {
      // In the Pull Model, we use a special stop command in the main test queue
      const queueName = QUEUE_NAMES.TEST_QUEUE;

      // Add stop command to the queue with high priority
      const job = await queueService.addJob(
        queueName,
        'test-stop', // Use a consistent job type
        {
          type: 'stop',
          testId,
          nodeId,
          timestamp: Date.now()
        },
        {
          attempts: 1,  // No retries for stop commands
          removeOnComplete: 100,
          priority: 20  // Higher priority than normal tests
        }
      );

      if (job) {
        logger.info(`Redis: Sent stop command for test ${testId} to node ${nodeId} (job ID: ${job.id})`);
        return true;
      } else {
        logger.error(`Redis: Failed to send stop command for test ${testId} to node ${nodeId}`);
        return false;
      }
    } catch (error: any) {
      logger.error(`Redis: Error sending stop command to node ${nodeId}: ${error.message}`);
      return false;
    }
  }
}

// Export singleton instance
export const redisAdapter = new RedisQueueConnectorAdapter();
export default redisAdapter;
