/**
 * Unified Scenario Service
 * 
 * Single service for both scenario creation and editing operations
 */

import { scenarioApi } from '@/lib/api'
import { ScenarioCreationService } from './scenarioCreationService'
import { ExtendedScenarioFormData, mapFormDataToApiScenario, mapApiScenarioToFormData } from '@/lib/utils/scenario-form-utils'
import { Scenario } from '@/models/scenario'
import { TestManagementProvider } from '@/store/testManagementStore'

export interface ScenarioOperationResult {
  success: boolean
  data?: Scenario
  error?: string
}

export interface GenerateStepsOptions {
  prompt: string
  name?: string
  description?: string
  isTestManagement?: boolean
  testManagementProvider?: TestManagementProvider
  testDataVariables?: Array<{
    name: string
    type: string
    description?: string
  }>
  creativityLevel?: number
}

/**
 * Unified Scenario Service Class
 */
export class UnifiedScenarioService {
  /**
   * Create a new scenario
   */
  static async createScenario(
    formData: ExtendedScenarioFormData,
    userInfo?: { userId: string; teamId: string; companyId: string }
  ): Promise<ScenarioOperationResult> {
    try {
      // Validate required form data
      if (!formData.name?.trim()) {
        return {
          success: false,
          error: 'Scenario name is required'
        }
      }

      // Validate steps array
      if (!Array.isArray(formData.steps)) {
        return {
          success: false,
          error: 'Invalid steps data'
        }
      }

      // If userInfo is not provided, try to get it from localStorage
      let userId = userInfo?.userId
      let teamId = userInfo?.teamId
      let companyId = userInfo?.companyId

      if (!userId || !teamId || !companyId) {
        if (typeof window !== 'undefined') {
          const userString = localStorage.getItem('user')
          if (userString) {
            const user = JSON.parse(userString)
            userId = userId || user.id
            teamId = teamId || user.teamId
            companyId = companyId || user.companyId
          }
        }
      }

      if (!userId || !teamId || !companyId) {
        return {
          success: false,
          error: 'User authentication information is required'
        }
      }

      const result = await ScenarioCreationService.createScenario({
        formData,
        userId,
        teamId,
        companyId
      })
      
      if (result.success && result.data) {
        return {
          success: true,
          data: result.data
        }
      } else {
        return {
          success: false,
          error: result.error || 'Failed to create scenario'
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to create scenario'
      }
    }
  }

  /**
   * Update an existing scenario
   */
  static async updateScenario(formData: ExtendedScenarioFormData): Promise<ScenarioOperationResult> {
    try {
      // Validate required fields
      if (!formData.id) {
        return {
          success: false,
          error: 'Scenario ID is required for updates'
        }
      }

      if (!formData.name?.trim()) {
        return {
          success: false,
          error: 'Scenario name is required'
        }
      }

      // Validate steps array
      if (!Array.isArray(formData.steps)) {
        return {
          success: false,
          error: 'Invalid steps data'
        }
      }

      const apiScenario = mapFormDataToApiScenario(formData)

      // Add additional validation for API scenario
      if (!apiScenario.name) {
        return {
          success: false,
          error: 'Failed to prepare scenario data for saving'
        }
      }

      const result = await scenarioApi.updateScenario(formData.id, apiScenario)

      if (result.success) {
        // Update endpoint returns { success: true, data: { message: "..." } }
        // We don't get the updated scenario back, so we return the data we sent
        return {
          success: true,
          data: { ...apiScenario, id: formData.id } as any
        }
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update scenario'
        }
      }
    } catch (error: any) {
      console.error('Error updating scenario:', error)
      return {
        success: false,
        error: error.message || 'Failed to update scenario'
      }
    }
  }

  /**
   * Generate steps using AI
   */
  static async generateSteps(options: GenerateStepsOptions): Promise<{
    success: boolean
    steps?: any[]
    name?: string
    description?: string
    error?: string
  }> {
    try {
      const result = await ScenarioCreationService.generateSteps({
        prompt: options.prompt,
        name: options.name,
        description: options.description,
        isTestManagement: options.isTestManagement,
        testManagementProvider: options.testManagementProvider,
        testDataVariables: options.testDataVariables,
        creativityLevel: options.creativityLevel
      })

      if (result.success && result.data?.steps) {
        return {
          success: true,
          steps: result.data.steps,
          name: result.data.name,
          description: result.data.description
        }
      } else {
        return {
          success: false,
          error: result.error || 'Failed to generate steps'
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to generate steps'
      }
    }
  }

  /**
   * Load scenario for editing
   */
  static async loadScenario(scenarioId: string): Promise<ScenarioOperationResult> {
    try {
      const result = await scenarioApi.getScenarioById(scenarioId)

      if (result.success) {
        // The API returns { success: true, scenario: {...} } or { success: true, data: {...} }
        // The fetch-wrapper puts this in result.data
        // We need to extract the actual scenario data from multiple possible locations
        let scenarioData = null;

        // Try different possible locations for scenario data
        if ((result as any).scenario) {
          // Direct scenario field (backward compatibility)
          scenarioData = (result as any).scenario;
        } else if (result.data?.scenario) {
          // Scenario nested under data.scenario
          scenarioData = result.data.scenario;
        } else if (result.data && typeof result.data === 'object' && result.data.id) {
          // Data field contains the scenario directly (most common case)
          scenarioData = result.data;
        }

        if (scenarioData) {
          console.log('Loaded scenario data:', scenarioData);
          return {
            success: true,
            data: scenarioData
          }
        } else {
          console.error('No scenario data found in response:', result);
          return {
            success: false,
            error: 'No scenario data found in response'
          }
        }
      } else {
        return {
          success: false,
          error: result.error || 'Failed to load scenario'
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to load scenario'
      }
    }
  }

  /**
   * Delete a scenario
   */
  static async deleteScenario(scenarioId: string): Promise<ScenarioOperationResult> {
    try {
      const result = await scenarioApi.deleteScenario(scenarioId)

      if (result.success) {
        return {
          success: true
        }
      } else {
        return {
          success: false,
          error: result.error || 'Failed to delete scenario'
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to delete scenario'
      }
    }
  }

  /**
   * Validate scenario data
   */
  static validateScenario(formData: ExtendedScenarioFormData): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // Basic validation
    if (!formData.name || formData.name.trim().length === 0) {
      errors.push('Scenario name is required')
    }

    if (!formData.steps || formData.steps.length === 0) {
      errors.push('At least one test step is required')
    }

    if (!formData.platform) {
      errors.push('Platform selection is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Check if scenario has unsaved changes
   */
  static hasUnsavedChanges(
    currentData: ExtendedScenarioFormData,
    originalData: ExtendedScenarioFormData
  ): boolean {
    // Compare key fields that indicate changes
    const fieldsToCompare = [
      'name',
      'description',
      'tags',
      'steps',
      'platform',
      'folderId',
      'testrailCases',
      'testrailSync',
      'zephyrscaleCases',
      'zephyrscaleSync'
    ]

    return fieldsToCompare.some(field => {
      const current = currentData[field as keyof ExtendedScenarioFormData]
      const original = originalData[field as keyof ExtendedScenarioFormData]

      // Handle arrays specially
      if (Array.isArray(current) && Array.isArray(original)) {
        return JSON.stringify(current) !== JSON.stringify(original)
      }

      return current !== original
    })
  }

  /**
   * Verify that scenario data was properly saved by re-fetching it
   */
  static async verifyScenarioSaved(scenarioId: string, expectedData: ExtendedScenarioFormData): Promise<{
    success: boolean
    verified: boolean
    error?: string
  }> {
    try {
      const result = await this.loadScenario(scenarioId)

      if (!result.success || !result.data) {
        return {
          success: false,
          verified: false,
          error: 'Failed to re-fetch scenario for verification'
        }
      }

      const fetchedData = mapApiScenarioToFormData(result.data)

      // Compare key fields to verify data was saved correctly
      const fieldsToVerify = ['name', 'description', 'steps', 'tags', 'platform', 'folderId']
      const mismatches: string[] = []

      for (const field of fieldsToVerify) {
        const expected = expectedData[field as keyof ExtendedScenarioFormData]
        const actual = fetchedData[field as keyof ExtendedScenarioFormData]

        if (Array.isArray(expected) && Array.isArray(actual)) {
          if (JSON.stringify(expected) !== JSON.stringify(actual)) {
            mismatches.push(field)
          }
        } else if (expected !== actual) {
          mismatches.push(field)
        }
      }

      return {
        success: true,
        verified: mismatches.length === 0,
        error: mismatches.length > 0 ? `Data mismatch in fields: ${mismatches.join(', ')}` : undefined
      }
    } catch (error: any) {
      return {
        success: false,
        verified: false,
        error: error.message || 'Failed to verify scenario data'
      }
    }
  }

  /**
   * Get scenario summary for display
   */
  static getScenarioSummary(formData: ExtendedScenarioFormData): {
    stepCount: number
    tagCount: number
    hasTestManagement: boolean
    activeProvider: TestManagementProvider | null
  } {
    const hasTestRail = formData.testrailCases?.length > 0
    const hasZephyrScale = formData.zephyrscaleCases?.length > 0

    return {
      stepCount: formData.steps?.length || 0,
      tagCount: formData.tags?.length || 0,
      hasTestManagement: hasTestRail || hasZephyrScale,
      activeProvider: hasTestRail ? 'testrail' : hasZephyrScale ? 'zephyrscale' : null
    }
  }
}
