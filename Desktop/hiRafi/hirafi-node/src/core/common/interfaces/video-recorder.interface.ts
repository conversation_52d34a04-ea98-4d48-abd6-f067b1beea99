/**
 * Video Recorder Interface
 * Defines the contract for platform-specific video recorders
 */

/**
 * Video Recorder Interface
 * Handles recording videos during test execution
 * @template T The type of target to record (Puppeteer Page, WebdriverIO Browser, etc.)
 */
export interface IVideoRecorder<T = any> {
  /**
   * Start recording a video of the target (page, device, etc.)
   * @param target The target to record (Puppeteer Page, WebdriverIO Browser, etc.)
   * @param testId Test identifier
   * @returns Promise resolving when recording has started
   */
  startRecording(target: T, testId: string): Promise<void>;

  /**
   * Stop recording and save the video
   * @returns Promise resolving to the URL of the stored video
   */
  stopRecording(): Promise<string>;

  /**
   * Stop recording and save the video asynchronously
   * @param onVideoUploaded Callback function called when video upload completes
   * @returns Promise resolving immediately after recording stops (before upload)
   */
  stopRecordingAsync?(onVideoUploaded?: (testId: string, videoUrl: string, error?: Error) => void): Promise<void>;

  /**
   * Initialize the video recorder with configuration
   * @param config Configuration object
   */
  initialize(config: any): Promise<void>;

  /**
   * Get the path where a video would be stored
   * @param testId Test identifier
   * @returns Path to the video
   */
  getVideoPath(testId: string): string;

  /**
   * Check if recording is currently active
   * @returns True if recording is active, false otherwise
   */
  isRecording(): boolean;
}
