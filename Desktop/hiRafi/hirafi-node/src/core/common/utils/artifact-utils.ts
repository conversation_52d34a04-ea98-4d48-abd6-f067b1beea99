/**
 * Common Artifact Utilities
 * Shared artifact creation and management utilities for all platforms
 */

import { IArtifact, IArtifactMetadata } from '../storage/artifact-storage.interface.js';
import { logger } from '../../../utils/logger.js';

/**
 * Create a standardized artifact metadata object
 * @param testId Test identifier
 * @param platform Platform identifier
 * @param artifactType Type of artifact
 * @param contentType Content type
 * @param stepName Optional step name
 * @param customTags Optional custom tags
 * @returns Artifact metadata
 */
export function createArtifactMetadata(
  testId: string,
  platform: 'web' | 'mobile' | 'api',
  artifactType: 'screenshot' | 'video' | 'log' | 'other',
  contentType: string,
  stepName?: string,
  customTags?: Record<string, string>
): IArtifactMetadata {
  return {
    testId,
    stepName,
    timestamp: new Date(),
    platform,
    artifactType,
    contentType,
    customTags: customTags || {}
  };
}

/**
 * Create a screenshot artifact
 * @param testId Test identifier
 * @param stepId Step identifier
 * @param type Screenshot type (before/after)
 * @param platform Platform identifier
 * @param filePath File path
 * @param customTags Optional custom tags
 * @returns Screenshot artifact
 */
export function createScreenshotArtifact(
  testId: string,
  stepId: string,
  type: 'before' | 'after',
  platform: 'web' | 'mobile',
  filePath: string,
  customTags?: Record<string, string>
): IArtifact {
  const metadata = createArtifactMetadata(
    testId,
    platform,
    'screenshot',
    'image/jpeg',
    stepId,
    {
      type,
      stepId,
      ...customTags
    }
  );

  return {
    metadata,
    fileName: `${stepId}_${type}.jpg`,
    filePath
  };
}

/**
 * Create a video artifact
 * @param testId Test identifier (used as report ID)
 * @param platform Platform identifier
 * @param filePath File path
 * @param customTags Optional custom tags
 * @returns Video artifact
 */
export function createVideoArtifact(
  testId: string,
  platform: 'web' | 'mobile',
  filePath: string,
  customTags?: Record<string, string>
): IArtifact {
  const metadata = createArtifactMetadata(
    testId,
    platform,
    'video',
    'video/mp4',
    undefined,
    customTags
  );

  return {
    metadata,
    fileName: `${testId}.mp4`,
    filePath
  };
}

/**
 * Create a log artifact
 * @param testId Test identifier
 * @param platform Platform identifier
 * @param filePath File path
 * @param logType Type of log
 * @param customTags Optional custom tags
 * @returns Log artifact
 */
export function createLogArtifact(
  testId: string,
  platform: 'web' | 'mobile' | 'api',
  filePath: string,
  logType: string,
  customTags?: Record<string, string>
): IArtifact {
  const metadata = createArtifactMetadata(
    testId,
    platform,
    'log',
    'text/plain',
    undefined,
    {
      logType,
      ...customTags
    }
  );

  return {
    metadata,
    fileName: `${logType}.log`,
    filePath
  };
}

/**
 * Create a metrics artifact
 * @param testId Test identifier
 * @param platform Platform identifier
 * @param filePath File path
 * @param customTags Optional custom tags
 * @returns Metrics artifact
 */
export function createMetricsArtifact(
  testId: string,
  platform: 'web' | 'mobile',
  filePath: string,
  customTags?: Record<string, string>
): IArtifact {
  const metadata = createArtifactMetadata(
    testId,
    platform,
    'other',
    'application/json',
    undefined,
    {
      metricsType: 'performance',
      ...customTags
    }
  );

  return {
    metadata,
    fileName: 'metrics.json',
    filePath
  };
}

/**
 * Validate artifact before upload
 * @param artifact Artifact to validate
 * @returns True if valid, false otherwise
 */
export function validateArtifact(artifact: IArtifact): boolean {
  try {
    // Check required metadata fields
    if (!artifact.metadata.testId) {
      logger.error('ArtifactUtils: Missing testId in artifact metadata');
      return false;
    }

    if (!artifact.metadata.platform) {
      logger.error('ArtifactUtils: Missing platform in artifact metadata');
      return false;
    }

    if (!artifact.metadata.artifactType) {
      logger.error('ArtifactUtils: Missing artifactType in artifact metadata');
      return false;
    }

    if (!artifact.metadata.contentType) {
      logger.error('ArtifactUtils: Missing contentType in artifact metadata');
      return false;
    }

    // Check that artifact has content
    if (!artifact.filePath && !artifact.contentBuffer && !artifact.contentStream) {
      logger.error('ArtifactUtils: Artifact must have filePath, contentBuffer, or contentStream');
      return false;
    }

    // Check filename
    if (!artifact.fileName) {
      logger.error('ArtifactUtils: Missing fileName in artifact');
      return false;
    }

    return true;
  } catch (error: any) {
    logger.error(`ArtifactUtils: Error validating artifact: ${error.message}`);
    return false;
  }
}

/**
 * Generate artifact key for storage
 * @param artifact Artifact
 * @returns Storage key
 */
export function generateArtifactKey(artifact: IArtifact): string {
  const { testId, platform, artifactType, timestamp } = artifact.metadata;
  const dateStr = timestamp.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = timestamp.toISOString().split('T')[1].split('.')[0].replace(/:/g, '-'); // HH-MM-SS

  return `${platform}/${artifactType}/${dateStr}/${testId}/${timeStr}_${artifact.fileName}`;
}

/**
 * Get artifact size information
 * @param artifact Artifact
 * @returns Size information
 */
export async function getArtifactSize(artifact: IArtifact): Promise<{ size: number; sizeFormatted: string }> {
  let size = 0;

  try {
    if (artifact.contentBuffer) {
      size = artifact.contentBuffer.length;
    } else if (artifact.filePath) {
      const fs = await import('fs');
      const stats = await fs.promises.stat(artifact.filePath);
      size = stats.size;
    }

    const sizeFormatted = formatBytes(size);
    return { size, sizeFormatted };
  } catch (error: any) {
    logger.warn(`ArtifactUtils: Error getting artifact size: ${error.message}`);
    return { size: 0, sizeFormatted: '0 B' };
  }
}

/**
 * Format bytes to human readable string
 * @param bytes Number of bytes
 * @returns Formatted string
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
