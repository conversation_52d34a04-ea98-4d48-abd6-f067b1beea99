/**
 * Company Service
 * Şirket yönetimi için MongoDB servis fonksiyonları
 */
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import { db, isMongoDBInitialized, companiesCollection } from './dbConnection.js';
import { createTeam } from './teamService.js';
import { getSystemSettingByType } from './systemSettingsService.js';
import { SystemSettingType } from '../../models/system-settings.js';
import {
  Company,
  CompanyStatus,
  CreateCompanyResult,
  GetCompanyResult,
  GetCompaniesResult,
  DeleteCompanyResult,
  UpdateCompanyResult,
  CompanyPaginationOptions,
  AccountType,
  AIModel
} from '../../models/company.js';

/**
 * Yeni bir şirket oluştur
 * @param companyData Şirket verileri
 * @returns Oluşturma sonucu
 */
export async function createCompany(companyData: Partial<Company>): Promise<CreateCompanyResult> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Zorunlu alanları kontrol et
    if (!companyData.name) {
      return { success: false, message: 'Company name is required' };
    }

    if (!companyData.createdBy) {
      return { success: false, message: 'Creator ID is required' };
    }

    // Şirket ID'si oluştur
    const companyId = uuidv4();
    const now = new Date();

    // Şirket nesnesini oluştur
    const company: Company = {
      id: companyId,
      name: companyData.name,
      description: companyData.description || '',
      status: companyData.status || CompanyStatus.ACTIVE,
      createdAt: now,
      updatedAt: now,
      createdBy: companyData.createdBy,
      contactEmail: companyData.contactEmail,
      contactPhone: companyData.contactPhone,
      address: companyData.address,
      website: companyData.website,
      settings: companyData.settings || {
        // Varsayılan hesap türü ve ayarlar
        accountType: AccountType.TRIAL,
        trialEndsAt: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 gün

        // Koşum limitleri
        runLimit: 100,
        runMinuteLimit: 500,
        concurrentRunLimit: 2,
        generationLimit: 200,

        // Kalan kullanım - başlangıçta limitlerle aynı
        remaining: {
          runs: 100,
          runMinutes: 500,
          generations: 200
        },

        // Kullanıcı ve takım limitleri
        maxUsers: 5,
        maxTeams: 1
      },
      teamId: null // Tek bir takım ID'si tutulacak
    };

    // Varsayılan AI modellerini ekle
    try {
      const defaultModels = await getDefaultAIModels();
      if (defaultModels.length > 0) {
        company.aiModels = defaultModels;
        logger.info(`Added ${defaultModels.length} default AI models to company: ${companyId}`);
      }
    } catch (modelError) {
      logger.error(`Error adding default AI models to company: ${companyId} - ${modelError}`);
      // AI model ekleme hatası şirket oluşturmayı etkilemeyecek
    }

    // MongoDB'ye ekle
    await companiesCollection.insertOne(company);

    logger.info(`Created new company with ID: ${companyId} by user: ${companyData.createdBy}`);

    // Otomatik olarak bir takım oluştur
    try {
      const teamName = `${companyData.name} Team`;
      const teamResult = await createTeam({
        name: teamName,
        description: `Default team for ${companyData.name}`,
        createdBy: companyData.createdBy,
        companyId: companyId
      });

      if (teamResult.success && teamResult.teamId) {
        // Takım ID'sini şirkete ekle
        await companiesCollection.updateOne(
          { id: companyId },
          { $set: { teamId: teamResult.teamId } }
        );

        logger.info(`Created default team with ID: ${teamResult.teamId} for company: ${companyId}`);

        // Takım için varsayılan rolleri oluştur
        try {
          const { createDefaultTeamRoles } = await import('./roleService.js');
          const rolesResult = await createDefaultTeamRoles(teamResult.teamId, companyId);

          if (rolesResult.success) {
            logger.info(`Created default roles for team ${teamResult.teamId} of company ${companyId}: ${rolesResult.roleIds?.join(', ')}`);
          } else {
            logger.error(`Failed to create default roles for team ${teamResult.teamId} of company ${companyId}: ${rolesResult.message}`);
          }
        } catch (roleError) {
          logger.error(`Error creating default roles for team ${teamResult.teamId} of company ${companyId}: ${roleError}`);
          // Rol oluşturma hatası şirket oluşturmayı etkilemeyecek
        }
      } else {
        logger.error(`Failed to create default team for company: ${companyId}`);
      }
    } catch (error) {
      logger.error(`Error creating default team for company: ${companyId} - ${error}`);
      // Takım oluşturma hatası şirket oluşturmayı etkilemeyecek
    }

    return {
      success: true,
      companyId: companyId
    };
  } catch (error: any) {
    logger.error(`Error creating company: ${error.message}`);
    return {
      success: false,
      message: `Failed to create company: ${error.message}`
    };
  }
}

/**
 * Şirket bilgilerini ID'ye göre getir
 * @param companyId Şirket ID'si
 * @returns Şirket bilgileri
 */
export async function getCompanyById(companyId: string): Promise<GetCompanyResult> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Şirketi bul
    const company = await companiesCollection.findOne({ id: companyId });

    if (!company) {
      return {
        success: false,
        message: 'Company not found'
      };
    }

    return {
      success: true,
      company: company as Company
    };
  } catch (error: any) {
    logger.error(`Error getting company: ${error.message}`);
    return {
      success: false,
      message: `Failed to get company: ${error.message}`
    };
  }
}

/**
 * Tüm şirketleri getir
 * @param options Sayfalama seçenekleri
 * @returns Şirket listesi
 */
export async function getAllCompanies(options: CompanyPaginationOptions = {}): Promise<GetCompaniesResult> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Sorgu oluştur
    const query: any = {};

    // Aktif olmayan şirketleri dahil etme seçeneği
    if (!options.includeInactive) {
      query.status = CompanyStatus.ACTIVE;
    }

    // Sayfalama seçenekleri
    const limit = options.limit || 50;
    const skip = options.skip || 0;

    // Sıralama seçenekleri
    const sortField = options.sortBy || 'createdAt';
    const sortOrder = options.sortOrder === 'asc' ? 1 : -1;

    // Şirketleri getir
    const companies = await companiesCollection
      .find(query)
      .sort({ [sortField]: sortOrder })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Toplam şirket sayısını getir
    const total = await companiesCollection.countDocuments(query);

    return {
      success: true,
      companies: companies as Company[],
      total
    };
  } catch (error: any) {
    logger.error(`Error getting companies: ${error.message}`);
    return {
      success: false,
      message: `Failed to get companies: ${error.message}`
    };
  }
}

/**
 * Şirket bilgilerini güncelle
 * @param companyId Şirket ID'si
 * @param updateData Güncellenecek veriler
 * @returns Güncelleme sonucu
 */
export async function updateCompany(
  companyId: string,
  updateData: Partial<Company>
): Promise<UpdateCompanyResult> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Güncellenemeyecek alanları kaldır
    const safeUpdateData = { ...updateData };
    delete safeUpdateData.id;
    delete safeUpdateData.createdAt;
    delete safeUpdateData.createdBy;

    // Güncellenme zamanını ayarla
    safeUpdateData.updatedAt = new Date();

    // Şirketi güncelle
    const result = await companiesCollection.findOneAndUpdate(
      { id: companyId },
      { $set: safeUpdateData },
      { returnDocument: 'after' }
    );

    if (!result) {
      return {
        success: false,
        message: 'Company not found or update failed'
      };
    }

    logger.info(`Updated company with ID: ${companyId}`);

    return {
      success: true,
      company: result as Company
    };
  } catch (error: any) {
    logger.error(`Error updating company: ${error.message}`);
    return {
      success: false,
      message: `Failed to update company: ${error.message}`
    };
  }
}

/**
 * Şirketi sil
 * @param companyId Şirket ID'si
 * @returns Silme sonucu
 */
export async function deleteCompany(companyId: string): Promise<DeleteCompanyResult> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Şirketi bul
    const company = await companiesCollection.findOne({ id: companyId });

    if (!company) {
      return {
        success: false,
        message: 'Company not found'
      };
    }

    // Şirketi sil
    await companiesCollection.deleteOne({ id: companyId });

    logger.info(`Deleted company with ID: ${companyId}`);

    return {
      success: true,
      message: 'Company deleted successfully'
    };
  } catch (error: any) {
    logger.error(`Error deleting company: ${error.message}`);
    return {
      success: false,
      message: `Failed to delete company: ${error.message}`
    };
  }
}

/**
 * Şirkete takım ekle
 * @param companyId Şirket ID'si
 * @param teamId Takım ID'si
 * @returns Ekleme sonucu
 */
export async function addTeamToCompany(
  companyId: string,
  teamId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Şirketi güncelle
    const result = await companiesCollection.updateOne(
      { id: companyId },
      {
        $set: {
          teamId: teamId,
          updatedAt: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: 'Company not found'
      };
    }

    logger.info(`Added team ${teamId} to company ${companyId}`);

    return {
      success: true,
      message: 'Team added to company successfully'
    };
  } catch (error: any) {
    logger.error(`Error adding team to company: ${error.message}`);
    return {
      success: false,
      message: `Failed to add team to company: ${error.message}`
    };
  }
}

/**
 * Şirketten takım çıkar
 * @param companyId Şirket ID'si
 * @param teamId Takım ID'si
 * @returns Çıkarma sonucu
 */
/**
 * Şirketin kalan kullanım bilgilerini güncelle
 * @param companyId Şirket ID'si
 * @param remaining Kalan kullanım bilgileri
 * @returns Güncelleme sonucu
 */
export async function updateCompanyRemaining(
  companyId: string,
  remaining: {
    runs?: number;
    runMinutes?: number;
    generations?: number;
  }
): Promise<UpdateCompanyResult> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Şirketi bul
    const company = await companiesCollection.findOne({ id: companyId });

    if (!company) {
      return {
        success: false,
        message: 'Company not found'
      };
    }

    // Mevcut ayarları al
    const settings = company.settings || {};

    // Kalan kullanım bilgilerini güncelle
    settings.remaining = {
      ...settings.remaining,
      ...remaining
    };

    // Şirketi güncelle
    const result = await companiesCollection.findOneAndUpdate(
      { id: companyId },
      {
        $set: {
          settings: settings,
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (!result) {
      return {
        success: false,
        message: 'Company not found or update failed'
      };
    }

    logger.info(`Updated company remaining usage with ID: ${companyId}`);

    return {
      success: true,
      company: result as Company
    };
  } catch (error: any) {
    logger.error(`Error updating company remaining: ${error.message}`);
    return {
      success: false,
      message: `Failed to update company remaining: ${error.message}`
    };
  }
}

/**
 * Sistem ayarlarından varsayılan AI modellerini alır
 * @returns Varsayılan AI modelleri
 */
export async function getDefaultAIModels(): Promise<AIModel[]> {
  try {
    // Sistem ayarlarından aktif AI modelini al
    const result = await getSystemSettingByType(SystemSettingType.AI_MODEL);

    if (!result.success || !result.setting) {
      logger.info('No default AI models found in system settings');
      return [];
    }

    // Sistem ayarlarındaki modeli şirket modeline dönüştür
    const systemModel = result.setting.config;
    const now = new Date();

    const defaultModel: AIModel = {
      id: uuidv4(),
      name: systemModel.name,
      api: systemModel.api,
      apiKey: systemModel.apiKey,
      isActive: systemModel.isActive,
      supportsImageProcessing: systemModel.supportsImageProcessing,
      createdAt: now,
      updatedAt: now,
      createdBy: 'system'
    };

    return [defaultModel];
  } catch (error: any) {
    logger.error(`Error getting default AI models: ${error.message}`);
    return [];
  }
}

/**
 * Şirket için AI Model ekler
 * @param companyId Şirket ID
 * @param userId Ekleyen kullanıcı ID
 * @param modelData AI Model verileri
 * @returns İşlem sonucu
 */
export async function addCompanyAIModel(
  companyId: string,
  userId: string,
  modelData: {
    name: string;
    api: string;
    apiKey: string;
    supportsImageProcessing: boolean;
  }
): Promise<{
  success: boolean;
  message?: string;
  model?: AIModel;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!companyId) {
      return { success: false, message: 'Company ID is required' };
    }

    if (!userId) {
      return { success: false, message: 'User ID is required' };
    }

    // Şirketi bul
    const company = await companiesCollection.findOne({ id: companyId });
    if (!company) {
      return { success: false, message: 'Company not found' };
    }

    // Yeni AI Model oluştur
    const now = new Date();
    const newModel: AIModel = {
      id: uuidv4(),
      name: modelData.name,
      api: modelData.api,
      apiKey: modelData.apiKey,
      isActive: true,
      supportsImageProcessing: modelData.supportsImageProcessing,
      createdAt: now,
      updatedAt: now,
      createdBy: userId
    };

    // Şirketin aiModels dizisini güncelle
    await companiesCollection.updateOne(
      { id: companyId },
      {
        $push: { aiModels: newModel as any },
        $set: { updatedAt: now }
      }
    );

    logger.info(`AI Model added for company ${companyId} by user ${userId}: ${newModel.name}`);

    return {
      success: true,
      message: 'AI Model added successfully',
      model: newModel
    };
  } catch (error: any) {
    logger.error(`Error adding AI Model for company ${companyId}: ${error.message}`);
    return { success: false, message: error.message };
  }
}

/**
 * Şirketin AI Modellerini listeler
 * @param companyId Şirket ID
 * @returns Şirketin AI Modelleri
 */
export async function getCompanyAIModels(
  companyId: string
): Promise<{
  success: boolean;
  message?: string;
  models?: AIModel[];
}> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!companyId) {
      return { success: false, message: 'Company ID is required' };
    }

    // Şirketi bul
    const company = await companiesCollection.findOne({ id: companyId });
    if (!company) {
      return { success: false, message: 'Company not found' };
    }

    // AI Modelleri döndür
    return {
      success: true,
      models: company.aiModels || []
    };
  } catch (error: any) {
    logger.error(`Error getting AI Models for company ${companyId}: ${error.message}`);
    return { success: false, message: error.message };
  }
}

/**
 * Şirketin belirli bir AI Modelini günceller
 * @param companyId Şirket ID
 * @param modelId AI Model ID
 * @param updateData Güncellenecek veriler
 * @returns İşlem sonucu
 */
export async function updateCompanyAIModel(
  companyId: string,
  modelId: string,
  updateData: Partial<AIModel>
): Promise<{
  success: boolean;
  message?: string;
  model?: AIModel;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!companyId || !modelId) {
      return { success: false, message: 'Company ID and Model ID are required' };
    }

    // Şirketi ve modeli bul
    const company = await companiesCollection.findOne({ id: companyId });
    if (!company) {
      return { success: false, message: 'Company not found' };
    }

    if (!company.aiModels) {
      return { success: false, message: 'Company has no AI models' };
    }

    // Modeli bul
    const modelIndex = company.aiModels.findIndex((model: any) => model.id === modelId);
    if (modelIndex === -1) {
      return { success: false, message: 'AI Model not found' };
    }

    // Modeli güncelle
    const now = new Date();
    const updatedModel = {
      ...company.aiModels[modelIndex],
      ...updateData,
      updatedAt: now
    };

    // İzin verilmeyen alanların güncellenmesini engelle
    updatedModel.id = modelId; // ID değiştirilemez
    updatedModel.createdAt = company.aiModels[modelIndex].createdAt; // Oluşturma tarihi değiştirilemez
    updatedModel.createdBy = company.aiModels[modelIndex].createdBy; // Oluşturan kullanıcı değiştirilemez

    // Veritabanındaki modeli güncelle
    await companiesCollection.updateOne(
      { id: companyId, "aiModels.id": modelId },
      {
        $set: {
          "aiModels.$": updatedModel,
          updatedAt: now
        }
      }
    );

    logger.info(`AI Model updated for company ${companyId}: ${modelId}`);

    return {
      success: true,
      message: 'AI Model updated successfully',
      model: updatedModel
    };
  } catch (error: any) {
    logger.error(`Error updating AI Model for company ${companyId}: ${error.message}`);
    return { success: false, message: error.message };
  }
}

/**
 * Şirketin belirli bir AI Modelini siler
 * @param companyId Şirket ID
 * @param modelId AI Model ID
 * @returns İşlem sonucu
 */
export async function deleteCompanyAIModel(
  companyId: string,
  modelId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!companyId || !modelId) {
      return { success: false, message: 'Company ID and Model ID are required' };
    }

    // Şirketi bul
    const company = await companiesCollection.findOne({ id: companyId });
    if (!company) {
      return { success: false, message: 'Company not found' };
    }

    if (!company.aiModels) {
      return { success: false, message: 'Company has no AI models' };
    }

    // Modeli sil
    const now = new Date();
    await companiesCollection.updateOne(
      { id: companyId },
      {
        $pull: { aiModels: { id: modelId } as any },
        $set: { updatedAt: now }
      }
    );

    logger.info(`AI Model deleted for company ${companyId}: ${modelId}`);

    return {
      success: true,
      message: 'AI Model deleted successfully'
    };
  } catch (error: any) {
    logger.error(`Error deleting AI Model for company ${companyId}: ${error.message}`);
    return { success: false, message: error.message };
  }
}

export async function removeTeamFromCompany(
  companyId: string,
  teamId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !companiesCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Şirketi güncelle
    const result = await companiesCollection.updateOne(
      { id: companyId, teamId: teamId },
      {
        $unset: { teamId: "" },
        $set: { updatedAt: new Date() }
      }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: 'Company not found'
      };
    }

    logger.info(`Removed team ${teamId} from company ${companyId}`);

    return {
      success: true,
      message: 'Team removed from company successfully'
    };
  } catch (error: any) {
    logger.error(`Error removing team from company: ${error.message}`);
    return {
      success: false,
      message: `Failed to remove team from company: ${error.message}`
    };
  }
}
