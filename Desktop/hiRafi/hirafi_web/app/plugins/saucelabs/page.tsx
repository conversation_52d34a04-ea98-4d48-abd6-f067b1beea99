"use client"

import { useState, useEffect } from "react"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Check, ChevronRight, Puzzle, AlertCircle, ArrowRight, CheckCircle2, LoaderCircle, Smartphone, Upload, Settings, User, Package, Trash2 } from "lucide-react"
import { CompanyOwnerRoute } from "@/components/company-owner-route"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { pluginApi } from "@/lib/api"
import { API_BASE_URL } from "@/lib/api/constants"
import ErrorBoundary from "@/components/error-boundary"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { ConfirmDialog } from "@/components/team/confirm-dialog"

function SauceLabsPlugin() {
  const { toast } = useToast()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [configStep, setConfigStep] = useState(1)
  const [isActive, setIsActive] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingConfig, setIsLoadingConfig] = useState(true)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [configData, setConfigData] = useState({
    username: "",
    accessKey: "",
    region: "us-west-1",
  })
  const [activeConfigData, setActiveConfigData] = useState<any>(null)
  const [accountInfo, setAccountInfo] = useState<any>(null)
  const [apps, setApps] = useState<any[]>([])
  const [appGroups, setAppGroups] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState("account")
  const [showReconfigureConfirm, setShowReconfigureConfirm] = useState(false)
  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false)
  const [showAppDeleteConfirm, setShowAppDeleteConfirm] = useState(false)
  const [appToDelete, setAppToDelete] = useState<any>(null)

  // Fetch current configuration on component mount
  useEffect(() => {
    fetchCurrentConfig()
  }, [])

  // Fetch current configuration
  const fetchCurrentConfig = async () => {
    setIsLoadingConfig(true)
    try {
      const response = await pluginApi.getSauceLabsConfig()

      if (response.success && response.config) {
        setActiveConfigData(response.config)
        setConfigData({
          username: response.config.username || "",
          accessKey: response.config.accessKey || "",
          region: response.config.region || "us-west-1"
        })

        const isPluginActive = response.plugin?.active || false
        setIsActive(isPluginActive)

        // If active, fetch account info, apps, and app groups
        if (isPluginActive) {
          fetchAccountInfo()
          fetchApps()
          fetchAppGroups()
        }
      } else {
        setIsActive(false)
      }
    } catch (error) {
      setIsActive(false)
    } finally {
      setIsLoadingConfig(false)
    }
  }

  // Test connection
  const testConnection = async () => {
    setIsTestingConnection(true)
    try {
      const response = await pluginApi.testSauceLabsConnection({
        username: configData.username,
        accessKey: configData.accessKey,
        region: configData.region
      })

      if (response.success) {
        const message = response.message || "SauceLabs bağlantısı başarıyla kuruldu"

        toast({
          title: "Bağlantı Başarılı",
          description: message,
          variant: "default"
        })
        setConfigStep(2)
      } else {
        // Enhanced error handling with troubleshooting
        const errorDescription = response.details
          ? `${response.error}\n\nDetay: ${response.details}`
          : response.error || "SauceLabs bağlantısı başarısız oldu"

        const troubleshooting = response.troubleshooting
        const troubleshootingText = troubleshooting && troubleshooting.length > 0
          ? `\n\nÇözüm önerileri:\n• ${troubleshooting.join('\n• ')}`
          : ''

        toast({
          title: "Bağlantı Hatası",
          description: errorDescription + troubleshootingText,
          variant: "destructive",
          duration: 8000 // Longer duration for detailed error
        })
      }
    } catch (error: any) {
      toast({
        title: "Bağlantı Hatası",
        description: error.message || "Bağlantı testi sırasında beklenmeyen bir hata oluştu",
        variant: "destructive"
      })
    } finally {
      setIsTestingConnection(false)
    }
  }

  // Save configuration
  const saveConfig = async () => {
    setIsLoading(true)
    try {
      const response = await pluginApi.updateSauceLabsConfig({
        username: configData.username,
        accessKey: configData.accessKey,
        region: configData.region
      })

      if (response.success) {
        toast({
          title: "Configuration Saved",
          description: "SauceLabs configuration has been saved successfully",
          variant: "default"
        })
        setIsActive(true)
        fetchCurrentConfig()
      } else {
        toast({
          title: "Save Failed",
          description: response.error || "Failed to save configuration",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Save Error",
        description: error.message || "An error occurred while saving configuration",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch account information
  const fetchAccountInfo = async () => {
    try {
      const response = await pluginApi.getSauceLabsAccountInfo()

      if (response.success && response.data) {
        setAccountInfo(response.data)
      }
    } catch (error) {
      // Account info fetch failed silently
    }
  }

  // Fetch apps
  const fetchApps = async () => {
    try {
      const response = await pluginApi.getSauceLabsApps()

      if (response.success && response.apps) {
        setApps(response.apps || [])
      }
    } catch (error) {
      // Apps fetch failed silently
    }
  }

  // Fetch app groups
  const fetchAppGroups = async () => {
    try {
      const response = await pluginApi.getSauceLabsAppGroups()

      if (response.success && response.groups) {
        setAppGroups(response.groups || [])
      }
    } catch (error) {
      // App groups fetch failed silently
    }
  }

  // Handle app delete confirmation
  const handleDeleteApp = (app: any) => {
    setAppToDelete(app)
    setShowAppDeleteConfirm(true)
  }

  // Confirm app deletion
  const confirmDeleteApp = async () => {
    if (!appToDelete) return

    try {
      const response = await pluginApi.deleteSauceLabsApp(appToDelete.id)

      if (response.success) {
        toast({
          title: "Delete Successful",
          description: "App has been deleted from SauceLabs",
          variant: "default"
        })
        fetchApps()
      } else {
        toast({
          title: "Delete Failed",
          description: response.error || "Failed to delete app from SauceLabs",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Delete Error",
        description: error.message || "An error occurred while deleting app",
        variant: "destructive"
      })
    } finally {
      setAppToDelete(null)
      setShowAppDeleteConfirm(false)
    }
  }



  // Reconfigure plugin (delete and allow reconfiguration)
  const reconfigurePlugin = async () => {
    try {
      const response = await pluginApi.deletePlugin('saucelabs')

      if (response.success) {
        toast({
          title: "Plugin Reconfigured",
          description: "SauceLabs plugin has been reset for reconfiguration",
          variant: "default"
        })
        // Reset to configuration step 1 to allow reconfiguration
        setConfigStep(1)
        setIsActive(false)
        // Reset config data
        setConfigData({
          username: "",
          accessKey: "",
          region: "us-west-1",
        })
        setActiveConfigData(null)
        setAccountInfo(null)
        setApps([])
        setAppGroups([])
        // Optionally, refresh the current config state
        // This will ensure we're in sync with the backend
        fetchCurrentConfig()
      } else {
        toast({
          title: "Reconfiguration Failed",
          description: response.error || "Failed to reset plugin configuration",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "An error occurred while resetting configuration",
        variant: "destructive"
      })
    } finally {
      setShowReconfigureConfirm(false)
    }
  }

  // Disconnect plugin completely
  const disconnectPlugin = async () => {
    try {
      const response = await pluginApi.disconnectSauceLabs()

      if (response.success) {
        toast({
          title: "Plugin Disconnected",
          description: "SauceLabs plugin has been disconnected successfully",
          variant: "default"
        })
        // Reset to configuration step 1
        setConfigStep(1)
        setIsActive(false)
        // Reset config data
        setConfigData({
          username: "",
          accessKey: "",
          region: "us-west-1",
        })
        setActiveConfigData(null)
        setAccountInfo(null)
        setApps([])
        setAppGroups([])
        // Refresh the current config state
        fetchCurrentConfig()
      } else {
        toast({
          title: "Disconnect Failed",
          description: response.error || "Failed to disconnect plugin",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "An error occurred while disconnecting",
        variant: "destructive"
      })
    } finally {
      setShowDisconnectConfirm(false)
    }
  }

  // Render active card
  const renderActiveCard = () => {
    if (!activeConfigData) {
      return (
        <Card className="border-0 shadow-md dark:bg-gray-900">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 rounded-full flex items-center justify-center">
                <AlertCircle className="h-5 w-5" />
              </div>
              <div>
                <CardTitle>SauceLabs Integration Not Configured</CardTitle>
                <CardDescription>Your SauceLabs integration is not set up yet. Please configure it below.</CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>
      )
    }

    return (
      <Card className="border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 border-b">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400 rounded-full flex items-center justify-center">
              <Check className="h-5 w-5" />
            </div>
            <div>
              <CardTitle>SauceLabs Integration Active</CardTitle>
              <CardDescription>Your SauceLabs integration is set up and working correctly.</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs defaultValue="account" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground mb-6">
              <TabsTrigger value="account" className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground">
                <User className="h-4 w-4" />
                Account
              </TabsTrigger>
              <TabsTrigger value="apps" className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground">
                <Package className="h-4 w-4" />
                Apps
              </TabsTrigger>
            </TabsList>

            {/* Account Tab */}
            <TabsContent value="account" className="space-y-6">
              {accountInfo && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Account Information</h3>
                    <Button variant="outline" size="sm" onClick={fetchAccountInfo} className="gap-2">
                      <LoaderCircle className="h-3.5 w-3.5" />
                      Refresh
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-100 dark:border-gray-700">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-md">
                          <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h4 className="font-medium">User Details</h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Basic account information</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Username</h5>
                          <p className="text-sm font-medium">{accountInfo.username}</p>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Name</h5>
                          <p className="text-sm font-medium">
                            {accountInfo.first_name} {accountInfo.last_name}
                          </p>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Email</h5>
                          <p className="text-sm font-medium">{accountInfo.email}</p>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Account Type</h5>
                          <p className="text-sm font-medium capitalize">
                            {accountInfo.user_type}
                            {accountInfo.is_organization_admin && " (Organization Admin)"}
                          </p>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Team</h5>
                          <p className="text-sm font-medium">
                            {accountInfo.teams?.[0]?.name || "N/A"}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-100 dark:border-gray-700">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-md">
                          <Smartphone className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                        </div>
                        <div>
                          <h4 className="font-medium">Usage & Limits</h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Account resources and settings</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Region</h5>
                          <p className="text-sm font-medium">{activeConfigData.region || "us-west-1"}</p>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Real Devices</h5>
                          <p className="text-sm font-medium">
                            {accountInfo.organization?.settings?.real_devices || accountInfo.teams?.[0]?.settings?.real_devices || "N/A"} device(s)
                          </p>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Virtual Machines</h5>
                          <p className="text-sm font-medium">
                            {accountInfo.organization?.settings?.virtual_machines || accountInfo.teams?.[0]?.settings?.virtual_machines || "N/A"} VM(s)
                          </p>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Allowed Regions</h5>
                          <p className="text-sm font-medium">
                            {accountInfo.allowed_regions ? accountInfo.allowed_regions.join(", ") : "N/A"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Apps Tab */}
            <TabsContent value="apps" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Installed Applications</h3>
                  <Button variant="outline" size="sm" onClick={fetchApps}>
                    <Package className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>

                {apps.length === 0 ? (
                  <div className="text-center py-12 border rounded-md bg-gray-50 dark:bg-gray-900/50">
                    <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Applications Found</h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-4 max-w-md mx-auto">
                      No applications have been uploaded to SauceLabs yet. Upload applications during test execution for better workflow management.
                    </p>
                  </div>
                ) : (
                  <div className="border rounded-lg overflow-hidden bg-white dark:bg-gray-800">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Application
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Details
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Upload Date
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Size
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          {apps.map((app: any, index: number) => (
                            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                              <td className="px-6 py-4">
                                <div className="flex items-center gap-3">
                                  <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-md flex-shrink-0">
                                    <Package className="h-4 w-4 text-green-600 dark:text-green-400" />
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate" title={app.name}>
                                      {app.name}
                                    </p>
                                    <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                                      ID: {app.id ? app.id.substring(0, 12) : "N/A"}
                                    </p>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <div className="space-y-1">
                                  <p className="text-sm text-gray-900 dark:text-white">
                                    {app.kind || "Unknown"}
                                  </p>
                                  <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                                    {app.description || "No description"}
                                  </p>
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <p className="text-sm text-gray-900 dark:text-white">
                                  {app.upload_timestamp ? new Date(app.upload_timestamp * 1000).toLocaleDateString() : "N/A"}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {app.upload_timestamp ? new Date(app.upload_timestamp * 1000).toLocaleTimeString() : ""}
                                </p>
                              </td>
                              <td className="px-6 py-4">
                                <p className="text-sm text-gray-900 dark:text-white">
                                  {app.size ? `${Math.round(app.size / 1024 / 1024 * 10) / 10} MB` : "N/A"}
                                </p>
                              </td>
                              <td className="px-6 py-4 text-right">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                                  onClick={() => handleDeleteApp(app)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>




          </Tabs>
        </CardContent>
        <CardFooter className="border-t bg-gray-50/50 dark:bg-gray-900/30 flex justify-center gap-3 py-4">
          <Button variant="outline" onClick={() => setShowReconfigureConfirm(true)}>
            <Settings className="h-4 w-4 mr-2" />
            Reconfigure
          </Button>
          <Button variant="destructive" onClick={() => setShowDisconnectConfirm(true)}>
            <Trash2 className="h-4 w-4 mr-2" />
            Disconnect
          </Button>
        </CardFooter>
      </Card>
    )
  }

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-6 bg-white dark:bg-gray-900">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-sm">
              <Smartphone className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">SauceLabs Integration</h1>
              <p className="text-gray-500 dark:text-gray-400">
                Connect your SauceLabs account to access mobile devices for testing
              </p>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-3xl mx-auto">
            {isLoadingConfig ? (
              <div className="flex items-center justify-center h-64">
                <LoaderCircle className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : isActive ? (
              renderActiveCard()
            ) : (
              <Card className="border-0 shadow-md">
                <CardHeader className="border-b">
                  <CardTitle>Configure SauceLabs Integration</CardTitle>
                  <CardDescription>
                    Connect your SauceLabs account to access mobile devices for testing
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  {configStep === 1 && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="username">SauceLabs Username</Label>
                        <Input
                          id="username"
                          placeholder="Enter your SauceLabs username"
                          value={configData.username}
                          onChange={(e) => setConfigData({ ...configData, username: e.target.value })}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="accessKey">Access Key</Label>
                        <Input
                          id="accessKey"
                          type="password"
                          placeholder="Enter your SauceLabs access key"
                          value={configData.accessKey}
                          onChange={(e) => setConfigData({ ...configData, accessKey: e.target.value })}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="region">Region</Label>
                        <Select
                          value={configData.region}
                          onValueChange={(value) => setConfigData({ ...configData, region: value })}
                        >
                          <SelectTrigger id="region">
                            <SelectValue placeholder="Select a region" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="us-west-1">US West (us-west-1)</SelectItem>
                            <SelectItem value="eu-central-1">EU Central (eu-central-1)</SelectItem>
                            <SelectItem value="apac-southeast-1">APAC Southeast (apac-southeast-1)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Alert className="mt-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                        <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <AlertTitle>Connection Required</AlertTitle>
                        <AlertDescription>
                          You need to test the connection before saving the configuration.
                        </AlertDescription>
                      </Alert>

                      {/* Action Buttons for Step 1 */}
                      <div className="flex justify-between pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
                        <Button variant="outline" onClick={() => {
                          setConfigData({
                            username: "",
                            accessKey: "",
                            region: "us-west-1",
                          })
                        }}>
                          Reset Form
                        </Button>
                        <Button
                          variant="default"
                          onClick={testConnection}
                          disabled={!configData.username || !configData.accessKey || isTestingConnection}
                        >
                          {isTestingConnection ? (
                            <>
                              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                              Testing...
                            </>
                          ) : (
                            <>
                              Test Connection
                              <ArrowRight className="ml-2 h-4 w-4" />
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  )}

                  {configStep === 2 && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
                        <CheckCircle2 className="h-5 w-5" />
                        <p className="font-medium">Connection successful! You can now save your configuration.</p>
                      </div>

                      <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-md border">
                        <h3 className="font-medium mb-2">Configuration Summary</h3>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Username:</span>
                            <span className="ml-2 font-medium">{configData.username}</span>
                          </div>
                          <div>
                            <span className="text-gray-500 dark:text-gray-400">Region:</span>
                            <span className="ml-2 font-medium">{configData.region}</span>
                          </div>
                        </div>
                      </div>

                      <Alert className="mt-4 bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800">
                        <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                        <AlertTitle>Important</AlertTitle>
                        <AlertDescription>
                          After saving, you'll be able to select SauceLabs devices when creating Android test runs.
                        </AlertDescription>
                      </Alert>

                      {/* Action Buttons for Step 2 */}
                      <div className="flex justify-between pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
                        <Button variant="outline" onClick={() => setConfigStep(1)}>
                          Back
                        </Button>
                        <Button
                          variant="default"
                          onClick={saveConfig}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <>
                              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            <>
                              Save Configuration
                              <Check className="ml-2 h-4 w-4" />
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </main>
      </div>

      {/* Plugin Reconfiguration Confirmation Dialog */}
      <ConfirmDialog
        open={showReconfigureConfirm}
        onOpenChange={setShowReconfigureConfirm}
        title="Reconfigure Plugin"
        description="Are you sure you want to reconfigure the SauceLabs plugin? This will reset the current configuration and allow you to set it up again."
        confirmLabel="Yes, Reconfigure"
        cancelLabel="Cancel"
        onConfirm={reconfigurePlugin}
        variant="default"
      />

      {/* Plugin Disconnect Confirmation Dialog */}
      <ConfirmDialog
        open={showDisconnectConfirm}
        onOpenChange={setShowDisconnectConfirm}
        title="Disconnect Plugin"
        description="Are you sure you want to disconnect the SauceLabs plugin? This action cannot be undone and all configuration data will be permanently removed."
        confirmLabel="Yes, Disconnect"
        cancelLabel="Cancel"
        onConfirm={disconnectPlugin}
        variant="destructive"
      />

      {/* App Deletion Confirmation Dialog */}
      <ConfirmDialog
        open={showAppDeleteConfirm}
        onOpenChange={setShowAppDeleteConfirm}
        title="Delete Application"
        description={`Are you sure you want to delete "${appToDelete?.name}"? This action cannot be undone and the application will be permanently removed from SauceLabs.`}
        confirmLabel="Yes, Delete"
        cancelLabel="Cancel"
        onConfirm={confirmDeleteApp}
        variant="destructive"
      />
    </div>
  )
}

export default function SauceLabsPluginPage() {
  return (
    <ErrorBoundary>
      <CompanyOwnerRoute>
        <SauceLabsPlugin />
      </CompanyOwnerRoute>
    </ErrorBoundary>
  )
}