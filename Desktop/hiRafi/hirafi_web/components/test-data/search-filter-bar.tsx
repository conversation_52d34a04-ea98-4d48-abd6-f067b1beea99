"use client"

import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Search, Filter, Globe, Database, Plus } from "lucide-react"
import type { EnvironmentType, CustomEnvironment } from "@/types/test-data"

interface SearchFilterBarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  activeTab: string
  environmentFilter?: EnvironmentType
  onEnvironmentFilterChange?: (filter: EnvironmentType) => void
  customEnvironments?: CustomEnvironment[]
  onNewEnvironment?: () => void
  onNewDataSource?: () => void
  onNewDataSet?: () => void
}

export function SearchFilterBar({
  searchQuery,
  onSearchChange,
  activeTab,
  environmentFilter,
  onEnvironmentFilterChange,
  customEnvironments = [],
  onNewEnvironment,
  onNewDataSource,
  onNewDataSet,
}: SearchFilterBarProps) {
  const getEnvironmentLabel = (env: EnvironmentType) => {
    const environment = customEnvironments.find((e) => e.id === env)
    return environment?.name || env
  }

  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3 w-full max-w-md">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search data sets, sources, and environments..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        {activeTab === "data-sets" && environmentFilter && onEnvironmentFilterChange && (
          <Select value={environmentFilter} onValueChange={onEnvironmentFilterChange}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <span>{environmentFilter === "all" ? "All Environments" : getEnvironmentLabel(environmentFilter)}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Environments</SelectItem>
              {customEnvironments
                .filter((env) => env.isActive)
                .map((env, index) => (
                  <SelectItem key={env.id || `env-${index}`} value={env.id}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full bg-${env.color || 'gray'}-500`}></div>
                      <span>{env.name || 'Unnamed Environment'}</span>
                      {env.type === "custom" && (
                        <Badge variant="outline" className="text-xs ml-1">
                          Custom
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        )}
      </div>

      <div className="flex items-center gap-3">
        {activeTab === "environments" && onNewEnvironment && (
          <Button variant="outline" className="gap-1" onClick={onNewEnvironment}>
            <Globe className="h-4 w-4" />
            New Environment
          </Button>
        )}

        {activeTab === "data-sources" && onNewDataSource && (
          <Button variant="outline" className="gap-1" onClick={onNewDataSource}>
            <Database className="h-4 w-4" />
            New Data Source
          </Button>
        )}

        {activeTab === "data-sets" && onNewDataSet && (
          <Button
            className="gap-1 bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
            onClick={onNewDataSet}
          >
            <Plus className="h-4 w-4" />
            New Data Set
          </Button>
        )}
      </div>
    </div>
  )
}
