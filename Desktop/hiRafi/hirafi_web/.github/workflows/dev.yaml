name: <PERSON> Docker build and Deploy Service

on:
  push:
    branches:
      - dev

env:
  PROJECT_NAME: hirafi-web
  AWS_REGION: eu-north-1
  HOST_IP: *************
  ECS_CLUSTER: Hirafi-Dev

jobs:
  build:
    runs-on: ubuntu-latest
 
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set short sha
        id: sha_short
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build & Push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ env.PROJECT_NAME }}:${{ steps.sha_short.outputs.sha_short }}
            ${{ steps.login-ecr.outputs.registry }}/${{ env.PROJECT_NAME }}:dev-latest
          build-args: |
            NEXT_PUBLIC_API_BASE_URL=https://dev-api.hirafi.ai/api
            

  service-update:
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Download task definition
        run:  aws ecs update-service --cluster ${{ env.ECS_CLUSTER }} --service hirafi-dev-web-service --region ${{ env.AWS_REGION }} --force-new-deployment
