/**
 * API Modülleri
 *
 * Tüm API modüllerini tek bir yerden export eder.
 */

// API Constants
export * from './constants';

// Fetch Wrapper
export * from './fetch-wrapper';

// API Modules
export * as authApi from './auth-api';
export * as adminAuthApi from './admin-auth-api';
export * as dashboardApi from './dashboard-api';
export * as scenarioApi from './scenario-api';
export * as runApi from './run-api';
export * as settingsApi from './settings-api';
export * as pluginApi from './plugin-api';
export * as teamApi from './team-api';
export * as adminApi from './admin-api';
export * as reportApi from './report-api';
export * as userApi from './user-api';
export * as feedbackApi from './feedback-api';
export * as vncApi from './vnc-api';
export * as testHubApi from './test-hub-api';
export * as generateStepsApi from './generate-steps-api';
export * as statusApi from './status-api';
export * as stepProgressApi from './step-progress-api';

// Re-export for backward compatibility
export {
  fetchApi,
  fetchAdminApi,
  get,
  post,
  put,
  del,
  patch,
  adminGet,
  adminPost,
  adminPut,
  adminDel,
  adminPatch
} from './fetch-wrapper';
