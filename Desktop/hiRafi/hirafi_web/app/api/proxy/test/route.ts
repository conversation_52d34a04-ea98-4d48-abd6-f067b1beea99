import { NextResponse } from 'next/server';
import axios from 'axios';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { HttpProxyAgent } from 'http-proxy-agent';

// Test URL to check proxy connection
const TEST_URL = 'https://httpbin.org/ip';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { proxy } = body;

    if (!proxy) {
      return NextResponse.json({ success: false, error: 'Proxy settings are required' }, { status: 400 });
    }

    if (!proxy.host || !proxy.port) {
      return NextResponse.json({ success: false, error: 'Proxy host and port are required' }, { status: 400 });
    }

    // Create proxy agent based on proxy type
    let agent;
    const hasCredentials = !!(proxy.username && proxy.password);
    const auth = hasCredentials ? `${proxy.username}:${proxy.password}@` : '';

    switch (proxy.type) {
      case 'SOCKS4':
      case 'SOCKS5':
        const socksUrl = `${proxy.type.toLowerCase()}://${auth}${proxy.host}:${proxy.port}`;
        agent = new SocksProxyAgent(socksUrl);
        break;
      case 'HTTPS':
        const httpsUrl = `https://${auth}${proxy.host}:${proxy.port}`;
        agent = new HttpsProxyAgent(httpsUrl);
        break;
      case 'HTTP':
      default:
        const httpUrl = `http://${auth}${proxy.host}:${proxy.port}`;
        agent = new HttpProxyAgent(httpUrl);
        break;
    }

    // Set timeout to 10 seconds (10000 ms)
    const TIMEOUT_MS = 10000; // 10 seconds

    // Test the proxy connection
    const response = await axios.get(TEST_URL, {
      httpsAgent: agent,
      httpAgent: agent,
      timeout: TIMEOUT_MS,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_7_5) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Safari/605.1.15'
      }
    });

    // If we get here, the proxy connection was successful
    return NextResponse.json({
      success: true,
      message: 'Proxy connection successful',
      data: {
        ip: response.data.origin,
        proxyType: proxy.type
      }
    });
  } catch (error: any) {
    console.error('Proxy test error:', error);

    let errorMessage = 'Failed to connect to proxy server';

    if (error?.code === 'ECONNREFUSED') {
      errorMessage = 'Connection refused. The proxy server is not accepting connections.';
    } else if (error?.code === 'ECONNABORTED') {
      errorMessage = 'Connection timed out. The proxy server took too long to respond.';
    } else if (error?.code === 'ENOTFOUND') {
      errorMessage = 'Proxy host not found. Please check the hostname.';
    } else if (error?.response) {
      errorMessage = `Proxy server returned error: ${error.response.status} ${error.response.statusText}`;
    }

    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}
