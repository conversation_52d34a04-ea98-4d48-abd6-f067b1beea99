/**
 * Report API Routes
 * MongoDB üzerinden test raporları işlemleri için API rotaları
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import {
  getReportsByScenarioId,
  getReportById,
  getAllReports,
  updateReportTestRailStatus,
  getReportsSummary,
  updateReportAIInsight,
  enableReportSharing,
  disableReportSharing,
  getReportByShareToken,
  updateReportShareAccess
} from '../../services/mongo/reportService.js';
import {
  createTestReportAtomic
} from '../../services/mongo/atomicReportService.js';
import {
  sendReportToTestRail,
  getTestRailStatus,
  verifyTestRailConnection,
  verifyTestRailConnectionWithSettings
} from '../../services/mongo/testRailService.js';
import { AuthRequest } from '../middleware/authMiddleware.js';
import OpenAI from 'openai';
import {
  getRunReportsByUserId,
  getRunTestReportsByExecutionId,
  getRunReportById,
  deleteRunReportAndTestReports,
  deleteMultipleRunReports
} from '../../services/mongo/atomicReportService.js';
import { createReportPdf } from '../../utils/testReportPdfExporter.js';
import { ensureMongoDBConnection } from '../../services/mongo/dbConnection.js';
import { db } from '../../services/mongo/dbConnection.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = Router();

const systemPromtForReport =  `You are an advanced website optimization analyst. Your task is to perform a comprehensive assessment of a website's performance, accessibility, and overall technical health using the provided website analysis data (Page Metrics, Network Statistics, Violations, and Network Logs). Your goal is not just to report the current status but also to provide concrete, prioritized, and actionable improvement recommendations.

Input: You will be provided with website analysis data grouped under the categories detailed below.

Task Definition: 1. Detailed Analysis: Carefully examine each provided data point. Identify anomalies (unexpectedly high/low values), errors, bottlenecks, and deviations from general web development best practices (e.g., performance budgets, accessibility standards). 2. Interpretation and Recommendation: Interpret your findings for each category. Explain the potential causes of issues and their likely impact on the site (e.g., slow loading, poor user experience, search engine ranking problems, accessibility barriers). Your comments must contain specific, measurable, achievable, relevant, and time-bound (similar to SMART principles) improvement suggestions. For example, instead of "optimize images," provide recommendations like "Consider saving JPEG images at 75% quality or converting them to WebP format; this could reduce page size by approximately X KB." 3. Scoring: Score each main category (Page Metrics, Network Statistics, Total Violations, Network Logs) on a scale of 0-100. Base your scoring logic on the guidelines below: Scoring Guidelines: Page Metrics: Lower Node count, JS Heap usage, and Script Duration increase the score. High values or exceeding acceptable thresholds (if specified) decrease the score. Weight based on potential impact on performance. Network Statistics: Fewer and smaller requests, high success rate (% Success), and efficient caching usage increase the score. Unnecessary redirects, large transfer sizes, and a high number of failed requests decrease the score. Total Violations: A low number of violations increases the score. 'critical' and 'serious' severity violations should significantly decrease the score. The presence and severity of accessibility violations (label, missing-name, image-alt, etc.) play a critical role in scoring. Network Logs: Fast response times (Time), small sizes (Size), successful status codes (Status: 2xx, 3xx), and efficient methods (GET vs POST) increase the score. 4xx/5xx errors, slow responses, and unnecessarily large resources decrease the score. Highlight specific bottlenecks (e.g., slow API responses, large images). 4. Overall Assessment: Combine the findings and scores from all categories to evaluate the site's overall optimization status. Summarize the most critical and highest-impact issues. Provide an overall optimization score (0-100) and identify the top 2-3 main areas for improvement that should be addressed with priority. 5. Output Format: Strictly return the analysis results and scores in the JSON format specified below, using the exact English keys provided.

Categories and Data Points to Evaluate: 1. Page Metrics: Documents (Number of documents), Nodes (Number of DOM nodes): Indicator of DOM complexity, JS Heap Used (Size of JS Heap Used): Indicator of memory usage, Script Duration (Total script execution time): Indicator of JS processing time, main-thread blocking. 2. Network Statistics: Request Types and Counts (e.g., document, xhr, fetch, script, css, image etc.): Resource distribution, Total Transfers (Total transfer size): Page weight, Request Status (% Success, % Failed): Network reliability. 3. Total Violations: Critical Count (Number of critical violations), Found During Test Execution Count (Number found during test), Severity Breakdown (serious, critical count): Severity of issues, Top Issue Types (e.g., label, missing-name, image-alt, contrast): Common problem areas, especially accessibility. 4. Network Logs (Examples/Summary): URL: Requested address, Method: GET, POST etc., Status: 200, 404, 500 etc., Type: script, image, xhr etc., Size: Size of the request, Time: Request completion time. (Analysis can focus on the top N slowest, largest, or erroneous requests).

Result JSON Structure (English Keys as originally requested): { "PageMetrics": { "Score": "Value between 0-100", "Comment": "Detailed comment" }, "NetworkStatistics": { "Score": "Value between 0-100", "Comment": "Detailed comment" }, "TotalViolations": { "Score": "Value between 0-100", "Comment": "Detailed comment" }, "NetworkLogs": { "Score": "Value between 0-100", "Comment": "Detailed comment" }, "OverallOptimizationScore": "Value between 0-100", "OverallComment": "Overall assessment and improvement recommendations" }

Final Instruction: Strictly adhering to the role, task definition, guidelines, and output format above, evaluate the provided website analysis data and generate the results in JSON format using the specified English keys. If data is missing for any category, mention this in the comments and adjust the scoring based on the available data accordingly.
`



/**
 * GET /api/reports/summary
 * Tüm raporların özet bilgilerini getirir (test başlığı, başarı/başarısızlık oranı, süre, adım sayısı)
 * Auth koruması ile kullanıcı bilgisi alınır, opsiyonel olarak limit ve tarih filtresi destekler
 */
router.get('/summary', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const limit = parseInt(req.query.limit?.toString() || '100');
    const days = parseInt(req.query.days?.toString() || '0'); // Opsiyonel gün filtresi

    // Kullanıcı kimliği doğrulanmamışsa hata döndür
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı kimliği doğrulanamadı'
      });
    }

    // Tarih aralığını hesapla - Sadece days parametresi gönderilmişse filtrele
    let startDate: string | undefined;
    if (days > 0) {
      const date = new Date();
      date.setDate(date.getDate() - days);
      startDate = date.toISOString();
    }

    // Her zaman userId ile sorgulama yap
    const options = {
      userId, // Kullanıcı ID'sini her zaman dahil et
      limit,
      ...(startDate ? { startDate } : {}), // Sadece startDate varsa ekle
      sort: { createdAt: -1 } as any // en yeni oluşturma tarihinden en eskiye
    };

    const result = await getReportsSummary(options);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }



    res.json({
      success: true,
      data: {
        count: result.reports?.length || 0,
        reports: result.reports
      }
    });

  } catch (error: any) {
    logger.error(`API Error in GET /reports/summary: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});


/**
 * GET /api/reports/light
 * Kullanıcıya ait tüm raporları getirir, screenshot alanı hariç tutulur
 * Auth koruması ile kullanıcı bilgisi alınır
 */
router.get('/light', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const limit = parseInt(req.query.limit?.toString() || '100');

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı kimliği doğrulanamadı'
      });
    }



    // Tüm raporları çek - en son tarihli olanları al ve limitle
    const result = await getAllReports({
      userId,
      limit,
      // Tarih sıralaması ve projection
      sort: { createdAt: -1 } as any, // -1 en yeniden en eskiye
      projection: { steps: 0 } as any
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      count: result.reports?.length || 0,
      reports: result.reports
    });

  } catch (error: any) {
    logger.error(`API Error in GET /reports/light: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});


/**
 * GET /api/reports
 * Gets all reports
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit?.toString() || '100');
    const startDate = req.query.startDate?.toString();
    const endDate = req.query.endDate?.toString();
    const status = req.query.status?.toString();
    const folderId = req.query.folderId?.toString(); // Add folder ID filter

    // Convert query params to options
    const options: any = {
      limit,
      startDate,
      endDate,
      status,
      folderId // Pass folder ID to the service
    };

    // Get reports from service
    const result = await getAllReports(options);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      count: result.count || 0,
      total: result.count,
      reports: result.reports
    });
  } catch (error: any) {
    logger.error(`API Error in GET /reports: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/all
 * Tüm raporları steps alanı olmadan getirir, limit ve gün filtreleri destekler
 * Authentication ve authorization kontrolü ile
 */
router.get('/all', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const limit = parseInt(req.query.limit?.toString() || '100');
    const days = parseInt(req.query.days?.toString() || '0'); // Opsiyonel gün filtresi
    const userId = authReq.user?.id;
    const userCompanyId = authReq.user?.companyId;
    const userTeamId = authReq.user?.teamId;
    const accountType = authReq.user?.accountType;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Tarih aralığını hesapla
    let startDate: string | undefined;
    if (days > 0) {
      const date = new Date();
      date.setDate(date.getDate() - days);
      startDate = date.toISOString();
    }

    // Admin kullanıcıları tüm raporları görebilir, normal kullanıcılar sadece kendi şirket/takımlarının raporlarını
    const queryOptions: any = {
      limit,
      startDate
    };

    // Normal kullanıcılar için şirket/takım filtresi ekle
    if (accountType !== 'admin') {
      // Kullanıcının kendi raporları + aynı şirket ve takımdaki raporlar
      queryOptions.userId = userId;
      queryOptions.companyId = userCompanyId;
      queryOptions.teamId = userTeamId;
    }

    // Servisden raporları çek
    const result = await getAllReports(queryOptions);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      count: result.reports?.length || 0,
      reports: result.reports
    });
  } catch (error: any) {
    logger.error(`API Error in GET /reports/all: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/runs
 * Kullanıcıya ait run reports'ları getirir
 * İki aşamalı süreç kullanır:
 * 1. Önce kullanıcının teamId ve companyId'sine ait run ID'leri alınır
 * 2. Sonra bu ID'ler kullanılarak ilgili run_reports getirilir
 */
router.get('/runs', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const teamId = authReq.user?.teamId;
    const companyId = authReq.user?.companyId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı kimliği doğrulanamadı'
      });
    }

    // Paginasyon parametrelerini al
    const limit = parseInt(req.query.limit?.toString() || '100');
    const skip = parseInt(req.query.skip?.toString() || '0');

    // İki aşamalı süreç:
    // 1. Kullanıcının teamId ve companyId'sine ait run raporlarını getir
    const result = await getRunReportsByUserId(userId, limit, skip, teamId, companyId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Run raporları alınamadı'
      });
    }

    // Başarılı cevap
    return res.json({
      success: true,
      data: {
        reports: result.reports || [],
        total: result.total || 0
      }
    });

  } catch (error: any) {
    logger.error(`API Error in GET /reports/runs: ${error.message}`,
      { error: error.message },
      'reports-fetch-error');
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/scenario/:scenarioId
 * Belirli bir senaryoya ait tüm raporları listeler
 * Authentication ve authorization kontrolü ile
 */
router.get('/scenario/:scenarioId', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const { scenarioId } = req.params;
    const userId = authReq.user?.id;
    const userCompanyId = authReq.user?.companyId;
    const userTeamId = authReq.user?.teamId;
    const accountType = authReq.user?.accountType;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Servisden raporları çek
    const result = await getReportsByScenarioId(scenarioId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Authorization kontrolü - Admin kullanıcıları tüm raporları görebilir
    let filteredReports = result.reports || [];

    if (accountType !== 'admin') {
      // Normal kullanıcılar sadece kendi şirket ve takımlarının raporlarını görebilir
      filteredReports = filteredReports.filter((report: any) => {
        const reportCompanyId = report.companyId;
        const reportTeamId = report.teamId;
        const reportUserId = report.userId;

        // Rapor sahibi kontrolü veya aynı şirket/takım kontrolü
        const isReportOwner = reportUserId === userId;
        const isSameCompany = userCompanyId && reportCompanyId && userCompanyId === reportCompanyId;
        const isSameTeam = userTeamId && reportTeamId && userTeamId === reportTeamId;

        // Erişim kontrolü: Rapor sahibi VEYA (aynı şirket VE aynı takım)
        return isReportOwner || (isSameCompany && isSameTeam);
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      scenarioId,
      count: filteredReports.length,
      reports: filteredReports
    });
  } catch (error: any) {
    logger.error(`API Error in GET /reports/scenario/${req.params.scenarioId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/:reportId
 * ID'ye göre tek bir raporu getirir
 * Authentication ve authorization kontrolü ile
 */
router.get('/:reportId', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const { reportId } = req.params;
    const userId = authReq.user?.id;
    const userCompanyId = authReq.user?.companyId;
    const userTeamId = authReq.user?.teamId;
    const accountType = authReq.user?.accountType;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Servisden raporu çek
    const result = await getReportById(reportId);

    // Rapor bulunamadıysa 404 hatası döndür
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Rapor bulunamadı: ${reportId}`
      });
    }

    const report = result.report;

    // Authorization kontrolü - Admin kullanıcıları tüm raporları görebilir
    if (accountType !== 'admin') {
      // Normal kullanıcılar sadece kendi şirket ve takımlarının raporlarını görebilir
      const reportCompanyId = report.companyId;
      const reportTeamId = report.teamId;
      const reportUserId = report.userId;

      // Rapor sahibi kontrolü veya aynı şirket/takım kontrolü
      const isReportOwner = reportUserId === userId;
      const isSameCompany = userCompanyId && reportCompanyId && userCompanyId === reportCompanyId;
      const isSameTeam = userTeamId && reportTeamId && userTeamId === reportTeamId;

      // Erişim kontrolü: Rapor sahibi VEYA (aynı şirket VE aynı takım)
      if (!isReportOwner && !(isSameCompany && isSameTeam)) {
        logger.warn(`[SECURITY] User ${userId} (company: ${userCompanyId}, team: ${userTeamId}) attempted to access report ${reportId} (company: ${reportCompanyId}, team: ${reportTeamId}, owner: ${reportUserId})`);
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to access this report'
        });
      }
    }

    // Başarılı yanıt
    res.json({
      success: true,
      report: result.report
    });
  } catch (error: any) {
    logger.error(`API Error in GET /reports/${req.params.reportId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/:reportId/pdf
 * ID'ye göre raporu PDF olarak indirir
 * Authentication ve authorization kontrolü ile
 */
router.get('/:reportId/pdf', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const { reportId } = req.params;
    const userId = authReq.user?.id;
    const userCompanyId = authReq.user?.companyId;
    const userTeamId = authReq.user?.teamId;
    const accountType = authReq.user?.accountType;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Servisden raporu çek
    const result = await getReportById(reportId);

    // Rapor bulunamadıysa 404 hatası döndür
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Rapor bulunamadı: ${reportId}`
      });
    }

    const report = result.report;

    // Authorization kontrolü - Admin kullanıcıları tüm raporları görebilir
    if (accountType !== 'admin') {
      // Normal kullanıcılar sadece kendi şirket ve takımlarının raporlarını görebilir
      const reportCompanyId = report.companyId;
      const reportTeamId = report.teamId;
      const reportUserId = report.userId;

      // Rapor sahibi kontrolü veya aynı şirket/takım kontrolü
      const isReportOwner = reportUserId === userId;
      const isSameCompany = userCompanyId && reportCompanyId && userCompanyId === reportCompanyId;
      const isSameTeam = userTeamId && reportTeamId && userTeamId === reportTeamId;

      // Erişim kontrolü: Rapor sahibi VEYA (aynı şirket VE aynı takım)
      if (!isReportOwner && !(isSameCompany && isSameTeam)) {
        logger.warn(`[SECURITY] User ${userId} (company: ${userCompanyId}, team: ${userTeamId}) attempted to download PDF for report ${reportId} (company: ${reportCompanyId}, team: ${reportTeamId}, owner: ${reportUserId})`);
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to download this report'
        });
      }
    }

    // PDF oluştur
    const pdfBuffer = await createReportPdf(result.report);

    // PDF yanıtını ayarla
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="report-${reportId}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    // PDF'i gönder
    res.send(pdfBuffer);
  } catch (error: any) {
    logger.error(`API Error in GET /reports/${req.params.reportId}/pdf: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/reports
 * Yeni bir test raporu kaydeder
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const reportData = req.body;
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;

    // Gerekli alanları kontrol et
    if (!reportData || !reportData.scenarioId) {
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir rapor ve scenarioId zorunludur'
      });
    }

    // Kimliği doğrulanmış kullanıcının ID'sini rapora ekle
    if (userId) {
      reportData.userId = userId;

      // Kullanıcı adını almak için MongoDB'ye sorgu yap
      try {
        await ensureMongoDBConnection();
        if (db) {
          const usersCollection = db.collection('users');
          const user = await usersCollection.findOne({ id: userId });

          if (user && user.name) {
            reportData.executedUserName = user.name;
          }
        }
      } catch (userError: any) {
        // Sessizce devam et
      }
    }

    // Servisden raporu atomik olarak kaydet
    const result = await createTestReportAtomic(reportData, {
      source: 'API-report-routes',
      transactionId: `api-report-${reportData.id || Date.now()}-${Date.now()}`
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.status(201).json({
      success: true,
      reportId: result.reportId,
      message: 'Rapor başarıyla kaydedildi'
    });
  } catch (error: any) {
    logger.error(`API Error in POST /reports: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/reports/plugins/testrail/send/:reportId
 * Bir raporu TestRail'e gönderir
 */
router.post('/plugins/testrail/send/:reportId', async (req: Request, res: Response) => {
  try {
    const { reportId } = req.params;
    const { userId, testCaseId, runId } = req.body;



    if (!userId || !testCaseId) {
      return res.status(400).json({
        success: false,
        error: 'userId and testCaseId are required'
      });
    }

    // Raporu TestRail'e asenkron olarak gönder (eğer runId varsa, bu run için TestRail run ID'sini kullan)
    // Asenkron işlemi başlat
    sendReportToTestRail(reportId, userId, testCaseId, runId)
      .then(result => {
        // İşlem tamamlandı, sessizce devam et
      })
      .catch(error => {
        logger.error(`Error syncing report to TestRail: ${error.message}`);
      });

    // Başarılı yanıt dön, işlem arka planda devam edecek
    return res.status(200).json({
      success: true,
      message: "TestRail sync initiated in the background"
    });
  } catch (error: any) {
    logger.error(`API Error in POST /reports/plugins/testrail/send/${req.params.reportId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/plugins/testrail/status/:reportId
 * Bir raporun TestRail entegrasyon durumunu kontrol eder
 */
router.get('/plugins/testrail/status/:reportId', async (req: Request, res: Response) => {
  try {
    const { reportId } = req.params;



    // TestRail entegrasyon durumunu kontrol et
    const result = await getTestRailStatus(reportId);

    // Başarılı yanıt
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      reportId,
      testRailStatus: result.data
    });
  } catch (error: any) {
    logger.error(`API Error in GET /reports/plugins/testrail/status/${req.params.reportId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/reports/plugins/testrail/verify
 * TestRail bağlantı ayarlarını doğrular
 */
router.post('/plugins/testrail/verify', async (req: Request, res: Response) => {
  try {
    const settings = req.body;

    // Gerekli alanları kontrol et
    if (!settings || !settings.url || !settings.apiKey) {
      return res.status(400).json({
        success: false,
        error: 'URL ve API Key zorunludur'
      });
    }



    // TestRail bağlantısını doğrula
    const result = await verifyTestRailConnectionWithSettings(settings);

    // Başarılı yanıt
    res.json({
      success: result.success,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in POST /reports/plugins/testrail/verify: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/reports/analyze/:reportId
 * Rapor verilerini OpenAI ile analiz eder ve JSON formatında sonuç döner
 * Authentication ve authorization kontrolü ile
 */
router.post('/analyze/:reportId', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const { reportId } = req.params;
    const userId = authReq.user?.id;
    const userCompanyId = authReq.user?.companyId;
    const userTeamId = authReq.user?.teamId;
    const accountType = authReq.user?.accountType;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Raporu getir
    const reportResult = await getReportById(reportId);

    if (!reportResult.success || !reportResult.report) {
      return res.status(404).json({
        success: false,
        error: reportResult.message || `Rapor bulunamadı: ${reportId}`
      });
    }

    const report = reportResult.report;

    // Authorization kontrolü - Admin kullanıcıları tüm raporları analiz edebilir
    if (accountType !== 'admin') {
      // Normal kullanıcılar sadece kendi şirket ve takımlarının raporlarını analiz edebilir
      const reportCompanyId = report.companyId;
      const reportTeamId = report.teamId;
      const reportUserId = report.userId;

      // Rapor sahibi kontrolü veya aynı şirket/takım kontrolü
      const isReportOwner = reportUserId === userId;
      const isSameCompany = userCompanyId && reportCompanyId && userCompanyId === reportCompanyId;
      const isSameTeam = userTeamId && reportTeamId && userTeamId === reportTeamId;

      // Erişim kontrolü: Rapor sahibi VEYA (aynı şirket VE aynı takım)
      if (!isReportOwner && !(isSameCompany && isSameTeam)) {
        logger.warn(`[SECURITY] User ${userId} (company: ${userCompanyId}, team: ${userTeamId}) attempted to analyze report ${reportId} (company: ${reportCompanyId}, team: ${reportTeamId}, owner: ${reportUserId})`);
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to analyze this report'
        });
      }
    }

    // OpenAI Client İnstance oluştur - sadece bu method içinde ihtiyaç duyulduğunda
    const openai = new OpenAI({
      baseURL:'https://api.openai.com/v1',
      apiKey: '********************************************************************************************************************************************************************',
    });

    // Construct the data object needed for AI analysis
    // Prioritize data from enhancedMetrics if available
    const analysisData = {
      PageMetrics: report.enhancedMetrics?.pageMetrics || {},
      NetworkStatistics: {
        requests: report.enhancedMetrics?.networkData?.requests || { total: 0, successful: 0, failed: 0, byType: {} },
        transferred: report.enhancedMetrics?.networkData?.transferred || { total: 0, unit: 'Bytes' }
      },
      TotalViolations: {
        count: report.enhancedMetrics?.accessibilityData?.violations?.count || 0,
        bySeverity: report.enhancedMetrics?.accessibilityData?.violations?.bySeverity || {},
        byType: report.enhancedMetrics?.accessibilityData?.violations?.byType || {}
      },
      // Attempt to parse logs if they are a JSON string, otherwise use as is (or default to empty array)
      NetworkLogs: (() => {
        const logs = report.enhancedMetrics?.networkData?.logs;
        if (typeof logs === 'string') {
          try {
            return JSON.parse(logs);
          } catch (e) {
            logger.error(`Error parsing NetworkLogs JSON in report ${reportId}:`, e);
            return []; // Return empty array on parse error
          }
        } else if (Array.isArray(logs)) {
          return logs; // Already an array
        }
        return []; // Default to empty array if logs are missing or not string/array
      })()
    };



    // OpenAI API'ye istek at
    const completion = await openai.chat.completions.create({
      model: 'gpt-4.1-mini', // En güncel model kullan
      messages: [
        { role: "system", content: systemPromtForReport },
        {
          role: "user",
          content: JSON.stringify(analysisData, null, 2)
        }
      ],
      temperature: 0.7,
      response_format: { type: "json_object" }
    });

    const analysisResult = completion.choices[0].message.content;

    // Parse JSON response to ensure it's valid
    const jsonResponse = JSON.parse(analysisResult || "{}");

    // AI insight'ı MongoDB'ye kaydet
    await updateReportAIInsight(reportId, jsonResponse);

    // Başarılı yanıtı döndür
    res.json({
      success: true,
      data: {
        reportId,
        analysis: jsonResponse
      }
    });

  } catch (error: any) {
    logger.error(`API Error in POST /reports/analyze/${req.params.reportId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/reports/runs/:reportId
 * Run reportunu ve ilişkili senaryo raporlarını siler
 * Auth koruması ile kullanıcı bilgisi alınır
 */
router.delete('/runs/:reportId', async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const { reportId } = req.params;
    const { runId, executionId } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı kimliği doğrulanamadı'
      });
    }

    if (!reportId || !runId || !executionId) {
      return res.status(400).json({
        success: false,
        error: 'ReportId, RunId ve ExecutionId gereklidir'
      });
    }



    // Önce run report'u al ve kullanıcıya ait olduğunu doğrula
    const reportResult = await getRunReportById(reportId);

    if (!reportResult.success || !reportResult.report) {
      return res.status(404).json({
        success: false,
        error: 'Run report bulunamadı'
      });
    }

    // Run report'un sahiplik doğrulaması
    // Not: Bu örnek için kullanıcı sahipliği kontrolünü atlıyoruz, gerçek uygulamada bu kontrolü yapmalısın

    // Run report'taki executionId ve runId'yi al (önemli)
    const reportDetail = reportResult.report;
    const reportRunId = reportDetail?.runId || runId;
    const reportExecutionId = reportDetail?.executionId || executionId;

    // Run report ve ilişkili test reportları sil
    const deleteResult = await deleteRunReportAndTestReports(
      reportId,
      reportRunId,
      reportExecutionId
    );

    if (!deleteResult.success) {
      return res.status(500).json({
        success: false,
        error: deleteResult.message
      });
    }



    return res.json({
      success: true,
      message: `Run report ve ilişkili ${deleteResult.deletedTestReports} test raporu başarıyla silindi`,
      deletedTestReports: deleteResult.deletedTestReports
    });

  } catch (error: any) {
    logger.error(`API Error in DELETE /reports/runs/${req.params.reportId}: ${error.message}`,
      { reportId: req.params.reportId, error: error.message },
      'report-delete-error');
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/run/:reportId
 * Run report detaylarını ID'ye göre getirir
 * Authentication ve authorization kontrolü ile
 */
router.get('/runs/:reportId', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const { reportId } = req.params;
    const userId = authReq.user?.id;
    const userCompanyId = authReq.user?.companyId;
    const userTeamId = authReq.user?.teamId;
    const accountType = authReq.user?.accountType;

    if (!reportId) {
      return res.status(400).json({
        success: false,
        error: 'Report ID is required'
      });
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Önce run report'unu getir
    const reportResult = await getRunReportById(reportId);

    if (!reportResult.success || !reportResult.report) {
      return res.status(404).json({
        success: false,
        error: `Run report with ID ${reportId} not found`
      });
    }

    const report = reportResult.report;

    // Authorization kontrolü - Admin kullanıcıları tüm raporları görebilir
    if (accountType !== 'admin') {
      // Normal kullanıcılar sadece kendi şirket ve takımlarının raporlarını görebilir
      const reportCompanyId = report.companyId;
      const reportTeamId = report.teamId;
      const reportUserId = report.userId;

      // Rapor sahibi kontrolü veya aynı şirket/takım kontrolü
      const isReportOwner = reportUserId === userId;
      const isSameCompany = userCompanyId && reportCompanyId && userCompanyId === reportCompanyId;
      const isSameTeam = userTeamId && reportTeamId && userTeamId === reportTeamId;

      // Erişim kontrolü: Rapor sahibi VEYA (aynı şirket VE aynı takım)
      if (!isReportOwner && !(isSameCompany && isSameTeam)) {
        logger.warn(`[SECURITY] User ${userId} (company: ${userCompanyId}, team: ${userTeamId}) attempted to access run report ${reportId} (company: ${reportCompanyId}, team: ${reportTeamId}, owner: ${reportUserId})`);
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to access this run report'
        });
      }
    }

    return res.json({
      success: true,
      report: reportResult.report
    });

  } catch (error: any) {
    logger.error(`API Error in GET /reports/run/${req.params.reportId}: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/reports/run-detail/:reportId
 * Tek bir endpoint ile run report, run detayları ve senaryo raporlarını birlikte getirir
 * Authentication ve authorization kontrolü ile
 */
router.get('/run-detail/:reportId', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const { reportId } = req.params;
    const userId = authReq.user?.id;
    const userCompanyId = authReq.user?.companyId;
    const userTeamId = authReq.user?.teamId;
    const accountType = authReq.user?.accountType;

    if (!reportId) {
      return res.status(400).json({
        success: false,
        error: 'Report ID is required'
      });
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    await ensureMongoDBConnection();

    if (!db) {
      return res.status(500).json({
        success: false,
        error: "MongoDB not initialized"
      });
    }

    // Koleksiyonu al
    const runReportsCollection = db.collection('run_reports');

    // Aggregation pipeline oluştur
    const pipeline = [
      // İlgili run report'u bul
      { $match: { id: reportId } },

      // Run bilgilerini join et
      {
        $lookup: {
          from: "runs",
          localField: "runId",
          foreignField: "id",
          as: "runDetails"
        }
      },

      // Run bilgilerini düzleştir
      {
        $addFields: {
          runDetails: { $arrayElemAt: ["$runDetails", 0] }
        }
      },

      // Test raporlarını getir - Tüm senaryo raporlarını getirmek için iyileştirilmiş sorgu
      {
        $lookup: {
          from: "test_reports",
          let: { runId: "$runId", executionId: "$executionId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$runId", "$$runId"] },
                    { $eq: ["$executionId", "$$executionId"] }
                  ]
                }
              }
            },
            // Sadece gerekli alanları getir ve yaratılma tarihine göre sırala
            {
              $project: {
                _id: 0,
                id: 1,
                status: 1,
                scenarioId: 1,
                scenarioName: 1,
                title: 1,
                name: 1,
                duration: 1,
                summary: 1,
                startTime: 1,
                createdAt: 1,
                // Dağıtılmış test stratejisi için cihaz bilgilerini ekle
                environmentSettings: 1
              }
            },
            // Önce scenarioId'ye göre grupla, sonra tarih sıralaması yap
            // Bu, aynı senaryonun farklı cihazlardaki raporlarını doğru şekilde gruplamak için
            { $sort: { scenarioId: 1, createdAt: -1 } }
          ],
          as: "scenarioReports"
        }
      },

      // Sonuç formatını düzenle, sadece kullanılan alanları al
      {
        $project: {
          _id: 0,
          // Run report detayları - sadece UI'da kullanılan alanlar
          report: {
            id: "$id",
            runId: "$runId",
            executionId: "$executionId",
            status: "$status",
            createdAt: "$createdAt",
            startedAt: "$startedAt",
            completedAt: "$completedAt",
            testResults: "$testResults",
            userId: "$userId",
            teamId: "$teamId",
            companyId: "$companyId"
          },
          // Run detayları - sadece UI'da kullanılan alanlar
          run: {
            id: "$runDetails.id",
            name: "$runDetails.name",
            description: "$runDetails.description",
            status: "$runDetails.status",
            createdAt: "$runDetails.createdAt",
            completedAt: "$runDetails.completedAt",
            environment: "$runDetails.environment",
            reportSettings: "$runDetails.reportSettings"
          },
          // Senaryo raporları - sadece gerekli alanları içeren alt projeksiyonla
          // Dağıtılmış test stratejisi için cihaz bilgilerini de ekle
          scenarioReports: {
            $map: {
              input: "$scenarioReports",
              as: "report",
              in: {
                id: "$$report.id",
                createdAt: "$$report.createdAt",
                status: "$$report.status",
                scenarioId: "$$report.scenarioId",
                scenarioName: "$$report.scenarioName",
                startTime: "$$report.startTime",
                name: "$$report.name",
                duration: "$$report.duration",
                // Özet bilgileri summary alanından al
                summary: "$$report.summary",
                // EnvironmentSettings'i doğrudan ekliyoruz - frontend'de erişilebilir olması için
                environmentSettings: "$$report.environmentSettings",
                // Cihaz bilgilerini ekle - dağıtılmış test stratejisi için
                deviceInfo: {
                  $cond: {
                    if: { $ifNull: ["$$report.environmentSettings", false] },
                    then: {
                      $cond: {
                        if: { $eq: ["$$report.environmentSettings.platform", "android"] },
                        then: {
                          $cond: {
                            if: {
                              $and: [
                                { $isArray: "$$report.environmentSettings.sauceLabs.selectedDevices" },
                                { $gt: [{ $size: "$$report.environmentSettings.sauceLabs.selectedDevices" }, 0] }
                              ]
                            },
                            then: { $arrayElemAt: ["$$report.environmentSettings.sauceLabs.selectedDevices", 0] },
                            else: null
                          }
                        },
                        else: null
                      }
                    },
                    else: null
                  }
                }
              }
            }
          }
        }
      }
    ];

    const results = await runReportsCollection.aggregate(pipeline).toArray();

    if (!results.length) {
      return res.status(404).json({
        success: false,
        error: `Run report with ID ${reportId} not found`
      });
    }

    // Gerekirse bazı nesneler içindeki null değerleri temizle
    const data = results[0];

    // Authorization kontrolü - Admin kullanıcıları ve team adminleri tüm raporları görebilir
    const isAdmin = accountType === 'admin';
    const isTeamAdmin = authReq.user?.role === 'team_admin';

    if (!isAdmin && !isTeamAdmin) {
      // Normal kullanıcılar sadece kendi şirket ve takımlarının raporlarını görebilir
      const reportCompanyId = data.report?.companyId;
      const reportTeamId = data.report?.teamId;
      const reportUserId = data.report?.userId;

      // Rapor sahibi kontrolü veya aynı şirket/takım kontrolü
      const isReportOwner = reportUserId === userId;
      const isSameCompany = userCompanyId && reportCompanyId && userCompanyId === reportCompanyId;
      const isSameTeam = userTeamId && reportTeamId && userTeamId === reportTeamId;

      // Erişim kontrolü: Rapor sahibi VEYA (aynı şirket VE aynı takım)
      if (!isReportOwner && !(isSameCompany && isSameTeam)) {
        logger.warn(`[SECURITY] User ${userId} (company: ${userCompanyId}, team: ${userTeamId}, isTeamAdmin: ${isTeamAdmin}) attempted to access run detail ${reportId} (company: ${reportCompanyId}, team: ${reportTeamId}, owner: ${reportUserId})`);
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to access this run report detail'
        });
      }
    }

    return res.json({
      success: true,
      data: data
    });
  } catch (error: any) {
    logger.error(`API Error in GET /reports/run-detail/${req.params.reportId}: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/reports/runs/bulk
 * Birden fazla run reportunu ve ilişkili senaryo raporlarını toplu olarak siler
 * Auth koruması ile kullanıcı bilgisi alınır
 * Asenkron olarak çalışır - hemen yanıt döndürür ve silme işlemini arka planda devam ettirir
 */
router.post('/runs/bulk', async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const { reportIds } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı kimliği doğrulanamadı'
      });
    }

    if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'En az bir adet Report ID gereklidir'
      });
    }

    // Hemen başarılı yanıt döndür
    res.json({
      success: true,
      message: `${reportIds.length} raporun silinme işlemi başlatıldı`,
      status: 'processing',
      totalReports: reportIds.length
    });

    // Silme işlemini arka planda devam ettir
    deleteMultipleRunReports(reportIds)
      .then(deleteResult => {
        if (deleteResult.success) {

        } else {
          logger.error(`[API] Error in background bulk delete: ${deleteResult.message}`,
            { error: deleteResult.message },
            'bulk-report-delete-error');
        }
      })
      .catch(error => {
        logger.error(`[API] Unhandled error in background bulk delete: ${error.message}`,
          { error: error.message },
          'bulk-report-delete-error');
      });

  } catch (error: any) {
    logger.error(`API Error in POST /reports/runs/bulk: ${error.message}`,
      { error: error.message },
      'bulk-report-delete-error');
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/reports/runs/bulk
 * Birden fazla run reportunu ve ilişkili senaryo raporlarını toplu olarak siler
 * Auth koruması ile kullanıcı bilgisi alınır
 */
router.delete('/runs/bulk', async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const { reportIds } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı kimliği doğrulanamadı'
      });
    }

    if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'En az bir adet Report ID gereklidir'
      });
    }



    // Toplu rapor silme işlemi için servis fonksiyonunu çağır
    const deleteResult = await deleteMultipleRunReports(reportIds);

    if (!deleteResult.success) {
      return res.status(500).json({
        success: false,
        error: deleteResult.message
      });
    }



    return res.json({
      success: true,
      message: deleteResult.message,
      deletedRunReports: deleteResult.deletedRunReports,
      deletedTestReports: deleteResult.deletedTestReports,
      failedReportIds: deleteResult.failedReportIds
    });

  } catch (error: any) {
    logger.error(`API Error in DELETE /reports/runs/bulk: ${error.message}`,
      { error: error.message },
      'bulk-report-delete-error');
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/reports/:reportId/share
 * Rapor paylaşımını etkinleştirir
 */
router.post('/:reportId/share', authenticate as any, async (req: Request, res: Response) => {
  try {
    const { reportId } = req.params;
    const { expiresIn, password, allowComments } = req.body;

    if (!reportId) {
      return res.status(400).json({
        success: false,
        error: 'Report ID is required'
      });
    }



    // Rapor paylaşımını etkinleştir
    const result = await enableReportSharing(reportId, {
      expiresIn, // Gün cinsinden (örn: 7, 30, null=süresiz)
      password,  // Opsiyonel şifre
      allowComments // Yorum yapılabilir mi
    });

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Report not found: ${reportId}`
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      shareToken: result.token,
      shareUrl: `${process.env.PUBLIC_URL || 'https://dev.hirafi.ai'}/reports/share/${result.token}`
    });
  } catch (error: any) {
    logger.error(`API Error in POST /reports/${req.params.reportId}/share: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/reports/:reportId/share
 * Rapor paylaşımını devre dışı bırakır
 */
router.delete('/:reportId/share', authenticate as any, async (req: Request, res: Response) => {
  try {
    const { reportId } = req.params;

    if (!reportId) {
      return res.status(400).json({
        success: false,
        error: 'Report ID is required'
      });
    }



    // Rapor paylaşımını devre dışı bırak
    const result = await disableReportSharing(reportId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Report not found: ${reportId}`
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: 'Report sharing disabled successfully'
    });
  } catch (error: any) {
    logger.error(`API Error in DELETE /reports/${req.params.reportId}/share: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});



export default router;
