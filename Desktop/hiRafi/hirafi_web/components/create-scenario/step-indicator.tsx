"use client"

import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"
import { Check, FileText, Settings } from "lucide-react"
import { cn } from "@/lib/utils"

interface StepIndicatorProps {
  stepLabels: string[]
  stepIcons?: React.ReactNode[]
}

export function StepIndicator({
  stepLabels,
  stepIcons = [<FileText key="details" className="h-4 w-4" />, <Settings key="environment" className="h-4 w-4" />]
}: StepIndicatorProps) {
  const { formData, ui, setCurrentStep } = useUnifiedScenarioContext()
  const currentStep = ui.currentStep

  // Check if a step is completed
  const isStepCompleted = (stepIndex: number) => {
    // First step is completed if name is provided or AI details is enabled
    if (stepIndex === 0) {
      return formData.useAIForDetails || !!formData.name;
    }

    // Second step is completed if there are steps
    if (stepIndex === 1) {
      return formData.steps.length > 0;
    }

    // Third step (TestRail integration) is completed if there are TestRail cases selected
    if (stepIndex === 2) {
      return formData.testrailCases && formData.testrailCases.length > 0;
    }

    return false;
  }

  // Determine if a step can be navigated to directly
  const canNavigateToStep = (stepIndex: number) => {
    // First step is always navigable
    if (stepIndex === 0) return true

    // For second step, first step must be complete (name and at least one step)
    if (stepIndex === 1) {
      return formData.useAIForDetails || !!formData.name;
    }

    // For third step (TestRail integration), second step must be complete
    if (stepIndex === 2) {
      return formData.steps.length > 0;
    }

    // For fourth step (Preview), third step is always navigable if previous steps are complete
    if (stepIndex === 3) {
      return formData.steps.length > 0;
    }

    return false;
  }

  return (
    <div className="mb-8 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-800 p-6">
      <div className="flex items-center">
        {stepLabels.map((label, index) => (
          <div key={label} className="flex-1 relative">
            <div className="flex items-center">
              {/* Step Number */}
              <button
                type="button"
                onClick={() => {
                  if (canNavigateToStep(index)) {
                    setCurrentStep(index)
                  }
                }}
                disabled={!canNavigateToStep(index)}
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-300",
                  currentStep === index
                    ? "bg-indigo-600 text-white shadow-md shadow-indigo-200 dark:shadow-indigo-900/30"
                    : isStepCompleted(index)
                      ? "bg-green-500 text-white shadow-md shadow-green-200 dark:shadow-green-900/30"
                      : "bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400",
                  canNavigateToStep(index) && "cursor-pointer hover:opacity-90"
                )}
              >
                {isStepCompleted(index) && currentStep !== index ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </button>

              {/* Connector Line */}
              {index < stepLabels.length - 1 && (
                <div className="flex-1 mx-2">
                  <div
                    className={cn(
                      "h-1 rounded-full",
                      isStepCompleted(index)
                        ? "bg-green-500"
                        : "bg-gray-200 dark:bg-gray-700"
                    )}
                  />
                </div>
              )}
            </div>

            {/* Step Label */}
            <div className="mt-3 text-center">
              <div className="flex items-center justify-center gap-1.5 mb-1">
                {stepIcons && stepIcons[index] ? (
                  <span className={cn(
                    currentStep === index
                      ? "text-indigo-600 dark:text-indigo-400"
                      : isStepCompleted(index)
                        ? "text-green-500 dark:text-green-400"
                        : "text-gray-500 dark:text-gray-400"
                  )}>
                    {stepIcons[index]}
                  </span>
                ) : null}
                <span
                  className={cn(
                    "font-medium",
                    currentStep === index
                      ? "text-indigo-600 dark:text-indigo-400"
                      : isStepCompleted(index)
                        ? "text-green-500 dark:text-green-400"
                        : "text-gray-500 dark:text-gray-400"
                  )}
                >
                  {label}
                </span>
              </div>

              {/* Optional Description */}
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {index === 0 ? 'Basic info & steps' : 'Browser & report settings'}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}