/**
 * DEPRECATED: This component is no longer in use.
 * The Reporting tab has been removed from the scenario editor.
 * This file is kept for reference but can be deleted in the future.
 */

"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ExtendedScenarioFormData } from "@/lib/utils/scenario-form-utils"

interface ReportingTabProps {
  scenarioData: ExtendedScenarioFormData
  updateNestedField: (parentField: "environment" | "reporting", field: string, value: any) => void
}

export function ReportingTab({ scenarioData, updateNestedField }: ReportingTabProps) {
  return (
    <Card className="overflow-hidden border-0 shadow-md">
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
        <CardTitle>Browser Report Settings</CardTitle>
        <CardDescription>Configure how test reports are generated</CardDescription>
      </CardHeader>
      <CardContent className="p-6 space-y-8">
        {/* Report Format */}
        <div>
          <h3 className="text-lg font-medium mb-4">Report Format</h3>
          <RadioGroup
            value={scenarioData.reporting.format}
            onValueChange={(value: "HTML" | "PDF" | "JSON") =>
              updateNestedField("reporting", "format", value)
            }
            className="grid grid-cols-3 gap-4"
          >
            <div>
              <RadioGroupItem value="HTML" id="html" className="peer sr-only" />
              <Label
                htmlFor="html"
                className="flex flex-col items-center justify-between rounded-md border-2 border-gray-200 dark:border-gray-700 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 peer-data-[state=checked]:border-indigo-600 [&:has([data-state=checked])]:border-indigo-600"
              >
                <div className="mb-2 rounded-full bg-gradient-to-r from-indigo-500 to-blue-500 w-10 h-10 flex items-center justify-center text-white font-bold">
                  H
                </div>
                <span className="text-sm font-medium">HTML</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">Interactive</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem value="PDF" id="pdf" className="peer sr-only" />
              <Label
                htmlFor="pdf"
                className="flex flex-col items-center justify-between rounded-md border-2 border-gray-200 dark:border-gray-700 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 peer-data-[state=checked]:border-indigo-600 [&:has([data-state=checked])]:border-indigo-600"
              >
                <div className="mb-2 rounded-full bg-gradient-to-r from-red-500 to-pink-500 w-10 h-10 flex items-center justify-center text-white font-bold">
                  P
                </div>
                <span className="text-sm font-medium">PDF</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">Document</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem value="JSON" id="json" className="peer sr-only" />
              <Label
                htmlFor="json"
                className="flex flex-col items-center justify-between rounded-md border-2 border-gray-200 dark:border-gray-700 p-4 hover:bg-gray-50 dark:hover:bg-gray-800 peer-data-[state=checked]:border-indigo-600 [&:has([data-state=checked])]:border-indigo-600"
              >
                <div className="mb-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 w-10 h-10 flex items-center justify-center text-white font-bold">
                  J
                </div>
                <span className="text-sm font-medium">JSON</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">Data</span>
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* Data Collection */}
        <div>
          <h3 className="text-lg font-medium mb-4">Data Collection</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Page Metrics</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Collect performance metrics for each page
                </p>
              </div>
              <div className="relative">
                <label className="inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer"
                    checked={scenarioData.reporting.pageMetrics}
                    onChange={(e) => updateNestedField("reporting", "pageMetrics", e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Network Data</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Record network requests and responses
                </p>
              </div>
              <div className="relative">
                <label className="inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer"
                    checked={scenarioData.reporting.networkData}
                    onChange={(e) => updateNestedField("reporting", "networkData", e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Tracing Data</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Include detailed execution traces
                </p>
              </div>
              <div className="relative">
                <label className="inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer"
                    checked={scenarioData.reporting.tracingData}
                    onChange={(e) => updateNestedField("reporting", "tracingData", e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Accessibility Data</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Test for accessibility issues
                </p>
              </div>
              <div className="relative">
                <label className="inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer"
                    checked={scenarioData.reporting.accessibilityData}
                    onChange={(e) => updateNestedField("reporting", "accessibilityData", e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Media Capture */}
        <div>
          <h3 className="text-lg font-medium mb-4">Media Capture</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Take Screenshots</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Capture screenshots at each test step
                </p>
              </div>
              <div className="relative">
                <label className="inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer"
                    checked={scenarioData.reporting.takeScreenshots}
                    onChange={(e) => updateNestedField("reporting", "takeScreenshots", e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Record Videos</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Record video of the entire test
                </p>
              </div>
              <div className="relative">
                <label className="inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer"
                    checked={scenarioData.reporting.takeVideos}
                    onChange={(e) => updateNestedField("reporting", "takeVideos", e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 