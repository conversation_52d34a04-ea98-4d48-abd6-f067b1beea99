/**
 * Node Manager Service Registry Interface
 * 
 * Defines the contract for the service registry that manages
 * all node manager services with dependency injection.
 */

import { INodeManager } from './node-manager.interface.js';
import { INodeIdentityService } from './node-identity-service.interface.js';
import { INodeStateService } from './node-state-service.interface.js';
import { IConnectionService } from './connection-service.interface.js';
import { IEventService } from './event-service.interface.js';
import { IPeriodicTaskService } from './periodic-task-service.interface.js';

/**
 * Service Registry Configuration
 */
export interface ServiceRegistryConfig {
  nodeId?: string;
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
}

/**
 * Node Manager Service Registry Interface
 * 
 * Manages service registration and provides centralized access
 * to all node manager services. Replaces singleton patterns with
 * proper dependency injection and service discovery.
 */
export interface INodeManagerServiceRegistry {
  /**
   * Initialize the service registry
   * @returns Promise that resolves when initialized
   */
  initialize(): Promise<void>;

  /**
   * Register an external service
   * @param name Service name
   * @param instance Service instance
   */
  registerExternalService<T>(name: string, instance: T): void;

  /**
   * Get the node manager
   * @returns Promise resolving to the node manager
   */
  getNodeManager(): Promise<INodeManager>;

  /**
   * Get the node identity service
   * @returns Promise resolving to the identity service
   */
  getNodeIdentityService(): Promise<INodeIdentityService>;

  /**
   * Get the node state service
   * @returns Promise resolving to the state service
   */
  getNodeStateService(): Promise<INodeStateService>;

  /**
   * Get the connection service
   * @returns Promise resolving to the connection service
   */
  getConnectionService(): Promise<IConnectionService>;

  /**
   * Get the event service
   * @returns Promise resolving to the event service
   */
  getEventService(): Promise<IEventService>;

  /**
   * Get the periodic task service
   * @returns Promise resolving to the periodic task service
   */
  getPeriodicTaskService(): Promise<IPeriodicTaskService>;

  /**
   * Dispose all services
   * @returns Promise that resolves when disposed
   */
  dispose(): Promise<void>;

  /**
   * Check if registry is initialized
   * @returns True if initialized
   */
  isInitialized(): boolean;
}
