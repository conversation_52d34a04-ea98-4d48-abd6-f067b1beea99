// Tüm içerikleri tek bir div içine alarak JSX ifadelerinin tek parent altında olmasını sağla
<div className="edit-scenario-content">
  {/* Tab içeriklerine animasyon ekle */}
  {activeTab === "details" && (
    <div className="animate-tab-transition tab-content-details">
      <DetailsTab
        scenarioData={scenarioData}
        setScenarioData={setScenarioData}
        folders={folders}
        foldersLoading={foldersLoading}
        getFolderColorClass={getFolderColorClass}
        newFolderOpen={newFolderOpen}
        setNewFolderOpen={setNewFolderOpen}
        newFolderName={newFolderName}
        setNewFolderName={setNewFolderName}
        newFolderColor={newFolderColor}
        setNewFolderColor={setNewFolderColor}
        isCreatingFolder={isCreatingFolder}
        handleCreateFolder={handleCreateFolder}
        handleAddTag={handleAddTag}
        handleRemoveTag={handleRemoveTag}
        handleDragEnd={handleDragEnd}
        handleAddStep={handleAddStep}
        handleRemoveStep={handleRemoveStep}
        handleUpdateStep={handleUpdateStep}
        handleStepTypeChange={handleStepTypeChange}
        getStepTypeIcon={getStepTypeIcon}
        getStepTypeLabel={getStepTypeLabel}
        getStepTypeColor={getStepTypeColor}
      />
    </div>
  )}

  {activeTab === "steps" && (
    <div className="animate-tab-transition tab-content-steps">
      <StepsTab
        scenarioData={scenarioData}
        handleDragEnd={handleDragEnd}
        handleAddStep={handleAddStep}
        handleRemoveStep={handleRemoveStep}
        handleUpdateStep={handleUpdateStep}
        handleStepTypeChange={handleStepTypeChange}
        getStepTypeIcon={getStepTypeIcon}
        getStepTypeLabel={getStepTypeLabel}
        getStepTypeColor={getStepTypeColor}
      />
    </div>
  )}

  {activeTab === "environment" && (
    <div className="animate-tab-transition tab-content-environment">
      <EnvironmentTab
        scenarioData={scenarioData}
        updateNestedField={updateNestedField}
        updateProxyField={updateProxyField}
      />
    </div>
  )}

  {activeTab === "reporting" && (
    <div className="animate-tab-transition tab-content-reporting">
      <ReportingTab
        scenarioData={scenarioData}
        updateNestedField={updateNestedField}
      />
    </div>
  )}



  {/* Animasyonlar arası geçişler için gölge efekti */}
  <div className="tab-transition-overlay"></div>

  {/* Global stil tanımlaması */}
  <style jsx global>{`
    /* Ana tab içeriği animasyonu */
    .animate-tab-transition {
      animation: tabFadeIn 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
      will-change: opacity, transform;
      position: relative;
      transform-origin: center top;
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
      transition: box-shadow 0.5s ease;
    }
    
    /* Tab içeriklerine özel varyant renkler */
    .tab-content-details {
      --tab-accent: rgba(59, 130, 246, 0.08);
    }
    
    .tab-content-steps {
      --tab-accent: rgba(124, 58, 237, 0.08);
    }
    
    .tab-content-environment {
      --tab-accent: rgba(16, 185, 129, 0.08);
    }
    
    .tab-content-reporting {
      --tab-accent: rgba(245, 158, 11, 0.08);
    }
    
    .tab-content-testmanagement {
      --tab-accent: rgba(79, 70, 229, 0.08);
    }
    
    /* Ana tab animasyonu */
    @keyframes tabFadeIn {
      0% {
        opacity: 0;
        transform: translateY(16px) scale(0.98);
        box-shadow: 0 0 0 0 var(--tab-accent);
      }
      30% {
        opacity: 1;
        transform: translateY(8px) scale(0.99);
      }
      100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        box-shadow: 0 0 30px 5px var(--tab-accent);
      }
    }
    
    /* Başlangıçta öğeleri görünür olacak şekilde kademeli animasyon */
    .animate-tab-transition > div > * {
      animation: elementFadeIn 0.5s ease forwards;
      opacity: 0;
    }
    
    .animate-tab-transition > div > *:nth-child(1) { animation-delay: 0.1s; }
    .animate-tab-transition > div > *:nth-child(2) { animation-delay: 0.15s; }
    .animate-tab-transition > div > *:nth-child(3) { animation-delay: 0.2s; }
    .animate-tab-transition > div > *:nth-child(4) { animation-delay: 0.25s; }
    .animate-tab-transition > div > *:nth-child(5) { animation-delay: 0.3s; }
    
    @keyframes elementFadeIn {
      from { 
        opacity: 0;
        transform: translateY(8px);
      }
      to { 
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    /* Tab içeriği görünür olduğunda üzerinde gezinen parıltı efekti */
    .animate-tab-transition::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      right: -50%;
      bottom: -50%;
      background: linear-gradient(
        to right,
        transparent,
        rgba(255, 255, 255, 0.05),
        transparent
      );
      transform: rotate(45deg) translate(0, -100%);
      animation: tabShine 3s ease-in-out infinite;
      z-index: -1;
    }
    
    @keyframes tabShine {
      0% { transform: rotate(45deg) translate(0, -100%); }
      50% { transform: rotate(45deg) translate(0, 100%); }
      100% { transform: rotate(45deg) translate(0, 100%); }
    }
    
    /* Tab geçişlerinde sayfanın geri kalanına hafif sis efekti */
    .tab-transition-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(
        circle at center,
        transparent 95%,
        rgba(59, 130, 246, 0.03) 100%
      );
      pointer-events: none;
      z-index: -1;
      animation: overlayPulse 2s ease-in-out infinite;
    }
    
    @keyframes overlayPulse {
      0%, 100% { opacity: 0.3; }
      50% { opacity: 0.8; }
    }
  `}</style>
</div> 