/**
 * Unified Service Registration Interface
 * 
 * PHASE 4 FIX: Consolidates 4 duplicate ServiceRegistration interfaces into a single source of truth
 * Replaces interfaces in:
 * - connector-service-registry.interface.ts
 * - service-types.ts
 * - base-service.interface.ts
 * - dependency-container.ts
 */

/**
 * Service lifecycle enumeration
 */
export enum ServiceLifecycle {
  SINGLETON = 'singleton',
  TRANSIENT = 'transient',
  SCOPED = 'scoped'
}

/**
 * Unified Service Registration Interface
 * Single source of truth for all service registration definitions
 */
export interface IServiceRegistration<T = any> {
  name: string;
  factory: (...args: any[]) => Promise<T> | T;
  dependencies: string[];
  lifecycle: ServiceLifecycle;
  instance?: T;
  initialized: boolean;
  scope?: string;
}

/**
 * Legacy aliases for backward compatibility
 * These will be deprecated in future versions
 */
export type ServiceRegistration<T = any> = IServiceRegistration<T>;

/**
 * Service registration metadata for enhanced tracking
 */
export interface IServiceRegistrationMetadata {
  registeredAt: Date;
  registeredBy: string;
  version?: string;
  description?: string;
  tags?: string[];
}

/**
 * Enhanced service registration with metadata
 */
export interface IEnhancedServiceRegistration<T = any> extends IServiceRegistration<T> {
  metadata?: IServiceRegistrationMetadata;
}

/**
 * Service registration options for flexible configuration
 */
export interface IServiceRegistrationOptions {
  lifecycle?: ServiceLifecycle;
  dependencies?: string[];
  scope?: string;
  metadata?: IServiceRegistrationMetadata;
  enableLogging?: boolean;
  enableValidation?: boolean;
}

/**
 * Service factory function type
 */
export type ServiceFactory<T = any> = (...args: any[]) => Promise<T> | T;

/**
 * Service resolver function type
 */
export type ServiceResolver<T = any> = (name: string, scope?: string) => Promise<T>;

/**
 * Service registration builder for fluent API
 */
export interface IServiceRegistrationBuilder<T = any> {
  withLifecycle(lifecycle: ServiceLifecycle): IServiceRegistrationBuilder<T>;
  withDependencies(dependencies: string[]): IServiceRegistrationBuilder<T>;
  withScope(scope: string): IServiceRegistrationBuilder<T>;
  withMetadata(metadata: IServiceRegistrationMetadata): IServiceRegistrationBuilder<T>;
  build(): IServiceRegistration<T>;
} 