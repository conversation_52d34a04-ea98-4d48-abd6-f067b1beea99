"use client"

import { useState, useEffect, useRef } from "react"
import { useRunForm } from "../run-context"
import { useAIModels } from "@/hooks/useAIModels"
import { toast } from "@/lib/utils/toast-utils"
import axios from "axios"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Switch
} from "@/components/ui/switch"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import {
  Monitor,
  Smartphone,
  Tablet,
  Globe,
  Wifi,
  Bot,
  Network,
  RefreshCw,
  Database
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export function WebEnvironmentSettings() {
  const {
    environmentSettings,
    updateEnvironmentSettings,
    errors,
    testDataEnvironments,
    selectedTestDataEnvironment,
    isLoadingTestDataEnvironments,
    loadTestDataEnvironments,
    selectTestDataEnvironment
  } = useRunForm()

  const { models, isLoading } = useAIModels()
  const [isTestingProxy, setIsTestingProxy] = useState(false)

  // Set default AI model when models are loaded
  useEffect(() => {
    if (!isLoading && models.length > 0 && (!environmentSettings.aiModel || environmentSettings.aiModel === "default")) {
      console.log("Setting default AI model for web platform:", models[0].id, models[0].name)
      updateEnvironmentSettings({
        aiModel: models[0].id,
        aiModelName: models[0].name
      })
    }
  }, [isLoading, models, environmentSettings.aiModel, updateEnvironmentSettings])

  // Load test data environments when component mounts
  useEffect(() => {
    if (testDataEnvironments.length === 0 && !isLoadingTestDataEnvironments) {
      loadTestDataEnvironments();
    }
  }, []) // Sadece component mount olduğunda çalışsın

  // Browser selection
  const handleBrowserChange = (browser: string) => {
    updateEnvironmentSettings({ browser })
  }

  // Device type selection
  const [selectedDeviceType, setSelectedDeviceType] = useState<string>(() => {
    const viewport = environmentSettings.viewport
    if (!viewport) return "desktop"

    if (viewport.width <= 480) return "mobile"
    if (viewport.width <= 1024) return "tablet"
    return "desktop"
  })

  const [selectedDeviceIndex, setSelectedDeviceIndex] = useState<number | null>(null)

  // Default user agent
  const defaultUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

  // Device presets
  const mobileDevices = [
    { name: "iPhone 12 Pro", width: 390, height: 844, userAgent: defaultUserAgent },
    { name: "iPhone SE", width: 375, height: 667, userAgent: defaultUserAgent },
    { name: "Pixel 5", width: 393, height: 851, userAgent: defaultUserAgent },
    { name: "Galaxy S20", width: 360, height: 800, userAgent: defaultUserAgent },
  ]

  const tabletDevices = [
    { name: "iPad Air", width: 820, height: 1180, userAgent: defaultUserAgent },
    { name: "iPad Mini", width: 768, height: 1024, userAgent: defaultUserAgent },
    { name: "Galaxy Tab S7", width: 753, height: 1193, userAgent: defaultUserAgent },
  ]

  const desktopPresets = [
    { name: "Laptop (1366x768)", width: 1366, height: 768 },
    { name: "Desktop (1920x1080)", width: 1920, height: 1080 },
    { name: "Desktop (2560x1440)", width: 2560, height: 1440 },
    { name: "Custom", width: environmentSettings.viewport.width, height: environmentSettings.viewport.height }
  ]

  // Cihaz seçimi fonksiyonları
  const handleDeviceTypeChange = (type: string) => {
    setSelectedDeviceType(type)
    setSelectedDeviceIndex(null)

    // Her tip değişiminde varsayılan ekran boyutlarını ayarla
    if (type === "mobile") {
      updateEnvironmentSettings({
        viewport: { width: 390, height: 844 },
        device: "iPhone 12 Pro",
        userAgent: defaultUserAgent
      })
    } else if (type === "tablet") {
      updateEnvironmentSettings({
        viewport: { width: 820, height: 1180 },
        device: "iPad Air",
        userAgent: defaultUserAgent
      })
    } else {
      updateEnvironmentSettings({
        viewport: { width: 1920, height: 1080 },
        device: "Desktop (1920x1080)",
        userAgent: defaultUserAgent
      })
    }
  }

  const handleDeviceSelect = (index: number) => {
    setSelectedDeviceIndex(index)

    let device

    if (selectedDeviceType === "mobile") {
      device = mobileDevices[index]
      updateEnvironmentSettings({
        viewport: { width: device.width, height: device.height },
        device: device.name,
        userAgent: defaultUserAgent
      })
    } else if (selectedDeviceType === "tablet") {
      device = tabletDevices[index]
      updateEnvironmentSettings({
        viewport: { width: device.width, height: device.height },
        device: device.name,
        userAgent: defaultUserAgent
      })
    } else {
      device = desktopPresets[index]
      updateEnvironmentSettings({
        viewport: { width: device.width, height: device.height },
        device: device.name,
        userAgent: defaultUserAgent
      })
    }
  }

  // Custom viewport size
  const handleViewportWidthChange = (value: number) => {
    updateEnvironmentSettings({
      viewport: { ...environmentSettings.viewport, width: value },
      device: "Custom"
    })
    setSelectedDeviceIndex(3) // Custom option
  }

  const handleViewportHeightChange = (value: number) => {
    updateEnvironmentSettings({
      viewport: { ...environmentSettings.viewport, height: value },
      device: "Custom"
    })
    setSelectedDeviceIndex(3) // Custom option
  }

  // Proxy settings
  const handleProxyToggle = (enabled: boolean) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, enabled }
    })
  }

  const handleProxyTypeChange = (type: string) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, type }
    })
  }

  const handleProxyHostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, host: e.target.value }
    })
  }

  const handleProxyPortChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const port = parseInt(e.target.value) || 0
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, port }
    })
  }

  const handleProxyUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, username: e.target.value }
    })
  }

  const handleProxyPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, password: e.target.value }
    })
  }

  // Test proxy connection
  const testProxyConnection = async () => {
    setIsTestingProxy(true)

    try {
      const response = await axios.post('/api/proxy/test', {
        proxy: environmentSettings.proxy
      }, {
        timeout: 10000 // 10 second timeout
      })

      if (response.data.success) {
        toast.success('Proxy connection successful', {
          description: 'The proxy connection was tested successfully.'
        })
      } else {
        toast.error('Proxy connection failed', {
          description: response.data.message || 'Could not connect to the proxy server.'
        })
      }
    } catch (error) {
      toast.error('Proxy connection failed', {
        description: 'Could not connect to the proxy server. Please check your settings.'
      })
      console.error('Proxy test error:', error)
    } finally {
      setIsTestingProxy(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* AI Model */}
      <div className="grid gap-2">
        <Label htmlFor="aiModel">AI Model <span className="text-red-500">*</span></Label>
        <Select
          value={environmentSettings.aiModel || "default"}
          onValueChange={(value: string) => {
            // Find the selected model to get its name
            const selectedModel = models.find(model => model.id === value);

            // Update both the model ID and name
            updateEnvironmentSettings({
              aiModel: value,
              aiModelName: selectedModel ? selectedModel.name : undefined
            });
          }}
        >
          <SelectTrigger id="aiModel">
            <SelectValue placeholder="Select AI model" />
          </SelectTrigger>
          <SelectContent>
            {isLoading ? (
              <SelectItem value="loading" disabled>Loading models...</SelectItem>
            ) : (
              models.map(model => (
                <SelectItem key={model.id} value={model.id} disabled={!model.isActive}>
                  {model.name} {!model.isActive && '(Devre Dışı)'}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
        {errors?.aiModel && (
          <p className="text-sm text-red-500">{errors.aiModel}</p>
        )}
      </div>

      {/* Test Data Environment */}
      <div className="grid gap-2">
        <Label htmlFor="testDataEnvironment">Test Data Environment</Label>
        <Select
          value={selectedTestDataEnvironment?.id || "none"}
          onValueChange={(value: string) => {
            if (value === "none") {
              selectTestDataEnvironment(null);
            } else {
              const environment = testDataEnvironments.find(env => env.id === value);
              selectTestDataEnvironment(environment || null);
            }
          }}
        >
          <SelectTrigger id="testDataEnvironment">
            <SelectValue placeholder="Select test data environment (optional)" />
          </SelectTrigger>
          <SelectContent>
            {isLoadingTestDataEnvironments ? (
              <SelectItem value="loading" disabled>Loading environments...</SelectItem>
            ) : (
              <>
                <SelectItem value="none">No environment selected</SelectItem>
                {testDataEnvironments.map(env => (
                  <SelectItem key={env.id} value={env.id}>
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      <span>{env.name}</span>
                      {env.description && (
                        <span className="text-xs text-gray-500">- {env.description}</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </>
            )}
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-500">
          Select a test data environment to use specific test data values during test execution.
        </p>
      </div>

      {/* Browser Selection */}
      <div className="grid gap-2">
        <Label htmlFor="browser">Browser</Label>
        <Select
          value={environmentSettings.browser || "chrome"}
          onValueChange={handleBrowserChange}
        >
          <SelectTrigger id="browser">
            <SelectValue placeholder="Select browser" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="chrome">Chrome</SelectItem>
            <SelectItem value="firefox">Firefox</SelectItem>
            <SelectItem value="safari">Safari</SelectItem>
          </SelectContent>
        </Select>
      </div>



      {/* Device Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>Device Type</Label>
          <div className="flex items-center">
            <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800">
              <span className="flex items-center gap-1">
                <Monitor className="h-3.5 w-3.5" />
                Viewport: {environmentSettings.viewport.width} × {environmentSettings.viewport.height}
              </span>
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div
            className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 cursor-pointer hover:shadow-md transform hover:translate-y-[-2px] transition-all duration-150 ease-out ${
              selectedDeviceType === "desktop"
                ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20"
                : "border-gray-200 dark:border-gray-800"
            }`}
            onClick={() => handleDeviceTypeChange("desktop")}
            style={{ willChange: "transform" }}
          >
            <Monitor className="h-8 w-8 mb-2 text-indigo-500" />
            <span className="text-sm font-medium">Desktop</span>
          </div>

          <div
            className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 cursor-pointer hover:shadow-md transform hover:translate-y-[-2px] transition-all duration-150 ease-out ${
              selectedDeviceType === "tablet"
                ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                : "border-gray-200 dark:border-gray-800"
            }`}
            onClick={() => handleDeviceTypeChange("tablet")}
            style={{ willChange: "transform" }}
          >
            <Tablet className="h-8 w-8 mb-2 text-purple-500" />
            <span className="text-sm font-medium">Tablet</span>
          </div>

          <div
            className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 cursor-pointer hover:shadow-md transform hover:translate-y-[-2px] transition-all duration-150 ease-out ${
              selectedDeviceType === "mobile"
                ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                : "border-gray-200 dark:border-gray-800"
            }`}
            onClick={() => handleDeviceTypeChange("mobile")}
            style={{ willChange: "transform" }}
          >
            <Smartphone className="h-8 w-8 mb-2 text-green-500" />
            <span className="text-sm font-medium">Mobile</span>
          </div>
        </div>

        {/* Device Resolution Options */}
        <div className="mt-4 border rounded-md p-3 bg-gray-50 dark:bg-gray-900/30">
          <div className="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Select Resolution</div>

          {selectedDeviceType === "desktop" && (
            <div className="grid grid-cols-2 gap-2">
              {desktopPresets.map((preset, index) => (
                <div
                  key={index}
                  onClick={() => handleDeviceSelect(index)}
                  className={`p-2 rounded border cursor-pointer transition-colors ${
                    selectedDeviceIndex === index
                      ? 'bg-indigo-100 dark:bg-indigo-900/30 border-indigo-300 dark:border-indigo-700'
                      : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{preset.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {preset.width}×{preset.height}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedDeviceType === "tablet" && (
            <div className="grid grid-cols-2 gap-2">
              {tabletDevices.map((device, index) => (
                <div
                  key={index}
                  onClick={() => handleDeviceSelect(index)}
                  className={`p-2 rounded border cursor-pointer transition-colors ${
                    selectedDeviceIndex === index
                      ? 'bg-purple-100 dark:bg-purple-900/30 border-purple-300 dark:border-purple-700'
                      : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{device.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {device.width}×{device.height}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedDeviceType === "mobile" && (
            <div className="grid grid-cols-2 gap-2">
              {mobileDevices.map((device, index) => (
                <div
                  key={index}
                  onClick={() => handleDeviceSelect(index)}
                  className={`p-2 rounded border cursor-pointer transition-colors ${
                    selectedDeviceIndex === index
                      ? 'bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-700'
                      : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/50'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-sm">{device.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {device.width}×{device.height}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Custom viewport controls - only show for desktop or when custom is selected */}
          {(selectedDeviceType === "desktop" && selectedDeviceIndex === 3) && (
            <div className="mt-3 space-y-3 pt-3 border-t">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <Label htmlFor="viewport-width" className="text-xs">Width</Label>
                  <span className="text-xs text-gray-500">{environmentSettings.viewport.width}px</span>
                </div>
                <Slider
                  id="viewport-width"
                  min={800}
                  max={3840}
                  step={1}
                  value={[environmentSettings.viewport.width]}
                  onValueChange={(values) => handleViewportWidthChange(values[0])}
                  className="py-1"
                />
              </div>

              <div className="space-y-1">
                <div className="flex justify-between">
                  <Label htmlFor="viewport-height" className="text-xs">Height</Label>
                  <span className="text-xs text-gray-500">{environmentSettings.viewport.height}px</span>
                </div>
                <Slider
                  id="viewport-height"
                  min={600}
                  max={2160}
                  step={1}
                  value={[environmentSettings.viewport.height]}
                  onValueChange={(values) => handleViewportHeightChange(values[0])}
                  className="py-1"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Proxy Settings */}
      <Accordion type="single" collapsible className="border rounded-md">
        <AccordionItem value="proxy">
          <AccordionTrigger className="px-4 py-2 hover:no-underline">
            <div className="flex items-center">
              <Network className="h-5 w-5 mr-2 text-blue-500" />
              <span>Proxy Configuration</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 pt-2 space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="proxy-enabled" className="cursor-pointer">Enable Proxy</Label>
              <Switch
                id="proxy-enabled"
                checked={environmentSettings.proxy?.enabled || false}
                onCheckedChange={handleProxyToggle}
              />
            </div>

            {environmentSettings.proxy?.enabled && (
              <div className="space-y-4 mt-2">
                <div className="grid gap-2">
                  <Label htmlFor="proxy-type">Proxy Type</Label>
                  <Select
                    value={environmentSettings.proxy?.type || "HTTP"}
                    onValueChange={handleProxyTypeChange}
                  >
                    <SelectTrigger id="proxy-type">
                      <SelectValue placeholder="Select proxy type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HTTP">HTTP</SelectItem>
                      <SelectItem value="HTTPS">HTTPS</SelectItem>
                      <SelectItem value="SOCKS4">SOCKS4</SelectItem>
                      <SelectItem value="SOCKS5">SOCKS5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="proxy-host">Host</Label>
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-gray-500" />
                      <Input
                        id="proxy-host"
                        placeholder="proxy.example.com"
                        value={environmentSettings.proxy?.host || ""}
                        onChange={handleProxyHostChange}
                      />
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="proxy-port">Port</Label>
                    <Input
                      id="proxy-port"
                      type="number"
                      placeholder="8080"
                      value={environmentSettings.proxy?.port || ""}
                      onChange={handleProxyPortChange}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="proxy-username">Username (Optional)</Label>
                    <Input
                      id="proxy-username"
                      placeholder="Username"
                      value={environmentSettings.proxy?.username || ""}
                      onChange={handleProxyUsernameChange}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="proxy-password">Password (Optional)</Label>
                    <Input
                      id="proxy-password"
                      type="password"
                      placeholder="Password"
                      value={environmentSettings.proxy?.password || ""}
                      onChange={handleProxyPasswordChange}
                    />
                  </div>
                </div>

                <Button
                  onClick={testProxyConnection}
                  disabled={isTestingProxy || !environmentSettings.proxy?.host || !environmentSettings.proxy?.port}
                  className="w-full"
                  variant="outline"
                >
                  {isTestingProxy ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Testing Connection...
                    </>
                  ) : (
                    <>
                      <Network className="h-4 w-4 mr-2" />
                      Test Connection
                    </>
                  )}
                </Button>
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  )
}
