"use client"

import { ReactNode, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/api/auth"
import { checkUserPermission } from "@/utils/check-user-permissions"

interface PermissionGuardProps {
  children: ReactNode
  resource: string
  action: string
  fallbackPath?: string
  showError?: boolean
}

export function PermissionGuard({ 
  children, 
  resource, 
  action, 
  fallbackPath = "/dashboard",
  showError = true 
}: PermissionGuardProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth")
      return
    }

    if (!isLoading && isAuthenticated && user) {
      // Admin ve company_owner her zaman erişebilir
      if (user.accountType === 'admin' || user.accountType === 'company_owner') {
        return
      }

      // Permission kontrolü yap
      const hasPermission = checkUserPermission(resource, action)
      if (!hasPermission) {
        router.push(fallbackPath)
      }
    }
  }, [isLoading, isAuthenticated, user, resource, action, fallbackPath, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  // Admin ve company_owner her zaman erişebilir
  if (user?.accountType === 'admin' || user?.accountType === 'company_owner') {
    return <>{children}</>
  }

  // Permission kontrolü
  const hasPermission = checkUserPermission(resource, action)
  if (!hasPermission) {
    if (showError) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-6xl mb-4">🚫</div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Erişim Reddedildi
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Bu sayfaya erişim için gerekli izniniz bulunmuyor.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Gerekli izin: {resource} - {action}
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  return <>{children}</>
}

// Higher Order Component versiyonu
export function withPermission(
  Component: React.ComponentType,
  resource: string,
  action: string,
  options?: {
    fallbackPath?: string
    showError?: boolean
  }
) {
  return function WithPermission(props: any) {
    return (
      <PermissionGuard 
        resource={resource} 
        action={action}
        fallbackPath={options?.fallbackPath}
        showError={options?.showError}
      >
        <Component {...props} />
      </PermissionGuard>
    )
  }
}
