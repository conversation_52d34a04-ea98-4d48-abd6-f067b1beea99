import type { DataTemplate } from "@/store/testDataSetsStore"

/**
 * Popular tags for data sets
 */
export const popularTags = [
  "api",
  "user",
  "payment",
  "product",
  "auth",
  "test",
  "mobile",
  "web",
  "admin",
  "customer",
  "order",
  "shipping",
  "inventory",
  "security",
  "performance",
  "analytics",
  "notification",
  "billing",
  "subscription",
  "integration"
]

/**
 * Data templates for quick data set creation
 */
export const dataTemplates: DataTemplate[] = [
  {
    id: "user_profiles",
    name: "User Profiles",
    description: "Generate realistic user profile data including names, emails, and addresses",
    variables: [
      { name: "firstName", type: "string", description: "User's first name" },
      { name: "lastName", type: "string", description: "User's last name" },
      { name: "email", type: "email", description: "User's email address" },
      { name: "phoneNumber", type: "phone", description: "User's phone number" },
      { name: "address", type: "address", description: "User's address" },
      { name: "jobTitle", type: "string", description: "User's job title" },
      { name: "company", type: "string", description: "User's company" },
    ],
  },
  {
    id: "product_catalog",
    name: "Product Catalog",
    description: "Generate product data with names, descriptions, prices, and categories",
    variables: [
      { name: "productName", type: "string", description: "Product name" },
      { name: "description", type: "string", description: "Product description" },
      { name: "price", type: "number", description: "Product price" },
      { name: "category", type: "string", description: "Product category" },
      { name: "sku", type: "string", description: "Stock keeping unit" },
      { name: "inStock", type: "boolean", description: "Availability status" },
    ],
  },
  {
    id: "payment_data",
    name: "Payment Data",
    description: "Generate payment transaction data with amounts, methods, and statuses",
    variables: [
      { name: "transactionId", type: "string", description: "Transaction identifier" },
      { name: "amount", type: "number", description: "Payment amount" },
      { name: "currency", type: "string", description: "Currency code" },
      { name: "paymentMethod", type: "string", description: "Payment method" },
      { name: "status", type: "string", description: "Transaction status" },
      { name: "timestamp", type: "date", description: "Transaction timestamp" },
    ],
  },
  {
    id: "login_credentials",
    name: "Login Credentials",
    description: "Generate test login credentials with usernames and passwords",
    variables: [
      { name: "username", type: "string", description: "Username" },
      { name: "password", type: "secret", description: "Password" },
      { name: "email", type: "email", description: "Email address" },
      { name: "role", type: "string", description: "User role" },
      { name: "isActive", type: "boolean", description: "Account status" },
    ],
  },
  {
    id: "api_responses",
    name: "API Responses",
    description: "Generate API response data with status codes and messages",
    variables: [
      { name: "statusCode", type: "number", description: "HTTP status code" },
      { name: "message", type: "string", description: "Response message" },
      { name: "data", type: "json", description: "Response data" },
      { name: "timestamp", type: "date", description: "Response timestamp" },
    ],
  },
  {
    id: "test_scenarios",
    name: "Test Scenarios",
    description: "Generate test scenario data with steps and expected results",
    variables: [
      { name: "scenarioName", type: "string", description: "Test scenario name" },
      { name: "description", type: "string", description: "Scenario description" },
      { name: "steps", type: "array", description: "Test steps" },
      { name: "expectedResult", type: "string", description: "Expected outcome" },
      { name: "priority", type: "string", description: "Test priority" },
    ],
  }
]
