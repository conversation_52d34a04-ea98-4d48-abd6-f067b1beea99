/**
 * Interface for Event Coordinator
 * Centralizes event handling setup for all connectors in hirafi-hub
 */

export interface IEventCoordinator {
  /**
   * Setup all event handlers
   */
  setupAllEvents(): void;

  /**
   * Setup WebSocket event handlers
   */
  setupWebSocketEvents(): void;

  /**
   * Setup Redis event handlers
   */
  setupRedisEvents(): void;

  /**
   * Setup test management event handlers
   */
  setupTestManagementEvents(): void;

  /**
   * Setup node registry event handlers
   */
  setupNodeRegistryEvents(): void;

  /**
   * Teardown all event handlers
   */
  teardownAllEvents(): void;

  /**
   * Teardown WebSocket event handlers
   */
  teardownWebSocketEvents(): void;

  /**
   * Teardown Redis event handlers
   */
  teardownRedisEvents(): void;

  /**
   * Teardown test management event handlers
   */
  teardownTestManagementEvents(): void;

  /**
   * Teardown node registry event handlers
   */
  teardownNodeRegistryEvents(): void;

  /**
   * Check if events are setup
   */
  areEventsSetup(): boolean;
}
