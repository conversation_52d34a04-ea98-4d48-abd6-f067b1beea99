name: <PERSON> Docker build and Deploy Service

on:
  push:
    branches:
      - dev

env:
  PROJECT_NAME: hirafi-hub
  AWS_REGION: eu-north-1
  HOST_IP: *************
  ECS_CLUSTER: Hirafi-Dev

jobs:
  build:
    runs-on: ubuntu-latest
 
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set short sha
        id: sha_short
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build & Push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ env.PROJECT_NAME }}:${{ steps.sha_short.outputs.sha_short }}
            ${{ steps.login-ecr.outputs.registry }}/${{ env.PROJECT_NAME }}:dev-latest
  
  #bu stage sunucuda deploy edilen dockercompose u restart ediyor
  #deploy:
    #runs-on: ubuntu-latest
    #needs: build
    
    #steps:
      #- name: Save SSH key to file
        #run: |
          #mkdir -p ~/.ssh
          #echo "${{ secrets.SSH_KEY }}" > ~/.ssh/id_rsa
          #chmod 600 ~/.ssh/id_rsa

      #- name: Deploy Hub service to dev.hirafi.ai
        #run: |
          #ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa ubuntu@${{ env.HOST_IP }} "sudo su -c 'bash /home/<USER>/update_service.sh && echo \"Service updated\"'"

  service-update:
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Download task definition
        run:  aws ecs update-service --cluster ${{ env.ECS_CLUSTER }} --service ${{ env.PROJECT_NAME }}-service --region ${{ env.AWS_REGION }} --force-new-deployment
