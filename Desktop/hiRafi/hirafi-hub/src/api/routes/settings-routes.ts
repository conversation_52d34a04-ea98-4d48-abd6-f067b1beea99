/**
 * Settings Routes
 * Settings related endpoints
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { authenticate, authorize, AuthRequest } from '../middleware/authMiddleware.js';
import {
  addCompanyAIModel,
  getCompanyAIModels,
  updateCompanyAIModel,
  deleteCompanyAIModel
} from '../../services/mongo/companyService.js';

const router = Router();

/**
 * AI Modelleri listele
 */
router.get('/ai-models', authenticate, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;

    if (!companyId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // MongoDB servisine istek at
    const result = await getCompanyAIModels(companyId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      data: {
        models: result.models
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /settings/ai-models: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Özel karakterleri ve boşlukları temizleyen yardımcı fonksiyon
 * @param input Temizlenecek metin
 * @returns Temizlenmiş metin
 */
function cleanInputString(input: string): string {
  if (!input) return input;

  // Başındaki ve sonundaki boşlukları temizle
  let cleaned = input.trim();

  // Tırnak işaretlerini temizle (tek tırnak, çift tırnak)
  cleaned = cleaned.replace(/['"]/g, '');

  // Gereksiz boşlukları temizle (birden fazla boşluğu tek boşluğa dönüştür)
  cleaned = cleaned.replace(/\s+/g, ' ');

  return cleaned;
}

/**
 * AI Model ekle - Sadece company_owner hesap türüne sahip kullanıcılar ekleyebilir
 */
router.post('/ai-models', authenticate, authorize('company_owner'), async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const companyId = authReq.user?.companyId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    if (!companyId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    const { name, api, endpoint, apiKey, supportsImageProcessing } = req.body;

    // Frontend'den gelen endpoint veya api parametresini kontrol et
    let apiEndpoint = api || endpoint;

    // Model adı ve API endpoint'ini temizle
    const cleanedName = cleanInputString(name);
    const cleanedApiEndpoint = cleanInputString(apiEndpoint);

    if (!cleanedName || !cleanedApiEndpoint || !apiKey) {
      return res.status(400).json({
        success: false,
        error: 'Model name, API endpoint and API key are required'
      });
    }

    // Temizleme işlemi yapıldıysa log kaydı oluştur
    if (cleanedName !== name) {
      logger.info(`AI Model name cleaned from "${name}" to "${cleanedName}"`);
    }

    if (cleanedApiEndpoint !== apiEndpoint) {
      logger.info(`AI Model API endpoint cleaned from "${apiEndpoint}" to "${cleanedApiEndpoint}"`);
    }

    // MongoDB servisine istek at
    const result = await addCompanyAIModel(companyId, userId, {
      name: cleanedName,
      api: cleanedApiEndpoint, // Temizlenmiş API endpoint'i kullan
      apiKey,
      supportsImageProcessing: supportsImageProcessing || false
    });

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'AI Model added successfully',
      model: result.model
    });
  } catch (error: any) {
    logger.error(`API Error in POST /settings/ai-models: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * AI Model güncelle - Sadece company_owner hesap türüne sahip kullanıcılar güncelleyebilir
 */
router.put('/ai-models/:modelId', authenticate, authorize('company_owner'), async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;
    const { modelId } = req.params;

    if (!companyId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // Gelen verileri temizle
    const updateData = { ...req.body };

    // Model adı varsa temizle
    if (updateData.name) {
      const cleanedName = cleanInputString(updateData.name);
      if (cleanedName !== updateData.name) {
        logger.info(`AI Model name cleaned from "${updateData.name}" to "${cleanedName}" during update`);
        updateData.name = cleanedName;
      }
    }

    // API endpoint varsa temizle (api veya endpoint olarak gelebilir)
    if (updateData.api) {
      const cleanedApi = cleanInputString(updateData.api);
      if (cleanedApi !== updateData.api) {
        logger.info(`AI Model API endpoint cleaned from "${updateData.api}" to "${cleanedApi}" during update`);
        updateData.api = cleanedApi;
      }
    } else if (updateData.endpoint) {
      const cleanedEndpoint = cleanInputString(updateData.endpoint);
      if (cleanedEndpoint !== updateData.endpoint) {
        logger.info(`AI Model endpoint cleaned from "${updateData.endpoint}" to "${cleanedEndpoint}" during update`);
        updateData.api = cleanedEndpoint; // api alanına kaydet
        delete updateData.endpoint; // endpoint alanını kaldır
      } else {
        // Endpoint alanını api alanına taşı
        updateData.api = updateData.endpoint;
        delete updateData.endpoint;
      }
    }

    // Temizlenmiş verileri kullanarak güncelleme yap
    const result = await updateCompanyAIModel(companyId, modelId, updateData);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      message: 'AI Model updated successfully',
      data: {
        model: result.model
      }
    });
  } catch (error: any) {
    logger.error(`API Error in PUT /settings/ai-models/${req.params.modelId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * AI Model sil - Sadece company_owner hesap türüne sahip kullanıcılar silebilir
 */
router.delete('/ai-models/:modelId', authenticate, authorize('company_owner'), async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;
    const { modelId } = req.params;

    if (!companyId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // Önce bu AI Model'in herhangi bir run'da kullanılıp kullanılmadığını kontrol et
    const { getRunsCollection } = await import('../../services/mongo/runService.js');
    const runsCollection = await getRunsCollection();

    // AI Model'in kullanıldığı run'ları ara
    const runsUsingModel = await runsCollection.countDocuments({
      'environment.aiModel': modelId
    });

    if (runsUsingModel > 0) {
      return res.status(400).json({
        success: false,
        error: 'This AI Model is being used in one or more runs and cannot be deleted. Please update the runs to use a different AI Model first.'
      });
    }

    // MongoDB servisine istek at
    const result = await deleteCompanyAIModel(companyId, modelId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      message: 'AI Model deleted successfully'
    });
  } catch (error: any) {
    logger.error(`API Error in DELETE /settings/ai-models/${req.params.modelId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * AI Model aktifleştir - Sadece company_owner hesap türüne sahip kullanıcılar aktifleştirebilir
 */
router.post('/ai-models/:modelId/activate', authenticate, authorize('company_owner'), async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;
    const { modelId } = req.params;

    if (!companyId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // MongoDB servisine istek at - isActive değerini true olarak güncelle
    const result = await updateCompanyAIModel(companyId, modelId, { isActive: true });

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      message: 'AI Model activated successfully',
      model: result.model
    });
  } catch (error: any) {
    logger.error(`API Error in POST /settings/ai-models/${req.params.modelId}/activate: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * AI Model devre dışı bırak - Sadece company_owner hesap türüne sahip kullanıcılar devre dışı bırakabilir
 */
router.post('/ai-models/:modelId/deactivate', authenticate, authorize('company_owner'), async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;
    const { modelId } = req.params;

    if (!companyId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // MongoDB servisine istek at - isActive değerini false olarak güncelle
    const result = await updateCompanyAIModel(companyId, modelId, { isActive: false });

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      message: 'AI Model deactivated successfully',
      model: result.model
    });
  } catch (error: any) {
    logger.error(`API Error in POST /settings/ai-models/${req.params.modelId}/deactivate: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
