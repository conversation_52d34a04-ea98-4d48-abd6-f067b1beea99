import type { VariableType, EnvironmentType, ValueConstraints } from "@/types/test-data"
import type { EnhancedDataVariable } from "@/types/data-generation"
import { encryptValue } from "@/lib/encryption"

/**
 * Data generation utility class
 */
export class DataGenerator {
  /**
   * Generate string values with constraints
   */
  static string(options: any = {}): string {
    const {
      length = 10,
      minLength = 1,
      maxLength = 100,
      includeUppercase = true,
      includeLowercase = true,
      includeNumbers = false,
      includeSpecial = false,
      mustInclude = [],
      pattern,
      environment,
    } = options

    // Add environment-specific variations if needed
    let envPrefix = ""
    if (environment) {
      switch (environment) {
        case "development":
          envPrefix = "DEV_"
          break
        case "testing":
          envPrefix = "TEST_"
          break
        case "staging":
          envPrefix = "STAGE_"
          break
        case "production":
          envPrefix = "PROD_"
          break
      }
    }

    // Build character set based on options
    let charset = ""
    if (includeLowercase) charset += "abcdefghijklmnopqrstuvwxyz"
    if (includeUppercase) charset += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    if (includeNumbers) charset += "0123456789"
    if (includeSpecial) charset += "!@#$%^&*()-_=+[]{}|;:,.<>?"

    // If no character sets are selected, default to lowercase
    if (charset === "") charset = "abcdefghijklmnopqrstuvwxyz"

    // If pattern is provided, use regex to generate
    if (pattern) {
      try {
        // Simple pattern handling - this could be expanded
        if (pattern === "[A-Z]{3}[0-9]{4}") {
          let result = ""
          for (let i = 0; i < 3; i++) {
            result += "ABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt(Math.floor(Math.random() * 26))
          }
          for (let i = 0; i < 4; i++) {
            result += "0123456789".charAt(Math.floor(Math.random() * 10))
          }
          return envPrefix + result
        }
        // Add more pattern handlers as needed
      } catch (e) {
        console.error("Invalid pattern:", pattern)
      }
    }

    // Determine actual length to use
    const targetLength = length || Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength

    // Generate the random string
    let result = ""
    for (let i = 0; i < targetLength; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length))
    }

    // Ensure required characters are included
    if (mustInclude && mustInclude.length > 0) {
      for (const char of mustInclude) {
        if (!result.includes(char)) {
          const pos = Math.floor(Math.random() * result.length)
          result = result.substring(0, pos) + char + result.substring(pos + 1)
        }
      }
    }

    return envPrefix + result
  }

  /**
   * Generate number values with constraints
   */
  static number(options: any = {}): string {
    const { min = 0, max = 100, decimals = 0, environment } = options
    const factor = Math.pow(10, decimals)

    // Add environment-specific variations
    let envMultiplier = 1
    if (environment) {
      switch (environment) {
        case "development":
          envMultiplier = 0.5 // Lower values for dev
          break
        case "testing":
          envMultiplier = 1 // Normal values for testing
          break
        case "staging":
          envMultiplier = 1.5 // Higher values for staging
          break
        case "production":
          envMultiplier = 2 // Highest values for production
          break
      }
    }

    const adjustedMin = min * envMultiplier
    const adjustedMax = max * envMultiplier

    return (Math.floor(Math.random() * (adjustedMax - adjustedMin + 1) * factor) / factor + adjustedMin).toFixed(
      decimals,
    )
  }

  /**
   * Generate boolean values
   */
  static boolean(): string {
    return Math.random() > 0.5 ? "true" : "false"
  }

  /**
   * Generate date values with constraints
   */
  static date(options: any = {}): string {
    const { start = new Date(2000, 0, 1), end = new Date(), environment } = options

    // Adjust dates based on environment
    let adjustedStart = new Date(start)
    let adjustedEnd = new Date(end)

    if (environment) {
      const now = new Date()
      switch (environment) {
        case "development":
          // Past dates for dev
          adjustedStart = new Date(now.getFullYear() - 2, 0, 1)
          adjustedEnd = new Date(now.getFullYear() - 1, 11, 31)
          break
        case "testing":
          // Recent dates for testing
          adjustedStart = new Date(now.getFullYear() - 1, 0, 1)
          adjustedEnd = new Date(now.getFullYear(), now.getMonth(), 0)
          break
        case "staging":
          // Current dates for staging
          adjustedStart = new Date(now.getFullYear(), now.getMonth() - 3, 1)
          adjustedEnd = new Date()
          break
        case "production":
          // Future dates for production
          adjustedStart = new Date()
          adjustedEnd = new Date(now.getFullYear() + 1, 11, 31)
          break
      }
    }

    const date = new Date(adjustedStart.getTime() + Math.random() * (adjustedEnd.getTime() - adjustedStart.getTime()))
    return date.toISOString().split("T")[0]
  }

  /**
   * Generate email addresses
   */
  static email(options: any = {}): string {
    const {
      domains = ["example.com", "test.com", "company.org", "mail.net", "domain.io"],
      mustInclude = [],
      environment,
    } = options

    // Generate username part
    let username = ""
    if (mustInclude.length > 0 && mustInclude.some((item: string) => !item.includes("@"))) {
      username = mustInclude.find((item: string) => !item.includes("@")) || ""
    } else {
      const names = ["john", "jane", "michael", "emily", "david", "sarah", "robert", "lisa"]
      username = names[Math.floor(Math.random() * names.length)]
      username += Math.floor(Math.random() * 1000)
    }

    // Generate or use specific domain
    let domain = domains[Math.floor(Math.random() * domains.length)]
    if (mustInclude.length > 0 && mustInclude.some((item: string) => item.includes("@"))) {
      const domainPart = mustInclude.find((item: string) => item.includes("@"))?.split("@")[1]
      if (domainPart) domain = domainPart
    }

    // Add environment-specific domains
    if (environment) {
      switch (environment) {
        case "development":
          domain = `dev.${domain}`
          break
        case "testing":
          domain = `test.${domain}`
          break
        case "staging":
          domain = `stage.${domain}`
          break
        case "production":
          // Use the original domain for production
          break
      }
    }

    return `${username}@${domain}`
  }

  /**
   * Generate phone numbers
   */
  static phone(options: any = {}): string {
    const { format = "international", pattern, environment } = options

    // If pattern is provided, try to follow it
    if (pattern) {
      let result = pattern
      // Replace X with random digits
      result = result.replace(/X/g, () => Math.floor(Math.random() * 10).toString())
      return result
    }

    // Add environment-specific area codes
    let areaCode = Math.floor(Math.random() * 900) + 100
    if (environment) {
      switch (environment) {
        case "development":
          areaCode = 555 // Dev area code
          break
        case "testing":
          areaCode = 666 // Test area code
          break
        case "staging":
          areaCode = 777 // Staging area code
          break
        case "production":
          areaCode = 888 // Production area code
          break
      }
    }

    const firstPart = Math.floor(Math.random() * 900) + 100
    const secondPart = Math.floor(Math.random() * 9000) + 1000
    return `+1 (${areaCode}) ${firstPart}-${secondPart}`
  }

  /**
   * Generate addresses
   */
  static address(options: any = {}): string {
    const { environment } = options

    // Environment-specific addresses
    if (environment) {
      switch (environment) {
        case "development":
          return "123 Dev Street, DevCity, DC 10001"
        case "testing":
          return "456 Test Avenue, TestVille, TV 20002"
        case "staging":
          return "789 Staging Boulevard, StageTown, ST 30003"
        case "production":
          return "101 Production Road, ProdCity, PC 40004"
      }
    }

    const streetNumbers = [123, 456, 789, 101, 202]
    const streetNames = ["Main St", "Oak Ave", "Maple Rd", "Park Blvd", "Cedar Ln"]
    const cities = ["Anytown", "Springfield", "Riverdale", "Lakeside", "Mountainview"]
    const states = ["CA", "NY", "TX", "FL", "IL"]
    const zipCodes = ["94043", "10001", "75001", "33101", "60601"]

    const streetNumber = streetNumbers[Math.floor(Math.random() * streetNumbers.length)]
    const streetName = streetNames[Math.floor(Math.random() * streetNames.length)]
    const city = cities[Math.floor(Math.random() * cities.length)]
    const state = states[Math.floor(Math.random() * states.length)]
    const zipCode = zipCodes[Math.floor(Math.random() * zipCodes.length)]

    return `${streetNumber} ${streetName}, ${city}, ${state} ${zipCode}`
  }

  /**
   * Generate JSON objects
   */
  static object(options: any = {}): string {
    const { properties = 2, environment } = options
    const keys = ["id", "name", "status", "type", "category"]
    const values = ["value", "active", "pending", "primary", "secondary"]

    // Environment-specific values
    if (environment) {
      const envValues = {
        development: ["dev", "draft", "beta", "local", "sandbox"],
        testing: ["test", "qa", "verify", "check", "trial"],
        staging: ["stage", "pre-prod", "review", "demo", "preview"],
        production: ["prod", "live", "release", "public", "official"],
      }

      const obj: Record<string, string> = {}
      for (let i = 0; i < properties; i++) {
        const key = keys[Math.floor(Math.random() * keys.length)]
        const value = envValues[environment][Math.floor(Math.random() * 5)]
        obj[key] = value
      }
      return JSON.stringify(obj)
    }

    const obj: Record<string, string> = {}
    for (let i = 0; i < properties; i++) {
      const key = keys[Math.floor(Math.random() * keys.length)]
      const value = values[Math.floor(Math.random() * values.length)]
      obj[key] = value
    }

    return JSON.stringify(obj)
  }

  /**
   * Generate arrays
   */
  static array(options: any = {}): string {
    const { length = 3, items = ["item1", "item2", "item3", "item4", "item5"], environment } = options

    // Environment-specific arrays
    if (environment) {
      const envItems = {
        development: ["dev1", "dev2", "dev3", "dev4", "dev5"],
        testing: ["test1", "test2", "test3", "test4", "test5"],
        staging: ["stage1", "stage2", "stage3", "stage4", "stage5"],
        production: ["prod1", "prod2", "prod3", "prod4", "prod5"],
      }

      const result = []
      for (let i = 0; i < length; i++) {
        const randomItem = envItems[environment][Math.floor(Math.random() * 5)]
        result.push(randomItem)
      }
      return JSON.stringify(result)
    }

    const result = []
    for (let i = 0; i < length; i++) {
      const randomItem = items[Math.floor(Math.random() * items.length)]
      result.push(randomItem)
    }

    return JSON.stringify(result)
  }

  /**
   * Main generator function that handles all types and formats
   */
  static generate(
    type: VariableType,
    format?: string,
    constraints?: ValueConstraints,
    environment?: EnvironmentType
  ): string {
    // Convert constraints to options
    const options = constraints ? { ...constraints, environment } : { environment }

    // Handle by type with constraints
    switch (type) {
      case "string":
        return DataGenerator.string(options)
      case "number":
        return DataGenerator.number(options)
      case "boolean":
        return DataGenerator.boolean()
      case "date":
        return DataGenerator.date(options)
      case "email":
        return DataGenerator.email(options)
      case "phone":
        return DataGenerator.phone(options)
      case "address":
        return DataGenerator.address(options)
      case "json":
        return DataGenerator.object(options)
      case "array":
        return DataGenerator.array(options)
      case "secret":
        // For secrets, ensure we have good complexity
        const secretOptions = {
          ...options,
          includeUppercase: true,
          includeLowercase: true,
          includeNumbers: true,
          includeSpecial: true,
          minLength: Math.max(options.minLength || 0, 12),
        }
        return encryptValue(DataGenerator.string(secretOptions))
      default:
        return "Generated value"
    }
  }

  /**
   * Generate a value based on variable name heuristics
   */
  static generateByName(variable: EnhancedDataVariable, environment?: EnvironmentType): string {
    const name = variable.name.toLowerCase()
    const type = variable.type

    // If it has a specific format, use that
    if (variable.format) {
      return DataGenerator.generate(type, variable.format, variable.constraints, environment)
    }

    // Otherwise try to infer from name and generate accordingly
    if (name.includes("email") || name.includes("mail")) {
      return DataGenerator.email({ environment })
    }
    if (name.includes("phone") || name.includes("mobile") || name.includes("tel")) {
      return DataGenerator.phone({ environment })
    }
    if (name.includes("address") || name.includes("location")) {
      return DataGenerator.address({ environment })
    }
    if (name.includes("password") || name.includes("secret") || name.includes("key")) {
      return DataGenerator.generate("secret", undefined, variable.constraints, environment)
    }
    if (name.includes("date") || name.includes("time")) {
      return DataGenerator.date({ environment })
    }

    // Default to type-based generation
    return DataGenerator.generate(type, undefined, variable.constraints, environment)
  }
}
