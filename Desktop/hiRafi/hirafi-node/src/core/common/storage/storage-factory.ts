/**
 * Storage Factory - Dependency Injection Implementation
 * 
 * Replaces the StorageManager singleton with a proper factory pattern
 * that integrates with dependency injection containers.
 */

import { ILoggerService, LoggerServiceFactory } from '../../../utils/logger-service.js';
import { IArtifactStorage } from './artifact-storage.interface.js';
import { S3ArtifactStorage } from './s3-artifact-storage.js';
import { MinioArtifactStorage } from './minio-artifact-storage.js';

/**
 * Storage provider configuration interface
 */
export interface StorageConfig {
  provider?: string;
  bucket?: string;
  region?: string;
  accessKey?: string;
  secretKey?: string;
  endpoint?: string;
  endPoint?: string; // MinIO uses endPoint
  port?: number;
  useSSL?: boolean;
  forcePathStyle?: boolean;
  publicBaseUrl?: string;
  forceReinitialize?: boolean;
}

/**
 * Storage factory options
 */
export interface StorageFactoryOptions {
  enableLogging?: boolean;
  defaultProvider?: string;
  fallbackToNoOp?: boolean;
}

/**
 * Storage Factory Class
 *
 * Provides factory methods for creating storage providers with proper
 * dependency injection support and lifecycle management.
 */
export class StorageFactoryInstance {
  private readonly options: StorageFactoryOptions;
  private readonly logger: ILoggerService;

  constructor(options: StorageFactoryOptions = {}) {
    this.options = {
      enableLogging: true,
      defaultProvider: 's3',
      fallbackToNoOp: true,
      ...options
    };
    this.logger = LoggerServiceFactory.createServiceLogger('StorageFactory');
  }

  /**
   * Create a storage provider instance
   * @param config Storage configuration
   * @returns Promise resolving to an initialized storage provider
   */
  async createStorageProvider(config: StorageConfig = {}): Promise<IArtifactStorage> {
    try {
      const { provider, mergedConfig } = this.determineProviderAndConfig(config);
      
      if (this.options.enableLogging) {
        this.logger.serviceInit('StorageFactory', `Creating storage provider: ${provider}`);
      }

      const storage = await this.instantiateProvider(provider, mergedConfig);
      await storage.initialize(mergedConfig);

      if (this.options.enableLogging) {
        this.logger.serviceInit('StorageFactory', `Storage provider initialized: ${storage.getProviderName()}`);
      }

      return storage;
    } catch (error: any) {
      if (this.options.enableLogging) {
        this.logger.warn(`StorageFactory: Failed to create storage provider: ${error.message}`);
      }

      if (this.options.fallbackToNoOp) {
        if (this.options.enableLogging) {
          this.logger.warn('StorageFactory: Falling back to no-op storage provider');
        }
        return await this.createNoOpStorage();
      }

      throw error;
    }
  }

  /**
   * Create multiple storage providers with different configurations
   * @param configs Array of storage configurations
   * @returns Promise resolving to array of initialized storage providers
   */
  async createMultipleProviders(configs: StorageConfig[]): Promise<IArtifactStorage[]> {
    const providers = await Promise.allSettled(
      configs.map(config => this.createStorageProvider(config))
    );

    const successfulProviders: IArtifactStorage[] = [];
    const failedProviders: string[] = [];

    providers.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successfulProviders.push(result.value);
      } else {
        failedProviders.push(`Config ${index}: ${result.reason.message}`);
      }
    });

    if (failedProviders.length > 0 && this.options.enableLogging) {
      this.logger.warn(`StorageFactory: Failed to create ${failedProviders.length} providers:`, failedProviders);
    }

    return successfulProviders;
  }

  /**
   * Determine provider type and merge configuration
   * @param customConfig Custom configuration overrides
   * @returns Object containing provider name and merged configuration
   */
  private determineProviderAndConfig(customConfig: StorageConfig): { provider: string, mergedConfig: StorageConfig } {
    let provider: string;

    // 1. Check custom config first
    if (customConfig.provider) {
      provider = customConfig.provider;
    }
    // 2. Check environment variables
    else {
      const minioEnabled = process.env.MINIO_ENABLED === 'true';
      const s3Enabled = process.env.S3_ENABLED === 'true';

      if (s3Enabled) {
        provider = 's3';
      } else if (minioEnabled) {
        provider = 'minio';
      } else {
        provider = process.env.ARTIFACT_STORAGE_PROVIDER || this.options.defaultProvider || 's3';
      }
    }

    // Merge configuration from environment
    const mergedConfig = this.mergeEnvironmentConfig(provider, customConfig);

    return { provider, mergedConfig };
  }

  /**
   * Merge environment configuration with custom config
   * @param provider Storage provider type
   * @param customConfig Custom configuration
   * @returns Merged configuration
   */
  private mergeEnvironmentConfig(provider: string, customConfig: StorageConfig): StorageConfig {
    const merged = { ...customConfig };

    if (provider === 's3') {
      merged.bucket = merged.bucket || process.env.S3_SCREENSHOT_BUCKET;
      merged.region = merged.region || process.env.AWS_REGION; // Use AWS_REGION instead of S3_REGION
      merged.accessKey = merged.accessKey || process.env.AWS_ACCESS_KEY_ID; // Use standard AWS env var
      merged.secretKey = merged.secretKey || process.env.AWS_SECRET_ACCESS_KEY; // Use standard AWS env var
      merged.endpoint = merged.endpoint || process.env.S3_ENDPOINT;
      merged.publicBaseUrl = merged.publicBaseUrl || process.env.S3_PUBLIC_URL; // Use S3_PUBLIC_URL instead of S3_PUBLIC_BASE_URL
      merged.forcePathStyle = merged.forcePathStyle ?? (process.env.S3_FORCE_PATH_STYLE === 'true');
    } else if (provider === 'minio') {
      merged.bucket = merged.bucket || process.env.MINIO_SCREENSHOT_BUCKET;
      merged.endPoint = merged.endPoint || process.env.MINIO_ENDPOINT;
      merged.port = merged.port || (process.env.MINIO_PORT ? parseInt(process.env.MINIO_PORT) : undefined);
      merged.useSSL = merged.useSSL ?? (process.env.MINIO_USE_SSL === 'true');
      merged.accessKey = merged.accessKey || process.env.MINIO_ACCESS_KEY;
      merged.secretKey = merged.secretKey || process.env.MINIO_SECRET_KEY;
      merged.region = merged.region || process.env.MINIO_REGION;
      merged.publicBaseUrl = merged.publicBaseUrl || process.env.MINIO_PUBLIC_BASE_URL;
    }

    return merged;
  }

  /**
   * Instantiate a storage provider
   * @param provider Provider type
   * @param config Provider configuration
   * @returns Storage provider instance
   */
  private async instantiateProvider(provider: string, config: StorageConfig): Promise<IArtifactStorage> {
    switch (provider.toLowerCase()) {
      case 's3':
        return new S3ArtifactStorage(config);
      case 'minio':
        return new MinioArtifactStorage(config);
      default:
        if (this.options.enableLogging) {
          this.logger.warn(`StorageFactory: Unknown provider "${provider}", defaulting to S3`);
        }
        return new S3ArtifactStorage(config);
    }
  }

  /**
   * Create a no-op storage provider for fallback scenarios
   * @returns No-op storage provider
   */
  private async createNoOpStorage(): Promise<IArtifactStorage> {
    // Use dynamic import for ES modules compatibility
    const { Readable } = await import('stream');

    return {
      getProviderName: () => 'no-op',
      initialize: async () => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Initialize called - no action taken');
        }
      },
      uploadArtifact: async () => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Upload artifact called - no action taken');
        }
        return { storagePath: '/dev/null', url: undefined };
      },
      downloadArtifact: async (_storagePath: string) => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Download artifact called - no action taken');
        }
        return new Readable({ read() { this.push(null); } });
      },
      deleteArtifact: async (_storagePath: string) => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Delete artifact called - no action taken');
        }
      },
      listArtifacts: async (_pathPrefix: string) => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: List artifacts called - returning empty array');
        }
        return [];
      },
      getPresignedUrl: async (_storagePath: string, _expiresInSeconds: number, _operation: 'getObject' | 'putObject') => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Get presigned URL called - returning empty string');
        }
        return '';
      },
      uploadFile: async (_filePath: string, _destinationKey: string, _contentType?: string) => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Upload file called - returning empty string');
        }
        return '';
      },
      uploadBuffer: async (_buffer: Buffer, _destinationKey: string, _contentType: string) => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Upload buffer called - returning empty string');
        }
        return '';
      },
      getFileUrl: async (_destinationKey: string) => {
        if (this.options.enableLogging) {
          this.logger.debug('NoOpStorage: Get file URL called - returning empty string');
        }
        return '';
      }
    };
  }
}

/**
 * Storage Factory - Main export with static methods
 *
 * Provides both static convenience methods and instance creation.
 */
export class StorageFactory {
  /**
   * Create a storage provider with default options (static method)
   * @param config Storage configuration
   * @returns Promise resolving to initialized storage provider
   */
  static async createStorageProvider(config: StorageConfig = {}): Promise<IArtifactStorage> {
    const factory = new StorageFactoryInstance();
    return factory.createStorageProvider(config);
  }

  /**
   * Create a new storage factory instance with specific options
   */
  static createFactory(options: StorageFactoryOptions = {}): StorageFactoryInstance {
    return new StorageFactoryInstance(options);
  }
}
