/**
 * Common AI String Handler
 * Handles executing AI string steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI String Handler
 * Handles executing AI string steps using the AI agent
 */
export class CommonAiStringHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiString';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiStringHandler';
  }

  /**
   * Execute an AI string step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiString, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const stringQuery = step.prompt || step.value || step.name || step.description;

    if (!stringQuery) {
      return { success: false, error: 'Missing string query for aiString step. Please provide a prompt field.' };
    }

    try {
      // Note: aiString does not support deepThink parameter according to Midscene.js API
      // Only domIncluded and screenshotIncluded options are supported

      logger.debug(`CommonAiStringHandler: Executing string query "${stringQuery}"`);

      const result = await agent.aiString(stringQuery || '');
      logger.debug(`aiString result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error && typeof result.result === 'string';

      const logEntry = formatAIResultForLog('aiString', stringQuery, result);
      logEntry.success = isSuccess;
      logEntry.metadata.result = result.result;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `String query failed: ${stringQuery}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime,
          result: result.result
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI String Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiString',
        description: stringQuery,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiStringHandler: String query failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 