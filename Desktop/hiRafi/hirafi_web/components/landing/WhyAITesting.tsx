import React from 'react';
import { CheckCircle } from 'lucide-react';

const WhyAITesting = () => {
  const challenges = [
    {
      category: "Manual Testing Challenges",
      items: [
        "Slow & Labor-Intensive: Manual test execution delays releases",
        "Human Errors: Testers miss edge cases due to fatigue or oversight",
        "Not Scalable: Impossible to keep up with agile/devops speed"
      ]
    },
    {
      category: "Flaws in Traditional Test Automation",
      items: [
        "Brittle Tests: Scripts break with minor UI changes",
        "High Maintenance: Teams spend 50%+ effort fixing outdated scripts",
        "Limited Coverage: Hard to automate complex scenarios"
      ]
    },
    {
      category: "Emerging Tech Demands",
      items: [
        "AI/ML Apps: Traditional tools can't validate non-deterministic outputs",
        "Microservices/APIs: Need auto-generated test cases for rapid changes",
        "Cross-Platform Chaos: Mobile + Web + Cloud require unified testing"
      ]
    },
    {
      category: "Business Impact",
      items: [
        "Costly Bugs: Failures in production hurt revenue and reputation",
        "Slow Time-to-Market: QA bottlenecks delay releases by weeks",
        "Skill Gaps: Few engineers can code/maintain automation frameworks"
      ]
    }
  ];

  return (
    <section id="why-ai" className="py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Why AI in Testing?</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Traditional testing methods are failing to keep pace with modern development.
            AI-powered testing is no longer optional—it's essential.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          {challenges.map((challenge, index) => (
            <div 
              key={index} 
              className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-shadow duration-300"
            >
              <h3 className="text-xl font-bold mb-6 text-blue-700">{challenge.category}</h3>
              <ul className="space-y-4">
                {challenge.items.map((item, idx) => (
                  <li key={idx} className="flex items-start">
                    <CheckCircle size={20} className="text-green-500 mr-2 flex-shrink-0 mt-1" />
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 md:p-12 text-white">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-3/4 mb-6 md:mb-0 md:pr-6">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">The HiRafi Advantage</h3>
              <p className="text-lg opacity-90 mb-4">
                Our AI-powered approach eliminates these challenges by providing self-healing tests, 
                codeless automation, and intelligent adaptation to changes.
              </p>
              <p className="text-lg opacity-90">
                The result? 50% less maintenance effort, 3x faster test creation, and significantly higher test coverage.
              </p>
            </div>
            <div className="md:w-1/4 flex justify-center">
              <button className="px-8 py-3 bg-white text-blue-600 font-medium rounded-lg shadow-md hover:shadow-xl transition-all transform hover:-translate-y-1">
                Learn How It Works
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyAITesting;