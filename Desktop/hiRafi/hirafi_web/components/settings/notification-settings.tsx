"use client"

import { useState } from "react"
import { Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"

export function NotificationSettings() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const [notifications, setNotifications] = useState({
    email: {
      testResults: true,
      weeklyReports: true,
      systemUpdates: false,
      marketing: false,
    },
    push: {
      testResults: true,
      testStarted: true,
      testFailed: true,
      systemUpdates: true,
    },
  })

  const handleToggleEmail = (key: keyof typeof notifications.email, value: boolean) => {
    setNotifications((prev) => ({
      ...prev,
      email: {
        ...prev.email,
        [key]: value,
      },
    }))
  }

  const handleTogglePush = (key: keyof typeof notifications.push, value: boolean) => {
    setNotifications((prev) => ({
      ...prev,
      push: {
        ...prev.push,
        [key]: value,
      },
    }))
  }

  const handleSaveNotifications = async () => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Notification settings updated",
        description: "Your notification preferences have been saved.",
      })
    } catch (error) {
      toast({
        title: "Failed to update settings",
        description: "An error occurred while saving your notification settings.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Email Notifications</CardTitle>
          <CardDescription>Configure which email notifications you want to receive</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">Test Results</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive email notifications when your tests complete
              </p>
            </div>
            <Switch
              checked={notifications.email.testResults}
              onCheckedChange={(checked) => handleToggleEmail("testResults", checked)}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">Weekly Reports</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive weekly summary reports of your test activities
              </p>
            </div>
            <Switch
              checked={notifications.email.weeklyReports}
              onCheckedChange={(checked) => handleToggleEmail("weeklyReports", checked)}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">System Updates</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive notifications about system updates and maintenance
              </p>
            </div>
            <Switch
              checked={notifications.email.systemUpdates}
              onCheckedChange={(checked) => handleToggleEmail("systemUpdates", checked)}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">Marketing & Promotions</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">Receive marketing emails and special offers</p>
            </div>
            <Switch
              checked={notifications.email.marketing}
              onCheckedChange={(checked) => handleToggleEmail("marketing", checked)}
              disabled={isLoading}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2 border-t px-6 py-4">
          <Button
            onClick={handleSaveNotifications}
            disabled={isLoading}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Email Preferences"
            )}
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Push Notifications</CardTitle>
          <CardDescription>Configure which push notifications you want to receive in the app</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">Test Results</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">Receive notifications when your tests complete</p>
            </div>
            <Switch
              checked={notifications.push.testResults}
              onCheckedChange={(checked) => handleTogglePush("testResults", checked)}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">Test Started</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive notifications when your tests start running
              </p>
            </div>
            <Switch
              checked={notifications.push.testStarted}
              onCheckedChange={(checked) => handleTogglePush("testStarted", checked)}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">Test Failed</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">Receive notifications when your tests fail</p>
            </div>
            <Switch
              checked={notifications.push.testFailed}
              onCheckedChange={(checked) => handleTogglePush("testFailed", checked)}
              disabled={isLoading}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h4 className="font-medium">System Updates</h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Receive notifications about system updates and maintenance
              </p>
            </div>
            <Switch
              checked={notifications.push.systemUpdates}
              onCheckedChange={(checked) => handleTogglePush("systemUpdates", checked)}
              disabled={isLoading}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2 border-t px-6 py-4">
          <Button
            onClick={handleSaveNotifications}
            disabled={isLoading}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Push Preferences"
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

