"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  LayoutGrid,
  FolderIcon,
  FolderOpen,
  Search,
  ChevronRight,
  ChevronDown,
  Inbox,
  X,
  Plus
} from "lucide-react"
import { useScenarioStore } from "@/store/scenarioStore"
import { cn } from "@/lib/utils"
import { useDebounce } from "@/hooks/useDebounce"

interface FolderType {
  id: string
  name: string
  color?: string
}

interface FolderNavigationProps {
  folders: FolderType[]
  uncategorizedCount: number
  allScenariosCount: number
  folderScenarioCounts: Record<string, number>
  onFolderSelect: (folderId: string | null) => void
  selectedFolder: string | null
  isLoading?: boolean
}

export function FolderNavigation({
  folders,
  uncategorizedCount,
  allScenariosCount,
  folderScenarioCounts,
  onFolderSelect,
  selectedFolder,
  isLoading = false
}: FolderNavigationProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const { openFolders, toggleOpenFolder } = useScenarioStore()

  const debouncedSearch = useDebounce(searchQuery, 300)

  // Filter folders based on search query - using useMemo instead of useState + useEffect
  const filteredFolders = useMemo(() => {
    if (!debouncedSearch) {
      return folders
    }

    return folders.filter(folder =>
      folder.name.toLowerCase().includes(debouncedSearch.toLowerCase())
    )
  }, [debouncedSearch, folders])

  // Clear search query
  const handleClearSearch = useCallback(() => {
    setSearchQuery("")
  }, [])

  return (
    <div className="flex flex-col h-full">
      {/* Search input */}
      <div className="p-2 relative">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search folders..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9 h-9 text-sm bg-gray-50 dark:bg-gray-800/50 focus-visible:ring-blue-500"
          />
          {searchQuery && (
            <button
              onClick={handleClearSearch}
              className="absolute right-2.5 top-2.5"
            >
              <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>
      </div>

      {/* Folders list - Using div with overflow instead of ScrollArea */}
      <div className="flex-1 overflow-auto">
        <div className="p-2 space-y-1">
          {/* All Scenarios */}
          <div
            className={cn(
              "flex items-center px-3 py-2.5 rounded-md transition-all cursor-pointer",
              selectedFolder === 'all'
                ? "bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 shadow-sm"
                : "hover:bg-gray-100 dark:hover:bg-gray-800/70 text-gray-700 dark:text-gray-300"
            )}
            onClick={() => onFolderSelect('all')}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <LayoutGrid className={cn(
                "h-4 w-4 flex-shrink-0",
                selectedFolder === 'all' ? "text-blue-500" : "text-gray-400 dark:text-gray-500"
              )} />
              <span className="font-medium text-sm truncate">All Scenarios</span>
            </div>

            <Badge className="ml-auto text-xs px-1.5 py-0 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-none">
              {allScenariosCount}
            </Badge>
          </div>

          {/* Uncategorized */}
          <div
            className={cn(
              "flex items-center px-3 py-2.5 rounded-md transition-all cursor-pointer",
              selectedFolder === 'uncategorized'
                ? "bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 shadow-sm"
                : "hover:bg-gray-100 dark:hover:bg-gray-800/70 text-gray-700 dark:text-gray-300"
            )}
            onClick={() => onFolderSelect('uncategorized')}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <Inbox className={cn(
                "h-4 w-4 flex-shrink-0",
                selectedFolder === 'uncategorized' ? "text-blue-500" : "text-gray-400 dark:text-gray-500"
              )} />
              <span className="font-medium text-sm truncate">Uncategorized</span>
            </div>

            <Badge className="ml-auto text-xs px-1.5 py-0 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-none">
              {uncategorizedCount}
            </Badge>
          </div>

          {/* Folder heading */}
          {filteredFolders.length > 0 && (
            <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Folders
            </div>
          )}

          {/* Folders */}
          {filteredFolders.map((folder, index) => (
            <div
              key={folder.id}
              className={cn(
                "flex items-center px-3 py-2.5 rounded-md transition-all cursor-pointer",
                selectedFolder === folder.id
                  ? "bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 shadow-sm"
                  : "hover:bg-gray-100 dark:hover:bg-gray-800/70 text-gray-700 dark:text-gray-300"
              )}
              onClick={() => onFolderSelect(folder.id)}
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                {selectedFolder === folder.id ? (
                  <FolderOpen
                    className="h-4 w-4 flex-shrink-0 text-blue-500"
                    style={{ color: folder.color || undefined }}
                  />
                ) : (
                  <FolderIcon
                    className="h-4 w-4 flex-shrink-0 text-gray-400 dark:text-gray-500"
                    style={{ color: folder.color || undefined }}
                  />
                )}
                <span className="font-medium text-sm truncate">{folder.name}</span>
              </div>

              <Badge className="ml-auto text-xs px-1.5 py-0 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-none">
                {folderScenarioCounts[folder.id] || 0}
              </Badge>
            </div>
          ))}

          {/* Empty state for search */}
          {debouncedSearch && filteredFolders.length === 0 && (
            <div className="px-3 py-6 text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">No folders found</p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Try a different search term</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
