/**
 * WebSocket Connection Manager
 * 
 * Handles WebSocket connection management with dependency injection.
 * Replaces the singleton WebSocketConnectionManager with a proper service implementation.
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { BaseService } from '../base/base-service.js';
import { IWebSocketConnectionManager } from '../../interfaces/websocket-connector.interface.js';
import { IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';
import { ConnectionOptions, ConnectionStats } from '../../types/connector-types.js';

/**
 * WebSocket Connection Manager Class
 * Manages WebSocket connections with dependency injection
 */
export class WebSocketConnectionManager extends BaseService implements IWebSocketConnectionManager {
  private ws: WebSocket | null = null;
  private connectionUrl: string = '';
  private connectionOptions: ConnectionOptions = {};
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectInterval = 5000;
  private isReconnecting = false;
  private connectionStats: ConnectionStats = {
    status: 'disconnected',
    uptime: 0,
    reconnectAttempts: 0,
    successfulReconnects: 0,
    messagesSent: 0,
    messagesReceived: 0,
    bytesTransferred: 0,
    averageLatency: 0
  };

  constructor(logger: any, config: IServiceConfig) {
    super('WebSocketConnectionManager', logger, config);
    this.maxReconnectAttempts = config.maxReconnectAttempts || 10;
    this.reconnectInterval = config.reconnectInterval || 5000;
  }

  /**
   * Initialize the connection manager
   * @param config Service configuration
   */
  protected async doInitialize(config: IServiceConfig): Promise<void> {
    this.connectionOptions = {
      timeout: config.timeout || 30000,
      reconnection: {
        enabled: config.autoReconnect !== false,
        maxAttempts: this.maxReconnectAttempts,
        initialDelay: this.reconnectInterval,
        maxDelay: 60000,
        backoffFactor: 1.5
      },
      ...config.connectionOptions
    };

    this.logger.debug('WebSocketConnectionManager: Initialized with options', this.connectionOptions);
  }

  /**
   * Close the connection manager
   */
  protected async doClose(): Promise<void> {
    await this.disconnect();
    this.logger.debug('WebSocketConnectionManager: Closed successfully');
  }

  /**
   * Connect to WebSocket server
   * @param url WebSocket URL
   * @param headers Connection headers
   */
  async connect(url: string, headers?: Record<string, string>): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.connectionUrl = url;
        this.connectionStats.status = 'connecting';
        
        this.logOperation('websocketConnectAttempt', { url });

        const wsOptions: any = {
          headers: headers || {},
          handshakeTimeout: this.connectionOptions.timeout || 30000
        };

        this.ws = new WebSocket(url, wsOptions);

        const connectTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.terminate();
            this.handleError(new Error('Connection timeout'), 'connect');
            reject(new Error('WebSocket connection timeout'));
          }
        }, this.connectionOptions.timeout || 30000);

        this.ws.on('open', () => {
          clearTimeout(connectTimeout);
          this.connectionStats.status = 'connected';
          this.connectionStats.connectedAt = new Date().toISOString();
          this.reconnectAttempts = 0;
          this.isReconnecting = false;

          this.logOperation('websocketConnected', { url });
          this.emit('connected');
          resolve();
        });

        this.ws.on('close', (code: number, reason: Buffer) => {
          clearTimeout(connectTimeout);
          this.connectionStats.status = 'disconnected';
          this.connectionStats.disconnectedAt = new Date().toISOString();

          const reasonString = reason.toString();
          this.logOperation('websocketDisconnected', { code, reason: reasonString });
          this.emit('disconnected', { code, reason: reasonString });

          // Auto-reconnect if enabled and not manually closed
          if (this.connectionOptions.reconnection?.enabled && code !== 1000 && !this.isReconnecting) {
            this.attemptReconnect();
          }
        });

        this.ws.on('message', (data: WebSocket.Data) => {
          this.connectionStats.messagesReceived++;
          this.connectionStats.bytesTransferred += data.toString().length;
          
          this.logOperation('websocketMessageReceived', { 
            size: data.toString().length 
          });
          
          this.emit('message', data);
        });

        this.ws.on('error', (error: Error) => {
          clearTimeout(connectTimeout);
          this.connectionStats.status = 'error';
          
          this.handleError(error, 'websocket connection');
          this.emit('error', error);
          
          if (this.ws?.readyState === WebSocket.CONNECTING) {
            reject(error);
          }
        });

      } catch (error) {
        this.handleError(error, 'connect');
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  async disconnect(): Promise<void> {
    if (this.ws) {
      this.isReconnecting = false;
      this.connectionStats.status = 'disconnected';
      
      this.ws.close(1000, 'Normal closure');
      this.ws = null;
      
      this.logOperation('websocketDisconnected', { manual: true });
    }
  }

  /**
   * Send message
   * @param message Message to send
   */
  async send(message: string): Promise<boolean> {
    if (!this.isConnected()) {
      this.handleError(new Error('WebSocket not connected'), 'send');
      return false;
    }

    try {
      this.ws!.send(message);
      this.connectionStats.messagesSent++;
      this.connectionStats.bytesTransferred += message.length;
      
      this.logOperation('websocketMessageSent', { 
        size: message.length 
      });
      
      return true;
    } catch (error) {
      this.handleError(error, 'send');
      return false;
    }
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection statistics
   */
  getConnectionStats(): ConnectionStats {
    return {
      ...this.connectionStats,
      uptime: this.connectionStats.connectedAt 
        ? Date.now() - new Date(this.connectionStats.connectedAt).getTime()
        : 0
    };
  }

  /**
   * Update pong timestamp
   * @param timestamp Timestamp
   */
  updatePongTimestamp(timestamp: number): void {
    this.connectionStats.lastPongTime = new Date(timestamp).toISOString();
    this.logOperation('websocketPongReceived', { timestamp });
  }

  /**
   * Reset reconnection state
   */
  resetReconnectionState(): void {
    this.reconnectAttempts = 0;
    this.isReconnecting = false;
    this.logOperation('websocketReconnectionStateReset');
  }

  /**
   * Attempt to reconnect
   */
  private async attemptReconnect(): Promise<void> {
    if (this.isReconnecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        this.emit('maxReconnectAttemptsReached');
      }
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;
    this.connectionStats.reconnectAttempts++;

    const delay = Math.min(
      this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1),
      60000
    );

    this.logOperation('websocketReconnectAttempt', { 
      attempt: this.reconnectAttempts,
      delay 
    });

    this.emit('reconnecting', { 
      attempt: this.reconnectAttempts, 
      delay 
    });

    setTimeout(async () => {
      try {
        await this.connect(this.connectionUrl);
        this.connectionStats.successfulReconnects++;
        
        // CRITICAL FIX: Emit reconnected event to trigger registration
        this.emit('reconnected');
        
        // CRITICAL FIX: Also emit connected event to ensure proper initialization
        this.emit('connected');
        
        this.logOperation('websocketReconnectSuccess', { 
          attempt: this.reconnectAttempts,
          totalAttempts: this.connectionStats.reconnectAttempts
        });
      } catch (error) {
        this.handleError(error, 'reconnect');
        this.isReconnecting = false;
        
        // Try again if we haven't reached max attempts
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.attemptReconnect();
        } else {
          this.logOperation('websocketReconnectFailed', { 
            finalAttempt: this.reconnectAttempts,
            totalAttempts: this.connectionStats.reconnectAttempts,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    }, delay);
  }

  /**
   * Get service statistics
   */
  getStats(): IServiceStats {
    return super.getStats();
  }

  /**
   * Get detailed connection statistics
   */
  getDetailedConnectionStats(): Record<string, any> {
    const baseStats = super.getStats();
    const connectionStats = this.getConnectionStats();
    return {
      ...baseStats,
      connectionStats: connectionStats,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      isReconnecting: this.isReconnecting,
      connectionUrl: this.connectionUrl
    };
  }
}
