/**
 * Dashboard API
 *
 * Dashboard ile ilgili API isteklerini yönetir.
 */

import { get } from './fetch-wrapper';
import { DASHBOARD_ENDPOINTS } from './constants';

/**
 * Dashboard istatistiklerini getirir
 *
 * @param timeframe Zaman aralığı (day, week, month)
 */
export async function getDashboardStats(timeframe: 'day' | 'week' | 'month' = 'week') {
  return get(DASHBOARD_ENDPOINTS.STATS, {
    params: { timeframe }
  });
}

/**
 * Son çalıştırılan testleri getirir
 *
 * @param limit Getirilecek test sayısı
 */
export async function getRecentRuns(limit = 5) {
  const response = await get(DASHBOARD_ENDPOINTS.RECENT_RUNS, {
    params: { limit }
  });

  // Backend artık data wrapper kullanıyor, geriye dönük uyumluluk için
  if (response.success && response.data) {
    return {
      ...response,
      runs: response.data.runs || response.runs,
      total: response.data.total || response.total
    };
  }

  return response;
}

/**
 * Son 24 saatteki test aktivitelerini getirir
 */
export async function getTestActivity() {
  const response = await get(DASHBOARD_ENDPOINTS.ACTIVITY);

  // Backend artık data wrapper kullanıyor, geriye dönük uyumluluk için
  if (response.success && response.data) {
    return {
      ...response,
      activity: response.data.activity || response.activity
    };
  }

  return response;
}

/**
 * Son oluşturulan senaryoları getirir
 *
 * @param limit Getirilecek senaryo sayısı
 */
export async function getRecentScenarios(limit = 5) {
  return get(DASHBOARD_ENDPOINTS.RECENT_SCENARIOS, {
    params: { limit }
  });
}


