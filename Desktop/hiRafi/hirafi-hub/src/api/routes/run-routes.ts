/**
 * Run API Routes
 * Handles all test run related endpoints
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { RunStatus } from '../../models/test-types.js';
import { testManager } from '../../core/test-manager/index.js';
import { nodeManager } from '../../core/node-manager/index.js';
import { testQueueService } from '../../services/test/TestQueueService.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { checkPermission } from '../middleware/permissionMiddleware.js';
import { attachRunTeamId, checkRunTeamAccess } from '../middleware/teamAccessMiddleware.js';
import {
  createRun,
  getRunById,
  getRunsByTeamId,
  addReportToRun,
  updateRun,
  deleteRun
} from '../../services/mongo/runService.js';
import { createTestRailRunForRun } from '../../services/mongo/runTestRailService.js';
import {
  createRunReportAtomic,
  getRunReportByRunId,
  updateRunReport,
  updateRunStatusAtomic,
  getRunTestReportsByExecutionId,
  getLatestTestReportsByRunId,
  createRunReport,
  forceResetRunReportStatus,
  getActiveRunReportsByUserId
} from '../../services/mongo/atomicReportService.js';
import { ensureMongoDBConnection, isMongoDBInitialized, db, runsCollection, scenariosCollection } from '../../services/mongo/dbConnection.js';

const router = Router();

/**
 * GET /api/run/active
 * Kullanıcının aktif (running) durumda olan tüm run'larını getirir
 */
router.get('/active', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get active runs'
      });
    }

    // Aktif run'ları getir
    const result = await getActiveRunReportsByUserId(userId, teamId, companyId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to get active runs'
      });
    }

    return res.json({
      success: true,
      runs: result.reports || []
    });
  } catch (error: any) {
    logger.error(`API Error in get active runs: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/run
 * Takımın test run'larını getirir
 */
router.get('/', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini ve takım ID'sini al
    const userId = req.user?.id;
    const teamId = req.user?.teamId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test runs'
      });
    }

    if (!teamId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID is required to get test runs'
      });
    }

    // Paginasyon parametreleri
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const skip = (page - 1) * limit;

    // Filtreleme parametreleri
    const filter: any = {};

    // Opsiyonel olarak belirli bir kullanıcının run'larını filtreleme
    if (req.query.userId) {
      filter.userId = req.query.userId as string;
    }

    // Safely handle status parameter
    if (req.query.status) {
      // Handle comma-separated status values
      if (typeof req.query.status === 'string' && req.query.status.includes(',')) {
        const statusValues = req.query.status.split(',').filter(Boolean);

        // Validate each status value is a valid RunStatus
        const validStatuses = statusValues.filter(status =>
          Object.values(RunStatus).includes(status as RunStatus)
        );

        if (validStatuses.length > 0) {
          filter.status = validStatuses as RunStatus[];
        }
      } else {
        // Single status value
        const statusValue = req.query.status as string;
        if (Object.values(RunStatus).includes(statusValue as RunStatus)) {
          filter.status = statusValue as RunStatus;
        }
      }
    }

    if (req.query.search) {
      filter.search = req.query.search as string;
    }

    if (req.query.startDate) {
      try {
        logger.info(`Processing startDate: ${req.query.startDate}`);
        filter.startDate = new Date(req.query.startDate as string);
        // Validate date is valid
        if (isNaN(filter.startDate.getTime())) {
          logger.warn(`Invalid startDate after parsing: ${req.query.startDate}`);
          delete filter.startDate;
        } else {
          logger.info(`Parsed startDate successfully: ${filter.startDate.toISOString()}`);
        }
      } catch (e) {
        logger.warn(`Error parsing startDate format: ${req.query.startDate}, Error: ${e}`);
      }
    }

    if (req.query.endDate) {
      try {
        logger.info(`Processing endDate: ${req.query.endDate}`);
        filter.endDate = new Date(req.query.endDate as string);
        // Validate date is valid
        if (isNaN(filter.endDate.getTime())) {
          logger.warn(`Invalid endDate after parsing: ${req.query.endDate}`);
          delete filter.endDate;
        } else {
          logger.info(`Parsed endDate successfully: ${filter.endDate.toISOString()}`);
        }
      } catch (e) {
        logger.warn(`Error parsing endDate format: ${req.query.endDate}, Error: ${e}`);
      }
    }

    if (req.query.tags) {
      if (typeof req.query.tags === 'string') {
        filter.tags = req.query.tags.split(',').filter(tag => tag.trim().length > 0);
      }
    }

    // Platform filtresi
    if (req.query.platform) {
      const platformValue = req.query.platform as string;
      if (['web', 'android'].includes(platformValue)) {
        filter.platform = platformValue;
        logger.info(`Adding platform filter: ${platformValue}`);
      }
    }

    // Önceden tanımlanmış tarih filtreleri
    if (req.query.dateFilter) {
      const dateFilterValue = req.query.dateFilter as string;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      switch (dateFilterValue) {
        case 'today':
          filter.startDate = new Date(today);
          filter.endDate = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1);
          logger.info(`Adding today filter: ${filter.startDate.toISOString()} to ${filter.endDate.toISOString()}`);
          break;
        case 'yesterday':
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          filter.startDate = new Date(yesterday);
          filter.endDate = new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1);
          logger.info(`Adding yesterday filter: ${filter.startDate.toISOString()} to ${filter.endDate.toISOString()}`);
          break;
        case 'last7days':
          const last7Days = new Date(today);
          last7Days.setDate(last7Days.getDate() - 7);
          filter.startDate = new Date(last7Days);
          filter.endDate = new Date();
          logger.info(`Adding last 7 days filter: ${filter.startDate.toISOString()} to ${filter.endDate.toISOString()}`);
          break;
        case 'last30days':
          const last30Days = new Date(today);
          last30Days.setDate(last30Days.getDate() - 30);
          filter.startDate = new Date(last30Days);
          filter.endDate = new Date();
          logger.info(`Adding last 30 days filter: ${filter.startDate.toISOString()} to ${filter.endDate.toISOString()}`);
          break;
      }
    }

    // Sorting parametreleri
    let sortOptions: any = { createdAt: -1 }; // Default sort
    if (req.query.sortBy) {
      const sortBy = req.query.sortBy as string;
      const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

      switch (sortBy) {
        case 'priority':
          // Priority sorting: running tests first, then by most recent execution time
          sortOptions = { priority: true };
          break;
        case 'date':
          sortOptions = { createdAt: sortOrder };
          break;
        case 'name':
          sortOptions = { name: sortOrder };
          break;
        case 'status':
          sortOptions = { status: sortOrder };
          break;
        default:
          // Default to priority sorting for better user experience
          sortOptions = { priority: true };
      }
      logger.debug(`Adding sort options: ${JSON.stringify(sortOptions)}`);
    } else {
      // When no sort is specified, use priority sorting by default
      sortOptions = { priority: true };
      logger.debug(`Using default priority sorting`);
    }

    // Run'ları takım ID'sine göre getir - sorting parametreleri ile
    const result = await getRunsByTeamId(teamId, limit, skip, filter, sortOptions);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to get runs'
      });
    }

    return res.json({
      success: true,
      runs: result.runs || [],
      total: result.total || 0,
      limit,
      skip
    });
  } catch (error: any) {
    logger.error(`API Error in get runs: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/run/:runId
 * Belirli bir run'ı getirir
 */
router.get('/:runId', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test run'
      });
    }

    const { runId } = req.params;

    // Faz 5: Artık tüm durum bilgileri veritabanından geliyor
    // MongoDB'den run verilerini al - getRunById fonksiyonu zaten run raporlarından durum bilgilerini alıyor
    const result = await getRunById(runId);

    if (!result.success || !result.run) {
      return res.status(404).json({
        success: false,
        error: result.message || 'Run not found'
      });
    }

    // Type-safety için MongoDB'den gelen verileri doğrula
    const mongoDbRun = result.run;

    // Doğrudan veritabanından gelen run bilgilerini kullan
    let runData = { ...mongoDbRun };

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını görebilir
    // Takım ID kontrolü yap
    if (runData.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view runs from your team'
      });
    }

    return res.json({
      success: true,
      data: {
        run: runData
      }
    });
  } catch (error: any) {
    logger.error(`API Error in get run: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/run
 * Yeni bir test run oluşturur
 */
router.post('/', authenticate, checkPermission('Run', 'create'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to create test run'
      });
    }

    const {
      name,
      description,
      scenarioIds,
      scenarios, // Eklendi: Alternatif alan adı
      platform, // Platform field (web or android)
      environment,
      reportSettings,
      tags,
      testDataEnvironmentId,
      testDataEnvironmentName
    } = req.body;

    // Log platform value for debugging
    logger.info(`Received run creation request with platform: ${platform || 'web'}`);

    // Log detailed environment settings for debugging
    if (environment) {
      logger.info(`Received environment settings: ${JSON.stringify(environment, null, 2)}`);

      // Log Android-specific settings if platform is android
      if (platform === 'android' && environment.sauceLabs) {
        logger.info(`Android SauceLabs settings: ${JSON.stringify(environment.sauceLabs, null, 2)}`);

        // Log device selection details
        if (environment.sauceLabs.selectedDevices) {
          logger.info(`Selected devices count: ${environment.sauceLabs.selectedDevices.length}`);
        }

        // Log test distribution strategy if available
        if (environment.testDistribution) {
          logger.info(`Test distribution strategy: ${environment.testDistribution.strategy}`);
        }
      }
    }

    // Log report settings
    if (reportSettings) {
      logger.info(`Received report settings: ${JSON.stringify(reportSettings, null, 2)}`);

      // Verify that web-specific settings are not included for Android platform
      if (platform === 'android') {
        // Check for web-specific fields that should not be present
        const webSpecificFields = ['pageMetrics', 'networkData', 'tracingData', 'accessibilityData'];
        const includedWebFields = webSpecificFields.filter(field => field in reportSettings);

        if (includedWebFields.length > 0) {
          logger.warn(`Warning: Android platform contains web-specific report settings: ${includedWebFields.join(', ')}`);
        } else {
          logger.info('Verified: No web-specific report settings included for Android platform');
        }
      }
    }

    // Hem scenarioIds hem de scenarios alanlarını kontrol et
    const scenarioIdsToUse = scenarioIds || scenarios;

    if (!scenarioIdsToUse || !Array.isArray(scenarioIdsToUse) || scenarioIdsToUse.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'At least one scenario ID is required'
      });
    }

    // Kullanıcının şirket ve takım bilgilerini al
    const companyId = req.user?.companyId || null;
    const teamId = req.user?.teamId || null;

    // Eğer companyId veya teamId yoksa hata döndür
    if (!companyId || !teamId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID and Team ID are required to create a run'
      });
    }

    // Run oluştur
    const runData = {
      name,
      description,
      userId,
      scenarioIds: scenarioIdsToUse, // Güncellenmiş değişkeni kullan
      platform: platform || 'web', // Ensure platform is explicitly set with default
      environment,
      reportSettings,
      tags,
      status: RunStatus.CREATED,
      companyId,
      teamId,
      testDataEnvironmentId,
      testDataEnvironmentName
    };

    // Log the final run data being sent to createRun
    logger.info(`Creating run with data: ${JSON.stringify({
      ...runData,
      platform: runData.platform // Explicitly log platform
    })}`);

    const result = await createRun(runData);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to create run'
      });
    }

    return res.status(201).json({
      success: true,
      data: {
        runId: result.runId,
        message: 'Test run created successfully'
      }
    });
  } catch (error: any) {
    logger.error(`API Error in create run: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * PUT /api/run/:runId
 * Bir run'ı günceller
 */
router.put('/:runId', authenticate, checkPermission('Run', 'update'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to update test run'
      });
    }

    const { runId } = req.params;



    // Run'ı kontrol et
    const runResult = await getRunById(runId);

    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını güncelleyebilir
    // Takım ID kontrolü yap
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only update runs from your team'
      });
    }

    // Güncelleme bilgilerini al
    const updateData = req.body;



    // Run'ı güncelle
    const result = await updateRun(runId, updateData);

    if (!result.success) {

      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to update run'
      });
    }


    return res.json({
      success: true,
      message: 'Run updated successfully'
    });
  } catch (error: any) {
    logger.error(`API Error in update run: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/run/:runId
 * Bir run'ı siler
 */
router.delete('/:runId', authenticate, checkPermission('Run', 'delete'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to delete test run'
      });
    }

    const { runId } = req.params;

    // Run'ı kontrol et
    const runResult = await getRunById(runId);

    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını silebilir
    // Takım ID kontrolü yap
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only delete runs from your team'
      });
    }

    // Run'ı sil
    const result = await deleteRun(runId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to delete run'
      });
    }

    return res.json({
      success: true,
      message: 'Run deleted successfully'
    });
  } catch (error: any) {
    logger.error(`API Error in delete run: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/run/:runId/execute
 * Belirli bir Run'ı başlatır
 */
router.post('/:runId/execute', authenticate, checkPermission('Run', 'update'), async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { runId } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to execute run'
      });
    }

    // RunCoordinator kullanarak run başlatma işlemini gerçekleştir

    // Run bilgilerini al
    const runResult = await getRunById(runId);
    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    const run = runResult.run;

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını execute edebilir
    // Takım ID kontrolü yap
    if (run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      logger.warn(`[RUN_EXECUTE] User ${req.user?.id} (team: ${req.user?.teamId}) attempted to execute run ${runId} from team ${run.teamId}`);
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to execute this run'
      });
    }

    logger.info(`[RUN_EXECUTE] User ${req.user?.id} executing run ${runId} from team ${run.teamId}`);

    // TestRail run oluştur (eğer takım ve şirket bilgileri varsa) - asenkron olarak
    if (run.teamId && run.companyId) {
      try {
        // Önce senaryoları kontrol et - herhangi bir TestRail entegrasyonu var mı?
        if (db && scenariosCollection) {
          const hasTestRailIntegration = await scenariosCollection.findOne({
            id: { $in: run.scenarioIds || [] },
            "testrailIntegration.sync": true,
            "testrailIntegration.caseIds": { $exists: true, $ne: [] }
          });

          if (hasTestRailIntegration) {
            // TestRail entegrasyonu olan en az bir senaryo var, TestRail run oluştur


            // Asenkron olarak TestRail run oluştur, await kullanmadan
            // Bu şekilde run başlatma işlemi TestRail run oluşturma işlemini beklemez
            createTestRailRunForRun(
              runId,
              run.teamId,
              run.companyId,
              run.name || `Run ${runId}`
            )
              .then(testrailResult => {
                if (testrailResult.success) {
                  logger.info(`API: TestRail run created for run ${runId} with ID ${testrailResult.runId}`);
                } else {
                  // TestRail run oluşturma başarısız olursa sadece log al, run'ı engelleme
                  logger.warn(`API: Failed to create TestRail run for run ${runId}: ${testrailResult.message}`);
                }
              })
              .catch(error => {
                // TestRail run oluşturma hatası olursa sadece log al, run'ı engelleme
                logger.error(`API: Error creating TestRail run for run ${runId}: ${error.message}`);
                if (error.stack) {
                  logger.error(`API: Error stack trace: ${error.stack}`);
                }
              });

            // TestRail run oluşturma işlemi başlatıldı mesajı
            logger.info(`API: TestRail run creation initiated for run ${runId} in the background`);
          } else {
            logger.info(`API: No scenarios with TestRail integration found for run ${runId}, skipping TestRail run creation`);
          }
        } else {
          logger.warn(`API: Database connection not established, skipping TestRail integration check`);
        }
      } catch (error: any) {
        logger.error(`API: Error checking TestRail integration for run ${runId}: ${error.message}`);
      }
    }

    // TestManager üzerinden RunCoordinator'a eriş
    const runCoordinator = testManager.getRunCoordinator();
    const result = await runCoordinator.initiateRun(runId, userId);

    if (!result.success) {
      logger.error(`API: Failed to initiate run ${runId}: ${result.error}`);

      // Şirketin kalan koşum dakikası kalmadığında 403 döndür
      if (result.error?.includes('kalan koşum dakikası')) {
        return res.status(403).json({
          success: false,
          error: result.error,
          executionId: result.executionId,
          scenarioErrors: result.scenarioErrors
        });
      }

      return res.status(result.error?.includes('not found') ? 404 : 500).json({
        success: false,
        error: result.error,
        executionId: result.executionId,
        scenarioErrors: result.scenarioErrors
      });
    }



    return res.json({
      success: true,
      runId,
      executionId: result.executionId,
      testsCreated: result.testIds?.length || 0,
      totalScenarios: result.testIds?.length || 0 + (result.scenarioErrors?.length || 0),
      testIds: result.testIds,
      scenarioErrors: result.scenarioErrors,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in execute run: ${error.message}`, {
      error,
      stack: error.stack,
      runId: req.params.runId,
      userId: req.user?.id
    });

    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/run/:runId/stop
 * Stop a specific run with complete cleanup workflow
 */
router.post('/:runId/stop', authenticate, checkPermission('Run', 'update'), async (req: AuthRequest, res: Response) => {
  try {
    const { runId } = req.params;
    logger.info(`Run Stop API: Starting complete stop workflow for run ${runId}`);

    // Get the run from the database first
    const runResult = await getRunById(runId);
    if (!runResult.success || !runResult.run) {
      return res.status(404).json({ success: false, error: 'Run not found' });
    }

    // CRITICAL FIX: Get tests from both MongoDB AND BullMQ queue to ensure we catch all active tests
    logger.info(`Run Stop API: Checking for active tests in run ${runId} from multiple sources`);

    // 1. Get tests from MongoDB
    const allTestsFromDB = await testManager.getAllTests();
    const testsFromDB = allTestsFromDB.filter((test: any) =>
      test.runId === runId && (test.status === 'queued' || test.status === 'running')
    );
    logger.info(`Run Stop API: Found ${testsFromDB.length} active tests in MongoDB for run ${runId}`);

    // 2. Get active tests from BullMQ queue (these are the ones actually running on nodes)
    const activeTestsFromQueue = await testManager.getRunningTests();
    const queueTestsForRun = activeTestsFromQueue.filter((test: any) => test.runId === runId);
    logger.info(`Run Stop API: Found ${queueTestsForRun.length} active tests in BullMQ queue for run ${runId}`);

    // 3. Combine both sources and deduplicate
    const allTestIds = new Set([
      ...testsFromDB.map(t => t.id),
      ...queueTestsForRun.map(t => t.id)
    ]);
    const testsToStop = Array.from(allTestIds).map(testId => {
      return testsFromDB.find(t => t.id === testId) || queueTestsForRun.find(t => t.id === testId);
    }).filter(Boolean);

    logger.info(`Run Stop API: Total unique tests to stop for run ${runId}: ${testsToStop.length}`);

    if (testsToStop.length === 0) {
      // No active tests to stop, but still update run status
      logger.info(`Run Stop API: No active tests found for run ${runId} in either MongoDB or BullMQ queue, updating run status to stopped`);

      try {
        // Import the atomic report service dynamically
        const { getRunReportByRunId, updateRunStatusAtomic } = await import('../../services/mongo/atomicReportService.js');

        // Use atomic status update to prevent race conditions
        const reportResult = await getRunReportByRunId(runId);

        if (reportResult.success && reportResult.report) {
          // Use atomic status update instead of direct updateRunReport
          const atomicUpdateResult = await updateRunStatusAtomic(runId, reportResult.report.executionId, 'stopped', {
            source: 'API-Stop-Request',
            noStartTime: true // Don't update start time when stopping
          });

          if (atomicUpdateResult.success) {
            logger.info(`Run Stop API: Atomically updated run report status to STOPPED for run ${runId}`);
          } else {
            logger.warn(`Run Stop API: Failed to atomically update run report status: ${atomicUpdateResult.message}`);
          }
        } else {
          logger.warn(`Run Stop API: Could not find run report for run ${runId}: ${reportResult.message}`);
        }

        // Also update the run status in the runs collection
        const { updateRun } = await import('../../services/mongo/runService.js');
        await updateRun(runId, {
          status: RunStatus.STOPPED,
          completedAt: new Date()
        });

      } catch (updateError) {
        logger.error(`Run Stop API: Error updating run ${runId} status: ${updateError}`);
      }

      return res.json({
        success: true,
        message: 'No active tests to stop, run marked as stopped',
        stoppedCount: 0,
        totalTests: 0
      });
    }

    // Check user permissions - team members can stop all team runs
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only stop runs from your team'
      });
    }

    // COMPREHENSIVE STOP WORKFLOW: Use TestManager's comprehensive stop method
    logger.info(`Run Stop API: Starting comprehensive stop workflow for run ${runId}`);

    const stopResult = await testManager.stopAllTestsForRun(runId);
    const { stoppedTests, failedToStop, queueCleanupCount } = stopResult;

    logger.info(`Run Stop API: Comprehensive stop completed. Stopped: ${stoppedTests.length}, Failed: ${failedToStop.length}, Queue cleanup: ${queueCleanupCount}`);

    // Note: Individual test cleanup (Redis locks, step progress) is handled by TestManager.stopAllTestsForRun

    // Update run status and scenarios
    try {
      // Import the atomic report service dynamically
      const { getRunReportByRunId, updateRunStatusAtomic } = await import('../../services/mongo/atomicReportService.js');

      const reportResult = await getRunReportByRunId(runId);

      if (reportResult.success && reportResult.report) {
        const report = reportResult.report;
        const executionId = report.executionId;

        // Update all unfinished scenarios to 'stopped' status
        if (report.scenarioStatuses && report.scenarioStatuses.length > 0) {
          const { updateScenarioStatusAtomic } = await import('../../services/mongo/atomicReportService.js');

          // Get scenarios that are not in a final state (running or queued)
          const unfinishedScenarios = report.scenarioStatuses.filter(
            (s: any) => s.status === 'running' || s.status === 'queued'
          );



          // Update each unfinished scenario to 'stopped' status
          for (const scenario of unfinishedScenarios) {
            try {
              await updateScenarioStatusAtomic(
                runId,
                executionId,
                scenario.scenarioId,
                'stopped',
                {
                  testId: scenario.testId,
                  completionTime: new Date(),
                  source: 'RunStop-API'
                }
              );
              logger.info(`Run Stop API: Updated scenario ${scenario.scenarioId} status to stopped`);
            } catch (err) {
              logger.error(`Run Stop API: Error updating scenario ${scenario.scenarioId} status: ${err}`);
            }
          }
        }

        // Use atomic status update instead of direct updateRunReport
        const atomicUpdateResult = await updateRunStatusAtomic(runId, report.executionId, 'stopped', {
          source: 'API-Stop-Request-Fallback',
          noStartTime: true // Don't update start time when stopping
        });

        if (atomicUpdateResult.success) {
          logger.info(`Run Stop API: Atomically updated run report ${report.id} status to STOPPED`);
        } else {
          logger.warn(`Run Stop API: Failed to atomically update run report status: ${atomicUpdateResult.message}`);
        }

        // Also update the run status in the runs collection
        const { updateRun } = await import('../../services/mongo/runService.js');
        await updateRun(runId, {
          status: RunStatus.STOPPED,
          completedAt: new Date()
        });
        logger.info(`Run Stop API: Updated run ${runId} status to STOPPED in runs collection`);

        // Note: Redis queue cleanup and step progress cleanup are already handled by TestManager.stopAllTestsForRun
        logger.info(`Run Stop API: Redis queue and step progress cleanup completed by comprehensive stop workflow`);
      }


    } catch (error: any) {
      logger.error(`Run Stop API: Error updating run status after stop: ${error.message}`);
    }

    // Get final run status
    const updatedRunResult = await getRunById(runId);

    // Return successful response
    return res.json({
      success: true,
      message: `Run ${runId} stopped successfully. Stopped ${stoppedTests.length} tests, cleaned up ${queueCleanupCount} queue entries.`,
      run: updatedRunResult.success ? updatedRunResult.run : null,
      stoppedTests,
      failedToStop: failedToStop.length > 0 ? failedToStop : undefined,
      totalStopped: stoppedTests.length,
      totalFailed: failedToStop.length,
      queueCleanupCount
    });
  } catch (error: any) {
    logger.error(`API Error in stop run: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/run/:runId/report/:testId
 * Test raporunu run'a ekler
 */
router.post('/:runId/report/:testId', authenticate, checkPermission('Run', 'update'), async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const { runId, testId } = req.params;
    const { reportId } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to add report to run'
      });
    }

    if (!reportId) {
      return res.status(400).json({
        success: false,
        error: 'Report ID is required'
      });
    }

    // Run'ı kontrol et
    const runResult = await getRunById(runId);

    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını güncelleyebilir
    // Takım ID kontrolü yap
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only update runs from your team'
      });
    }

    // Raporu run'a ekle
    const result = await addReportToRun(runId, reportId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to add report to run'
      });
    }

    // Artık testResults alanını kullanmıyoruz, bunun yerine scenarioStatuses'ı güncelleyeceğiz
    // Bu kod bloğu kaldırıldı, çünkü aşağıdaki blokta scenarioStatuses güncellemesi yapılıyor

      // Artık testResults alanını kullanmıyoruz, bunun yerine scenarioStatuses'ı güncelliyoruz
    try {
      const reportResult = await getRunReportByRunId(runId);

      if (reportResult.success && reportResult.report) {
        // Senaryo durumunu güncelle
        const { updateScenarioStatusAtomic } = await import('../../services/mongo/atomicReportService.js');

        // Test bilgilerini al
        const { getTestById } = await import('../../services/mongo/testService.js');
        const testResult = await getTestById(testId);

        if (testResult.success && testResult.test) {
          const test = testResult.test;
          const scenarioId = test.scenarioId;
          const executionId = reportResult.report.executionId;

          if (scenarioId && executionId) {
            // Test durumunu senaryo durumuna dönüştür
            let scenarioStatus = 'queued';
            if (test.status === 'completed') scenarioStatus = 'passed';
            else if (test.status === 'failed') scenarioStatus = 'failed';
            else if (test.status === 'stopped') scenarioStatus = 'stopped';
            else if (test.status === 'running') scenarioStatus = 'running';

            // Senaryo durumunu güncelle
            await updateScenarioStatusAtomic(runId, executionId, scenarioId, scenarioStatus as any, {
              testId: testId,
              completionTime: test.status === 'completed' || test.status === 'failed' || test.status === 'stopped' ?
                              new Date() : undefined,
              source: 'API-Report-Add'
            });

            logger.info(`Updated scenario status for run ${runId}, scenario ${scenarioId} to ${scenarioStatus}`);
          } else {
            logger.warn(`Cannot update scenario status: missing scenarioId or executionId for test ${testId}`);
          }
        }
      }
    } catch (error: any) {
      logger.error(`Error updating scenario status: ${error.message}`);
    }

    return res.json({
      success: true,
      message: 'Report added to run successfully'
    });
  } catch (error: any) {
    logger.error(`API Error in add report to run: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/run/:runId/tests
 * Run'a ait testleri getirir
 */
router.get('/:runId/tests', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get run tests'
      });
    }

    const { runId } = req.params;

    // Run'ı kontrol et
    const runResult = await getRunById(runId);

    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını görebilir
    // Takım ID kontrolü yap
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view runs from your team'
      });
    }

    // Get tests from MongoDB instead of memory
    try {
      const { getTestsByRunId } = await import('../../services/mongo/testService.js');
      const testsResult = await getTestsByRunId(runId);

      if (!testsResult.success || !testsResult.tests || testsResult.tests.length === 0) {
        return res.json({
          success: true,
          tests: []
        });
      }

      // Map test information
      const tests = testsResult.tests.map(test => {
        return {
          id: test.id,
          scenarioId: test.scenarioId,
          scenarioName: test.scenarioName,
          status: test.status,
          queuedAt: test.queuedAt,
          startedAt: test.startedAt,
          completedAt: test.completedAt,
          nodeId: test.nodeId,
          result: test.result,
          userId: test.userId
        };
      });

      return res.json({
        success: true,
        runId,
        tests
      });
    } catch (error) {
      logger.error(`Error getting tests for run ${runId} from MongoDB: ${error}`);

      // Fallback to memory if MongoDB fails
      const allTests = await testManager.getAllTests();
      const testsForRun = allTests.filter((test: any) => test.runId === runId);

      if (testsForRun.length === 0) {
        return res.json({
          success: true,
          tests: []
        });
      }

      // Map test information
      const tests = testsForRun.map((test: any) => {
        return {
          id: test.id,
          scenarioId: test.scenarioId,
          scenarioName: test.scenarioName,
          status: test.status,
          queuedAt: test.queuedAt,
          startedAt: test.startedAt,
          completedAt: test.completedAt,
          nodeId: test.nodeId,
          result: test.result,
          userId: test.userId
        };
      });

      return res.json({
        success: true,
        runId,
        tests
      });
    }

    // TypeScript için gerekli olan kod kaldırıldı
  } catch (error: any) {
    logger.error(`API Error in get run tests: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/run/:runId/status
 * Run durumunu döndürür - sadece run çalışıyorsa memory'den, değilse DB'den okur
 */
router.get('/:runId/status', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get run status'
      });
    }

    const { runId } = req.params;

    // Önce veritabanından run bilgilerini al
    const runResult = await getRunById(runId);

    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını görebilir
    // Takım ID kontrolü yap
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view runs from your team'
      });
    }

    // Veritabanından gelen run verisi - bu veri artık run raporlarından güncellenmiş durumu içeriyor
    const dbRun: any = runResult.run;

    // Faz 5: Artık tüm durum bilgileri veritabanından geliyor
    // getRunById fonksiyonu zaten run raporlarından güncel durum bilgilerini alıyor

    // Run report bilgilerini daha detaylı almak için
    try {
      // Run report bilgilerini al
      const reportResult = await getRunReportByRunId(runId);

      if (reportResult.success && reportResult.report) {
        // Import the calculateTestResults function
        const { calculateTestResults } = await import('../../models/test-types.js');

        // Senaryo durumlarını ekle
        if (reportResult.report.scenarioStatuses && reportResult.report.scenarioStatuses.length > 0) {
          dbRun.scenarioStatuses = reportResult.report.scenarioStatuses;

          // Test sonuçlarını hesapla
          // Create a temporary Run object with the required fields
          const tempRun = {
            id: dbRun.id,
            name: dbRun.name,
            status: dbRun.status,
            createdAt: dbRun.createdAt,
            userId: dbRun.userId,
            scenarioIds: dbRun.scenarioIds,
            scenarioStatuses: reportResult.report.scenarioStatuses
          };

          // Hesaplanmış test sonuçlarını ekle
          dbRun.testResults = calculateTestResults(tempRun);

          logger.debug(`Run ${runId}: Added scenarioStatuses (${reportResult.report.scenarioStatuses.length}) and calculated testResults`);
        } else if (reportResult.report.testResults) {
          // Eğer scenarioStatuses yoksa ama testResults varsa, onu kullan
          dbRun.testResults = reportResult.report.testResults;
          logger.debug(`Run ${runId}: No scenarioStatuses found, using existing testResults`);
        }

        // Execution ID'yi ekle
        if (reportResult.report.executionId) {
          dbRun.executionId = reportResult.report.executionId;
        }
      }
    } catch (error) {
      logger.warn(`Error getting detailed run report data: ${error}`);
      // Hata durumunda mevcut run verisini kullan
    }

    // Run çalışmıyorsa veya memory'de değilse doğrudan veritabanı verisini döndür
    // Bu veri zaten getRunById fonksiyonu içinde run raporlarından güncellenmiş durumu içeriyor
    logger.debug(`Using database status (with run report data) for run ${runId}`);

    return res.json({
      success: true,
      run: dbRun
    });
  } catch (error: any) {
    logger.error(`API Error in get run status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});



/**
 * GET /api/run/:runId/detail
 * Optimize edilmiş run detay sayfası için sadece gerekli verileri içeren tek endpoint
 * Run bilgileri, test listesi, rapor verileri ve step progress bilgilerini tek bir istekte döndürür
 * Test adımları gibi detay veriler filtrelenir
 */
router.get('/:runId/detail', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get run details'
      });
    }

    const { runId } = req.params;

    // MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not available'
      });
    }

    // Aggregation ile run ve en son run report bilgilerini birlikte getir
    if (!runsCollection) {
      return res.status(500).json({
        success: false,
        error: 'Runs collection not available'
      });
    }

    // Önce run'ı kontrol et
    const runDoc = await runsCollection.findOne({ id: runId });

    if (!runDoc) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını görebilir
    // Takım ID kontrolü yap
    if (runDoc.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view runs from your team'
      });
    }

    // Aggregation pipeline oluştur - run ve en son run report'u birleştir
    const aggregationPipeline = [
      // Run'ı bul
      { $match: { id: runId } },

      // Run reports koleksiyonundan en son raporu getir
      {
        $lookup: {
          from: 'run_reports',
          let: { runId: '$id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$runId', '$$runId'] } } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 }
          ],
          as: 'latestRunReport'
        }
      },

      // En son run report'u düzleştir
      {
        $addFields: {
          latestRunReport: { $arrayElemAt: ['$latestRunReport', 0] }
        }
      }
    ];

    // Aggregation'ı çalıştır
    const aggregationResult = await runsCollection.aggregate(aggregationPipeline).toArray();

    if (!aggregationResult.length) {
      return res.status(404).json({
        success: false,
        error: 'Run not found after aggregation'
      });
    }

    // Aggregation sonucundan run ve en son run report bilgilerini al
    const runWithReport = aggregationResult[0];
    const run = { ...runWithReport };
    const latestRunReport = runWithReport.latestRunReport;

    // Run report bilgilerini run nesnesine ekle
    if (latestRunReport) {
      run.status = latestRunReport.status || run.status;

      // Import the calculateTestResults function
      const { calculateTestResults } = await import('../../models/test-types.js');

      // TRUTH OF SOURCE: Always calculate testResults from fresh scenarioStatuses
      // This ensures stopped runs show correct testResults immediately
      if (latestRunReport.scenarioStatuses && latestRunReport.scenarioStatuses.length > 0) {
        // Create a temporary Run object with the required fields
        const tempRun = {
          id: run.id,
          name: run.name,
          status: run.status,
          createdAt: run.createdAt,
          userId: run.userId,
          scenarioIds: run.scenarioIds,
          scenarioStatuses: latestRunReport.scenarioStatuses
        };

        // Always recalculate testResults from current scenarioStatuses
        run.testResults = calculateTestResults(tempRun);

        logger.debug(`Run ${run.id}: Calculated testResults from ${latestRunReport.scenarioStatuses.length} scenarioStatuses - running: ${run.testResults.running}, stopped: ${run.testResults.stopped}, completed: ${run.testResults.completed}`);
      } else {
        // Fallback: use stored testResults if no scenarioStatuses available
        run.testResults = latestRunReport.testResults || run.testResults;
        logger.debug(`Run ${run.id}: Using stored testResults (no scenarioStatuses available)`);
      }

      // Senaryo durumlarını da ekle
      run.scenarioStatuses = latestRunReport.scenarioStatuses;
      run.latestRunReportId = latestRunReport.id; // Run report ID'sini ekle
      run.latestExecutionId = latestRunReport.executionId; // Execution ID'yi ekle
    }

    // Gereksiz alanları temizle
    delete run.latestRunReport;

    // Test listesini MongoDB'den al
    let tests: any[] = [];

    try {
      const { getTestsByRunId } = await import('../../services/mongo/testService.js');
      const testsResult = await getTestsByRunId(runId);

      if (testsResult.success && testsResult.tests && testsResult.tests.length > 0) {
        // Test sonuçları - detay bilgiler filtrelenerek
        tests = testsResult.tests.map(test => {
          // Adım sayısı bilgilerini hesapla
          let totalSteps = 0;
          let completedSteps = 0;

          if (test.result && test.result.steps && Array.isArray(test.result.steps)) {
            totalSteps = test.result.steps.length;
            completedSteps = test.result.steps.filter((step: any) => step.success === true).length;
          }

          // Basitleştirilmiş test sonucu - adım detayları olmadan
          const simpleTestResult = test.result ? {
            id: test.result.id,
            scenarioId: test.result.scenarioId,
            scenarioName: test.result.scenarioName,
            name: test.result.name,
            url: test.result.url,
            date: test.result.date,
            status: test.result.status,
            success: test.result.success,
            duration: test.result.duration,
            summary: test.result.summary,
            error: test.result.error,
            videoUrl: test.result.videoUrl,
            // Step özet bilgileri ekliyoruz
            stepsCount: totalSteps,
            completedStepsCount: completedSteps
          } : null;

          return {
            id: test.id,
            scenarioId: test.scenarioId,
            scenarioName: test.scenarioName,
            status: test.status,
            queuedAt: test.queuedAt,
            startedAt: test.startedAt,
            completedAt: test.completedAt,
            nodeId: test.nodeId,
            result: simpleTestResult,
            userId: test.userId
          };
        });
      } else {
        logger.info(`No tests found in MongoDB for run ${runId}, using empty array`);
      }
    } catch (error) {
      logger.error(`Error getting tests for run ${runId} from MongoDB: ${error}`);

      // Fallback to memory if MongoDB fails
      const allTests = await testManager.getAllTests();
      const testsForRun = allTests.filter((test: any) => test.runId === runId);

      if (testsForRun.length > 0) {
        // Test sonuçları - detay bilgiler filtrelenerek
        tests = testsForRun.map((test: any) => {
          // Adım sayısı bilgilerini hesapla
          let totalSteps = 0;
          let completedSteps = 0;

          if (test.result && test.result.steps && Array.isArray(test.result.steps)) {
            totalSteps = test.result.steps.length;
            completedSteps = test.result.steps.filter((step: any) => step.success === true).length;
          }

          // Basitleştirilmiş test sonucu - adım detayları olmadan
          const simpleTestResult = test.result ? {
            id: test.result.id,
            scenarioId: test.result.scenarioId,
            scenarioName: test.result.scenarioName,
            name: test.result.name,
            url: test.result.url,
            date: test.result.date,
            status: test.result.status,
            success: test.result.success,
            duration: test.result.duration,
            summary: test.result.summary,
            error: test.result.error,
            videoUrl: test.result.videoUrl,
            // Step özet bilgileri ekliyoruz
            stepsCount: totalSteps,
            completedStepsCount: completedSteps
          } : null;

          return {
            id: test.id,
            scenarioId: test.scenarioId,
            scenarioName: test.scenarioName,
            status: test.status,
            queuedAt: test.queuedAt,
            startedAt: test.startedAt,
            completedAt: test.completedAt,
            nodeId: test.nodeId,
            result: simpleTestResult,
            userId: test.userId
          };
        });
      }
    }

    // Step progress bilgilerini al
    let stepProgressSummary: any = null;

    try {
      const { stepProgressService } = await import('../../services/step-progress/stepProgressService.js');

      // Sadece aktif run'lar için step progress bilgilerini al
      if (run.status === 'running' || run.status === 'queued') {
        const stepProgressData = await stepProgressService.getRunStepProgress(runId) || [];

        // Scenario ID'lerini topla ve scenario isimlerini al
        const scenarioIds = [...new Set(stepProgressData.map((p: any) => p.scenarioId).filter(Boolean))];
        const scenarioNameMap = new Map<string, string>();

        if (scenarioIds.length > 0) {
          try {
            const { getScenarioById } = await import('../../services/mongo/scenarioService.js');

            // Her scenario ID için isim al
            for (const scenarioId of scenarioIds) {
              const scenarioResult = await getScenarioById(scenarioId as string);
              if (scenarioResult.success && scenarioResult.scenario) {
                scenarioNameMap.set(scenarioId as string, scenarioResult.scenario.name || 'Unnamed Scenario');
              } else {
                scenarioNameMap.set(scenarioId as string, 'Unnamed Scenario');
              }
            }
          } catch (scenarioError) {
            logger.warn(`Could not get scenario names for run ${runId}: ${scenarioError}`);
          }
        }

        // Summary hesapla ve senaryo isimlerini ekle
        if (stepProgressData.length > 0) {
          stepProgressSummary = {
            totalTests: stepProgressData.length,
            testsInProgress: stepProgressData.filter((p: any) => p.currentStepStatus === 'started').length,
            testsCompleted: stepProgressData.filter((p: any) => p.currentStepStatus === 'completed').length,
            testsFailed: stepProgressData.filter((p: any) => p.currentStepStatus === 'failed').length,
            averageProgress: stepProgressData.reduce((sum: number, p: any) => sum + (p.totalSteps > 0 ? (p.currentStep / p.totalSteps) * 100 : 0), 0) / stepProgressData.length,
            tests: stepProgressData.map((p: any) => ({
              testId: p.testId,
              scenarioId: p.scenarioId,
              scenarioName: scenarioNameMap.get(p.scenarioId) || 'Unnamed Scenario',
              currentStep: p.currentStep,
              totalSteps: p.totalSteps,
              currentStepName: p.currentStepName,
              currentStepType: p.currentStepType,
              currentStepStatus: p.currentStepStatus,
              progress: p.totalSteps > 0 ? (p.currentStep / p.totalSteps) * 100 : 0,
              lastUpdated: p.lastUpdated,
              platform: p.platform,
              runId: p.runId,
              executionId: p.executionId,
              steps: p.steps || []
            }))
          };
        }
      }
    } catch (stepProgressError) {
      logger.warn(`Could not get step progress for run ${runId}: ${stepProgressError}`);
    }

    // Yanıt verisi için interface tanımı
    interface RunDetailResponse {
      success: boolean;
      run: any;
      tests: any[];
      reports?: any[];
      runReportId?: string;
      stepProgressSummary?: any;
    }

    // Yanıt verisi
    const responseData: RunDetailResponse = {
      success: true,
      run,
      tests,
      stepProgressSummary
    };

    // Run report ID'sini ekle
    if (latestRunReport) {
      responseData.runReportId = latestRunReport.id;
    }

    // Tüm run durumları için rapor bilgilerini ekle (running dahil)
    // Running run'lar için de raporları göstermek gerekiyor
    if (run.status === RunStatus.RUNNING || run.status === RunStatus.QUEUED || run.status === RunStatus.COMPLETED || run.status === RunStatus.FAILED || run.status === RunStatus.STOPPED || run.status === RunStatus.PARTIAL) {
      // Önce en son execution ID'yi belirle - latestRunReport'tan al
      let executionId = latestRunReport?.executionId;

      // Eğer latestRunReport varsa, doğrudan onun executionId'sini kullan
      if (executionId) {
        logger.info(`Run ${runId}: Using executionId ${executionId} from latest run report`);

        // Bu executionId ile test raporlarını getir
        const reportsResult = await getRunTestReportsByExecutionId(executionId, runId);


        if (reportsResult?.success && reportsResult?.reports?.length) {
          // Senaryo raporlarını basitleştir
          const simpleReports = reportsResult.reports.map(report => {
            // Adım sayısı bilgilerini hesapla
            let totalSteps = 0;
            let completedSteps = 0;

            if (report.steps && Array.isArray(report.steps)) {
              totalSteps = report.steps.length;
              completedSteps = report.steps.filter((step: any) => step.success === true).length;
            }

            // Senaryo adı için ilgili kaynakları kontrol et
            const scenarioName =
              report.scenarioName || // Mevcut senaryo adı
              report.title || // Veya rapor başlığı
              report.name || // Veya rapor adı
              'Unnamed Scenario'; // Son çare olarak varsayılan ad

            return {
              id: report.id,
              scenarioId: report.scenarioId,
              scenarioName: scenarioName,
              status: report.status,
              success: report.success,
              duration: report.duration,
              url: report.url,
              date: report.date,
              startTime: report.startTime || report.startedAt || report.queuedAt || report.date,
              summary: report.summary,
              error: report.error,
              videoUrl: report.videoUrl,
              // Step özet bilgileri
              stepsCount: totalSteps,
              completedStepsCount: completedSteps,
              // Run report ve execution bilgilerini ekle
              runReportId: latestRunReport.id,
              executionId: executionId
            };
          });

          responseData.reports = simpleReports;
        } else {
          logger.warn(`Run ${runId}: No test reports found for executionId ${executionId} from latest run report`);
        }
      } else {
        // Eğer latestRunReport yoksa veya executionId yoksa, eski yaklaşımı kullan
        logger.info(`Run ${runId}: No executionId found in latest run report, using fallback approach`);

        // Önce run.latestExecutionId'yi kontrol et
        executionId = run.latestExecutionId;

        // ExecutionId varsa rapor bilgilerini getir
        let reportsResult;
        if (executionId) {
          reportsResult = await getRunTestReportsByExecutionId(executionId, runId);

        }

        // Eğer executionId yoksa veya rapor bulunamadıysa, tarih bazlı yaklaşımı kullan
        if (!executionId || !reportsResult?.success || !reportsResult?.reports?.length) {
          logger.info(`Run ${runId}: No reports found using run.latestExecutionId, trying date-based approach`);
          reportsResult = await getLatestTestReportsByRunId(runId);


          // Eğer tarih bazlı yaklaşımla raporlar bulunduysa ve executionId değeri varsa, onu executionId olarak kullan
          if (reportsResult?.success && reportsResult?.reports && reportsResult.reports.length > 0 && reportsResult.reports[0]?.executionId) {
            executionId = reportsResult.reports[0].executionId;


            // Run report yoksa oluştur
            if (!latestRunReport) {
              const createResult = await createRunReport(runId, executionId);
              if (createResult.success && createResult.reportId) {
                logger.info(`Run ${runId}: Created new run report with ID ${createResult.reportId}`);
                // Yeni oluşturulan run report ID'sini ekle
                responseData.runReportId = createResult.reportId;
              }
            }
          }
        }

        if (reportsResult?.success && reportsResult?.reports?.length) {
          // Raporları executionId'ye göre grupla
          const reportsByExecutionId: { [key: string]: any[] } = {};

          reportsResult.reports.forEach(report => {
            const reportExecutionId = report.executionId || 'unknown';
            if (!reportsByExecutionId[reportExecutionId]) {
              reportsByExecutionId[reportExecutionId] = [];
            }
            reportsByExecutionId[reportExecutionId].push(report);
          });

          // En son execution'ı bul (en yeni tarihli raporların executionId'si)
          let latestExecutionId = '';
          let latestDate = new Date(0); // 1970-01-01

          Object.entries(reportsByExecutionId).forEach(([reportExecId, reports]) => {
            // Her execution'daki en yeni raporu bul
            const latestReport = reports.reduce((latest, current) => {
              const currentDate = new Date(current.date || current.createdAt);
              const latestReportDate = new Date(latest.date || latest.createdAt);
              return currentDate > latestReportDate ? current : latest;
            }, reports[0]);

            // Bu execution'ın en yeni raporu, genel olarak en yeni mi?
            const reportDate = new Date(latestReport.date || latestReport.createdAt);
            if (reportDate > latestDate) {
              latestDate = reportDate;
              latestExecutionId = reportExecId;
            }
          });

          // Sadece en son execution'a ait raporları kullan
          const latestReports = reportsByExecutionId[latestExecutionId] || [];
          logger.info(`Run ${runId}: Using ${latestReports.length} reports from latest execution: ${latestExecutionId}`);

          // Rapor verilerini de ekle - adım detayları olmadan
          const simpleReports = latestReports.map(report => {
            // Adım sayısı bilgilerini hesapla
            let totalSteps = 0;
            let completedSteps = 0;

            if (report.steps && Array.isArray(report.steps)) {
              totalSteps = report.steps.length;
              completedSteps = report.steps.filter((step: any) => step.success === true).length;
            }

            // Senaryo adı için ilgili kaynakları kontrol et
            const scenarioName =
              report.scenarioName || // Mevcut senaryo adı
              report.title || // Veya rapor başlığı
              report.name || // Veya rapor adı
              'Unnamed Scenario'; // Son çare olarak varsayılan ad

            return {
              id: report.id,
              scenarioId: report.scenarioId,
              scenarioName: scenarioName,
              status: report.status,
              success: report.success,
              duration: report.duration,
              url: report.url,
              date: report.date,
              startTime: report.startTime || report.startedAt || report.queuedAt || report.date,
              summary: report.summary,
              error: report.error,
              videoUrl: report.videoUrl,
              // Step özet bilgileri
              stepsCount: totalSteps,
              completedStepsCount: completedSteps,
              // Execution bilgisini ekle
              executionId: report.executionId || latestExecutionId
            };
          });

          responseData.reports = simpleReports;
        }
      }
    }

    return res.json(responseData);
  } catch (error: any) {
    logger.error(`API Error in get run detail: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/run/:runId/reset-report
 * Run raporunu resetler
 */
router.post('/:runId/reset-report', authenticate, checkPermission('Run', 'update'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to reset run report'
      });
    }

    const { runId } = req.params;

    // Run'ı kontrol et
    const runResult = await getRunById(runId);

    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını resetleyebilir
    // Takım ID kontrolü yap
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only reset runs from your team'
      });
    }

    // Run raporunu resetle
    const result = await forceResetRunReportStatus(runId, 'completed');

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to reset run report'
      });
    }

    return res.json({
      success: true,
      message: 'Run report reset successfully'
    });
  } catch (error: any) {
    logger.error(`API Error in reset run report: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/run/:runId/scenario-reports
 * Belirli bir run'a ait senaryo raporlarını getirir
 * Optional executionId parametresi ile belirli bir execution için raporları getirebilir
 */
router.get('/:runId/scenario-reports', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get run scenario reports'
      });
    }

    const { runId } = req.params;
    const { executionId } = req.query;

    // Log the request
    logger.info(`Getting scenario reports for run ${runId} ${executionId ? `with executionId ${executionId}` : 'without executionId'}`);

    // Run'ı kontrol et
    const runResult = await getRunById(runId);

    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Kullanıcı kontrolü - takım üyeleri tüm takım run'larını görebilir
    // Takım ID kontrolü yap
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view runs from your team'
      });
    }

    // Önce MongoDB bağlantısını kontrol et
    await ensureMongoDBConnection();
    if (!db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not available'
      });
    }

    // Aggregation ile run ve en son run report bilgilerini birlikte getir
    if (!runsCollection) {
      return res.status(500).json({
        success: false,
        error: 'Runs collection not available'
      });
    }

    // Aggregation pipeline oluştur - run ve en son run report'u birleştir
    const aggregationPipeline = [
      // Run'ı bul
      { $match: { id: runId } },

      // Run reports koleksiyonundan en son raporu getir
      {
        $lookup: {
          from: 'run_reports',
          let: { runId: '$id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$runId', '$$runId'] } } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 }
          ],
          as: 'latestRunReport'
        }
      },

      // En son run report'u düzleştir
      {
        $addFields: {
          latestRunReport: { $arrayElemAt: ['$latestRunReport', 0] }
        }
      }
    ];

    // Aggregation'ı çalıştır
    const aggregationResult = await runsCollection.aggregate(aggregationPipeline).toArray();

    if (!aggregationResult.length) {
      return res.status(404).json({
        success: false,
        error: 'Run not found after aggregation'
      });
    }

    // Aggregation sonucundan run ve en son run report bilgilerini al
    const runWithReport = aggregationResult[0];
    const latestRunReport = runWithReport.latestRunReport;

    // Execution ID'yi belirle - öncelik sırası:
    // 1. Query'den gelen executionId
    // 2. En son run report'taki executionId
    // 3. Run nesnesindeki lastExecutionId
    let effectiveExecutionId = (executionId as string) ||
                              (latestRunReport?.executionId) ||
                              runResult.run.lastExecutionId;

    // Eğer hala effectiveExecutionId yoksa, tarih bazlı yaklaşımı kullan
    if (!effectiveExecutionId) {
      logger.info(`No executionId found for run ${runId} - trying date-based approach`);

      // Tarih bazlı yaklaşımla en son raporları al
      const latestReportsResult = await getLatestTestReportsByRunId(runId);

      if (latestReportsResult.success && latestReportsResult.reports && latestReportsResult.reports.length > 0) {
        // En son raporlardan executionId'yi al
        const firstReport = latestReportsResult.reports[0];
        if (firstReport.executionId) {
          effectiveExecutionId = firstReport.executionId;


          // Bu executionId ile run raporu oluştur - eğer henüz bir run report yoksa
          if (!latestRunReport && effectiveExecutionId) {
            const createResult = await createRunReport(runId, effectiveExecutionId);
            if (createResult.success) {
              logger.info(`Created run report for run ${runId} with executionId ${effectiveExecutionId}`);
            } else {
              logger.warn(`Failed to create run report: ${createResult.message}`);
            }
          }
        }
      }
    }

    // Log executionId bilgisini
    if (effectiveExecutionId) {
      logger.info(`Using executionId ${effectiveExecutionId} for run ${runId} scenario reports`);
    } else {
      logger.warn(`No executionId found for run ${runId} after all attempts`);
    }

    if (!effectiveExecutionId) {
      logger.error(`No executionId found for run ${runId} even after date-based approach - cannot retrieve reports`);
      return res.status(400).json({
        success: false,
        error: 'No execution ID available for this run - check if run was executed'
      });
    }

    // Ayrıca scenarios koleksiyonuna bakalım ve senaryo isimlerini de getirelim
    try {
      await ensureMongoDBConnection();

      if (!db) {
        return res.status(500).json({
          success: false,
          error: 'MongoDB connection not available'
        });
      }

      // Test raporlarını al - hem runId hem de executionId'yi kullanarak
      const reportsResult = await getRunTestReportsByExecutionId(effectiveExecutionId, runId);

      if (!reportsResult.success) {
        return res.status(500).json({
          success: false,
          error: reportsResult.message || 'Failed to get test reports'
        });
      }

      // Run'ın scenarioIds'sini al
      const runScenarioIds = runResult.run.scenarioIds || [];
      const retrievedScenarioIds = reportsResult.reports?.map(report => report.scenarioId) || [];

      // Eksik senaryoları kontrol et
      const missingScenarioIds = runScenarioIds.filter(id => !retrievedScenarioIds.includes(id));

      // Running run'lar için aktif test bilgilerini de kontrol et
      let activeTestInfo: any[] = [];
      if (runResult.run?.status === 'running') {
        try {
          // Test manager'dan aktif testleri al
          const { getTestsByRunId } = await import('../../services/mongo/testService.js');
          const activeTestsResult = await getTestsByRunId(runId);

          if (activeTestsResult.success && activeTestsResult.tests) {
            activeTestInfo = activeTestsResult.tests.filter(test =>
              test.status === 'running' || test.status === 'queued'
            );

          }
        } catch (error) {
          logger.warn(`Could not get active test info for run ${runId}: ${error}`);
        }
      }

      // Eğer eksik senaryolar bulunduysa, log ekle
      if (missingScenarioIds.length > 0) {
        logger.warn(`Run ${runId} has ${runScenarioIds.length} scenarios, but only ${retrievedScenarioIds.length} reports found. Missing reports for scenarios: ${missingScenarioIds.join(', ')}`);
      }

      // Senaryo ID'leri üzerinden scenarios koleksiyonundan isim bilgilerini çekip raporlara ekleyelim
      // Tüm senaryoları getir - hem eksik olanlar hem de mevcut olanlar
      if (!scenariosCollection) {
        return res.status(500).json({
          success: false,
          error: 'Scenarios collection not available'
        });
      }

      // Tüm run senaryo ID'lerini topla
      const scenarioIds = [...new Set([...runScenarioIds, ...retrievedScenarioIds.filter(id => !!id)])];

        if (scenarioIds.length > 0) {
          // Tüm senaryo ID'leri için MongoDB'den senaryo adlarını çek
          const scenarios = await scenariosCollection.find(
            { id: { $in: scenarioIds } },
            { projection: { id: 1, name: 1, title: 1 } }
          ).toArray();

        // Mevcut raporları düzenle
        let updatedReports = [];
        if (reportsResult.reports && reportsResult.reports.length > 0) {
          updatedReports = reportsResult.reports.map(report => {
            const scenario = scenarios.find((s: any) => s.id === report.scenarioId);

            // Running run'lar için aktif test bilgisini ekle
            const activeTest = activeTestInfo.find(test => test.scenarioId === report.scenarioId);

            const updatedReport = {
              ...report,
              // Öncelikle report'taki title/name kullan, yoksa senaryo koleksiyonundan al
              scenarioName: report.title || report.name || scenario?.title || scenario?.name || 'Unnamed Scenario'
            };

            // Aktif test bilgisi varsa ve rapor henüz tamamlanmamışsa, aktif test bilgisini ekle
            if (activeTest && (report.status === 'running' || report.status === 'queued')) {
              updatedReport.testId = activeTest.id;
              updatedReport.nodeId = activeTest.nodeId;
              updatedReport.startedAt = activeTest.startedAt;
              updatedReport.queuedAt = activeTest.queuedAt;
              // Aktif test'in status'unu kullan (daha güncel olabilir)
              updatedReport.status = activeTest.status;
            }

            return updatedReport;
          });
        }

        // Eksik raporlar için placeholder raporlar ekle
        // Running run'lar için eksik senaryoları 'queued' olarak göster
        for (const scenarioId of missingScenarioIds) {
          const scenario = scenarios.find((s: any) => s.id === scenarioId);
          if (scenario) {
            // Running run'lar için aktif test bilgisini kontrol et
            const activeTest = activeTestInfo.find(test => test.scenarioId === scenarioId);
            let placeholderStatus = 'pending';
            let message = 'Report not available for this scenario';

            if (runResult.run?.status === 'running') {
              if (activeTest) {
                placeholderStatus = activeTest.status; // 'running' veya 'queued'
                message = activeTest.status === 'running' ? 'Test is currently running' : 'Test is queued for execution';
              } else {
                placeholderStatus = 'queued';
                message = 'Test is queued for execution';
              }
            }

            updatedReports.push({
              scenarioId,
              runId,
              executionId: effectiveExecutionId,
              status: placeholderStatus,
              scenarioName: scenario.title || scenario.name || 'Unnamed Scenario',
              // Diğer alanlar boş veya placeholder olabilir
              name: scenario.title || scenario.name || 'Unnamed Scenario',
              steps: [],
              message,
              // Aktif test bilgisi varsa ekle
              ...(activeTest && {
                testId: activeTest.id,
                nodeId: activeTest.nodeId,
                startedAt: activeTest.startedAt,
                queuedAt: activeTest.queuedAt
              })
          });
        }
      }

        // Run status bilgisini de ekle
        updatedReports = updatedReports.map(report => ({
          ...report,
          runStatus: runResult.run?.status
        }));

      return res.json({
        success: true,
          reports: updatedReports
        });
      } else {
        return res.json({
          success: true,
          reports: []
        });
      }
    } catch (error: any) {
      logger.error(`API Error in get run scenario reports: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }
  } catch (error: any) {
    logger.error(`API Error in get run scenario reports: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/run/:runId/active-tests
 * Belirli bir run'a ait aktif testleri getirir
 */
router.get('/:runId/active-tests', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    const { runId } = req.params;
    const allActiveTests = await testManager.getAllTests();
    const activeTests = allActiveTests.filter((test: any) => test.runId === runId && (test.status === 'running' || test.status === 'queued'));

    res.json({
      success: true,
      activeTests: activeTests.map(test => ({
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        completedAt: test.completedAt,
        nodeId: test.nodeId,
        result: test.result,
        userId: test.userId
      }))
    });
  } catch (error: any) {
    logger.error(`API Error in get active tests: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;