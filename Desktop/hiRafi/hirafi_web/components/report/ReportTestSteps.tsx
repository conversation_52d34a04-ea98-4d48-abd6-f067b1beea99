"use client"

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Card, CardContent
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle2,
  XCircle,
  Terminal,
  AlertCircle,
  ZoomIn,
  Play,
  MousePointer,
  Clock,
  ArrowRight,
  Search,
  Keyboard,
  AlertTriangle,
  ImageIcon,
  Zap,
  MessageSquare,
  Eye,
  Hash,
  Type,
  MousePointer2,
  ScrollText,
  Target,
  ToggleLeft,
  GitBranch,
  RotateCcw,
  Repeat,
  ChevronRight,
  ChevronDown
} from 'lucide-react';
import { ReportDetail } from '@/hooks/useReport';
import { Lightbox } from '@/components/ui/lightbox';
import { format } from "date-fns";
import Image from "next/image";
import { AIOperationLog } from './AIOperationLog';
import { detectReportPlatform } from '@/lib/utils/platform-utils';
import { formatStepDuration } from '@/lib/utils/duration-utils';
import { getAIMethodConfig, isA<PERSON>ethod, getAIMethodCardBorder } from '@/lib/utils/ai-method-config';

type Step = ReportDetail['steps'][0];

interface ReportTestStepsProps {
  steps: Step[];
  report?: ReportDetail | null; // Use full ReportDetail for platform detection
}

// Get the appropriate icon based on step type and content
function getStepIcon(step: Step) {
  if (!step.success) {
    return <XCircle className="h-5 w-5 text-rose-500 mt-0.5 flex-shrink-0" />;
  }

  // Control flow step icons
  if (step.type === 'ifElse') {
    return <GitBranch className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5 flex-shrink-0" />;
  }
  if (step.type === 'forLoop') {
    return <RotateCcw className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />;
  }
  if (step.type === 'whileLoop') {
    return <Repeat className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />;
  }

  // Use centralized AI method configuration
  if (isAIMethod(step.type)) {
    const config = getAIMethodConfig(step.type);
    const IconComponent = config.icon;

    // For aiAssertion, use different icon based on success/failure
    if (step.type === "aiAssertion" && !step.success) {
      return <XCircle className="h-5 w-5 text-rose-500 mt-0.5 flex-shrink-0" />;
    }

    return <IconComponent className={`h-5 w-5 ${config.color} mt-0.5 flex-shrink-0`} />;
  }

  return <Terminal className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />;
}

// Get a user-friendly step name based on step type and content
function getStepName(step: Step, index: number) {
  // First check for JSON AI operation logs - these have the most accurate descriptions
  if (step.logs && step.logs.length > 0) {
    const firstLog = step.logs[0];
    if (firstLog.trim().startsWith('{"operation"')) {
      try {
        const data = JSON.parse(firstLog);
        if (data.description) {
          return data.description;
        }
      } catch (e) {
        // Fall through to other methods
      }
    }
  }

  // For goto steps, extract URL from logs or step data
  if (step.type === 'goto') {
    if (step.logs && step.logs.length > 0) {
      const firstLog = step.logs[0];
      return firstLog
        .replace('🌐 Navigating to: ', '')
        .replace('Navigating to: ', '')
        .replace(/^["']|["']$/g, ''); // Remove quotes from start and end
    }
    const url = step.url || step.value;
    return url ? `Navigate to ${url}` : 'Navigate to page';
  }

  // For AI methods, use prompt/value to create meaningful names
  if (step.type.startsWith('ai')) {
    const promptOrValue = step.prompt || step.value;

    if (promptOrValue) {
      switch (step.type) {
        case 'aiAction':
          return promptOrValue;
        case 'aiInput':
          const inputTarget = step.target;
          return inputTarget ? `Input "${promptOrValue}" into ${inputTarget}` : promptOrValue;
        case 'aiTap':
          return promptOrValue;
        case 'aiRightClick':
          return promptOrValue;
        case 'aiHover':
          return promptOrValue;
        case 'aiScroll':
          return promptOrValue;
        case 'aiKeyboardPress':
          const keyTarget = step.target;
          return keyTarget ? `Press "${promptOrValue}" on ${keyTarget}` : `Press "${promptOrValue}"`;
        case 'aiWaitElement':
          return promptOrValue;
        case 'aiLocate':
          return promptOrValue;
        case 'aiAssertion':
          return promptOrValue;
        case 'aiQuery':
          return promptOrValue;
        case 'aiString':
          return promptOrValue;
        case 'aiNumber':
          return promptOrValue;
        case 'aiBoolean':
          return promptOrValue;
        default:
          return promptOrValue;
      }
    }
  }

  // For sleep steps
  if (step.type === 'sleep') {
    const duration = step.duration || step.value;
    return duration ? `Wait ${duration}s` : 'Wait';
  }

  // For control flow steps
  if (step.type === 'ifElse') {
    const condition = step.prompt || step.value || 'condition';
    const branchInfo = step.controlFlowData?.executedBranch 
      ? ` (${step.controlFlowData.executedBranch.toUpperCase()} branch executed)`
      : '';
    return `IF ${condition}${branchInfo}`;
  }

  if (step.type === 'forLoop') {
    const iterations = step.controlFlowData?.completedIterations || step.controlFlowData?.iterationCount || 'N';
    const iterationInfo = step.controlFlowData?.iterationCount 
      ? ` (${step.controlFlowData.completedIterations}/${step.controlFlowData.iterationCount} iterations)`
      : '';
    return `FOR Loop${iterationInfo}`;
  }

  if (step.type === 'whileLoop') {
    const iterations = step.controlFlowData?.completedIterations || 0;
    const maxIterations = step.controlFlowData?.iterationCount || 'N';
    const iterationInfo = ` (${iterations} iterations)`;
    return `WHILE Loop${iterationInfo}`;
  }

  // Fallback to step name or type
  return step.name || step.type || `Step ${index + 1}`;
}

// Get a badge for the step type
function getStepTypeBadge(step: Step) {
  // Control flow badges
  if (step.type === 'ifElse') {
    return (
      <Badge variant="outline" className="border-purple-200 text-purple-700 bg-purple-50 dark:border-purple-800 dark:text-purple-300 dark:bg-purple-900/20">
        IF-ELSE
      </Badge>
    );
  }
  if (step.type === 'forLoop') {
    return (
      <Badge variant="outline" className="border-blue-200 text-blue-700 bg-blue-50 dark:border-blue-800 dark:text-blue-300 dark:bg-blue-900/20">
        FOR LOOP
      </Badge>
    );
  }
  if (step.type === 'whileLoop') {
    return (
      <Badge variant="outline" className="border-green-200 text-green-700 bg-green-50 dark:border-green-800 dark:text-green-300 dark:bg-green-900/20">
        WHILE LOOP
      </Badge>
    );
  }

  // Use centralized AI method configuration
  if (isAIMethod(step.type)) {
    const config = getAIMethodConfig(step.type);
    return (
      <Badge variant="outline" className={config.badgeColor}>
        {config.displayName}
      </Badge>
    );
  }

  return (
    <Badge variant="outline">
      {step.type || 'Unknown'}
    </Badge>
  );
}

// Function to format error message by removing midscene references and prefixes
function formatErrorMessage(errorMessage: string): string {
  if (!errorMessage) return '';

  // Remove midscene reference and troubleshooting links
  if (errorMessage.includes('midscenejs.com')) {
    return errorMessage.split('Trouble shooting:')[0].trim();
  }

  // Remove AI Assertion error prefix if present
  if (errorMessage.startsWith('AI Assertion error:')) {
    return errorMessage.replace('AI Assertion error:', '').trim();
  }

  // Highlight SauceLabs credential errors
  if (errorMessage.includes('SauceLabs Credentials Error:')) {
    return errorMessage;
  }

  // Highlight SauceLabs connection errors
  if (errorMessage.includes('SauceLabs Connection Error:')) {
    return errorMessage;
  }

  return errorMessage;
}

// Control Flow Visualization Component
function ControlFlowVisualization({ step, onImageClick }: { step: Step; onImageClick?: (url: string) => void }) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    executedSteps: true // Auto-expand nested steps by default
  });

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  if (!step.controlFlowData) return null;

  const { controlFlowData } = step;

  // IF-ELSE Visualization
  if (step.type === 'ifElse') {
    const executedSteps = controlFlowData.executedBranch === 'true' 
      ? controlFlowData.trueSteps 
      : controlFlowData.falseSteps;
    const stepCount = executedSteps?.length || 0;

    return (
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/10 dark:to-indigo-900/10 rounded-lg border border-purple-200 dark:border-purple-800">
        {/* Compact Header */}
        <div className="p-3 border-b border-purple-200 dark:border-purple-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <GitBranch className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                <span className="text-sm font-semibold text-purple-900 dark:text-purple-100">IF-ELSE</span>
              </div>
              <Badge 
                variant="outline" 
                className={controlFlowData.conditionResult
                  ? "border-emerald-300 text-emerald-800 bg-emerald-100 dark:bg-emerald-900/30 dark:text-emerald-200 dark:border-emerald-700 text-xs"
                  : "border-rose-300 text-rose-800 bg-rose-100 dark:bg-rose-900/30 dark:text-rose-200 dark:border-rose-700 text-xs"
                }
              >
                {controlFlowData.conditionResult ? '✓ TRUE' : '✗ FALSE'}
              </Badge>
              <div className="text-xs text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded-full">
                {stepCount} step{stepCount !== 1 ? 's' : ''} executed
              </div>
            </div>
            {stepCount > 0 && (
              <button
                onClick={() => toggleSection('executedSteps')}
                className="flex items-center gap-1 text-xs text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 transition-colors"
              >
                <span>{expandedSections.executedSteps ? 'Hide' : 'Show'} steps</span>
                <div className={`transition-transform ${expandedSections.executedSteps ? 'rotate-180' : ''}`}>
                  <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
            )}
          </div>
        </div>

        {/* Nested Steps - Compact Display */}
        {stepCount > 0 && expandedSections.executedSteps && (
          <div className="p-3">
            <CompactNestedStepsDisplay 
              steps={executedSteps}
              onImageClick={onImageClick}
              flowType="ifElse"
              branchName={controlFlowData.executedBranch?.toUpperCase() || 'UNKNOWN'}
            />
          </div>
        )}
      </div>
    );
  }

  // FOR/WHILE LOOP Visualization
  if (step.type === 'forLoop' || step.type === 'whileLoop') {
    const isForLoop = step.type === 'forLoop';
    const IconComponent = isForLoop ? RotateCcw : Repeat;
    const color = isForLoop ? 'blue' : 'emerald';
    const bgColor = isForLoop 
      ? 'from-blue-50 to-cyan-50 dark:from-blue-900/10 dark:to-cyan-900/10' 
      : 'from-emerald-50 to-teal-50 dark:from-emerald-900/10 dark:to-teal-900/10';
    const borderColor = isForLoop ? 'border-blue-200 dark:border-blue-800' : 'border-emerald-200 dark:border-emerald-800';
    
    return (
      <div className={`bg-gradient-to-r ${bgColor} rounded-lg border ${borderColor}`}>
        {/* Compact Header */}
        <div className={`p-3 border-b ${borderColor}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <IconComponent className={`h-4 w-4 text-${color}-600 dark:text-${color}-400`} />
                <span className={`text-sm font-semibold text-${color}-900 dark:text-${color}-100`}>
                  {isForLoop ? 'FOR LOOP' : 'WHILE LOOP'}
                </span>
              </div>
              <Badge variant="outline" className={`border-${color}-300 text-${color}-800 bg-${color}-100 dark:bg-${color}-900/30 dark:text-${color}-200 dark:border-${color}-700 text-xs`}>
                {controlFlowData.completedIterations || 0} iterations
              </Badge>
              {controlFlowData.totalStepsExecuted && (
                <div className={`text-xs text-${color}-700 dark:text-${color}-300 bg-${color}-100 dark:bg-${color}-900/30 px-2 py-1 rounded-full`}>
                  {controlFlowData.totalStepsExecuted} total steps
                </div>
              )}
            </div>
          </div>

          {/* Additional Info Row */}
          <div className="flex items-center gap-3 mt-2">
            {controlFlowData.iterationCount && (
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Planned: {controlFlowData.iterationCount} iterations
              </div>
            )}
            {controlFlowData.hitMaxIterations && (
              <Badge variant="outline" className="border-amber-300 text-amber-800 bg-amber-100 dark:bg-amber-900/30 dark:text-amber-200 dark:border-amber-700 text-xs">
                ⚠ Max limit reached
              </Badge>
            )}
            {controlFlowData.failedIteration !== undefined && (
              <Badge variant="outline" className="border-red-300 text-red-800 bg-red-100 dark:bg-red-900/30 dark:text-red-200 dark:border-red-700 text-xs">
                ✗ Failed at iteration {controlFlowData.failedIteration}
              </Badge>
            )}
          </div>
        </div>

        {/* Loop Execution Summary */}
        {controlFlowData.totalStepsExecuted && controlFlowData.totalStepsExecuted > 0 && (
          <div className="p-3">
            <div className={`text-sm text-${color}-800 dark:text-${color}-200`}>
              <span className="font-medium">Loop executed successfully</span>
              <span className="text-xs text-gray-600 dark:text-gray-400 ml-2">
                ({controlFlowData.totalStepsExecuted} total steps across {controlFlowData.completedIterations} iterations)
              </span>
            </div>
          </div>
        )}
      </div>
    );
  }

  return null;
}

// Compact Nested Steps Display Component
function CompactNestedStepsDisplay({ 
  steps, 
  onImageClick, 
  flowType, 
  branchName 
}: { 
  steps?: any[]; 
  onImageClick?: (url: string) => void; 
  flowType: 'ifElse' | 'loop';
  branchName?: string;
}) {
  if (!steps || steps.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-gray-500 italic">
        No steps executed in this {flowType === 'ifElse' ? 'branch' : 'loop'}
      </div>
    );
  }

  const hasScreenshots = steps.some(step => step.beforeScreenshotUrl || step.afterScreenshotUrl);

  return (
    <div className="space-y-2">
      {/* Steps Grid */}
      <div className="grid gap-2">
        {steps.map((nestedStep, index) => (
          <div 
            key={nestedStep.id || index}
            className={`flex items-center gap-3 p-2 rounded-lg border transition-all hover:shadow-sm ${
              nestedStep.success 
                ? 'bg-white dark:bg-gray-800/50 border-emerald-200 dark:border-emerald-800/50' 
                : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            }`}
          >
            {/* Status Icon */}
            <div className="flex-shrink-0">
              {nestedStep.success ? (
                <div className="w-5 h-5 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center">
                  <CheckCircle2 className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
                </div>
              ) : (
                <div className="w-5 h-5 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                  <XCircle className="h-3 w-3 text-red-600 dark:text-red-400" />
                </div>
              )}
            </div>

            {/* Step Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium truncate">
                  {nestedStep.name || nestedStep.type || `Step ${index + 1}`}
                </span>
                <Badge variant="outline" className="text-xs flex-shrink-0">
                  {nestedStep.type}
                </Badge>
                {nestedStep.duration && (
                  <span className="text-xs text-gray-500 flex-shrink-0">
                    {formatStepDuration(nestedStep.duration)}
                  </span>
                )}
              </div>
              {nestedStep.error && (
                <div className="text-xs text-red-600 dark:text-red-400 mt-1 truncate">
                  {nestedStep.error}
                </div>
              )}
            </div>

            {/* Screenshots */}
            {(nestedStep.beforeScreenshotUrl || nestedStep.afterScreenshotUrl) && (
              <div className="flex gap-1 flex-shrink-0">
                {nestedStep.beforeScreenshotUrl && (
                  <div 
                    className="relative cursor-pointer group"
                    onClick={() => onImageClick && onImageClick(nestedStep.beforeScreenshotUrl)}
                  >
                    <img
                      src={nestedStep.beforeScreenshotUrl}
                      alt="Before"
                      className="w-8 h-8 object-cover rounded border border-gray-200 dark:border-gray-600 transition-transform group-hover:scale-110"
                      loading="lazy"
                    />
                    <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full border border-white dark:border-gray-800 flex items-center justify-center">
                      <span className="text-[8px] text-white font-bold">B</span>
                    </div>
                  </div>
                )}
                {nestedStep.afterScreenshotUrl && (
                  <div 
                    className="relative cursor-pointer group"
                    onClick={() => onImageClick && onImageClick(nestedStep.afterScreenshotUrl)}
                  >
                    <img
                      src={nestedStep.afterScreenshotUrl}
                      alt="After"
                      className="w-8 h-8 object-cover rounded border border-gray-200 dark:border-gray-600 transition-transform group-hover:scale-110"
                      loading="lazy"
                    />
                    <div className="absolute -top-1 -left-1 w-3 h-3 bg-emerald-500 rounded-full border border-white dark:border-gray-800 flex items-center justify-center">
                      <span className="text-[8px] text-white font-bold">A</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary Footer */}
      {hasScreenshots && (
        <div className="flex items-center justify-center gap-2 pt-2 text-xs text-gray-500 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Before</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
            <span>After</span>
          </div>
        </div>
      )}
    </div>
  );
}

// Nested Steps Display Component
function NestedStepsDisplay({ steps, onImageClick }: { steps?: any[]; onImageClick?: (url: string) => void }) {
  if (!steps || steps.length === 0) {
    return (
      <div className="text-sm text-gray-500 italic py-2">
        No steps executed in this branch
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {steps.map((nestedStep, index) => (
        <div 
          key={nestedStep.id || index}
          className={`flex items-start gap-3 p-2 rounded border-l-2 ${
            nestedStep.success 
              ? 'border-l-green-400 bg-green-50 dark:bg-green-900/10' 
              : 'border-l-red-400 bg-red-50 dark:bg-red-900/10'
          }`}
        >
          <div className="flex-shrink-0 mt-0.5">
            {nestedStep.success ? (
              <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium truncate">
                {nestedStep.name || nestedStep.type || `Step ${index + 1}`}
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${
                  nestedStep.success 
                    ? 'border-green-200 text-green-700' 
                    : 'border-red-200 text-red-700'
                }`}
              >
                {nestedStep.type}
              </Badge>
            </div>
            {nestedStep.error && (
              <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                {nestedStep.error}
              </div>
            )}
            
            {/* Compact Screenshots for Nested Steps */}
            {(nestedStep.beforeScreenshotUrl || nestedStep.afterScreenshotUrl) && (
              <div className="flex gap-2 mt-2 mb-2">
                {nestedStep.beforeScreenshotUrl && (
                  <div 
                    className="relative cursor-pointer group flex-shrink-0"
                    onClick={() => onImageClick && onImageClick(nestedStep.beforeScreenshotUrl)}
                  >
                    <img
                      src={nestedStep.beforeScreenshotUrl}
                      alt="Before nested step"
                      className="w-12 h-12 object-cover rounded border border-gray-200 dark:border-gray-600 transition-transform group-hover:scale-105"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 flex items-center justify-center transition-all opacity-0 group-hover:opacity-100 rounded">
                      <ZoomIn className="h-3 w-3 text-white" />
                    </div>
                    <div className="absolute -bottom-1 -left-1 bg-blue-500 text-white text-xs px-1 rounded text-[10px] leading-none py-0.5">
                      Before
                    </div>
                  </div>
                )}
                {nestedStep.afterScreenshotUrl && (
                  <div 
                    className="relative cursor-pointer group flex-shrink-0"
                    onClick={() => onImageClick && onImageClick(nestedStep.afterScreenshotUrl)}
                  >
                    <img
                      src={nestedStep.afterScreenshotUrl}
                      alt="After nested step"
                      className="w-12 h-12 object-cover rounded border border-gray-200 dark:border-gray-600 transition-transform group-hover:scale-105"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 flex items-center justify-center transition-all opacity-0 group-hover:opacity-100 rounded">
                      <ZoomIn className="h-3 w-3 text-white" />
                    </div>
                    <div className="absolute -bottom-1 -left-1 bg-green-500 text-white text-xs px-1 rounded text-[10px] leading-none py-0.5">
                      After
                    </div>
                  </div>
                )}
              </div>
            )}
            
            <div className="text-xs text-gray-500 mt-1">
              Duration: {formatStepDuration(nestedStep.duration || 0)}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

function StepDetails({ step, onImageClick, report }: { step: Step; onImageClick: (url: string) => void; report?: ReportDetail | null }) {
  // Detect platform for responsive sizing
  const platform = detectReportPlatform(report || null);
  const isAndroid = platform === 'android';
  return (
    <div className="px-4 pb-4 pt-1 border-t border-gray-100 dark:border-gray-800">
      {/* Display SauceLabs errors prominently */}
      {step.error && (step.error.includes('SauceLabs Credentials Error:') || step.error.includes('SauceLabs Connection Error:')) && (
        <div className="mt-2 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-md border border-amber-200 dark:border-amber-800">
          <div className="flex items-start gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
            <div>
              <p className="font-medium text-amber-800 dark:text-amber-300">
                {step.error}
              </p>
              <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                Please check your SauceLabs configuration and credentials in the plugin settings.
              </p>
            </div>
          </div>
        </div>
      )}

      {step.logs && step.logs.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Step Logs</h4>
          <div className="space-y-2 bg-gray-50 dark:bg-gray-900/50 p-3 rounded-md border border-gray-200 dark:border-gray-800">
            {step.logs.map((log, idx) => {
              // Check if the log is a JSON string that starts with '{"operation"'
              if (log.trim().startsWith('{"operation"')) {
                return <AIOperationLog key={idx} log={log} />;
              }

              // For non-JSON logs or logs that don't match our expected format,
              // use the existing log display logic
              const isJsonLine = log.startsWith('Action details:') || log.startsWith('Assertion details:');
              const isPlanLine = log.startsWith('Plan:');
              const isErrorLine = log.includes('error:') || log.includes('failed');
              const isSuccessLine = log.includes('✅') || log.includes('successfully executed');
              const isElementLine = log.includes('🔍 Element bulundu:');
              const isActionLine = log.includes('⌨️') || log.includes('🔤') || log.includes('🖱️') || log.includes('⚡ Eylem') || log.includes('⚡ Action:');
              const isAssertionLine = log.includes('🔍 Assertion:');
              const isReasoningLine = log.includes('💭 Reasoning:');

              // Satırların stillerini ve ikonlarını belirle
              const lineIconClass = isErrorLine ? "text-rose-500" :
                                  isSuccessLine ? "text-emerald-500" :
                                  isPlanLine ? "text-blue-500" :
                                  isElementLine ? "text-amber-500" :
                                  isActionLine ? "text-indigo-500" :
                                  isAssertionLine ? "text-emerald-500" :
                                  isReasoningLine ? "text-purple-500" :
                                  "text-gray-500";

              const lineIcon = isErrorLine ? <AlertCircle className={`h-4 w-4 ${lineIconClass} mt-0.5`} /> :
                              isSuccessLine ? <CheckCircle2 className={`h-4 w-4 ${lineIconClass} mt-0.5`} /> :
                              isPlanLine ? <Terminal className={`h-4 w-4 ${lineIconClass} mt-0.5`} /> :
                              isElementLine ? <Search className={`h-4 w-4 ${lineIconClass} mt-0.5`} /> :
                              isActionLine ? <MousePointer className={`h-4 w-4 ${lineIconClass} mt-0.5`} /> :
                              isAssertionLine ? <CheckCircle2 className={`h-4 w-4 ${lineIconClass} mt-0.5`} /> :
                              isReasoningLine ? <AlertTriangle className={`h-4 w-4 ${lineIconClass} mt-0.5`} /> :
                              <Terminal className={`h-4 w-4 ${lineIconClass} mt-0.5`} />;

              // JSON detayları için farklı görünüm
              if (isJsonLine) {
                try {
                  // JSON ayır ve işle
                  let jsonPart = log;
                  let title = '';

                  if (log.startsWith('Action details:')) {
                    title = 'Action Details';
                    jsonPart = log.replace('Action details:', '').trim();
                  } else if (log.startsWith('Assertion details:')) {
                    title = 'Assertion Details';
                    jsonPart = log.replace('Assertion details:', '').trim();
                  }

                  // JSON'ı çözümle
                  const jsonObj = JSON.parse(jsonPart);

                  return (
                    <div key={idx} className="rounded-md bg-gray-100 dark:bg-gray-800 p-3 mb-2">
                      <h5 className="text-xs font-semibold mb-2">{title}</h5>
                      <div className="space-y-1">
                        {Object.entries(jsonObj).map(([key, value]) => {
                          // Format the value based on its type
                          let formattedValue = value as string;
                          let valueClass = "text-gray-700 dark:text-gray-300";

                          if (typeof value === 'boolean') {
                            valueClass = value ? "text-emerald-600 dark:text-emerald-400 font-medium" : "text-rose-600 dark:text-rose-400 font-medium";
                          } else if (Array.isArray(value)) {
                            formattedValue = `[${value.join(', ')}]`;
                          } else if (typeof value === 'object' && value !== null) {
                            formattedValue = JSON.stringify(value, null, 2);
                          }

                          return (
                            <div key={key} className="grid grid-cols-4 text-xs">
                              <span className="font-medium col-span-1 text-gray-900 dark:text-gray-100">{key}:</span>
                              <pre className={`col-span-3 ${valueClass} font-mono overflow-auto`}>{formattedValue}</pre>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                } catch (e) {
                  // JSON çözümlenemezse normal log gibi göster
                  return (
                    <div key={idx} className="flex items-start">
                      {lineIcon}
                      <pre className={`ml-2 text-xs overflow-auto ${lineIconClass} font-mono`}>{log}</pre>
                    </div>
                  );
                }
              }

              // AI adımlarını daha iyi görüntülemek için özel stillemeler
              if (isActionLine || isAssertionLine) {
                // Eylem adımı içeriğini ayır
                const parts = log.split(':');
                const prefix = parts[0];
                const content = parts.slice(1).join(':').trim();

                // If content has quotes around it, make it stand out
                const formattedContent = content.startsWith('"') && content.endsWith('"')
                  ? <span className="font-medium text-gray-900 dark:text-white">{content}</span>
                  : <span>{content}</span>;

                return (
                  <div key={idx} className="flex items-start p-1">
                    {lineIcon}
                    <div className="ml-2">
                      <span className={`text-xs font-semibold ${lineIconClass}`}>{prefix}:</span>
                      <span className="text-xs ml-1">{formattedContent}</span>
                    </div>
                  </div>
                );
              }

              if (isReasoningLine) {
                const parts = log.split(':');
                const prefix = parts[0];
                const content = parts.slice(1).join(':').trim();

                return (
                  <div key={idx} className="flex items-start bg-purple-50 dark:bg-purple-900/20 p-2 rounded-md">
                    {lineIcon}
                    <div className="ml-2">
                      <span className={`text-xs font-semibold ${lineIconClass}`}>{prefix}:</span>
                      <span className="text-xs ml-1 text-gray-700 dark:text-gray-300">{content}</span>
                    </div>
                  </div>
                );
              }

              return (
                <div key={idx} className="flex items-start">
                  {lineIcon}
                  <pre className={`ml-2 text-xs overflow-auto font-mono ${lineIconClass}`}>{log}</pre>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Control Flow Visualization */}
      {step.controlFlowData && (
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-3">Control Flow Results</h4>
          <ControlFlowVisualization step={step} onImageClick={onImageClick} />
        </div>
      )}

      {(step.beforeScreenshotUrl || step.afterScreenshotUrl) && (
        <div className={`mt-4 ${
          isAndroid
            ? 'flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto' // Center and limit width for Android
            : 'grid grid-cols-2 gap-4' // Original side-by-side layout for web
        }`}>
          <div className={isAndroid ? 'flex-1' : ''}>
            <h4 className="text-sm font-medium mb-2">Before</h4>
            <div
              className={`border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden relative group ${
                step.beforeScreenshotUrl ? 'cursor-pointer' : ''
              } ${
                isAndroid ? 'max-w-xs mx-auto' : '' // Limit width and center for Android
              }`}
              onClick={() => step.beforeScreenshotUrl && onImageClick(step.beforeScreenshotUrl)}
            >
              {step.beforeScreenshotUrl ? (
                <>
                  <img
                    src={step.beforeScreenshotUrl}
                    alt="Before execution"
                    className={`w-full transition-transform group-hover:scale-[1.02] ${
                      isAndroid
                        ? 'h-auto max-h-80 object-contain' // Smaller max height and contain for Android
                        : 'h-auto' // Original sizing for web
                    }`}
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 flex items-center justify-center transition-all opacity-0 group-hover:opacity-100">
                    <div className="bg-white/90 dark:bg-black/80 p-2 rounded-full">
                      <ZoomIn className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                </>
              ) : (
                <div className={`bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${
                  isAndroid ? 'h-80' : 'h-48'
                }`}>
                  <div className="text-center">
                    <ImageIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">No screenshot available</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className={isAndroid ? 'flex-1' : ''}>
            <h4 className="text-sm font-medium mb-2">After</h4>
            <div
              className={`border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden relative group ${
                step.afterScreenshotUrl ? 'cursor-pointer' : ''
              } ${
                isAndroid ? 'max-w-xs mx-auto' : '' // Limit width and center for Android
              }`}
              onClick={() => step.afterScreenshotUrl && onImageClick(step.afterScreenshotUrl)}
            >
              {step.afterScreenshotUrl ? (
                <>
                  <img
                    src={step.afterScreenshotUrl}
                    alt="After execution"
                    className={`w-full transition-transform group-hover:scale-[1.02] ${
                      isAndroid
                        ? 'h-auto max-h-80 object-contain' // Smaller max height and contain for Android
                        : 'h-auto' // Original sizing for web
                    }`}
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 flex items-center justify-center transition-all opacity-0 group-hover:opacity-100">
                    <div className="bg-white/90 dark:bg-black/80 p-2 rounded-full">
                      <ZoomIn className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                </>
              ) : (
                <div className={`bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${
                  isAndroid ? 'h-80' : 'h-48'
                }`}>
                  <div className="text-center">
                    <ImageIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">No screenshot available</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export function ReportTestSteps({ steps, report }: ReportTestStepsProps) {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxImages, setLightboxImages] = useState<string[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const handleImageClick = (url: string) => {
    // Collect all images including nested steps
    const allImages: string[] = [];
    
    steps.forEach(step => {
      // Add main step screenshots
      if (step.beforeScreenshotUrl) allImages.push(step.beforeScreenshotUrl);
      if (step.afterScreenshotUrl) allImages.push(step.afterScreenshotUrl);
      
      // Add nested step screenshots from control flow data
      if (step.controlFlowData) {
        // IF-ELSE nested steps
        if (step.controlFlowData.trueSteps) {
          step.controlFlowData.trueSteps.forEach((nestedStep: any) => {
            if (nestedStep.beforeScreenshotUrl) allImages.push(nestedStep.beforeScreenshotUrl);
            if (nestedStep.afterScreenshotUrl) allImages.push(nestedStep.afterScreenshotUrl);
          });
        }
        if (step.controlFlowData.falseSteps) {
          step.controlFlowData.falseSteps.forEach((nestedStep: any) => {
            if (nestedStep.beforeScreenshotUrl) allImages.push(nestedStep.beforeScreenshotUrl);
            if (nestedStep.afterScreenshotUrl) allImages.push(nestedStep.afterScreenshotUrl);
          });
        }
      }
    });

    setLightboxImages(allImages);
    setSelectedImageIndex(allImages.indexOf(url));
    setLightboxOpen(true);
  };

  // Check if this is a connection failure (no steps but has error)
  if (!steps || steps.length === 0) {
    // If there's an error and no steps, show connection failure card
    if (report?.error) {
      return (
        <div className="space-y-4">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-rose-200 dark:border-rose-800 border-l-4 border-l-rose-400 dark:border-l-rose-600">
              <CardContent className="p-0">
                <details className="group" open={true}>
                  <summary className="flex items-center gap-3 p-4 cursor-pointer list-none">
                    <XCircle className="h-5 w-5 text-rose-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 dark:text-white">Connection Failed</h3>
                      <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                        <span>Test failed during connection phase</span>
                        <Badge variant="destructive">Failed</Badge>
                      </div>
                    </div>
                    <div className="flex-shrink-0 transition-transform group-open:rotate-180">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </summary>
                  <div className="px-4 pb-4 pt-1 border-t border-gray-100 dark:border-gray-800">
                    <div className="mt-2 p-3 bg-rose-50 dark:bg-rose-900/20 rounded-md border border-rose-200 dark:border-rose-800">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-5 w-5 text-rose-600 mt-0.5" />
                        <div>
                          <p className="font-medium text-rose-800 dark:text-rose-300">
                            {formatErrorMessage(report.error)}
                          </p>
                          <p className="text-sm text-rose-700 dark:text-rose-400 mt-1">
                            The test failed to establish a connection before any steps could be executed. Please check your configuration and try again.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </details>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      );
    }

    // Default no steps message
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-full mb-4">
          <AlertCircle className="h-6 w-6 text-gray-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No test steps available</h3>
        <p className="text-gray-500 dark:text-gray-400 max-w-md">
          This report doesn't have any test steps recorded.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {steps.map((step, index) => (
          <motion.div
            key={step.id || index} // Use index as fallback key if id is missing
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className={`
              ${!step.success ? "border-rose-200 dark:border-rose-800" : ""}
              ${isAIMethod(step.type) ? getAIMethodCardBorder(step.type, step.success) : ""}
              hover:shadow-md transition-shadow
            `}>
              <CardContent className="p-0">
                <details className="group" open={index === 0}> {/* Open first step by default */}
                  <summary className="flex items-center gap-3 p-4 cursor-pointer list-none">
                    {getStepIcon(step)}
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 dark:text-white">{getStepName(step, index)}</h3>
                      <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                        <span>Total Steps: {report?.summary?.total || steps.length}</span>
                        <span>Duration: {formatStepDuration(step.duration)}</span>
                        {getStepTypeBadge(step)}
                        {step.success ? (
                          <Badge variant="outline" className="bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800">
                            Success
                          </Badge>
                        ) : (
                          <Badge variant="destructive">Failed</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex-shrink-0 transition-transform group-open:rotate-180">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </summary>
                  <StepDetails step={step} onImageClick={handleImageClick} report={report} />
                </details>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Lightbox component */}
      <Lightbox
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        images={lightboxImages}
        initialIndex={selectedImageIndex}
      />
    </>
  );
}