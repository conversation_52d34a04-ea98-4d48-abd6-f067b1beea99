"use client";

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import {
  RefreshCw,
  Layers,
  Smartphone,
  Calendar,
  Upload,
  Package
} from "lucide-react"
import { pluginApi } from "@/lib/api"
import { API_BASE_URL } from "@/lib/api/constants"
import { toast } from "@/lib/utils/toast-utils"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { formatDistanceToNow } from "date-fns"


import { EnvironmentSettings } from "../../run-context"
import {
  AndroidEnvironmentSettings,
  AndroidApp,
  SauceLabsConfig,
} from "@/types/android-environment";

interface AppsTabProps {
  environmentSettings: EnvironmentSettings;
  updateEnvironmentSettings: (settings: EnvironmentSettings) => void;
  activeProviders: {
    sauceLabs: boolean;
    testinium: boolean;
  };
}

// Type guard to check if environmentSettings is AndroidEnvironmentSettings
const isAndroidSettings = (
  settings: EnvironmentSettings,
): settings is AndroidEnvironmentSettings => {
  return settings.platform === "android";
};

// Define the SauceLabs app type for better type safety
interface SauceLabsApp {
  id: string;
  name: string;
  version?: string;
  uploaded?: string;
  metadata?: {
    identifier?: string;
    version?: string;
    versionCode?: string;
    name?: string;
  };
  kind?: string;
  group?: string;
  description?: string;
}

// Define the Testinium app type
interface TestiniumApp {
  id: string;
  name: string;
  fileName: string;
  fileKey: string;
  version?: string;
  description?: string;
  fileSize?: number;
  uploadedAt: string;
  uploadedBy?: string;
  companyId: string;
  teamId?: string;
  metadata?: {
    platform?: string;
    bundleId?: string;
    versionCode?: string;
    minSdkVersion?: string;
    targetSdkVersion?: string;
    [key: string]: any;
  };
}

export function AppsTab({
  environmentSettings,
  updateEnvironmentSettings,
  activeProviders,
}: AppsTabProps) {
  const [sauceLabsApps, setSauceLabsApps] = useState<SauceLabsApp[]>([])
  const [testiniumApps, setTestiniumApps] = useState<TestiniumApp[]>([])
  const [isLoadingApps, setIsLoadingApps] = useState(false)
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [appFile, setAppFile] = useState<File | null>(null)
  const [appDescription, setAppDescription] = useState("")
  const [isUploadingApp, setIsUploadingApp] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  // Load apps when the component mounts based on active providers
  useEffect(() => {
    if (activeProviders.sauceLabs) {
      loadSauceLabsApps();
    }
    if (activeProviders.testinium) {
      loadTestiniumApps();
    }
  }, [activeProviders.sauceLabs, activeProviders.testinium]);

  // Load SauceLabs apps
  const loadSauceLabsApps = async () => {
    setIsLoadingApps(true);
    try {
      // Use the correct API method from the pluginApi module
      const response = await pluginApi.getSauceLabsApps();
      if (
        response.success &&
        (response as any).apps &&
        Array.isArray((response as any).apps)
      ) {
        // Filter to only show Android apps
        const androidApps = (response as any).apps.filter(
          (app: SauceLabsApp) =>
            app.kind === "android" ||
            (app.name && app.name.toLowerCase().endsWith(".apk")),
        );
        setSauceLabsApps(androidApps);
      } else {
        throw new Error(response.error || "Failed to load apps");
      }
    } catch (error) {
      console.error("Error loading SauceLabs apps:", error);
      toast.error("Failed to load SauceLabs apps", {
        description: "Please check your SauceLabs configuration and try again.",
      });
    } finally {
      setIsLoadingApps(false);
    }
  };

  // Load Testinium apps
  const loadTestiniumApps = async () => {
    setIsLoadingApps(true);
    try {
      const response = await pluginApi.getTestiniumApps();
      if (
        response.success &&
        response.apps &&
        Array.isArray(response.apps)
      ) {
        setTestiniumApps(response.apps);
      } else {
        throw new Error(response.error || "Failed to load apps");
      }
    } catch (error) {
      console.error("Error loading Testinium apps:", error);
      toast.error("Failed to load Testinium apps", {
        description: "Please check your Testinium configuration and try again.",
      });
    } finally {
      setIsLoadingApps(false);
    }
  };

  // Handle SauceLabs app selection
  const handleSauceLabsAppSelect = (appId: string) => {
    // Find the selected app to get its details
    const selectedApp = sauceLabsApps.find((app) => app.id === appId);

    // Get current Android settings or create default ones
    const androidSettings = isAndroidSettings(environmentSettings)
      ? environmentSettings
      : {
          platform: "android" as const,
          sauceLabs: {
            username: "",
            accessKey: "",
            selectedDevices: [],
          },
          testinium: {
            apiUrl: "",
            clientId: "",
            clientSecret: "",
            issuerUri: "",
            selectedDevices: [],
          },
        };

    // Create the app object
    const appObject: AndroidApp | undefined = selectedApp
      ? {
          id: selectedApp.id,
          name: selectedApp.name,
          version:
            selectedApp.metadata?.version || selectedApp.version || "Unknown",
        }
      : undefined;

    // Create updated SauceLabs config
    const updatedSauceLabsConfig: SauceLabsConfig = {
      ...androidSettings.sauceLabs,
      selectedApp: appObject,
    };

    // Create new Android settings
    const newSettings: AndroidEnvironmentSettings = {
      ...androidSettings,
      platform: "android",
      sauceLabs: updatedSauceLabsConfig,
    };

    // Update environment settings
    updateEnvironmentSettings(newSettings);
  };

  // Handle Testinium app selection
  const handleTestiniumAppSelect = (appId: string) => {
    // Find the selected app to get its details
    const selectedApp = testiniumApps.find((app) => app.id === appId);

    // Get current Android settings or create default ones
    const androidSettings = isAndroidSettings(environmentSettings)
      ? environmentSettings
      : {
          platform: "android" as const,
          sauceLabs: {
            username: "",
            accessKey: "",
            selectedDevices: [],
          },
          testinium: {
            apiUrl: "",
            clientId: "",
            clientSecret: "",
            issuerUri: "",
            selectedDevices: [],
          },
        };

    // Create the app object - use fileKey for Testinium apps
    const appObject: AndroidApp | undefined = selectedApp
      ? {
          id: selectedApp.fileKey, // Use fileKey instead of id for Testinium
          name: selectedApp.name,
          version: selectedApp.version || "Unknown",
          fileKey: selectedApp.fileKey,
        }
      : undefined;

    // Create updated Testinium config
    const updatedTestiniumConfig = {
      ...androidSettings.testinium,
      selectedApp: appObject,
    };

    // Create new Android settings
    const newSettings: AndroidEnvironmentSettings = {
      ...androidSettings,
      platform: "android",
      testinium: updatedTestiniumConfig,
    };

    // Update environment settings
    updateEnvironmentSettings(newSettings);

    // Log the selected app for debugging
    console.log(
      "Selected app:",
      selectedApp
        ? JSON.stringify(
            {
              id: selectedApp.id,
              name: selectedApp.name,
              version:
                selectedApp.metadata?.version ||
                selectedApp.version ||
                "Unknown",
            },
            null,
            2,
          )
        : "No app selected",
    );

    console.log(
      "Updated environment settings:",
      JSON.stringify(newSettings, null, 2),
    );
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const fileExtension = file.name.split(".").pop()?.toLowerCase();
      if (fileExtension !== "apk") {
        toast.error("Invalid file type", {
          description: "Only .apk files are allowed for Android applications.",
        });
        return;
      }

      // Validate file size (max 100MB)
      if (file.size > 100 * 1024 * 1024) {
        toast.error("File too large", {
          description: "Maximum file size is 100MB.",
        });
        return;
      }

      setAppFile(file);
    }
  };

  // Upload app to SauceLabs
  const uploadApp = async () => {
    if (!appFile) {
      toast.error("No file selected", {
        description: "Please select an APK file to upload.",
      });
      return;
    }

    setIsUploadingApp(true);
    setUploadProgress(0);

    try {
      // Create a new FormData instance
      const formData = new FormData();

      // Add the app file
      formData.append("app", appFile);

      // Add description if provided
      if (appDescription.trim()) {
        formData.append("description", appDescription.trim());
      }

      // Get the API URL and auth token
      const apiUrl = `${API_BASE_URL}/plugins/upload-saucelabs-app`;
      const token =
        localStorage.getItem("authToken") ||
        sessionStorage.getItem("authToken");

      // Use XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            toast.success("App uploaded successfully", {
              description: "The application has been uploaded to SauceLabs.",
            });
            // Reset form
            setAppFile(null);
            setAppDescription("");
            setShowUploadForm(false);
            // Refresh app list
            loadSauceLabsApps();
          } else {
            toast.error("Upload failed", {
              description:
                response.error || "Failed to upload app to SauceLabs.",
            });
          }
        } else {
          toast.error("Upload failed", {
            description: "Failed to upload app to SauceLabs. Please try again.",
          });
        }
        setIsUploadingApp(false);
      });

      xhr.addEventListener("error", () => {
        toast.error("Upload failed", {
          description:
            "Network error occurred while uploading. Please try again.",
        });
        setIsUploadingApp(false);
      });

      xhr.addEventListener("abort", () => {
        toast.error("Upload aborted", {
          description: "The upload was cancelled.",
        });
        setIsUploadingApp(false);
      });

      // Open and send the request
      xhr.open("POST", apiUrl);
      xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      xhr.send(formData);
    } catch (error) {
      console.error("Error uploading app:", error);
      toast.error("Upload failed", {
        description:
          error instanceof Error ? error.message : "An unknown error occurred.",
      });
      setIsUploadingApp(false);
    }
  };

  // Upload app to Testinium
  const uploadTestiniumApp = async () => {
    if (!appFile) {
      toast.error("No file selected", {
        description: "Please select an APK file to upload.",
      });
      return;
    }

    setIsUploadingApp(true);
    setUploadProgress(0);

    try {
      // Create a new FormData instance
      const formData = new FormData();

      // Add the app file
      formData.append("app", appFile);

      // Add description if provided
      if (appDescription.trim()) {
        formData.append("description", appDescription.trim());
      }

      // Get the API URL and auth token
      const apiUrl = `${API_BASE_URL}/plugins/upload-testinium-app`;
      const token =
        localStorage.getItem("authToken") ||
        sessionStorage.getItem("authToken");

      // Use XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            toast.success("App uploaded successfully", {
              description: "The application has been uploaded to Testinium.",
            });
            // Reset form
            setAppFile(null);
            setAppDescription("");
            setShowUploadForm(false);
            // Refresh app list
            loadTestiniumApps();
          } else {
            toast.error("Upload failed", {
              description:
                response.error || "Failed to upload app to Testinium.",
            });
          }
        } else {
          toast.error("Upload failed", {
            description: "Failed to upload app to Testinium. Please try again.",
          });
        }
        setIsUploadingApp(false);
      });

      xhr.addEventListener("error", () => {
        toast.error("Upload failed", {
          description:
            "Network error occurred while uploading. Please try again.",
        });
        setIsUploadingApp(false);
      });

      xhr.addEventListener("abort", () => {
        toast.error("Upload aborted", {
          description: "The upload was cancelled.",
        });
        setIsUploadingApp(false);
      });

      // Open and send the request
      xhr.open("POST", apiUrl);
      xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      xhr.send(formData);
    } catch (error) {
      console.error("Error uploading app to Testinium:", error);
      toast.error("Upload failed", {
        description:
          error instanceof Error ? error.message : "An unknown error occurred.",
      });
      setIsUploadingApp(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* SauceLabs Apps Section - Only show when SauceLabs is active */}
      {activeProviders.sauceLabs && (
        <div className="space-y-4">
          <div className="p-4 border rounded-md bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-950/30 dark:to-teal-950/30 border-green-200 dark:border-green-800 shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="bg-green-100 dark:bg-green-900/50 p-1.5 rounded-full mr-2">
                  <Layers className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-green-700 dark:text-green-300">
                    SauceLabs Android Applications
                  </h4>
                  {isAndroidSettings(environmentSettings) &&
                    environmentSettings.sauceLabs.selectedApp && (
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        Selected:{" "}
                        {environmentSettings.sauceLabs.selectedApp.name}
                        {environmentSettings.sauceLabs.selectedApp.version &&
                          ` (v${environmentSettings.sauceLabs.selectedApp.version})`}
                      </div>
                    )}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowUploadForm(!showUploadForm)}
                  className="h-8 transition-all duration-150 border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/20"
                >
                  <Upload className="h-4 w-4 mr-2 text-green-600" />
                  {showUploadForm ? "Hide Upload" : "Upload App"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadSauceLabsApps}
                  disabled={isLoadingApps}
                  className="h-8 transition-all duration-150 border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/20"
                >
                  {isLoadingApps ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin text-green-600" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2 text-green-600" />
                  )}
                  Refresh
                </Button>
              </div>
            </div>

            {/* App Upload Form */}
            {showUploadForm && (
              <div className="mt-3 mb-4 p-3 bg-white dark:bg-gray-800 rounded-md border border-green-200 dark:border-green-800">
                <h5 className="text-sm font-medium mb-2 flex items-center">
                  <Upload className="h-4 w-4 mr-1 text-green-600" />
                  Upload New Android App (.apk)
                </h5>
                <div className="space-y-3">
                  {/* File Upload Area */}
                  <div className="flex flex-col items-center justify-center w-full">
                    <label
                      htmlFor="app-file"
                      className={`flex flex-col items-center justify-center w-full h-24 border-2 border-dashed rounded-lg cursor-pointer
                        ${
                          appFile
                            ? "bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-700"
                            : "bg-gray-50 dark:bg-gray-900/50 border-gray-300 dark:border-gray-700"
                        }
                        hover:bg-gray-100 dark:hover:bg-gray-800/50 transition-colors duration-200`}
                    >
                      <div className="flex flex-col items-center justify-center pt-3 pb-4">
                        {appFile ? (
                          <>
                            <Smartphone className="w-8 h-8 mb-1 text-green-500" />
                            <p className="text-sm font-medium text-green-600 dark:text-green-400">
                              {appFile.name}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {(appFile.size / (1024 * 1024)).toFixed(2)} MB
                            </p>
                          </>
                        ) : (
                          <>
                            <Upload className="w-8 h-8 mb-1 text-gray-400" />
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              <span className="font-semibold">
                                Click to upload
                              </span>{" "}
                              or drag and drop
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Android (.APK) files only
                            </p>
                          </>
                        )}
                      </div>
                      <input
                        id="app-file"
                        type="file"
                        accept=".apk"
                        onChange={handleFileChange}
                        className="hidden"
                        disabled={isUploadingApp}
                      />
                    </label>
                  </div>

                  {/* Description Field */}
                  <div className="space-y-1">
                    <label
                      htmlFor="app-description"
                      className="text-xs font-medium text-gray-700 dark:text-gray-300"
                    >
                      Description (optional)
                    </label>
                    <input
                      id="app-description"
                      type="text"
                      value={appDescription}
                      onChange={(e) => setAppDescription(e.target.value)}
                      placeholder="Enter a description for this app"
                      className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-600 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-600"
                      disabled={isUploadingApp}
                    />
                  </div>

                  {/* Upload Progress */}
                  {isUploadingApp && (
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          Uploading...
                        </span>
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          {uploadProgress}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Upload Button */}
                  <div className="flex justify-end">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={uploadApp}
                      disabled={!appFile || isUploadingApp}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      {isUploadingApp ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload App
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* App Selection Area */}
            <div className="bg-white dark:bg-gray-800 rounded-md p-3 border border-green-200 dark:border-green-800">
              {isLoadingApps ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Skeleton className="h-24 w-full rounded-md" />
                    <Skeleton className="h-24 w-full rounded-md" />
                  </div>
                </div>
              ) : sauceLabsApps.length > 0 ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Available Applications ({sauceLabsApps.length})
                    </h5>
                    {isAndroidSettings(environmentSettings) &&
                      environmentSettings.sauceLabs.selectedApp && (
                        <Badge
                          variant="outline"
                          className="text-xs bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800"
                        >
                          1 Selected
                        </Badge>
                      )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto pr-1">
                    {sauceLabsApps.map((app) => (
                      <Card
                        key={app.id}
                        className={`border hover:shadow-md transition-all duration-200 cursor-pointer overflow-hidden ${
                          isAndroidSettings(environmentSettings) &&
                          environmentSettings.sauceLabs.selectedApp?.id ===
                            app.id
                            ? "border-green-300 dark:border-green-700 bg-green-50 dark:bg-green-900/20"
                            : "border-gray-200 dark:border-gray-700"
                        }`}
                        onClick={() => handleSauceLabsAppSelect(app.id)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-2">
                              <div className="mt-0.5 bg-green-100 dark:bg-green-900/50 p-1.5 rounded-full">
                                <Smartphone className="h-4 w-4 text-green-600 dark:text-green-400" />
                              </div>
                              <div>
                                <h6 className="text-sm font-medium line-clamp-1">
                                  {app.name}
                                </h6>
                                <div className="flex items-center mt-1 space-x-2">
                                  {(app.metadata?.version || app.version) && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs px-1.5 py-0 h-5"
                                    >
                                      v{app.metadata?.version || app.version}
                                    </Badge>
                                  )}
                                  {app.uploaded && (
                                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                      <Calendar className="h-3 w-3 mr-1" />
                                      {formatDistanceToNow(
                                        new Date(app.uploaded),
                                        { addSuffix: true },
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                            {isAndroidSettings(environmentSettings) &&
                              environmentSettings.sauceLabs.selectedApp?.id ===
                                app.id && (
                                <Badge className="bg-green-500 text-white border-0">
                                  Selected
                                </Badge>
                              )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-6">
                  <Package className="h-10 w-10 mx-auto text-gray-400 mb-2" />
                  <h5 className="text-sm font-medium mb-1">
                    No Applications Found
                  </h5>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                    Upload an Android application to get started.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowUploadForm(true)}
                    className="border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/20"
                  >
                    <Upload className="h-4 w-4 mr-2 text-green-600" />
                    Upload App
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Testinium Apps Section - Only show when Testinium is active */}
      {activeProviders.testinium && (
        <div className="space-y-4">
          <div className="p-4 border rounded-md bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/30 dark:to-indigo-950/30 border-purple-200 dark:border-purple-800 shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="bg-purple-100 dark:bg-purple-900/50 p-1.5 rounded-full mr-2">
                  <Layers className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-purple-700 dark:text-purple-300">
                    Testinium Applications
                  </h4>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {isAndroidSettings(environmentSettings) &&
                    environmentSettings.testinium.selectedApp ? (
                      <div className="text-purple-600 dark:text-purple-400 font-medium">
                        Selected:{" "}
                        {environmentSettings.testinium.selectedApp.name}
                        {environmentSettings.testinium.selectedApp.version &&
                          ` (v${environmentSettings.testinium.selectedApp.version})`}
                      </div>
                    ) : (
                      "Select an application to use with your tests"
                    )}
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowUploadForm(!showUploadForm)}
                  className="h-8 transition-all duration-150 border-purple-200 dark:border-purple-800 hover:bg-purple-100 dark:hover:bg-purple-900/20"
                >
                  <Upload className="h-4 w-4 mr-2 text-purple-600" />
                  {showUploadForm ? "Hide Upload" : "Upload App"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadTestiniumApps}
                  disabled={isLoadingApps}
                  className="h-8 transition-all duration-150 border-purple-200 dark:border-purple-800 hover:bg-purple-100 dark:hover:bg-purple-900/20"
                >
                  {isLoadingApps ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin text-purple-600" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2 text-purple-600" />
                  )}
                  Refresh
                </Button>
              </div>
            </div>
          </div>

          {/* Testinium Upload Form */}
          {showUploadForm && (
            <div className="bg-purple-50 dark:bg-purple-950/30 rounded-md p-4 border border-purple-200 dark:border-purple-800">
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-purple-700 dark:text-purple-300">
                  Upload New Application to Testinium
                </h4>

                <div className="space-y-3">
                  {/* File Upload Area */}
                  <div className="space-y-1">
                    <label
                      htmlFor="testinium-app-file"
                      className="text-xs font-medium text-gray-700 dark:text-gray-300"
                    >
                      Application File
                    </label>
                    <label
                      htmlFor="testinium-app-file"
                      className="flex flex-col items-center justify-center w-full h-24 border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-md cursor-pointer bg-purple-50 dark:bg-purple-950/50 hover:bg-purple-100 dark:hover:bg-purple-900/50 transition-colors"
                    >
                      <div className="flex flex-col items-center justify-center pt-2 pb-2">
                        {appFile ? (
                          <>
                            <Package className="w-6 h-6 mb-1 text-purple-600" />
                            <p className="text-sm font-medium text-purple-700 dark:text-purple-300">
                              {appFile.name}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {(appFile.size / (1024 * 1024)).toFixed(2)} MB
                            </p>
                          </>
                        ) : (
                          <>
                            <Upload className="w-8 h-8 mb-1 text-gray-400" />
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              <span className="font-semibold">
                                Click to upload
                              </span>{" "}
                              or drag and drop
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Android (.APK) files only
                            </p>
                          </>
                        )}
                      </div>
                      <input
                        id="testinium-app-file"
                        type="file"
                        accept=".apk"
                        onChange={handleFileChange}
                        className="hidden"
                        disabled={isUploadingApp}
                      />
                    </label>
                  </div>

                  {/* Description Field */}
                  <div className="space-y-1">
                    <label
                      htmlFor="testinium-app-description"
                      className="text-xs font-medium text-gray-700 dark:text-gray-300"
                    >
                      Description (optional)
                    </label>
                    <input
                      id="testinium-app-description"
                      type="text"
                      value={appDescription}
                      onChange={(e) => setAppDescription(e.target.value)}
                      placeholder="Enter a description for this app"
                      className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-600"
                      disabled={isUploadingApp}
                    />
                  </div>

                  {/* Upload Progress */}
                  {isUploadingApp && (
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          Uploading...
                        </span>
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          {uploadProgress}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Upload Button */}
                  <div className="flex justify-end">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={uploadTestiniumApp}
                      disabled={!appFile || isUploadingApp}
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                    >
                      {isUploadingApp ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload App
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Testinium Apps List */}
          <div className="bg-white dark:bg-gray-800 rounded-md p-4 border border-purple-200 dark:border-purple-800">
            {isLoadingApps ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-4 w-4 rounded" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  </div>
                ))}
              </div>
            ) : testiniumApps.length > 0 ? (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Available Applications
                </Label>
                <Select
                  value={
                    isAndroidSettings(environmentSettings)
                      ? environmentSettings.testinium.selectedApp?.id || ""
                      : ""
                  }
                  onValueChange={handleTestiniumAppSelect}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select an application" />
                  </SelectTrigger>
                  <SelectContent>
                    {testiniumApps.map((app) => (
                      <SelectItem key={app.id} value={app.id}>
                        <div className="flex items-center space-x-2">
                          <Package className="h-4 w-4 text-purple-500" />
                          <div className="flex flex-col">
                            <span className="font-medium">{app.name}</span>
                            <span className="text-xs text-gray-500">
                              {app.fileName}
                              {app.fileSize &&
                                ` • ${(app.fileSize / (1024 * 1024)).toFixed(1)} MB`}
                              {app.uploadedAt &&
                                ` • ${formatDistanceToNow(new Date(app.uploadedAt), { addSuffix: true })}`}
                            </span>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  No applications found
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Upload applications using the upload button above to get
                  started.
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* No Provider Selected */}
      {!activeProviders.sauceLabs && !activeProviders.testinium && (
        <div className="p-4 border rounded-md bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700 shadow-sm text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Please select a device farm provider in the Devices tab to manage
            applications.
          </p>
        </div>
      )}
    </div>
  );
}
