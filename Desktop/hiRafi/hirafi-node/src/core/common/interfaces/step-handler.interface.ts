/**
 * Step Handler Interface
 * Defines the contract for test step handlers
 */

import { ScenarioStep } from '../../../models/types.js';

/**
 * Step Handler Interface
 * Handles executing specific types of test steps
 */
export interface IStepHandler {
  /**
   * Execute a test step
   * @param target The target to execute the step on (Puppeteer Page, WebdriverIO Browser, etc.)
   * @param step The step to execute
   * @param agent The AI agent to use for AI-powered steps
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  execute(
    target: any,
    step: ScenarioStep,
    agent: any,
    logs: string[]
  ): Promise<{ success: boolean; error: string | null; data?: any }>;

  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step, false otherwise
   */
  canHandle(step: ScenarioStep): boolean;

  /**
   * Get the name of this step handler
   * @returns The name of the step handler
   */
  getName(): string;
}
