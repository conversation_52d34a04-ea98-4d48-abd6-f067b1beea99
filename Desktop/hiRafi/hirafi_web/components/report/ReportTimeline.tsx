import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle2,
  XCircle,
  Terminal,
  Play,
  MousePointer,
  Clock,
  ArrowRight,
  Search,
  Keyboard,
  AlertTriangle,
  Eye,
  Hourglass
} from 'lucide-react';
import { ReportDetail } from '@/hooks/useReport';
import { AIOperationLog } from './AIOperationLog';
import { formatTimelineDuration } from '@/lib/utils/duration-utils';
import { getAIMethodConfig, isAIMethod } from '@/lib/utils/ai-method-config';

type Step = ReportDetail['steps'][0];

interface ReportTimelineProps {
  report: ReportDetail | null;
}

function getTimelineIcon(step: Step) {
  if (!step.success) {
    return <XCircle className="h-4 w-4 text-rose-500" />;
  }

  // Use centralized AI method configuration
  if (isAIMethod(step.type)) {
    const config = getAIMethodConfig(step.type);
    const IconComponent = config.icon;

    // For aiAssertion, use different icon based on success/failure
    if (step.type === "aiAssertion" && !step.success) {
      return <AlertTriangle className="h-4 w-4 text-rose-500" />;
    }

    return <IconComponent className={`h-4 w-4 ${config.color}`} />;
  }

  return <Terminal className="h-4 w-4 text-gray-500" />;
}

function getTimelineIconBg(step: Step) {
  if (!step.success) {
    return "bg-rose-100 dark:bg-rose-900/30";
  }

  // Use centralized AI method configuration
  if (isAIMethod(step.type)) {
    const config = getAIMethodConfig(step.type);
    return config.bgColor;
  }

  return "bg-gray-100 dark:bg-gray-800";
}

// Function to format error message by removing midscene references and prefixes
function formatErrorMessage(errorMessage: string): string {
  if (!errorMessage) return '';

  // Replace midscene/midscenejs with hirafi
  errorMessage = errorMessage.replace(/midscene(js)?/gi, 'hirafi');

  // Remove midscene reference and troubleshooting links
  if (errorMessage.includes('hirafi.com')) {
    return errorMessage.split('Trouble shooting:')[0].trim();
  }

  // Remove AI Assertion error prefix if present
  if (errorMessage.startsWith('AI Assertion error:')) {
    return errorMessage.replace('AI Assertion error:', '').trim();
  }

  return errorMessage;
}

// Helper function to safely format dates
function safeFormatDate(timestamp: number | string | undefined | null, fallback: string = 'Time unavailable'): string {
  if (timestamp === undefined || timestamp === null) return fallback;

  try {
    // Handle string timestamps by parsing them
    const time = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;

    // Check if the parsed value is a valid number
    if (isNaN(time as number)) return fallback;

    // Create and validate the date object
    const date = new Date(time);
    if (isNaN(date.getTime())) return fallback;

    // Format the date
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return fallback;
  }
}



// Function to check if a log is a JSON AI operation log
function isAIOperationLog(log: string): boolean {
  try {
    // Basic validation before attempting to parse
    if (!log || typeof log !== 'string') return false;
    const trimmedLog = log.trim();
    if (!trimmedLog.startsWith('{') || !trimmedLog.endsWith('}')) return false;

    // Try to parse the JSON
    const data = JSON.parse(trimmedLog);

    // Validate that it has the expected structure
    return !!data &&
           typeof data === 'object' &&
           !!data.operation &&
           ['aiAction', 'aiTap', 'aiRightClick', 'aiHover', 'aiScroll', 'aiInput', 'aiKeyboardPress',
            'aiWaitElement', 'aiWaitFor', 'aiLocate', 'aiAssertion', 'aiQuery', 'aiString', 'aiNumber', 'aiBoolean'].includes(data.operation);
  } catch (e) {
    // Any parsing error means it's not a valid AI operation log
    return false;
  }
}



export function ReportTimeline({ report }: ReportTimelineProps) {
  if (!report || !report.steps || report.steps.length === 0) {
    // Handle case where there are no steps
    return (
      <Card>
        <CardContent className="p-4 text-center text-gray-500">
          No timeline data available.
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="relative">
          <div className="absolute top-0 bottom-0 left-6 w-0.5 bg-gray-200 dark:bg-gray-800"></div>
          <div className="space-y-6">
            {report.steps.map((step, index) => {
              // Calculate step start time, ensuring it's a valid number
              let stepStartTime;
              try {
                // Check if startTime is a valid number
                const baseTime = typeof report.startTime === 'number' ? report.startTime :
                                (typeof report.startTime === 'string' ? parseInt(report.startTime, 10) :
                                (report.date ? new Date(report.date).getTime() : Date.now()));

                // Only add durations if baseTime is valid
                if (!isNaN(baseTime)) {
                  stepStartTime = baseTime + report.steps.slice(0, index).reduce((acc, s) => acc + (s.duration || 0), 0);
                } else {
                  // Fallback to current time if baseTime is invalid
                  stepStartTime = Date.now();
                }
              } catch (e) {
                console.error('Error calculating step time:', e);
                stepStartTime = Date.now(); // Fallback to current time
              }

              // Use our safe date formatting helper
              const stepTime = safeFormatDate(stepStartTime);

              // Format the step name for display - simple approach
              let displayName = step.name || `${step.type || 'Step'} ${index + 1}`;

              // First check for JSON AI operation logs - these have the most accurate descriptions
              if (step.logs && step.logs.length > 0) {
                const firstLog = step.logs[0];
                if (isAIOperationLog(firstLog)) {
                  try {
                    const data = JSON.parse(firstLog);
                    if (data.description) {
                      displayName = data.description;
                    }
                  } catch (e) {
                    // Fall through to other methods
                  }
                } else {
                  // Handle goto steps from logs
                  if (step.type === 'goto') {
                    displayName = firstLog
                      .replace('🌐 Navigating to: ', '')
                      .replace('Navigating to: ', '')
                      .replace(/^["']|["']$/g, ''); // Remove quotes from start and end
                  }
                }
              }

              // If no meaningful name from logs, use prompt/value for AI methods
              if (displayName === step.name || displayName === `${step.type || 'Step'} ${index + 1}`) {
                if (step.type === 'goto') {
                  const url = step.url || step.value;
                  displayName = url ? `Navigate to ${url}` : 'Navigate to page';
                } else if (step.type.startsWith('ai')) {
                  const promptOrValue = step.prompt || step.value;
                  if (promptOrValue) {
                    if (step.type === 'aiInput' && step.target) {
                      displayName = `Input "${promptOrValue}" into ${step.target}`;
                    } else {
                      displayName = promptOrValue;
                    }
                  }
                } else if (step.type === 'sleep') {
                  const duration = step.duration || step.value;
                  displayName = duration ? `Wait ${duration}s` : 'Wait';
                }
              }

              return (
                <div key={step.id || index} className="flex gap-4">
                  <div
                    className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full ${getTimelineIconBg(step)}`}
                  >
                    {getTimelineIcon(step)}
                  </div>
                  <div className="flex-1 pt-1.5">
                    {/* Check if displayName is a JSON string and extract description if it is */}
                    {typeof displayName === 'string' && displayName.startsWith('{') && displayName.endsWith('}') ? (
                      (() => {
                        try {
                          const data = JSON.parse(displayName);
                          return <p className="text-sm font-medium">{data.description || 'AI Operation'}</p>;
                        } catch {
                          return <p className="text-sm font-medium">{displayName}</p>;
                        }
                      })()
                    ) : (
                      <p className="text-sm font-medium">{displayName}</p>
                    )}

                    {/* Add step details when available */}
                    {step.logs && step.logs.length > 0 && (
                      <div className="mt-2">
                        {/* Display AI operation logs (JSON format) - only the first one to avoid clutter */}
                        {step.logs.some(log => isAIOperationLog(log)) && (
                          <div>
                            {/* Only show the first AI operation log to keep the timeline clean */}
                            <div className="text-xs">
                              {(() => {
                                // Find the first valid AI operation log
                                const aiLog = step.logs.find(log => isAIOperationLog(log)) || '';

                                // For aiAssertion, make sure we're handling it properly
                                try {
                                  if (aiLog && step.type === 'aiAssertion') {
                                    const data = JSON.parse(aiLog);
                                    if (data.operation === 'aiAssertion') {
                                      return <AIOperationLog log={aiLog} compact={true} />;
                                    }
                                  }
                                  // For other operation types or if parsing fails
                                  return <AIOperationLog log={aiLog} compact={true} />;
                                } catch (e) {
                                  // Fallback for any parsing errors
                                  return <AIOperationLog log={aiLog} compact={true} />;
                                }
                              })()}
                            </div>
                          </div>
                        )}

                        {/* Then show traditional logs for aiAction (only non-JSON logs) */}
                        {step.type === 'aiAction' &&
                          step.logs.filter(log => !isAIOperationLog(log) && (log.includes('Element bulundu') || log.includes('Eylem'))).length > 0 && (
                          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            {step.logs
                              .filter(log => !isAIOperationLog(log) && (log.includes('Element bulundu') || log.includes('Eylem')))
                              .map((log, i) => (
                                <span key={i} className="block">
                                  {log.replace('⚡ Eylem (Tap): ', '').replace('🔍 Element bulundu: ', '')}
                                </span>
                            ))}
                          </p>
                        )}
                      </div>
                    )}

                    {/* aiWaitElement logs are now handled in the main logs section above */}

                    <div className="flex items-center gap-2 mt-1">
                      <p className="text-xs text-gray-500">{stepTime}</p>
                      <p className="text-xs text-gray-500">
                        Duration: {formatTimelineDuration(step.duration)}
                      </p>
                      {!step.success && (
                        <Badge variant="destructive" className="text-xs">Failed</Badge>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}