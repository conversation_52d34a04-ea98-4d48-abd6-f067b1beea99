"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { toast } from "@/lib/utils/toast-utils"
import { Trash2, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, File<PERSON> } from "lucide-react"
import { useScenariosActions } from "@/hooks/useScenariosActions"

interface BulkDeleteScenarioDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  scenarioIds: string[]
  scenarios?: Array<{ id: string; name: string; }>
  onSuccess?: (deletedCount: number, inUseScenarios?: any[]) => void
}

export function BulkDeleteScenarioDialog({
  open,
  onOpenChange,
  scenarioIds,
  scenarios = [],
  onSuccess
}: BulkDeleteScenarioDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  // Get bulk delete function
  const { bulkDeleteScenarios } = useScenariosActions()

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    if (scenarioIds.length === 0) return

    setIsDeleting(true)

    try {
      // Call bulk delete API
      const result = await bulkDeleteScenarios(scenarioIds)

      if (result.success) {
        // Başarılı silme durumu
        if (result.inUseScenarios && result.inUseScenarios.length > 0) {
          // Kısmi başarı - bazı senaryolar silinemedi
          toast.warning("Kısmi Silme Tamamlandı", {
            description: `${result.deletedCount} senaryo silindi, ${result.inUseScenarios.length} senaryo aktif run'larda kullanıldığı için silinemedi`
          })
        } else {
          // Tam başarı - tüm senaryolar silindi
          toast.success("Senaryolar Silindi", {
            description: `${result.deletedCount} senaryo başarıyla silindi`
          })
        }

        // Call success callback
        if (onSuccess) {
          onSuccess(result.deletedCount, result.inUseScenarios)
        }

        // Close dialog
        onOpenChange(false)
      } else {
        // Handle failure cases
        if (result.inUseScenarios && result.inUseScenarios.length > 0) {
          // Scenarios couldn't be deleted because they're in use
          toast.error("Senaryolar Silinemedi", {
            description: "Seçilen senaryolar aktif run'larda kullanıldığı için silinemedi"
          })
          if (onSuccess) {
            onSuccess(result.deletedCount || 0, result.inUseScenarios)
          }
          onOpenChange(false)
        } else {
          // Other error cases
          toast.error("Senaryo Silme Hatası", {
            description: result.error || "Senaryolar silinirken bir hata oluştu"
          })
        }
      }
    } catch (error) {
      console.error("Bulk deletion error:", error)
      toast.error("Senaryo Silme Hatası", {
        description: "Senaryolar silinirken bir hata oluştu"
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] border-0 shadow-lg bg-white dark:bg-gray-900 rounded-xl">
        <DialogHeader className="pb-4 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30">
              <Trash2 className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold bg-gradient-to-r from-red-600 to-red-700 bg-clip-text text-transparent">
                Bulk Delete Scenarios
              </DialogTitle>
              <DialogDescription className="text-gray-500 dark:text-gray-400">
                Permanently delete {scenarioIds.length} selected scenarios. This action cannot be undone.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-5 py-5">
          {/* Warning Alert */}
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-100 dark:border-red-800/30">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
              <span className="text-sm font-medium text-red-700 dark:text-red-300">
                <strong>Warning:</strong> This action is permanent and cannot be undone. 
                All selected scenarios and their data will be permanently deleted from the system.
              </span>
            </div>
          </div>

          {/* Scenario Count Info */}
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-100 dark:border-red-800/30">
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-red-600 dark:text-red-400" />
              <span className="text-sm font-medium text-red-700 dark:text-red-300">
                {scenarioIds.length} scenarios selected for deletion
              </span>
            </div>
          </div>

          {/* Scenarios List (if provided) */}
          {scenarios.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <FileX className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Scenarios to be deleted:
                </span>
              </div>
              <div className="max-h-40 overflow-y-auto border rounded-md p-3 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <div className="space-y-2">
                  {scenarios.slice(0, 10).map((scenario, index) => (
                    <div key={scenario.id} className="flex items-center gap-2 text-sm">
                      <div className="w-1.5 h-1.5 rounded-full bg-red-500"></div>
                      <span className="text-gray-700 dark:text-gray-300 truncate">
                        {scenario.name}
                      </span>
                    </div>
                  ))}
                  {scenarios.length > 10 && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 text-center pt-2 border-t border-gray-200 dark:border-gray-600">
                      ... and {scenarios.length - 10} more scenarios
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Important Note */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Important Notes:
              </h4>
              <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                <li className="flex items-start gap-2">
                  <div className="w-1 h-1 rounded-full bg-gray-400 mt-1.5 flex-shrink-0"></div>
                  <span>Scenarios that are currently being used in active test runs cannot be deleted</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1 h-1 rounded-full bg-gray-400 mt-1.5 flex-shrink-0"></div>
                  <span>If some scenarios are in use, only the available ones will be deleted</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1 h-1 rounded-full bg-gray-400 mt-1.5 flex-shrink-0"></div>
                  <span>You will be notified about any scenarios that couldn't be deleted</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <DialogFooter className="border-t border-gray-100 dark:border-gray-800 pt-4 mt-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            Cancel
          </Button>
          <Button
            onClick={handleBulkDelete}
            disabled={isDeleting || scenarioIds.length === 0}
            variant="destructive"
            className="gap-2 bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 border-none"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting {scenarioIds.length} scenarios...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete {scenarioIds.length} Scenarios
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 