/**
 * Directory Utilities
 * Utilities for handling directory creation and management
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * Ensures a directory exists, creating it if necessary
 * @param dirPath Path to the directory
 * @param recursive Whether to create parent directories
 */
export function ensureDirectoryExists(dirPath: string, recursive: boolean = true): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive });
  }
}

/**
 * Creates multiple directories if they don't exist
 * @param directories Array of directory paths to create
 * @param recursive Whether to create parent directories
 */
export function ensureDirectoriesExist(directories: string[], recursive: boolean = true): void {
  directories.forEach(dir => ensureDirectoryExists(dir, recursive));
}

/**
 * Gets the absolute path for a directory, creating it if necessary
 * @param dirPath Directory path (can be relative or absolute)
 * @param createIfNotExists Whether to create the directory if it doesn't exist
 * @returns Absolute path to the directory
 */
export function getAbsoluteDirectoryPath(dirPath: string, createIfNotExists: boolean = true): string {
  const absolutePath = path.isAbsolute(dirPath) ? dirPath : path.resolve(dirPath);

  if (createIfNotExists) {
    ensureDirectoryExists(absolutePath);
  }

  return absolutePath;
}

/**
 * Validates that a directory path is valid and accessible
 * @param dirPath Directory path to validate
 * @throws Error if directory is not accessible
 */
export function validateDirectoryPath(dirPath: string): void {
  try {
    const stats = fs.statSync(dirPath);
    if (!stats.isDirectory()) {
      throw new Error(`Path ${dirPath} is not a directory`);
    }

    // Test write access
    fs.accessSync(dirPath, fs.constants.W_OK);
  } catch (error) {
    throw new Error(`Directory ${dirPath} is not accessible: ${error}`);
  }
}

/**
 * Gets a temporary directory path with optional subdirectory
 * @param subdirectory Optional subdirectory name
 * @param createIfNotExists Whether to create the directory if it doesn't exist
 * @returns Path to the temporary directory
 */
export function getTempDirectory(subdirectory?: string, createIfNotExists: boolean = true): string {
  const tempDir = subdirectory ? path.join(os.tmpdir(), subdirectory) : os.tmpdir();

  if (createIfNotExists) {
    ensureDirectoryExists(tempDir);
  }

  return tempDir;
}
