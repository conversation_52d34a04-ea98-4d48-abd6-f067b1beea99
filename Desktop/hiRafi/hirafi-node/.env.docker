# Test Node Docker Yapılandırması
# Node Ayarları
NODE_NAME=test-node-1
NODE_ID=test-node-1
HUB_URL=http://test-hub:5000
NODE_CAPABILITIES=chrome,edge,firefox

# WebSocket Yapılandırması
WEBSOCKET_ENABLED=true

# Docker konteynerlerde sistem bilgilerini topla
USE_SYSTEM_INFO=true
DOCKER_ENV=true

# Redis Yapılandırması
REDIS_ENABLED=true
REDIS_HOST=test-hub-redis
REDIS_PORT=6379
REDIS_USERNAME=default
REDIS_PASSWORD=redispassword
REDIS_DB=0
REDIS_PREFIX=

# Ekran Yapılandırması (Headless)
DISPLAY=:0
RESOLUTION=1920x1080x24

# Puppeteer Yapılandırması
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-gpu"

# Loglar
LOG_LEVEL=info

# Storage Yapılandırması
# MinIO'yu etkinleştir
MINIO_ENABLED=true
MINIO_ENDPOINT=**********
MINIO_PORT=9000
MINIO_USE_SSL=false
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SCREENSHOT_BUCKET=test-screenshots
MINIO_REPORT_BUCKET=test-reports
MINIO_PUBLIC_URL=http://**********:9000

# AWS S3 Yapılandırması
S3_ENABLED=false
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
S3_SCREENSHOT_BUCKET=test-screenshots
S3_REPORT_BUCKET=test-reports
S3_PUBLIC_URL=https://your-bucket.s3.eu-west-1.amazonaws.com