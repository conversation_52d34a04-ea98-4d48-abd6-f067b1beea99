import { runApi } from '@/lib/api';
import { Run } from '@/lib/utils/run-logic';
import { getBestTestResults, determineRunStatus } from '@/lib/utils/run-utils';

/**
 * SWR fetcher for runs list
 */
export async function runsFetcher(queryKey: string): Promise<{
  runs: Run[];
  totalCount: number;
}> {
  const params = JSON.parse(queryKey);
  
  const response = await runApi.getRuns(params);
  
  if (!response.success) {
    throw new Error(response.error || 'Failed to fetch runs');
  }
  
  // Enrich each run with calculated testResults (and a normalized status)
  const rawRuns = response.runs || response.data?.runs || [];
  const enrichedRuns: Run[] = rawRuns.map((run: any) => {
    const testResults = getBestTestResults(run);
    const derivedStatus = determineRunStatus(run, testResults);

    return {
      ...run,
      // Inject the best available testResults so UI components like RunCard can
      // show queued / running / completed / failed counts even if backend did
      // not include them directly.
      testResults,
      // If API already provided a status keep it, otherwise fallback to derived
      // one. This prevents overriding intentional API values while still
      // ensuring we always have a sensible status.
      status: run.status || derivedStatus
    } as Run;
  });

  return {
    runs: enrichedRuns,
    // Backend returns 'total' but we need 'totalCount' for consistency
    totalCount: response.total || response.data?.total || response.data?.totalCount || 0
  };
}

/**
 * SWR fetcher for single run detail
 */
export async function runDetailFetcher(runId: string): Promise<{
  run: any;
  tests: any[];
  reports: any[];
  stepProgressSummary: any;
}> {
  console.log('[runDetailFetcher] Fetching run detail for:', runId);
  
  const response = await runApi.getRunDetail(runId);
  
  if (!response.success) {
    throw new Error(response.error || 'Failed to fetch run details');
  }
  
  return {
    run: response.run || response.data?.run || null,
    tests: response.tests || response.data?.tests || [],
    reports: response.reports || response.data?.reports || [],
    stepProgressSummary: response.stepProgressSummary || response.data?.stepProgressSummary || null
  };
}

/**
 * SWR fetcher for run status only (lightweight)
 */
export async function runStatusFetcher(runId: string): Promise<{
  status: string;
  testResults?: any;
}> {
  console.log('[runStatusFetcher] Fetching run status for:', runId);
  
  const response = await runApi.getRunStatus(runId);
  
  if (!response.success) {
    throw new Error(response.error || 'Failed to fetch run status');
  }
  
  return {
    status: response.data?.status || 'unknown',
    testResults: response.data?.testResults
  };
}
