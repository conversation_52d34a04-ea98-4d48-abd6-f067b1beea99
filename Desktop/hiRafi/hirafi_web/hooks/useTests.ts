import { useState, useEffect, useCallback } from "react"
import { useAuth } from "@/lib/api/auth"
import { toast } from "sonner"
import { runApi } from "@/lib/api"

// Test durumları
export enum TestStatus {
  QUEUED = 'queued',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  STOPPED = 'stopped'
}

// Test bilgisi - Birleştirilmiş API ile gelen step bilgilerini de içerecek şekilde güncellendi
export interface TestInfo {
  id: string
  scenarioId: string
  scenarioName: string
  status: TestStatus
  queuedAt: string
  startedAt?: string
  completedAt?: string
  result?: any
  logs?: string[]
  stepProgress?: {
    currentStep: number
    totalSteps: number
    completedSteps: number
    failedSteps: number
    steps: Array<{
      id: string
      name: string
      type: string
      success: boolean | null
      error?: string
      duration?: number
      timestamp?: string
    }>
  }
}

export type ActiveTests = {
  count: number
  tests: TestInfo[]
}

export function useTests() {
  const { isAuthenticated, user } = useAuth()
  const [activeTests, setActiveTests] = useState<ActiveTests>({ count: 0, tests: [] })
  const [isLoading, setIsLoading] = useState(false)
  const [isStartingTest, setIsStartingTest] = useState(false)
  const [isStoppingTest, setIsStoppingTest] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Yeni birleştirilmiş API endpoint'inden tüm test ve adım verilerini getir
  const fetchUnifiedTests = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Yeni API yapısını kullan
      const response = await runApi.getActiveTests()

      if (!response.success) {
        throw new Error(response.error || "Testler yüklenirken bir hata oluştu")
      }

      // Response formatını ActiveTests tipine dönüştür
      setActiveTests({
        count: response.count || response.data?.count || 0,
        tests: response.tests || response.data?.tests || []
      })

      return response.data
    } catch (err: any) {
      setError(err.message)
      console.error("Testler yüklenirken hata:", err)
      return { success: false, error: err.message }
    } finally {
      setIsLoading(false)
    }
  }, [])

  // API'den aktif testleri getir - Geri uyumluluk için korundu, içeride yeni endpoint kullanılıyor
  const fetchActiveTests = useCallback(async () => {
    return fetchUnifiedTests()
  }, [fetchUnifiedTests])

  // Belirli bir testin tüm bilgilerini getir (durum ve adım ilerlemesi dahil)
  const fetchTestDetails = useCallback(async (testId: string) => {
    try {
      // Yeni API yapısını kullan
      const response = await runApi.getTestDetails(testId)

      if (!response.success) {
        throw new Error(response.error || "Test bilgileri alınırken bir hata oluştu")
      }

      return response.data
    } catch (err: any) {
      console.error("Test bilgileri alınırken hata:", err)
      return { success: false, error: err.message }
    }
  }, [])

  // Belirli bir testin durumunu getir - Geri uyumluluk için korundu
  const fetchTestStatus = useCallback(async (testId: string) => {
    try {
      const testDetails = await fetchTestDetails(testId)
      return testDetails.success ? testDetails.test : null
    } catch (err: any) {
      console.error("Test durumu alınırken hata:", err)
      return null
    }
  }, [fetchTestDetails])

  // Test başlatma fonksiyonu
  const startTest = async (scenarioId: string): Promise<{ success: boolean, testId?: string, error?: string }> => {
    if (!isAuthenticated) {
      return { success: false, error: "Kimlik doğrulama gerekli" }
    }

    try {
      // Yeni API yapısını kullan
      const response = await runApi.startTest(scenarioId, user?.id)

      if (!response.success) {
        return { success: false, error: response.error || "Test başlatılamadı" }
      }

      // Başarılı yanıt durumunda test ID'sini döndür
      // Aktif testleri yenile
      fetchUnifiedTests()

      return {
        success: true,
        testId: response.data?.testId
      }
    } catch (err: any) {
      console.error("Test başlatma hatası:", err)
      return { success: false, error: err.message || "Test başlatılamadı" }
    }
  }

  // Toplu test başlatma fonksiyonu
  const startBulkTests = async (scenarioIds: string[]): Promise<{
    success: boolean,
    successfulTests?: Array<{ scenarioId: string, testId: string, scenarioName: string }>,
    failedScenarios?: Array<{ scenarioId: string, reason: string }>,
    error?: string
  }> => {
    if (!isAuthenticated) {
      return { success: false, error: "Kimlik doğrulama gerekli" }
    }

    try {
      // Yeni API yapısını kullan
      const response = await runApi.startBulkTests(scenarioIds, user?.id)

      if (!response.success) {
        return { success: false, error: response.error || "Toplu testler başlatılamadı" }
      }

      // Başarılı yanıt durumunda sonuçları döndür
      // Aktif testleri yenile
      fetchUnifiedTests()

      return {
        success: true,
        successfulTests: response.data?.successfulTests,
        failedScenarios: response.data?.failedScenarios
      }
    } catch (err: any) {
      console.error("Toplu test başlatma hatası:", err)
      return { success: false, error: err.message || "Toplu testler başlatılamadı" }
    }
  }

  // Test durdurma fonksiyonu
  const stopTest = useCallback(async (testId: string) => {
    setIsStoppingTest(true)
    setError(null)

    try {
      // Yeni API yapısını kullan
      const response = await runApi.stopTest(testId)

      if (!response.success) {
        throw new Error(response.error || "Test durdurulurken bir hata oluştu")
      }

      toast.success("Test başarıyla durduruldu")

      // Aktif testleri güncelle
      fetchUnifiedTests()

      return {
        success: true,
        message: response.data?.message
      }
    } catch (err: any) {
      setError(err.message)
      console.error("Test durdurma hatası:", err)

      toast.error(`Test durdurulamadı: ${err.message}`)

      return {
        success: false,
        error: err.message
      }
    } finally {
      setIsStoppingTest(false)
    }
  }, [fetchUnifiedTests])

  // Sayfa yüklendiğinde aktif testleri getir
  useEffect(() => {
    fetchUnifiedTests()
  }, [fetchUnifiedTests])

  // Belirli aralıklarla aktif testleri güncelle
  useEffect(() => {
    fetchUnifiedTests()

    // 10 saniyede bir aktif testleri yenile
    const interval = setInterval(fetchUnifiedTests, 10000)

    return () => clearInterval(interval)
  }, [fetchUnifiedTests])

  // Step progress bilgilerini almak için yeni fonksiyon - Geri uyumluluk için korundu
  const fetchStepProgress = useCallback(async () => {
    return fetchUnifiedTests()
  }, [fetchUnifiedTests])

  return {
    activeTests,
    isLoading,
    error,
    isStartingTest,
    isStoppingTest,
    fetchActiveTests,
    fetchTestStatus,
    fetchTestDetails,
    fetchStepProgress,
    fetchUnifiedTests,
    startTest,
    startBulkTests,
    stopTest,
  }
}