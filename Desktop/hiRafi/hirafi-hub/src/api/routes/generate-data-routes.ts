/**
 * Generate Test Data Routes
 * 
 * AI-powered test data generation for data sets
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { getCompanyById, updateCompanyRemaining } from '../../services/mongo/companyService.js';
import OpenAI from 'openai';
import { CreateDataSetRequest } from '../../models/test-data.js';

const router = Router();

/**
 * Convert creativity level to OpenAI parameters
 */
function creativityToParameters(creativityLevel: number): {
  temperature: number;
  seed?: number;
} {
  switch (creativityLevel) {
    case 0: // Consistent
      return {
        temperature: 0,
        seed: 42 // Fixed seed for reproducible results
      };
    case 1: // Balanced
      return {
        temperature: 0.4,
        seed: undefined // No seed for slight variations
      };
    case 2: // Creative
      return {
        temperature: 0.7,
        seed: undefined
      };
    case 3: // Highly Creative
      return {
        temperature: 0.9,
        seed: undefined
      };
    default:
      return {
        temperature: 0.4,
        seed: undefined
      };
  }
}

// System prompt to guide the AI in generating appropriate test data
const SYSTEM_PROMPT_DATA_VALUES = `You are a specialized AI assistant for generating realistic test data. Your primary responsibility is to create appropriate test values for variables based on their types, descriptions, and constraints.

Guidelines:
1. Generate realistic, diverse test data that matches the variable type and description
2. Respect any constraints provided (min/max values, formats, etc.)
3. Create different values for different environments to simulate real-world scenarios
4. For production-like environments, use more realistic data
5. For development/testing environments, you can use more varied or edge-case data
6. Always return valid JSON with the exact structure requested
7. Ensure generated values are appropriate for the variable's intended use

Variable Types:
- string: Generate realistic text based on description
- number: Generate numeric values within constraints
- email: Generate valid email addresses
- boolean: Generate true/false values
- date: Generate valid dates in ISO format
- url: Generate valid URLs
- phone: Generate valid phone numbers
- uuid: Generate valid UUIDs
- json: Generate valid JSON objects
- array: Generate valid JSON arrays

Response Format:
{
  "variables": [
    {
      "name": "variableName",
      "environmentValues": {
        "environmentId1": "generatedValue1",
        "environmentId2": "generatedValue2"
      }
    }
  ]
}`;

// System prompt for generating variable definitions (name, type, description)
const SYSTEM_PROMPT_VARIABLE_DEFINITIONS = `You are a specialized AI assistant for generating test data variable definitions. Your primary responsibility is to create realistic variable names, appropriate data types, and meaningful descriptions based on the context provided by the user.

Guidelines:
1. Generate variable names that are descriptive and follow common naming conventions (camelCase or snake_case)
2. Choose appropriate data types based on the variable's intended use
3. Write clear, concise descriptions that explain what the variable represents
4. Create variables that would be commonly needed for the described test scenario
5. Generate 5-10 variables unless the user specifies otherwise
6. Ensure variable names are unique within the set
7. Always return valid JSON with the exact structure requested

Available Variable Types:
- string: For text data, names, descriptions, etc.
- number: For numeric values, IDs, counts, etc.
- email: For email addresses
- boolean: For true/false flags
- date: For dates and timestamps
- url: For web addresses
- phone: For phone numbers
- uuid: For unique identifiers
- json: For complex object data
- array: For lists of items

Response Format:
{
  "variables": [
    {
      "name": "variableName",
      "type": "string",
      "description": "Description of what this variable represents",
      "environmentValues": {
        "environmentId1": "generatedValue1",
        "environmentId2": "generatedValue2"
      }
    }
  ]
}`;

// System prompt for generating a full data set structure
const SYSTEM_PROMPT_DATASET_STRUCTURE = `You are a specialized AI assistant for generating complete test data set structures. Your task is to create a comprehensive JSON object that conforms to the CreateDataSetRequest interface based on a user's prompt.

Guidelines:
1.  Infer a suitable name, description, and tags for the data set from the user's prompt.
2.  Generate relevant variable definitions (name, type, description).
3.  For each variable, provide realistic example values for ALL specified environments. If no environments are specified by the user, provide values for generic "dev" and "prod" environments.
4.  The 'environment' field in the main request should be a single, general environment type (e.g., "development", "production") based on the prompt, or default to "development".
5.  CRITICALLY IMPORTANT: The 'variables' array MUST contain objects. Each object MUST include 'name' (string), 'type' (string from available types), 'description' (string), and 'environmentValues' (object). The 'environmentValues' object is MANDATORY for every variable.
6.  Available Variable Types: string, number, email, boolean, date, url, phone, uuid, json, array.
7.  Always return valid JSON conforming to the CreateDataSetRequest structure.
8.  The 'environmentValues' object maps environment IDs (or generic names like "dev", "test", "prod" if no specific IDs are provided) to the generated data for that variable in that environment.
    - If specific environments (e.g., with IDs) are provided in the user's request, YOU MUST generate a value for EACH of these environments within 'environmentValues'.
    - If no specific environments are detailed by the user, YOU MUST generate values for at least "dev" and "prod" environments using these literal keys (e.g., "environmentValues": { "dev": "dev_value", "prod": "prod_value" }).
    - If for some reason a value cannot be generated for a specific requested environment, use a null or appropriate placeholder string like "N/A" for that environment's value within 'environmentValues', but the key for that environment MUST still be present. DO NOT OMIT an environment key if it was requested or if it's one of the default "dev"/"prod" keys.

Example CreateDataSetRequest Structure:
{
  "name": "User Login Data",
  "description": "Test data for user login scenarios, including valid and invalid credentials.",
  "tags": ["login", "user", "auth"],
  "environment": "development", // General environment for the dataset
  "variables": [
    {
      "name": "username",
      "type": "email",
      "description": "User's login email address.",
      "environmentValues": {
        "env_id_1": "<EMAIL>",
        "env_id_2": "<EMAIL>"
      }
    },
    {
      "name": "password",
      "type": "string",
      "description": "User's login password.",
      "environmentValues": {
        "env_id_1": "P@sswOrd123!",
        "env_id_2": "SecureP@ss!2024"
      }
    },
    {
      "name": "isActive",
      "type": "boolean",
      "description": "Indicates if the user account is active.",
      "environmentValues": {
        "env_id_1": true,
        "env_id_2": false
      }
    }
  ],
  "metadata": { "ai_prompt": "User prompt can be stored here if needed" }
}

If the user provides a list of environments with IDs, like:
Environments:
- Staging (env_staging_123)
- Production (env_prod_456)

Then the environmentValues for each variable MUST look like:
"environmentValues": {
  "env_staging_123": "staging_value_for_varX",
  "env_prod_456": "production_value_for_varX"
}

If no specific environment IDs are given, then for each variable, environmentValues MUST include at least:
"environmentValues": {
  "dev": "dev_value_for_varX",
  "prod": "prod_value_for_varX"
}
`;

/**
 * POST /api/generate-data
 * AI ile test verisi üretir
 */
router.post('/', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const companyId = authReq.user?.companyId;

    // Debug logging to understand the authentication state
    logger.info(`[GENERATE_DATA] Authentication check - userId: ${userId}, companyId: ${companyId}, accountType: ${authReq.user?.accountType}`);

    const openai = new OpenAI({
      baseURL:'https://api.openai.com/v1',
      apiKey: '********************************************************************************************************************************************************************',
    });

    if (!userId) {
      logger.error(`[GENERATE_DATA] User ID is missing from auth request`);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Skip companyId check for admin users
    if (authReq.user?.accountType === 'admin') {
      logger.info(`[GENERATE_DATA] Admin user detected, skipping companyId check`);
    } else if (!companyId) {
      logger.error(`[GENERATE_DATA] Company ID is missing for non-admin user`);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // Only check company for non-admin users
    let company = null;
    let remainingGenerations = undefined;

    if (authReq.user?.accountType !== 'admin' && companyId) {
      const companyResult = await getCompanyById(companyId);
      if (!companyResult.success || !companyResult.company) {
        return res.status(404).json({
          success: false,
          error: 'Company not found'
        });
      }

      company = companyResult.company;
      remainingGenerations = company.settings?.remaining?.generations;
      if (remainingGenerations !== undefined && remainingGenerations <= 0) {
        return res.status(403).json({
          success: false,
          error: 'No remaining generations available. Please contact support to increase your limit.'
        });
      }
    }

    const { variables, environments, datasetMetadata, creativityLevel = 1 } = req.body;

    if (!variables || !Array.isArray(variables) || variables.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Variables array is required and must not be empty'
      });
    }

    if (!environments || !Array.isArray(environments) || environments.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Environments array is required and must not be empty'
      });
    }

    // Create user message with variable and environment information
    let userMessage = `Generate test data for the following variables across these environments:

Variables:
${variables.map(v => `- ${v.name} (${v.type}): ${v.description || 'No description'}${v.constraints ? ` | Constraints: ${JSON.stringify(v.constraints)}` : ''}`).join('\n')}

Environments:
${environments.map(env => `- ${env.name} (${env.id}): ${env.description || 'No description'} [${env.type}]`).join('\n')}`;

    // Add dataset metadata if provided
    if (datasetMetadata) {
      userMessage += `

Dataset Context:
- Name: ${datasetMetadata.name}
- Description: ${datasetMetadata.description}
- Tags: ${datasetMetadata.tags.length > 0 ? datasetMetadata.tags.join(', ') : 'None'}`;

      if (datasetMetadata.aiPrompt) {
        userMessage += `
- Original AI Prompt: ${datasetMetadata.aiPrompt}`;
      }

      userMessage += `

Please generate test values that are appropriate for this dataset context. Consider the dataset name, description, and original prompt when creating realistic and relevant test data.`;
    }

    userMessage += `

Please generate appropriate test values for each variable in each environment. Make the values realistic and diverse across environments.`;

    // Get creativity parameters
    const creativityParams = creativityToParameters(creativityLevel);

    // OpenAI API'sini çağır
    logger.info(`Generating test data with OpenAI for ${variables.length} variables across ${environments.length} environments (creativity: ${creativityLevel}, temp: ${creativityParams.temperature}, seed: ${creativityParams.seed || 'none'})`);

    const openaiParams: any = {
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT_DATA_VALUES },
        { role: 'user', content: userMessage }
      ],
      response_format: { type: 'json_object' },
      temperature: creativityParams.temperature,
      max_tokens: 4000,
    };

    // Add seed if specified for consistent results
    if (creativityParams.seed !== undefined) {
      openaiParams.seed = creativityParams.seed;
    }

    const completion = await openai.chat.completions.create(openaiParams);

    // Yanıtı işle
    const responseContent = completion.choices[0]?.message?.content;

    if (!responseContent) {
      throw new Error('No response from OpenAI');
    }

    try {
      const result = JSON.parse(responseContent);

      // Validate the response structure
      if (!result.variables || !Array.isArray(result.variables)) {
        throw new Error('Invalid response structure: missing variables array');
      }

      // Başarılı generation sonrası kalan generation hakkını azalt (only for non-admin users)
      let newRemainingGenerations = remainingGenerations;
      if (remainingGenerations !== undefined && companyId) {
        newRemainingGenerations = remainingGenerations - 1;
        await updateCompanyRemaining(companyId, {
          generations: newRemainingGenerations
        });

        logger.info(`Decremented remaining generations for company ${companyId}. New value: ${newRemainingGenerations}`);

        // Kalan generation hakkını yanıta ekle
        (result as any).remainingGenerations = newRemainingGenerations;
      }

      return res.json({
        success: true,
        data: result,
        remainingGenerations: newRemainingGenerations
      });
    } catch (parseError: any) {
      logger.error(`Error parsing OpenAI response: ${parseError.message}`);
      return res.status(500).json({
        success: false,
        error: "OpenAI yanıtı ayrıştırılamadı",
        details: parseError.message
      });
    }

  } catch (error: any) {
    logger.error(`Error generating test data: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: "Test verisi oluşturulurken hata oluştu",
      details: error.message
    });
  }
});

/**
 * POST /api/generate-data/dataset
 * AI ile tam bir veri kümesi yapısı üretir
 */
router.post('/dataset', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const companyId = authReq.user?.companyId;

    // Debug logging to understand the authentication state
    logger.info(`[GENERATE_DATA_DATASET] Authentication check - userId: ${userId}, companyId: ${companyId}, accountType: ${authReq.user?.accountType}`);

    const openai = new OpenAI({
      baseURL:'https://api.openai.com/v1',
      apiKey: '********************************************************************************************************************************************************************',
    });

    if (!userId) {
      logger.error(`[GENERATE_DATA_DATASET] User ID is missing from auth request`);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Skip companyId check for admin users
    if (authReq.user?.accountType === 'admin') {
      logger.info(`[GENERATE_DATA_DATASET] Admin user detected, skipping companyId check`);
    } else if (!companyId) {
      logger.error(`[GENERATE_DATA_DATASET] Company ID is missing for non-admin user`);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // Only check company for non-admin users
    let company = null;
    let remainingGenerations = undefined;

    if (authReq.user?.accountType !== 'admin' && companyId) {
      const companyResult = await getCompanyById(companyId);
      if (!companyResult.success || !companyResult.company) {
        return res.status(404).json({
          success: false,
          error: 'Company not found'
        });
      }

      company = companyResult.company;
      remainingGenerations = company.settings?.remaining?.generations;
      if (remainingGenerations !== undefined && remainingGenerations <= 0) {
        return res.status(403).json({
          success: false,
          error: 'No remaining generations available. Please contact support to increase your limit.'
        });
      }
    }

    const { prompt, environments, creativityLevel = 1 } = req.body; // environments might be an array of {id, name, type}

    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Prompt is required and must be a non-empty string'
      });
    }

    let userMessage = `User Prompt: "${prompt}"

Based on this prompt, generate a complete JSON object for a new test data set.`;

    if (environments && Array.isArray(environments) && environments.length > 0) {
      userMessage += `

Consider the following environments when generating data values for each variable. Use the provided environment IDs as keys in the 'environmentValues' object for each variable:
${environments.map(env => `- ${env.name} (${env.id || 'N/A'}) [${env.type || 'N/A'}]`).join('\n')}`;
    }

    // Get creativity parameters
    const creativityParams = creativityToParameters(creativityLevel);

    logger.info(`Generating full dataset structure with OpenAI for prompt: "${prompt}" (creativity: ${creativityLevel}, temp: ${creativityParams.temperature}, seed: ${creativityParams.seed || 'none'})`);

    const openaiParams: any = {
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT_DATASET_STRUCTURE },
        { role: 'user', content: userMessage }
      ],
      response_format: { type: 'json_object' },
      temperature: creativityParams.temperature,
      max_tokens: 4000,
    };

    // Add seed if specified for consistent results
    if (creativityParams.seed !== undefined) {
      openaiParams.seed = creativityParams.seed;
    }

    const completion = await openai.chat.completions.create(openaiParams);

    const aiResponse = completion.choices[0].message?.content;

    if (!aiResponse) {
      logger.error('[API] OpenAI response was empty for dataset generation.');
      return res.status(500).json({
        success: false,
        error: 'AI failed to generate data. Response was empty.'
      });
    }

    try {
      const generatedData = JSON.parse(aiResponse);
      // Basic validation of the structure - can be expanded
      if (!generatedData.name || !Array.isArray(generatedData.variables)) {
         logger.error('[API] OpenAI response for dataset generation had invalid structure.', generatedData);
        return res.status(500).json({
          success: false,
          error: 'AI generated data with invalid structure.'
        });
      }

      // Decrement remaining generations for non-admin users
      if (remainingGenerations !== undefined && companyId) {
        await updateCompanyRemaining(companyId, { generations: remainingGenerations - 1 });
      }


      return res.json({
        success: true,
        data: generatedData as CreateDataSetRequest
      });
    } catch (parseError: any) {
      logger.error(`[API] Error parsing OpenAI JSON response for dataset generation: ${parseError.message}`, aiResponse);
      return res.status(500).json({
        success: false,
        error: 'AI generated invalid JSON data.'
      });
    }

  } catch (error: any) {
    logger.error(`[API] Error in POST /generate-data/dataset: ${error.message}`);
    if (error.response) {
      logger.error('[API] OpenAI API Error Details:', error.response.data);
    }
    return res.status(500).json({
      success: false,
      error: 'Internal server error during AI data generation.'
    });
  }
});

/**
 * POST /api/generate-data/variables
 * AI ile test değişkenleri tanımları üretir
 */
router.post('/variables', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const companyId = authReq.user?.companyId;

    // Debug logging to understand the authentication state
    logger.info(`[GENERATE_DATA_VARIABLES] Authentication check - userId: ${userId}, companyId: ${companyId}, accountType: ${authReq.user?.accountType}`);

    const openai = new OpenAI({
      baseURL:'https://api.openai.com/v1',
      apiKey: '********************************************************************************************************************************************************************',
    });

    if (!userId) {
      logger.error(`[GENERATE_DATA_VARIABLES] User ID is missing from auth request`);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Skip companyId check for admin users
    if (authReq.user?.accountType === 'admin') {
      logger.info(`[GENERATE_DATA_VARIABLES] Admin user detected, skipping companyId check`);
    } else if (!companyId) {
      logger.error(`[GENERATE_DATA_VARIABLES] Company ID is missing for non-admin user`);
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // Only check company for non-admin users
    let company = null;
    let remainingGenerations = undefined;

    if (authReq.user?.accountType !== 'admin' && companyId) {
      const companyResult = await getCompanyById(companyId);
      if (!companyResult.success || !companyResult.company) {
        return res.status(404).json({
          success: false,
          error: 'Company not found'
        });
      }

      company = companyResult.company;
      remainingGenerations = company.settings?.remaining?.generations;
      if (remainingGenerations !== undefined && remainingGenerations <= 0) {
        return res.status(403).json({
          success: false,
          error: 'No remaining generations available. Please contact support to increase your limit.'
        });
      }
    }

    const { prompt, environments, creativityLevel = 1 } = req.body;

    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      return res.status(400).json({
        success: false,
        error: 'Prompt is required and must be a non-empty string'
      });
    }

    if (!environments || !Array.isArray(environments) || environments.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Environments array is required and must not be empty'
      });
    }

    // Create user message for variable generation
    const userMessage = `Based on the following description, generate appropriate test data variables with their definitions and sample values for each environment:

Description: ${prompt}

Target Environments:
${environments.map(env => `- ${env.name} (${env.id}): ${env.description || 'No description'} [${env.type}]`).join('\n')}

Please generate 5-10 relevant variables that would be needed for testing the described scenario. For each variable, provide:
1. A descriptive name (camelCase or snake_case)
2. An appropriate data type
3. A clear description of what it represents
4. Sample values for each target environment

Make the values realistic and different across environments to simulate real-world testing scenarios.`;

    // Get creativity parameters
    const creativityParams = creativityToParameters(creativityLevel);

    // OpenAI API'sini çağır
    logger.info(`Generating variable definitions with OpenAI for prompt: "${prompt.substring(0, 100)}..." across ${environments.length} environments (creativity: ${creativityLevel}, temp: ${creativityParams.temperature}, seed: ${creativityParams.seed || 'none'})`);
    logger.info('[GENERATE_VARIABLES] Environments being sent to AI:', environments.map(env => ({ id: env.id, name: env.name, type: env.type })));

    const openaiParams: any = {
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT_VARIABLE_DEFINITIONS },
        { role: 'user', content: userMessage }
      ],
      response_format: { type: 'json_object' },
      temperature: creativityParams.temperature,
      max_tokens: 4000,
    };

    // Add seed if specified for consistent results
    if (creativityParams.seed !== undefined) {
      openaiParams.seed = creativityParams.seed;
    }

    const completion = await openai.chat.completions.create(openaiParams);

    // Yanıtı işle
    const responseContent = completion.choices[0]?.message?.content;

    if (!responseContent) {
      throw new Error('No response from OpenAI');
    }

    try {
      const result = JSON.parse(responseContent);

      // Log the parsed result for debugging
      logger.info('[GENERATE_VARIABLES] Parsed AI response:', {
        variableCount: result.variables?.length || 0,
        variables: result.variables?.map((v: any) => ({
          name: v.name,
          type: v.type,
          environmentValueKeys: v.environmentValues ? Object.keys(v.environmentValues) : []
        })) || []
      });

      // Validate the response structure
      if (!result.variables || !Array.isArray(result.variables)) {
        throw new Error('Invalid response structure: missing variables array');
      }

      // Validate each variable has required fields
      for (const variable of result.variables) {
        if (!variable.name || !variable.type || !variable.environmentValues) {
          logger.error('[GENERATE_VARIABLES] Invalid variable structure:', variable);
          throw new Error('Invalid variable structure: missing required fields');
        }
      }

      // Başarılı generation sonrası kalan generation hakkını azalt (only for non-admin users)
      let newRemainingGenerations = remainingGenerations;
      if (remainingGenerations !== undefined && companyId) {
        newRemainingGenerations = remainingGenerations - 1;
        await updateCompanyRemaining(companyId, {
          generations: newRemainingGenerations
        });

        logger.info(`Decremented remaining generations for company ${companyId}. New value: ${newRemainingGenerations}`);

        // Kalan generation hakkını yanıta ekle
        (result as any).remainingGenerations = newRemainingGenerations;
      }

      return res.json({
        success: true,
        data: result,
        remainingGenerations: newRemainingGenerations
      });
    } catch (parseError: any) {
      logger.error(`Error parsing OpenAI response: ${parseError.message}`);
      return res.status(500).json({
        success: false,
        error: "OpenAI yanıtı ayrıştırılamadı",
        details: parseError.message
      });
    }

  } catch (error: any) {
    logger.error(`Error generating variable definitions: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: "Değişken tanımları oluşturulurken hata oluştu",
      details: error.message
    });
  }
});

export default router;
