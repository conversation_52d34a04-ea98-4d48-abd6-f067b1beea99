/**
 * Step Progress API Routes
 * Provides endpoints for retrieving test step progress information
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { checkPermission } from '../middleware/permissionMiddleware.js';
import { stepProgressService } from '../../services/step-progress/stepProgressService.js';
import { getRunById } from '../../services/mongo/runService.js';

const router = Router();

/**
 * GET /api/step-progress/test/:testId
 * Get step progress for a specific test
 */
router.get('/test/:testId', authenticate, checkPermission('Test', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    const stepProgress = await stepProgressService.getTestStepProgress(testId);

    if (!stepProgress) {
      return res.status(404).json({
        success: false,
        error: 'Step progress not found for this test'
      });
    }

    // Authorization check - if step progress has runId, verify user has access to that run
    if (stepProgress.runId) {
      const runResult = await getRunById(stepProgress.runId);
      if (runResult.success && runResult.run) {
        // Check if user has access to the run that contains this test
        if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
          return res.status(403).json({
            success: false,
            error: 'You can only view step progress for tests from your team'
          });
        }
      }
    }

    res.json({
      success: true,
      data: stepProgress
    });
  } catch (error) {
    logger.error(`StepProgressAPI: Error getting step progress for test ${req.params.testId}: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/step-progress/run/:runId
 * Get step progress for all tests in a run
 */
router.get('/run/:runId', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    const { runId } = req.params;

    if (!runId) {
      return res.status(400).json({
        success: false,
        error: 'Run ID is required'
      });
    }

    // Check if user has access to this run
    const runResult = await getRunById(runId);
    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Authorization check - users can only access runs from their team
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view step progress for runs from your team'
      });
    }

    const stepProgressList = await stepProgressService.getRunStepProgress(runId);

    res.json({
      success: true,
      data: stepProgressList
    });
  } catch (error) {
    logger.error(`StepProgressAPI: Error getting step progress for run ${req.params.runId}: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/step-progress/run/:runId/summary
 * Get step progress summary for a run (aggregated data)
 */
router.get('/run/:runId/summary', authenticate, checkPermission('Run', 'read'), async (req: AuthRequest, res: Response) => {
  try {
    const { runId } = req.params;

    if (!runId) {
      return res.status(400).json({
        success: false,
        error: 'Run ID is required'
      });
    }

    // Check if user has access to this run
    const runResult = await getRunById(runId);
    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Authorization check - users can only access runs from their team
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view step progress for runs from your team'
      });
    }

    const stepProgressList = await stepProgressService.getRunStepProgress(runId);

    // Get scenario names for the step progress
    const scenarioIds = [...new Set(stepProgressList.map(p => p.scenarioId).filter(Boolean))] as string[];
    const scenarioNameMap = new Map<string, string>();

    if (scenarioIds.length > 0) {
      try {
        const { getScenarioById } = await import('../../services/mongo/scenarioService.js');

        // Get scenario names for each scenario ID
        for (const scenarioId of scenarioIds) {
          const scenarioResult = await getScenarioById(scenarioId);
          if (scenarioResult.success && scenarioResult.scenario) {
            scenarioNameMap.set(scenarioId, scenarioResult.scenario.name || 'Unnamed Scenario');
          } else {
            scenarioNameMap.set(scenarioId, 'Unnamed Scenario');
          }
        }
      } catch (scenarioError) {
        logger.warn(`Could not get scenario names for run ${runId}: ${scenarioError}`);
      }
    }

    // Calculate summary statistics
    const summary = {
      totalTests: stepProgressList.length,
      testsInProgress: stepProgressList.filter(p => p.currentStepStatus === 'started').length,
      testsCompleted: stepProgressList.filter(p => p.currentStepStatus === 'completed').length,
      testsFailed: stepProgressList.filter(p => p.currentStepStatus === 'failed').length,
      averageProgress: stepProgressList.length > 0
        ? stepProgressList.reduce((sum, p) => sum + (p.totalSteps > 0 ? (p.currentStep / p.totalSteps) * 100 : 0), 0) / stepProgressList.length
        : 0,
      tests: stepProgressList.map(p => ({
        testId: p.testId,
        scenarioId: p.scenarioId,
        scenarioName: p.scenarioId ? (scenarioNameMap.get(p.scenarioId) || 'Unnamed Scenario') : 'Unnamed Scenario',
        currentStep: p.currentStep,
        totalSteps: p.totalSteps,
        currentStepName: p.currentStepName,
        currentStepStatus: p.currentStepStatus,
        progress: p.totalSteps > 0 ? (p.currentStep / p.totalSteps) * 100 : 0,
        lastUpdated: p.lastUpdated,
        platform: p.platform
      }))
    };

    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    logger.error(`StepProgressAPI: Error getting step progress summary for run ${req.params.runId}: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * DELETE /api/step-progress/test/:testId
 * Clean up step progress for a specific test (admin only)
 */
router.delete('/test/:testId', authenticate, checkPermission('Test', 'delete'), async (req: AuthRequest, res: Response) => {
  try {
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    // Get step progress to check authorization
    const stepProgress = await stepProgressService.getTestStepProgress(testId);

    // Authorization check - if step progress has runId, verify user has access to that run
    if (stepProgress && stepProgress.runId) {
      const runResult = await getRunById(stepProgress.runId);
      if (runResult.success && runResult.run) {
        // Check if user has access to the run that contains this test
        if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
          return res.status(403).json({
            success: false,
            error: 'You can only delete step progress for tests from your team'
          });
        }
      }
    }

    await stepProgressService.cleanupTestStepProgress(testId);

    res.json({
      success: true,
      message: 'Step progress cleaned up successfully'
    });
  } catch (error) {
    logger.error(`StepProgressAPI: Error cleaning up step progress for test ${req.params.testId}: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * DELETE /api/step-progress/run/:runId
 * Clean up step progress for all tests in a run (admin only)
 */
router.delete('/run/:runId', authenticate, checkPermission('Run', 'delete'), async (req: AuthRequest, res: Response) => {
  try {
    const { runId } = req.params;

    if (!runId) {
      return res.status(400).json({
        success: false,
        error: 'Run ID is required'
      });
    }

    // Check if user has access to this run
    const runResult = await getRunById(runId);
    if (!runResult.success || !runResult.run) {
      return res.status(404).json({
        success: false,
        error: 'Run not found'
      });
    }

    // Authorization check - users can only delete step progress for runs from their team
    if (runResult.run.teamId !== req.user?.teamId && req.user?.accountType !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only delete step progress for runs from your team'
      });
    }

    await stepProgressService.cleanupRunStepProgress(runId);

    res.json({
      success: true,
      message: 'Step progress cleaned up successfully for all tests in the run'
    });
  } catch (error) {
    logger.error(`StepProgressAPI: Error cleaning up step progress for run ${req.params.runId}: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
