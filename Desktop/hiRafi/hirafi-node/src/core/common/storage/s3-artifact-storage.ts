import {
  S3Client,
  PutO<PERSON>Command,
  HeadBucketCommand,
  CreateBucketCommand,
  PutBucketPolicyCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  ListObjectsV2Command
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import * as fs from 'fs';
import * as path from 'path';
import { Readable } from 'stream';
import { logger } from '../../../utils/logger.js';
import { IArtifactStorage, IArtifact, IUploadResult } from './artifact-storage.interface.js';

/**
 * S3ArtifactStorage class for storing artifacts in AWS S3
 * Implements the IArtifactStorage interface
 */
export class S3ArtifactStorage implements IArtifactStorage {
  private client!: S3Client; // Definite assignment assertion - initialized in initialize()
  private bucket: string;
  private config: any;
  private publicBaseUrl: string | undefined;
  private initialized: boolean = false;
  private region: string;

  /**
   * Gets the name of the storage provider
   * @returns The provider name as a string
   */
  getProviderName(): string {
    return 's3';
  }

  /**
   * Create a new S3ArtifactStorage
   * @param customConfig Configuration object
   */
  constructor(customConfig: any = {}) {
    this.config = customConfig;
    this.bucket = customConfig.bucket || 'test-artifacts';
    this.region = customConfig.region || process.env.AWS_REGION || 'eu-west-1'; // Use AWS_REGION env var with eu-west-1 default
    this.publicBaseUrl = customConfig.publicBaseUrl;

    // Remove client creation from constructor to prevent double initialization
    // Client will be created only once during initialize() method
    logger.debug(`S3ArtifactStorage: Created with bucket: ${this.bucket}, region: ${this.region}`);
  }

  /**
   * Initialize the storage provider with necessary configurations.
   * @param config Provider-specific configuration.
   */
  async initialize(config: any): Promise<void> {
    if (this.initialized) {
      logger.debug('S3ArtifactStorage: Already initialized, skipping');
      return;
    }

    // Merge config with existing config
    this.config = { ...this.config, ...config };
    this.bucket = this.config.bucket || this.bucket;
    this.region = this.config.region || this.region;
    this.publicBaseUrl = this.config.publicBaseUrl || this.publicBaseUrl;

    // Create S3 client only once during initialization
    this.client = this.createS3Client(this.config);

    // Log initialization details
    logger.info(`S3ArtifactStorage: Initialized with bucket: ${this.bucket}`);
    logger.info(`S3ArtifactStorage: Using region: ${this.region}`);

    if (this.publicBaseUrl) {
      logger.info(`S3ArtifactStorage: Using public URL: ${this.publicBaseUrl}`);
    } else {
      logger.info(`S3ArtifactStorage: No public URL configuration found, will use direct presigned URLs`);
    }

    // Ensure bucket exists
    await this.ensureBucketExists();
    this.initialized = true;
  }

  /**
   * Upload a file to storage (Legacy method for backward compatibility)
   * @param filePath Path to the file to upload
   * @param destinationKey Key to store the file under
   * @param contentType MIME type of the file
   * @returns Promise resolving to the URL of the stored file
   */
  async uploadFile(filePath: string, _destinationKey: string, contentType?: string): Promise<string> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Determine content type if not provided
      const finalContentType = contentType || this.getContentTypeFromPath(filePath);

      // Create an IArtifact from the file
      const artifact: IArtifact = {
        metadata: {
          testId: 'unknown', // These fields would be filled by caller in newer code
          timestamp: new Date(),
          platform: 'web',
          artifactType: this.getArtifactTypeFromContentType(finalContentType),
          contentType: finalContentType
        },
        fileName: path.basename(filePath),
        filePath: filePath
      };

      // Upload using the new method
      const result = await this.uploadArtifact(artifact);

      // For backward compatibility, just return the URL
      return result.url || result.storagePath;
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error uploading file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Upload a buffer to storage (Legacy method for backward compatibility)
   * @param buffer Buffer to upload
   * @param destinationKey Key to store the buffer under
   * @param contentType MIME type of the buffer
   * @returns Promise resolving to the URL of the stored buffer
   */
  async uploadBuffer(buffer: Buffer, destinationKey: string, contentType: string): Promise<string> {
    try {
      // Create an IArtifact from the buffer
      const artifact: IArtifact = {
        metadata: {
          testId: 'unknown', // These fields would be filled by caller in newer code
          timestamp: new Date(),
          platform: 'web',
          artifactType: this.getArtifactTypeFromContentType(contentType),
          contentType: contentType
        },
        fileName: path.basename(destinationKey),
        contentBuffer: buffer
      };

      // Upload using the new method
      const result = await this.uploadArtifact(artifact, path.dirname(destinationKey));

      // For backward compatibility, just return the URL
      return result.url || result.storagePath;
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error uploading buffer: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get the URL for a stored file (Legacy method for backward compatibility)
   * @param destinationKey Key the file is stored under
   * @returns Promise resolving to the URL of the file
   */
  async getFileUrl(destinationKey: string): Promise<string> {
    try {
      // If public base URL is configured, use it
      if (this.publicBaseUrl) {
        return `${this.publicBaseUrl}/${destinationKey}`;
      }

      // Otherwise generate a presigned URL
      const url = await this.getPresignedUrl(destinationKey, 24 * 60 * 60, 'getObject');
      return url;
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error getting file URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Uploads an artifact to the storage.
   * @param artifact The artifact to upload.
   * @param destinationPath Optional path prefix within the bucket.
   * @returns A promise resolving to the storage path and URL of the uploaded artifact.
   */
  async uploadArtifact(artifact: IArtifact, destinationPath?: string): Promise<IUploadResult> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // Build the storage key using destination path and filename
      let key = artifact.fileName;
      if (destinationPath) {
        key = `${destinationPath}/${key}`;
      }

      // Determine the artifact content and upload it
      let body: Buffer | Readable | string;

      if (artifact.contentBuffer) {
        body = artifact.contentBuffer;
      } else if (artifact.contentStream) {
        // Ensure the contentStream is a Readable stream
        if (artifact.contentStream instanceof Readable) {
          body = artifact.contentStream;
        } else {
          throw new Error('Content stream must be a Readable stream');
        }
      } else if (artifact.filePath) {
        // Read the file content
        body = fs.readFileSync(artifact.filePath);
      } else {
        throw new Error('Artifact must have either contentBuffer, contentStream, or filePath');
      }

      // Upload the artifact to S3
      await this.client.send(new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: body,
        ContentType: artifact.metadata.contentType,
        Metadata: {
          testId: artifact.metadata.testId,
          stepName: artifact.metadata.stepName || '',
          timestamp: artifact.metadata.timestamp.toISOString(),
          platform: artifact.metadata.platform,
          artifactType: artifact.metadata.artifactType,
          ...artifact.metadata.customTags
        }
      }));

      logger.debug(`S3ArtifactStorage: Uploaded artifact to ${key}`);

      // Generate the URL
      let url: string;
      if (this.publicBaseUrl) {
        // Use the public URL pattern if configured
        url = `${this.publicBaseUrl}/${key}`;
        logger.debug(`S3ArtifactStorage: Generated public URL: ${url}`);
      } else {
        // Generate a presigned URL with 24-hour expiration
        url = await this.getPresignedUrl(key, 24 * 60 * 60, 'getObject');
        logger.debug(`S3ArtifactStorage: Generated presigned URL with 24h expiration`);
      }

      // For debugging
      logger.info(`S3ArtifactStorage: Artifact URL generated: ${url.substring(0, 100)}${url.length > 100 ? '...' : ''}`);

      // Return the result with both storage path and URL
      return {
        storagePath: `s3://${this.bucket}/${key}`,
        url
      };
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error uploading artifact: ${error.message}`);
      throw error;
    }
  }

  /**
   * Downloads an artifact from the storage.
   * @param storagePath The full path of the artifact in storage.
   * @returns A promise resolving to a readable stream of the artifact content.
   */
  async downloadArtifact(storagePath: string): Promise<NodeJS.ReadableStream> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // Extract the key from the storage path
      // Format: s3://bucket/key
      const key = this.extractKeyFromStoragePath(storagePath);

      // Get the object from S3
      const response = await this.client.send(new GetObjectCommand({
        Bucket: this.bucket,
        Key: key
      }));

      // Return the body as a readable stream
      if (!response.Body) {
        throw new Error('No body returned from S3');
      }

      return response.Body as NodeJS.ReadableStream;
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error downloading artifact: ${error.message}`);
      throw error;
    }
  }

  /**
   * Deletes an artifact from the storage.
   * @param storagePath The full path of the artifact in storage.
   * @returns A promise resolving when the artifact is deleted.
   */
  async deleteArtifact(storagePath: string): Promise<void> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // Extract the key from the storage path
      const key = this.extractKeyFromStoragePath(storagePath);

      // Delete the object from S3
      await this.client.send(new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key
      }));

      logger.debug(`S3ArtifactStorage: Deleted artifact ${key}`);
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error deleting artifact: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lists artifacts in a given path.
   * @param pathPrefix The path prefix to list artifacts from.
   * @returns A promise resolving to a list of artifact names or full paths.
   */
  async listArtifacts(pathPrefix: string): Promise<string[]> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // List objects in the bucket with the given prefix
      const response = await this.client.send(new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: pathPrefix
      }));

      // Extract the keys from the response
      const keys: string[] = [];
      if (response.Contents) {
        for (const object of response.Contents) {
          if (object.Key) {
            keys.push(`s3://${this.bucket}/${object.Key}`);
          }
        }
      }

      return keys;
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error listing artifacts: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generates a pre-signed URL for temporary access to an artifact.
   * @param storagePath The full path of the artifact in storage.
   * @param expiresInSeconds The duration for which the URL is valid.
   * @param operation 'getObject' | 'putObject'
   * @returns A promise resolving to the pre-signed URL.
   */
  async getPresignedUrl(storagePath: string, expiresInSeconds: number, operation: 'getObject' | 'putObject'): Promise<string> {
    try {
      if (!this.initialized && operation !== 'getObject') {
        await this.initialize(this.config);
      }

      // Extract the key from the storage path if it's a full storage path
      let key = storagePath;
      if (storagePath.startsWith('s3://')) {
        key = this.extractKeyFromStoragePath(storagePath);
      }

      // Create the command based on the operation
      let command;
      if (operation === 'getObject') {
        command = new GetObjectCommand({
          Bucket: this.bucket,
          Key: key
        });
      } else {
        command = new PutObjectCommand({
          Bucket: this.bucket,
          Key: key
        });
      }

      // Generate the presigned URL
      const url = await getSignedUrl(this.client, command, { expiresIn: expiresInSeconds });
      return url;
    } catch (error: any) {
      logger.error(`S3ArtifactStorage: Error generating presigned URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create an S3 client with the given configuration
   * @param config Configuration object
   * @returns S3 client
   * @private
   */
  private createS3Client(config: any): S3Client {
    // Determine region - use config, then AWS_REGION env var, then default to eu-west-1
    const region = config.region || process.env.AWS_REGION || 'eu-west-1';

    // Build credentials from config
    const credentials = {
      accessKeyId: config.accessKey || process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: config.secretKey || process.env.AWS_SECRET_ACCESS_KEY || ''
    };

    // Build S3 client configuration with enhanced timeout and retry settings
    const s3Config: any = {
      region,
      requestHandler: {
        requestTimeout: 30000, // 30 seconds timeout
        connectionTimeout: 10000, // 10 seconds connection timeout
      },
      maxAttempts: 3, // Retry up to 3 times
      retryMode: 'adaptive' // Use adaptive retry mode for better handling of network issues
    };

    // Only add credentials if they are provided
    if (credentials.accessKeyId && credentials.secretAccessKey) {
      s3Config.credentials = credentials;
    }

    // Return the S3 client
    return new S3Client(s3Config);
  }

  /**
   * Extract the key from a storage path
   * @param storagePath Storage path to extract key from (format: s3://bucket/key)
   * @returns Key part of the path
   * @private
   */
  private extractKeyFromStoragePath(storagePath: string): string {
    // Handle different formats
    if (storagePath.startsWith('s3://')) {
      // Format: s3://bucket/key
      const parts = storagePath.substring(5).split('/');
      const bucket = parts[0];

      if (bucket !== this.bucket) {
        logger.warn(`S3ArtifactStorage: Storage path bucket (${bucket}) does not match configured bucket (${this.bucket})`);
      }

      return parts.slice(1).join('/');
    }

    // If it's not a full storage path, assume it's just the key
    return storagePath;
  }

  /**
   * Ensure the bucket exists and has correct permissions
   * @private
   */
  private async ensureBucketExists(): Promise<void> {
    try {
      // Check if bucket exists
      await this.client.send(new HeadBucketCommand({ Bucket: this.bucket }));
      logger.debug(`S3ArtifactStorage: Bucket already exists: ${this.bucket}`);
    } catch (error: any) {
      // If bucket doesn't exist, create it
      if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
        try {
          // Create configuration object
          const createBucketConfig: any = {
            Bucket: this.bucket
          };

          // Add LocationConstraint if not us-east-1
          // (us-east-1 is the default and doesn't accept a LocationConstraint)
          if (this.region !== 'us-east-1') {
            createBucketConfig.CreateBucketConfiguration = {
              LocationConstraint: this.region
            };
          }

          await this.client.send(new CreateBucketCommand(createBucketConfig));

          logger.info(`S3ArtifactStorage: Created bucket: ${this.bucket}`);

          // Set bucket policy to public read if needed
          const policy = {
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Principal: { AWS: ['*'] },
                Action: ['s3:GetObject'],
                Resource: [`arn:aws:s3:::${this.bucket}/*`]
              }
            ]
          };

          await this.client.send(new PutBucketPolicyCommand({
            Bucket: this.bucket,
            Policy: JSON.stringify(policy)
          }));

          logger.info(`S3ArtifactStorage: Set bucket policy to public read`);
        } catch (createError: any) {
          logger.error(`S3ArtifactStorage: Error creating bucket: ${createError.message}`);
          throw createError;
        }
      } else {
        // Log more details about the error
        logger.error(`S3ArtifactStorage: Error checking bucket. Name: ${error.name}, Message: ${error.message}, StatusCode: ${error.$metadata?.httpStatusCode}, RequestId: ${error.$metadata?.requestId}`);
        // For very detailed diagnostics, you might log the whole error object, but be cautious as it can be large.
        // logger.error(`S3ArtifactStorage: Full error object: ${JSON.stringify(error, null, 2)}`);
        throw error;
      }
    }
  }

  /**
   * Get content type from file path
   * @param filePath Path to the file
   * @returns Content type string
   * @private
   */
  private getContentTypeFromPath(filePath: string): string {
    const extension = path.extname(filePath).toLowerCase();

    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.mp4':
        return 'video/mp4';
      case '.webm':
        return 'video/webm';
      case '.html':
        return 'text/html';
      case '.json':
        return 'application/json';
      case '.txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Determine artifact type from content type
   * @param contentType MIME type
   * @returns Artifact type
   * @private
   */
  private getArtifactTypeFromContentType(contentType: string): 'screenshot' | 'video' | 'log' | 'other' {
    if (contentType.startsWith('image/')) {
      return 'screenshot';
    } else if (contentType.startsWith('video/')) {
      return 'video';
    } else if (contentType === 'text/plain' || contentType === 'application/json') {
      return 'log';
    } else {
      return 'other';
    }
  }
}
