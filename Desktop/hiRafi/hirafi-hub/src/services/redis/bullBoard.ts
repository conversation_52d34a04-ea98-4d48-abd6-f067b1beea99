/**
 * Bull Board UI Service
 * Provides a web UI for monitoring Bull queues
 */

import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter.js';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bullmq';
import { logger } from '../../utils/logger.js';
import { queueService, QUEUE_NAMES } from './queueService.js';

class BullBoardService {
  private static instance: BullBoardService;
  private serverAdapter: ExpressAdapter | null = null;
  private isInitialized: boolean = false;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): BullBoardService {
    if (!BullBoardService.instance) {
      BullBoardService.instance = new BullBoardService();
    }
    return BullBoardService.instance;
  }

  /**
   * Initialize Bull Board
   */
  public async initialize(): Promise<ExpressAdapter> {
    if (this.isInitialized && this.serverAdapter) {
      return this.serverAdapter;
    }

    // Create server adapter
    this.serverAdapter = new ExpressAdapter();
    this.serverAdapter.setBasePath('/admin/queues');

    // Create Bull Board
    const { addQueue, removeQueue, setQueues } = createBullBoard({
      queues: [],
      serverAdapter: this.serverAdapter,
    });

    // Add main queues
    try {
      // Wait for queue service to initialize
      await queueService.initialize();

      // Add main queues with enhanced configuration for the test queue
      const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (testQueue) {
        // Use any type to bypass TypeScript errors
        const testAdapter = new BullMQAdapter(testQueue) as any;

        // Configure the adapter to show more details about retries and job attempts
        testAdapter.setFormatter({
          // Show more details in the job data display
          formatters: {
            // Format job data to show more details
            data: (data: any) => {
              try {
                // If the data has a testId, highlight it
                if (data && data.testId) {
                  return {
                    ...data,
                    _testId: data.testId, // Duplicate for visibility
                    _platform: data.platform || 'unknown',
                    _sauceLabsTest: data.environmentSettings?.appium?.useSauceLabs === true
                  };
                }
                return data;
              } catch (error) {
                return data;
              }
            }
          }
        });

        addQueue(testAdapter);
        logger.info(`BullBoard: Added ${QUEUE_NAMES.TEST_QUEUE} to dashboard with enhanced retry visibility`);
      }

      const resultQueue = queueService.getQueue(QUEUE_NAMES.RESULT_QUEUE);
      if (resultQueue) {
        // Use any type to bypass TypeScript errors
        const resultAdapter = new BullMQAdapter(resultQueue) as any;
        addQueue(resultAdapter);
        logger.info(`BullBoard: Added ${QUEUE_NAMES.RESULT_QUEUE} to dashboard`);
      }

      const stepProgressQueue = queueService.getQueue(QUEUE_NAMES.STEP_PROGRESS_QUEUE);
      if (stepProgressQueue) {
        // Use any type to bypass TypeScript errors
        const stepProgressAdapter = new BullMQAdapter(stepProgressQueue) as any;
        addQueue(stepProgressAdapter);
        logger.info(`BullBoard: Added ${QUEUE_NAMES.STEP_PROGRESS_QUEUE} to dashboard`);
      }

      // Node registration queue removed - using WebSocket-only registration

      // Add node-specific queues
      this.addNodeSpecificQueues(addQueue);

      this.isInitialized = true;

    } catch (error) {
      logger.error(`BullBoard: Initialization error: ${error}`);
    }

    return this.serverAdapter;
  }

  /**
   * Add node-specific queues to Bull Board - DEPRECATED in Pull Model
   * This method is kept for backward compatibility during migration
   * @param addQueueFn Function to add a queue to Bull Board
   */
  private addNodeSpecificQueues(addQueueFn: any): void {
    try {
      // In the Pull Model, we don't use node-specific queues anymore
      // Just add the main test queue if it's not already added
      const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (testQueue) {
        // Use any type to bypass TypeScript errors
        const adapter = new BullMQAdapter(testQueue) as any;
        addQueueFn(adapter);
        logger.info(`BullBoard: Added central test queue to dashboard (Pull Model)`);
      }
    } catch (error) {
      logger.error(`BullBoard: Error adding central test queue: ${error}`);
    }
  }

  /**
   * Add a queue to Bull Board
   * @param queue Queue to add
   * @param name Optional name for the queue
   */
  public addQueue(queue: Queue, name?: string): void {
    if (!this.serverAdapter) {
      logger.warn('BullBoard: Cannot add queue - Bull Board not initialized');
      return;
    }

    try {
      const { addQueue } = createBullBoard({
        queues: [],
        serverAdapter: this.serverAdapter,
      });

      // Use any type to bypass TypeScript errors
      const adapter = new BullMQAdapter(queue) as any;
      addQueue(adapter);
      logger.info(`BullBoard: Added queue ${name || queue.name} to dashboard`);
    } catch (error) {
      logger.error(`BullBoard: Error adding queue ${name || queue.name}: ${error}`);
    }
  }

  /**
   * Get the Express adapter
   */
  public getServerAdapter(): ExpressAdapter | null {
    return this.serverAdapter;
  }
}

// Export singleton instance
export const bullBoardService = BullBoardService.getInstance();
export default bullBoardService;
