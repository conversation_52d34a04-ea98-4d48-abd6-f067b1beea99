/**
 * Common Error Handling Utilities
 * Shared error handling patterns for all platforms
 */

import { logger } from '../../../utils/logger.js';

/**
 * Standard error result interface
 */
export interface ErrorResult {
  success: false;
  error: string;
  errorCode?: string;
  details?: any;
}

/**
 * Standard success result interface
 */
export interface SuccessResult<T = any> {
  success: true;
  data?: T;
}

/**
 * Combined result type
 */
export type Result<T = any> = SuccessResult<T> | ErrorResult;

/**
 * Create a standardized error result
 * @param error Error message or Error object
 * @param errorCode Optional error code
 * @param details Optional error details
 * @returns Standardized error result
 */
export function createErrorResult(
  error: string | Error,
  errorCode?: string,
  details?: any
): ErrorResult {
  const errorMessage = error instanceof Error ? error.message : error;

  return {
    success: false,
    error: errorMessage,
    errorCode,
    details
  };
}

/**
 * Create a standardized success result
 * @param data Optional data to include
 * @returns Standardized success result
 */
export function createSuccessResult<T>(data?: T): SuccessResult<T> {
  return {
    success: true,
    data
  };
}

/**
 * Handle async operations with standardized error handling
 * @param operation Async operation to execute
 * @param context Context for logging
 * @param errorCode Optional error code
 * @returns Result with success/error status
 */
export async function handleAsyncOperation<T>(
  operation: () => Promise<T>,
  context: string,
  errorCode?: string
): Promise<Result<T>> {
  try {
    const result = await operation();
    logger.debug(`${context}: Operation completed successfully`);
    return createSuccessResult(result);
  } catch (error: any) {
    const errorMessage = error.message || 'Unknown error occurred';
    logger.error(`${context}: Operation failed: ${errorMessage}`);
    // Don't pass the full error object to avoid stack traces in database
    return createErrorResult(errorMessage, errorCode);
  }
}

/**
 * Handle sync operations with standardized error handling
 * @param operation Sync operation to execute
 * @param context Context for logging
 * @param errorCode Optional error code
 * @returns Result with success/error status
 */
export function handleSyncOperation<T>(
  operation: () => T,
  context: string,
  errorCode?: string
): Result<T> {
  try {
    const result = operation();
    logger.debug(`${context}: Operation completed successfully`);
    return createSuccessResult(result);
  } catch (error: any) {
    const errorMessage = error.message || 'Unknown error occurred';
    logger.error(`${context}: Operation failed: ${errorMessage}`);
    // Don't pass the full error object to avoid stack traces in database
    return createErrorResult(errorMessage, errorCode);
  }
}

/**
 * Check if an error is an assertion or test failure
 * These errors should NEVER be retried as they indicate test logic issues
 * @param error Error to check
 * @returns True if the error is an assertion/test failure
 */
export function isAssertionError(error: Error | string): boolean {
  const errorMessage = error instanceof Error ? error.message : error;
  const lowerMessage = errorMessage.toLowerCase();

  const assertionPatterns = [
    // Common assertion patterns
    'assertion',
    'assert',
    'expected',
    'actual',
    'timeout waiting',
    'element is not visible',
    'element is not present',
    'test assertion',
    'verification failed',
    'condition not met',
    'expected condition',
    'the assertion',
    'suggests waiting for',
    'there are no',
    'content not available',
    'page content does not match'
  ];

  return assertionPatterns.some(pattern => lowerMessage.includes(pattern));
}

/**
 * Check if an error is a server/service error
 * @param error Error to check
 * @returns True if the error is a server/service error
 */
export function isServerError(error: Error | string): boolean {
  const errorMessage = error instanceof Error ? error.message : error;
  const lowerMessage = errorMessage.toLowerCase();

  const serverErrorPatterns = [
    'econnreset',
    'etimedout',
    'enotfound',
    'enetunreach',
    'econnrefused',
    'socket hang up',
    'connection reset',
    'network timeout',
    'service unavailable',
    'too many requests',
    'gateway timeout',
    'internal server error',
    'bad gateway',
    '500',
    '502',
    '503',
    '504',
    '429'
  ];

  return serverErrorPatterns.some(pattern => lowerMessage.includes(pattern));
}

/**
 * Validate required parameters
 * @param params Object with parameters to validate
 * @param requiredFields Array of required field names
 * @param context Context for error messages
 * @returns Validation result
 */
export function validateRequiredParams(
  params: Record<string, any>,
  requiredFields: string[],
  context: string
): Result<void> {
  const missingFields: string[] = [];

  for (const field of requiredFields) {
    if (params[field] === undefined || params[field] === null) {
      missingFields.push(field);
    }
  }

  if (missingFields.length > 0) {
    const errorMessage = `Missing required parameters: ${missingFields.join(', ')}`;
    logger.error(`${context}: ${errorMessage}`);
    // Only pass the missing fields list, not the full error object
    return createErrorResult(errorMessage, 'MISSING_PARAMS');
  }

  return createSuccessResult();
}

/**
 * Safe JSON parse with error handling
 * @param jsonString JSON string to parse
 * @param context Context for logging
 * @returns Parsed object or error result
 */
export function safeJsonParse<T = any>(jsonString: string, context: string): Result<T> {
  try {
    const parsed = JSON.parse(jsonString);
    return createSuccessResult(parsed);
  } catch (error: any) {
    const errorMessage = `Invalid JSON: ${error.message}`;
    logger.error(`${context}: ${errorMessage}`);
    // Don't pass the full error object to avoid stack traces in database
    return createErrorResult(errorMessage, 'INVALID_JSON');
  }
}

/**
 * Safe JSON stringify with error handling
 * @param obj Object to stringify
 * @param context Context for logging
 * @returns JSON string or error result
 */
export function safeJsonStringify(obj: any, context: string): Result<string> {
  try {
    const jsonString = JSON.stringify(obj, null, 2);
    return createSuccessResult(jsonString);
  } catch (error: any) {
    const errorMessage = `JSON stringify failed: ${error.message}`;
    logger.error(`${context}: ${errorMessage}`);
    // Don't pass the full error object to avoid stack traces in database
    return createErrorResult(errorMessage, 'STRINGIFY_FAILED');
  }
}

/**
 * Check if result is an error
 * @param result Result to check
 * @returns True if result is an error
 */
export function isErrorResult(result: Result): result is ErrorResult {
  return !result.success;
}

/**
 * Check if result is successful
 * @param result Result to check
 * @returns True if result is successful
 */
export function isSuccessResult<T>(result: Result<T>): result is SuccessResult<T> {
  return result.success;
}

/**
 * Simplify error message by removing common prefixes, stack traces, and cleaning up
 * @param message Error message to simplify
 * @returns Simplified error message
 */
export function simplifyErrorMessage(message: string): string {
  if (!message) return '';

  // Replace midscene/midscenejs with hirafi
  let simplified = message.replace(/midscene(js)?/gi, 'hirafi');

  // Remove stack traces - keep only the first line (the actual error message)
  if (simplified.includes('\n    at ') || simplified.includes('\n  at ')) {
    simplified = simplified.split('\n')[0].trim();
  }

  // Remove common prefixes
  const prefixes = [
    'AI Assertion error:',
    'AI Action error:',
    'AI Wait error:',
    'AI Query error:'
  ];

  for (const prefix of prefixes) {
    if (simplified.includes(prefix)) {
      simplified = simplified.replace(prefix, '').trim();
      break;
    }
  }

  // Remove troubleshooting links
  if (simplified.includes('Trouble shooting:')) {
    simplified = simplified.split('Trouble shooting:')[0].trim();
  }

  return simplified;
}
