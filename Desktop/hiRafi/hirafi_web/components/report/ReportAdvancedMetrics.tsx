import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  BarChart2,
  Server,
  Accessibility,
  Network,
  Info,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  Code,
  Smartphone,
  Battery,
  Cpu,
  MemoryStick,
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger
} from '@/components/ui/tooltip';
import { useReport } from '@/hooks/useReport'; // Import the hook
import { AccessibilityViolationItem, NetworkLogEntry } from '@/types/metrics';
import { detectReportPlatform, isMetricRelevantForPlatform } from '@/lib/utils/platform-utils';

interface ReportAdvancedMetricsProps {
  hookResult: ReturnType<typeof useReport>;
}

// Helper function for safe counting
const safeCount = (count: unknown): number => {
  return typeof count === 'number' ? count : 0;
};

function WebVitals({ metrics }: { metrics: any }) { // Assuming metrics structure for now
  // Check for performance metrics in multiple possible locations
  const performanceData = metrics?.tracingData?.performanceMetrics ||
                         metrics?.performanceMetrics ||
                         metrics?.pageMetrics;

  if (!performanceData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <BarChart2 className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Web Vitals</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <AlertCircle className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">No Web Vitals data available</p>
            <p className="text-sm text-gray-400 max-w-md mx-auto">
              Web performance metrics may not be enabled for this test or no data was collected during execution.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Convert existing performance metrics to Web Vitals format
  const webVitals = {
    // Use firstContentfulPaint as FCP (First Contentful Paint) - close to LCP for simple pages
    LCP: performanceData.firstContentfulPaint || performanceData.firstPaint || null,
    // Use domContentLoaded as a proxy for interactivity timing
    FID: performanceData.domContentLoaded && performanceData.domContentLoaded < 100 ? performanceData.domContentLoaded : null,
    // CLS is not available from basic metrics, set to 0 as default
    CLS: 0,
    // Use loadComplete as TTFB proxy
    TTFB: performanceData.loadComplete || null,
    // INP is not available from basic metrics
    INP: null
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <BarChart2 className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">Web Vitals</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  FCP
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>First Contentful Paint (FCP) measures loading performance. Should be below 1800ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!webVitals.LCP ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : webVitals.LCP > 1800 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{webVitals.LCP ? webVitals.LCP.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">First Contentful Paint (ms)</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  DCL
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>DOM Content Loaded measures when initial HTML is loaded. Should be below 100ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!webVitals.FID ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : webVitals.FID > 100 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{webVitals.FID ? webVitals.FID.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">DOM Content Loaded (ms)</div>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  FP
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>First Paint measures when first pixel is rendered. Should be below 1000ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!performanceData.firstPaint ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : performanceData.firstPaint > 1000 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{performanceData.firstPaint ? performanceData.firstPaint.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">First Paint (ms)</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  LC
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>Load Complete measures when page is fully loaded. Should be below 50ms.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!webVitals.TTFB ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : webVitals.TTFB > 50 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Bad</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Good</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{webVitals.TTFB ? webVitals.TTFB.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">Load Complete (ms)</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium flex items-center gap-1">
                  CT
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p>Collection Time measures how long metrics collection took. Lower is better.</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </span>
                {!performanceData.collectionTime ? (
                  <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">No Metric</Badge>
                ) : performanceData.collectionTime > 1000 ? (
                  <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300">Slow</Badge>
                ) : (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300">Fast</Badge>
                )}
              </div>
              <div className="text-2xl font-bold">{performanceData.collectionTime ? performanceData.collectionTime.toFixed(2) : 'N/A'}</div>
              <div className="text-xs text-gray-500 mt-1">Collection Time (ms)</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function NetworkStatistics({ metrics }: { metrics: any }) { // Assuming metrics structure for now
  if (!metrics?.networkData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Server className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Network Statistics</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Network className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">No Network Statistics available</p>
            <p className="text-sm text-gray-400 max-w-md mx-auto">
              Network tracking may not be enabled for this test or no network activity was recorded during execution.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const networkData = metrics.networkData;
  const totalRequests = networkData.requests?.total || 1;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Server className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">Network Statistics</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">Request Types</h4>
            {Object.entries(networkData.requests?.byType || {}).map(([type, count]) => (
              <div key={type} className="flex items-center mb-2">
                <div className="w-24 capitalize">{type}</div>
                <div className="flex-1 mx-2">
                  <div className="h-2 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-indigo-500"
                      style={{
                        width: `${Math.min(100, (Number(count) / totalRequests) * 100)}%`,
                      }}
                    ></div>
                  </div>
                </div>
                <div className="w-20 text-right text-sm">
                  {String(count)} ({Math.round((Number(count) / totalRequests) * 100)}%)
                </div>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="text-sm font-medium mb-1">Total Transfers</div>
              <div className="text-2xl font-bold">{networkData.transferred?.total?.toFixed(2) || 'N/A'}</div>
              <div className="text-xs text-gray-500">{networkData.transferred?.unit || 'Bytes'}</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="text-sm font-medium mb-1">Request Status</div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <div className="text-sm">Success: {networkData.requests?.successful || 0}</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-rose-500"></div>
                <div className="text-sm">Failed: {networkData.requests?.failed || 0}</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function AccessibilityMetrics({ hookResult }: ReportAdvancedMetricsProps) {
  const {
    report,
    selectedSeverityFilter,
    setSelectedSeverityFilter,
    showAllViolations,
    setShowAllViolations,
    filteredViolations
  } = hookResult;

  // Check for accessibility data in either enhancedMetrics or metrics field
  const violationsData = report?.enhancedMetrics?.accessibilityData?.violations ||
                         report?.metrics?.accessibilityData?.violations;

  if (!violationsData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Accessibility className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Accessibility Metrics</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Accessibility className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500">No accessibility data available for this test</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Accessibility className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">Accessibility Metrics</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/60 dark:to-gray-900/40 p-4 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Total Violations</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                  safeCount(violationsData.count) > 50
                  ? "bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300"
                  : safeCount(violationsData.count) > 20
                  ? "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300"
                  : "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300"
                }`}>
                  {safeCount(violationsData.count) > 50 ? "Critical" : safeCount(violationsData.count) > 20 ? "Warning" : "Good"}
                </span>
              </div>
              <div className="text-3xl font-bold mb-1">{safeCount(violationsData.count).toString()}</div>
              <div className="text-xs text-gray-500">Found during test execution</div>
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/60 dark:to-gray-900/40 p-4 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
              <h3 className="text-sm font-medium mb-3">Severity Breakdown</h3>
              {violationsData.bySeverity ? (
                <div className="space-y-2">
                  {Object.entries(violationsData.bySeverity).map(([severity, count]) => (
                    <div key={severity} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className={`w-3 h-3 rounded-full ${
                          severity === 'critical' ? 'bg-rose-500' :
                          severity === 'serious' ? 'bg-amber-500' :
                          severity === 'moderate' ? 'bg-yellow-500' : 'bg-blue-500'
                        }`}></span>
                        <span className="text-sm capitalize">{severity}</span>
                      </div>
                      <span className="text-sm font-bold">{count as React.ReactNode}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-500">No severity data available</div>
              )}
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/60 dark:to-gray-900/40 p-4 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
              <h3 className="text-sm font-medium mb-3">Top Issue Types</h3>
              <div className="space-y-2">
                {violationsData.byType ? (
                  Object.entries(violationsData.byType)
                    .sort((a, b) => (b[1] as number) - (a[1] as number))
                    .slice(0, 4)
                    .map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="text-sm truncate max-w-[180px]">{type}</div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 h-1.5 rounded-full mt-1">
                            <div
                              className="bg-indigo-500 h-1.5 rounded-full"
                              style={{ width: `${Math.min(100, (count as number) / (violationsData.count || 1) * 100)}%` }}>
                            </div>
                          </div>
                        </div>
                        <span className="text-sm font-bold ml-2">{count as React.ReactNode}</span>
                      </div>
                    ))
                ) : (
                  <div className="text-sm text-gray-500">No issue type data available</div>
                )}
              </div>
            </div>
          </div>

          {/* Violation Details */}
          {filteredViolations.length > 0 ? (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-base font-medium">Violation Details</h3>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Filter:</span>
                  <select
                    className="text-sm border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded px-2 py-1"
                    value={selectedSeverityFilter}
                    onChange={(e) => setSelectedSeverityFilter(e.target.value)}
                  >
                    <option value="all">All Severities</option>
                    <option value="critical">Critical</option>
                    <option value="serious">Serious</option>
                    <option value="moderate">Moderate</option>
                    <option value="minor">Minor</option>
                  </select>
                </div>
              </div>
              <div className="space-y-3">
                {filteredViolations
                  .slice(0, showAllViolations ? undefined : 5)
                  .map((violation, index) => (
                    <ViolationDetailsCard key={index} violation={violation} />
                  ))}
              </div>
              {!showAllViolations && filteredViolations.length > 5 && (
                <div className="flex justify-center mt-4">
                  <Button variant="outline" className="text-sm flex items-center gap-1" onClick={() => setShowAllViolations(true)}>
                    <span>Show all {filteredViolations.length} violations</span><ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              )}
              {showAllViolations && filteredViolations.length > 5 && (
                <div className="flex justify-center mt-4">
                  <Button variant="outline" className="text-sm flex items-center gap-1" onClick={() => setShowAllViolations(false)}>
                    <span>Show less</span><ChevronUp className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          ) : null}
          <AccessibilityTips />
        </div>
      </CardContent>
    </Card>
  );
}

function ViolationDetailsCard({ violation }: { violation: AccessibilityViolationItem }) {
  return (
    <Card className={`overflow-hidden border ${
      violation.severity === 'critical' ? 'border-rose-200 dark:border-rose-800' :
      violation.severity === 'serious' ? 'border-amber-200 dark:border-amber-800' :
      'border-gray-200 dark:border-gray-800'
    }`}>
      <details className="group">
        <summary className="list-none cursor-pointer">
          <div className="flex items-start p-3 hover:bg-gray-50 dark:hover:bg-gray-900/30">
            <div className={`p-1.5 rounded-full flex-shrink-0 mt-0.5 ${
              violation.severity === 'critical' ? 'bg-rose-100 dark:bg-rose-900/30' :
              violation.severity === 'serious' ? 'bg-amber-100 dark:bg-amber-900/30' :
              'bg-blue-100 dark:bg-blue-900/30'
            }`}>
              {violation.severity === 'critical' ? (
                <AlertCircle className="h-4 w-4 text-rose-600 dark:text-rose-400" />
              ) : violation.severity === 'serious' ? (
                <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
              ) : (
                <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              )}
            </div>
            <div className="ml-3 flex-1">
              <div className="flex items-center justify-between">
                <div className="font-medium">{violation.type}</div>
                <Badge className={`ml-2 ${
                  violation.severity === 'critical' ? 'bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300' :
                  violation.severity === 'serious' ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300' :
                  'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                }`}>
                  {violation.severity}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{violation.description}</p>
              <div className="flex items-center gap-3 mt-2">
                <div className="px-2 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs text-gray-600 dark:text-gray-400">
                  {violation.element}
                </div>
                <span className="text-xs text-gray-500 flex items-center">
                  <Info className="h-3 w-3 mr-1" /> More details
                </span>
              </div>
            </div>
            <div className="ml-2 transition-transform group-open:rotate-180">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </summary>
        <div className="p-3 pt-0 border-t border-gray-100 dark:border-gray-800">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
            {violation.elementHtml && (
              <div>
                <h4 className="text-xs font-medium text-gray-500 mb-1">Element HTML</h4>
                <div className="bg-gray-50 dark:bg-gray-900 p-2 rounded-md border border-gray-200 dark:border-gray-800 overflow-auto max-h-24">
                  <pre className="text-xs font-mono">{violation.elementHtml}</pre>
                </div>
              </div>
            )}
            {violation.expectedResult && (
              <div>
                <h4 className="text-xs font-medium text-gray-500 mb-1">Expected Result</h4>
                <div className="bg-gray-50 dark:bg-gray-900 p-2 rounded-md border border-gray-200 dark:border-gray-800">
                  <p className="text-sm">{violation.expectedResult}</p>
                </div>
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
            {violation.failureReason && (
              <div>
                <h4 className="text-xs font-medium text-gray-500 mb-1">Failure Reason</h4>
                <div className="bg-gray-50 dark:bg-gray-900 p-2 rounded-md border border-gray-200 dark:border-gray-800">
                  <p className="text-sm">{violation.failureReason}</p>
                </div>
              </div>
            )}
            {violation.impact && (
              <div>
                <h4 className="text-xs font-medium text-gray-500 mb-1">Impact</h4>
                <div className="bg-gray-50 dark:bg-gray-900 p-2 rounded-md border border-gray-200 dark:border-gray-800">
                  <p className="text-sm">{violation.impact}</p>
                </div>
              </div>
            )}
          </div>
          <div className="mt-4">
            <h4 className="text-xs font-medium text-gray-500 mb-1">How to Fix</h4>
            <div className="bg-indigo-50 dark:bg-indigo-900/20 p-2 rounded-md border border-indigo-100 dark:border-indigo-800">
              <p className="text-sm">
                {violation.type.includes('alt') && 'Add appropriate alt text to image elements.' ||
                 violation.type.includes('label') && 'Ensure all form elements have associated labels.' ||
                 violation.type.includes('name') && 'Add accessible name to interactive elements.' ||
                 'Fix the accessibility issue based on WCAG guidelines.'}
              </p>
            </div>
          </div>
        </div>
      </details>
    </Card>
  );
}

function AccessibilityTips() {
  return (
    <Card>
      <CardHeader className="py-2 px-4">
        <CardTitle className="text-sm flex items-center gap-2"><Info className="h-4 w-4" /> Accessibility Tips</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="divide-y divide-gray-100 dark:divide-gray-800">
          <div className="p-3 flex items-start gap-3">
            <div className="bg-purple-100 dark:bg-purple-900/30 p-1.5 rounded-full">
              <AlertCircle className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <div className="text-sm font-medium">WCAG Compliance</div>
              <div className="text-xs text-gray-500 mt-0.5">Follow WCAG 2.1 AA standards</div>
            </div>
          </div>
          <div className="p-3 flex items-start gap-3">
            <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full">
              <Accessibility className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <div className="text-sm font-medium">Screen Reader Testing</div>
              <div className="text-xs text-gray-500 mt-0.5">Test with NVDA or VoiceOver</div>
            </div>
          </div>
          <div className="p-3 flex items-start gap-3">
            <div className="bg-indigo-100 dark:bg-indigo-900/30 p-1.5 rounded-full">
              <Code className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
            </div>
            <div>
              <div className="text-sm font-medium">Semantic HTML</div>
              <div className="text-xs text-gray-500 mt-0.5">Use nav, header, main etc.</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function NetworkLogs({ hookResult }: ReportAdvancedMetricsProps) {
  const {
    report,
    selectedNetworkTypeFilter,
    setSelectedNetworkTypeFilter,
    selectedNetworkStatusFilter,
    setSelectedNetworkStatusFilter,
    selectedNetworkPerformanceFilter,
    setSelectedNetworkPerformanceFilter,
    selectedNetworkMethodFilter,
    setSelectedNetworkMethodFilter,
    showAllNetworkLogs,
    setShowAllNetworkLogs,
    filteredNetworkLogs,
    uniqueNetworkTypes,
    uniqueHttpMethods,
    formatTime
  } = hookResult;

  // Check if filters are applied
  const isFiltered = selectedNetworkTypeFilter !== 'all' ||
                    selectedNetworkStatusFilter !== 'all' ||
                    selectedNetworkPerformanceFilter !== 'all' ||
                    selectedNetworkMethodFilter !== 'all';

  // Always render the Card with filtering controls
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Network className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Network Logs</CardTitle>
            {filteredNetworkLogs.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {filteredNetworkLogs.length} requests
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-gray-500">Filters:</span>

            {/* Type Filter */}
            <select
              className="text-sm border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded px-2 py-1"
              value={selectedNetworkTypeFilter}
              onChange={(e) => setSelectedNetworkTypeFilter(e.target.value)}
            >
              <option value="all">All Types</option>
              {uniqueNetworkTypes.map(type => {
                // Display user-friendly names for types
                const displayName = type === 'xhr' ? 'API Requests'
                                  : type === 'document' ? 'Documents'
                                  : type === 'other' ? 'Other'
                                  : type;
                return (
                  <option key={type} value={type}>{displayName}</option>
                );
              })}
            </select>

            {/* Status Filter */}
            <select
              className="text-sm border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded px-2 py-1"
              value={selectedNetworkStatusFilter}
              onChange={(e) => setSelectedNetworkStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="success">Success (2xx)</option>
              <option value="redirect">Redirect (3xx)</option>
              <option value="client-error">Client Error (4xx)</option>
              <option value="server-error">Server Error (5xx)</option>
              <option value="failed">Failed (4xx+)</option>
            </select>

            {/* Performance Filter */}
            <select
              className="text-sm border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded px-2 py-1"
              value={selectedNetworkPerformanceFilter}
              onChange={(e) => setSelectedNetworkPerformanceFilter(e.target.value)}
            >
              <option value="all">All Requests</option>
              <option value="api-only">API Only</option>
              <option value="slow">Slow (&gt;1s)</option>
              <option value="fast">Fast (≤500ms)</option>
            </select>

            {/* HTTP Method Filter */}
            <select
              className="text-sm border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded px-2 py-1"
              value={selectedNetworkMethodFilter}
              onChange={(e) => setSelectedNetworkMethodFilter(e.target.value)}
            >
              <option value="all">All Methods</option>
              {uniqueHttpMethods.map(method => (
                <option key={method} value={method}>{method}</option>
              ))}
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {filteredNetworkLogs.length === 0 ? (
          // No results content - but filtering controls remain visible above
          <div className="text-center py-8">
            <Network className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">
              {isFiltered ? "No requests match the current filters" : "No network logs available for this test"}
            </p>
            <p className="text-sm text-gray-400">
              {isFiltered
                ? "Try adjusting your filters to see more results. Only API requests and documents are monitored."
                : "Network monitoring focuses on API requests and documents for better performance insights. Static assets (images, CSS, JS) are automatically filtered out."
              }
            </p>
          </div>
        ) : (
          // Results content
          <>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-800 text-left">
                    <th className="py-2 px-3 text-sm font-medium text-gray-500">URL</th>
                    <th className="py-2 px-3 text-sm font-medium text-gray-500">Method</th>
                    <th className="py-2 px-3 text-sm font-medium text-gray-500">Status</th>
                    <th className="py-2 px-3 text-sm font-medium text-gray-500">Type</th>
                    <th className="py-2 px-3 text-sm font-medium text-gray-500">Size</th>
                    <th className="py-2 px-3 text-sm font-medium text-gray-500">Time</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredNetworkLogs
                    .slice(0, showAllNetworkLogs ? undefined : 10)
                    .map((log, index) => (
                      <NetworkLogRow key={index} log={log} formatTime={formatTime} index={index} />
                    ))}
                </tbody>
              </table>
            </div>
            {!showAllNetworkLogs && filteredNetworkLogs.length > 10 && (
              <div className="flex justify-center mt-4">
                <Button variant="outline" className="text-sm flex items-center gap-1" onClick={() => setShowAllNetworkLogs(true)}>
                  <span>Show all {filteredNetworkLogs.length} requests</span><ChevronDown className="h-4 w-4" />
                </Button>
              </div>
            )}
            {showAllNetworkLogs && filteredNetworkLogs.length > 10 && (
              <div className="flex justify-center mt-4">
                <Button variant="outline" className="text-sm flex items-center gap-1" onClick={() => setShowAllNetworkLogs(false)}>
                  <span>Show less</span><ChevronUp className="h-4 w-4" />
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

function NetworkLogRow({ log, formatTime, index }: { log: NetworkLogEntry, formatTime: (time: string) => string, index: number }) {
  const statusColor = log.status >= 400 ? "text-rose-600 dark:text-rose-400"
                   : log.status >= 300 ? "text-amber-600 dark:text-amber-400"
                   : "text-emerald-600 dark:text-emerald-400";

  // Enhanced row background for better visual distinction
  const isApiRequest = log.type === 'xhr' || (log as any).isApiRequest;
  const isFailed = log.status >= 400;
  const timeMatch = log.time.match(/(\d+\.?\d*)/);
  const timeMs = timeMatch ? parseFloat(timeMatch[1]) : 0;
  const isSlow = timeMs > 1000;

  let rowBg = "";
  if (isFailed) {
    rowBg = "bg-rose-50 dark:bg-rose-900/10";
  } else if (isApiRequest) {
    rowBg = "bg-blue-50 dark:bg-blue-900/10";
  } else if (index % 2 === 0) {
    rowBg = "bg-gray-50 dark:bg-gray-900/30";
  }

  return (
    <tr className={`border-b border-gray-100 dark:border-gray-800 ${rowBg}`}>
      <td className="py-2 px-3 text-sm font-mono truncate max-w-[200px]" title={log.url}>
        <div className="flex items-center gap-2">
          {isApiRequest && (
            <Badge variant="outline" className="text-xs px-1 py-0 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300">
              API
            </Badge>
          )}
          <span className="truncate">{log.url}</span>
        </div>
      </td>
      <td className="py-2 px-3 text-sm">
        <span className={`px-2 py-1 rounded text-xs font-medium ${
          log.method === 'GET' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300' :
          log.method === 'POST' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300' :
          log.method === 'PUT' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300' :
          log.method === 'DELETE' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300' :
          log.method === 'PATCH' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300' :
          'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
        }`}>
          {log.method}
        </span>
      </td>
      <td className="py-2 px-3 text-sm">
        <span className={statusColor}>{log.status}</span>
        {isFailed && (
          <Badge variant="destructive" className="text-xs ml-1 px-1 py-0">
            FAILED
          </Badge>
        )}
      </td>
      <td className="py-2 px-3 text-sm">
        {log.type === 'xhr' ? 'API'
         : log.type === 'document' ? 'Document'
         : log.type === 'other' ? 'Other'
         : log.type}
      </td>
      <td className="py-2 px-3 text-sm">{log.size}</td>
      <td className="py-2 px-3 text-sm">
        <span className={isSlow ? "text-amber-600 dark:text-amber-400 font-medium" : ""}>
          {formatTime(log.time)}
        </span>
        {isSlow && (
          <Badge variant="outline" className="text-xs ml-1 px-1 py-0 bg-amber-100 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300">
            SLOW
          </Badge>
        )}
      </td>
    </tr>
  );
}

// Android-specific metrics components
function AndroidDeviceMetrics({ metrics }: { metrics: any }) {
  if (!metrics?.deviceInfo && !metrics?.cpuUsage && !metrics?.memoryUsage && !metrics?.batteryLevel) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Smartphone className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">Device Performance</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Smartphone className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">No device performance data available</p>
            <p className="text-sm text-gray-400 max-w-md mx-auto">
              Device metrics may not be enabled for this test or no data was collected during execution.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Smartphone className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">Device Performance</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* CPU Usage */}
            {metrics.cpuUsage && (
              <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Cpu className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium">CPU Usage</span>
                </div>
                <div className="text-2xl font-bold">
                  {typeof metrics.cpuUsage === 'number'
                    ? `${metrics.cpuUsage.toFixed(1)}%`
                    : metrics.cpuUsage.percentage || 'N/A'
                  }
                </div>
                <div className="text-xs text-gray-500 mt-1">Average during test</div>
              </div>
            )}

            {/* Memory Usage */}
            {metrics.memoryUsage && (
              <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <MemoryStick className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium">Memory Usage</span>
                </div>
                <div className="text-2xl font-bold">
                  {typeof metrics.memoryUsage === 'number'
                    ? `${(metrics.memoryUsage / 1024 / 1024).toFixed(1)} MB`
                    : metrics.memoryUsage.used || 'N/A'
                  }
                </div>
                <div className="text-xs text-gray-500 mt-1">Peak memory usage</div>
              </div>
            )}

            {/* Battery Level */}
            {metrics.batteryLevel && (
              <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Battery className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                  <span className="text-sm font-medium">Battery Level</span>
                </div>
                <div className="text-2xl font-bold">
                  {typeof metrics.batteryLevel === 'number'
                    ? `${metrics.batteryLevel}%`
                    : metrics.batteryLevel.level || 'N/A'
                  }
                </div>
                <div className="text-xs text-gray-500 mt-1">At test completion</div>
              </div>
            )}

            {/* Active App */}
            {metrics.activeApp && (
              <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Smartphone className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  <span className="text-sm font-medium">Active App</span>
                </div>
                <div className="text-lg font-bold truncate">
                  {metrics.activeApp}
                </div>
                <div className="text-xs text-gray-500 mt-1">Package name</div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function AndroidNetworkMetrics({ metrics }: { metrics: any }) {
  // For Android, we might have different network data structure
  // Focus on API calls rather than web requests
  if (!metrics?.networkData) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Network className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">API Calls</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Network className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">No API call data available</p>
            <p className="text-sm text-gray-400 max-w-md mx-auto">
              Network monitoring may not be enabled for this test or no API calls were made during execution.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const networkData = metrics.networkData;
  const totalRequests = networkData.requests?.total || 1;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Network className="h-5 w-5 text-indigo-500" />
          <CardTitle className="text-lg">API Calls</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="text-sm font-medium mb-1">Total API Calls</div>
              <div className="text-2xl font-bold">{totalRequests}</div>
              <div className="text-xs text-gray-500">During test execution</div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
              <div className="text-sm font-medium mb-1">Success Rate</div>
              <div className="text-2xl font-bold">
                {totalRequests > 0
                  ? `${Math.round(((networkData.requests?.successful || 0) / totalRequests) * 100)}%`
                  : 'N/A'
                }
              </div>
              <div className="text-xs text-gray-500">
                {networkData.requests?.successful || 0} successful
              </div>
            </div>
          </div>

          {networkData.requests?.byType && Object.keys(networkData.requests.byType).length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium mb-2">Request Types</h4>
              {Object.entries(networkData.requests.byType).map(([type, count]) => (
                <div key={type} className="flex items-center mb-2">
                  <div className="w-24 capitalize">{type}</div>
                  <div className="flex-1 mx-2">
                    <div className="h-2 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-indigo-500"
                        style={{
                          width: `${Math.min(100, (Number(count) / totalRequests) * 100)}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-20 text-right text-sm">
                    {String(count)} ({Math.round((Number(count) / totalRequests) * 100)}%)
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function ReportAdvancedMetrics({ hookResult }: ReportAdvancedMetricsProps) {
  const { report } = hookResult;

  // Check for metrics data in either enhancedMetrics or metrics field
  const metricsData = report?.enhancedMetrics || report?.metrics;

  if (!metricsData) {
    return (
      <div className="text-center py-12">
        <Info className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <p className="text-gray-500">No advanced metrics data available for this test.</p>
      </div>
    );
  }

  // Detect platform to render appropriate metrics
  const platform = detectReportPlatform(report);
  const isAndroid = platform === 'android';

  if (isAndroid) {
    // Android-specific metrics layout
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AndroidDeviceMetrics metrics={metricsData} />
          <AndroidNetworkMetrics metrics={metricsData} />
        </div>
        {/* Only show accessibility if relevant for Android */}
        {metricsData.accessibilityData && (
          <AccessibilityMetrics hookResult={hookResult} />
        )}
        {/* Show network logs but with Android context */}
        {metricsData.networkData?.logs && (
          <NetworkLogs hookResult={hookResult} />
        )}
      </div>
    );
  }

  // Web-specific metrics layout (preserve existing functionality)
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <WebVitals metrics={metricsData} />
        <NetworkStatistics metrics={metricsData} />
      </div>
      <AccessibilityMetrics hookResult={hookResult} />
      <NetworkLogs hookResult={hookResult} />
    </div>
  );
}