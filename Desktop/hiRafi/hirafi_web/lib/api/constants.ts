/**
 * API URL ve endpoint sabitleri
 *
 * <PERSON><PERSON> dosya, tüm API URL'lerini ve endpoint'leri merkezi bir yerde tutar.
 * Herhangi bir endpoint de<PERSON><PERSON><PERSON>, sadece bu dosyayı güncellemek yeterlidir.
 */

// Ana API URL'si
// Doğrudan API'ye bağlan
// Çevre değişkeninden API URL'sini al, yoksa varsayılan de<PERSON> kullan
// Development ortamında localhost:5001, production ortamında hirafi.ai:5000 kullanılır
export const API_BASE_URL = typeof window !== 'undefined'
  ? process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:5001/api"
  : process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:5001/api"

// Auth endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  REFRESH: '/auth/refresh',
  LOGOUT: '/auth/logout',
  VERIFY: '/auth/validate', // Düzeltildi: verify -> validate
  RESET_PASSWORD: '/auth/reset-password',
  CHANGE_PASSWORD: '/auth/change-password',
  PROFILE: '/auth/profile',
  VALIDATE: '/auth/validate',
};

// Dashboard endpoints
export const DASHBOARD_ENDPOINTS = {
  STATS: '/dashboard/stats',
  RECENT_RUNS: '/dashboard/recent-runs',
  RECENT_SCENARIOS: '/dashboard/recent-scenarios',
  ACTIVITY: '/dashboard/activity',
};

// Scenario endpoints
export const SCENARIO_ENDPOINTS = {
  BASE: '/scenarios',
  GET_ALL: '/scenarios',
  GET_BY_ID: (id: string) => `/scenarios/${id}`,
  CREATE: '/scenarios',
  UPDATE: (id: string) => `/scenarios/${id}`,
  DELETE: (id: string) => `/scenarios/${id}`,
  DUPLICATE: (id: string) => `/scenarios/${id}/duplicate`,
  EXPORT: '/scenario-export/export',
  IMPORT: '/scenarios/import',
  BULK_DELETE: '/scenarios/bulk-delete',
  BULK_DUPLICATE: '/scenarios/bulk-duplicate',
  AGGREGATED: '/scenarios/aggregated',
  MY_SCENARIOS: '/scenarios/my-scenarios',
};

// Folder endpoints
export const FOLDER_ENDPOINTS = {
  BASE: '/folders',
  GET_ALL: '/folders',
  GET_BY_ID: (id: string) => `/folders/${id}`,
  CREATE: '/folders',
  UPDATE: (id: string) => `/folders/${id}`,
  DELETE: (id: string) => `/folders/${id}`,
};

// Run endpoints
export const RUN_ENDPOINTS = {
  BASE: '/runs',
  GET_ALL: '/runs',
  GET_BY_ID: (id: string) => `/runs/${id}`,
  CREATE: '/runs',
  UPDATE: (id: string) => `/runs/${id}`,
  DELETE: (id: string) => `/runs/${id}`,
  EXECUTE: (id: string) => `/runs/${id}/execute`,
  STOP: (id: string) => `/runs/${id}/stop`,
  STATUS: (id: string) => `/runs/${id}/status`,
  REPORTS: (id: string) => `/runs/${id}/reports`,
  TESTS: (id: string) => `/runs/${id}/tests`,
  DETAIL: (id: string) => `/runs/${id}/detail`,
  SCENARIO_REPORTS: (id: string) => `/runs/${id}/scenario-reports`,
  GET_REPORT: (id: string) => `/reports/${id}`,
  REPORT_DETAIL: (id: string) => `/reports/run-detail/${id}`,
  GET_ACTIVE: '/runs/active',
};

// Team endpoints
export const TEAM_ENDPOINTS = {
  BASE: '/teams',
  GET_ALL: '/teams',
  GET_BY_ID: (id: string) => `/teams/${id}`,
  CREATE: '/teams',
  UPDATE: (id: string) => `/teams/${id}`,
  DELETE: (id: string) => `/teams/${id}`,
  MEMBERS: {
    GET_ALL: (teamId: string) => `/teams/${teamId}/members`,
    GET_BY_ID: (teamId: string, userId: string) => `/teams/${teamId}/members/${userId}`,
    ADD: (teamId: string) => `/teams/${teamId}/members`,
    UPDATE: (teamId: string, userId: string) => `/teams/${teamId}/members/${userId}`,
    DELETE: (teamId: string, userId: string) => `/teams/${teamId}/members/${userId}`,
    BULK_INVITE: (teamId: string) => `/teams/${teamId}/members/bulk-invite`,
    UPDATE_STATUS: (teamId: string, userId: string) => `/teams/${teamId}/members/${userId}/status`,
  },
  ROLES: {
    GET_ALL: (teamId: string) => `/teams/${teamId}/roles`,
    GET_BY_ID: (teamId: string, roleId: string) => `/teams/${teamId}/roles/${roleId}`,
    CREATE: (teamId: string) => `/teams/${teamId}/roles`,
    UPDATE: (teamId: string, roleId: string) => `/teams/${teamId}/roles/${roleId}`,
    DELETE: (teamId: string, roleId: string) => `/teams/${teamId}/roles/${roleId}`,
  },
};

// Settings endpoints
export const SETTINGS_ENDPOINTS = {
  BASE: '/settings',
  GET_ALL: '/settings',
  UPDATE: '/settings',
  AI_MODELS: {
    GET_ALL: '/settings/ai-models',
    GET_BY_ID: (id: string) => `/settings/ai-models/${id}`,
    CREATE: '/settings/ai-models',
    UPDATE: (id: string) => `/settings/ai-models/${id}`,
    DELETE: (id: string) => `/settings/ai-models/${id}`,
    ACTIVATE: (id: string) => `/settings/ai-models/${id}/activate`,
    DEACTIVATE: (id: string) => `/settings/ai-models/${id}/deactivate`,
  },
};

// Plugin endpoints
export const PLUGIN_ENDPOINTS = {
  BASE: '/plugins',
  GET_ALL: '/plugins',
  GET_BY_ID: (id: string) => `/plugins/${id}`,
  INSTALL: '/plugins/install',
  UNINSTALL: (id: string) => `/plugins/${id}/uninstall`,
  ACTIVATE: (id: string) => `/plugins/${id}/activate`,
  DEACTIVATE: (id: string) => `/plugins/${id}/deactivate`,
  GET_AVAILABLE_TEST_MANAGEMENT: '/plugins/get_available_test_management_integrations',
};

// TestRail endpoints
export const TESTRAIL_ENDPOINTS = {
  GET_CONFIG: '/plugins/get_testrail_config',
  GET_CASES: '/plugins/get_testrail_cases',
  GET_CASE_STEPS: '/plugins/get_testrail_case_steps',
  GET_PROJECTS: '/plugins/get_testrail_projects',
  GET_SUITES: '/plugins/get_suites',
  GET_SUITES_FOR_PROJECT: '/plugins/get_suites_for_project',
  SAVE_CONFIG: '/plugins/save_testrail_config',
  TEST_HEALTH: '/plugins/testrail-health',
  DISCONNECT: '/plugins/disconnect_testrail',
  // Lazy loading endpoints
  GET_PROJECTS_ONLY: '/plugins/get_testrail_projects_only',
  GET_CASES_FOR_SUITE: '/plugins/get_testrail_cases_for_suite',
};

// SauceLabs endpoints
export const SAUCELABS_ENDPOINTS = {
  TEST_CONNECTION: '/plugins/saucelabs-health',
  UPDATE_CONFIG: '/plugins/update-saucelabs-config',
  GET_CONFIG: '/plugins/get-saucelabs-config',
  GET_DEVICES: '/plugins/get-saucelabs-devices',
  GET_ACCOUNT_INFO: '/plugins/get-saucelabs-account-info',
  GET_APPS: '/plugins/get-saucelabs-apps',
  UPLOAD_APP: '/plugins/upload-saucelabs-app',
  DELETE_APP: (id: string) => `/plugins/delete-saucelabs-app/${id}`,
  GET_APP_GROUPS: '/plugins/get-saucelabs-app-groups',
  UPDATE_APP_SETTINGS: '/plugins/update-saucelabs-app-settings',
  DISCONNECT: '/plugins/disconnect-saucelabs',
};

// Testinium endpoints
export const TESTINIUM_ENDPOINTS = {
  TEST_CONNECTION: '/plugins/testinium-health',
  UPDATE_CONFIG: '/plugins/update-testinium-config',
  GET_CONFIG: '/plugins/get-testinium-config',
  GET_DEVICES: '/plugins/get-testinium-devices',
  ALLOCATE: '/plugins/testinium-allocate',
  START_SESSION: '/plugins/testinium-start-session',
  CLOSE_SESSION: '/plugins/testinium-close-session',
  UPLOAD_APP: '/plugins/upload-testinium-app',
  GET_APPS: '/plugins/get-testinium-apps',
  DELETE_APP: (id: string) => `/plugins/delete-testinium-app/${id}`,
  DOWNLOAD_APP: (id: string) => `/plugins/download-testinium-app/${id}`,
  DISCONNECT: '/plugins/disconnect-testinium',
};

// Zephyr Scale endpoints
export const ZEPHYR_SCALE_ENDPOINTS = {
  TEST_CONNECTION: '/plugins/zephyrscale-health',
  GET_PROJECTS: '/plugins/get_zephyrscale_projects',
  GET_FOLDERS: '/plugins/get_zephyrscale_folders',
  GET_TESTCASES: '/plugins/get_zephyrscale_testcases',
  GET_TESTCASE_DETAILS: '/plugins/get_zephyrscale_testcase_details',
  SAVE_CONFIG: '/plugins/save_zephyrscale_config',
  GET_CONFIG: '/plugins/get_zephyrscale_config',
  DISCONNECT: '/plugins/disconnect_zephyrscale',
};

// Jira endpoints
export const JIRA_ENDPOINTS = {
  TEST_CONNECTION: '/plugins/jira-health',
  GET_PROJECTS: '/plugins/get_jira_projects',
  GET_ISSUE_TYPES: '/plugins/get_jira_issue_types',
  SAVE_CONFIG: '/plugins/save_jira_config',
  GET_CONFIG: '/plugins/get_jira_config',
  DISCONNECT: '/plugins/disconnect_jira',
  CREATE_ISSUE: '/plugins/create_jira_issue',
};

// Admin endpoints
export const ADMIN_ENDPOINTS = {
  BASE: '/admin',
  LOGIN: '/admin/login',
  VALIDATE: '/admin/validate',
  DASHBOARD: {
    STATS: '/admin/stats',
  },
  QUEUE_MONITOR: '/admin/queue-monitor',
  STUCK_TESTS: {
    DELETE: (testId: string) => `/admin/stuck-tests/${testId}`,
  },
  COMPLETE_TEST: (testId: string) => `/admin/complete-test/${testId}`,
  ACTIVE_TESTS: {
    GET_ALL: '/admin/active-tests',
    COMPLETE: (testId: string) => `/admin/complete-active-test/${testId}`,
    REMOVE: (testId: string) => `/admin/remove-active-test/${testId}`,
    COMPLETE_ALL: '/admin/complete-all-active-tests',
    REMOVE_ALL: '/admin/remove-all-active-tests',
  },
  NUCLEAR_CLEAR_QUEUE: '/admin/nuclear-clear-queue',
  HEALTH: '/admin/health',
  USERS: {
    GET_ALL: '/admin/system-users',
    GET_BY_ID: (id: string) => `/admin/system-users/${id}`,
    CREATE: '/admin/system-users',
    UPDATE: (id: string) => `/admin/system-users/${id}`,
    DELETE: (id: string) => `/admin/system-users/${id}`,
  },
  COMPANIES: {
    GET_ALL: '/admin/companies',
    GET_BY_ID: (id: string) => `/admin/companies/${id}`,
    CREATE: '/admin/companies',
    UPDATE: (id: string) => `/admin/companies/${id}`,
    DELETE: (id: string) => `/admin/companies/${id}`,
    REMAINING: (id: string) => `/admin/companies/${id}/remaining`,
    USERS: (id: string) => `/admin/companies/${id}/users`,
    TEAMS: (id: string) => `/admin/companies/${id}/teams`,
  },
  SETTINGS: {
    GET_ALL: '/admin/settings',
    UPDATE: '/admin/settings',
    SYSTEM: {
      GET_ALL: '/admin/system-settings',
      GET_BY_ID: (id: string) => `/admin/system-settings/${id}`,
      GET_BY_TYPE: (type: string) => `/admin/system-settings/type/${type}`,
      CREATE: '/admin/system-settings',
      UPDATE: (id: string) => `/admin/system-settings/${id}`,
      DELETE: (id: string) => `/admin/system-settings/${id}`,
      TEST_SMTP: '/admin/system-settings/test-smtp',
      TEST_SLACK: '/admin/system-settings/test-slack',
    },
  },
  NODES: '/nodes',
  CHANGE_PASSWORD: '/admin/change-password',
  REDIS: {
    GET_DATA: '/admin/redis/data',
    DELETE_KEY: '/admin/redis/key',
    PURGE_ALL: '/admin/redis/purge-all',
  },
};

// Report endpoints
export const REPORT_ENDPOINTS = {
  BASE: '/reports',
  GET_ALL: '/reports',
  GET_LIGHT: '/reports/light',
  GET_BY_ID: (id: string) => `/reports/${id}`,
  ANALYZE: (id: string) => `/reports/analyze/${id}`,
  RUN_DETAIL: (id: string) => `/reports/run-detail/${id}`,
  RUNS_BULK: '/reports/runs/bulk',
  SCENARIOS_BY_EXECUTION: (executionId: string) => `/reports/runs/${executionId}/scenarios`,
  DOWNLOAD_PDF: (reportId: string) => `/reports/${reportId}/pdf`,
  SHARE: {
    ENABLE: (reportId: string) => `/reports/${reportId}/share`,
    DISABLE: (reportId: string) => `/reports/${reportId}/share`,
    GET_BY_TOKEN: (token: string) => `/shared-reports/${token}`,
  },
  RUNS: {
    GET_ALL: '/reports/runs',
    GET_BY_ID: (id: string) => `/reports/run/${id}`,
    DELETE: (id: string) => `/reports/runs/${id}`,
  },
};

// User endpoints
export const USER_ENDPOINTS = {
  PROFILE: '/users/profile',
  CHANGE_PASSWORD: '/users/change-password',
  NOTIFICATION_SETTINGS: '/users/notification-settings',
};

// VNC endpoints
export const VNC_ENDPOINTS = {
  TOKEN: '/vnc/token',
  CHECK_STATUS: (testId: string) => `/vnc/check-status/${testId}`,
  NODE_INFO: (testId: string) => `/vnc/node-info/${testId}`,
  CLOSE_SESSION: '/vnc/close-session',
  VIEWER: (token: string) => `/vnc/viewer/${token}`,
};

// Feedback endpoints
export const FEEDBACK_ENDPOINTS = {
  SUBMIT: '/feedback',
  GET_ALL: '/feedback',
};

// Landing Page endpoints
export const LANDING_PAGE_ENDPOINTS = {
  SUBMIT_CONTACT: '/landing-page/contact',
};

// Test Hub endpoints
export const TEST_HUB_ENDPOINTS = {
  ACTIVE_TESTS: '/test-hub/active-tests',
  TEST_DETAILS: (testId: string) => `/test-hub/tests/${testId}`,
  START_TEST: '/test-hub',
  STOP_TEST: (testId: string) => `/test-hub/${testId}/stop`,
  BULK_TESTS: '/test-hub',
};

// Generate Steps endpoints
export const GENERATE_STEPS_ENDPOINTS = {
  GENERATE: '/generate-steps',
};

// Generate Data endpoints
export const GENERATE_DATA_ENDPOINTS = {
  GENERATE: '/generate-data',
  VARIABLES: '/generate-data/variables',
  DATASET_STRUCTURE: '/generate-data/dataset',
};

// Schedule endpoints
export const SCHEDULE_ENDPOINTS = {
  BASE: '/schedules',
  GET_ALL: '/schedules',
  GET_BY_ID: (id: string) => `/schedules/${id}`,
  CREATE: '/schedules',
  UPDATE: (id: string) => `/schedules/${id}`,
  DELETE: (id: string) => `/schedules/${id}`,
  STATUS: (id: string) => `/schedules/${id}/status`,
  RUNS: '/schedules/runs',
};

// Status endpoints
export const STATUS_ENDPOINTS = {
  SYSTEM: '/status/system',
  NODES: '/status/nodes',
  QUEUES: '/status/queues',
  PAUSE_SYSTEM: '/admin/system/pause',
  RESUME_SYSTEM: '/admin/system/resume',
  PAUSE_NODE: (nodeId: string) => `/admin/nodes/${nodeId}/pause`,
  RESUME_NODE: (nodeId: string) => `/admin/nodes/${nodeId}/resume`,
  NODE_PAUSE_STATUS: (nodeId: string) => `/admin/nodes/${nodeId}/pause-status`,
  NODES_PAUSE_STATUS: '/admin/nodes/pause-status',
};

// Step Progress endpoints
export const STEP_PROGRESS_ENDPOINTS = {
  BASE: '/step-progress',
  GET_TEST: (testId: string) => `/step-progress/test/${testId}`,
  GET_RUN: (runId: string) => `/step-progress/run/${runId}`,
  GET_RUN_SUMMARY: (runId: string) => `/step-progress/run/${runId}/summary`,
  DELETE_TEST: (testId: string) => `/step-progress/test/${testId}`,
  DELETE_RUN: (runId: string) => `/step-progress/run/${runId}`,
};

// Test Data endpoints
export const TEST_DATA_ENDPOINTS = {
  BASE: '/test-data',
  DATA_SETS: {
    GET_ALL: '/test-data/data-sets',
    GET_KEYS: '/test-data/data-sets/keys',
    GET_BY_ID: (id: string) => `/test-data/data-sets/${id}`,
    CREATE: '/test-data/data-sets',
    UPDATE: (id: string) => `/test-data/data-sets/${id}`,
    DELETE: (id: string) => `/test-data/data-sets/${id}`,
  },
  DATA_SOURCES: {
    GET_ALL: '/test-data/data-sources',
    GET_BY_ID: (id: string) => `/test-data/data-sources/${id}`,
    CREATE: '/test-data/data-sources',
    UPDATE: (id: string) => `/test-data/data-sources/${id}`,
    DELETE: (id: string) => `/test-data/data-sources/${id}`,
    UPLOAD_CSV: '/test-data/data-sources/csv-upload',
    TEST_DB_CONNECTION: '/test-data/data-sources/db/test-connection',
    GET_DB_TABLES: '/test-data/data-sources/db/tables',
    GET_DB_COLUMNS: '/test-data/data-sources/db/columns',
    GET_DB_PREVIEW_DATA: '/test-data/data-sources/db/preview',
    EXECUTE_DB_FLOW: '/test-data/data-sources/db/execute-flow',
    TEST_API_REQUEST: '/test-data/data-sources/api/test',
  },
  ENVIRONMENTS: {
    GET_ALL: '/test-data/environments',
    GET_BY_ID: (id: string) => `/test-data/environments/${id}`,
    CREATE: '/test-data/environments',
    UPDATE: (id: string) => `/test-data/environments/${id}`,
    DELETE: (id: string) => `/test-data/environments/${id}`,
  },
};

// Excel endpoints
export const EXCEL_ENDPOINTS = {
  INSPECT: '/excel/inspect',
  PARSE: '/excel/parse',
};
