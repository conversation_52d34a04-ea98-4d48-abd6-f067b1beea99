/**
 * Redis Controller
 * Provides API endpoints for Redis queue management
 */

import { Request, Response } from 'express';
import { queueService, QUEUE_NAMES } from '../../services/redis/index.js';
import { centralizedStuckMonitoringService } from '../../services/monitoring/centralizedStuckMonitoringService.js';
import { logger } from '../../utils/logger.js';

/**
 * Get queue status
 * @param req Express request
 * @param res Express response
 */
export const getQueueStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const queueName = req.params.queueName || QUEUE_NAMES.TEST_QUEUE;

    // Get queue status
    const queueStatus = await queueService.getQueueStatus(queueName);

    // Extract the status for the specific queue
    const status = queueStatus[queueName];

    res.status(200).json({
      success: true,
      data: {
        queueName,
        status
      }
    });
  } catch (error: any) {
    logger.error(`Redis Controller: Error getting queue status: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting queue status: ${error.message}`
    });
  }
};

/**
 * Get all queues status
 * @param req Express request
 * @param res Express response
 */
export const getAllQueuesStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get status for all main queues using the no-parameter version
    const mainQueues = await queueService.getQueueStatus();

    // In the Pull Model, we don't use node-specific queues anymore
    const nodeQueues: { [key: string]: any } = {};
    // Just include a note about the Pull Model
    nodeQueues['pull-model-note'] = 'Node-specific queues have been replaced with a central test queue in the Pull Model';

    // This is a simplified approach - in a real implementation, you would need to
    // track all created node queues or query Redis for all queues with the prefix

    res.status(200).json({
      success: true,
      data: {
        mainQueues,
        nodeQueues
      }
    });
  } catch (error: any) {
    logger.error(`Redis Controller: Error getting all queues status: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting all queues status: ${error.message}`
    });
  }
};

/**
 * Purge a queue
 * @param req Express request
 * @param res Express response
 */
export const purgeQueue = async (req: Request, res: Response): Promise<void> => {
  try {
    const queueName = req.params.queueName;

    if (!queueName) {
      res.status(400).json({
        success: false,
        message: 'Queue name is required'
      });
      return;
    }

    // Check if this is a protected queue
    const protectedQueues = [
      QUEUE_NAMES.TEST_QUEUE,
      QUEUE_NAMES.RESULT_QUEUE
      // NODE_REGISTRATION_QUEUE removed - using WebSocket-only registration
    ];

    if (protectedQueues.includes(queueName) && req.body.force !== true) {
      res.status(403).json({
        success: false,
        message: `Cannot purge protected queue ${queueName}. Use force=true to override.`
      });
      return;
    }

    // Get the queue
    const queue = queueService.getQueue(queueName);

    if (!queue) {
      res.status(404).json({
        success: false,
        message: `Queue ${queueName} not found`
      });
      return;
    }

    // Purge the queue
    await queue.obliterate({ force: true });

    logger.info(`Redis Controller: Purged queue ${queueName}`);

    res.status(200).json({
      success: true,
      message: `Queue ${queueName} purged successfully`
    });
  } catch (error: any) {
    logger.error(`Redis Controller: Error purging queue ${req.params.queueName}: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error purging queue: ${error.message}`
    });
  }
};

/**
 * Add a job to a queue
 * @param req Express request
 * @param res Express response
 */
export const addJob = async (req: Request, res: Response): Promise<void> => {
  try {
    const { queueName, jobType, data, options } = req.body;

    if (!queueName || !jobType || !data) {
      res.status(400).json({
        success: false,
        message: 'Queue name, job type, and data are required'
      });
      return;
    }

    // Add job to the queue
    const job = await queueService.addJob(queueName, jobType, data, options);

    if (job) {
      logger.info(`Redis Controller: Added job ${job.id} to queue ${queueName}`);

      res.status(201).json({
        success: true,
        data: {
          jobId: job.id,
          queueName
        },
        message: `Job added to queue ${queueName}`
      });
    } else {
      res.status(500).json({
        success: false,
        message: `Failed to add job to queue ${queueName}`
      });
    }
  } catch (error: any) {
    logger.error(`Redis Controller: Error adding job to queue: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error adding job to queue: ${error.message}`
    });
  }
};

/**
 * Force complete a stuck job
 * @param req Express request
 * @param res Express response
 */
export const forceCompleteStuckJob = async (req: Request, res: Response): Promise<void> => {
  try {
    const { jobId, queueName } = req.body;

    if (!jobId || !queueName) {
      res.status(400).json({
        success: false,
        message: 'Job ID and queue name are required'
      });
      return;
    }

    // For backward compatibility, we'll try to extract testId from jobId or require it
    const testId = req.body.testId || jobId; // Assume testId is provided or use jobId as fallback

    logger.info(`Redis Controller: Force recovering stuck test ${testId} via centralized monitoring service`);

    const success = await centralizedStuckMonitoringService.forceRecoverTest(testId);

    if (success) {
      res.status(200).json({
        success: true,
        message: `Successfully force recovered stuck test ${testId} via centralized monitoring`
      });
    } else {
      res.status(500).json({
        success: false,
        message: `Failed to force recover stuck test ${testId} via centralized monitoring`
      });
    }
  } catch (error: any) {
    logger.error(`Redis Controller: Error force recovering stuck test: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error force recovering stuck test: ${error.message}`
    });
  }
};

/**
 * Get centralized monitoring statistics
 * @param req Express request
 * @param res Express response
 */
export const getMonitoringStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = centralizedStuckMonitoringService.getStatistics();

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error: any) {
    logger.error(`Redis Controller: Error getting monitoring stats: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting monitoring stats: ${error.message}`
    });
  }
};

/**
 * Get queue names
 * @param req Express request
 * @param res Express response
 */
export const getQueueNames = async (req: Request, res: Response): Promise<void> => {
  try {
    res.status(200).json({
      success: true,
      data: {
        queueNames: {
          TEST_QUEUE: QUEUE_NAMES.TEST_QUEUE,
          NODE_SPECIFIC_PREFIX: QUEUE_NAMES.NODE_SPECIFIC_PREFIX, // Kept for backward compatibility
          RESULT_QUEUE: QUEUE_NAMES.RESULT_QUEUE,
          // NODE_REGISTRATION_QUEUE removed - using WebSocket-only registration
          NODE_STATUS_QUEUE: QUEUE_NAMES.NODE_STATUS_QUEUE
        },
        pullModelInfo: 'The system now uses a Pull Model architecture where nodes pull tests from the central test queue'
      }
    });
  } catch (error: any) {
    logger.error(`Redis Controller: Error getting queue names: ${error.message}`);
    res.status(500).json({
      success: false,
      message: `Error getting queue names: ${error.message}`
    });
  }
};
