/**
 * Shared Device Manager Utilities
 * Common utilities and patterns used across different device managers
 */

import { AndroidAgent } from '../device-manager.interface.js';
import { TestRequest } from '../../../../models/types.js';
import { isAndroidEnvironmentSettings } from '../../../../utils/type-guards.js';
import { ILoggerService } from '../../../../utils/logger-service.js';

/**
 * Device connection retry configuration
 */
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBackoff: boolean;
}

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  exponentialBackoff: true
};

/**
 * Device busy specific retry configuration with longer backoff
 */
export const DEVICE_BUSY_RETRY_CONFIG: RetryConfig = {
  maxRetries: 5, // 5 additional retries for device busy
  baseDelay: 5000, // 5 seconds base delay
  maxDelay: 60000, // 1 minute max delay
  exponentialBackoff: true
};

/**
 * Device availability check result
 */
export interface DeviceAvailabilityResult {
  available: boolean;
  reason?: string;
  retryable: boolean;
}

/**
 * Test queue management interface for retrying tests
 */
export interface TestQueueManager {
  /**
   * Requeue test for later execution
   * @param testId Test identifier
   * @param reason Reason for requeueing
   * @param delay Delay before next attempt (optional)
   */
  requeueTest(testId: string, reason: string, delay?: number): Promise<void>;
}

/**
 * Enhanced retry result with queue management option
 */
export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  requeued?: boolean;
  attemptsUsed: number;
}

/**
 * Common error types for device operations
 */
export enum DeviceErrorType {
  CREDENTIALS_MISSING = 'CredentialsMissing',
  DEVICE_UNAVAILABLE = 'DeviceUnavailable',
  DEVICE_BUSY = 'DeviceBusy',
  ALLOCATION_FAILED = 'AllocationFailed',
  SESSION_FAILED = 'SessionFailed',
  NETWORK_ERROR = 'NetworkError',
  TIMEOUT = 'Timeout',
  UNKNOWN = 'Unknown'
}

/**
 * Device manager utilities class
 */
export class DeviceManagerUtils {
  private logger: ILoggerService;

  constructor(logger: ILoggerService) {
    this.logger = logger;
  }

  /**
   * Validate test request for device connection
   * @param testRequest Test request to validate
   * @returns Validation result
   */
  validateTestRequest(testRequest: TestRequest): { valid: boolean; error?: string } {
    if (!testRequest.id) {
      return { valid: false, error: 'Test request ID is missing' };
    }

    if (!testRequest.environmentSettings || !isAndroidEnvironmentSettings(testRequest.environmentSettings)) {
      return { valid: false, error: 'Android environment settings not found in test request' };
    }

    return { valid: true };
  }

  /**
   * Extract device information from test request
   * @param testRequest Test request
   * @param provider Device provider ('sauceLabs' or 'testinium')
   * @returns Device information
   */
  extractDeviceInfo(testRequest: TestRequest, provider: 'sauceLabs' | 'testinium'): {
    deviceId?: string;
    deviceSerial?: string;
    osVersion?: string;
    appId?: string;
    devices: any[];
  } {
    const androidSettings = testRequest.environmentSettings as any;
    const providerConfig = androidSettings[provider];

    if (!providerConfig || !providerConfig.selectedDevices || providerConfig.selectedDevices.length === 0) {
      throw new Error(`No ${provider} devices selected in test request`);
    }

    const selectedDevice = providerConfig.selectedDevices[0];
    return {
      deviceId: selectedDevice.id,
      deviceSerial: selectedDevice.serial, // Include serial for direct usage
      osVersion: selectedDevice.osVersion,
      appId: provider === 'testinium' ? providerConfig.selectedApp?.fileKey || providerConfig.selectedApp?.id : providerConfig.selectedApp?.id,
      devices: providerConfig.selectedDevices
    };
  }

  /**
   * Classify error type for retry logic
   * @param error Error to classify
   * @returns Error type and retry recommendation
   */
  classifyError(error: any): { type: DeviceErrorType; retryable: boolean } {
    const errorMessage = error.message?.toLowerCase() || '';

    if (errorMessage.includes('credentials') || errorMessage.includes('authentication')) {
      return { type: DeviceErrorType.CREDENTIALS_MISSING, retryable: false };
    }

    if (errorMessage.includes('device busy') || 
        errorMessage.includes('device in use') ||
        errorMessage.includes('already allocated') ||
        errorMessage.includes('device is currently in use') ||
        errorMessage.includes('device not available for allocation')) {
      return { type: DeviceErrorType.DEVICE_BUSY, retryable: true };
    }

    if (errorMessage.includes('device unavailable') || 
        errorMessage.includes('device not found') ||
        errorMessage.includes('device not available') ||
        errorMessage.includes('device serial not found')) {
      return { type: DeviceErrorType.DEVICE_UNAVAILABLE, retryable: true };
    }

    if (errorMessage.includes('allocation failed') || 
        errorMessage.includes('failed to allocate') ||
        errorMessage.includes('unable to allocate device') ||
        errorMessage.includes('allocation request failed')) {
      return { type: DeviceErrorType.ALLOCATION_FAILED, retryable: true };
    }

    if (errorMessage.includes('session') || errorMessage.includes('failed to start session')) {
      return { type: DeviceErrorType.SESSION_FAILED, retryable: true };
    }

    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return { type: DeviceErrorType.TIMEOUT, retryable: true };
    }

    if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return { type: DeviceErrorType.NETWORK_ERROR, retryable: true };
    }

    return { type: DeviceErrorType.UNKNOWN, retryable: false };
  }

  /**
   * Execute operation with retry logic
   * @param operation Operation to execute
   * @param config Retry configuration
   * @param context Context for logging
   * @returns Operation result
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig = DEFAULT_RETRY_CONFIG,
    context: string = 'operation'
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          this.logger.info(`DeviceManagerUtils: Retrying ${context} (attempt ${attempt + 1}/${config.maxRetries + 1})`);
        }

        return await operation();
      } catch (error: any) {
        lastError = error;
        const errorClassification = this.classifyError(error);

        if (!errorClassification.retryable || attempt === config.maxRetries) {
          this.logger.error(`DeviceManagerUtils: ${context} failed after ${attempt + 1} attempts: ${error.message}`);
          throw error;
        }

        const delay = this.calculateDelay(attempt, config);
        this.logger.warn(`DeviceManagerUtils: ${context} failed (attempt ${attempt + 1}), retrying in ${delay}ms: ${error.message}`);
        
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Execute device connection with enhanced retry logic for device busy scenarios
   * @param operation Operation to execute
   * @param testId Test identifier for requeueing
   * @param queueManager Queue manager for requeueing tests
   * @param context Context for logging
   * @returns Enhanced retry result
   */
  async executeDeviceConnectionWithRetry<T>(
    operation: () => Promise<T>,
    testId?: string,
    queueManager?: TestQueueManager,
    context: string = 'device-connection'
  ): Promise<RetryResult<T>> {
    let lastError: any;
    let usedRetryConfig = DEFAULT_RETRY_CONFIG;
    let deviceBusyDetected = false;

    for (let attempt = 0; attempt <= usedRetryConfig.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          this.logger.info(`DeviceManagerUtils: Retrying ${context} (attempt ${attempt + 1}/${usedRetryConfig.maxRetries + 1})`);
        }

        const result = await operation();
        
        return {
          success: true,
          result: result,
          attemptsUsed: attempt + 1
        };
      } catch (error: any) {
        lastError = error;
        const errorClassification = this.classifyError(error);

        // Check if this is a device busy error
        if (errorClassification.type === DeviceErrorType.DEVICE_BUSY || 
            errorClassification.type === DeviceErrorType.DEVICE_UNAVAILABLE) {
          
          if (!deviceBusyDetected) {
            // Switch to device busy retry config for longer backoff
            deviceBusyDetected = true;
            usedRetryConfig = DEVICE_BUSY_RETRY_CONFIG;
            this.logger.info(`DeviceManagerUtils: Device busy detected, switching to enhanced retry strategy (${usedRetryConfig.maxRetries} retries with longer backoff)`);
          }
        }

        // Check if we should continue retrying
        if (!errorClassification.retryable || attempt === usedRetryConfig.maxRetries) {
          // If we have a queue manager and test ID, and this is a device busy error, requeue the test
          if (queueManager && testId && deviceBusyDetected && errorClassification.retryable) {
            try {
              await queueManager.requeueTest(testId, `Device busy after ${attempt + 1} attempts`, 30000); // 30 second delay
              this.logger.info(`DeviceManagerUtils: Test ${testId} requeued due to device busy after ${attempt + 1} attempts`);
              
              return {
                success: false,
                error: lastError,
                requeued: true,
                attemptsUsed: attempt + 1
              };
            } catch (requeueError: any) {
              this.logger.error(`DeviceManagerUtils: Failed to requeue test ${testId}: ${requeueError.message}`);
            }
          }

          this.logger.error(`DeviceManagerUtils: ${context} failed after ${attempt + 1} attempts: ${error.message}`);
          
          return {
            success: false,
            error: lastError,
            attemptsUsed: attempt + 1
          };
        }

        const delay = this.calculateDelayWithJitter(attempt, usedRetryConfig);
        this.logger.warn(`DeviceManagerUtils: ${context} failed (attempt ${attempt + 1}), retrying in ${delay}ms: ${error.message}`);
        
        await this.sleep(delay);
      }
    }

    return {
      success: false,
      error: lastError,
      attemptsUsed: usedRetryConfig.maxRetries + 1
    };
  }

  /**
   * Calculate delay for retry attempt
   * @param attempt Current attempt number (0-based)
   * @param config Retry configuration
   * @returns Delay in milliseconds
   */
  private calculateDelay(attempt: number, config: RetryConfig): number {
    if (!config.exponentialBackoff) {
      return config.baseDelay;
    }

    const delay = config.baseDelay * Math.pow(2, attempt);
    return Math.min(delay, config.maxDelay);
  }

  /**
   * Calculate delay for retry attempt with jitter to avoid thundering herd
   * @param attempt Current attempt number (0-based)
   * @param config Retry configuration
   * @returns Delay in milliseconds with jitter
   */
  private calculateDelayWithJitter(attempt: number, config: RetryConfig): number {
    const baseDelay = this.calculateDelay(attempt, config);
    
    // Add random jitter (±25% of the base delay)
    const jitterRange = baseDelay * 0.25;
    const jitter = (Math.random() * 2 - 1) * jitterRange; // -25% to +25%
    
    const delayWithJitter = Math.max(1000, baseDelay + jitter); // Minimum 1 second
    return Math.floor(delayWithJitter);
  }

  /**
   * Sleep for specified duration
   * @param ms Duration in milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Safely disconnect from device with error handling
   * @param device Device to disconnect from
   * @param provider Provider name for logging
   */
  async safeDisconnect(device: AndroidAgent, provider: string): Promise<void> {
    try {
      if (device && typeof device.quit === 'function') {
        await device.quit();
        this.logger.info(`DeviceManagerUtils: Successfully disconnected from ${provider} device`);
      }
    } catch (error: any) {
      this.logger.warn(`DeviceManagerUtils: Error during ${provider} device disconnection: ${error.message}`);
      // Don't re-throw as this is cleanup code
    }
  }

  /**
   * Validate device configuration
   * @param config Device configuration
   * @param provider Provider name
   * @returns Validation result
   */
  validateDeviceConfig(config: any, provider: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config) {
      errors.push(`${provider} configuration is missing`);
      return { valid: false, errors };
    }

    if (provider === 'sauceLabs') {
      if (!config.user && !config.username) {
        errors.push('SauceLabs username is missing');
      }
      if (!config.key && !config.accessKey) {
        errors.push('SauceLabs access key is missing');
      }
    } else if (provider === 'testinium') {
      if (!config.apiUrl) {
        errors.push('Testinium API URL is missing');
      }
      if (!config.clientId) {
        errors.push('Testinium client ID is missing');
      }
      if (!config.clientSecret) {
        errors.push('Testinium client secret is missing');
      }
      if (!config.issuerUri) {
        errors.push('Testinium issuer URI is missing');
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Create standardized error with context
   * @param type Error type
   * @param message Error message
   * @param context Additional context
   * @returns Standardized error
   */
  createError(type: DeviceErrorType, message: string, context?: any): Error {
    const error = new Error(`${type}: ${message}`);
    (error as any).type = type;
    (error as any).context = context;
    return error;
  }

  /**
   * Common device disconnection logic
   * @param device Device to disconnect from
   * @param deviceKey Device key for unregistering
   * @param unregisterFn Function to unregister the agent
   * @param provider Provider name for logging
   */
  async performCommonDisconnection(
    device: AndroidAgent,
    deviceKey: string | null,
    unregisterFn: (key: string) => void,
    provider: string
  ): Promise<void> {
    try {
      if (deviceKey) {
        unregisterFn(deviceKey);
      }

      // Quit the device agent
      await this.safeDisconnect(device, provider);

      this.logger.info(`${provider}DeviceManager: Disconnected from device`);
    } catch (error: any) {
      this.logger.error(`${provider}DeviceManager: Error disconnecting from device: ${error.message}`);
      throw error;
    }
  }

  /**
   * Common test request validation and device info extraction
   * @param testRequest Test request to process
   * @param provider Provider name ('sauceLabs' or 'testinium')
   * @returns Extracted device information
   */
  processTestRequest(testRequest: TestRequest, provider: 'sauceLabs' | 'testinium'): {
    deviceId: string;
    osVersion?: string;
    appId?: string;
  } {
    const validation = this.validateTestRequest(testRequest);
    if (!validation.valid) {
      throw this.createError(DeviceErrorType.UNKNOWN, validation.error || 'Invalid test request');
    }

    const deviceInfo = this.extractDeviceInfo(testRequest, provider);
    if (!deviceInfo.deviceId) {
      throw this.createError(DeviceErrorType.UNKNOWN, 'Device ID not found in test request environment settings');
    }

    return {
      deviceId: deviceInfo.deviceId,
      osVersion: deviceInfo.osVersion,
      appId: deviceInfo.appId
    };
  }

  /**
   * Common cleanup logic for all device managers
   * @param agents Map of agents to cleanup
   * @param disconnectFn Function to disconnect from device
   * @param provider Provider name for logging
   */
  async performCommonCleanup(
    agents: Map<string, AndroidAgent>,
    disconnectFn: (device: AndroidAgent) => Promise<void>,
    provider: string
  ): Promise<void> {
    try {
      // Disconnect from all devices
      const devices = Array.from(agents.values());
      for (const device of devices) {
        await disconnectFn(device);
      }

      // Clear agents map
      agents.clear();

      this.logger.info(`${provider}DeviceManager: Cleanup completed`);
    } catch (error: any) {
      this.logger.error(`${provider}DeviceManager: Error during cleanup: ${error.message}`);
      throw error;
    }
  }
}
