/**
 * Step Progress Service
 * Manages test step progress information in Redis
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { redisConnection } from '../redis/redisConnection.js';

export interface StepProgressData {
  nodeId: string;
  stepIndex: number;
  totalSteps: number;
  stepId: string;
  stepName: string;
  stepType: string;
  status: 'started' | 'completed' | 'failed';
  timestamp: string;
  platform: 'web' | 'android';
  runId?: string;
  executionId?: string;
  scenarioId?: string;
  result?: any;
}

export interface NestedStepProgressData {
  nodeId: string;
  stepId: string;
  stepName: string;
  stepType: string;
  status: 'started' | 'completed' | 'failed';
  timestamp: string;
  platform: 'web' | 'android';
  runId?: string;
  executionId?: string;
  scenarioId?: string;
  result?: any;
  parentStepId?: string;
  nestingLevel?: number;
}

export interface TestStepProgress {
  testId: string;
  currentStep: number;
  totalSteps: number;
  currentStepName: string;
  currentStepType: string;
  currentStepStatus: string;
  lastUpdated: string;
  platform: string;
  runId?: string;
  executionId?: string;
  scenarioId?: string;
  steps: StepProgressData[];
}

class StepProgressService extends EventEmitter {
  private readonly STEP_PROGRESS_PREFIX = 'step_progress:';
  private readonly RUN_INDEX_PREFIX = 'run_progress_index:';
  private readonly STEP_PROGRESS_TTL = 24 * 60 * 60; // 24 hours in seconds
  private cleanupInterval: NodeJS.Timeout | null = null;

  // Lua script for atomic step progress update
  private readonly UPDATE_STEP_SCRIPT = `
    local key = KEYS[1]
    local run_index_key = KEYS[2]
    local step_data = cjson.decode(ARGV[1])
    local ttl = tonumber(ARGV[2])

    -- Get existing data
    local existing_data = redis.call('HGETALL', key)
    local data_map = {}
    for i = 1, #existing_data, 2 do
      data_map[existing_data[i]] = existing_data[i + 1]
    end

    -- Parse existing steps or initialize empty array
    local steps = {}
    if data_map.steps then
      steps = cjson.decode(data_map.steps)
    end

    -- Update or add the current step with status validation
    local step_updated = false
    for i, step in ipairs(steps) do
      if step.stepIndex == step_data.stepIndex then
        -- Validate status transitions to prevent invalid state changes
        local existing_status = step.status
        local new_status = step_data.status

        -- Allow status transitions: started -> completed/failed, but not backwards
        local valid_transition = true
        if existing_status == 'completed' or existing_status == 'failed' then
          -- Don't allow changing from completed/failed back to started
          if new_status == 'started' then
            valid_transition = false
          end
        end

        if valid_transition then
          -- Update existing step
          for k, v in pairs(step_data) do
            step[k] = v
          end
        else
          -- Keep existing status if transition is invalid
          step_data.status = existing_status
        end
        step_updated = true
        break
      end
    end

    -- Add new step if not updated
    if not step_updated then
      table.insert(steps, step_data)
    end

    -- Sort steps by stepIndex
    table.sort(steps, function(a, b) return a.stepIndex < b.stepIndex end)

    -- Calculate currentStep based on completed steps + 1 (for forward-only progress)
    local completed_count = 0
    local failed_count = 0
    local current_step_index = 1
    local current_step_name = step_data.stepName
    local current_step_type = step_data.stepType
    local current_step_status = step_data.status

    -- Count completed/failed steps and find the highest running/started step
    local highest_active_step = 0
    local highest_active_step_info = nil

    for i, step in ipairs(steps) do
      if step.status == 'completed' then
        completed_count = completed_count + 1
      elseif step.status == 'failed' then
        failed_count = failed_count + 1
      elseif step.status == 'started' and step.stepIndex > highest_active_step then
        highest_active_step = step.stepIndex
        highest_active_step_info = step
      end
    end

    -- Current step logic:
    -- 1. If there's an active (started) step, use that
    -- 2. Otherwise, use completed + failed + 1 (next step to execute)
    if highest_active_step > 0 and highest_active_step_info then
      current_step_index = highest_active_step
      current_step_name = highest_active_step_info.stepName
      current_step_type = highest_active_step_info.stepType
      current_step_status = highest_active_step_info.status
    else
      current_step_index = completed_count + failed_count + 1
    end

    -- Ensure currentStep never goes backwards (compare with existing currentStep)
    local existing_current_step = tonumber(data_map.currentStep) or 1
    if current_step_index < existing_current_step then
      current_step_index = existing_current_step
      -- Keep existing step info if we're not moving forward
      current_step_name = data_map.currentStepName or current_step_name
      current_step_type = data_map.currentStepType or current_step_type
      current_step_status = data_map.currentStepStatus or current_step_status
    end

    -- Prepare hash data
    local hash_data = {
      'testId', step_data.testId or data_map.testId or '',
      'currentStep', tostring(current_step_index),
      'totalSteps', tostring(step_data.totalSteps),
      'currentStepName', current_step_name,
      'currentStepType', current_step_type,
      'currentStepStatus', current_step_status,
      'lastUpdated', step_data.timestamp,
      'platform', step_data.platform,
      'runId', step_data.runId or '',
      'executionId', step_data.executionId or '',
      'scenarioId', step_data.scenarioId or '',
      'steps', cjson.encode(steps)
    }

    -- Store in Redis
    redis.call('HSET', key, unpack(hash_data))
    redis.call('EXPIRE', key, ttl)

    -- Add to run index if runId exists
    if step_data.runId and step_data.runId ~= '' then
      redis.call('SADD', run_index_key, key)
      redis.call('EXPIRE', run_index_key, ttl)
    end

    return cjson.encode({
      testId = step_data.testId or data_map.testId or '',
      currentStep = current_step_index,
      totalSteps = step_data.totalSteps,
      currentStepName = current_step_name,
      currentStepType = current_step_type,
      currentStepStatus = current_step_status,
      lastUpdated = step_data.timestamp,
      platform = step_data.platform,
      runId = step_data.runId,
      executionId = step_data.executionId,
      scenarioId = step_data.scenarioId,
      steps = steps
    })
  `;

  /**
   * Update step progress for a test using atomic Lua script
   */
  async updateStepProgress(testId: string, stepData: StepProgressData): Promise<void> {
    try {
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        logger.warn('StepProgressService: Redis client not available');
        return;
      }

      const key = `${this.STEP_PROGRESS_PREFIX}${testId}`;
      const runIndexKey = `${this.RUN_INDEX_PREFIX}${stepData.runId || 'unknown'}`;

      // Execute atomic update using Lua script
      const result = await redisClient.eval(
        this.UPDATE_STEP_SCRIPT,
        2, // number of keys
        key,
        runIndexKey,
        JSON.stringify({
          testId,
          stepIndex: stepData.stepIndex,
          totalSteps: stepData.totalSteps,
          stepId: stepData.stepId,
          stepName: stepData.stepName,
          stepType: stepData.stepType,
          status: stepData.status,
          timestamp: stepData.timestamp,
          platform: stepData.platform,
          runId: stepData.runId,
          executionId: stepData.executionId,
          scenarioId: stepData.scenarioId,
          nodeId: stepData.nodeId,
          result: stepData.result
        }),
        this.STEP_PROGRESS_TTL.toString()
      ) as string;

      // Parse the result from Lua script
      const progressSummary: TestStepProgress = JSON.parse(result);

      // Emit event for real-time updates
      this.emit('step:progress:updated', progressSummary);

      const stepInfo = stepData.stepIndex !== undefined 
        ? `${stepData.stepIndex}/${stepData.totalSteps || 'N/A'}`
        : 'nested';
      logger.debug(`StepProgressService: Updated step progress for test ${testId} - Step ${stepInfo}: ${stepData.stepName || 'unknown'} (${stepData.status})`);
    } catch (error) {
      logger.error(`StepProgressService: Error updating step progress for test ${testId}: ${error}`);
      throw error;
    }
  }

  /**
   * Get step progress for a specific test
   */
  async getTestStepProgress(testId: string): Promise<TestStepProgress | null> {
    try {
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        logger.warn('StepProgressService: Redis client not available');
        return null;
      }

      const key = `${this.STEP_PROGRESS_PREFIX}${testId}`;
      const data = await redisClient.hgetall(key);

      if (!data || Object.keys(data).length === 0) {
        return null;
      }

      // Parse steps
      let steps: StepProgressData[] = [];
      if (data.steps) {
        try {
          steps = JSON.parse(data.steps);
        } catch (error) {
          logger.warn(`StepProgressService: Error parsing steps for test ${testId}: ${error}`);
        }
      }

      return {
        testId: data.testId,
        currentStep: parseInt(data.currentStep) || 0,
        totalSteps: parseInt(data.totalSteps) || 0,
        currentStepName: data.currentStepName || '',
        currentStepType: data.currentStepType || '',
        currentStepStatus: data.currentStepStatus || '',
        lastUpdated: data.lastUpdated || '',
        platform: data.platform || 'web',
        runId: data.runId || undefined,
        executionId: data.executionId || undefined,
        scenarioId: data.scenarioId || undefined,
        steps
      };
    } catch (error) {
      logger.error(`StepProgressService: Error getting step progress for test ${testId}: ${error}`);
      return null;
    }
  }

  /**
   * Get step progress for multiple tests by run ID using SET-based indexing
   */
  async getRunStepProgress(runId: string): Promise<TestStepProgress[]> {
    try {
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        logger.warn('StepProgressService: Redis client not available');
        return [];
      }

      // Get test keys from run index
      const runIndexKey = `${this.RUN_INDEX_PREFIX}${runId}`;
      const testKeys = await redisClient.smembers(runIndexKey);

      if (testKeys.length === 0) {
        return [];
      }

      const results: TestStepProgress[] = [];

      // Use pipeline for efficient batch retrieval
      const pipeline = redisClient.pipeline();
      testKeys.forEach(key => {
        pipeline.hgetall(key);
      });

      const pipelineResults = await pipeline.exec();

      if (!pipelineResults) {
        return [];
      }

      // Process results
      for (let i = 0; i < pipelineResults.length; i++) {
        const [error, data] = pipelineResults[i];

        if (error || !data || typeof data !== 'object') {
          logger.warn(`StepProgressService: Error or invalid data for key ${testKeys[i]}: ${error}`);
          continue;
        }

        const hashData = data as Record<string, string>;

        // Parse steps
        let steps: StepProgressData[] = [];
        if (hashData.steps) {
          try {
            steps = JSON.parse(hashData.steps);
          } catch (error) {
            logger.warn(`StepProgressService: Error parsing steps for key ${testKeys[i]}: ${error}`);
          }
        }

        results.push({
          testId: hashData.testId,
          currentStep: parseInt(hashData.currentStep) || 0,
          totalSteps: parseInt(hashData.totalSteps) || 0,
          currentStepName: hashData.currentStepName || '',
          currentStepType: hashData.currentStepType || '',
          currentStepStatus: hashData.currentStepStatus || '',
          lastUpdated: hashData.lastUpdated || '',
          platform: hashData.platform || 'web',
          runId: hashData.runId || undefined,
          executionId: hashData.executionId || undefined,
          scenarioId: hashData.scenarioId || undefined,
          steps
        });
      }

      return results;
    } catch (error) {
      logger.error(`StepProgressService: Error getting run step progress for run ${runId}: ${error}`);
      return [];
    }
  }

  /**
   * Clean up step progress for a completed test
   */
  async cleanupTestStepProgress(testId: string, runId?: string): Promise<void> {
    try {
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        logger.warn('StepProgressService: Redis client not available');
        return;
      }

      const key = `${this.STEP_PROGRESS_PREFIX}${testId}`;

      // If runId is provided, also remove from run index
      if (runId) {
        const runIndexKey = `${this.RUN_INDEX_PREFIX}${runId}`;
        await redisClient.srem(runIndexKey, key);
      }

      await redisClient.del(key);

      logger.debug(`StepProgressService: Cleaned up step progress for test ${testId}`);
    } catch (error) {
      logger.error(`StepProgressService: Error cleaning up step progress for test ${testId}: ${error}`);
    }
  }

  /**
   * Clean up step progress for all tests in a run using SET-based indexing
   */
  async cleanupRunStepProgress(runId: string): Promise<void> {
    try {
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        logger.warn('StepProgressService: Redis client not available');
        return;
      }

      const runIndexKey = `${this.RUN_INDEX_PREFIX}${runId}`;

      // Get all test keys for this run
      const testKeys = await redisClient.smembers(runIndexKey);

      if (testKeys.length === 0) {
        return;
      }

      // Use pipeline for efficient batch deletion
      const pipeline = redisClient.pipeline();
      testKeys.forEach(key => {
        pipeline.del(key);
      });

      // Also delete the run index
      pipeline.del(runIndexKey);

      await pipeline.exec();

      logger.debug(`StepProgressService: Cleaned up step progress for run ${runId} (${testKeys.length} tests)`);
    } catch (error) {
      logger.error(`StepProgressService: Error cleaning up step progress for run ${runId}: ${error}`);
    }
  }

  /**
   * Start periodic cleanup of expired step progress data
   */
  startPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      return;
    }

    // Run cleanup every hour
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredStepProgress().catch(error => {
        logger.error(`StepProgressService: Error in periodic cleanup: ${error}`);
      });
    }, 60 * 60 * 1000); // 1 hour

    logger.info('StepProgressService: Started periodic cleanup of expired step progress data');
  }

  /**
   * Stop periodic cleanup
   */
  stopPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      logger.info('StepProgressService: Stopped periodic cleanup');
    }
  }

  /**
   * Clean up expired step progress data
   * Note: This method still uses KEYS for cleanup as it's a background operation
   * and we need to clean up orphaned data that might not be in indexes
   */
  private async cleanupExpiredStepProgress(): Promise<void> {
    try {
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        return;
      }

      // Clean up step progress keys
      const stepPattern = `${this.STEP_PROGRESS_PREFIX}*`;
      const stepKeys = await redisClient.keys(stepPattern);

      // Clean up run index keys
      const runIndexPattern = `${this.RUN_INDEX_PREFIX}*`;
      const runIndexKeys = await redisClient.keys(runIndexPattern);

      let cleanedCount = 0;
      const now = Date.now();
      const expiredThreshold = 2 * 24 * 60 * 60 * 1000; // 2 days

      // Clean expired step progress data
      for (const key of stepKeys) {
        const data = await redisClient.hgetall(key);
        if (data && data.lastUpdated) {
          const lastUpdated = new Date(data.lastUpdated).getTime();
          if (now - lastUpdated > expiredThreshold) {
            await redisClient.del(key);
            cleanedCount++;

            // Also remove from run index if runId exists
            if (data.runId) {
              const runIndexKey = `${this.RUN_INDEX_PREFIX}${data.runId}`;
              await redisClient.srem(runIndexKey, key);
            }
          }
        }
      }

      // Clean up empty run indexes
      for (const runIndexKey of runIndexKeys) {
        const members = await redisClient.scard(runIndexKey);
        if (members === 0) {
          await redisClient.del(runIndexKey);
        }
      }

      if (cleanedCount > 0) {
        logger.info(`StepProgressService: Cleaned up ${cleanedCount} expired step progress entries`);
      }
    } catch (error) {
      logger.error(`StepProgressService: Error cleaning up expired step progress: ${error}`);
    }
  }

  /**
   * Update nested step progress for a test
   * Nested steps are stored separately to avoid interfering with main step progress tracking
   */
  async updateNestedStepProgress(testId: string, nestedStepData: NestedStepProgressData): Promise<void> {
    try {
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        logger.warn('StepProgressService: Redis client not available');
        return;
      }

      const nestedKey = `${this.STEP_PROGRESS_PREFIX}${testId}:nested`;
      const runIndexKey = `${this.RUN_INDEX_PREFIX}${nestedStepData.runId || 'unknown'}`;

      // Get existing nested steps or initialize empty array
      let nestedSteps: NestedStepProgressData[] = [];
      const existingNestedData = await redisClient.get(nestedKey);
      if (existingNestedData) {
        try {
          nestedSteps = JSON.parse(existingNestedData);
        } catch (error) {
          logger.warn(`StepProgressService: Error parsing existing nested steps for test ${testId}: ${error}`);
        }
      }

      // Update or add the nested step
      let stepUpdated = false;
      for (let i = 0; i < nestedSteps.length; i++) {
        if (nestedSteps[i].stepId === nestedStepData.stepId) {
          // Update existing nested step
          nestedSteps[i] = { ...nestedSteps[i], ...nestedStepData };
          stepUpdated = true;
          break;
        }
      }

      // Add new nested step if not updated
      if (!stepUpdated) {
        nestedSteps.push(nestedStepData);
      }

      // Sort nested steps by timestamp
      nestedSteps.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

      // Store nested steps in Redis
      await redisClient.setex(nestedKey, this.STEP_PROGRESS_TTL, JSON.stringify(nestedSteps));

      // Add to run index if runId exists
      if (nestedStepData.runId) {
        await redisClient.sadd(runIndexKey, nestedKey);
        await redisClient.expire(runIndexKey, this.STEP_PROGRESS_TTL);
      }

      // Get main step progress and emit event with nested steps (without modifying main progress)
      const mainProgress = await this.getTestStepProgress(testId);
      if (mainProgress) {
        // Emit event for real-time updates including nested steps
        // Do NOT modify the main step progress currentStepName to avoid confusion
        this.emit('step:progress:updated', {
          ...mainProgress,
          nestedSteps: nestedSteps
        });
      }

      logger.debug(`StepProgressService: Updated nested step progress for test ${testId} - Step: ${nestedStepData.stepName} (${nestedStepData.status})`);
    } catch (error) {
      logger.error(`StepProgressService: Error updating nested step progress for test ${testId}: ${error}`);
      throw error;
    }
  }
}

// Create and export singleton instance
export const stepProgressService = new StepProgressService();
