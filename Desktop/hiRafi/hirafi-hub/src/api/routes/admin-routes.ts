/**
 * Admin API Routes
 * Admin paneli için API route'ları
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import {
  loginAdmin,
  validateAdminToken,
  getAllAdmins,
  getAdminById,
  createAdmin,
  updateAdmin,
  deleteAdmin,
  changePassword
} from '../../services/mongo/adminService.js';
import {
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser,
  registerUser,
  getCompanyUsers
} from '../../services/mongo/userService.js';
import { ensureMongoDBConnection, isMongoDBInitialized, getMongoDBInitError } from '../../services/mongo/dbConnection.js';
import { db } from '../../services/mongo/dbConnection.js';
import {
  createCompany,
  getCompanyById,
  getAllCompanies,
  updateCompany,
  deleteCompany,
  addTeamToCompany,
  removeTeamFromCompany,
  updateCompanyRemaining
} from '../../services/mongo/companyService.js';
import {
  getCompanyTeams
} from '../../services/mongo/teamService.js';
import { getDashboardStats } from '../../services/mongo/adminStatsService.js';
import {
  getAllSystemSettings,
  getSystemSettingById,
  getSystemSettingByType,
  createSystemSetting,
  updateSystemSetting,
  deleteSystemSetting,
  testSMTPConfig,
  testSlackConfig
} from '../../services/mongo/systemSettingsService.js';
import { SystemSettingType, TestSMTPRequest, TestSlackRequest } from '../../models/system-settings.js';
import { AdminLoginRequest } from '../../models/admin.js';
import { authenticateAdmin, authorizeAdmin, AdminRequest } from '../middleware/adminAuthMiddleware.js';
import { AdminRole } from '../../models/admin.js';
// UserRole artık kullanılmıyor, accountType kullanılıyor
import { CompanyStatus } from '../../models/company.js';
import pauseRoutes from './pause-routes.js';
import { testManager } from '../../core/test-manager/index.js';
import { nodeManager } from '../../core/node-manager/index.js';
import { config } from '../../config/index.js';
import { queueService, QUEUE_NAMES } from '../../services/redis/queueService.js';
import { redisConnection } from '../../services/redis/redisConnection.js';
import { Job } from 'bullmq';
import { operationQueue } from '../../utils/operationQueue.js';
import { redisAdminService } from '../../services/redis/RedisAdminService.js'; // Added import

const router = Router();

/**
 * Update admin settings
 * @param settingsData Settings data to update
 * @returns Update result
 */
async function updateAdminSettings(settingsData: any): Promise<{ success: boolean; message?: string; settings?: any }> {
  try {
    // Validate settings data
    if (!settingsData || typeof settingsData !== 'object') {
      return {
        success: false,
        message: 'Invalid settings data'
      };
    }

    // For now, we'll just return success with the provided settings
    // In a real implementation, you would save these to a database
    logger.info(`Admin settings updated: ${JSON.stringify(settingsData)}`);

    return {
      success: true,
      message: 'Settings updated successfully',
      settings: settingsData
    };
  } catch (error: any) {
    logger.error(`Error updating admin settings: ${error.message}`);
    return {
      success: false,
      message: `Failed to update settings: ${error.message}`
    };
  }
}

/**
 * Helper function to safely remove a BullMQ job with different strategies based on job state
 */
async function safeRemoveBullMQJob(job: Job, testId: string): Promise<{ success: boolean; message: string }> {
  try {
    const jobState = await job.getState();
    logger.info(`[ADMIN] Attempting to remove job ${testId} in state: ${jobState}`);

    // First try direct removal
    try {
      await job.remove();
      return { success: true, message: `Successfully removed ${jobState} job ${testId}` };
    } catch (removeError: any) {
      logger.warn(`[ADMIN] Direct removal failed for ${jobState} job ${testId}: ${removeError.message}`);

      // Try alternative strategies based on job state
      if (jobState === 'active') {
        // For active jobs, move to failed first, then remove
        logger.info(`[ADMIN] Moving active job ${testId} to failed state before removal`);
        await job.moveToFailed(new Error('Manually deleted by admin'), job.token || '', false);
        await job.remove();
        return { success: true, message: `Successfully removed active job ${testId} after moving to failed` };
      } else if (jobState === 'completed' || jobState === 'failed') {
        // For completed/failed jobs, they should be removable, but try force removal
        logger.info(`[ADMIN] Force removing ${jobState} job ${testId}`);
        await job.remove();
        return { success: true, message: `Successfully force removed ${jobState} job ${testId}` };
      } else {
        // For other states (waiting, delayed), retry standard removal
        logger.info(`[ADMIN] Retrying removal for ${jobState} job ${testId}`);
        await job.remove();
        return { success: true, message: `Successfully removed ${jobState} job ${testId} on retry` };
      }
    }
  } catch (error: any) {
    logger.error(`[ADMIN] All removal strategies failed for job ${testId}: ${error.message}`);
    return { success: false, message: `Failed to remove job ${testId}: ${error.message}` };
  }
}

/**
 * Helper function to safely complete a BullMQ job with different strategies based on job state
 */
async function safeCompleteBullMQJob(job: Job, testId: string): Promise<{ success: boolean; message: string }> {
  try {
    const jobState = await job.getState();
    logger.info(`[ADMIN] Attempting to complete job ${testId} in state: ${jobState}`);

    if (jobState === 'active') {
      // Move active job to completed state
      try {
        await job.moveToCompleted('Force completed by admin', job.token || '', false);
        return { success: true, message: `Successfully moved active job ${testId} to completed` };
      } catch (moveError: any) {
        // CRITICAL FIX: Handle lock mismatch errors by removing the job entirely
        if (moveError.message && moveError.message.includes('Lock mismatch')) {
          logger.warn(`[ADMIN] Lock mismatch for active job ${testId}, attempting to remove job entirely`);
          try {
            await job.remove();
            return { success: true, message: `Successfully removed stuck active job ${testId} due to lock mismatch` };
          } catch (removeError: any) {
            logger.error(`[ADMIN] Failed to remove active job ${testId}: ${removeError.message}`);
            return { success: false, message: `Failed to complete or remove active job ${testId}: ${removeError.message}` };
          }
        }
        throw moveError; // Re-throw if not a lock mismatch error
      }
    } else if (jobState === 'waiting' || jobState === 'delayed') {
      // For waiting/delayed jobs, move to completed directly
      await job.moveToCompleted('Force completed by admin', testId, false);
      return { success: true, message: `Successfully moved ${jobState} job ${testId} to completed` };
    } else if (jobState === 'completed') {
      // Already completed
      return { success: true, message: `Job ${testId} is already completed` };
    } else if (jobState === 'failed') {
      // Move failed job to completed
      try {
        await job.moveToCompleted('Force completed by admin (was failed)', testId, false);
        return { success: true, message: `Successfully moved failed job ${testId} to completed` };
      } catch (moveError: any) {
        // CRITICAL FIX: Handle lock mismatch errors by removing the job entirely
        if (moveError.message && moveError.message.includes('Lock mismatch')) {
          logger.warn(`[ADMIN] Lock mismatch for failed job ${testId}, attempting to remove job entirely`);
          try {
            await job.remove();
            return { success: true, message: `Successfully removed stuck failed job ${testId} due to lock mismatch` };
          } catch (removeError: any) {
            logger.error(`[ADMIN] Failed to remove failed job ${testId}: ${removeError.message}`);
            return { success: false, message: `Failed to complete or remove failed job ${testId}: ${removeError.message}` };
          }
        }
        throw moveError; // Re-throw if not a lock mismatch error
      }
    } else {
      logger.warn(`[ADMIN] Unknown job state ${jobState} for test ${testId}, attempting standard completion`);
      try {
        await job.moveToCompleted('Force completed by admin', testId, false);
        return { success: true, message: `Successfully completed job ${testId}` };
      } catch (moveError: any) {
        // CRITICAL FIX: Handle lock mismatch errors by removing the job entirely
        if (moveError.message && moveError.message.includes('Lock mismatch')) {
          logger.warn(`[ADMIN] Lock mismatch for job ${testId} in state ${jobState}, attempting to remove job entirely`);
          try {
            await job.remove();
            return { success: true, message: `Successfully removed stuck job ${testId} from state ${jobState} due to lock mismatch` };
          } catch (removeError: any) {
            logger.error(`[ADMIN] Failed to remove job ${testId} from state ${jobState}: ${removeError.message}`);
            return { success: false, message: `Failed to complete or remove job ${testId} from state ${jobState}: ${removeError.message}` };
          }
        }
        throw moveError; // Re-throw if not a lock mismatch error
      }
    }
  } catch (error: any) {
    logger.error(`[ADMIN] Failed to complete job ${testId}: ${error.message}`);
    return { success: false, message: `Failed to complete job ${testId}: ${error.message}` };
  }
}

/**
 * Helper function to perform comprehensive cleanup of a test from all possible BullMQ states
 */
async function comprehensiveTestCleanup(testId: string): Promise<{ success: boolean; details: any }> {
  const results = {
    bullmqJob: false,
    redisLocks: false,
    testManager: false,
    details: {} as any
  };

  // Step 1: Try to handle BullMQ job
  if (config.connections?.redis?.enabled) {
    try {
      const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (testQueue) {
        const job = await testQueue.getJob(testId);
        if (job) {
          const removalResult = await safeRemoveBullMQJob(job, testId);
          results.bullmqJob = removalResult.success;
          results.details.bullmqMessage = removalResult.message;
        } else {
          // Job not found, check if it exists in any queue state
          logger.info(`[ADMIN] Job ${testId} not found via getJob, checking queue states`);

          // Check different queue states
          const waiting = await testQueue.getWaiting(0, 1000);
          const active = await testQueue.getActive(0, 1000);
          const completed = await testQueue.getCompleted(0, 1000);
          const failed = await testQueue.getFailed(0, 1000);

          const foundInWaiting = waiting.find(j => j.id === testId);
          const foundInActive = active.find(j => j.id === testId);
          const foundInCompleted = completed.find(j => j.id === testId);
          const foundInFailed = failed.find(j => j.id === testId);

          if (foundInWaiting || foundInActive || foundInCompleted || foundInFailed) {
            const foundJob = foundInWaiting || foundInActive || foundInCompleted || foundInFailed;

            const removalResult = await safeRemoveBullMQJob(foundJob!, testId);
            results.bullmqJob = removalResult.success;
            results.details.bullmqMessage = removalResult.message;
          } else {
            logger.info(`[ADMIN] Job ${testId} not found in any queue state`);
            results.bullmqJob = true; // Consider successful if not found
            results.details.bullmqMessage = `Job ${testId} not found in any queue state`;
          }
        }
      } else {
        logger.error(`[ADMIN] Could not get test queue`);
        results.details.bullmqMessage = 'Could not get test queue';
      }
    } catch (error: any) {
      logger.error(`[ADMIN] Error in BullMQ cleanup for test ${testId}: ${error.message}`);
      results.details.bullmqMessage = `Error in BullMQ cleanup: ${error.message}`;
    }

    // Step 2: Clean up Redis locks and state
    try {
      const redisClient = redisConnection.getClient();
      if (redisClient) {
        const lockKeys = [
          `test:lock:${testId}`,
          `test:claim:${testId}`,
          `test:reservation:${testId}`,
          `test:status:${testId}`,
          `test:node:${testId}`
        ];

        const deletedCount = await redisClient.del(...lockKeys);
        results.redisLocks = true;
        results.details.redisMessage = `Cleaned up ${deletedCount} Redis keys`;
        logger.info(`[ADMIN] Cleaned up ${deletedCount} Redis keys for test: ${testId}`);
      }
    } catch (error: any) {
      logger.error(`[ADMIN] Error cleaning up Redis locks for test ${testId}: ${error.message}`);
      results.details.redisMessage = `Error cleaning up Redis locks: ${error.message}`;
    }
  }

  // Step 3: Try to stop the test in the test manager
  try {
    const stopped = await testManager.stopTest(testId);
    results.testManager = stopped;
    results.details.testManagerMessage = stopped
      ? `Successfully stopped test in test manager`
      : `Test not found in test manager or already stopped`;

    if (stopped) {

    } else {
      logger.info(`[ADMIN] Test not found in test manager or already stopped: ${testId}`);
    }
  } catch (error: any) {
    logger.error(`[ADMIN] Error stopping test in test manager ${testId}: ${error.message}`);
    results.details.testManagerMessage = `Error stopping test in test manager: ${error.message}`;
  }

  return {
    success: results.bullmqJob && results.redisLocks,
    details: results
  };
}

// Mount pause routes
router.use('/', pauseRoutes);

/**
 * @route   GET /api/admin/queue-monitor
 * @desc    Get detailed test queue monitoring information
 * @access  Admin
 */
router.get('/queue-monitor', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    // Get test queue status
    const testQueueStatus = await testManager.getQueueStatus();

    // Get detailed information about queued tests
    const queuedTests = await testManager.getQueuedTests();

    // Get information about running tests
    const runningTests = await testManager.getRunningTests();

    // Get Redis queue information if Redis is enabled
    let redisQueues: Record<string, any> = {};

    if (config.connections?.redis?.enabled) {
      try {
        // Get main test queue info
        const testQueueInfo = await queueService.getQueueInfo(QUEUE_NAMES.TEST_QUEUE);
        redisQueues[QUEUE_NAMES.TEST_QUEUE] = testQueueInfo;

        // Get result queue info
        const resultQueueInfo = await queueService.getQueueInfo(QUEUE_NAMES.RESULT_QUEUE);
        redisQueues[QUEUE_NAMES.RESULT_QUEUE] = resultQueueInfo;

        // In the Pull Model, we don't use node-specific queues anymore
        // Just get the central test queue info
        const centralQueueName = QUEUE_NAMES.TEST_QUEUE;
        const centralQueueInfo = await queueService.getQueueInfo(centralQueueName);
        redisQueues[centralQueueName] = {
          ...centralQueueInfo,
          queueType: 'Central Test Queue (Pull Model)'
        };
      } catch (error) {
        logger.error(`[ADMIN] Error getting Redis queue information: ${error}`);
      }
    }

    // Get information about stuck tests
    const stuckTests = await testManager.getStuckTests();

    // Calculate queue health metrics
    const queueHealth = {
      oldestQueuedTest: null as any,
      longestRunningTest: null as any,
      queuedTimeAvg: 0,
      runningTimeAvg: 0
    };

    // Find oldest queued test
    if (queuedTests.length > 0) {
      const oldestTest = queuedTests.reduce((oldest: any, test: any) => {
        // Ensure both dates are properly compared
        let oldestDate: Date | null = null;
        let testDate: Date | null = null;

        try {
          if (oldest.queuedAt) {
            oldestDate = oldest.queuedAt instanceof Date ? oldest.queuedAt : new Date(oldest.queuedAt);
          }

          if (test.queuedAt) {
            testDate = test.queuedAt instanceof Date ? test.queuedAt : new Date(test.queuedAt);
          }

          if (!oldestDate || (testDate && testDate < oldestDate)) {
            return test;
          }
        } catch (error) {
          logger.error(`[ADMIN] Error comparing dates for oldest test: ${error}`);
        }

        return oldest;
      }, queuedTests[0]);

      if (oldestTest && oldestTest.queuedAt) {
        try {
          // Ensure queuedAt is a proper Date object
          let queuedTime: number;
          if (oldestTest.queuedAt instanceof Date) {
            queuedTime = Date.now() - oldestTest.queuedAt.getTime();
          } else {
            // If it's a string or timestamp, convert to Date first
            const queuedDate = new Date(oldestTest.queuedAt);
            queuedTime = Date.now() - queuedDate.getTime();
          }

          queueHealth.oldestQueuedTest = {
            id: oldestTest.id,
            scenarioName: oldestTest.scenarioName,
            queuedAt: oldestTest.queuedAt,
            queuedTimeSeconds: Math.round(queuedTime / 1000)
          };
        } catch (error) {
          logger.error(`[ADMIN] Error calculating oldest queued test time: ${error}`);
        }
      }

      // Calculate average queued time
      let totalQueuedTime = 0;
      let validTestCount = 0;

      for (const test of queuedTests) {
        if (test.queuedAt) {
          try {
            let queuedTime: number;
            if (test.queuedAt instanceof Date) {
              queuedTime = Date.now() - test.queuedAt.getTime();
            } else {
              // If it's a string or timestamp, convert to Date first
              const queuedDate = new Date(test.queuedAt);
              queuedTime = Date.now() - queuedDate.getTime();
            }

            totalQueuedTime += queuedTime;
            validTestCount++;
          } catch (error) {
            logger.error(`[ADMIN] Error calculating queued time for test ${test.id}: ${error}`);
          }
        }
      }

      if (validTestCount > 0) {
        queueHealth.queuedTimeAvg = Math.round((totalQueuedTime / validTestCount) / 1000);
      }
    }

    // Find longest running test
    if (runningTests.length > 0) {
      const longestTest = runningTests.reduce((longest: any, test: any) => {
        // Ensure both dates are properly compared
        let longestDate: Date | null = null;
        let testDate: Date | null = null;

        try {
          if (longest.startedAt) {
            longestDate = longest.startedAt instanceof Date ? longest.startedAt : new Date(longest.startedAt);
          }

          if (test.startedAt) {
            testDate = test.startedAt instanceof Date ? test.startedAt : new Date(test.startedAt);
          }

          if (!longestDate || (testDate && testDate < longestDate)) {
            return test;
          }
        } catch (error) {
          logger.error(`[ADMIN] Error comparing dates for longest test: ${error}`);
        }

        return longest;
      }, runningTests[0]);

      if (longestTest && longestTest.startedAt) {
        try {
          // Ensure startedAt is a proper Date object
          let runningTime: number;
          if (longestTest.startedAt instanceof Date) {
            runningTime = Date.now() - longestTest.startedAt.getTime();
          } else {
            // If it's a string or timestamp, convert to Date first
            const startedDate = new Date(longestTest.startedAt);
            runningTime = Date.now() - startedDate.getTime();
          }

          queueHealth.longestRunningTest = {
            id: longestTest.id,
            scenarioName: longestTest.scenarioName,
            startedAt: longestTest.startedAt,
            nodeId: longestTest.nodeId,
            runningTimeSeconds: Math.round(runningTime / 1000)
          };
        } catch (error) {
          logger.error(`[ADMIN] Error calculating longest running test time: ${error}`);
        }
      }

      // Calculate average running time
      let totalRunningTime = 0;
      let validTestCount = 0;

      for (const test of runningTests) {
        if (test.startedAt) {
          try {
            let runningTime: number;
            if (test.startedAt instanceof Date) {
              runningTime = Date.now() - test.startedAt.getTime();
            } else {
              // If it's a string or timestamp, convert to Date first
              const startedDate = new Date(test.startedAt);
              runningTime = Date.now() - startedDate.getTime();
            }

            totalRunningTime += runningTime;
            validTestCount++;
          } catch (error) {
            logger.error(`[ADMIN] Error calculating running time for test ${test.id}: ${error}`);
          }
        }
      }

      if (validTestCount > 0) {
        queueHealth.runningTimeAvg = Math.round((totalRunningTime / validTestCount) / 1000);
      }
    }

    return res.json({
      success: true,
      timestamp: new Date().toISOString(),
      queueStatus: {
        queued: testQueueStatus.queued,
        running: testQueueStatus.running,
        completed: testQueueStatus.completed || 0,
        failed: testQueueStatus.failed || 0
      },
      queuedTests: queuedTests.map((test: any) => {
        // Ensure queuedAt is a proper Date object
        let queuedTime = 0;
        if (test.queuedAt) {
          try {
            // If it's already a Date object, use getTime()
            if (test.queuedAt instanceof Date) {
              queuedTime = Math.round((Date.now() - test.queuedAt.getTime()) / 1000);
            } else {
              // If it's a string or timestamp, convert to Date first
              const queuedDate = new Date(test.queuedAt);
              queuedTime = Math.round((Date.now() - queuedDate.getTime()) / 1000);
            }
          } catch (error) {
            logger.error(`[ADMIN] Error calculating queued time for test ${test.id}: ${error}`);
          }
        }

        return {
          id: test.id,
          scenarioId: test.scenarioId,
          scenarioName: test.scenarioName,
          queuedAt: test.queuedAt,
          priority: test.priority,
          queuedTimeSeconds: queuedTime,
          jobState: test.jobState || 'waiting'
        };
      }),
      runningTests: runningTests.map((test: any) => {
        // Ensure startedAt is a proper Date object
        let runningTime = 0;
        if (test.startedAt) {
          try {
            // If it's already a Date object, use getTime()
            if (test.startedAt instanceof Date) {
              runningTime = Math.round((Date.now() - test.startedAt.getTime()) / 1000);
            } else {
              // If it's a string or timestamp, convert to Date first
              const startedDate = new Date(test.startedAt);
              runningTime = Math.round((Date.now() - startedDate.getTime()) / 1000);
            }
          } catch (error) {
            logger.error(`[ADMIN] Error calculating running time for test ${test.id}: ${error}`);
          }
        }

        return {
          id: test.id,
          scenarioId: test.scenarioId,
          scenarioName: test.scenarioName,
          startedAt: test.startedAt,
          nodeId: test.nodeId,
          runningTimeSeconds: runningTime
        };
      }),
      stuckTests: stuckTests.map((test: any) => {
        return {
          id: test.id,
          scenarioId: test.scenarioId,
          scenarioName: test.scenarioName,
          status: test.status,
          queuedAt: test.queuedAt,
          startedAt: test.startedAt,
          nodeId: test.nodeId,
          stuckTimeSeconds: test.stuckTimeSeconds || 0 // Use the value calculated in TestQueueService
        };
      }),
      redisQueues,
      queueHealth
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Queue Monitor Error: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/admin/stuck-tests/:testId
 * @desc    Delete a stuck test from the queue and clean up associated resources
 * @access  Admin
 */
router.delete('/stuck-tests/:testId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    logger.info(`[ADMIN] Attempting to delete stuck test: ${testId}`);

    // Use comprehensive cleanup function
    const cleanupResult = await comprehensiveTestCleanup(testId);

    return res.json({
      success: cleanupResult.success,
      message: cleanupResult.success
        ? `Stuck test ${testId} successfully deleted and cleaned up`
        : `Partial cleanup completed for test ${testId}. Some operations may have failed.`,
      details: {
        bullmqJob: cleanupResult.details.bullmqJob,
        redisLocks: cleanupResult.details.redisLocks,
        testManager: cleanupResult.details.testManager,
        messages: {
          bullmq: cleanupResult.details.bullmqMessage,
          redis: cleanupResult.details.redisMessage,
          testManager: cleanupResult.details.testManagerMessage
        }
      }
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error deleting stuck test ${req.params.testId}: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/admin/stuck-tests/cleanup-all
 * @desc    Clean up all stuck tests in the system
 * @access  Admin
 */
router.delete('/stuck-tests/cleanup-all', authenticateAdmin, async (_req: AdminRequest, res: Response) => {
  try {
    logger.info(`[ADMIN] Attempting to cleanup all stuck tests`);

    // Get all stuck tests
    const stuckTests = await testManager.getStuckTests();

    if (stuckTests.length === 0) {
      return res.json({
        success: true,
        message: 'No stuck tests found to cleanup',
        details: {
          processedCount: 0,
          successCount: 0,
          failureCount: 0,
          results: []
        }
      });
    }



    const cleanupResults = [];
    let successCount = 0;
    let failureCount = 0;

    // Process each stuck test
    for (const stuckTest of stuckTests) {
      try {
        logger.info(`[ADMIN] Cleaning up stuck test: ${stuckTest.id}`);
        const cleanupResult = await comprehensiveTestCleanup(stuckTest.id);

        cleanupResults.push({
          testId: stuckTest.id,
          scenarioName: stuckTest.scenarioName || 'Unknown',
          success: cleanupResult.success,
          details: cleanupResult.details
        });

        if (cleanupResult.success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error: any) {
        logger.error(`[ADMIN] Error cleaning up stuck test ${stuckTest.id}: ${error.message}`);
        cleanupResults.push({
          testId: stuckTest.id,
          scenarioName: stuckTest.scenarioName || 'Unknown',
          success: false,
          details: {
            error: error.message
          }
        });
        failureCount++;
      }
    }

    const overallSuccess = failureCount === 0;

    return res.json({
      success: overallSuccess,
      message: overallSuccess
        ? `Successfully cleaned up all ${successCount} stuck tests`
        : `Cleaned up ${successCount} tests successfully, ${failureCount} failed`,
      details: {
        processedCount: stuckTests.length,
        successCount,
        failureCount,
        results: cleanupResults
      }
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error in bulk stuck test cleanup: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   POST /api/admin/complete-test/:testId
 * @desc    Force complete an active test and move it to completed state
 * @access  Admin
 */
router.post('/complete-test/:testId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    logger.info(`[ADMIN] Attempting to force complete test: ${testId}`);

    let completionResults = {
      bullmqJob: false,
      redisLocks: false,
      testManager: false
    };

    // Step 1: Try to complete the BullMQ job if it exists
    if (config.connections?.redis?.enabled) {
      try {
        const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
        if (testQueue) {
          // Try to get the job
          const job = await testQueue.getJob(testId);
          if (job) {
            const jobState = await job.getState();


            const completionResult = await safeCompleteBullMQJob(job, testId);
            completionResults.bullmqJob = completionResult.success;
            logger.info(`[ADMIN] ${completionResult.message}`);
          } else {
            logger.info(`[ADMIN] No BullMQ job found for test: ${testId}`);
            completionResults.bullmqJob = true; // Consider it successful if job doesn't exist
          }
        } else {
          logger.error(`[ADMIN] Could not get test queue for job completion`);
          completionResults.bullmqJob = false;
        }
      } catch (error: any) {
        logger.error(`[ADMIN] Error in BullMQ job completion process for test ${testId}: ${error.message}`);
        completionResults.bullmqJob = false;
      }

      // Step 2: Clean up Redis locks and state
      try {
        const redisClient = redisConnection.getClient();
        if (redisClient) {
          const lockKeys = [
            `test:lock:${testId}`,
            `test:claim:${testId}`,
            `test:reservation:${testId}`,
            `test:status:${testId}`,
            `test:node:${testId}`
          ];

          // Delete all lock keys
          const deletedCount = await redisClient.del(...lockKeys);
          completionResults.redisLocks = true;
          logger.info(`[ADMIN] Cleaned up ${deletedCount} Redis keys for test: ${testId}`);
        }
      } catch (error) {
        logger.error(`[ADMIN] Error cleaning up Redis locks for test ${testId}: ${error}`);
      }
    }

    // Step 3: Try to stop the test in the test manager
    try {
      const stopped = await testManager.stopTest(testId);
      completionResults.testManager = stopped;
      if (stopped) {
        logger.info(`[ADMIN] Test stopped in test manager: ${testId}`);
      } else {
        logger.info(`[ADMIN] Test not found in test manager or already stopped: ${testId}`);
      }
    } catch (error) {
      logger.error(`[ADMIN] Error stopping test in test manager ${testId}: ${error}`);
    }

    // Step 4: Process test completion through CentralResultProcessor to update counters
    try {
      const { centralResultProcessor } = await import('../../services/result/centralResultProcessor.js');

      // Create a standardized completion result for the processor
      const completionResult = {
        testId: testId,
        status: 'stopped',
        result: {
          success: false,
          error: 'Force completed by admin',
          adminAction: true
        },
        timestamp: new Date().toISOString(),
        source: 'admin-force-completion'
      };

      // Process through central processor to ensure proper counter updates
      const processingResult = await centralResultProcessor.processResult(
        completionResult,
        'Admin-Force-Complete',
        `admin-force-${testId}-${Date.now()}`
      );

      if (processingResult) {
        logger.info(`[ADMIN] Test ${testId} processed through CentralResultProcessor for proper counter updates`);
      } else {
        logger.warn(`[ADMIN] Failed to process test ${testId} through CentralResultProcessor`);
      }
    } catch (processingError) {
      logger.error(`[ADMIN] Error processing test ${testId} through CentralResultProcessor: ${processingError}`);
    }

    // Determine overall success
    const overallSuccess = completionResults.bullmqJob && completionResults.redisLocks;

    return res.json({
      success: overallSuccess,
      message: overallSuccess
        ? `Test ${testId} successfully force completed`
        : `Partial completion for test ${testId}. Some operations may have failed.`,
      details: completionResults
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error force completing test ${req.params.testId}: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   GET /api/admin/active-tests
 * @desc    Get all active tests in BullMQ test queue
 * @access  Admin
 */
router.get('/active-tests', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    if (!config.connections?.redis?.enabled) {
      return res.status(503).json({
        success: false,
        error: 'Redis is not enabled'
      });
    }

    const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
    if (!testQueue) {
      return res.status(500).json({
        success: false,
        error: 'Could not get test queue'
      });
    }

    // Get active jobs
    const activeJobs = await testQueue.getActive(0, 1000); // Get up to 1000 active jobs

    const activeTests = await Promise.all(activeJobs.map(async (job) => {
      try {
        const jobState = await job.getState();
        const testData = job.data;

        return {
          id: job.id,
          testId: testData?.id || job.id,
          scenarioId: testData?.scenarioId,
          scenarioName: testData?.scenarioName || testData?.scenario?.name || 'Unknown',
          state: jobState,
          attempts: job.attemptsMade,
          maxAttempts: job.opts.attempts || 1,
          processedOn: job.processedOn ? new Date(job.processedOn) : null,
          finishedOn: job.finishedOn ? new Date(job.finishedOn) : null,
          runId: testData?.runId,
          nodeId: testData?.nodeId,
          priority: job.opts.priority || 0,
          delay: job.opts.delay || 0,
          timestamp: job.timestamp ? new Date(job.timestamp) : null,
          progress: job.progress || 0
        };
      } catch (error) {
        logger.error(`[ADMIN] Error processing active job ${job.id}: ${error}`);
        return {
          id: job.id,
          testId: job.id,
          scenarioName: 'Error loading job data',
          state: 'unknown',
          attempts: 0,
          maxAttempts: 0,
          error: 'Failed to load job data'
        };
      }
    }));

    return res.json({
      success: true,
      activeTests,
      count: activeTests.length,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error getting active tests: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   POST /api/admin/complete-active-test/:testId
 * @desc    Force complete a specific active test by moving it to completed state
 * @access  Admin
 */
router.post('/complete-active-test/:testId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    if (!config.connections?.redis?.enabled) {
      return res.status(503).json({
        success: false,
        error: 'Redis is not enabled'
      });
    }

    logger.info(`[ADMIN] Attempting to force complete active test: ${testId}`);

    const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
    if (!testQueue) {
      return res.status(500).json({
        success: false,
        error: 'Could not get test queue'
      });
    }

    // Find the job in active state
    const activeJobs = await testQueue.getActive();
    const job = activeJobs.find(j => j.id === testId || j.data?.id === testId);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: `Active test with ID ${testId} not found`
      });
    }

    const jobState = await job.getState();


    if (jobState !== 'active') {
      return res.status(400).json({
        success: false,
        error: `Test ${testId} is not in active state (current state: ${jobState})`
      });
    }

    // Force complete the job
    const completionResult = await safeCompleteBullMQJob(job, testId);

    if (completionResult.success) {
      // Process through CentralResultProcessor for proper counter updates
      try {
        const { centralResultProcessor } = await import('../../services/result/centralResultProcessor.js');

        const processingResult = {
          testId: testId,
          status: 'stopped',
          result: {
            success: false,
            error: 'Force completed by admin',
            adminAction: true
          },
          timestamp: new Date().toISOString(),
          source: 'admin-single-completion'
        };

        await centralResultProcessor.processResult(
          processingResult,
          'Admin-Single-Complete',
          `admin-single-${testId}-${Date.now()}`
        );

        logger.info(`[ADMIN] Test ${testId} processed through CentralResultProcessor for counter updates`);
      } catch (processingError) {
        logger.warn(`[ADMIN] Error processing test ${testId} through CentralResultProcessor: ${processingError}`);
      }

      // Also clean up Redis locks
      try {
        const redisClient = redisConnection.getClient();
        if (redisClient) {
          const lockKeys = [
            `test:lock:${testId}`,
            `test:claim:${testId}`,
            `test:reservation:${testId}`,
            `test:status:${testId}`,
            `test:node:${testId}`
          ];
          const deletedCount = await redisClient.del(...lockKeys);
          logger.info(`[ADMIN] Cleaned up ${deletedCount} Redis keys for completed test: ${testId}`);
        }
      } catch (cleanupError) {
        logger.warn(`[ADMIN] Error cleaning up Redis locks for test ${testId}: ${cleanupError}`);
      }
    }

    return res.json({
      success: completionResult.success,
      message: completionResult.message,
      testId: testId
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error force completing active test ${req.params.testId}: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/admin/remove-active-test/:testId
 * @desc    Remove a specific active test from the queue
 * @access  Admin
 */
router.delete('/remove-active-test/:testId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    if (!config.connections?.redis?.enabled) {
      return res.status(503).json({
        success: false,
        error: 'Redis is not enabled'
      });
    }

    logger.info(`[ADMIN] Attempting to remove active test: ${testId}`);

    const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
    if (!testQueue) {
      return res.status(500).json({
        success: false,
        error: 'Could not get test queue'
      });
    }

    // Find the job in active state
    const activeJobs = await testQueue.getActive();
    const job = activeJobs.find(j => j.id === testId || j.data?.id === testId);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: `Active test with ID ${testId} not found`
      });
    }

    const jobState = await job.getState();


    if (jobState !== 'active') {
      return res.status(400).json({
        success: false,
        error: `Test ${testId} is not in active state (current state: ${jobState})`
      });
    }

    // Remove the job
    const removalResult = await safeRemoveBullMQJob(job, testId);

    if (removalResult.success) {
      // Also clean up Redis locks
      try {
        const redisClient = redisConnection.getClient();
        if (redisClient) {
          const lockKeys = [
            `test:lock:${testId}`,
            `test:claim:${testId}`,
            `test:reservation:${testId}`,
            `test:status:${testId}`,
            `test:node:${testId}`
          ];
          const deletedCount = await redisClient.del(...lockKeys);
          logger.info(`[ADMIN] Cleaned up ${deletedCount} Redis keys for removed test: ${testId}`);
        }
      } catch (cleanupError) {
        logger.warn(`[ADMIN] Error cleaning up Redis locks for test ${testId}: ${cleanupError}`);
      }
    }

    return res.json({
      success: removalResult.success,
      message: removalResult.message,
      testId: testId
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error removing active test ${req.params.testId}: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   POST /api/admin/complete-all-active-tests
 * @desc    Force complete all active tests in the queue
 * @access  Admin
 */
router.post('/complete-all-active-tests', authenticateAdmin, async (_req: AdminRequest, res: Response) => {
  try {
    if (!config.connections?.redis?.enabled) {
      return res.status(503).json({
        success: false,
        error: 'Redis is not enabled'
      });
    }

    logger.info(`[ADMIN] Attempting to force complete all active tests`);

    const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
    if (!testQueue) {
      return res.status(500).json({
        success: false,
        error: 'Could not get test queue'
      });
    }

    // Get all active jobs
    const activeJobs = await testQueue.getActive();

    if (activeJobs.length === 0) {
      return res.json({
        success: true,
        message: 'No active tests found to complete',
        details: {
          processedCount: 0,
          successCount: 0,
          failureCount: 0,
          results: []
        }
      });
    }



    const completionResults = [];
    let successCount = 0;
    let failureCount = 0;

    // Process each active test
    for (const job of activeJobs) {
      try {
        const testId = job.data?.id || job.id;
        logger.info(`[ADMIN] Completing active test: ${testId}`);

        const completionResult = await safeCompleteBullMQJob(job, testId);

        completionResults.push({
          testId: testId,
          jobId: job.id,
          scenarioName: job.data?.scenarioName || job.data?.scenario?.name || 'Unknown',
          success: completionResult.success,
          message: completionResult.message
        });

        if (completionResult.success) {
          successCount++;

          // Process through CentralResultProcessor for proper counter updates
          try {
            const { centralResultProcessor } = await import('../../services/result/centralResultProcessor.js');

            const processingResult = {
              testId: testId,
              status: 'stopped',
              result: {
                success: false,
                error: 'Bulk force completed by admin',
                adminAction: true
              },
              timestamp: new Date().toISOString(),
              source: 'admin-bulk-completion'
            };

            await centralResultProcessor.processResult(
              processingResult,
              'Admin-Bulk-Complete',
              `admin-bulk-${testId}-${Date.now()}`
            );

            logger.info(`[ADMIN] Test ${testId} processed through CentralResultProcessor for counter updates`);
          } catch (processingError) {
            logger.warn(`[ADMIN] Error processing test ${testId} through CentralResultProcessor: ${processingError}`);
          }

          // Clean up Redis locks for successful completions
          try {
            const redisClient = redisConnection.getClient();
            if (redisClient) {
              const lockKeys = [
                `test:lock:${testId}`,
                `test:claim:${testId}`,
                `test:reservation:${testId}`,
                `test:status:${testId}`,
                `test:node:${testId}`
              ];
              await redisClient.del(...lockKeys);
            }
          } catch (cleanupError) {
            logger.warn(`[ADMIN] Error cleaning up Redis locks for test ${testId}: ${cleanupError}`);
          }
        } else {
          failureCount++;
        }
      } catch (error: any) {
        const testId = job.data?.id || job.id;
        logger.error(`[ADMIN] Error completing active test ${testId}: ${error.message}`);
        completionResults.push({
          testId: testId,
          jobId: job.id,
          scenarioName: job.data?.scenarioName || 'Unknown',
          success: false,
          message: `Error: ${error.message}`
        });
        failureCount++;
      }
    }

    const overallSuccess = failureCount === 0;

    return res.json({
      success: overallSuccess,
      message: overallSuccess
        ? `Successfully completed all ${successCount} active tests`
        : `Completed ${successCount} tests successfully, ${failureCount} failed`,
      details: {
        processedCount: activeJobs.length,
        successCount,
        failureCount,
        results: completionResults
      }
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error in bulk active test completion: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/admin/remove-all-active-tests
 * @desc    Remove all active tests from the queue
 * @access  Admin
 */
router.delete('/remove-all-active-tests', authenticateAdmin, async (_req: AdminRequest, res: Response) => {
  try {
    if (!config.connections?.redis?.enabled) {
      return res.status(503).json({
        success: false,
        error: 'Redis is not enabled'
      });
    }

    logger.info(`[ADMIN] Attempting to remove all active tests`);

    const testQueue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
    if (!testQueue) {
      return res.status(500).json({
        success: false,
        error: 'Could not get test queue'
      });
    }

    // Get all active jobs
    const activeJobs = await testQueue.getActive();

    if (activeJobs.length === 0) {
      return res.json({
        success: true,
        message: 'No active tests found to remove',
        details: {
          processedCount: 0,
          successCount: 0,
          failureCount: 0,
          results: []
        }
      });
    }



    const removalResults = [];
    let successCount = 0;
    let failureCount = 0;

    // Process each active test
    for (const job of activeJobs) {
      try {
        const testId = job.data?.id || job.id;
        logger.info(`[ADMIN] Removing active test: ${testId}`);

        const removalResult = await safeRemoveBullMQJob(job, testId);

        removalResults.push({
          testId: testId,
          jobId: job.id,
          scenarioName: job.data?.scenarioName || job.data?.scenario?.name || 'Unknown',
          success: removalResult.success,
          message: removalResult.message
        });

        if (removalResult.success) {
          successCount++;

          // Clean up Redis locks for successful removals
          try {
            const redisClient = redisConnection.getClient();
            if (redisClient) {
              const lockKeys = [
                `test:lock:${testId}`,
                `test:claim:${testId}`,
                `test:reservation:${testId}`,
                `test:status:${testId}`,
                `test:node:${testId}`
              ];
              await redisClient.del(...lockKeys);
            }
          } catch (cleanupError) {
            logger.warn(`[ADMIN] Error cleaning up Redis locks for test ${testId}: ${cleanupError}`);
          }
        } else {
          failureCount++;
        }
      } catch (error: any) {
        const testId = job.data?.id || job.id;
        logger.error(`[ADMIN] Error removing active test ${testId}: ${error.message}`);
        removalResults.push({
          testId: testId,
          jobId: job.id,
          scenarioName: job.data?.scenarioName || 'Unknown',
          success: false,
          message: `Error: ${error.message}`
        });
        failureCount++;
      }
    }

    const overallSuccess = failureCount === 0;

    return res.json({
      success: overallSuccess,
      message: overallSuccess
        ? `Successfully removed all ${successCount} active tests`
        : `Removed ${successCount} tests successfully, ${failureCount} failed`,
      details: {
        processedCount: activeJobs.length,
        successCount,
        failureCount,
        results: removalResults
      }
    });

  } catch (error: any) {
    logger.error(`[ADMIN] Error in bulk active test removal: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @route   POST /api/admin/nuclear-clear-queue
 * @desc    Nuclear option: Completely obliterate the test queue and all its contents
 * @access  Admin
 */
router.post('/nuclear-clear-queue', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { queueName, confirmationCode } = req.body;

    // Safety check: require confirmation code
    if (confirmationCode !== 'NUCLEAR_CLEAR_CONFIRMED') {
      return res.status(400).json({
        success: false,
        error: 'Nuclear queue clear requires confirmation code: NUCLEAR_CLEAR_CONFIRMED'
      });
    }

    if (!config.connections?.redis?.enabled) {
      return res.status(503).json({
        success: false,
        error: 'Redis is not enabled'
      });
    }

    // Default to test queue if not specified
    const targetQueue = queueName || QUEUE_NAMES.TEST_QUEUE;

    logger.warn(`[ADMIN] NUCLEAR CLEAR: Starting complete obliteration of queue: ${targetQueue}`);

    const testQueue = queueService.getQueue(targetQueue);
    if (!testQueue) {
      return res.status(500).json({
        success: false,
        error: `Could not get queue: ${targetQueue}`
      });
    }

    const results = {
      queueObliterated: false,
      redisKeysCleared: false,
      locksCleaned: false,
      details: {
        obliterateMessage: '',
        redisKeysMessage: '',
        locksMessage: '',
        preObliterateStats: {} as any,
        postObliterateStats: {} as any
      }
    };

    // Step 1: Get pre-obliterate statistics
    try {
      const waiting = await testQueue.getWaiting(0, 1000);
      const active = await testQueue.getActive(0, 1000);
      const completed = await testQueue.getCompleted(0, 1000);
      const failed = await testQueue.getFailed(0, 1000);
      const delayed = await testQueue.getDelayed(0, 1000);

      results.details.preObliterateStats = {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length
      };

      logger.warn(`[ADMIN] NUCLEAR CLEAR: Pre-obliterate stats for ${targetQueue}:`, results.details.preObliterateStats);
    } catch (statsError: any) {
      logger.warn(`[ADMIN] NUCLEAR CLEAR: Could not get pre-obliterate stats: ${statsError.message}`);
    }

    // Step 2: Nuclear obliterate the queue (force=true bypasses all safety checks)
    try {
      logger.warn(`[ADMIN] NUCLEAR CLEAR: Obliterating queue ${targetQueue} with force=true`);
      await testQueue.obliterate({ force: true });
      results.queueObliterated = true;
      results.details.obliterateMessage = `Successfully obliterated queue ${targetQueue}`;
      logger.warn(`[ADMIN] NUCLEAR CLEAR: Queue ${targetQueue} obliterated successfully`);
    } catch (obliterateError: any) {
      logger.error(`[ADMIN] NUCLEAR CLEAR: Failed to obliterate queue ${targetQueue}: ${obliterateError.message}`);
      results.details.obliterateMessage = `Failed to obliterate queue: ${obliterateError.message}`;
    }

    // Step 3: Clean up any remaining Redis keys manually
    try {
      const redisClient = redisConnection.getClient();
      if (redisClient) {
        logger.warn(`[ADMIN] NUCLEAR CLEAR: Cleaning up remaining Redis keys for ${targetQueue}`);

        // Get all keys related to the queue
        const queuePattern = `bull:${targetQueue}:*`;
        const keys = await redisClient.keys(queuePattern);

        if (keys.length > 0) {
          logger.warn(`[ADMIN] NUCLEAR CLEAR: Found ${keys.length} Redis keys to delete`);
          await redisClient.del(...keys);
          results.details.redisKeysMessage = `Deleted ${keys.length} Redis keys`;
        } else {
          results.details.redisKeysMessage = 'No Redis keys found to delete';
        }

        results.redisKeysCleared = true;
        logger.warn(`[ADMIN] NUCLEAR CLEAR: Redis keys cleanup completed`);
      }
    } catch (redisError: any) {
      logger.error(`[ADMIN] NUCLEAR CLEAR: Failed to clean up Redis keys: ${redisError.message}`);
      results.details.redisKeysMessage = `Failed to clean up Redis keys: ${redisError.message}`;
    }

    // Step 4: Clean up test-related locks and state
    try {
      const redisClient = redisConnection.getClient();
      if (redisClient) {
        logger.warn(`[ADMIN] NUCLEAR CLEAR: Cleaning up test locks and state`);

        // Clean up test-related patterns
        const lockPatterns = [
          'test:lock:*',
          'test:claim:*',
          'test:reservation:*',
          'test:status:*',
          'test:node:*',
          'test:step:*',
          'test:progress:*'
        ];

        let totalLocksDeleted = 0;
        for (const pattern of lockPatterns) {
          const keys = await redisClient.keys(pattern);
          if (keys.length > 0) {
            await redisClient.del(...keys);
            totalLocksDeleted += keys.length;
          }
        }

        results.locksCleaned = true;
        results.details.locksMessage = `Deleted ${totalLocksDeleted} test-related locks and state keys`;
        logger.warn(`[ADMIN] NUCLEAR CLEAR: Deleted ${totalLocksDeleted} test-related locks`);
      }
    } catch (locksError: any) {
      logger.error(`[ADMIN] NUCLEAR CLEAR: Failed to clean up test locks: ${locksError.message}`);
      results.details.locksMessage = `Failed to clean up test locks: ${locksError.message}`;
    }

    // Step 5: Get post-obliterate statistics
    try {
      // Recreate the queue to get fresh stats
      const freshQueue = queueService.getQueue(targetQueue);
      if (freshQueue) {
        const waiting = await freshQueue.getWaiting(0, 1000);
        const active = await freshQueue.getActive(0, 1000);
        const completed = await freshQueue.getCompleted(0, 1000);
        const failed = await freshQueue.getFailed(0, 1000);
        const delayed = await freshQueue.getDelayed(0, 1000);

        results.details.postObliterateStats = {
          waiting: waiting.length,
          active: active.length,
          completed: completed.length,
          failed: failed.length,
          delayed: delayed.length,
          total: waiting.length + active.length + completed.length + failed.length + delayed.length
        };

        logger.warn(`[ADMIN] NUCLEAR CLEAR: Post-obliterate stats for ${targetQueue}:`, results.details.postObliterateStats);
      } else {
        logger.warn(`[ADMIN] NUCLEAR CLEAR: Could not get fresh queue instance for post-obliterate stats`);
      }
    } catch (statsError: any) {
      logger.warn(`[ADMIN] NUCLEAR CLEAR: Could not get post-obliterate stats: ${statsError.message}`);
    }

    const overallSuccess = results.queueObliterated && results.redisKeysCleared && results.locksCleaned;

    logger.warn(`[ADMIN] NUCLEAR CLEAR: Operation completed. Success: ${overallSuccess}`);

    return res.json({
      success: overallSuccess,
      message: overallSuccess
        ? `Nuclear clear of queue ${targetQueue} completed successfully`
        : `Nuclear clear of queue ${targetQueue} completed with some failures`,
      queueName: targetQueue,
      results
    });

  } catch (error: any) {
    logger.error(`[ADMIN] NUCLEAR CLEAR: Critical error during nuclear clear: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Nuclear clear failed: ${error.message}`
    });
  }
});

/**
 * @route   GET /api/admin/health
 * @desc    Get detailed system health information for admin
 * @access  Admin
 */
router.get('/health', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    // Get node information
    const nodes = nodeManager.getAllNodes();
    const availableNodes = nodeManager.getAvailableNodes().length;
    const totalNodes = nodes.length;

    // Get test queue status
    const testQueueStatus = await testManager.getQueueStatus();

    // Get Redis queue status if Redis is enabled
    let redisStatus = {
      connected: false,
      queues: {},
      queueDetails: {}
    };

    if (config.connections?.redis?.enabled) {
      try {
        redisStatus.connected = redisConnection.isConnected();
        if (redisStatus.connected) {
          redisStatus.queues = await queueService.getQueueStatus();

          // Get more detailed information about queues
          const allQueues = queueService.getAllQueues();
          const queueDetails: any = {};

          for (const [name, queue] of allQueues.entries()) {
            queueDetails[name] = {
              name,
              jobCount: await queue.count(),
              workers: (queue as any).workers && Array.isArray((queue as any).workers) ? (queue as any).workers.length : 0
            };
          }

          redisStatus.queueDetails = queueDetails;
        }
      } catch (redisError) {
        logger.error(`[ADMIN] Health Check Redis Error: ${redisError}`);
        redisStatus.connected = false;
      }
    }

    // Get MongoDB status
    const mongoDBStatus = {
      initialized: isMongoDBInitialized(),
      error: getMongoDBInitError()?.message,
      collections: db ? Object.keys(db.collections).length : 0
    };

    // Get operation queue stats
    const operationQueueStats = operationQueue.getStats();

    // Get memory usage
    const memoryUsage = process.memoryUsage();

    // Get system information
    const systemInfo = {
      uptime: process.uptime(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      cpuUsage: process.cpuUsage(),
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`
      }
    };

    return res.json({
      success: true,
      status: 'UP',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || 'unknown',
      environment: process.env.NODE_ENV || 'development',
      nodes: {
        total: totalNodes,
        available: availableNodes,
        busy: totalNodes - availableNodes,
        details: nodes.map(node => ({
          id: node.id,
          name: node.name,
          status: node.status,
          lastSeen: node.lastSeen,
          capabilities: node.capabilities || {}
        }))
      },
      tests: {
        queued: testQueueStatus.queued || 0,
        running: testQueueStatus.running || 0,
        maxConcurrent: 10 // Default value since config.testManager doesn't exist
      },
      config: {
        port: config.server.port,
        websocketEnabled: config.connections.websocket.enabled,
        redisEnabled: config.connections.redis.enabled
      },
      connections: {
        redis: redisStatus,
        mongodb: mongoDBStatus
      },
      system: systemInfo,
      operationQueue: operationQueueStats
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Health Check Error: ${error.message}`);
    return res.status(500).json({
      success: false,
      status: 'DOWN',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/admin/login
 * @desc    Admin login
 * @access  Public
 */
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body as AdminLoginRequest;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Admin login
    const result = await loginAdmin(email, password);

    if (!result.success) {
      logger.warn(`[ADMIN] Failed login attempt for: ${email}`);
      return res.status(401).json(result);
    }

    // Başarılı login için data wrapper ile sarmalayalım
    return res.json({
      success: true,
      data: {
        admin: result.admin,
        token: result.token
      }
    });
  } catch (error) {
    logger.error('[ADMIN] Login error:', error);
    return res.status(500).json({
      success: false,
      error: 'Login failed'
    });
  }
});

/**
 * @route   POST /api/admin/validate
 * @desc    Validate admin token
 * @access  Public
 */
router.post('/validate', async (req: Request, res: Response) => {
  try {
    // Authorization header'ı al
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({
        success: false,
        error: 'Authorization header is required'
      });
    }

    // Bearer token formatını kontrol et
    const parts = authHeader.split(' ');

    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return res.status(401).json({
        success: false,
        error: 'Invalid authorization format. Use: Bearer <token>'
      });
    }

    const token = parts[1];

    // Token doğrula
    const result = await validateAdminToken(token);

    if (result.success && result.admin) {
      return res.json({
        success: true,
        data: {
          admin: result.admin
        }
      });
    } else {
      return res.json(result);
    }
  } catch (error) {
    logger.error('[ADMIN] Token validation error:', error);
    return res.status(500).json({
      success: false,
      error: 'Validation failed'
    });
  }
});

/**
 * @route   GET /api/admin/profile
 * @desc    Get admin profile
 * @access  Admin
 */
router.get('/profile', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Admin bilgileri middleware tarafından ekleniyor
    const admin = req.admin;

    if (!admin) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    return res.json({
      success: true,
      admin
    });
  } catch (error) {
    logger.error('[ADMIN] Profile error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get profile'
    });
  }
});

/**
 * @route   GET /api/admin/check-auth
 * @desc    Check if admin is authenticated
 * @access  Admin
 */
router.get('/check-auth', authenticateAdmin, (req: AdminRequest, res: Response) => {
  return res.json({
    success: true,
    message: 'Authenticated',
    admin: req.admin
  });
});

/**
 * @route   GET /api/admin/stats
 * @desc    Get dashboard statistics
 * @access  Admin
 */
router.get('/stats', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    // Dashboard istatistiklerini getir
    const result = await getDashboardStats();

    if (!result.success) {
      logger.error(`[ADMIN] Failed to get dashboard stats: ${result.error}`);
      return res.status(500).json({
        success: false,
        error: result.error || 'Failed to get dashboard statistics'
      });
    }

    return res.json({
      success: true,
      data: {
        stats: result.stats
      }
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/stats: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/super-admin-only
 * @desc    Super admin only endpoint (test)
 * @access  Super Admin
 */
router.get('/super-admin-only',
  authenticateAdmin,
  authorizeAdmin(AdminRole.SUPER_ADMIN),
  (req: AdminRequest, res: Response) => {
    return res.json({
      success: true,
      message: 'Super admin access granted',
      admin: req.admin
    });
  }
);

/**
 * @route   GET /api/admin/users
 * @desc    Get all admin users
 * @access  Admin
 */
router.get('/users', authenticateAdmin, async (_req: AdminRequest, res: Response) => {
  try {
    const result = await getAllAdmins();
    return res.json(result);
  } catch (error) {
    logger.error('[ADMIN] Error getting admin users:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get admin users'
    });
  }
});

/**
 * @route   GET /api/admin/users/:adminId
 * @desc    Get admin user by ID
 * @access  Admin
 */
router.get('/users/:adminId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { adminId } = req.params;

    if (!adminId) {
      return res.status(400).json({
        success: false,
        error: 'Admin ID is required'
      });
    }

    const result = await getAdminById(adminId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.error || 'Admin user not found'
      });
    }

    return res.json({
      success: true,
      data: {
        admin: result.admin
      }
    });
  } catch (error) {
    logger.error('[ADMIN] Error getting admin user:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get admin user'
    });
  }
});

/**
 * @route   POST /api/admin/users
 * @desc    Create new admin user
 * @access  Super Admin
 */
router.post('/users',
  authenticateAdmin,
  authorizeAdmin(AdminRole.SUPER_ADMIN),
  async (req: AdminRequest, res: Response) => {
    try {
      const { email, password, name, role } = req.body;

      if (!email || !password || !role) {
        return res.status(400).json({
          success: false,
          error: 'Email, password and role are required'
        });
      }

      const result = await createAdmin({
        email,
        password,
        name,
        role,
      });

      return res.status(result.success ? 201 : 400).json(result);
    } catch (error) {
      logger.error('[ADMIN] Error creating admin user:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to create admin user'
      });
    }
  }
);

/**
 * @route   PUT /api/admin/users/:adminId
 * @desc    Update admin user
 * @access  Super Admin
 */
router.put('/users/:adminId',
  authenticateAdmin,
  authorizeAdmin(AdminRole.SUPER_ADMIN),
  async (req: AdminRequest, res: Response) => {
    try {
      const { adminId } = req.params;
      const { email, name, role, active } = req.body;

      if (!adminId) {
        return res.status(400).json({
          success: false,
          error: 'Admin ID is required'
        });
      }

      const result = await updateAdmin(adminId, {
        email,
        name,
        role,
        active
      });

      return res.json(result);
    } catch (error) {
      logger.error('[ADMIN] Error updating admin user:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to update admin user'
      });
    }
  }
);

/**
 * @route   DELETE /api/admin/users/:adminId
 * @desc    Delete admin user
 * @access  Super Admin
 */
router.delete('/users/:adminId',
  authenticateAdmin,
  authorizeAdmin(AdminRole.SUPER_ADMIN),
  async (req: AdminRequest, res: Response) => {
    try {
      const { adminId } = req.params;

      if (!adminId) {
        return res.status(400).json({
          success: false,
          error: 'Admin ID is required'
        });
      }

      const result = await deleteAdmin(adminId);
      return res.json(result);
    } catch (error) {
      logger.error('[ADMIN] Error deleting admin user:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to delete admin user'
      });
    }
  }
);

/**
 * @route   POST /api/admin/change-password
 * @desc    Change admin password
 * @access  Admin
 */
router.post('/change-password', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const adminId = req.admin?.id;

    if (!adminId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Current password and new password are required'
      });
    }

    const result = await changePassword(adminId, currentPassword, newPassword);
    return res.json(result);
  } catch (error) {
    logger.error('[ADMIN] Error changing password:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to change password'
    });
  }
});

/**
 * @route   GET /api/admin/system-users
 * @desc    Get all system users (not admin users)
 * @access  Admin
 */
router.get('/system-users', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Sayfalama parametreleri
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const search = req.query.search as string || '';
    const sortBy = req.query.sortBy as string || 'createdAt';
    const sortOrder = req.query.sortOrder as 'asc' | 'desc' || 'desc';
    const includeInactive = req.query.includeInactive === 'true';
    const role = req.query.role as string || '';
    const companyId = req.query.companyId as string || '';
    const active = req.query.active as string || '';

    // Tüm kullanıcıları getir
    const result = await getAllUsers({
      limit,
      page,
      search,
      sortBy,
      sortOrder,
      includeInactive,
      role,
      companyId,
      active
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to get users'
      });
    }

    return res.json({
      success: true,
      data: {
        users: result.users,
        total: result.total,
        page: result.page,
        totalPages: result.totalPages
      }
    });
  } catch (error) {
    logger.error('[ADMIN] Error getting system users:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get system users'
    });
  }
});

/**
 * @route   GET /api/admin/system-users/:userId
 * @desc    Get system user by ID
 * @access  Admin
 */
router.get('/system-users/:userId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Kullanıcıyı getir
    const result = await getUserById(userId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || 'User not found'
      });
    }

    return res.json({
      success: true,
      data: {
        user: result.data
      }
    });
  } catch (error) {
    logger.error('[ADMIN] Error getting system user:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get system user'
    });
  }
});

/**
 * @route   PUT /api/admin/system-users/:userId
 * @desc    Update system user
 * @access  Admin
 */
router.put('/system-users/:userId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Kullanıcıyı güncelle
    const result = await updateUser(userId, updateData);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || 'Failed to update user'
      });
    }

    return res.json({
      success: true,
      message: 'User updated successfully',
      user: result.data
    });
  } catch (error) {
    logger.error('[ADMIN] Error updating system user:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to update system user'
    });
  }
});

/**
 * @route   DELETE /api/admin/system-users/:userId
 * @desc    Delete system user
 * @access  Admin
 */
router.delete('/system-users/:userId', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Kullanıcıyı sil
    const result = await deleteUser(userId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || 'Failed to delete user'
      });
    }

    return res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    logger.error('[ADMIN] Error deleting system user:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to delete system user'
    });
  }
});

/**
 * @route   GET /api/admin/companies
 * @desc    Tüm şirketleri getir
 * @access  Admin
 */
router.get('/companies', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    // Sayfalama parametreleri
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
    const skip = req.query.skip ? parseInt(req.query.skip as string) : 0;
    const includeInactive = req.query.includeInactive === 'true';
    const sortBy = req.query.sortBy as string || 'createdAt';
    const sortOrder = req.query.sortOrder as 'asc' | 'desc' || 'desc';

    // Şirketleri getir
    const result = await getAllCompanies({
      limit,
      skip,
      includeInactive,
      sortBy,
      sortOrder
    });

    if (!result.success) {
      logger.error(`[ADMIN] Failed to get companies: ${result.message}`);
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Calculate total pages for pagination
    const totalPages = Math.ceil((result.total || 0) / limit);

    return res.json({
      success: true,
      companies: result.companies,
      total: result.total,
      totalPages: totalPages
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/companies: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/companies/:companyId
 * @desc    Şirket detaylarını getir
 * @access  Admin
 */
router.get('/companies/:companyId', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { companyId } = req.params;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required'
      });
    }

    // Şirket detaylarını getir
    const result = await getCompanyById(companyId);

    if (!result.success) {
      logger.error(`[ADMIN] Failed to get company: ${result.message}`);
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      data: {
        company: result.company
      }
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/companies/:companyId: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/companies
 * @desc    Yeni şirket oluştur
 * @access  Admin
 */
router.post('/companies', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const {
      name,
      description,
      status,
      contactEmail,
      contactPhone,
      address,
      website,
      industry,
      size,
      settings
    } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Company name is required'
      });
    }

    const userId = req.admin?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Şirket oluştur
    const result = await createCompany({
      name,
      description,
      status: status || CompanyStatus.ACTIVE,
      contactEmail,
      contactPhone,
      address,
      website,
      // Use type assertion to include industry and size properties
      ...(industry ? { industry } : {}),
      ...(size ? { size } : {}),
      settings,
      createdBy: userId
    } as any);

    if (!result.success) {
      logger.error(`[ADMIN] Failed to create company: ${result.message}`);
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    return res.status(201).json({
      success: true,
      companyId: result.companyId,
      message: 'Company created successfully'
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in POST /api/admin/companies: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   PUT /api/admin/companies/:companyId
 * @desc    Şirket bilgilerini güncelle
 * @access  Admin
 */
router.put('/companies/:companyId', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { companyId } = req.params;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required'
      });
    }

    const {
      name,
      description,
      status,
      contactEmail,
      contactPhone,
      address,
      website,
      industry,
      size,
      settings
    } = req.body;

    // Şirket bilgilerini güncelle
    const result = await updateCompany(companyId, {
      name,
      description,
      status,
      contactEmail,
      contactPhone,
      address,
      website,
      // Use type assertion to include industry and size properties
      ...(industry ? { industry } : {}),
      ...(size ? { size } : {}),
      settings
    } as any);

    if (!result.success) {
      logger.error(`[ADMIN] Failed to update company: ${result.message}`);
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      company: result.company,
      message: 'Company updated successfully'
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in PUT /api/admin/companies/:companyId: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   DELETE /api/admin/companies/:companyId
 * @desc    Şirket sil
 * @access  Admin
 */
router.delete('/companies/:companyId', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { companyId } = req.params;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required'
      });
    }

    // Şirketi sil
    const result = await deleteCompany(companyId);

    if (!result.success) {
      logger.error(`[ADMIN] Failed to delete company: ${result.message}`);
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      message: 'Company deleted successfully'
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in DELETE /api/admin/companies/:companyId: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/companies/:companyId/teams/:teamId
 * @desc    Şirkete takım ekle
 * @access  Admin
 */
router.post('/companies/:companyId/teams/:teamId', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { companyId, teamId } = req.params;

    if (!companyId || !teamId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID and Team ID are required'
      });
    }

    // Takımı şirkete ekle
    const result = await addTeamToCompany(companyId, teamId);

    if (!result.success) {
      logger.error(`[ADMIN] Failed to add team to company: ${result.message}`);
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      message: 'Team added to company successfully'
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in POST /api/admin/companies/:companyId/teams/:teamId: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   DELETE /api/admin/companies/:companyId/teams/:teamId
 * @desc    Şirketten takım çıkar
 * @access  Admin
 */
router.delete('/companies/:companyId/teams/:teamId', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { companyId, teamId } = req.params;

    if (!companyId || !teamId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID and Team ID are required'
      });
    }

    // Takımı şirketten çıkar
    const result = await removeTeamFromCompany(companyId, teamId);

    if (!result.success) {
      logger.error(`[ADMIN] Failed to remove team from company: ${result.message}`);
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      message: 'Team removed from company successfully'
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in DELETE /api/admin/companies/:companyId/teams/:teamId: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/companies/:companyId/users
 * @desc    Get company users
 * @access  Admin
 */
router.get('/companies/:companyId/users', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { companyId } = req.params;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required'
      });
    }

    // Şirket kullanıcılarını getir
    const result = await getCompanyUsers(companyId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || 'Failed to get company users'
      });
    }

    return res.json({
      success: true,
      users: result.users
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/companies/:companyId/users: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/companies/:companyId/teams
 * @desc    Get company teams
 * @access  Admin
 */
router.get('/companies/:companyId/teams', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { companyId } = req.params;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required'
      });
    }

    // Şirket takımlarını getir
    const result = await getCompanyTeams(companyId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || 'Failed to get company teams'
      });
    }

    const teams = result.teams || [];

    return res.json({
      success: true,
      teams
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/companies/:companyId/teams: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/system-users
 * @desc    Create system user
 * @access  Admin
 */
router.post('/system-users', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Ensure MongoDB connection is established
    await ensureMongoDBConnection();

    const {
      email,
      password,
      name,
      role,
      teamRole,
      companyId,
      accountType
    } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // ✅ AccountType validation - frontend'den gelen değerleri kontrol et
    const validAccountTypes = ['user', 'company_owner', 'admin'];
    if (accountType && !validAccountTypes.includes(accountType)) {
      return res.status(400).json({
        success: false,
        error: `Invalid account type. Must be one of: ${validAccountTypes.join(', ')}`
      });
    }

    // Şirketin takımını bul
    const company = await getCompanyById(companyId);

    if (!company.success || !company.company) {
      return res.status(400).json({
        success: false,
        error: 'Company not found'
      });
    }

    // Şirketin takım ID'sini al
    const teamId = company.company.teamId;

    if (!teamId) {
      logger.warn(`[ADMIN] Company ${companyId} has no team, user will be created without team association`);
    }

    // Create user with registerUser function (no transactions)
    const userData = {
      email,
      password,
      name: name || email.split('@')[0],
      accountType: accountType || 'user', // Frontend'den gelen accountType'ı kullan, yoksa 'user'
      teamRole: teamRole, // Takım rolünü doğrudan request'ten al
      companyId,
      teamId: teamId, // Şirketin takımına bağla
      active: true,
      fromAdminPanel: true // Admin panelinden oluşturulduğunu belirt
    };

    if (!teamRole) {
      logger.warn(`[ADMIN] No team role provided in request, using default 'team_member'`);
    }

    // Kullanıcı oluşturduktan sonra team_members koleksiyonuna da kayıt eklenecek

    // Use registerUser which doesn't use transactions
    const result = await registerUser(userData);

    if (!result || !result.success) {
      const errorMsg = result ? result.message : 'Unknown error during user creation';
      logger.error(`[ADMIN] Failed to create user: ${errorMsg}`);
      return res.status(400).json({
        success: false,
        error: errorMsg
      });
    }

    // Kullanıcı başarıyla oluşturuldu, şimdi team_members koleksiyonuna kayıt ekleyelim
    if (teamId && result.data?.user?.id) {
      try {
        // Kullanıcı ve takımın var olduğunu doğrula
        await ensureMongoDBConnection();

        if (!db) {
          logger.error(`[ADMIN] MongoDB connection not available`);
          return res.status(500).json({
            success: false,
            error: 'Database connection not available'
          });
        }

        const usersCollection = db.collection('users');
        const teamsCollection = db.collection('teams');
        const rolePermissionsCollection = db.collection('role_permissions');

        const user = await usersCollection.findOne({ id: result.data.user.id });
        const team = await teamsCollection.findOne({ id: teamId });

        if (!user) {
          logger.error(`[ADMIN] User with ID ${result.data.user.id} not found in database`);
          return res.status(500).json({
            success: false,
            error: 'User created but not found in database'
          });
        }

        if (!team) {
          logger.error(`[ADMIN] Team with ID ${teamId} not found in database`);
          return res.status(500).json({
            success: false,
            error: 'Team not found in database'
          });
        }

        const userId = result.data.user.id;

        // Null kontrolü yap
        if (!teamId || !userId) {
          logger.error(`[ADMIN] Cannot add user to team: teamId or userId is null or empty. teamId=${teamId}, userId=${userId}`);
          return res.status(400).json({
            success: false,
            error: 'Team ID or User ID is missing'
          });
        }

        // ✅ DUPLICATE KONTROL: Kullanıcının bu takımda zaten üyesi olup olmadığını kontrol et
        const existingMember = await rolePermissionsCollection.findOne({
          team_id: teamId,
          user_id: userId,
          status: 'active',
          is_system: false
        });

        if (existingMember) {
          logger.info(`[ADMIN] User ${userId} is already a member of team ${teamId}, skipping team member creation`);
        } else {
          // Tüm kullanıcılar için role_permissions kaydı oluştur
          // Seçilen rol ile role_permissions koleksiyonuna kayıt ekle
          let selectedTeamRole = teamRole || 'viewer';

          // ✅ Eğer kullanıcı company_owner ise, admin rolünü kullan
          if (accountType === 'company_owner') {
            selectedTeamRole = 'admin';
            logger.info(`[ADMIN] Company owner detected, using admin role: ${selectedTeamRole}`);
          }

          const { addTeamMember } = await import('../../services/mongo/teamService.js');
          const teamMemberResult = await addTeamMember(teamId, userId, selectedTeamRole, 'system');

          if (!teamMemberResult.success) {
            logger.warn(`[ADMIN] Failed to add user ${userId} to team ${teamId}: ${teamMemberResult.message}`);
          } else {
            logger.info(`[ADMIN] Successfully added user ${userId} to team ${teamId} with role ${selectedTeamRole} (accountType: ${accountType})`);
          }
        }
      } catch (teamError: any) {
        logger.error(`[ADMIN] Error adding user to team: ${teamError.message}`);
      }
    }

    return res.status(201).json({
      success: true,
      userId: result.data?.user?.id || 'unknown',
      message: 'User created successfully'
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in POST /api/admin/system-users: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Failed to create system user'
    });
  }
});

/**
 * @route   PUT /api/admin/companies/:companyId/remaining
 * @desc    Update company remaining usage
 * @access  Admin
 */
router.put('/companies/:companyId/remaining', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { companyId } = req.params;
    const { runs, runMinutes, generations } = req.body;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required'
      });
    }

    // Kalan kullanım bilgilerini güncelle
    const result = await updateCompanyRemaining(companyId, {
      runs,
      runMinutes,
      generations
    });

    if (result.success) {
      return res.json({
        success: true,
        company: result.company
      });
    } else {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }
  } catch (error: any) {
    logger.error(`[ADMIN] Error updating company remaining: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/settings
 * @desc    Get admin settings
 * @access  Admin
 */
router.get('/settings', authenticateAdmin, async (_req: AdminRequest, res: Response) => {
  try {
    // Mock data for now
    const settings = {
      profile: {
        name: "Admin User",
        email: "<EMAIL>"
      },
      email: {
        smtpServer: "smtp.example.com",
        smtpPort: 587,
        smtpUsername: "user",
        smtpPassword: "password",
        senderEmail: "<EMAIL>",
        senderName: "System Notifications"
      },
      system: {
        apiUrl: "https://api.example.com",
        maxUploadSize: 10,
        allowedFileTypes: "jpg,png,pdf,doc,docx",
        debugMode: false
      },
      database: {
        mongoUri: "mongodb://localhost:27017",
        databaseName: "testdb",
        maxConnections: 100
      }
    };

    return res.json({
      success: true,
      settings
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/settings: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   PUT /api/admin/settings
 * @desc    Update admin settings
 * @access  Admin
 */
router.put('/settings', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const settingsData = req.body;

    // Validate required fields
    if (!settingsData || typeof settingsData !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Invalid settings data'
      });
    }

    // Implement settings update logic
    const result = await updateAdminSettings(settingsData);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || 'Failed to update settings'
      });
    }

    return res.json({
      success: true,
      message: 'Settings updated successfully',
      settings: result.settings
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in PUT /api/admin/settings: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});



/**
 * @route   GET /api/admin/system-settings
 * @desc    Get all system settings
 * @access  Admin
 */
router.get('/system-settings', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    // Get type from query parameter
    const type = req.query.type as SystemSettingType | undefined;

    // Get all settings
    const result = await getAllSystemSettings(type);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.json({
      success: true,
      settings: result.settings
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/system-settings: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/system-settings/:id
 * @desc    Get a system setting by ID
 * @access  Admin
 */
router.get('/system-settings/:id', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Get setting by ID
    const result = await getSystemSettingById(id);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.json({
      success: true,
      data: {
        setting: result.setting
      }
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/system-settings/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/system-settings/type/:type
 * @desc    Get a system setting by type
 * @access  Admin
 */
router.get('/system-settings/type/:type', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { type } = req.params;

    // Validate type
    if (!Object.values(SystemSettingType).includes(type as SystemSettingType)) {
      return res.status(400).json({
        success: false,
        error: `Invalid setting type: ${type}`
      });
    }

    // Get setting by type
    const result = await getSystemSettingByType(type as SystemSettingType);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.json({
      success: true,
      setting: result.setting
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in GET /api/admin/system-settings/type/:type: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/system-settings
 * @desc    Create a new system setting
 * @access  Admin
 */
router.post('/system-settings', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    // Create setting
    const result = await createSystemSetting(req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.status(201).json({
      success: true,
      message: result.message,
      setting: result.setting
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in POST /api/admin/system-settings: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   PUT /api/admin/system-settings/:id
 * @desc    Update a system setting
 * @access  Admin
 */
router.put('/system-settings/:id', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Update setting
    const result = await updateSystemSetting(id, req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.json({
      success: true,
      message: result.message,
      setting: result.setting
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in PUT /api/admin/system-settings/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   DELETE /api/admin/system-settings/:id
 * @desc    Delete a system setting
 * @access  Admin
 */
router.delete('/system-settings/:id', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    // Delete setting
    const result = await deleteSystemSetting(id);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in DELETE /api/admin/system-settings/:id: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/system-settings/test-smtp
 * @desc    Test SMTP configuration
 * @access  Admin
 */
router.post('/system-settings/test-smtp', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    // Validate request
    const request = req.body as TestSMTPRequest;
    if (!request.testEmail) {
      return res.status(400).json({
        success: false,
        error: 'Test email address is required'
      });
    }

    // Test SMTP configuration
    const result = await testSMTPConfig(request);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in POST /api/admin/system-settings/test-smtp: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/system-settings/test-slack
 * @desc    Test Slack configuration
 * @access  Admin
 */
router.post('/system-settings/test-slack', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    // Test Slack configuration
    const result = await testSlackConfig(req.body as TestSlackRequest);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message || result.error
      });
    }

    return res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[ADMIN] Error in POST /api/admin/system-settings/test-slack: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// SECTION: Redis Management Routes
// =================================================================================================

/**
 * @route GET /api/admin/redis/data
 * @description Gets all Redis data for admin viewing. Requires Admin role.
 * @access Private (Admin)
 */
router.get(
  '/redis/data',
  authenticateAdmin,
  async (req: AdminRequest, res: Response) => {
    const adminIdentifier = req.admin?.name || req.admin?.email;
    logger.info(`[ADMIN API] Request received to VIEW ALL REDIS DATA by admin: ${adminIdentifier} (ID: ${req.admin?.id}). IP: ${req.ip}`);
    try {
      const result = await redisAdminService.getAllRedisData();
      if (result.success) {
        logger.info(`[ADMIN API] Redis data fetch successful. Triggered by admin: ${adminIdentifier}`);

        // Debug log to see the structure being sent
        logger.info(`[ADMIN API] Sending response with data structure: ${JSON.stringify({
          success: true,
          data: result.data,
          message: result.message
        }, null, 2)}`);

        return res.json({
          success: true,
          data: result.data,
          message: result.message
        });
      } else {
        logger.error(`[ADMIN API] Redis data fetch failed: ${result.message}. Triggered by admin: ${adminIdentifier}`);
        return res.status(500).json({
          success: false,
          error: result.message || 'Failed to fetch Redis data'
        });
      }
    } catch (error: any) {
      const errorMessage = `[ADMIN API] Unexpected error during Redis data fetch: ${error.message || error}`;
      logger.error(`${errorMessage}. Triggered by admin: ${adminIdentifier}`);
      return res.status(500).json({
        success: false,
        error: errorMessage
      });
    }
  }
);

/**
 * @route DELETE /api/admin/redis/key
 * @description Deletes a specific key from a Redis database. Requires Admin role.
 * @access Private (Admin)
 */
router.delete(
  '/redis/key',
  authenticateAdmin,
  async (req: AdminRequest, res: Response) => {
    const adminIdentifier = req.admin?.name || req.admin?.email;
    const { database, key } = req.body;

    if (typeof database !== 'number' || !key) {
      return res.status(400).json({
        success: false,
        error: 'Database number and key name are required'
      });
    }

    logger.warn(`[ADMIN API] Request received to DELETE REDIS KEY "${key}" from database ${database} by admin: ${adminIdentifier} (ID: ${req.admin?.id}). IP: ${req.ip}`);
    try {
      const result = await redisAdminService.deleteKey(database, key);
      if (result.success) {
        logger.info(`[ADMIN API] Redis key deletion successful. Key: "${key}", Database: ${database}. Triggered by admin: ${adminIdentifier}`);
        return res.json({
          success: true,
          message: result.message
        });
      } else {
        logger.error(`[ADMIN API] Redis key deletion failed. Key: "${key}", Database: ${database}. Triggered by admin: ${adminIdentifier}. Reason: ${result.message}`);
        return res.status(500).json({
          success: false,
          error: result.message || 'Failed to delete Redis key'
        });
      }
    } catch (error: any) {
      const errorMessage = `[ADMIN API] Unexpected error during Redis key deletion: ${error.message || error}`;
      logger.error(`${errorMessage}. Key: "${key}", Database: ${database}. Triggered by admin: ${adminIdentifier}`);
      return res.status(500).json({
        success: false,
        error: errorMessage
      });
    }
  }
);

/**
 * @route POST /api/admin/redis/purge-all
 * @description Purges all data from all Redis databases. Requires Admin role.
 * @access Private (Admin)
 */
router.post(
  '/redis/purge-all',
  authenticateAdmin,
  async (req: AdminRequest, res: Response) => {
    const adminIdentifier = req.admin?.name || req.admin?.email; // Corrected: Use name or email
    logger.warn(`[ADMIN API] Request received to PURGE ALL REDIS DATA by admin: ${adminIdentifier} (ID: ${req.admin?.id}). IP: ${req.ip}`);
    try {
      const result = await redisAdminService.purgeAllData();
      if (result.success) {
        logger.info(`[ADMIN API] Redis PURGE ALL DATA successful. Triggered by admin: ${adminIdentifier}`);
        res.status(200).json({ success: true, message: 'Successfully purged all Redis data.', details: result.message });
      } else {
        logger.error(`[ADMIN API] Redis PURGE ALL DATA failed. Triggered by admin: ${adminIdentifier}. Reason: ${result.message}`);
        res.status(500).json({ success: false, message: 'Failed to purge Redis data.', details: result.message });
      }
    } catch (error: any) {
      logger.error(`[ADMIN API] Critical error during Redis PURGE ALL DATA. Triggered by admin: ${adminIdentifier}. Error: ${error.message}`);
      res.status(500).json({ success: false, message: 'An unexpected error occurred while purging Redis data.', details: error.message });
    }
  }
);

/**
 * @route   POST /api/admin/cleanup-duplicate-team-members
 * @desc    Clean up duplicate team member records
 * @access  Admin
 */
router.post('/cleanup-duplicate-team-members', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const { teamId } = req.body; // Opsiyonel - belirli bir team için

    // TeamService'den cleanup fonksiyonunu import et
    const { cleanupDuplicateTeamMembers } = await import('../../services/mongo/teamService.js');

    // Cleanup işlemini çalıştır
    const result = await cleanupDuplicateTeamMembers(teamId);

    if (result.success) {
      logger.info(`[ADMIN] Duplicate cleanup completed by admin ${req.admin?.id}: ${result.message}`);
      
      return res.json({
        success: true,
        message: result.message,
        removedCount: result.removedCount,
        details: result.details
      });
    } else {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }
  } catch (error: any) {
    logger.error(`[ADMIN] Error in cleanup duplicate team members: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Failed to cleanup duplicate team members'
    });
  }
});

export default router;
