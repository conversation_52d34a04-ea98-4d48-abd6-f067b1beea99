"use client"

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ExternalLink, Bug, Check, LoaderCircle, Plus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { RunReport } from '@/hooks/useRunReports';
import { getJiraConfig, createJiraIssue } from '@/lib/api/plugin-api';

interface JiraIntegrationProps {
  report: RunReport | null;
}

interface JiraConfig {
  url: string;
  email: string;
  projectsData: Array<{ id: string; key: string; name: string }>;
  issueTypesData: Array<{ id: string; name: string }>;
}

export function RunReportJiraIntegration({ report }: JiraIntegrationProps) {
  const { toast } = useToast();
  const [jiraConfig, setJiraConfig] = useState<JiraConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [isCreatingIssue, setIsCreatingIssue] = useState(false);
  
  const [issueForm, setIssueForm] = useState({
    projectKey: '',
    issueTypeId: '',
    summary: '',
    description: ''
  });

  useEffect(() => {
    fetchJiraConfig();
  }, []);

  useEffect(() => {
    if (report && jiraConfig) {
      // Auto-populate form when report or config changes
      setIssueForm({
        projectKey: jiraConfig.projectsData[0]?.key || '',
        issueTypeId: jiraConfig.issueTypesData[0]?.id || '',
        summary: `Test Failure: ${report.name || 'Unknown Test'}`,
        description: generateIssueDescription(report)
      });
    }
  }, [report, jiraConfig]);

  const fetchJiraConfig = async () => {
    setIsLoadingConfig(true);
    try {
      const response = await getJiraConfig();
      if (response.success && response.data) {
        setJiraConfig(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch Jira config:', error);
    } finally {
      setIsLoadingConfig(false);
    }
  };

  const generateIssueDescription = (report: RunReport): string => {
    const failedScenarios = report.scenarioStatuses?.filter(s => s.status === 'failed') || [];
    const totalScenarios = report.scenarioStatuses?.length || 0;
    
    let description = `## Test Execution Report\n\n`;
    description += `**Test Run:** ${report.name || 'Unknown'}\n`;
    description += `**Run ID:** ${report.runId || 'N/A'}\n`;
    description += `**Status:** ${report.status || 'Unknown'}\n`;
    description += `**Created:** ${report.createdAt ? new Date(report.createdAt).toLocaleString() : 'Unknown'}\n`;
    if (report.startedAt) {
      description += `**Started:** ${new Date(report.startedAt).toLocaleString()}\n`;
    }
    if (report.completedAt) {
      description += `**Completed:** ${new Date(report.completedAt).toLocaleString()}\n`;
    }
    description += `**Total Scenarios:** ${totalScenarios}\n`;
    description += `**Failed Scenarios:** ${failedScenarios.length}\n\n`;
    
    if (failedScenarios.length > 0) {
      description += `## Failed Scenarios\n\n`;
      failedScenarios.forEach((scenario, index) => {
        description += `### Scenario ${index + 1}\n`;
        description += `**ID:** ${scenario.scenarioId}\n`;
        description += `**Status:** ${scenario.status}\n`;
        if (scenario.startedAt) {
          description += `**Started:** ${new Date(scenario.startedAt).toLocaleString()}\n`;
        }
        if (scenario.completedAt) {
          description += `**Completed:** ${new Date(scenario.completedAt).toLocaleString()}\n`;
        }
        description += `\n`;
      });
    }
    
    description += `---\n*This issue was automatically created from test execution report.*`;
    
    return description;
  };

  const handleCreateIssue = async () => {
    if (!report || !jiraConfig) return;

    if (!issueForm.projectKey || !issueForm.issueTypeId || !issueForm.summary.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Project, Issue Type, and Summary).",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingIssue(true);
    try {
      const response = await createJiraIssue({
        reportId: report.id || '',
        projectKey: issueForm.projectKey,
        issueTypeId: issueForm.issueTypeId,
        summary: issueForm.summary,
        description: issueForm.description
      });

      if (response.success) {
        toast({
          title: "Issue Created",
          description: `Jira issue ${response.data?.key || ''} created successfully.`,
        });
        setShowCreateDialog(false);
        
                 // Reset form
         setIssueForm({
           projectKey: jiraConfig.projectsData[0]?.key || '',
           issueTypeId: jiraConfig.issueTypesData[0]?.id || '',
           summary: `Test Failure: ${report.name || 'Unknown Test'}`,
           description: generateIssueDescription(report)
         });
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to create Jira issue.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred while creating the issue.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingIssue(false);
    }
  };

  if (isLoadingConfig) {
    return (
      <Card className="overflow-hidden border border-gray-200 dark:border-gray-800">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <Bug className="h-5 w-5 mr-2 text-blue-500" />
            Jira Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4">
            <LoaderCircle className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2 text-sm text-gray-500">Loading Jira configuration...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!jiraConfig) {
    return (
      <Card className="overflow-hidden border border-gray-200 dark:border-gray-800">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <Bug className="h-5 w-5 mr-2 text-blue-500" />
            Jira Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-sm text-gray-500 mb-3">Jira integration is not configured.</p>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => window.open('/plugins/jira', '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              Configure Jira
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all shadow-sm hover:shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <Bug className="h-5 w-5 mr-2 text-blue-500" />
          Jira Integration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2 mb-3">
          <div className="bg-green-100 dark:bg-green-900/30 p-1 rounded-full">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
          </div>
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Jira integration is active
          </span>
        </div>
        
        <div className="space-y-2">
          <p className="text-xs text-gray-500">
            Connected to: {jiraConfig.url}
          </p>
          <p className="text-xs text-gray-500">
            Projects: {jiraConfig.projectsData.map(p => p.name).join(', ')}
          </p>
        </div>
        
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full mt-3 bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800 dark:hover:bg-blue-900/40"
            >
              <Plus className="h-4 w-4 mr-1" />
              Create Jira Issue
            </Button>
          </DialogTrigger>
          
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Create Jira Issue</DialogTitle>
              <DialogDescription>
                Create a new Jira issue from this test report.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="project">Project *</Label>
                <Select 
                  value={issueForm.projectKey} 
                  onValueChange={(value) => setIssueForm({ ...issueForm, projectKey: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select project" />
                  </SelectTrigger>
                  <SelectContent>
                    {jiraConfig.projectsData.map((project) => (
                      <SelectItem key={project.key} value={project.key}>
                        {project.name} ({project.key})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="issueType">Issue Type *</Label>
                <Select 
                  value={issueForm.issueTypeId} 
                  onValueChange={(value) => setIssueForm({ ...issueForm, issueTypeId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select issue type" />
                  </SelectTrigger>
                  <SelectContent>
                    {jiraConfig.issueTypesData.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="summary">Summary *</Label>
                <Input
                  id="summary"
                  value={issueForm.summary}
                  onChange={(e) => setIssueForm({ ...issueForm, summary: e.target.value })}
                  placeholder="Brief description of the issue"
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={issueForm.description}
                  onChange={(e) => setIssueForm({ ...issueForm, description: e.target.value })}
                  placeholder="Detailed description of the issue"
                  rows={6}
                  className="resize-none"
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateIssue} disabled={isCreatingIssue}>
                {isCreatingIssue ? (
                  <>
                    <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                    Creating...
                  </>
                ) : (
                  "Create Issue"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
} 