import useSWR from 'swr';
import { useMemo } from 'react';
import { runDetailFetcher } from '@/lib/fetchers/runFetcher';
import { isRunActive, shouldRefresh } from '@/lib/utils/run-logic';
import { toast } from '@/lib/utils/toast-utils';

export interface UseRunDetailOptions {
  runId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface RunDetailData {
  run: any;
  tests: any[];
  reports: any[];
  stepProgressSummary: any;
}

/**
 * SWR-based useRunDetail hook
 * Simplified and focused on data fetching with automatic refresh for active runs
 */
export function useRunDetail({ 
  runId, 
  autoRefresh = true, 
  refreshInterval 
}: UseRunDetailOptions) {
  
  // Use SWR for data fetching
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate
  } = useSWR(
    runId ? ['run-detail', runId] : null,
    () => runDetailFetcher(runId),
    {
      refreshInterval: (data) => {
        if (!autoRefresh) return 0;
        
        // Use custom refresh interval if provided
        if (refreshInterval !== undefined) {
          return refreshInterval;
        }
        
        // Use shouldRefresh utility to determine refresh interval
        return shouldRefresh(data);
      },
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 2000,
      errorRetryCount: 3,
      onError: (err) => {
        toast.error(err.message || 'Failed to fetch run details');
      }
    }
  );

  // Derived data using useMemo for performance
  const derivedData = useMemo(() => {
    if (!data) {
      return {
        run: null,
        tests: [],
        reports: [],
        stepProgressSummary: null,
        isActiveRun: false,
        isCompleted: false
      };
    }

    const { run, tests, reports, stepProgressSummary } = data;
    
    return {
      run,
      tests,
      reports,
      stepProgressSummary,
      isActiveRun: isRunActive(run, tests),
      isCompleted: !isRunActive(run, tests)
    };
  }, [data]);

  // Helper functions
  const refresh = () => {
    return mutate();
  };

  const refreshing = isValidating && !isLoading;

  return {
    // Raw data
    data,
    
    // Loading states
    loading: isLoading,
    refreshing,
    error: error?.message || null,
    
    // Derived data
    ...derivedData,
    
    // Actions
    refresh,
    
    // SWR utilities
    mutate
  };
}
