/**
 * Configuration Manager - Backward Compatibility Facade
 *
 * Maintains backward compatibility while using the new ConfigurationService
 * under the hood. This allows existing code to continue working without changes.
 */

import { NodeConfig } from '../../models/types.js';
import { ConfigChangeEvent, ConfigWatcher, ConfigSource } from '../types/config-types.js';
import { ConfigurationService, IConfigurationService } from '../services/configuration-service.js';
import { logger } from '../../utils/logger.js';

/**
 * Configuration Manager - Backward Compatibility Facade
 *
 * Provides the same API as the old singleton while using ConfigurationService internally.
 * This maintains backward compatibility for existing code.
 */
export class ConfigurationManager {
  private static instance: ConfigurationManager;
  private configurationService: IConfigurationService;

  /**
   * Private constructor to enforce singleton pattern (backward compatibility)
   */
  private constructor() {
    this.configurationService = new ConfigurationService({
      enableWatchers: true,
      enableHistory: true,
      maxHistorySize: 100,
      enableLogging: true
    });
  }

  /**
   * Gets the singleton instance of ConfigurationManager (backward compatibility)
   * @deprecated Use ConfigurationService directly or dependency injection instead
   * @returns ConfigurationManager instance
   */
  public static getInstance(): ConfigurationManager {
    logger.warn('ConfigurationManager.getInstance() is deprecated. Use ConfigurationService directly or dependency injection instead.');

    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  /**
   * Initialize the configuration manager with a configuration object (backward compatibility)
   * @param config Initial configuration
   */
  public initialize(config: NodeConfig): void {
    this.configurationService.initialize(config);
  }

  /**
   * Get the current configuration (backward compatibility)
   * @returns Current configuration object
   * @throws Error if configuration is not initialized
   */
  public getConfig(): NodeConfig {
    return this.configurationService.getConfig();
  }

  /**
   * Get a specific configuration value (backward compatibility)
   * @param key Configuration key (supports dot notation)
   * @returns Configuration value
   */
  public get<T>(key: string): T | undefined {
    return this.configurationService.get<T>(key);
  }

  /**
   * Set a configuration value (backward compatibility)
   * @param key Configuration key (supports dot notation)
   * @param value New value
   * @param source Source of the configuration change
   */
  public set<T>(key: string, value: T, source: ConfigSource = 'runtime'): void {
    this.configurationService.set(key, value, source);
  }

  /**
   * Update multiple configuration values (backward compatibility)
   * @param updates Object with key-value pairs to update
   * @param source Source of the configuration changes
   */
  public updateMultiple(updates: Record<string, any>, source: ConfigSource = 'runtime'): void {
    this.configurationService.updateMultiple(updates, source);
  }

  /**
   * Watch for changes to a specific configuration key (backward compatibility)
   * @param key Configuration key to watch
   * @param callback Callback function to call when value changes
   */
  public watch<T>(key: string, callback: ConfigWatcher<T>): void {
    this.configurationService.watch(key, callback);
  }

  /**
   * Remove a watcher for a specific configuration key (backward compatibility)
   * @param key Configuration key
   * @param callback Callback function to remove
   */
  public unwatch<T>(key: string, callback: ConfigWatcher<T>): void {
    this.configurationService.unwatch(key, callback);
  }

  /**
   * Get configuration change history (backward compatibility)
   * @param limit Maximum number of history entries to return
   * @returns Array of configuration change events
   */
  public getHistory(limit?: number): ConfigChangeEvent[] {
    return this.configurationService.getHistory(limit);
  }

  /**
   * Clear configuration change history (backward compatibility)
   */
  public clearHistory(): void {
    this.configurationService.clearHistory();
  }

  /**
   * Validate the current configuration (backward compatibility)
   * @returns Validation result
   */
  public validate(): { isValid: boolean; errors: string[] } {
    return this.configurationService.validate();
  }

  /**
   * Reset configuration to default values (backward compatibility)
   */
  public reset(): void {
    this.configurationService.reset();
  }

}
