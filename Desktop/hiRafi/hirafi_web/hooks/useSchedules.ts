/**
 * Schedule Hook
 * Provides functionality for managing schedules
 */

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import {
  getSchedules,
  getScheduleById,
  createSchedule,
  updateSchedule,
  deleteSchedule,
  updateScheduleStatus,
  getRunsForSchedule
} from '@/lib/api/scheduleApi';

export const useSchedules = () => {
  const [schedules, setSchedules] = useState<any[]>([]);
  const [schedule, setSchedule] = useState<any>(null);
  const [runs, setRuns] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  /**
   * Fetch all schedules
   */
  const fetchSchedules = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getSchedules();
      if (response.success) {
        // <PERSON><PERSON>t yapısına göre schedules verisini al
        const schedulesData = response.data?.schedules || response.schedules || [];
        setSchedules(schedulesData);
      } else {
        setError(response.error || 'Failed to fetch schedules');
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch schedules',
          variant: 'destructive',
        });
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast({
        title: 'Error',
        description: err.message || 'An error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  /**
   * Fetch a schedule by ID
   */
  const fetchScheduleById = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await getScheduleById(id);
      console.log("API Response for schedule:", response);

      if (response.success) {
        // Yanıt yapısına göre schedule verisini al
        const scheduleData = response.data?.schedule || response.schedule || null;
        setSchedule(scheduleData);
        return response; // Tüm yanıtı döndür
      } else {
        setError(response.error || 'Failed to fetch schedule');
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch schedule',
          variant: 'destructive',
        });
        return response; // Hata durumunda da yanıtı döndür
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast({
        title: 'Error',
        description: err.message || 'An error occurred',
        variant: 'destructive',
      });
      return { success: false, error: err.message || 'An error occurred' };
    } finally {
      setLoading(false);
    }
  }, [toast]);

  /**
   * Create a new schedule
   */
  const handleCreateSchedule = useCallback(async (scheduleData: any) => {
    setLoading(true);
    setError(null);
    try {
      const response = await createSchedule(scheduleData);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Schedule created successfully',
        });
        return { success: true, scheduleId: response.scheduleId };
      } else {
        setError(response.error || 'Failed to create schedule');
        toast({
          title: 'Error',
          description: response.error || 'Failed to create schedule',
          variant: 'destructive',
        });
        return { success: false, error: response.error };
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast({
        title: 'Error',
        description: err.message || 'An error occurred',
        variant: 'destructive',
      });
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [toast]);

  /**
   * Update a schedule
   */
  const handleUpdateSchedule = useCallback(async (id: string, scheduleData: any) => {
    setLoading(true);
    setError(null);
    try {
      const response = await updateSchedule(id, scheduleData);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Schedule updated successfully',
        });
        return { success: true };
      } else {
        setError(response.error || 'Failed to update schedule');
        toast({
          title: 'Error',
          description: response.error || 'Failed to update schedule',
          variant: 'destructive',
        });
        return { success: false, error: response.error };
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast({
        title: 'Error',
        description: err.message || 'An error occurred',
        variant: 'destructive',
      });
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [toast]);

  /**
   * Delete a schedule
   */
  const handleDeleteSchedule = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await deleteSchedule(id);
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Schedule deleted successfully',
        });
        return { success: true };
      } else {
        setError(response.error || 'Failed to delete schedule');
        toast({
          title: 'Error',
          description: response.error || 'Failed to delete schedule',
          variant: 'destructive',
        });
        return { success: false, error: response.error };
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast({
        title: 'Error',
        description: err.message || 'An error occurred',
        variant: 'destructive',
      });
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [toast]);

  /**
   * Update schedule status
   */
  const handleUpdateStatus = useCallback(async (id: string, status: 'active' | 'paused') => {
    setLoading(true);
    setError(null);
    try {
      const response = await updateScheduleStatus(id, status);
      if (response.success) {
        toast({
          title: 'Success',
          description: `Schedule ${status === 'active' ? 'activated' : 'paused'} successfully`,
        });
        return { success: true };
      } else {
        setError(response.error || 'Failed to update schedule status');
        toast({
          title: 'Error',
          description: response.error || 'Failed to update schedule status',
          variant: 'destructive',
        });
        return { success: false, error: response.error };
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast({
        title: 'Error',
        description: err.message || 'An error occurred',
        variant: 'destructive',
      });
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [toast]);

  /**
   * Fetch runs for schedule selection
   */
  const fetchRunsForSchedule = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getRunsForSchedule();
      if (response.success) {
        // Yanıt yapısına göre runs verisini al - data wrapper'ı kontrol et
        const runsData = response.data?.runs || response.runs || [];
        setRuns(runsData);
      } else {
        setError(response.error || 'Failed to fetch runs');
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch runs',
          variant: 'destructive',
        });
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast({
        title: 'Error',
        description: err.message || 'An error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    schedules,
    schedule,
    runs,
    loading,
    error,
    fetchSchedules,
    fetchScheduleById,
    createSchedule: handleCreateSchedule,
    updateSchedule: handleUpdateSchedule,
    deleteSchedule: handleDeleteSchedule,
    updateScheduleStatus: handleUpdateStatus,
    fetchRunsForSchedule,
  };
};
