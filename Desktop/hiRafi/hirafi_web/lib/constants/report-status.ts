/**
 * Report status constants and utilities
 * Standardized status values for consistent filtering across components
 */

export interface StatusOption {
  value: string
  label: string
  icon?: string
  color?: string
}

/**
 * Standard status options for report filtering
 * These values should match what determineRunStatus() returns
 */
export const REPORT_STATUS_OPTIONS: StatusOption[] = [
  { value: "all", label: "All Statuses", color: "gray" },
  { value: "running", label: "Running", icon: "●", color: "blue" },
  { value: "passed", label: "Passed", icon: "✓", color: "green" },
  { value: "failed", label: "Failed", icon: "✕", color: "red" },
  { value: "partial", label: "Partial", icon: "◐", color: "orange" },
  { value: "stopped", label: "Stopped", icon: "■", color: "yellow" },
]

/**
 * Map determineRunStatus() output to filter values
 * This ensures compatibility between status determination and filtering
 */
export const STATUS_MAPPING: Record<string, string> = {
  // determineRunStatus() outputs -> filter values
  "success": "passed",      // Key mapping: success -> passed
  "completed": "passed",    // Alternative success status
  "passed": "passed",       // Direct mapping
  "failed": "failed",       // Direct mapping
  "partial": "partial",     // Direct mapping
  "stopped": "stopped",     // Direct mapping
  "running": "running",     // Direct mapping
  "queued": "running",      // Treat queued as running for filtering
  "pending": "running",     // Treat pending as running for filtering
}

/**
 * Get the filter value for a given status
 * @param status Status from determineRunStatus() or API
 * @returns Standardized filter value
 */
export function getFilterValueForStatus(status: string): string {
  if (!status) return "unknown"
  
  const normalizedStatus = status.toLowerCase()
  return STATUS_MAPPING[normalizedStatus] || normalizedStatus
}

/**
 * Check if a report matches the selected status filter
 * @param reportStatus Status from determineRunStatus()
 * @param filterValue Selected filter value
 * @returns True if report matches filter
 */
export function matchesStatusFilter(reportStatus: string, filterValue: string): boolean {
  if (filterValue === "all") return true
  
  const mappedStatus = getFilterValueForStatus(reportStatus)
  return mappedStatus === filterValue
}

/**
 * Get status option by value
 * @param value Status filter value
 * @returns Status option or undefined
 */
export function getStatusOption(value: string): StatusOption | undefined {
  return REPORT_STATUS_OPTIONS.find(option => option.value === value)
}
