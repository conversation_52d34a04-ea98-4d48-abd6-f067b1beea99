/**
 * Auth API
 *
 * Kullanıcı kimlik doğrulama ile ilgili API isteklerini yönetir.
 */

import { get, post, put } from './fetch-wrapper';
import { AUTH_ENDPOINTS } from './constants';

/**
 * Kullanıcı girişi yapar
 *
 * @param email Kullanıcı e-posta adresi
 * @param password Kullanıcı şifresi
 */
export async function login(email: string, password: string) {
  return post(AUTH_ENDPOINTS.LOGIN, { email, password });
}

/**
 * Kullanıcı kaydı yapar
 *
 * @param userData Kullanıcı verileri
 */
export async function register(userData: {
  email: string;
  password: string;
  name: string;
  accountType?: string;
  role?: string;
}) {
  return post(AUTH_ENDPOINTS.REGISTER, userData);
}

/**
 * Token doğrulaması yapar
 *
 * @param token JWT token
 */
export async function validateToken(token: string) {
  const response = await post(AUTH_ENDPOINTS.VERIFY, {}, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  // Backend response format: { success: true, data: { user: {...} } }
  // fetch-wrapper puts this in data wrapper, so we need to extract it
  if (response.success && response.data) {
    return {
      ...response,
      user: response.data.user || response.user,
      data: response.data
    };
  }

  return response;
}

/**
 * Şifre sıfırlama isteği gönderir
 *
 * @param email Kullanıcı e-posta adresi
 */
export async function resetPassword(email: string) {
  return post(AUTH_ENDPOINTS.RESET_PASSWORD, { email });
}

/**
 * Şifre değiştirir
 *
 * @param currentPassword Mevcut şifre
 * @param newPassword Yeni şifre
 */
export async function changePassword(currentPassword: string, newPassword: string) {
  return put(AUTH_ENDPOINTS.CHANGE_PASSWORD, { currentPassword, newPassword });
}

/**
 * Kullanıcı profilini getirir
 */
export async function getProfile() {
  const response = await get(AUTH_ENDPOINTS.PROFILE);

  // Backend response format: { success: true, data: { user: {...} } }
  // fetch-wrapper puts this in data wrapper, so we need to extract it
  if (response.success && response.data) {
    return {
      ...response,
      user: response.data.user || response.user,
      data: response.data
    };
  }

  return response;
}
