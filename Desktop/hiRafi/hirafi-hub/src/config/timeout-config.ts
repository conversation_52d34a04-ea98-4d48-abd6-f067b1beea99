/**
 * Centralized timeout configuration for the hirafi system
 * This ensures consistent timeout values across all components
 */

import { logger } from '../utils/logger.js';

export interface TimeoutConfig {
  // Node health and heartbeat timeouts
  node: {
    heartbeatInterval: number;        // How often nodes send heartbeats
    inactiveTimeout: number;          // Mark node as inactive after this time
    removalTimeout: number;           // Remove node from registry after this time
    busyTimeout: number;              // Timeout for BUSY nodes without heartbeat
    healthCheckInterval: number;      // How often to check node health
  };

  // Test execution timeouts
  test: {
    maxExecutionTime: number;         // Maximum time for a single test
    lockExtensionInterval: number;    // How often to extend BullMQ locks
    lockDuration: number;             // BullMQ lock duration
    stuckDetectionThreshold: number;  // When to consider a test stuck
    stepTimeout: number;              // Maximum time for a single step
    stepTimeoutMultiplier: number;    // Multiplier for average step duration
  };

  // WebSocket and connection timeouts
  connection: {
    websocketPingInterval: number;    // WebSocket ping interval
    websocketPongTimeout: number;     // WebSocket pong timeout
    reconnectDelay: number;           // Delay before reconnection attempts
    maxReconnectAttempts: number;     // Maximum reconnection attempts
    messageRetryInterval: number;     // Interval for retrying failed messages
    messageRetryMaxAttempts: number;  // Maximum message retry attempts
  };

  // Result processing timeouts
  result: {
    processingLockTimeout: number;    // Lock timeout for result processing
    resultExpiryTime: number;         // How long to keep processed results
    cleanupInterval: number;          // How often to clean up old results
  };

  // Monitoring and recovery timeouts
  monitoring: {
    stuckMonitoringInterval: number;  // How often to check for stuck tests
    criticalStuckThreshold: number;   // Always terminate after this time
    nodeUnresponsiveThreshold: number; // Node unresponsive threshold
    recoveryGracePeriod: number;      // Grace period for recovery operations
  };
}

/**
 * Get environment variable as integer with default value
 */
function getEnvInt(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (value === undefined || value === '') {
    return defaultValue;
  }
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    logger.warn(`TimeoutConfig: Invalid integer value for ${key}: ${value}, using default: ${defaultValue}`);
    return defaultValue;
  }
  return parsed;
}

/**
 * Default timeout configuration
 */
export const defaultTimeoutConfig: TimeoutConfig = {
  node: {
    heartbeatInterval: getEnvInt('NODE_HEARTBEAT_INTERVAL', 30000),        // 30 seconds
    inactiveTimeout: getEnvInt('NODE_INACTIVE_TIMEOUT', 120000),           // 2 minutes
    removalTimeout: getEnvInt('NODE_REMOVAL_TIMEOUT', 300000),             // 5 minutes
    busyTimeout: getEnvInt('NODE_BUSY_TIMEOUT', 180000),                   // 3 minutes
    healthCheckInterval: getEnvInt('NODE_HEALTH_CHECK_INTERVAL', 15000),   // 15 seconds
  },

  test: {
    maxExecutionTime: getEnvInt('TEST_MAX_EXECUTION_TIME', 2400000),       // 40 minutes - increased for longer tests with many steps
    lockExtensionInterval: getEnvInt('TEST_LOCK_EXTENSION_INTERVAL', 30000), // 30 seconds
    lockDuration: getEnvInt('TEST_LOCK_DURATION', 1200000),               // 20 minutes - increased for longer tests
    stuckDetectionThreshold: getEnvInt('TEST_STUCK_THRESHOLD', 1200000),  // 20 minutes - increased for longer tests
    stepTimeout: getEnvInt('TEST_STEP_TIMEOUT', 900000),                  // 15 minutes
    stepTimeoutMultiplier: getEnvInt('TEST_STEP_TIMEOUT_MULTIPLIER', 5),  // 5x average
  },

  connection: {
    websocketPingInterval: getEnvInt('WS_PING_INTERVAL', 30000),          // 30 seconds
    websocketPongTimeout: getEnvInt('WS_PONG_TIMEOUT', 60000),            // 60 seconds
    reconnectDelay: getEnvInt('WS_RECONNECT_DELAY', 5000),                // 5 seconds
    maxReconnectAttempts: getEnvInt('WS_MAX_RECONNECT_ATTEMPTS', 10),     // 10 attempts
    messageRetryInterval: getEnvInt('MESSAGE_RETRY_INTERVAL', 5000),      // 5 seconds
    messageRetryMaxAttempts: getEnvInt('MESSAGE_RETRY_MAX_ATTEMPTS', 3),  // 3 attempts
  },

  result: {
    processingLockTimeout: getEnvInt('RESULT_LOCK_TIMEOUT', 300000),      // 5 minutes
    resultExpiryTime: getEnvInt('RESULT_EXPIRY_TIME', 300000),            // 5 minutes
    cleanupInterval: getEnvInt('RESULT_CLEANUP_INTERVAL', 60000),         // 1 minute
  },

  monitoring: {
    stuckMonitoringInterval: getEnvInt('STUCK_MONITORING_INTERVAL', 15000), // 15 seconds
    criticalStuckThreshold: getEnvInt('CRITICAL_STUCK_THRESHOLD', 2400000), // 40 minutes - increased for longer tests with many steps
    nodeUnresponsiveThreshold: getEnvInt('NODE_UNRESPONSIVE_THRESHOLD', 300000), // 5 minutes
    recoveryGracePeriod: getEnvInt('RECOVERY_GRACE_PERIOD', 10000),        // 10 seconds
  },
};

/**
 * Get the current timeout configuration
 */
export function getTimeoutConfig(): TimeoutConfig {
  return defaultTimeoutConfig;
}

/**
 * Validate timeout configuration for consistency
 */
export function validateTimeoutConfig(config: TimeoutConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Node timeouts should be in ascending order
  if (config.node.heartbeatInterval >= config.node.inactiveTimeout) {
    errors.push('Node heartbeat interval should be less than inactive timeout');
  }
  if (config.node.inactiveTimeout >= config.node.removalTimeout) {
    errors.push('Node inactive timeout should be less than removal timeout');
  }
  if (config.node.busyTimeout >= config.node.inactiveTimeout) {
    errors.push('Node busy timeout should be less than inactive timeout');
  }

  // Test timeouts should be reasonable
  if (config.test.lockExtensionInterval >= config.test.lockDuration) {
    errors.push('Test lock extension interval should be less than lock duration');
  }
  if (config.test.stuckDetectionThreshold >= config.test.maxExecutionTime) {
    errors.push('Test stuck detection threshold should be less than max execution time');
  }

  // Connection timeouts should be reasonable
  if (config.connection.websocketPingInterval >= config.connection.websocketPongTimeout) {
    errors.push('WebSocket ping interval should be less than pong timeout');
  }

  // Monitoring intervals should be reasonable
  if (config.monitoring.stuckMonitoringInterval >= config.monitoring.criticalStuckThreshold) {
    errors.push('Stuck monitoring interval should be less than critical stuck threshold');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Log timeout configuration on startup
 */
export function logTimeoutConfig(): void {
  const config = getTimeoutConfig();
  const validation = validateTimeoutConfig(config);

  logger.info('TimeoutConfig: Current timeout configuration:');
  logger.info(`  Node heartbeat interval: ${config.node.heartbeatInterval}ms`);
  logger.info(`  Node inactive timeout: ${config.node.inactiveTimeout}ms`);
  logger.info(`  Node removal timeout: ${config.node.removalTimeout}ms`);
  logger.info(`  Node busy timeout: ${config.node.busyTimeout}ms`);
  logger.info(`  Test max execution time: ${config.test.maxExecutionTime}ms`);
  logger.info(`  Test lock extension interval: ${config.test.lockExtensionInterval}ms`);
  logger.info(`  Test stuck detection threshold: ${config.test.stuckDetectionThreshold}ms`);
  logger.info(`  Critical stuck threshold: ${config.monitoring.criticalStuckThreshold}ms`);

  if (!validation.valid) {
    logger.error('TimeoutConfig: Configuration validation failed:');
    validation.errors.forEach(error => logger.error(`  - ${error}`));
  } else {
    logger.info('TimeoutConfig: Configuration validation passed');
  }
}
