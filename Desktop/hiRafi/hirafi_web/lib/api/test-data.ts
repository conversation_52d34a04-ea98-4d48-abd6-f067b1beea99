/**
 * Test Data API Service
 * API functions for test data management
 */

import { get, post, put, del } from './fetch-wrapper';
import { API_BASE_URL, TEST_DATA_ENDPOINTS, GENERATE_DATA_ENDPOINTS, EXCEL_ENDPOINTS } from './constants';
import type {
  DataSource,
  CreateDataSetRequest,
  UpdateDataSetRequest,
  CreateDataSourceRequest,
  UpdateDataSourceRequest,
  CreateDataEnvironmentRequest,
  UpdateDataEnvironmentRequest,
  DataSetQueryOptions,
  DataSourceQueryOptions,
  DataEnvironmentQueryOptions,
} from '../../types/test-data';
import { toast } from "sonner";

/**
 * Data Sets API Functions
 */

export const dataSetApi = {
  /**
   * Get all data sets
   */
  getAll: async (options?: DataSetQueryOptions) => {
    const params: any = {};

    if (options?.limit) params.limit = options.limit;
    if (options?.skip) params.skip = options.skip;
    if (options?.search) params.search = options.search;
    if (options?.environment && options.environment !== 'all') params.environment = options.environment;
    if (options?.tags?.length) params.tags = options.tags.join(',');

    return get(TEST_DATA_ENDPOINTS.DATA_SETS.GET_ALL, { params });
  },

  /**
   * Get lightweight data set information with only variable names for dropdown performance
   */
  getKeys: async (options?: DataSetQueryOptions) => {
    const params: any = {};

    if (options?.limit) params.limit = options.limit;
    if (options?.skip) params.skip = options.skip;
    if (options?.search) params.search = options.search;
    if (options?.environment && options.environment !== 'all') params.environment = options.environment;
    if (options?.tags?.length) params.tags = options.tags.join(',');

    return get(TEST_DATA_ENDPOINTS.DATA_SETS.GET_KEYS, { params });
  },

  /**
   * Get data set by ID
   */
  getById: async (id: string) => {
    return get(TEST_DATA_ENDPOINTS.DATA_SETS.GET_BY_ID(id));
  },

  /**
   * Create new data set
   */
  create: async (data: CreateDataSetRequest) => {
    return post(TEST_DATA_ENDPOINTS.DATA_SETS.CREATE, data, {
      showSuccessToast: true,
      successMessage: 'Data set created successfully'
    });
  },

  /**
   * Update data set
   */
  update: async (id: string, data: UpdateDataSetRequest) => {
    return put(TEST_DATA_ENDPOINTS.DATA_SETS.UPDATE(id), data, {
      showSuccessToast: true,
      successMessage: 'Data set updated successfully'
    });
  },

  /**
   * Delete data set
   */
  delete: async (id: string) => {
    return del(TEST_DATA_ENDPOINTS.DATA_SETS.DELETE(id), {
      showSuccessToast: true,
      successMessage: 'Data set deleted successfully'
    });
  },
};

/**
 * Data Sources API Functions
 */

export const dataSourceApi = {
  /**
   * Get all data sources
   */
  getAll: async (options?: DataSourceQueryOptions) => {
    const params: any = {};

    if (options?.limit) params.limit = options.limit;
    if (options?.skip) params.skip = options.skip;
    if (options?.search) params.search = options.search;
    if (options?.type) params.type = options.type;
    if (options?.isActive !== undefined) params.isActive = options.isActive;

    return get(TEST_DATA_ENDPOINTS.DATA_SOURCES.GET_ALL, { params });
  },

  /**
   * Get data source by ID
   */
  getById: async (id: string) => {
    return get(TEST_DATA_ENDPOINTS.DATA_SOURCES.GET_BY_ID(id));
  },

  /**
   * Create new data source
   */
  create: async (data: CreateDataSourceRequest) => {
    return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.CREATE, data, {
      showSuccessToast: true,
      successMessage: 'Data source created successfully'
    });
  },

  /**
   * Update data source
   */
  update: async (id: string, data: UpdateDataSourceRequest) => {
    return put(TEST_DATA_ENDPOINTS.DATA_SOURCES.UPDATE(id), data, {
      showSuccessToast: true,
      successMessage: 'Data source updated successfully'
    });
  },

  /**
   * Delete data source
   */
  delete: async (id: string) => {
    return del(TEST_DATA_ENDPOINTS.DATA_SOURCES.DELETE(id), {
      showSuccessToast: true,
      successMessage: 'Data source deleted successfully'
    });
  },
};

/**
 * Data Environments API Functions
 */

export const dataEnvironmentApi = {
  /**
   * Get all data environments
   */
  getAll: async (options?: DataEnvironmentQueryOptions) => {
    const params: any = {};

    if (options?.limit) params.limit = options.limit;
    if (options?.skip) params.skip = options.skip;
    if (options?.search) params.search = options.search;
    if (options?.type) params.type = options.type;
    if (options?.isActive !== undefined) params.isActive = options.isActive;

    return get(TEST_DATA_ENDPOINTS.ENVIRONMENTS.GET_ALL, { params });
  },

  /**
   * Get data environment by ID
   */
  getById: async (id: string) => {
    return get(TEST_DATA_ENDPOINTS.ENVIRONMENTS.GET_BY_ID(id));
  },

  /**
   * Create new data environment
   */
  create: async (data: CreateDataEnvironmentRequest) => {
    return post(TEST_DATA_ENDPOINTS.ENVIRONMENTS.CREATE, data, {
      showSuccessToast: true,
      successMessage: 'Environment created successfully'
    });
  },

  /**
   * Update data environment
   */
  update: async (id: string, data: UpdateDataEnvironmentRequest) => {
    return put(TEST_DATA_ENDPOINTS.ENVIRONMENTS.UPDATE(id), data, {
      showSuccessToast: true,
      successMessage: 'Environment updated successfully'
    });
  },

  /**
   * Delete data environment
   */
  delete: async (id: string) => {
    return del(TEST_DATA_ENDPOINTS.ENVIRONMENTS.DELETE(id), {
      showSuccessToast: true,
      successMessage: 'Environment deleted successfully'
    });
  },
};

/**
 * Generate test data using AI
 */
export async function generateTestData(params: {
  variables: Array<{
    name: string;
    type: string;
    description?: string;
    constraints?: any;
  }>;
  environments: Array<{
    id: string;
    name: string;
    description?: string;
    type: string;
  }>;
  datasetMetadata?: {
    name: string;
    description: string;
    tags: string[];
    aiPrompt: string;
  };
}) {
  return post(GENERATE_DATA_ENDPOINTS.GENERATE, params);
}

/**
 * Generate variable definitions using AI
 */
export async function generateVariableDefinitions(params: {
  prompt: string;
  environments: Array<{
    id: string;
    name: string;
    description?: string;
    type: string;
  }>;
  creativityLevel?: number;
}) {
  return post(GENERATE_DATA_ENDPOINTS.VARIABLES, params);
}

/**
 * Generate data set structure using AI
 */
export async function generateDataSetStructure(params: {
  prompt: string;
  environments?: Array<{
    id: string;
    name: string;
    description?: string;
    type: string;
  }>;
  creativityLevel?: number;
}) {
  return post(GENERATE_DATA_ENDPOINTS.DATASET_STRUCTURE, params);
}

/**
 * Upload and parse CSV file
 */
export async function uploadCsvFile(file: File): Promise<{
  success: boolean;
  data?: {
    filename: string;
    variables: Array<{
      id: string;
      name: string;
      value: string;
      type: string;
      description: string;
    }>;
    totalRows: number;
    preview: Array<{
      id: string;
      name: string;
      value: string;
      type: string;
      description: string;
    }>;
  };
  error?: string;
  message?: string;
}> {
  const formData = new FormData();
  formData.append('file', file);
  // Assuming a hypothetical endpoint, replace with actual if available
  return post('/data-sources/upload-csv', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    showSuccessToast: false,
  });
}

/**
 * Upload and parse CSV file for a data source
 */
export async function uploadDataSourceCsv(file: File): Promise<{
  success: boolean;
  data?: {
    filename: string;
    variables: Array<{
      id: string;
      name: string;
      value: string;
    }>;
    totalRows: number;
  };
  error?: string;
  message?: string;
}> {
  const formData = new FormData();
  formData.append('file', file);
  return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.UPLOAD_CSV, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    showSuccessToast: false,
  });
}

/**
 * Inspects an Excel file to get its sheet names.
 */
export async function inspectExcel(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return post(EXCEL_ENDPOINTS.INSPECT, formData, {
    isFormData: true,
    showSuccessToast: false,
  });
}

/**
 * Parses a specific sheet from an Excel file.
 */
export async function parseExcel(file: File, sheetName: string) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sheetName', sheetName);
  return post(EXCEL_ENDPOINTS.PARSE, formData, {
    isFormData: true,
    showSuccessToast: false,
  });
}

/**
 * Tests the database connection.
 */
export async function testDbConnection(connectionString: string) {
  return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.TEST_DB_CONNECTION, { connectionString });
}

/**
 * Gets the list of tables from a database connection.
 */
export async function getDbTables(connectionString: string) {
  return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.GET_DB_TABLES, { connectionString });
}

/**
 * Gets the list of columns from a database table.
 */
export async function getDbColumns(connectionString: string, table: string) {
  return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.GET_DB_COLUMNS, { connectionString, table });
}

/**
 * Gets the preview data from a database table.
 */
export async function getDbPreviewData(connectionString: string, table: string, nameColumn: string, valueColumn: string) {
  return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.GET_DB_PREVIEW_DATA, { connectionString, table, nameColumn, valueColumn });
}

/**
 * Executes a dynamic query flow on a database.
 */
export async function executeDbFlow(connectionString: string, flow: any[]) {
  return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.EXECUTE_DB_FLOW, { connectionString, flow });
}

/**
 * Executes a test API request.
 */
export async function testApiRequest(payload: { 
  method: string; 
  url: string; 
  headers: any[]; 
  body: string;
  bodyType?: string;
  params?: any[];
}) {
  return post(TEST_DATA_ENDPOINTS.DATA_SOURCES.TEST_API_REQUEST, payload);
}
