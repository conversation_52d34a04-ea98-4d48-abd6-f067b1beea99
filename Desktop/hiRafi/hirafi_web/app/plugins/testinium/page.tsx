"use client"

import { useState, useEffect } from "react"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Check, ChevronRight, Puzzle, AlertCircle, ArrowRight, CheckCircle2, LoaderCircle, Smartphone, Settings, User, Package, Trash2 } from "lucide-react"
import { CompanyOwnerRoute } from "@/components/company-owner-route"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { pluginApi } from "@/lib/api"
import { API_BASE_URL } from "@/lib/api/constants"
import ErrorBoundary from "@/components/error-boundary"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { ConfirmDialog } from "@/components/team/confirm-dialog"

interface TestiniumConfig {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  issuerUri: string;
}

interface Device {
  id: string;
  name?: string;
  marketName?: string;
  osVersion?: string;
  platformVersion?: string;
  deviceType?: string;
  manufacturer?: string;
  available?: boolean;
  [key: string]: any;
}

interface TestiniumApp {
  id: string;
  name: string;
  fileName: string;
  fileKey: string;
  version?: string;
  description?: string;
  fileSize?: number;
  uploadedAt: string;
  uploadedBy?: string;
  companyId: string;
  teamId?: string;
  metadata?: {
    platform?: string;
    bundleId?: string;
    versionCode?: string;
    minSdkVersion?: string;
    targetSdkVersion?: string;
    [key: string]: any;
  };
}

function TestiniumPlugin() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [configStep, setConfigStep] = useState(1)
  const [isActive, setIsActive] = useState(false)
  const [isLoadingConfig, setIsLoadingConfig] = useState(true)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [isSavingConfig, setIsSavingConfig] = useState(false)
  const [isLoadingDevices, setIsLoadingDevices] = useState(false)
  const [showReconfigureConfirm, setShowReconfigureConfirm] = useState(false)
  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false)
  const [isLoadingApps, setIsLoadingApps] = useState(false)
  const [showAppDeleteConfirm, setShowAppDeleteConfirm] = useState(false)
  const [appToDelete, setAppToDelete] = useState<TestiniumApp | null>(null)

  const [configData, setConfigData] = useState<TestiniumConfig>({
    apiUrl: "",
    clientId: "",
    clientSecret: "",
    issuerUri: ""
  })

  const [activeConfigData, setActiveConfigData] = useState<TestiniumConfig | null>(null)
  const [devices, setDevices] = useState<Device[]>([])
  const [apps, setApps] = useState<TestiniumApp[]>([])

  const { toast } = useToast()

  // Fetch current configuration
  const fetchCurrentConfig = async () => {
    setIsLoadingConfig(true)
    try {
      const response = await pluginApi.getTestiniumConfig()

      if (response.success && response.config) {
        setActiveConfigData(response.config)
        setConfigData({
          apiUrl: response.config.apiUrl || "",
          clientId: response.config.clientId || "",
          clientSecret: response.config.clientSecret || "",
          issuerUri: response.config.issuerUri || ""
        })

        const isPluginActive = response.plugin?.active || false
        setIsActive(isPluginActive)

        // If active, fetch devices and apps
        if (isPluginActive) {
          fetchDevices()
          fetchApps()
        }
      } else {
        setIsActive(false)
      }
    } catch (error) {
      setIsActive(false)
    } finally {
      setIsLoadingConfig(false)
    }
  }

  // Fetch devices
  const fetchDevices = async () => {
    setIsLoadingDevices(true)
    try {
      const response = await pluginApi.getTestiniumDevices()

      if (response.success && response.devices) {
        setDevices(response.devices || [])
      } else {
        setDevices([])
        toast({
          title: "Cihaz Listesi Alınamadı",
          description: response.error || "Cihazlar yüklenirken bir hata oluştu",
          variant: "destructive"
        })
      }
    } catch (error) {
      setDevices([])
      toast({
        title: "Hata",
        description: "Cihazlar yüklenirken bir hata oluştu",
        variant: "destructive"
      })
    } finally {
      setIsLoadingDevices(false)
    }
  }

  // Fetch apps
  const fetchApps = async () => {
    setIsLoadingApps(true)
    try {
      const response = await pluginApi.getTestiniumApps()

      if (response.success && response.apps) {
        setApps(response.apps || [])
      } else {
        setApps([])
        toast({
          title: "App Listesi Alınamadı",
          description: response.error || "Uygulamalar yüklenirken bir hata oluştu",
          variant: "destructive"
        })
      }
    } catch (error) {
      setApps([])
      toast({
        title: "Hata",
        description: "Uygulamalar yüklenirken bir hata oluştu",
        variant: "destructive"
      })
    } finally {
      setIsLoadingApps(false)
    }
  }

  // Test connection
  const testConnection = async () => {
    if (!configData.apiUrl || !configData.clientId || !configData.clientSecret || !configData.issuerUri) {
      toast({
        title: "Eksik Bilgi",
        description: "Lütfen tüm alanları doldurun",
        variant: "destructive"
      })
      return
    }

    setIsTestingConnection(true)
    try {
      const response = await pluginApi.testTestiniumConnection(configData)

      if (response.success) {
        toast({
          title: "Bağlantı Başarılı",
          description: "Testinium bağlantısı başarıyla test edildi",
        })
        setConfigStep(2)
      } else {
        toast({
          title: "Bağlantı Hatası",
          description: response.error || "Bağlantı test edilirken bir hata oluştu",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Bağlantı test edilirken bir hata oluştu",
        variant: "destructive"
      })
    } finally {
      setIsTestingConnection(false)
    }
  }

  // Save configuration
  const saveConfiguration = async () => {
    setIsSavingConfig(true)
    try {
      const response = await pluginApi.updateTestiniumConfig(configData)

      if (response.success) {
        toast({
          title: "Yapılandırma Kaydedildi",
          description: "Testinium yapılandırması başarıyla kaydedildi",
        })
        setIsActive(true)
        setActiveConfigData(configData)
        fetchDevices()
        fetchApps()
      } else {
        toast({
          title: "Kaydetme Hatası",
          description: response.error || "Yapılandırma kaydedilirken bir hata oluştu",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Yapılandırma kaydedilirken bir hata oluştu",
        variant: "destructive"
      })
    } finally {
      setIsSavingConfig(false)
    }
  }

  // Reconfigure plugin (delete and allow reconfiguration)
  const reconfigurePlugin = async () => {
    try {
      const response = await pluginApi.deletePlugin('testinium')

      if (response.success) {
        toast({
          title: "Plugin Reconfigured",
          description: "Testinium plugin has been reset for reconfiguration",
        })
        setIsActive(false)
        setActiveConfigData(null)
        setConfigData({
          apiUrl: "",
          clientId: "",
          clientSecret: "",
          issuerUri: ""
        })
        setDevices([])
        setApps([])
        setConfigStep(1)
      } else {
        toast({
          title: "Reconfiguration Failed",
          description: "Failed to reset plugin configuration",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while resetting configuration",
        variant: "destructive"
      })
    } finally {
      setShowReconfigureConfirm(false)
    }
  }

  // Disconnect plugin completely
  const disconnectPlugin = async () => {
    try {
      const response = await pluginApi.disconnectTestinium()

      if (response.success) {
        toast({
          title: "Plugin Disconnected",
          description: "Testinium plugin has been disconnected successfully",
        })
        setIsActive(false)
        setActiveConfigData(null)
        setConfigData({
          apiUrl: "",
          clientId: "",
          clientSecret: "",
          issuerUri: ""
        })
        setDevices([])
        setApps([])
        setConfigStep(1)
      } else {
        toast({
          title: "Disconnect Failed",
          description: "Failed to disconnect plugin",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while disconnecting",
        variant: "destructive"
      })
    } finally {
      setShowDisconnectConfirm(false)
    }
  }

  // Handle app delete confirmation
  const handleDeleteApp = (app: TestiniumApp) => {
    setAppToDelete(app)
    setShowAppDeleteConfirm(true)
  }

  // Confirm app deletion
  const confirmDeleteApp = async () => {
    if (!appToDelete) return

    try {
      const response = await pluginApi.deleteTestiniumApp(appToDelete.id)

      if (response.success) {
        toast({
          title: "App Deleted",
          description: "App deleted successfully",
        })
        fetchApps()
      } else {
        toast({
          title: "Delete Failed",
          description: "Failed to delete app",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete app",
        variant: "destructive"
      })
    } finally {
      setAppToDelete(null)
      setShowAppDeleteConfirm(false)
    }
  }

  useEffect(() => {
    fetchCurrentConfig()
  }, [])

  const renderActiveCard = () => (
    <Card className="border-0 shadow-md">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/30 dark:to-violet-900/30 border-b">
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 bg-purple-100 dark:bg-purple-900/50 text-purple-600 dark:text-purple-400 rounded-full flex items-center justify-center">
            <Check className="h-5 w-5" />
          </div>
          <div>
            <CardTitle>Testinium Integration Active</CardTitle>
            <CardDescription>Your Testinium integration is set up and working correctly.</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <Tabs defaultValue="account" className="w-full">
          <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground mb-6">
            <TabsTrigger value="account" className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground">
              <User className="h-4 w-4" />
              Account
            </TabsTrigger>
            <TabsTrigger value="apps" className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground">
              <Package className="h-4 w-4" />
              Apps
            </TabsTrigger>
          </TabsList>

          {/* Account Tab */}
          <TabsContent value="account" className="space-y-6">
            <div className="space-y-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-md">
                    <User className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h4 className="font-medium">Account Information</h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Your Testinium account details</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">API URL</h5>
                    <p className="text-sm font-medium">{activeConfigData?.apiUrl || "N/A"}</p>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Client ID</h5>
                    <p className="text-sm font-medium">{activeConfigData?.clientId || "N/A"}</p>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Issuer URI</h5>
                    <p className="text-sm font-medium">{activeConfigData?.issuerUri || "N/A"}</p>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Status</h5>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">Connected</p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-md">
                    <Smartphone className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h4 className="font-medium">Device Access</h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Available devices and resources</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Available Devices</h5>
                    <p className="text-sm font-medium">{devices.length} device(s)</p>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Connection Status</h5>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">Active</p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="apps" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Installed Applications</h3>
                <Button variant="outline" size="sm" onClick={fetchApps}>
                  <Package className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>

              {apps.length === 0 ? (
                <div className="text-center py-12 border rounded-md bg-gray-50 dark:bg-gray-900/50">
                  <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Applications Found</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4 max-w-md mx-auto">
                    No applications have been uploaded to Testinium yet. Upload applications during test execution for better workflow management.
                  </p>
                </div>
              ) : (
                <div className="border rounded-lg overflow-hidden bg-white dark:bg-gray-800">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Application
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Details
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Upload Date
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Size
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {apps.map((app: any, index: number) => (
                          <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                            <td className="px-6 py-4">
                              <div className="flex items-center gap-3">
                                <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-md flex-shrink-0">
                                  <Package className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                </div>
                                <div className="min-w-0 flex-1">
                                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate" title={app.name}>
                                    {app.name}
                                  </p>
                                  <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                                    ID: {app.id ? app.id.substring(0, 12) : "N/A"}
                                  </p>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="space-y-1">
                                <p className="text-sm text-gray-900 dark:text-white">
                                  {app.fileName || "Unknown"}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                                  {app.description || "No description"}
                                </p>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <p className="text-sm text-gray-900 dark:text-white">
                                {app.uploadedAt ? new Date(app.uploadedAt).toLocaleDateString() : "N/A"}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {app.uploadedAt ? new Date(app.uploadedAt).toLocaleTimeString() : ""}
                              </p>
                            </td>
                            <td className="px-6 py-4">
                              <p className="text-sm text-gray-900 dark:text-white">
                                {app.fileSize ? `${(app.fileSize / (1024 * 1024)).toFixed(1)} MB` : "N/A"}
                              </p>
                            </td>
                            <td className="px-6 py-4 text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                                onClick={() => handleDeleteApp(app)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>


        </Tabs>
      </CardContent>
      <CardFooter className="border-t bg-gray-50/50 dark:bg-gray-900/30 flex justify-center gap-3 py-4">
        <Button variant="outline" onClick={() => setShowReconfigureConfirm(true)}>
          <Settings className="h-4 w-4 mr-2" />
          Reconfigure
        </Button>
        <Button variant="destructive" onClick={() => setShowDisconnectConfirm(true)}>
          <Trash2 className="h-4 w-4 mr-2" />
          Disconnect
        </Button>
      </CardFooter>
    </Card>
  )

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-6 bg-white dark:bg-gray-900">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm">
              <Smartphone className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Testinium Integration</h1>
              <p className="text-gray-500 dark:text-gray-400">
                Connect your Testinium account to access mobile devices for testing
              </p>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-3xl mx-auto">
            {isLoadingConfig ? (
              <div className="flex items-center justify-center h-64">
                <LoaderCircle className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : isActive ? (
              renderActiveCard()
            ) : (
              <Card className="border-0 shadow-md">
                <CardHeader className="border-b">
                  <CardTitle>Configure Testinium Integration</CardTitle>
                  <CardDescription>
                    Connect your Testinium account to access mobile devices for testing
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  {configStep === 1 && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="apiUrl">API URL</Label>
                        <Input
                          id="apiUrl"
                          type="url"
                          placeholder="https://your-devicepark-api.com"
                          value={configData.apiUrl}
                          onChange={(e) => setConfigData({ ...configData, apiUrl: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientId">Client ID</Label>
                        <Input
                          id="clientId"
                          type="text"
                          placeholder="Your client ID"
                          value={configData.clientId}
                          onChange={(e) => setConfigData({ ...configData, clientId: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientSecret">Client Secret</Label>
                        <Input
                          id="clientSecret"
                          type="password"
                          placeholder="Your client secret"
                          value={configData.clientSecret}
                          onChange={(e) => setConfigData({ ...configData, clientSecret: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="issuerUri">Issuer URI</Label>
                        <Input
                          id="issuerUri"
                          type="url"
                          placeholder="https://your-auth-server.com"
                          value={configData.issuerUri}
                          onChange={(e) => setConfigData({ ...configData, issuerUri: e.target.value })}
                        />
                      </div>
                    </div>
                  )}

                  {configStep === 2 && (
                    <div className="space-y-4">
                      <Alert>
                        <CheckCircle2 className="h-4 w-4" />
                        <AlertTitle>Connection Successful!</AlertTitle>
                        <AlertDescription>
                          Your Testinium credentials have been verified. Click "Save Configuration" to complete the setup.
                        </AlertDescription>
                      </Alert>

                      <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                        <h4 className="font-medium">Configuration Summary:</h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p><strong>API URL:</strong> {configData.apiUrl}</p>
                          <p><strong>Client ID:</strong> {configData.clientId}</p>
                          <p><strong>Issuer URI:</strong> {configData.issuerUri}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="border-t bg-gray-50 flex justify-between">
                  {configStep === 1 ? (
                    <>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        Step 1 of 2: Enter credentials
                      </div>
                      <Button
                        onClick={testConnection}
                        disabled={isTestingConnection || !configData.apiUrl || !configData.clientId || !configData.clientSecret || !configData.issuerUri}
                      >
                        {isTestingConnection ? (
                          <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <ArrowRight className="h-4 w-4 mr-2" />
                        )}
                        Test Connection
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        variant="outline"
                        onClick={() => setConfigStep(1)}
                      >
                        <ChevronRight className="h-4 w-4 mr-2 rotate-180" />
                        Back
                      </Button>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          Step 2 of 2: Save configuration
                        </div>
                        <Button
                          onClick={saveConfiguration}
                          disabled={isSavingConfig}
                        >
                          {isSavingConfig ? (
                            <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                          ) : (
                            <Check className="h-4 w-4 mr-2" />
                          )}
                          Save Configuration
                        </Button>
                      </div>
                    </>
                  )}
                </CardFooter>
              </Card>
            )}
          </div>
        </main>
      </div>

      {/* Plugin Reconfiguration Confirmation Dialog */}
      <ConfirmDialog
        open={showReconfigureConfirm}
        onOpenChange={setShowReconfigureConfirm}
        title="Reconfigure Plugin"
        description="Are you sure you want to reconfigure the Testinium plugin? This will reset the current configuration and allow you to set it up again."
        confirmLabel="Yes, Reconfigure"
        cancelLabel="Cancel"
        onConfirm={reconfigurePlugin}
        variant="default"
      />

      {/* Plugin Disconnect Confirmation Dialog */}
      <ConfirmDialog
        open={showDisconnectConfirm}
        onOpenChange={setShowDisconnectConfirm}
        title="Disconnect Plugin"
        description="Are you sure you want to disconnect the Testinium plugin? This action cannot be undone and all configuration data will be permanently removed."
        confirmLabel="Yes, Disconnect"
        cancelLabel="Cancel"
        onConfirm={disconnectPlugin}
        variant="destructive"
      />

      {/* App Deletion Confirmation Dialog */}
      <ConfirmDialog
        open={showAppDeleteConfirm}
        onOpenChange={setShowAppDeleteConfirm}
        title="Delete Application"
        description={`Are you sure you want to delete "${appToDelete?.name}"? This action cannot be undone and the application will be permanently removed from Testinium.`}
        confirmLabel="Yes, Delete"
        cancelLabel="Cancel"
        onConfirm={confirmDeleteApp}
        variant="destructive"
      />
    </div>
  )
}

export default function TestiniumPluginPage() {
  return (
    <ErrorBoundary>
      <CompanyOwnerRoute>
        <TestiniumPlugin />
      </CompanyOwnerRoute>
    </ErrorBoundary>
  )
}
