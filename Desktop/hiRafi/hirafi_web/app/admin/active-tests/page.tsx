'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trash2, <PERSON><PERSON>ircle, RefreshCw, AlertTriangle, Clock, Play } from 'lucide-react';
import { toast } from '@/lib/utils/toast-utils';
import {
  getActiveTests,
  completeActiveTest,
  removeActiveTest,
  completeAllActiveTests,
  removeAllActiveTests
} from '@/lib/api/admin-api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface ActiveTest {
  id: string;
  testId: string;
  scenarioId?: string;
  scenarioName: string;
  state: string;
  attempts: number;
  maxAttempts: number;
  processedOn?: Date;
  finishedOn?: Date;
  runId?: string;
  nodeId?: string;
  priority: number;
  delay: number;
  timestamp?: Date;
  progress: number;
  error?: string;
}

interface ActiveTestsData {
  activeTests: ActiveTest[];
  count: number;
  timestamp: string;
}

export default function ActiveTestsPage() {
  const [activeTests, setActiveTests] = useState<ActiveTest[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [count, setCount] = useState(0);
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [processingTest, setProcessingTest] = useState<string | null>(null);
  const [bulkProcessing, setBulkProcessing] = useState(false);

  const fetchActiveTests = async (showRefreshIndicator = false) => {
    if (showRefreshIndicator) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      const response = await getActiveTests();
      
      if (response.success) {
        setActiveTests(response.activeTests || response.data?.activeTests || []);
        setCount(response.count || response.data?.count || 0);
        setLastUpdated(response.timestamp || response.data?.timestamp || new Date().toISOString());
      } else {
        toast.error(response.error || 'Aktif testler yüklenirken hata oluştu');
      }
    } catch (error) {
      toast.error('Aktif testler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchActiveTests();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchActiveTests(true);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleCompleteTest = async (testId: string) => {
    setProcessingTest(testId);
    try {
      const response = await completeActiveTest(testId);
      
      if (response.success) {
        toast.success('Test başarıyla tamamlandı');
        await fetchActiveTests(true);
      } else {
        toast.error(response.error || 'Test tamamlanırken hata oluştu');
      }
    } catch (error) {
      toast.error('Test tamamlanırken hata oluştu');
    } finally {
      setProcessingTest(null);
    }
  };

  const handleRemoveTest = async (testId: string) => {
    setProcessingTest(testId);
    try {
      const response = await removeActiveTest(testId);
      
      if (response.success) {
        toast.success('Test başarıyla silindi');
        await fetchActiveTests(true);
      } else {
        toast.error(response.error || 'Test silinirken hata oluştu');
      }
    } catch (error) {
      toast.error('Test silinirken hata oluştu');
    } finally {
      setProcessingTest(null);
    }
  };

  const handleCompleteAllTests = async () => {
    setBulkProcessing(true);
    try {
      const response = await completeAllActiveTests();
      
      if (response.success) {
        toast.success(response.message || 'Tüm testler başarıyla tamamlandı');
        await fetchActiveTests(true);
      } else {
        toast.error(response.error || 'Testler tamamlanırken hata oluştu');
      }
    } catch (error) {
      toast.error('Testler tamamlanırken hata oluştu');
    } finally {
      setBulkProcessing(false);
    }
  };

  const handleRemoveAllTests = async () => {
    setBulkProcessing(true);
    try {
      const response = await removeAllActiveTests();
      
      if (response.success) {
        toast.success(response.message || 'Tüm testler başarıyla silindi');
        await fetchActiveTests(true);
      } else {
        toast.error(response.error || 'Testler silinirken hata oluştu');
      }
    } catch (error) {
      toast.error('Testler silinirken hata oluştu');
    } finally {
      setBulkProcessing(false);
    }
  };

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return '-';

    try {
      const d = typeof date === 'string' ? new Date(date) : date;

      // Validate the date
      if (isNaN(d.getTime())) {
        console.warn('Invalid date value received:', date);
        return 'Invalid date';
      }

      return d.toLocaleString('tr-TR');
    } catch (error) {
      console.error('Error formatting date:', error, 'Input:', date);
      return 'Date error';
    }
  };

  const getStateBadgeColor = (state: string) => {
    switch (state) {
      case 'active':
        return 'bg-green-500';
      case 'waiting':
        return 'bg-yellow-500';
      case 'completed':
        return 'bg-blue-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Aktif testler yükleniyor...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">BullMQ Aktif Testler</h1>
          <p className="text-gray-600 mt-2">
            Toplam {count} aktif test • Son güncelleme: {formatDate(lastUpdated)}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => fetchActiveTests(true)}
            disabled={refreshing}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
          
          {count > 0 && (
            <>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    disabled={bulkProcessing}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Tümünü Tamamla
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Tüm Aktif Testleri Tamamla</AlertDialogTitle>
                    <AlertDialogDescription>
                      Bu işlem tüm aktif testleri zorla tamamlayacak ve completed durumuna geçirecektir. 
                      Bu işlem geri alınamaz. Devam etmek istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={handleCompleteAllTests}>
                      Tümünü Tamamla
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="destructive"
                    disabled={bulkProcessing}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Tümünü Sil
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Tüm Aktif Testleri Sil</AlertDialogTitle>
                    <AlertDialogDescription>
                      Bu işlem tüm aktif testleri zorla silecektir. 
                      Bu işlem geri alınamaz ve testler tamamen kaybolacaktır. 
                      Devam etmek istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={handleRemoveAllTests} className="bg-red-600 hover:bg-red-700">
                      Tümünü Sil
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          )}
        </div>
      </div>

      {count === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">Aktif Test Bulunamadı</h3>
            <p className="text-gray-600 text-center">
              Şu anda BullMQ test kuyruğunda aktif durumda bekleyen test bulunmuyor.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {activeTests.map((test) => (
            <Card key={test.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Play className="h-5 w-5 text-blue-500" />
                      {test.scenarioName}
                      {test.error && (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      )}
                    </CardTitle>
                    <div className="flex gap-2 mt-2">
                      <Badge className={getStateBadgeColor(test.state)}>
                        {test.state}
                      </Badge>
                      <Badge variant="outline">
                        {test.attempts}/{test.maxAttempts} deneme
                      </Badge>
                      {test.progress > 0 && (
                        <Badge variant="outline">
                          %{test.progress} tamamlandı
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleCompleteTest(test.testId)}
                      disabled={processingTest === test.testId || bulkProcessing}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Tamamla
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          size="sm"
                          variant="destructive"
                          disabled={processingTest === test.testId || bulkProcessing}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Sil
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Testi Sil</AlertDialogTitle>
                          <AlertDialogDescription>
                            Bu testi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>İptal</AlertDialogCancel>
                          <AlertDialogAction 
                            onClick={() => handleRemoveTest(test.testId)}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Sil
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Test ID:</span>
                    <p className="font-mono text-xs">{test.testId}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Job ID:</span>
                    <p className="font-mono text-xs">{test.id}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Run ID:</span>
                    <p className="font-mono text-xs">{test.runId || '-'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Node ID:</span>
                    <p className="font-mono text-xs">{test.nodeId || '-'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">İşlenme Zamanı:</span>
                    <p className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatDate(test.processedOn)}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Oluşturulma:</span>
                    <p className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatDate(test.timestamp)}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Öncelik:</span>
                    <p>{test.priority}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Gecikme:</span>
                    <p>{test.delay}ms</p>
                  </div>
                </div>
                {test.error && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                    <span className="font-medium text-red-800">Hata:</span>
                    <p className="text-red-700 text-sm mt-1">{test.error}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
