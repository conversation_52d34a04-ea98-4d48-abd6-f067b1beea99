"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Save, Loader2 } from "lucide-react"
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"

interface StepNavigationProps {
  totalSteps: number
  isDetailsComplete: boolean
  isStepsComplete: boolean
}

export function StepNavigation({
  totalSteps,
  isDetailsComplete,
  isStepsComplete
}: StepNavigationProps) {
  const {
    ui,
    setCurrentStep,
    saveScenario
  } = useUnifiedScenarioContext()

  const currentStep = ui.currentStep
  const isLoading = ui.isSaving

  // Go to previous step
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Go to next step
  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  // Determine if can proceed to next step
  const canProceedToNextStep = () => {
    if (currentStep === 0) {
      return isDetailsComplete
    }

    return true
  }

  // Check if can save (only if both details and steps are complete)
  const canSave = isDetailsComplete && isStepsComplete

  return (
    <div className="mt-8 flex items-center justify-end">
      <div className="flex gap-3">
        {currentStep > 0 && (
          <Button
            type="button"
            variant="outline"
            className="gap-1 min-w-[110px] rounded-full"
            onClick={handlePrevious}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
        )}

        {currentStep < totalSteps - 1 ? (
          <Button
            type="button"
            className="gap-1 min-w-[110px] rounded-full bg-indigo-600 hover:bg-indigo-700"
            onClick={handleNext}
            disabled={!canProceedToNextStep()}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        ) : (
          <Button
            type="button"
            className="gap-1 min-w-[110px] px-4 rounded-full bg-white text-indigo-600 hover:bg-white/90 border border-gray-200"
            onClick={saveScenario}
            disabled={!canSave || isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Save
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  )
}