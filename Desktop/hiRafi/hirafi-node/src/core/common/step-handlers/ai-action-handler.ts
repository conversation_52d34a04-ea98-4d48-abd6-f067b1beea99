/**
 * Common AI Action Handler
 * Handles executing AI action steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Action Handler
 * Handles executing AI action steps using the AI agent
 */
export class CommonAiActionHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiAction';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiActionHandler';
  }

  /**
   * Execute an AI action step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiAction, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const actionText = step.prompt || step.value || step.name || step.description;

    if (!actionText) {
      return { success: false, error: 'Missing action instruction for aiAction step. Please provide a prompt field.' };
    }

    try {
      // Note: aiAction does not support deepThink parameter according to Midscene.js API
      // Only cacheable option is supported, but we don't expose it in our interface yet

      // Log the action for debugging
      logger.debug(`CommonAiActionHandler: Executing action "${actionText}"`);

      // Use the AI agent to perform the action directly
      const result = await agent.aiAction(actionText || '');
      logger.debug(`aiAction result: ${JSON.stringify(result)}`);

      // Check if the action was successful
      const isSuccess = result && !result.error;

      // Format the result to standard log format
      const logEntry = formatAIResultForLog('aiAction', actionText, result);

      // Update action-specific fields
      logEntry.success = isSuccess;

      // If there's an error in the result, add it to the log entry
      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      // Add the formatted log entry to logs
      logs.push(JSON.stringify(logEntry));

      // If the action failed, return the error
      if (!isSuccess) {
        const errorMessage = result.error || `Action failed: ${actionText}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      // Extract error message and reason
      const errorMessage = error.message || error.toString();
      const reasonMessage = error.Reason || '';

      // Log only the error message, not the full error object to avoid stack traces
      logger.info('AI Action Error:', errorMessage);

      // Simplify error messages
      const simplifiedError = simplifyErrorMessage(errorMessage);
      const simplifiedReason = simplifyErrorMessage(reasonMessage);

      // Create an error log entry with both error and reason
      const errorLogEntry = {
        operation: 'aiAction',
        description: actionText,
        success: false,
        error: simplifiedError,
        reason: simplifiedReason,
        metadata: {
          status: 'failed'
        }
      };

      // Add the formatted error log entry to logs
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiActionHandler: Action failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
}
