/**
 * Logger Service - Dependency Injection Implementation
 * 
 * Provides a logger service that can be injected and managed through DI containers.
 * Maintains backward compatibility with the existing logger singleton.
 */

import chalk from 'chalk';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export type LogContext = 'startup' | 'runtime' | 'test' | 'system' | 'error';

/**
 * Logger service options
 */
export interface LoggerServiceOptions {
  minLevel?: LogLevel;
  showTimestamp?: boolean;
  showLevel?: boolean;
  quietStartup?: boolean;
  startupComplete?: boolean;
  serviceName?: string;
}

/**
 * Logger Service Interface
 */
export interface ILoggerService {
  setMinLevel(level: LogLevel): void;
  markStartupComplete(): void;
  debug(message: string, data?: any): void;
  info(message: string, data?: any): void;
  warn(message: string, data?: any): void;
  error(message: string, data?: any): void;
  startup(message: string, data?: any): void;
  serviceInit(serviceName: string, message?: string, data?: any): void;
  runtime(message: string, data?: any): void;
  test(message: string, data?: any): void;
  connectionSummary(nodeId: string, connections: { websocket: boolean; redis: boolean }): void;
}

const LOG_LEVELS: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3
};

// Services that should always log initialization (critical services only)
const CRITICAL_SERVICES = [
  'NodeManager',
  'ApplicationContainer',
  'NodeIdentityService',
  'ConnectionService',
  'TestQueueWorker',
  'EventService'
];

// Services that should be completely silent during startup
const SILENT_SERVICES = [
  'TestDataConverter',
  'AIConfigurationService',
  'TestEventService',
  'StorageManager',
  'BaseMetricsCollector',
  'HeartbeatService',
  'EventCoordinator',
  'PeriodicTaskManager',
  'HeartbeatManager',
  'TestQueueWorker',
  'RedisClient',
  'RedisConnectionManager',
  'WebSocketConnectionManager',
  'MinioArtifactStorage',
  'TestExecutionFactory',
  'TestExecutionManager',
  'WebSocket',
  'ConnectionManager',
  'NodeIdentityManager',
  'RegistrationService',
  'TestCommunicationService',
  'WebSocketMessageHandler',
  'NodeStateManager'
];

/**
 * Logger Service Implementation
 * 
 * Provides structured logging functionality with dependency injection support.
 * Can be injected into services and managed through DI containers.
 */
export class LoggerService implements ILoggerService {
  private options: LoggerServiceOptions;

  constructor(options: LoggerServiceOptions = {}) {
    this.options = {
      minLevel: 'info',
      showTimestamp: true,
      showLevel: true,
      quietStartup: process.env.QUIET_STARTUP === 'true',
      startupComplete: false,
      serviceName: 'LoggerService',
      ...options
    };

    // Check for environment variable overrides
    if (process.env.LOG_LEVEL) {
      const envLevel = process.env.LOG_LEVEL.toLowerCase() as LogLevel;
      if (envLevel in LOG_LEVELS) {
        this.options.minLevel = envLevel;
      }
    }
  }

  /**
   * Set the minimum log level
   */
  public setMinLevel(level: LogLevel): void {
    this.options.minLevel = level;
  }

  /**
   * Mark startup as complete - enables normal logging
   */
  public markStartupComplete(): void {
    this.options.startupComplete = true;
    this.info('System startup completed - normal logging enabled');
  }

  /**
   * Log a debug message
   */
  public debug(message: string, data?: any): void {
    this.log('debug', message, data);
  }

  /**
   * Log an info message
   */
  public info(message: string, data?: any): void {
    this.log('info', message, data);
  }

  /**
   * Log a warning message
   */
  public warn(message: string, data?: any): void {
    this.log('warn', message, data);
  }

  /**
   * Log an error message
   */
  public error(message: string, data?: any): void {
    this.log('error', message, data);
  }

  /**
   * Log a startup message (filtered during quiet startup)
   */
  public startup(message: string, data?: any): void {
    this.logWithContext('info', 'startup', message, data);
  }

  /**
   * Log a service initialization message (intelligently filtered)
   */
  public serviceInit(serviceName: string, message?: string, data?: any): void {
    const fullMessage = message ? `${serviceName}: ${message}` : `${serviceName}: Initialized`;

    // Check if this service should be silent during startup
    const isSilent = SILENT_SERVICES.some(service => serviceName.includes(service));

    // If it's a silent service and we're in quiet startup mode, don't log at all
    if (isSilent && this.options.quietStartup && !this.options.startupComplete) {
      return;
    }

    // Check if this is a critical service
    const isCritical = CRITICAL_SERVICES.some(service => serviceName.includes(service));

    if (isCritical || this.options.startupComplete || !this.options.quietStartup) {
      this.log('info', fullMessage, data);
    } else {
      this.log('debug', fullMessage, data);
    }
  }

  /**
   * Log a runtime message (always shown after startup)
   */
  public runtime(message: string, data?: any): void {
    this.logWithContext('info', 'runtime', message, data);
  }

  /**
   * Log a test-related message
   */
  public test(message: string, data?: any): void {
    this.logWithContext('info', 'test', message, data);
  }

  /**
   * Log a connection summary (always shown during startup)
   */
  public connectionSummary(nodeId: string, connections: { websocket: boolean; redis: boolean }): void {
    const wsStatus = connections.websocket ? '✅ Connected' : '❌ Failed';
    const redisStatus = connections.redis ? '✅ Connected' : '❌ Failed';
    this.info(`🔗 Node ${nodeId} connections: WebSocket ${wsStatus}, Redis ${redisStatus}`);
  }

  /**
   * Log a message with context awareness
   */
  private logWithContext(level: LogLevel, context: LogContext, message: string, data?: any): void {
    // During startup, only suppress debug messages if quiet startup is enabled
    if (context === 'startup' && this.options.quietStartup && !this.options.startupComplete) {
      // Allow info, warn, and error messages during quiet startup
      if (level === 'debug') {
        return;
      }
    }

    this.log(level, message, data);
  }

  /**
   * Log a message with the specified level
   */
  private log(level: LogLevel, message: string, data?: any): void {
    // Check if this log level should be shown
    if (LOG_LEVELS[level] < LOG_LEVELS[this.options.minLevel!]) {
      return;
    }

    const parts: string[] = [];

    // Add timestamp if enabled
    if (this.options.showTimestamp) {
      const timestamp = new Date().toISOString();
      parts.push(chalk.gray(`[${timestamp}]`));
    }

    // Add log level if enabled
    if (this.options.showLevel) {
      let levelText: string;
      switch (level) {
        case 'debug':
          levelText = chalk.blue('[DEBUG]');
          break;
        case 'info':
          levelText = chalk.green('[INFO]');
          break;
        case 'warn':
          levelText = chalk.yellow('[WARN]');
          break;
        case 'error':
          levelText = chalk.red('[ERROR]');
          break;
        default:
          levelText = `[${String(level).toUpperCase()}]`;
      }
      parts.push(levelText);
    }

    // Add service name if provided
    if (this.options.serviceName && this.options.serviceName !== 'LoggerService') {
      parts.push(chalk.cyan(`[${this.options.serviceName}]`));
    }

    // Add message
    parts.push(message);

    // Output the log message
    console.log(parts.join(' '));

    // If data is provided, output it on the next line
    if (data !== undefined) {
      if (level === 'error' && data instanceof Error) {
        console.error(chalk.red(data.stack || data.toString()));
      } else {
        try {
          const dataString = typeof data === 'object'
            ? JSON.stringify(data, null, 2)
            : data.toString();
          console.log(chalk.gray(dataString));
        } catch (err) {
          console.log(chalk.gray('[Unable to stringify data]'));
        }
      }
    }
  }
}

/**
 * Logger Service Factory
 */
export class LoggerServiceFactory {
  /**
   * Create a logger service instance
   * @param options Logger service options
   * @returns Logger service instance
   */
  static createService(options: LoggerServiceOptions = {}): ILoggerService {
    return new LoggerService(options);
  }

  /**
   * Create a logger service with default options
   * @param serviceName Optional service name for logging context
   * @returns Logger service instance
   */
  static createDefaultService(serviceName?: string): ILoggerService {
    return new LoggerService({
      minLevel: 'info',
      showTimestamp: true,
      showLevel: true,
      quietStartup: process.env.QUIET_STARTUP === 'true',
      startupComplete: false,
      serviceName
    });
  }

  /**
   * Create a logger service for a specific service
   * @param serviceName Service name for logging context
   * @param options Additional logger options
   * @returns Logger service instance
   */
  static createServiceLogger(serviceName: string, options: Partial<LoggerServiceOptions> = {}): ILoggerService {
    return new LoggerService({
      ...options,
      serviceName
    });
  }
}
