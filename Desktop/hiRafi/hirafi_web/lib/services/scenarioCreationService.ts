/**
 * Scenario Creation Service
 * 
 * Service layer for scenario creation business logic and API interactions
 */

import { ScenarioFormData, TestStep } from '@/models/scenario'
import { scenarioApi, generateStepsApi } from '@/lib/api'
import { toast } from '@/lib/utils/toast-utils'
import { useScenarioTestDataStore } from '@/store/scenarioTestDataStore'

// Types for service responses
interface GenerateStepsRequest {
  prompt: string
  name?: string
  description?: string
  isTestManagement?: boolean
  testManagementProvider?: 'testrail' | 'zephyrscale'
  testDataVariables?: Array<{
    name: string
    type: string
    description?: string
  }>
  creativityLevel?: number
}

interface GenerateStepsResponse {
  success: boolean
  data?: {
    steps: any[]
    name?: string
    description?: string
    remainingGenerations?: number
  }
  error?: string
}

interface CreateScenarioRequest {
  formData: ScenarioFormData
  userId: string
  teamId: string
  companyId: string
}

interface CreateScenarioResponse {
  success: boolean
  data?: any
  error?: string
}

/**
 * Service class for scenario creation operations
 */
export class ScenarioCreationService {
  /**
   * Generate test steps using AI
   */
  static async generateSteps(request: GenerateStepsRequest): Promise<GenerateStepsResponse> {
    try {
      if (!request.prompt.trim()) {
        return {
          success: false,
          error: 'Prompt is required for step generation'
        }
      }

      // Determine if this is a test management import
      const isTestManagement = request.isTestManagement || false

      const response = await generateStepsApi.generateSteps({
        prompt: request.prompt,
        name: request.name || '',
        description: request.description || '',
        isTestManagement: isTestManagement,
        testManagementProvider: request.testManagementProvider,
        testDataVariables: request.testDataVariables || [],
        creativityLevel: request.creativityLevel
      })

      if (!response.success) {
        // Handle specific error cases
        if (response.error && response.error.includes('generation hakkı')) {
          toast.error('Generation Limiti Aşıldı', {
            description: response.error || 'Şirketinizin senaryo generation hakkı kalmamıştır. Lütfen yöneticinizle iletişime geçin.'
          })
        }
        
        return {
          success: false,
          error: response.error || 'Failed to generate steps'
        }
      }

      // Extract data from response wrapper
      const data = response.data || response

      if (!data.steps || !Array.isArray(data.steps)) {
        return {
          success: false,
          error: 'No valid steps generated'
        }
      }

      return {
        success: true,
        data: {
          steps: data.steps,
          name: data.name,
          description: data.description,
          remainingGenerations: data.remainingGenerations
        }
      }
    } catch (error: any) {
      console.error('Error generating steps:', error)
      return {
        success: false,
        error: error.message || 'Could not generate steps'
      }
    }
  }

  /**
   * Transform API steps to frontend format
   */
  static transformStepsToFrontendFormat(apiSteps: any[]): TestStep[] {
    return apiSteps.map((step: any) => {
      const formattedStep: TestStep = {
        id: step.id || this.generateStepId(),
        type: step.type,
        name: '',
        value: step.value || '',
        description: '',
        prompt: step.value || '',
        target: step.target || '',
        deepThink: step.deepThink || false,
      }

      // Type-specific transformations
      if (step.type === 'goto') {
        // For goto steps, prioritize URL field from API response
        const url = step.url || step.value || ''
        formattedStep.url = url
        formattedStep.value = url  // Set value to the URL, not the description
        formattedStep.name = step.value || (url ? `Navigate to ${url}` : 'Navigate to page')
        // Description should contain the human-readable description if provided
        formattedStep.description = step.value || `Navigate to ${url}`
      } else if (step.type === 'sleep') {
        // Handle duration field for sleep steps - API expects milliseconds
        const durationMs = step.duration || parseInt(step.value || '1000')
        formattedStep.duration = Math.max(1, Math.floor(durationMs / 1000)) // Convert to seconds for frontend
        formattedStep.value = step.value || `Wait for ${formattedStep.duration} second${formattedStep.duration !== 1 ? 's' : ''}`
        formattedStep.prompt = step.value || formattedStep.value
      } else if (step.type === 'aiInput') {
        // Handle aiInput specific fields
        formattedStep.value = step.value || ''
        formattedStep.target = step.target || ''
        formattedStep.text = step.text || step.value || ''
        formattedStep.prompt = step.value || ''
      } else if (step.type === 'aiWaitElement') {
        // Handle aiWaitElement specific fields
        formattedStep.value = step.value || ''
        // Duration from API is in milliseconds, keep as-is for frontend
        formattedStep.duration = step.duration || 5000 // Default 5 seconds in ms
        formattedStep.timeoutMs = step.timeoutMs || step.duration || 5000
        formattedStep.checkIntervalMs = step.checkIntervalMs || 1000
        formattedStep.prompt = step.value || ''
      } else if (this.isControlFlowStep(step.type)) {
        // Handle control flow steps from API
        if (step.type === 'ifElse') {
          formattedStep.condition = step.condition || step.value || ''
          formattedStep.value = step.condition || step.value || ''
          formattedStep.trueSteps = step.trueSteps ? this.transformStepsToFrontendFormat(step.trueSteps) : []
          formattedStep.falseSteps = step.falseSteps ? this.transformStepsToFrontendFormat(step.falseSteps) : []
        } else if (step.type === 'forLoop') {
          formattedStep.iterationCount = step.iterationCount || step.value || ''
          formattedStep.value = step.iterationCount || step.value || ''
          formattedStep.loopSteps = step.loopSteps ? this.transformStepsToFrontendFormat(step.loopSteps) : []
        } else if (step.type === 'whileLoop') {
          formattedStep.condition = step.condition || step.value || ''
          formattedStep.value = step.condition || step.value || ''
          formattedStep.loopSteps = step.loopSteps ? this.transformStepsToFrontendFormat(step.loopSteps) : []
          formattedStep.maxIterations = step.maxIterations || 50
        }
      } else if (this.isAIStep(step.type)) {
        // Handle other AI steps
        formattedStep.value = step.value || ''
        formattedStep.prompt = step.prompt || step.value || ''
        formattedStep.deepThink = step.deepThink || false
      }

      return formattedStep
    })
  }

  /**
   * Transform frontend steps to API format
   */
  static transformStepsToApiFormat(frontendSteps: TestStep[]): any[] {
    return frontendSteps.map(step => {
      const apiStep: any = {
        id: step.id,
        type: step.type,
        deepThink: step.deepThink || false
      }

      // Type-specific transformations
      if (step.type === 'goto') {
        // For goto steps, only send URL field
        apiStep.url = step.url || ''
      } else if (step.type === 'sleep') {
        apiStep.value = step.duration ? String(step.duration * 1000) : '1000'
      } else if (step.type === 'aiInput') {
        // For aiInput: value contains text to input, target specifies where to input
        apiStep.value = step.value || '' // The text to input
        apiStep.target = step.target || '' // Where to input the text
      } else if (this.isAIStep(step.type)) {
        // For AI steps, only send prompt field
        apiStep.prompt = step.prompt || step.description || ''

        // Add AI-specific options for aiWaitElement
        if (step.type === 'aiWaitElement') {
          if (step.timeoutMs) {
            apiStep.timeoutMs = step.timeoutMs
          }
          if (step.checkIntervalMs) {
            apiStep.checkIntervalMs = step.checkIntervalMs
          }
        }
      } else if (this.isControlFlowStep(step.type)) {
        // Handle control flow steps
        if (step.type === 'ifElse') {
          apiStep.condition = step.condition || ''
          apiStep.trueSteps = step.trueSteps ? this.transformStepsToApiFormat(step.trueSteps) : []
          apiStep.falseSteps = step.falseSteps ? this.transformStepsToApiFormat(step.falseSteps) : []
        } else if (step.type === 'forLoop') {
          apiStep.iterationCount = step.iterationCount || ''
          apiStep.loopSteps = step.loopSteps ? this.transformStepsToApiFormat(step.loopSteps) : []
        } else if (step.type === 'whileLoop') {
          apiStep.condition = step.condition || ''
          apiStep.loopSteps = step.loopSteps ? this.transformStepsToApiFormat(step.loopSteps) : []
          apiStep.maxIterations = step.maxIterations || 50 // Default safety limit
        }
      } else {
        apiStep.name = step.description || ''
        apiStep.description = step.description || ''
        apiStep.value = step.value || step.description || ''
      }

      return apiStep
    })
  }

  /**
   * Create a new scenario
   */
  static async createScenario(request: CreateScenarioRequest): Promise<CreateScenarioResponse> {
    try {
      const { formData, userId, teamId, companyId } = request

      // Validate required fields
      if (!formData.name.trim()) {
        return {
          success: false,
          error: 'Scenario name is required'
        }
      }

      if (!formData.steps.length) {
        return {
          success: false,
          error: 'At least one test step is required'
        }
      }

      // Get dataset information from store
      const testDataStore = useScenarioTestDataStore.getState()
      const selectedDataSet = testDataStore.selectedDataSet
      const variables = testDataStore.variables

      // ENHANCED DEBUG: Log dataset selection
      console.log('🔍 SCENARIO CREATION DEBUG:');
      console.log('  selectedDataSet:', selectedDataSet);
      console.log('  variables:', variables);
      console.log('  testDataSetId will be:', selectedDataSet?.id || 'undefined');

      // Prepare scenario data for API
      const scenarioData = {
        name: formData.name,
        description: formData.description,
        tags: formData.tags,
        platform: formData.platform || 'web',
        steps: this.transformStepsToApiFormat(formData.steps),
        folderId: formData.folderId,
        userId,
        teamId,
        companyId,
        testDataSetId: selectedDataSet?.id || undefined,
        metadata: selectedDataSet ? {
          variables: variables || []
        } : undefined,
        testrailIntegration: formData.testrailCases.length > 0 ? {
          caseIds: formData.testrailCases,
          sync: formData.testrailSync
        } : undefined,
        zephyrscaleIntegration: formData.zephyrscaleCases?.length > 0 ? {
          caseIds: formData.zephyrscaleCases,
          sync: formData.zephyrscaleSync || false
        } : undefined
      }

      const response = await scenarioApi.createScenario(scenarioData)

      if (!response.success) {
        return {
          success: false,
          error: response.error || 'Failed to create scenario'
        }
      }

      // Extract data from new response format
      const responseData = response.data || response
      const scenarioId = responseData.scenarioId || response.scenarioId
      const message = responseData.message || response.message

      console.log('[ScenarioCreationService] Scenario created successfully on backend:', scenarioId)

      return {
        success: true,
        data: {
          scenarioId,
          message
        }
      }
    } catch (error: any) {
      console.error('Error creating scenario:', error)
      return {
        success: false,
        error: error.message || 'Could not create scenario'
      }
    }
  }

  /**
   * Validate scenario form data
   */
  static validateScenarioData(formData: ScenarioFormData): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!formData.name.trim()) {
      errors.push('Scenario name is required')
    }

    if (formData.name.trim().length < 3) {
      errors.push('Scenario name must be at least 3 characters')
    }

    if (!formData.steps.length) {
      errors.push('At least one test step is required')
    }

    if (formData.description && formData.description.length > 500) {
      errors.push('Description must be less than 500 characters')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Check if a step type is an AI step
   */
  private static isAIStep(type: string): boolean {
    const aiSteps = [
      'aiAction', 'aiAssertion', 'aiWaitElement', 'aiLocate', 'aiTap', 
      'aiHover', 'aiKeyboardPress', 'aiScroll', 'aiRightClick', 
      'aiQuery', 'aiString', 'aiNumber', 'aiBoolean'
    ]
    return aiSteps.includes(type)
  }

  /**
   * Check if a step type is a control flow step
   */
  private static isControlFlowStep(type: string): boolean {
    const controlFlowSteps = ['ifElse', 'forLoop', 'whileLoop']
    return controlFlowSteps.includes(type)
  }

  /**
   * Generate a unique step ID
   */
  private static generateStepId(): string {
    return `step_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * Clean form data for submission
   */
  static cleanFormData(formData: ScenarioFormData): ScenarioFormData {
    return {
      ...formData,
      name: formData.name.trim(),
      description: formData.description.trim(),
      tags: formData.tags.filter(tag => tag.trim().length > 0),
      steps: formData.steps.filter(step => (step.value && step.value.trim().length > 0) || step.type === 'sleep' || step.type === 'goto')
    }
  }

  /**
   * Get step type configuration
   */
  static getStepTypeConfig() {
    return {
      actionGroups: [
        {
          label: "Instant Actions",
          actions: ["aiTap", "aiInput", "aiHover", "aiKeyboardPress", "aiScroll", "aiRightClick", "aiAssertion", "aiWaitElement"]
        },
        {
          label: "Data Extraction", 
          actions: ["aiQuery", "aiString", "aiNumber", "aiBoolean", "aiLocate"]
        },
        {
          label: "Control Flow",
          actions: ["ifElse", "forLoop", "whileLoop"]
        },
        {
          label: "High-Level Goal",
          actions: ["aiAction"]
        },
        {
          label: "Browser Control",
          actions: ["goto", "sleep"]
        }
      ]
    }
  }
}

export type { GenerateStepsRequest, GenerateStepsResponse, CreateScenarioRequest, CreateScenarioResponse }
