/**
 * Atomic State Manager Service
 *
 * Manages NodeStatus state with atomic operations to prevent race conditions.
 * Focused on node-level state only - TestExecutionState is managed by test-execution module.
 * Implemented with dependency injection.
 */

import { NodeStatus } from '../../types/node-manager-types.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

/**
 * Node state representing only node-level status
 */
export interface AtomicNodeState {
  nodeStatus: NodeStatus;
  currentTestId: string | null;
  isStopping: boolean;
  timestamp: number;
}

/**
 * State transition request
 */
export interface StateTransitionRequest {
  fromNodeStatus?: NodeStatus;
  toNodeStatus?: NodeStatus;
  testId?: string;
  reason?: string;
}

/**
 * State transition result
 */
export interface StateTransitionResult {
  success: boolean;
  previousState: AtomicNodeState;
  newState: AtomicNodeState;
  reason?: string;
}

/**
 * Atomic State Manager Service
 * 
 * Provides thread-safe, atomic state management for both node-level and execution-level states.
 * Prevents race conditions by using a single global lock and atomic compare-and-swap operations.
 * Implemented with dependency injection instead of singleton pattern.
 */
export class AtomicStateManager {
  // Global state lock - prevents concurrent state modifications
  private globalStateLock: boolean = false;
  private lockAcquisitionQueue: Array<() => Promise<void>> = [];
  private lockTimeout: number = 5000; // 5 second timeout for lock acquisition
  private logger: ILoggerService;

  // Current atomic state
  private currentState: AtomicNodeState = {
    nodeStatus: NodeStatus.AVAILABLE,
    currentTestId: null,
    isStopping: false,
    timestamp: Date.now()
  };

  // State transition validation rules
  private validNodeTransitions: Record<NodeStatus, NodeStatus[]> = {
    [NodeStatus.AVAILABLE]: [NodeStatus.BUSY, NodeStatus.STOPPING, NodeStatus.ERROR],
    [NodeStatus.BUSY]: [NodeStatus.AVAILABLE, NodeStatus.STOPPING, NodeStatus.ERROR],
    [NodeStatus.STOPPING]: [NodeStatus.AVAILABLE, NodeStatus.ERROR],
    [NodeStatus.OFFLINE]: [NodeStatus.AVAILABLE, NodeStatus.ERROR],
    [NodeStatus.ERROR]: [NodeStatus.AVAILABLE, NodeStatus.STOPPING]
  };



  constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('AtomicStateManager');
    this.logger.serviceInit('AtomicStateManager', 'Initialized node state manager with dependency injection');
  }

  /**
   * Get current atomic state (read-only copy)
   */
  getCurrentState(): AtomicNodeState {
    return { ...this.currentState };
  }

  /**
   * Get current node status
   */
  getNodeStatus(): NodeStatus {
    return this.currentState.nodeStatus;
  }



  /**
   * Get current test ID
   */
  getCurrentTestId(): string | null {
    return this.currentState.currentTestId;
  }

  /**
   * Check if node is stopping
   */
  isStopping(): boolean {
    return this.currentState.isStopping;
  }

  /**
   * Check if a test is currently running (based on node status)
   */
  isTestRunning(): boolean {
    return this.currentState.nodeStatus === NodeStatus.BUSY;
  }

  /**
   * Get complete state snapshot
   */
  getState(): AtomicNodeState {
    return { ...this.currentState };
  }



  /**
   * Acquire global state lock with timeout
   */
  private async acquireStateLock(): Promise<boolean> {
    if (!this.globalStateLock) {
      this.globalStateLock = true;
      return true;
    }

    // If lock is held, wait in queue with timeout
    return new Promise((resolve) => {
      const timeoutId = setTimeout(() => {
        // Remove from queue and resolve with false
        const index = this.lockAcquisitionQueue.findIndex(fn => fn === queuedFunction);
        if (index !== -1) {
          this.lockAcquisitionQueue.splice(index, 1);
        }
        resolve(false);
      }, this.lockTimeout);

      const queuedFunction = async () => {
        clearTimeout(timeoutId);
        this.globalStateLock = true;
        resolve(true);
      };

      this.lockAcquisitionQueue.push(queuedFunction);
    });
  }

  /**
   * Release global state lock and process queue
   */
  private releaseStateLock(): void {
    this.globalStateLock = false;

    // Process next queued lock acquisition
    if (this.lockAcquisitionQueue.length > 0) {
      const nextFunction = this.lockAcquisitionQueue.shift();
      if (nextFunction) {
        // Execute next queued function asynchronously
        setImmediate(() => nextFunction());
      }
    }
  }

  /**
   * Validate if a state transition is allowed
   */
  private isValidTransition(request: StateTransitionRequest): boolean {
    // Validate node status transition if specified
    if (request.fromNodeStatus !== undefined && request.toNodeStatus !== undefined) {
      const validTargets = this.validNodeTransitions[request.fromNodeStatus];
      if (!validTargets || !validTargets.includes(request.toNodeStatus)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Atomic compare-and-swap state transition
   */
  async atomicTransition(request: StateTransitionRequest): Promise<StateTransitionResult> {
    // Acquire global lock with timeout
    const lockAcquired = await this.acquireStateLock();
    if (!lockAcquired) {
      this.logger.error('AtomicStateManager: Failed to acquire state lock within timeout');
      return {
        success: false,
        previousState: this.getCurrentState(),
        newState: this.getCurrentState(),
        reason: 'Lock acquisition timeout'
      };
    }

    try {
      const previousState = { ...this.currentState };

      // Validate current state matches expected state
      if (request.fromNodeStatus !== undefined && this.currentState.nodeStatus !== request.fromNodeStatus) {
        return {
          success: false,
          previousState,
          newState: previousState,
          reason: `Node status mismatch: expected ${request.fromNodeStatus}, got ${this.currentState.nodeStatus}`
        };
      }



      // Validate transition is allowed
      if (!this.isValidTransition(request)) {
        return {
          success: false,
          previousState,
          newState: previousState,
          reason: 'Invalid state transition'
        };
      }

      // Perform atomic state update
      const newState: AtomicNodeState = {
        nodeStatus: request.toNodeStatus ?? this.currentState.nodeStatus,
        currentTestId: request.testId ?? this.currentState.currentTestId,
        isStopping: request.toNodeStatus === NodeStatus.STOPPING ? true :
                   request.toNodeStatus === NodeStatus.AVAILABLE ? false :
                   this.currentState.isStopping,
        timestamp: Date.now()
      };

      // Additional validation for test ID consistency
      if (newState.nodeStatus === NodeStatus.BUSY && !newState.currentTestId) {
        return {
          success: false,
          previousState,
          newState: previousState,
          reason: 'Test ID required for BUSY node status'
        };
      }

      // Commit the state change
      this.currentState = newState;

      this.logger.info(`AtomicStateManager: Successful transition - Node: ${previousState.nodeStatus} → ${newState.nodeStatus}, Test: ${newState.currentTestId || 'none'}`);

      return {
        success: true,
        previousState,
        newState,
        reason: request.reason
      };

    } finally {
      this.releaseStateLock();
    }
  }

  /**
   * Convenience method for node status transitions
   */
  async transitionNodeStatus(
    fromStatus: NodeStatus,
    toStatus: NodeStatus,
    testId?: string,
    reason?: string
  ): Promise<boolean> {
    const result = await this.atomicTransition({
      fromNodeStatus: fromStatus,
      toNodeStatus: toStatus,
      testId,
      reason
    });
    return result.success;
  }



  /**
   * Backward compatibility method for NodeStateManager.trySetState()
   */
  trySetState(fromState: NodeStatus, toState: NodeStatus, testId?: string): boolean {
    // For backward compatibility, we need to handle this synchronously
    // In practice, if there's no lock contention, this will resolve immediately
    if (this.globalStateLock) {
      this.logger.warn(`AtomicStateManager: trySetState called while lock is held - transition from ${fromState} to ${toState} failed`);
      return false;
    }

    // Perform immediate transition if lock is available
    this.globalStateLock = true;
    try {
      // Validate current state
      if (this.currentState.nodeStatus !== fromState) {
        this.logger.warn(`AtomicStateManager: trySetState - current state ${this.currentState.nodeStatus} does not match expected ${fromState}`);
        return false;
      }

      // Validate transition
      const validTargets = this.validNodeTransitions[fromState];
      if (!validTargets || !validTargets.includes(toState)) {
        this.logger.warn(`AtomicStateManager: trySetState - invalid transition from ${fromState} to ${toState}`);
        return false;
      }

      // Perform state update
      this.currentState = {
        ...this.currentState,
        nodeStatus: toState,
        currentTestId: testId ?? this.currentState.currentTestId,
        isStopping: toState === NodeStatus.STOPPING ? true :
                   toState === NodeStatus.AVAILABLE ? false :
                   this.currentState.isStopping,
        timestamp: Date.now()
      };

      this.logger.info(`AtomicStateManager: trySetState successful - ${fromState} → ${toState}, Test: ${testId || 'none'}`);
      return true;

    } finally {
      this.releaseStateLock();
    }
  }

  /**
   * Reset state to initial values
   */
  async reset(): Promise<void> {
    const lockAcquired = await this.acquireStateLock();
    if (!lockAcquired) {
      this.logger.error('AtomicStateManager: Failed to acquire lock for reset operation');
      return;
    }

    try {
      this.currentState = {
        nodeStatus: NodeStatus.AVAILABLE,
        currentTestId: null,
        isStopping: false,
        timestamp: Date.now()
      };
      this.logger.info('AtomicStateManager: State reset to initial values');
    } finally {
      this.releaseStateLock();
    }
  }

  /**
   * Force state update (emergency use only)
   */
  async forceState(newState: Partial<AtomicNodeState>): Promise<void> {
    const lockAcquired = await this.acquireStateLock();
    if (!lockAcquired) {
      this.logger.error('AtomicStateManager: Failed to acquire lock for force state operation');
      return;
    }

    try {
      this.currentState = {
        ...this.currentState,
        ...newState,
        timestamp: Date.now()
      };
      this.logger.warn(`AtomicStateManager: Force state update applied - ${JSON.stringify(newState)}`);
    } finally {
      this.releaseStateLock();
    }
  }
}
