/**
 * Step Progress Queue Worker
 * Processes step progress jobs from Redis queue and handles storage + UI broadcast
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { redisConnection } from '../redis/redisConnection.js';
import { QUEUE_NAMES } from '../redis/queueService.js';
import { stepProgressService } from './stepProgressService.js';

// Import from bullmq directly since it's already in package.json
import { Job, Worker } from 'bullmq';

export interface StepProgressJobData {
  testId: string;
  nodeId: string;
  transactionId: string;
  timestamp: string;
  stepProgress: {
    type: string;
    stepIndex?: number;
    totalSteps?: number;
    stepId?: string;
    stepName?: string;
    stepType?: string;
    status: 'started' | 'completed' | 'failed';
    timestamp: string;
    error?: string;
    duration?: number;
    result?: any;
    runId?: string;
    executionId?: string;
    scenarioId?: string;
    nodeId?: string;
    parentStepId?: string;
    nestingLevel?: number;
  };
}

export interface StepProgressQueueWorkerEvents {
  'step-progress:processed': (testId: string, jobId: string) => void;
  'step-progress:failed': (testId: string, jobId: string, error: Error) => void;
  'step-progress:ui-broadcast': (testId: string, data: any) => void;
}

/**
 * Step Progress Queue Worker
 * Processes step progress jobs from Redis BullMQ queue
 */
class StepProgressQueueWorker extends EventEmitter {
  private worker: Worker | null = null;
  private isInitialized: boolean = false;
  private websocketConnector: any = null;

  constructor() {
    super();
  }

  /**
   * Initialize the step progress queue worker
   */
  public async initialize(websocketConnector?: any): Promise<boolean> {
    try {
      if (this.isInitialized) {
        logger.debug('StepProgressQueueWorker: Already initialized');
        return true;
      }

      // Store WebSocket connector for UI broadcasts
      this.websocketConnector = websocketConnector;

      // Ensure Redis connection is established
      const redisClient = redisConnection.getClient();
      if (!redisClient) {
        logger.error('StepProgressQueueWorker: Redis client not available');
        return false;
      }

      // Create worker for step progress queue
      this.worker = new Worker(
        QUEUE_NAMES.STEP_PROGRESS_QUEUE,
        async (job: Job<StepProgressJobData>) => {
          return await this.processStepProgressJob(job);
        },
        {
          connection: redisClient,
          concurrency: 5, // Process up to 5 step progress jobs concurrently
          stalledInterval: 30000, // Check for stalled jobs every 30 seconds
          maxStalledCount: 3,
          // Keep step progress for UI display - remove after 30 minutes for active monitoring
          removeOnComplete: {
            count: 50, // Keep more completed jobs for UI display
            age: 30 * 60 * 1000 // Remove after 30 minutes to allow UI monitoring
          },
          removeOnFail: {
            count: 20, // Keep only last 20 failed jobs
            age: 30 * 60 * 1000 // Remove after 30 minutes
          }
        }
      );

      // Set up worker event handlers
      this.worker.on('completed', (job: Job) => {
        logger.debug(`StepProgressQueueWorker: Completed job ${job.id} for test ${job.data?.testId}`);
        this.emit('step-progress:processed', job.data?.testId, job.id);
      });

      this.worker.on('failed', (job: Job | undefined, error: Error) => {
        logger.error(`StepProgressQueueWorker: Failed job ${job?.id} for test ${job?.data?.testId}: ${error.message}`);
        this.emit('step-progress:failed', job?.data?.testId, job?.id, error);
      });

      this.worker.on('error', (error: Error) => {
        logger.error(`StepProgressQueueWorker: Worker error: ${error.message}`);
      });

      this.isInitialized = true;
      logger.info('StepProgressQueueWorker: Successfully initialized step progress queue worker');
      return true;

    } catch (error: any) {
      logger.error(`StepProgressQueueWorker: Initialization failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Process a step progress job
   */
  private async processStepProgressJob(job: Job<StepProgressJobData>): Promise<void> {
    const { testId, nodeId, transactionId, stepProgress } = job.data;

    try {
      logger.debug(`StepProgressQueueWorker: Processing step progress job ${job.id} for test ${testId}, transaction ${transactionId}`);

      // Handle nested steps differently - they have type 'nestedStep' and no stepIndex
      const isNestedStep = stepProgress.type === 'nestedStep' || stepProgress.stepType === 'nestedStep';
      
      if (isNestedStep) {
        // For nested steps, we need to handle them as part of the parent step progress
        // Store as a separate nested step progress entry
        await stepProgressService.updateNestedStepProgress(testId, {
          nodeId,
          stepId: stepProgress.stepId || `nested-${Date.now()}`,
          stepName: stepProgress.stepName || 'Nested Step',
          stepType: stepProgress.stepType || 'nestedStep',
          status: stepProgress.status || 'started',
          timestamp: stepProgress.timestamp || new Date().toISOString(),
          platform: 'web', // Default platform
          runId: stepProgress.runId,
          executionId: stepProgress.executionId,
          scenarioId: stepProgress.scenarioId,
          result: stepProgress.result,
          parentStepId: stepProgress.parentStepId,
          nestingLevel: stepProgress.nestingLevel || 1
        });
      } else {
        // Store regular step progress in Redis using the step progress service
        await stepProgressService.updateStepProgress(testId, {
          nodeId,
          stepIndex: stepProgress.stepIndex || 0,
          totalSteps: stepProgress.totalSteps || 0,
          stepId: stepProgress.stepId || `step-${stepProgress.stepIndex || 0}`,
          stepName: stepProgress.stepName || 'Unknown Step',
          stepType: stepProgress.stepType || 'unknown',
          status: stepProgress.status || 'started',
          timestamp: stepProgress.timestamp || new Date().toISOString(),
          platform: 'web', // Default platform
          runId: stepProgress.runId,
          executionId: stepProgress.executionId,
          scenarioId: stepProgress.scenarioId,
          result: stepProgress.result
        });
      }

      const stepInfo = stepProgress.stepIndex !== undefined 
        ? `${stepProgress.stepIndex}/${stepProgress.totalSteps || 'N/A'}`
        : 'nested';
      logger.debug(`StepProgressQueueWorker: Updated step progress for test ${testId} - Step ${stepInfo}: ${stepProgress.stepName || 'unknown'}`);

      // Broadcast step progress to UI clients via WebSocket
      if (this.websocketConnector && typeof this.websocketConnector.broadcastToAll === 'function') {
        const broadcastData = {
          testId,
          runId: stepProgress.runId,
          executionId: stepProgress.executionId,
          scenarioId: stepProgress.scenarioId,
          stepIndex: stepProgress.stepIndex || 0,
          totalSteps: stepProgress.totalSteps || 0,
          stepName: stepProgress.stepName || 'Unknown Step',
          stepType: stepProgress.stepType || 'unknown',
          status: stepProgress.status || 'started',
          timestamp: stepProgress.timestamp || new Date().toISOString(),
          platform: 'web',
          nodeId,
          result: stepProgress.result
        };

        this.websocketConnector.broadcastToAll('step_progress_update', broadcastData);
        logger.debug(`StepProgressQueueWorker: Broadcasted step progress update to UI clients for test ${testId}`);
        
        this.emit('step-progress:ui-broadcast', testId, broadcastData);
      } else {
        logger.warn('StepProgressQueueWorker: WebSocket connector not available for UI broadcast');
      }

      logger.info(`StepProgressQueueWorker: Successfully processed step progress for test ${testId}, transaction ${transactionId}`);

    } catch (error: any) {
      logger.error(`StepProgressQueueWorker: Error processing step progress job ${job.id} for test ${testId}: ${error.message}`);
      throw error; // Re-throw to mark job as failed
    }
  }

  /**
   * Check if the worker is initialized
   */
  public isWorkerInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get worker statistics
   */
  public async getStats(): Promise<{
    isRunning: boolean;
    concurrency: number;
  } | null> {
    if (!this.worker) {
      return null;
    }

    try {
      return {
        isRunning: this.worker.isRunning(),
        concurrency: this.worker.opts.concurrency || 1
      };
    } catch (error: any) {
      logger.error(`StepProgressQueueWorker: Error getting stats: ${error.message}`);
      return null;
    }
  }

  /**
   * Close the worker
   */
  public async close(): Promise<void> {
    if (this.worker) {
      await this.worker.close();
      this.worker = null;
    }
    this.isInitialized = false;
    logger.info('StepProgressQueueWorker: Closed step progress queue worker');
  }
}

/**
 * Factory function for creating StepProgressQueueWorker instances
 * Used by DI container for dependency injection
 */
export function createStepProgressQueueWorker(): StepProgressQueueWorker {
  return new StepProgressQueueWorker();
}

// Export class for DI container registration
export { StepProgressQueueWorker };
