"use client";

import {
  create<PERSON>ontext,
  useContext,
  useState,
  ReactNode,
  useCallback,
  useEffect,
} from "react";
import { useRuns as useRunsBase, Run } from "@/hooks/useRuns";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "@/lib/utils/toast-utils";
import { useAuth } from "@/lib/api/auth";
import { AndroidEnvironmentSettings } from "@/types/android-environment";
import { dataEnvironmentApi } from "@/lib/api/test-data";
import { DeviceProviderUtils } from "@/types/device-provider";
import { scenarioApi } from "@/lib/api";
import { Scenario } from "@/types/scenario";

// Platform types
export type PlatformType = "web" | "android";

// Web-specific environment settings
export interface WebEnvironmentSettings {
  platform: "web"; // Explicitly indicate platform
  browser: string;
  viewport: { width: number; height: number };
  userAgent?: string;
  device?: string;
  geolocation?: { latitude: number; longitude: number };
  aiModel?: string;
  proxy?: ProxySettings;
}

// Common proxy settings
export interface ProxySettings {
  enabled: boolean;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
}

// Common report settings
export interface BaseReportSettings {
  takeScreenshots: boolean;
  takeVideos: boolean;
}

// Web-specific report settings
export interface WebReportSettings extends BaseReportSettings {
  pageMetrics: boolean;
  networkData: boolean;
  tracingData: boolean;
  accessibilityData: boolean;
}

// Android-specific report settings
export interface AndroidReportSettings extends BaseReportSettings {
  collectMetrics?: boolean; // Whether to collect performance metrics
  videoQuality?: "low" | "medium" | "high"; // Video recording quality
  metricsInterval?: number; // Interval for collecting metrics in milliseconds
}

// Combined environment settings type - use discriminated union instead of intersection
export type EnvironmentSettings =
  | WebEnvironmentSettings
  | AndroidEnvironmentSettings;

// Combined report settings type - use union instead of intersection to prevent field contamination
export type ReportSettings = WebReportSettings | AndroidReportSettings;

// Test Data Environment interface
export interface TestDataEnvironment {
  id: string;
  name: string;
  description?: string;
  color?: string;
  type: "default" | "custom";
  isActive: boolean;
}

interface IRunFormContext {
  // Temel form durumu
  currentStep: number;
  formData: Partial<Run> & {
    scenarios?: Array<{ id: string; selected: boolean; [key: string]: any }>;
    platform?: PlatformType;
    testDataEnvironmentId?: string;
    testDataEnvironmentName?: string;
  };

  // Step fonksiyonları
  nextStep: () => void;
  prevStep: () => void;
  setStep: (step: number) => void;

  // Form data fonksiyonları
  updateFormData: (data: Partial<Run>) => void;
  setFormDataFromExisting: (
    data: Partial<Run> & {
      scenarios?: Array<{ id: string; selected: boolean; [key: string]: any }>;
    },
  ) => void;

  // Platform değişikliği
  handlePlatformChange: (platform: PlatformType) => void;

  // Senaryolar
  selectedScenarios: string[];
  setSelectedScenarios: (ids: string[]) => void;
  selectedScenariosDetails: any[]; // Seçili senaryoların tam detayları
  updateSelectedScenariosDetails: (details: any[]) => void;

  // Çevre ayarları
  environmentSettings: EnvironmentSettings;
  updateEnvironmentSettings: (data: Partial<EnvironmentSettings>) => void;

  // Rapor ayarları
  reportSettings: ReportSettings;
  updateReportSettings: (data: Partial<ReportSettings>) => void;

  // Test Data Environment
  testDataEnvironments: TestDataEnvironment[];
  selectedTestDataEnvironment: TestDataEnvironment | null;
  isLoadingTestDataEnvironments: boolean;
  loadTestDataEnvironments: () => Promise<void>;
  selectTestDataEnvironment: (environment: TestDataEnvironment | null) => void;

  // Form işlemleri
  isSubmitting: boolean;
  submitRun: () => Promise<{ success: boolean; error?: string } | undefined>;

  // Doğrulama durumu
  errors: Record<string, string>;
  validateCurrentStep: () => boolean;
}

const RunFormContext = createContext<IRunFormContext | undefined>(undefined);

export const useRunForm = () => {
  const context = useContext(RunFormContext);
  if (!context) {
    throw new Error("useRunForm must be used within a RunFormProvider");
  }
  return context;
};

interface RunFormProviderProps {
  children: ReactNode;
  onRunCreated?: (runId: string) => void;
  isEditMode?: boolean;
}

// Custom hook to prevent automatic data fetching
function useCustomRuns(isEditMode: boolean = false) {
  return useRunsBase({
    autoFetch: false, // Disable automatic data fetching to prevent unnecessary API calls
  });
}

export const RunFormProvider = ({
  children,
  onRunCreated,
  isEditMode = false,
}: RunFormProviderProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const { createRun, executeRun, updateRun } = useCustomRuns(isEditMode);

  // Form durumları
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Test Data Environment durumları
  const [testDataEnvironments, setTestDataEnvironments] = useState<
    TestDataEnvironment[]
  >([]);
  const [selectedTestDataEnvironment, setSelectedTestDataEnvironment] =
    useState<TestDataEnvironment | null>(null);
  const [isLoadingTestDataEnvironments, setIsLoadingTestDataEnvironments] =
    useState(false);

  // Form verisi
  const [formData, setFormData] = useState<Partial<Run>>({
    name: "",
    description: "",
    tags: [],
    platform: "web", // Default to web platform
    environment: {
      browser: "chrome",
      viewport: {
        width: 1920,
        height: 1080,
      },
      device: "Desktop (1920x1080)",
      aiModel: "default",
      proxy: {
        enabled: false,
        type: "HTTP",
        host: "",
        port: 8080,
        username: "",
        password: "",
      },
      // Removed Android-specific settings as they'll be added when platform changes
    },
    reportSettings: {
      takeScreenshots: true,
      takeVideos: false,
    },
  });

  // Seçilen senaryolar
  const [selectedScenarios, setSelectedScenarios] = useState<string[]>([]);
  const [selectedScenariosDetails, setSelectedScenariosDetails] = useState<
    any[]
  >([]);

  // State to track if initial scenarios have been loaded from URL
  const [initialScenariosLoaded, setInitialScenariosLoaded] = useState(false);

  // Çevre ayarları - default olarak web ayarları
  const [environmentSettings, setEnvironmentSettings] =
    useState<EnvironmentSettings>({
      platform: "web", // Explicitly set platform for web
      browser: "chrome",
      viewport: { width: 1920, height: 1080 },
      userAgent: "",
      device: "Desktop (1920x1080)",
      geolocation: undefined,
      aiModel: "default",
      proxy: {
        enabled: false,
        type: "HTTP",
        host: "",
        port: 8080,
        username: "",
        password: "",
      },
      // Removed Android-specific settings as they'll be added when platform changes
    });

  // Rapor ayarları - Start with only common fields, platform-specific fields added on platform change
  const [reportSettings, setReportSettings] = useState<ReportSettings>({
    takeScreenshots: true,
    takeVideos: false,
  });

  // Load scenarios from URL query params
  useEffect(() => {
    if (isEditMode || initialScenariosLoaded) {
      return;
    }

    const scenarioIdsFromQuery = searchParams.get("scenarios");
    if (scenarioIdsFromQuery) {
      const scenarioIds = scenarioIdsFromQuery.split(",");

      const fetchInitialScenarios = async () => {
        try {
          const scenarioPromises = scenarioIds.map((id) =>
            scenarioApi.getScenarioById(id),
          );
          const responses = await Promise.all(scenarioPromises);

          const scenarios: Scenario[] = responses
            .map((res) => (res.success ? res.data : null))
            .filter((s): s is Scenario => s !== null);

          if (scenarios.length > 0) {
            setSelectedScenarios(scenarios.map((s) => s.id));
            setSelectedScenariosDetails(scenarios);
          }
        } catch (error) {
          console.error("Failed to fetch initial scenarios:", error);
          toast({
            title: "Error",
            description: "Failed to load scenarios specified in the URL.",
            variant: "destructive",
          });
        } finally {
          setInitialScenariosLoaded(true);
        }
      };

      fetchInitialScenarios();
    } else {
      // If no scenarios in query, mark as loaded to prevent re-running
      setInitialScenariosLoaded(true);
    }
  }, [searchParams, isEditMode, initialScenariosLoaded]);

  // Platform değişikliği yönetimi
  const handlePlatformChange = useCallback(
    (platform: PlatformType) => {
      // Mevcut form verisini koru
      const updatedFormData = { ...formData, platform };

      // Platform değiştiğinde ilgili alanları sıfırla
      if (platform === "web") {
        // Web için varsayılan değerleri ayarla
        const webEnvironment: WebEnvironmentSettings = {
          platform: "web", // Explicitly set platform
          browser: "chrome",
          viewport: { width: 1920, height: 1080 },
          device: "Desktop (1920x1080)",
          aiModel: environmentSettings.aiModel || "default",
          proxy: environmentSettings.proxy,
        };

        setEnvironmentSettings((prev) => ({
          ...prev,
          ...webEnvironment,
        }));

        const webReportSettings: WebReportSettings = {
          takeScreenshots: true,
          takeVideos: false,
          pageMetrics: false,
          networkData: false,
          tracingData: false,
          accessibilityData: false,
        };

        setReportSettings(webReportSettings);

        // Form verisini güncelle
        updatedFormData.environment = {
          ...updatedFormData.environment,
          ...webEnvironment,
        };
        updatedFormData.reportSettings = webReportSettings;
      } else if (platform === "android") {
        // Android için varsayılan değerleri ayarla
        const androidEnvironment: Partial<AndroidEnvironmentSettings> = {
          platform: "android", // Explicitly set platform
          aiModel: environmentSettings.aiModel || "default",
          aiModelName: environmentSettings.aiModelName,
          // Removed appium object as it's no longer needed
          sauceLabs: {
            username: "",
            accessKey: "",
            selectedDevices: [],
          },
          testinium: {
            apiUrl: "",
            clientId: "",
            clientSecret: "",
            issuerUri: "",
            selectedDevices: [],
          },
          testDistribution: {
            strategy: "all-on-all", // Default strategy
          },
          proxy: environmentSettings.proxy,
        };

        setEnvironmentSettings((prev) => ({
          ...prev,
          ...androidEnvironment,
        }));

        // Create Android-specific report settings without web-specific fields
        const androidReportSettings: AndroidReportSettings = {
          takeScreenshots: true,
          takeVideos: false,
          collectMetrics: false,
          videoQuality: "medium",
          metricsInterval: 10000, // 10 seconds
        };

        // Set report settings with only Android-specific fields
        setReportSettings(androidReportSettings);

        // Form verisini güncelle
        updatedFormData.environment = {
          ...updatedFormData.environment,
          ...androidEnvironment,
        };

        // Set only Android-specific report settings in form data
        updatedFormData.reportSettings = androidReportSettings;
      }

      setFormData(updatedFormData);
    },
    [formData, environmentSettings],
  );

  // Form verilerini güncelle
  const updateFormData = useCallback(
    (data: Partial<Run>) => {
      setFormData((prev) => {
        const updated = { ...prev, ...data };

        // Form verisi değiştiğinde hataları temizle
        if (data.name && errors.name) {
          setErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors.name;
            return newErrors;
          });
        }

        return updated;
      });
    },
    [errors],
  );

  // Mevcut bir run'dan form verilerini tamamen ayarlama
  const setFormDataFromExisting = useCallback(
    (
      data: Partial<Run> & {
        scenarios?: Array<{
          id: string;
          selected: boolean;
          [key: string]: any;
        }>;
      },
    ) => {
      // Form verisini ayarla
      setFormData(data);

      // Seçili senaryoları ayarla
      if (data.scenarioIds) {
        setSelectedScenarios(data.scenarioIds);
      }

      // Seçili senaryo detaylarını ayarla
      if (data.scenarios) {
        const selectedDetails = data.scenarios.filter(
          (s: { selected: boolean }) => s.selected,
        );
        setSelectedScenariosDetails(selectedDetails);
      }

      // Platform bilgisini kontrol et
      const platform = data.platform || "web";

      // Çevre ayarlarını platforma göre ayarla
      if (data.environment) {
        // Create a platform-specific environment object
        let platformEnvironment = { ...data.environment };

        // Ensure the environment has the necessary structure based on platform
        if (platform === "web") {
          // Ensure web-specific properties exist
          platformEnvironment = {
            ...platformEnvironment,
            platform: "web", // Explicitly set platform
            browser: platformEnvironment.browser || "chrome",
            viewport: platformEnvironment.viewport || {
              width: 1920,
              height: 1080,
            },
            device: platformEnvironment.device || "Desktop (1920x1080)",
            aiModel: platformEnvironment.aiModel || "default",
          };
        } else if (platform === "android") {
          // Ensure Android-specific properties exist
          platformEnvironment = {
            ...platformEnvironment,
            platform: "android", // Explicitly set platform
            aiModel: platformEnvironment.aiModel || "default",
            // Remove appium object as it's no longer needed
            // Ensure sauceLabs object exists with selectedDevices
            sauceLabs: platformEnvironment.sauceLabs || {
              selectedDevices: [],
            },
            // Ensure testinium object exists with selectedDevices
            testinium: platformEnvironment.testinium || {
              selectedDevices: [],
            },
          };
        }

        setEnvironmentSettings(platformEnvironment);
      }

      // Rapor ayarlarını ayarla - Platform-specific filtering
      if (data.reportSettings) {
        let filteredReportSettings = { ...data.reportSettings };

        // Filter based on platform to prevent cross-platform field contamination
        if (platform === "web") {
          // For web platform, remove mobile-specific fields
          const { collectMetrics, metricsInterval, ...webOnlySettings } =
            filteredReportSettings;
          filteredReportSettings = webOnlySettings;
        } else if (platform === "android") {
          // For android platform, remove web-specific fields
          const {
            pageMetrics,
            networkData,
            tracingData,
            accessibilityData,
            ...androidOnlySettings
          } = filteredReportSettings;
          filteredReportSettings = androidOnlySettings;
        }

        setReportSettings(filteredReportSettings);
      }

      setErrors({});
    },
    [],
  );

  // Çevre ayarlarını güncelle
  const updateEnvironmentSettings = useCallback((data: any) => {
    setEnvironmentSettings((prev) => ({ ...prev, ...data }));

    // Aynı zamanda form verisi içindeki environment alanını da güncelle
    setFormData((prev) => ({
      ...prev,
      environment: {
        ...prev.environment,
        ...data,
      },
    }));
  }, []);

  // Rapor ayarlarını güncelle
  const updateReportSettings = useCallback((data: any) => {
    setReportSettings((prev) => ({ ...prev, ...data }));

    // Aynı zamanda form verisi içindeki reportSettings alanını da güncelle
    setFormData((prev) => ({
      ...prev,
      reportSettings: {
        ...prev.reportSettings,
        ...data,
      },
    }));
  }, []);

  // Seçili senaryoların detaylarını güncelle
  const updateSelectedScenariosDetails = useCallback((details: any[]) => {
    setSelectedScenariosDetails(details);
  }, []);

  // Test Data Environment'ları yükle
  const loadTestDataEnvironments = useCallback(async () => {
    setIsLoadingTestDataEnvironments(true);
    try {
      const response = await dataEnvironmentApi.getAll({ isActive: true });

      if (response && response.success) {
        // Check for environments in different possible locations
        const environments =
          response.dataEnvironments ||
          response.environments ||
          response.data?.dataEnvironments ||
          response.data?.environments ||
          [];
        setTestDataEnvironments(environments);
      } else {
        setTestDataEnvironments([]);
      }
    } catch (error) {
      setTestDataEnvironments([]);
    } finally {
      setIsLoadingTestDataEnvironments(false);
    }
  }, []); // Dependency array'i boş bıraktık

  // Test Data Environment seç
  const selectTestDataEnvironment = useCallback(
    (environment: TestDataEnvironment | null) => {
      setSelectedTestDataEnvironment(environment);

      // Form data'yı güncelle
      updateFormData({
        testDataEnvironmentId: environment?.id || undefined,
        testDataEnvironmentName: environment?.name || undefined,
      });
    },
    [updateFormData],
  );

  // Sonraki adıma geç
  const nextStep = useCallback(() => {
    // Burada validateCurrentStep fonksiyonunu çağırmayacağız
    // Çünkü zaten buton onClick eventi içinde çağırılıyor
    setCurrentStep((prev) => Math.min(prev + 1, 3));
  }, []);

  // Önceki adıma dön
  const prevStep = useCallback(() => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  }, []);

  // Belirli bir adıma geç
  const setStep = useCallback((step: number) => {
    if (step >= 0 && step <= 3) {
      setCurrentStep(step);
    }
  }, []);

  // Mevcut adımı doğrula
  const validateCurrentStep = useCallback(() => {
    let isValid = true;
    const newErrors: Record<string, string> = {};

    if (currentStep === 0) {
      // Temel bilgileri doğrula - name değerini doğrudan local değişkene ata
      const nameValue =
        formData.name !== undefined ? String(formData.name) : "";

      if (!nameValue || nameValue.trim().length === 0) {
        newErrors.name = "Run name is required";
        isValid = false;
      }
    } else if (currentStep === 1) {
      // Senaryo seçimini doğrula
      if (selectedScenarios.length === 0) {
        newErrors.scenarios = "At least one scenario must be selected";
        isValid = false;
      }
    } else if (currentStep === 2) {
      // Environment ayarlarını doğrula - AI Model seçimi zorunlu
      if (
        !environmentSettings.aiModel ||
        environmentSettings.aiModel === "default"
      ) {
        newErrors.aiModel = "AI Model selection is required";
        isValid = false;
      }
    } else if (currentStep === 3) {
      // Son adımda tüm validasyonları tekrar yap
      // Name kontrolü
      const nameValue =
        formData.name !== undefined ? String(formData.name) : "";
      if (!nameValue || nameValue.trim().length === 0) {
        newErrors.name = "Run name is required";
        isValid = false;
      }

      // Senaryo kontrolü
      if (selectedScenarios.length === 0) {
        newErrors.scenarios = "At least one scenario must be selected";
        isValid = false;
      }

      // AI Model kontrolü
      if (
        !environmentSettings.aiModel ||
        environmentSettings.aiModel === "default"
      ) {
        newErrors.aiModel = "AI Model selection is required";
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  }, [currentStep, formData, selectedScenarios]);

  // Run gönderme
  const submitRun = useCallback(async () => {
    if (!validateCurrentStep()) return;

    setIsSubmitting(true);

    try {
      // Run oluşturmak için gerekli tüm verileri hazırla
      const { scenarios, ...formDataWithoutScenarios } = formData as any;

      // Create a platform-specific environment object based on the current platform
      let platformEnvironment = { ...environmentSettings };
      const currentPlatform = (formData.platform as PlatformType) || "web";

      // If platform is web, remove Android-specific settings
      if (currentPlatform === "web") {
        // Extract only web-specific environment settings
        const {
          browser,
          viewport,
          device,
          userAgent,
          geolocation,
          aiModel,
          proxy,
        } = platformEnvironment;

        platformEnvironment = {
          platform: "web", // Explicitly set platform
          browser,
          viewport,
          device,
          ...(userAgent ? { userAgent } : {}),
          ...(geolocation ? { geolocation } : {}),
          aiModel,
          ...(proxy?.enabled ? { proxy } : {}),
        };
      }
      // If platform is android, remove web-specific settings
      else if (currentPlatform === "android") {
        // Extract only android-specific environment settings
        const {
          aiModel,
          aiModelName,
          deviceProvider,
          sauceLabs,
          testinium,
          testDistribution,
          proxy,
        } = platformEnvironment;

        // Ensure we're capturing all Android-specific settings
        platformEnvironment = {
          platform: "android", // Explicitly set platform
          aiModel,
          aiModelName,
          deviceProvider, // Include deviceProvider for validation
          // Removed appium object as it's no longer needed
          ...(sauceLabs
            ? {
                sauceLabs: {
                  ...sauceLabs,
                  // Remove redundant deviceId, deviceIds, and appId fields
                  selectedDevices: sauceLabs.selectedDevices || [],
                  selectedApp: sauceLabs.selectedApp,
                  // Explicitly remove redundant fields
                  deviceId: undefined,
                  deviceIds: undefined,
                  appId: undefined,
                },
              }
            : {}),
          ...(testinium
            ? {
                testinium: {
                  ...testinium,
                  selectedDevices: testinium.selectedDevices || [],
                },
              }
            : {}),
          ...(testDistribution ? { testDistribution } : {}),
          ...(proxy?.enabled ? { proxy } : {}),
        };
      }

      // Filter report settings based on platform
      let platformReportSettings = { ...reportSettings };

      if (currentPlatform === "web") {
        // For web platform, remove mobile-specific fields
        const {
          collectMetrics,
          metricsInterval,
          videoQuality,
          ...webOnlySettings
        } = platformReportSettings;
        platformReportSettings = webOnlySettings;
      } else if (currentPlatform === "android") {
        // For android platform, remove web-specific fields
        const {
          pageMetrics,
          networkData,
          tracingData,
          accessibilityData,
          ...androidOnlySettings
        } = platformReportSettings;
        platformReportSettings = androidOnlySettings;
      }

      // Extract deviceProvider from Android environment settings
      const deviceProvider =
        formData.platform === "android" &&
        platformEnvironment &&
        "deviceProvider" in platformEnvironment
          ? platformEnvironment.deviceProvider
          : undefined;

      const runData: Partial<Run> = {
        ...formDataWithoutScenarios,
        scenarioIds: selectedScenarios,
        platform: formData.platform || "web", // Include platform field
        deviceProvider: deviceProvider, // Include device provider for mutual exclusivity
        environment: platformEnvironment,
        reportSettings: platformReportSettings, // Use platform-specific report settings
        tags: formData.tags || [], // Tags'ı açıkça belirt
        companyId: user?.companyId || null,
        teamId: user?.teamId || null,
        testDataEnvironmentId: selectedTestDataEnvironment?.id,
        testDataEnvironmentName: selectedTestDataEnvironment?.name,
      };

      // Validate device provider selection for Android runs
      if (runData.platform === "android") {
        const validation = DeviceProviderUtils.validateAndroidRun(runData);
        if (!validation.isValid) {
          toast.error("Koşum Kaydedilemedi", {
            description: validation.error || "Geçersiz cihaz sağlayıcı yapılandırması",
          });
          setIsSubmitting(false);
          return { success: false, error: validation.error };
        }
      }

      let result;

      // Eğer düzenleme modundaysak, güncelleme yap
      if (isEditMode && formData.id) {
        result = await updateRun(formData.id, runData);

        if (result.success) {
          toast.success("Run updated", {
            description: "Test run has been updated successfully",
          });

          // onRunCreated callback'i varsa çağır, 1 saniye gecikme ile
          // isSubmitting durumunu yönlendirme tamamlanana kadar true olarak tut
          setTimeout(() => {
            if (onRunCreated && formData.id) {
              onRunCreated(formData.id);
            } else {
              // Yoksa run sayfasına yönlendir
              router.push("/runs");
            }
            // Yönlendirme tamamlandıktan sonra isSubmitting'i false yap
            setIsSubmitting(false);
          }, 1000); // 1 saniye gecikme
        } else {
          toast.error("Error", {
            description: result.error || "Failed to update test run",
          });
        }
      } else {
        // Yeni run oluştur
        result = await createRun(runData);

        if (result.success && result.runId) {
          toast.success("Run created", {
            description: "Test run has been created successfully",
          });

          // onRunCreated callback'i varsa çağır, 1 saniye gecikme ile
          // isSubmitting durumunu yönlendirme tamamlanana kadar true olarak tut
          setTimeout(() => {
            if (onRunCreated && result.runId) {
              onRunCreated(result.runId);
            } else {
              // Yoksa runs sayfasına yönlendir
              router.push("/runs");
            }
            // Yönlendirme tamamlandıktan sonra isSubmitting'i false yap
            setIsSubmitting(false);
          }, 1000); // 1 saniye gecikme
        } else {
          toast.error("Error", {
            description: result.error || "Failed to create test run",
          });
        }
      }

      return result;
    } catch (error) {
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
      // Hata durumunda isSubmitting'i false yap
      setIsSubmitting(false);
    }
    // finally bloğunu kaldırdık çünkü başarılı durumda isSubmitting'i
    // yönlendirme tamamlandıktan sonra false yapıyoruz
  }, [
    formData,
    selectedScenarios,
    environmentSettings,
    reportSettings,
    createRun,
    updateRun,
    router,

    validateCurrentStep,
    onRunCreated,
    isEditMode,
    formData.id,
    user,
  ]);

  const value = {
    currentStep,
    formData,
    nextStep,
    prevStep,
    setStep,
    updateFormData,
    setFormDataFromExisting,
    handlePlatformChange,
    selectedScenarios,
    setSelectedScenarios,
    selectedScenariosDetails,
    updateSelectedScenariosDetails,
    environmentSettings,
    updateEnvironmentSettings,
    reportSettings,
    updateReportSettings,
    testDataEnvironments,
    selectedTestDataEnvironment,
    isLoadingTestDataEnvironments,
    loadTestDataEnvironments,
    selectTestDataEnvironment,
    isSubmitting,
    submitRun,
    errors,
    validateCurrentStep,
  };

  return (
    <RunFormContext.Provider value={value}>{children}</RunFormContext.Provider>
  );
};
