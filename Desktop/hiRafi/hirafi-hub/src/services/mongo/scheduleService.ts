/**
 * Schedule Service
 * MongoDB service functions for schedule operations
 */

import { Collection } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';
import { db, ensureMongoDBConnection, schedulesCollection, scheduleRunsCollection } from './dbConnection.js';
import logger from '../../utils/logger.js';
import { operationQueue } from '../../utils/operationQueue.js';
import {
  Schedule,
  ScheduleRun,
  ScheduleStatus,
  CreateScheduleRequest,
  UpdateScheduleRequest,
  ScheduleServiceResponse,
  ScheduleWithDetails
} from '../../models/schedule-types.js';

/**
 * Create a new schedule
 */
export async function createSchedule(scheduleData: CreateScheduleRequest): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `create-schedule-${Date.now()}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        // Generate ID
        const scheduleId = uuidv4();
        const now = new Date();

        // Check for conflicting parameters and fix them
        if (scheduleData.scheduleType === 'weekly' && scheduleData.hourlyInterval) {
          logger.warn(`Conflicting parameters in schedule creation: scheduleType is 'weekly' but hourlyInterval is ${scheduleData.hourlyInterval}. Removing hourlyInterval for weekly schedule.`);
          // Remove hourlyInterval for weekly schedules
          scheduleData.hourlyInterval = undefined;
        }

        // Calculate next run time
        const nextRunAt = calculateNextRunTime(
          scheduleData.scheduleType,
          scheduleData.startDate,
          scheduleData.startTime,
          scheduleData.hourlyInterval,
          scheduleData.repeatDays,
          scheduleData.timezone
        );

        logger.info(`Calculated next run time for new schedule: ${nextRunAt?.toISOString() || 'null'}`);

        // Create schedule object
        const schedule: Schedule = {
          id: scheduleId,
          name: scheduleData.name,
          description: scheduleData.description,
          scheduleType: scheduleData.scheduleType as any,
          hourlyInterval: scheduleData.hourlyInterval as any,
          startDate: scheduleData.startDate,
          startTime: scheduleData.startTime,
          endDate: scheduleData.endDate,
          endTime: scheduleData.endTime,
          timezone: scheduleData.timezone, // Saat dilimi alanı eklendi
          repeatDays: scheduleData.repeatDays,
          runIds: scheduleData.runIds || [],
          userId: scheduleData.userId,
          teamId: scheduleData.teamId,
          companyId: scheduleData.companyId,
          status: scheduleData.status || ScheduleStatus.ACTIVE,
          createdAt: now,
          updatedAt: now,
          nextRunAt: nextRunAt || undefined,
          notifications: scheduleData.notifications,
          emailRecipients: scheduleData.emailRecipients,
          slackChannel: scheduleData.slackChannel
        };

        // Insert into MongoDB
        await schedulesCollection.insertOne(schedule);

        logger.info(`Created new schedule with ID: ${scheduleId}`);

        return {
          success: true,
          scheduleId: scheduleId
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`Error creating schedule: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to create schedule: ${error.message}`
    };
  }
}

/**
 * Get schedule by ID
 */
export async function getScheduleById(scheduleId: string): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `get-schedule-${scheduleId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        const schedule = await schedulesCollection.findOne({ id: scheduleId });

        if (!schedule) {
          return {
            success: false,
            message: `Schedule with ID ${scheduleId} not found`
          };
        }

        return {
          success: true,
          schedule: schedule as unknown as Schedule
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting schedule: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get schedule: ${error.message}`
    };
  }
}

/**
 * Get schedules by user ID
 */
export async function getSchedulesByUser(userId: string): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `get-schedules-user-${userId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        const schedules = await schedulesCollection.find({
          userId: userId,
          status: { $ne: ScheduleStatus.DELETED }
        }).sort({ createdAt: -1 }).toArray();

        return {
          success: true,
          schedules: schedules as unknown as Schedule[]
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting schedules by user: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get schedules: ${error.message}`
    };
  }
}

/**
 * Get schedules by team ID
 */
export async function getSchedulesByTeam(teamId: string): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `get-schedules-team-${teamId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        const schedules = await schedulesCollection.find({
          teamId: teamId,
          status: { $ne: ScheduleStatus.DELETED }
        }).sort({ createdAt: -1 }).toArray();

        return {
          success: true,
          schedules: schedules as unknown as Schedule[]
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting schedules by team: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get schedules: ${error.message}`
    };
  }
}

/**
 * Get schedules by company ID
 */
export async function getSchedulesByCompany(companyId: string): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `get-schedules-company-${companyId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        const schedules = await schedulesCollection.find({
          companyId: companyId,
          status: { $ne: ScheduleStatus.DELETED }
        }).sort({ createdAt: -1 }).toArray();

        return {
          success: true,
          schedules: schedules as unknown as Schedule[]
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting schedules by company: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get schedules: ${error.message}`
    };
  }
}

/**
 * Update schedule
 */
export async function updateSchedule(scheduleId: string, updateData: UpdateScheduleRequest): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `update-schedule-${scheduleId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        // Get current schedule
        const currentSchedule = await schedulesCollection.findOne({ id: scheduleId });

        if (!currentSchedule) {
          return {
            success: false,
            message: `Schedule with ID ${scheduleId} not found`
          };
        }

        // Check for conflicting parameters and fix them
        if (updateData.scheduleType === 'weekly' && updateData.hourlyInterval) {
          logger.warn(`Conflicting parameters in schedule update: scheduleType is 'weekly' but hourlyInterval is ${updateData.hourlyInterval}. Removing hourlyInterval for weekly schedule.`);
          // Remove hourlyInterval for weekly schedules
          updateData.hourlyInterval = undefined;
        } else if (currentSchedule.scheduleType === 'weekly' && updateData.hourlyInterval && !updateData.scheduleType) {
          logger.warn(`Conflicting parameters in schedule update: existing scheduleType is 'weekly' but hourlyInterval is being set to ${updateData.hourlyInterval}. Removing hourlyInterval.`);
          // Remove hourlyInterval for weekly schedules
          updateData.hourlyInterval = undefined;
        }

        // Calculate next run time if schedule parameters changed
        let nextRunAt = currentSchedule.nextRunAt;
        if (
          updateData.scheduleType ||
          updateData.startDate ||
          updateData.startTime ||
          updateData.hourlyInterval ||
          updateData.repeatDays ||
          updateData.timezone
        ) {
          // Get the effective schedule type
          const scheduleType = updateData.scheduleType || currentSchedule.scheduleType;

          // For weekly schedules, ensure hourlyInterval is not used
          const hourlyInterval = scheduleType === 'weekly' ?
            undefined :
            (updateData.hourlyInterval !== undefined ? updateData.hourlyInterval : currentSchedule.hourlyInterval);

          nextRunAt = calculateNextRunTime(
            scheduleType,
            updateData.startDate || currentSchedule.startDate,
            updateData.startTime || currentSchedule.startTime,
            hourlyInterval,
            updateData.repeatDays || currentSchedule.repeatDays,
            updateData.timezone || currentSchedule.timezone
          );

          logger.info(`Calculated next run time for schedule ${scheduleId}: ${nextRunAt?.toISOString() || 'null'}`);
        }

        // Update fields
        const updateFields: any = {
          updatedAt: new Date()
        };

        if (updateData.name !== undefined) updateFields.name = updateData.name;
        if (updateData.description !== undefined) updateFields.description = updateData.description;
        if (updateData.scheduleType) updateFields.scheduleType = updateData.scheduleType;
        if (updateData.hourlyInterval) updateFields.hourlyInterval = updateData.hourlyInterval;
        if (updateData.startDate) updateFields.startDate = updateData.startDate;
        if (updateData.startTime) updateFields.startTime = updateData.startTime;
        if (updateData.endDate !== undefined) updateFields.endDate = updateData.endDate;
        if (updateData.endTime !== undefined) updateFields.endTime = updateData.endTime;
        if (updateData.timezone !== undefined) updateFields.timezone = updateData.timezone;
        if (updateData.repeatDays) updateFields.repeatDays = updateData.repeatDays;
        if (updateData.runIds) updateFields.runIds = updateData.runIds;
        if (updateData.status) updateFields.status = updateData.status;
        if (updateData.notifications) updateFields.notifications = updateData.notifications;
        if (updateData.emailRecipients !== undefined) updateFields.emailRecipients = updateData.emailRecipients;
        if (updateData.slackChannel !== undefined) updateFields.slackChannel = updateData.slackChannel;

        // Set next run time
        updateFields.nextRunAt = nextRunAt || undefined;

        // Update in MongoDB
        await schedulesCollection.updateOne(
          { id: scheduleId },
          { $set: updateFields }
        );

        logger.info(`Updated schedule with ID: ${scheduleId}`);

        return {
          success: true
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`Error updating schedule: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to update schedule: ${error.message}`
    };
  }
}

/**
 * Delete schedule
 */
export async function deleteSchedule(scheduleId: string): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `delete-schedule-${scheduleId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        // Soft delete - update status to DELETED
        await schedulesCollection.updateOne(
          { id: scheduleId },
          {
            $set: {
              status: ScheduleStatus.DELETED,
              updatedAt: new Date()
            }
          }
        );

        logger.info(`Deleted schedule with ID: ${scheduleId}`);

        return {
          success: true
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`Error deleting schedule: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to delete schedule: ${error.message}`
    };
  }
}

/**
 * Update schedule status
 */
export async function updateScheduleStatus(scheduleId: string, status: ScheduleStatus): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `update-schedule-status-${scheduleId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        await schedulesCollection.updateOne(
          { id: scheduleId },
          {
            $set: {
              status: status,
              updatedAt: new Date()
            }
          }
        );

        logger.info(`Updated status of schedule ${scheduleId} to ${status}`);

        return {
          success: true
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`Error updating schedule status: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to update schedule status: ${error.message}`
    };
  }
}

/**
 * Get schedules with run and scenario details
 */
export async function getSchedulesWithDetails(
  filter: { userId?: string; teamId?: string; companyId?: string; }
): Promise<ScheduleServiceResponse> {
  try {
    return await operationQueue.enqueue(
      `get-schedules-details-${JSON.stringify(filter)}`,
      async () => {
        await ensureMongoDBConnection();

        if (!db || !schedulesCollection) {
          return {
            success: false,
            message: 'MongoDB connection not available'
          };
        }

        // Build match criteria
        const matchCriteria: any = {
          status: { $ne: ScheduleStatus.DELETED }
        };

        if (filter.userId) matchCriteria.userId = filter.userId;
        if (filter.teamId) matchCriteria.teamId = filter.teamId;
        if (filter.companyId) matchCriteria.companyId = filter.companyId;

        // Aggregate to get run and scenario details
        const schedules = await schedulesCollection.aggregate([
          { $match: matchCriteria },
          {
            $lookup: {
              from: 'runs',
              localField: 'runIds',
              foreignField: 'id',
              as: 'runDetails'
            }
          },
          {
            $lookup: {
              from: 'scenarios',
              localField: 'runDetails.scenarioIds',
              foreignField: 'id',
              as: 'scenarios'
            }
          },
          {
            $project: {
              _id: 0,
              id: 1,
              name: 1,
              description: 1,
              scheduleType: 1,
              hourlyInterval: 1,
              startDate: 1,
              startTime: 1,
              endDate: 1,
              endTime: 1,
              repeatDays: 1,
              runIds: 1,
              userId: 1,
              teamId: 1,
              companyId: 1,
              status: 1,
              createdAt: 1,
              updatedAt: 1,
              lastRunAt: 1,
              nextRunAt: 1,
              notifications: 1,
              emailRecipients: 1,
              slackChannel: 1,
              runs: {
                $map: {
                  input: '$runDetails',
                  as: 'run',
                  in: {
                    id: '$$run.id',
                    name: '$$run.name',
                    scenarioIds: '$$run.scenarioIds',
                    scenarioCount: { $size: '$$run.scenarioIds' }
                  }
                }
              },
              scenarios: {
                $map: {
                  input: '$scenarios',
                  as: 'scenario',
                  in: {
                    id: '$$scenario.id',
                    name: '$$scenario.name'
                  }
                }
              }
            }
          },
          { $sort: { nextRunAt: 1 } }
        ]).toArray();

        return {
          success: true,
          schedules: schedules as unknown as ScheduleWithDetails[]
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting schedules with details: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get schedules with details: ${error.message}`
    };
  }
}

/**
 * Create a schedule run record
 */
export async function createScheduleRun(scheduleId: string, runId: string, executionId?: string): Promise<{ success: boolean; scheduleRunId?: string; message?: string; }> {
  try {
    return await operationQueue.enqueue(
      `create-schedule-run-${scheduleId}-${runId}`,
      async () => {
        if (!scheduleRunsCollection || !schedulesCollection) {
          throw new Error('Schedule collections are not initialized.');
        }

        // Generate ID
        const scheduleRunId = uuidv4();
        const now = new Date();

        // Create schedule run object - simplified to only track schedule-run relationship
        const scheduleRun: ScheduleRun = {
          id: scheduleRunId,
          scheduleId: scheduleId,
          runId: runId,
          createdAt: now,
          ...(executionId && { executionId })
        };

        // Insert into MongoDB
        await scheduleRunsCollection.insertOne(scheduleRun);

        // Update schedule's lastRunAt
        await schedulesCollection.updateOne(
          { id: scheduleId },
          {
            $set: {
              lastRunAt: now,
              updatedAt: now
            }
          }
        );

        logger.info(`Created new schedule run with ID: ${scheduleRunId} for schedule ${scheduleId} and run ${runId}`);

        return {
          success: true,
          scheduleRunId: scheduleRunId
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`Error creating schedule run: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to create schedule run: ${error.message}`
    };
  }
}

/**
 * Get pending schedules that need to be executed
 */
export async function getPendingSchedules(): Promise<{ success: boolean; schedules?: Schedule[]; message?: string; }> {
  try {
    return await operationQueue.enqueue(
      `get-pending-schedules`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        const now = new Date();
        logger.debug(`Getting pending schedules at ${now.toISOString()}`);

        // Find schedules that are active and need to be executed
        const schedules = await schedulesCollection.find({
          status: ScheduleStatus.ACTIVE,
          nextRunAt: { $lte: now }
        }).toArray();

        // Validate nextRunAt dates
        const validSchedules = schedules.filter(schedule => {
          // Check if nextRunAt is valid
          if (!schedule.nextRunAt || schedule.nextRunAt.getTime() === 0) {
            logger.warn(`Schedule ${schedule.id} has invalid nextRunAt: ${schedule.nextRunAt}. Updating it.`);

            // Update the schedule with a valid nextRunAt
            updateNextRunTime(schedule.id).catch(err => {
              logger.error(`Failed to update nextRunAt for schedule ${schedule.id}: ${err.message}`);
            });

            // Skip this schedule for now
            return false;
          }

          return true;
        });

        logger.debug(`Found ${validSchedules.length} pending schedules out of ${schedules.length} total`);

        // Log details of each pending schedule
        validSchedules.forEach(schedule => {
          logger.info(`Pending schedule: ${schedule.id} (${schedule.name}), Type: ${schedule.scheduleType}, NextRunAt: ${schedule.nextRunAt?.toISOString()}`);
        });

        return {
          success: true,
          schedules: validSchedules as unknown as Schedule[]
        };
      },
      { priority: 'HIGH', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting pending schedules: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get pending schedules: ${error.message}`
    };
  }
}

/**
 * Update next run time for a schedule
 */
export async function updateNextRunTime(scheduleId: string): Promise<{ success: boolean; message?: string; nextRunAt?: Date }> {
  try {
    return await operationQueue.enqueue(
      `update-next-run-time-${scheduleId}`,
      async () => {
        if (!schedulesCollection) {
          throw new Error('Schedules collection is not initialized.');
        }

        // Get current schedule
        const schedule = await schedulesCollection.findOne({ id: scheduleId });

        if (!schedule) {
          return {
            success: false,
            message: `Schedule with ID ${scheduleId} not found`
          };
        }

        logger.info(`Updating next run time for schedule ${scheduleId} (${schedule.name}), Type: ${schedule.scheduleType}, Current nextRunAt: ${schedule.nextRunAt?.toISOString() || 'undefined'}`);

        // Log schedule details for debugging
        logger.debug(`Schedule details:
          Type: ${schedule.scheduleType}
          Start Date: ${schedule.startDate}
          Start Time: ${schedule.startTime}
          Hourly Interval: ${schedule.hourlyInterval || 'N/A'}
          Repeat Days: ${JSON.stringify(schedule.repeatDays || [])}
          Timezone: ${schedule.timezone || 'default'}
          Status: ${schedule.status}
        `);

        // Calculate next run time
        const nextRunAt = calculateNextRunTime(
          schedule.scheduleType,
          schedule.startDate,
          schedule.startTime,
          schedule.hourlyInterval,
          schedule.repeatDays,
          schedule.timezone
        );

        // If no next run time (e.g., one-time schedule or past end date), mark as completed
        if (!nextRunAt) {
          logger.info(`No next run time calculated for schedule ${scheduleId}, marking as completed`);

          await schedulesCollection.updateOne(
            { id: scheduleId },
            {
              $set: {
                status: ScheduleStatus.COMPLETED,
                updatedAt: new Date()
              }
            }
          );

          logger.info(`Schedule ${scheduleId} marked as completed - no more runs scheduled`);

          return { success: true };
        }

        // Validate the calculated next run time
        const now = new Date();
        if (nextRunAt.getTime() <= now.getTime()) {
          logger.warn(`Calculated next run time (${nextRunAt.toISOString()}) is in the past or equal to now (${now.toISOString()})`);

          // For weekly schedules, try to recalculate with an adjusted current time
          if (schedule.scheduleType === 'weekly' && schedule.repeatDays && schedule.repeatDays.length > 0) {
            logger.info(`Attempting to recalculate next run time for weekly schedule by adding 1 minute to current time`);

            // Add 1 minute to current time to force next week calculation
            const adjustedNow = new Date(now.getTime() + 60000);

            // Recalculate with adjusted time
            const adjustedNextRunAt = calculateNextRunTime(
              schedule.scheduleType,
              schedule.startDate,
              schedule.startTime,
              schedule.hourlyInterval,
              schedule.repeatDays,
              schedule.timezone,
              adjustedNow
            );

            if (adjustedNextRunAt && adjustedNextRunAt.getTime() > now.getTime()) {


              // Update next run time with the adjusted calculation
              await schedulesCollection.updateOne(
                { id: scheduleId },
                {
                  $set: {
                    nextRunAt: adjustedNextRunAt,
                    updatedAt: new Date()
                  }
                }
              );

              return {
                success: true,
                nextRunAt: adjustedNextRunAt
              };
            }
          }
        }

        // Update next run time
        await schedulesCollection.updateOne(
          { id: scheduleId },
          {
            $set: {
              nextRunAt: nextRunAt,
              updatedAt: new Date()
            }
          }
        );

        logger.info(`Updated next run time for schedule ${scheduleId} to ${nextRunAt.toISOString()}`);

        return {
          success: true,
          nextRunAt: nextRunAt
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`Error updating next run time: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to update next run time: ${error.message}`
    };
  }
}

/**
 * Calculate next run time based on schedule parameters
 */
function calculateNextRunTime(
  scheduleType: string,
  startDate: string,
  startTime: string,
  hourlyInterval?: number,
  repeatDays?: string[],
  timezone?: string,
  currentTime?: Date
): Date | null {
  // Use provided current time or get current time
  const now = currentTime || new Date();

  logger.debug(`Calculating next run time for schedule type: ${scheduleType}, Current time: ${now.toISOString()}`);

  // Validate inputs
  if (!startDate || !startTime) {
    logger.warn(`Invalid schedule parameters: startDate=${startDate}, startTime=${startTime}`);
    return null;
  }

  // Log schedule parameters for debugging
  logger.debug(`Schedule parameters: type=${scheduleType}, hourlyInterval=${hourlyInterval}, repeatDays=${JSON.stringify(repeatDays || [])}, timezone=${timezone || 'default'}`);

  // Check for conflicting parameters
  if (scheduleType === 'weekly' && hourlyInterval) {
    logger.warn(`Conflicting parameters: scheduleType is 'weekly' but hourlyInterval is ${hourlyInterval}. Ignoring hourlyInterval for weekly schedule.`);
    // Set hourlyInterval to undefined for weekly schedules to avoid confusion
    hourlyInterval = undefined;
  }

  // Parse date and time components
  const [year, month, day] = startDate.split('-').map(Number);
  const [hours, minutes] = startTime.split(':').map(Number);

  // Helper function to create date in specified timezone and convert to UTC
  const createDateInTimezone = (year: number, month: number, day: number, hours: number, minutes: number, tz?: string): Date => {
    if (!tz || tz === 'UTC') {
      // If no timezone specified or UTC, create UTC date directly
      return new Date(Date.UTC(year, month - 1, day, hours, minutes));
    }

    try {
      // Create a date string in ISO format (assumes local timezone for parsing)
      const localDateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
      
      // Parse as local date first
      const localDate = new Date(localDateString);
      
      // Get the offset for the target timezone at this date
      const tempDate = new Date(localDate.getTime());
      
      // Use Intl API to get the time in the target timezone
      // This gives us what the time would be in the target timezone
      const timeInTargetTz = new Intl.DateTimeFormat('en-CA', {
        timeZone: tz,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).formatToParts(tempDate);

      // Extract parts from the formatted result
      const tzYear = parseInt(timeInTargetTz.find(part => part.type === 'year')?.value || '0');
      const tzMonth = parseInt(timeInTargetTz.find(part => part.type === 'month')?.value || '0');
      const tzDay = parseInt(timeInTargetTz.find(part => part.type === 'day')?.value || '0');
      const tzHour = parseInt(timeInTargetTz.find(part => part.type === 'hour')?.value || '0');
      const tzMinute = parseInt(timeInTargetTz.find(part => part.type === 'minute')?.value || '0');

      // Calculate the difference between what we want and what we got
      const wantedMs = Date.UTC(year, month - 1, day, hours, minutes);
      const actualMs = Date.UTC(tzYear, tzMonth - 1, tzDay, tzHour, tzMinute);
      const diffMs = wantedMs - actualMs;

      // Apply the difference to get the correct UTC time
      const utcDate = new Date(tempDate.getTime() + diffMs);
      
      logger.debug(`Timezone conversion for ${tz}: ${localDateString} -> ${utcDate.toISOString()}`);
      return utcDate;
      
    } catch (error) {
      logger.error(`Error in timezone conversion for ${tz}: ${error}. Falling back to UTC.`);
      // Fallback to UTC if timezone conversion fails
      return new Date(Date.UTC(year, month - 1, day, hours, minutes));
    }
  };

  // Create start date in the specified timezone and convert to UTC
  const startDateTime = createDateInTimezone(year, month, day, hours, minutes, timezone);

  // For one-time schedules, return the start time if it's in the future
  if (scheduleType === 'once') {
    return startDateTime > now ? startDateTime : null;
  }

  // Check if start date is in the future - if so, use it as the base time for calculations
  const baseTime = startDateTime > now ? startDateTime : now;
  logger.debug(`Using base time for calculations: ${baseTime.toISOString()} (start date: ${startDateTime.toISOString()}, current time: ${now.toISOString()})`);

  // For hourly schedules
  if (scheduleType === 'hourly' && hourlyInterval) {
    // If start date is in the future, return the start date as the first run
    if (startDateTime > now) {
      logger.debug(`Start date is in future for hourly schedule, returning start date: ${startDateTime.toISOString()}`);
      return startDateTime;
    }

    const nextRun = new Date(baseTime);

    // Round to the next interval
    const currentHour = nextRun.getHours();
    const currentMinute = nextRun.getMinutes();

    // Calculate next run based on hourly interval
    const hoursToAdd = hourlyInterval - (currentHour % hourlyInterval);

    if (hoursToAdd === hourlyInterval && currentMinute < minutes) {
      // If we're in the same hour interval but before the specified minutes
      nextRun.setHours(currentHour);
      nextRun.setMinutes(minutes);
      nextRun.setSeconds(0);
      nextRun.setMilliseconds(0);
    } else {
      // Move to the next interval
      nextRun.setHours(currentHour + hoursToAdd);
      nextRun.setMinutes(minutes);
      nextRun.setSeconds(0);
      nextRun.setMilliseconds(0);
    }

    return nextRun;
  }

  // For daily schedules
  if (scheduleType === 'daily') {
    // If start date is in the future, return the start date as the first run
    if (startDateTime > now) {
      logger.debug(`Start date is in future for daily schedule, returning start date: ${startDateTime.toISOString()}`);
      return startDateTime;
    }

    const nextRun = new Date(baseTime);
    
    // Get current date components in the target timezone
    let targetDate: Date;
    if (timezone && timezone !== 'UTC') {
      // Get current time in the target timezone
      const baseTimeInTz = new Intl.DateTimeFormat('en-CA', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).formatToParts(baseTime);

      const tzYear = parseInt(baseTimeInTz.find(part => part.type === 'year')?.value || '0');
      const tzMonth = parseInt(baseTimeInTz.find(part => part.type === 'month')?.value || '0');
      const tzDay = parseInt(baseTimeInTz.find(part => part.type === 'day')?.value || '0');
      
      targetDate = createDateInTimezone(tzYear, tzMonth, tzDay, hours, minutes, timezone);
    } else {
      targetDate = new Date(baseTime);
      targetDate.setUTCHours(hours, minutes, 0, 0);
    }

    // If today's time has passed, move to tomorrow
    if (targetDate <= baseTime) {
      if (timezone && timezone !== 'UTC') {
        // Add one day in the target timezone
        const nextDay = new Date(targetDate.getTime() + 24 * 60 * 60 * 1000);
        const nextDayParts = new Intl.DateTimeFormat('en-CA', {
          timeZone: timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }).formatToParts(nextDay);

        const nextYear = parseInt(nextDayParts.find(part => part.type === 'year')?.value || '0');
        const nextMonth = parseInt(nextDayParts.find(part => part.type === 'month')?.value || '0');
        const nextDayVal = parseInt(nextDayParts.find(part => part.type === 'day')?.value || '0');
        
        targetDate = createDateInTimezone(nextYear, nextMonth, nextDayVal, hours, minutes, timezone);
      } else {
        targetDate.setUTCDate(targetDate.getUTCDate() + 1);
      }
    }

    return targetDate;
  }

  // For weekly schedules
  if (scheduleType === 'weekly' && repeatDays && repeatDays.length > 0) {
    // If hourlyInterval is set for a weekly schedule, log a warning and ignore it
    if (hourlyInterval) {
      logger.warn(`Ignoring hourlyInterval=${hourlyInterval} for weekly schedule. This is a conflicting parameter.`);
    }

    // If start date is in the future, find the first occurrence on or after the start date
    if (startDateTime > now) {
      logger.debug(`Start date is in future for weekly schedule, calculating from start date: ${startDateTime.toISOString()}`);
      
      const dayMap: { [key: string]: number } = {
        'sunday': 0,
        'monday': 1,
        'tuesday': 2,
        'wednesday': 3,
        'thursday': 4,
        'friday': 5,
        'saturday': 6
      };

      // Get start date day in the target timezone
      let startDay: number;
      if (timezone && timezone !== 'UTC') {
        const startInTz = new Intl.DateTimeFormat('en-US', {
          timeZone: timezone,
          weekday: 'long'
        }).format(startDateTime).toLowerCase();
        startDay = dayMap[startInTz] || startDateTime.getDay();
      } else {
        startDay = startDateTime.getUTCDay();
      }

      // Convert day names to lowercase for case-insensitive comparison
      const selectedDays = repeatDays.map(day => dayMap[day.toLowerCase()]).filter(day => day !== undefined).sort((a, b) => a - b);

      logger.debug(`Weekly schedule from future start date - Start day: ${startDay}, Selected days: ${JSON.stringify(selectedDays)}, Original repeat days: ${JSON.stringify(repeatDays)}`);

      // If no valid days found, return null
      if (selectedDays.length === 0) {
        logger.warn(`No valid days found in repeatDays: ${JSON.stringify(repeatDays)}`);
        return null;
      }

      // If the start day is one of the selected days, return the start date
      if (selectedDays.includes(startDay)) {
        logger.debug(`Start day matches selected days, returning start date: ${startDateTime.toISOString()}`);
        return startDateTime;
      }

      // Find the next day to run after the start day
      let nextDay = selectedDays.find(day => day > startDay);
      let daysToAdd: number;

      if (nextDay === undefined) {
        // No day found this week, take the first day in the next week
        nextDay = selectedDays[0];
        daysToAdd = 7 - startDay + nextDay;
      } else {
        // Found a day this week
        daysToAdd = nextDay - startDay;
      }

      // Calculate the target date from start date
      const targetTime = new Date(startDateTime.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
      
      let nextRun: Date;
      if (timezone && timezone !== 'UTC') {
        // Get the target date in the target timezone
        const targetParts = new Intl.DateTimeFormat('en-CA', {
          timeZone: timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }).formatToParts(targetTime);

        const targetYear = parseInt(targetParts.find(part => part.type === 'year')?.value || '0');
        const targetMonth = parseInt(targetParts.find(part => part.type === 'month')?.value || '0');
        const targetDay = parseInt(targetParts.find(part => part.type === 'day')?.value || '0');
        
        nextRun = createDateInTimezone(targetYear, targetMonth, targetDay, hours, minutes, timezone);
      } else {
        nextRun = new Date(targetTime);
        nextRun.setUTCHours(hours, minutes, 0, 0);
      }

      logger.debug(`Weekly schedule from future start date - Days to add: ${daysToAdd}, Next run: ${nextRun.toISOString()}`);
      return nextRun;
    }

    const dayMap: { [key: string]: number } = {
      'sunday': 0,
      'monday': 1,
      'tuesday': 2,
      'wednesday': 3,
      'thursday': 4,
      'friday': 5,
      'saturday': 6
    };

    // Get current day in the target timezone (using baseTime for consistency)
    let currentDay: number;
    if (timezone && timezone !== 'UTC') {
      const baseTimeInTz = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        weekday: 'long'
      }).format(baseTime).toLowerCase();
      currentDay = dayMap[baseTimeInTz] || baseTime.getDay();
    } else {
      currentDay = baseTime.getUTCDay();
    }

    // Convert day names to lowercase for case-insensitive comparison
    const selectedDays = repeatDays.map(day => dayMap[day.toLowerCase()]).filter(day => day !== undefined).sort((a, b) => a - b);

    // Log the selected days for debugging
    logger.debug(`Weekly schedule calculation - Current day: ${currentDay}, Selected days: ${JSON.stringify(selectedDays)}, Original repeat days: ${JSON.stringify(repeatDays)}`);

    // If no valid days found, return null
    if (selectedDays.length === 0) {
      logger.warn(`No valid days found in repeatDays: ${JSON.stringify(repeatDays)}`);
      return null;
    }

    // Find the next day to run
    let nextDay = selectedDays.find(day => day > currentDay);
    let daysToAdd: number;

    if (nextDay === undefined) {
      // No day found this week, take the first day in the next week
      nextDay = selectedDays[0];
      daysToAdd = 7 - currentDay + nextDay;
    } else {
      // Found a day this week
      daysToAdd = nextDay - currentDay;
    }

    // Calculate the target date
    const targetTime = new Date(baseTime.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    
    let nextRun: Date;
    if (timezone && timezone !== 'UTC') {
      // Get the target date in the target timezone
      const targetParts = new Intl.DateTimeFormat('en-CA', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).formatToParts(targetTime);

      const targetYear = parseInt(targetParts.find(part => part.type === 'year')?.value || '0');
      const targetMonth = parseInt(targetParts.find(part => part.type === 'month')?.value || '0');
      const targetDay = parseInt(targetParts.find(part => part.type === 'day')?.value || '0');
      
      nextRun = createDateInTimezone(targetYear, targetMonth, targetDay, hours, minutes, timezone);
    } else {
      nextRun = new Date(targetTime);
      nextRun.setUTCHours(hours, minutes, 0, 0);
    }

    // If we calculated today but the time has passed, move to next week
    if (daysToAdd === 0 && nextRun <= baseTime) {
      const nextWeekTime = new Date(baseTime.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      if (timezone && timezone !== 'UTC') {
        const nextWeekParts = new Intl.DateTimeFormat('en-CA', {
          timeZone: timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }).formatToParts(nextWeekTime);

        const nextWeekYear = parseInt(nextWeekParts.find(part => part.type === 'year')?.value || '0');
        const nextWeekMonth = parseInt(nextWeekParts.find(part => part.type === 'month')?.value || '0');
        const nextWeekDay = parseInt(nextWeekParts.find(part => part.type === 'day')?.value || '0');
        
        nextRun = createDateInTimezone(nextWeekYear, nextWeekMonth, nextWeekDay, hours, minutes, timezone);
      } else {
        nextRun = new Date(nextWeekTime);
        nextRun.setUTCHours(hours, minutes, 0, 0);
      }
      
      logger.debug(`Today's time has passed, moving to next week - New next run: ${nextRun.toISOString()}`);
    }

    logger.debug(`Weekly schedule - Days to add: ${daysToAdd}, Next run: ${nextRun.toISOString()}`);
    return nextRun;
  }

  // For monthly schedules, run on the same day each month
  if (scheduleType === 'monthly') {
    // If start date is in the future, return the start date as the first run
    if (startDateTime > now) {
      logger.debug(`Start date is in future for monthly schedule, returning start date: ${startDateTime.toISOString()}`);
      return startDateTime;
    }

    let nextRun: Date;
    
    if (timezone && timezone !== 'UTC') {
      // Get current date in target timezone
      const baseTimeInTz = new Intl.DateTimeFormat('en-CA', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit'
      }).formatToParts(baseTime);

      const currentYear = parseInt(baseTimeInTz.find(part => part.type === 'year')?.value || '0');
      const currentMonth = parseInt(baseTimeInTz.find(part => part.type === 'month')?.value || '0');
      
      nextRun = createDateInTimezone(currentYear, currentMonth, day, hours, minutes, timezone);
      
      // If this month's day has passed, move to next month
      if (nextRun <= baseTime) {
        const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
        const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
        nextRun = createDateInTimezone(nextYear, nextMonth, day, hours, minutes, timezone);
      }
    } else {
      nextRun = new Date(baseTime);
      nextRun.setUTCDate(day);
      nextRun.setUTCHours(hours, minutes, 0, 0);

      // If this month's day has passed, move to next month
      if (nextRun <= baseTime) {
        nextRun.setUTCMonth(nextRun.getUTCMonth() + 1);
      }
    }

    return nextRun;
  }

  // Default fallback - return start date if it's in the future
  return startDateTime > now ? startDateTime : null;
}
