/**
 * Common File Utilities
 * Shared file handling utilities for all platforms
 */

import * as path from 'path';
import * as fs from 'fs';
import { logger } from '../../../utils/logger.js';

/**
 * Ensure a directory exists, creating it if necessary
 * @param dirPath Directory path to ensure exists
 */
export async function ensureDirectoryExists(dirPath: string): Promise<void> {
  try {
    await fs.promises.access(dirPath);
  } catch {
    await fs.promises.mkdir(dirPath, { recursive: true });
    logger.debug(`FileUtils: Created directory ${dirPath}`);
  }
}

/**
 * Generate a standardized screenshot path
 * @param baseDir Base directory for screenshots
 * @param testId Test identifier
 * @param stepId Step identifier
 * @param type Screenshot type (before/after)
 * @param platform Platform identifier
 * @returns Generated screenshot path
 */
export function generateScreenshotPath(
  baseDir: string,
  testId: string,
  stepId: string,
  type: 'before' | 'after',
  platform: string
): string {
  const fileName = `${stepId}_${type}_${platform}.jpg`;
  return path.join(baseDir, testId, 'screenshots', fileName);
}

/**
 * Generate a standardized video path
 * @param baseDir Base directory for videos
 * @param testId Test identifier (used as report ID)
 * @param platform Platform identifier
 * @returns Generated video path
 */
export function generateVideoPath(
  baseDir: string,
  testId: string,
  platform: string
): string {
  const fileName = `${testId}.mp4`;
  return path.join(baseDir, testId, 'videos', fileName);
}

/**
 * Generate a standardized metrics path
 * @param baseDir Base directory for metrics
 * @param testId Test identifier
 * @param platform Platform identifier
 * @returns Generated metrics path
 */
export function generateMetricsPath(
  baseDir: string,
  testId: string,
  platform: string
): string {
  const fileName = `metrics_${platform}.json`;
  return path.join(baseDir, testId, 'metrics', fileName);
}

/**
 * Clean up old files in a directory based on age
 * @param dirPath Directory to clean
 * @param maxAgeMs Maximum file age in milliseconds
 * @param filePattern Optional RegExp pattern to match specific files
 */
export function cleanupOldFiles(
  dirPath: string,
  maxAgeMs: number,
  filePattern?: RegExp
): number {
  if (!fs.existsSync(dirPath)) {
    return 0;
  }

  try {
    const now = new Date().getTime();
    const files = fs.readdirSync(dirPath);
    let deletedCount = 0;

    for (const file of files) {
      const filePath = path.join(dirPath, file);

      // Skip if it doesn't match the pattern
      if (filePattern && !filePattern.test(file)) {
        continue;
      }

      try {
        const stats = fs.statSync(filePath);
        const fileAge = now - stats.mtimeMs;

        if (fileAge > maxAgeMs) {
          if (stats.isDirectory()) {
            deleteDirectoryWithRetry(filePath);
          } else {
            deleteFileWithRetry(filePath);
          }
          deletedCount++;
        }
      } catch (error) {
        logger.warn(`Error checking file ${filePath} during cleanup`, error);
      }
    }

    return deletedCount;
  } catch (error) {
    logger.error(`Failed to clean up directory ${dirPath}`, error);
    return 0;
  }
}

/**
 * Get file size in bytes
 * @param filePath Path to file
 * @returns File size in bytes
 */
export async function getFileSize(filePath: string): Promise<number> {
  try {
    const stats = await fs.promises.stat(filePath);
    return stats.size;
  } catch (error: any) {
    logger.error(`FileUtils: Error getting file size for ${filePath}: ${error.message}`);
    return 0;
  }
}

/**
 * Check if file exists
 * @param filePath Path to file
 * @returns True if file exists, false otherwise
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.promises.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Copy file from source to destination
 * @param sourcePath Source file path
 * @param destPath Destination file path
 */
export async function copyFile(sourcePath: string, destPath: string): Promise<void> {
  try {
    await ensureDirectoryExists(path.dirname(destPath));
    await fs.promises.copyFile(sourcePath, destPath);
    logger.debug(`FileUtils: Copied file from ${sourcePath} to ${destPath}`);
  } catch (error: any) {
    logger.error(`FileUtils: Error copying file: ${error.message}`);
    throw error;
  }
}

/**
 * Read file as buffer
 * @param filePath Path to file
 * @returns File buffer
 */
export async function readFileAsBuffer(filePath: string): Promise<Buffer> {
  try {
    return await fs.promises.readFile(filePath);
  } catch (error: any) {
    logger.error(`FileUtils: Error reading file ${filePath}: ${error.message}`);
    throw error;
  }
}

/**
 * Write buffer to file
 * @param filePath Path to file
 * @param buffer Buffer to write
 */
export async function writeBufferToFile(filePath: string, buffer: Buffer): Promise<void> {
  try {
    await ensureDirectoryExists(path.dirname(filePath));
    await fs.promises.writeFile(filePath, buffer);
    logger.debug(`FileUtils: Wrote buffer to file ${filePath}`);
  } catch (error: any) {
    logger.error(`FileUtils: Error writing buffer to file ${filePath}: ${error.message}`);
    throw error;
  }
}

/**
 * Delete file with retries
 * @param filePath Path to the file to delete
 * @param retries Number of retry attempts
 * @param delayMs Delay between retries in milliseconds
 */
export async function deleteFileWithRetry(
  filePath: string,
  retries: number = 3,
  delayMs: number = 100
): Promise<boolean> {
  if (!fs.existsSync(filePath)) {
    return true; // File doesn't exist, nothing to delete
  }

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      fs.unlinkSync(filePath);
      return true;
    } catch (error) {
      if (attempt < retries) {
        logger.debug(`Failed to delete file ${filePath}, retrying... (${attempt + 1}/${retries})`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      } else {
        logger.error(`Failed to delete file ${filePath} after ${retries} attempts`, error);
        return false;
      }
    }
  }

  return false;
}

/**
 * Recursively delete a directory with retries
 * @param dirPath Path to the directory to delete
 * @param retries Number of retry attempts
 */
export async function deleteDirectoryWithRetry(dirPath: string, retries: number = 3): Promise<boolean> {
  if (!fs.existsSync(dirPath)) {
    return true; // Directory doesn't exist, nothing to delete
  }

  const deleteRecursive = async (targetPath: string): Promise<void> => {
    try {
      const stats = fs.statSync(targetPath);

      if (stats.isDirectory()) {
        // Read directory contents
        const files = fs.readdirSync(targetPath);

        // Delete all files and subdirectories
        for (const file of files) {
          const filePath = path.join(targetPath, file);
          await deleteRecursive(filePath);
        }

        // Delete the now empty directory
        fs.rmdirSync(targetPath);
      } else {
        // Delete file
        fs.unlinkSync(targetPath);
      }
    } catch (error) {
      logger.error(`Error during recursive delete: ${targetPath}`, error);
      throw error;
    }
  };

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      await deleteRecursive(dirPath);
      return true;
    } catch (error) {
      if (attempt < retries) {
        logger.debug(`Failed to delete directory ${dirPath}, retrying... (${attempt + 1}/${retries})`);
        await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
      } else {
        logger.error(`Failed to delete directory ${dirPath} after ${retries} attempts`, error);
        return false;
      }
    }
  }

  return false;
}

/**
 * Save data to a JSON file
 * @param filePath Path to save the file
 * @param data Data to save
 */
export async function saveJsonToFile(filePath: string, data: any): Promise<boolean> {
  try {
    const dirPath = path.dirname(filePath);
    await ensureDirectoryExists(dirPath);

    const jsonData = JSON.stringify(data, null, 2);
    await fs.promises.writeFile(filePath, jsonData, 'utf8');
    logger.debug(`Saved JSON data to ${filePath}`);
    return true;
  } catch (error) {
    logger.error(`Failed to save JSON to ${filePath}`, error);
    return false;
  }
}

/**
 * Read data from a JSON file
 * @param filePath Path to the file
 * @returns Parsed JSON data or null if file doesn't exist or is invalid
 */
export function readJsonFromFile<T = any>(filePath: string): T | null {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }

    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data) as T;
  } catch (error) {
    logger.error(`Failed to read or parse JSON from ${filePath}`, error);
    return null;
  }
}
