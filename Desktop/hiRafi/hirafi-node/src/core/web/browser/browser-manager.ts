/**
 * Browser Manager
 * Handles browser initialization, page creation, and agent management
 */

import { <PERSON><PERSON><PERSON>, <PERSON> } from 'rebrowser-puppeteer';
import puppeteer from 'rebrowser-puppeteer';
import { PuppeteerAgent } from 'rfi-ai-web/puppeteer';
import { ILoggerService } from '../../../utils/logger-service.js';

import { IBrowserManager } from './browser-manager.interface.js';

/**
 * Browser state enumeration for atomic state management
 */
enum BrowserState {
  IDLE = 'idle',
  INITIALIZING = 'initializing',
  READY = 'ready',
  CLOSING = 'closing',
  CLOSED = 'closed',
  ERROR = 'error'
}

/**
 * Browser Manager
 * Implements browser initialization, page creation, and agent management with atomic state transitions
 */
/**
 * Process Manager Interface for browser process tracking
 */
interface IProcessManager {
  trackProcess(process: any, type: string, testId?: string, description?: string): void;
  cleanupProcess(processId: number): Promise<void>;
  getInstance(): IProcessManager;
  getTrackedProcesses(): Map<number, any>;
  cleanupAllProcesses(): Promise<void>;
  getProcessesByType(type: string): any[];
}

/**
 * Process Manager Implementation
 * Tracks and manages browser-related processes
 */
class ProcessManager implements IProcessManager {
  private trackedProcesses: Map<number, any> = new Map();
  private logger: ILoggerService;

  constructor(logger: ILoggerService) {
    this.logger = logger;
  }

  trackProcess(process: any, type: string, testId?: string, description?: string): void {
    if (!process || !process.pid) {
      this.logger.warn('ProcessManager: Cannot track process without PID');
      return;
    }

    const processInfo = {
      pid: process.pid,
      type,
      testId,
      description,
      startTime: new Date(),
      process
    };

    this.trackedProcesses.set(process.pid, processInfo);
    this.logger.debug(`ProcessManager: Tracking ${type} process ${process.pid}${testId ? ` for test ${testId}` : ''}`);
  }

  async cleanupProcess(processId: number): Promise<void> {
    const processInfo = this.trackedProcesses.get(processId);
    if (!processInfo) {
      this.logger.debug(`ProcessManager: Process ${processId} not tracked, skipping cleanup`);
      return;
    }

    try {
      if (processInfo.process && !processInfo.process.killed) {
        this.logger.debug(`ProcessManager: Terminating ${processInfo.type} process ${processId}`);
        processInfo.process.kill('SIGTERM');

        // Wait a bit for graceful termination
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Force kill if still running
        if (!processInfo.process.killed) {
          this.logger.warn(`ProcessManager: Force killing ${processInfo.type} process ${processId}`);
          processInfo.process.kill('SIGKILL');
        }
      }
    } catch (error: any) {
      this.logger.error(`ProcessManager: Error cleaning up process ${processId}: ${error.message}`);
    } finally {
      this.trackedProcesses.delete(processId);
    }
  }

  getInstance(): IProcessManager {
    return this;
  }

  getTrackedProcesses(): Map<number, any> {
    return new Map(this.trackedProcesses);
  }

  async cleanupAllProcesses(): Promise<void> {
    const processIds = Array.from(this.trackedProcesses.keys());
    this.logger.info(`ProcessManager: Cleaning up ${processIds.length} tracked processes`);

    await Promise.allSettled(
      processIds.map(pid => this.cleanupProcess(pid))
    );
  }

  getProcessesByType(type: string): any[] {
    const processes: any[] = [];
    for (const processInfo of this.trackedProcesses.values()) {
      if (processInfo.type === type) {
        processes.push(processInfo);
      }
    }
    return processes;
  }
}

export class BrowserManager implements IBrowserManager {
  private browser: Browser | null = null;
  private pageAgents: Map<Page, any> = new Map();
  private processManager: IProcessManager;
  private state: BrowserState = BrowserState.IDLE;
  private stateTransitionLock: Promise<void> = Promise.resolve();
  private cleanupInProgress: Set<Page> = new Set();
  private logger: ILoggerService;

  // Race condition prevention for force cleanup
  private forceCleanupInProgress: boolean = false;
  private forceCleanupPromise: Promise<void> | null = null;

  /**
   * Default browser launch arguments optimized for non-headless Chrome with minimal CPU overhead
   * Cleaned up and deduplicated for better performance
   */
  private DEFAULT_BROWSER_ARGS = [
    // Disable HTTP/2 to prevent ERR_HTTP2_PROTOCOL_ERROR on some sites
    '--disable-http2',

    // Essential security flags for Docker
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',

    // Basic rendering (minimal GPU usage)
    '--disable-gpu',
    '--disable-software-rasterizer',

    // System integration
    '--disable-dbus',
    '--no-first-run',
    '--no-zygote',

    // Essential feature disabling for stability and performance
    '--disable-extensions',
    '--disable-plugins',
    '--disable-translate',
    '--disable-default-apps',
    '--disable-sync',
    '--disable-desktop-notifications',
    '--disable-background-networking',

    // Performance optimization (reduced CPU usage)
    '--memory-pressure-off',
    '--disable-hang-monitor',
    '--disable-component-update',
    '--disable-domain-reliability',

    // Stealth and anti-detection (essential only)
    '--disable-blink-features=AutomationControlled',
    '--disable-web-security',
    '--disable-infobars',
    '--exclude-switches=enable-automation',
    '--disable-client-side-phishing-detection',
    '--disable-popup-blocking',
    '--enable-automation=false',
    '--password-store=basic',
    '--use-mock-keychain',
  ];

  /**
   * Browser launch configuration optimized for non-headless mode with minimal overhead
   */
  private BROWSER_LAUNCH_CONFIG: {
    headless: boolean | 'shell' | 'new';
    args: string[];
    protocolTimeout: number;
    defaultViewport: null;
    executablePath?: string;
    ignoreDefaultArgs?: boolean | string[];
    handleSIGINT?: boolean;
    handleSIGTERM?: boolean;
    handleSIGHUP?: boolean;
  } = {
    headless: 'new', // Use new headless mode to prevent flashing
    args: this.DEFAULT_BROWSER_ARGS,
    protocolTimeout: parseInt(process.env.PUPPETEER_PROTOCOL_TIMEOUT || '180000'), // Reduced from 300000
    defaultViewport: null,
    handleSIGINT: false,
    handleSIGTERM: false,
    handleSIGHUP: false,
    // Only ignore automation-related args that could expose automation
    ignoreDefaultArgs: [
      '--enable-automation',
      '--enable-blink-features=IdleDetection',
    ],
  };

  constructor(logger: ILoggerService) {
    this.logger = logger;

    // Note: AI configuration is now handled by AIConfigurationService
    // to avoid overriding test-specific AI model settings

    // Initialize ProcessManager for browser process tracking
    this.processManager = new ProcessManager(logger);

    // Configure rebrowser-patches environment variables for optimal stealth
    this.configureStealthEnvironment();

    this.logger.info('BrowserManager: Initialized with rebrowser-puppeteer stealth capabilities and ProcessManager');
  }

  /**
   * Atomically transition browser state with validation
   * @param newState Target state to transition to
   * @param reason Optional reason for state transition
   * @returns Promise that resolves when transition is complete
   */
  private async transitionState(newState: BrowserState, reason?: string): Promise<void> {
    // Serialize state transitions to prevent race conditions
    this.stateTransitionLock = this.stateTransitionLock.then(async () => {
      const oldState = this.state;

      // Validate state transition
      if (!this.isValidStateTransition(oldState, newState)) {
        const error = `Invalid state transition from ${oldState} to ${newState}`;
        this.logger.error(`BrowserManager: ${error}${reason ? ` - ${reason}` : ''}`);
        throw new Error(error);
      }

      this.logger.debug(`BrowserManager: State transition ${oldState} -> ${newState}${reason ? ` (${reason})` : ''}`);
      this.state = newState;
    });

    await this.stateTransitionLock;
  }

  /**
   * Validate if a state transition is allowed
   * @param from Current state
   * @param to Target state
   * @returns True if transition is valid
   */
  private isValidStateTransition(from: BrowserState, to: BrowserState): boolean {
    const validTransitions: Record<BrowserState, BrowserState[]> = {
      [BrowserState.IDLE]: [BrowserState.INITIALIZING, BrowserState.ERROR],
      [BrowserState.INITIALIZING]: [BrowserState.READY, BrowserState.ERROR, BrowserState.CLOSED],
      [BrowserState.READY]: [BrowserState.CLOSING, BrowserState.ERROR, BrowserState.CLOSED],
      [BrowserState.CLOSING]: [BrowserState.CLOSED, BrowserState.ERROR],
      [BrowserState.CLOSED]: [BrowserState.INITIALIZING], // Allow re-initialization
      [BrowserState.ERROR]: [BrowserState.INITIALIZING, BrowserState.CLOSING, BrowserState.CLOSED]
    };

    return validTransitions[from]?.includes(to) ?? false;
  }

  /**
   * Get current browser state
   * @returns Current state
   */
  private getCurrentState(): BrowserState {
    return this.state;
  }

  /**
   * Check if browser is in a state that allows new operations
   * @returns True if operations are allowed
   */
  private canPerformOperations(): boolean {
    return this.state === BrowserState.READY;
  }

  /**
   * Check if browser is closing or closed
   * @returns True if browser is shutting down
   */
  private isShuttingDown(): boolean {
    return this.state === BrowserState.CLOSING || this.state === BrowserState.CLOSED;
  }

  /**
   * Wait for browser to reach a specific state
   * @param targetState State to wait for
   * @param timeout Maximum time to wait in milliseconds
   * @returns Promise that resolves when state is reached or rejects on timeout
   */
  private async waitForStateTransition(targetState: BrowserState, timeout: number = 5000): Promise<void> {
    const startTime = Date.now();

    while (this.state !== targetState && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (this.state !== targetState) {
      throw new Error(`Timeout waiting for state transition to ${targetState}. Current state: ${this.state}`);
    }
  }

  /**
   * Initialize the browser manager
   * @param config Configuration object
   */
  async initialize(config: any): Promise<void> {
    // Update browser launch config with any config values
    if (config?.browser?.headless !== undefined) {
      this.BROWSER_LAUNCH_CONFIG.headless = config.browser.headless;
    }

    if (config?.browser?.executablePath) {
      this.BROWSER_LAUNCH_CONFIG.executablePath = config.browser.executablePath;
    }

    this.logger.info('BrowserManager: Initialized with configuration');
  }

  /**
   * Create a browser instance
   * @param options Browser options
   * @returns Promise resolving to a browser instance
   */
  async createBrowser(options?: any): Promise<Browser> {
    // Check if we can create a browser in current state
    if (this.state === BrowserState.CLOSING || this.state === BrowserState.ERROR) {
      throw new Error(`Cannot create browser in state: ${this.state}`);
    }

    // Transition to initializing state
    await this.transitionState(BrowserState.INITIALIZING, 'Starting browser creation');

    try {
      // Ensure any previous browser is completely cleaned up
      if (this.browser) {
        this.logger.warn('BrowserManager: Previous browser instance detected, force cleaning up');
        await this.forceCleanupAll();
        
        // Additional cleanup delay to ensure complete process termination
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      this.logger.info('BrowserManager: Using new headless mode (optimized, no flashing)');

      // Simplified browser launch configuration with minimal CPU overhead
      const dynamicPort = await this.findAvailablePort(9222, 9300);
      const userDataDir = `/tmp/chrome-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const launchConfig = {
        ...this.BROWSER_LAUNCH_CONFIG,
        ...options,
        args: [
          ...this.DEFAULT_BROWSER_ARGS,
          `--remote-debugging-port=${dynamicPort}`,
          `--user-data-dir=${userDataDir}`,
          // Only add essential args that aren't already in DEFAULT_BROWSER_ARGS
          '--disable-shared-memory-usage',
          '--no-default-browser-check',
          '--disable-background-timer-throttling',
          '--disable-renderer-backgrounding',
          '--disable-backgrounding-occluded-windows',
          ...(options?.args || [])
        ]
      };

      this.logger.info('BrowserManager: Launching stealth browser with effective config:', launchConfig);

      this.browser = await puppeteer.launch(launchConfig as any);

      // Handle browser disconnection with coordinated cleanup
      if (this.browser) {
        this.browser.on('disconnected', () => {
          this.logger.error('BrowserManager: Browser disconnected unexpectedly - this indicates a browser crash');
          this.logBrowserCrashDetails();

          // Coordinate cleanup without race conditions
          this.handleBrowserDisconnection().catch((error: any) => {
            this.logger.error(`BrowserManager: Error during browser disconnection cleanup: ${error.message}`);
          });
        });

        // Handle browser process errors with detailed logging and track with ProcessManager
        if (this.browser.process()) {
          const browserProcess = this.browser.process();

          // Track browser process with ProcessManager
          if (browserProcess && browserProcess.pid) {
            this.processManager.trackProcess(
              browserProcess,
              'browser',
              undefined, // testId will be set later when test starts
              'Main browser process'
            );
            this.logger.info(`BrowserManager: Tracking browser process ${browserProcess.pid} with ProcessManager`);
          }

          browserProcess?.on('error', (error: any) => {
            this.logger.error(`BrowserManager: Browser process error detected: ${error.message}`);
            this.logger.error(`BrowserManager: Error stack: ${error.stack}`);
            this.logBrowserCrashDetails();
          });

          browserProcess?.on('exit', (code: any, signal: any) => {
            this.logger.error(`BrowserManager: Browser process exited unexpectedly - code: ${code}, signal: ${signal}`);
            this.logBrowserProcessExit(code, signal);
          });

          browserProcess?.on('close', (code: any, signal: any) => {
            this.logger.warn(`BrowserManager: Browser process closed - code: ${code}, signal: ${signal}`);
          });

          // Monitor browser process memory usage
          this.startBrowserProcessMonitoring();
        }

        this.logger.info('BrowserManager: Browser successfully launched with rebrowser-puppeteer (stealth mode enabled)');

        // Transition to ready state
        await this.transitionState(BrowserState.READY, 'Browser successfully initialized');

        return this.browser;
      }

      throw new Error('Failed to initialize browser - browser instance is null');
    } catch (error) {
      this.logger.error('BrowserManager: Failed to launch browser', error);

      // Transition to error state
      await this.transitionState(BrowserState.ERROR, `Browser initialization failed: ${error}`);
      throw error;
    }
  }

  /**
   * Handle page close with coordinated cleanup
   * @param page Page that was closed
   * @private
   */
  private async handlePageClose(page: Page): Promise<void> {
    try {
      // Prevent concurrent cleanup for the same page
      if (this.cleanupInProgress.has(page)) {
        this.logger.debug('BrowserManager: Page cleanup already in progress, skipping duplicate cleanup');
        return;
      }

      this.cleanupInProgress.add(page);

      // Get the agent for this page
      const agent = this.pageAgents.get(page);

      if (agent) {
        // Dispose of the agent if it has a dispose method
        if (typeof agent.dispose === 'function') {
          try {
            await agent.dispose();
            this.logger.debug('BrowserManager: Successfully disposed agent for closed page');
          } catch (agentError: any) {
            this.logger.warn(`BrowserManager: Error disposing agent for closed page: ${agentError.message}`);
          }
        }

        // Remove the agent from the Map to prevent memory leak
        this.pageAgents.delete(page);
        this.logger.debug('BrowserManager: Removed agent from pageAgents Map for closed page');
      } else {
        this.logger.debug('BrowserManager: No agent found for closed page - no cleanup needed');
      }

      this.logger.debug(`BrowserManager: Page close cleanup completed. Remaining agents: ${this.pageAgents.size}`);

    } catch (cleanupError: any) {
      this.logger.error(`BrowserManager: Error during page close cleanup: ${cleanupError.message}`);
    } finally {
      this.cleanupInProgress.delete(page);
    }
  }

  /**
   * Handle browser disconnection with coordinated cleanup
   * @private
   */
  private async handleBrowserDisconnection(): Promise<void> {
    try {
      // Only proceed if not already closing/closed
      if (this.isShuttingDown()) {
        this.logger.debug('BrowserManager: Browser disconnection cleanup skipped - already shutting down');
        return;
      }

      // Transition to closing state to prevent new operations
      await this.transitionState(BrowserState.CLOSING, 'Browser disconnected unexpectedly');

      // Dispose all agents with timeout
      const agentCleanupPromises = Array.from(this.pageAgents.entries()).map(async ([page, agent]) => {
        if (agent && typeof agent.dispose === 'function') {
          try {
            await Promise.race([
              agent.dispose(),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Agent disposal timeout')), 5000))
            ]);
            this.logger.debug('BrowserManager: Successfully disposed agent during disconnection cleanup');
          } catch (error: any) {
            this.logger.warn(`BrowserManager: Error disposing agent during disconnection: ${error.message}`);
          }
        }
      });

      await Promise.allSettled(agentCleanupPromises);

      // Clear references
      this.browser = null;
      this.pageAgents.clear();
      this.cleanupInProgress.clear();

      // Transition to closed state
      await this.transitionState(BrowserState.CLOSED, 'Browser disconnection cleanup completed');

    } catch (error: any) {
      this.logger.error(`BrowserManager: Error during browser disconnection cleanup: ${error.message}`);
      await this.transitionState(BrowserState.ERROR, `Disconnection cleanup failed: ${error.message}`);
    }
  }

  /**
   * Create a page in a browser
   * @param browser Browser instance
   * @param options Page options
   * @returns Promise resolving to a page
   */
  async createPage(browser: Browser, options?: any): Promise<Page> {
    this.logger.debug('BrowserManager: Creating new page');

    // Prevent page creation during browser shutdown
    if (!this.canPerformOperations()) {
      throw new Error(`Cannot create page while browser is in ${this.state} state`);
    }

    try {
      const page = await browser.newPage();

      // Set default navigation timeout (use env var or default to 3 minutes for AWS environments)
      page.setDefaultNavigationTimeout(parseInt(process.env.PUPPETEER_NAVIGATION_TIMEOUT || '180000'));

      // Set default timeout for all operations (including screenshots) - use env var or default for AWS
      page.setDefaultTimeout(parseInt(process.env.PUPPETEER_DEFAULT_TIMEOUT || '180000'));

      // Add comprehensive page error handlers
      page.on('error', (error: any) => {
        this.logger.error(`BrowserManager: Page error detected: ${error.message}`);
        this.logger.error(`BrowserManager: Page error stack: ${error.stack}`);
        this.logPageCrashDetails(page, error);
      });

      // Verbose logging - only enable when debugging performance or page issues
      if (options?.enableVerboseLogging) {
        page.on('pageerror', (error: any) => {
          this.logger.warn(`BrowserManager: Page JavaScript error: ${error.message}`);
          this.logger.debug(`BrowserManager: Page JavaScript error stack: ${error.stack}`);
        });

        page.on('requestfailed', (request: any) => {
          const failure = request.failure();
          this.logger.debug(`BrowserManager: Request failed: ${request.url()} - ${failure?.errorText || 'Unknown error'}`);

          // Log critical request failures
          if (failure?.errorText?.includes('net::ERR_CONNECTION_REFUSED') ||
              failure?.errorText?.includes('net::ERR_NAME_NOT_RESOLVED')) {
            this.logger.warn(`BrowserManager: Critical network error: ${failure.errorText} for URL: ${request.url()}`);
          }
        });

        // Add console message handler for debugging
        page.on('console', (msg: any) => {
          const type = msg.type();
          if (type === 'error') {
            this.logger.debug(`BrowserManager: Browser console error: ${msg.text()}`);
          } else if (type === 'warn') {
            this.logger.debug(`BrowserManager: Browser console warning: ${msg.text()}`);
          }
        });
      }

      // Add page close handler with coordinated cleanup
      page.on('close', () => {
        this.logger.warn('BrowserManager: Page was closed unexpectedly - starting coordinated cleanup');

        // Use coordinated cleanup instead of "fire and forget"
        this.handlePageClose(page).catch((error: any) => {
          this.logger.error(`BrowserManager: Error during page close cleanup: ${error.message}`);
        });
      });

      // Set default user agent if not specified in options
      const userAgent = options?.userAgent || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36';
      if (userAgent) {
        const userAgentSet = await this.safeSetUserAgent(page, userAgent);
        if (!userAgentSet) {
          this.logger.warn('BrowserManager: Failed to set user agent, continuing without it');
        }
      }

      // Apply zoom controls
      await this.applyZoomControls(page);

      // Log viewport info
      this.logger.info("BrowserManager: createPage - Viewport will be set by test-runner based on hub settings");

      // Initialize PuppeteerAgent if scenarioId is provided (use scenarioId for caching)
      if (options?.scenarioId) {
        await this.createAgent(page, options.scenarioId);
      }

      return page;
    } catch (error: any) {
      this.logger.error(`BrowserManager: Failed to create page: ${error.message}`);
      throw error;
    }
  }

  /**
   * Close a browser instance
   * @param browser Browser instance to close
   */
  async closeBrowser(browser: Browser): Promise<void> {
    if (!browser) {
      return;
    }

    // Prevent concurrent close operations
    if (this.isShuttingDown()) {
      this.logger.debug('BrowserManager: Browser already closing/closed, skipping duplicate close operation');
      return;
    }

    // Transition to closing state to prevent new operations
    await this.transitionState(BrowserState.CLOSING, 'Starting browser closure');

    try {
      this.logger.info('BrowserManager: Starting browser closure with agent disposal');

      // Get open pages
      const pages = await browser.pages().catch((e: any) => {
        this.logger.error('BrowserManager: Error getting open pages', e);
        return [];
      });

      this.logger.info(`BrowserManager: Found ${pages.length} pages to close`);

      // Phase 1: Dispose of all agents in parallel for performance
      const agentDisposalResults = await Promise.allSettled(
        pages.map(async (page: any) => {
          const agent = this.pageAgents.get(page);
          if (agent && typeof agent.dispose === 'function') {
            try {
              await agent.dispose();
              this.logger.debug('BrowserManager: Successfully disposed agent for page');
              return true;
            } catch (agentError: any) {
              this.logger.warn(`BrowserManager: Error disposing agent: ${agentError.message}`);
              return false;
            }
          }
          return true; // No agent to dispose
        })
      );

      const successfulAgentDisposals = agentDisposalResults.filter(
        (result: any) => result.status === 'fulfilled' && result.value
      ).length;
      this.logger.info(`BrowserManager: Disposed ${successfulAgentDisposals}/${pages.length} page agents`);

      // Phase 2: Close all pages in parallel
      const pageCloseResults = await Promise.all(
        pages.map((page: any) => this.closeSinglePage(page))
      );

      this.logger.info(`BrowserManager: Closed ${pageCloseResults.filter(Boolean).length}/${pages.length} pages`);

      // Phase 3: Try closing the browser with escalating approaches
      let closed = await this.closeBrowserNormally(browser);

      if (!closed) {
        closed = await this.forceBrowserDisconnect(browser);
      }

      if (!closed) {
        await this.killBrowserProcessWithProcessManager(browser);
      }
      // Transition to closed state on successful closure
      await this.transitionState(BrowserState.CLOSED, 'Browser closure completed successfully');

    } catch (error) {
      this.logger.error('BrowserManager: Error closing browser', error);

      // Transition to error state
      await this.transitionState(BrowserState.ERROR, `Browser closure failed: ${error}`);

    } finally {
      // Clear the browser reference no matter what
      if (this.browser === browser) {
        this.browser = null;
      }
      this.pageAgents.clear();
      this.cleanupInProgress.clear();
      this.logger.info('BrowserManager: Browser instance and agent references cleared');
    }
  }

  /**
   * Create an AI agent for a page
   * @param page Page to create agent for
   * @param scenarioId Scenario identifier for caching (should be scenarioId, not testId)
   * @returns Promise resolving to an agent
   */
  async createAgent(page: Page, scenarioId?: string): Promise<any> {
    try {
      const agent = new PuppeteerAgent(page as any, {
        cacheId: scenarioId,  // Use scenarioId for caching instead of testId
        generateReport: false,
        autoPrintReportMsg: false,
        aiTemperature: 0.1,  // Low temperature for consistent, deterministic automation
        aiSeed: 55           // Fixed seed for reproducible AI behavior
      } as any);

      // Store the agent in the Map with the page as key
      this.pageAgents.set(page, agent);
      this.logger.debug("BrowserManager: PuppeteerAgent successfully created");

      return agent;
    } catch (err: any) {
      this.logger.error("BrowserManager: Error creating PuppeteerAgent:", err);
      throw err;
    }
  }

  /**
   * Get the AI agent for a page
   * @param page Page to get agent for
   * @returns Agent or undefined if not found
   */
  getPageAgent(page: Page): any {
    return this.pageAgents.get(page);
  }

  /**
   * Clean up the agent for a specific page
   * @param page Page to clean up agent for
   * @returns Promise resolving to true if cleanup was successful
   */
  async cleanupPageAgent(page: Page): Promise<boolean> {
    try {
      const agent = this.pageAgents.get(page);

      if (!agent) {
        this.logger.debug('BrowserManager: No agent found for page - no cleanup needed');
        return true;
      }

      // Dispose of the agent if it has a dispose method
      if (typeof agent.dispose === 'function') {
        try {
          await agent.dispose();
          this.logger.debug('BrowserManager: Successfully disposed agent for page');
        } catch (agentError: any) {
          this.logger.warn(`BrowserManager: Error disposing agent: ${agentError.message}`);
          // Continue with cleanup even if disposal fails
        }
      }

      // Remove the agent from the Map
      this.pageAgents.delete(page);
      this.logger.debug(`BrowserManager: Removed agent from pageAgents Map. Remaining agents: ${this.pageAgents.size}`);

      return true;
    } catch (error: any) {
      this.logger.error(`BrowserManager: Error during page agent cleanup: ${error.message}`);
      return false;
    }
  }

  /**
   * Apply zoom controls to a page
   * @param page Page to apply zoom controls to
   */
  private async applyZoomControls(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      // Set default zoom to 100% with null check
      document.addEventListener('DOMContentLoaded', () => {
        if (document.body) {
          // TypeScript error for any type
          (document.body.style as any).zoom = '100%';
        }
      });

      // Prevent zoom events
      window.addEventListener('wheel', (e) => {
        if (e.ctrlKey) {
          e.preventDefault();
        }
      }, { passive: false, capture: true });

      // Prevent keyboard zoom shortcuts
      window.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && (e.key === '=' || e.key === '-' || e.key === '+')) {
          e.preventDefault();
        }
      }, { passive: false, capture: true });
    });
  }

  /**
   * Safely set user agent with error handling
   * @param page Page to set user agent for
   * @param userAgent User agent string
   * @returns True if successful, false otherwise
   */
  private async safeSetUserAgent(page: Page, userAgent: string): Promise<boolean> {
    if (!page) {
      this.logger.warn('BrowserManager: Cannot set user agent - page is null');
      return false;
    }

    try {
      // Check if page target is still valid before setting user agent
      const target = page.target();
      if (!target || target.type() === 'other') {
        this.logger.warn('BrowserManager: Cannot set user agent - page target is invalid or closed');
        return false;
      }

      // Check if page is closed
      if (page.isClosed()) {
        this.logger.warn('BrowserManager: Cannot set user agent - page is closed');
        return false;
      }

      // Try to set user agent with timeout
      await Promise.race([
        page.setUserAgent(userAgent),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('User agent setting timeout')), 5000)
        )
      ]);

      this.logger.debug(`BrowserManager: Set user agent: ${userAgent}`);
      return true;
    } catch (error: any) {
      const errorMessage = error.message || '';

      // Check for target/protocol errors that indicate browser is closing
      if (errorMessage.includes('Target closed') ||
          errorMessage.includes('TargetCloseError') ||
          errorMessage.includes('Protocol error') ||
          errorMessage.includes('Session closed') ||
          errorMessage.includes('Network.setUserAgentOverride')) {
        this.logger.warn(`BrowserManager: Browser target closed while setting user agent: ${errorMessage}`);
        return false;
      }

      this.logger.error(`BrowserManager: Error setting user agent: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Close a single page
   * @param page Page to close
   * @returns True if successful, false otherwise
   */
  private async closeSinglePage(page: Page): Promise<boolean> {
    try {
      // Clean up the agent before closing the page
      await this.cleanupPageAgent(page);

      await page.close({ runBeforeUnload: false }).catch((e: any) => {
        this.logger.warn(`BrowserManager: Error closing page: ${e}`);
      });
      return true;
    } catch (error) {
      this.logger.error(`BrowserManager: Failed to close page: ${error}`);
      return false;
    }
  }

  /**
   * Close browser normally
   * @param browser Browser to close
   * @returns True if successful, false otherwise
   */
  private async closeBrowserNormally(browser: Browser): Promise<boolean> {
    try {
      await browser.close();
      this.logger.info('BrowserManager: Browser closed successfully');
      return true;
    } catch (closeError) {
      this.logger.error('BrowserManager: Failed to close browser normally', closeError);
      return false;
    }
  }

  /**
   * Force disconnect browser
   * @param browser Browser to disconnect
   * @returns True if successful, false otherwise
   */
  private async forceBrowserDisconnect(browser: Browser): Promise<boolean> {
    try {
      browser.disconnect();
      this.logger.info('BrowserManager: Disconnected browser after close failure');
      return true;
    } catch (disconnectError) {
      this.logger.error('BrowserManager: Even disconnect failed', disconnectError);
      return false;
    }
  }

  /**
   * Kill browser process using ProcessManager (new implementation)
   * @param browser Browser instance
   * @returns Promise resolving to true if successful
   */
  private async killBrowserProcessWithProcessManager(browser: Browser): Promise<boolean> {
    try {
      const process = browser.process();
      if (!process) {
        this.logger.warn('BrowserManager: No browser process to kill');
        return false;
      }

      const pid = process.pid;
      if (!pid) {
        this.logger.warn('BrowserManager: Browser process has no PID');
        return false;
      }

      this.logger.info(`BrowserManager: Killing browser process with PID ${pid} using ProcessManager`);

      // Use ProcessManager for comprehensive process cleanup
      await this.processManager.cleanupProcess(pid);

      this.logger.info(`BrowserManager: Successfully terminated browser process ${pid} using ProcessManager`);
      return true;
    } catch (killError) {
      this.logger.error('BrowserManager: Failed to kill browser process with ProcessManager', killError);
      return false;
    }
  }

  /**
   * Force cleanup all browser instances (emergency cleanup with state machine support)
   * Enhanced with race condition prevention
   */
  public async forceCleanupAll(): Promise<void> {
    // Prevent concurrent force cleanup operations
    if (this.forceCleanupInProgress) {
      this.logger.debug('BrowserManager: Force cleanup already in progress, waiting for completion');
      await this.forceCleanupPromise;
      return;
    }

    // Set cleanup in progress flag and create promise
    this.forceCleanupInProgress = true;
    this.forceCleanupPromise = this.executeForceCleanup();

    try {
      await this.forceCleanupPromise;
    } finally {
      this.forceCleanupInProgress = false;
      this.forceCleanupPromise = null;
    }
  }

  /**
   * Execute the actual force cleanup logic
   */
  private async executeForceCleanup(): Promise<void> {
    try {
      this.logger.warn('BrowserManager: Starting force cleanup of all browser instances');

      // Check current state and handle appropriately
      const currentState = this.getCurrentState();

      if (currentState === BrowserState.CLOSED) {
        this.logger.debug('BrowserManager: Browser already closed, skipping force cleanup');
        return;
      }

      if (currentState === BrowserState.CLOSING) {
        this.logger.debug('BrowserManager: Browser already closing, waiting for completion');
        // Wait for current closing operation to complete
        await this.waitForStateTransition(BrowserState.CLOSED, 10000);
        return;
      }

      // Force transition to closing state if not already closing/closed
      if (!this.isShuttingDown()) {
        await this.transitionState(BrowserState.CLOSING, 'Force cleanup initiated');
      }

      if (this.browser) {
        await this.closeBrowser(this.browser);
      }

      // Use ProcessManager to cleanup any remaining browser processes
      const browserProcesses = this.processManager.getProcessesByType('browser');
      if (browserProcesses.length > 0) {
        this.logger.warn(`BrowserManager: Force cleaning ${browserProcesses.length} remaining browser processes`);
        for (const processInfo of browserProcesses) {
          await this.processManager.cleanupProcess(processInfo.pid);
        }
      }

      // ENHANCED CLEANUP: Kill any remaining Chrome/Chromium processes
      await this.killRemainingChromeProcesses();

      // ENHANCED CLEANUP: Clean up temporary directories and locks
      await this.cleanupBrowserArtifacts();

      // ENHANCED CLEANUP: Add extended cooling period between browser instances
      this.logger.info('BrowserManager: Waiting for browser process cleanup to complete...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second cooling period

      // Clear all references
      this.browser = null;
      this.pageAgents.clear();
      this.cleanupInProgress.clear();

      // Transition to closed state only if not already closed
      if (this.getCurrentState() !== BrowserState.CLOSED) {
        await this.transitionState(BrowserState.CLOSED, 'Force cleanup completed');
      }

      this.logger.info('BrowserManager: Force cleanup completed');
    } catch (error: any) {
      this.logger.error('BrowserManager: Error during force cleanup', error);
      // Force transition to error state
      try {
        await this.transitionState(BrowserState.ERROR, `Force cleanup failed: ${error}`);
      } catch (stateError) {
        this.logger.error('BrowserManager: Failed to transition to error state during cleanup', stateError);
      }
    }
  }

  /**
   * Kill any remaining Chrome/Chromium processes that might interfere with new instances
   * @private
   */
  private async killRemainingChromeProcesses(): Promise<void> {
    try {
      const { spawn } = await import('child_process');
      
      // Kill chrome processes on Linux/Docker
      const killChrome = spawn('pkill', ['-f', 'chrome'], { stdio: 'ignore' });
      await new Promise(resolve => {
        killChrome.on('close', resolve);
        setTimeout(resolve, 1000); // Timeout after 1 second
      });

      // Kill chromium processes on Linux/Docker  
      const killChromium = spawn('pkill', ['-f', 'chromium'], { stdio: 'ignore' });
      await new Promise(resolve => {
        killChromium.on('close', resolve);
        setTimeout(resolve, 1000); // Timeout after 1 second
      });

      this.logger.debug('BrowserManager: Killed remaining Chrome/Chromium processes');
    } catch (error: any) {
      this.logger.debug(`BrowserManager: Error killing Chrome processes (this is usually harmless): ${error.message}`);
    }
  }

  /**
   * Clean up browser artifacts like temp directories, caches, and lock files
   * @private
   */
  private async cleanupBrowserArtifacts(): Promise<void> {
    try {
      const { spawn } = await import('child_process');
      
      // Clean up Chrome temp directories
      const cleanupTemp = spawn('find', ['/tmp', '-name', 'chrome_*', '-type', 'd', '-exec', 'rm', '-rf', '{}', '+'], { stdio: 'ignore' });
      await new Promise(resolve => {
        cleanupTemp.on('close', resolve);
        setTimeout(resolve, 2000); // Timeout after 2 seconds
      });

      // Clean up Chromium temp directories  
      const cleanupChromiumTemp = spawn('find', ['/tmp', '-name', 'chromium_*', '-type', 'd', '-exec', 'rm', '-rf', '{}', '+'], { stdio: 'ignore' });
      await new Promise(resolve => {
        cleanupChromiumTemp.on('close', resolve);
        setTimeout(resolve, 2000); // Timeout after 2 seconds
      });

      this.logger.debug('BrowserManager: Cleaned up browser artifacts');
    } catch (error: any) {
      this.logger.debug(`BrowserManager: Error cleaning up browser artifacts (this is usually harmless): ${error.message}`);
    }
  }

  /**
   * Find an available port for browser debugging
   * @param startPort Starting port to check
   * @param endPort Ending port to check
   * @returns Promise resolving to an available port number
   * @private
   */
  private async findAvailablePort(startPort: number, endPort: number): Promise<number> {
    const net = await import('net');

    for (let port = startPort; port <= endPort; port++) {
      if (await this.isPortAvailable(port)) {
        return port;
      }
    }
    
    // Fallback to default port if no available port found
    this.logger.warn(`BrowserManager: No available port found between ${startPort}-${endPort}, using default`);
    return 9222;
  }

  /**
   * Check if a port is available
   * @param port Port number to check
   * @returns Promise resolving to true if port is available
   * @private
   */
  private async isPortAvailable(port: number): Promise<boolean> {
    const net = await import('net');
    
    return new Promise((resolve) => {
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => {
          resolve(true);
        });
        server.close();
      });
      
      server.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * Log detailed browser crash information
   */
  private logBrowserCrashDetails(): void {
    try {
      this.logger.error('BrowserManager: === BROWSER CRASH DETECTED ===');
      this.logger.error(`BrowserManager: Timestamp: ${new Date().toISOString()}`);
      this.logger.error(`BrowserManager: Node.js version: ${process.version}`);
      this.logger.error(`BrowserManager: Platform: ${process.platform}`);
      this.logger.error(`BrowserManager: Architecture: ${process.arch}`);

      // Log memory usage
      const memUsage = process.memoryUsage();
      this.logger.error(`BrowserManager: Memory usage - RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap Used: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB, Heap Total: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);

      // Log environment variables related to Puppeteer
      this.logger.error(`BrowserManager: PUPPETEER_EXECUTABLE_PATH: ${process.env.PUPPETEER_EXECUTABLE_PATH}`);
      this.logger.error(`BrowserManager: PUPPETEER_PROTOCOL_TIMEOUT: ${process.env.PUPPETEER_PROTOCOL_TIMEOUT}`);
      this.logger.error(`BrowserManager: DISPLAY: ${process.env.DISPLAY}`);
      this.logger.error(`BrowserManager: DOCKER_ENV: ${process.env.DOCKER_ENV}`);

      this.logger.error('BrowserManager: === END CRASH DETAILS ===');
    } catch (error) {
      this.logger.error(`BrowserManager: Error logging crash details: ${error}`);
    }
  }

  /**
   * Log browser process exit details
   * @param code Exit code
   * @param signal Exit signal
   */
  private logBrowserProcessExit(code: number | null, signal: string | null): void {
    try {
      this.logger.error('BrowserManager: === BROWSER PROCESS EXIT ===');
      this.logger.error(`BrowserManager: Exit code: ${code}`);
      this.logger.error(`BrowserManager: Exit signal: ${signal}`);

      // Interpret common exit codes
      if (code === 1) {
        this.logger.error('BrowserManager: Exit code 1 - General error (possible crash or out of memory)');
      } else if (code === 139) {
        this.logger.error('BrowserManager: Exit code 139 - Segmentation fault (SIGSEGV)');
      } else if (code === 137) {
        this.logger.error('BrowserManager: Exit code 137 - Process killed by SIGKILL (likely OOM killer)');
      } else if (signal === 'SIGKILL') {
        this.logger.error('BrowserManager: SIGKILL signal - Process was forcefully terminated (likely by OOM killer)');
      } else if (signal === 'SIGSEGV') {
        this.logger.error('BrowserManager: SIGSEGV signal - Segmentation fault');
      }

      this.logger.error('BrowserManager: === END PROCESS EXIT DETAILS ===');
    } catch (error) {
      this.logger.error(`BrowserManager: Error logging process exit details: ${error}`);
    }
  }

  /**
   * Start monitoring browser process for memory and health (optimized for lower CPU usage)
   */
  private startBrowserProcessMonitoring(): void {
    if (!this.browser || !this.browser.process()) {
      return;
    }

    // Monitor every 60 seconds (reduced from 30 seconds to lower CPU usage)
    const monitorInterval = setInterval(() => {
      try {
        if (!this.browser || !this.browser.connected || !this.browser.process()) {
          clearInterval(monitorInterval);
          return;
        }

        const browserProcess = this.browser.process();
        if (browserProcess && !browserProcess.killed) {
          // Only log if there are issues to reduce log noise
          const memUsage = process.memoryUsage();
          const memUsageMB = Math.round(memUsage.rss / 1024 / 1024);

          if (memUsageMB > 1500) { // Only log if memory usage is very high (>1.5GB)
            this.logger.warn(`BrowserManager: High Node.js memory usage detected - RSS: ${memUsageMB}MB`);
          }
        } else {
          this.logger.warn('BrowserManager: Browser process monitoring detected killed process');
          clearInterval(monitorInterval);
        }
      } catch (error) {
        this.logger.warn(`BrowserManager: Error in process monitoring: ${error}`);
        clearInterval(monitorInterval);
      }
    }, 60000); // 60 seconds (increased from 30 seconds)

    // Clear interval after 5 minutes to prevent memory leaks (reduced from 10 minutes)
    setTimeout(() => {
      clearInterval(monitorInterval);
      this.logger.debug('BrowserManager: Stopped browser process monitoring after 5 minutes');
    }, 300000); // 5 minutes
  }

  /**
   * Log detailed page crash information
   * @param page The page that crashed
   * @param error The error that occurred
   */
  private logPageCrashDetails(page: Page, error: Error): void {
    try {
      this.logger.error('BrowserManager: === PAGE CRASH DETECTED ===');
      this.logger.error(`BrowserManager: Page URL: ${page.url()}`);

      // Get page title asynchronously without blocking
      page.title().then((title: any) => {
        this.logger.error(`BrowserManager: Page title: ${title}`);
      }).catch(() => {
        this.logger.error('BrowserManager: Page title: Unable to get title');
      });

      this.logger.error(`BrowserManager: Page closed: ${page.isClosed()}`);
      this.logger.error(`BrowserManager: Error message: ${error.message}`);
      this.logger.error(`BrowserManager: Error name: ${error.name}`);

      // Check browser connection
      const browser = page.browser();
      if (browser) {
        this.logger.error(`BrowserManager: Browser connected: ${browser.connected}`);
        const process = browser.process();
        if (process) {
          this.logger.error(`BrowserManager: Browser process PID: ${process.pid}`);
          this.logger.error(`BrowserManager: Browser process killed: ${process.killed}`);
        }
      }

      this.logger.error('BrowserManager: === END PAGE CRASH DETAILS ===');
    } catch (logError) {
      this.logger.error(`BrowserManager: Error logging page crash details: ${logError}`);
    }
  }

  /**
   * Configure rebrowser-patches environment variables for optimal stealth
   * These settings control the behavior of the stealth patches
   */
  private configureStealthEnvironment(): void {
    // Configure Runtime.Enable fix mode (default: addBinding)
    // Options: addBinding, alwaysIsolated, enableDisable, 0 (disabled)
    if (!process.env.REBROWSER_PATCHES_RUNTIME_FIX_MODE) {
      process.env.REBROWSER_PATCHES_RUNTIME_FIX_MODE = 'addBinding';
    }

    // Configure source URL to generic script name
    if (!process.env.REBROWSER_PATCHES_SOURCE_URL) {
      process.env.REBROWSER_PATCHES_SOURCE_URL = 'app.js';
    }

    // Configure utility world name
    if (!process.env.REBROWSER_PATCHES_UTILITY_WORLD_NAME) {
      process.env.REBROWSER_PATCHES_UTILITY_WORLD_NAME = 'util';
    }

    // Enable debug mode if needed (uncomment for debugging)
    // process.env.REBROWSER_PATCHES_DEBUG = '1';

    this.logger.debug('BrowserManager: Configured rebrowser-patches environment variables for stealth mode');
  }

  /**
   * Get proxy arguments for browser launch
   * @param proxy Proxy configuration
   * @returns Array of proxy arguments
   */
  private getProxyArgs(proxy: any): string[] {
    const proxyArgs: string[] = [];

    if (!proxy || !proxy.server) {
      return proxyArgs;
    }

    // Add proxy server
    proxyArgs.push(`--proxy-server=${proxy.server}`);

    // Add proxy bypass list if provided
    if (proxy.bypass) {
      proxyArgs.push(`--proxy-bypass-list=${proxy.bypass}`);
    }

    return proxyArgs;
  }
}
