/**
 * Screenshot Provider Interface
 * Defines the contract for platform-specific screenshot providers
 */

/**
 * Screenshot Provider Interface
 * Handles taking and processing screenshots during test execution
 */
export interface IScreenshotProvider {
  /**
   * Take a screenshot of the target (page, device, etc.)
   * @param target The target to take a screenshot of (Puppeteer Page, WebdriverIO Browser, etc.)
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @param stepIndex Step index for queue ordering (optional)
   * @returns Promise resolving to the URL of the stored screenshot
   */
  takeScreenshot(target: any, testId: string, stepId: string, type: 'before' | 'after', stepIndex?: number): Promise<string>;

  /**
   * Process a screenshot buffer and store it
   * @param buffer Screenshot buffer
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @returns Promise resolving to the URL of the stored screenshot
   */
  processScreenshot(buffer: Buffer, testId: string, stepId: string, type: 'before' | 'after'): Promise<string>;

  /**
   * Initialize the screenshot provider with configuration
   * @param config Configuration object
   */
  initialize(config: any): Promise<void>;

  /**
   * Get the path where a screenshot would be stored
   * @param testId Test identifier
   * @param stepId Step identifier
   * @param type Type of screenshot (before or after step execution)
   * @returns Path to the screenshot
   */
  getScreenshotPath(testId: string, stepId: string, type: 'before' | 'after'): string;
}
