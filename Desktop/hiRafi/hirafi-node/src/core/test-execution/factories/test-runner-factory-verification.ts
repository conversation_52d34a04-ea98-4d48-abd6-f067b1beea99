/**
 * TestRunnerFactory DI Integration Verification
 * 
 * This script verifies that the TestRunnerFactory correctly integrates with
 * the main ApplicationContainer instead of creating isolated DI containers.
 */

import { ApplicationContainer } from '../../common/di/application-container.js';
import { TestRunnerFactory } from './test-runner-factory.js';
import { LoggerServiceFactory } from '../../../utils/logger-service.js';

/**
 * Verify that TestRunner dependencies are properly registered in ApplicationContainer
 */
async function verifyTestRunnerDIIntegration(): Promise<void> {
  const logger = LoggerServiceFactory.createServiceLogger('TestRunnerFactoryVerification');
  
  try {
    logger.info('🔍 Starting TestRunnerFactory DI integration verification...');

    // Initialize ApplicationContainer
    const appContainer = new ApplicationContainer({
      enableLogging: true,
      loggerServiceName: 'TestRunnerVerification'
    });
    
    await appContainer.initialize();
    logger.info('✅ ApplicationContainer initialized successfully');

    // Verify TestRunner service factories are registered
    const serviceFactories = await appContainer.getTestRunnerServiceFactories();
    logger.info('✅ TestRunner service factories retrieved from main DI container');

    // CRITICAL FIX: Get artifactStorage from DI container for factory calls
    const artifactStorage = await appContainer.getStorageProvider();
    logger.info('✅ ArtifactStorage resolved from DI container');

    // Create mock execution coordinator for factory calls
    const mockExecutionCoordinator = { 
      emit: () => {},
      on: () => {},
      off: () => {}
    };

    // Verify each factory exists
    const requiredFactories = [
      'screenshotProviderFactory',
      'videoRecorderFactory', 
      'metricsCollectorFactory',
      'stepHandlerRegistries',
      'platformManagerFactory',
      'deviceManagerFactory'
    ];

    for (const factoryName of requiredFactories) {
      if ((serviceFactories as any)[factoryName]) {
        logger.info(`✅ ${factoryName} factory is registered`);
      } else {
        throw new Error(`❌ ${factoryName} factory is missing`);
      }
    }

    // Test creating platform-specific instances
    const testConfig = {
      android: {
        deviceProvider: 'sauceLabs',
        sauceLabs: {
          username: 'test',
          accessKey: 'test'
        }
      }
    };

    // Test Android platform manager creation
    const androidPlatformManager = await (serviceFactories.platformManagerFactory as any).createForPlatform('android', testConfig);
    logger.info('✅ Android platform manager created successfully');

    // Test Web platform manager creation
    const webPlatformManager = await (serviceFactories.platformManagerFactory as any).createForPlatform('web', testConfig);
    logger.info('✅ Web platform manager created successfully');

    // Test screenshot provider creation
    const androidScreenshotProvider = await (serviceFactories.screenshotProviderFactory as any).createForPlatform('android', testConfig, mockExecutionCoordinator, artifactStorage);
    logger.info('✅ Android screenshot provider created successfully');

    const webScreenshotProvider = await (serviceFactories.screenshotProviderFactory as any).createForPlatform('web', testConfig, mockExecutionCoordinator, artifactStorage);
    logger.info('✅ Web screenshot provider created successfully');

    // Test metrics collector creation
    const androidMetricsCollector = await (serviceFactories.metricsCollectorFactory as any).createForPlatform('android', testConfig);
    logger.info('✅ Android metrics collector created successfully');

    const webMetricsCollector = await (serviceFactories.metricsCollectorFactory as any).createForPlatform('web', testConfig);
    logger.info('✅ Web metrics collector created successfully');

    // Verify step handler registries
    if ((serviceFactories.stepHandlerRegistries as any).android && (serviceFactories.stepHandlerRegistries as any).web) {
      logger.info('✅ Step handler registries for both platforms are available');
    } else {
      throw new Error('❌ Step handler registries are missing');
    }

    // Test pure factory method
    const videoRecorder = await (serviceFactories.videoRecorderFactory as any).createForPlatform('android', testConfig, mockExecutionCoordinator, artifactStorage);
    logger.info('✅ Video recorder created successfully');

    // Test pure TestRunnerFactory method
    const testRunner = TestRunnerFactory.createTestRunnerWithDependencies({
      platformManager: androidPlatformManager,
      screenshotProvider: androidScreenshotProvider,
      videoRecorder: videoRecorder,
      metricsCollector: androidMetricsCollector,
      stepHandlerRegistry: (serviceFactories.stepHandlerRegistries as any).android
    }, testConfig);

    logger.info('✅ TestRunner created successfully using pure factory method');

    // Cleanup
    await appContainer.dispose();
    logger.info('✅ ApplicationContainer disposed successfully');

    logger.info('🎉 TestRunnerFactory DI integration verification completed successfully!');
    logger.info('🔧 The "DI island" issue has been resolved - all dependencies now come from the main ApplicationContainer');

  } catch (error: any) {
    logger.error(`❌ TestRunnerFactory DI integration verification failed: ${error.message}`);
    throw error;
  }
}

/**
 * Run verification if this file is executed directly
 */
if (require.main === module) {
  verifyTestRunnerDIIntegration()
    .then(() => {
      console.log('✅ Verification completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Verification failed:', error);
      process.exit(1);
    });
}

export { verifyTestRunnerDIIntegration };
