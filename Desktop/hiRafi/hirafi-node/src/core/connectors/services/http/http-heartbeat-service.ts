/**
 * HTTP Heartbeat Service
 * 
 * Handles HTTP-based heartbeat functionality with dependency injection.
 * Replaces the singleton HttpHeartbeatService with a proper service implementation.
 */

import { BaseService } from '../base/base-service.js';
import { IHttpHeartbeatService } from '../../interfaces/http-connector.interface.js';
import { IHeartbeatService } from '../../interfaces/heartbeat-service.interface.js';
import { IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';

/**
 * HTTP Heartbeat Service Class
 * Implements HTTP-specific heartbeat functionality with dependency injection
 */
export class HttpHeartbeatService extends BaseService implements IHttpHeartbeatService {
  private hubUrl: string = '';
  private nodeSecretKey: string = '';
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private defaultInterval = 30000; // 30 seconds

  constructor(
    logger: any,
    config: IServiceConfig,
    private heartbeatService: IHeartbeatService
  ) {
    super('HttpHeartbeatService', logger, config);
    this.defaultInterval = config.heartbeatInterval || 30000;
  }

  /**
   * Initialize the service
   * @param config Service configuration
   */
  protected async doInitialize(config: IServiceConfig): Promise<void> {
    this.validateConfig(config, ['hubUrl', 'secretKey']);
    
    this.hubUrl = config.hubUrl!;
    this.nodeSecretKey = config.secretKey!;
    
    this.logger.debug('HttpHeartbeatService: Initialized with hub URL:', this.hubUrl);
  }

  /**
   * Close the service
   */
  protected async doClose(): Promise<void> {
    this.stopHeartbeat();
    this.hubUrl = '';
    this.nodeSecretKey = '';
    this.logger.debug('HttpHeartbeatService: Closed successfully');
  }

  /**
   * Initialize the service (backward compatibility)
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  async initialize(config?: IServiceConfig): Promise<void>;
  async initialize(hubUrl: string, nodeSecretKey: string): Promise<void>;
  async initialize(configOrHubUrl?: IServiceConfig | string, nodeSecretKey?: string): Promise<void> {
    if (typeof configOrHubUrl === 'string') {
      // Legacy signature
      this.hubUrl = configOrHubUrl;
      this.nodeSecretKey = nodeSecretKey!;
      await super.initialize({ hubUrl: this.hubUrl, secretKey: this.nodeSecretKey });
    } else {
      // New signature
      await super.initialize(configOrHubUrl);
    }
  }

  /**
   * Initialize the service (backward compatibility)
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  async initializeLegacy(hubUrl: string, nodeSecretKey: string): Promise<void> {
    await super.initialize({
      hubUrl,
      secretKey: nodeSecretKey
    });
  }

  /**
   * Send heartbeat
   * @param nodeId Node ID
   */
  async sendHeartbeat(nodeId: string): Promise<boolean> {
    try {
      const heartbeatData = this.heartbeatService.prepareHeartbeatData(
        nodeId,
        'available' // Default status for HTTP heartbeat
      );

      this.logOperation('httpHeartbeatRequest', {
        url: `${this.hubUrl}/api/nodes/${nodeId}/heartbeat`,
        nodeId
      });

      const response = await fetch(`${this.hubUrl}/api/nodes/${nodeId}/heartbeat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.nodeSecretKey
        },
        body: JSON.stringify(heartbeatData)
      });

      const success = response.ok;
      
      if (success) {
        this.logOperation('httpHeartbeatSuccess', { nodeId });
      } else {
        this.handleError(new Error(`HTTP ${response.status}: ${response.statusText}`), 'sendHeartbeat');
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendHeartbeat');
      return false;
    }
  }

  /**
   * Start heartbeat interval
   * @param nodeId Node ID
   */
  startHeartbeat(nodeId: string): void {
    // Stop existing heartbeat
    this.stopHeartbeat();

    const heartbeatFunction = async () => {
      return await this.sendHeartbeat(nodeId);
    };

    // Use the injected heartbeat service to manage the interval
    this.heartbeatService.startHeartbeat(
      `http-${nodeId}`,
      heartbeatFunction,
      this.defaultInterval
    );

    this.logOperation('httpHeartbeatStarted', { 
      nodeId, 
      interval: this.defaultInterval 
    });

    this.logger.info(`HttpHeartbeatService: Started heartbeat for node ${nodeId} with interval ${this.defaultInterval}ms`);
  }

  /**
   * Stop heartbeat interval
   */
  stopHeartbeat(): void {
    // The heartbeat service will handle stopping all heartbeats for this service
    // We could be more specific here if we tracked the nodeId
    const activeHeartbeats = this.heartbeatService.getActiveHeartbeats();
    const httpHeartbeats = activeHeartbeats.filter(id => id.startsWith('http-'));
    
    httpHeartbeats.forEach(heartbeatId => {
      this.heartbeatService.stopHeartbeat(heartbeatId);
    });

    this.logOperation('httpHeartbeatStopped', { 
      stoppedCount: httpHeartbeats.length 
    });

    this.logger.info('HttpHeartbeatService: Stopped heartbeat');
  }

  /**
   * Close the service
   */
  async close(): Promise<void> {
    // HTTP heartbeat service doesn't need special cleanup
    this.hubUrl = '';
    this.nodeSecretKey = '';
  }

  /**
   * Get service statistics
   */
  getStats(): IServiceStats {
    return super.getStats();
  }

  /**
   * Get detailed HTTP heartbeat statistics
   */
  getDetailedStats(): Record<string, any> {
    const baseStats = super.getStats();
    const heartbeatStats = this.heartbeatService.getStats();
    
    // Filter heartbeat stats for HTTP-specific heartbeats
    const httpHeartbeatStats = Object.entries(heartbeatStats.connectorStats || {})
      .filter(([key]) => key.startsWith('http-'))
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {} as Record<string, any>);

    return {
      ...baseStats,
      hubUrl: this.hubUrl,
      initialized: !!this.hubUrl,
      defaultInterval: this.defaultInterval,
      httpHeartbeats: httpHeartbeatStats,
      heartbeatServiceStats: heartbeatStats
    };
  }
}
