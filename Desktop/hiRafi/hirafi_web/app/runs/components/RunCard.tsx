import React from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Eye, Edit, Trash2, Play, Square, MoreHorizontal,
  LoaderCircle, CheckCircle, X, Clock, Layers,
  Settings2, CalendarClock, Camera, Video, LineChart,
  Network, Database, XCircle, Smartphone, Globe
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { RunStatusBadge } from '@/lib/utils/run-ui';
import { canStartRun, canStopRun } from '@/lib/utils/run-logic';
import { useRunUpdating, useRunSimulatedState } from '@/store/runStore';

export interface RunCardProps {
  run: any;
  onStart: (runId: string) => void;
  onStop: (runId: string) => void;
  onDelete: (runId: string) => void;
  className?: string;
}

export function RunCard({ run, onStart, onStop, onDelete, className = "" }: RunCardProps) {
  const router = useRouter();
  const isUpdating = useRunUpdating(run.id);
  const simulatedState = useRunSimulatedState(run.id);

  const handleStart = () => onStart(run.id);
  const handleStop = () => onStop(run.id);
  const handleDelete = () => onDelete(run.id);
  const handleViewDetails = () => router.push(`/runs/${run.id}`);
  const handleEdit = () => router.push(`/runs/edit/${run.id}`);



  // Calculate running time for active runs
  const getRunningTime = () => {
    if (!run.startedAt || (run.status !== "running" && simulatedState !== "running")) return null;

    const startTime = new Date(run.startedAt).getTime();
    const currentTime = Date.now();
    const diffMs = currentTime - startTime;

    const minutes = Math.floor(diffMs / 60000);
    const seconds = Math.floor((diffMs % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const runningTime = getRunningTime();

  return (
    <div
      className={`
        bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 rounded-lg shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md hover:border-indigo-200 dark:hover:border-indigo-800 transform hover:translate-y-[-2px]
        ${simulatedState === "running" ? "ring-2 ring-blue-400 dark:ring-blue-600 ring-offset-2 ring-offset-white dark:ring-offset-gray-900 glow-animation" :
          simulatedState === "passed" ? "ring-1 ring-emerald-400 dark:ring-emerald-600" :
          simulatedState === "failed" ? "ring-1 ring-red-400 dark:ring-red-600" :
          simulatedState === "stopped" ? "ring-1 ring-amber-400 dark:ring-amber-600" : ""}
        ${className}
      `}
    >


      <div className="flex flex-col md:flex-row relative z-10">
        {/* Status indicator */}
        <div className={`
          w-2 h-full hidden md:block
          ${simulatedState === "queue" ? "bg-yellow-400" :
            simulatedState === "running" ? "bg-blue-500 animate-pulse" :
            simulatedState === "passed" ? "bg-emerald-500" :
            simulatedState === "failed" ? "bg-red-500" :
            run.status === "running" ? "bg-blue-500" :
            run.status === "completed" ? "bg-emerald-500" :
            run.status === "failed" ? "bg-red-500" :
            run.status === "partial" ? "bg-yellow-400" :
            "bg-amber-400"}
        `}></div>

        <div className="flex-1 p-0">
          <div className="flex flex-col md:flex-row">
            {/* Main info */}
            <div className="flex-1 p-5">
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <RunStatusBadge
                    status={run.status}
                    simulatedStatus={simulatedState}
                  />

                  {/* Platform badge */}
                  <Badge
                    variant="outline"
                    className={`ml-2 ${
                      run.platform === "web"
                        ? "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800"
                        : run.platform === "android"
                        ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800"
                        : "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900 dark:text-gray-400 dark:border-gray-800"
                    }`}
                  >
                    {run.platform === "web" ? (
                      <>
                        <Globe className="h-3 w-3 mr-1" />
                        Web
                      </>
                    ) : run.platform === "android" ? (
                      <>
                        <Smartphone className="h-3 w-3 mr-1" />
                        Android
                      </>
                    ) : (
                      run.platform || "Unknown"
                    )}
                  </Badge>

                  {/* Run name with click handler */}
                  <div
                    className="ml-2 cursor-pointer flex items-center"
                    onClick={handleViewDetails}
                  >
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
                      {run.name}
                    </h3>
                  </div>
                </div>

                {/* Test Status Icons - Top Right */}
                {run.testResults && (
                  <div className="flex items-center gap-2">
                    {run.testResults.completed > 0 && (
                      <div className="flex items-center text-emerald-600 dark:text-emerald-400">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        <span className="text-sm font-medium">{run.testResults.completed}</span>
                      </div>
                    )}

                    {run.testResults.failed > 0 && (
                      <div className="flex items-center text-red-600 dark:text-red-400">
                        <X className="h-4 w-4 mr-1" />
                        <span className="text-sm font-medium">{run.testResults.failed}</span>
                      </div>
                    )}

                    {run.testResults.stopped > 0 && (
                      <div className="flex items-center text-gray-600 dark:text-gray-400">
                        <XCircle className="h-4 w-4 mr-1" />
                        <span className="text-sm font-medium">{run.testResults.stopped}</span>
                      </div>
                    )}

                    {run.testResults.running > 0 && (
                      <div className="flex items-center text-blue-600 dark:text-blue-400">
                        <LoaderCircle className="h-4 w-4 mr-1 animate-spin" />
                        <span className="text-sm font-medium">{run.testResults.running}</span>
                      </div>
                    )}

                    {run.testResults.queued > 0 && (
                      <div className="flex items-center text-amber-600 dark:text-amber-400">
                        <Clock className="h-4 w-4 mr-1" />
                        <span className="text-sm font-medium">{run.testResults.queued}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Scenario metrics */}
              <div className="mt-4">
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-1.5">
                  <div className="flex items-center gap-2">
                    <span>Scenarios: {run.scenarioIds?.length || 0}</span>
                    <span>•</span>
                    <span>Created {formatDistanceToNow(new Date(run.createdAt), { addSuffix: true })}</span>
                    {run.lastRunDate && (
                      <>
                        <span>•</span>
                        <span>Last run {formatDistanceToNow(new Date(run.lastRunDate), { addSuffix: true })}</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Compact Test Status for Running Tests */}
                {run.testResults && (run.status === "running" || simulatedState === "running") && (
                  <div className="mt-3 p-2.5 bg-blue-50/80 dark:bg-blue-950/20 border border-blue-200/60 dark:border-blue-800/60 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {/* Running count with prominent display */}
                        {run.testResults.running > 0 && (
                          <div className="flex items-center text-blue-700 dark:text-blue-300 font-semibold">
                            <LoaderCircle className="h-4 w-4 mr-1.5 animate-spin text-blue-600" />
                            <span className="text-sm">{run.testResults.running} running</span>
                          </div>
                        )}

                        {/* Other counts in compact format */}
                        <div className="flex items-center gap-2 text-xs">
                          {run.testResults.completed > 0 && (
                            <span className="text-emerald-600 dark:text-emerald-400 font-medium">
                              {run.testResults.completed} done
                            </span>
                          )}
                          {run.testResults.failed > 0 && (
                            <span className="text-red-600 dark:text-red-400 font-medium">
                              {run.testResults.failed} failed
                            </span>
                          )}
                          {run.testResults.queued > 0 && (
                            <span className="text-amber-600 dark:text-amber-400 font-medium">
                              {run.testResults.queued} queued
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Running time and total */}
                      <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
                        {runningTime && (
                          <span className="bg-blue-100 dark:bg-blue-900/50 px-2 py-0.5 rounded font-medium">
                            {runningTime}
                          </span>
                        )}
                        <span className="text-gray-500 dark:text-gray-400">
                          {run.testResults.total} total
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Tags */}
              {run.tags && run.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3">
                  {run.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Report Features */}
              <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-800">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-xs font-medium text-gray-600 dark:text-gray-400 flex items-center">
                    <LineChart className="h-3.5 w-3.5 mr-1.5 text-indigo-500" />
                    Report Features
                  </h4>
                </div>

                <div className="flex flex-wrap items-center gap-2">
                  {/* Screenshots */}
                  <div className={`flex items-center rounded-full px-2 py-0.5 text-xs ${
                    run.reportSettings?.takeScreenshots 
                      ? "bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60"
                  }`}>
                    <Camera className="h-3 w-3 mr-1" />
                    {run.reportSettings?.takeScreenshots ? "Screenshots" : <s>Screenshots</s>}
                  </div>

                  {/* Videos */}
                  <div className={`flex items-center rounded-full px-2 py-0.5 text-xs ${
                    run.reportSettings?.takeVideos 
                      ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60"
                  }`}>
                    <Video className="h-3 w-3 mr-1" />
                    {run.reportSettings?.takeVideos ? "Videos" : <s>Videos</s>}
                  </div>

                  {/* Metrics */}
                  <div className={`flex items-center rounded-full px-2 py-0.5 text-xs ${
                    run.reportSettings?.pageMetrics 
                      ? "bg-emerald-50 dark:bg-emerald-900/20 text-emerald-600 dark:text-emerald-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60"
                  }`}>
                    <LineChart className="h-3 w-3 mr-1" />
                    {run.reportSettings?.pageMetrics ? "Metrics" : <s>Metrics</s>}
                  </div>

                  {/* Network */}
                  <div className={`flex items-center rounded-full px-2 py-0.5 text-xs ${
                    run.reportSettings?.networkData 
                      ? "bg-amber-50 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60"
                  }`}>
                    <Network className="h-3 w-3 mr-1" />
                    {run.reportSettings?.networkData ? "Network" : <s>Network</s>}
                  </div>
                </div>
              </div>
            </div>

            {/* Details and actions */}
            <div className="border-t md:border-t-0 md:border-l border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-900/50 p-5 flex flex-col w-full md:w-64">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Layers className="h-3.5 w-3.5 text-indigo-500 mr-1.5" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Scenarios</span>
                  </div>
                  <span className="text-xs font-medium">{run.scenarioIds?.length || 0}</span>
                </div>

                {/* Platform-specific environment info */}
                {run.platform === "web" ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Settings2 className="h-3.5 w-3.5 text-blue-500 mr-1.5" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Browser</span>
                    </div>
                    <span className="text-xs font-medium capitalize">{run.environment?.browser || "Chrome"}</span>
                  </div>
                ) : run.platform === "android" ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Smartphone className="h-3.5 w-3.5 text-green-500 mr-1.5" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Device Provider</span>
                    </div>
                    <span className="text-xs font-medium capitalize">
                      {run.deviceProvider === 'sauceLabs' ? 'SauceLabs' :
                       run.deviceProvider === 'testinium' ? 'Testinium' :
                       run.deviceProvider || 'SauceLabs'}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Settings2 className="h-3.5 w-3.5 text-gray-500 mr-1.5" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Platform</span>
                    </div>
                    <span className="text-xs font-medium capitalize">{run.platform || "Unknown"}</span>
                  </div>
                )}

                {/* Android device count */}
                {run.platform === "android" && run.environment?.sauceLabs?.selectedDevices?.length > 0 && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Smartphone className="h-3.5 w-3.5 text-green-500 mr-1.5" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Devices</span>
                    </div>
                    <span className="text-xs font-medium">
                      {run.environment.sauceLabs.selectedDevices.length} device{run.environment.sauceLabs.selectedDevices.length > 1 ? 's' : ''}
                    </span>
                  </div>
                )}

                {/* Web viewport info */}
                {run.platform === "web" && run.environment?.viewport && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Globe className="h-3.5 w-3.5 text-blue-500 mr-1.5" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Viewport</span>
                    </div>
                    <span className="text-xs font-medium">
                      {run.environment.viewport.width}×{run.environment.viewport.height}
                    </span>
                  </div>
                )}

                {/* Test Data Environment */}
                {run.testDataEnvironmentName && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Database className="h-3.5 w-3.5 text-green-500 mr-1.5" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Test Data Env</span>
                    </div>
                    <span className="text-xs font-medium text-green-600 dark:text-green-400">
                      {run.testDataEnvironmentName}
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <CalendarClock className="h-3.5 w-3.5 text-indigo-500 mr-1.5" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">Created</span>
                  </div>
                  <span className="text-xs font-medium">
                    {format(new Date(run.createdAt), 'MMM d, yyyy')}
                  </span>
                </div>

                {/* Last Run Date */}
                {run.lastRunDate && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <CalendarClock className="h-3.5 w-3.5 text-green-500 mr-1.5" />
                      <span className="text-xs text-gray-600 dark:text-gray-400">Last Run</span>
                    </div>
                    <span className="text-xs font-medium text-green-600 dark:text-green-400">
                      {format(new Date(run.lastRunDate), 'MMM d, yyyy')}
                    </span>
                  </div>
                )}

                <div className="flex gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 h-8 border-indigo-200 dark:border-indigo-800 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-all duration-200"
                    onClick={handleViewDetails}
                  >
                    <Eye className="h-3.5 w-3.5 mr-1" />
                    Details
                  </Button>

                  {/* Run control buttons */}
                  {canStopRun(run) || simulatedState === "running" ? (
                    <Button
                      size="sm"
                      className="flex-1 h-8 bg-red-600 hover:bg-red-700 text-white transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                      onClick={handleStop}
                      disabled={isUpdating}
                    >
                      <Square className="h-3.5 w-3.5 mr-1" />
                      Stop
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      className="flex-1 h-8 bg-gradient-to-r from-indigo-600 to-blue-600 text-white hover:from-indigo-700 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                      onClick={handleStart}
                      disabled={isUpdating || !canStartRun(run)}
                    >
                      {isUpdating ? (
                        <>
                          <LoaderCircle className="h-3.5 w-3.5 mr-1 animate-spin" />
                          Starting...
                        </>
                      ) : (
                        <>
                          <Play className="h-3.5 w-3.5 mr-1" />
                          Start
                        </>
                      )}
                    </Button>
                  )}
                </div>

                {/* Three dots menu */}
                <div className="mt-3 flex justify-center">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800">
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="center">
                      <DropdownMenuItem
                        onClick={handleViewDetails}
                        className="flex items-center cursor-pointer"
                      >
                        <Eye className="h-4 w-4 mr-2 text-gray-500" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={handleEdit}
                        className="flex items-center cursor-pointer"
                      >
                        <Edit className="h-4 w-4 mr-2 text-gray-500" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={handleDelete}
                        className="flex items-center cursor-pointer text-red-600 dark:text-red-400 focus:text-red-700 dark:focus:text-red-300"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
