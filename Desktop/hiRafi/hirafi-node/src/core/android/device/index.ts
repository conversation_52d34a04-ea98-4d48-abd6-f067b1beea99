/**
 * Device Manager Module Exports
 * Centralized exports for device management functionality
 */

// Core interfaces
export * from './device-manager.interface.js';
export * from './interfaces/device-provider-registry.interface.js';

// Base classes
export * from './base-device-manager.js';

// Device manager implementations
export * from './sauce-labs-device-manager.js';
export * from './testinium-device-manager.js';

// Factory and services
export * from './device-manager-factory.js';
export * from './services/device-provider-config.service.js';
export * from './services/device-provider-registry.service.js';

// Utilities
export * from './shared/device-manager-utils.js';

// Legacy exports for backward compatibility
export { createDeviceManager } from './device-manager-factory.js';
