/**
 * Redis Configuration Domain
 * Handles Redis connection and configuration settings
 */

import { getEnv, getEnvBoolean, getEnvInt } from '../utils/env-utils.js';
import { RedisConfigDomain } from '../types/config-types.js';

/**
 * Creates Redis configuration from environment variables
 * @returns Redis configuration object
 */
export function createRedisConfig(): RedisConfigDomain {
  const config: RedisConfigDomain = {
    enabled: getEnvBoolean('REDIS_ENABLED', true),
    host: getEnv('REDIS_HOST', 'localhost'),
    port: getEnvInt('REDIS_PORT', 6379),
    password: getEnv('REDIS_PASSWORD', ''),
    username: getEnv('REDIS_USERNAME', ''),
    db: getEnvInt('REDIS_DB', 0),
    prefix: getEnv('REDIS_PREFIX', ''),
    url: ''
  };

  // Build Redis URL with proper encoding
  config.url = buildRedisUrl(config);

  return config;
}

/**
 * Builds Redis URL from configuration
 * @param config Redis configuration
 * @returns Redis URL string
 */
export function buildRedisUrl(config: Omit<RedisConfigDomain, 'url'>): string {
  // Use REDIS_URL if provided directly
  if (process.env.REDIS_URL) {
    return process.env.REDIS_URL;
  }

  // Build URL from components
  const auth = config.username || config.password 
    ? `${config.username}:${encodeURIComponent(config.password)}@`
    : '';
  
  return `redis://${auth}${config.host}:${config.port}`;
}

/**
 * Validates Redis configuration
 * @param config Redis configuration to validate
 * @returns Validation result
 */
export function validateRedisConfig(config: RedisConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.enabled) {
    return { isValid: true, errors: [] }; // Skip validation if disabled
  }

  // Validate host
  if (!config.host || config.host.trim() === '') {
    errors.push('Redis host is required when Redis is enabled');
  }

  // Validate port range
  if (config.port < 1 || config.port > 65535) {
    errors.push('Redis port must be between 1 and 65535');
  }

  // Validate database number
  if (config.db < 0 || config.db > 15) {
    errors.push('Redis database number must be between 0 and 15');
  }

  // Validate URL format if provided
  if (config.url) {
    try {
      const url = new URL(config.url);
      if (url.protocol !== 'redis:' && url.protocol !== 'rediss:') {
        errors.push('Redis URL must use redis:// or rediss:// protocol');
      }
    } catch {
      errors.push('Redis URL format is invalid');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets default Redis configuration
 * @returns Default Redis configuration
 */
export function getDefaultRedisConfig(): RedisConfigDomain {
  const config = {
    enabled: true,
    host: 'localhost',
    port: 6379,
    password: '',
    username: '',
    db: 0,
    prefix: '',
    url: ''
  };

  config.url = buildRedisUrl(config);
  return config;
}

/**
 * Merges Redis configuration with overrides
 * @param baseConfig Base configuration
 * @param overrides Configuration overrides
 * @returns Merged configuration
 */
export function mergeRedisConfig(
  baseConfig: RedisConfigDomain,
  overrides: Partial<RedisConfigDomain>
): RedisConfigDomain {
  const merged = {
    ...baseConfig,
    ...overrides
  };

  // Rebuild URL if connection parameters changed
  if (overrides.host || overrides.port || overrides.username || overrides.password) {
    merged.url = buildRedisUrl(merged);
  }

  return merged;
}

/**
 * Tests Redis connection configuration
 * @param config Redis configuration
 * @returns Promise that resolves if connection is successful
 */
export async function testRedisConnection(config: RedisConfigDomain): Promise<boolean> {
  if (!config.enabled) {
    return true; // Consider disabled as "successful"
  }

  try {
    // This would require Redis client, but we'll just validate the config for now
    // In a real implementation, you would create a Redis client and test the connection
    const validation = validateRedisConfig(config);
    return validation.isValid;
  } catch (error) {
    return false;
  }
}

/**
 * Parses Redis URL into configuration components
 * @param url Redis URL string
 * @returns Partial Redis configuration
 */
export function parseRedisUrl(url: string): Partial<RedisConfigDomain> {
  try {
    const parsed = new URL(url);
    
    return {
      host: parsed.hostname,
      port: parsed.port ? parseInt(parsed.port, 10) : 6379,
      username: parsed.username || '',
      password: parsed.password || '',
      url
    };
  } catch (error) {
    throw new Error(`Invalid Redis URL: ${error}`);
  }
}
