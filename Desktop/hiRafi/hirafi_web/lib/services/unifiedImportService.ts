/**
 * Unified Import Service
 * 
 * Handles test case import and AI step generation for multiple test management providers
 */

import { TestManagementProvider } from '@/store/testManagementStore'
import { pluginApi } from '@/lib/api'
import { ScenarioCreationService } from './scenarioCreationService'

export interface ImportRequest {
  caseId: string
  provider: TestManagementProvider
  scenarioName?: string
  scenarioDescription?: string
  testDataVariables?: Array<{
    name: string
    type: string
    description?: string
  }>
  creativityLevel?: number
}

export interface ImportResponse {
  success: boolean
  data?: {
    prompt: string
    caseDetails: any
    steps?: any[]
    name?: string
    description?: string
  }
  error?: string
}

export interface CaseDetails {
  id: string
  title?: string
  name?: string
  description?: string
  steps?: string
  expectedResults?: string
  objective?: string
  precondition?: string
  provider: TestManagementProvider
  [key: string]: any
}

/**
 * Unified Import Service Class
 */
export class UnifiedImportService {
  /**
   * Import test case details from any supported provider
   */
  static async importTestCase(request: ImportRequest): Promise<ImportResponse> {
    try {
      // Fetch case details from the appropriate provider
      const caseDetails = await this.fetchCaseDetails(request.caseId, request.provider)
      
      // Prepare AI prompt from case details
      const prompt = this.preparePrompt(caseDetails, request.provider)
      
      if (!prompt.trim()) {
        return {
          success: false,
          error: 'No usable content found in the test case'
        }
      }

      // Extract scenario name and description from case details
      const scenarioName = request.scenarioName || this.extractScenarioName(caseDetails)
      const scenarioDescription = request.scenarioDescription || this.extractScenarioDescription(caseDetails)

      return {
        success: true,
        data: {
          prompt,
          caseDetails,
          name: scenarioName,
          description: scenarioDescription
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to import test case'
      }
    }
  }

  /**
   * Import test case and generate AI steps in one operation
   */
  static async importAndGenerateSteps(request: ImportRequest): Promise<ImportResponse> {
    try {
      // First import the test case
      const importResult = await this.importTestCase(request)
      
      if (!importResult.success || !importResult.data) {
        return importResult
      }

      // Generate AI steps using the imported prompt
      const stepsResponse = await ScenarioCreationService.generateSteps({
        prompt: importResult.data.prompt,
        name: importResult.data.name || '',
        description: importResult.data.description || '',
        isTestManagement: true, // Mark as test management import
        testManagementProvider: request.provider,
        testDataVariables: request.testDataVariables || [],
        creativityLevel: request.creativityLevel
      })

      if (!stepsResponse.success) {
        return {
          success: false,
          error: stepsResponse.error || 'Failed to generate steps from imported test case'
        }
      }

      return {
        success: true,
        data: {
          ...importResult.data,
          steps: stepsResponse.data?.steps
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to import and generate steps'
      }
    }
  }

  /**
   * Fetch case details from the appropriate provider
   */
  private static async fetchCaseDetails(caseId: string, provider: TestManagementProvider): Promise<CaseDetails> {
    switch (provider) {
      case 'testrail':
        return this.fetchTestRailCaseDetails(caseId)
      case 'zephyrscale':
        return this.fetchZephyrScaleCaseDetails(caseId)
      default:
        throw new Error(`Unsupported provider: ${provider}`)
    }
  }

  /**
   * Fetch TestRail case details
   */
  private static async fetchTestRailCaseDetails(caseId: string): Promise<CaseDetails> {
    const response = await pluginApi.getTestRailCaseSteps(caseId)
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch TestRail case details')
    }

    const caseData = response.caseData || response.data?.caseData || response.case
    if (!caseData) {
      throw new Error('No case data found in TestRail response')
    }

    return {
      id: caseData.id?.toString() || caseId,
      title: caseData.title,
      name: caseData.title,
      description: caseData.custom_steps_formatted || caseData.custom_steps,
      steps: caseData.custom_steps_formatted || caseData.custom_steps,
      expectedResults: caseData.custom_expected_formatted || caseData.custom_expected,
      provider: 'testrail',
      ...caseData
    }
  }

  /**
   * Fetch Zephyr Scale case details
   */
  private static async fetchZephyrScaleCaseDetails(caseId: string): Promise<CaseDetails> {
    const response = await pluginApi.getZephyrScaleTestCaseDetails({ testCaseKey: caseId })

    console.log('[UnifiedImportService] ZephyrScale API response:', response)

    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch Zephyr Scale case details')
    }

    const caseData = response.data
    console.log('[UnifiedImportService] ZephyrScale case data:', caseData)

    if (!caseData) {
      throw new Error('No case data found in Zephyr Scale response')
    }

    return {
      id: caseData.key || caseData.id || caseId,
      title: caseData.name,
      name: caseData.name,
      description: caseData.objective,
      objective: caseData.objective,
      precondition: caseData.precondition,
      steps: this.formatZephyrScaleSteps(caseData),
      provider: 'zephyrscale',
      ...caseData
    }
  }

  /**
   * Format Zephyr Scale test steps into a readable string
   */
  private static formatZephyrScaleSteps(caseData: any): string {
    console.log('[UnifiedImportService] formatZephyrScaleSteps - Input caseData:', JSON.stringify(caseData, null, 2))

    // Check for steps in the new format (directly in caseData.steps)
    let steps = caseData.steps || []

    // Fallback to old format for backward compatibility
    if (steps.length === 0 && caseData.testScript?.steps) {
      steps = caseData.testScript.steps
    }

    console.log('[UnifiedImportService] formatZephyrScaleSteps - Found steps:', JSON.stringify(steps, null, 2))

    if (!steps || steps.length === 0) {
      console.log('[UnifiedImportService] formatZephyrScaleSteps - No steps found, returning empty string')
      return ''
    }

    // Ensure steps is an array
    if (!Array.isArray(steps)) {
      console.warn('[UnifiedImportService] formatZephyrScaleSteps - Steps is not an array:', typeof steps, steps)
      return ''
    }

    let formattedSteps = ''
    steps.forEach((step: any, index: number) => {
      console.log(`[UnifiedImportService] formatZephyrScaleSteps - Processing step ${index + 1}:`, JSON.stringify(step, null, 2))

      const stepNumber = index + 1

      // Safely convert to string and handle potential objects
      let description = ''
      if (step.description !== undefined && step.description !== null) {
        description = typeof step.description === 'string' ? step.description : JSON.stringify(step.description)
      } else if (step.action !== undefined && step.action !== null) {
        description = typeof step.action === 'string' ? step.action : JSON.stringify(step.action)
      }

      let expectedResult = ''
      if (step.expectedResult !== undefined && step.expectedResult !== null) {
        expectedResult = typeof step.expectedResult === 'string' ? step.expectedResult : JSON.stringify(step.expectedResult)
      }

      let testData = ''
      if (step.testData !== undefined && step.testData !== null) {
        testData = typeof step.testData === 'string' ? step.testData : JSON.stringify(step.testData)
      }

      console.log(`[UnifiedImportService] formatZephyrScaleSteps - Step ${stepNumber} processed:`, {
        description: description.substring(0, 100) + (description.length > 100 ? '...' : ''),
        expectedResult: expectedResult.substring(0, 100) + (expectedResult.length > 100 ? '...' : ''),
        testData: testData.substring(0, 50) + (testData.length > 50 ? '...' : '')
      })

      // Handle HTML <br> tags in description - convert to numbered sub-steps
      if (description && description.includes('<br>')) {
        const subSteps = description.split('<br>').map(s => s.trim()).filter(s => s.length > 0)
        formattedSteps += `Step ${stepNumber}:\n`
        subSteps.forEach((subStep, subIndex) => {
          // Remove existing numbering if present (e.g., "1. Action" -> "Action")
          const cleanSubStep = subStep.replace(/^\d+\.\s*/, '')
          formattedSteps += `  ${subIndex + 1}. ${cleanSubStep}\n`
        })
      } else if (description) {
        formattedSteps += `Step ${stepNumber}: ${description}\n`
      }

      // Add test data if present
      if (testData) {
        formattedSteps += `  Test Data: ${testData}\n`
      }

      // Handle HTML <br> tags in expected result
      if (expectedResult) {
        if (expectedResult.includes('<br>')) {
          const expectedSubSteps = expectedResult.split('<br>').map(s => s.trim()).filter(s => s.length > 0)
          formattedSteps += `  Expected Results:\n`
          expectedSubSteps.forEach((expectedSubStep, subIndex) => {
            // Remove existing numbering if present
            const cleanExpectedSubStep = expectedSubStep.replace(/^\d+\.\s*/, '')
            formattedSteps += `    ${subIndex + 1}. ${cleanExpectedSubStep}\n`
          })
        } else {
          formattedSteps += `  Expected: ${expectedResult}\n`
        }
      }

      formattedSteps += '\n'
    })

    const result = formattedSteps.trim()
    console.log('[UnifiedImportService] formatZephyrScaleSteps - Final result length:', result.length)
    console.log('[UnifiedImportService] formatZephyrScaleSteps - Final result preview:', result.substring(0, 200) + (result.length > 200 ? '...' : ''))

    return result
  }

  /**
   * Prepare AI prompt from case details
   */
  private static preparePrompt(caseDetails: CaseDetails, provider: TestManagementProvider): string {
    switch (provider) {
      case 'testrail':
        return this.prepareTestRailPrompt(caseDetails)
      case 'zephyrscale':
        return this.prepareZephyrScalePrompt(caseDetails)
      default:
        return ''
    }
  }

  /**
   * Prepare TestRail prompt
   */
  private static prepareTestRailPrompt(caseDetails: CaseDetails): string {
    let prompt = `TestRail Test Case: ${caseDetails.title || 'Untitled'}\n\n`
    
    if (caseDetails.steps) {
      prompt += `Test Steps:\n${caseDetails.steps}\n\n`
    }
    
    if (caseDetails.expectedResults) {
      prompt += `Expected Results:\n${caseDetails.expectedResults}`
    }
    
    return prompt.trim()
  }

  /**
   * Prepare Zephyr Scale prompt
   */
  private static prepareZephyrScalePrompt(caseDetails: CaseDetails): string {
    console.log('[UnifiedImportService] prepareZephyrScalePrompt - Input caseDetails:', JSON.stringify(caseDetails, null, 2))

    let prompt = `Zephyr Scale Test Case: ${caseDetails.name || 'Untitled'}\n\n`

    if (caseDetails.objective) {
      prompt += `Objective:\n${caseDetails.objective}\n\n`
    }

    if (caseDetails.precondition) {
      prompt += `Precondition:\n${caseDetails.precondition}\n\n`
    }

    if (caseDetails.steps) {
      // Safely handle steps - ensure it's a string
      let stepsText = ''
      if (typeof caseDetails.steps === 'string') {
        stepsText = caseDetails.steps
      } else if (Array.isArray(caseDetails.steps)) {
        // If steps is still an array, format it here as fallback
        console.warn('[UnifiedImportService] prepareZephyrScalePrompt - Steps is still an array, formatting as fallback')
        stepsText = this.formatZephyrScaleSteps({ steps: caseDetails.steps })
      } else if (typeof caseDetails.steps === 'object') {
        // If steps is an object, try to stringify it safely
        console.warn('[UnifiedImportService] prepareZephyrScalePrompt - Steps is an object, converting to JSON')
        stepsText = JSON.stringify(caseDetails.steps, null, 2)
      } else {
        stepsText = String(caseDetails.steps)
      }

      console.log('[UnifiedImportService] prepareZephyrScalePrompt - Steps text length:', stepsText.length)
      console.log('[UnifiedImportService] prepareZephyrScalePrompt - Steps text preview:', stepsText.substring(0, 200) + (stepsText.length > 200 ? '...' : ''))

      prompt += `Test Steps:\n${stepsText}`
    }

    const result = prompt.trim()
    console.log('[UnifiedImportService] prepareZephyrScalePrompt - Final prompt length:', result.length)

    return result
  }

  /**
   * Extract scenario name from case details
   */
  private static extractScenarioName(caseDetails: CaseDetails): string {
    return caseDetails.title || caseDetails.name || 'Imported Test Case'
  }

  /**
   * Extract scenario description from case details
   */
  private static extractScenarioDescription(caseDetails: CaseDetails): string {
    if (caseDetails.provider === 'zephyrscale' && caseDetails.objective) {
      return caseDetails.objective
    }
    
    if (caseDetails.expectedResults) {
      return caseDetails.expectedResults
    }
    
    return `Imported from ${caseDetails.provider === 'testrail' ? 'TestRail' : 'Zephyr Scale'} case ${caseDetails.id}`
  }

  /**
   * Validate import request
   */
  static validateImportRequest(request: ImportRequest): { valid: boolean; error?: string } {
    if (!request.caseId) {
      return { valid: false, error: 'Case ID is required' }
    }

    if (!request.provider) {
      return { valid: false, error: 'Provider is required' }
    }

    if (!['testrail', 'zephyrscale'].includes(request.provider)) {
      return { valid: false, error: 'Unsupported provider' }
    }

    return { valid: true }
  }
}
