"use client"

import { useState } from "react"
import { FolderPlus, FileText, Upload, Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { Scenario, FolderType } from "@/types/scenario"
import { ScrollArea } from "@/components/ui/scroll-area"
import { FolderStructure } from "@/components/folder-structure-new"

interface ScenarioWithMeta extends Scenario {
  lastRun?: string;
  updatedAt?: string;
  testType?: 'web' | 'mobile' | 'api';
}

interface ModernLayoutProps {
  scenarios: ScenarioWithMeta[]
  folders: FolderType[]
  selectedFolder: string | null
  searchQuery: string
  onSearchChange: (query: string) => void
  onSelectFolder: (folderId: string | null) => void
  onToggleFolder?: (folderId: string) => void
  onCreateFolder?: (name: string) => void
  onRenameFolder?: (id: string, name: string) => void
  onDeleteFolder?: (id: string) => void
  onDragScenario?: (scenarioId: string, e: React.DragEvent) => void
  onDragOverFolder?: (folderId: string) => void
  onDragLeaveFolder?: () => void
  onDropOnFolder?: (folderId: string | null) => void
  draggedScenario?: string | null
  children: React.ReactNode
  onNewScenario?: () => void
  onImportScenarios?: () => void
  onExportScenarios?: () => void
  isLoading?: boolean
  selectedScenarios?: string[]
  onApplyStatusFilter?: (status: string | null) => void
  onApplyTestTypeFilter?: (type: string | null) => void
  onApplyTagFilter?: (tag: string | null) => void
  statusFilter?: string | null
  testTypeFilter?: string | null
  tagFilter?: string | null
  allTags?: string[]
  onClearFilters?: () => void
}

export function ModernLayout({
  scenarios,
  folders,
  selectedFolder,
  searchQuery,
  onSearchChange,
  onSelectFolder,
  onToggleFolder,
  onCreateFolder,
  onRenameFolder,
  onDeleteFolder,
  onDragScenario,
  onDragOverFolder,
  onDragLeaveFolder,
  onDropOnFolder,
  draggedScenario,
  children,
  onNewScenario,
  onImportScenarios,
  onExportScenarios,
  isLoading = false,
  selectedScenarios = [],
  onApplyStatusFilter,
  onApplyTestTypeFilter,
  onApplyTagFilter,
  statusFilter,
  testTypeFilter,
  tagFilter,
  allTags = [],
  onClearFilters
}: ModernLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null)

  // Handle folder drag events
  const handleDropOnFolder = (folderId: string) => {
    if (onDropOnFolder) {
      onDropOnFolder(folderId);
      setDragOverFolder(null);
    }
  };
  const handleDragLeave = () => {
    setDragOverFolder(null)
    if (onDragLeaveFolder) onDragLeaveFolder()
  }

  // Handle drag over event for the folder
  const handleDragOver = (e: React.DragEvent<Element>, folderId: string) => {
    // Prevent default to allow drop
    if (e) e.preventDefault()
    // Update UI to show the folder is being dragged over
    setDragOverFolder(folderId)
    // Call the parent handler if provided
    if (onDragOverFolder) onDragOverFolder(folderId)
  }

  // Dummy function for onDragScenario
  const handleDragScenario = (scenarioId: string, e: React.DragEvent) => {
    // This is just a placeholder to satisfy the FolderStructure component requirements
    if (onDragScenario) {
      onDragScenario(scenarioId, e);
    }
  }

  // Get all tags
  const uniqueTags = allTags.length > 0 ? allTags : Array.from(
    new Set(
      scenarios.flatMap(scenario => scenario.tags || [])
      .filter(Boolean)
    )
  ).sort()

  // Active filters count
  const activeFiltersCount = [
    statusFilter,
    testTypeFilter,
    tagFilter
  ].filter(Boolean).length

  return (
    <div className={cn(
      "h-screen flex",
      "dark:bg-gray-950 bg-gray-50",
      "overflow-hidden transition-all"
    )}>
      {/* Sidebar */}
      <div className={cn(
        "flex flex-col border-r dark:border-gray-800 bg-white dark:bg-gray-900",
        "transition-all duration-300",
        sidebarOpen ? "w-[220px] min-w-[220px]" : "w-0 min-w-0 hidden"
      )}>


        <div className="border-t dark:border-gray-800">
          <ScrollArea className="flex-1 h-[calc(100vh-130px)] pt-2">
            <FolderStructure
              folders={folders}
              scenarios={scenarios}
              selectedFolder={selectedFolder}
              onSelectFolder={(folderId) => onSelectFolder ? onSelectFolder(folderId) : null}
              onCreateFolder={(name, color) => onCreateFolder ? onCreateFolder(name) : null}
              onRenameFolder={(folderId, newName) => onRenameFolder ? onRenameFolder(folderId, newName) : null}
              onDeleteFolder={(folderId) => onDeleteFolder ? onDeleteFolder(folderId) : null}
              onDropOnFolder={(folderId) => onDropOnFolder ? onDropOnFolder(folderId) : null}
              onDragOverFolder={handleDragOver}
              onDragLeaveFolder={handleDragLeave}
              dragOverFolder={dragOverFolder}
            />
          </ScrollArea>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header with search and actions */}
        <header className="bg-white dark:bg-gray-900 border-b dark:border-gray-800 p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2 flex-1">
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 shrink-0 xl:hidden"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <FolderPlus className="h-4 w-4" />
                <span className="sr-only">Toggle Sidebar</span>
              </Button>
            </div>

            <div className="flex items-center gap-2">
              {/* Import Button */}
              {onImportScenarios && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onImportScenarios}
                  className="h-9 px-4 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  <span>İçe Aktar</span>
                </Button>
              )}

              {/* Export Button */}
              {onExportScenarios && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onExportScenarios}
                  className="h-9 px-4 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  <span>Dışarı Aktar</span>
                </Button>
              )}

              {/* Create Scenario Button */}
              {onNewScenario && (
                <Button
                  size="sm"
                  onClick={onNewScenario}
                  className="h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors hover:shadow-md"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  <span>Senaryo Yarat</span>
                </Button>
              )}
            </div>
          </div>
        </header>

        {/* Main content area */}
        <div className="flex-1 overflow-auto p-4">
          {children}
        </div>
      </div>
    </div>
  )
}
