"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Database, Plus, Edit, Key, Trash2, MoreHorizontal, AlertCircle, RefreshCw } from "lucide-react"
import type { DataSource } from "@/types/test-data"
import { getDataSourceIcon, getDataSourceTypeLabel, formatDate } from "@/lib/test-data-utils"

interface DataSourcesTableProps {
  dataSources: DataSource[]
  onNewDataSource: () => void
  onEditDataSource: (id: string) => void
  onDeleteDataSource: (id: string) => void
  onToggleDataSourceStatus: (id: string) => void
  onTestConnection: (connectionString: string) => void
  isLoading?: boolean
  error?: string | null
  onRetry?: () => void
}

export function DataSourcesTable({
  dataSources,
  onNewDataSource,
  onEditDataSource,
  onDeleteDataSource,
  onToggleDataSourceStatus,
  onTestConnection,
  isLoading = false,
  error = null,
  onRetry
}: DataSourcesTableProps) {

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
          <CardTitle>Data Sources</CardTitle>
          <CardDescription>Manage your test data sources and connections</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px]">Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="w-[120px]">Type</TableHead>
                <TableHead className="w-[120px]">Status</TableHead>
                <TableHead className="w-[180px]">Last Updated</TableHead>
                <TableHead className="w-[100px] text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(3)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>{error}</span>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  if (dataSources.length === 0) {
    return (
      <Card className="border-dashed">
        <CardContent className="pt-6 text-center">
          <Database className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium">No Data Sources Found</h3>
          <p className="text-sm text-gray-500 mt-1 mb-4">No data sources match your search criteria.</p>
          <Button
            onClick={onNewDataSource}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Data Source
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
        <CardTitle>Data Sources</CardTitle>
        <CardDescription>Manage your test data sources and connections</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="w-[120px]">Type</TableHead>
              <TableHead className="w-[120px]">Status</TableHead>
              <TableHead className="w-[180px]">Last Updated</TableHead>
              <TableHead className="w-[100px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {dataSources.map((source, index) => (
              <TableRow key={source.id || `source-${index}`}>
                <TableCell className="font-medium">{source.name || 'Unnamed Source'}</TableCell>
                <TableCell>{source.description || 'No description provided'}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-1.5">
                    {getDataSourceIcon(source.type)}
                    <span>
                      {source.type === 'file' && source.filePath
                        ? source.filePath.split('.').pop()?.toUpperCase()
                        : getDataSourceTypeLabel(source.type)}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                   <div
                    className="inline-flex items-center gap-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 p-1 rounded-md transition-colors"
                    onClick={() => onToggleDataSourceStatus(source.id)}
                  >
                    <div
                      className={`h-2 w-2 rounded-full ${
                        source.isActive ? "bg-emerald-500" : "bg-gray-300 dark:bg-gray-600"
                      }`}
                    ></div>
                    <span className="text-sm select-none">{source.isActive ? "Active" : "Inactive"}</span>
                  </div>
                </TableCell>
                <TableCell>{formatDate(source.updatedAt || source.createdAt)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => onEditDataSource(source.id)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600" onClick={() => onDeleteDataSource(source.id)}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
