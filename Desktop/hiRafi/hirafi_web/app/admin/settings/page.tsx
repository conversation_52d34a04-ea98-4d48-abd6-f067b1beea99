"use client"

import { useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "@/components/ui/use-toast"
import { Save, User, Lock, Settings, Mail, Slack, Bot, Globe } from "lucide-react"
import { getAdminSettings, updateAdminSettings, changeAdminPassword } from "@/lib/api/admin-api"
import { useEffect } from "react"
import { AdminRouteGuard } from "@/lib/api/admin-auth"
import Link from "next/link"

// Admin profil formu şeması
const profileFormSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
})

// Şifre değiştirme formu şeması
const passwordFormSchema = z.object({
  currentPassword: z.string().min(1, {
    message: "Current password is required.",
  }),
  newPassword: z.string().min(6, {
    message: "New password must be at least 6 characters.",
  }),
  confirmPassword: z.string().min(6, {
    message: "Confirm password must be at least 6 characters.",
  }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
})

// Email, System ve Database ayarları kaldırıldı

type ProfileFormValues = z.infer<typeof profileFormSchema>
type PasswordFormValues = z.infer<typeof passwordFormSchema>
// Email, System ve Database form tipleri kaldırıldı

export default function AdminSettingsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Profil formu
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "",
      email: "",
    },
  })

  // Şifre değiştirme formu
  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  })

  // Email, System ve Database form tanımları kaldırıldı

  // Ayarları yükle
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true)
      setError(null) // Clear previous errors
      try {
        const response = await getAdminSettings()

        if (response.success && response.data) {
          const settings = response.data.settings || response.data

          // Profil bilgilerini doldur
          if (settings.profile) {
            profileForm.reset({
              name: settings.profile.name || "",
              email: settings.profile.email || "",
            })
          }

          // Email, System ve Database ayarları kaldırıldı
        } else {
          const errorMessage = response.error || "Unknown error occurred while fetching settings"
          setError(errorMessage)
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          })
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        setError(errorMessage)
        toast({
          title: "Error",
          description: "Failed to load settings",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadSettings()
  }, [profileForm])

  // Profil formunu gönder
  const onProfileSubmit = async (data: ProfileFormValues) => {
    setIsSaving(true)
    setError(null) // Clear previous errors
    try {
      const response = await updateAdminSettings({
        profile: data
      })

      if (response.success) {
        toast({
          title: "Success",
          description: "Profile settings updated successfully",
        })
      } else {
        const errorMessage = response.error || "Unknown error occurred while updating profile settings"
        setError(errorMessage)
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      setError(errorMessage)
      toast({
        title: "Error",
        description: "Failed to update profile settings",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Şifre formunu gönder
  const onPasswordSubmit = async (data: PasswordFormValues) => {
    setIsSaving(true)
    setError(null) // Clear previous errors
    try {
      const response = await changeAdminPassword(
        data.currentPassword,
        data.newPassword
      )

      if (response.success) {
        toast({
          title: "Success",
          description: "Password updated successfully",
        })
        passwordForm.reset({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        })
      } else {
        const errorMessage = response.error || "Unknown error occurred while updating password"
        setError(errorMessage)
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      setError(errorMessage)
      toast({
        title: "Error",
        description: "Failed to update password",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Email, System ve Database ayarları formları kaldırıldı

  if (isLoading) {
    return (
      <AdminRouteGuard>
        <AdminLayout>
          <div className="flex h-[50vh] items-center justify-center">
            <div className="flex flex-col items-center gap-2">
              <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
              <p className="text-sm text-muted-foreground">Loading settings...</p>
            </div>
          </div>
        </AdminLayout>
      </AdminRouteGuard>
    )
  }

  return (
    <AdminRouteGuard>
      <AdminLayout>
        <div className="flex flex-col gap-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">
              Manage your admin account and system settings
            </p>
          </div>

          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>

            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5 text-muted-foreground" />
                    <CardTitle>Profile Settings</CardTitle>
                  </div>
                  <CardDescription>
                    Update your admin profile information
                  </CardDescription>
                </CardHeader>
                <Form {...profileForm}>
                  <form onSubmit={profileForm.handleSubmit(onProfileSubmit)}>
                    <CardContent className="space-y-4">
                      <FormField
                        control={profileForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Your name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input placeholder="Your email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                    <CardFooter>
                      <Button type="submit" disabled={isSaving}>
                        {isSaving ? (
                          <div className="flex items-center">
                            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></div>
                            Saving...
                          </div>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </form>
                </Form>
              </Card>
            </TabsContent>

            <TabsContent value="security">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Lock className="h-5 w-5 text-muted-foreground" />
                      <CardTitle>Security Settings</CardTitle>
                    </div>
                    <CardDescription>
                      Update your password and security settings
                    </CardDescription>
                  </CardHeader>
                  <Form {...passwordForm}>
                    <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}>
                      <CardContent className="space-y-4">
                        <FormField
                          control={passwordForm.control}
                          name="currentPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Current Password</FormLabel>
                              <FormControl>
                                <Input type="password" placeholder="••••••••" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={passwordForm.control}
                          name="newPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>New Password</FormLabel>
                              <FormControl>
                                <Input type="password" placeholder="••••••••" {...field} />
                              </FormControl>
                              <FormDescription>
                                Password must be at least 6 characters long
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={passwordForm.control}
                          name="confirmPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Confirm New Password</FormLabel>
                              <FormControl>
                                <Input type="password" placeholder="••••••••" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                      <CardFooter>
                        <Button type="submit" disabled={isSaving}>
                          {isSaving ? (
                            <div className="flex items-center">
                              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></div>
                              Updating...
                            </div>
                          ) : (
                            <>
                              <Save className="mr-2 h-4 w-4" />
                              Update Password
                            </>
                          )}
                        </Button>
                      </CardFooter>
                    </form>
                  </Form>
                </Card>

                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Settings className="h-5 w-5 text-muted-foreground" />
                      <CardTitle>System Settings</CardTitle>
                    </div>
                    <CardDescription>
                      Configure system-wide settings for notifications and integrations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="border border-gray-200 dark:border-gray-800">
                        <CardHeader className="p-4">
                          <div className="flex items-center gap-2">
                            <Mail className="h-5 w-5 text-blue-500" />
                            <CardTitle className="text-base">SMTP Configuration</CardTitle>
                          </div>
                          <CardDescription className="text-xs">
                            Configure email server settings for notifications
                          </CardDescription>
                        </CardHeader>
                        <CardFooter className="p-4 pt-0 flex justify-end">
                          <Link href="/admin/settings/system?tab=smtp">
                            <Button size="sm" variant="outline">
                              Configure
                            </Button>
                          </Link>
                        </CardFooter>
                      </Card>

                      <Card className="border border-gray-200 dark:border-gray-800">
                        <CardHeader className="p-4">
                          <div className="flex items-center gap-2">
                            <Slack className="h-5 w-5 text-purple-500" />
                            <CardTitle className="text-base">Slack Integration</CardTitle>
                          </div>
                          <CardDescription className="text-xs">
                            Configure Slack bot for sending notifications
                          </CardDescription>
                        </CardHeader>
                        <CardFooter className="p-4 pt-0 flex justify-end">
                          <Link href="/admin/settings/system?tab=slack">
                            <Button size="sm" variant="outline">
                              Configure
                            </Button>
                          </Link>
                        </CardFooter>
                      </Card>

                      <Card className="border border-gray-200 dark:border-gray-800">
                        <CardHeader className="p-4">
                          <div className="flex items-center gap-2">
                            <Bot className="h-5 w-5 text-green-500" />
                            <CardTitle className="text-base">Default AI Model</CardTitle>
                          </div>
                          <CardDescription className="text-xs">
                            Configure default AI model for new companies
                          </CardDescription>
                        </CardHeader>
                        <CardFooter className="p-4 pt-0 flex justify-end">
                          <Link href="/admin/settings/system?tab=ai_model">
                            <Button size="sm" variant="outline">
                              Configure
                            </Button>
                          </Link>
                        </CardFooter>
                      </Card>

                      <Card className="border border-gray-200 dark:border-gray-800">
                        <CardHeader className="p-4">
                          <div className="flex items-center gap-2">
                            <Globe className="h-5 w-5 text-indigo-500" />
                            <CardTitle className="text-base">Landing Schedule</CardTitle>
                          </div>
                          <CardDescription className="text-xs">
                            Configure landing page meeting schedule form and notifications
                          </CardDescription>
                        </CardHeader>
                        <CardFooter className="p-4 pt-0 flex justify-end">
                          <Link href="/admin/settings/system?tab=landing_page">
                            <Button size="sm" variant="outline">
                              Configure
                            </Button>
                          </Link>
                        </CardFooter>
                      </Card>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Email ayarları kaldırıldı */}

            {/* System ayarları kaldırıldı */}

            {/* Database ayarları kaldırıldı */}
          </Tabs>
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  )
}
