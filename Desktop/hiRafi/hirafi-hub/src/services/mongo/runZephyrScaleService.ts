/**
 * Run Zephyr Scale Service
 * Manages Zephyr Scale integration for test runs
 */

import { logger } from '../../utils/logger.js';
import { db, isMongoDBInitialized, pluginsCollection, runsCollection, scenariosCollection } from './dbConnection.js';
import axios from 'axios';
import { getRunReportByRunId, updateRunReportZephyrScale, getRunReportZephyrScaleInfo } from './atomicReportService.js';

// Zephyr Scale API endpoints
const ZEPHYR_SCALE_CONFIG = {
  baseUrl: 'https://api.zephyrscale.smartbear.com/v2',
  apiCreateTestCycle: '/testcycles',
  apiGetTestCycles: '/testcycles',
  apiUpdateTestCycle: '/testcycles/{testCycleId}',
  apiAddTestsToTestCycle: '/testcycles/{testCycleId}/links/testcases'
};

/**
 * Create Zephyr Scale test cycle for a run
 * @param runId Run ID
 * @param teamId Team ID
 * @param companyId Company ID
 * @param runName Run name
 * @returns Zephyr Scale test cycle ID and URL
 */
export async function createZephyrScaleTestCycleForRun(
  runId: string,
  teamId: string,
  companyId: string,
  runName: string
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection || !runsCollection || !scenariosCollection) {
      return {
        success: false,
        message: "Database connection not established",
        data: null
      };
    }

    // Check if Zephyr Scale test cycle already exists for this run
    const runReportResult = await getRunReportZephyrScaleInfo(runId);

    if (runReportResult.success && runReportResult.zephyrscaleTestCycleId) {
      logger.info(`Run ${runId} already has a Zephyr Scale test cycle ID: ${runReportResult.zephyrscaleTestCycleId}`);
      return {
        success: true,
        message: "Zephyr Scale test cycle already exists for this run",
        data: null,
        testCycleId: runReportResult.zephyrscaleTestCycleId,
        testCycleUrl: runReportResult.zephyrscaleTestCycleLink
      };
    }

    // Get Zephyr Scale configuration for the team
    const plugin = await pluginsCollection.findOne({
      teamId: teamId,
      companyId: companyId,
      "plugins.id": "zephyrscale"
    });

    if (!plugin) {
      return {
        success: false,
        message: "Zephyr Scale plugin not configured for this team",
        data: null
      };
    }

    // Find Zephyr Scale plugin
    const zephyrScalePlugin = plugin.plugins.find((p: any) => p.id === "zephyrscale");

    if (!zephyrScalePlugin || !zephyrScalePlugin.config) {
      return {
        success: false,
        message: "Zephyr Scale plugin configuration not found",
        data: null
      };
    }

    const pluginConfig = zephyrScalePlugin.config;

    // Check required fields
    if (!pluginConfig.apiToken) {
      return {
        success: false,
        message: "Missing Zephyr Scale API token",
        data: null
      };
    }

    // Get first project
    if (!pluginConfig.projectsData || !pluginConfig.projectsData.length) {
      return {
        success: false,
        message: "No projects configured in Zephyr Scale settings",
        data: null
      };
    }

    const projectKey = pluginConfig.projectsData[0].key || pluginConfig.projectsData[0].id;

    // Set up headers for Zephyr Scale API
    const headers = {
      'Authorization': `Bearer ${pluginConfig.apiToken}`,
      'Content-Type': 'application/json'
    };

    // Format date for test cycle name
    const formattedDate = new Date().toISOString().split('T')[0];

    // Collect Zephyr Scale test case keys for this run
    let testCaseKeys: string[] = [];

    // Get run from runs collection
    const run = await runsCollection.findOne({ id: runId });

    if (run && run.scenarioIds && run.scenarioIds.length > 0) {
      logger.info(`Found ${run.scenarioIds.length} scenarios in run ${runId}`);

      // Get scenarios with Zephyr Scale integration
      const scenarios = await scenariosCollection.find({
        id: { $in: run.scenarioIds },
        "zephyrscaleIntegration.sync": true // Only scenarios with Zephyr Scale integration
      }).toArray();

      logger.info(`Found ${scenarios.length} scenarios with Zephyr Scale integration in run ${runId}`);

      // Collect Zephyr Scale test case keys
      for (const scenario of scenarios) {
        if (scenario.zephyrscaleIntegration &&
            scenario.zephyrscaleIntegration.caseIds &&
            scenario.zephyrscaleIntegration.caseIds.length > 0) {

          // Add each test case key
          for (const caseKey of scenario.zephyrscaleIntegration.caseIds) {
            if (caseKey && typeof caseKey === 'string') {
              testCaseKeys.push(caseKey);
            }
          }
        }
      }

      // Remove duplicates
      testCaseKeys = [...new Set(testCaseKeys)];
      logger.info(`Collected ${testCaseKeys.length} unique test case keys for Zephyr Scale test cycle`);
    }

    // Prepare test cycle data
    const testCycleData = {
      projectKey: projectKey,
      name: `${runName} - ${formattedDate}`,
      description: `Automated test cycle created for run ID: ${runId}`,
      plannedStartDate: new Date().toISOString().split('T')[0],
      plannedEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 7 days from now
    };

    // Create test cycle in Zephyr Scale
    const url = `${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiCreateTestCycle}`;

    logger.info(`Creating Zephyr Scale test cycle for run ID ${runId}: ${url}`);
    logger.info(`Test cycle data: ${JSON.stringify(testCycleData)}`);

    const response = await axios.post(url, testCycleData, { headers, timeout: 30000 });

    if (response.status === 201 || response.status === 200) {
      const testCycle = response.data;
      logger.info(`Zephyr Scale test cycle created successfully with ID: ${testCycle.id}`);

      // Generate test cycle URL
      const testCycleUrl = `https://smartbear.atlassian.net/projects/${projectKey}/testcycles/${testCycle.id}`;
      logger.info(`Zephyr Scale test cycle URL: ${testCycleUrl}`);

      // Add test cases to the test cycle if we have any
      if (testCaseKeys.length > 0) {
        try {
          await addTestCasesToTestCycle(testCycle.id, testCaseKeys, headers);
          logger.info(`Added ${testCaseKeys.length} test cases to Zephyr Scale test cycle ${testCycle.id}`);
        } catch (addError: any) {
          logger.warn(`Failed to add test cases to Zephyr Scale test cycle: ${addError.message}`);
          // Don't fail the entire operation if adding test cases fails
        }
      }

      // Update run report with Zephyr Scale test cycle information
      try {
        await updateRunReportZephyrScale(runId, {
          testCycleId: testCycle.id,
          testCycleUrl: testCycleUrl,
          testCycleKey: testCycle.key,
          projectKey: projectKey,
          createdAt: new Date()
        });
        logger.info(`Updated run report ${runId} with Zephyr Scale test cycle information`);
      } catch (updateError: any) {
        logger.warn(`Failed to update run report with Zephyr Scale info: ${updateError.message}`);
      }

      return {
        success: true,
        message: "Zephyr Scale test cycle created successfully",
        data: testCycle,
        testCycleId: testCycle.id,
        testCycleUrl: testCycleUrl,
        testCycleKey: testCycle.key
      };
    } else {
      return {
        success: false,
        message: `Failed to create Zephyr Scale test cycle: ${response.status}`,
        data: null
      };
    }
  } catch (error: any) {
    logger.error(`Error creating Zephyr Scale test cycle for run ${runId}: ${error.message}`);
    if (error.response) {
      logger.error(`Zephyr Scale API response status: ${error.response.status}`);
      logger.error(`Zephyr Scale API response data: ${JSON.stringify(error.response.data)}`);
    }
    return {
      success: false,
      message: `Error creating Zephyr Scale test cycle: ${error.message}`,
      data: null
    };
  }
}

/**
 * Add test cases to a Zephyr Scale test cycle
 */
async function addTestCasesToTestCycle(testCycleId: string, testCaseKeys: string[], headers: any) {
  const url = `${ZEPHYR_SCALE_CONFIG.baseUrl}${ZEPHYR_SCALE_CONFIG.apiAddTestsToTestCycle.replace('{testCycleId}', testCycleId)}`;
  
  const linkData = {
    testCaseKeys: testCaseKeys
  };

  const response = await axios.post(url, linkData, { headers, timeout: 30000 });
  
  if (response.status !== 200 && response.status !== 201) {
    throw new Error(`Failed to add test cases to test cycle: ${response.status}`);
  }
  
  return response.data;
}

/**
 * Get Zephyr Scale test cycle ID for a run
 */
export async function getZephyrScaleTestCycleIdForRun(runId: string) {
  try {
    const runReportResult = await getRunReportZephyrScaleInfo(runId);
    
    if (runReportResult.success && runReportResult.zephyrscaleTestCycleId) {
      return {
        success: true,
        message: "Zephyr Scale test cycle found for run",
        testCycleId: runReportResult.zephyrscaleTestCycleId,
        testCycleUrl: runReportResult.zephyrscaleTestCycleLink
      };
    } else {
      return {
        success: false,
        message: "No Zephyr Scale test cycle found for this run",
        testCycleId: null,
        testCycleUrl: null
      };
    }
  } catch (error: any) {
    logger.error(`Error getting Zephyr Scale test cycle for run ${runId}: ${error.message}`);
    return {
      success: false,
      message: `Error getting Zephyr Scale test cycle: ${error.message}`,
      testCycleId: null,
      testCycleUrl: null
    };
  }
}
