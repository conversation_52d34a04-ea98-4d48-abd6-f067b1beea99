/**
 * Metrics Types
 * Type definitions for metrics-related functionality
 */

// Network Log Entry interface
export interface NetworkLogEntry {
  url: string;
  method: string;
  status: number;
  type: string;
  size: string;
  time: string;
  timing?: {
    dnsResolution?: number;
    tcpConnection?: number;
    tlsNegotiation?: number;
    timeToFirstByte?: number;
    download?: number;
  };
  // Enhanced metadata for better filtering
  isApiRequest?: boolean;
  isFailed?: boolean;
  isSlow?: boolean;
}

// Accessibility Violation Item interface
export interface AccessibilityViolationItem {
  type: string;
  severity: 'minor' | 'moderate' | 'serious' | 'critical';
  description: string;
  element: string;
  elementHtml: string;
  expectedResult: string;
  impact: string;
  tags: string[];
  target: string[];
  failureSummary: string;
}

// Enhanced Metrics interface
export interface EnhancedMetrics {
  webVitals?: {
    lcp?: number;
    cls?: number;
    fid?: number;
    ttfb?: number;
    inp?: number;
    fcp?: number;
  };
  pageMetrics?: {
    documents?: number;
    nodes?: number;
    jsHeapUsedSize?: number;
    jsHeapTotalSize?: number;
    scriptDuration?: number;
    layoutCount?: number;
    recalcStyleCount?: number;
  };
  networkData?: {
    requests?: {
      total?: number;
      successful?: number;
      failed?: number;
      byType?: Record<string, number>;
    };
    transferred?: {
      total?: number;
      unit?: string;
    };
    logs?: NetworkLogEntry[];
  };
  accessibilityData?: {
    violations?: {
      count?: number;
      items?: AccessibilityViolationItem[];
      byType?: Record<string, number>;
      bySeverity?: Record<string, number>;
    };
    passes?: any[];
    summary?: {
      totalElements?: number;
      accessibilityScore?: number;
      [key: string]: any;
    };
  };
  performanceMetrics?: {
    [key: string]: any;
  };
  tracingData?: {
    [key: string]: any;
  };
  timestamp?: string;
  collectionPerformance?: {
    totalTime?: number;
    pageMetricsCollection?: number;
    networkDataCollection?: number;
    accessibilityDataCollection?: number;
    [key: string]: number | undefined;
  };
}

// Report Detail interface
export interface ReportDetail {
  id: string;
  name: string;
  date: string;
  status: string;
  duration: number;
  passed: number;
  failed: number;
  totalTests: number;
  platform?: 'web' | 'android';
  enhancedMetrics?: EnhancedMetrics;
  metrics?: EnhancedMetrics; // Legacy support
  steps?: any[];
  logs?: string[];
  error?: string;
  url?: string;
  summary?: any;
}
