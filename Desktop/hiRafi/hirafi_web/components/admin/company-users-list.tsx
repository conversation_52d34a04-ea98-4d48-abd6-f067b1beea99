"use client"

import { useState, useEffect, forwardRef, useImperativeH<PERSON><PERSON> } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { MoreHorizontal, Edit, Trash2, Eye, Mail } from "lucide-react"
import Link from "next/link"
import { getCompanyUsers, deleteUser } from "@/lib/api/admin-api"
import { EditUserDialog } from "./edit-user-dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON><PERSON>, AlertDialogTrigger } from "@/components/ui/alert-dialog"

interface User {
  id: string
  email: string
  name?: string
  role: string
  accountType?: string
  teamRole?: string
  createdAt: string
  lastLogin?: string
}

interface CompanyUsersListProps {
  companyId: string
}

export const CompanyUsersList = forwardRef<{ refresh: () => void }, CompanyUsersListProps>(({ companyId }, ref) => {
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)

  // Refresh fonksiyonunu dışarıya aç
  useImperativeHandle(ref, () => ({
    refresh: fetchUsers
  }));

  useEffect(() => {
    if (companyId) {
      fetchUsers()
    }
  }, [companyId])

  const fetchUsers = async () => {
    if (!companyId) {
      setUsers([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    try {
      const response = await getCompanyUsers(companyId)

      if (response.success && (response.data?.users || response.users)) {
        setUsers(response.data?.users || response.users)
      } else {
        console.error("Failed to fetch users:", response.error)
        toast({
          title: "Hata",
          description: `Kullanıcılar yüklenemedi: ${response.error || 'Bilinmeyen hata'}`,
          variant: "destructive",
        })
        setUsers([])
      }
    } catch (error) {
      console.error("Error fetching users:", error)
      toast({
        title: "Hata",
        description: "Kullanıcılar yüklenirken bir hata oluştu.",
        variant: "destructive",
      })
      setUsers([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteUser = async () => {
    if (!userToDelete) return

    setIsDeleting(true)
    try {
      const response = await deleteUser(userToDelete)

      if (response.success) {
        toast({
          title: "Kullanıcı silindi",
          description: "Kullanıcı başarıyla silindi.",
        })
        fetchUsers()
      } else {
        toast({
          title: "Hata",
          description: response.error || "Kullanıcı silinirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting user:", error)
      toast({
        title: "Hata",
        description: "Kullanıcı silinirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setUserToDelete(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive'
      case 'team_leader':
        return 'warning'
      default:
        return 'secondary'
    }
  }

  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Admin'
      case 'team_leader':
        return 'Takım Lideri'
      case 'user':
        return 'Kullanıcı'
      default:
        return role
    }
  }

  const getTeamRoleText = (teamRole: string) => {
    switch (teamRole) {
      case 'team_leader':
        return 'Takım Lideri'
      case 'team_member':
        return 'Takım Üyesi'
      case 'tester':
        return 'Test Uzmanı'
      case 'developer':
        return 'Geliştirici'
      default:
        return teamRole
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Kullanıcılar yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (users.length === 0) {
    return (
      <div className="flex h-40 flex-col items-center justify-center gap-4">
        <p className="text-muted-foreground">Bu şirkete henüz kullanıcı eklenmemiş.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Kullanıcı</TableHead>
              <TableHead>Account Type</TableHead>
              <TableHead>Takım Rolü</TableHead>
              <TableHead>Kayıt Tarihi</TableHead>
              <TableHead>Son Giriş</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="font-medium">{user.name || "İsimsiz Kullanıcı"}</div>
                  <div className="text-sm text-muted-foreground">
                    <a href={`mailto:${user.email}`} className="flex items-center gap-1 hover:underline">
                      <Mail className="h-3 w-3" />
                      {user.email}
                    </a>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getRoleBadgeVariant(user.role) as any}>
                    {user.accountType || getRoleText(user.role)}
                  </Badge>
                </TableCell>
                <TableCell>
                  {user.teamRole ? getTeamRoleText(user.teamRole) : "Belirtilmemiş"}
                </TableCell>
                <TableCell>{formatDate(user.createdAt)}</TableCell>
                <TableCell>
                  {user.lastLogin ? formatDate(user.lastLogin) : "Henüz giriş yapmadı"}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">İşlemler</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      <DropdownMenuItem onSelect={(e) => {
                        e.preventDefault()
                        setSelectedUser(user)
                        setEditUserDialogOpen(true)
                      }}>
                        <Edit className="mr-2 h-4 w-4" />
                        Düzenle
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem onSelect={(e) => {
                            e.preventDefault()
                            setUserToDelete(user.id)
                          }}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Sil
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Kullanıcıyı silmek istediğinize emin misiniz?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bu işlem geri alınamaz. Bu kullanıcı ve ilişkili tüm veriler kalıcı olarak silinecektir.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel onClick={() => setUserToDelete(null)}>İptal</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleDeleteUser}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              disabled={isDeleting}
                            >
                              {isDeleting ? (
                                <div className="flex items-center">
                                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-current"></div>
                                  Siliniyor...
                                </div>
                              ) : (
                                "Kullanıcıyı Sil"
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Kullanıcı Düzenleme Modalı */}
      {selectedUser && (
        <EditUserDialog
          open={editUserDialogOpen}
          onOpenChange={setEditUserDialogOpen}
          user={selectedUser}
          onSuccess={fetchUsers}
        />
      )}
    </div>
  )
})
