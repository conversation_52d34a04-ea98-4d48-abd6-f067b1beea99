export type VariableType = "string" | "number" | "boolean" | "date" | "email" | "phone" | "address" | "secret" | "object" | "array"

export type EnvironmentType = "development" | "testing" | "staging" | "production" | "all"

export type DataSourceType = "database" | "api" | "file" | "manual" | "csv" | "json" | "excel"

export type EnvironmentTypeEnum = "default" | "custom"

export interface DataVariable {
  id: string
  name: string
  value: string
  type: VariableType
  description?: string
  format?: string
  constraints?: ValueConstraints
}

export interface ValueConstraints {
  minLength?: number
  maxLength?: number
  minValue?: number
  maxValue?: number
  decimals?: number
  pattern?: string
  includeUppercase?: boolean
  includeLowercase?: boolean
  includeNumbers?: boolean
  includeSpecial?: boolean
  allowedValues?: string[]
  excludedValues?: string[]
  mustInclude?: string[]
  mustExclude?: string[]
  unique?: boolean
}

export interface DataSet {
  id: string
  name: string
  description: string
  tags: string[]
  environment: EnvironmentType
  metadata?: any // Comprehensive data structure with all environments
  teamId?: string
  companyId?: string
  createdBy?: string
  createdAt?: string
  updatedAt?: string
  lastUpdated?: string // Keep for backward compatibility
}

// Enhanced data set structure for keys endpoint with environment information
export interface DataSetWithVariableEnvironments {
  id: string
  name: string
  description: string
  variables: Array<{
    name: string
    environments: Array<{
      id: string
      name: string
    }>
  }>
}

export interface DataSource {
  id: string
  name: string
  description?: string
  type: DataSourceType
  connectionString?: string
  filePath?: string
  variables?: Array<{ name: string; value: string }>;
  isActive: boolean
  createdAt: string
  updatedAt: string
  lastUpdated: string // Bu alan muhtemelen `updatedAt` ile aynıdır, tutarlılık için ekledim.
  config?: {
    provider?: 'csv' | 'excel' | 'database' | 'api';
    mode?: 'read' | 'visual' | 'raw';
    filePath?: string;
    sheetName?: string;
    data?: Array<{ name: string; value: string; }>;
    table?: string;
    nameColumn?: string;
    valueColumn?: string;
    setupQuery?: string;
    teardownQuery?: string;
    flow?: any[];
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    url?: string;
    headers?: Array<{ key: string; value: string }>;
    body?: string;
    // API step-based wizard fields
    stepCompleted?: {
      request?: boolean;
      response?: boolean;
      parsing?: boolean;
    };
    currentStep?: number;
    testResult?: any;
    lastTestResult?: any;
    parsingRules?: Array<{
      id: string;
      variableName: string;
      jsonPath: string;
      type: 'string' | 'number' | 'boolean';
      regexPattern?: string;
      regexGroup?: number;
      transform?: 'uppercase' | 'lowercase' | 'trim' | 'none';
    }>;
    extractionRules?: Array<{
      id: string;
      variableName: string;
      jsonPath: string;
      type: 'string' | 'number' | 'boolean';
      regexPattern?: string;
      regexGroup?: number;
      transform?: 'uppercase' | 'lowercase' | 'trim' | 'none';
    }>;
  };
}

export interface CustomEnvironment {
  id: string
  name: string
  description: string
  color: string
  type: EnvironmentTypeEnum
  isActive: boolean
  teamId?: string
  companyId?: string
  createdBy?: string
  createdAt: string
  updatedAt: string
}

export interface ColorOption {
  value: string
  label: string
  class: string
}

export interface DataTemplate {
  id: string
  name: string
  description: string
  icon: React.ReactNode | null
  examplePrompt: string
  variables: Array<{
    name: string
    type: VariableType
    description: string
  }>
}

export interface EnvironmentValues {
  development: string
  testing: string
  staging: string
  production: string
}

// Request/Response DTOs
export interface CreateDataSetRequest {
  name: string
  description: string
  variables: Omit<DataVariable, 'id'>[]
  tags: string[]
  environment: EnvironmentType
  metadata?: any // Comprehensive data structure with all environments
}

export interface UpdateDataSetRequest {
  name?: string
  description?: string
  tags?: string[]
  environment?: EnvironmentType
  metadata?: any // Comprehensive data structure with all environments
}

export interface CreateDataSourceRequest {
  name: string
  type: DataSourceType
  description: string
  connectionString?: string
  filePath?: string
  variables?: Array<{ name: string; value: string }>
  isActive?: boolean
  config?: {
    provider: 'csv' | 'excel' | 'database' | 'api';
    mode?: 'read' | 'visual' | 'raw';
    filePath?: string;
    sheetName?: string;
    data?: Array<{ name: string; value: string; }>;
    table?: string;
    nameColumn?: string;
    valueColumn?: string;
    setupQuery?: string;
    teardownQuery?: string;
    flow?: any[]; // Daha sonra detaylandırılabilir
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    url?: string;
    headers?: Array<{ key: string; value: string }>;
    body?: string; // JSON string olarak
    // API step-based wizard fields
    stepCompleted?: {
      request?: boolean;
      response?: boolean;
      parsing?: boolean;
    };
    currentStep?: number;
    testResult?: any;
    lastTestResult?: any;
    parsingRules?: Array<{
      id: string;
      variableName: string;
      jsonPath: string;
      type: 'string' | 'number' | 'boolean';
      regexPattern?: string;
      regexGroup?: number;
      transform?: 'uppercase' | 'lowercase' | 'trim' | 'none';
    }>;
    extractionRules?: Array<{
      id: string;
      variableName: string;
      jsonPath: string;
      type: 'string' | 'number' | 'boolean';
      regexPattern?: string;
      regexGroup?: number;
      transform?: 'uppercase' | 'lowercase' | 'trim' | 'none';
    }>;
  }
}

export interface UpdateDataSourceRequest {
  name?: string
  type?: DataSourceType
  description?: string
  connectionString?: string
  variables?: Array<{ name: string; value: string }>;
  isActive?: boolean
  config?: CreateDataSourceRequest['config'];
}

export interface CreateDataEnvironmentRequest {
  name: string
  description: string
  color: string
  type?: EnvironmentTypeEnum
  isActive?: boolean
}

export interface UpdateDataEnvironmentRequest {
  name?: string
  description?: string
  color?: string
  type?: EnvironmentTypeEnum
  isActive?: boolean
}

// Query options
export interface DataSetQueryOptions {
  limit?: number
  skip?: number
  search?: string
  environment?: EnvironmentType
  tags?: string[]
}

export interface DataSourceQueryOptions {
  limit?: number
  skip?: number
  search?: string
  type?: DataSourceType
  isActive?: boolean
}

export interface DataEnvironmentQueryOptions {
  limit?: number
  skip?: number
  search?: string
  type?: EnvironmentTypeEnum
  isActive?: boolean
}
