"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { createUser } from "@/lib/api/admin-api"

interface AddUserToCompanyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  companyId: string
  companyName: string
  onSuccess?: () => void
}

// Form şeması
const userFormSchema = z.object({
  email: z.string().email({
    message: "Geçerli bir e-posta adresi girin.",
  }).default(""),
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }).default(""),
  password: z.string().min(6, {
    message: "Şifre en az 6 karakter olmalıdır.",
  }).default(""),
  accountType: z.enum(["user", "company_owner"], {
    required_error: "Lütfen bir hesap türü seçin.",
  }).default("user"),
  teamRole: z.enum(["team_admin", "tester", "viewer", "admin"], {
    required_error: "Lütfen bir takım rolü seçin.",
  }).default("viewer"),
})

type UserFormValues = z.infer<typeof userFormSchema>

export function AddUserToCompanyDialog({
  open,
  onOpenChange,
  companyId,
  companyName,
  onSuccess
}: AddUserToCompanyDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Varsayılan form değerleri - useRef kullanarak sabit tutuyoruz
  const defaultValues = React.useRef<UserFormValues>({
    email: "",
    name: "",
    password: "",
    accountType: "user",
    teamRole: "viewer",
  }).current

  // Form tanımı
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues,
    mode: "onChange",
  })

  // Reset form when dialog opens/closes
  useEffect(() => {
    // Dialog açıldığında formu sıfırla
    if (open) {
      // Formu sıfırlarken tüm alanların tanımlı olduğundan emin ol
      form.reset(defaultValues)
    }
  }, [open, form])

  // accountType değiştiğinde teamRole'u otomatik ayarla
  useEffect(() => {
    const accountType = form.watch('accountType');
    if (accountType === 'company_owner') {
      form.setValue('teamRole', 'team_admin');
    }
  }, [form.watch('accountType')]);

  // Form gönderme
  const onSubmit = async (data: UserFormValues) => {
    setIsSubmitting(true)
    try {
      // Company owner için team_admin rolünü zorla
      if (data.accountType === 'company_owner') {
        data.teamRole = 'team_admin';
      }

      // Verileri logla
      console.log("Submitting user data:", {
        ...data,
        companyId,
      })

      // API'ye gönder
      const response = await createUser({
        ...data,
        companyId,
      })

      if (response.success) {
        toast({
          title: "Kullanıcı oluşturuldu",
          description: "Kullanıcı başarıyla oluşturuldu ve şirkete eklendi.",
        })
        form.reset(defaultValues)
        onOpenChange(false)

        // Başarılı olduğunda callback'i çağır
        if (onSuccess) {
          onSuccess()
        }
      } else {
        toast({
          title: "Hata",
          description: response.data?.error || response.error || "Kullanıcı oluşturulurken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating user:", error)
      toast({
        title: "Hata",
        description: "Kullanıcı oluşturulurken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Şirkete Kullanıcı Ekle</DialogTitle>
          <DialogDescription>
            {companyName} şirketine yeni bir kullanıcı ekleyin.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>E-posta *</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>
                    Kullanıcının giriş yaparken kullanacağı e-posta adresi
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ad Soyad *</FormLabel>
                  <FormControl>
                    <Input placeholder="Ad Soyad" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Şifre *</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="******" {...field} />
                  </FormControl>
                  <FormDescription>
                    En az 6 karakter olmalıdır
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="accountType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hesap Türü *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Hesap türü seçin" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="user">Kullanıcı</SelectItem>
                        <SelectItem value="company_owner">Şirket Sahibi</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Kullanıcının hesap türü
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="teamRole"
                render={({ field }) => {
                  const accountType = form.watch('accountType');
                  const isCompanyOwner = accountType === 'company_owner';

                  // Hesap türüne göre rol seçenekleri
                  const getRoleOptions = () => {
                    if (isCompanyOwner) {
                      return [
                        { value: "admin", label: "Admin" }
                      ];
                    } else {
                      return [
                        { value: "team_admin", label: "Takım Yöneticisi" },
                        { value: "tester", label: "Test Uzmanı" },
                        { value: "viewer", label: "Görüntüleyici" }
                      ];
                    }
                  };

                  // Hesap türü değiştiğinde uygun varsayılan rolü ayarla
                  React.useEffect(() => {
                    if (isCompanyOwner && field.value !== "admin") {
                      field.onChange("admin");
                    } else if (!isCompanyOwner && field.value === "admin") {
                      field.onChange("viewer");
                    }
                  }, [accountType, field, isCompanyOwner]);

                  return (
                    <FormItem className="w-full">
                      <FormLabel>Takım Rolü *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || (isCompanyOwner ? "admin" : "viewer")}
                        disabled={isCompanyOwner}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Takım rolü seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {getRoleOptions().map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {isCompanyOwner
                          ? "Şirket Sahibi için Admin rolü otomatik atanır"
                          : "Kullanıcının takım içindeki rolü"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></div>
                    Ekleniyor...
                  </div>
                ) : (
                  "Kullanıcı Ekle"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
