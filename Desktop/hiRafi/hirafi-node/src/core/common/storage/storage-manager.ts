/**
 * Storage Manager - Backward Compatibility Facade
 *
 * Maintains backward compatibility while using the new StorageFactory
 * under the hood. This allows existing code to continue working without changes.
 */

import { ILoggerService, LoggerServiceFactory } from '../../../utils/logger-service.js';
import { config } from '../../../config/index.js';
import { IArtifactStorage } from './artifact-storage.interface.js';
import { StorageFactoryInstance, StorageConfig } from './storage-factory.js';

/**
 * StorageManager - Backward Compatibility Facade
 *
 * Provides the same API as the old singleton while using StorageFactory internally.
 * This maintains backward compatibility for existing code.
 */
export class StorageManager {
  private static instance: StorageManager;
  private storageFactory: StorageFactoryInstance;
  private storageProvider: IArtifactStorage | null = null;
  private readonly initializationLock = new Map<string, Promise<IArtifactStorage>>();
  private logger: ILoggerService;

  /**
   * Private constructor to enforce singleton pattern (backward compatibility)
   */
  private constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('StorageManagerFacade');
    this.storageFactory = new StorageFactoryInstance({
      enableLogging: true,
      defaultProvider: 's3',
      fallbackToNoOp: true
    });
    this.logger.debug('StorageManager: Facade instance created with StorageFactoryInstance');
  }

  /**
   * Get the singleton instance of StorageManager (backward compatibility)
   * @deprecated Use StorageFactory.createStorageProvider() or dependency injection instead
   * @returns The singleton StorageManager instance
   */
  static getInstance(): StorageManager {
    // This is a facade, so it needs its own logger, but we can't get it from the instance yet.
    // We create a temporary static logger for this one-time warning.
    const staticLogger = LoggerServiceFactory.createServiceLogger('StorageManagerFacade');
    staticLogger.warn('StorageManager.getInstance() is deprecated. Use StorageFactory.createStorageProvider() or dependency injection instead.');

    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
      // The instance's own logger will log this message.
      StorageManager.instance.logger.serviceInit('StorageManager', 'Facade instance initialized');
    }
    return StorageManager.instance;
  }

  /**
   * Get a storage provider instance with thread-safe initialization (backward compatibility)
   * @param customConfig Optional configuration overrides
   * @returns Promise resolving to an initialized IArtifactStorage instance
   */
  async getStorageProvider(customConfig: any = {}): Promise<IArtifactStorage> {
    const configKey = JSON.stringify(customConfig);

    // Return existing provider if available and no force reinitialize
    if (this.storageProvider && !customConfig.forceReinitialize) {
      this.logger.debug('StorageManager: Returning existing storage provider');
      return this.storageProvider;
    }

    // Check if initialization is already in progress for this config
    if (this.initializationLock.has(configKey)) {
      this.logger.debug('StorageManager: Waiting for ongoing initialization');
      return this.initializationLock.get(configKey)!;
    }

    // Start new initialization using StorageFactory
    const initPromise = this.initializeStorageProviderWithFactory(customConfig);
    this.initializationLock.set(configKey, initPromise);

    try {
      this.storageProvider = await initPromise;
      this.logger.serviceInit('StorageManager', `Storage provider initialized successfully: ${this.storageProvider.getProviderName()}`);
      return this.storageProvider;
    } catch (error) {
      this.logger.warn(`StorageManager: Failed to initialize storage provider: ${error}`);
      this.logger.warn('StorageManager: Storage will be disabled - artifacts will not be saved');

      // Set storage provider to null to indicate storage is disabled
      this.storageProvider = null;

      // Return a no-op storage that doesn't actually store anything
      return await this.createNoOpStorageFromFactory();
    } finally {
      this.initializationLock.delete(configKey);
    }
  }

  /**
   * Initialize storage provider using StorageFactory (new implementation)
   * @param customConfig Configuration for the storage provider
   * @returns Promise resolving to an initialized storage provider
   */
  private async initializeStorageProviderWithFactory(customConfig: any): Promise<IArtifactStorage> {
    this.logger.serviceInit('StorageManager', 'Initializing storage provider using StorageFactory');

    // Convert legacy config format to new StorageConfig format
    const storageConfig: StorageConfig = this.convertLegacyConfig(customConfig);

    return this.storageFactory.createStorageProvider(storageConfig);
  }

  /**
   * Convert legacy configuration to new StorageConfig format
   * @param legacyConfig Legacy configuration object
   * @returns StorageConfig object
   */
  private convertLegacyConfig(legacyConfig: any): StorageConfig {
    const converted: StorageConfig = { ...legacyConfig };

    // Apply configuration from app config if not provided
    if (!converted.provider) {
      // Determine provider from environment or config
      const minioEnabled = process.env.MINIO_ENABLED === 'true';
      const s3Enabled = process.env.S3_ENABLED === 'true';

      if (s3Enabled) {
        converted.provider = 's3';
      } else if (minioEnabled) {
        converted.provider = 'minio';
      } else {
        converted.provider = 's3'; // default
      }
    }

    return converted;
  }

  /**
   * Create a no-op storage provider using StorageFactory (backward compatibility)
   * @returns No-op storage provider
   */
  private async createNoOpStorageFromFactory(): Promise<IArtifactStorage> {
    // Use the factory's no-op storage creation
    return await (this.storageFactory as any).createNoOpStorage();
  }

  /**
   * Reset the storage provider (backward compatibility)
   */
  resetStorageProvider(): void {
    this.storageProvider = null;
    this.initializationLock.clear();
    this.logger.info('StorageManager: Storage provider reset - next access will create new instance');
  }

  /**
   * Check if a storage provider is currently initialized (backward compatibility)
   * @returns True if a storage provider is available
   */
  isInitialized(): boolean {
    return this.storageProvider !== null;
  }

  /**
   * Get the current storage provider without initialization (backward compatibility)
   * @returns The current storage provider or null if not initialized
   */
  getCurrentProvider(): IArtifactStorage | null {
    return this.storageProvider;
  }
}
