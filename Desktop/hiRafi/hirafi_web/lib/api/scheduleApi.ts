/**
 * Schedule API Service
 * Handles all API calls related to schedules
 */

import { SCHEDULE_ENDPOINTS } from './constants';
import { fetchApi } from './fetch-wrapper';

/**
 * Get all schedules
 * @returns List of schedules
 */
export const getSchedules = async () => {
  return fetchApi(SCHEDULE_ENDPOINTS.GET_ALL);
};

/**
 * Get schedule by ID
 * @param id Schedule ID
 * @returns Schedule details
 */
export const getScheduleById = async (id: string) => {
  try {
    console.log("Fetching schedule with ID:", id);
    const response = await fetchApi(SCHEDULE_ENDPOINTS.GET_BY_ID(id));
    console.log("Raw API response:", response);
    return response;
  } catch (error) {
    console.error("Error in getScheduleById:", error);
    throw error;
  }
};

/**
 * Create a new schedule
 * @param scheduleData Schedule data
 * @returns Created schedule
 */
export const createSchedule = async (scheduleData: any) => {
  // Veri dönü<PERSON>ümü yap
  const modifiedData = { ...scheduleData };

  // Eğer runId varsa, runIds dizisine dönüştür
  if (modifiedData.runId) {
    modifiedData.runIds = [modifiedData.runId];
    delete modifiedData.runId;
  }
  // Eğer runIds varsa ve runId yoksa, runIds'i kullan
  else if (!modifiedData.runIds) {
    modifiedData.runIds = [];
  }

  return fetchApi(SCHEDULE_ENDPOINTS.CREATE, {
    method: 'POST',
    body: modifiedData,
  });
};

/**
 * Update a schedule
 * @param id Schedule ID
 * @param scheduleData Schedule data
 * @returns Updated schedule
 */
export const updateSchedule = async (id: string, scheduleData: any) => {
  // Veri dönüşümü yap
  const modifiedData = { ...scheduleData };

  // Eğer runId varsa, runIds dizisine dönüştür
  if (modifiedData.runId) {
    modifiedData.runIds = [modifiedData.runId];
    delete modifiedData.runId;
  }
  // Eğer runIds varsa ve runId yoksa, runIds'i kullan
  else if (!modifiedData.runIds) {
    modifiedData.runIds = [];
  }

  return fetchApi(SCHEDULE_ENDPOINTS.UPDATE(id), {
    method: 'PUT',
    body: modifiedData,
  });
};

/**
 * Delete a schedule
 * @param id Schedule ID
 * @returns Success status
 */
export const deleteSchedule = async (id: string) => {
  return fetchApi(SCHEDULE_ENDPOINTS.DELETE(id), {
    method: 'DELETE',
  });
};

/**
 * Update schedule status (activate/pause)
 * @param id Schedule ID
 * @param status New status ('active' or 'paused')
 * @returns Success status
 */
export const updateScheduleStatus = async (id: string, status: 'active' | 'paused') => {
  return fetchApi(SCHEDULE_ENDPOINTS.STATUS(id), {
    method: 'PATCH',
    body: { status }, // JSON.stringify kaldırıldı, fetchApi içinde zaten yapılıyor
  });
};

/**
 * Get runs for schedule selection
 * @returns List of runs with scenario details
 */
export const getRunsForSchedule = async () => {
  return fetchApi(SCHEDULE_ENDPOINTS.RUNS);
};
