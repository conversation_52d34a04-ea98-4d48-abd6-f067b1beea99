"use client";

import { useState, useR<PERSON>, useEffect, useMemo, use<PERSON><PERSON>back } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  ChevronUp,
  ChevronDown,
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  Smartphone,
  Globe,
  ServerIcon,
  FolderIcon,
  FileCheck,
  Copy,
  Trash2,
  Edit,
  GripVertical,
  ChevronRight,
  Calendar,
  Tag,
  Search,
  SlidersHorizontal,
  X,
  Loader2,
  Filter
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>ider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import type { Scenario, FolderType, ScenarioStatus } from "@/types/scenario"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { formatDistanceToNow } from "date-fns"
import { tr } from "date-fns/locale"
import { FilterBox } from "./filter-box"

// Extended Scenario type with metadata
interface ScenarioWithMeta extends Scenario {
  lastRun?: string;
  updatedAt?: string;
  testType?: "web" | "mobile" | "api";
  platform?: "web" | "android" | "ios" | "api";
  runStatus?: ScenarioStatus;
  testrailIntegration?: {
    caseIds: string[] | number[];
    sync?: boolean;
  };
  zephyrIntegration?: {
    caseIds: string[] | number[];
    sync?: boolean;
  };
}

// Filter types for scenarios
export interface ScenarioFilters {
  search?: string;
  status?: string[];
  testType?: string[];
  tags?: string[];
  dateRange?: { from?: Date; to?: Date };
  testrailStatus?: "active" | "inactive" | null; // Represents test management status (TestRail/Zephyr Scale)
  sortDate?: "asc" | "desc" | null;
}

export interface ModernTableProps {
  scenarios: ScenarioWithMeta[];
  folders: FolderType[];
  loading?: boolean;
  selectedScenarios: string[];
  onSelectScenario: (id: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  onSort: (column: string) => void;
  sortColumn?: string;
  sortDirection?: "asc" | "desc";
  onEditScenario: (id: string) => void;
  onDeleteScenario: (id: string) => void;
  onDuplicateScenario: (id: string) => void;
  onDragScenario?: (id: string, event: React.DragEvent<Element>) => void;
  onMoveScenario?: (scenarioId: string, targetFolderId: string) => void;
  filters?: ScenarioFilters;
  onFilterChange?: (filters: ScenarioFilters) => void;
  onAddScenario?: () => void;
  onImportScenarios?: () => void;
  selectedFolder?: string | null;
  onClearSelectedFolder?: () => void;
  // Batch operations
  onBulkDeleteScenarios?: (scenarioIds: string[]) => void;
  onBulkDuplicateScenarios?: (
    scenarioIds: string[],
    options?: { namePrefix?: string; folderId?: string },
  ) => void;
}

export function ModernTable({
  scenarios,
  folders,
  loading,
  selectedScenarios,
  onSelectScenario,
  onSelectAll,
  sortColumn,
  sortDirection,
  onSort,
  onEditScenario,
  onDuplicateScenario,
  onDeleteScenario,
  onDragScenario,
  onMoveScenario,
  filters,
  onFilterChange,
  onAddScenario,
  onImportScenarios,
  selectedFolder,
  onClearSelectedFolder,
  onBulkDeleteScenarios,
  onBulkDuplicateScenarios,
}: ModernTableProps) {
  // Memoize selection calculations to prevent unnecessary re-renders
  const allSelected = useMemo(
    () => scenarios.length > 0 && selectedScenarios.length === scenarios.length,
    [scenarios.length, selectedScenarios.length],
  );

  const someSelected = useMemo(
    () =>
      selectedScenarios.length > 0 &&
      selectedScenarios.length < scenarios.length,
    [selectedScenarios.length, scenarios.length],
  );

  const [draggedScenarioId, setDraggedScenarioId] = useState<string | null>(
    null,
  );
  const tableRef = useRef<HTMLDivElement>(null);

  // Memoize folder lookup to prevent repeated searches
  const folderMap = useMemo(() => {
    return folders.reduce(
      (map, folder) => {
        map[folder.id] = folder;
        return map;
      },
      {} as Record<string, FolderType>,
    );
  }, [folders]);

  const getFolderWithIcon = useCallback(
    (folderId: string) => {
      const folder = folderMap[folderId];
      const folderName = folder?.name || "Kategorisiz";
      const folderColor = folder?.color || "#6b7280";

      return (
        <div className="flex items-center gap-2">
          <FolderIcon className="h-4 w-4" style={{ color: folderColor }} />
          <span>{folderName}</span>
        </div>
      );
    },
    [folderMap],
  );

  const getTestRailBadge = useCallback((scenario: ScenarioWithMeta) => {
    if (
      scenario.testrailIntegration &&
      scenario.testrailIntegration.caseIds &&
      scenario.testrailIntegration.caseIds.length > 0
    ) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800"
              >
                <FileCheck className="h-3 w-3 mr-1" />
                {scenario.testrailIntegration.caseIds.length > 1
                  ? `${scenario.testrailIntegration.caseIds.length} Cases`
                  : `C${scenario.testrailIntegration.caseIds[0]}`}
              </Badge>
            </TooltipTrigger>
            <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
              <p className="text-xs">
                TestRail Cases:{" "}
                {scenario.testrailIntegration.caseIds
                  .map((id) => `C${id}`)
                  .join(", ")}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return null;
  }, []);

  const getStatusBadge = useCallback((scenario: ScenarioWithMeta) => {
    // Use string comparison for flexibility with status values
    const status = scenario.runStatus || scenario.status || "unknown";
    const statusStr = status.toString().toLowerCase();

    switch (statusStr) {
      case "passed":
      case "success":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/50">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Başarılı
          </Badge>
        );
      case "failed":
      case "error":
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/50">
            <XCircle className="h-3 w-3 mr-1" />
            Başarısız
          </Badge>
        );
      case "running":
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/50">
            <Clock className="h-3 w-3 mr-1 animate-spin" />
            Çalışıyor
          </Badge>
        );
      case "pending":
      case "queue":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-800 dark:hover:bg-yellow-900/50">
            <Clock className="h-3 w-3 mr-1" />
            Sırada
          </Badge>
        );
      case "inactive":
      case "passive":
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700">
            <AlertCircle className="h-3 w-3 mr-1" />
            Pasif
          </Badge>
        );
      case "active":
      case "new":
        return (
          <Badge className="bg-green-50 text-green-700 border-green-100 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400 dark:border-green-900 dark:hover:bg-green-900/40">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Aktif
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700">
            {status || "Bilinmiyor"}
          </Badge>
        );
    }
  }, []);

  const getTestTypeIcon = (scenario: ScenarioWithMeta) => {
    const testType = scenario.testType || "web";
    const platform = scenario.platform || "web";

    // For mobile test type or android/ios platforms
    if (testType === "mobile" || platform === "android" || platform === "ios") {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center">
                <Badge
                  variant="outline"
                  className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800"
                >
                  <Smartphone className="h-3 w-3 mr-1" />
                  <span className="text-xs">Mobil</span>
                </Badge>
              </div>
            </TooltipTrigger>
            <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
              <p className="text-xs">
                Mobil Test ({platform === "ios" ? "iOS" : "Android"})
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    // For API test type
    if (testType === "api" || platform === "api") {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center">
                <Badge
                  variant="outline"
                  className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800"
                >
                  <ServerIcon className="h-3 w-3 mr-1" />
                  <span className="text-xs">API</span>
                </Badge>
              </div>
            </TooltipTrigger>
            <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
              <p className="text-xs">API Test</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    // Default to web for all other cases
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center">
              <Badge
                variant="outline"
                className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800"
              >
                <Globe className="h-3 w-3 mr-1" />
                <span className="text-xs">Web</span>
              </Badge>
            </div>
          </TooltipTrigger>
          <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
            <p className="text-xs">Web Test</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const formatDate = (dateString: string | undefined | null): string => {
    if (!dateString) return "Hiç";
    if (dateString === "") return "Hiç";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return "Hiç";
      }

      // Şu andan ne kadar önce olduğunu göster
      return formatDistanceToNow(date, { addSuffix: true, locale: tr });
    } catch (error) {
      return "Hiç";
    }
  };

  const renderSortIndicator = (column: string) => {
    if (sortColumn !== column) return null;
    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDown className="h-4 w-4 ml-1" />
    );
  };

  // State for collapsed folders
  const [collapsedFolders, setCollapsedFolders] = useState<Set<string>>(
    new Set(),
  );

  // Toggle folder collapse state
  const toggleFolderCollapse = (folderId: string) => {
    setCollapsedFolders((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });

    // Re-calculate pagination on next render
    // This is important because collapsing/expanding folders affects how many folders fit on a page
    setTimeout(() => {
      // Check if we need to adjust the current page
      // This is needed if collapsing folders causes the current page to become empty
      if (folderPages.length > 0 && currentPage > folderPages.length) {
        setCurrentPage(Math.max(1, folderPages.length));
      }
    }, 0);
  };

  // Group scenarios by folder
  const scenariosByFolder = scenarios.reduce(
    (acc, scenario) => {
      // Normalize invalid folder IDs to uncategorized
      let folderId = scenario.folderId;
      if (
        !folderId ||
        folderId === "" ||
        folderId === "none" ||
        folderId === "1"
      ) {
        folderId = "uncategorized";
      }

      if (!acc[folderId]) {
        acc[folderId] = [];
      }
      acc[folderId].push(scenario);
      return acc;
    },
    {} as Record<string, ScenarioWithMeta[]>,
  );

  // Get folder for display
  const getFolderForDisplay = (folderId: string) => {
    const folder = folders.find((f) => f.id === folderId);
    return (
      folder || { id: "uncategorized", name: "Kategorisiz", color: "#6b7280" }
    );
  };

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const pageSizeOptions = [10, 20, 50, 100];

  // Sort folders by name
  const sortedFolderEntries = Object.entries(scenariosByFolder)
    .filter(([_, folderScenarios]) => folderScenarios.length > 0)
    .sort(([folderIdA], [folderIdB]) => {
      const folderA = getFolderForDisplay(folderIdA);
      const folderB = getFolderForDisplay(folderIdB);
      return folderA.name.localeCompare(folderB.name);
    });

  // Calculate total scenarios and scenarios per page
  const totalScenarios = scenarios.length;

  // Create a structure that tracks which folders should be displayed on each page
  // This ensures that all scenarios from the same folder stay together
  const folderPages: Array<Array<[string, ScenarioWithMeta[]]>> = [];
  let currentPageScenarios = 0;
  let currentPageFolders: Array<[string, ScenarioWithMeta[]]> = [];

  // Group folders into pages, ensuring all scenarios from a folder stay together
  // Take into account whether folders are collapsed
  sortedFolderEntries.forEach((folderEntry) => {
    const [folderId, folderScenarios] = folderEntry;

    // Calculate effective scenario count based on collapse state
    // If folder is collapsed, count it as 1 scenario (just the header)
    const effectiveScenarioCount = collapsedFolders.has(folderId)
      ? 1
      : folderScenarios.length;

    // If adding this folder would exceed page size and we already have folders on this page,
    // start a new page (unless this is the first folder on the first page)
    if (
      currentPageScenarios + effectiveScenarioCount > pageSize &&
      currentPageFolders.length > 0
    ) {
      folderPages.push([...currentPageFolders]);
      currentPageFolders = [];
      currentPageScenarios = 0;
    }

    // Add folder to current page
    currentPageFolders.push(folderEntry);
    currentPageScenarios += effectiveScenarioCount;
  });

  // Add the last page if it has any folders
  if (currentPageFolders.length > 0) {
    folderPages.push(currentPageFolders);
  }

  // Calculate total pages based on our folder grouping
  const totalPages = folderPages.length;

  // Ensure current page is valid
  const validCurrentPage = Math.min(
    Math.max(1, currentPage),
    Math.max(1, totalPages),
  );
  if (validCurrentPage !== currentPage) {
    setCurrentPage(validCurrentPage);
  }

  // Get folders for the current page
  const paginatedFolderEntries = folderPages[validCurrentPage - 1] || [];

  // Create a flat list of scenarios from paginated folders for selection logic
  const paginatedScenarios = paginatedFolderEntries.flatMap(
    ([folderId, folderScenarios]) => folderScenarios,
  );

  // Calculate the actual number of visible scenarios (taking into account collapsed folders)
  const visibleScenariosCount = paginatedFolderEntries.reduce(
    (count, [folderId, folderScenarios]) => {
      return (
        count + (collapsedFolders.has(folderId) ? 0 : folderScenarios.length)
      );
    },
    0,
  );

  // Local filter state
  const [localFilters, setLocalFilters] = useState<ScenarioFilters>(
    filters || {},
  );

  // Update filters - immediate for better UX
  const handleFilterChange = (key: keyof ScenarioFilters, value: any) => {
    console.log(`[ModernTable] Updating filter ${key} to:`, value);
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);

    // Update immediately for all filters
    if (onFilterChange) {
      onFilterChange(newFilters);
    }
  };

  // Handle folder drop for moving scenarios
  const handleFolderDrop = (
    e: React.DragEvent<HTMLDivElement>,
    targetFolderId: string,
  ) => {
    e.preventDefault();
    if (e.dataTransfer && draggedScenarioId && onMoveScenario) {
      // Uygulama verisinde senaryoyu taşı
      onMoveScenario(draggedScenarioId, targetFolderId);

      // Kullanıcıya görsel geri bildirim için animasyon
      const element = document.getElementById(`scenario-${draggedScenarioId}`);
      if (element) {
        element.classList.add("animate-move-success");
        setTimeout(() => {
          element.classList.remove("animate-move-success");
        }, 1000);
      }

      // Taşıma işlemi tamamlandı
      setDraggedScenarioId(null);

      // Hedef klasör kapalıysa aç
      if (collapsedFolders.has(targetFolderId)) {
        toggleFolderCollapse(targetFolderId);
      }
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Re-calculate pagination when collapsed folders change
  useEffect(() => {
    // If the current page is now invalid due to folder collapse changes,
    // adjust to the last valid page
    if (folderPages.length > 0 && currentPage > folderPages.length) {
      setCurrentPage(Math.max(1, folderPages.length));
    }
  }, [collapsedFolders, folderPages.length, currentPage]);

  // Update local filters when props change (for reset/clear operations)
  useEffect(() => {
    // If filters prop is empty but localFilters has values, reset local state
    if (
      filters &&
      Object.keys(filters).length === 0 &&
      Object.keys(localFilters).some(
        (key) => localFilters[key as keyof ScenarioFilters],
      )
    ) {
      console.log(
        "ModernTable: Detected filters cleared from outside, resetting local filters",
      );
      setLocalFilters({});

      // If selectedFolder is set and we have a way to clear it, do so
      if (selectedFolder && onClearSelectedFolder) {
        console.log(
          "ModernTable: Clearing selectedFolder as part of external filter reset",
        );
        onClearSelectedFolder();
      }
    }
  }, [filters, localFilters, selectedFolder, onClearSelectedFolder]);

  return (
    <div
      className="w-full bg-white dark:bg-gray-900 rounded-lg shadow-sm overflow-hidden relative"
      ref={tableRef}
    >
      {/* Batch Operations Bar - appears when scenarios are selected */}
      {selectedScenarios.length > 0 && (
        <div className="absolute top-0 left-0 right-0 z-50 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-blue-900 dark:text-blue-300">
                {selectedScenarios.length} senaryo seçildi
              </span>
            </div>
            <div className="flex items-center gap-2">
              {onBulkDuplicateScenarios && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() =>
                    onBulkDuplicateScenarios(selectedScenarios, {
                      namePrefix: "Copy",
                    })
                  }
                  className="bg-white dark:bg-gray-800 border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/30"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </Button>
              )}
              {onBulkDeleteScenarios && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onBulkDeleteScenarios(selectedScenarios)}
                  className="bg-white dark:bg-gray-800 border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              )}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onSelectAll(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      <AnimatePresence>
        <div
          className="overflow-visible"
          style={{ marginTop: selectedScenarios.length > 0 ? "60px" : "0" }}
        >
          {/* Enhanced Filter Bar */}
          <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 p-4">
            <div className="flex flex-col gap-4">
              {/* Top Row - Search and Main Filters */}
              {/* Use the new FilterBox component */}
              <FilterBox
                searchQuery={localFilters.search || ""}
                onSearchChange={(query) => handleFilterChange("search", query)}
                filters={localFilters}
                onFilterChange={(newFilters) => {
                  // Check if this is a clear all filters operation (empty object)
                  const isClearingAllFilters =
                    Object.keys(newFilters).length === 0;

                  // Immediately update local state
                  setLocalFilters(newFilters);

                  // Clear selected folder if needed
                  if (
                    isClearingAllFilters &&
                    selectedFolder &&
                    onClearSelectedFolder
                  ) {
                    console.log(
                      "ModernTable: Clearing selectedFolder as part of clear all filters",
                    );
                    onClearSelectedFolder();
                  }

                  // Pass the changes up to parent component
                  if (onFilterChange) {
                    onFilterChange(newFilters);
                  }
                }}
                folders={folders}
                availableTags={scenarios
                  .flatMap((s) => s.tags || [])
                  .filter(
                    (value, index, self) => self.indexOf(value) === index,
                  )}
              />

              {/* No bottom row filters - all moved to the FilterBox component */}
            </div>
          </div>

          {/* Table Header */}
          <div className="bg-gray-50 dark:bg-gray-900/60 sticky top-0 z-10 border-b border-gray-200 dark:border-gray-800 py-2 px-4 flex items-center gap-2">
            <Checkbox
              checked={allSelected}
              ref={(input: any) => {
                if (input) {
                  input.indeterminate = someSelected;
                }
              }}
              onCheckedChange={onSelectAll}
              aria-label="Tüm senaryoları seç"
              className="dark:border-gray-600"
            />
            <div className="grid grid-cols-7 flex-1 text-sm font-medium text-gray-500 dark:text-gray-400 pl-8">
              <div
                className="cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 transition-colors flex items-center col-span-2"
                onClick={() => onSort("name")}
              >
                Senaryo {renderSortIndicator("name")}
              </div>
              <div className="flex items-center pl-2">Tip</div>
              <div
                className="flex items-center cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                onClick={() => onSort("status")}
              >
                Durum {renderSortIndicator("status")}
              </div>
              <div className="flex items-center">TestRail</div>
              <div
                className="cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 transition-colors flex items-center pl-2"
                onClick={() => onSort("lastRun")}
              >
                Güncelleme {renderSortIndicator("lastRun")}
              </div>
              <div className="text-right">İşlemler</div>
            </div>
          </div>

          {/* Content */}
          <div className="min-h-[300px]">
            {loading ? (
              <div className="py-8 text-center">
                <div className="flex flex-col items-center py-8">
                  <div className="text-indigo-400 dark:text-indigo-500 mb-2 animate-spin">
                    <Loader2 className="h-12 w-12" />
                  </div>
                  <p className="text-lg font-medium text-gray-600 dark:text-gray-400">
                    Senaryolar yükleniyor...
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500">
                    Lütfen bekleyin...
                  </p>
                </div>
              </div>
            ) : paginatedFolderEntries.length === 0 ? (
              <div className="py-8 text-center">
                <div className="flex flex-col items-center py-8">
                  <div className="text-gray-400 dark:text-gray-500 mb-2">
                    <FolderIcon className="h-12 w-12" />
                  </div>
                  <p className="text-lg font-medium text-gray-600 dark:text-gray-400">
                    Senaryo bulunamadı
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500">
                    Bu klasörde senaryo yok ya da filtrelere uygun senaryo
                    bulunamadı.
                  </p>
                </div>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-800">
                {paginatedFolderEntries.map(([folderId, folderScenarios]) => {
                  const folder = getFolderForDisplay(folderId);
                  // All scenarios in this folder should be shown (no filtering by page)
                  const scenariosToShow = folderScenarios;

                  return (
                    <div key={folderId} className="space-y-1">
                      {/* Folder Header */}
                      <div
                        className={cn(
                          "py-2 px-4 flex items-center cursor-pointer transition-colors",
                          draggedScenarioId
                            ? "bg-blue-50/50 dark:bg-blue-900/10 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            : "bg-gray-50 dark:bg-gray-800/40 hover:bg-gray-100 dark:hover:bg-gray-800/60",
                        )}
                        style={{ borderLeft: `4px solid ${folder.color}` }}
                        onClick={() => {
                          // Prevent onClick when dragging, only toggle folder collapse on normal click
                          if (!draggedScenarioId) {
                            toggleFolderCollapse(folderId);
                          }
                        }}
                        onDragOver={(e) => {
                          // Allow dropping
                          e.preventDefault();
                          // Expand folder when dragging over it
                          if (collapsedFolders.has(folderId)) {
                            toggleFolderCollapse(folderId);
                          }
                        }}
                        onDrop={(e) => handleFolderDrop(e, folderId)}
                      >
                        <div className="mr-1 text-gray-500 flex items-center justify-center h-5 w-5">
                          {collapsedFolders.has(folderId) ? (
                            <ChevronRight className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </div>
                        <FolderIcon
                          className="h-4 w-4 mr-2 flex-shrink-0"
                          style={{ color: folder.color }}
                        />
                        <div className="flex-1 font-medium text-gray-700 dark:text-gray-300 flex items-center justify-between">
                          <span>
                            {folder.name}{" "}
                            <span className="text-xs text-gray-500">
                              ({folderScenarios.length})
                            </span>
                          </span>
                          {draggedScenarioId && (
                            <span className="text-xs text-blue-600 dark:text-blue-400 flex items-center">
                              <span className="mr-1">Buraya bırak</span>
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Scenarios */}
                      <div className="space-y-0.5 px-2 pt-1 pb-2">
                        {!collapsedFolders.has(folderId) &&
                          scenariosToShow.map((scenario) => (
                            <motion.div
                              id={`scenario-${scenario.id}`}
                              key={scenario.id}
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.15 }}
                              className={cn(
                                "flex items-center px-2 py-1.5 rounded hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors gap-1.5",
                                selectedScenarios.includes(scenario.id) &&
                                  "bg-blue-50 dark:bg-blue-900/20",
                                draggedScenarioId === scenario.id &&
                                  "bg-blue-100 dark:bg-blue-900/30 ring-1 ring-blue-400 dark:ring-blue-600 opacity-70",
                                // Not: Tailwind config'e @keyframes ve animate-move-success eklenmeli:
                                // @keyframes moveSuccess { 0% { background-color: transparent; } 50% { background-color: rgba(34, 197, 94, 0.2); } 100% { background-color: transparent; } }
                                // .animate-move-success { animation: moveSuccess 1s ease-in-out; }
                              )}
                            >
                              <Checkbox
                                checked={selectedScenarios.includes(
                                  scenario.id,
                                )}
                                onCheckedChange={(checked) =>
                                  onSelectScenario(scenario.id, !!checked)
                                }
                                aria-label={`Select scenario ${scenario.name}`}
                                className="dark:border-gray-600 h-4 w-4"
                              />

                              {/* Drag Handle */}
                              <div
                                className="cursor-grab hover:bg-gray-200 dark:hover:bg-gray-700 h-6 w-6 rounded flex items-center justify-center text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                                draggable
                                onDragStart={(e) => {
                                  if (onDragScenario) {
                                    if (e.dataTransfer) {
                                      e.dataTransfer.setData(
                                        "text/plain",
                                        scenario.id,
                                      );
                                      e.dataTransfer.effectAllowed = "move";
                                    }
                                    setDraggedScenarioId(scenario.id);
                                    onDragScenario(
                                      scenario.id,
                                      e as unknown as React.DragEvent<Element>,
                                    );
                                  }
                                }}
                                onDragEnd={() => {
                                  setDraggedScenarioId(null);
                                }}
                              >
                                <GripVertical className="h-3.5 w-3.5" />
                              </div>

                              {/* Scenario Grid Layout */}
                              <div className="grid grid-cols-7 flex-1 items-center text-sm">
                                {/* Name + Description */}
                                <div className="flex flex-col pr-2 col-span-2">
                                  <div className="font-medium text-gray-900 dark:text-gray-200 text-sm truncate">
                                    {scenario.name}
                                  </div>
                                  {scenario.description && (
                                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[220px]">
                                      {scenario.description}
                                    </div>
                                  )}
                                  {scenario.tags &&
                                    scenario.tags.length > 0 && (
                                      <div className="flex flex-wrap gap-1 mt-0.5">
                                        {scenario.tags
                                          .slice(0, 2)
                                          .map((tag) => (
                                            <Badge
                                              key={tag}
                                              variant="outline"
                                              className="text-[10px] px-1 py-0 h-4 bg-gray-50 dark:bg-gray-800/50"
                                            >
                                              {tag}
                                            </Badge>
                                          ))}
                                        {scenario.tags.length > 2 && (
                                          <Badge
                                            variant="outline"
                                            className="text-[10px] px-1 py-0 h-4 bg-gray-50 dark:bg-gray-800/50"
                                          >
                                            +{scenario.tags.length - 2}
                                          </Badge>
                                        )}
                                      </div>
                                    )}
                                </div>

                                {/* Test Type */}
                                <div className="pl-2">
                                  {getTestTypeIcon(scenario)}
                                </div>

                                {/* Status */}
                                <div className="flex justify-start">
                                  {getStatusBadge(scenario)}
                                </div>

                                {/* TestRail */}
                                <div>{getTestRailBadge(scenario)}</div>

                                {/* Last Updated */}
                                <div className="pl-2">
                                  {scenario.runStatus === "running" ? (
                                    <span className="text-xs text-blue-500 dark:text-blue-400 flex items-center">
                                      <Clock className="h-3 w-3 mr-1 animate-spin" />
                                      Çalışıyor...
                                    </span>
                                  ) : (
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      {formatDate(
                                        scenario.updatedAt || scenario.lastRun,
                                      )}
                                    </span>
                                  )}
                                </div>

                                {/* Actions */}
                                <div className="flex items-center justify-end gap-1 col-span-1">
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    onClick={() => onEditScenario(scenario.id)}
                                    className="h-6 w-6 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                                  >
                                    <Edit className="h-3.5 w-3.5" />
                                  </Button>
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    onClick={() =>
                                      onDuplicateScenario(scenario.id)
                                    }
                                    className="h-6 w-6 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                                  >
                                    <Copy className="h-3.5 w-3.5" />
                                  </Button>
                                  <Button
                                    size="icon"
                                    variant="ghost"
                                    onClick={() =>
                                      onDeleteScenario(scenario.id)
                                    }
                                    className="h-6 w-6 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-500"
                                  >
                                    <Trash2 className="h-3.5 w-3.5" />
                                  </Button>
                                </div>
                              </div>
                            </motion.div>
                          ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Pagination Controls */}
          <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 py-3 sm:px-6">
            <div className="flex flex-1 justify-between sm:hidden">
              <div>
                <p className="text-xs text-gray-700 dark:text-gray-300">
                  {currentPage}/{totalPages} sayfa ({visibleScenariosCount}{" "}
                  senaryo)
                </p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                >
                  Önceki
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    handlePageChange(Math.min(totalPages, currentPage + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                >
                  Sonraki
                </Button>
              </div>
            </div>
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Toplam <span className="font-medium">{totalScenarios}</span>{" "}
                  senaryodan{" "}
                  <span className="font-medium">{visibleScenariosCount}</span>{" "}
                  senaryo gösteriliyor (
                  <span className="font-medium">
                    {paginatedFolderEntries.length}
                  </span>{" "}
                  klasör)
                </p>
              </div>
              <div className="flex items-center space-x-4">
                {/* Page Size Selection */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Sayfa başına:
                  </span>
                  <select
                    value={pageSize}
                    onChange={(e) => {
                      setPageSize(Number(e.target.value));
                      setCurrentPage(1); // Reset to first page when changing page size
                    }}
                    className="h-8 rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-300 px-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
                  >
                    {pageSizeOptions.map((size) => (
                      <option key={size} value={size}>
                        {size}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Pagination Buttons */}
                <nav
                  className="isolate inline-flex -space-x-px rounded-md shadow-sm"
                  aria-label="Pagination"
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                    className="rounded-l-md dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 px-2 pagination-first-page"
                  >
                    İlk
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 rounded-none px-2"
                  >
                    Önceki
                  </Button>

                  {/* Page Numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      // Less than 5 pages, show all
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      // Near start
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      // Near end
                      pageNum = totalPages - 4 + i;
                    } else {
                      // Middle
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={
                          currentPage === pageNum ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className={cn(
                          "rounded-none w-8 px-0",
                          currentPage === pageNum
                            ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
                            : "dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300",
                        )}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 rounded-none px-2"
                  >
                    Sonraki
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage === totalPages}
                    className="rounded-r-md dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 px-2"
                  >
                    Son
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </AnimatePresence>
    </div>
  );
}
