"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { But<PERSON> } from "@/components/ui/button";
import {
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  FileText,
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  Smartphone,
  Globe,
  ServerIcon,
  FolderIcon,
  ChevronRight,
  Pencil,
  Copy,
  Trash2,
  GripVertical,
  FileCheck
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import type { <PERSON><PERSON><PERSON>, FolderType, ScenarioStatus } from "@/types/scenario"

// Extended Scenario type with metadata
interface ScenarioWithMeta extends Scenario {
  lastRun?: string;
  updatedAt?: string;
  testType?: 'web' | 'mobile' | 'api';
  runStatus?: ScenarioStatus; // Added runStatus field to display current run status
}

interface GroupedScenariosTableProps {
  scenarios: ScenarioWithMeta[];
  folders: FolderType[];
  selectedScenarios: string[];
  selectedFolder: string | null;
  onSelectScenario: (id: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  sortColumn: string;
  sortDirection: "asc" | "desc";
  onSort: (column: string) => void;
  onEditScenario: (id: string) => void;
  onDuplicateScenario: (id: string) => void;
  onDeleteScenario: (id: string) => void;
  onToggleFolder: (folderId: string) => void;
  onDragScenario?: (scenarioId: string, e: React.DragEvent) => void;
  onSelectFolderScenarios?: (folderId: string, selected: boolean) => void;
  onDropOnFolder?: (folderId: string) => void;
}

export function GroupedScenariosTable({
  scenarios,
  folders,
  selectedScenarios,
  selectedFolder,
  onSelectScenario,
  onSelectAll,
  sortColumn,
  sortDirection,
  onSort,
  onEditScenario,
  onDuplicateScenario,
  onDeleteScenario,
  onToggleFolder,
  onDragScenario,
  onSelectFolderScenarios,
  onDropOnFolder,
}: GroupedScenariosTableProps) {
  const allSelected =
    scenarios.length > 0 && selectedScenarios.length === scenarios.length;
  const someSelected =
    selectedScenarios.length > 0 && selectedScenarios.length < scenarios.length;

  // Local state to track folder open/close state
  const [openFolders, setOpenFolders] = useState<Record<string, boolean>>({});

  // State to track which folder is being dragged over
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null);

  // State to track which scenario is being dragged
  const [draggedScenarioId, setDraggedScenarioId] = useState<string | null>(
    null,
  );

  // Initialize open folders based on folders prop - all folders open by default
  useEffect(() => {
    const initialOpenState: Record<string, boolean> = {};
    folders.forEach((folder) => {
      // Set all folders to be open by default (true)
      initialOpenState[folder.id] =
        folder.isOpen !== undefined ? folder.isOpen : true;
    });
    setOpenFolders(initialOpenState);
  }, [folders]);

  // Group scenarios by folder
  const scenariosByFolder: Record<string, ScenarioWithMeta[]> = {};

  // Initialize with all folders (even empty ones)
  folders.forEach((folder) => {
    scenariosByFolder[folder.id] = [];
  });

  // Add uncategorized folder if not already present
  if (!scenariosByFolder["uncategorized"]) {
    scenariosByFolder["uncategorized"] = [];
  }

  // Group scenarios
  scenarios.forEach((scenario) => {
    // Default to uncategorized if folderId is missing, empty, or "none"
    let folderId = scenario.folderId;

    // Normalize invalid folder IDs to uncategorized
    if (
      !folderId ||
      folderId === "" ||
      folderId === "none" ||
      folderId === "1"
    ) {
      folderId = "uncategorized";
    }

    // Check if the folder exists in our folders list
    const folderExists = folders.some((folder) => folder.id === folderId);

    // If folder doesn't exist, put in uncategorized
    if (!folderExists && folderId !== "uncategorized") {
      folderId = "uncategorized";
    }

    // Ensure the folder array exists
    if (!scenariosByFolder[folderId]) {
      scenariosByFolder[folderId] = [];
    }

    // Add scenario to the appropriate folder
    scenariosByFolder[folderId].push(scenario);
  });

  // Helper function to get status badge
  const getStatusBadge = (scenario: ScenarioWithMeta) => {
    // Use runStatus if available, otherwise fall back to status
    let status = scenario.runStatus || scenario.status;

    // "completed" durumunu "passed" olarak işle
    if (typeof status === 'string' && status.toLowerCase() === 'completed') {
      status = "passed" as ScenarioStatus;
    }

    switch (status) {
      case "passed":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/50 py-0 h-4 text-xs font-normal">
            <CheckCircle2 className="h-2.5 w-2.5 mr-0.5" />
            Passed
          </Badge>
        )
      case "failed":
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/50 py-0 h-4 text-xs font-normal">
            <XCircle className="h-2.5 w-2.5 mr-0.5" />
            Failed
          </Badge>
        )
      case "running":
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/50 py-0 h-4 text-xs font-normal">
            <Clock className="h-2.5 w-2.5 mr-0.5" />
            Running
          </Badge>
        )
      case "queue":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-800 dark:hover:bg-yellow-900/50 py-0 h-4 text-xs font-normal">
            <Clock className="h-2.5 w-2.5 mr-0.5" />
            Queued
          </Badge>
        )
      case "inactive":
      case "passive":
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700 py-0 h-4 text-xs font-normal">
            <AlertCircle className="h-2.5 w-2.5 mr-0.5" />
            Inactive
          </Badge>
        )
      case "active":
      case "new":
        return (
          <Badge className="bg-green-50 text-green-700 border-green-100 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400 dark:border-green-900 dark:hover:bg-green-900/40 py-0 h-4 text-xs font-normal">
            <CheckCircle2 className="h-2.5 w-2.5 mr-0.5" />
            Active
          </Badge>
        )
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700 py-0 h-4 text-xs font-normal">
            {status || "Unknown"}
          </Badge>
        )
    }
  }

  // Helper function to get TestRail integration badge
  const getTestRailBadge = (scenario: ScenarioWithMeta) => {
    // Check if scenario has TestRail integration
    if (scenario.testrailIntegration &&
        scenario.testrailIntegration.caseIds &&
        scenario.testrailIntegration.caseIds.length > 0) {

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800 py-0 h-4 text-xs font-normal">
                <FileCheck className="h-2.5 w-2.5 mr-0.5" />
                {scenario.testrailIntegration.caseIds.length > 1
                  ? `${scenario.testrailIntegration.caseIds.length} Cases`
                  : `C${scenario.testrailIntegration.caseIds[0]}`}
              </Badge>
            </TooltipTrigger>
            <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
              <p className="text-xs">
                TestRail Cases: {scenario.testrailIntegration.caseIds.map(id => `C${id}`).join(', ')}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return null;
  }

  // Helper function to get test type icon
  const getTestTypeIcon = (scenario: ScenarioWithMeta) => {
    // Check if scenario has testType property
    const testType = scenario.testType || 'web'

    switch (testType) {
      case 'mobile':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800 py-0 h-4 text-xs font-normal">
                  <Smartphone className="h-2.5 w-2.5 mr-0.5" />
                  Mobile
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
                <p className="text-xs">Mobile Test</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      case 'api':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800 py-0 h-4 text-xs font-normal">
                  <ServerIcon className="h-2.5 w-2.5 mr-0.5" />
                  API
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
                <p className="text-xs">API Test</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800 py-0 h-4 text-xs font-normal">
                  <Globe className="h-2.5 w-2.5 mr-0.5" />
                  Web
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
                <p className="text-xs">Web Test</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
    }
  }

  // Helper function to render sort indicator
  const renderSortIndicator = (column: string) => {
    if (sortColumn !== column) return null;

    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDown className="h-4 w-4 ml-1" />
    );
  };

  // Helper function to safely format dates
  const formatDate = (dateString: string | undefined | null): string => {
    if (!dateString) return "Never";

    // Handle empty strings
    if (dateString === "") return "Never";

    try {
      // Try to parse the date string
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date format received: ${dateString}`);
        return "Never"; // Return "Never" instead of "Invalid Date" for better UX
      }

      // Format date as "YYYY-MM-DD HH:MM"
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error(
        "Error formatting date:",
        error,
        "for dateString:",
        dateString,
      );
      return "Never"; // Return "Never" instead of "Invalid Date" for better UX
    }
  };

  return (
    <div className="rounded-sm border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-900">
      <Table className="border-collapse dark:text-gray-300">
        <TableHeader className="bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
          <TableRow className="hover:bg-gray-50 dark:hover:bg-gray-800">
            <TableHead className="w-[30px] text-center py-2 px-2">
              <div className="flex justify-center">
                <Checkbox
                  checked={allSelected}
                  ref={(input: any) => {
                    if (input?.input) {
                      input.input.indeterminate = someSelected;
                    }
                  }}
                  onCheckedChange={onSelectAll}
                  aria-label="Select all scenarios"
                  className="h-3.5 w-3.5 rounded-sm border-gray-300 dark:border-gray-600 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                />
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 py-2 px-2 text-xs font-medium text-gray-600 dark:text-gray-300 w-[60px]"
              onClick={() => onSort("id")}
            >
              <div className="flex items-center">
                ID
                {renderSortIndicator("id")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 py-2 px-2 text-xs font-medium text-gray-600 dark:text-gray-300 w-[40%]"
              onClick={() => onSort("name")}
            >
              <div className="flex items-center">
                Name
                {renderSortIndicator("name")}
              </div>
            </TableHead>
            <TableHead className="py-2 px-2 text-xs font-medium text-gray-600 dark:text-gray-300 w-[80px]">
              Type
            </TableHead>
            <TableHead className="py-2 px-2 text-xs font-medium text-gray-600 dark:text-gray-300 w-[80px]">
              TestRail
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 py-2 px-2 text-xs font-medium text-gray-600 dark:text-gray-300 w-[100px]"
              onClick={() => onSort("status")}
            >
              <div className="flex items-center">
                Status
                {renderSortIndicator("status")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 py-2 px-2 text-xs font-medium text-gray-600 dark:text-gray-300 w-[120px]"
              onClick={() => onSort("lastRun")}
            >
              <div className="flex items-center">
                Last Updated
                {renderSortIndicator("lastRun")}
              </div>
            </TableHead>
            <TableHead className="w-[100px] text-right py-2 px-2 text-xs font-medium text-gray-600 dark:text-gray-300">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Object.keys(scenariosByFolder).length === 0 ? (
            <TableRow className="dark:border-gray-800">
              <TableCell
                colSpan={8}
                className="h-24 text-center text-sm text-gray-500 dark:text-gray-400"
              >
                No scenarios found.
              </TableCell>
            </TableRow>
          ) : (
            // Filter folders to only show the selected folder or all folders if no folder is selected
            Object.entries(scenariosByFolder)
              .filter(([folderId, folderScenarios]) => {
                // If no folder is selected, show all folders that have scenarios
                if (!selectedFolder) {
                  // Status filtrelemesi yapıldığında boş klasörleri gösterme
                  if (folderScenarios.length === 0) {
                    return false;
                  }
                  return true;
                }

                // If a folder is selected, only show that folder
                return folderId === selectedFolder;
              })
              .map(([folderId, folderScenarios]) => {
                // Don't skip empty folders anymore
                // if (folderScenarios.length === 0) return null;

                // Find the folder or default to Uncategorized
                const folder = folders.find((f) => f.id === folderId);
                // If folder not found, treat as Uncategorized instead of "Unknown Folder"
                const folderName = folder?.name || "Uncategorized";
                const isOpen = openFolders[folderId] || false;

                // Handle folder toggle
                const handleToggleFolder = () => {
                  // Update local state
                  setOpenFolders((prev) => ({
                    ...prev,
                    [folderId]: !isOpen,
                  }));

                  // Call parent handler
                  onToggleFolder(folderId);
                };

                return (
                  <React.Fragment key={folderId}>
                    {/* Folder Row */}
                    <TableRow
                      className={cn(
                        "border-t border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer",
                        dragOverFolder === folderId &&
                          "bg-blue-50 dark:bg-blue-900/20 ring-1 ring-blue-300 dark:ring-blue-700",
                      )}
                      onClick={handleToggleFolder}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (onDragScenario) {
                          setDragOverFolder(folderId);
                        }
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (dragOverFolder === folderId) {
                          setDragOverFolder(null);
                        }
                      }}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        // Get the dragged scenario ID from the dataTransfer
                        const draggedScenarioId =
                          e.dataTransfer.getData("text/plain");

                        // Call the parent's onDropOnFolder function
                        if (draggedScenarioId && onDropOnFolder) {
                          // This will trigger the parent component's handleDrop function
                          // which will update the scenario's folder ID via API
                          onDropOnFolder(folderId);

                          console.log(
                            `Dropped scenario ${draggedScenarioId} onto folder ${folderId}`,
                          );
                        }

                        setDragOverFolder(null);
                      }}
                    >
                      <TableCell className="py-1.5 px-2 w-[30px] text-center">
                        <div
                          className="flex justify-center"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Checkbox
                            checked={folderScenarios.every((s) =>
                              selectedScenarios.includes(s.id),
                            )}
                            ref={(input: any) => {
                              if (input?.input) {
                                input.input.indeterminate =
                                  folderScenarios.some((s) =>
                                    selectedScenarios.includes(s.id),
                                  ) &&
                                  !folderScenarios.every((s) =>
                                    selectedScenarios.includes(s.id),
                                  );
                              }
                            }}
                            onCheckedChange={(checked) => {
                              if (onSelectFolderScenarios) {
                                onSelectFolderScenarios(folderId, !!checked);
                              }
                            }}
                            aria-label={`Select all scenarios in ${folderName}`}
                            className="h-3.5 w-3.5 rounded-sm border-gray-300 dark:border-gray-600 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-xs py-1 px-2 text-gray-500 dark:text-gray-400">
                        {scenario.id.substring(0, 6)}
                      </TableCell>
                      <TableCell className="py-1 px-2">
                        <div className="flex items-center gap-1.5 pl-8">
                          <FileText className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500" />
                          <span className="text-sm text-gray-700 dark:text-gray-300" title={scenario.name}>
                            {scenario.name}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="py-1 px-2">
                        {getTestTypeIcon(scenario)}
                      </TableCell>
                      <TableCell className="py-1 px-2">
                        {getTestRailBadge(scenario)}
                      </TableCell>
                      <TableCell className="py-1 px-2">
                        {getStatusBadge(scenario)}
                      </TableCell>
                      <TableCell className="py-1 px-2 text-xs text-gray-500 dark:text-gray-400">
                        {formatDate(scenario.lastRun || scenario.updatedAt)}
                      </TableCell>
                      <TableCell className="text-right py-1 px-2">
                        <div className="flex items-center justify-end space-x-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-5 mr-1 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleFolder();
                            }}
                          >
                            {isOpen ? (
                              <ChevronDown className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                            ) : (
                              <ChevronRight className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                            )}
                          </Button>

                          <div className="flex items-center gap-1.5 pl-8">
                            <FolderIcon
                              className="h-3.5 w-3.5 mr-1"
                              style={{ color: folder?.color || "#6b7280" }}
                            />
                            <span className="font-medium text-sm text-gray-700 dark:text-gray-300">
                              {folderName}
                            </span>
                            <Badge className="ml-2 text-xs py-0 px-1.5 h-4 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 font-normal">
                              {folderScenarios.length}
                            </Badge>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>

                    {/* Scenario Rows - only show if folder is open */}
                    {isOpen && folderScenarios.length === 0 && (
                      <TableRow className="hover:bg-gray-50 dark:hover:bg-gray-800 border-t border-gray-50 dark:border-gray-800">
                        <TableCell
                          colSpan={8}
                          className="py-3 px-2 text-center"
                        >
                          <div className="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400 italic">
                            No scenarios found in this folder
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                    {isOpen &&
                      folderScenarios.length > 0 &&
                      folderScenarios.map((scenario) => (
                        <TableRow
                          key={scenario.id}
                          className={cn(
                            "hover:bg-gray-50 dark:hover:bg-gray-800 border-t border-gray-50 dark:border-gray-800",
                            selectedScenarios.includes(scenario.id) &&
                              "bg-blue-50 dark:bg-blue-900/20",
                            draggedScenarioId === scenario.id &&
                              "bg-blue-100 dark:bg-blue-900/30 ring-1 ring-blue-400 dark:ring-blue-600 opacity-70",
                          )}
                        >
                          <TableCell className="text-center py-1 px-2">
                            <div className="flex justify-center items-center gap-1">
                              <div
                                className="cursor-grab opacity-60 hover:opacity-100"
                                draggable
                                onDragStart={(e) => {
                                  e.stopPropagation();

                                  // Make drag image transparent (not fully supported in all browsers)
                                  if (e.dataTransfer.setDragImage) {
                                    const dragImage = new Image();
                                    dragImage.src =
                                      "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"; // Transparent 1x1 pixel
                                    e.dataTransfer.setDragImage(
                                      dragImage,
                                      0,
                                      0,
                                    );
                                  }

                                  // Set the data transfer
                                  e.dataTransfer.setData(
                                    "text/plain",
                                    scenario.id,
                                  );
                                  e.dataTransfer.effectAllowed = "move";

                                  // Set the dragged scenario ID for visual feedback
                                  setDraggedScenarioId(scenario.id);

                                  if (onDragScenario) {
                                    onDragScenario(scenario.id, e);
                                  }
                                }}
                                onDragEnd={() => {
                                  // Clear the dragged scenario ID when drag ends
                                  setDraggedScenarioId(null);
                                }}
                              >
                                <GripVertical className="h-3 w-3 text-gray-400 dark:text-gray-500" />
                              </div>
                              <Checkbox
                                checked={selectedScenarios.includes(
                                  scenario.id,
                                )}
                                onCheckedChange={(checked) =>
                                  onSelectScenario(scenario.id, !!checked)
                                }
                                aria-label={`Select scenario ${scenario.name}`}
                                className="h-3.5 w-3.5 rounded-sm border-gray-300 dark:border-gray-600 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                              />
                            </div>
                          </TableCell>
                          <TableCell className="font-mono text-xs py-1 px-2 text-gray-500 dark:text-gray-400">
                            {scenario.id.substring(0, 6)}
                          </TableCell>
                          <TableCell className="py-1 px-2">
                            <div className="flex items-center gap-1.5 pl-8">
                              <FileText className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500" />
                              <span
                                className="text-sm text-gray-700 dark:text-gray-300"
                                title={scenario.name}
                              >
                                {scenario.name}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="py-1 px-2">
                            {getTestTypeIcon(scenario)}
                          </TableCell>
                          <TableCell className="py-1 px-2">
                            {getTestManagementBadge(scenario)}
                          </TableCell>
                          <TableCell className="py-1 px-2">
                            {getStatusBadge(scenario)}
                          </TableCell>
                          <TableCell className="py-1 px-2 text-xs text-gray-500 dark:text-gray-400">
                            {formatScenarioDateAbsolute(
                              scenario.lastRun || scenario.updatedAt,
                            )}
                          </TableCell>
                          <TableCell className="text-right py-1 px-2">
                            <div className="flex items-center justify-end space-x-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 hover:bg-gray-100 dark:hover:bg-gray-800"
                                onClick={() => onEditScenario(scenario.id)}
                                title="Edit"
                              >
                                <Pencil className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                              </Button>

                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 hover:bg-gray-100 dark:hover:bg-gray-800"
                                onClick={() => onDuplicateScenario(scenario.id)}
                                title="Duplicate"
                              >
                                <Copy className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                              </Button>

                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6 hover:bg-gray-100 dark:hover:bg-gray-800"
                                  >
                                    <MoreHorizontal className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent
                                  align="end"
                                  className="w-36 dark:bg-gray-800 dark:border-gray-700"
                                >
                                  <DropdownMenuItem
                                    onClick={() =>
                                      onDeleteScenario(scenario.id)
                                    }
                                    className="text-red-600 dark:text-red-400 text-xs dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                                  >
                                    <Trash2 className="h-3 w-3 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                  </React.Fragment>
                );
              })
          )}
        </TableBody>
      </Table>
    </div>
  );
}
