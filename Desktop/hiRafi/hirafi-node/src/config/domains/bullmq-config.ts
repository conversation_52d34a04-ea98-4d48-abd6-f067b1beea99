/**
 * BullMQ Configuration Domain
 * Handles BullMQ-specific configuration settings
 */

import { getEnvInt } from '../utils/env-utils.js';
import { BullMQConfigDomain } from '../types/config-types.js';

/**
 * Creates BullMQ configuration from environment variables
 * @returns BullMQ configuration object
 */
export function createBullMQConfig(): BullMQConfigDomain {
  return {
    // Job lock configuration - Heartbeat-based approach
    jobLockRefreshInterval: getEnvInt('BULLMQ_JOB_LOCK_REFRESH_INTERVAL', 30000), // 30 seconds (heartbeat interval)
    stalledJobLockExtension: getEnvInt('BULLMQ_STALLED_JOB_LOCK_EXTENSION', 2700000), // 45 minutes (increased to allow longer tests)

    // Worker configuration - Balanced for heartbeat monitoring
    lockDuration: getEnvInt('BULLMQ_LOCK_DURATION', 2700000), // 45 minutes (increased from 10 minutes to allow longer tests)
    stalledInterval: getEnvInt('BULLMQ_STALLED_INTERVAL', 120000), // 2 minutes (kept for stuck detection)
    maxStalledCount: getEnvInt('BULLMQ_MAX_STALLED_COUNT', 3), // 3 attempts (standard value)

    // Active job monitoring - Aligned with heartbeat approach
    activeJobMonitoringTimeout: getEnvInt('BULLMQ_ACTIVE_JOB_MONITORING_TIMEOUT', 300000), // 5 minutes (longer since heartbeat is simpler)
    activeJobLockExtension: getEnvInt('BULLMQ_ACTIVE_JOB_LOCK_EXTENSION', 2700000), // 45 minutes (aligned with lockDuration)

    // Queue processing - CPU optimized
    concurrency: getEnvInt('BULLMQ_CONCURRENCY', 1), // 1 job at a time (critical for race condition prevention)
    drainDelay: getEnvInt('BULLMQ_DRAIN_DELAY', 5000) // 5 seconds (kept for CPU optimization)
  };
}

/**
 * Validates BullMQ configuration
 * @param config BullMQ configuration to validate
 * @returns Validation result
 */
export function validateBullMQConfig(config: BullMQConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate positive values
  if (config.jobLockRefreshInterval <= 0) {
    errors.push('Job lock refresh interval must be positive');
  }

  if (config.stalledJobLockExtension <= 0) {
    errors.push('Stalled job lock extension must be positive');
  }

  if (config.lockDuration <= 0) {
    errors.push('Lock duration must be positive');
  }

  if (config.stalledInterval <= 0) {
    errors.push('Stalled interval must be positive');
  }

  if (config.maxStalledCount <= 0) {
    errors.push('Max stalled count must be positive');
  }

  if (config.activeJobMonitoringTimeout <= 0) {
    errors.push('Active job monitoring timeout must be positive');
  }

  if (config.activeJobLockExtension <= 0) {
    errors.push('Active job lock extension must be positive');
  }

  if (config.concurrency <= 0) {
    errors.push('Concurrency must be positive');
  }

  if (config.drainDelay < 0) {
    errors.push('Drain delay must be non-negative');
  }

  // Validate logical relationships
  if (config.jobLockRefreshInterval >= config.stalledJobLockExtension) {
    errors.push('Job lock refresh interval should be less than stalled job lock extension');
  }

  if (config.stalledInterval >= config.lockDuration) {
    errors.push('Stalled interval should be less than lock duration');
  }

  if (config.activeJobMonitoringTimeout >= config.activeJobLockExtension) {
    errors.push('Active job monitoring timeout should be less than active job lock extension');
  }

  // Warn about concurrency > 1 (race condition risk)
  if (config.concurrency > 1) {
    errors.push('WARNING: Concurrency > 1 may cause race conditions in test execution');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets default BullMQ configuration
 * @returns Default BullMQ configuration
 */
export function getDefaultBullMQConfig(): BullMQConfigDomain {
  return {
    // Job lock configuration - optimized for heartbeat-based extension
    jobLockRefreshInterval: 30000,    // 30 seconds - heartbeat interval for regular lock extension
    stalledJobLockExtension: 2700000,  // 45 minutes (increased from 10 minutes to allow longer tests)

    // Worker configuration - balanced for heartbeat monitoring
    lockDuration: 2700000,             // 45 minutes (increased from 10 minutes to allow longer tests)
    stalledInterval: 120000,          // 2 minutes - reasonable for stuck detection
    maxStalledCount: 3,               // 3 attempts - standard value

    // Active job monitoring - aligned with heartbeat approach
    activeJobMonitoringTimeout: 300000, // 5 minutes - longer since heartbeat is simpler and more reliable
    activeJobLockExtension: 2700000,     // 45 minutes (increased from 10 minutes, aligned with lockDuration)

    // Queue processing - optimized for race condition prevention
    concurrency: 1,                   // CRITICAL: Only 1 job at a time to prevent race conditions
    drainDelay: 2000                  // 2 seconds - reasonable delay between job checks
  };
}
