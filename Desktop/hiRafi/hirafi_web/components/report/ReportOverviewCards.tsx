import React from 'react';
import {
  Clock,
  Calendar,
  User,
  Server,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { ReportDetail } from '@/hooks/useReport';
import { getFormattedDuration } from '@/lib/utils/run-utils';

interface ReportOverviewCardsProps {
  report: ReportDetail | null;
}

// Format date to a more compact format
function formatDate(date: number | Date | string | undefined | null): string {
  // Handle null, undefined, or empty values
  if (!date) {
    return 'Date unavailable';
  }

  try {
    // Create Date object from various input types
    let d: Date;

    if (typeof date === 'string') {
      // Handle string dates (ISO strings from API)
      d = new Date(date);
    } else if (typeof date === 'number') {
      // Handle timestamp numbers
      d = new Date(date);
    } else if (date instanceof Date) {
      // Handle Date objects
      d = date;
    } else {
      // Fallback for unexpected types
      d = new Date(date as any);
    }

    // Validate the created date
    if (isNaN(d.getTime())) {
      console.warn('Invalid date value received:', date);
      return 'Invalid date';
    }

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Check if it's today or yesterday
    if (d.toDateString() === today.toDateString()) {
      return `Today, ${d.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;
    } else if (d.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${d.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;
    } else {
      // Format: Jan 15, 2023 at 14:30
      return `${d.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })} at ${d.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;
    }
  } catch (error) {
    console.error('Error formatting date:', error, 'Input:', date);
    return 'Date formatting error';
  }
}

export function ReportOverviewCards({ report }: ReportOverviewCardsProps) {
  if (!report) return null;

  // TestRail kartını kaldırdık, sadece tab olarak kalacak

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all shadow-sm hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-900/40 p-2 rounded-lg">
              <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Duration</p>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {getFormattedDuration(report)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Total execution time</p>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all shadow-sm hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-900/40 p-2 rounded-lg">
              <Calendar className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Execution Date</p>
          </div>
          <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{formatDate(report.date)}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Test run completed</p>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all shadow-sm hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-900/40 p-2 rounded-lg">
              <User className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Triggered By</p>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{report.executedUserName || report.userId || 'System'}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{report.executedUserName || report.userId ? 'Manual execution' : 'Automatic execution'}</p>
        </CardContent>
      </Card>

      <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 transition-all shadow-sm hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-900/40 p-2 rounded-lg">
              <Server className="h-5 w-5 text-amber-600 dark:text-amber-400" />
            </div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Environment</p>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">Production</p> {/* Assuming Production for now */}
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Test environment</p>
        </CardContent>
      </Card>

      {/* TestRail kartını kaldırdık, sadece tab olarak kalacak */}
    </div>
  );
}