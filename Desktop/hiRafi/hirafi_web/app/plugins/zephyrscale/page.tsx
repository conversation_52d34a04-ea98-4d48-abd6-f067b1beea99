"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Check, ChevronRight, Puzzle, AlertCircle, ArrowRight, CheckCircle2, LoaderCircle, TestTube, Layers } from "lucide-react"
import { CompanyOwnerRoute } from "@/components/company-owner-route"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { pluginApi } from "@/lib/api"
import ErrorBoundary from "@/components/error-boundary"

export default function ZephyrScalePluginPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [configStep, setConfigStep] = useState(1)
  const [configData, setConfigData] = useState({
    apiToken: "",
    selectedProjects: [] as string[],
    selectedFolders: [] as string[]
  })

  const [isActive, setIsActive] = useState(false)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<"idle" | "success" | "error">("idle")
  const [connectionError, setConnectionError] = useState("")
  const [projects, setProjects] = useState<any[]>([])
  const [isLoadingProjects, setIsLoadingProjects] = useState(false)
  const [folders, setFolders] = useState<any[]>([])
  const [foldersByProject, setFoldersByProject] = useState<Record<string, any[]>>({})
  const [isLoadingFolders, setIsLoadingFolders] = useState(false)
  const [activeConfigData, setActiveConfigData] = useState<any>(null)
  const [isLoadingConfig, setIsLoadingConfig] = useState(false)
  const [lastSyncedTime, setLastSyncedTime] = useState<string>('')
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false)
  const [isDisconnecting, setIsDisconnecting] = useState(false)

  // Helper function to get step title
  const getStepTitle = () => {
    switch (configStep) {
      case 1:
        return "Connect to Zephyr Scale"
      case 2:
        return "Select Projects"
      case 3:
        return "Select Folders (Optional)"
      default:
        return "Configure Zephyr Scale"
    }
  }

  // Helper function to format last updated time
  const formatLastUpdatedTime = (updatedAt: string) => {
    const now = new Date()
    const updated = new Date(updatedAt)
    const diffMins = Math.floor((now.getTime() - updated.getTime()) / (1000 * 60))

    if (diffMins < 1) {
      setLastSyncedTime('Az önce')
    } else if (diffMins < 60) {
      setLastSyncedTime(`${diffMins} dakika önce`)
    } else if (diffMins < 1440) {
      setLastSyncedTime(`${Math.floor(diffMins / 60)} saat önce`)
    } else {
      setLastSyncedTime(`${Math.floor(diffMins / 1440)} gün önce`)
    }
  }

  const fetchProjects = async () => {
    setIsLoadingProjects(true)
    setProjects([]) // Clear existing projects

    try {
      const response = await pluginApi.getZephyrScaleProjects({
        apiToken: configData.apiToken
      })

      console.log('Zephyr Scale API Response:', response) // Debug log

      const projectsArray = response.data || []

      if (response.success && Array.isArray(projectsArray)) {
        if (projectsArray.length > 0) {
          // Backend now returns clean data structure: { id, name, key, description }
          // where name is already mapped from key
          const projectsData = projectsArray

          console.log('Projects data from backend:', projectsData) // Debug log

          // Update projects state
          setProjects(projectsData)

          console.log('Projects state updated, count:', projectsData.length) // Debug log

          toast({
            title: "Success",
            description: `Found ${projectsData.length} project(s)`,
          })
        } else {
          // Empty array - no projects found
          console.log('No projects found in response') // Debug log
          setProjects([])
          toast({
            title: "No Projects",
            description: "No projects found. Please check your API token permissions.",
            variant: "destructive"
          })
        }
      } else {
        // Invalid response structure
        console.error('Invalid response format:', response) // Debug log
        setProjects([])
        toast({
          title: "Error",
          description: response.error || "Invalid response format from Zephyr Scale API",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error('Failed to fetch projects:', error)
      setProjects([])
      toast({
        title: "Error",
        description: "Failed to fetch projects. Please check your API token.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingProjects(false)
    }
  }

  const fetchFolders = async (projectKeys: string[]) => {
    if (!projectKeys.length) return

    setIsLoadingFolders(true)
    try {
      // Fetch folders for all selected projects
      const allFolders: any[] = []
      const foldersByProjectTemp: Record<string, any[]> = {}

      for (const projectKey of projectKeys) {
        const response = await pluginApi.getZephyrScaleFolders({
          apiToken: configData.apiToken,
          projectKey
        })

        const foldersArray = response.data || []

        if (response.success && Array.isArray(foldersArray)) {
          const projectFolders = foldersArray.map((folder: any) => ({
            id: folder.id,
            name: folder.name,
            projectKey
          }))

          allFolders.push(...projectFolders)
          foldersByProjectTemp[projectKey] = projectFolders
        }
      }

      setFolders(allFolders)
      setFoldersByProject(foldersByProjectTemp)
    } catch (error: any) {
      console.error('Failed to fetch folders:', error)
      toast({
        title: "Error",
        description: "Failed to fetch folders for the selected projects.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingFolders(false)
    }
  }

  const testConnection = async () => {
    setIsTestingConnection(true)
    setConnectionStatus("idle")
    setConnectionError("")

    try {
      // Input validation
      const trimmedApiToken = configData.apiToken?.trim() || ''

      if (!trimmedApiToken) {
        setConnectionStatus("error")
        setConnectionError("API token is required.")
        toast({
          title: "Missing Information",
          description: "Please enter your API token.",
          variant: "destructive"
        })
        return
      }

      const response = await pluginApi.testZephyrScaleConnection({
        apiToken: trimmedApiToken
      })

      if (response.success) {
        setConnectionStatus("success")
        toast({
          title: "Connection Successful",
          description: "Zephyr Scale connection established successfully.",
        })
        // Automatically fetch projects after successful connection
        await fetchProjects()
      } else {
        setConnectionStatus("error")

        let errorMessage = response.error || "Connection failed."
        let toastTitle = "Connection Error"

        if ((response as any).errorCode) {
          switch ((response as any).errorCode) {
            case 'AUTHENTICATION_FAILED':
              toastTitle = "Authentication Failed"
              break
            case 'ACCESS_DENIED':
              toastTitle = "Access Denied"
              break
            case 'INSTANCE_NOT_FOUND':
              toastTitle = "Zephyr Scale Not Found"
              break
            case 'INVALID_TOKEN_FORMAT':
              toastTitle = "Invalid Token"
              break
            default:
              toastTitle = "Connection Error"
          }
        }

        setConnectionError(errorMessage)
        toast({
          title: toastTitle,
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error: any) {
      setConnectionStatus("error")
      setConnectionError(error.message || 'Connection failed')
      toast({
        title: "Connection Failed",
        description: error.message || "Failed to connect to Zephyr Scale",
        variant: "destructive"
      })
    } finally {
      setIsTestingConnection(false)
    }
  }

  // Step navigation
  const handleNextStep = async () => {
    if (configStep === 1 && connectionStatus === "success") {
      setConfigStep(2)
    } else if (configStep === 2 && configData.selectedProjects.length > 0) {
      setConfigStep(3)
      // Fetch folders for selected projects
      await fetchFolders(configData.selectedProjects)
    } else if (configStep === 3) {
      // Save configuration
      await saveConfiguration()
    }
  }

  const handlePrevStep = () => {
    if (configStep > 1) {
      setConfigStep(configStep - 1)
      // Reset connection status when going back
      if (configStep === 2) {
        setConnectionStatus("idle")
        setConnectionError("")
      }
    }
  }

  // Project selection handler
  const toggleProjectSelection = (projectKey: string) => {
    setConfigData(prev => {
      const currentProjects = prev.selectedProjects || []
      const isSelected = currentProjects.includes(projectKey)

      if (isSelected) {
        // Remove project - also clear related folders
        return {
          ...prev,
          selectedProjects: currentProjects.filter(key => key !== projectKey),
          // Clear folders when projects change
          selectedFolders: []
        }
      } else {
        // Add project - clear folders since project selection changed
        return {
          ...prev,
          selectedProjects: [...currentProjects, projectKey],
          // Clear folders when projects change
          selectedFolders: []
        }
      }
    })

    // Clear folder-related state when projects change
    setFolders([])
    setFoldersByProject({})
  }

  // Folder selection handler
  const toggleFolderSelection = (folderId: string) => {
    setConfigData(prev => {
      const currentFolders = prev.selectedFolders || []
      const isSelected = currentFolders.includes(folderId)

      if (isSelected) {
        // Remove folder
        return {
          ...prev,
          selectedFolders: currentFolders.filter(id => id !== folderId)
        }
      } else {
        // Add folder
        return {
          ...prev,
          selectedFolders: [...currentFolders, folderId]
        }
      }
    })
  }

  const saveConfiguration = async () => {
    if (configData.selectedProjects.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one project",
        variant: "destructive"
      })
      return false
    }

    try {
      // Build projects data from selected projects
      const selectedProjectsData = configData.selectedProjects.map(projectKey => {
        const projectDetails = projects.find(p => p.key === projectKey)
        return {
          id: projectKey,
          name: projectDetails?.name || projectKey,
          key: projectKey,
          description: projectDetails?.description || ''
        }
      })

      // Build folders data from selected folders
      const selectedFoldersData = configData.selectedFolders.map(folderId => {
        const folderDetails = folders.find(f => f.id === folderId)
        return {
          id: folderId,
          name: folderDetails?.name || `Folder ${folderId}`,
          projectKey: folderDetails?.projectKey || ''
        }
      })

      const response = await pluginApi.saveZephyrScaleConfig({
        apiToken: configData.apiToken,
        projectsData: selectedProjectsData,
        foldersData: selectedFoldersData
      })

      if (response.success) {
        toast({
          title: "Success",
          description: response.message || "Zephyr Scale integration saved successfully.",
        })
        setIsActive(true)

        // Fetch active config after successful save
        await fetchActiveConfig()
        return true
      } else {
        toast({
          title: "Error",
          description: response.error || "Zephyr Scale configuration could not be saved.",
          variant: "destructive"
        })
        return false
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: "An error occurred while saving Zephyr Scale configuration.",
        variant: "destructive"
      })
      return false
    }
  }

  // Fetch active configuration
  const fetchActiveConfig = async () => {
    setIsLoadingConfig(true)
    try {
      const response = await pluginApi.getZephyrScaleConfig()

      if (response.success) {
        setActiveConfigData(response)
        setIsActive(true)

        // Format last updated time
        if (response.plugin?.updatedAt) {
          formatLastUpdatedTime(response.plugin.updatedAt)
        }
      } else {
        // Configuration not found - this is normal if not configured yet
        setIsActive(false)
        setActiveConfigData(null)
        // Don't show error toast for missing configuration - it's expected
        console.debug('Zephyr Scale configuration not found - user has not configured it yet');
      }
    } catch (error) {
      // Error occurred - could be network issue or configuration not found
      setIsActive(false)
      setActiveConfigData(null)
      console.debug('Error fetching Zephyr Scale configuration:', error);
      // Only show error toast for actual errors, not missing configuration
      if (error && typeof error === 'object' && 'message' in error &&
          !error.message.includes('not configured') && !error.message.includes('not found')) {
        toast({
          title: "Error",
          description: "Failed to fetch Zephyr Scale configuration due to network error.",
          variant: "destructive"
        });
      }
    } finally {
      setIsLoadingConfig(false)
    }
  }

  // Disconnect handler
  const handleDisconnect = async () => {
    setIsDisconnecting(true)
    try {
      const response = await pluginApi.disconnectZephyrScale()

      if (response.success) {
        toast({
          title: "Disconnected Successfully",
          description: "Zephyr Scale integration has been disconnected successfully",
          variant: "default",
        })

        // Update UI
        setIsActive(false)
        setActiveConfigData(null)
        setShowDisconnectDialog(false)

        // Clear configuration data
        setConfigData({
          apiToken: "",
          selectedProjects: [],
          selectedFolders: []
        })

        // Reset to first step
        setConfigStep(1)
        setConnectionStatus("idle")
        setConnectionError("")
      } else {
        toast({
          title: "Disconnect Failed",
          description: response.error || "Failed to disconnect Zephyr Scale integration",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "An error occurred while disconnecting",
        variant: "destructive",
      })
    } finally {
      setIsDisconnecting(false)
    }
  }

  // Check for existing configuration on mount
  useEffect(() => {
    const checkExistingConfig = async () => {
      try {
        const response = await pluginApi.getZephyrScaleConfig()
        if (response.success && response.config && response.plugin) {
          // Existing configuration found, set active state
          setIsActive(true)
          setActiveConfigData(response)

          // Format last updated time
          if (response.plugin?.updatedAt) {
            formatLastUpdatedTime(response.plugin.updatedAt)
          }
        } else {
          // No configuration, show new configuration screen
          setIsActive(false)
          setActiveConfigData(null)
        }
      } catch (error) {
        // No existing config, which is fine
        console.log('No existing Zephyr Scale configuration found')
        setIsActive(false)
        setActiveConfigData(null)
      }
    }

    checkExistingConfig()
  }, [])

  // Clear folders when no projects are selected (but don't auto-fetch)
  useEffect(() => {
    if (configData.selectedProjects && configData.selectedProjects.length === 0) {
      // Clear folders when no projects are selected
      setFolders([])
      setFoldersByProject({})
      setConfigData(prev => ({
        ...prev,
        selectedFolders: []
      }))
    }
  }, [configData.selectedProjects])

  // Render active configuration card
  const renderActiveCard = () => {
    if (!activeConfigData || !activeConfigData.plugin) {
      return (
        <Card className="border-0 shadow-md dark:bg-gray-900">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 rounded-full flex items-center justify-center">
                <AlertCircle className="h-5 w-5" />
              </div>
              <div>
                <CardTitle className="text-lg">Configuration Loading</CardTitle>
                <CardDescription>Loading Zephyr Scale configuration...</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          </CardContent>
        </Card>
      )
    }

    return (
      <Card className="border-0 shadow-md dark:bg-gray-900">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 rounded-full flex items-center justify-center">
              <Check className="h-5 w-5" />
            </div>
            <div>
              <CardTitle className="text-lg">Zephyr Scale Connected</CardTitle>
              <CardDescription>
                Integration is active and ready to use • Last synced: {lastSyncedTime}
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Connection Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Connected Projects</Label>
              <div className="space-y-2">
                {activeConfigData.config?.projectsData?.map((project: any) => (
                  <div key={project.key} className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <Layers className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{project.name}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">({project.key})</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Connected Folders</Label>
              <div className="space-y-2">
                {activeConfigData.config?.foldersData?.length > 0 ? (
                  activeConfigData.config.foldersData.map((folder: any) => (
                    <div key={folder.id} className="flex items-center gap-2 p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <TestTube className="h-4 w-4 text-purple-500" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">{folder.name}</span>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 dark:text-gray-400 italic">
                    All folders (no specific folder filter)
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200 dark:border-gray-800">
            <Button
              variant="outline"
              onClick={() => setIsActive(false)}
              className="flex-1 bg-white dark:bg-gray-800"
            >
              Reconfigure
            </Button>
            <Button
              variant="destructive"
              onClick={() => setShowDisconnectDialog(true)}
              className="flex-1"
            >
              Disconnect
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <CompanyOwnerRoute>
      <ErrorBoundary>
        <div className="flex h-screen bg-white dark:bg-gray-950">
          {/* Sidebar */}
          <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-lg flex items-center justify-center">
                  <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor" />
                    <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Zephyr Scale Integration</h1>
                  <p className="text-gray-500 dark:text-gray-400">
                    Connect your Zephyr Scale Cloud account to sync test cases and results
                  </p>
                </div>
              </div>
            </header>

            <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
              <div className="max-w-3xl mx-auto">
                {isActive ? (
                  renderActiveCard()
                ) : (
                  <Card className="border-0 shadow-md dark:bg-gray-900">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl">{getStepTitle()}</CardTitle>
                      <CardDescription>
                        {configStep === 1 && "Enter your Zephyr Scale API token and test the connection before proceeding."}
                        {configStep === 2 && "Select the projects you want to connect with."}
                        {configStep === 3 && "Optionally select specific folders to filter test cases."}
                      </CardDescription>
                    </CardHeader>

                    <CardContent>
                      <div className="mb-6">
                        <Progress
                          value={(configStep / 3) * 100}
                          className="h-2 bg-gray-100 dark:bg-gray-800"
                        />
                        <div className="flex justify-between mt-4">
                          <div className="flex flex-col items-center">
                            <div
                              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                configStep >= 1
                                  ? "bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400"
                                  : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                              }`}
                            >
                              {configStep > 1 ? (
                                <Check className="h-5 w-5" />
                              ) : (
                                <span className="text-sm font-medium">1</span>
                              )}
                            </div>
                            <span className={`text-xs mt-2 ${
                              configStep >= 1
                                ? "text-indigo-600 dark:text-indigo-400"
                                : "text-gray-400 dark:text-gray-500"
                            }`}>Connection</span>
                          </div>

                          <div className="w-full mx-4 mt-5">
                            <div className={`h-1 ${
                              configStep >= 2
                                ? "bg-indigo-500 dark:bg-indigo-600"
                                : "bg-gray-200 dark:bg-gray-700"
                            } rounded-full`}></div>
                          </div>

                          <div className="flex flex-col items-center">
                            <div
                              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                configStep >= 2
                                  ? "bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400"
                                  : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                              }`}
                            >
                              {configStep > 2 ? (
                                <Check className="h-5 w-5" />
                              ) : (
                                <span className="text-sm font-medium">2</span>
                              )}
                            </div>
                            <span className={`text-xs mt-2 ${
                              configStep >= 2
                                ? "text-indigo-600 dark:text-indigo-400"
                                : "text-gray-400 dark:text-gray-500"
                            }`}>Projects</span>
                          </div>

                          <div className="w-full mx-4 mt-5">
                            <div className={`h-1 ${
                              configStep >= 3
                                ? "bg-indigo-500 dark:bg-indigo-600"
                                : "bg-gray-200 dark:bg-gray-700"
                            } rounded-full`}></div>
                          </div>

                          <div className="flex flex-col items-center">
                            <div
                              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                configStep >= 3
                                  ? "bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400"
                                  : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                              }`}
                            >
                              <span className="text-sm font-medium">3</span>
                            </div>
                            <span className={`text-xs mt-2 ${
                              configStep >= 3
                                ? "text-indigo-600 dark:text-indigo-400"
                                : "text-gray-400 dark:text-gray-500"
                            }`}>Folders</span>
                          </div>
                        </div>
                      </div>

                      {/* Step 1: API Token */}
                      {configStep === 1 && (
                        <div className="space-y-4 pt-2">
                          <div>
                            <Label htmlFor="apiToken" className="text-base">Zephyr Scale API Token</Label>
                            <Input
                              id="apiToken"
                              type="password"
                              placeholder="Enter your Zephyr Scale API token"
                              value={configData.apiToken}
                              onChange={(e) => setConfigData(prev => ({ ...prev, apiToken: e.target.value }))}
                              className="mt-2"
                              disabled={isTestingConnection}
                            />
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                              You can generate an API token from your Zephyr Scale account settings
                            </p>
                          </div>

                          <Button
                            onClick={testConnection}
                            disabled={isTestingConnection || !configData.apiToken.trim()}
                            className="w-full bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800"
                          >
                            {isTestingConnection ? (
                              <>
                                <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                                Testing Connection...
                              </>
                            ) : (
                              'Test Connection'
                            )}
                          </Button>

                          {connectionStatus === "success" && (
                            <Alert className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
                              <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
                              <AlertTitle className="text-green-800 dark:text-green-200">Connection Successful</AlertTitle>
                              <AlertDescription className="text-green-700 dark:text-green-300">
                                Zephyr Scale connection established successfully. You can proceed to the next step.
                              </AlertDescription>
                            </Alert>
                          )}

                          {connectionStatus === "error" && (
                            <Alert className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20" variant="destructive">
                              <AlertCircle className="h-4 w-4" />
                              <AlertTitle>Connection Failed</AlertTitle>
                              <AlertDescription>
                                {connectionError}
                              </AlertDescription>
                            </Alert>
                          )}
                        </div>
                      )}

                      {/* Step 2: Project Selection */}
                      {configStep === 2 && (
                        <div className="space-y-4 pt-2">
                          <div className="flex justify-between items-center">
                            <Label className="text-base">Select Projects</Label>
                            {configData.selectedProjects?.length > 0 && (
                              <span className="text-sm text-indigo-600 dark:text-indigo-400">
                                {configData.selectedProjects.length} project(s) selected
                              </span>
                            )}
                          </div>

                          {isLoadingProjects ? (
                            <div className="flex items-center justify-center p-8">
                              <LoaderCircle className="h-8 w-8 animate-spin text-indigo-500" />
                              <span className="ml-3 text-sm text-gray-500">Loading projects...</span>
                            </div>
                          ) : projects.length === 0 ? (
                            <div className="p-8 text-center">
                              <AlertCircle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
                              <p className="text-gray-600 dark:text-gray-400">No projects found. Please check your API token permissions.</p>
                            </div>
                          ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {projects.map((project) => (
                                <div
                                  key={project.key}
                                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                                    configData.selectedProjects?.includes(project.key)
                                      ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 dark:border-indigo-700"
                                      : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-indigo-300 dark:hover:border-indigo-700"
                                  }`}
                                  onClick={() => toggleProjectSelection(project.key)}
                                >
                                  <div className="flex items-start justify-between">
                                    <div className="flex-1 pr-2 overflow-hidden">
                                      <h3 className="font-medium text-sm whitespace-nowrap overflow-hidden text-ellipsis" title={project.name}>
                                        {project.name}
                                      </h3>
                                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                                        Key: {project.key}
                                      </p>
                                    </div>
                                    {configData.selectedProjects?.includes(project.key) && (
                                      <div className="h-6 w-6 flex-shrink-0 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center">
                                        <Check className="h-4 w-4" />
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}

                          {/* Show selected projects count */}
                          {configData.selectedProjects?.length > 0 && (
                            <div className="mt-4 p-3 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg">
                              <div className="flex items-center text-sm text-indigo-700 dark:text-indigo-300">
                                <Layers className="h-4 w-4 mr-2" />
                                <span>{configData.selectedProjects.length} project(s) selected. Click Next to proceed to folder selection.</span>
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Step 3: Folder Selection */}
                      {configStep === 3 && (
                        <div className="space-y-4 pt-2">
                          <div className="flex justify-between items-center">
                            <Label className="text-base">Select Folders (Optional)</Label>
                            {configData.selectedFolders?.length > 0 && (
                              <span className="text-sm text-indigo-600 dark:text-indigo-400">
                                {configData.selectedFolders.length} folder(s) selected
                              </span>
                            )}
                          </div>

                          {isLoadingFolders ? (
                            <div className="flex items-center justify-center p-8">
                              <LoaderCircle className="h-8 w-8 animate-spin text-indigo-500" />
                              <span className="ml-3 text-sm text-gray-500">Loading folders...</span>
                            </div>
                          ) : folders.length === 0 ? (
                            <div className="p-8 text-center">
                              <TestTube className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                              <p className="text-gray-600 dark:text-gray-400">No folders found for the selected projects, or all test cases will be synced.</p>
                            </div>
                          ) : (
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              {folders.map((folder) => (
                                <div
                                  key={folder.id}
                                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                                    configData.selectedFolders?.includes(folder.id)
                                      ? "border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 dark:border-indigo-700"
                                      : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-indigo-300 dark:hover:border-indigo-700"
                                  }`}
                                  onClick={() => toggleFolderSelection(folder.id)}
                                >
                                  <div className="flex items-start justify-between">
                                    <div className="flex-1 pr-2 overflow-hidden">
                                      <h3 className="font-medium text-sm whitespace-nowrap overflow-hidden text-ellipsis" title={folder.name}>
                                        {folder.name}
                                      </h3>
                                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                                        {folder.projectKey && `Project: ${folder.projectKey}`}
                                      </p>
                                    </div>
                                    {configData.selectedFolders?.includes(folder.id) && (
                                      <div className="h-6 w-6 flex-shrink-0 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center">
                                        <Check className="h-4 w-4" />
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>

                    <CardFooter className="flex justify-between border-t border-gray-200 dark:border-gray-800 px-6 py-4">
                      {configStep > 1 ? (
                        <Button variant="outline" onClick={handlePrevStep} className="bg-white dark:bg-gray-800">
                          Back
                        </Button>
                      ) : (
                        <Button variant="outline" onClick={() => setIsActive(true)} className="bg-white dark:bg-gray-800">
                          Cancel
                        </Button>
                      )}
                      <Button
                        onClick={handleNextStep}
                        disabled={
                          (configStep === 1 && connectionStatus !== "success") ||
                          (configStep === 2 && (!configData.selectedProjects || configData.selectedProjects.length === 0)) ||
                          (configStep === 3 && isLoadingFolders) ||
                          isLoadingProjects
                        }
                        className="gap-2 bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-700 dark:hover:bg-indigo-800"
                      >
                        {configStep < 3 ? (
                          <>
                            Next
                            <ArrowRight className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <CheckCircle2 className="h-4 w-4" />
                            Save Configuration
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                )}
              </div>
            </main>
          </div>

          {/* Disconnect Dialog */}
          <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Disconnect Zephyr Scale</DialogTitle>
                <DialogDescription className="text-gray-600 dark:text-gray-400">
                  Are you sure you want to disconnect Zephyr Scale integration? This will remove all configuration settings and you'll need to reconfigure the integration if you want to use it again.
                </DialogDescription>
              </DialogHeader>

              <div className="py-4">
                <div className="bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-900 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-red-800 dark:text-red-300 mb-1">
                        This action cannot be undone
                      </p>
                      <p className="text-red-700 dark:text-red-400">
                        All Zephyr Scale configuration data including project selections, folder mappings, and authentication details will be permanently removed.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowDisconnectDialog(false)}
                  disabled={isDisconnecting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDisconnect}
                  disabled={isDisconnecting}
                  className="gap-2"
                >
                  {isDisconnecting ? (
                    <>
                      <LoaderCircle className="h-4 w-4 animate-spin" />
                      Disconnecting...
                    </>
                  ) : (
                    "Yes, Disconnect"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </ErrorBoundary>
    </CompanyOwnerRoute>
  )
}
