/**
 * Node Configuration Domain
 * Handles node-specific configuration settings
 */

import * as os from 'os';
import { getEnv, getEnvArray, getEnvBoolean, getEnvInt } from '../utils/env-utils.js';
import { NodeConfigDomain } from '../types/config-types.js';

/**
 * Generate an AI-themed random name for the node
 */
function generateAIThemedNodeName(): string {
  const AI_PREFIXES = [
    'ai', 'neural', 'quantum', 'cyber', 'smart', 'auto', 'robo', 'intel',
    'cogni', 'synth', 'logic', 'brain', 'mind', 'think', 'learn'
  ];

  const AI_SUFFIXES = [
    'bot', 'core', 'mind', 'brain', 'node', 'unit', 'agent', 'worker',
    'runner', 'engine', 'processor', 'analyzer', 'executor', 'tester'
  ];

  const RANDOM_WORDS = [
    'alpha', 'beta', 'gamma', 'delta', 'omega', 'prime', 'nova', 'stellar',
    'cosmic', 'fusion', 'matrix', 'nexus', 'vertex', 'apex', 'zenith'
  ];

  const prefix = AI_PREFIXES[Math.floor(Math.random() * AI_PREFIXES.length)];
  const suffix = AI_SUFFIXES[Math.floor(Math.random() * AI_SUFFIXES.length)];
  const word = RANDOM_WORDS[Math.floor(Math.random() * RANDOM_WORDS.length)];
  const number = Math.floor(Math.random() * 999) + 1;

  return `${prefix}-${word}-${suffix}-${number}`;
}

/**
 * Creates node configuration from environment variables
 * @returns Node configuration object
 */
export function createNodeConfig(): NodeConfigDomain {
  // Always use AI-themed random name as default, only use NODE_NAME if explicitly set
  const defaultName = generateAIThemedNodeName();

  return {
    name: getEnv('NODE_NAME', defaultName),
    hubUrl: getEnv('HUB_URL', 'http://localhost:5000'),
    capabilities: getEnvArray('NODE_CAPABILITIES', ['web', 'android']), // Default capabilities
    version: getEnv('VERSION', '1.0.0'),
    environment: getEnv('NODE_ENV', 'development'),
    useSystemInfo: getEnvBoolean('USE_SYSTEM_INFO', false),
    port: getEnvInt('NODE_PORT', 0),
    nodeId: getEnv('NODE_ID', '') // Persistent node ID from environment
  };
}

/**
 * Validates node configuration
 * @param config Node configuration to validate
 * @returns Validation result
 */
export function validateNodeConfig(config: NodeConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate required fields
  if (!config.name || config.name.trim() === '') {
    errors.push('Node name is required');
  }

  if (!config.hubUrl || config.hubUrl.trim() === '') {
    errors.push('Hub URL is required');
  }

  // Validate URL format
  if (config.hubUrl) {
    try {
      new URL(config.hubUrl);
    } catch {
      errors.push('Hub URL must be a valid URL');
    }
  }

  // Validate version format (basic semver check)
  if (config.version && !/^\d+\.\d+\.\d+/.test(config.version)) {
    errors.push('Version must follow semantic versioning format (e.g., 1.0.0)');
  }

  // Validate environment
  const validEnvironments = ['development', 'staging', 'production', 'test'];
  if (config.environment && !validEnvironments.includes(config.environment)) {
    errors.push(`Environment must be one of: ${validEnvironments.join(', ')}`);
  }

  // Validate port range
  if (config.port < 0 || config.port > 65535) {
    errors.push('Port must be between 0 and 65535');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets default node configuration
 * @returns Default node configuration
 */
export function getDefaultNodeConfig(): NodeConfigDomain {
  return {
    name: generateAIThemedNodeName(),
    hubUrl: 'http://localhost:5000',
    capabilities: ['web', 'android'], // Default capabilities
    version: '1.0.0',
    environment: 'development',
    useSystemInfo: false,
    port: 0,
    nodeId: ''
  };
}

/**
 * Merges node configuration with overrides
 * @param baseConfig Base configuration
 * @param overrides Configuration overrides
 * @returns Merged configuration
 */
export function mergeNodeConfig(
  baseConfig: NodeConfigDomain,
  overrides: Partial<NodeConfigDomain>
): NodeConfigDomain {
  return {
    ...baseConfig,
    ...overrides,
    // Special handling for arrays
    capabilities: overrides.capabilities || baseConfig.capabilities
  };
}
