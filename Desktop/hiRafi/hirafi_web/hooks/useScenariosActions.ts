/**
 * Scenario Actions Hook
 *
 * CRUD operations for scenarios and folders
 * Uses global cache invalidation for consistent updates
 */

import { useCallback, useState } from 'react'
import { toast } from '@/lib/utils/toast-utils'
import { scenarioApi } from '@/lib/api'
import { CacheManager } from '@/lib/utils/cache-utils'
import { useScenarioStore } from '@/store/scenarioStore'
import type { CreateFolderParams } from '@/types/scenario-extended'

/**
 * Options for bulk operations
 */
interface BulkOperationOptions {
  targetFolderId?: string
}

/**
 * Hook for scenario and folder CRUD operations
 */
export function useScenariosActions() {
  // Get user info from Zustand store
  const { userInfo } = useScenarioStore()

  // Loading states for CRUD operations
  const [loadingOperations, setLoadingOperations] = useState<Set<string>>(new Set())

  // Helper functions for operation loading states
  const isOperationInProgress = useCallback((operation: string) => {
    return loadingOperations.has(operation)
  }, [loadingOperations])

  const setOperationLoading = useCallback((operation: string, loading: boolean) => {
    setLoadingOperations(prev => {
      const newSet = new Set(prev)
      if (loading) {
        newSet.add(operation)
      } else {
        newSet.delete(operation)
      }
      return newSet
    })
  }, [])

  // Global cache invalidation helper
  const invalidateCache = useCallback(async () => {
    await CacheManager.invalidateScenarioData()
  }, [])

  /**
   * Create a new folder
   */
  const createFolder = useCallback(async (folderData: Omit<CreateFolderParams, 'teamId' | 'companyId'>) => {
    const operationKey = `createFolder-${folderData.name}`

    if (isOperationInProgress(operationKey)) {
      console.warn('Folder creation already in progress')
      return { success: false, error: 'Operation already in progress' }
    }

    if (!userInfo?.teamId || !userInfo?.companyId) {
      toast.error('User context not available')
      return { success: false, error: 'User context not available' }
    }

    setOperationLoading(operationKey, true)

    try {
      // Add team and company info
      const folderDataWithTeam = {
        ...folderData,
        teamId: userInfo.teamId,
        companyId: userInfo.companyId
      }

      // Call API
      const response = await scenarioApi.createFolder(folderDataWithTeam)

      if (!response.success) {
        toast.error("Klasör Oluşturulamadı", {
          description: response.error || "Klasör oluşturulurken bir hata oluştu"
        })
        return { success: false, error: response.error }
      }

      // Trigger global cache invalidation
      await invalidateCache()

      toast.success("Klasör Oluşturuldu", {
        description: `"${folderData.name}" klasörü başarıyla oluşturuldu.`
      })

      return {
        success: true,
        folder: response.data?.folder || response.folder
      }
    } catch (err: any) {
      toast.error("Klasör Oluşturulamadı", {
        description: err.message || "Klasör oluşturulurken bir hata oluştu"
      })
      return { success: false, error: err.message }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [userInfo, isOperationInProgress, setOperationLoading, invalidateCache])

  /**
   * Rename a folder
   */
  const renameFolder = useCallback(async (folderId: string, newName: string) => {
    const operationKey = `renameFolder-${folderId}`

    if (isOperationInProgress(operationKey)) {
      return { success: false, error: 'Operation already in progress' }
    }

    setOperationLoading(operationKey, true)

    try {
      const response = await scenarioApi.updateFolder(folderId, { name: newName })

      if (!response.success) {
        toast.error("Klasör Yeniden Adlandırılamadı", {
          description: response.error || "Klasör yeniden adlandırılırken bir hata oluştu"
        })
        return { success: false, error: response.error }
      }

      // Trigger global cache invalidation
      await invalidateCache()

      toast.success("Klasör Yeniden Adlandırıldı", {
        description: `Klasör başarıyla "${newName}" olarak yeniden adlandırıldı.`
      })

      return { success: true, folder: response.data?.folder }
    } catch (err: any) {
      toast.error("Klasör Yeniden Adlandırılamadı", {
        description: err.message || "Klasör yeniden adlandırılırken bir hata oluştu"
      })
      return { success: false, error: err.message }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [isOperationInProgress, setOperationLoading, invalidateCache])

  /**
   * Delete a folder
   */
  const deleteFolder = useCallback(async (folderId: string) => {
    const operationKey = `deleteFolder-${folderId}`

    if (isOperationInProgress(operationKey)) {
      return { success: false, error: 'Operation already in progress' }
    }

    setOperationLoading(operationKey, true)

    try {
      const response = await scenarioApi.deleteFolder(folderId)

      if (!response.success) {
        toast.error("Klasör Silinemedi", {
          description: response.error || "Klasör silinirken bir hata oluştu"
        })
        return { success: false, error: response.error }
      }

      // Trigger global cache invalidation
      await invalidateCache()

      toast.success("Klasör Silindi", {
        description: "Klasör başarıyla silindi."
      })

      return { success: true }
    } catch (err: any) {
      toast.error("Klasör Silinemedi", {
        description: err.message || "Klasör silinirken bir hata oluştu"
      })
      return { success: false, error: err.message }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [isOperationInProgress, setOperationLoading, invalidateCache])

  /**
   * Delete a scenario
   */
  const deleteScenario = useCallback(async (scenarioId: string) => {
    const operationKey = `deleteScenario-${scenarioId}`

    if (isOperationInProgress(operationKey)) {
      return { success: false, error: 'Operation already in progress' }
    }

    setOperationLoading(operationKey, true)

    try {
      const response = await scenarioApi.deleteScenario(scenarioId)

      if (!response.success) {
        return {
          success: false,
          error: response.error,
          inUseByRuns: (response as any).inUseByRuns
        }
      }

      // Specific cache invalidation for better performance
      await CacheManager.invalidateScenarios({ includeFolders: true })

      return { success: true }
    } catch (err: any) {
      return { success: false, error: err.message }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [isOperationInProgress, setOperationLoading, invalidateCache])

  /**
   * Duplicate a scenario
   */
  const duplicateScenario = useCallback(async (scenarioId: string, options?: BulkOperationOptions) => {
    const operationKey = `duplicateScenario-${scenarioId}`

    if (isOperationInProgress(operationKey)) {
      return { success: false, error: 'Operation already in progress' }
    }

    setOperationLoading(operationKey, true)

    try {
      const response = await scenarioApi.duplicateScenario(scenarioId, options)

      if (!response.success) {
        toast.error("Senaryo Kopyalama Hatası", {
          description: response.error || "Senaryo kopyalanırken bir hata oluştu"
        })
        return { success: false, error: response.error }
      }

      // Trigger global cache invalidation
      await invalidateCache()

      toast.success("Senaryo Kopyalandı", {
        description: response.message || "Senaryo başarıyla kopyalandı"
      })

      return {
        success: true,
        message: response.message || "Senaryo başarıyla kopyalandı",
        newScenarioId: response.data?.newScenarioId
      }
    } catch (err: any) {
      toast.error("Senaryo Kopyalama Hatası", {
        description: err.message || "Senaryo kopyalanırken bir hata oluştu"
      })
      return { success: false, error: err.message }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [isOperationInProgress, setOperationLoading, invalidateCache])

  /**
   * Move scenario to folder
   */
  const moveScenarioToFolder = useCallback(async (scenarioId: string, folderId: string | null) => {
    const operationKey = `moveScenario-${scenarioId}-${folderId || 'uncategorized'}`

    if (isOperationInProgress(operationKey)) {
      return { success: false, error: 'Operation already in progress' }
    }

    setOperationLoading(operationKey, true)

    try {
      const response = await scenarioApi.updateScenario(scenarioId, {
        folderId: folderId || undefined
      })

      if (!response.success) {
        toast.error("Senaryo Taşınamadı", {
          description: response.error || "Senaryo taşınırken bir hata oluştu"
        })
        return { success: false, error: response.error }
      }

      // Trigger global cache invalidation
      await invalidateCache()

      const folderName = folderId ? "seçilen klasöre" : "Kategorisiz klasörüne"
      toast.success("Senaryo Taşındı", {
        description: `Senaryo başarıyla ${folderName} taşındı.`
      })

      return { success: true }
    } catch (err: any) {
      toast.error("Senaryo Taşınamadı", {
        description: err.message || "Senaryo taşınırken bir hata oluştu"
      })
      return { success: false, error: err.message }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [isOperationInProgress, setOperationLoading, invalidateCache])

  /**
   * Bulk delete scenarios
   */
  const bulkDeleteScenarios = useCallback(async (scenarioIds: string[]) => {
    const operationKey = `bulkDeleteScenarios-${scenarioIds.length}`

    if (isOperationInProgress(operationKey)) {
      return { success: false, error: 'Operation already in progress', deletedCount: 0 }
    }

    setOperationLoading(operationKey, true)

    try {
      const response = await scenarioApi.bulkDeleteScenarios(scenarioIds)

      if (!response.success) {
        return {
          success: false,
          error: response.error,
          inUseScenarios: (response as any).inUseScenarios
        }
      }

      // Specific cache invalidation for better performance
      await CacheManager.invalidateScenarios({ includeFolders: true })

      // Extract data from new response format
      const responseData = response.data || response

      return {
        success: true,
        message: responseData.message,
        deletedCount: responseData.deletedCount,
        inUseScenarios: responseData.inUseScenarios
      }
    } catch (err: any) {
      if (err.response?.data?.inUseScenarios) {
        return {
          success: false,
          error: err.response.data.error,
          inUseScenarios: err.response.data.inUseScenarios
        }
      }

      return { success: false, error: err.message, deletedCount: 0 }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [isOperationInProgress, setOperationLoading])

  /**
   * Bulk duplicate scenarios
   */
  const bulkDuplicateScenarios = useCallback(async (scenarioIds: string[], options?: BulkOperationOptions) => {
    const operationKey = `bulkDuplicateScenarios-${scenarioIds.length}`

    if (isOperationInProgress(operationKey)) {
      return { success: false, error: 'Operation already in progress', duplicatedCount: 0 }
    }

    setOperationLoading(operationKey, true)

    try {
      const response = await scenarioApi.bulkDuplicateScenarios(scenarioIds, options)

      if (!response.success) {
        return {
          success: false,
          error: response.error,
          duplicatedCount: 0
        }
      }

      // Specific cache invalidation for better performance
      await CacheManager.invalidateScenarios({ includeFolders: true })

      // Extract data from new response format
      const responseData = response.data || response
      const duplicatedCount = responseData.duplicatedCount || scenarioIds.length

      return {
        success: true,
        duplicatedCount,
        message: `${duplicatedCount} senaryo başarıyla kopyalandı`
      }
    } catch (error: any) {
      console.error('Bulk duplicate scenarios error:', error)

      return {
        success: false,
        error: error.message || "Senaryolar kopyalanırken bir hata oluştu",
        duplicatedCount: 0
      }
    } finally {
      setOperationLoading(operationKey, false)
    }
  }, [isOperationInProgress, setOperationLoading])

  return {
    // Folder operations
    createFolder,
    renameFolder,
    deleteFolder,

    // Scenario operations
    deleteScenario,
    duplicateScenario,
    moveScenarioToFolder,

    // Bulk operations
    bulkDeleteScenarios,
    bulkDuplicateScenarios,

    // State helpers
    isOperationInProgress
  }
}
