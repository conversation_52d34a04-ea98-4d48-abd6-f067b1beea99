/**
 * Node State Service
 * 
 * Tracks node-level state and test statistics with dependency injection.
 * Delegates execution state queries to AtomicStateManager (single source of truth).
 */

import { TestRequest, TestResult } from '../../../../models/types.js';
import { INodeStateService } from '../../interfaces/node-state-service.interface.js';
import { TestExecutionInfo, NodeStatus } from '../../types/node-manager-types.js';
import { AtomicStateManager } from './atomic-state-manager.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

export class NodeStateService implements INodeStateService {
  // Atomic state manager for coordinated state management
  private atomicStateManager: AtomicStateManager;
  private logger: ILoggerService;

  // Node-level statistics (preserved in NodeStateService)
  private testsExecuted: number = 0;
  private testsFailed: number = 0;
  private testsSucceeded: number = 0;
  private startTime: Date = new Date();

  constructor(atomicStateManager: AtomicStateManager) {
    this.logger = LoggerServiceFactory.createServiceLogger('NodeStateService');
    this.atomicStateManager = atomicStateManager;
    this.logger.debug('NodeStateService: Initialized with AtomicStateManager dependency injection');
  }

  /**
   * Atomically transition from one state to another using AtomicStateManager
   */
  trySetState(fromState: NodeStatus, toState: NodeStatus, testId?: string, testData?: TestRequest): boolean {
    // Delegate to AtomicStateManager for race-condition-free state transitions
    const success = this.atomicStateManager.trySetState(fromState, toState, testId);

    if (success) {
      this.logger.info(`NodeStateService: Successfully transitioned from ${fromState} to ${toState} for test ${testId || 'none'}`);
    } else {
      const currentState = this.atomicStateManager.getNodeStatus();
      this.logger.warn(`NodeStateService: Failed to transition from ${fromState} to ${toState}. Current state is ${currentState}`);
    }

    return success;
  }

  /**
   * Get current test ID (delegates to AtomicStateManager)
   */
  getCurrentTestId(): string | null {
    return this.atomicStateManager.getCurrentTestId();
  }

  /**
   * Get current test data (deprecated - test data not stored in state managers)
   */
  getCurrentTestData(): TestRequest | null {
    // Test data is not stored in state managers for memory efficiency
    // This method is kept for interface compatibility but always returns null
    this.logger.debug('NodeStateService: getCurrentTestData called but test data is not stored in state managers');
    return null;
  }

  /**
   * Check if node is running a test (delegates to AtomicStateManager)
   */
  isRunningTest(): boolean {
    return this.atomicStateManager.isTestRunning();
  }

  /**
   * Get current node status (delegates to AtomicStateManager)
   */
  getStatus(): NodeStatus {
    return this.atomicStateManager.getNodeStatus();
  }

  /**
   * Set stopping state (delegates to AtomicStateManager)
   */
  setStopping(stopping: boolean): void {
    if (stopping) {
      // Force transition to STOPPING state
      this.atomicStateManager.forceState({ isStopping: true, nodeStatus: NodeStatus.STOPPING });
    } else {
      // Force clear stopping state
      this.atomicStateManager.forceState({ isStopping: false });
    }
    this.logger.debug(`NodeStateService: Stopping state set to ${stopping}`);
  }

  /**
   * Check if node is in stopping state (delegates to AtomicStateManager)
   */
  isStopping(): boolean {
    return this.atomicStateManager.isStopping();
  }

  /**
   * Get current node status including STOPPING state (same as getStatus)
   */
  getDetailedStatus(): NodeStatus {
    return this.getStatus();
  }

  /**
   * Get full execution state (combines AtomicStateManager state with node statistics)
   */
  getExecutionState(): TestExecutionInfo {
    // Get execution state from AtomicStateManager
    const isRunningTest = this.isRunningTest();
    const currentTestId = this.getCurrentTestId();
    const currentTestData = this.getCurrentTestData();

    return {
      isRunningTest,
      currentTestId,
      currentTestData,
      testsExecuted: this.testsExecuted,
      testsFailed: this.testsFailed,
      testsSucceeded: this.testsSucceeded,
      startTime: this.startTime
    };
  }

  /**
   * Update test counters based on result
   */
  updateTestCounters(result: TestResult): void {
    this.testsExecuted++;

    // Only count as succeeded if status is 'completed'
    // Count as failed if status is 'failed' or any other status (including 'stopped')
    if (result.status === 'completed') {
      this.testsSucceeded++;
      this.logger.debug(`NodeStateService: Test succeeded. Total: ${this.testsSucceeded}/${this.testsExecuted}`);
    } else {
      this.testsFailed++;
      this.logger.debug(`NodeStateService: Test failed. Total: ${this.testsFailed}/${this.testsExecuted}`);
    }
  }

  /**
   * Get test statistics
   */
  getTestStatistics(): {
    executed: number;
    succeeded: number;
    failed: number;
    uptime: number;
  } {
    return {
      executed: this.testsExecuted,
      succeeded: this.testsSucceeded,
      failed: this.testsFailed,
      uptime: this.getUptime()
    };
  }

  /**
   * Reset all counters and node-level state
   */
  reset(): void {
    // Reset atomic state
    this.atomicStateManager.reset();

    // Reset local counters
    this.testsExecuted = 0;
    this.testsFailed = 0;
    this.testsSucceeded = 0;
    this.startTime = new Date();
    this.logger.info('NodeStateService: Node-level state and counters reset (execution state managed by AtomicStateManager)');
  }

  /**
   * Get uptime in seconds
   */
  getUptime(): number {
    return Math.floor((new Date().getTime() - this.startTime.getTime()) / 1000);
  }
}
