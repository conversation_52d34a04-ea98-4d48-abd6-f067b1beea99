/**
 * Role Service
 * Rol yönetimi için MongoDB servis fonksiyonları
 */

import { v4 as uuidv4 } from 'uuid';
import logger from '../../utils/logger.js';
import { db, ensureMongoDBConnection, isMongoDBInitialized } from './dbConnection.js';
import { Role, Permission } from '../../models/team.js';

/**
 * Roles koleksiyonunu getir
 */
async function getRolesCollection() {
  await ensureMongoDBConnection();

  if (!db || !teamRolesCollection) {
    throw new Error('MongoDB connection or team roles collection not available');
  }

  return teamRolesCollection;
}

// Sistem rolleri kaldırıldı

/**
 * Takıma özel rol oluştur
 */
export async function createRole(teamId: string, roleData: {
  name: string;
  description?: string;
  permissions: Permission[];
  companyId?: string;
}): Promise<{
  success: boolean;
  roleId?: string;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // Gerekli alanları kontrol et
    if (!teamId) {
      return { success: false, message: 'Team ID is required' };
    }

    if (!roleData.name) {
      return { success: false, message: 'Role name is required' };
    }

    if (!roleData.permissions || !Array.isArray(roleData.permissions) || roleData.permissions.length === 0) {
      return { success: false, message: 'At least one permission is required' };
    }

    // İzinleri doğrula
    for (const permission of roleData.permissions) {
      if (!permission.resource || !permission.action) {
        return { success: false, message: 'Each permission must have resource and action' };
      }
    }

    // Aynı isimde rol var mı kontrol et
    const query: any = {
      team_id: teamId,
      name: roleData.name
    };

    // Şirket ID'si varsa ekle
    if (roleData.companyId) {
      query.company_id = roleData.companyId;
    }

    const existingRole = await collection.findOne(query);

    if (existingRole) {
      return { success: false, message: 'A role with this name already exists in this team' };
    }

    // Yeni rol oluştur
    const roleId = uuidv4();
    const now = new Date();

    const role: Role = {
      id: roleId,
      name: roleData.name,
      description: roleData.description || '',
      permissions: roleData.permissions,
      is_system: false,
      team_id: teamId, // Takım ID'sini ekle
      company_id: roleData.companyId || null, // Şirket ID'sini ekle
      created_at: now,
      updated_at: now
    };

    await collection.insertOne(role);

    logger.info(`Created new role with ID: ${roleId} for team: ${teamId}`);

    return {
      success: true,
      roleId: roleId
    };
  } catch (error: any) {
    logger.error(`Error creating role: ${error.message}`);
    return {
      success: false,
      message: `Failed to create role: ${error.message}`
    };
  }
}

/**
 * Rol bilgilerini getir
 */
export async function getRoleById(roleId: string): Promise<{
  success: boolean;
  role?: Role;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // ID ile rol bul
    const role = await collection.findOne({ id: roleId });

    if (!role) {
      return { success: false, message: 'Role not found' };
    }

    return {
      success: true,
      role: role as Role
    };
  } catch (error: any) {
    logger.error(`Error getting role: ${error.message}`);
    return {
      success: false,
      message: `Failed to get role: ${error.message}`
    };
  }
}

/**
 * Takıma ait rolleri getir
 */
export async function getRolesByTeamId(teamId: string, options: {
  includeSystem?: boolean;
} = {}): Promise<{
  success: boolean;
  roles?: Role[];
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const { includeSystem = true } = options;
    const rolePermissionsCollection = db.collection('role_permissions');

    const roles: Role[] = [];

    if (includeSystem) {
      // Sistem rollerini getir (sabit 4 rol)
      const systemRoles = await rolePermissionsCollection.find({
        is_system: true,
        user_id: null // Sadece rol tanımları, kullanıcı atamaları değil
      }).toArray();

      roles.push(...systemRoles.map((role: any) => ({
        id: role.role_id,
        name: role.role_name,
        description: role.description || '',
        permissions: role.permissions || [],
        is_system: true,
        team_id: null,
        company_id: null
      })));
    }

    // Takıma özel roller (eğer varsa)
    if (teamId) {
      const teamSpecificRoles = await rolePermissionsCollection.find({
        team_id: teamId,
        is_system: false,
        user_id: null // Sadece rol tanımları
      }).toArray();

      roles.push(...teamSpecificRoles.map((role: any) => ({
        id: role.role_id,
        name: role.role_name,
        description: role.description || '',
        permissions: role.permissions || [],
        is_system: false,
        team_id: teamId,
        company_id: role.company_id
      })));
    }

    return {
      success: true,
      roles
    };
  } catch (error: any) {
    logger.error(`Error getting roles: ${error.message}`);
    return {
      success: false,
      message: `Failed to get roles: ${error.message}`
    };
  }
}

/**
 * Tüm rolleri getir
 */
export async function getAllRoles(): Promise<{
  success: boolean;
  roles?: Role[];
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // Tüm rolleri getir
    const roles = await collection.find({}).toArray();

    return {
      success: true,
      roles: roles as Role[]
    };
  } catch (error: any) {
    logger.error(`Error getting all roles: ${error.message}`);
    return {
      success: false,
      message: `Failed to get all roles: ${error.message}`
    };
  }
}

/**
 * Rol bilgilerini güncelle
 */
export async function updateRole(roleId: string, updateData: {
  name?: string;
  description?: string;
  permissions?: Permission[];
}): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // Rolü bul
    const existingRole = await collection.findOne({ id: roleId });

    if (!existingRole) {
      return { success: false, message: 'Role not found' };
    }

    // Sistem rollerini güncellemeye izin verme
    if (existingRole.is_system) {
      return { success: false, message: 'System roles cannot be modified' };
    }

    // Güncellenecek verileri hazırla
    const update: any = {
      updated_at: new Date()
    };

    if (updateData.name) update.name = updateData.name;
    if (updateData.description !== undefined) update.description = updateData.description;

    if (updateData.permissions) {
      // İzinleri doğrula
      for (const permission of updateData.permissions) {
        if (!permission.resource || !permission.action) {
          return { success: false, message: 'Each permission must have resource and action' };
        }
      }
      update.permissions = updateData.permissions;
    }

    // Güncelleme işlemi
    const result = await collection.updateOne(
      { id: roleId },
      { $set: update as any }
    );

    if (result.matchedCount === 0) {
      return { success: false, message: 'Role not found or not modified' };
    }

    logger.info(`Updated role with ID: ${roleId}`);

    return {
      success: true,
      message: 'Role updated successfully'
    };
  } catch (error: any) {
    logger.error(`Error updating role: ${error.message}`);
    return {
      success: false,
      message: `Failed to update role: ${error.message}`
    };
  }
}

/**
 * Role izin ekle
 */
export async function addPermissionToRole(roleId: string, permission: Permission): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // Rolü bul
    const existingRole = await collection.findOne({ id: roleId });

    if (!existingRole) {
      return { success: false, message: 'Role not found' };
    }

    // Sistem rollerini güncellemeye izin verme
    if (existingRole.is_system) {
      return { success: false, message: 'System roles cannot be modified' };
    }

    // İzni doğrula
    if (!permission.resource || !permission.action) {
      return { success: false, message: 'Permission must have resource and action' };
    }

    // İzin zaten var mı kontrol et
    const permissionExists = existingRole.permissions.some(
      (p: Permission) => p.resource === permission.resource && p.action === permission.action
    );

    if (permissionExists) {
      return { success: false, message: 'Permission already exists in this role' };
    }

    // İzni ekle
    const result = await collection.updateOne(
      { id: roleId },
      {
        $push: { permissions: permission as any },
        $set: { updated_at: new Date() }
      }
    );

    if (result.matchedCount === 0) {
      return { success: false, message: 'Role not found or not modified' };
    }

    logger.info(`Added permission to role with ID: ${roleId}`);

    return {
      success: true,
      message: 'Permission added successfully'
    };
  } catch (error: any) {
    logger.error(`Error adding permission: ${error.message}`);
    return {
      success: false,
      message: `Failed to add permission: ${error.message}`
    };
  }
}

/**
 * Rolden izin kaldır
 */
export async function removePermissionFromRole(roleId: string, permission: Permission): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // Rolü bul
    const existingRole = await collection.findOne({ id: roleId });

    if (!existingRole) {
      return { success: false, message: 'Role not found' };
    }

    // Sistem rollerini güncellemeye izin verme
    if (existingRole.is_system) {
      return { success: false, message: 'System roles cannot be modified' };
    }

    // İzni doğrula
    if (!permission.resource || !permission.action) {
      return { success: false, message: 'Permission must have resource and action' };
    }

    // İzni kaldır
    const result = await collection.updateOne(
      { id: roleId },
      {
        $pull: {
          permissions: {
            resource: permission.resource,
            action: permission.action
          } as any
        },
        $set: { updated_at: new Date() }
      }
    );

    if (result.matchedCount === 0) {
      return { success: false, message: 'Role not found or not modified' };
    }

    logger.info(`Removed permission from role with ID: ${roleId}`);

    return {
      success: true,
      message: 'Permission removed successfully'
    };
  } catch (error: any) {
    logger.error(`Error removing permission: ${error.message}`);
    return {
      success: false,
      message: `Failed to remove permission: ${error.message}`
    };
  }
}

/**
 * Rolü sil (takıma özel roller için)
 */
export async function deleteRole(roleId: string): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamMembersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // Rolü bul
    const existingRole = await collection.findOne({ id: roleId });

    if (!existingRole) {
      return { success: false, message: 'Role not found' };
    }

    // Sistem rollerini silmeye izin verme
    if (existingRole.is_system) {
      return { success: false, message: 'System roles cannot be deleted' };
    }

    // Rol bir takım üyesi tarafından kullanılıyor mu kontrol et
    if (teamMembersCollection) {
      // Hem doğrudan roleId hem de roleId_teamId formatında kullanımı kontrol et
      const query = {
        $or: [
          { role_id: roleId },
          { role_id: { $regex: new RegExp(`^${roleId}_`) } } // roleId_ ile başlayan herhangi bir role_id
        ]
      };

      // Kullanım sayısını al
      const usageCount = await teamMembersCollection.countDocuments(query);

      if (usageCount > 0) {
        return {
          success: false,
          message: `This role is assigned to ${usageCount} team member(s). You must reassign these members before deleting the role.`
        };
      }
    }

    // Rolü sil
    const result = await collection.deleteOne({ id: roleId } as any);

    if (result.deletedCount === 0) {
      return { success: false, message: 'Role not found or not deleted' };
    }

    logger.info(`Deleted role with ID: ${roleId}`);

    return {
      success: true,
      message: 'Role deleted successfully'
    };
  } catch (error: any) {
    logger.error(`Error deleting role: ${error.message}`);
    return {
      success: false,
      message: `Failed to delete role: ${error.message}`
    };
  }
}

/**
 * Kullanıcının belirli bir kaynağa erişim yetkisini kontrol et
 */
export async function checkPermission(roleId: string, resource: string, action: string): Promise<{
  success: boolean;
  hasPermission: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, hasPermission: false, message: 'Database connection not established' };
    }

    const collection = await getRolesCollection();

    // Rolü bul
    const role = await collection.findOne({ id: roleId });

    if (!role) {
      return {
        success: false,
        hasPermission: false,
        message: 'Role not found'
      };
    }

    // Yetkileri kontrol et
    let hasPermission = false;

    // Özel izin kontrolü
    const exactPermission = role.permissions.some(
      (p: Permission) => p.resource === resource && p.action === action
    );

    if (exactPermission) {
      hasPermission = true;
    }

    // Genel izin kontrolü ("manage" action'ı tüm aksiyonlara izin verir)
    const managePermission = role.permissions.some(
      (p: Permission) => p.resource === resource && p.action === 'manage'
    );

    if (managePermission) {
      hasPermission = true;
    }

    // Tüm kaynaklara yönetim izni kontrolü
    const allPermission = role.permissions.some(
      (p: Permission) => p.resource === '*' && (p.action === 'manage' || p.action === '*')
    );

    if (allPermission) {
      hasPermission = true;
    }

    return {
      success: true,
      hasPermission,
      message: hasPermission ? 'Permission granted' : 'Permission denied'
    };
  } catch (error: any) {
    logger.error(`Error checking permission: ${error.message}`);
    return {
      success: false,
      hasPermission: false,
      message: `Failed to check permission: ${error.message}`
    };
  }
}

/**
 * Takım için varsayılan rolleri oluştur
 * @param teamId Takım ID'si
 * @param companyId Şirket ID'si (opsiyonel)
 * @returns Oluşturulan rol ID'leri
 */
export async function createDefaultTeamRoles(teamId: string, companyId?: string): Promise<{
  success: boolean;
  message?: string;
  roleIds?: string[];
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    logger.info(`Creating default roles for team: ${teamId}`);

    // Eğer companyId verilmemişse, takım bilgilerinden al
    let finalCompanyId = companyId;
    if (!finalCompanyId) {
      try {
        const { getTeamById } = await import('./teamService.js');
        const teamResult = await getTeamById(teamId);

        if (teamResult.success && teamResult.team && teamResult.team.companyId) {
          finalCompanyId = teamResult.team.companyId;

        } else {
          logger.warn(`Could not find company ID for team ${teamId}`);
        }
      } catch (error) {
        logger.warn(`Error getting company ID for team ${teamId}: ${error}`);
      }
    }

    const collection = await getRolesCollection();
    const roleIds: string[] = [];

    // Takıma özel varsayılan roller
    const defaultRoles = [
      {
        id: `company_owner_${teamId}`,
        name: 'Şirket Sahibi',
        description: 'Şirket sahibi rolü',
        permissions: [
          { resource: '*', action: 'manage' },
          // Klasör izinleri
          { resource: 'Folder', action: 'create' },
          { resource: 'Folder', action: 'edit' },
          { resource: 'Folder', action: 'delete' },
          // Senaryo izinleri
          { resource: 'Scenario', action: 'create' },
          { resource: 'Scenario', action: 'edit' },
          { resource: 'Scenario', action: 'delete' },
          { resource: 'Scenario', action: 'view' },
          // Çalıştırma izinleri
          { resource: 'Run', action: 'create' },
          { resource: 'Run', action: 'edit' },
          { resource: 'Run', action: 'delete' },
          { resource: 'Run', action: 'view' },
          // Zamanlama izinleri
          { resource: 'Schedule', action: 'create' },
          { resource: 'Schedule', action: 'edit' },
          { resource: 'Schedule', action: 'delete' }
        ],
        team_id: teamId,
        company_id: finalCompanyId
      },
      {
        id: `team_admin_${teamId}`,
        name: 'Takım Yöneticisi',
        description: 'Takım yöneticisi rolü',
        permissions: [
          // Klasör izinleri
          { resource: 'Folder', action: 'create' },
          { resource: 'Folder', action: 'edit' },
          { resource: 'Folder', action: 'delete' },
          // Senaryo izinleri
          { resource: 'Scenario', action: 'create' },
          { resource: 'Scenario', action: 'edit' },
          { resource: 'Scenario', action: 'delete' },
          { resource: 'Scenario', action: 'view' },
          // Çalıştırma izinleri
          { resource: 'Run', action: 'create' },
          { resource: 'Run', action: 'edit' },
          { resource: 'Run', action: 'delete' },
          { resource: 'Run', action: 'view' },
          { resource: 'Run', action: 'execute' },
          // Zamanlama izinleri
          { resource: 'Schedule', action: 'create' },
          { resource: 'Schedule', action: 'edit' },
          { resource: 'Schedule', action: 'delete' }
        ],
        team_id: teamId,
        company_id: finalCompanyId
      },
      {
        id: `tester_${teamId}`,
        name: 'Test Uzmanı',
        description: 'Test uzmanı rolü',
        permissions: [
          // Klasör izinleri
          { resource: 'Folder', action: 'create' },
          // Senaryo izinleri
          { resource: 'Scenario', action: 'create' },
          { resource: 'Scenario', action: 'edit' },
          { resource: 'Scenario', action: 'view' },
          // Çalıştırma izinleri
          { resource: 'Run', action: 'create' },
          { resource: 'Run', action: 'edit' },
          { resource: 'Run', action: 'view' },
          { resource: 'Run', action: 'execute' },
          // Zamanlama izinleri
          { resource: 'Schedule', action: 'create' },
          { resource: 'Schedule', action: 'edit' }
        ],
        team_id: teamId,
        company_id: finalCompanyId
      },
      {
        id: `viewer_${teamId}`,
        name: 'İzleyici',
        description: 'Sadece görüntüleme yetkisi',
        permissions: [
          { resource: 'Scenario', action: 'view' },
          { resource: 'Run', action: 'view' },
          { resource: 'Schedule', action: 'view' }
        ],
        team_id: teamId,
        company_id: finalCompanyId
      }
    ];

    // Her bir rolü oluştur
    for (const roleData of defaultRoles) {
      // Aynı ID veya isimde bir rol var mı kontrol et
      const query: any = {
        team_id: teamId,
        company_id: finalCompanyId || null
      };

      if (roleData.id) {
        query.$or = [
          { id: roleData.id },
          { name: roleData.name }
        ];
      } else {
        query.name = roleData.name;
      }

      const existingRole = await collection.findOne(query);

      if (existingRole) {
        logger.info(`Role ${roleData.name} already exists for team ${teamId}`);
        roleIds.push(existingRole.id);
        continue;
      }

      // Rol ID'si belirtilmişse onu kullan, yoksa yeni oluştur
      const roleId = roleData.id || uuidv4();
      const now = new Date();

      const role: Role = {
        id: roleId,
        name: roleData.name,
        description: roleData.description,
        permissions: roleData.permissions,
        is_system: false,
        team_id: teamId,
        company_id: finalCompanyId || null,
        created_at: now,
        updated_at: now
      };

      await collection.insertOne(role as any);
      logger.info(`Created default role ${roleData.name} with ID: ${roleId} for team: ${teamId}`);
      roleIds.push(roleId);
    }

    return {
      success: true,
      roleIds
    };
  } catch (error: any) {
    logger.error(`Error creating default team roles: ${error.message}`);
    return {
      success: false,
      message: `Failed to create default team roles: ${error.message}`
    };
  }
}