"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/api/auth"
import Header from '@/components/landing/Header'
import Hero from '@/components/landing/Hero'
import Features from '@/components/landing/Features'
import WhyAITesting from '@/components/landing/WhyAITesting'
import ContactSection from '@/components/landing/ContactSection'
import Footer from '@/components/landing/Footer'
import DashboardPopup from '@/components/landing/DashboardPopup'


export default function LandingPage() {
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Artık otomatik yönlendirme yapmıyoruz, bunun yerine popup göstereceğiz

  if (!mounted) return null

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        <Hero />
        <Features />
        <WhyAITesting />
        <ContactSection />
      </main>
      <Footer />
      {/* <PERSON><PERSON><PERSON><PERSON><PERSON> giriş yapmışsa dashboard popup'ını göster */}
      <DashboardPopup isAuthenticated={isAuthenticated} />
    </div>
  )
}
