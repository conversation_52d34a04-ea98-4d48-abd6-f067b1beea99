/**
 * WebSocket Connector
 * Provides a singleton instance for WebSocket communication using ws package
 */

import { WebSocketServer, WebSocket } from 'ws';
import { Server as HttpServer } from 'http';
import { logger } from '../../utils/logger.js';
import { RealTimeConnector } from '../interfaces.js';
import { IncomingMessage } from 'http';
import { URL } from 'url';
import { EventEmitter } from 'events';
import { nodeRegistry } from '../../services/node/NodeRegistry.js';
import { resultQueueService } from '../../services/result/ResultQueueService.js';
import { TestStatus } from '../../models/test-types.js';
import { MessageType } from '../../models/message-types.js';

/**
 * WebSocket Connector Class
 * Manages WebSocket connections with test nodes using ws package
 * Implements RealTimeConnector interface and extends EventEmitter for event handling
 */
export class WebSocketConnector extends EventEmitter implements RealTimeConnector {
  private static instance: WebSocketConnector;
  private wss: WebSocketServer | null = null;
  private connectedNodes: Map<string, WebSocket> = new Map(); // nodeId -> WebSocket
  private nodeIdBySocket: Map<WebSocket, string> = new Map(); // WebSocket -> nodeId
  private clientIdBySocket: Map<WebSocket, string> = new Map(); // WebSocket -> clientId
  private groups: Map<string, Set<WebSocket>> = new Map(); // group -> Set of WebSockets

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    super(); // Initialize EventEmitter
    // Initialize empty
  }

  /**
   * Get the singleton instance
   * @returns WebSocketConnector instance
   */
  public static getInstance(): WebSocketConnector {
    if (!WebSocketConnector.instance) {
      WebSocketConnector.instance = new WebSocketConnector();
    }
    return WebSocketConnector.instance;
  }

  /**
   * Initialize the WebSocket server
   * @param config Configuration object or HTTP server to attach to
   * @returns Promise resolving to success status
   */
  public async initialize(config?: any): Promise<boolean> {
    try {
      if (this.wss) {
        logger.warn('WebSocketConnector: Already initialized');
        return true;
      }

      // If config is an HTTP server, use it directly
      if (config instanceof HttpServer) {
        this.wss = new WebSocketServer({
          server: config,
          clientTracking: true,
          perMessageDeflate: false,
          maxPayload: 10 * 1024 * 1024, // 10MB
          skipUTF8Validation: false
        });
        this.setupEventHandlers();
        logger.info('WebSocketConnector: Initialized with HTTP server');
        return true;
      } else if (config && config.server instanceof HttpServer) {
        // If config contains a server property, use that
        this.wss = new WebSocketServer({
          server: config.server,
          clientTracking: true,
          perMessageDeflate: false,
          maxPayload: 10 * 1024 * 1024, // 10MB
          skipUTF8Validation: false
        });
        this.setupEventHandlers();
        logger.info('WebSocketConnector: Initialized with config.server');
        return true;
      } else {
        logger.error('WebSocketConnector: No valid HTTP server provided for initialization');
        return false;
      }
    } catch (error) {
      logger.error(`WebSocketConnector: Error initializing: ${error}`);
      return false;
    }
  }

  /**
   * Set up WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.wss) return;

    this.wss.on('connection', (ws: WebSocket, req: IncomingMessage) => {
      try {
        // Extract nodeId and clientId from URL query parameters or headers
        const url = new URL(req.url || '', `http://${req.headers.host}`);
        let nodeId = url.searchParams.get('nodeId');
        let clientId = url.searchParams.get('clientId');

        // Check for X-Node-ID header as test_node sends it this way
        if (!nodeId && req.headers['x-node-id']) {
          nodeId = req.headers['x-node-id'] as string;
          logger.info(`WebSocketConnector: Extracted nodeId ${nodeId} from X-Node-ID header`);
        }

        // Check for X-Client-ID header for stable node identification
        if (!clientId && req.headers['x-client-id']) {
          clientId = req.headers['x-client-id'] as string;
          logger.info(`WebSocketConnector: Extracted clientId ${clientId} from X-Client-ID header`);
        }

        // Also check for custom headers that test_node might be using
        if (!nodeId && req.headers['nodeid']) {
          nodeId = req.headers['nodeid'] as string;
          logger.info(`WebSocketConnector: Extracted nodeId ${nodeId} from nodeid header`);
        }

        // If we have a clientId but no nodeId, try to find existing node by clientId
        if (clientId && !nodeId) {
          const existingNode = this.findNodeByClientId(clientId);
          if (existingNode) {
            nodeId = existingNode.id;

          }
        }

        // Set up message handler - we'll extract nodeId from registration message if not provided
        ws.on('message', (message: WebSocket.Data) => {
          try {
            const parsedMessage = JSON.parse(message.toString());

            // If this is a registration message and we don't have a nodeId yet, extract it
            if (!nodeId && parsedMessage.type === 'register') {
              // Handle different registration message formats
              if (parsedMessage.data && parsedMessage.data.nodeId) {
                // Format: { type: 'register', data: { nodeId: '...' } }
                nodeId = parsedMessage.data.nodeId;
              } else if (parsedMessage.nodeId) {
                // Format: { type: 'register', nodeId: '...' }
                nodeId = parsedMessage.nodeId;
              }

              if (nodeId) {
                logger.info(`WebSocketConnector: Extracted nodeId ${nodeId} from registration message`);

                // Register the node now that we have an ID
                this.registerNode(nodeId, ws);
              }
            }

            // Handle the message
            if (nodeId) {
              this.handleMessage(nodeId, ws, parsedMessage);
            } else {
              logger.warn('WebSocketConnector: Received message without nodeId');
            }
          } catch (error) {
            logger.error(`WebSocketConnector: Error parsing message: ${error}`);
          }
        });

        // Set up close handler
        ws.on('close', (code: number, reason: Buffer) => {
          const reasonStr = reason ? reason.toString() : 'No reason provided';
          if (nodeId) {
            if (code === 1006) {
              logger.warn(`WebSocketConnector: Node ${nodeId} abnormal disconnection (code: ${code}, reason: ${reasonStr})`);
            } else {
              logger.info(`WebSocketConnector: Node ${nodeId} disconnected (code: ${code}, reason: ${reasonStr})`);
            }
            this.handleNodeDisconnectionWithCode(nodeId, code, reasonStr);
          } else {
            logger.info(`WebSocketConnector: Unknown client disconnected (code: ${code}, reason: ${reasonStr})`);
          }
        });

        // Set up error handler
        ws.on('error', (error) => {
          if (nodeId) {
            logger.error(`WebSocketConnector: Error with node ${nodeId}: ${error}`);
          } else {
            logger.error(`WebSocketConnector: Error with unknown client: ${error}`);
          }
        });

        // Handle WebSocket ping frames - respond with pong frames
        ws.on('ping', (data: Buffer) => {
          try {
            ws.pong(data);
            logger.debug(`WebSocketConnector: Responded to ping frame with pong frame`);

            // Also update node heartbeat if we have nodeId
            if (nodeId) {
              nodeRegistry.updateNodeHeartbeat(nodeId).catch(error => {
                logger.warn(`WebSocketConnector: Error updating node heartbeat on ping frame: ${error}`);
              });
            }
          } catch (error) {
            logger.error(`WebSocketConnector: Error responding to ping frame: ${error}`);
          }
        });

        // Store clientId if available for later use
        if (clientId) {
          this.clientIdBySocket.set(ws, clientId);
          logger.debug(`WebSocketConnector: Stored clientId ${clientId} for WebSocket connection`);
        }

        // If we already have a nodeId, register immediately
        if (nodeId) {
          this.registerNode(nodeId, ws);
          logger.info(`WebSocketConnector: Node ${nodeId} connected`);
        } else {
          logger.info('WebSocketConnector: Client connected without nodeId, waiting for registration message');
        }
      } catch (error) {
        logger.error(`WebSocketConnector: Error handling connection: ${error}`);
        ws.close();
      }
    });

    this.wss.on('error', (error) => {
      logger.error(`WebSocketConnector: Server error: ${error}`);
    });
  }

  /**
   * Handle incoming WebSocket messages
   * @param nodeId Node ID
   * @param ws WebSocket connection
   * @param message Parsed message
   */
  private async handleMessage(nodeId: string, ws: WebSocket, message: any): Promise<void> {
    if (!message || !message.type) {
      logger.warn(`WebSocketConnector: Received message without type from node ${nodeId}`);
      return;
    }

    // Handle different message types
    switch (message.type) {
      case 'ping':
        // Extract timestamp from ping message if available
        const pingTimestamp = message.timestamp || message.data?.timestamp || Date.now();
        const receivedTime = Date.now();

        logger.debug(`WebSocketConnector: Received ping from node ${nodeId} at ${new Date(receivedTime).toISOString()}`);
        logger.debug(`WebSocketConnector: Ping message timestamp: ${new Date(pingTimestamp).toISOString()}`);

        // Calculate and log the one-way latency (if timestamps are available)
        const oneWayLatency = receivedTime - pingTimestamp;
        if (oneWayLatency > 5000) {
          logger.warn(`WebSocketConnector: High latency detected for node ${nodeId}: ${oneWayLatency}ms`);
        } else {
          logger.debug(`WebSocketConnector: One-way latency: ${oneWayLatency}ms`);
        }

        // Send pong response in the format expected by test_node
        const pongTimestamp = Date.now();
        const pongSuccess = this.sendToSocket(ws, {
          type: 'pong',
          data: {
            nodeId,
            originalTimestamp: pingTimestamp,
            serverTimestamp: pongTimestamp
          },
          timestamp: pongTimestamp
        });

        if (pongSuccess) {
          logger.debug(`WebSocketConnector: Sent pong to node ${nodeId} at ${new Date(pongTimestamp).toISOString()}`);
        } else {
          logger.warn(`WebSocketConnector: Failed to send pong to node ${nodeId}`);
        }

        // Update node's last seen timestamp in the registry
        try {
          const node = nodeRegistry.getNode(nodeId);
          if (node) {
            // Update the node's last seen timestamp
            await nodeRegistry.updateNodeHeartbeat(nodeId);
            logger.debug(`WebSocketConnector: Updated heartbeat timestamp for node ${nodeId} on ping`);
          }
        } catch (error) {
          logger.warn(`WebSocketConnector: Error updating node heartbeat on ping: ${error}`);
        }

        if (!pongSuccess) {
          logger.warn(`WebSocketConnector: Failed to send pong response to node ${nodeId}`);
        }
        break;
      case 'register':
        // Node registration
        logger.info(`WebSocketConnector: Received registration from node ${nodeId}`);

        // Extract node capabilities and other info
        const nodeInfo = message.data || {};

        // Extract clientId from message data or stored connection data
        const storedClientId = this.clientIdBySocket.get(ws);
        let registrationClientId = nodeInfo.clientId || storedClientId;

        // Register the node with the NodeRegistry
        const nodeData = {
          id: nodeId,
          name: nodeInfo.name,
          capabilities: nodeInfo.capabilities || [],
          vncUrl: nodeInfo.vncUrl,
          vncPort: nodeInfo.vncPort,
          version: nodeInfo.version,
          clientId: registrationClientId
        };

        logger.info(`WebSocketConnector: Registering node ${nodeId} with clientId ${registrationClientId}`);

        // Register the node
        const registrationSuccess = await nodeRegistry.registerNode(nodeData);

        if (!registrationSuccess) {
          logger.error(`WebSocketConnector: Failed to register node ${nodeId}`);
          this.sendToSocket(ws, {
            type: 'registration-failed',
            data: {
              nodeId,
              error: 'Registration failed'
            },
            timestamp: Date.now()
          });
          return;
        }

        // Emit registration event for backward compatibility
        this.emit('node:registered', nodeData);

        // Send acknowledgement in the format expected by test_node
        // Use 'registered' type instead of 'register:success' to match MessageType.REGISTERED
        // and include nodeId in the data property as expected by test_node
        this.sendToSocket(ws, {
          type: 'registered',
          data: {
            nodeId,
            status: 'registered'
          },
          timestamp: Date.now()
        });
        break;
      case 'heartbeat':
        // Node heartbeat
        logger.debug(`WebSocketConnector: Received heartbeat from node ${nodeId}`);

        try {
          // Update the node's last seen timestamp
          const node = nodeRegistry.getNode(nodeId);
          if (node) {
            // Always update the heartbeat timestamp
            await nodeRegistry.updateNodeHeartbeat(nodeId);

            // If the node is reporting as available but we have it as busy, update it
            const status = message.data?.status || 'available';
            if (status === 'available' && node.status === 'busy') {
              logger.info(`WebSocketConnector: Node ${nodeId} reported as available but was marked as busy, updating status`);
              await nodeRegistry.markNodeAsAvailable(nodeId);
            }

            // Extract current test ID from heartbeat data if available
            const currentTestId = message.data?.currentTestId;
            
            // Update node's currentTestId if it has changed
            if (node.currentTestId !== currentTestId) {
              logger.info(`WebSocketConnector: Node ${nodeId} currentTestId changed from ${node.currentTestId || 'none'} to ${currentTestId || 'none'} via heartbeat`);
              node.currentTestId = currentTestId;
              
              // Emit node status response event for EventHandler to process
              this.emit('node:status-response', {
                nodeId: nodeId,
                isRunning: currentTestId !== null && currentTestId !== 'none',
                testId: currentTestId,
                timestamp: Date.now()
              });
            }
            
            // Legacy compatibility: If node reports a test ID but we have it as available, emit proper event
            if (currentTestId && currentTestId !== 'none' && node.status === 'available') {
              logger.info(`WebSocketConnector: Node ${nodeId} reported test ${currentTestId} but was marked as available, emitting testClaimed event`);
              this.emit('node:testClaimed', {
                nodeId: nodeId,
                testId: currentTestId,
                success: true
              });
            }
          } else {
            // Node not found, might need to re-register
            logger.warn(`WebSocketConnector: Heartbeat from unknown node ${nodeId}, requesting re-registration`);

            // Check if this is a reconnected node that we should re-register
            const clientId = message.data?.clientId;
            if (clientId) {
              logger.info(`WebSocketConnector: Node ${nodeId} with client ID ${clientId} attempting to reconnect, allowing re-registration`);
            }

            this.sendToSocket(ws, {
              type: 'registration-required',
              data: {
                nodeId,
                reason: 'Node not found in registry'
              },
              timestamp: Date.now()
            });
            return;
          }

          // Emit heartbeat event for backward compatibility
          this.emit('node:heartbeat', {
            nodeId,
            status: message.data?.status || 'available',
            timestamp: Date.now()
          });

          // Send acknowledgement in the format expected by test_node
          const ackSuccess = this.sendToSocket(ws, {
            type: 'heartbeat-ack',
            data: {
              nodeId
            },
            timestamp: Date.now()
          });

          if (!ackSuccess) {
            logger.warn(`WebSocketConnector: Failed to send heartbeat acknowledgement to node ${nodeId}`);
          }
        } catch (error) {
          logger.error(`WebSocketConnector: Error processing heartbeat from node ${nodeId}: ${error}`);

          // Try to send an error response
          try {
            this.sendToSocket(ws, {
              type: 'heartbeat-error',
              data: {
                nodeId,
                error: 'Internal server error',
                message: 'Error processing heartbeat'
              },
              timestamp: Date.now()
            });
          } catch (sendError) {
            logger.error(`WebSocketConnector: Error sending heartbeat error response: ${sendError}`);
          }
        }
        break;
      case 'test:result':
        // Test result
        const resultTestId = message.data?.testId;
        logger.info(`WebSocketConnector: Received test result from node ${nodeId} for test ${resultTestId}`);

        if (!resultTestId) {
          logger.warn(`WebSocketConnector: Received test result without testId from node ${nodeId}`);
          break;
        }

        // Add the result to the result queue
        try {
          // Prepare the result data with additional context
          const resultData: {
            testId: string;
            nodeId: string;
            status: string;
            result: any;
            timestamp: number;
            source: string;
            runId?: string;
            executionId?: string;
            scenarioId?: string;
            executedUser?: string;
            executedUserName?: string;
            previousStatus?: string;
            transactionId?: string;
          } = {
            testId: resultTestId,
            nodeId,
            status: message.data?.status || 'unknown',
            result: message.data || {},
            timestamp: Date.now(),
            source: 'websocket',
            // Ekstra bilgileri ekle
            runId: message.data?.runId,
            executionId: message.data?.executionId,
            scenarioId: message.data?.scenarioId,
            executedUser: message.data?.executedUser || message.data?.userId,
            executedUserName: message.data?.executedUserName,
            // Önceki durum bilgisini ekle (varsa)
            previousStatus: message.data?.previousStatus
          };

          logger.info(`WebSocketConnector: Processing test result for ${resultTestId} from node ${nodeId} with status ${resultData.status}, runId: ${resultData.runId || 'unknown'}, executionId: ${resultData.executionId || 'unknown'}`);

          // Generate a transaction ID. Prefer executionId for stability.
          let transactionId: string;
          if (resultData.executionId) {
            transactionId = `ws-exec-${resultData.executionId}`; // Stable ID based on executionId
            logger.info(`WebSocketConnector: Using executionId-based transactionId: ${transactionId} for test ${resultTestId}, executionId: ${resultData.executionId}`);
          } else {
            const randomSuffix = Math.random().toString(36).substring(2, 7);
            transactionId = `ws-time-${Date.now()}-${randomSuffix}`; // Fallback if no executionId
            logger.warn(`WebSocketConnector: No executionId found for test ${resultTestId}. Generating time-based transactionId: ${transactionId}. This may lead to issues if the same result is sent multiple times without an executionId.`);
          }
          resultData.transactionId = transactionId;

          // Send acknowledgement immediately to avoid blocking node
          this.sendToSocket(ws, {
            type: MessageType.TEST_RESULT_RECEIVED,
            data: {
              testId: resultTestId,
              nodeId
            },
            timestamp: Date.now()
          });

          // Add result to queue asynchronously to avoid blocking WebSocket response
          resultQueueService.addResult(resultTestId, resultData).catch(error => {
            logger.error(`WebSocketConnector: Error adding result to queue for test ${resultTestId}: ${error}`);
          });

          // Mark the node as available if the test is complete - do this asynchronously
          const status = message.data?.status || 'unknown';
          if (status === 'completed' || status === 'failed' || status === 'stopped') {
            logger.info(`WebSocketConnector: Test ${resultTestId} completed with status ${status}, marking node ${nodeId} as available`);
            nodeRegistry.markNodeAsAvailable(nodeId).catch(error => {
              logger.error(`WebSocketConnector: Error marking node ${nodeId} as available: ${error}`);
            });
          }
        } catch (error) {
          logger.error(`WebSocketConnector: Error processing test result for test ${resultTestId} from node ${nodeId}: ${error}`);
        }

        // REMOVED: test:result event emission to prevent duplicate processing
        // Test results are handled directly by centralResultProcessor
        // No need for additional event emission that could cause circular processing
        break;
      case 'test:request':
        // Node is requesting a test (pull model)
        logger.info(`WebSocketConnector: Node ${nodeId} is requesting a test`);

        // REMOVED: test:request event emission to prevent event pollution
        // Test requests are handled directly by the pull-based queue system
        // No need for additional event emission
        break;

      case 'test:started':
        // Node is reporting that a test has started
        const startedTestId = message.data?.testId;
        logger.info(`WebSocketConnector: Node ${nodeId} started test ${startedTestId}`);

        // Emit proper event instead of calling markNodeAsBusy directly
        if (startedTestId) {
          this.emit('node:testClaimed', {
            nodeId: nodeId,
            testId: startedTestId,
            success: true
          });

          // Update test status to running in database
          try {
            // Import testManager here to avoid circular dependency
            const { testManager } = await import('../../core/test-manager/index.js');

            const transactionId = `test-started-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

            logger.info(`[TEST-STARTED] Updating test ${startedTestId} status to RUNNING (transaction: ${transactionId})`);

            // Update test status in the database
            await testManager.updateTestStatus(startedTestId, TestStatus.RUNNING, {
              nodeId,
              transactionId,
              source: 'test:started'
            });



            // Also update scenario status to running if we have the necessary data
            const runId = message.data?.runId;
            const scenarioId = message.data?.scenarioId;

            if (runId && scenarioId) {
              try {
                logger.info(`[TEST-STARTED] Updating scenario ${scenarioId} status to running in run ${runId} (transaction: ${transactionId})`);

                await testManager.updateScenarioStatus(runId, scenarioId, 'running', startedTestId);


              } catch (scenarioError) {
                logger.error(`[TEST-STARTED] Error updating scenario ${scenarioId} status to running: ${scenarioError} (transaction: ${transactionId})`);
              }
            } else {
              logger.warn(`[TEST-STARTED] Missing runId (${runId}) or scenarioId (${scenarioId}) for scenario status update (transaction: ${transactionId})`);
            }
          } catch (error) {
            logger.error(`[TEST-STARTED] Error updating test ${startedTestId} status to RUNNING: ${error}`);
          }
        }

        // Emit for backward compatibility
        this.emit('test:started', {
          nodeId,
          ...message.data
        });
        break;

      case 'device:unavailable':
        // Node is reporting that a device is unavailable
        const deviceTestId = message.data?.testId;
        const deviceId = message.data?.deviceId;

        logger.info(`WebSocketConnector: Node ${nodeId} reporting device ${deviceId} unavailable for test ${deviceTestId}`);

        if (deviceTestId && deviceId) {
          try {
            // Import dynamically to avoid circular dependencies
            const { sauceLabsDeviceAvailabilityManager } = await import('../../services/saucelabs/SauceLabsDeviceAvailabilityManager.js');

            // Get user ID from message or use a default
            const userId = message.data?.userId || 'unknown';

            // Update device status in cache
            await sauceLabsDeviceAvailabilityManager.handleDeviceUnavailableFromNode(
              deviceTestId,
              deviceId,
              userId
            );

            // Send acknowledgement
            this.sendToSocket(ws, {
              type: 'device-unavailable-received',
              data: {
                testId: deviceTestId,
                deviceId: deviceId
              },
              timestamp: Date.now()
            });

            // Log that BullMQ will handle the retry
            logger.info(`WebSocketConnector: Device unavailability for test ${deviceTestId} will be handled by BullMQ retry mechanism`);
          } catch (error) {
            logger.error(`WebSocketConnector: Error handling device unavailability: ${error}`);
          }
        } else {
          logger.warn(`WebSocketConnector: Received device:unavailable message without testId or deviceId from node ${nodeId}`);
        }
        break;

      case MessageType.TEST_STATUS_UPDATE:
        // Handle test status update
        const statusUpdateTestId = message.data?.testId;
        const status = message.data?.status;
        const runReportId = message.data?.runReportId;
        const transactionId = message.data?.transactionId || `auto-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

        if (!statusUpdateTestId || !status) {
          logger.warn(`WebSocketConnector: Received test-status-update without required fields from node ${nodeId}`);
          break;
        }

        // Enhanced logging for status update verification
        logger.info(`[STATUS-VERIFICATION] Received test status update:
          - Test ID: ${statusUpdateTestId}
          - Status: ${status}
          - Run Report ID: ${runReportId || 'N/A'}
          - Node ID: ${nodeId}
          - Transaction ID: ${transactionId}
          - Timestamp: ${new Date().toISOString()}`);

        try {
          // Import the atomic report service to update scenario status in run report
          const { updateScenarioStatusAtomic } = await import('../../services/mongo/atomicReportService.js');

          // Map the status string to scenario status
          let scenarioStatus: 'queued' | 'running' | 'passed' | 'failed' | 'stopped' | undefined;
          switch (status) {
            case 'running':
              scenarioStatus = 'running';
              logger.info(`[STATUS-VERIFICATION] Mapping status 'running' to scenario status 'running' for test ${statusUpdateTestId}`);
              break;
            case 'completed':
              scenarioStatus = 'passed';
              logger.info(`[STATUS-VERIFICATION] Mapping status 'completed' to scenario status 'passed' for test ${statusUpdateTestId}`);
              break;
            case 'failed':
              scenarioStatus = 'failed';
              logger.info(`[STATUS-VERIFICATION] Mapping status 'failed' to scenario status 'failed' for test ${statusUpdateTestId}`);
              break;
            case 'stopped':
              scenarioStatus = 'stopped';
              logger.info(`[STATUS-VERIFICATION] Mapping status 'stopped' to scenario status 'stopped' for test ${statusUpdateTestId}`);
              break;
            default:
              logger.warn(`[STATUS-VERIFICATION] Unknown status '${status}' for test ${statusUpdateTestId}, skipping update`);
              scenarioStatus = undefined;
              break;
          }

          if (scenarioStatus) {
            // Get test information from queue to find runId, executionId, and scenarioId
            logger.info(`[STATUS-VERIFICATION] Getting test information for ${statusUpdateTestId} to update scenario status (transaction: ${transactionId})`);

            const { testManager } = await import('../../core/test-manager/index.js');
            const test = await testManager.getTest(statusUpdateTestId);

            if (!test) {
              logger.error(`[STATUS-VERIFICATION] CRITICAL: Test ${statusUpdateTestId} not found in queue, cannot update scenario status (transaction: ${transactionId})`);
            } else {
              const { runId, executionId, scenarioId } = test;

              if (!runId || !executionId || !scenarioId) {
                logger.error(`[STATUS-VERIFICATION] CRITICAL: Missing required fields for test ${statusUpdateTestId} - runId: ${runId}, executionId: ${executionId}, scenarioId: ${scenarioId} (transaction: ${transactionId})`);
              } else {
                logger.info(`[STATUS-VERIFICATION] Updating scenario ${scenarioId} status to ${scenarioStatus} in run ${runId} (transaction: ${transactionId})`);

                // Update scenario status in run report
                const updateResult = await updateScenarioStatusAtomic(runId, executionId, scenarioId, scenarioStatus, {
                  testId: statusUpdateTestId,
                  startTime: scenarioStatus === 'running' ? new Date() : undefined,
                  completionTime: (scenarioStatus === 'passed' || scenarioStatus === 'failed' || scenarioStatus === 'stopped') ? new Date() : undefined,
                  source: 'WebSocket-StatusUpdate',
                  transactionId: transactionId
                });

                if (updateResult.success) {
                  logger.info(`[STATUS-VERIFICATION] SUCCESS: Updated scenario ${scenarioId} status to ${scenarioStatus} in run ${runId} (transaction: ${transactionId})`);
                } else {
                  logger.error(`[STATUS-VERIFICATION] CRITICAL: Failed to update scenario ${scenarioId} status: ${updateResult.message} (transaction: ${transactionId})`);
                }
              }
            }

            // Send acknowledgement back to the node
            const ws = this.connectedNodes.get(nodeId);
            if (ws && ws.readyState === WebSocket.OPEN) {
              this.sendToSocket(ws, {
                type: 'status-update-ack',
                data: {
                  testId: statusUpdateTestId,
                  status: scenarioStatus,
                  transactionId,
                  timestamp: Date.now()
                }
              });
              logger.info(`[STATUS-VERIFICATION] Sent status update acknowledgement to node ${nodeId} for test ${statusUpdateTestId} (transaction: ${transactionId})`);
            }
          }
        } catch (error) {
          logger.error(`[STATUS-VERIFICATION] Error processing test status update for test ${statusUpdateTestId}: ${error} (transaction: ${transactionId})`);
        }

        // REMOVED: Emit event to prevent duplicate processing
        // Test status updates are handled directly by testManager.updateTestStatus
        // No need to emit additional events that could cause circular processing

        logger.info(`[STATUS-VERIFICATION] Test status update processed via scenario status update for test ${statusUpdateTestId} (transaction: ${transactionId})`);
        break;

      case MessageType.TEST_CLAIMED:
        // Handle test claimed notification
        const claimedTestId = message.data?.testId;
        const claimedNodeId = message.data?.nodeId;

        if (!claimedTestId || !claimedNodeId) {
          logger.warn(`WebSocketConnector: Received test-claimed without required fields from node ${nodeId}`);
          break;
        }

        logger.info(`WebSocketConnector: Node ${claimedNodeId} claimed test ${claimedTestId}`);

        try {
          // Emit the proper event instead of calling markNodeAsBusy directly
          // This will be handled by NodeRegistry's event listener
          this.emit('node:testClaimed', {
            nodeId: claimedNodeId,
            testId: claimedTestId,
            success: true
          });

          // Send acknowledgement back to the node
          this.sendToSocket(ws, {
            type: MessageType.TEST_CLAIMED_ACK,
            data: {
              testId: claimedTestId,
              nodeId: claimedNodeId,
              timestamp: Date.now()
            }
          });

        } catch (error) {
          logger.error(`WebSocketConnector: Error processing test claim for test ${claimedTestId} by node ${claimedNodeId}: ${error}`);
        }
        break;

      case 'test-completed':
        // Handle test completed notification
        const completedTestId = message.data?.testId;
        const completedNodeId = message.data?.nodeId;
        const testResult = message.data?.result;

        if (!completedTestId || !completedNodeId) {
          logger.warn(`WebSocketConnector: Received test-completed without required fields from node ${nodeId}`);
          break;
        }

        logger.info(`WebSocketConnector: Node ${completedNodeId} completed test ${completedTestId}`);

        try {
          // Emit event for test completion
          this.emit('node:testCompleted', {
            nodeId: completedNodeId,
            testId: completedTestId,
            result: testResult,
            success: true
          });

          // Send acknowledgement back to the node
          this.sendToSocket(ws, {
            type: 'test-completed-ack',
            data: {
              testId: completedTestId,
              nodeId: completedNodeId,
              timestamp: Date.now()
            }
          });

        } catch (error) {
          logger.error(`WebSocketConnector: Error processing test completion for test ${completedTestId} by node ${completedNodeId}: ${error}`);
        }
        break;

      case 'test-failed':
        // Handle test failed notification
        const failedTestId = message.data?.testId;
        const failedNodeId = message.data?.nodeId;
        const testError = message.data?.error;

        if (!failedTestId || !failedNodeId) {
          logger.warn(`WebSocketConnector: Received test-failed without required fields from node ${nodeId}`);
          break;
        }

        logger.info(`WebSocketConnector: Node ${failedNodeId} failed test ${failedTestId}: ${testError}`);

        try {
          // Emit event for test failure
          this.emit('node:testFailed', {
            nodeId: failedNodeId,
            testId: failedTestId,
            error: testError,
            success: false
          });

          // Send acknowledgement back to the node
          this.sendToSocket(ws, {
            type: 'test-failed-ack',
            data: {
              testId: failedTestId,
              nodeId: failedNodeId,
              timestamp: Date.now()
            }
          });

        } catch (error) {
          logger.error(`WebSocketConnector: Error processing test failure for test ${failedTestId} by node ${failedNodeId}: ${error}`);
        }
        break;

      // Step progress handling removed - now handled by Redis queue worker

      case 'get_step_progress':
        // Handle step progress request from nodes
        const requestTestId = message.testId;
        const requestId = message.requestId;

        if (!requestTestId) {
          logger.warn(`WebSocketConnector: Received get_step_progress message without testId from node ${nodeId}`);
          break;
        }

        try {
          // Import step progress service
          const { stepProgressService } = await import('../../services/step-progress/stepProgressService.js');

          // Get step progress from Redis
          const stepProgress = await stepProgressService.getTestStepProgress(requestTestId);

          // Send response back to node
          this.sendToSocket(ws, {
            type: 'step_progress_response',
            data: {
              testId: requestTestId,
              requestId,
              success: !!stepProgress,
              stepProgress: stepProgress || null
            },
            timestamp: Date.now()
          });

          logger.debug(`WebSocketConnector: Sent step progress response for test ${requestTestId} to node ${nodeId}`);
        } catch (error: any) {
          logger.error(`WebSocketConnector: Error getting step progress for test ${requestTestId} from node ${nodeId}: ${error}`);

          // Send error response
          this.sendToSocket(ws, {
            type: 'step_progress_response',
            data: {
              testId: requestTestId,
              requestId,
              success: false,
              error: error?.message || error?.toString() || 'Unknown error occurred'
            },
            timestamp: Date.now()
          });
        }
        break;

      case 'video-upload-completed':
        // Video upload completed notification
        const videoTestId = message.data?.testId;
        const videoUrl = message.data?.videoUrl;

        logger.info(`WebSocketConnector: Received video upload completed from node ${nodeId} for test ${videoTestId}`);

        if (!videoTestId || !videoUrl) {
          logger.warn(`WebSocketConnector: Received video upload completed without testId or videoUrl from node ${nodeId}`);
          break;
        }

        try {
          // Update test result with video URL
          await this.handleVideoUploadCompleted(videoTestId, videoUrl, nodeId);

          // Send acknowledgement
          this.sendToSocket(ws, {
            type: 'video-upload-received',
            data: {
              testId: videoTestId,
              nodeId
            },
            timestamp: Date.now()
          });
        } catch (error) {
          logger.error(`WebSocketConnector: Error handling video upload completed for test ${videoTestId}: ${error}`);
        }
        break;

      // Node coordination responses (migrated from Redis)
      case 'node-status-response':
        logger.debug(`WebSocketConnector: Received node-status-response from node ${nodeId}`);
        this.emit('node:status-response', {
          nodeId,
          isRunning: message.data?.isRunning,
          testId: message.data?.testId,
          timestamp: message.data?.timestamp
        });
        break;

      case 'current-test-id-response':
        logger.debug(`WebSocketConnector: Received current-test-id-response from node ${nodeId}`);
        this.emit('node:current-test-id-response', {
          nodeId,
          testId: message.data?.testId,
          timestamp: message.data?.timestamp
        });
        break;

      default:
        // Log unknown message types but don't emit generic events to prevent pollution
        logger.debug(`WebSocketConnector: Received unhandled message type ${message.type} from node ${nodeId}`);

        // Only emit specific events that are actually needed
        if (message.type === 'heartbeat' || message.type === 'node-status') {
          this.emit(`node:${message.type}`, {
            nodeId,
            ...message.data
          });
        }
        break;
    }
  }

  /**
   * Handle video upload completed notification
   * @param testId Test ID
   * @param videoUrl Video URL
   * @param nodeId Node ID
   */
  private async handleVideoUploadCompleted(testId: string, videoUrl: string, nodeId: string): Promise<void> {
    try {
      logger.info(`WebSocketConnector: Handling video upload completed for test ${testId} from node ${nodeId}`);

      // Import report service to update the test result with video URL
      const { updateVideoUrl } = await import('../../services/mongo/reportService.js');

      // Update test result with video URL
      const updateResult = await updateVideoUrl(testId, videoUrl);

      if (!updateResult.success) {
        throw new Error(updateResult.message);
      }

      logger.info(`WebSocketConnector: Updated test ${testId} with video URL: ${videoUrl}`);
    } catch (error: any) {
      logger.error(`WebSocketConnector: Error updating test ${testId} with video URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register a node with the WebSocket connector
   * @param nodeId Node ID
   * @param ws WebSocket connection
   */
  public registerNode(nodeId: string, ws: WebSocket): void {
    this.connectedNodes.set(nodeId, ws);
    this.nodeIdBySocket.set(ws, nodeId);
    logger.info(`WebSocketConnector: Registered node ${nodeId}`);
  }

  /**
   * Handle node disconnection with enhanced code analysis
   * 
   * DISCONNECTION FLOW:
   * 1. WebSocket 'close' event triggers this method
   * 2. This method calls unregisterNode() to clean up WebSocket-specific state
   * 3. This method emits 'node:disconnected' event for other components
   * 4. EventHandler receives event and calls NodeRegistry.handleImmediateNodeDisconnection()
   * 5. NodeRegistry handles registry-specific cleanup (does NOT attempt WebSocket cleanup)
   * 
   * This flow ensures single-responsibility and prevents duplicate cleanup operations.
   * 
   * @param nodeId Node ID
   * @param code WebSocket close code
   * @param reason Close reason
   */
  private handleNodeDisconnectionWithCode(nodeId: string, code: number, reason: string): void {
    // Analyze disconnection reason
    let disconnectionType = 'unknown';
    let shouldAttemptReconnect = false;

    switch (code) {
      case 1000: // Normal closure
        disconnectionType = 'normal';
        break;
      case 1001: // Going away
        disconnectionType = 'going_away';
        break;
      case 1006: // Abnormal closure (no close frame)
        disconnectionType = 'abnormal';
        shouldAttemptReconnect = true;
        break;
      case 1011: // Server error
        disconnectionType = 'server_error';
        shouldAttemptReconnect = true;
        break;
      case 1012: // Service restart
        disconnectionType = 'service_restart';
        shouldAttemptReconnect = true;
        break;
      default:
        disconnectionType = `code_${code}`;
        shouldAttemptReconnect = code >= 1006; // Abnormal closures
    }

    logger.info(`WebSocketConnector: Node ${nodeId} disconnection analysis - Type: ${disconnectionType}, Should reconnect: ${shouldAttemptReconnect}`);

    // Clean up WebSocket-specific state (maps, groups, etc.)
    this.unregisterNode(nodeId);

    // Emit disconnection event for other components to handle their own cleanup
    // NOTE: Other components should NOT attempt WebSocket cleanup as it's already done here
    this.emit('node:disconnected', {
      nodeId,
      code,
      reason,
      disconnectionType,
      shouldAttemptReconnect,
      timestamp: Date.now()
    });
  }

  /**
   * Unregister a node from the WebSocket connector
   * @param nodeId Node ID
   */
  public unregisterNode(nodeId: string): void {
    const ws = this.connectedNodes.get(nodeId);
    if (ws) {
      this.nodeIdBySocket.delete(ws);
      this.clientIdBySocket.delete(ws);

      // Remove from all groups
      for (const [groupName, sockets] of this.groups.entries()) {
        if (sockets.has(ws)) {
          sockets.delete(ws);
          logger.debug(`WebSocketConnector: Removed node ${nodeId} from group ${groupName}`);
        }
      }
    }

    this.connectedNodes.delete(nodeId);
    logger.info(`WebSocketConnector: Unregistered node ${nodeId}`);
  }

  /**
   * Send data to a specific WebSocket
   * @param ws WebSocket to send to
   * @param data Data to send
   * @returns Success status
   */
  private sendToSocket(ws: WebSocket, data: any): boolean {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(data));
        return true;
      } else {
        const nodeId = this.nodeIdBySocket.get(ws);
        logger.warn(`WebSocketConnector: Cannot send to node ${nodeId}, socket not open (state: ${ws.readyState})`);
        return false;
      }
    } catch (error) {
      logger.error(`WebSocketConnector: Error sending to socket: ${error}`);
      return false;
    }
  }

  /**
   * Check if a node is connected via WebSocket
   * @param nodeId Node ID
   * @returns True if connected, false otherwise
   */
  public isNodeConnected(nodeId: string): boolean {
    return this.connectedNodes.has(nodeId);
  }

  /**
   * Send a test to a node - Implementation for RealTimeConnector interface
   *
   * In the Pull Model, tests are not sent directly to nodes.
   * Instead, they are added to the central queue for nodes to pull.
   *
   * @param nodeId Node ID (unused in Pull Model)
   * @param test Test to send
   * @returns Always false in Pull Model
   */
  public sendTestToNode(_nodeId: string, _test: any): boolean {
    logger.warn(`WebSocketConnector: sendTestToNode is deprecated in Pull Model. Tests should be added to the central queue instead.`);
    // No-op in Pull Model - tests are pulled by nodes from the central queue
    return false;
  }

  /**
   * Send a stop test command to a node
   * @param nodeId Node ID
   * @param testId Test ID to stop
   * @returns Success status
   */
  public sendStopTestToNode(nodeId: string, testId: string): boolean {
    // Check if the node is connected
    const ws = this.connectedNodes.get(nodeId);
    if (!ws) {
      logger.warn(`WebSocketConnector: Node ${nodeId} not connected, cannot stop test ${testId}`);
      return false;
    }

    // Check if the node is registered
    const node = nodeRegistry.getNode(nodeId);
    if (!node) {
      logger.warn(`WebSocketConnector: Node ${nodeId} not registered, cannot stop test ${testId}`);
      return false;
    }

    // Check if the node is running this test
    if (node.currentTestId !== testId) {
      logger.warn(`WebSocketConnector: Node ${nodeId} is not running test ${testId} (current test: ${node.currentTestId || 'none'})`);
      // We'll still try to send the stop command in case the node registry is out of sync
    }

    try {
      // Use a consistent message format for stopping tests
      const stopMessage = {
        type: 'stop-test',
        data: {
          testId,
          nodeId
        }
      };

      logger.info(`WebSocketConnector: Sending stop command message to node ${nodeId}: ${JSON.stringify(stopMessage)}`);

      const success = this.sendToSocket(ws, stopMessage);

      if (success) {
        logger.info(`WebSocketConnector: Successfully sent stop command for test ${testId} to node ${nodeId}`);
      } else {
        logger.error(`WebSocketConnector: Failed to send stop command for test ${testId} to node ${nodeId} - sendToSocket returned false`);
      }

      return success;
    } catch (error) {
      logger.error(`WebSocketConnector: Error sending stop command to node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Get the WebSocket server instance
   * @returns WebSocket server instance
   */
  public getWSS(): WebSocketServer | null {
    return this.wss;
  }

  /**
   * Close the WebSocket server
   * @returns Promise that resolves when the server is closed
   */
  public async close(): Promise<void> {
    if (this.wss) {
      return new Promise((resolve) => {
        this.wss!.close(() => {
          this.wss = null;
          this.connectedNodes.clear();
          this.nodeIdBySocket.clear();
          this.clientIdBySocket.clear();
          this.groups.clear();
          logger.info('WebSocketConnector: Closed');
          resolve();
        });
      });
    }
  }

  /**
   * Alias for close() for backward compatibility
   */
  public stop(): Promise<void> {
    return this.close();
  }

  /**
   * Check if the connector is connected
   * @returns True if connected
   */
  public isConnected(): boolean {
    return this.wss !== null;
  }

  /**
   * Send a message to a specific topic
   * @param topic Topic to send to
   * @param data Data to send
   * @returns Promise resolving to success status
   */
  public async sendMessage(topic: string, data: any): Promise<boolean> {
    if (!this.wss) {
      logger.error('WebSocketConnector: Not initialized');
      return false;
    }

    try {
      // Broadcast to all clients
      this.broadcastToAll(topic, data);
      return true;
    } catch (error) {
      logger.error(`WebSocketConnector: Error sending message to topic ${topic}: ${error}`);
      return false;
    }
  }

  /**
   * Broadcast a message to all connected clients
   * @param event Event name
   * @param data Data to send
   */
  public broadcastToAll(event: string, data: any): void {
    if (!this.wss) {
      logger.error('WebSocketConnector: Not initialized');
      return;
    }

    try {
      const message = JSON.stringify({ type: event, data });
      let successCount = 0;
      let failCount = 0;

      this.wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(message);
          successCount++;
        } else {
          failCount++;
        }
      });

      logger.debug(`WebSocketConnector: Broadcast message to all clients: ${event} (success: ${successCount}, fail: ${failCount})`);
    } catch (error) {
      logger.error(`WebSocketConnector: Error broadcasting to all: ${error}`);
    }
  }

  /**
   * Broadcast a message to a group of clients
   * @param group Group name
   * @param event Event name
   * @param data Data to send
   */
  public broadcastToGroup(group: string, event: string, data: any): void {
    if (!this.wss) {
      logger.error('WebSocketConnector: Not initialized');
      return;
    }

    try {
      const groupSockets = this.groups.get(group);
      if (!groupSockets || groupSockets.size === 0) {
        logger.warn(`WebSocketConnector: No clients in group ${group}`);
        return;
      }

      const message = JSON.stringify({ type: event, data });
      let successCount = 0;
      let failCount = 0;

      groupSockets.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(message);
          successCount++;
        } else {
          failCount++;
        }
      });

      logger.debug(`WebSocketConnector: Broadcast message to group ${group}: ${event} (success: ${successCount}, fail: ${failCount})`);
    } catch (error) {
      logger.error(`WebSocketConnector: Error broadcasting to group ${group}: ${error}`);
    }
  }

  /**
   * Get all connected clients
   * @returns Map of connected clients
   */
  public getConnectedClients(): Map<string, WebSocket> {
    return new Map(this.connectedNodes);
  }

  /**
   * Disconnect a client
   * @param clientId Client ID to disconnect
   * @returns Success status
   */
  public disconnectClient(clientId: string): boolean {
    try {
      // If clientId is a node ID, get the WebSocket
      const ws = this.connectedNodes.get(clientId);
      if (!ws) {
        // Use debug level instead of warn since this is normal during cleanup
        logger.debug(`WebSocketConnector: Cannot disconnect client ${clientId}, not found (already disconnected)`);
        return false;
      }

      // Close the WebSocket connection
      ws.close();
      logger.info(`WebSocketConnector: Disconnected client ${clientId}`);
      return true;
    } catch (error) {
      logger.error(`WebSocketConnector: Error disconnecting client ${clientId}: ${error}`);
      return false;
    }
  }

  /**
   * Check if a client is connected
   * @param clientId Client ID to check
   * @returns True if connected
   */
  public isClientConnected(clientId: string): boolean {
    // If clientId is a node ID, check if it's in the map
    return this.connectedNodes.has(clientId);
  }

  /**
   * Add a client to a group
   * @param clientId Client ID
   * @param group Group name
   */
  public addClientToGroup(clientId: string, group: string): void {
    try {
      // Get the WebSocket for this client
      const ws = this.connectedNodes.get(clientId);
      if (!ws) {
        logger.warn(`WebSocketConnector: Cannot add client ${clientId} to group ${group}, not found`);
        return;
      }

      // Get or create the group
      if (!this.groups.has(group)) {
        this.groups.set(group, new Set());
      }

      // Add the client to the group
      const groupSockets = this.groups.get(group)!;
      groupSockets.add(ws);

      logger.debug(`WebSocketConnector: Added client ${clientId} to group ${group}`);
    } catch (error) {
      logger.error(`WebSocketConnector: Error adding client ${clientId} to group ${group}: ${error}`);
    }
  }

  /**
   * Remove a client from a group
   * @param clientId Client ID
   * @param group Group name
   */
  public removeClientFromGroup(clientId: string, group: string): void {
    try {
      // Get the WebSocket for this client
      const ws = this.connectedNodes.get(clientId);
      if (!ws) {
        logger.warn(`WebSocketConnector: Cannot remove client ${clientId} from group ${group}, not found`);
        return;
      }

      // Get the group
      const groupSockets = this.groups.get(group);
      if (!groupSockets) {
        logger.warn(`WebSocketConnector: Group ${group} does not exist`);
        return;
      }

      // Remove the client from the group
      groupSockets.delete(ws);

      logger.debug(`WebSocketConnector: Removed client ${clientId} from group ${group}`);
    } catch (error) {
      logger.error(`WebSocketConnector: Error removing client ${clientId} from group ${group}: ${error}`);
    }
  }

  /**
   * Find a node by clientId for re-association
   * @param clientId Client ID to search for
   * @returns Node if found, null otherwise
   */
  private findNodeByClientId(clientId: string): any | null {
    try {
      // Get all nodes from the registry and find one with matching clientId
      const allNodes = nodeRegistry.getAllNodes();
      for (const [nodeId, node] of allNodes) {
        // Check if the node has a clientId property that matches
        if ((node as any).clientId === clientId) {
          logger.debug(`WebSocketConnector: Found node ${nodeId} with clientId ${clientId}`);
          return node;
        }
      }
      logger.debug(`WebSocketConnector: No node found with clientId ${clientId}`);
      return null;
    } catch (error) {
      logger.error(`WebSocketConnector: Error finding node by clientId ${clientId}: ${error}`);
      return null;
    }
  }

  // ===== COORDINATION METHODS (migrated from Redis) =====

  /**
   * Send node status query to a specific node (replaces Redis 'check:running')
   * @param nodeId Node ID to query
   * @returns True if sent successfully
   */
  public sendNodeStatusQuery(nodeId: string): boolean {
    const ws = this.connectedNodes.get(nodeId);
    if (!ws) {
      logger.warn(`WebSocketConnector: Node ${nodeId} not connected, cannot send node status query`);
      return false;
    }

    return this.sendToSocket(ws, {
      type: 'node-status-query',
      data: { nodeId },
      timestamp: Date.now()
    });
  }

  /**
   * Send get current test ID query to a specific node (replaces Redis 'get:current-test-id')
   * @param nodeId Node ID to query
   * @returns True if sent successfully
   */
  public sendGetCurrentTestIdQuery(nodeId: string): boolean {
    const ws = this.connectedNodes.get(nodeId);
    if (!ws) {
      logger.warn(`WebSocketConnector: Node ${nodeId} not connected, cannot send get current test ID query`);
      return false;
    }

    return this.sendToSocket(ws, {
      type: 'get-current-test-id',
      data: { nodeId },
      timestamp: Date.now()
    });
  }

  /**
   * Send test released notification to a specific node (replaces Redis 'test:released')
   * @param nodeId Node ID to notify
   * @param testId Test ID that was released
   * @returns True if sent successfully
   */
  public sendTestReleased(nodeId: string, testId: string): boolean {
    const ws = this.connectedNodes.get(nodeId);
    if (!ws) {
      logger.warn(`WebSocketConnector: Node ${nodeId} not connected, cannot send test released notification`);
      return false;
    }

    return this.sendToSocket(ws, {
      type: 'test-released',
      data: { testId, nodeId },
      timestamp: Date.now()
    });
  }
}
