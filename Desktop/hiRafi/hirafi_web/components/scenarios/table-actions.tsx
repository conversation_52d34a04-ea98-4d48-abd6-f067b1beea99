"use client"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Plus,
  Download,
  Upload,
  Trash2,
  MoreHorizontal,
  Play,
  Copy,
  RefreshCw,
  ChevronDown,
  LayoutGrid,
  List,
  FileText
} from "lucide-react"
import { useState } from "react"

interface TableActionsProps {
  selectedScenarios: string[]
  onCreateScenario: () => void
  onExportScenarios: () => void
  onImportScenarios: () => void
  onDeleteSelectedScenarios: () => void // This should now be an async function
  onDuplicateSelectedScenarios: () => void
  onRefreshScenarios: () => void
  hasSelection: boolean
  useGroupedView: boolean
  onToggleGroupedView: () => void
}

export function TableActions({
  selectedScenarios,
  onCreateScenario,
  onExportScenarios,
  onImportScenarios,
  onDeleteSelectedScenarios,
  onDuplicateSelectedScenarios,
  onRefreshScenarios,
  hasSelection,
  useGroupedView,
  onToggleGroupedView
}: TableActionsProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [inUseScenarios, setInUseScenarios] = useState<Array<{
    scenarioId: string;
    inUseByRuns: Array<{id: string, name: string}>
  }>>([])
  const [deleteErrorMessage, setDeleteErrorMessage] = useState<string | null>(null)

  const handleDeleteConfirm = () => {
    onDeleteSelectedScenarios()
    setDeleteDialogOpen(false)
    setInUseScenarios([])
    setDeleteErrorMessage(null)
  }

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center gap-2">
        <Button onClick={onCreateScenario} className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800">
          <Plus className="mr-2 h-4 w-4" />
          New Scenario
        </Button>

        {/* Bulk actions dropdown - only enabled when scenarios are selected */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              disabled={!hasSelection}
              className={!hasSelection ? "opacity-50 cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300" : "dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700"}
            >
              Actions
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="dark:bg-gray-800 dark:border-gray-700">

            <DropdownMenuItem onClick={onDuplicateSelectedScenarios} className="dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
              <Copy className="mr-2 h-4 w-4" />
              Duplicate Selected
            </DropdownMenuItem>
            <DropdownMenuSeparator className="dark:bg-gray-700" />
            <DropdownMenuItem
              onClick={() => setDeleteDialogOpen(true)}
              className="text-red-600 dark:text-red-400 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Selected
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          onClick={onToggleGroupedView}
          className="flex items-center gap-1 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm h-9"
        >
          {useGroupedView ? (
            <>
              <List className="h-4 w-4 mr-1" />
              Flat View
            </>
          ) : (
            <>
              <LayoutGrid className="h-4 w-4 mr-1" />
              Grouped View
            </>
          )}
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="flex items-center gap-1 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm h-9"
            >
              <Download className="mr-1 h-4 w-4" />
              Export
              <ChevronDown className="h-3.5 w-3.5 ml-1 text-gray-500 dark:text-gray-400" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48 dark:bg-gray-800 dark:border-gray-700">
            <DropdownMenuItem onClick={onExportScenarios} className="text-sm py-2 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
              <FileText className="mr-2 h-4 w-4" />
              Export as CSV
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onExportScenarios} className="text-sm py-2 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
              <FileText className="mr-2 h-4 w-4" />
              Export as PDF
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onExportScenarios} className="text-sm py-2 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
              <FileText className="mr-2 h-4 w-4" />
              Export Selected
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="outline"
          onClick={onImportScenarios}
          className="flex items-center gap-1 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm h-9"
        >
          <Upload className="mr-1 h-4 w-4" />
          Import
        </Button>
      </div>

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          setDeleteDialogOpen(open)
          if (!open) {
            setInUseScenarios([])
            setDeleteErrorMessage(null)
          }
        }}
      >
        <DialogContent className="dark:bg-gray-800 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="dark:text-gray-100">Delete Selected Scenarios</DialogTitle>
            <DialogDescription className="dark:text-gray-400">
              {!deleteErrorMessage ? (
                `Are you sure you want to delete ${selectedScenarios.length} selected scenario${selectedScenarios.length !== 1 ? 's' : ''}? This action cannot be undone.`
              ) : (
                <span className="text-rose-500 font-medium">{deleteErrorMessage}</span>
              )}
            </DialogDescription>
          </DialogHeader>

          {/* Show list of scenarios that are in use by active runs */}
          {inUseScenarios.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-sm mb-2 dark:text-gray-200">
                The following scenarios cannot be deleted because they are used in active runs:
              </p>
              <div className="max-h-40 overflow-y-auto border rounded-md p-2 bg-gray-50 dark:bg-gray-900 dark:border-gray-700">
                {inUseScenarios.map(scenario => (
                  <div key={scenario.scenarioId} className="mb-3">
                    <p className="text-sm font-medium dark:text-gray-300">Scenario ID: {scenario.scenarioId}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Used in runs:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      {scenario.inUseByRuns.map(run => (
                        <li key={run.id} className="text-xs dark:text-gray-400">
                          {run.name}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                To delete these scenarios, you must first complete or stop the runs that use them.
              </p>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false)
                setInUseScenarios([])
                setDeleteErrorMessage(null)
              }}
              className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              {inUseScenarios.length > 0 ? "OK" : "Cancel"}
            </Button>
            {inUseScenarios.length === 0 && (
              <Button
                variant="destructive"
                onClick={handleDeleteConfirm}
                className="dark:bg-red-900 dark:hover:bg-red-800"
              >
                Delete
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
