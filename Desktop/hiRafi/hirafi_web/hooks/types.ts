/**
 * Shared Types for Hooks
 *
 * Common types and interfaces used across multiple hooks
 */

/**
 * Standard API response structure
 */
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

/**
 * Loading states for hooks
 */
export interface LoadingState {
  isLoading: boolean
  isValidating?: boolean
  isError: boolean
  error?: string | null
}

/**
 * Extended loading state with operation-specific loading
 */
export interface ExtendedLoadingState extends LoadingState {
  isCreating?: boolean
  isUpdating?: boolean
  isDeleting?: boolean
  isSaving?: boolean
  isGenerating?: boolean
}

/**
 * CRUD operation result
 */
export interface OperationResult<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult<T = any> {
  success: boolean
  successCount?: number
  failedCount?: number
  results?: OperationResult<T>[]
  error?: string
  message?: string
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number
  limit?: number
  skip?: number
  offset?: number
}

/**
 * Pagination state
 */
export interface PaginationState extends PaginationParams {
  total?: number
  totalPages?: number
  hasMore?: boolean
  hasPrevious?: boolean
}

/**
 * Search and filter parameters
 */
export interface SearchParams {
  search?: string
  searchQuery?: string
  query?: string
}

/**
 * Filter parameters
 */
export interface FilterParams {
  status?: string
  type?: string
  category?: string
  tag?: string
  startDate?: string
  endDate?: string
  [key: string]: any
}

/**
 * Sort parameters
 */
export interface SortParams {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  orderBy?: string
  order?: 'asc' | 'desc'
}

/**
 * Combined query parameters
 */
export interface QueryParams extends PaginationParams, SearchParams, FilterParams, SortParams {
  [key: string]: any
}

/**
 * Hook options for data fetching
 */
export interface HookOptions {
  autoFetch?: boolean
  refreshInterval?: number
  revalidateOnFocus?: boolean
  revalidateOnReconnect?: boolean
  errorRetryCount?: number
}

/**
 * SWR-style hook return type
 */
export interface SWRHookReturn<T> extends LoadingState {
  data: T | null
  mutate: () => void
  refresh: () => Promise<void>
}

/**
 * Form validation state
 */
export interface ValidationState {
  isValid: boolean
  errors: Record<string, string | undefined>
  touched: Record<string, boolean>
}

/**
 * Form field update function type
 */
export type FieldUpdater<T> = (field: keyof T, value: any) => void

/**
 * Generic list management state
 */
export interface ListState<T> {
  items: T[]
  selectedItems: string[]
  searchTerm: string
  filters: Record<string, any>
  sort: SortParams
  pagination: PaginationState
}

/**
 * Generic list management actions
 */
export interface ListActions<T> {
  // Selection
  selectItem: (id: string) => void
  selectMultiple: (ids: string[]) => void
  clearSelection: () => void
  selectAll: () => void

  // Search and filter
  setSearchTerm: (term: string) => void
  setFilter: (key: string, value: any) => void
  clearFilters: () => void

  // Sort
  setSort: (sortBy: string, order?: 'asc' | 'desc') => void

  // Pagination
  setPage: (page: number) => void
  setLimit: (limit: number) => void

  // Data operations
  refresh: () => Promise<void>
  loadMore?: () => Promise<void>
}

/**
 * CRUD hook return type
 */
export interface CRUDHookReturn<T, CreateData = Partial<T>, UpdateData = Partial<T>>
  extends SWRHookReturn<T[]> {
  // CRUD operations
  create: (data: CreateData) => Promise<OperationResult<T>>
  update: (id: string, data: UpdateData) => Promise<OperationResult<T>>
  delete: (id: string) => Promise<OperationResult<void>>

  // Bulk operations
  createMultiple?: (items: CreateData[]) => Promise<BulkOperationResult<T>>
  updateMultiple?: (updates: Array<{ id: string; data: UpdateData }>) => Promise<BulkOperationResult<T>>
  deleteMultiple?: (ids: string[]) => Promise<BulkOperationResult<void>>

  // Operation states
  operations: {
    isCreating: boolean
    isUpdating: boolean
    isDeleting: boolean
  }
}

/**
 * File upload state
 */
export interface FileUploadState {
  file: File | null
  progress: number
  isUploading: boolean
  error: string | null
  result: any | null
}

/**
 * Async operation state
 */
export interface AsyncOperationState<T = any> {
  isLoading: boolean
  isSuccess: boolean
  isError: boolean
  data: T | null
  error: string | null
  startTime: number | null
  endTime: number | null
}

/**
 * Debounced input configuration
 */
export interface DebounceConfig {
  delay?: number
  immediate?: boolean
  leading?: boolean
  trailing?: boolean
}

/**
 * Cache configuration
 */
export interface CacheConfig {
  ttl?: number // Time to live in milliseconds
  staleTime?: number
  cacheKey?: string
  invalidateOn?: string[]
}

/**
 * Optimistic update configuration
 */
export interface OptimisticUpdateConfig<T> {
  enabled?: boolean
  updateFn?: (current: T[], update: any) => T[]
  revertOnError?: boolean
}

/**
 * Real-time update configuration
 */
export interface RealTimeConfig {
  enabled?: boolean
  interval?: number
  endpoint?: string
  onUpdate?: (data: any) => void
}

/**
 * Export/Import operations
 */
export interface ExportResult {
  success: boolean
  data?: Blob | string
  filename?: string
  error?: string
}

export interface ImportResult<T = any> {
  success: boolean
  imported?: T[]
  failed?: Array<{ item: any; reason: string }>
  summary?: {
    total: number
    imported: number
    failed: number
  }
  error?: string
}

/**
 * User context for operations
 */
export interface UserContext {
  userId: string
  teamId?: string
  companyId?: string
  permissions?: string[]
}

/**
 * Environment configuration
 */
export interface EnvironmentConfig {
  development?: boolean
  staging?: boolean
  production?: boolean
  apiUrl?: string
  wsUrl?: string
}
