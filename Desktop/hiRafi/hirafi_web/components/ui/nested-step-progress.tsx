"use client";

import React from 'react';
import { ChevronDown, ChevronRight, Check<PERSON>ircle, Clock, XCircle, PlayCircle } from 'lucide-react';
import { StepProgressData } from '@/lib/api/step-progress-api';
import { cn } from '@/lib/utils';

interface NestedStepProgressProps {
  steps: StepProgressData[];
  currentStep?: number;
  totalSteps?: number;
  currentStepName?: string;
  showExpanded?: boolean;
}

const StepIcon = ({ status, nestingLevel = 0 }: { status: string; nestingLevel?: number }) => {
  const iconClass = nestingLevel > 0 ? "h-3 w-3" : "h-4 w-4";
  
  switch (status) {
    case 'completed':
      return <CheckCircle className={cn(iconClass, "text-green-500")} />;
    case 'failed':
      return <XCircle className={cn(iconClass, "text-red-500")} />;
    case 'started':
      return <PlayCircle className={cn(iconClass, "text-blue-500 animate-pulse")} />;
    default:
      return <Clock className={cn(iconClass, "text-gray-400")} />;
  }
};

const NestedStepItem = ({ 
  step, 
  isExpanded, 
  onToggle, 
  nestingLevel = 0 
}: { 
  step: StepProgressData; 
  isExpanded: boolean; 
  onToggle: () => void; 
  nestingLevel?: number;
}) => {
  const hasNestedSteps = step.nestedSteps && step.nestedSteps.length > 0;
  const isControlFlow = ['ifElse', 'forLoop', 'whileLoop'].includes(step.stepType);
  
  return (
    <div className={cn("space-y-1", nestingLevel > 0 && "ml-4")}>
      <div 
        className={cn(
          "flex items-center gap-2 p-2 rounded-lg transition-colors",
          nestingLevel === 0 ? "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700" : "bg-gray-50 dark:bg-gray-700/50",
          step.status === 'started' && "ring-2 ring-blue-200 dark:ring-blue-800"
        )}
      >
        {/* Expand/Collapse button for control flow steps */}
        {isControlFlow && hasNestedSteps && (
          <button
            onClick={onToggle}
            className="flex-shrink-0 p-1 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors"
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-gray-500" />
            ) : (
              <ChevronRight className="h-3 w-3 text-gray-500" />
            )}
          </button>
        )}
        
        {/* Step icon */}
        <div className="flex-shrink-0">
          <StepIcon status={step.status} nestingLevel={nestingLevel} />
        </div>
        
        {/* Step info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className={cn(
              "font-medium truncate",
              nestingLevel === 0 ? "text-sm" : "text-xs",
              step.status === 'completed' && "text-green-700 dark:text-green-300",
              step.status === 'failed' && "text-red-700 dark:text-red-300",
              step.status === 'started' && "text-blue-700 dark:text-blue-300",
              step.status !== 'completed' && step.status !== 'failed' && step.status !== 'started' && "text-gray-600 dark:text-gray-400"
            )}>
              {step.stepName}
            </span>
            
            {/* Control flow indicator */}
            {isControlFlow && (
              <span className="flex-shrink-0 px-2 py-0.5 text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full">
                {step.stepType === 'ifElse' ? 'IF' : step.stepType === 'forLoop' ? 'FOR' : 'WHILE'}
              </span>
            )}
          </div>
          
          {/* Nested step count for control flow */}
          {isControlFlow && hasNestedSteps && (
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
              {step.nestedSteps!.length} nested step{step.nestedSteps!.length !== 1 ? 's' : ''}
            </div>
          )}
        </div>
        
        {/* Status indicator */}
        <div className="flex-shrink-0">
          <span className={cn(
            "text-xs px-2 py-1 rounded-full font-medium",
            step.status === 'completed' && "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300",
            step.status === 'failed' && "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300",
            step.status === 'started' && "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300",
            step.status !== 'completed' && step.status !== 'failed' && step.status !== 'started' && "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
          )}>
            {step.status}
          </span>
        </div>
      </div>
      
      {/* Nested steps */}
      {isControlFlow && hasNestedSteps && isExpanded && (
        <div className="space-y-1 pl-2 border-l-2 border-gray-200 dark:border-gray-600">
          {step.nestedSteps!.map((nestedStep, index) => (
            <NestedStepItem
              key={`${nestedStep.stepId}-${index}`}
              step={nestedStep}
              isExpanded={false} // Nested steps don't expand by default
              onToggle={() => {}} // Nested steps don't toggle
              nestingLevel={nestingLevel + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const NestedStepProgress: React.FC<NestedStepProgressProps> = ({
  steps,
  currentStep,
  totalSteps,
  currentStepName,
  showExpanded = true
}) => {
  const [expandedSteps, setExpandedSteps] = React.useState<Set<string>>(new Set());
  
  // Auto-expand control flow steps that are currently running or recently completed
  React.useEffect(() => {
    const autoExpand = new Set<string>();
    
    steps.forEach(step => {
      const isControlFlow = ['ifElse', 'forLoop', 'whileLoop'].includes(step.stepType);
      const hasNestedSteps = step.nestedSteps && step.nestedSteps.length > 0;
      
      if (isControlFlow && hasNestedSteps && (step.status === 'started' || step.status === 'completed')) {
        autoExpand.add(step.stepId);
      }
    });
    
    setExpandedSteps(autoExpand);
  }, [steps]);
  
  const toggleExpanded = (stepId: string) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(stepId)) {
      newExpanded.delete(stepId);
    } else {
      newExpanded.add(stepId);
    }
    setExpandedSteps(newExpanded);
  };
  
  if (!steps || steps.length === 0) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">
            {currentStepName || 'Processing...'}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {totalSteps > 0 ? `${currentStep}/${totalSteps}` : 'Running...'}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
          <div
            className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-300"
            style={{
              width: `${totalSteps && currentStep !== undefined ? (currentStep / totalSteps) * 100 : 20}%`
            }}
          />
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-2">
      {/* Overall progress */}
      <div className="flex items-center justify-between">
        <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">
          {currentStepName || 'Processing steps...'}
        </span>
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {currentStep}/{totalSteps}
        </span>
      </div>
      
      {/* Progress bar */}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
        <div
          className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-300"
          style={{
            width: `${totalSteps && currentStep ? (currentStep / totalSteps) * 100 : 0}%`
          }}
        />
      </div>
      
      {/* Detailed step progress (expandable) */}
      {showExpanded && (
        <div className="mt-3 space-y-1 max-h-64 overflow-y-auto">
          {steps.map((step, index) => (
            <NestedStepItem
              key={`${step.stepId}-${index}`}
              step={step}
              isExpanded={expandedSteps.has(step.stepId)}
              onToggle={() => toggleExpanded(step.stepId)}
              nestingLevel={0}
            />
          ))}
        </div>
      )}
    </div>
  );
}; 