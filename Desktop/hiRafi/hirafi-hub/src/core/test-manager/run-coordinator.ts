import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import {
  generateExecutionId,
  getRunById
} from '../../services/mongo/runService.js';
import {
  createRunReportAtomic,
  updateRunReport,
  getRunReportByRunId
} from '../../services/mongo/atomicReportService.js';
import { getScenarioById } from '../../services/mongo/scenarioService.js';
import { getUserById } from '../../services/mongo/userService.js';
import { getCompanyById, updateCompanyRemaining } from '../../services/mongo/companyService.js';
import { RunStatus, AndroidEnvironmentSettings, WebEnvironmentSettings, EnvironmentSettings } from "../../models/test-types.js";

// Enhanced interfaces for better type safety
interface TestProcess {
  id: string;
  runId?: string;
  scenarioId?: string;
  status?: string;
  nodeId?: string;
  priority?: number;
  createdAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
}

interface DeviceInfo {
  id: string;
  name: string;
  osVersion: string;
  provider: string;
}

interface UserInfo {
  _id?: any;
  id?: string;
  name?: string;
  email?: string;
  companyId?: string;
  teamId?: string;
  accountType?: string;
  [key: string]: any; // Allow additional properties from database
}

interface CompanyInfo {
  id: string;
  name: string;
  settings?: {
    remaining?: {
      runMinutes?: number;
    };
  };
  aiModels?: Array<{
    id: string;
    name: string;
    api: string;
    apiKey: string;
    isActive: boolean;
  }>;
}

interface RunInfo {
  id: string;
  name?: string;
  userId: string;
  teamId?: string | null;
  companyId?: string | null;
  scenarioIds: string[];
  platform?: string;
  deviceProvider?: 'sauceLabs' | 'testinium'; // Mutually exclusive device provider selection
  environment?: EnvironmentSettings;
  reportSettings?: any;
  status?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface RunStatusInfo {
  runId: string;
  executionId: string;
  status: string;
  startedAt?: Date;
  completedAt?: Date;
  testResults?: any;
  scenarioStatuses?: any[];
}

interface TestManagerInterface {
  addTest(
    scenarioId: string,
    testName: string,
    scenario: any,
    priority: number,
    userId: string,
    options: Record<string, any>
  ): Promise<string>;
  updateRunStatus(runId: string, status: string): void;
  getTestsByRunId(runId: string): TestProcess[];
  removeTest(testId: string): Promise<boolean>;
  getAllTests(): Promise<TestProcess[]>;
  stopTest(testId: string): Promise<boolean>;
  emit(event: string, data: any): void;
}

/**
 * Run işlemlerini koordine eden servis
 * Run başlatma, izleme ve sonlandırma operasyonlarının tutarlı şekilde yürütülmesini sağlar
 */
export class RunCoordinator {
  private testManager: TestManagerInterface;
  private runMonitorTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor(testManager: TestManagerInterface) {
    this.testManager = testManager;

  }

  /**
   * Extract and normalize device information from environment settings
   * Handles legacy device ID fields and converts them to selectedDevices array
   */
  private extractDeviceInformation(run: RunInfo): {
    selectedDevices: DeviceInfo[];
    hasMultipleDevices: boolean;
    deviceIds: string[];
  } {
    let selectedDevices: DeviceInfo[] = [];
    let deviceIds: string[] = [];

    // Type guard for Android settings
    const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
      return settings && settings.platform === 'android';
    };

    if (run.environment && isAndroidSettings(run.environment)) {
      selectedDevices = run.environment.sauceLabs?.selectedDevices || [];
      deviceIds = (run.environment.sauceLabs as any)?.deviceIds || [];
    }

    // Check if we have SauceLabs settings with selected devices
    if (selectedDevices.length > 0) {
      logger.info(`RunCoordinator: Found ${selectedDevices.length} selected devices`);
      selectedDevices.forEach((device: { name: string; osVersion: string; id: string; provider?: string }, index: number) => {
        logger.info(`RunCoordinator: Device ${index + 1}: ${device.name} (${device.osVersion}), ID: ${device.id}, Provider: ${device.provider || 'unknown'}`);
      });
    }
    // Handle legacy deviceIds field
    else if (deviceIds.length > 0) {
      logger.info(`RunCoordinator: Converting legacy deviceIds array to selectedDevices`);
      selectedDevices = deviceIds.map((id: string) => ({
        id,
        name: `Device ${id}`,
        osVersion: 'Unknown',
        provider: 'sauceLabs'
      }));
      this.updateEnvironmentWithDevices(run, selectedDevices);
    }
    // Handle legacy deviceId field
    else if (run.environment && isAndroidSettings(run.environment) && (run.environment.sauceLabs as any)?.deviceId) {
      const deviceId = (run.environment.sauceLabs as any).deviceId;
      logger.info(`RunCoordinator: Converting legacy deviceId to selectedDevices`);
      selectedDevices = [{
        id: deviceId,
        name: `Device ${deviceId}`,
        osVersion: 'Unknown',
        provider: 'sauceLabs'
      }];
      this.updateEnvironmentWithDevices(run, selectedDevices);
    }
    // Handle legacy appium.sauceLabsDeviceId field
    else if (run.environment && 'appium' in run.environment && (run.environment as any).appium?.sauceLabsDeviceId) {
      const deviceId = (run.environment as any).appium.sauceLabsDeviceId;
      logger.info(`RunCoordinator: Converting legacy appium.sauceLabsDeviceId to selectedDevices`);
      selectedDevices = [{
        id: deviceId,
        name: `Device ${deviceId}`,
        osVersion: 'Unknown',
        provider: 'sauceLabs'
      }];
      this.convertToAndroidEnvironment(run, selectedDevices);
    }

    const hasMultipleDevices = selectedDevices.length > 1;

    return {
      selectedDevices,
      hasMultipleDevices,
      deviceIds: selectedDevices.map(device => device.id)
    };
  }

  /**
   * Update environment settings with normalized device information
   */
  private updateEnvironmentWithDevices(run: RunInfo, selectedDevices: DeviceInfo[]): void {
    const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
      return settings && settings.platform === 'android';
    };

    if (run.environment && isAndroidSettings(run.environment)) {
      // Ensure sauceLabs object has required properties
      if (!run.environment.sauceLabs.username || !run.environment.sauceLabs.accessKey) {
        logger.warn(`RunCoordinator: SauceLabs configuration is missing required username or accessKey`);
        run.environment.sauceLabs.username = run.environment.sauceLabs.username || '';
        run.environment.sauceLabs.accessKey = run.environment.sauceLabs.accessKey || '';
      }
      run.environment.sauceLabs.selectedDevices = selectedDevices;
    }
  }

  /**
   * Convert environment settings to Android format with device information
   */
  private convertToAndroidEnvironment(run: RunInfo, selectedDevices: DeviceInfo[]): void {
    const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
      return settings && settings.platform === 'android';
    };

    if (run.environment && !isAndroidSettings(run.environment)) {
      // Convert to Android environment settings
      // CRITICAL FIX: Preserve existing testinium selectedDevices when converting
      const existingTestiniumDevices = (run.environment as any)?.testinium?.selectedDevices || [];
      
      const androidSettings: AndroidEnvironmentSettings = {
        platform: 'android',
        aiModel: run.environment.aiModel as string,
        aiModelName: (run.environment as any).aiModelName,
        sauceLabs: {
          username: '',
          accessKey: '',
          selectedDevices: selectedDevices
        },
        testinium: {
          apiUrl: '',
          clientId: '',
          clientSecret: '',
          issuerUri: '',
          selectedDevices: existingTestiniumDevices
        }
      };
      run.environment = androidSettings;
      logger.info(`RunCoordinator: Converted environment settings to Android format`);
    } else if (run.environment && isAndroidSettings(run.environment)) {
      if (!run.environment.sauceLabs) {
        run.environment.sauceLabs = {
          username: '',
          accessKey: '',
          selectedDevices: []
        };
      }
      run.environment.sauceLabs.selectedDevices = selectedDevices;
    }
  }

  /**
   * Fetch user information once and cache it for the duration of the run
   */
  private async fetchUserInformation(userId: string): Promise<{
    user: UserInfo | null;
    executedUserName: string;
    companyId: string | null;
    teamId: string | null;
  }> {
    try {
      const userResult = await getUserById(userId);
      if (!userResult.success || !userResult.data) {
        logger.warn(`RunCoordinator: User ${userId} not found`);
        return {
          user: null,
          executedUserName: 'System',
          companyId: null,
          teamId: null
        };
      }

      const user = userResult.data;
      return {
        user,
        executedUserName: user.name || 'System',
        companyId: user.companyId || null,
        teamId: user.teamId || null
      };
    } catch (error) {
      logger.error(`RunCoordinator: Error fetching user information for ${userId}: ${error}`);
      return {
        user: null,
        executedUserName: 'System',
        companyId: null,
        teamId: null
      };
    }
  }

  /**
   * Run'ı başlatır - tüm hazırlık işlemlerini gerçekleştirir
   */
  async initiateRun(runId: string, userId: string): Promise<{
    success: boolean;
    executionId?: string;
    reportId?: string;
    testIds?: string[];
    scenarioErrors?: Array<{scenarioId: string; error: string}>;
    message?: string;
    error?: string;
  }> {
    // 1. İşlem durum takibi için değişkenler
    let executionId: string | undefined;
    let reportId: string | undefined;
    const testIds: string[] = [];
    const scenarioErrors: Array<{scenarioId: string; error: string}> = [];
    let runInitialized = false;

    // 2. Fetch user information once at the beginning
    const userInfo = await this.fetchUserInformation(userId);
    const { user, executedUserName, companyId, teamId } = userInfo;

    try {
      // 2. Run bilgilerini al
      logger.info(`RunCoordinator: Initiating run ${runId} for user ${userId}`);
      const runResult = await getRunById(runId);

      if (!runResult.success || !runResult.run) {
        return {
          success: false,
          error: `Run with ID ${runId} not found`
        };
      }

      const run = runResult.run;

      // 3. Yetki kontrolü
      if (run.userId !== userId) {
        return {
          success: false,
          error: 'You do not have permission to execute this run'
        };
      }

      // 3.1 Kullanıcının şirket bilgilerini kontrol et
      try {
        if (!user) {
          logger.error(`RunCoordinator: User ${userId} not found when checking company limits`);
          return {
            success: false,
            error: 'User not found'
          };
        }

        if (!companyId) {
          logger.warn(`RunCoordinator: User ${userId} has no company ID, skipping company limit check`);
        } else {
          // Şirket bilgilerini al
          const companyResult = await getCompanyById(companyId);
          if (!companyResult.success || !companyResult.company) {
            logger.error(`RunCoordinator: Company ${companyId} not found for user ${userId}`);
            return {
              success: false,
              error: 'Company not found'
            };
          }

          const company = companyResult.company;
          const remainingRunMinutes = company.settings?.remaining?.runMinutes;

          // Kalan run dakikası yoksa veya 0 ise hata döndür
          if (remainingRunMinutes !== undefined && remainingRunMinutes <= 0) {
            logger.error(`RunCoordinator: Company ${companyId} has no remaining run minutes`);
            return {
              success: false,
              error: 'Şirketinizin kalan koşum dakikası kalmamıştır. Lütfen yöneticinizle iletişime geçin.'
            };
          }

          logger.info(`RunCoordinator: Company ${companyId} has ${remainingRunMinutes || 'unlimited'} remaining run minutes`);
        }
      } catch (error: any) {
        logger.error(`RunCoordinator: Error checking company limits: ${error.message}`);
        // Güvenlik için hata durumunda run'ı durdur
        return {
          success: false,
          error: 'Error checking company limits'
        };
      }

      // 4. Android platform için device provider validasyonu
      if (run.platform === 'android') {
        // Basit Android run validasyonu - hub içinde
        if (!run.environment) {
          logger.error(`RunCoordinator: Android run validation failed for run ${runId}: Environment settings missing`);
          return {
            success: false,
            error: 'Android testleri için ortam ayarları gereklidir'
          };
        }

        const deviceProvider = run.deviceProvider;
        if (!deviceProvider) {
          logger.error(`RunCoordinator: Android run validation failed for run ${runId}: Device provider missing`);
          return {
            success: false,
            error: 'Android testleri için cihaz sağlayıcısı belirtilmelidir'
          };
        }

        if (deviceProvider !== 'sauceLabs' && deviceProvider !== 'testinium') {
          logger.error(`RunCoordinator: Android run validation failed for run ${runId}: Invalid device provider ${deviceProvider}`);
          return {
            success: false,
            error: `Geçersiz cihaz sağlayıcısı: ${deviceProvider}. 'sauceLabs' veya 'testinium' olmalıdır`
          };
        }

        logger.info(`RunCoordinator: Android run validation passed for run ${runId} with provider ${deviceProvider}`);
      }

      // 5. Senaryo kontrolü
      if (!run.scenarioIds || run.scenarioIds.length === 0) {
        return {
          success: false,
          error: 'No scenarios in this run'
        };
      }

      // 6. Benzersiz execution ID oluştur
      executionId = generateExecutionId();
      logger.info(`RunCoordinator: Generated execution ID ${executionId} for run ${runId}`);

      // 7. Run için run raporu oluştur - cached user information kullan
      // Run bilgilerinden teamId ve companyId'yi al, yoksa cached değerleri kullan
      const finalTeamId = run.teamId || teamId;
      const finalCompanyId = run.companyId || companyId;

      logger.info(`RunCoordinator: Creating run report with userId: ${userId}, teamId: ${finalTeamId}, companyId: ${finalCompanyId}, executedUser: ${userId}, executedUserName: ${executedUserName}`);

      const runReportResult = await createRunReportAtomic(runId, executionId, {
        userId: userId,
        teamId: finalTeamId ? finalTeamId : undefined,
        companyId: finalCompanyId ? finalCompanyId : undefined,
        executedUser: userId,
        executedUserName: executedUserName
      });

      if (!runReportResult.success) {
        logger.error(`RunCoordinator: Failed to create run report for run ${runId} with executionId ${executionId}: ${runReportResult.message}`);
        return {
          success: false,
          error: runReportResult.message || 'Failed to create run report'
        };
      }

      // Oluşturulan rapor ID'sini al
      reportId = runReportResult.reportId;
      logger.info(`RunCoordinator: Created run report ${reportId} for run ${runId} with executionId ${executionId}`);

      // Run raporunun oluşturulduğunu doğrula
      const verifyReportResult = await getRunReportByRunId(runId, executionId);

      if (!verifyReportResult.success || !verifyReportResult.report) {
        logger.warn(`RunCoordinator: Run report verification failed for run ${runId} with executionId ${executionId}. Creating a new report.`);
        // Tekrar run raporu oluşturmayı dene - cached user bilgilerini kullan
        const retryResult = await createRunReportAtomic(runId, executionId, {
          userId: userId,
          teamId: finalTeamId ? finalTeamId : undefined,
          companyId: finalCompanyId ? finalCompanyId : undefined,
          executedUser: userId,
          executedUserName: executedUserName
        });

        if (retryResult.success && retryResult.reportId) {

          if (!reportId) {
            reportId = retryResult.reportId;
          }
        } else {
          logger.error(`RunCoordinator: Failed to create run report on retry: ${retryResult.message}`);
        }
      } else {
        logger.info(`RunCoordinator: Run report with executionId ${executionId} successfully verified for run ${runId}`);
      }

      // 8. Run raporu zaten oluşturuldu, bu adımı atlıyoruz
      logger.info(`RunCoordinator: Using existing run report ${reportId} for run ${runId} with executionId ${executionId}`);

      // 9. Run'ı başlat ve durumunu güncelle
      runInitialized = true;

      // Run raporunu güncelle - QUEUED durumuna ayarla
      if (reportId) {
        await updateRunReport(reportId, {
          status: RunStatus.QUEUED,
          $set: { source: 'RunCoordinator-executeRun' }
        });
        logger.info(`RunCoordinator: Updated run report ${reportId} status to QUEUED`);
      }
      logger.info(`RunCoordinator: Run ${runId} initialized and status set to QUEUED`);

      // Type guard to check if environmentSettings is AndroidEnvironmentSettings
      const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
        return settings && settings.platform === 'android';
      };

      // CRITICAL FIX: Retrieve and update run with actual device provider credentials
      if (run.platform === 'android' && run.environment && isAndroidSettings(run.environment)) {
        const deviceProvider = run.environment.deviceProvider || 'sauceLabs';
        logger.info(`RunCoordinator: Detected Android platform with device provider: ${deviceProvider}`);
        
        try {
          // Import credential services
          const { getSauceLabsCredentialsByCompanyAndTeam } = await import('../../services/mongo/sauceLabsService.js');
          const { getTestiniumCredentialsByCompanyAndTeam } = await import('../../services/mongo/testiniumService.js');
          const { updateRun } = await import('../../services/mongo/runService.js');
          
          let credentialsUpdated = false;
          
          if (deviceProvider === 'sauceLabs') {
            // Retrieve SauceLabs credentials
            const credentialsResult = await getSauceLabsCredentialsByCompanyAndTeam(finalCompanyId || '', finalTeamId);
            
            if (credentialsResult.success && credentialsResult.credentials) {
              logger.info(`RunCoordinator: Retrieved SauceLabs credentials for run ${runId}`);
              
              // Update run environment with actual credentials
              const updatedEnvironment = { ...run.environment };
              updatedEnvironment.sauceLabs = {
                ...updatedEnvironment.sauceLabs,
                username: credentialsResult.credentials.username,
                accessKey: credentialsResult.credentials.accessKey,
                region: updatedEnvironment.sauceLabs?.region || credentialsResult.credentials.region
              };
              
              // Update the run with new credentials
              const updateResult = await updateRun(runId, { environment: updatedEnvironment });
              if (updateResult.success) {
                logger.info(`RunCoordinator: Successfully updated run ${runId} with SauceLabs credentials`);
                run.environment = updatedEnvironment; // Update local copy
                credentialsUpdated = true;
              } else {
                logger.error(`RunCoordinator: Failed to update run ${runId} with SauceLabs credentials: ${updateResult.message}`);
              }
            } else {
              logger.error(`RunCoordinator: No SauceLabs credentials found for companyId: ${finalCompanyId}, teamId: ${finalTeamId}`);
              throw new Error(`SauceLabs credentials not configured for this team/company. Please configure SauceLabs plugin credentials before running Android tests.`);
            }
          } else if (deviceProvider === 'testinium') {
            // Retrieve Testinium credentials
            const credentialsResult = await getTestiniumCredentialsByCompanyAndTeam(finalCompanyId || '', finalTeamId);
            
            if (credentialsResult.success && credentialsResult.credentials) {
              logger.info(`RunCoordinator: Retrieved Testinium credentials for run ${runId}`);
              
              // Update run environment with actual credentials
              const updatedEnvironment = { ...run.environment };
              updatedEnvironment.testinium = {
                ...updatedEnvironment.testinium,
                apiUrl: credentialsResult.credentials.apiUrl,
                clientId: credentialsResult.credentials.clientId,
                clientSecret: credentialsResult.credentials.clientSecret,
                issuerUri: credentialsResult.credentials.issuerUri
              };
              
              // Update the run with new credentials
              const updateResult = await updateRun(runId, { environment: updatedEnvironment });
              if (updateResult.success) {
                logger.info(`RunCoordinator: Successfully updated run ${runId} with Testinium credentials`);
                run.environment = updatedEnvironment; // Update local copy
                credentialsUpdated = true;
              } else {
                logger.error(`RunCoordinator: Failed to update run ${runId} with Testinium credentials: ${updateResult.message}`);
              }
            } else {
              logger.error(`RunCoordinator: No Testinium credentials found for companyId: ${finalCompanyId}, teamId: ${finalTeamId}`);
              throw new Error(`Testinium credentials not configured for this team/company. Please configure Testinium plugin credentials before running Android tests.`);
            }
          }
          
          if (credentialsUpdated) {
            logger.info(`RunCoordinator: Run ${runId} credentials successfully updated before test creation`);
          }
        } catch (credentialError: any) {
          logger.error(`RunCoordinator: Error retrieving/updating device provider credentials for run ${runId}: ${credentialError.message}`);
          // Clean up failed run
          await this.cleanupFailedRun(runId, executionId, runInitialized);
          return {
            success: false,
            executionId,
            reportId,
            error: credentialError.message || 'Failed to retrieve device provider credentials'
          };
        }
      }

      // 9. Test oluşturma işlemini paralel yap
      logger.info(`RunCoordinator: Creating tests for ${run.scenarioIds.length} scenarios`);

      // Use the imported types

      // Check for test distribution strategy
      let testDistributionStrategy = 'all-on-all';
      if (run.environment && isAndroidSettings(run.environment)) {
        testDistributionStrategy = run.environment.testDistribution?.strategy || 'all-on-all';
      }
      logger.info(`RunCoordinator: Using test distribution strategy: ${testDistributionStrategy}`);

      // Extract and normalize device information using helper method
      const deviceInfo = this.extractDeviceInformation(run);
      const { selectedDevices, hasMultipleDevices, deviceIds } = deviceInfo;

      logger.info(`RunCoordinator: Device extraction complete - ${selectedDevices.length} devices found, multiple devices: ${hasMultipleDevices}`);

      // Create a function to process a single scenario
      const processScenario = async (scenarioId: string, deviceIndex?: number) => {
        try {
          // Senaryoyu getir
          const scenarioResult = await getScenarioById(scenarioId);

          if (!scenarioResult.success || !scenarioResult.scenario) {
            return {
              success: false,
              scenarioId,
              error: 'Scenario not found'
            };
          }

          const scenario = scenarioResult.scenario;

          // RACE CONDITION DEBUG: Log scenario metadata for tracking
          logger.info(`RunCoordinator: Retrieved scenario ${scenarioId} - Name: "${scenario.name || scenario.title}", Steps: ${scenario.steps?.length || 0}, Updated: ${scenario.updatedAt}, Platform: ${scenario.platform}`);

          // Çevre ve rapor ayarlarını uygula
          const updatedScenario = { ...scenario };
          if (run.environment) {
            updatedScenario.environment = { ...updatedScenario.environment, ...run.environment };
          }

          if (run.reportSettings) {
            updatedScenario.reportSettings = { ...updatedScenario.reportSettings, ...run.reportSettings };
          }

          // ENHANCED DEBUG: Log scenario info
          logger.info(`RunCoordinator: DEBUG - Processing scenario ${scenarioId}: "${scenario.name}"`);
          logger.info(`RunCoordinator: DEBUG - scenario.testDataSetId: ${scenario.testDataSetId || 'UNDEFINED'}`);
          logger.info(`RunCoordinator: DEBUG - scenario.metadata: ${JSON.stringify(scenario.metadata || {}, null, 2)}`);

          // Dataset bilgisini scenario'ya ekle (eğer testDataSetId varsa)
          if (scenario.testDataSetId) {
            try {
              const { getDataSetById } = await import('../../services/mongo/testDataService.js');
              const datasetResult = await getDataSetById(scenario.testDataSetId, finalTeamId || '', finalCompanyId || '');
              
              if (datasetResult.success && datasetResult.data) {
                const dataset = datasetResult.data as any; // Cast to any for runtime variables access
                
                // Tüm variable türlerini birleştir
                const allVariables = [
                  // Dataset seviyesindeki variables (manuel/csv/excel) - runtime'da olabilir
                  ...(dataset.variables || []),
                  // Metadata seviyesindeki variables (dataset creation'da oluşturulan)
                  ...(dataset.metadata?.variables || [])
                ];
                
                // DataSource variables'ları da ekle (database/API variables için)
                let dataSourceVariables: any[] = [];
                let rawDataSources: any[] = []; // Store raw dataSources for sourceId lookup
                try {
                  const { getDataSources } = await import('../../services/mongo/testDataService.js');
                  const dataSourcesResult = await getDataSources(finalTeamId || '', finalCompanyId || '', { isActive: true });
                  
                  if (dataSourcesResult.success && dataSourcesResult.data?.dataSources) {
                    rawDataSources = dataSourcesResult.data.dataSources; // Store for later use
                    // ENHANCED DEBUG: Log DataSource structure
                    logger.info(`RunCoordinator: DEBUG - Raw DataSources: ${JSON.stringify(dataSourcesResult.data.dataSources.slice(0, 2), null, 2)}`);
                    
                    // DataSource'ları variable formatına çevir
                    dataSourceVariables = dataSourcesResult.data.dataSources.map((ds: any) => {
                      // Extract fields based on DataSource type
                      let mappedVariable: any = {
                        name: ds.name,
                        sourceType: ds.type,
                        sourceId: ds.id,
                        description: ds.description,
                        isActive: ds.isActive
                      };

                      if (ds.type === 'database') {
                        // ✅ FIXED: Database specific - Extract from config structure
                        logger.info(`RunCoordinator: DEBUG - DB CONFIG for '${ds.name}':`, JSON.stringify(ds.config, null, 2));
                        logger.info(`RunCoordinator: DEBUG - DB Extraction for '${ds.name}':`);
                        logger.info(`  ds.connectionString: ${ds.connectionString ? '***EXISTS***' : 'UNDEFINED'}`);
                        logger.info(`  ds.config.mode: ${ds.config?.mode || 'UNDEFINED'}`);
                        logger.info(`  ds.config.table: ${ds.config?.table || 'UNDEFINED'}`);
                        logger.info(`  ds.config.nameColumn: ${ds.config?.nameColumn || 'UNDEFINED'}`);
                        logger.info(`  ds.config.valueColumn: ${ds.config?.valueColumn || 'UNDEFINED'}`);
                        logger.info(`  ds.config.flow exists: ${!!ds.config?.flow}`);
                        
                        // ✅ STEP 1 FIX: Extract from flow[0].data (where the real data is)
                        const flowData = ds.config?.flow?.[0]?.data;
                        
                        mappedVariable = {
                          ...mappedVariable,
                          connectionString: ds.connectionString,
                          tableName: flowData?.table || ds.config?.table,
                          nameColumn: flowData?.whereConditions?.[0]?.col || ds.config?.nameColumn,
                          valueColumn: flowData?.selectedColumns?.[0] !== '*' ? flowData?.selectedColumns?.[0] : (flowData?.whereConditions?.[0]?.col || 'id'),
                          mode: ds.config?.mode || 'visual',
                          flow: ds.config?.flow
                        };
                        
                        logger.info(`RunCoordinator: STEP 1 - Final DB mapping: tableName=${mappedVariable.tableName}, nameColumn=${mappedVariable.nameColumn}, valueColumn=${mappedVariable.valueColumn}`);
                      } else if (ds.type === 'api') {
                        // ✅ FIXED: API specific - Extract from config structure
                        logger.info(`RunCoordinator: DEBUG - API CONFIG for '${ds.name}':`, JSON.stringify(ds.config, null, 2));
                        logger.info(`RunCoordinator: DEBUG - API Extraction for '${ds.name}':`);
                        logger.info(`  ds.config.method: ${ds.config?.method || 'UNDEFINED'}`);
                        logger.info(`  ds.config.url: ${ds.config?.url || 'UNDEFINED'}`);
                        logger.info(`  ds.config.headers: ${ds.config?.headers ? `${ds.config.headers.length} items` : 'UNDEFINED'}`);
                        logger.info(`  ds.config.body: ${ds.config?.body ? 'EXISTS' : 'UNDEFINED'}`);
                        
                        mappedVariable = {
                          ...mappedVariable,
                          method: ds.config?.method,
                          url: ds.config?.url,
                          headers: ds.config?.headers || [],
                          params: ds.config?.params || [],
                          jsonPath: ds.config?.jsonPath || ds.config?.body || 'data'
                        };
                      }
                      
                      // DEBUG: Log mapped variable
                      logger.info(`RunCoordinator: DEBUG - Mapped DataSource '${ds.name}' (${ds.type}): ${JSON.stringify(mappedVariable, null, 2)}`);
                      
                      return mappedVariable;
                    });
                    
                    logger.info(`RunCoordinator: Found ${dataSourceVariables.length} active data sources for scenario ${scenarioId}`);
                  }
                } catch (dsError: any) {
                  logger.warn(`RunCoordinator: Error loading data sources for scenario ${scenarioId}: ${dsError.message}`);
                }
                
                // 🔧 CRITICAL FIX: Merge dataset variables with datasource info
                logger.info(`RunCoordinator: Starting variable merge - dataset vars: ${allVariables.length}, datasource vars: ${dataSourceVariables.length}`);
                logger.info(`RunCoordinator: Dataset variable names: ${JSON.stringify(allVariables.map(v => v.name))}`);
                logger.info(`RunCoordinator: DataSource variable names: ${JSON.stringify(dataSourceVariables.map(v => v.name))}`);
                
                const finalVariables = allVariables.map(datasetVar => {
                  // 🎯 NEW LOGIC: Find matching datasource by sourceId (not name!)
                  let matchingDatasource = null;
                  
                  if (datasetVar.sourceId) {
                    // Find by sourceId for imported variables - search in rawDataSources
                    matchingDatasource = rawDataSources.find((ds: any) => ds.id === datasetVar.sourceId);
                    logger.info(`RunCoordinator: Processing dataset variable '${datasetVar.name}' - found datasource by sourceId: ${!!matchingDatasource} (sourceId: ${datasetVar.sourceId})`);
                  } else {
                    // Fallback to name matching for legacy variables
                    matchingDatasource = dataSourceVariables.find(dsVar => dsVar.name === datasetVar.name);
                    logger.info(`RunCoordinator: Processing dataset variable '${datasetVar.name}' - found datasource by name: ${!!matchingDatasource}`);
                  }
                  
                  if (matchingDatasource) {
                    // 🚀 STEP 1: Flatten config fields for easy access
                    const config = matchingDatasource.config || {};
                    
                    const mergedVariable = {
                      ...datasetVar,
                      // Preserve dataset-specific fields
                      id: datasetVar.id,
                      type: datasetVar.type,
                      description: datasetVar.description,
                      isRequired: datasetVar.isRequired,
                      environmentValues: datasetVar.environmentValues,
                      // Add datasource core info
                      sourceType: matchingDatasource.sourceType,
                      sourceId: matchingDatasource.sourceId,
                      isActive: matchingDatasource.isActive,
                      // 🔧 FLATTEN API CONFIG FIELDS
                      method: config.method,
                      url: config.url,
                      headers: config.headers || [],
                      params: config.params || [],
                      // 🔧 FLATTEN DB CONFIG FIELDS  
                      connectionString: matchingDatasource.connectionString,
                      tableName: config.table,
                      flow: config.flow,
                      setupQuery: config.setupQuery,
                      mode: config.mode || 'visual',
                      // Keep original config for reference
                      config: config
                    };
                    
                    logger.info(`RunCoordinator: FLATTENED variable '${datasetVar.name}': method=${mergedVariable.method}, url=${mergedVariable.url}, tableName=${mergedVariable.tableName}, flow=${!!mergedVariable.flow}`);
                    return mergedVariable;
                  } else {
                    // No matching datasource, keep original dataset variable
                    return datasetVar;
                  }
                });
                
                // Add datasource-only variables that don't exist in dataset
                const datasetVarNames = allVariables.map(v => v.name);
                const additionalDsVars = dataSourceVariables.filter(dsVar => 
                  !datasetVarNames.includes(dsVar.name)
                );
                
                const allMergedVariables = [...finalVariables, ...additionalDsVars];

                // Dataset metadata'sını scenario'ya ekle
                updatedScenario.testDataSetId = scenario.testDataSetId;
                updatedScenario.metadata = {
                  variables: allMergedVariables,
                  // Original metadata'yı da koru
                  ...(scenario.metadata || {}),
                  ...(dataset.metadata || {})
                };
                
                logger.info(`RunCoordinator: Added dataset ${scenario.testDataSetId} with ${allMergedVariables.length} total variables to scenario ${scenarioId} (dataset: ${allVariables.length}, dataSources: ${dataSourceVariables.length}, merged: ${finalVariables.length})`);
                logger.info(`RunCoordinator: DEBUG - Final merged variables: ${JSON.stringify(allMergedVariables.map((v: any) => ({ 
                  name: v.name, 
                  sourceType: v.sourceType,
                  hasConnectionString: !!v.connectionString,
                  hasTableName: !!v.tableName,
                  hasMethod: !!v.method,
                  hasUrl: !!v.url
                })), null, 2)}`);
              } else {
                logger.warn(`RunCoordinator: Dataset ${scenario.testDataSetId} not found for scenario ${scenarioId}, continuing without dataset`);
              }
            } catch (error: any) {
              logger.error(`RunCoordinator: Error loading dataset ${scenario.testDataSetId} for scenario ${scenarioId}: ${error.message}`);
            }
          }

          // Options objesi oluştur - cached user bilgilerini kullan
          const options: {
            runId: string;
            executionId: string;
            executedUser: string;
            teamId?: string | null;
            companyId?: string | null;
            platform: string;
            executedUserName: string;
            [key: string]: any; // Allow additional properties
          } = {
            runId: runId,
            executionId: executionId!, // executionId is guaranteed to be defined at this point
            executedUser: userId,
            teamId: finalTeamId,
            companyId: finalCompanyId,
            platform: updatedScenario.platform || run.platform || 'web', // Use run platform or default to web
            executedUserName: executedUserName // Use cached user name
          };

          if (run.environment) {
            // Clone the environment settings to avoid modifying the original
            options.environmentSettings = JSON.parse(JSON.stringify(run.environment));

            // If we're processing a specific device, update the environment settings
            if (deviceIndex !== undefined) {
              // Ensure sauceLabs object exists with required properties
              if (!options.environmentSettings.sauceLabs) {
                options.environmentSettings.sauceLabs = {
                  username: '',
                  accessKey: ''
                };
              }

              // Use the imported types

              // Type guard to check if environmentSettings is AndroidEnvironmentSettings
              const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
                return settings && settings.platform === 'android';
              };

              // For the "distribute" strategy or when processing a specific device in "all-on-all"
              if (selectedDevices.length > 0 && deviceIndex < selectedDevices.length) {
                // Use a specific device from selectedDevices
                const device = selectedDevices[deviceIndex];

                // Ensure we're working with Android environment settings
                if (isAndroidSettings(options.environmentSettings)) {
                  // Set the selected device
                  options.environmentSettings.sauceLabs.selectedDevices = [device];

                  logger.info(`RunCoordinator: Using device ${device.name} (${device.id}) for scenario ${scenarioId}`);
                } else {
                  // Convert to Android environment settings
                  logger.info(`RunCoordinator: Converting environment settings to Android format for scenario ${scenarioId}`);

                  // CRITICAL FIX: Preserve existing testinium selectedDevices when converting
                  const existingTestiniumDevices = options.environmentSettings.testinium?.selectedDevices || [];

                  // Create a new Android environment settings object
                  const androidSettings: AndroidEnvironmentSettings = {
                    platform: 'android',
                    aiModel: options.environmentSettings.aiModel as string,
                    aiModelName: options.environmentSettings.aiModelName,
                    aiModelConfig: options.environmentSettings.aiModelConfig,
                    sauceLabs: {
                      ...options.environmentSettings.sauceLabs,
                      selectedDevices: [device],
                      username: options.environmentSettings.sauceLabs?.username || '',
                      accessKey: options.environmentSettings.sauceLabs?.accessKey || ''
                    },
                    testinium: {
                      apiUrl: '',
                      clientId: '',
                      clientSecret: '',
                      issuerUri: '',
                      selectedDevices: existingTestiniumDevices
                    }
                  };

                  // Replace the environment settings
                  options.environmentSettings = androidSettings;

                  logger.info(`RunCoordinator: Using device ${device.name} (${device.id}) for scenario ${scenarioId}`);
                }
              } else if (deviceIds.length > 0 && deviceIndex < deviceIds.length) {
                // Use a specific device from deviceIds
                const deviceId = deviceIds[deviceIndex];

                // Create a device object from the legacy deviceId
                const device = {
                  id: deviceId,
                  name: `Device ${deviceId}`,
                  osVersion: 'Unknown',
                  provider: 'sauceLabs'
                };

                // Ensure we're working with Android environment settings
                if (isAndroidSettings(options.environmentSettings)) {
                  // Set the selected device
                  options.environmentSettings.sauceLabs.selectedDevices = [device];

                  logger.info(`RunCoordinator: Using legacy device ID ${deviceId} for scenario ${scenarioId}`);
                } else {
                  // Convert to Android environment settings
                  logger.info(`RunCoordinator: Converting environment settings to Android format for scenario ${scenarioId}`);

                  // CRITICAL FIX: Preserve existing testinium selectedDevices when converting
                  const existingTestiniumDevices = options.environmentSettings.testinium?.selectedDevices || [];

                  // Create a new Android environment settings object
                  const androidSettings: AndroidEnvironmentSettings = {
                    platform: 'android',
                    aiModel: options.environmentSettings.aiModel as string,
                    aiModelName: options.environmentSettings.aiModelName,
                    aiModelConfig: options.environmentSettings.aiModelConfig,
                    sauceLabs: {
                      ...options.environmentSettings.sauceLabs,
                      selectedDevices: [device],
                      username: options.environmentSettings.sauceLabs?.username || '',
                      accessKey: options.environmentSettings.sauceLabs?.accessKey || ''
                    },
                    testinium: {
                      apiUrl: '',
                      clientId: '',
                      clientSecret: '',
                      issuerUri: '',
                      selectedDevices: existingTestiniumDevices
                    }
                  };

                  // Replace the environment settings
                  options.environmentSettings = androidSettings;

                  logger.info(`RunCoordinator: Using legacy device ID ${deviceId} for scenario ${scenarioId}`);
                }
              }
            }

            // AI Model bilgilerini ekle
            if (run.environment.aiModel) {
              try {
                // Cached user bilgilerini kullan
                if (!user) {
                  logger.warn(`RunCoordinator: User ${userId} not found or has no data. Continuing without AI model configuration.`);
                }
                // Kullanıcı bulunduysa ve şirket ID'si varsa devam et
                else if (companyId) {
                  const companyResult = await getCompanyById(companyId);

                  if (companyResult && companyResult.success && companyResult.company && companyResult.company.aiModels) {
                    // Seçilen AI modeli bul
                    const selectedModel = companyResult.company.aiModels.find(model =>
                      model.id === run.environment?.aiModel && model.isActive
                    );

                    if (selectedModel) {
                      // AI model bilgilerini options'a ekle
                      options.environmentSettings.aiModelConfig = {
                        OPENAI_BASE_URL: selectedModel.api,
                        OPENAI_API_KEY: selectedModel.apiKey,
                        MIDSCENE_MODEL_NAME: selectedModel.name
                      };

                      // Also add the AI model name
                      options.environmentSettings.aiModelName = selectedModel.name;

                    } else {
                      // Seçilen AI model bulunamadı veya aktif değil - hata döndür
                      logger.error(`RunCoordinator: Selected AI model ${run.environment.aiModel} not found or not active`);
                      return {
                        success: false,
                        scenarioId,
                        error: `Selected AI model is not available. Please select another AI model or contact your administrator.`
                      };
                    }
                  } else {
                    // Şirket bulunamadı veya AI modelleri yok - hata döndür
                    logger.error(`RunCoordinator: Company ${companyId} not found or has no AI models`);
                    return {
                      success: false,
                      scenarioId,
                      error: `No AI models available for your company. Please contact your administrator.`
                    };
                  }
                } else {
                  // Kullanıcının şirket ID'si yok - hata döndür
                  logger.error(`RunCoordinator: User ${userId} has no company ID`);
                  return {
                    success: false,
                    scenarioId,
                    error: `User is not associated with a company. Please contact your administrator.`
                  };
                }
              } catch (error) {
                // Hata durumunda log'la ve hata döndür
                logger.error(`RunCoordinator: Error getting AI model information: ${error}`);
                return {
                  success: false,
                  scenarioId,
                  error: `Error configuring AI model: ${error}`
                };
              }
            } else {
              // AI Model seçilmemiş - hata döndür
              logger.error(`RunCoordinator: No AI model selected for run ${runId}`);
              return {
                success: false,
                scenarioId,
                error: `No AI model selected. Please select an AI model in the run settings.`
              };
            }
          }

          if (run.reportSettings) {
            options.reportSettings = run.reportSettings;
          }

          // Use the original scenario name without appending device information
          let testName = updatedScenario.title || updatedScenario.name || "Unnamed Scenario";

          // Instead of modifying the scenario name, ensure device information is properly stored in the environment settings
          if (deviceIndex !== undefined && hasMultipleDevices) {
            // Log the device being used for this test for debugging purposes
            if (selectedDevices.length > 0 && deviceIndex < selectedDevices.length) {
              const device = selectedDevices[deviceIndex];
              logger.info(`RunCoordinator: Using device ${device.name} (${device.osVersion}) for test "${testName}" without modifying the name`);
            } else if (deviceIds.length > 0 && deviceIndex < deviceIds.length) {
              logger.info(`RunCoordinator: Using device ID ${deviceIds[deviceIndex]} for test "${testName}" without modifying the name`);
            }
          }

          // Test oluştur - executionId ve runId test oluşturma sürecinde geçirilir
          const testId = await this.testManager.addTest(
            scenarioId,
            testName,
            updatedScenario,
            10, // default priority
            userId,
            options
          );

          // Device bilgisini scenario status'a eklemek için prepare et
          let deviceInfo = undefined;
          if (deviceIndex !== undefined && hasMultipleDevices) {
            if (selectedDevices.length > 0 && deviceIndex < selectedDevices.length) {
              const device = selectedDevices[deviceIndex];
              deviceInfo = {
                id: device.id,
                name: device.name,
                osVersion: device.osVersion || 'Unknown',
                provider: device.provider as 'sauceLabs' | 'testinium' || 'sauceLabs'
              };
            } else if (deviceIds.length > 0 && deviceIndex < deviceIds.length) {
              deviceInfo = {
                id: deviceIds[deviceIndex],
                name: `Device ${deviceIds[deviceIndex]}`,
                osVersion: 'Unknown',
                provider: 'sauceLabs' as const
              };
            }
          }

          // Artık senaryolar otomatik olarak run report'a ekleniyor, ek bir işlem gerekmiyor
          if (reportId) {
            logger.debug(`RunCoordinator: Scenario ${scenarioId} will be automatically added to run report ${reportId}`);
          }

          return {
            success: true,
            testId,
            scenarioId,
            deviceInfo // Device bilgisini return'e ekle
          };
        } catch (error: any) {
          logger.error(`RunCoordinator: Error creating test for scenario ${scenarioId}: ${error.message}`);
          return {
            success: false,
            scenarioId,
            error: error.message
          };
        }
      };

      // Create test promises based on the distribution strategy
      let testPromises: Promise<any>[] = [];

      if (hasMultipleDevices && run.platform === 'android') {
        if (testDistributionStrategy === 'all-on-all') {
          // For "all-on-all" strategy, run each scenario on each device
          logger.info(`RunCoordinator: Using "all-on-all" strategy - running each scenario on each device`);

          const deviceCount = selectedDevices.length || deviceIds.length;

          // For each scenario, create a test for each device
          run.scenarioIds.forEach(scenarioId => {
            for (let deviceIndex = 0; deviceIndex < deviceCount; deviceIndex++) {
              testPromises.push(processScenario(scenarioId, deviceIndex));
            }
          });

          logger.info(`RunCoordinator: Created ${testPromises.length} test promises for ${run.scenarioIds.length} scenarios on ${deviceCount} devices`);
        } else if (testDistributionStrategy === 'distribute') {
          // For "distribute" strategy, distribute scenarios across devices
          logger.info(`RunCoordinator: Using "distribute" strategy - distributing scenarios across devices`);

          const deviceCount = selectedDevices.length || deviceIds.length;

          // Distribute scenarios across devices
          run.scenarioIds.forEach((scenarioId, index) => {
            const deviceIndex = index % deviceCount;
            testPromises.push(processScenario(scenarioId, deviceIndex));
          });

          logger.info(`RunCoordinator: Created ${testPromises.length} test promises by distributing ${run.scenarioIds.length} scenarios across ${deviceCount} devices`);
        } else {
          // Unknown strategy, fall back to default behavior
          logger.warn(`RunCoordinator: Unknown test distribution strategy: ${testDistributionStrategy}, falling back to default behavior`);
          testPromises = run.scenarioIds.map(scenarioId => processScenario(scenarioId));
        }
      } else {
        // For web platform or single device, process each scenario normally
        logger.info(`RunCoordinator: Processing scenarios normally (no multi-device distribution needed)`);
        testPromises = run.scenarioIds.map(scenarioId => processScenario(scenarioId));
      }

      logger.info(`RunCoordinator: Created ${testPromises.length} test promises in total`);

      // Tüm test oluşturma promise'larını çalıştır
      const results = await Promise.all(testPromises);

      // Sonuçları işle
      for (const result of results) {
        if (result.success && result.testId) {
          testIds.push(result.testId);
        } else if (!result.success) {
          scenarioErrors.push({
            scenarioId: result.scenarioId,
            error: result.error
          });
        }
      }

      // 10. Run durumunu güncelle
      if (testIds.length === 0) {
        // Hiç test oluşturulamamışsa failed olarak işaretle
        if (reportId) {
          await updateRunReport(reportId, {
            status: RunStatus.FAILED,
            $set: { source: 'RunCoordinator-executeRun-noTests' }
          });
          logger.info(`RunCoordinator: Updated run report ${reportId} status to FAILED`);
        }
        logger.error(`RunCoordinator: Failed to create any tests for run ${runId}`);

        return {
          success: false,
          executionId,
          reportId,
          scenarioErrors,
          error: 'Failed to create any tests for this run'
        };
      }

      // Durum güncelleme
      this.testManager.updateRunStatus(runId, RunStatus.RUNNING);

      // Run raporunu güncelle
      if (reportId) {
        await updateRunReport(reportId, {
          status: RunStatus.RUNNING,
          $set: { source: 'RunCoordinator-executeRun-testsCreated' }
        });
        logger.info(`RunCoordinator: Updated run report ${reportId} status to RUNNING`);
      }

      // Bellek-veritabanı durumunu senkronize et
      await this.syncRunStatusWithDatabase(runId);



      // 11. Run izlemeyi başlat - bu asenkron olarak çalışacak
      if (executionId) {
        // Asenkron olarak çalıştır ve sonucu bekleme
        this.monitorRunExecution(runId, executionId).catch(error => {
          logger.error(`RunCoordinator: Error starting monitoring for run ${runId}: ${error.message}`);
        });
        logger.info(`RunCoordinator: Started monitoring for run ${runId}`);
      }

      return {
        success: true,
        executionId,
        reportId,
        testIds,
        scenarioErrors: scenarioErrors.length > 0 ? scenarioErrors : undefined,
        message: `Created ${testIds.length} tests from ${run.scenarioIds.length} scenarios`
      };

    } catch (error: any) {
      // Kapsamlı hata işleme
      logger.error(`RunCoordinator: Error initiating run ${runId}: ${error.message}`, {
        error,
        stack: error.stack,
        runId,
        executionId,
        reportId
      });

      // Temizleme işlemi
      await this.cleanupFailedRun(runId, executionId, runInitialized);

      return {
        success: false,
        executionId,
        error: `Failed to initiate run: ${error.message}`
      };
    }
  }

  /**
   * Run raporunu ID'ye göre getirir
   * @param runId Run ID
   * @param executionId Execution ID (opsiyonel)
   */
  public async getRunReportByRunId(runId: string, executionId?: string): Promise<any | null> {
    try {
      // atomicReportService'i kullanarak run raporunu al
      const reportResult = await getRunReportByRunId(runId, executionId);

      if (!reportResult.success) {
        logger.warn(`RunCoordinator: Cannot get run report for ${runId}: ${reportResult.message}`);
        return null;
      }

      return reportResult.report;
    } catch (error: any) {
      logger.error(`RunCoordinator: Error getting run report: ${error.message}`);
      return null;
    }
  }

  /**
   * Run durumunu veritabanından okur
   * @param runId Run ID
   * @param executionId Execution ID (opsiyonel)
   */
  public async getRunStatusFromDatabase(runId: string, executionId?: string): Promise<RunStatusInfo | null> {
    try {
      // Önce atomicReportService'i kullanarak run raporunu al
      const reportResult = await getRunReportByRunId(runId, executionId);

      if (reportResult.success && reportResult.report) {
        // Run raporundan durum bilgilerini dön
        // Import the calculateTestResults function
        const { calculateTestResults } = await import('../../models/test-types.js');

        // Calculate test results from scenarioStatuses
        const statusCounts = calculateTestResults(reportResult.report);

        return {
          runId: reportResult.report.runId,
          executionId: reportResult.report.executionId,
          status: reportResult.report.status,
          startedAt: reportResult.report.startedAt,
          completedAt: reportResult.report.completedAt,
          testResults: statusCounts, // Use calculated counts instead of the field
          scenarioStatuses: reportResult.report.scenarioStatuses || []
        };
      }

      // Eğer run raporu bulunamazsa, atomicReportService'i dene
      const { getRunStatus } = await import('../../services/mongo/atomicReportService.js');
      const statusResult = await getRunStatus(runId, executionId);

      if (!statusResult.success) {
        logger.warn(`RunCoordinator: Cannot get run ${runId} status from database: ${statusResult.message}`);
        return null;
      }

      return statusResult.status;
    } catch (error: any) {
      logger.error(`RunCoordinator: Error getting run status from database: ${error.message}`);
      return null;
    }
  }

  /**
   * Run raporunun mevcut olduğunu kontrol eder ve gerekirse oluşturur
   * @param runId Run ID
   * @param executionId Execution ID
   */
  public async ensureRunReportExists(runId: string, executionId: string): Promise<boolean> {
    try {
      // Önce run raporunu kontrol et
      const reportResult = await getRunReportByRunId(runId, executionId);

      // Rapor zaten varsa, true döndür
      if (reportResult.success && reportResult.report) {
        logger.debug(`RunCoordinator: Run report already exists for run ${runId} with executionId ${executionId}`);
        return true;
      }

      // Rapor yoksa, oluştur - run bilgilerini al ve userId, teamId, companyId bilgilerini ekle
      logger.info(`RunCoordinator: Creating new run report for run ${runId} with executionId ${executionId}`);

      // Run bilgilerini al
      const runResult = await getRunById(runId);
      if (!runResult.success || !runResult.run) {
        logger.error(`RunCoordinator: Failed to get run ${runId} when creating run report`);
        return false;
      }

      const run = runResult.run;
      const userId = run.userId;
      const teamId = run.teamId || null;
      const companyId = run.companyId || null;

      logger.info(`RunCoordinator: Creating run report with userId: ${userId}, teamId: ${teamId}, companyId: ${companyId}`);

      const createResult = await createRunReportAtomic(runId, executionId, {
        userId: userId,
        teamId: teamId ? teamId : undefined,
        companyId: companyId ? companyId : undefined
      });

      if (!createResult.success) {
        logger.error(`RunCoordinator: Failed to create run report for run ${runId}: ${createResult.message}`);
        return false;
      }


      return true;
    } catch (error: any) {
      logger.error(`RunCoordinator: Error ensuring run report exists: ${error.message}`);
      return false;
    }
  }

  /**
   * Veritabanı durumunu senkronize eder (eski bellek tabanlı senkronizasyon kaldırıldı)
   * Bu metod artık async olarak çalışıyor
   * @param runId Run ID
   */

  /**
   * Update run status
   * @param runId Run ID
   * @param status New status
   * @param testResults Optional test results
   */
  public async updateRunStatus(runId: string, status?: string, testResults?: Record<string, any>): Promise<boolean> {
    try {
      logger.info(`RunCoordinator: Updating run ${runId} status to ${status}`);

      // Get the current run report
      const report = await this.getRunReportByRunId(runId);

      if (!report) {
        logger.warn(`RunCoordinator: Cannot update run ${runId} status - report not found`);
        return false;
      }

      // Update the report with the new status
      const updateData: Record<string, any> = {};

      if (status) {
        updateData.status = status;
      }

      if (testResults) {
        updateData.testResults = testResults;
      }

      // Update the report - use report.id instead of report._id to match the UUID format
      const updateResult = await updateRunReport(report.id, {
        ...updateData,
        $set: { source: 'RunCoordinator-updateRunStatus' }
      });

      if (!updateResult.success) {
        logger.error(`RunCoordinator: Failed to update run ${runId} status: ${updateResult.message}`);
        return false;
      }


      return true;
    } catch (error: any) {
      logger.error(`RunCoordinator: Error updating run ${runId} status: ${error.message}`);
      return false;
    }
  }

  /**
   * Update scenario status
   * @param runId Run ID
   * @param scenarioId Scenario ID
   * @param status New status
   * @param testId Optional test ID
   */
  public async updateScenarioStatus(
    runId: string,
    scenarioId: string,
    status: 'queued' | 'running' | 'passed' | 'failed' | 'stopped' | 'idle',
    testId?: string
  ): Promise<boolean> {
    try {
      logger.info(`RunCoordinator: Updating scenario ${scenarioId} status to ${status} in run ${runId}`);

      // Get the current run report to get the executionId
      const report = await this.getRunReportByRunId(runId);

      if (!report) {
        logger.warn(`RunCoordinator: Cannot update scenario ${scenarioId} status - report for run ${runId} not found`);
        return false;
      }

      // Use the atomic report service for updating scenario status
      const { updateScenarioStatusAtomic } = await import('../../services/mongo/atomicReportService.js');

      // Call the atomic update method with the executionId from the report
      const updateResult = await updateScenarioStatusAtomic(
        runId,
        report.executionId,
        scenarioId,
        status,
        {
          testId: testId,
          source: 'RunCoordinator',
          transactionId: `coordinator-${runId}-${scenarioId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`
        }
      );

      if (!updateResult.success) {
        logger.error(`RunCoordinator: Failed to update scenario ${scenarioId} status: ${updateResult.message}`);
        return false;
      }


      return true;
    } catch (error: any) {
      logger.error(`RunCoordinator: Error updating scenario ${scenarioId} status: ${error.message}`);
      return false;
    }
  }

  /**
   * Execute a run with all its scenarios
   * @param runId Run ID
   * @param options Additional options for the run
   */
  public async executeRun(runId: string, options: {
    userId?: string;
    executionId?: string;
    isScheduled?: boolean;
    scheduleId?: string;
    scheduleRunId?: string;
  } = {}): Promise<{ success: boolean; message?: string }> {
    try {
      const userId = options.userId || '';

      // Initiate the run
      const result = await this.initiateRun(runId, userId);

      if (!result.success) {
        return {
          success: false,
          message: result.error || 'Failed to execute run'
        };
      }

      return {
        success: true,
        message: result.message || 'Run executed successfully'
      };
    } catch (error: any) {
      logger.error(`RunCoordinator: Error executing run ${runId}: ${error.message}`);
      return {
        success: false,
        message: `Error executing run: ${error.message}`
      };
    }
  }

  /**
   * Veritabanı durumunu senkronize eder (eski bellek tabanlı senkronizasyon kaldırıldı)
   * @param runId Run ID
   */
  private async syncRunStatusWithDatabase(runId: string): Promise<void> {
    // Bu metod artık hiçbir şey yapmıyor - bellek tabanlı durum yönetimi kaldırıldı
    logger.debug(`RunCoordinator: syncRunStatusWithDatabase is now a no-op - memory-based state management has been removed for run ${runId}`);
  }

  /**
   * Başarısız olan run için temizleme işlemi yapar
   */
  private async cleanupFailedRun(
    runId: string,
    executionId?: string,
    _runInitialized = false // Renamed with underscore to indicate it's not used
  ): Promise<void> {
    try {
      logger.info(`RunCoordinator: Cleaning up failed run ${runId}`);

      // 1. Run raporunu FAILED olarak güncelle
      if (executionId) {
        try {
          // Run raporunu getir - executionId belirtilmişse o raporu, belirtilmemişse en son raporu getir
          const { getRunReportByRunId } = await import('../../services/mongo/atomicReportService.js');
          const reportResult = await getRunReportByRunId(runId, executionId);

          if (reportResult.success && reportResult.report) {
            // Run raporunu güncelle
            await updateRunReport(reportResult.report.id, {
              status: RunStatus.FAILED,
              completedAt: new Date(),
              $set: { source: 'RunCoordinator-stopRun' }
            });
            logger.info(`RunCoordinator: Updated run report ${reportResult.report.id} status to FAILED`);
          }
        } catch (reportError) {
          logger.error(`RunCoordinator: Error updating run report: ${reportError}`);
        }
      }

      // 2. Varsa oluşturulan testleri iptal et
      const allTests = await this.testManager.getAllTests();
      const testsToCancel = allTests.filter((t: TestProcess) => t.runId === runId);
      logger.info(`RunCoordinator: Cancelling ${testsToCancel.length} tests for failed run ${runId}`);

      for (const test of testsToCancel) {
        try {
          await this.testManager.stopTest(test.id);
        } catch (removeError) {
          logger.error(`RunCoordinator: Error stopping test ${test.id}: ${removeError}`);
        }
      }

      logger.info(`RunCoordinator: Cleanup completed for failed run ${runId}`);
    } catch (error: any) {
      logger.error(`RunCoordinator: Error during cleanup for run ${runId}: ${error.message}`);
    }
  }

  /**
   * Run'ın yürütmesini izler ve tamamlanma durumunu tespit eder
   * Test seviyesinde stuck detection zaten mevcut olduğu için run seviyesinde stuck detection kaldırıldı
   * @param runId Run ID'si
   * @param executionId Execution ID'si
   * @param monitorInterval İzleme aralığı (ms), varsayılan 10 saniye
   */
  public async monitorRunExecution(
    runId: string,
    executionId: string,
    monitorInterval: number = 10000
  ): Promise<void> {
    // Önceki izleme timerı varsa temizle
    if (this.runMonitorTimers.has(runId)) {
      clearInterval(this.runMonitorTimers.get(runId)!);
      this.runMonitorTimers.delete(runId);
    }

    logger.info(`RunCoordinator: Starting to monitor run ${runId} with execution ID ${executionId} - completion events only`);

    // Run raporunun mevcut olduğunu kontrol et ve gerekirse oluştur
    this.ensureRunReportExists(runId, executionId).catch(error => {
      logger.error(`RunCoordinator: Error ensuring run report exists: ${error.message}`);
    });

    // runReportEvents'ten event listener ekle - dynamic import kullanarak
    const { runReportEvents } = await import('../../services/mongo/atomicReportService.js');

    // Tamamlanma olayını dinle
    const completionHandler = (result: any) => {
      if (result.runId === runId && result.executionId === executionId) {
        logger.info(`RunCoordinator: Received completion event for run ${runId} with status ${result.status}`);

        // Run tamamlandığında şirketin kalan dakikalarını güncelle
        this.updateCompanyRunMinutes(runId);

        // Tamamlanma olayını yayınla
        const runResult = {
          success: result.status === 'completed', // "completed" durumu başarılı olarak kabul ediliyor
          status: result.status,
          total: result.total || 0,
          passed: result.passed || 0, // Burada "passed" kullanılıyor, ancak bu değer "completed" testlerin sayısını temsil ediyor
          failed: result.failed || 0
        };

        // Emit the event with the run result
        logger.info(`RunCoordinator: Emitting run:${runId}:${executionId}:completed event`);
        this.testManager.emit(`run:${runId}:${executionId}:completed`, runResult);

        // İzleme timerını durdur ve temizle
        if (this.runMonitorTimers.has(runId)) {
          clearInterval(this.runMonitorTimers.get(runId)!);
          this.runMonitorTimers.delete(runId);
        }

        // Event listener'ı kaldır
        runReportEvents.removeListener(`run:${runId}:${executionId}:completed`, completionHandler);
      }
    };

    // Event listener'ı ekle
    runReportEvents.on(`run:${runId}:${executionId}:completed`, completionHandler);

    // Run durumunu düzenli aralıklarla kontrol et - sadece tamamlanma durumu için
    const timerId = setInterval(async () => {
      try {
        // Doğrudan veritabanından run durumunu al
        const { getRunReportByRunId } = await import('../../services/mongo/atomicReportService.js');
        const reportResult = await getRunReportByRunId(runId, executionId);

        if (!reportResult.success || !reportResult.report) {
          logger.warn(`RunCoordinator: Could not find run report for ${runId} with executionId ${executionId}. Stopping monitor.`);
          clearInterval(timerId);
          this.runMonitorTimers.delete(runId);
          return;
        }

        const report = reportResult.report;
        const currentStatus = report.status;

        // Tamamlanmış, başarısız veya durdurulmuş durumları kontrol et
        if (['completed', 'failed', 'stopped', 'partial'].includes(currentStatus)) {
          logger.info(`RunCoordinator: Run ${runId} has finished with status: ${currentStatus}. Final tests: Total=${report.testResults?.total || 0}, Completed=${report.testResults?.completed || 0}, Failed=${report.testResults?.failed || 0}, Stopped=${report.testResults?.stopped || 0}`);

          // Run tamamlandığında şirketin kalan dakikalarını güncelle
          this.updateCompanyRunMinutes(runId);

          // Tamamlanma olayını yayınla
          const runResult = {
            success: currentStatus === 'completed', // "completed" durumu başarılı olarak kabul ediliyor
            status: currentStatus,
            total: report.testResults?.total || 0,
            passed: report.testResults?.completed || 0, // "completed" testler "passed" olarak raporlanıyor
            failed: report.testResults?.failed || 0
          };

          // Emit the event with the run result
          logger.info(`RunCoordinator: Emitting run:${runId}:${executionId}:completed event`);
          this.testManager.emit(`run:${runId}:${executionId}:completed`, runResult);

          // İzleme timerını durdur ve temizle
          clearInterval(timerId);
          this.runMonitorTimers.delete(runId);

          // Event listener'ı kaldır
          runReportEvents.removeListener(`run:${runId}:${executionId}:completed`, completionHandler);
          return;
        }

        // REMOVED: Run-level stuck detection logic
        // Reason: Individual tests are already monitored by CentralizedStuckMonitoringService and NodeTimeoutGuardian
        // Run status is automatically updated based on test completions, so run-level lastUpdated monitoring 
        // creates false positives when tests are progressing normally but run report isn't updated
        
      } catch (error: any) {
        logger.error(`RunCoordinator: Error monitoring run ${runId}: ${error.message}`);
      }
    }, monitorInterval);

    // Timer'ı kaydet
    this.runMonitorTimers.set(runId, timerId);
  }

  /**
   * Run süresini hesaplar ve şirketin kalan dakikalarını günceller - asenkron çalışır
   * Başarılı veya başarısız tüm tamamlanmış runlar için süre hesaplanır
   * @param runId Run ID'si
   */
  private updateCompanyRunMinutes(runId: string): void {
    // Asenkron olarak çalıştır ve sonucu bekleme
    this.updateCompanyRunMinutesAsync(runId).catch(error => {
      logger.error(`RunCoordinator: Unhandled error in updateCompanyRunMinutesAsync for ${runId}: ${error.message}`);
    });
  }

  /**
   * Run süresini hesaplar ve şirketin kalan dakikalarını günceller - asenkron implementasyon
   * Başarılı veya başarısız tüm tamamlanmış runlar için süre hesaplanır
   * @param runId Run ID'si
   */
  private async updateCompanyRunMinutesAsync(runId: string): Promise<void> {
    try {
      // Run raporunu getir
      const reportResult = await getRunReportByRunId(runId);
      if (!reportResult.success || !reportResult.report) {
        logger.error(`RunCoordinator: Could not find run report for run ${runId} when updating company run minutes`);
        return;
      }

      const report = reportResult.report;

      // Run'un tamamlanmış olup olmadığını kontrol et
      if (!['completed', 'failed', 'stopped', 'partial'].includes(report.status)) {
        logger.warn(`RunCoordinator: Run ${runId} has not finished (status: ${report.status}), skipping duration calculation`);
        return;
      }

      // Run süresini hesapla
      if (!report.startedAt || !report.completedAt) {
        logger.warn(`RunCoordinator: Run ${runId} has no start or completion time, cannot calculate duration`);
        return;
      }

      // Tarihleri Date objelerine çevir
      const startDate = new Date(report.startedAt);
      const endDate = new Date(report.completedAt);

      // Geçerli tarihler mi kontrol et
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        logger.warn(`RunCoordinator: Run ${runId} has invalid start or completion time`);
        return;
      }

      // Süreyi milisaniye cinsinden hesapla
      const durationMs = endDate.getTime() - startDate.getTime();

      // Negatif süre durumunu kontrol et
      if (durationMs < 0) {
        logger.warn(`RunCoordinator: Run ${runId} has negative duration (${durationMs}ms), using absolute value`);
      }

      // Süreyi dakika cinsine çevir (yukarı yuvarlama)
      const durationMinutes = Math.ceil(Math.abs(durationMs) / (1000 * 60));

      logger.info(`RunCoordinator: Run ${runId} duration: ${durationMinutes} minutes (status: ${report.status})`);

      // Run'a ait kullanıcıyı bul
      const runResult = await getRunById(runId);
      if (!runResult.success || !runResult.run) {
        logger.error(`RunCoordinator: Could not find run ${runId} when updating company run minutes`);
        return;
      }

      const run = runResult.run;
      const userId = run.userId;

      if (!userId) {
        logger.error(`RunCoordinator: Run ${runId} has no user ID`);
        return;
      }

      // Kullanıcı bilgilerini al
      const userResult = await getUserById(userId);
      if (!userResult.success || !userResult.data) {
        logger.error(`RunCoordinator: User ${userId} not found when updating company run minutes`);
        return;
      }

      const user = userResult.data;
      if (!user.companyId) {
        logger.error(`RunCoordinator: User ${userId} has no company ID`);
        return;
      }

      // Şirket bilgilerini al
      const companyResult = await getCompanyById(user.companyId);
      if (!companyResult.success || !companyResult.company) {
        logger.error(`RunCoordinator: Company ${user.companyId} not found for user ${userId}`);
        return;
      }

      const company = companyResult.company;
      const remainingRunMinutes = company.settings?.remaining?.runMinutes;

      if (remainingRunMinutes === undefined) {
        logger.warn(`RunCoordinator: Company ${user.companyId} has no remaining run minutes setting`);
        return;
      }

      // Kalan dakikayı hesapla
      let newRemainingMinutes = remainingRunMinutes - durationMinutes;

      // Kalan dakika 0'dan küçükse 0 olarak ayarla
      if (newRemainingMinutes < 0) {
        newRemainingMinutes = 0;
      }

      // Şirketin kalan dakikalarını güncelle
      const updateResult = await updateCompanyRemaining(user.companyId, {
        runMinutes: newRemainingMinutes
      });

      if (updateResult.success) {
        logger.info(`RunCoordinator: Updated company ${user.companyId} remaining run minutes from ${remainingRunMinutes} to ${newRemainingMinutes} (run status: ${report.status})`);
      } else {
        logger.error(`RunCoordinator: Failed to update company ${user.companyId} remaining run minutes: ${updateResult.message}`);
      }
    } catch (error: any) {
      logger.error(`RunCoordinator: Error updating company run minutes: ${error.message}`);
    }
  }

  /**
   * Tüm izleme timerlarını temizler
   */
  public cleanupAllMonitors(): void {
    for (const [runId, timerId] of this.runMonitorTimers.entries()) {
      clearInterval(timerId);
      logger.info(`RunCoordinator: Cleaned up monitor for run ${runId}`);
    }
    this.runMonitorTimers.clear();
  }
}
