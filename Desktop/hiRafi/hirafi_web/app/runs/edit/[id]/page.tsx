'use client'

import { CreateRunLayout } from "@/components/create-run/create-run-layout"
import { RunDetails } from "@/components/create-run/common/run-details"
import { SelectScenarios } from "@/components/create-run/common/select-scenarios"
import { EnvironmentSettings } from "@/components/create-run/environment-settings"
import { RunPreview } from "@/components/create-run/run-preview"
import { RunFormProvider, useRunForm } from "@/components/create-run/run-context"
import { useState, useEffect, useCallback } from "react"
import { LoaderCircle } from "lucide-react"
import { Sidebar } from "@/components/sidebar/sidebar"
import { ProtectedRoute } from "@/components/protected-route"
import { useParams } from 'next/navigation'
import { toast } from "@/lib/utils/toast-utils"
import { fetchWithAuth, API_BASE_URL } from "@/lib/api/auth"
import { runApi } from "@/lib/api"

function EditRunPageContent() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const params = useParams();
  const runId = params?.id as string;
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [errorLoading, setErrorLoading] = useState<string | null>(null);
  // State to hold the prepared form data once fetched
  const [initialFormData, setInitialFormData] = useState<any | null>(null);
  const [scenariosLoaded, setScenariosLoaded] = useState(false);

  // Fetch run data on component mount - only load run data, not scenarios
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates on unmounted component
    if (runId) {
      setIsLoadingData(true);
      setErrorLoading(null);
      setInitialFormData(null); // Reset initial data

      const fetchRunData = async () => {
        try {
          const response = await runApi.getRunById(runId);

          if (!response.success) {
            throw new Error(response.error || `Failed to fetch run data`);
          }

          const data = response;
          if (!isMounted) return; // Exit if component unmounted

          if (data.success && data.data?.run) {
            // Prepare data in RunFormData format without scenarios

            // reportSettings alanını kontrol et
            const reportSettings = data.data.run.reportSettings || {
              takeScreenshots: true,
              takeVideos: false,
              pageMetrics: false,
              networkData: false,
              tracingData: false,
              accessibilityData: false
            };

            // Get the platform from the run data
            const platform = data.data.run.platform || 'web';

            // Get the environment settings from the run data
            let environment = data.data.run.environment || {};

            // Ensure the environment has the platform field
            environment.platform = platform;

            // For Android platform, ensure the environment has the correct structure
            if (platform === 'android') {
              // Make sure sauceLabs object exists
              if (!environment.sauceLabs) {
                environment.sauceLabs = {};
              }

              // If there are deviceIds in the old format, convert them to selectedDevices
              if (environment.deviceIds && Array.isArray(environment.deviceIds) && environment.deviceIds.length > 0) {
                // If selectedDevices doesn't exist, create it
                if (!environment.sauceLabs.selectedDevices) {
                  environment.sauceLabs.selectedDevices = [];
                }

                // If there are no selectedDevices yet, try to convert deviceIds to selectedDevices
                if (environment.sauceLabs.selectedDevices.length === 0) {
                  // Try to fetch device details from SauceLabs API, but fallback to placeholder if needed
                  try {
                    // For now, create better placeholders with device IDs that can be resolved later
                    environment.sauceLabs.selectedDevices = environment.deviceIds.map((id: string) => ({
                      id,
                      name: `Device ${id}`, // Better placeholder with actual ID
                      osVersion: "Android",   // More generic placeholder
                      provider: "sauceLabs",  // Default provider
                      deviceType: "phone",    // Default type
                      needsResolution: true   // Flag to indicate this needs to be resolved
                    }));
                  } catch (error) {
                    console.warn('Could not resolve device details for deviceIds:', environment.deviceIds);
                    // Keep the placeholder approach as fallback
                    environment.sauceLabs.selectedDevices = environment.deviceIds.map((id: string) => ({
                      id,
                      name: `Device ${id}`,
                      osVersion: "Android",
                      provider: "sauceLabs",
                      deviceType: "phone"
                    }));
                  }
                }
              }

              // If there's a deviceId in the old format, add it to selectedDevices if not already there
              if (environment.deviceId && !environment.deviceIds?.includes(environment.deviceId)) {
                if (!environment.sauceLabs.selectedDevices) {
                  environment.sauceLabs.selectedDevices = [];
                }

                // Add the deviceId to selectedDevices if it's not already there
                const deviceExists = environment.sauceLabs.selectedDevices.some(
                  (d: any) => d.id === environment.deviceId
                );

                if (!deviceExists) {
                  environment.sauceLabs.selectedDevices.push({
                    id: environment.deviceId,
                    name: "Unknown Device", // Placeholder
                    osVersion: "Unknown",   // Placeholder
                    provider: "sauceLabs",  // Default provider
                    deviceType: "phone"     // Default type
                  });
                }
              }

              // Set default test distribution strategy if not present
              if (!environment.testDistribution) {
                environment.testDistribution = {
                  strategy: 'all-on-all' // Default strategy
                };
              }

              // Clean up old fields that are no longer used
              delete environment.deviceIds;
              delete environment.deviceId;

              // If appium object exists, extract relevant fields and then remove it
              if (environment.appium) {
                // If there's app information in appium, move it to sauceLabs
                if (environment.appium.appPackage) {
                  environment.sauceLabs.selectedApp = {
                    id: environment.appium.appPackage,
                    name: environment.appium.appPackage,
                    version: environment.appium.platformVersion
                  };
                }

                // Remove the appium object as it's no longer used
                delete environment.appium;
              }

              // If there's a standalone appId, convert it to selectedApp
              if (environment.sauceLabs.appId && !environment.sauceLabs.selectedApp) {
                environment.sauceLabs.selectedApp = {
                  id: environment.sauceLabs.appId,
                  name: `App ${environment.sauceLabs.appId}`,
                  version: 'Unknown'
                };

                // Remove the redundant appId field
                delete environment.sauceLabs.appId;
              }
            }

            const formDataToSet = {
              id: data.data.run.id,
              name: data.data.run.name || '',
              description: data.data.run.description || '',
              tags: data.data.run.tags || [],
              platform: platform,
              environment: environment,
              reportSettings: reportSettings,
              scenarioIds: data.data.run.scenarioIds || [],
              scenarios: [], // Will be populated later when needed
              newTag: '', // Initialize newTag
              options: data.data.run.options || {} // Include options for sequential run setting
            };

            setInitialFormData(formDataToSet); // Set the prepared data
          } else {
            throw new Error(data.error || "Failed to fetch valid run data");
          }
        } catch (err: any) {
          if (!isMounted) return;
          setErrorLoading(err.message);
          toast.error(err.message || "Could not load run data for editing.");
        } finally {
          if (isMounted) {
            setIsLoadingData(false);
          }
        }
      };

      fetchRunData();
    } else {
      setErrorLoading("Run ID is missing from the URL.");
      setIsLoadingData(false);
    }

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [runId]);

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />
      <div className="flex-1 overflow-hidden flex flex-col">
        <RunFormProvider isEditMode={true}>
          <EditRunLoader
            isLoading={isLoadingData}
            error={errorLoading}
            runId={runId}
            initialFormData={initialFormData}
            scenariosLoaded={scenariosLoaded}
            setScenariosLoaded={setScenariosLoaded}
          />
        </RunFormProvider>
      </div>
    </div>
  )
}

interface EditRunLoaderProps {
  isLoading: boolean;
  error: string | null;
  runId: string;
  initialFormData: any; // Receive initial data
  scenariosLoaded: boolean;
  setScenariosLoaded: (loaded: boolean) => void;
}

function EditRunLoader({ isLoading, error, runId, initialFormData, scenariosLoaded, setScenariosLoaded }: EditRunLoaderProps) {
  const { setFormDataFromExisting, currentStep } = useRunForm();
  const [hasSetInitialData, setHasSetInitialData] = useState(false);

  // Effect to set form data once when it's available and loading is done
  useEffect(() => {
    if (!isLoading && !error && initialFormData && !hasSetInitialData) {
      // Deep copy yaparak form datasını hazırlayalım
      const preparedFormData = JSON.parse(JSON.stringify(initialFormData));

      // Verileri ayarla ve bayrağı güncelle
      setFormDataFromExisting(preparedFormData);
      setHasSetInitialData(true);
    }
  }, [isLoading, error, initialFormData, hasSetInitialData, setFormDataFromExisting]);

  // Load scenarios data only when needed (when currentStep is 1 - Select Scenarios)
  useEffect(() => {
    if (currentStep === 1 && !scenariosLoaded && initialFormData) {
      const loadScenarios = async () => {
        try {
          const scenariosResponse = await fetchWithAuth(`${API_BASE_URL}/scenarios`);

          if (!scenariosResponse.ok) {
            throw new Error("Failed to fetch available scenarios");
          }

          const scenariosData = await scenariosResponse.json();

          if (scenariosData.success && scenariosData.scenarios) {
            // Combine with existing form data
            const allScenarios = scenariosData.scenarios.map((s: any) => ({
              ...s,
              name: s.name || s.title || "Unnamed Scenario",
              selected: initialFormData.scenarioIds.includes(s.id)
            }));

            // Update the form data with scenarios
            const updatedFormData = {
              ...initialFormData,
              scenarios: allScenarios
            };

            // Update selected scenarios in context
            const selectedScenarioIds = initialFormData.scenarioIds || [];
            const selectedDetails = allScenarios
              .filter((s: any) => selectedScenarioIds.includes(s.id))
              .map((s: any) => ({ ...s, selected: true }));

            // Refresh form data with scenarios
            setFormDataFromExisting(updatedFormData);

            // Mark scenarios as loaded to prevent multiple requests
            setScenariosLoaded(true);
          }
        } catch (error) {
          toast.error("Failed to load scenarios. Please try again.");
        }
      };

      loadScenarios();
    }
  }, [currentStep, scenariosLoaded, initialFormData, setFormDataFromExisting, setScenariosLoaded]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <LoaderCircle className="w-12 h-12 text-indigo-600 animate-spin mb-4" />
        <p className="text-lg font-medium text-gray-700 dark:text-gray-300">Loading Run Data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-6">
        <p className="text-xl font-semibold text-red-600 mb-2">Error Loading Run</p>
        <p className="text-gray-600 dark:text-gray-400">{error}</p>
      </div>
    );
  }

  // Render the layout only when data is ready (initialFormData is not null)
  if (!initialFormData) {
    // This state should ideally not be reached if isLoading/error handles correctly,
    // but serves as a fallback.
    return (
        <div className="flex flex-col items-center justify-center h-full">
            <p>Initializing...</p>
        </div>
    );
  }

  return (
    <CreateRunLayout isEditing={true} runId={runId}>
      <RunContent
        runId={runId}
        initialFormData={initialFormData}
        scenariosLoaded={scenariosLoaded}
      />
    </CreateRunLayout>
  );
}

// RunContent remains largely the same, relying on data populated in the context
interface RunContentProps {
  runId: string;
  initialFormData: any;
  scenariosLoaded: boolean;
}

// Custom wrapper for SelectScenariosNew in edit mode
function EditSelectScenarios({ initialFormData }: { initialFormData: any }) {
  const { selectedScenarios, setSelectedScenarios, updateSelectedScenariosDetails } = useRunForm();

  // Add a memoized handler to prevent recreation on each render
  const handleToggleScenario = useCallback((scenarioId: string) => {
    setSelectedScenarios(prevSelected => {
      let newSelected: string[];
      if (prevSelected.includes(scenarioId)) {
        // Senaryoyu çıkar
        newSelected = prevSelected.filter(id => id !== scenarioId);
      } else {
        // Senaryoyu ekle
        newSelected = [...prevSelected, scenarioId];
      }

      // Seçilmiş senaryoların detaylarını güncelle
      const allScenarios = initialFormData?.scenarios || [];
      const selectedDetails = allScenarios
        .filter((s: any) => newSelected.includes(s.id))
        .map((s: any) => ({ ...s, selected: true }));

      updateSelectedScenariosDetails(selectedDetails);

      return newSelected;
    });
  }, [setSelectedScenarios, updateSelectedScenariosDetails, initialFormData]);

  return <SelectScenarios onToggleScenario={handleToggleScenario} editMode={true} />;
}

function RunContent({ runId, initialFormData, scenariosLoaded }: RunContentProps) {
  const { currentStep } = useRunForm()
  const [isStepLoading, setIsStepLoading] = useState(false)

  // Show loading animation between steps
  useEffect(() => {
    setIsStepLoading(true)
    const timer = setTimeout(() => {
      setIsStepLoading(false)
    }, 300) // Slightly faster transition for edit

    return () => clearTimeout(timer)
  }, [currentStep])

  if (isStepLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh]">
        <div className="relative w-16 h-16">
          <div className="w-16 h-16 rounded-full border-4 border-indigo-200 border-t-indigo-600 animate-spin"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 rounded-full bg-white dark:bg-gray-900"></div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <LoaderCircle className="w-5 h-5 text-indigo-600 animate-pulse" />
          </div>
        </div>
        <p className="mt-4 text-indigo-600 dark:text-indigo-400 font-medium">
          Loading {currentStep === 0 ? "Run Details" :
                   currentStep === 1 ? "Select Scenarios" :
                   currentStep === 2 ? "Environment & Reports" :
                   "Review & Update"}...
        </p>
      </div>
    )
  }

  // If we're on the Select Scenarios step but scenarios haven't loaded yet, show loading
  if (currentStep === 1 && !scenariosLoaded) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh]">
        <LoaderCircle className="w-12 h-12 text-indigo-600 animate-spin mb-4" />
        <p className="text-lg">Loading scenarios...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full h-full">
      {/* Step 1: Edit Run Details */}
      {currentStep === 0 && (
        <RunDetails />
      )}

      {/* Step 2: Edit Selected Scenarios */}
      {currentStep === 1 && (
        <EditSelectScenarios initialFormData={initialFormData} />
      )}

      {/* Step 3: Edit Environment & Reports */}
      {currentStep === 2 && (
        <EnvironmentSettings />
      )}

      {/* Step 4: Review & Update */}
      {currentStep === 3 && (
        <RunPreview isEditing={true} runId={runId} />
      )}
    </div>
  )
}

export default function EditRunPage() {
  return (
    <ProtectedRoute>
      <EditRunPageContent />
    </ProtectedRoute>
  )
}