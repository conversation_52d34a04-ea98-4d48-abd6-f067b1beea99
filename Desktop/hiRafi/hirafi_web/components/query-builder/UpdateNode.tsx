import { <PERSON><PERSON>, Position } from 'reactflow';
import { But<PERSON> } from '@/components/ui/button';
import { X, Plus, Pencil } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState, useEffect } from 'react';

function UpdateNode({ data, id }: { data: any, id: string }) {
  const [columns, setColumns] = useState<string[]>([]);
  const [isFetchingColumns, setIsFetchingColumns] = useState(false);

  useEffect(() => {
    // Eğer tablo seçili ve connection string mevcut ise sütunları getir
    if (data.table && data.connectionString) {
      setIsFetchingColumns(true);
      
      // Doğrudan getDbColumns API'sini çağır
      import('@/lib/api/test-data').then(({ getDbColumns }) => {
        return getDbColumns(data.connectionString, data.table);
      }).then((result: any) => {
        if (result.success && result.data?.columns) {
          setColumns(result.data.columns);
        } else {
          setColumns([]);
        }
        setIsFetchingColumns(false);
      }).catch((error: any) => {
        console.error("Failed to fetch columns directly", error);
        setColumns([]);
        setIsFetchingColumns(false);
      });
    }
  }, [data.version, data.dbTables, data.table, data.connectionString]);

  const handleTableChange = async (tableName: string, resetState = true) => {
    if(resetState) {
        data.updateData({ table: tableName, fields: [{ col: '', val: '' }], conditions: [{ col: '', op: '=', val: '' }] });
    } else {
        data.updateData({ table: tableName });
    }

    setIsFetchingColumns(true);
    try {
      const fetchedColumns = await data.onTableSelect(tableName);
      setColumns(fetchedColumns || []);
    } catch (error) {
      console.error("Failed to fetch columns", error);
    } finally {
        setIsFetchingColumns(false);
    }
  };

  const handleFieldChange = (index: number, field: 'col' | 'val', value: string) => {
    const newFields = [...data.fields];
    newFields[index][field] = value;
    data.updateData({ fields: newFields });
  };
  
  const addField = () => {
      data.updateData({ fields: [...data.fields, { col: '', val: '' }] });
  };
  
  const removeField = (index: number) => {
      const newFields = data.fields.filter((_: any, i: number) => i !== index);
      data.updateData({ fields: newFields.length > 0 ? newFields : [{ col: '', val: '' }] });
  };
  
  const handleConditionChange = (index: number, field: 'col' | 'op' | 'val', value: string) => {
      const newConditions = [...data.conditions];
      newConditions[index][field] = value;
      data.updateData({ conditions: newConditions });
  };
  
  const addCondition = () => {
      data.updateData({ conditions: [...data.conditions, { col: '', op: '=', val: '' }] });
  };
  
  const removeCondition = (index: number) => {
      const newConditions = data.conditions.filter((_: any, i: number) => i !== index);
      data.updateData({ conditions: newConditions.length > 0 ? newConditions : [{ col: '', op: '=', val: '' }] });
  };

  return (
    <div className="query-builder-node">
      <Handle type="target" position={Position.Left} />
      <div className="node-header">
        <div className="node-title"><Pencil className="h-4 w-4 text-orange-500" /><span>UPDATE Data</span></div>
        <Button variant="ghost" size="icon" className="h-6 w-6 btn" onClick={data.onDelete}><X className="h-4 w-4" /></Button>
      </div>

      <div className="node-body">
        <div className="node-section">
            <Label className="node-section-label">TABLE</Label>
            <Select onValueChange={handleTableChange} value={data.table}>
                <SelectTrigger className="react-select-trigger"><SelectValue placeholder="Select table..." /></SelectTrigger>
                <SelectContent>{data.dbTables.map((t: string) => <SelectItem key={t} value={t}>{t}</SelectItem>)}</SelectContent>
            </Select>
        </div>

        {isFetchingColumns && <div>Loading...</div>}

        {data.table && !isFetchingColumns && (
            <>
                <div className="node-section">
                    <Label className="node-section-label">SET</Label>
                    {data.fields.map((field: any, index: number) => (
                        <div key={index} className="node-row">
                            <Select value={field.col} onValueChange={(val) => handleFieldChange(index, 'col', val)}><SelectTrigger className="react-select-trigger"><SelectValue placeholder="Column" /></SelectTrigger><SelectContent>{columns.map(c => <SelectItem key={c} value={c}>{c}</SelectItem>)}</SelectContent></Select>
                            <Input className="react-input" placeholder="Value" value={field.val} onChange={(e) => handleFieldChange(index, 'val', e.target.value)} />
                            <Button variant="ghost" size="icon" className="btn" onClick={() => removeField(index)}><X className="h-4 w-4" /></Button>
                        </div>
                    ))}
                    <Button variant="outline" size="sm" className="btn" onClick={addField}><Plus className="h-4 w-4 mr-2" />Add Field</Button>
                </div>
                <div className="node-section border-t pt-2">
                    <Label className="node-section-label">WHERE</Label>
                     {data.conditions.map((cond: any, index: number) => (
                        <div key={index} className="node-row">
                            <Select value={cond.col} onValueChange={(val) => handleConditionChange(index, 'col', val)}><SelectTrigger className="react-select-trigger"><SelectValue placeholder="Column" /></SelectTrigger><SelectContent>{columns.map(c => <SelectItem key={c} value={c}>{c}</SelectItem>)}</SelectContent></Select>
                            <Select value={cond.op} onValueChange={(val) => handleConditionChange(index, 'op', val)}><SelectTrigger className="w-20 react-select-trigger"><SelectValue /></SelectTrigger><SelectContent><SelectItem value="=">=</SelectItem><SelectItem value="!=">!=</SelectItem></SelectContent></Select>
                            <Input className="react-input" placeholder="Value" value={cond.val} onChange={(e) => handleConditionChange(index, 'val', e.target.value)} />
                            <Button variant="ghost" size="icon" className="btn" onClick={() => removeCondition(index)}><X className="h-4 w-4" /></Button>
                        </div>
                    ))}
                    <Button variant="outline" size="sm" className="btn" onClick={addCondition}><Plus className="h-4 w-4 mr-2" />Add Condition</Button>
                </div>
            </>
        )}
      </div>

      <Handle type="source" position={Position.Right} />
    </div>
  );
}

export default UpdateNode; 