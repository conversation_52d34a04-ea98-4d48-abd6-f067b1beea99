"use client"

import { useRunForm } from "../run-context"
import {
  Card,
  CardContent
} from "@/components/ui/card"
import { WebEnvironmentSettings } from "./web-environment-settings"
import { AndroidEnvironmentSettings } from "./android-environment-settings"
import { PlatformType } from "../run-context"

export function EnvironmentSettings() {
  const { formData } = useRunForm()
  const platform = formData.platform as PlatformType || 'web'

  return (
    <div className="h-full">
      <Card className="h-full">
        <CardContent className="space-y-6 p-5 h-full overflow-y-auto">
          {/* Render appropriate settings based on platform */}
          {platform === 'android' ? (
            <AndroidEnvironmentSettings />
          ) : (
            <WebEnvironmentSettings />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
