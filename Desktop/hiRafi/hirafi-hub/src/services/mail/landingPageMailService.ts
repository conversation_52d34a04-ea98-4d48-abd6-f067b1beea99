/**
 * Landing Page Mail Service
 *
 * Service for sending emails related to landing page form submissions
 */

import { logger } from '../../utils/logger.js';
import { getSystemSettingByType } from '../mongo/systemSettingsService.js';
import { SystemSettingType } from '../../models/system-settings.js';
import nodemailer from 'nodemailer';

/**
 * Send landing page contact form email notifications
 * This function is designed to be called asynchronously (fire and forget)
 * @param data Form data and settings
 */
export async function sendLandingPageEmail(data: {
  name: string;
  email: string;
  company: string;
  message: string;
  phone: string;
  preferredDate: string;
  landingPageSetting: any;
}): Promise<boolean> {
  try {
    const { name, email, company, message, phone, preferredDate, landingPageSetting } = data;

    // Get SMTP settings
    const smtpSettingResult = await getSystemSettingByType(SystemSettingType.SMTP);

    logger.info(`[LANDING PAGE MAIL] SMTP settings result: ${JSON.stringify(smtpSettingResult)}`);

    if (!smtpSettingResult.success || !smtpSettingResult.setting) {
      logger.error('[LANDING PAGE MAIL] SMTP settings not found');
      return false;
    }

    const smtpConfig = smtpSettingResult.setting.config;

    logger.info(`[LANDING PAGE MAIL] Using SMTP config: ${JSON.stringify({
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      user: smtpConfig.auth?.user ? '****' : 'not set',
      from: smtpConfig.from,
      fromName: smtpConfig.fromName
    })}`);

    // Log landing page settings
    logger.info(`[LANDING PAGE MAIL] Using landing page config: ${JSON.stringify({
      adminEmails: landingPageSetting.config.adminEmails,
      emailSubject: landingPageSetting.config.emailSubject
    })}`);


    // Create transporter
    const transporter = nodemailer.createTransport({
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      auth: {
        user: smtpConfig.auth.user,
        pass: smtpConfig.auth.pass
      },
      // AWS SES için TLS seçenekleri
      tls: {
        // TLS sorunlarını gidermek için
        rejectUnauthorized: false,
        minVersion: 'TLSv1.2'
      },
      debug: true // Hata ayıklama için
    });

    logger.info(`Creating SMTP transporter with: host=${smtpConfig.host}, port=${smtpConfig.port}, secure=${smtpConfig.secure}`);


    // Parse admin emails
    const adminEmails = landingPageSetting.config.adminEmails
      .split(',')
      .map((email: string) => email.trim())
      .filter((email: string) => email);

    if (adminEmails.length === 0) {
      logger.error('[LANDING PAGE MAIL] No admin emails configured');
      return false;
    }

    // Prepare email content
    const emailSubject = landingPageSetting.config.emailSubject || 'New Contact Form Submission';

    // Replace placeholders in email template
    let emailTemplate = landingPageSetting.config.emailTemplate || '';
    emailTemplate = emailTemplate
      .replace(/{{name}}/g, name)
      .replace(/{{email}}/g, email)
      .replace(/{{company}}/g, company)
      .replace(/{{phone}}/g, phone)
      .replace(/{{message}}/g, message)
      .replace(/{{preferredDate}}/g, preferredDate);

    try {
      // Verify SMTP connection
      try {
        logger.info('[LANDING PAGE MAIL] Verifying SMTP connection...');
        await transporter.verify();
        logger.info('[LANDING PAGE MAIL] SMTP connection verified successfully');
      } catch (verifyError: any) {
        logger.error(`[LANDING PAGE MAIL] SMTP connection verification failed: ${verifyError.message}`);
        throw new Error(`SMTP connection verification failed: ${verifyError.message}`);
      }

      // Send email to admin recipients
      logger.info(`[LANDING PAGE MAIL] Sending notification email to admins: ${adminEmails.join(', ')}`);

      try {
        // Tüm e-posta gönderme işlemlerini paralel olarak yapacağız
        const emailPromises = [];

        // Admin e-postası için promise oluştur
        logger.info(`[LANDING PAGE MAIL] Preparing notification email to admins: ${adminEmails.join(', ')}`);

        const adminEmailPromise = transporter.sendMail({
          from: `"${smtpConfig.fromName}" <${smtpConfig.from}>`,
          to: adminEmails.join(', '),
          subject: emailSubject,
          html: emailTemplate
        }).then(() => {
          logger.info(`[LANDING PAGE MAIL] Notification email sent to ${adminEmails.join(', ')}`);
        }).catch((err) => {
          logger.error(`[LANDING PAGE MAIL] Failed to send email to admins: ${err.message}`);
          // Hata fırlatma, diğer e-postaların gönderilmesini engellemeyecek
          return Promise.resolve(); // Promise zincirini bozmamak için resolved promise döndür
        });

        emailPromises.push(adminEmailPromise);

        // Kullanıcıya onay e-postası için promise oluştur
        logger.info(`[LANDING PAGE MAIL] Preparing confirmation email to: ${email}`);

        const userEmailPromise = transporter.sendMail({
          from: `"${smtpConfig.fromName}" <${smtpConfig.from}>`,
          to: email,
          subject: 'Your Meeting Request Has Been Received',
          html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Meeting Request Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(to right, #4f46e5, #7c3aed); padding: 20px; border-radius: 8px 8px 0 0; }
            .header h1 { color: white; margin: 0; font-size: 24px; }
            .content { background: #fff; padding: 20px; border-radius: 0 0 8px 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
            .footer { margin-top: 20px; font-size: 12px; color: #6b7280; text-align: center; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Meeting Request Confirmation</h1>
            </div>
            <div class="content">
              <p>Dear ${name},</p>
              <p>Thank you for your interest in HiRafi AI. We have received your meeting request and our team will get back to you shortly.</p>
              <p>Here's a summary of the information you provided:</p>
              <ul>
                <li><strong>Company:</strong> ${company}</li>
                <li><strong>Phone:</strong> ${phone || 'Not provided'}</li>
                ${preferredDate ? `<li><strong>Preferred Date:</strong> ${preferredDate}</li>` : ''}
                <li><strong>Message:</strong> ${message}</li>
              </ul>
              <p>If you have any questions in the meantime, please don't hesitate to contact us.</p>
              <div class="footer">
                This is an automated confirmation from HiRafi AI.
              </div>
            </div>
          </div>
        </body>
        </html>
      `
    }).then(() => {
          logger.info(`[LANDING PAGE MAIL] Confirmation email sent to ${email}`);
        }).catch((err) => {
          logger.error(`[LANDING PAGE MAIL] Failed to send confirmation email to ${email}: ${err.message}`);
          return Promise.resolve(); // Promise zincirini bozmamak için resolved promise döndür
        });

        emailPromises.push(userEmailPromise);

        // Tüm e-posta gönderme işlemlerini paralel olarak bekle
        await Promise.all(emailPromises);
        logger.info(`[LANDING PAGE MAIL] All emails have been processed`);

        return true;
      } catch (mailError: any) {
        logger.error(`[LANDING PAGE MAIL] Error in email sending process: ${mailError.message}`);
        logger.error(`[LANDING PAGE MAIL] Error details: ${JSON.stringify(mailError)}`);
        throw mailError;
      }
    } catch (error: any) {
      logger.error(`[LANDING PAGE MAIL] Error in mail service: ${error.message}`);
      logger.error(`[LANDING PAGE MAIL] Error stack: ${error.stack}`);
      throw error;
    }
  } catch (error: any) {
    logger.error(`[LANDING PAGE MAIL] Outer error: ${error.message}`);
    logger.error(`[LANDING PAGE MAIL] Outer error stack: ${error.stack}`);
    throw error;
  }
}
