/**
 * Registration Service
 * 
 * Handles node registration logic for all connector types with dependency injection.
 * Replaces the singleton RegistrationService with a proper service implementation.
 */

import { BaseService } from '../base/base-service.js';
import { IRegistrationService, IRegistrationConfig, IRegistrationData, IRegistrationStats } from '../../interfaces/registration-service.interface.js';
import { IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';

/**
 * Registration Service Class
 * Provides common registration functionality with dependency injection
 */
export class RegistrationService extends BaseService implements IRegistrationService {
  private registrationStats: Map<string, any> = new Map();
  private registrationConfig: IRegistrationConfig = {};

  constructor(logger: any, config: IServiceConfig = {}) {
    super('RegistrationService', logger, config);
    this.registrationConfig = {
      enableLogging: false,
      maxRetries: 3,
      retryDelay: 5000,
      timeout: 30000,
      validateResponse: true,
      ...config
    };
  }

  /**
   * Initialize the registration service
   * @param config Service configuration
   */
  protected async doInitialize(config: IServiceConfig): Promise<void> {
    this.registrationConfig = { ...this.registrationConfig, ...config };
    this.logger.debug('RegistrationService: Initialized with config', this.registrationConfig);
  }

  /**
   * Close the registration service
   */
  protected async doClose(): Promise<void> {
    this.registrationStats.clear();
    this.logger.debug('RegistrationService: Closed successfully');
  }

  /**
   * Log registration attempt
   * @param name Node name
   * @param capabilities Node capabilities
   * @param connectorType Connector type
   */
  logRegistrationAttempt(
    name: string,
    capabilities: string[],
    connectorType: string
  ): void {
    const timestamp = new Date().toISOString();
    
    // Update connector stats
    if (!this.registrationStats.has(connectorType)) {
      this.registrationStats.set(connectorType, {
        attempts: 0,
        successes: 0,
        failures: 0
      });
    }
    
    const stats = this.registrationStats.get(connectorType);
    stats.attempts++;
    stats.lastAttempt = timestamp;

    this.logOperation('registrationAttempt', {
      name,
      capabilities,
      connectorType,
      timestamp
    });

    this.logger.info(`RegistrationService: Registration attempt for ${name} via ${connectorType} with capabilities: [${capabilities.join(', ')}]`);
  }

  /**
   * Prepare registration data
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Client ID
   * @param additionalData Additional registration data
   */
  prepareRegistrationData(
    name: string,
    capabilities: string[],
    clientId?: string,
    additionalData: Record<string, any> = {}
  ): IRegistrationData {
    const registrationData: IRegistrationData = {
      name,
      capabilities,
      timestamp: new Date().toISOString(),
      ...additionalData
    };

    if (clientId) {
      registrationData.clientId = clientId;
    }

    this.logOperation('prepareRegistrationData', {
      name,
      capabilities,
      clientId,
      hasAdditionalData: Object.keys(additionalData).length > 0
    });

    return registrationData;
  }

  /**
   * Create registration headers
   * @param secretKey Node secret key
   * @param additionalHeaders Additional headers
   */
  createRegistrationHeaders(
    secretKey: string,
    additionalHeaders: Record<string, string> = {}
  ): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'AIDrive-Node',
      ...additionalHeaders
    };

    if (secretKey) {
      headers['X-Node-Key'] = secretKey;
    }

    this.logOperation('createRegistrationHeaders', {
      hasSecretKey: !!secretKey,
      headerCount: Object.keys(headers).length
    });

    return headers;
  }

  /**
   * Validate registration response
   * @param response Response data
   * @param validator Validation function
   */
  validateRegistrationResponse(
    response: any,
    validator: (data: any) => boolean
  ): boolean {
    if (!this.registrationConfig.validateResponse) {
      return true;
    }

    try {
      const isValid = validator(response);
      
      this.logOperation('validateRegistrationResponse', {
        isValid,
        hasResponse: !!response
      });

      return isValid;
    } catch (error) {
      this.handleError(error, 'validateRegistrationResponse');
      return false;
    }
  }

  /**
   * Determine node ID from response
   * @param existingNodeId Existing node ID
   * @param responseNodeId Node ID from response
   */
  determineNodeId(
    existingNodeId: string | undefined,
    responseNodeId: string
  ): string | null {
    // Priority: response node ID > existing node ID
    const nodeId = responseNodeId || existingNodeId || null;

    this.logOperation('determineNodeId', {
      existingNodeId,
      responseNodeId,
      determinedNodeId: nodeId
    });

    if (nodeId) {
      this.logger.info(`RegistrationService: Determined node ID: ${nodeId}`);
    } else {
      this.logger.warn('RegistrationService: Could not determine valid node ID');
    }

    return nodeId;
  }

  /**
   * Handle registration success
   * @param nodeId Assigned node ID
   */
  handleRegistrationSuccess(nodeId: string): void {
    this.logOperation('registrationSuccess', { nodeId });
    this.logger.info(`RegistrationService: Registration successful - Node ID: ${nodeId}`);
  }

  /**
   * Handle registration failure
   * @param error Error that occurred
   * @param connectorType Connector type
   */
  handleRegistrationFailure(error: any, connectorType: string): void {
    // Update connector stats
    if (this.registrationStats.has(connectorType)) {
      const stats = this.registrationStats.get(connectorType);
      stats.failures++;
    }

    this.handleError(error, `registration via ${connectorType}`);
    this.logger.error(`RegistrationService: Registration failed via ${connectorType}: ${error}`);
  }

  /**
   * Get registration statistics
   */
  getStats(): IServiceStats {
    return super.getStats();
  }

  /**
   * Get registration-specific statistics
   */
  getRegistrationStats(): IRegistrationStats {
    const baseStats = super.getStats();
    
    let totalAttempts = 0;
    let successfulRegistrations = 0;
    let failedRegistrations = 0;
    const connectorStats: Record<string, any> = {};

    for (const [connectorType, stats] of this.registrationStats) {
      totalAttempts += stats.attempts;
      successfulRegistrations += stats.successes;
      failedRegistrations += stats.failures;
      
      connectorStats[connectorType] = {
        attempts: stats.attempts,
        successes: stats.successes,
        failures: stats.failures,
        lastAttempt: stats.lastAttempt
      };
    }

    const averageResponseTime = totalAttempts > 0 
      ? Math.round((successfulRegistrations / totalAttempts) * 100) / 100 
      : 0;

    return {
      ...baseStats,
      totalAttempts,
      successfulRegistrations,
      failedRegistrations,
      averageResponseTime,
      connectorStats
    };
  }
}
