/**
 * Common AI Assertion Handler
 * Handles executing AI assertion steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Assertion Handler
 * Handles executing AI assertion steps using the AI agent
 */
export class CommonAiAssertionHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiAssertion';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiAssertionHandler';
  }

  /**
   * Execute an AI assertion step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiAssertion, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const assertionText = step.prompt || step.value || step.name || step.description;

    if (!assertionText) {
      return { success: false, error: 'Missing assertion instruction for aiAssertion step. Please provide a prompt field.' };
    }

    try {
      // Note: aiAssert does not support deepThink parameter according to Midscene.js API

      // Log the assertion for debugging
      logger.debug(`CommonAiAssertionHandler: Verifying assertion "${assertionText}"`);

      // Use the AI agent to verify the assertion directly
      // Try both method names for compatibility
      const methodName = typeof agent.aiAssertion === 'function' ? 'aiAssertion' : 'aiAssert';
      const result = await agent[methodName](assertionText || '', `Assertion failed: ${assertionText}`);
      logger.debug(`${methodName} result: ${JSON.stringify(result)}`);

      // Check if the assertion passed
      // result.result can be a boolean or an object with a pass property
      const isPassed = typeof result.result === 'object' ?
                      result.result?.pass === true :
                      result.result === true;

      // Format the result to standard log format
      const logEntry = formatAIResultForLog('aiAssertion', assertionText, result);

      // Update assertion-specific fields
      logEntry.success = isPassed;
      logEntry.metadata.pass = isPassed;

      // If the assertion failed, make sure we capture the thought process
      if (!isPassed && typeof result.result === 'object' && result.result?.thought) {
        logEntry.metadata.thought = result.result.thought;
      }

      // Add the formatted log entry to logs
      logs.push(JSON.stringify(logEntry));

      return {
        success: isPassed,
        error: isPassed ? null : `Assertion failed: ${assertionText}`,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      // Extract error message and reason
      const errorMessage = error.message || error.toString();
      const reasonMessage = error.Reason || '';

      // Log only the error message, not the full error object to avoid stack traces
      logger.info('AI Assertion Error:', errorMessage);

      // Simplify error messages
      const simplifiedError = simplifyErrorMessage(errorMessage);
      const simplifiedReason = simplifyErrorMessage(reasonMessage);

      // Create an error log entry with both error and reason
      const errorLogEntry = {
        operation: 'aiAssertion',
        description: assertionText,
        success: false,
        error: simplifiedError,
        reason: simplifiedReason,
        metadata: {
          status: 'failed'
        }
      };

      // Add the formatted error log entry to logs
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiAssertionHandler: Assertion failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
}
