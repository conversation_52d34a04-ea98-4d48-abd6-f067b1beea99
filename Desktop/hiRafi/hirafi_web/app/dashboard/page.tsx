"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import {
  BarChart2,
  ArrowRight,
  Activity,
  CheckCircle2,
  XCircle,
  Clock,
  Calendar,
  LineChart,
  TrendingUp,
  AlertTriangle,
} from "lucide-react"
import { Sidebar } from "@/components/sidebar/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { motion } from "framer-motion"
import { ProtectedRoute } from "@/components/protected-route"
import { useDashboard } from "@/hooks/useDashboard"

function Dashboard() {
  const router = useRouter()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [timeframe, setTimeframe] = useState<'day' | 'week' | 'month'>('week')

  // Dashboard verilerini ve son çalıştırılan testleri çekelim
  const { stats, weeklyActivity, hourlyActivity, recentRuns, isLoading } = useDashboard(timeframe)

  // API'den gelen haftanın günlerini UI için formatlayalım
  const weeklyDataFormatted = weeklyActivity.map(day => {
    // Gün adını alalım (Mon, Tue, Wed, vb.)
    const date = new Date(day.date)
    const dayName = date.toLocaleDateString('en-US', { weekday: 'short' }).slice(0, 3)

    return {
      day: dayName,
      passed: day.passed || 0,
      failed: day.failed || 0
    }
  })

  // API'den gelen saatlik aktivite verilerini UI için formatlayalım
  const formatHour = (dateStr: string) => {
    try {
      // dateStr, 'YYYY-MM-DD HH:00' formatında olmalı
      const parts = dateStr.split(' ');
      if (parts.length === 2) {
        return parts[1]; // Sadece saat kısmını döndür
      }
      // Eğer 'YYYY-MM-DD HH:00' formatında değilse, tüm string'i döndür
      return dateStr;
    } catch (e) {
      return dateStr;
    }
  };

  // Saatlik aktivite verilerini formatlayalım
  const hourlyDataFormatted = hourlyActivity.map(hour => {
    return {
      hour: formatHour(hour.date),
      count: hour.passed + hour.failed
    }
  });

  // Veri dizisini her zaman en az 6 elemanlı olacak şekilde hazırlayalım
  const ensureDataPoints = () => {
    const data = [...hourlyDataFormatted];

    // Hiç veri yoksa, 6 saatlik boş veri oluşturalım
    if (data.length === 0) {
      for (let i = 0; i < 6; i++) {
        data.push({
          hour: `${i * 4}:00`,
          count: 0
        });
      }
      return data;
    }

    // 6'dan az veri varsa, kalan kısmı doldur
    if (data.length < 6) {
      const lastIndex = data.length - 1;
      const lastHour = parseInt(data[lastIndex]?.hour?.split(':')[0]) || 0;

      for (let i = 1; i <= 6 - data.length; i++) {
        const nextHour = (lastHour + (i * 4)) % 24;
        data.push({
          hour: `${nextHour}:00`,
          count: 0
        });
      }
    }

    return data;
  };

  // Grafik için her zaman 6 elemanlı bir dizi oluştur
  const chartData = ensureDataPoints();

  const getStatusIcon = (status: string, result: string) => {
    if (status === "running") {
      return <Clock className="h-5 w-5 text-blue-500 animate-pulse" />
    } else if (status === "scheduled") {
      return <Calendar className="h-5 w-5 text-purple-500" />
    } else if (status === "partial") {
      return <AlertTriangle className="h-5 w-5 text-amber-500" />
    } else if (result === "passed" || status === "completed") {
      return <CheckCircle2 className="h-5 w-5 text-emerald-500" />
    } else if (result === "failed" || status === "failed") {
      return <XCircle className="h-5 w-5 text-rose-500" />
    } else {
      return <Activity className="h-5 w-5 text-amber-500" />
    }
  }

  const getStatusBadge = (status: string, result: string) => {
    if (status === "running") {
      return (
        <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 border-0">Running</Badge>
      )
    } else if (status === "scheduled") {
      return (
        <Badge className="bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300 border-0">
          Scheduled
        </Badge>
      )
    } else if (status === "partial") {
      return (
        <Badge className="bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 border-0">
          Partial
        </Badge>
      )
    } else if (result === "passed" || status === "completed") {
      return (
        <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 border-0">
          Passed
        </Badge>
      )
    } else if (result === "failed" || status === "failed") {
      return <Badge className="bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300 border-0">Failed</Badge>
    } else {
      return (
        <Badge className="bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 border-0">Warning</Badge>
      )
    }
  }

  const handleViewReport = (reportId: string) => {
    router.push(`/reports/${reportId}`)
  }

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
              <p className="text-gray-500 dark:text-gray-400">Test execution overview and analytics</p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                className="gap-1 bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 border-none"
                onClick={() => router.push("/scenarios")}
              >
                Go to Scenarios
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Total Steps</CardTitle>
                    <Activity className="h-4 w-4 text-gray-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{isLoading ? '...' : stats.steps.total}</div>
                    <p className="text-xs text-gray-500 mt-1">All test steps run</p>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Passed Steps</CardTitle>
                    <CheckCircle2 className="h-4 w-4 text-emerald-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-emerald-600 dark:text-emerald-500">
                      {isLoading ? '...' : stats.steps.passed}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Successfully executed steps</p>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Failed Steps</CardTitle>
                    <XCircle className="h-4 w-4 text-rose-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-rose-600 dark:text-rose-500">
                      {isLoading ? '...' : stats.steps.failed}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Steps with errors or failures</p>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.4 }}
              >
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                    <BarChart2 className="h-4 w-4 text-blue-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-blue-600 dark:text-blue-500">
                      {isLoading ? '...' : `${stats.steps.successRate}%`}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Overall pass percentage</p>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Charts Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Weekly Chart - Futuristic Redesign */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.5 }}
              >
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle>Weekly Step Results</CardTitle>
                    <BarChart2 className="h-4 w-4 text-gray-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="h-[240px] relative mt-6">
                      <div className="absolute inset-0 flex items-end justify-between px-2">
                        {isLoading ? (
                          <div className="w-full flex items-center justify-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
                          </div>
                        ) : (
                          weeklyDataFormatted.map((day, i) => {
                            const totalSteps = day.passed + day.failed;
                            if (totalSteps === 0) return (
                              <div key={day.day} className="flex flex-col items-center w-[12%]">
                                <div className="w-full flex flex-col items-center">
                                  <motion.div
                                    className="w-full rounded-b-sm bg-gray-200 dark:bg-gray-800"
                                    initial={{ height: 0 }}
                                    animate={{ height: 10 }}
                                    transition={{ duration: 0.5, delay: 0.6 + i * 0.1 }}
                                  />
                                </div>
                                <div className="absolute bottom-0 text-xs text-gray-500 font-medium mt-2 transform translate-y-6">
                                  {day.day}
                                </div>
                                <div className="absolute top-0 text-xs font-medium text-gray-700 dark:text-gray-300 transform -translate-y-6">
                                  0
                                </div>
                              </div>
                            );

                            const passedHeight = (day.passed / totalSteps) * 160;
                            const failedHeight = (day.failed / totalSteps) * 160;

                            return (
                              <div key={day.day} className="flex flex-col items-center w-[12%] group relative">
                                <div className="w-full flex flex-col items-center">
                                  <motion.div
                                    className="w-full rounded-t-sm bg-rose-500"
                                    initial={{ height: 0 }}
                                    animate={{ height: failedHeight }}
                                    transition={{ duration: 0.5, delay: 0.6 + i * 0.1 }}
                                  />
                                  <motion.div
                                    className="w-full rounded-b-sm bg-emerald-500"
                                    initial={{ height: 0 }}
                                    animate={{ height: passedHeight }}
                                    transition={{ duration: 0.5, delay: 0.6 + i * 0.1 }}
                                  />
                                </div>
                                <div className="absolute bottom-0 text-xs text-gray-500 font-medium mt-2 transform translate-y-6">
                                  {day.day}
                                </div>
                                <div className="absolute top-0 text-xs font-medium text-gray-700 dark:text-gray-300 transform -translate-y-6">
                                  {totalSteps}
                                </div>

                                {/* Tooltip on hover */}
                                <div className="absolute z-10 invisible group-hover:visible bg-white dark:bg-gray-800 p-2 rounded shadow-lg text-xs -translate-y-[120%] border border-gray-200 dark:border-gray-700">
                                  <div className="font-medium text-gray-900 dark:text-white mb-1">{day.day}</div>
                                  <div className="flex items-center gap-1 text-emerald-500">
                                    <CheckCircle2 className="h-3 w-3" /> Passed: {day.passed}
                                  </div>
                                  <div className="flex items-center gap-1 text-rose-500">
                                    <XCircle className="h-3 w-3" /> Failed: {day.failed}
                                  </div>
                                  <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400 mt-1">
                                    <Activity className="h-3 w-3" /> Total: {totalSteps}
                                  </div>
                                </div>
                              </div>
                            )
                          })
                        )}
                      </div>
                    </div>

                    <div className="flex justify-center mt-10 gap-6">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
                        <span className="text-sm">Passed Steps</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-rose-500"></div>
                        <span className="text-sm">Failed Steps</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Test Activity Chart */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.6 }}
              >
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle>Test Activity (24h)</CardTitle>
                    <LineChart className="h-4 w-4 text-gray-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="h-[240px] relative mt-6">
                      {isLoading ? (
                        <div className="w-full h-full flex items-center justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
                        </div>
                      ) : (
                        /* Activity Line */
                        <svg className="w-full h-full" viewBox="0 0 600 240" preserveAspectRatio="none">
                          {/* Grid lines */}
                          <line x1="0" y1="200" x2="600" y2="200" stroke="#e5e7eb" strokeWidth="1" />
                          <line x1="0" y1="160" x2="600" y2="160" stroke="#e5e7eb" strokeWidth="1" />
                          <line x1="0" y1="120" x2="600" y2="120" stroke="#e5e7eb" strokeWidth="1" />
                          <line x1="0" y1="80" x2="600" y2="80" stroke="#e5e7eb" strokeWidth="1" />
                          <line x1="0" y1="40" x2="600" y2="40" stroke="#e5e7eb" strokeWidth="1" />

                          {/* Activity path */}
                          <motion.path
                            d={`M 0 ${200 - (chartData[0]?.count || 0) * 5}
                                L ${600 / 5} ${200 - (chartData[1]?.count || 0) * 5}
                                L ${(600 / 5) * 2} ${200 - (chartData[2]?.count || 0) * 5}
                                L ${(600 / 5) * 3} ${200 - (chartData[3]?.count || 0) * 5}
                                L ${(600 / 5) * 4} ${200 - (chartData[4]?.count || 0) * 5}
                                L 600 ${200 - (chartData[5]?.count || 0) * 5}`}
                            fill="none"
                            stroke="#8b5cf6"
                            strokeWidth="3"
                            initial={{ pathLength: 0 }}
                            animate={{ pathLength: 1 }}
                            transition={{ duration: 1.5, delay: 0.7 }}
                          />

                          {/* Area under the line */}
                          <motion.path
                            d={`M 0 ${200 - (chartData[0]?.count || 0) * 5}
                                L ${600 / 5} ${200 - (chartData[1]?.count || 0) * 5}
                                L ${(600 / 5) * 2} ${200 - (chartData[2]?.count || 0) * 5}
                                L ${(600 / 5) * 3} ${200 - (chartData[3]?.count || 0) * 5}
                                L ${(600 / 5) * 4} ${200 - (chartData[4]?.count || 0) * 5}
                                L 600 ${200 - (chartData[5]?.count || 0) * 5}
                                L 600 200 L 0 200 Z`}
                            fill="url(#activity-gradient)"
                            opacity="0.3"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 0.3 }}
                            transition={{ duration: 1, delay: 1 }}
                          />

                          {/* Data points with tooltips */}
                          {chartData.map((point, index) => (
                            <g key={index} className="group">
                              <motion.circle
                                cx={index * (600 / 5)}
                                cy={200 - (point?.count || 0) * 5}
                                r="5"
                                fill="#8b5cf6"
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                              />

                              {/* Tooltip for data point */}
                              <foreignObject
                                x={index * (600 / 5) - 50}
                                y={200 - (point?.count || 0) * 5 - 70}
                                width="100"
                                height="60"
                                className="invisible group-hover:visible"
                              >
                                <div className="bg-white dark:bg-gray-800 p-2 rounded shadow-lg text-xs border border-gray-200 dark:border-gray-700">
                                  <div className="font-medium text-gray-900 dark:text-white mb-1">{point.hour}</div>
                                  <div className="flex items-center gap-1 text-purple-500">
                                    <Activity className="h-3 w-3" /> Tests: {point.count}
                                  </div>
                                </div>
                              </foreignObject>
                            </g>
                          ))}

                          {/* Gradient definition */}
                          <defs>
                            <linearGradient id="activity-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                              <stop offset="0%" stopColor="#8b5cf6" />
                              <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0" />
                            </linearGradient>
                          </defs>
                        </svg>
                      )}

                      {/* Y-axis labels */}
                      <div className="absolute top-0 left-0 h-full flex flex-col justify-between text-xs text-gray-500 py-2">
                        <div>40</div>
                        <div>30</div>
                        <div>20</div>
                        <div>10</div>
                        <div>0</div>
                      </div>
                    </div>

                    {/* X-axis labels - Fixed spacing */}
                    <div className="flex justify-between mt-2 px-4">
                      {chartData.map((point, index) => (
                        <div key={index} className="text-xs text-gray-500 font-medium">
                          {point.hour}
                        </div>
                      ))}
                    </div>

                    <div className="flex justify-center mt-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                        <span className="text-sm">Test Executions</span>
                      </div>
                      {!isLoading && chartData.some(data => data.count > 0) && (
                        <div className="flex items-center gap-2 ml-6">
                          <TrendingUp className="h-4 w-4 text-purple-500" />
                          <span className="text-sm font-medium">Test Activity (24h)</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Recent Test Runs - Modern design with visual enhancements */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.7 }}
            >
              <Card className="overflow-hidden border-0 shadow-md">
                <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
                  <div className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-indigo-500" />
                    <CardTitle>Recent Test Runs</CardTitle>
                  </div>
                  <Button
                    onClick={() => router.push("/reports")}
                    className="gap-1 bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 border-none"
                    size="sm"
                  >
                    View All Reports
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent className="p-0">
                  {isLoading ? (
                    // Loading state with improved animation
                    <div className="w-full flex flex-col items-center justify-center py-16">
                      <div className="relative w-16 h-16">
                        <div className="absolute inset-0 rounded-full border-4 border-gray-200 dark:border-gray-800"></div>
                        <div className="absolute inset-0 rounded-full border-4 border-t-indigo-500 animate-spin"></div>
                      </div>
                      <p className="mt-4 text-indigo-600 dark:text-indigo-400 font-medium">Loading recent runs...</p>
                    </div>
                  ) : recentRuns.length === 0 ? (
                    // Empty state with improved visuals
                    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
                      <div className="w-20 h-20 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                        <Activity className="h-10 w-10 text-indigo-500 dark:text-indigo-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Recent Test Runs</h3>
                      <p className="text-gray-500 dark:text-gray-400 max-w-md mb-6">
                        Start running tests to see your recent activity here.
                      </p>
                      <Button
                        onClick={() => router.push("/runs/create")}
                        className="gap-1 bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 border-none"
                      >
                        Create New Run
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    // Modern list of recent runs
                    <div className="divide-y divide-gray-100 dark:divide-gray-800">
                      {recentRuns.map((report, index: number) => (
                        <motion.div
                          key={report.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}
                          className="group"
                        >
                          <div
                            className="p-4 hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-colors cursor-pointer"
                            onClick={() => handleViewReport(report.id)}
                          >
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-3">
                                <div className="relative">
                                  {getStatusIcon(report.status || '', report.result || '')}
                                  {report.status === "running" && (
                                    <div className="absolute inset-0 rounded-full border border-indigo-200 dark:border-indigo-800 animate-ping"></div>
                                  )}
                                </div>
                                <div>
                                  <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                                    {report.name || 'Unnamed Test'}
                                  </h3>
                                  <div className="flex items-center gap-2 mt-1">
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      {report.date || 'N/A'}
                                    </span>
                                    <span className="text-gray-300 dark:text-gray-700">•</span>
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      {typeof report.duration === 'string' ? report.duration : 'N/A'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                {getStatusBadge(report.status || '', report.result || '')}
                                <div className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center text-gray-500 dark:text-gray-400 group-hover:bg-indigo-100 dark:group-hover:bg-indigo-900/30 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                                  <ArrowRight className="h-3.5 w-3.5" />
                                </div>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                              <div className="flex flex-col">
                                <div className="flex items-center gap-1.5">
                                  <div className="w-1.5 h-1.5 rounded-full bg-indigo-500"></div>
                                  <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Scenarios</p>
                                </div>
                                <p className="text-sm font-medium ml-3">{typeof report.totalTests === 'number' ? report.totalTests : 0}</p>
                              </div>
                              <div className="flex flex-col">
                                <div className="flex items-center gap-1.5">
                                  <div className="w-1.5 h-1.5 rounded-full bg-emerald-500"></div>
                                  <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Pass Rate</p>
                                </div>
                                <p className="text-sm font-medium ml-3">{typeof report.passRate === 'number' ? `${report.passRate}%` : '0%'}</p>
                              </div>
                              <div className="flex flex-col">
                                <div className="flex items-center gap-1.5">
                                  <div className="w-1.5 h-1.5 rounded-full bg-amber-500"></div>
                                  <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Environment</p>
                                </div>
                                <p className="text-sm font-medium ml-3">{report.environment || 'N/A'}</p>
                              </div>
                              <div className="flex flex-col">
                                <div className="flex items-center gap-1.5">
                                  <div className="w-1.5 h-1.5 rounded-full bg-purple-500"></div>
                                  <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">Triggered By</p>
                                </div>
                                <p className="text-sm font-medium ml-3">{report.triggeredBy || 'System'}</p>
                              </div>
                            </div>

                            <div className="mt-2">
                              <div className="flex items-center justify-between mb-1.5">
                                <div className="flex items-center gap-2">
                                  <span className="text-xs font-medium">Scenario Success</span>
                                  <span className={`text-xs font-medium ${
                                    report.status === "failed" ? "text-rose-600 dark:text-rose-400" :
                                    report.status === "partial" ? "text-amber-600 dark:text-amber-400" :
                                    "text-emerald-600 dark:text-emerald-400"
                                  }`}>
                                    {typeof report.passRate === 'number' ? `${report.passRate}%` : '0%'}
                                  </span>
                                </div>
                                <span className="text-xs text-gray-500">
                                  {typeof report.passed === 'number' ? report.passed : 0}/{typeof report.totalTests === 'number' ? report.totalTests : 0}
                                </span>
                              </div>
                              <div className="h-2 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                                <div
                                  className={`h-full rounded-full transition-all duration-500 ease-out ${
                                    report.status === "failed" ? "bg-rose-500" :
                                    report.status === "partial" ? "bg-amber-500" :
                                    "bg-emerald-500"
                                  }`}
                                  style={{
                                    width: `${typeof report.passRate === 'number' ? report.passRate : 0}%`,
                                    opacity: (report.passRate || 0) > 0 ? 1 : 0.3
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  )
}
