import React from 'react';
import { Linkedin, Mail, Phone } from 'lucide-react';
import Image from 'next/image';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <div>
            <div className="mb-6">
              <Image
                src="/images/hirafil_logo.png"
                alt="HiRafi Logo"
                width={180}
                height={70}
                className="h-14 w-auto"
              />
            </div>
            <p className="text-gray-400 mb-6">
              Revolutionizing test automation with AI-powered solutions that make testing smarter, faster, and more reliable.
            </p>
            <div className="flex space-x-4">
              <a href="https://www.linkedin.com/company/testinium" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <a href="#features" className="text-gray-400 hover:text-white transition-colors">Features</a>
              </li>
              <li>
                <a href="#why-ai" className="text-gray-400 hover:text-white transition-colors">Why AI Testing</a>
              </li>
              <li>
                <a href="#testimonials" className="text-gray-400 hover:text-white transition-colors">Testimonials</a>
              </li>
              <li>
                <a href="#contact" className="text-gray-400 hover:text-white transition-colors">Contact Us</a>
              </li>
            </ul>
          </div>



          <div>
            <h4 className="text-lg font-semibold mb-6">Contact Us</h4>
            <ul className="space-y-3">
              <li className="flex items-start">
                <Mail size={20} className="text-gray-400 mr-3 flex-shrink-0 mt-1" />
                <span className="text-gray-400"><EMAIL></span>
              </li>
              <li className="flex items-start">
                <Phone size={20} className="text-gray-400 mr-3 flex-shrink-0 mt-1" />
                <span className="text-gray-400">+90 216 504 56 55</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="pt-8 border-t border-gray-800 text-center text-gray-500 text-sm">
          <p>© {new Date().getFullYear()} HiRafi. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;