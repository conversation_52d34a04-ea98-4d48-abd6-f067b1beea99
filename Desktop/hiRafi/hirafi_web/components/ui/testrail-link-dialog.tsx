import React from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface TestRailLinkDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  caseId: string | null
  onConfirm: () => void
}

export function TestRailLinkDialog({
  open,
  onOpenChange,
  caseId,
  onConfirm,
}: TestRailLinkDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Link with TestRail</AlertDialogTitle>
          <AlertDialogDescription>
            Would you like to link this scenario with TestRail case C{caseId}? This will allow you to sync test results with TestRail.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>No, thanks</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm}>
            Link with TestRail
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
