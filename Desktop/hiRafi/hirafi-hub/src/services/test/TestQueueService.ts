/**
 * Test Queue Service
 * Manages the test queue for the pull-based model
 */

import { EventEmitter } from 'events';
import { Queue, Job, JobsOptions } from 'bullmq';
import { logger } from '../../utils/logger.js';
import { queueService, QUEUE_NAMES, JOB_TYPES } from '../redis/queueService.js';
import { TestProcess, TestRequest, AndroidEnvironmentSettings, TestStatus } from '../../models/test-types.js';
import { config } from '../../config/index.js';
import { redisConnection } from '../redis/redisConnection.js';

export interface TestQueueServiceEvents {
  'test:added': (testId: string, data: any) => void;
  'test:claimed': (testId: string, nodeId: string) => void;
  'test:error': (testId: string, error: Error) => void;
  'test:recovered': (testId: string, recoveryMethod: string) => void;
  'test:recovery:failed': (testId: string, error: Error) => void;
}

/**
 * Service for managing the test queue
 * Implements the pull-based model for test distribution
 */
class TestQueueService extends EventEmitter {
  private static instance: TestQueueService;
  private isInitialized: boolean = false;

  private constructor() {
    super();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): TestQueueService {
    if (!TestQueueService.instance) {
      TestQueueService.instance = new TestQueueService();
    }
    return TestQueueService.instance;
  }

  /**
   * Initialize the test queue service
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Ensure Redis is enabled
      if (!config.connections?.redis?.enabled) {
        logger.error('TestQueueService: Redis is not enabled, cannot initialize test queue service');
        return false;
      }

      // Set up event listeners for the queue service
      queueService.on('job:completed', (jobId, result, queue) => {
        if (queue === QUEUE_NAMES.TEST_QUEUE) {
          logger.debug(`TestQueueService: Job ${jobId} completed in test queue`);
        }
      });

      queueService.on('job:failed', (jobId, error, queue) => {
        if (queue === QUEUE_NAMES.TEST_QUEUE) {
          logger.error(`TestQueueService: Job ${jobId} failed in test queue: ${error}`);
        }
      });

      // Listen for test recovery events - now emitted by TestQueueService itself

      // Listen for the new event from QueueService for tests that failed claim validation
      queueService.on('test:claim:failed_validation', (job, testId, failedReason) => {
        logger.warn(`TestQueueService: Received 'test:claim:failed_validation' for test ${testId} (job ${job.id}). Reason: ${failedReason}. Scheduling recovery.`);
        // Schedule recovery for this test after a delay
        setTimeout(async () => {
          await this.recoverFailedClaimTest(job, testId, failedReason);
        }, 5000); // 5 second delay before recovery
      });

      this.isInitialized = true;
      logger.info('TestQueueService: Initialized successfully');
      return true;
    } catch (error) {
      logger.error(`TestQueueService: Initialization error: ${error}`);
      return false;
    }
  }

  /**
   * Add a test to the queue
   * @param test Test to add
   * @param priority Priority of the test (higher number = higher priority)
   * @returns Promise resolving to the job ID if successful, null otherwise
   */
  public async addTest(test: TestRequest, priority: number = 10): Promise<string | null> {
    try {
      if (!this.isInitialized) {
        logger.error('TestQueueService: Not initialized, cannot add test');
        return null;
      }

      // Log SauceLabs credentials before adding to queue (if present)
      if (test.platform === 'android' && test.environmentSettings) {
        // Type guard to check if environmentSettings is AndroidEnvironmentSettings
        const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
          return settings && settings.platform === 'android';
        };

        if (isAndroidSettings(test.environmentSettings)) {
          const androidSettings = test.environmentSettings;
          logger.info(`TestQueueService: SauceLabs credentials before queue for test ${test.id}: ${
            androidSettings.sauceLabs ?
            `username: ${androidSettings.sauceLabs.username ? 'PRESENT' : 'MISSING'},
             accessKey: ${androidSettings.sauceLabs.accessKey ? 'PRESENT' : 'MISSING'},
             region: ${androidSettings.sauceLabs.region || 'N/A'}` :
            'MISSING'
          }`);
        } else {
          logger.warn(`TestQueueService: Test ${test.id} has platform 'android' but environmentSettings is not AndroidEnvironmentSettings`);
        }
      }

      // Add job to the main test queue with priority and retry settings
      let job;

      // Define job options, including SauceLabs specifics
      const isSauceLabsAndroidTest = test.platform === 'android'; // Simpler check, assuming any android test might be SauceLabs
      const jobOptions: JobsOptions = {
        jobId: test.id,
        attempts: isSauceLabsAndroidTest ? 30 : 5,  // 30 attempts for SauceLabs (10 min total)
        backoff: {
          type: 'fixed',
          delay: isSauceLabsAndroidTest ? 20000 : 1000  // 20s delay for SauceLabs (30 * 20s = 10 min)
        },
        removeOnComplete: 50,
        removeOnFail: 25, // Keep fewer failed jobs
        priority: priority // Use priority for Redis job
      };

      try {
        job = await queueService.addJob(
          QUEUE_NAMES.TEST_QUEUE,
          JOB_TYPES.TEST_REQUEST,
          test,
          jobOptions
        );
      } catch (error: any) {
        logger.error(`TestQueueService: Failed to add test ${test.id} to queue: ${error.message}`);
        this.emit('test:error', test.id, error as Error);
        return null;
      }

      if (job) {
        logger.info(`TestQueueService: Added test ${test.id} to queue with priority ${priority} (job ID: ${job.id})`);

        // Verify the job data has SauceLabs credentials (if applicable)
        if (test.platform === 'android') {
          const jobData = job.data as TestRequest;

          if (jobData.environmentSettings) {
            // Type guard to check if environmentSettings is AndroidEnvironmentSettings
            const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
              return settings && settings.platform === 'android';
            };

            if (isAndroidSettings(jobData.environmentSettings)) {
              const androidSettings = jobData.environmentSettings;
              logger.info(`TestQueueService: SauceLabs credentials in job data for test ${test.id}: ${
                androidSettings.sauceLabs ?
                `username: ${androidSettings.sauceLabs.username ? 'PRESENT' : 'MISSING'},
                 accessKey: ${androidSettings.sauceLabs.accessKey ? 'PRESENT' : 'MISSING'},
                 region: ${androidSettings.sauceLabs.region || 'N/A'}` :
                'MISSING'
              }`);
            } else {
              logger.warn(`TestQueueService: Job data for test ${test.id} has platform 'android' but environmentSettings is not AndroidEnvironmentSettings`);
            }
          } else {
            logger.warn(`TestQueueService: Job data for test ${test.id} has no environmentSettings`);
          }
        }

        this.emit('test:added', test.id, test);
        return job.id || null;
      } else {
        logger.error(`TestQueueService: Failed to add test ${test.id} to queue`);
        return null;
      }
    } catch (error) {
      logger.error(`TestQueueService: Error adding test ${test.id} to queue: ${error}`);
      this.emit('test:error', test.id, error as Error);
      return null;
    }
  }

  /**
   * Get the queue status
   * @returns Promise resolving to the queue status
   */
  public async getQueueStatus(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    try {
      if (!this.isInitialized) {
        logger.error('TestQueueService: Not initialized, cannot get queue status');
        return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
      }

      const queueStatus = await queueService.getQueueStatus(QUEUE_NAMES.TEST_QUEUE);

      // Convert the object to the expected format
      if (queueStatus && typeof queueStatus === 'object') {
        const mainQueueStatus = queueStatus[QUEUE_NAMES.TEST_QUEUE] || { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
        return mainQueueStatus;
      }

      return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
    } catch (error) {
      logger.error(`TestQueueService: Error getting queue status: ${error}`);
      return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
    }
  }

  /**
   * Get all tests in the queue
   * @returns Promise resolving to an array of tests
   */
  public async getAllTests(): Promise<TestRequest[]> {
    try {
      if (!this.isInitialized) {
        logger.error('TestQueueService: Not initialized, cannot get all tests');
        return [];
      }

      // Use the public method to get the queue
      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);

      if (!queue) {
        logger.error(`TestQueueService: Could not get queue ${QUEUE_NAMES.TEST_QUEUE}`);
        return [];
      }

      // Get all jobs with a higher limit (BullMQ default is 10, we set it to 1000)
      // This ensures we get all jobs in the queue, not just the first 10
      const waitingJobs = await queue.getWaiting(0, 1000);
      const activeJobs = await queue.getActive(0, 1000);
      const delayedJobs = await queue.getDelayed(0, 1000);

      const allJobs = [...waitingJobs, ...activeJobs, ...delayedJobs];

      logger.debug(`TestQueueService: Retrieved ${allJobs.length} total jobs (waiting: ${waitingJobs.length}, active: ${activeJobs.length}, delayed: ${delayedJobs.length})`);

      return allJobs.map(job => job.data as TestRequest);
    } catch (error) {
      logger.error(`TestQueueService: Error getting all tests: ${error}`);
      return [];
    }
  }

  /**
   * Get only waiting tests from the queue (including waiting and prioritized)
   * @returns Promise resolving to an array of waiting test requests
   */
  public async getWaitingTests(): Promise<TestRequest[]> {
    try {
      if (!this.isInitialized) {
        logger.error('TestQueueService: Not initialized, cannot get waiting tests');
        return [];
      }

      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (!queue) {
        logger.error(`TestQueueService: Could not get queue ${QUEUE_NAMES.TEST_QUEUE}`);
        return [];
      }

      // Get waiting and delayed jobs (delayed jobs might include prioritized ones)
      const waitingJobs = await queue.getWaiting(0, 1000);
      const delayedJobs = await queue.getDelayed(0, 1000);

      // Combine both arrays
      const allQueuedJobs = [...waitingJobs, ...delayedJobs];

      logger.debug(`TestQueueService: Retrieved ${waitingJobs.length} waiting jobs and ${delayedJobs.length} delayed jobs (total: ${allQueuedJobs.length})`);

      return allQueuedJobs.map(job => {
        const testData = job.data as TestRequest;

        // Determine job state based on job properties
        let jobState = 'waiting';
        if (job.opts?.priority && job.opts.priority > 0) {
          jobState = 'prioritized';
        } else if (job.opts?.delay && job.opts.delay > 0) {
          jobState = 'delayed';
        } else if (delayedJobs.includes(job)) {
          jobState = 'delayed';
        }

        // Add queue timing information
        return {
          ...testData,
          queuedAt: job.timestamp ? new Date(job.timestamp) : testData.queuedAt,
          queuedTimeSeconds: job.timestamp ? Math.floor((Date.now() - job.timestamp) / 1000) : 0,
          jobState: jobState,
          priority: job.opts?.priority || testData.priority || 0
        };
      });
    } catch (error) {
      logger.error(`TestQueueService: Error getting waiting tests: ${error}`);
      return [];
    }
  }

  /**
   * Get only active tests from the queue
   * @returns Promise resolving to an array of active test requests
   */
  public async getActiveTests(): Promise<TestRequest[]> {
    try {
      if (!this.isInitialized) {
        logger.error('TestQueueService: Not initialized, cannot get active tests');
        return [];
      }

      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (!queue) {
        logger.error(`TestQueueService: Could not get queue ${QUEUE_NAMES.TEST_QUEUE}`);
        return [];
      }

      const activeJobs = await queue.getActive(0, 1000);
      logger.debug(`TestQueueService: Retrieved ${activeJobs.length} active jobs`);

      return activeJobs.map(job => {
        const testData = job.data as TestRequest;
        // Add running timing information
        return {
          ...testData,
          startedAt: job.processedOn ? new Date(job.processedOn) : new Date(),
          runningTimeSeconds: job.processedOn ? Math.floor((Date.now() - job.processedOn) / 1000) : 0
        };
      });
    } catch (error) {
      logger.error(`TestQueueService: Error getting active tests: ${error}`);
      return [];
    }
  }

  /**
   * Get stuck tests (tests that have been active for too long)
   * @returns Promise resolving to an array of stuck test requests
   */
  public async getStuckTests(): Promise<any[]> {
    try {
      if (!this.isInitialized) {
        logger.error('TestQueueService: Not initialized, cannot get stuck tests');
        return [];
      }

      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (!queue) {
        logger.error(`TestQueueService: Could not get queue ${QUEUE_NAMES.TEST_QUEUE}`);
        return [];
      }

      // Get active jobs and check for stuck ones
      const activeJobs = await queue.getActive(0, 1000);

      const now = Date.now();
      const STUCK_THRESHOLD = 5 * 60 * 1000; // 5 minutes for active jobs
      const WAITING_STUCK_THRESHOLD = 20 * 60 * 1000; // 20 minutes for waiting jobs - increased for longer tests

      const stuckTests: TestRequest[] = [];

      // Check active jobs for stuck tests
      for (const job of activeJobs) {
        if (job.processedOn && (now - job.processedOn) > STUCK_THRESHOLD) {
          const testData = job.data as TestRequest;
          stuckTests.push({
            ...testData,
            status: TestStatus.RUNNING,
            startedAt: new Date(job.processedOn),
            stuckTimeSeconds: Math.floor((now - job.processedOn) / 1000),
            nodeId: 'unknown' // We don't have node info in BullMQ job
          });
        }
      }

      // Check waiting jobs for stuck tests (too long in queue)
      const waitingJobsForStuck = await queue.getWaiting(0, 1000);
      const delayedJobsForStuck = await queue.getDelayed(0, 1000);
      const allQueuedJobsForStuck = [...waitingJobsForStuck, ...delayedJobsForStuck];

      for (const job of allQueuedJobsForStuck) {
        if (job.timestamp && (now - job.timestamp) > WAITING_STUCK_THRESHOLD) {
          const testData = job.data as TestRequest;

          // Determine job state
          let jobState = 'waiting';
          if (job.opts?.priority && job.opts.priority > 0) {
            jobState = 'prioritized';
          } else if (delayedJobsForStuck.includes(job)) {
            jobState = 'delayed';
          }

          stuckTests.push({
            ...testData,
            status: TestStatus.QUEUED,
            queuedAt: new Date(job.timestamp),
            stuckTimeSeconds: Math.floor((now - job.timestamp) / 1000),
            jobState: jobState
          } as any);
        }
      }

      logger.debug(`TestQueueService: Found ${stuckTests.length} stuck tests (${activeJobs.length} active, ${waitingJobsForStuck.length} waiting, ${delayedJobsForStuck.length} delayed checked)`);
      return stuckTests;
    } catch (error) {
      logger.error(`TestQueueService: Error getting stuck tests: ${error}`);
      return [];
    }
  }

  /**
   * Remove tests from the queue by runId (including active jobs)
   * @param runId The run ID to remove tests for
   * @returns Promise resolving to the number of tests removed
   */
  public async removeTestsByRunId(runId: string): Promise<number> {
    try {
      if (!this.isInitialized) {
        logger.error('TestQueueService: Not initialized, cannot remove tests');
        return 0;
      }

      // Get the queue
      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (!queue) {
        logger.error(`TestQueueService: Could not get queue ${QUEUE_NAMES.TEST_QUEUE}`);
        return 0;
      }

      // Get all jobs in different states with higher limit to ensure we get all jobs
      const waitingJobs = await queue.getWaiting(0, 1000);
      const delayedJobs = await queue.getDelayed(0, 1000);
      const activeJobs = await queue.getActive(0, 1000);

      // Combine all jobs that need to be checked
      const allJobs = [...waitingJobs, ...delayedJobs, ...activeJobs];

      // Filter jobs by runId
      const jobsToRemove = allJobs.filter(job => {
        const testData = job.data as TestRequest;
        return testData.runId === runId;
      });

      logger.info(`TestQueueService: Found ${jobsToRemove.length} jobs to remove for run ${runId} (${waitingJobs.length} waiting, ${delayedJobs.length} delayed, ${activeJobs.length} active)`);

      // Remove each job from the queue
      let removedCount = 0;
      for (const job of jobsToRemove) {
        try {
          const testData = job.data as TestRequest;
          const jobState = await job.getState();

          // Handle active jobs differently - move to completed first
          if (jobState === 'active') {
            logger.info(`TestQueueService: Moving active job ${job.id} (test ${testData.id}) to completed before removal`);
            try {
              const stoppedResult = {
                success: false,
                message: 'Test stopped due to run stop',
                status: 'stopped',
                testId: testData.id,
                stoppedAt: new Date().toISOString(),
                stoppedReason: 'run_stop'
              };
              await job.moveToCompleted(stoppedResult, job.token || '', false);
              logger.info(`TestQueueService: Successfully moved active job ${job.id} to completed for run stop`);
            } catch (moveError) {
              logger.warn(`TestQueueService: Failed to move active job ${job.id} to completed, attempting direct removal: ${moveError}`);
              await job.remove();
            }
          } else {
            // For waiting/delayed jobs, direct removal is fine
            await job.remove();
          }

          logger.info(`TestQueueService: Removed test ${testData.id} (job ${job.id}, state: ${jobState}) for run ${runId} from Redis queue`);
          removedCount++;
        } catch (error) {
          logger.error(`TestQueueService: Error removing job ${job.id} from queue: ${error}`);
        }
      }

      logger.info(`TestQueueService: Successfully removed ${removedCount} tests for run ${runId} from Redis queue`);
      return removedCount;
    } catch (error) {
      logger.error(`TestQueueService: Error removing tests for run ${runId} from queue: ${error}`);
      return 0;
    }
  }

  /**
   * Attempt to recover a test that failed due to claim validation issues.
   * This might involve re-queueing the test.
   * @param job The original BullMQ job object
   * @param testId The ID of the test that failed to be claimed
   * @param failedReason The reason for the claim failure
   */
  private async recoverFailedClaimTest(job: Job, testId: string, failedReason: string): Promise<void> {
    logger.warn(`TestQueueService: Attempting to recover test ${testId} (Job ID: ${job.id}) which failed claim validation. Reason: ${failedReason}`);

    try {
      // Option 1: Simply log and rely on BullMQ's own retry for the job if configured.
      // The TestQueueWorker on the node side, when it encounters a TestClaimFailed or similar,
      // should throw an error that causes BullMQ to retry the job based on its retry strategy.
      // The worker's own error handling (e.g., in handleProcessingError) should also perform
      // atomicJobCleanup for the specific node if it had partially processed something.

      // Option 2: If we want to explicitly re-queue from the hub, ensure the old job is handled.
      // For now, we'll primarily log, assuming the node's failure to claim will result in BullMQ retrying the job.
      // The critical part is that the HUB should NOT attempt to clean application-level locks
      // managed by the worker (e.g., test:claim:<testId>).

      logger.info(`TestQueueService: Recovery for test ${testId} (failed claim validation) - Relaying on BullMQ's job retry mechanisms for job ${job.id}. The responsible TestQueueWorker should have cleaned its state.`);
      this.emit('test:recovery:failed', testId, new Error(`Claim validation failed, recovery depends on BullMQ retry: ${failedReason}`));



    } catch (error) {
      logger.error(`TestQueueService: Error during recovery attempt for test ${testId} (failed claim validation): ${error}`);
      this.emit('test:recovery:failed', testId, error as Error);
    }
  }

  /**
   * Close the test queue service
   */
  public async close(): Promise<void> {
    if (this.isInitialized) {
      this.isInitialized = false;
      logger.info('TestQueueService: Closed');
    }
  }
}

// Export singleton instance
export const testQueueService = TestQueueService.getInstance();
export default testQueueService;
