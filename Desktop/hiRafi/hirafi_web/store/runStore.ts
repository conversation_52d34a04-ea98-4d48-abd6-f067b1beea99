import { create } from "zustand";
import { storeManager } from "@/lib/utils/store-management";

/**
 * Global run state store using Zustand
 * Manages cross-page state for run operations
 */
interface RunStore {
  // Track which runs are currently being updated (start/stop operations)
  updating: Record<string, boolean>;

  // Set updating state for a specific run
  setUpdating: (runId: string, isUpdating: boolean) => void;

  // Clear updating state for a run
  clearUpdating: (runId: string) => void;

  // Clear all updating states
  clearAllUpdating: () => void;

  // Track simulated run states for UI consistency
  simulatedStates: Record<string, string>;

  // Set simulated state for a run
  setSimulatedState: (runId: string, state: string) => void;

  // Clear simulated state for a run
  clearSimulatedState: (runId: string) => void;

  // Clear all simulated states
  clearAllSimulatedStates: () => void;

  // Clear persisted state for store manager
  clearPersistedState: () => void;
}

export const useRunStore = create<RunStore>((set, get) => ({
  updating: {},

  setUpdating: (runId: string, isUpdating: boolean) =>
    set((state) => ({
      updating: {
        ...state.updating,
        [runId]: isUpdating,
      },
    })),

  clearUpdating: (runId: string) =>
    set((state) => {
      const { [runId]: _, ...rest } = state.updating;
      return { updating: rest };
    }),

  clearAllUpdating: () => set({ updating: {} }),

  simulatedStates: {},

  setSimulatedState: (runId: string, state: string) =>
    set((prevState) => ({
      simulatedStates: {
        ...prevState.simulatedStates,
        [runId]: state,
      },
    })),

  clearSimulatedState: (runId: string) =>
    set((state) => {
      const { [runId]: _, ...rest } = state.simulatedStates;
      return { simulatedStates: rest };
    }),

  clearAllSimulatedStates: () => set({ simulatedStates: {} }),

  // Clear persisted state for store manager
  clearPersistedState: () => {
    set({ updating: {}, simulatedStates: {} });
  },
}));

// Register this store with the store manager for automatic cleanup
if (typeof window !== "undefined") {
  storeManager.registerStore({
    clearPersistedState: () => useRunStore.getState().clearPersistedState(),
  });
}

/**
 * Hook to get updating state for a specific run
 */
export function useRunUpdating(runId: string): boolean {
  return useRunStore((state) => state.updating[runId] || false);
}

/**
 * Hook to get simulated state for a specific run
 */
export function useRunSimulatedState(runId: string): string | undefined {
  return useRunStore((state) => state.simulatedStates[runId]);
}
