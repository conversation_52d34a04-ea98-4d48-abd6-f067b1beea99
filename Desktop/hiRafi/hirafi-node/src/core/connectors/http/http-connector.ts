/**
 * HTTP Connector
 * 
 * Refactored HTTP connector with dependency injection and single responsibility principle.
 * Replaces the singleton HttpConnector with a proper service implementation.
 */

import { BaseConnector } from '../core/base-connector.js';
import { IHttpConnector } from '../interfaces/http-connector.interface.js';
import { IHttpRegistrationService, IHttpHeartbeatService, IHttpTestService } from '../interfaces/http-connector.interface.js';
import { IConnectorConfig, IConnectorStats } from '../interfaces/base-connector.interface.js';
import {
  TestStatusResponse,
  NextTestResponse
} from '../../../models/types.js';

/**
 * HTTP Connector Class
 * Implements HTTP-based communication with the test hub using dependency injection
 */
export class HttpConnector extends BaseConnector implements IHttpConnector {
  constructor(
    logger: any,
    config: IConnectorConfig,
    private registrationService: IHttpRegistrationService,
    private heartbeatService: IHttpHeartbeatService,
    private testService: IHttpTestService
  ) {
    super('HTTP', logger, config);
  }

  /**
   * Initialize the HTTP connector
   * @param config Configuration object
   */
  protected async doInitialize(config: IConnectorConfig): Promise<void> {
    const hubUrl = this.getHubUrl();
    const secretKey = this.getNodeSecretKey();

    // Initialize services with configuration
    await this.registrationService.initialize(hubUrl, secretKey);
    await this.heartbeatService.initialize(hubUrl, secretKey);
    await this.testService.initialize(hubUrl, secretKey);

    this.logOperation('httpConnectorInitialized', { hubUrl });
  }

  /**
   * Close the HTTP connector
   */
  protected async doClose(): Promise<void> {
    this.heartbeatService.stopHeartbeat();
    
    // Close services
    await this.registrationService.close();
    await this.heartbeatService.close();
    await this.testService.close();

    this.logOperation('httpConnectorClosed');
  }

  /**
   * Check if the connector is connected
   * For HTTP, we consider it connected if we have a node ID
   */
  isConnected(): boolean {
    return this.nodeId !== null && this.isInitialized;
  }

  /**
   * Register node with hub - DEPRECATED: Use WebSocket registration only
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Optional client ID for reconnection
   */
  async registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null> {
    this.logger.warn('HTTP: registerNode is deprecated - registration should only happen via WebSocket');
    this.logger.warn('HTTP: This prevents duplicate registrations and maintains single truth source');

    // Return null to indicate HTTP registration is not supported
    return null;
  }

  /**
   * Send heartbeat to hub
   */
  async sendHeartbeat(): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('HTTP: Cannot send heartbeat: No node ID');
      return false;
    }

    try {
      const success = await this.heartbeatService.sendHeartbeat(this.nodeId);
      
      if (success) {
        this.logOperation('httpHeartbeatSent', { nodeId: this.nodeId });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'sendHeartbeat');
      return false;
    }
  }

  /**
   * Start heartbeat interval
   */
  startHeartbeat(): void {
    if (!this.nodeId) {
      this.logger.warn('HTTP: Cannot start heartbeat: No node ID');
      return;
    }

    this.heartbeatService.startHeartbeat(this.nodeId);
    this.logOperation('httpHeartbeatStarted', { nodeId: this.nodeId });
  }

  /**
   * Stop heartbeat interval
   */
  stopHeartbeat(): void {
    this.heartbeatService.stopHeartbeat();
    this.logOperation('httpHeartbeatStopped');
  }

  /**
   * Get test by ID
   * @param testId Test ID
   */
  async getTest(testId: string): Promise<TestStatusResponse | null> {
    try {
      const result = await this.testService.getTest(testId);
      
      if (result) {
        this.logOperation('httpGetTestSuccess', { testId });
      }

      return result;
    } catch (error) {
      this.handleError(error, 'getTest');
      return null;
    }
  }

  /**
   * Update test status
   * @param testId Test ID
   * @param status New status
   * @param result Test result (optional)
   */
  async updateTestStatus(testId: string, status: string, result?: any): Promise<boolean> {
    if (!this.nodeId) {
      this.logger.warn('HTTP: Cannot update test status: No node ID');
      return false;
    }

    try {
      const success = await this.testService.updateTestStatus(testId, status, result, this.nodeId);
      
      if (success) {
        this.logOperation('httpTestStatusUpdated', { testId, status });
      }

      return success;
    } catch (error) {
      this.handleError(error, 'updateTestStatus');
      return false;
    }
  }

  /**
   * Get next test from hub
   * @deprecated This polling mechanism is deprecated. Nodes should rely on their BullMQ worker to get tests.
   */
  async getNextTest(): Promise<NextTestResponse | null> {
    this.logger.warn('HttpConnector: getNextTest() is deprecated. Nodes should acquire tests via BullMQ worker. Returning null.');
    return null; // Deprecated
  }

  /**
   * Generic POST method for the connector.
   * @param url The URL to POST to.
   * @param data The data to send in the POST body.
   * @returns Promise resolving to the response data.
   */
  public async post<T>(url: string, data: any): Promise<T> {
    if (!this.isConnected()) {
      this.logger.warn(`HTTP: Not connected, cannot make POST request to ${url}`);
      throw new Error('HTTP connector not connected');
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      const secretKey = this.getNodeSecretKey();
      if (secretKey) {
        headers['X-Node-Key'] = secretKey; // Standard header for node authentication
      }
      
      this.logOperation('httpPostRequest', { url, hasData: !!data });

      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`HTTP POST to ${url} failed: ${response.status} ${response.statusText} - ${errorText}`);
        throw new Error(`HTTP POST failed: ${response.status} ${errorText}`);
      }

      // Check if response has content before trying to parse JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        const result = await response.json();
        this.logOperation('httpPostSuccess', { url, hasResult: !!result });
        return result as T;
      }

      // If no content or not JSON, resolve with the response object itself
      this.logOperation('httpPostSuccess', { url, noJsonContent: true });
      return response as unknown as T;

    } catch (error: any) {
      this.handleError(error, `POST request to ${url}`);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  /**
   * Get connector statistics
   */
  getStats(): IConnectorStats {
    return super.getStats();
  }

  /**
   * Get detailed statistics including service stats
   */
  getDetailedStats(): Record<string, any> {
    const baseStats = super.getStats();
    return {
      ...baseStats,
      services: {
        registration: this.registrationService.getStats(),
        heartbeat: this.heartbeatService.getStats(),
        test: this.testService.getStats()
      }
    };
  }
}
