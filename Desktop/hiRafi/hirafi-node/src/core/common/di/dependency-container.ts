/**
 * Dependency Container
 *
 * Consolidates the duplicate DI container implementations from connectors and test-execution modules
 * into a single, reusable, feature-complete dependency injection container.
 * 
 * PHASE 3 ENHANCEMENT: Added comprehensive disposal tracking for scope management
 */

import { EventEmitter } from 'events';

/**
 * Service lifecycle enumeration
 */
export enum ServiceLifecycle {
  SINGLETON = 'singleton',
  TRANSIENT = 'transient',
  SCOPED = 'scoped'
}

/**
 * Service registration interface
 */
export interface ServiceRegistration<T = any> {
  name: string;
  factory: (...args: any[]) => Promise<T> | T;
  dependencies: string[];
  lifecycle: ServiceLifecycle;
  instance?: T;
  initialized: boolean;
  scope?: string;
}

/**
 * Dependency validation result interface
 */
export interface DependencyValidationResult {
  valid: boolean;
  issues: DependencyValidationIssue[];
  registrationOrder?: string[];
}

/**
 * Dependency validation issue interface
 */
export interface DependencyValidationIssue {
  type: 'missing_dependency' | 'circular_dependency' | 'registration_order' | 'invalid_dependency';
  serviceName: string;
  dependencyName?: string;
  message: string;
  suggestion?: string;
  circularPath?: string[];
}

/**
 * Container configuration interface
 */
export interface ContainerConfig {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  enableDependencyValidation?: boolean;
  enableRegistrationOrderValidation?: boolean;
  maxResolutionDepth?: number;
  enableStatistics?: boolean;
  enableDisposalTracking?: boolean;  // PHASE 3: New disposal tracking option
}

/**
 * Container statistics interface
 */
export interface ContainerStatistics {
  serviceCount: number;
  singletonCount: number;
  transientCount: number;
  scopedCount: number;
  initialized: number;
  resolutionCount: number;
  averageResolutionTime: number;
  circularDependencyDetections: number;
}

/**
 * PHASE 3: Enhanced disposal tracking metadata
 */
interface DisposalTracker {
  scopeId: string;
  serviceName: string;
  created: Date;
  disposed?: Date;
  resourceType: string;
  hasDispose: boolean;
  disposeAttempted?: boolean;
  disposeSuccessful?: boolean;
  disposeError?: string;
}

/**
 * PHASE 3: Scope cleanup statistics
 */
interface ScopeCleanupStats {
  scopeId: string;
  created: Date;
  disposed?: Date;
  totalResources: number;
  disposedResources: number;
  failedDisposals: number;
  cleanupDuration?: number;
  resourceTypes: string[];
}

/**
 * Dependency Container
 * 
 * A comprehensive dependency injection container that consolidates features from
 * multiple module-specific implementations into a single, reusable component.
 */
export class DependencyContainer extends EventEmitter {
  private parent?: DependencyContainer;
  private services = new Map<string, ServiceRegistration>();
  private instances = new Map<string, any>();
  private singletonPromises = new Map<string, Promise<any>>();
  private scopedInstances = new Map<string, Map<string, any>>();
  private resolutionStack: string[] = [];
  private config: ContainerConfig;
  private statistics: ContainerStatistics;
  private dependencyGraph = new Map<string, Set<string>>();
  private registrationOrder: string[] = [];
  
  // PHASE 3: Enhanced disposal tracking
  private disposalTrackers: Map<string, DisposalTracker[]> = new Map(); // scope -> trackers
  private scopeCleanupStats: Map<string, ScopeCleanupStats> = new Map();
  private activeScopes: Set<string> = new Set();
  private disposalInProgress: Set<string> = new Set();

  constructor(config: ContainerConfig = {}, parent?: DependencyContainer) {
    super();
    this.parent = parent;
    this.config = {
      enableLogging: config.enableLogging ?? false,
      enableCircularDependencyDetection: config.enableCircularDependencyDetection ?? true,
      enableDependencyValidation: config.enableDependencyValidation ?? true,
      enableRegistrationOrderValidation: config.enableRegistrationOrderValidation ?? true,
      maxResolutionDepth: config.maxResolutionDepth ?? 15,
      enableStatistics: config.enableStatistics ?? true,
      enableDisposalTracking: config.enableDisposalTracking ?? false
    };

    this.statistics = {
      serviceCount: 0,
      singletonCount: 0,
      transientCount: 0,
      scopedCount: 0,
      initialized: 0,
      resolutionCount: 0,
      averageResolutionTime: 0,
      circularDependencyDetections: 0
    };

    if (this.config.enableLogging) {
      console.debug('[DependencyContainer] Initialized with config', this.config);
    }
  }

  /**
   * Register a service with the container (generic method for backward compatibility)
   */
  register<T>(
    name: string,
    factory: (...args: any[]) => Promise<T> | T,
    lifecycle: ServiceLifecycle = ServiceLifecycle.SINGLETON,
    dependencies: string[] = []
  ): void {
    this.registerService(name, factory, dependencies, lifecycle);
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    name: string,
    factory: (...args: any[]) => Promise<T> | T,
    dependencies: string[] = []
  ): void {
    this.registerService(name, factory, dependencies, ServiceLifecycle.SINGLETON);
  }

  /**
   * Register a transient service
   */
  registerTransient<T>(
    name: string,
    factory: (...args: any[]) => Promise<T> | T,
    dependencies: string[] = []
  ): void {
    this.registerService(name, factory, dependencies, ServiceLifecycle.TRANSIENT);
  }

  /**
   * Register a scoped service
   */
  registerScoped<T>(
    name: string,
    factory: (...args: any[]) => Promise<T> | T,
    dependencies: string[] = [],
    scope: string = 'default'
  ): void {
    const registration = this.registerService(name, factory, dependencies, ServiceLifecycle.SCOPED);
    registration.scope = scope;
  }

  /**
   * Register a service with specific lifecycle
   */
  private registerService<T>(
    name: string,
    factory: (...args: any[]) => Promise<T> | T,
    dependencies: string[],
    lifecycle: ServiceLifecycle
  ): ServiceRegistration<T> {
    if (this.services.has(name)) {
      throw new Error(`Service '${name}' is already registered`);
    }

    // Validate dependencies if enabled
    if (this.config.enableDependencyValidation) {
      this.validateServiceDependencies(name, dependencies);
    }

    const registration: ServiceRegistration<T> = {
      name,
      factory,
      dependencies,
      lifecycle,
      initialized: false
    };

    this.services.set(name, registration);
    this.updateDependencyGraph(name, dependencies);
    this.registrationOrder.push(name);
    this.updateStatistics(lifecycle, 'register');

    if (this.config.enableLogging) {
      console.debug(`[DependencyContainer] Registered ${lifecycle} service '${name}' with dependencies: [${dependencies.join(', ')}]`);
    }

    return registration;
  }

  /**
   * Resolve a service by name
   */
  async resolve<T>(name: string, scope?: string): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await this.resolveInternal<T>(name, scope);
      this.updateResolutionStatistics(Date.now() - startTime);
      return result;
    } catch (error: any) {
      if (this.config.enableLogging) {
        console.error(`[DependencyContainer] Failed to resolve service '${name}': ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Internal resolution logic
   */
  private async resolveInternal<T>(name: string, scope?: string): Promise<T> {
    // Check for circular dependencies
    if (this.config.enableCircularDependencyDetection && this.resolutionStack.includes(name)) {
      this.statistics.circularDependencyDetections++;
      throw new Error(`Circular dependency detected: ${this.resolutionStack.join(' -> ')} -> ${name}`);
    }

    // Check resolution depth
    if (this.resolutionStack.length >= this.config.maxResolutionDepth!) {
      throw new Error(`Maximum resolution depth (${this.config.maxResolutionDepth}) exceeded`);
    }

    const registration = this.services.get(name);
    if (!registration) {
      throw new Error(`Service '${name}' is not registered`);
    }

    // Handle different lifecycles
    switch (registration.lifecycle) {
      case ServiceLifecycle.SINGLETON:
        // Singleton services are always resolved from the root container to ensure a single instance.
        if (this.parent) {
          return this.parent.resolve<T>(name);
        }
        return this.resolveSingleton<T>(registration);
      
      case ServiceLifecycle.TRANSIENT:
        return this.resolveTransient<T>(registration);
      
      case ServiceLifecycle.SCOPED:
        // Scoped services should be resolved from the root container to maintain proper scope isolation
        if (this.parent) {
          return this.parent.resolve<T>(name, scope);
        }
        return this.resolveScoped<T>(registration, scope || 'default');
      
      default:
        throw new Error(`Unknown service lifecycle: ${registration.lifecycle}`);
    }
  }

  /**
   * Resolve singleton service
   */
  private async resolveSingleton<T>(registration: ServiceRegistration<T>): Promise<T> {
    if (this.instances.has(registration.name)) {
      return this.instances.get(registration.name);
    }

    if (this.singletonPromises.has(registration.name)) {
      return this.singletonPromises.get(registration.name)!;
    }

    const instancePromise = this.createInstance<T>(registration)
      .then(instance => {
        this.instances.set(registration.name, instance);
        this.singletonPromises.delete(registration.name);

        if (!registration.initialized) {
          registration.initialized = true;
          this.statistics.initialized++;
        }

        return instance;
      })
      .catch(err => {
        // Make sure to clean up promise map on failure too
        this.singletonPromises.delete(registration.name);
        throw err;
      });
    
    this.singletonPromises.set(registration.name, instancePromise);
    return instancePromise;
  }

  /**
   * Resolve transient service
   */
  private async resolveTransient<T>(registration: ServiceRegistration<T>): Promise<T> {
    return this.createInstance<T>(registration);
  }

  /**
   * Resolve scoped service
   */
  private async resolveScoped<T>(registration: ServiceRegistration<T>, scope: string): Promise<T> {
    if (!this.scopedInstances.has(scope)) {
      this.scopedInstances.set(scope, new Map());
    }

    const scopeMap = this.scopedInstances.get(scope)!;
    
    if (scopeMap.has(registration.name)) {
      return scopeMap.get(registration.name);
    }

    const instance = await this.createInstance<T>(registration);
    scopeMap.set(registration.name, instance);

    if (!registration.initialized) {
      registration.initialized = true;
      this.statistics.initialized++;
    }

    return instance;
  }

  /**
   * Create service instance
   */
  private async createInstance<T>(registration: ServiceRegistration<T>): Promise<T> {
    this.resolutionStack.push(registration.name);

    try {
      // Resolve dependencies
      const dependencies = [];
      for (const dep of registration.dependencies) {
        dependencies.push(await this.resolveInternal(dep, registration.scope));
      }

      // Create instance
      const instance = await registration.factory(...dependencies);

      if (this.config.enableLogging) {
        console.debug(`[DependencyContainer] Created instance of '${registration.name}'`);
      }

      return instance;
    } finally {
      this.resolutionStack.pop();
    }
  }

  /**
   * Check if service is registered
   */
  isRegistered(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered service names (for backward compatibility)
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Get container statistics
   */
  getStatistics(): ContainerStatistics {
    return { ...this.statistics };
  }

  /**
   * Clear scope
   */
  clearScope(scope: string): void {
    this.scopedInstances.delete(scope);

    if (this.config.enableLogging) {
      console.debug(`[DependencyContainer] Cleared scope '${scope}'`);
    }
  }

  /**
   * Dispose a specific scope and all its instances
   */
  async disposeScope(scopeId: string): Promise<void> {
    const scopeMap = this.scopedInstances.get(scopeId);
    if (scopeMap) {
      const disposePromises: Promise<void>[] = [];
      
      for (const [serviceName, instance] of scopeMap.entries()) {
        if (instance && typeof instance.dispose === 'function') {
          // Dispose each service independently to prevent one failure from affecting others
          disposePromises.push(
            instance.dispose().catch((error: any) => {
              if (this.config.enableLogging) {
                console.error(`[DependencyContainer] Error disposing service '${serviceName}' in scope '${scopeId}': ${error.message}`);
              }
              // Don't re-throw to prevent cascade failures
            })
          );
        }
      }
      
      // Wait for all disposals to complete
      await Promise.all(disposePromises);
      
      // Clear the scope map
      this.scopedInstances.delete(scopeId);

      if (this.config.enableLogging) {
        console.debug(`[DependencyContainer] Disposed scope '${scopeId}' with ${scopeMap.size} services`);
      }
    }
  }

  /**
   * Dispose container
   */
  async dispose(): Promise<void> {
    // Dispose all instances that have a dispose method
    for (const instance of this.instances.values()) {
      if (instance && typeof instance.dispose === 'function') {
        await instance.dispose();
      }
    }

    // Clear all scoped instances
    for (const [, scopeMap] of this.scopedInstances) {
      for (const instance of scopeMap.values()) {
        if (instance && typeof instance.dispose === 'function') {
          await instance.dispose();
        }
      }
    }

    // CRITICAL FIX: Only clear service registrations if this is a root container (not a child)
    // Child containers share the services map with their parent, so they should never clear it
    if (!this.parent) {
      // This is a root container, safe to clear service registrations
      this.services.clear();
      this.dependencyGraph.clear();
      this.registrationOrder = [];
    }
    // Always clear instance-specific data (these are not shared with parent)
    this.instances.clear();
    this.singletonPromises.clear();
    this.scopedInstances.clear();
    this.resolutionStack = [];

    if (this.config.enableLogging) {
      const containerType = this.parent ? 'child' : 'root';
      console.debug(`[DependencyContainer] ${containerType} container disposed successfully`);
    }
  }

  /**
   * Update statistics
   */
  private updateStatistics(lifecycle: ServiceLifecycle, operation: 'register' | 'resolve'): void {
    if (!this.config.enableStatistics) return;

    if (operation === 'register') {
      this.statistics.serviceCount++;

      switch (lifecycle) {
        case ServiceLifecycle.SINGLETON:
          this.statistics.singletonCount++;
          break;
        case ServiceLifecycle.TRANSIENT:
          this.statistics.transientCount++;
          break;
        case ServiceLifecycle.SCOPED:
          this.statistics.scopedCount++;
          break;
      }
    }
  }

  /**
   * Update resolution statistics
   */
  private updateResolutionStatistics(resolutionTime: number): void {
    if (!this.config.enableStatistics) return;

    this.statistics.resolutionCount++;
    this.statistics.averageResolutionTime =
      (this.statistics.averageResolutionTime * (this.statistics.resolutionCount - 1) + resolutionTime) /
      this.statistics.resolutionCount;
  }

  /**
   * Validate service dependencies during registration
   */
  private validateServiceDependencies(serviceName: string, dependencies: string[]): void {
    const issues: DependencyValidationIssue[] = [];

    // Check for registration order issues if enabled
    if (this.config.enableRegistrationOrderValidation) {
      for (const dependency of dependencies) {
        if (!this.services.has(dependency)) {
          issues.push({
            type: 'registration_order',
            serviceName,
            dependencyName: dependency,
            message: `Service '${serviceName}' depends on '${dependency}' which is not yet registered`,
            suggestion: `Register '${dependency}' before '${serviceName}' or ensure it will be registered before container initialization completes`
          });
        }
      }
    }

    // Check for circular dependencies if enabled
    if (this.config.enableCircularDependencyDetection) {
      // Temporarily add this service to check for cycles
      const tempGraph = new Map(this.dependencyGraph);
      tempGraph.set(serviceName, new Set(dependencies));

      const circularPath = this.findCircularDependencyPath(serviceName, tempGraph);
      if (circularPath.length > 0) {
        issues.push({
          type: 'circular_dependency',
          serviceName,
          message: `Circular dependency detected: ${circularPath.join(' -> ')}`,
          suggestion: 'Consider using event-driven communication or breaking the circular dependency with interfaces',
          circularPath
        });
      }
    }

    // Throw error if critical issues found
    if (issues.length > 0) {
      const criticalIssues = issues.filter(issue =>
        issue.type === 'circular_dependency' ||
        (issue.type === 'registration_order' && this.config.enableRegistrationOrderValidation)
      );

      if (criticalIssues.length > 0) {
        const errorMessage = `Dependency validation failed for service '${serviceName}':\n` +
          criticalIssues.map(issue => `  - ${issue.message}${issue.suggestion ? `\n    Suggestion: ${issue.suggestion}` : ''}`).join('\n');
        throw new Error(errorMessage);
      }
    }
  }

  /**
   * Update dependency graph
   */
  private updateDependencyGraph(serviceName: string, dependencies: string[]): void {
    this.dependencyGraph.set(serviceName, new Set(dependencies));
  }

  /**
   * Find circular dependency path using DFS
   */
  private findCircularDependencyPath(
    startService: string,
    graph: Map<string, Set<string>> = this.dependencyGraph,
    visited: Set<string> = new Set(),
    path: string[] = []
  ): string[] {
    if (path.includes(startService)) {
      // Found cycle - return the circular path
      const cycleStart = path.indexOf(startService);
      return [...path.slice(cycleStart), startService];
    }

    if (visited.has(startService)) {
      return [];
    }

    visited.add(startService);
    path.push(startService);

    const dependencies = graph.get(startService) || new Set();
    for (const dependency of dependencies) {
      const circularPath = this.findCircularDependencyPath(dependency, graph, visited, [...path]);
      if (circularPath.length > 0) {
        return circularPath;
      }
    }

    return [];
  }

  /**
   * Validate entire dependency graph
   */
  validateDependencyGraph(): DependencyValidationResult {
    const issues: DependencyValidationIssue[] = [];

    // Check for missing dependencies
    for (const [serviceName, dependencies] of this.dependencyGraph) {
      for (const dependency of dependencies) {
        if (!this.services.has(dependency)) {
          issues.push({
            type: 'missing_dependency',
            serviceName,
            dependencyName: dependency,
            message: `Service '${serviceName}' depends on unregistered service '${dependency}'`,
            suggestion: `Register service '${dependency}' or remove it from '${serviceName}' dependencies`
          });
        }
      }
    }

    // Check for circular dependencies
    for (const serviceName of this.dependencyGraph.keys()) {
      const circularPath = this.findCircularDependencyPath(serviceName);
      if (circularPath.length > 0) {
        // Only report each cycle once (from the first service in alphabetical order)
        const sortedPath = [...circularPath].sort();
        if (serviceName === sortedPath[0]) {
          issues.push({
            type: 'circular_dependency',
            serviceName,
            message: `Circular dependency detected: ${circularPath.join(' -> ')}`,
            suggestion: 'Consider using event-driven communication or breaking the circular dependency with interfaces',
            circularPath
          });
        }
      }
    }

    return {
      valid: issues.length === 0,
      issues,
      registrationOrder: [...this.registrationOrder]
    };
  }

  /**
   * Get dependency graph for analysis
   */
  getDependencyGraph(): Map<string, string[]> {
    const result = new Map<string, string[]>();
    for (const [service, dependencies] of this.dependencyGraph) {
      result.set(service, Array.from(dependencies));
    }
    return result;
  }

  /**
   * Get registration order
   */
  getRegistrationOrder(): string[] {
    return [...this.registrationOrder];
  }

  /**
   * Creates a new child container.
   * Child containers inherit service registrations but have their own instance lifecycle for scoped and transient services.
   */
  createChild(): DependencyContainer {
    const child = new DependencyContainer(this.config, this);
    // Child container inherits registrations from the parent by sharing the services map reference.
    child.services = this.services;
    child.dependencyGraph = this.dependencyGraph;
    child.registrationOrder = this.registrationOrder;
    return child;
  }

  // PHASE 3: Enhanced disposal tracking methods
  
  /**
   * Track resource creation for disposal monitoring
   */
  private trackResourceCreation(scopeId: string, serviceName: string, instance: any): void {
    if (!this.config.enableDisposalTracking) return;

    const tracker: DisposalTracker = {
      scopeId,
      serviceName,
      created: new Date(),
      resourceType: instance?.constructor?.name || 'Unknown',
      hasDispose: typeof instance?.dispose === 'function'
    };

    if (!this.disposalTrackers.has(scopeId)) {
      this.disposalTrackers.set(scopeId, []);
    }
    
    this.disposalTrackers.get(scopeId)!.push(tracker);
    this.activeScopes.add(scopeId);

    if (this.config.enableLogging) {
      console.debug(`[DisposalTracker] Created ${tracker.resourceType} '${serviceName}' in scope '${scopeId}'`);
    }
  }

  /**
   * Get disposal tracking statistics for a scope
   */
  getScopeCleanupStats(scopeId: string): ScopeCleanupStats | null {
    return this.scopeCleanupStats.get(scopeId) || null;
  }

  /**
   * Get all active scopes
   */
  getActiveScopes(): string[] {
    return Array.from(this.activeScopes);
  }

  /**
   * Enhanced dispose scope with comprehensive tracking
   */
  async disposeScopeWithTracking(scopeId: string): Promise<ScopeCleanupStats> {
    if (this.disposalInProgress.has(scopeId)) {
      throw new Error(`Disposal already in progress for scope '${scopeId}'`);
    }

    this.disposalInProgress.add(scopeId);
    const startTime = Date.now();
    
    const stats: ScopeCleanupStats = {
      scopeId,
      created: new Date(startTime),
      totalResources: 0,
      disposedResources: 0,
      failedDisposals: 0,
      resourceTypes: []
    };

    try {
      const scopeMap = this.scopedInstances.get(scopeId);
      const trackers = this.disposalTrackers.get(scopeId) || [];
      
      stats.totalResources = scopeMap?.size || 0;
      stats.resourceTypes = [...new Set(trackers.map(t => t.resourceType))];

      if (scopeMap) {
        const disposePromises: Promise<void>[] = [];
        
        for (const [serviceName, instance] of scopeMap.entries()) {
          if (instance && typeof instance.dispose === 'function') {
            const tracker = trackers.find(t => t.serviceName === serviceName);
            
            if (tracker) {
              tracker.disposeAttempted = true;
            }

            const disposePromise = instance.dispose()
              .then(() => {
                stats.disposedResources++;
                if (tracker) {
                  tracker.disposeSuccessful = true;
                  tracker.disposed = new Date();
                }
                if (this.config.enableLogging) {
                  console.debug(`[DisposalTracker] Successfully disposed '${serviceName}' in scope '${scopeId}'`);
                }
              })
              .catch((error: any) => {
                stats.failedDisposals++;
                if (tracker) {
                  tracker.disposeSuccessful = false;
                  tracker.disposeError = error.message;
                }
                if (this.config.enableLogging) {
                  console.error(`[DisposalTracker] Failed to dispose '${serviceName}' in scope '${scopeId}': ${error.message}`);
                }
              });
            
            disposePromises.push(disposePromise);
          } else {
            // Instance without dispose method still counts as "disposed"
            stats.disposedResources++;
          }
        }
        
        await Promise.all(disposePromises);
        this.scopedInstances.delete(scopeId);
      }

      stats.disposed = new Date();
      stats.cleanupDuration = Date.now() - startTime;
      
      this.scopeCleanupStats.set(scopeId, stats);
      this.activeScopes.delete(scopeId);
      
      // Emit disposal event for monitoring
      this.emit('scopeDisposed', {
        scopeId,
        stats,
        success: stats.failedDisposals === 0
      });

      if (this.config.enableLogging) {
        console.info(`[DisposalTracker] Scope '${scopeId}' disposed: ${stats.disposedResources}/${stats.totalResources} resources, ${stats.failedDisposals} failures, ${stats.cleanupDuration}ms`);
      }

      return stats;
      
    } finally {
      this.disposalInProgress.delete(scopeId);
    }
  }

  /**
   * Get comprehensive disposal report
   */
  getDisposalReport(): {
    activeScopes: string[];
    totalScopes: number;
    successfulCleanups: number;
    failedCleanups: number;
    averageCleanupTime: number;
    resourceTypeDistribution: Record<string, number>;
  } {
    const allStats = Array.from(this.scopeCleanupStats.values());
    const resourceTypes: Record<string, number> = {};
    
    // Count resource types across all scopes
    for (const stats of allStats) {
      for (const type of stats.resourceTypes) {
        resourceTypes[type] = (resourceTypes[type] || 0) + 1;
      }
    }

    const successfulCleanups = allStats.filter(s => s.failedDisposals === 0).length;
    const averageCleanupTime = allStats.length > 0 
      ? allStats.reduce((sum, s) => sum + (s.cleanupDuration || 0), 0) / allStats.length 
      : 0;

    return {
      activeScopes: Array.from(this.activeScopes),
      totalScopes: allStats.length,
      successfulCleanups,
      failedCleanups: allStats.length - successfulCleanups,
      averageCleanupTime,
      resourceTypeDistribution: resourceTypes
    };
  }
}
