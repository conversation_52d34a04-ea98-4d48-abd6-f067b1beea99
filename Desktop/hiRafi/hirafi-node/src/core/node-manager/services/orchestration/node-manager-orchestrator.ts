/**
 * Node Manager Orchestrator Service
 * 
 * PHASE 5 FIX: Extracted service orchestration logic to reduce over-dependencies
 * Handles coordination between different node manager services
 */

import { EventEmitter } from 'events';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';
import { IConnectionService } from '../../interfaces/connection-service.interface.js';
import { IPeriodicTaskService } from '../../interfaces/periodic-task-service.interface.js';
import { IEventService } from '../../interfaces/event-service.interface.js';
import { ITestQueueWorker } from '../../../test-execution/interfaces/test-queue-worker.interface.js';
import { INodeIdentityService } from '../../interfaces/node-identity-service.interface.js';

/**
 * Node manager orchestration interface
 */
export interface INodeManagerOrchestrator {
  /**
   * Initialize all services
   */
  initialize(): Promise<boolean>;

  /**
   * Start all services
   */
  start(): Promise<boolean>;

  /**
   * Stop all services
   */
  stop(): Promise<void>;

  /**
   * Setup event coordination
   */
  setupEventCoordination(): void;

  /**
   * Check if orchestrator is initialized
   */
  isInitialized(): boolean;
}

/**
 * Node Manager Orchestrator Service
 * 
 * Handles the coordination of various node manager services to reduce
 * the dependency burden on the main NodeManager class.
 */
export class NodeManagerOrchestrator implements INodeManagerOrchestrator {
  private logger: ILoggerService;
  private identityService: INodeIdentityService;
  private connectionService: IConnectionService;
  private periodicTaskService: IPeriodicTaskService;
  private eventService: IEventService;
  private testQueueWorker: ITestQueueWorker;
  private nodeEvents: EventEmitter;
  private initialized: boolean = false;

  constructor(
    identityService: INodeIdentityService,
    connectionService: IConnectionService,
    periodicTaskService: IPeriodicTaskService,
    eventService: IEventService,
    testQueueWorker: ITestQueueWorker,
    nodeEvents: EventEmitter
  ) {
    this.logger = LoggerServiceFactory.createServiceLogger('NodeManagerOrchestrator');
    this.identityService = identityService;
    this.connectionService = connectionService;
    this.periodicTaskService = periodicTaskService;
    this.eventService = eventService;
    this.testQueueWorker = testQueueWorker;
    this.nodeEvents = nodeEvents;

    this.logger.serviceInit('NodeManagerOrchestrator', 'Initialized with service coordination responsibilities');
  }

  /**
   * Initialize all managed services with explicit sequential dependency chain
   * PHASE 5 FIX: Removed event-based timing issues with synchronous approach
   */
  async initialize(): Promise<boolean> {
    try {
      this.logger.info('NodeManagerOrchestrator: Starting sequential service initialization...');

      // Step 1: Initialize identity service FIRST to ensure nodeId is available
      this.logger.info('NodeManagerOrchestrator: [1/4] Initializing NodeIdentityService...');
      await this.identityService.initialize();
      const nodeId = this.identityService.getNodeId();
      this.logger.info(`NodeManagerOrchestrator: ✅ NodeIdentityService initialized with nodeId: ${nodeId}`);

      // Step 2: Health check before connection
      this.logger.info('NodeManagerOrchestrator: [2/4] Performing connection health check...');
      await this.performConnectionHealthCheck();

      // Step 3: Initialize connections (WebSocket + Redis)
      this.logger.info('NodeManagerOrchestrator: [3/4] Initializing connection services...');
      const connectionSuccess = await this.connectionService.initialize();
      if (!connectionSuccess) {
        this.logger.error('NodeManagerOrchestrator: Failed to initialize connections');
        return await this.attemptGracefulDegradation();
      }
      this.logger.info('NodeManagerOrchestrator: ✅ Connection services initialized');

      // Step 4: Initialize dependent services AFTER connection is established
      this.logger.info('NodeManagerOrchestrator: [4/4] Initializing dependent services...');
      await this.initializeDependentServices();
      this.logger.info('NodeManagerOrchestrator: ✅ All dependent services initialized');

      this.initialized = true;
      this.logger.info('NodeManagerOrchestrator: 🎉 All services initialized successfully');
      return true;

    } catch (error: any) {
      this.logger.error(`NodeManagerOrchestrator: Initialization failed: ${error.message}`);
      
      // Enhanced error context
      if (error.message.includes('timeout')) {
        this.logger.error('NodeManagerOrchestrator: Timeout detected - likely hub connectivity issue');
      } else if (error.message.includes('connection')) {
        this.logger.error('NodeManagerOrchestrator: Connection error - check network and hub status');
      }
      
      return false;
    }
  }

  /**
   * Initialize services that depend on successful connection
   * PHASE 5 FIX: Explicit dependency management instead of event-based
   */
  private async initializeDependentServices(): Promise<void> {
    try {
      // Initialize auxiliary services (EventService connectors)
      this.logger.info('NodeManagerOrchestrator: Initializing EventService...');
      await this.eventService.initialize();
      this.logger.info('NodeManagerOrchestrator: ✅ EventService initialized');

      // Initialize TestQueueWorker (depends on Redis queues being ready)
      this.logger.info('NodeManagerOrchestrator: Initializing TestQueueWorker...');
      await this.testQueueWorker.initialize();
      this.setupTestQueueWorkerEvents();
      this.logger.info('NodeManagerOrchestrator: ✅ TestQueueWorker initialized and ready for tests');

      // Setup event coordination
      this.logger.info('NodeManagerOrchestrator: Setting up event coordination...');
      this.eventService.setupAllEvents();
      this.logger.info('NodeManagerOrchestrator: ✅ Event coordination established');

      // Start periodic tasks
      this.logger.info('NodeManagerOrchestrator: Starting periodic tasks...');
      this.periodicTaskService.startAllTasks();
      this.logger.info('NodeManagerOrchestrator: ✅ Periodic tasks started');

    } catch (error: any) {
      this.logger.error(`NodeManagerOrchestrator: Error initializing dependent services: ${error.message}`);
      throw error;
    }
  }

  /**
   * Perform connection health check before initialization
   */
  private async performConnectionHealthCheck(): Promise<void> {
    this.logger.info('NodeManagerOrchestrator: Performing connection health check...');
    
    try {
      // Basic connectivity test could be added here
      // For now, just log the configuration being used
      this.logger.info(`NodeManagerOrchestrator: Target hub URL: ${process.env.HUB_URL || 'default'}`);
      this.logger.info('NodeManagerOrchestrator: WebSocket registration will attempt in 3 seconds...');
      
      // Brief delay to ensure any previous connections are fully closed
      await new Promise(resolve => setTimeout(resolve, 3000));
      
    } catch (error: any) {
      this.logger.warn(`NodeManagerOrchestrator: Health check warning: ${error.message}`);
    }
  }

  /**
   * Attempt graceful degradation when connection initialization fails
   * PHASE 5 FIX: Simplified - direct failure instead of complex background retries
   */
  private async attemptGracefulDegradation(): Promise<boolean> {
    this.logger.error('NodeManagerOrchestrator: Connection initialization failed');
    this.logger.error('NodeManagerOrchestrator: Possible causes:');
    this.logger.error('  1. Hub service is not running or accessible');
    this.logger.error('  2. Network connectivity issues');
    this.logger.error('  3. Configuration issues (hubUrl, credentials)');
    this.logger.error('  4. WebSocket registration timeout');
    this.logger.error('');
    this.logger.error('NodeManagerOrchestrator: Please fix the connection issue and restart the node');
    
    // Return false to indicate initialization failure
    return false;
  }

  /**
   * Start orchestration services
   * PHASE 5 FIX: Simplified - all initialization now happens in initialize() method
   */
  async start(): Promise<boolean> {
    if (!this.initialized) {
      throw new Error('NodeManagerOrchestrator: Must initialize before starting');
    }

    try {
      this.logger.info('NodeManagerOrchestrator: Starting orchestration coordination...');
      
      // All services are already initialized and started in initialize() method
      // This method now just confirms orchestration is ready
      this.logger.info('NodeManagerOrchestrator: ✅ Orchestration ready - all services operational');
      
      return true;
    } catch (error: any) {
      this.logger.error(`NodeManagerOrchestrator: Failed to start orchestration: ${error.message}`);
      return false;
    }
  }

  /**
   * Stop all managed services
   */
  async stop(): Promise<void> {
    this.logger.info('NodeManagerOrchestrator: Stopping all services');

    try {
      // Stop periodic tasks
      this.periodicTaskService.stopAllTasks();

      // Teardown event handlers
      this.eventService.teardownAllEvents();

      // Close connections
      await this.connectionService.close();

      this.initialized = false;
      this.logger.info('NodeManagerOrchestrator: All services stopped successfully');
    } catch (error: any) {
      this.logger.error(`NodeManagerOrchestrator: Error during service stop: ${error.message}`);
      throw error;
    }
  }

  /**
   * Setup event coordination between services
   */
  setupEventCoordination(): void {
    this.logger.info('NodeManagerOrchestrator: Setting up event coordination');

    // Setup WebSocket coordination handlers
    this.setupWebSocketCoordinationHandlers();

    // Setup test lifecycle event coordination
    this.setupTestLifecycleEventCoordination();
  }

  /**
   * Check if orchestrator is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Setup test queue worker events
   */
  private setupTestQueueWorkerEvents(): void {
    this.testQueueWorker.on('testClaimedByWorker', (testId: string, nodeId: string) => {
      this.logger.info(`NodeManagerOrchestrator: Test ${testId} claimed by worker on node ${nodeId}`);
      // Emit to main event system for NodeManagerCore to handle
      this.nodeEvents.emit('test:claimed', testId, nodeId);
    });
  }

  /**
   * Setup WebSocket coordination handlers (migrated from Redis)
   */
  private setupWebSocketCoordinationHandlers(): void {
    // Handle WebSocket node status queries (replaces Redis 'check:running')
    this.eventService.on('websocket:node-status-query', async (data: any) => {
      this.logger.debug('NodeManagerOrchestrator: Handling WebSocket node-status-query');
      
      // Forward to main event system for NodeManagerCore to handle
      // This will trigger the proper status response through the established event chain
      this.nodeEvents.emit('websocket:node-status-query', data);
      
      this.logger.debug('NodeManagerOrchestrator: Forwarded node-status-query to main event system');
    });

    // Handle WebSocket get current test ID queries (replaces Redis 'get:current-test-id')
    this.eventService.on('websocket:get-current-test-id', async (data: any) => {
      this.logger.debug('NodeManagerOrchestrator: Handling WebSocket get-current-test-id query');
      
      // Emit to main event system for NodeManagerCore to handle
      this.nodeEvents.emit('websocket:get-current-test-id', data);
    });

    // Handle test released events (replaces Redis 'test:released')
    this.eventService.on('test-released', (testId: string) => {
      this.logger.info(`NodeManagerOrchestrator: Received test-released event for test ${testId} via WebSocket`);
      
      // Emit to main event system for NodeManagerCore to handle
      this.nodeEvents.emit('test:released', testId);
    });

    // Listen for stop-test events from the EventService
    this.eventService.on('stop-test', (testId: string) => {
      this.logger.info(`NodeManagerOrchestrator: Received internal 'stop-test' event for test ${testId} from EventService`);
      
      // Emit to main event system for NodeManagerCore to handle
      this.nodeEvents.emit('stop-test', testId);
    });
  }

  /**
   * Setup test lifecycle event coordination
   */
  private setupTestLifecycleEventCoordination(): void {
    // Forward test lifecycle events from the main event system to individual services
    this.nodeEvents.on('test:started', (testId: string) => {
      this.logger.debug(`NodeManagerOrchestrator: Forwarding test:started event for ${testId}`);
    });

    this.nodeEvents.on('test:completed', (testId: string, result: any) => {
      this.logger.debug(`NodeManagerOrchestrator: Forwarding test:completed event for ${testId}`);
    });

    this.nodeEvents.on('test:failed', (testId: string, error: Error) => {
      this.logger.debug(`NodeManagerOrchestrator: Forwarding test:failed event for ${testId}`);
    });

    this.nodeEvents.on('test:log', (testId: string, result: any, duration: number) => {
      this.logger.debug(`NodeManagerOrchestrator: Test ${testId} completed in ${duration}ms`);
    });
  }
} 