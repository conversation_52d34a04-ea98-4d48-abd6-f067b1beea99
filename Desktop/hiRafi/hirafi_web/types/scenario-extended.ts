/**
 * Extended Scenario Types
 *
 * This file contains extended types for scenarios with additional metadata
 * that are used throughout the application.
 */

import type { Scenario, FolderType, ScenarioStatus } from './scenario'

/**
 * Extended test type
 */
export type TestType = 'web' | 'mobile' | 'api'

/**
 * Platform type
 */
export type PlatformType = 'web' | 'android' | 'ios' | 'api'

/**
 * Extended scenario status that includes 'completed'
 */
export type ExtendedScenarioStatus = ScenarioStatus | 'completed'

/**
 * Step progress information
 */
export interface StepProgress {
  stepName: string
  stepIndex: number
  totalSteps: number
  timestamp: Date
  additionalData?: any
}

/**
 * Extended scenario with additional metadata
 */
export interface ScenarioWithMeta extends Scenario {
  // UI state
  __updating?: boolean

  // Additional data
  url?: string
  stepProgress?: StepProgress
  currentStepName?: string
  lastRun?: string
  updatedAt?: string
  testType?: TestType
  platform?: PlatformType
  runStatus?: ExtendedScenarioStatus

  // Test Management integrations (TestRail and Zephyr Scale)
  testrailIntegration?: {
    caseIds: string[] | number[]
    sync?: boolean
  }
  zephyrIntegration?: {
    caseIds: string[] | number[]
    sync?: boolean
  }
}

/**
 * Parameters for creating a folder
 */
export interface CreateFolderParams {
  name: string
  description?: string
  color?: string
  teamId?: string
  companyId?: string
}

/**
 * Options for the scenario manager hook
 */
export interface ScenarioManagerOptions {
  scenarioLimit?: number
  folderLimit?: number
  scenarioStatus?: "active" | "inactive" | "archived" | "all"
  autoFetch?: boolean
  useMyScenarios?: boolean
  editMode?: boolean
  searchQuery?: string
  tagFilter?: string | null
  testTypeFilter?: string | null
}

/**
 * Filter parameters for scenarios
 */
export interface ScenarioFilters {
  search?: string
  status?: string[]
  testType?: string[]
  tags?: string[]
  dateRange?: { from?: Date; to?: Date }
  testrailStatus?: 'active' | 'inactive' | null // Keep for backward compatibility, represents test management status
  sortDate?: 'asc' | 'desc' | null
}
