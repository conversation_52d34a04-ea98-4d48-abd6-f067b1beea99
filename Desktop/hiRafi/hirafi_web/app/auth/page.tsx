"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { AuthLayout, AuthToggle } from "@/components/auth/auth-layout"
import { LoginForm } from "@/components/auth/login-form"
import { RegisterForm } from "@/components/auth/register-form"

export default function AuthPage() {
  const router = useRouter()
  const [activeView, setActiveView] = useState<"login" | "register">("login")

  const handleToggleView = () => {
    setActiveView(activeView === "login" ? "register" : "login")
  }

  const handleAuthSuccess = () => {
    router.push("/dashboard")
  }

  return (
    <AuthLayout backgroundImage="/placeholder.svg?height=1080&width=1920">
      {activeView === "login" ? (
        <>
          <LoginForm onSuccess={handleAuthSuccess} />
          <AuthToggle activeView={activeView} onToggle={handleToggleView} />
        </>
      ) : (
        <>
          <RegisterForm onSuccess={handleAuthSuccess} />
          <AuthToggle activeView={activeView} onToggle={handleToggleView} />
        </>
      )}
    </AuthLayout>
  )
}

