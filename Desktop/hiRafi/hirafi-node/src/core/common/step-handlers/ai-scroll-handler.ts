/**
 * Common AI Scroll Handler
 * Handles executing AI scroll steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Scroll Handler
 * Handles executing AI scroll steps using the AI agent
 */
export class CommonAiScrollHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiScroll';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiScrollHandler';
  }

  /**
   * Execute an AI scroll step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiScroll, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const scrollDetails = step.prompt || step.value || step.name || step.description;

    if (!scrollDetails) {
      return { success: false, error: 'Missing prompt, value, name or description for aiScroll step' };
    }

    try {
      // Prepare options with available parameters
      const options: any = {};

      if (step.deepThink) {
        options.deepThink = true;
      }

      logger.debug(`CommonAiScrollHandler: Executing scroll "${scrollDetails}" with options: ${JSON.stringify(options)}`);

      // aiScroll now takes only string and options: aiScroll(scrollDescription, options?)
      const result = await agent.aiScroll(scrollDetails, options);
      logger.debug(`aiScroll result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error;

      const logEntry = formatAIResultForLog('aiScroll', scrollDetails, result);
      logEntry.success = isSuccess;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Scroll failed: ${scrollDetails}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Scroll Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiScroll',
        description: scrollDetails,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiScrollHandler: Scroll failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
}