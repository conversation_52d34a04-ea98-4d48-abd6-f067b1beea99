import { Search, Filter, ArrowUpDown, FolderIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { useEffect } from "react"
import { useScenarioManager } from "@/hooks/useScenarioManager"
import { REPORT_STATUS_OPTIONS } from "@/lib/constants/report-status"
import type { Folder } from "@/hooks/useFolders"

interface ReportFiltersProps {
  searchQuery: string
  setSearchQuery: (query: string) => void
  statusFilter: string
  setStatusFilter: (status: string) => void
  sortBy: string
  setSortBy: (sort: string) => void
  sortOrder: string
  setSortOrder: (order: string) => void
  folderFilter: string
  setFolderFilter: (folderId: string) => void
}

export function ReportFilters({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter,
  sortBy,
  setSortBy,
  sortOrder,
  setSortOrder,
  folderFilter,
  setFolderFilter,
}: ReportFiltersProps) {
  const { folders, foldersLoading: isFoldersLoading } = useScenarioManager({
    autoFetch: true,
  })

  return (
    <div className="flex flex-col gap-4 p-4 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800">
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
          <Input
            placeholder="Search reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Status Filter */}
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            {REPORT_STATUS_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Folder Filter */}
        <Select value={folderFilter} onValueChange={setFolderFilter}>
          <SelectTrigger className="w-[180px]">
            <div className="flex items-center">
              <FolderIcon className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Filter by folder" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Folders</SelectItem>
            {folders.map((folder) => (
              <SelectItem key={folder.id} value={folder.id}>
                <div className="flex items-center space-x-2">
                  <span className={`w-2 h-2 rounded-full bg-${folder.color || 'gray'}-500`}></span>
                  <span>{folder.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Sort Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-[180px]">
              <ArrowUpDown className="mr-2 h-4 w-4" />
              Sort by
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setSortBy("date")}>
              Date
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSortBy("name")}>
              Name
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSortBy("duration")}>
              Duration
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Active Filters Display */}
      <div className="flex flex-wrap gap-2">
        {searchQuery && (
          <Badge variant="secondary" className="gap-1">
            Search: {searchQuery}
            <button
              onClick={() => setSearchQuery("")}
              className="ml-1 hover:text-red-500"
            >
              ×
            </button>
          </Badge>
        )}
        {statusFilter !== "all" && (
          <Badge variant="secondary" className="gap-1">
            Status: {statusFilter}
            <button
              onClick={() => setStatusFilter("all")}
              className="ml-1 hover:text-red-500"
            >
              ×
            </button>
          </Badge>
        )}
        {folderFilter !== "all" && (
          <Badge variant="secondary" className="gap-1">
            Folder: {folders.find(f => f.id === folderFilter)?.name || folderFilter}
            <button
              onClick={() => setFolderFilter("all")}
              className="ml-1 hover:text-red-500"
            >
              ×
            </button>
          </Badge>
        )}
        {sortBy !== "date" && (
          <Badge variant="secondary" className="gap-1">
            Sort: {sortBy} ({sortOrder})
            <button
              onClick={() => {
                setSortBy("date")
                setSortOrder("desc")
              }}
              className="ml-1 hover:text-red-500"
            >
              ×
            </button>
          </Badge>
        )}
      </div>
    </div>
  )
}