"use client"

import { useState, useMemo } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Search, Sparkles, Loader2, CheckSquare, Square } from "lucide-react"
import type { DataEnvironment } from "@/store/testDataSetsStore"

interface EnvironmentSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  environments: DataEnvironment[]
  selectedEnvironments: string[]
  onGenerate: (selectedEnvIds: string[]) => void
  isGenerating: boolean
  title?: string
  description?: string
  buttonText?: string
}

export function EnvironmentSelectionModal({
  open,
  onOpenChange,
  environments,
  selectedEnvironments,
  onGenerate,
  isGenerating,
  title = "Generate Variables with AI",
  description = "Select the environments where you want to generate test data variables. AI will create variable definitions and populate values for the selected environments.",
  buttonText = "Generate Variables",
}: EnvironmentSelectionModalProps) {
  // Ensure selectedEnvironments is always an array
  const safeSelectedEnvironments = Array.isArray(selectedEnvironments)
    ? selectedEnvironments
    : []

  const [searchQuery, setSearchQuery] = useState("")
  const [localSelectedEnvs, setLocalSelectedEnvs] = useState<Set<string>>(
    new Set(safeSelectedEnvironments)
  )

  // Filter active environments and apply search
  const filteredEnvironments = useMemo(() => {
    const activeEnvs = environments.filter(env => env.isActive)
    
    if (!searchQuery.trim()) {
      return activeEnvs
    }

    const searchLower = searchQuery.toLowerCase()
    return activeEnvs.filter(env => 
      env.name.toLowerCase().includes(searchLower) ||
      (env.description && env.description.toLowerCase().includes(searchLower)) ||
      env.type.toLowerCase().includes(searchLower)
    )
  }, [environments, searchQuery])

  const handleToggleEnvironment = (envId: string) => {
    const newSelected = new Set(localSelectedEnvs)
    if (newSelected.has(envId)) {
      newSelected.delete(envId)
    } else {
      newSelected.add(envId)
    }
    setLocalSelectedEnvs(newSelected)
  }

  const handleSelectAll = () => {
    const allFilteredIds = new Set(filteredEnvironments.map(env => env.id))
    setLocalSelectedEnvs(allFilteredIds)
  }

  const handleDeselectAll = () => {
    setLocalSelectedEnvs(new Set())
  }

  const handleGenerate = () => {
    if (localSelectedEnvs.size === 0) {
      return
    }
    onGenerate(Array.from(localSelectedEnvs))
  }

  const handleCancel = () => {
    // Reset to original selection
    setLocalSelectedEnvs(new Set(safeSelectedEnvironments))
    setSearchQuery("")
    onOpenChange(false)
  }

  // Reset local state when modal opens
  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      setLocalSelectedEnvs(new Set(safeSelectedEnvironments))
      setSearchQuery("")
    }
    onOpenChange(newOpen)
  }

  const allFilteredSelected = filteredEnvironments.length > 0 && 
    filteredEnvironments.every(env => localSelectedEnvs.has(env.id))
  const someFilteredSelected = filteredEnvironments.some(env => localSelectedEnvs.has(env.id))

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-indigo-500" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 space-y-4 overflow-hidden">
          {/* Search and bulk actions */}
          <div className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search environments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  disabled={allFilteredSelected || filteredEnvironments.length === 0}
                  className="h-8 text-xs"
                >
                  <CheckSquare className="h-3 w-3 mr-1" />
                  Select All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAll}
                  disabled={!someFilteredSelected}
                  className="h-8 text-xs"
                >
                  <Square className="h-3 w-3 mr-1" />
                  Deselect All
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                {localSelectedEnvs.size} of {filteredEnvironments.length} selected
              </div>
            </div>
          </div>

          {/* Environment list */}
          <div className="flex-1 overflow-y-auto border rounded-lg">
            {filteredEnvironments.length === 0 ? (
              <div className="p-8 text-center text-muted-foreground">
                {searchQuery ? "No environments match your search" : "No active environments found"}
              </div>
            ) : (
              <div className="p-4 space-y-3">
                {filteredEnvironments.map((env) => {
                  const isSelected = localSelectedEnvs.has(env.id)
                  
                  return (
                    <div
                      key={env.id}
                      className={`flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all ${
                        isSelected
                          ? 'border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-900/20'
                          : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                      onClick={() => handleToggleEnvironment(env.id)}
                    >
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={() => handleToggleEnvironment(env.id)}
                          className="data-[state=checked]:bg-indigo-600"
                        />
                        <div
                          className={`w-3 h-3 rounded-full bg-${env.color || 'gray'}-500`}
                          title={`${env.color || 'gray'} environment`}
                        />
                        <div>
                          <Label className="text-sm font-medium cursor-pointer">
                            {env.name}
                          </Label>
                          {env.description && (
                            <p className="text-xs text-muted-foreground mt-0.5">
                              {env.description}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <Badge
                        variant={env.type === 'default' ? 'secondary' : 'outline'}
                        className="text-xs"
                      >
                        {env.type}
                      </Badge>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isGenerating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || localSelectedEnvs.size === 0}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                {buttonText} ({localSelectedEnvs.size})
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
