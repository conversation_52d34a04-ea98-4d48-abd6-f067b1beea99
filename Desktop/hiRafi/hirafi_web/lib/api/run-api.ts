/**
 * Run API
 *
 * Koşumlar ile ilgili API isteklerini yönetir.
 */

import { get, post, put, del } from './fetch-wrapper';
import { RUN_ENDPOINTS, TEST_HUB_ENDPOINTS } from './constants';

/**
 * Tüm koşumları getirir
 *
 * @param params Filtreleme parametreleri
 * @param signal AbortController signal
 */
export async function getRuns(params?: {
  status?: string;
  search?: string;
  platform?: string;
  dateFilter?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
  teamId?: string;
  companyId?: string;
}, signal?: AbortSignal) {
  // Retry mekanizması ekle - AbortError durumunda retry yapma
  try {
    return await get(RUN_ENDPOINTS.GET_ALL, {
      params,
      signal,
      showErrorToast: false // Hata toast'ların<PERSON> g<PERSON>, hook içinde yönetilecek
    });
  } catch (error: any) {
    // AbortError durumunda sessizce çık
    if (error.name === 'AbortError') {
      console.log(`[run-api] getRuns request was aborted`);
      return {
        success: false,
        error: 'Request aborted',
        aborted: true
      };
    }

    // Diğer hataları yeniden fırlat
    throw error;
  }
}

/**
 * Belirli bir koşumu getirir
 *
 * @param id Koşum ID'si
 * @param signal AbortController signal
 */
export async function getRunById(id: string, signal?: AbortSignal) {
  return get(RUN_ENDPOINTS.GET_BY_ID(id), { signal });
}

/**
 * Yeni bir koşum oluşturur
 *
 * @param run Koşum verileri
 */
export async function createRun(run: {
  name: string;
  description?: string;
  scenarios: string[];
  platform?: 'web' | 'android';
  environment?: {
    device?: string;
    browser?: string;
    viewport?: string;
    aiModel?: string;
    aiModelId?: string;
    appium?: {
      deviceName?: string;
      platformVersion?: string;
      appPackage?: string;
      appActivity?: string;
    };
  };
  reportSettings?: {
    takeScreenshots?: boolean;
    recordVideo?: boolean;
    generatePdf?: boolean;
  };
  teamId?: string;
  companyId?: string;
  testDataEnvironmentId?: string;
  testDataEnvironmentName?: string;
}) {
  return post(RUN_ENDPOINTS.CREATE, run, {
    showSuccessToast: true,
    successMessage: 'Koşum başarıyla oluşturuldu'
  });
}

/**
 * Bir koşumu günceller
 *
 * @param id Koşum ID'si
 * @param run Güncellenecek koşum verileri
 */
export async function updateRun(id: string, run: {
  name?: string;
  description?: string;
  scenarios?: string[];
  platform?: 'web' | 'android';
  environment?: {
    device?: string;
    browser?: string;
    viewport?: string;
    aiModel?: string;
    aiModelId?: string;
    appium?: {
      deviceName?: string;
      platformVersion?: string;
      appPackage?: string;
      appActivity?: string;
    };
  };
  reportSettings?: {
    takeScreenshots?: boolean;
    recordVideo?: boolean;
    generatePdf?: boolean;
  };
}) {
  return put(RUN_ENDPOINTS.UPDATE(id), run, {
    showSuccessToast: true,
    successMessage: 'Koşum başarıyla güncellendi'
  });
}

/**
 * Bir koşumu siler
 *
 * @param id Koşum ID'si
 */
export async function deleteRun(id: string) {
  return del(RUN_ENDPOINTS.DELETE(id), {
    showSuccessToast: true,
    successMessage: 'Koşum başarıyla silindi'
  });
}

/**
 * Bir koşumu çalıştırır
 *
 * @param id Koşum ID'si
 */
export async function executeRun(id: string) {
  return post(RUN_ENDPOINTS.EXECUTE(id), {}, {
    showSuccessToast: false, // Component seviyesinde göster
    showErrorToast: false // Hata mesajlarını component seviyesinde göster
  });
}

/**
 * Bir koşumu durdurur
 *
 * @param id Koşum ID'si
 */
export async function stopRun(id: string) {
  return post(RUN_ENDPOINTS.STOP(id), {}, {
    showSuccessToast: false, // Component seviyesinde göster
    showErrorToast: false // Hata mesajlarını component seviyesinde göster
  });
}

/**
 * Bir koşumun durumunu getirir
 *
 * @param id Koşum ID'si
 * @param signal AbortController signal
 */
export async function getRunStatus(id: string, signal?: AbortSignal) {
  try {
    return await get(RUN_ENDPOINTS.STATUS(id), {
      signal,
      showErrorToast: false // Hata toast'larını gösterme, hook içinde yönetilecek
    });
  } catch (error: any) {
    // AbortError durumunda sessizce çık
    if (error.name === 'AbortError') {
      console.log(`[run-api] getRunStatus request was aborted for run ${id}`);
      return {
        success: false,
        error: 'Request aborted',
        aborted: true
      };
    }

    // Diğer hataları yeniden fırlat
    throw error;
  }
}

/**
 * Bir koşumun raporlarını getirir
 *
 * @param id Koşum ID'si
 */
export async function getRunReports(id: string) {
  return get(RUN_ENDPOINTS.REPORTS(id));
}

/**
 * Bir koşumun testlerini getirir
 *
 * @param id Koşum ID'si
 */
export async function getRunTests(id: string) {
  return get(RUN_ENDPOINTS.TESTS(id));
}

/**
 * Bir koşumun detaylarını getirir
 *
 * @param id Koşum ID'si
 * @param signal AbortController signal
 */
export async function getRunDetail(id: string, signal?: AbortSignal) {
  try {
    return await get(RUN_ENDPOINTS.DETAIL(id), {
      signal,
      showErrorToast: false // Hata toast'larını gösterme, hook içinde yönetilecek
    });
  } catch (error: any) {
    // AbortError durumunda sessizce çık
    if (error.name === 'AbortError') {
      console.log(`[run-api] getRunDetail request was aborted for run ${id}`);
      return {
        success: false,
        error: 'Request aborted',
        aborted: true
      };
    }

    // Diğer hataları yeniden fırlat
    throw error;
  }
}

/**
 * Bir koşumun senaryo raporlarını getirir
 *
 * @param id Koşum ID'si
 * @param params Ek parametreler (executionId gibi)
 */
export async function getRunScenarioReports(id: string, params?: any) {
  return get(RUN_ENDPOINTS.SCENARIO_REPORTS(id), { params });
}

/**
 * Belirli bir raporu ID'sine göre getirir
 *
 * @param id Rapor ID'si
 */
export async function getRunReport(id: string) {
  return get(RUN_ENDPOINTS.GET_REPORT(id));
}

/**
 * Kullanıcının aktif (running) durumda olan tüm koşumlarını getirir
 *
 * @param signal AbortController signal
 */
export async function getActiveRuns(signal?: AbortSignal) {
  try {
    return await get(RUN_ENDPOINTS.GET_ACTIVE, {
      signal,
      showErrorToast: false // Hata toast'larını gösterme, hook içinde yönetilecek
    });
  } catch (error: any) {
    // AbortError durumunda sessizce çık
    if (error.name === 'AbortError') {
      console.log(`[run-api] getActiveRuns request was aborted`);
      return {
        success: false,
        error: 'Request aborted',
        aborted: true
      };
    }

    // Diğer hataları yeniden fırlat
    throw error;
  }
}


/**
 * Aktif testleri getirir
 */
export async function getActiveTests() {
  return get(TEST_HUB_ENDPOINTS.ACTIVE_TESTS);
}

/**
 * Belirli bir testin detaylarını getirir
 *
 * @param testId Test ID'si
 */
export async function getTestDetails(testId: string) {
  return get(TEST_HUB_ENDPOINTS.TEST_DETAILS(testId));
}

/**
 * Bir testi başlatır
 *
 * @param scenarioId Senaryo ID'si
 * @param userId Kullanıcı ID'si (opsiyonel)
 * @param platform Test platformu (web veya android)
 * @param environmentSettings Çevre ayarları
 */
export async function startTest(
  scenarioId: string,
  userId?: string,
  platform?: 'web' | 'android',
  environmentSettings?: any
) {
  return post(TEST_HUB_ENDPOINTS.START_TEST, {
    action: 'add-test',
    scenarioId,
    userId,
    platform,
    environmentSettings
  });
}

/**
 * Birden fazla testi toplu olarak başlatır
 *
 * @param scenarioIds Senaryo ID'leri
 * @param userId Kullanıcı ID'si (opsiyonel)
 * @param platform Test platformu (web veya android)
 * @param environmentSettings Çevre ayarları
 */
export async function startBulkTests(
  scenarioIds: string[],
  userId?: string,
  platform?: 'web' | 'android',
  environmentSettings?: any
) {
  return post(TEST_HUB_ENDPOINTS.BULK_TESTS, {
    action: 'bulk-run-tests',
    scenarioIds,
    userId,
    platform,
    environmentSettings
  });
}

/**
 * Bir testi durdurur
 *
 * @param testId Test ID'si
 */
export async function stopTest(testId: string) {
  return post(TEST_HUB_ENDPOINTS.STOP_TEST(testId), {
    action: 'stop-test'
  });
}
