/**
 * Configuration Module
 * Central configuration settings for the application with environment variable support
 */

import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config();

/**
 * Application Configuration
 */
export const config = {
  // Server settings
  server: {
    port: parseInt(process.env.PORT || '5000'),
  },

  // Connection settings
  connections: {
    // WebSocket configuration
    websocket: {
      enabled: process.env.WEBSOCKET_ENABLED ? process.env.WEBSOCKET_ENABLED === 'true' : true,
    },

    // Redis configuration
    redis: {
      enabled: process.env.REDIS_ENABLED ? process.env.REDIS_ENABLED === 'true' : true,
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      username: process.env.REDIS_USERNAME || '',
      password: process.env.REDIS_PASSWORD || '',
      db: parseInt(process.env.REDIS_DB || '0'),
      prefix: process.env.REDIS_PREFIX || 'testhub:',
      // Queue names
      testQueue: process.env.REDIS_TEST_QUEUE || 'test-queue',
      resultQueue: process.env.REDIS_RESULT_QUEUE || 'result-queue',
      // Construct URL from components (for backward compatibility)
      get url() {
        const auth = this.password ? `:${this.password}@` : '';
        return `redis://${auth}${this.host}:${this.port}/${this.db}`;
      }
    },

    // MongoDB configuration
    mongodb: {
      host: process.env.MONGODB_HOST,
      port: parseInt(process.env.MONGODB_PORT!),
      database: process.env.MONGODB_DATABASE,
      username: process.env.MONGODB_USERNAME,
      password: process.env.MONGODB_PASSWORD,
      useSSL: process.env.MONGODB_USE_SSL === 'true',
      authSource: process.env.MONGODB_AUTH_SOURCE || 'admin',
      // Construct URI from components
      get uri() {
        const auth = this.username && this.password ? `${this.username}:${this.password}@` : '';
        const authSource = `authSource=${this.authSource}`;
        const sslOption = this.useSSL ? '&ssl=true' : '';
        return `mongodb://${auth}${this.host}:${this.port}/${this.database}?${authSource}${sslOption}`;
      },
      // Alternative URI as a fallback
      get alternativeUri() {
        const auth = this.username && this.password ? `${this.username}:${this.password}@` : '';
        const authSource = `authSource=${this.authSource}`;
        const sslOption = this.useSSL ? '&ssl=true' : '';
        return `mongodb://${auth}${this.host}:${this.port}/${this.database}?${authSource}${sslOption}`;
      },
    },
  },

  // Authentication settings
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    jwtExpiry: process.env.JWT_EXPIRY || '7d',
    nodeSecretKey: process.env.NODE_SECRET_KEY || 'node-secret-key',
  },

  // JWT settings
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRY || '7d',
    adminSecret: process.env.JWT_SECRET || 'your-secret-key', // Aynı secret kullan
    adminExpiresIn: process.env.JWT_ADMIN_EXPIRY || '24h',
  },

  // Storage paths
  paths: {
    dataDir: process.env.DATA_DIR || path.join(process.cwd(), 'data'),
    stateFile: process.env.STATE_FILE || path.join(process.cwd(), 'data', 'state.json'),
    screenshotsDir: process.env.SCREENSHOTS_DIR || path.join(process.cwd(), 'data', 'screenshots'),
    reportsDir: process.env.REPORTS_DIR || path.join(process.cwd(), 'data', 'reports'),
  },

  // Base URL for the application
  baseUrl: process.env.BASE_URL || process.env.NODE_ENV === 'production'
    ? 'https://hirafi.ai'
    : 'http://localhost:3000',

  // Test settings
  test: {
    timeout: parseInt(process.env.TEST_TIMEOUT || '300000') // Default 5 minutes
  },

  // Node management settings - using centralized timeout config
  node: {
    // Node health check interval (how often to check node health)
    healthCheckInterval: parseInt(process.env.NODE_HEALTH_CHECK_INTERVAL || '15000'), // 15 seconds
    // Node inactive timeout (mark as inactive after this time)
    inactiveTimeout: parseInt(process.env.NODE_INACTIVE_TIMEOUT || '120000'), // 2 minutes
    // Node removal timeout (remove from registry after this time)
    removalTimeout: parseInt(process.env.NODE_REMOVAL_TIMEOUT || '300000'), // 5 minutes (updated)
    // Node busy timeout (timeout for BUSY nodes without heartbeat)
    busyTimeout: parseInt(process.env.NODE_BUSY_TIMEOUT || '180000'), // 3 minutes
    // Active nodes logging interval
    logInterval: parseInt(process.env.NODE_LOG_INTERVAL || '15000') // 15 seconds
  }
};

/**
 * Update a configuration value at runtime
 * @param key Configuration key to update
 * @param value New value
 */
export function updateConfig(key: string, value: any): void {
  // Update the config structure based on the key path
  // Example: updateConfig('connections.redis.enabled', false)
  const keys = key.split('.');
  let current: any = config;

  for (let i = 0; i < keys.length - 1; i++) {
    if (current[keys[i]] === undefined) {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }

  current[keys[keys.length - 1]] = value;
}

// Export PATHS with the legacy uppercase keys for backward compatibility
export const PATHS = {
  // New lowercase properties
  ...config.paths,

  // Legacy uppercase properties
  DATA_DIR: config.paths.dataDir,
  STATE_FILE: config.paths.stateFile,
  SCREENSHOTS_DIR: config.paths.screenshotsDir,
  REPORTS_DIR: config.paths.reportsDir,
};

export default config;