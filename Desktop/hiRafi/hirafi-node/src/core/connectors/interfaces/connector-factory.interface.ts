/**
 * Connector Factory Interface
 * Defines the contract for connector factories
 */

import { IBaseConnector } from './base-connector.interface.js';
import { IConnectorManager } from './connector-manager.interface.js';

/**
 * Connector factory interface
 */
export interface IConnectorFactory {
  /**
   * Create a connector by type
   * @param type Connector type
   * @param config Connector configuration
   */
  createConnector(type: string, config?: any): Promise<IBaseConnector>;

  /**
   * Create HTTP connector
   * @param config Connector configuration
   */
  createHttpConnector(config?: any): Promise<IBaseConnector>;

  /**
   * Create WebSocket connector
   * @param config Connector configuration
   */
  createWebSocketConnector(config?: any): Promise<IBaseConnector>;

  /**
   * Get available connector types
   */
  getAvailableTypes(): string[];

  /**
   * Validate connector type
   * @param type Connector type
   */
  isValidType(type: string): boolean;

  /**
   * Create connector manager
   * @param config Manager configuration
   */
  createConnectorManager(config?: any): Promise<IConnectorManager>;
}

/**
 * Connector factory configuration interface
 */
export interface IConnectorFactoryConfig {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  maxResolutionDepth?: number;
  defaultConnectorType?: string;
  hubUrl?: string;
  secretKey?: string;
  nodeId?: string;
  clientId?: string;
}

/**
 * Connector creation options interface
 */
export interface IConnectorCreationOptions {
  type: string;
  config?: any;
  dependencies?: Record<string, any>;
  lifecycle?: 'singleton' | 'transient';
}

/**
 * Connector type registry interface
 */
export interface IConnectorTypeRegistry {
  /**
   * Register a connector type
   * @param type Connector type
   * @param factory Factory function
   * @param dependencies Dependencies
   */
  registerType(
    type: string,
    factory: (...args: any[]) => Promise<IBaseConnector>,
    dependencies?: string[]
  ): void;

  /**
   * Unregister a connector type
   * @param type Connector type
   */
  unregisterType(type: string): void;

  /**
   * Check if type is registered
   * @param type Connector type
   */
  isTypeRegistered(type: string): boolean;

  /**
   * Get registered types
   */
  getRegisteredTypes(): string[];

  /**
   * Get type factory
   * @param type Connector type
   */
  getTypeFactory(type: string): ((...args: any[]) => Promise<IBaseConnector>) | null;

  /**
   * Get type dependencies
   * @param type Connector type
   */
  getTypeDependencies(type: string): string[];
}
