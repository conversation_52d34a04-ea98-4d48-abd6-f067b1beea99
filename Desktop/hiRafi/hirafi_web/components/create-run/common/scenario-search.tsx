"use client"

import { useState, useCallback } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  X,
  Filter,
  Tag as TagIcon,
  CheckSquare,
  Smartphone,
  Globe,
  Code
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"

import { cn } from "@/lib/utils"

interface ScenarioSearchProps {
  onSearch: (query: string) => void
  onFilterByTag: (tag: string | null) => void
  onFilterByPlatform: (platform: string | null) => void
  availableTags: string[]
  activeTagFilter: string | null
  activePlatformFilter: string | null
  onClearFilters: () => void
}

export function ScenarioSearch({
  onSearch,
  onFilterByTag,
  onFilterByPlatform,
  availableTags = [],
  activeTagFilter,
  activePlatformFilter,
  onClearFilters
}: ScenarioSearchProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // Immediate search without debouncing for better UX
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value)
    onSearch(value)
  }, [onSearch])

  // Clear search
  const handleClearSearch = useCallback(() => {
    setSearchQuery("")
    onSearch("")
  }, [onSearch])

  // Check if any filter is active
  const isFilterActive = activeTagFilter || activePlatformFilter

  return (
    <div className="flex flex-wrap items-center gap-2">
      {/* Search input */}
      <div className="relative flex-grow min-w-[220px]">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search scenarios..."
          value={searchQuery}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-9 h-9 text-sm bg-gray-50 dark:bg-gray-800/50 focus-visible:ring-blue-500 w-full"
        />
        {searchQuery && (
          <button
            onClick={handleClearSearch}
            className="absolute right-2.5 top-2.5"
          >
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Platform filter buttons */}
      <div className="flex items-center gap-2">
        <Button
          variant={activePlatformFilter === 'web' ? 'default' : 'outline'}
          size="sm"
          className={cn(
            'h-8 text-xs flex items-center gap-1.5',
            activePlatformFilter === 'web'
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/20 dark:hover:text-blue-300'
          )}
          onClick={() => onFilterByPlatform(activePlatformFilter === 'web' ? null : 'web')}
        >
          <Globe className="h-3.5 w-3.5" />
          Web
        </Button>

        <Button
          variant={activePlatformFilter === 'mobile' ? 'default' : 'outline'}
          size="sm"
          className={cn(
            'h-8 text-xs flex items-center gap-1.5',
            activePlatformFilter === 'mobile'
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'hover:bg-green-50 hover:text-green-700 dark:hover:bg-green-900/20 dark:hover:text-green-300'
          )}
          onClick={() => onFilterByPlatform(activePlatformFilter === 'mobile' ? null : 'mobile')}
        >
          <Smartphone className="h-3.5 w-3.5" />
          Mobile
        </Button>

        <Button
          variant={activePlatformFilter === 'api' ? 'default' : 'outline'}
          size="sm"
          className={cn(
            'h-8 text-xs flex items-center gap-1.5',
            activePlatformFilter === 'api'
              ? 'bg-purple-600 text-white hover:bg-purple-700'
              : 'hover:bg-purple-50 hover:text-purple-700 dark:hover:bg-purple-900/20 dark:hover:text-purple-300'
          )}
          onClick={() => onFilterByPlatform(activePlatformFilter === 'api' ? null : 'api')}
        >
          <Code className="h-3.5 w-3.5" />
          API
        </Button>
      </div>

      {/* Tag filter & clear */}
      <div className="flex items-center gap-2">
        {/* Tags filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "h-8 text-xs",
                activeTagFilter ? "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800" : ""
              )}
            >
              <TagIcon className="h-3.5 w-3.5 mr-1.5" />
              Tags
              {activeTagFilter && (
                <Badge className="ml-1.5 bg-blue-100 text-blue-700 border-none text-[10px] px-1 py-0 h-4">
                  {activeTagFilter}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-48">
            <DropdownMenuLabel>Filter by Tag</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {availableTags.length > 0 ? (
              <div className="max-h-[200px] overflow-y-auto">
                {availableTags.map((tag) => (
                  <DropdownMenuItem
                    key={tag}
                    className="flex items-center gap-2"
                    onClick={() => onFilterByTag(tag)}
                  >
                    <Checkbox
                      checked={activeTagFilter === tag}
                      className="h-3.5 w-3.5 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                    />
                    <TagIcon className="h-3.5 w-3.5 text-gray-400" />
                    <span className="text-sm truncate">{tag}</span>
                  </DropdownMenuItem>
                ))}
              </div>
            ) : (
              <div className="px-2 py-4 text-center">
                <p className="text-sm text-gray-500">No tags available</p>
              </div>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-gray-500 text-xs"
              onClick={() => onFilterByTag(null)}
            >
              Clear tag filter
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear all filters button */}
        {isFilterActive && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
            onClick={onClearFilters}
          >
            <X className="h-3.5 w-3.5 mr-1" />
            Clear
          </Button>
        )}
      </div>
    </div>
  )
}
