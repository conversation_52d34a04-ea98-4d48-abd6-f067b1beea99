/**
 * Step Progress API Client
 * Handles API calls for test step progress information
 */

import { useState, useEffect, useCallback } from 'react';
import { get, del } from './fetch-wrapper';
import { STEP_PROGRESS_ENDPOINTS } from './constants';

export interface StepProgressData {
  nodeId: string;
  stepIndex: number;
  totalSteps: number;
  stepId: string;
  stepName: string;
  stepType: string;
  status: 'started' | 'completed' | 'failed';
  timestamp: string;
  platform: 'web' | 'android';
  runId?: string;
  executionId?: string;
  scenarioId?: string;
  result?: any;
  // Nested step progress for control flow steps
  nestedSteps?: StepProgressData[];
  parentStepId?: string;
  nestingLevel?: number; // 0 for top level, 1 for first nested, etc.
}

export interface TestStepProgress {
  testId: string;
  currentStep: number;
  totalSteps: number;
  currentStepName: string;
  currentStepType: string;
  currentStepStatus: string;
  lastUpdated: string;
  platform: string;
  runId?: string;
  executionId?: string;
  scenarioId?: string;
  steps: StepProgressData[];
}

export interface RunStepProgressSummary {
  totalTests: number;
  testsInProgress: number;
  testsCompleted: number;
  testsFailed: number;
  averageProgress: number;
  tests: Array<{
    testId: string;
    scenarioId?: string;
    scenarioName?: string;
    currentStep: number;
    totalSteps: number;
    currentStepName: string;
    currentStepType?: string;
    currentStepStatus: string;
    progress: number;
    lastUpdated: string;
    platform: string;
    runId?: string;
    executionId?: string;
    steps?: any[];
  }>;
}

/**
 * Get step progress for a specific test
 */
export async function getTestStepProgress(testId: string): Promise<TestStepProgress | null> {
  try {
    const response = await get(STEP_PROGRESS_ENDPOINTS.GET_TEST(testId), {
      showErrorToast: false
    });

    if (response.success) {
      return response.data;
    }

    return null;
  } catch (error: any) {
    if (error.status === 404) {
      return null; // No step progress found
    }
    console.error('Error fetching test step progress:', error);
    throw error;
  }
}

/**
 * Get step progress for all tests in a run
 */
export async function getRunStepProgress(runId: string): Promise<TestStepProgress[]> {
  try {
    const response = await get(STEP_PROGRESS_ENDPOINTS.GET_RUN(runId), {
      showErrorToast: false
    });

    if (response.success) {
      return response.data;
    }

    return [];
  } catch (error: any) {
    console.error('Error fetching run step progress:', error);
    return [];
  }
}

/**
 * Get step progress summary for a run
 */
export async function getRunStepProgressSummary(runId: string): Promise<RunStepProgressSummary | null> {
  try {
    const response = await get(STEP_PROGRESS_ENDPOINTS.GET_RUN_SUMMARY(runId), {
      showErrorToast: false
    });

    if (response.success) {
      return response.data;
    }

    return null;
  } catch (error: any) {
    console.error('Error fetching run step progress summary:', error);
    return null;
  }
}

/**
 * Clean up step progress for a specific test (admin only)
 */
export async function cleanupTestStepProgress(testId: string): Promise<boolean> {
  try {
    const response = await del(STEP_PROGRESS_ENDPOINTS.DELETE_TEST(testId));
    return response.success;
  } catch (error: any) {
    console.error('Error cleaning up test step progress:', error);
    return false;
  }
}

/**
 * Clean up step progress for all tests in a run (admin only)
 */
export async function cleanupRunStepProgress(runId: string): Promise<boolean> {
  try {
    const response = await del(STEP_PROGRESS_ENDPOINTS.DELETE_RUN(runId));
    return response.success;
  } catch (error: any) {
    console.error('Error cleaning up run step progress:', error);
    return false;
  }
}

/**
 * Hook for real-time step progress updates using polling
 */
export function useStepProgressPolling(
  runId: string,
  intervalMs: number = 2000,
  enabled: boolean = true
): {
  stepProgress: TestStepProgress[];
  summary: RunStepProgressSummary | null;
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
} {
  const [stepProgress, setStepProgress] = useState<TestStepProgress[]>([]);
  const [summary, setSummary] = useState<RunStepProgressSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refresh = useCallback(async () => {
    if (!enabled) return;

    try {
      setError(null);
      const [progressData, summaryData] = await Promise.all([
        getRunStepProgress(runId),
        getRunStepProgressSummary(runId)
      ]);

      setStepProgress(progressData);
      setSummary(summaryData);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch step progress');
    } finally {
      setIsLoading(false);
    }
  }, [runId, enabled]);

  useEffect(() => {
    if (!enabled) return;

    refresh();
    const interval = setInterval(refresh, intervalMs);

    return () => clearInterval(interval);
  }, [refresh, intervalMs, enabled]);

  return {
    stepProgress,
    summary,
    isLoading,
    error,
    refresh
  };
}
