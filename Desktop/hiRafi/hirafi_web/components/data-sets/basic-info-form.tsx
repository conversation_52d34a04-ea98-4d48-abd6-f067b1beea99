"use client"

import type React from "react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { X } from "lucide-react"
import type { DataSetFormData } from "@/types/data-generation"

interface BasicInfoFormProps {
  formData: DataSetFormData
  onFormChange: (field: keyof DataSetFormData, value: any) => void
  newTag: string
  onNewTagChange: (value: string) => void
  onAddTag: () => void
  onRemoveTag: (tag: string) => void
}

export function BasicInfoForm({
  formData,
  onFormChange,
  newTag,
  onNewTagChange,
  onAddTag,
  onRemoveTag,
}: BasicInfoFormProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      onAddTag()
    }
  }

  return (
    <Card>
      <CardHeader>
        <div>
          <CardTitle>Data Set Information</CardTitle>
          <CardDescription>
            Enter the basic details for your data set.
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              placeholder="Enter data set name"
              value={formData.name}
              onChange={(e) => onFormChange("name", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter a description for this data set"
              value={formData.description}
              onChange={(e) => onFormChange("description", e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {formData.tags.map((tag) => (
                <Badge
                  key={tag}
                  className="flex items-center gap-1 bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300 hover:bg-indigo-200 px-3 py-1 text-sm"
                >
                  {tag}
                  <X className="h-3 w-3 cursor-pointer ml-1" onClick={() => onRemoveTag(tag)} />
                </Badge>
              ))}
              {formData.tags.length === 0 && (
                <span className="text-sm text-gray-500 dark:text-gray-400">No tags added yet</span>
              )}
            </div>
            <div className="flex gap-2">
              <Input
                id="tags"
                placeholder="Add a tag"
                value={newTag}
                onChange={(e) => onNewTagChange(e.target.value)}
                onKeyDown={handleKeyDown}
              />
              <Button type="button" onClick={onAddTag} variant="outline" className="px-4">
                Add
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
