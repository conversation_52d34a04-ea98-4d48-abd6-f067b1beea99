"use client"

import { useEffect, useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Users,
  Building2,
  Activity,
  Server,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react"
import { getDashboardStats } from "@/lib/api/admin-api"

interface DashboardStats {
  users?: {
    total: number
    active: number
    newToday: number
    growthRate: number
  }
  companies?: {
    total: number
    active: number
    newToday: number
    growthRate: number
  }
  scenarios?: {
    total: number
    newToday: number
  }
  reports?: {
    total: number
    newToday: number
  }
  runs?: {
    total: number
    today: number
  }
  // Eski format için geriye dönük uyumluluk
  totalUsers: number
  totalCompanies: number
  activeUsers: number
  newUsersToday: number
  newCompaniesToday: number
  userGrowth: number
  companyGrowth: number
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    users: {
      total: 0,
      active: 0,
      newToday: 0,
      growthRate: 0
    },
    companies: {
      total: 0,
      active: 0,
      newToday: 0,
      growthRate: 0
    },
    scenarios: {
      total: 0,
      newToday: 0
    },
    reports: {
      total: 0,
      newToday: 0
    },
    runs: {
      total: 0,
      today: 0
    },
    // Eski format için geriye dönük uyumluluk
    totalUsers: 0,
    totalCompanies: 0,
    activeUsers: 0,
    newUsersToday: 0,
    newCompaniesToday: 0,
    userGrowth: 0,
    companyGrowth: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Gerçek API'den istatistikleri getir
        const response = await getDashboardStats()

        if (response.success && (response.stats || response.data?.stats)) {
          setStats(response.stats || response.data.stats)
          setError(null) // Clear any previous errors
        } else {
          // API başarısız olursa veya istatistik yoksa, varsayılan değerler kullan
          const errorMessage = response.error || "Unknown error occurred while fetching dashboard stats"
          setError(errorMessage)

          // Varsayılan istatistikler
          setStats({
            users: {
              total: 0,
              active: 0,
              newToday: 0,
              growthRate: 0
            },
            companies: {
              total: 0,
              active: 0,
              newToday: 0,
              growthRate: 0
            },
            scenarios: {
              total: 0,
              newToday: 0
            },
            reports: {
              total: 0,
              newToday: 0
            },
            runs: {
              total: 0,
              today: 0
            },
            // Eski format için geriye dönük uyumluluk
            totalUsers: 0,
            totalCompanies: 0,
            activeUsers: 0,
            newUsersToday: 0,
            newCompaniesToday: 0,
            userGrowth: 0,
            companyGrowth: 0
          })
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        setError(errorMessage)

        // Hata durumunda varsayılan değerler
        setStats({
          users: {
            total: 0,
            active: 0,
            newToday: 0,
            growthRate: 0
          },
          companies: {
            total: 0,
            active: 0,
            newToday: 0,
            growthRate: 0
          },
          scenarios: {
            total: 0,
            newToday: 0
          },
          reports: {
            total: 0,
            newToday: 0
          },
          runs: {
            total: 0,
            today: 0
          },
          // Eski format için geriye dönük uyumluluk
          totalUsers: 0,
          totalCompanies: 0,
          activeUsers: 0,
          newUsersToday: 0,
          newCompaniesToday: 0,
          userGrowth: 0,
          companyGrowth: 0
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchStats()
  }, [])

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          )}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Overview of system statistics and recent activities
            </p>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? "..." : stats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.userGrowth > 0 ? (
                    <span className="flex items-center text-green-500">
                      <ArrowUpRight className="mr-1 h-3 w-3" />
                      {stats.userGrowth}% from last month
                    </span>
                  ) : (
                    <span className="flex items-center text-red-500">
                      <ArrowDownRight className="mr-1 h-3 w-3" />
                      {Math.abs(stats.userGrowth)}% from last month
                    </span>
                  )}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Total Companies</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? "..." : stats.totalCompanies}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.companyGrowth > 0 ? (
                    <span className="flex items-center text-green-500">
                      <ArrowUpRight className="mr-1 h-3 w-3" />
                      {stats.companyGrowth}% from last month
                    </span>
                  ) : (
                    <span className="flex items-center text-red-500">
                      <ArrowDownRight className="mr-1 h-3 w-3" />
                      {Math.abs(stats.companyGrowth)}% from last month
                    </span>
                  )}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{isLoading ? "..." : stats.activeUsers}</div>
                <p className="text-xs text-muted-foreground">
                  {isLoading ? "..." : Math.round((stats.activeUsers / stats.totalUsers) * 100)}% of total users
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">System Status</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-500">Healthy</div>
                <p className="text-xs text-muted-foreground">All services operational</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>System activity in the last 24 hours</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-4 rounded-lg border p-3">
                    <div className="rounded-full bg-blue-100 p-2 dark:bg-blue-900">
                      <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">New Users</p>
                      <p className="text-xs text-muted-foreground">
                        {isLoading ? "..." : stats.newUsersToday} new users registered today
                      </p>
                    </div>
                    <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-700 dark:bg-green-900 dark:text-green-300">
                      +{isLoading ? "..." : stats.newUsersToday}
                    </div>
                  </div>

                  <div className="flex items-center gap-4 rounded-lg border p-3">
                    <div className="rounded-full bg-indigo-100 p-2 dark:bg-indigo-900">
                      <Building2 className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">New Companies</p>
                      <p className="text-xs text-muted-foreground">
                        {isLoading ? "..." : stats.newCompaniesToday} new companies added today
                      </p>
                    </div>
                    <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-700 dark:bg-green-900 dark:text-green-300">
                      +{isLoading ? "..." : stats.newCompaniesToday}
                    </div>
                  </div>

                  <div className="flex items-center gap-4 rounded-lg border p-3">
                    <div className="rounded-full bg-orange-100 p-2 dark:bg-orange-900">
                      <BarChart3 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">System Performance</p>
                      <p className="text-xs text-muted-foreground">
                        System is operating at optimal performance
                      </p>
                    </div>
                    <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-700 dark:bg-green-900 dark:text-green-300">
                      Optimal
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common administrative tasks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2">
                  <a
                    href="/admin/users/new"
                    className="flex items-center gap-3 rounded-lg border p-3 text-sm hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <Users className="h-4 w-4" />
                    <div className="font-medium">Add New User</div>
                  </a>
                  <a
                    href="/admin/companies/new"
                    className="flex items-center gap-3 rounded-lg border p-3 text-sm hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <Building2 className="h-4 w-4" />
                    <div className="font-medium">Add New Company</div>
                  </a>

                  <a
                    href="/admin/settings"
                    className="flex items-center gap-3 rounded-lg border p-3 text-sm hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <Server className="h-4 w-4" />
                    <div className="font-medium">System Settings</div>
                  </a>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
    </AdminLayout>
  )
}
