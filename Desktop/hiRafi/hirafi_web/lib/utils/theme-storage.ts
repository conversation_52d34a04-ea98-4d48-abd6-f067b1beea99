/**
 * Company-Aware Theme Storage
 * Theme settings are isolated per company
 */

import { getCompanySpecificKey } from './company-aware-storage';

export const themeStorage = {
  getTheme: (): string | null => {
    if (typeof window === 'undefined') return null;
    
    try {
      const themeKey = getCompanySpecificKey('theme');
      return localStorage.getItem(themeKey);
    } catch (error) {
      console.warn('Failed to get theme:', error);
      return null;
    }
  },
  
  setTheme: (theme: string): void => {
    if (typeof window === 'undefined') return;
    
    try {
      const themeKey = getCompanySpecificKey('theme');
      localStorage.setItem(themeKey, theme);
    } catch (error) {
      console.warn('Failed to set theme:', error);
    }
  },
  
  removeTheme: (): void => {
    if (typeof window === 'undefined') return;
    
    try {
      const themeKey = getCompanySpecificKey('theme');
      localStorage.removeItem(themeKey);
    } catch (error) {
      console.warn('Failed to remove theme:', error);
    }
  }
}; 