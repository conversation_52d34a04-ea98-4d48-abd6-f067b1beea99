/**
 * Scenario Creation Store
 *
 * Zustand store for persisting scenario creation form data across page refreshes
 * Includes automatic expiry and cleanup functionality
 */

import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { companyAwareStorage } from "@/lib/utils/company-aware-storage";
import {
  ExtendedScenarioFormData,
  createDefaultFormData,
} from "@/lib/utils/scenario-form-utils";
import { storeManager } from "@/lib/utils/store-management";

// Draft metadata interface
interface DraftMetadata {
  createdAt: number;
  updatedAt: number;
  expiresAt: number;
  version: number;
}

// Scenario creation draft interface
interface ScenarioCreationDraft {
  id: string;
  formData: ExtendedScenarioFormData;
  metadata: DraftMetadata;
}

// Enhanced persistence configuration
interface AutoSaveConfig {
  enabled: boolean;
  interval: number; // in milliseconds
  lastAutoSave: number | null;
  isActive: boolean;
  timerId: NodeJS.Timeout | null;
}

interface ExpiryConfig {
  defaultTime: number; // in minutes
  warningsEnabled: boolean;
}

interface NotificationConfig {
  saveNotifications: boolean;
  expiryWarnings: boolean;
}

interface PersistenceOptions {
  enableAutoSave?: boolean;
  autoSaveInterval?: number;
  debounceDelay?: number;
  expiryTime?: number;
  showSaveNotifications?: boolean;
  showExpiryWarnings?: boolean;
  onDraftLoaded?: (formData: ExtendedScenarioFormData) => void;
  onDraftSaved?: () => void;
  onDraftExpired?: () => void;
}

// Enhanced store state interface
interface ScenarioCreationState {
  // Current draft
  currentDraft: ScenarioCreationDraft | null;

  // UI state
  isDraftLoading: boolean;
  lastSaveTime: number | null;
  hasUnsavedChanges: boolean;
  isInitialized: boolean;

  // Enhanced persistence state
  autoSave: AutoSaveConfig;
  expiry: ExpiryConfig;
  notifications: NotificationConfig;

  // Debounce state
  debounceDelay: number;
  debounceTimerId: NodeJS.Timeout | null;

  // Callbacks
  callbacks: {
    onDraftLoaded?: (formData: ExtendedScenarioFormData) => void;
    onDraftSaved?: () => void;
    onDraftExpired?: () => void;
  };
}

// Enhanced store actions interface
interface ScenarioCreationActions {
  // Core draft management
  createDraft: (initialData?: Partial<ExtendedScenarioFormData>) => string;
  updateDraft: (formData: ExtendedScenarioFormData) => void;
  loadDraft: (draftId: string) => ScenarioCreationDraft | null;
  deleteDraft: (draftId?: string) => void;
  clearCurrentDraft: () => void;

  // Form data management
  updateFormField: (field: keyof ExtendedScenarioFormData, value: any) => void;
  resetFormData: () => void;

  // Enhanced persistence management
  initializePersistence: (options: PersistenceOptions) => void;
  saveDraftDebounced: (formData: ExtendedScenarioFormData) => void;
  enableAutoSave: (interval?: number) => void;
  disableAutoSave: () => void;
  setAutoSaveInterval: (interval: number) => void;

  // Expiry management
  setExpiryTime: (minutes: number) => void;
  isExpired: (draftId?: string) => boolean;
  cleanupExpiredDrafts: () => number;

  // Utility functions
  hasChanges: () => boolean;
  getDraftAge: (draftId?: string) => number; // in minutes
  exportDraft: () => string; // JSON string
  importDraft: (jsonString: string) => boolean;

  // Internal persistence methods
  _startAutoSaveTimer: () => void;
  _stopAutoSaveTimer: () => void;
  _clearDebounceTimer: () => void;

  // Clear persisted state for store manager
  clearPersistedState: () => void;
}

type ScenarioCreationStore = ScenarioCreationState & ScenarioCreationActions;

// Helper function to generate draft ID
const generateDraftId = (): string => {
  return `draft_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Helper function to create draft metadata
const createDraftMetadata = (expiryMinutes: number): DraftMetadata => {
  const now = Date.now();
  return {
    createdAt: now,
    updatedAt: now,
    expiresAt: now + expiryMinutes * 60 * 1000,
    version: 1,
  };
};

// Initial state
const initialState: ScenarioCreationState = {
  currentDraft: null,
  isDraftLoading: false,
  lastSaveTime: null,
  hasUnsavedChanges: false,
  isInitialized: false,

  // Enhanced persistence state
  autoSave: {
    enabled: true,
    interval: 30000, // 30 seconds
    lastAutoSave: null,
    isActive: false,
    timerId: null,
  },

  expiry: {
    defaultTime: 60, // 60 minutes
    warningsEnabled: true,
  },

  notifications: {
    saveNotifications: false,
    expiryWarnings: true,
  },

  // Debounce state
  debounceDelay: 1000, // 1 second
  debounceTimerId: null,

  // Callbacks
  callbacks: {},
};

export const useScenarioCreationStore = create<ScenarioCreationStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Create a new draft
        createDraft: (initialData = {}) => {
          const draftId = generateDraftId();
          const formData = { ...createDefaultFormData(false), ...initialData };
          const metadata = createDraftMetadata(get().expiry.defaultTime);

          const draft: ScenarioCreationDraft = {
            id: draftId,
            formData,
            metadata,
          };

          set({
            currentDraft: draft,
            lastSaveTime: Date.now(),
            hasUnsavedChanges: false,
          });

          // Trigger callback if available
          const { callbacks } = get();
          if (callbacks.onDraftSaved) {
            callbacks.onDraftSaved();
          }

          return draftId;
        },

        // Update current draft
        updateDraft: (formData) => {
          const currentDraft = get().currentDraft;
          if (!currentDraft) return;

          const updatedDraft: ScenarioCreationDraft = {
            ...currentDraft,
            formData: { ...formData },
            metadata: {
              ...currentDraft.metadata,
              updatedAt: Date.now(),
            },
          };

          set({
            currentDraft: updatedDraft,
            lastSaveTime: Date.now(),
            hasUnsavedChanges: false,
          });
        },

        // Load a specific draft
        loadDraft: (draftId) => {
          const currentDraft = get().currentDraft;
          if (currentDraft?.id === draftId) {
            return currentDraft;
          }
          // For now, we only support one current draft
          // This could be extended to support multiple drafts
          return null;
        },

        // Delete a draft
        deleteDraft: (draftId) => {
          const currentDraft = get().currentDraft;
          if (!draftId || currentDraft?.id === draftId) {
            set({
              currentDraft: null,
              lastSaveTime: null,
              hasUnsavedChanges: false,
            });
          }
        },

        // Clear current draft
        clearCurrentDraft: () => {
          set({
            currentDraft: null,
            lastSaveTime: null,
            hasUnsavedChanges: false,
          });
        },

        // Update a specific form field
        updateFormField: (field, value) => {
          const currentDraft = get().currentDraft;
          if (!currentDraft) return;

          const updatedFormData = {
            ...currentDraft.formData,
            [field]: value,
          };

          get().updateDraft(updatedFormData);
          set({ hasUnsavedChanges: true });
        },

        // Reset form data to defaults
        resetFormData: () => {
          const currentDraft = get().currentDraft;
          if (!currentDraft) return;

          const defaultFormData = createDefaultFormData(false);
          get().updateDraft(defaultFormData);
        },

        // Initialize persistence with options
        initializePersistence: (options) => {
          const state = get();

          // Update configuration
          set({
            autoSave: {
              ...state.autoSave,
              enabled: options.enableAutoSave ?? state.autoSave.enabled,
              interval: options.autoSaveInterval ?? state.autoSave.interval,
            },
            expiry: {
              ...state.expiry,
              defaultTime: options.expiryTime ?? state.expiry.defaultTime,
              warningsEnabled: options.showExpiryWarnings ?? state.expiry.warningsEnabled,
            },
            notifications: {
              ...state.notifications,
              saveNotifications: options.showSaveNotifications ?? state.notifications.saveNotifications,
              expiryWarnings: options.showExpiryWarnings ?? state.notifications.expiryWarnings,
            },
            debounceDelay: options.debounceDelay ?? state.debounceDelay,
            callbacks: {
              onDraftLoaded: options.onDraftLoaded,
              onDraftSaved: options.onDraftSaved,
              onDraftExpired: options.onDraftExpired,
            },
            isInitialized: true,
          });

          // Start auto-save if enabled
          if (options.enableAutoSave) {
            get()._startAutoSaveTimer();
          }

          // Cleanup expired drafts and load existing draft
          const cleanedCount = get().cleanupExpiredDrafts();
          if (cleanedCount > 0 && options.showExpiryWarnings) {
            const { callbacks } = get();
            callbacks.onDraftExpired?.();
          }

          // Load existing draft if available
          const currentDraft = get().currentDraft;
          if (currentDraft && !get().isExpired() && options.onDraftLoaded) {
            options.onDraftLoaded(currentDraft.formData);
          }
        },

        // Debounced save function
        saveDraftDebounced: (formData) => {
          const state = get();

          // Clear existing debounce timer
          if (state.debounceTimerId) {
            clearTimeout(state.debounceTimerId);
          }

          // Set new debounce timer
          const timerId = setTimeout(() => {
            if (!state.currentDraft) {
              get().createDraft(formData);
            } else {
              get().updateDraft(formData);
            }

            // Clear the timer reference
            set({ debounceTimerId: null });
          }, state.debounceDelay);

          set({ debounceTimerId: timerId });
        },

        // Enhanced enable auto-save
        enableAutoSave: (interval) => {
          const state = get();
          set({
            autoSave: {
              ...state.autoSave,
              enabled: true,
              interval: interval ?? state.autoSave.interval,
            }
          });
          get()._startAutoSaveTimer();
        },

        // Enhanced disable auto-save
        disableAutoSave: () => {
          const state = get();
          set({
            autoSave: {
              ...state.autoSave,
              enabled: false,
              isActive: false,
            }
          });
          get()._stopAutoSaveTimer();
        },

        // Set auto-save interval
        setAutoSaveInterval: (interval) => {
          const state = get();
          const newInterval = Math.max(5000, interval); // Minimum 5 seconds
          set({
            autoSave: {
              ...state.autoSave,
              interval: newInterval,
            }
          });

          // Restart timer if auto-save is active
          if (state.autoSave.isActive) {
            get()._stopAutoSaveTimer();
            get()._startAutoSaveTimer();
          }
        },

        // Set expiry time
        setExpiryTime: (minutes) => {
          const state = get();
          set({
            expiry: {
              ...state.expiry,
              defaultTime: Math.max(5, minutes), // Minimum 5 minutes
            }
          });
        },

        // Check if draft is expired
        isExpired: (draftId) => {
          const currentDraft = get().currentDraft;
          const draft = draftId ? get().loadDraft(draftId) : currentDraft;

          if (!draft) return true;

          return Date.now() > draft.metadata.expiresAt;
        },

        // Cleanup expired drafts
        cleanupExpiredDrafts: () => {
          const currentDraft = get().currentDraft;
          let cleanedCount = 0;

          if (currentDraft && get().isExpired(currentDraft.id)) {
            get().clearCurrentDraft();
            cleanedCount++;
          }

          return cleanedCount;
        },

        // Check if there are unsaved changes
        hasChanges: () => {
          return get().hasUnsavedChanges;
        },

        // Get draft age in minutes
        getDraftAge: (draftId) => {
          const currentDraft = get().currentDraft;
          const draft = draftId ? get().loadDraft(draftId) : currentDraft;

          if (!draft) return 0;

          return Math.floor(
            (Date.now() - draft.metadata.createdAt) / (1000 * 60),
          );
        },

        // Export draft as JSON
        exportDraft: () => {
          const currentDraft = get().currentDraft;
          if (!currentDraft) return "{}";

          return JSON.stringify(currentDraft, null, 2);
        },

        // Import draft from JSON
        importDraft: (jsonString) => {
          try {
            const draft = JSON.parse(jsonString) as ScenarioCreationDraft;

            // Validate draft structure
            if (!draft.id || !draft.formData || !draft.metadata) {
              return false;
            }

            set({
              currentDraft: draft,
              lastSaveTime: Date.now(),
              hasUnsavedChanges: false,
            });

            return true;
          } catch (error) {
            console.error("Failed to import draft:", error);
            return false;
          }
        },

        // Internal: Start auto-save timer
        _startAutoSaveTimer: () => {
          const state = get();

          // Don't start if already active or disabled
          if (state.autoSave.isActive || !state.autoSave.enabled) {
            return;
          }

          const timerId = setInterval(() => {
            const currentState = get();

            // Stop if auto-save was disabled
            if (!currentState.autoSave.enabled) {
              get()._stopAutoSaveTimer();
              return;
            }

            // Save if there are unsaved changes
            if (currentState.hasUnsavedChanges && currentState.currentDraft) {
              get().updateDraft(currentState.currentDraft.formData);

              // Update last auto-save time
              set({
                autoSave: {
                  ...currentState.autoSave,
                  lastAutoSave: Date.now(),
                }
              });

              // Trigger callback if available
              const { callbacks } = get();
              if (callbacks.onDraftSaved) {
                callbacks.onDraftSaved();
              }
            }
          }, state.autoSave.interval);

          set({
            autoSave: {
              ...state.autoSave,
              isActive: true,
              timerId,
            }
          });
        },

        // Internal: Stop auto-save timer
        _stopAutoSaveTimer: () => {
          const state = get();

          if (state.autoSave.timerId) {
            clearInterval(state.autoSave.timerId);
          }

          set({
            autoSave: {
              ...state.autoSave,
              isActive: false,
              timerId: null,
            }
          });
        },

        // Internal: Clear debounce timer
        _clearDebounceTimer: () => {
          const state = get();

          if (state.debounceTimerId) {
            clearTimeout(state.debounceTimerId);
            set({ debounceTimerId: null });
          }
        },

        // Clear persisted state for store manager
        clearPersistedState: () => {
          const state = get();

          // Stop any active timers
          get()._stopAutoSaveTimer();
          get()._clearDebounceTimer();

          // Only clear if there's no current draft or if it's expired
          // This prevents accidental clearing of valid drafts on page refresh
          if (!state.currentDraft || get().isExpired()) {
            set({ ...initialState });
            console.log(
              "[ScenarioCreationStore] Cleared expired or empty draft",
            );
          } else {
            // Preserve the current draft but reset other transient state
            set({
              ...initialState,
              currentDraft: state.currentDraft,
              lastSaveTime: state.lastSaveTime,
              hasUnsavedChanges: state.hasUnsavedChanges,
            });
            console.log(
              "[ScenarioCreationStore] Preserved valid draft during context change",
            );
          }
        },
      }),
      {
        name: "scenario-creation-store",
        storage: companyAwareStorage,
        // Persist everything except loading states and timers
        partialize: (state) => ({
          currentDraft: state.currentDraft,
          lastSaveTime: state.lastSaveTime,
          hasUnsavedChanges: state.hasUnsavedChanges,
          isInitialized: state.isInitialized,
          autoSave: {
            ...state.autoSave,
            // Don't persist timer state
            isActive: false,
            timerId: null,
            lastAutoSave: state.autoSave.lastAutoSave,
          },
          expiry: state.expiry,
          notifications: state.notifications,
          debounceDelay: state.debounceDelay,
          // Don't persist callbacks or timer IDs
        }),
        // Custom merge function
        merge: (persistedState: any, currentState: ScenarioCreationStore) => ({
          ...currentState,
          ...persistedState,
          // Always start with loading false
          isDraftLoading: false,
        }),
      },
    ),
    { name: "scenario-creation-store" },
  ),
);

// Register this store with the store manager for automatic cleanup
if (typeof window !== "undefined") {
  storeManager.registerStore({
    clearPersistedState: () =>
      useScenarioCreationStore.getState().clearPersistedState(),
  });
}

// Export types for use in components
export type { ScenarioCreationDraft, DraftMetadata };
