/**
 * Centralized duration formatting utilities
 * This file provides consistent duration formatting across the application
 */

/**
 * Format duration from milliseconds to human-readable format
 * @param durationMs Duration in milliseconds
 * @param options Formatting options
 * @returns Formatted duration string
 */
export function formatDuration(
  durationMs: number | string | undefined | null,
  options: {
    precision?: number;
    showMs?: boolean;
    fallback?: string;
  } = {}
): string {
  const { precision = 2, showMs = false, fallback = 'N/A' } = options;

  // Handle null/undefined values
  if (durationMs === undefined || durationMs === null) {
    return fallback;
  }

  // Parse string values
  let duration: number;
  if (typeof durationMs === 'string') {
    duration = parseFloat(durationMs);
    if (isNaN(duration)) {
      return fallback;
    }
  } else {
    duration = durationMs;
  }

  // Handle invalid numbers
  if (isNaN(duration) || duration < 0) {
    return fallback;
  }

  // If duration is very small and showMs is true, show in milliseconds
  if (showMs && duration < 1000) {
    return `${Math.round(duration)}ms`;
  }

  // Convert to seconds
  const seconds = duration / 1000;

  // Less than 1 minute
  if (seconds < 60) {
    return `${seconds.toFixed(precision)}s`;
  }

  // Less than 1 hour
  if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(precision)}s`;
  }

  // 1 hour or more
  const hours = Math.floor(seconds / 3600);
  const remainingMinutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  if (remainingSeconds > 0) {
    return `${hours}h ${remainingMinutes}m ${remainingSeconds.toFixed(precision)}s`;
  } else if (remainingMinutes > 0) {
    return `${hours}h ${remainingMinutes}m`;
  } else {
    return `${hours}h`;
  }
}

/**
 * Format duration for test steps (optimized for step-level durations)
 * @param durationMs Duration in milliseconds
 * @returns Formatted duration string optimized for steps
 */
export function formatStepDuration(durationMs: number | string | undefined | null): string {
  return formatDuration(durationMs, {
    precision: 2,
    showMs: true,
    fallback: 'Unknown'
  });
}

/**
 * Format duration for timeline display (compact format)
 * @param durationMs Duration in milliseconds
 * @returns Formatted duration string optimized for timeline
 */
export function formatTimelineDuration(durationMs: number | string | undefined | null): string {
  return formatDuration(durationMs, {
    precision: 2,
    showMs: false,
    fallback: 'Unknown'
  });
}

/**
 * Format duration for reports and summaries (detailed format)
 * @param durationMs Duration in milliseconds
 * @returns Formatted duration string optimized for reports
 */
export function formatReportDuration(durationMs: number | string | undefined | null): string {
  return formatDuration(durationMs, {
    precision: 1,
    showMs: false,
    fallback: 'N/A'
  });
}

/**
 * Calculate total duration from an array of steps
 * @param steps Array of steps with duration property
 * @returns Total duration in milliseconds
 */
export function calculateTotalDuration(steps: Array<{ duration?: number }>): number {
  return steps.reduce((total, step) => {
    const stepDuration = step.duration || 0;
    return total + stepDuration;
  }, 0);
}

/**
 * Validate if a duration value is valid
 * @param duration Duration value to validate
 * @returns True if duration is valid
 */
export function isValidDuration(duration: any): duration is number {
  return typeof duration === 'number' && !isNaN(duration) && duration >= 0;
}
