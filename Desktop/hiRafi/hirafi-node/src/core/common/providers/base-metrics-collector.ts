/**
 * Unified Metrics Collector
 * Simplified and optimized metrics collection for all platforms
 */

import { IMetricsCollector } from '../interfaces/metrics-collector.interface.js';
import { EnhancedMetrics, ReportSettings, WebReportSettings, AndroidReportSettings } from '../../../models/types.js';
import { logger } from '../../../utils/logger.js';
import { PerformanceMonitor } from 'rfi-ai-android';
import { NetworkCollector, collectAllMetrics } from '../../web/metrics-collector.js';

// Type guards for report settings
function isWebReportSettings(settings: ReportSettings): settings is WebReportSettings {
  return 'pageMetrics' in settings;
}

function isAndroidReportSettings(settings: ReportSettings): settings is AndroidReportSettings {
  return 'collectMetrics' in settings || 'metricsInterval' in settings;
}



/**
 * Unified Metrics Collector
 * Simplified implementation for all platforms
 */
export class UnifiedMetricsCollector implements IMetricsCollector {
  protected isCollectingMetrics: boolean = false;
  protected currentTestId: string | null = null;
  protected platform: 'web' | 'mobile';
  protected reportSettings: ReportSettings;
  protected config: any = {};

  // Platform-specific properties
  private androidPerformanceMonitor: PerformanceMonitor | null = null;
  private webNetworkCollector: NetworkCollector | null = null;
  private currentTarget: any = null;

  // Race condition prevention
  private stopInProgress: boolean = false;
  private stopPromise: Promise<void> | null = null;

  /**
   * Create a new UnifiedMetricsCollector
   * @param platform Platform identifier
   */
  constructor(platform: 'web' | 'mobile', config: any = {}) {
    this.platform = platform;
    this.config = config;
    this.reportSettings = {
      takeScreenshots: true,
      takeVideos: false
    } as ReportSettings;

    logger.debug(`UnifiedMetricsCollector: Initialized for ${platform} platform`);
  }

  /**
   * Initialize the metrics collector
   * @param config Configuration object
   */
  async initialize(config: any): Promise<void> {
    this.config = { ...this.config, ...config };
    logger.debug(`UnifiedMetricsCollector: Initialized with config for ${this.platform} platform`);
  }

  /**
   * Set up metrics collection for a test
   * @param target The target to collect metrics from
   * @param testId Test identifier
   * @param reportSettings Settings for what metrics to collect
   */
  async setup(target: any, testId: string, reportSettings: ReportSettings): Promise<void> {
    this.currentTestId = testId;
    this.currentTarget = target;
    this.reportSettings = { ...this.reportSettings, ...reportSettings };

    // Platform-specific setup
    if (this.platform === 'web') {
      await this.setupWebMetrics(target, testId);
    } else {
      await this.setupAndroidMetrics(target, testId);
    }

    logger.info(`UnifiedMetricsCollector: Set up for test ${testId} on ${this.platform} platform`);
  }

  /**
   * Start collecting metrics
   * @param target The target to collect metrics from
   */
  async startCollection(target: any): Promise<void> {
    if (this.isCollectingMetrics) {
      logger.debug(`UnifiedMetricsCollector: Metrics collection already in progress`);
      return;
    }

    this.isCollectingMetrics = true;
    this.currentTarget = target;

    // Platform-specific start collection
    if (this.platform === 'web') {
      await this.startWebCollection(target);
    } else {
      await this.startAndroidCollection(target);
    }

    logger.info(`UnifiedMetricsCollector: Started metrics collection on ${this.platform} platform`);
  }

  /**
   * Stop collecting metrics with race condition prevention
   */
  async stopCollection(): Promise<void> {
    const context = `BaseMetricsCollector.stopCollection`;

    // Prevent concurrent stop operations
    if (this.stopInProgress) {
      logger.debug(`${context}: Stop collection already in progress, waiting for completion`);
      await this.stopPromise;
      return;
    }

    if (!this.isCollectingMetrics) {
      logger.debug(`${context}: No active metrics collection to stop`);
      return;
    }

    // Set stop in progress flag and create promise
    this.stopInProgress = true;
    this.stopPromise = this.executeStopCollection();

    try {
      await this.stopPromise;
    } finally {
      this.stopInProgress = false;
      this.stopPromise = null;
    }
  }

  /**
   * Execute the actual stop collection logic
   */
  private async executeStopCollection(): Promise<void> {
    const context = `BaseMetricsCollector.executeStopCollection`;

    try {
      // Platform-specific stop collection
      if (this.platform === 'web') {
        await this.stopWebCollection();
      } else {
        await this.stopAndroidCollection();
      }

      this.isCollectingMetrics = false;

      // Export metrics if configured (placeholder for future implementation)
      if (this.config.exportMetricsToFile && this.currentTestId) {
        logger.debug(`UnifiedMetricsCollector: Metrics export requested for test ${this.currentTestId}`);
      }

      logger.info(`${context}: Stopped metrics collection on ${this.platform} platform`);
    } catch (error: any) {
      logger.error(`${context}: Error stopping metrics collection: ${error.message}`);
      // Force reset collection state
      this.isCollectingMetrics = false;
      throw error;
    }
  }

  /**
   * Collect metrics from target (deprecated - use collectWithCoordination for web platform)
   * @param target The target to collect metrics from
   * @param options Options for metrics collection
   * @returns Promise resolving to collected metrics
   */
  async collect(target: any, options: any = {}): Promise<Partial<EnhancedMetrics>> {
    try {
      logger.warn(`UnifiedMetricsCollector: Using deprecated collect method. Consider using collectWithCoordination for web platform.`);

      // For web platform, redirect to coordinated collection
      if (this.platform === 'web') {
        return await this.collectWithCoordination(target, options);
      }

      // For Android platform, use direct collection
      const metrics = await this.collectAndroidMetrics(target, options);

      // Add common metadata
      return {
        ...metrics,
        timestamp: new Date().toISOString(),
        platform: this.platform
      };
    } catch (error: any) {
      logger.error(`UnifiedMetricsCollector: Failed to collect metrics: ${error.message}`);
      return {
        error: error.message,
        timestamp: new Date().toISOString(),
        platform: this.platform
      };
    }
  }

  /**
   * Collect metrics with enhanced coordination to prevent race conditions
   * @param target The target to collect metrics from
   * @param options Options for metrics collection
   * @returns Promise resolving to collected metrics
   */
  async collectWithCoordination(target: any, options: any = {}): Promise<Partial<EnhancedMetrics>> {
    try {
      logger.info(`UnifiedMetricsCollector: Starting coordinated metrics collection for ${this.platform} platform`);

      // Use the enhanced coordinated collection for web platform
      if (this.platform === 'web') {
        // Import the enhanced metrics collection function
        const { ensureAllMetricsCompleted } = await import('../../web/metrics-collector.js');

        // Use actual reportSettings from the test request instead of hardcoded values
        const webReportSettings = isWebReportSettings(this.reportSettings) ? this.reportSettings : {
          pageMetrics: true,
          networkData: true,
          tracingData: false,
          accessibilityData: false
        };

        const coordinatedOptions = {
          maxTimeout: options.timeout || options.maxTimeout || 12000,
          reportSettings: webReportSettings
        };

        return await ensureAllMetricsCompleted(
          target,
          this.currentTestId || 'unknown',
          this.webNetworkCollector,
          coordinatedOptions
        );
      } else {
        // For Android platform, use the regular collection method
        return await this.collectMetrics(target, options);
      }

    } catch (error: any) {
      logger.warn(`UnifiedMetricsCollector: Error during coordinated metrics collection: ${error.message}`);

      // Return safe fallback
      return {
        timestamp: new Date().toISOString(),
        platform: this.platform
      };
    }
  }

  /**
   * Collect final metrics before cleanup to prevent race conditions
   * @param target The target to collect metrics from
   * @param options Options for metrics collection
   * @returns Promise resolving to collected metrics
   */
  async collectMetrics(target: any, options: any = {}): Promise<Partial<EnhancedMetrics>> {
    try {
      logger.info(`UnifiedMetricsCollector: Collecting final metrics for ${this.platform} platform before cleanup`);

      // Use coordinated collection for web platform, regular for others
      const finalMetrics = this.platform === 'web'
        ? await this.collectWithCoordination(target, options)
        : await this.collectAndroidMetrics(target, options);
      logger.info(`UnifiedMetricsCollector: Final metrics collection completed successfully`);
      return finalMetrics;

    } catch (error: any) {
      // Check for race condition errors
      const isRaceConditionError = error.message.includes('Session closed') ||
                                  error.message.includes('Target closed') ||
                                  error.message.includes('Protocol error');

      if (isRaceConditionError) {
        logger.info(`UnifiedMetricsCollector: Race condition prevented - metrics collection attempted before cleanup`);
        return {
          error: `Race condition prevented during final metrics collection`,
          timestamp: new Date().toISOString(),
          platform: this.platform
        };
      }

      return {
        error: `Final metrics collection failed: ${error.message}`,
        timestamp: new Date().toISOString(),
        platform: this.platform
      };
    }
  }

  // Platform-specific implementation methods

  private async setupWebMetrics(page: any, testId: string): Promise<void> {
    // Create network collector if needed (but don't start it yet)
    if (isWebReportSettings(this.reportSettings) && this.reportSettings.networkData !== false) {
      try {
        this.webNetworkCollector = new NetworkCollector(page, testId);
        logger.debug('UnifiedMetricsCollector: NetworkCollector created (will be started in startCollection)');
      } catch (error: any) {
        logger.warn(`UnifiedMetricsCollector: Failed to create NetworkCollector: ${error.message}`);
        this.webNetworkCollector = null;
      }
    }
  }

  private async startWebCollection(page: any): Promise<void> {
    // Start network collector if available
    if (this.webNetworkCollector) {
      try {
        await this.webNetworkCollector.start();
        logger.debug('UnifiedMetricsCollector: NetworkCollector started');
      } catch (error: any) {
        logger.warn(`UnifiedMetricsCollector: Failed to start NetworkCollector: ${error.message}`);
      }
    }
  }

  private async stopWebCollection(): Promise<void> {
    if (this.webNetworkCollector) {
      try {
        await this.webNetworkCollector.stop();
        logger.debug('UnifiedMetricsCollector: NetworkCollector stopped');
      } catch (error: any) {
        logger.warn(`UnifiedMetricsCollector: Error stopping NetworkCollector: ${error.message}`);
      }
    }
    this.isCollectingMetrics = false;
  }

  private async collectWebMetrics(page: any, options: any): Promise<Partial<EnhancedMetrics>> {
    try {
      // Validate page
      if (!page || (typeof page.isClosed === 'function' && page.isClosed())) {
        logger.warn('UnifiedMetricsCollector: Page is invalid, returning empty metrics');
        return { timestamp: new Date().toISOString() };
      }

      // Use the comprehensive metrics collection
      const webReportSettings = isWebReportSettings(this.reportSettings) ? this.reportSettings : {
        pageMetrics: true,
        networkData: true,
        tracingData: false,
        accessibilityData: false
      };

      const metrics = await collectAllMetrics(
        page,
        this.currentTestId || 'unknown',
        this.webNetworkCollector,
        {
          reportSettings: webReportSettings
        }
      );

      return {
        ...metrics,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      logger.warn(`UnifiedMetricsCollector: Error collecting web metrics: ${error.message}`);
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }



  // Android-specific implementation methods
  private async setupAndroidMetrics(agent: any, testId: string): Promise<void> {
    try {
      this.androidPerformanceMonitor = new PerformanceMonitor(agent.page);
      await this.androidPerformanceMonitor.initialize();
      logger.debug('UnifiedMetricsCollector: Android metrics setup completed');
    } catch (error: any) {
      logger.warn(`UnifiedMetricsCollector: Error setting up Android metrics: ${error.message}`);
    }
  }

  private async startAndroidCollection(agent: any): Promise<void> {
    if (this.androidPerformanceMonitor) {
      this.androidPerformanceMonitor.startMonitoring(10000);
      logger.debug('UnifiedMetricsCollector: Started Android performance monitoring');
    }
  }

  private async stopAndroidCollection(): Promise<void> {
    if (this.androidPerformanceMonitor) {
      try {
        this.androidPerformanceMonitor.stopMonitoring();
        logger.debug('UnifiedMetricsCollector: Stopped Android performance monitoring');
      } catch (error: any) {
        logger.warn(`UnifiedMetricsCollector: Error stopping Android monitoring: ${error.message}`);
      }
    }
    this.isCollectingMetrics = false;
  }

  private async collectAndroidMetrics(agent: any, options: any): Promise<Partial<EnhancedMetrics>> {
    try {
      const metrics: any = {
        timestamp: new Date().toISOString()
      };

      if (this.androidPerformanceMonitor) {
        const currentMetrics = await this.androidPerformanceMonitor.getCurrentMetrics();
        if (currentMetrics) {
          metrics.performanceMetrics = currentMetrics;
        }
      }

      return metrics;
    } catch (error: any) {
      logger.warn(`UnifiedMetricsCollector: Error collecting Android metrics: ${error.message}`);
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }



  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.isCollectingMetrics) {
      try {
        await this.stopCollection();
      } catch (error: any) {
        logger.warn(`UnifiedMetricsCollector: Error stopping collection during cleanup: ${error.message}`);
      }
    }

    // Platform-specific cleanup
    if (this.platform === 'web' && this.webNetworkCollector) {
      try {
        await this.webNetworkCollector.stop();
        this.webNetworkCollector.destroy();
        this.webNetworkCollector = null;
      } catch (error: any) {
        logger.warn(`UnifiedMetricsCollector: Error cleaning up web collector: ${error.message}`);
      }
    } else if (this.platform === 'mobile' && this.androidPerformanceMonitor) {
      try {
        this.androidPerformanceMonitor.stopMonitoring();
        this.androidPerformanceMonitor = null;
      } catch (error: any) {
        logger.warn(`UnifiedMetricsCollector: Error cleaning up Android monitor: ${error.message}`);
      }
    }

    this.currentTarget = null;
    logger.debug(`UnifiedMetricsCollector: Cleanup completed for ${this.platform} platform`);
  }

  /**
   * Check if metrics collection is currently active
   */
  isCollecting(): boolean {
    return this.isCollectingMetrics;
  }
}

// Backward compatibility export
export { UnifiedMetricsCollector as BaseMetricsCollector };
