declare module 'amqplib' {
  import { EventEmitter } from 'events';

  export interface Connect {
    protocol: string;
    hostname: string;
    port: number;
    username: string;
    password: string;
    vhost: string;
  }

  export interface Connection {
    createChannel(): Promise<Channel>;
    close(): Promise<void>;
    on(event: string, listener: Function): Connection;
    serverProperties: any;
    expectSocketClose: boolean;
    sentSinceLastCheck: boolean;
    recvSinceLastCheck: boolean;
    sendMessage: Function;
  }

  export interface Channel extends EventEmitter {
    assertExchange(exchange: string, type: string, options?: any): Promise<any>;
    assertQueue(queue: string, options?: any): Promise<any>;
    bindQueue(queue: string, exchange: string, pattern: string, args?: any): Promise<any>;
    consume(queue: string, onMessage: (msg: any) => void, options?: any): Promise<any>;
    publish(exchange: string, routingKey: string, content: Buffer, options?: any): boolean;
    sendToQueue(queue: string, content: Buffer, options?: any): boolean;
    ack(message: any): void;
    nack(message: any, allUpTo?: boolean, requeue?: boolean): void;
    close(): Promise<void>;
  }

  export interface Message {
    content: Buffer;
    fields: {
      exchange: string;
      routingKey: string;
      redelivered: boolean;
    };
    properties: {
      contentType: string;
      contentEncoding: string;
      headers: any;
      deliveryMode: number;
      priority: number;
      correlationId: string;
      replyTo: string;
      expiration: string;
      messageId: string;
      timestamp: number;
      type: string;
      userId: string;
      appId: string;
      clusterId: string;
    };
  }

  export function connect(url: string | Connect): Promise<Connection>;
} 