"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  Search,
  UserPlus,
  Edit,
  Trash2,
  Filter
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getUsers, deleteUser, getCompanies } from "@/lib/api/admin-api"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { EditUserDialog } from "./edit-user-dialog"

interface User {
  id: string
  email: string
  name?: string
  role: string
  teamRole?: string
  accountType?: string
  companyId?: string
  teamId?: string
  active?: boolean
  createdDate: string
  lastLogin?: string
}

export function UserList() {
  const [users, setUsers] = useState<User[]>([])
  const [companies, setCompanies] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [selectedRole, setSelectedRole] = useState<string>("")
  const [selectedCompany, setSelectedCompany] = useState<string>("")
  const [selectedActive, setSelectedActive] = useState<string>("true") // Varsayılan olarak aktif kullanıcıları göster
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [error, setError] = useState<string | null>(null)
  const pageSize = 10

  useEffect(() => {
    fetchUsers()
    fetchCompanies()
  }, [currentPage])

  const fetchUsers = async () => {
    setIsLoading(true)
    setError(null) // Clear previous errors
    try {
      // Gerçek API'den kullanıcıları getir
      console.log("Fetching users with filters:", {
        page: currentPage,
        limit: pageSize,
        search: searchTerm,
        role: selectedRole,
        companyId: selectedCompany,
        active: selectedActive
      })

      const response = await getUsers({
        page: currentPage,
        limit: pageSize,
        search: searchTerm,
        role: selectedRole,
        companyId: selectedCompany,
        active: selectedActive
      })

      // Debug log to see the actual response
      console.log('Users response:', response)

      if (response.success && (response.data?.users || response.users)) {
        const users = response.data?.users || response.users;
        setUsers(users)
        setTotalPages(response.data?.totalPages || response.totalPages || 1)
        setTotalUsers(response.data?.total || response.total || users.length)
      } else {
        // API başarısız olursa veya kullanıcı yoksa, boş liste göster
        setUsers([])
        setTotalPages(1)
        setTotalUsers(0)
        const errorMessage = response.error || "Unknown error occurred while fetching users"
        console.error("Failed to fetch users:", errorMessage)
        setError(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error("Failed to fetch users:", errorMessage)
      setError(errorMessage)
      // Hata durumunda boş liste göster
      setUsers([])
      setTotalPages(1)
      setTotalUsers(0)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchCompanies = async () => {
    setIsLoadingCompanies(true)
    try {
      const response = await getCompanies({
        page: 1,
        limit: 100
      }) // Tüm şirketleri getir

      // Debug log to see the actual response
      console.log('Companies response:', response)

      if (response.success && response.companies) {
        setCompanies(response.companies)
      } else {
        setCompanies([])
        const errorMessage = response.error || "Unknown error occurred while fetching companies"
        console.error("Failed to fetch companies:", errorMessage)
        // Don't set error state here to avoid overriding user list errors
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error("Failed to fetch companies:", errorMessage)
      setCompanies([])
      // Don't set error state here to avoid overriding user list errors
    } finally {
      setIsLoadingCompanies(false)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    fetchUsers()
  }

  const handleRoleChange = (value: string) => {
    setSelectedRole(value === "all" ? "" : value)
    setCurrentPage(1)
    setTimeout(() => fetchUsers(), 0)
  }

  const handleCompanyChange = (value: string) => {
    setSelectedCompany(value === "all" ? "" : value)
    setCurrentPage(1)
    setTimeout(() => fetchUsers(), 0)
  }

  const handleActiveChange = (value: string) => {
    setSelectedActive(value)
    setCurrentPage(1)
    setTimeout(() => fetchUsers(), 0)
  }

  const clearFilters = () => {
    setSelectedRole("")
    setSelectedCompany("")
    setSelectedActive("true") // Varsayılan olarak aktif kullanıcıları göster
    setSearchTerm("")
    setCurrentPage(1)
    setTimeout(() => fetchUsers(), 0)
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setIsEditUserDialogOpen(true)
  }

  const handleDeleteUser = async (userId: string) => {
    if (!confirm("Are you sure you want to delete this user?")) {
      return
    }

    try {
      // Gerçek API'den kullanıcıyı sil
      const response = await deleteUser(userId)

      if (response.success) {
        // Başarılı silme işleminden sonra kullanıcı listesini yenile
        fetchUsers()
      } else {
        console.error("Failed to delete user:", response.error)
        alert(`Failed to delete user: ${response.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error("Failed to delete user:", error)
      alert("An error occurred while deleting the user")
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Never"
    return new Date(dateString).toLocaleDateString()
  }

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-[250px]"
          />
          <Button variant="outline" size="icon" onClick={handleSearch}>
            <Search className="h-4 w-4" />
          </Button>

          <Select value={selectedRole || "all"} onValueChange={handleRoleChange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="user">User</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedCompany || "all"} onValueChange={handleCompanyChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by company" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Companies</SelectItem>
              {companies.map((company) => (
                <SelectItem key={company.id} value={company.id}>
                  {company.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedActive} onValueChange={handleActiveChange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Active</SelectItem>
              <SelectItem value="false">Inactive</SelectItem>
              <SelectItem value="all">All Users</SelectItem>
            </SelectContent>
          </Select>

          {(selectedRole || selectedCompany || selectedActive !== "true") && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              Clear Filters
            </Button>
          )}
        </div>
        {/* Add User butonu kaldırıldı, sayfa üstünde zaten var */}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name/Email</TableHead>
              <TableHead>Team Role</TableHead>
              <TableHead>Account Type</TableHead>
              <TableHead>Company</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Last Login</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="font-medium">{user.name || "N/A"}</div>
                    <div className="text-sm text-muted-foreground">{user.email}</div>
                  </TableCell>
                  <TableCell>
                    {user.teamRole ? (
                      <Badge variant="outline">
                        {user.teamRole.replace('_', ' ')}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">None</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {user.accountType || "basic"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.companyId ? (
                      <Link
                        href={`/admin/companies/${user.companyId}`}
                        className="text-blue-600 hover:underline"
                      >
                        {companies.find(c => c.id === user.companyId)?.name || "Company"}
                      </Link>
                    ) : (
                      "None"
                    )}
                  </TableCell>
                  <TableCell>
                    {user.active !== undefined ? (
                      user.active ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                          Active
                        </Badge>
                      ) : (
                        <Badge className="bg-red-100 text-red-800 hover:bg-red-200">
                          Inactive
                        </Badge>
                      )
                    ) : (
                      <Badge variant="outline">Unknown</Badge>
                    )}
                  </TableCell>
                  <TableCell>{formatDate(user.createdDate)}</TableCell>
                  <TableCell>{formatDate(user.lastLogin)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {/* View butonu kaldırıldı */}
                        <DropdownMenuItem onSelect={() => handleEditUser(user)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteUser(user.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {users.length} of {totalUsers} users
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Edit User Dialog */}
      {selectedUser && (
        <EditUserDialog
          open={isEditUserDialogOpen}
          onOpenChange={setIsEditUserDialogOpen}
          onSuccess={fetchUsers}
          user={selectedUser}
        />
      )}
    </div>
  )
}
