/**
 * SauceLabs Device Availability Manager
 * Manages SauceLabs device availability for tests
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { getSauceLabsDevices } from '../mongo/sauceLabsService.js';

// Device status cache entry
interface DeviceStatusCacheEntry {
  available: boolean;
  lastChecked: Date;
}

export class SauceLabsDeviceAvailabilityManager extends EventEmitter {
  private static instance: SauceLabsDeviceAvailabilityManager;
  private deviceStatusCache: Map<string, DeviceStatusCacheEntry> = new Map();
  private cacheExpiryMs: number;
  private isInitialized: boolean = false;

  /**
   * Create a new SauceLabsDeviceAvailabilityManager
   * @param options Configuration options
   */
  private constructor(options?: {
    cacheExpiryMs?: number;
  }) {
    super();
    this.cacheExpiryMs = options?.cacheExpiryMs || 60000; // Default: 1 minute
  }

  /**
   * Get the singleton instance
   * @param options Configuration options
   * @returns SauceLabsDeviceAvailabilityManager instance
   */
  public static getInstance(options?: {
    cacheExpiryMs?: number;
  }): SauceLabsDeviceAvailabilityManager {
    if (!SauceLabsDeviceAvailabilityManager.instance) {
      SauceLabsDeviceAvailabilityManager.instance = new SauceLabsDeviceAvailabilityManager(options);
    }
    return SauceLabsDeviceAvailabilityManager.instance;
  }

  /**
   * Initialize the manager
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    logger.info('SauceLabsDeviceAvailabilityManager: Initializing');
    this.isInitialized = true;
    logger.info('SauceLabsDeviceAvailabilityManager: Initialized successfully');
  }

  /**
   * Check if a device is available
   * @param userId User ID
   * @param deviceId Device ID
   * @returns Promise resolving to true if device is available, false otherwise
   */
  public async isDeviceAvailable(userId: string, deviceId: string): Promise<boolean> {
    // Check cache first
    const cacheKey = `${userId}:${deviceId}`;
    const cachedStatus = this.deviceStatusCache.get(cacheKey);

    if (cachedStatus && (new Date().getTime() - cachedStatus.lastChecked.getTime() < this.cacheExpiryMs)) {
      logger.debug(`SauceLabsDeviceAvailabilityManager: Using cached status for device ${deviceId}: ${cachedStatus.available}`);
      return cachedStatus.available;
    }

    // Cache miss or expired, check from API
    const available = await this.checkDeviceAvailabilityFromApi(userId, deviceId);

    // Update cache
    this.deviceStatusCache.set(cacheKey, {
      available,
      lastChecked: new Date()
    });

    logger.info(`SauceLabsDeviceAvailabilityManager: Device ${deviceId} availability: ${available}`);
    return available;
  }

  /**
   * Check if multiple devices are available
   * @param userId User ID
   * @param deviceIds Array of device IDs
   * @returns Promise resolving to an object mapping device IDs to their availability
   */
  public async areDevicesAvailable(userId: string, deviceIds: string[]): Promise<Record<string, boolean>> {
    logger.info(`SauceLabsDeviceAvailabilityManager: Checking availability for ${deviceIds.length} devices`);

    const result: Record<string, boolean> = {};

    // Check each device in parallel
    await Promise.all(
      deviceIds.map(async (deviceId) => {
        result[deviceId] = await this.isDeviceAvailable(userId, deviceId);
      })
    );

    logger.info(`SauceLabsDeviceAvailabilityManager: Completed availability check for ${deviceIds.length} devices`);
    return result;
  }

  /**
   * Check device availability from SauceLabs API
   * @param userId User ID
   * @param deviceId Device ID
   * @returns Promise resolving to true if device is available, false otherwise
   */
  private async checkDeviceAvailabilityFromApi(userId: string, deviceId: string): Promise<boolean> {
    try {
      // Get all devices from SauceLabs (now includes status information)
      const result = await getSauceLabsDevices(userId);

      if (!result.success || !result.devices || !Array.isArray(result.devices)) {
        logger.error(`SauceLabsDeviceAvailabilityManager: Failed to get devices for user ${userId}`);
        return false;
      }

      // Find the specific device
      const device = result.devices.find((d: any) => d.id === deviceId);

      if (!device) {
        logger.error(`SauceLabsDeviceAvailabilityManager: Device ${deviceId} not found for user ${userId}`);
        return false;
      }

      // Check if device is available
      // First check the state property (from the status endpoint)
      if (device.state) {
        const isAvailable = device.state === 'AVAILABLE';
        logger.info(`SauceLabsDeviceAvailabilityManager: Device ${deviceId} availability from API (state=${device.state}): ${isAvailable}`);
        return isAvailable;
      }

      // Fallback to legacy properties if state is not available
      const isAvailable = device.is_available === true || device.available === true;
      logger.info(`SauceLabsDeviceAvailabilityManager: Device ${deviceId} availability from API (legacy check): ${isAvailable}`);
      return isAvailable;
    } catch (error) {
      logger.error(`SauceLabsDeviceAvailabilityManager: Error checking device availability: ${error}`);
      return false;
    }
  }

  /**
   * Handle test node reporting device unavailability
   * This method is kept for compatibility but only updates the cache
   * @param testId Test ID
   * @param deviceId Device ID
   * @param userId User ID
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async handleDeviceUnavailableFromNode(testId: string, deviceId: string, userId: string): Promise<boolean> {
    try {
      logger.info(`SauceLabsDeviceAvailabilityManager: Received device unavailability report from node for test ${testId}, device ${deviceId}`);

      // Update device status in cache
      const cacheKey = `${userId}:${deviceId}`;
      this.deviceStatusCache.set(cacheKey, {
        available: false,
        lastChecked: new Date()
      });

      logger.info(`SauceLabsDeviceAvailabilityManager: Updated device status cache for ${deviceId} (user: ${userId})`);
      return true;
    } catch (error) {
      logger.error(`SauceLabsDeviceAvailabilityManager: Error handling device unavailability from node: ${error}`);
      return false;
    }
  }
}

// Export singleton instance
export const sauceLabsDeviceAvailabilityManager = SauceLabsDeviceAvailabilityManager.getInstance();
