"use client"

import { useState, useEffect } from "react"
import { Reorder, motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import {
  GripVertical,
  X,
  CheckSquare,
  Tag,
  Clock,
  ArrowUpDown,
  Trash2,
  AlertCircle,
  Smartphone,
  Globe,
  Code
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatDistanceToNow } from "date-fns"

interface Scenario {
  id: string
  name?: string
  title?: string
  description?: string
  tags?: string[]
  steps?: any[]
  stepsCount?: number
  updatedAt?: string
  platform?: string
}

interface SelectedScenariosListProps {
  selectedScenarios: string[]
  scenariosData: Scenario[]
  onReorder: (newOrder: string[]) => void
  onRemove: (id: string) => void
  onClear: () => void
}

export function SelectedScenariosList({
  selectedScenarios,
  scenariosData,
  onReorder,
  onRemove,
  onClear
}: SelectedScenariosListProps) {
  const [dragEnabled, setDragEnabled] = useState(false)

  // Get platform icon
  const getPlatformIcon = (platform: string = 'web') => {
    switch (platform.toLowerCase()) {
      case 'android':
      case 'ios':
      case 'mobile':
        return <Smartphone className="h-3.5 w-3.5 text-green-500" />
      case 'api':
        return <Code className="h-3.5 w-3.5 text-purple-500" />
      case 'web':
      default:
        return <Globe className="h-3.5 w-3.5 text-blue-500" />
    }
  }

  // Get platform label
  const getPlatformLabel = (platform: string = 'web') => {
    switch (platform.toLowerCase()) {
      case 'android':
      case 'ios':
        return 'Mobile'
      case 'api':
        return 'API'
      case 'web':
      default:
        return 'Web'
    }
  }

  // Get steps count
  const getStepsCount = (scenario: Scenario) => {
    if (scenario.stepsCount !== undefined) return scenario.stepsCount
    if (scenario.steps?.length) return scenario.steps.length
    return 0
  }

  if (selectedScenarios.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-12 text-center">
        <AlertCircle className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-4" />
        <p className="text-gray-500 dark:text-gray-400">No scenarios selected</p>
        <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
          Select scenarios from the list to add them to your test run
        </p>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex flex-col p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <CheckSquare className="h-4 w-4 text-blue-500" />
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Selected Scenarios ({selectedScenarios.length})
            </h3>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-2 text-xs text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              onClick={onClear}
            >
              <Trash2 className="h-3.5 w-3.5 mr-1" />
              Clear All
            </Button>
          </div>
        </div>

        {/* Scenario Ordering Section - More Prominent */}
        <div className={cn(
          "flex items-center justify-between p-3 rounded-md transition-all",
          dragEnabled
            ? "bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800"
            : "bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700"
        )}>
          <div className="flex items-center gap-2">
            <ArrowUpDown className={cn(
              "h-4 w-4",
              dragEnabled ? "text-blue-600 dark:text-blue-400" : "text-gray-500 dark:text-gray-400"
            )} />
            <span className={cn(
              "text-sm font-medium",
              dragEnabled ? "text-blue-700 dark:text-blue-300" : "text-gray-700 dark:text-gray-300"
            )}>
              Scenario Order
            </span>
          </div>

          <Button
            variant={dragEnabled ? "default" : "outline"}
            size="sm"
            className={cn(
              "h-7 px-3 text-xs",
              dragEnabled
                ? "bg-blue-600 hover:bg-blue-700 text-white"
                : "text-gray-600 dark:text-gray-300"
            )}
            onClick={() => setDragEnabled(!dragEnabled)}
          >
            {dragEnabled ? "Done Reordering" : "Reorder Scenarios"}
          </Button>
        </div>
      </div>

      {/* Scenarios list - Using div with overflow instead of ScrollArea to avoid potential issues */}
      <div className="flex-1 overflow-auto">
        <div className="p-3">
          {dragEnabled ? (
            <Reorder.Group
              axis="y"
              values={selectedScenarios}
              onReorder={onReorder}
              className="space-y-2"
            >
              {selectedScenarios.map((scenarioId) => {
                const scenario = scenariosData.find(s => s.id === scenarioId)
                if (!scenario) return null

                return (
                  <Reorder.Item
                    key={scenarioId}
                    value={scenarioId}
                    className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-3 cursor-move hover:shadow-md transition-all"
                  >
                    <div className="flex items-center gap-3">
                      <GripVertical className="h-5 w-5 text-gray-400" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{scenario.name || scenario.title || 'Unnamed Scenario'}</p>
                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1 gap-3">
                          <span className="flex items-center">
                            {getPlatformIcon(scenario.platform)}
                            <span className="ml-1">{getPlatformLabel(scenario.platform)}</span>
                          </span>

                          {scenario.tags && scenario.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-1">
                              {scenario.tags.slice(0, 3).map((tag, idx) => (
                                <Badge
                                  key={idx}
                                  variant="outline"
                                  className="bg-gray-50 text-xs px-1.5 py-0 border-gray-200 text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                                >
                                  {tag}
                                </Badge>
                              ))}
                              {scenario.tags.length > 3 && (
                                <Badge
                                  variant="outline"
                                  className="bg-gray-50 text-xs px-1.5 py-0 border-gray-200 text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                                >
                                  +{scenario.tags.length - 3}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Reorder.Item>
                )
              })}
            </Reorder.Group>
          ) : (
            <div className="space-y-2">
              {selectedScenarios.map((scenarioId, index) => {
                const scenario = scenariosData.find(s => s.id === scenarioId)
                if (!scenario) return null

                return (
                  <div
                    key={scenarioId}
                    className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-3 hover:border-gray-300 dark:hover:border-gray-600 transition-all group"
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 dark:bg-gray-700 text-xs font-medium text-gray-600 dark:text-gray-300">
                        {index + 1}
                      </div>

                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{scenario.name || scenario.title || 'Unnamed Scenario'}</p>
                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1 gap-3">
                          <span className="flex items-center">
                            {getPlatformIcon(scenario.platform)}
                            <span className="ml-1">{getPlatformLabel(scenario.platform)}</span>
                          </span>

                          {scenario.tags && scenario.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-1">
                              {scenario.tags.slice(0, 3).map((tag, idx) => (
                                <Badge
                                  key={idx}
                                  variant="outline"
                                  className="bg-gray-50 text-xs px-1.5 py-0 border-gray-200 text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                                >
                                  {tag}
                                </Badge>
                              ))}
                              {scenario.tags.length > 3 && (
                                <Badge
                                  variant="outline"
                                  className="bg-gray-50 text-xs px-1.5 py-0 border-gray-200 text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300"
                                >
                                  +{scenario.tags.length - 3}
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-red-500"
                        onClick={() => onRemove(scenarioId)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
