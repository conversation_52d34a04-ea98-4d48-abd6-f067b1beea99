{"name": "aidrive_testhub", "version": "1.5.5", "description": "Test hub for AI Drive automation testing", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx src/server.ts", "start": "cross-env NODE_ENV=production node dist/server.js", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts", "build:protected": "node scripts/build-protected.js", "package:binary": "node scripts/package-binary.js", "build:all": "npm run build && npm run build:protected && npm run package:binary"}, "author": "", "license": "ISC", "dependencies": {"@bull-board/api": "^6.9.5", "@bull-board/express": "^6.9.5", "@devicepark/sdk": "^1.0.2", "@devicepark/shared": "^1.0.2", "@devicepark/storage-client": "^1.0.0", "@slack/web-api": "^7.9.1", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "bull": "^4.16.5", "bullmq": "^5.4.5", "chalk": "^5.4.1", "commander": "^13.1.0", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.4.7", "express": "^5.1.0", "form-data": "^4.0.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "mongodb": "^6.16.0", "multer": "^2.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.2", "openai": "^4.87.3", "pdfkit": "^0.14.0", "pdfkit-table": "^0.1.99", "proxy-from-env": "^1.1.0", "routing-controllers": "^0.11.2", "socket.io": "^4.8.1", "ts-node": "^10.9.2", "typedi": "^0.10.0", "uuid": "^9.0.1", "winston": "^3.13.0", "ws": "^8.18.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20", "@types/pdfkit": "^0.13.9", "@types/uuid": "^9.0.8", "@types/ws": "^8.18.0", "bytenode": "^1.5.3", "cross-env": "^7.0.3", "eslint": "^9", "glob": "^10.3.10", "javascript-obfuscator": "^4.1.1", "pkg": "^5.8.1", "tsx": "^4.19.3", "typescript": "^5.8.3"}}