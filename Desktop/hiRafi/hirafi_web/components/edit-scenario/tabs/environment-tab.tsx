/**
 * DEPRECATED: This component is no longer in use.
 * The Environment tab has been removed from the scenario editor.
 * This file is kept for reference but can be deleted in the future.
 */

"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ExtendedScenarioFormData } from "@/lib/utils/scenario-form-utils"
import { Chrome, Smartphone, Tablet, Monitor, LayoutGrid } from "lucide-react"

interface EnvironmentTabProps {
  scenarioData: ExtendedScenarioFormData
  updateNestedField: (parentField: "environment" | "reporting", field: string, value: any) => void
  updateProxyField: (field: string, value: any) => void
}

export function EnvironmentTab({ 
  scenarioData, 
  updateNestedField,
  updateProxyField
}: EnvironmentTabProps) {
  return (
    <Card className="overflow-hidden border-0 shadow-md">
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
        <CardTitle>Environment Settings</CardTitle>
        <CardDescription>Configure the browser and test environment</CardDescription>
      </CardHeader>
      <CardContent className="p-6 space-y-8">
        {/* Browser Settings */}
        <div>
          <h3 className="text-lg font-medium mb-4">Browser Settings</h3>
          <div className="space-y-6">
            <div>
              <Label className="text-base mb-3 block">Browser</Label>
              <RadioGroup 
                value={scenarioData.environment.browser} 
                onValueChange={(value) => {
                  updateNestedField("environment", "browser", value as "chrome" | "firefox")
                  updateNestedField("environment", "browserName", value === "chrome" ? "Chrome" : "Firefox")
                }}
                className="grid grid-cols-2 gap-4"
              >
                <div>
                  <RadioGroupItem 
                    value="chrome" 
                    id="browser-chrome" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="browser-chrome"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20 h-[158px]"
                  >
                    <Chrome className="mb-3 h-10 w-10 text-blue-500" />
                    <div className="space-y-1 text-center">
                      <p className="text-base font-medium">Google Chrome</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Recommended browser</p>
                    </div>
                  </Label>
                </div>
                
                <div>
                  <RadioGroupItem 
                    value="firefox" 
                    id="browser-firefox" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="browser-firefox"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20 h-[158px]"
                  >
                    <div className="mb-3 h-10 w-10 relative">
                      <img 
                        src="/images/firefox-logo.png" 
                        alt="Firefox Logo" 
                        className="h-10 w-10 object-contain"
                      />
                    </div>
                    <div className="space-y-1 text-center">
                      <p className="text-base font-medium">Mozilla Firefox</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Alternative browser</p>
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">AI Model</Label>
              <div className="h-10 px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-gray-900 dark:text-gray-100 flex items-center bg-gray-50 dark:bg-gray-800">
                Gemini 2.5 Pro
              </div>
            </div>
          </div>
        </div>

        {/* Viewport Size Settings */}
        <div>
          <Label className="text-base mb-3 block">Viewport Size</Label>
          <RadioGroup 
            value={scenarioData.environment.viewportSize} 
            onValueChange={(value: "Mobile" | "Tablet" | "Desktop" | "Custom") => {
              updateNestedField("environment", "viewportSize", value)
              
              // Preset viewports
              const viewports = {
                Mobile: { width: 375, height: 667 },
                Tablet: { width: 768, height: 1024 },
                Desktop: { width: 1280, height: 720 },
                Custom: scenarioData.environment.customViewport
              }
              
              if (value !== "Custom") {
                updateNestedField("environment", "viewport", viewports[value])
              } else {
                updateNestedField("environment", "viewport", scenarioData.environment.customViewport)
              }
            }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4"
          >
            <div>
              <RadioGroupItem 
                value="Mobile" 
                id="viewport-mobile" 
                className="peer sr-only"
              />
              <Label
                htmlFor="viewport-mobile"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
              >
                <Smartphone className="mb-2 h-6 w-6 text-indigo-500" />
                <div className="text-center">
                  <p className="text-sm font-medium">Mobile</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">375 x 667</p>
                </div>
              </Label>
            </div>
            
            <div>
              <RadioGroupItem 
                value="Tablet" 
                id="viewport-tablet" 
                className="peer sr-only"
              />
              <Label
                htmlFor="viewport-tablet"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
              >
                <Tablet className="mb-2 h-6 w-6 text-purple-500" />
                <div className="text-center">
                  <p className="text-sm font-medium">Tablet</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">768 x 1024</p>
                </div>
              </Label>
            </div>
            
            <div>
              <RadioGroupItem 
                value="Desktop" 
                id="viewport-desktop" 
                className="peer sr-only"
              />
              <Label
                htmlFor="viewport-desktop"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
              >
                <Monitor className="mb-2 h-6 w-6 text-blue-500" />
                <div className="text-center">
                  <p className="text-sm font-medium">Desktop</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">1280 x 720</p>
                </div>
              </Label>
            </div>
            
            <div>
              <RadioGroupItem 
                value="Custom" 
                id="viewport-custom" 
                className="peer sr-only"
              />
              <Label
                htmlFor="viewport-custom"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
              >
                <LayoutGrid className="mb-2 h-6 w-6 text-amber-500" />
                <div className="text-center">
                  <p className="text-sm font-medium">Custom</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Set size</p>
                </div>
              </Label>
            </div>
          </RadioGroup>
          
          {/* Custom Viewport Size Inputs */}
          {scenarioData.environment.viewportSize === "Custom" && (
            <div className="flex items-center gap-4 mt-4 p-4 border border-gray-200 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-800/40">
              <div className="flex-1">
                <Label htmlFor="viewport-width" className="text-sm mb-1 block">Width (px)</Label>
                <Input
                  id="viewport-width"
                  type="number"
                  min="320"
                  max="3840"
                  value={scenarioData.environment.customViewport.width}
                  onChange={(e) => {
                    const width = parseInt(e.target.value)
                    if (!isNaN(width)) {
                      const newCustomViewport = {
                        ...scenarioData.environment.customViewport,
                        width
                      }
                      updateNestedField("environment", "customViewport", newCustomViewport)
                      if (scenarioData.environment.viewportSize === "Custom") {
                        updateNestedField("environment", "viewport", newCustomViewport)
                      }
                    }
                  }}
                  className="w-full"
                />
              </div>
              <div className="flex-1">
                <Label htmlFor="viewport-height" className="text-sm mb-1 block">Height (px)</Label>
                <Input
                  id="viewport-height"
                  type="number"
                  min="240"
                  max="2160"
                  value={scenarioData.environment.customViewport.height}
                  onChange={(e) => {
                    const height = parseInt(e.target.value)
                    if (!isNaN(height)) {
                      const newCustomViewport = {
                        ...scenarioData.environment.customViewport,
                        height
                      }
                      updateNestedField("environment", "customViewport", newCustomViewport)
                      if (scenarioData.environment.viewportSize === "Custom") {
                        updateNestedField("environment", "viewport", newCustomViewport)
                      }
                    }
                  }}
                  className="w-full"
                />
              </div>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="network-speed" className="text-sm font-medium">
            Network Speed
          </Label>
          <Select
            value={scenarioData.environment.networkSpeed}
            onValueChange={(value: "Slow 3G" | "Fast 3G" | "Slow 4G" | "Fast 4G" | "Normal") => 
              updateNestedField("environment", "networkSpeed", value)
            }
          >
            <SelectTrigger id="network-speed">
              <SelectValue placeholder="Select network speed" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Slow 3G">Slow 3G</SelectItem>
              <SelectItem value="Fast 3G">Fast 3G</SelectItem>
              <SelectItem value="Slow 4G">Slow 4G</SelectItem>
              <SelectItem value="Fast 4G">Fast 4G</SelectItem>
              <SelectItem value="Normal">Normal (No throttling)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Proxy Settings */}
        <div>
          <h3 className="text-lg font-medium mb-4">Proxy Settings</h3>
          <div className="space-y-6">
            <div className="flex items-center space-x-2">
              <div className="flex-1 h-8">
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox" 
                    className="sr-only peer"
                    checked={scenarioData.environment.proxy.enabled}
                    onChange={(e) => updateProxyField("enabled", e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                  <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Use proxy
                  </span>
                </label>
              </div>
            </div>

            {scenarioData.environment.proxy.enabled && (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="proxy-type" className="text-sm font-medium">
                    Proxy Type
                  </Label>
                  <Select
                    value={scenarioData.environment.proxy.type}
                    onValueChange={(value: "HTTP" | "HTTPS" | "SOCKS5") => 
                      updateProxyField("type", value)
                    }
                  >
                    <SelectTrigger id="proxy-type">
                      <SelectValue placeholder="Select proxy type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HTTP">HTTP</SelectItem>
                      <SelectItem value="HTTPS">HTTPS</SelectItem>
                      <SelectItem value="SOCKS5">SOCKS5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="proxy-host" className="text-sm font-medium">
                      Host
                    </Label>
                    <Input
                      id="proxy-host"
                      placeholder="proxy.example.com"
                      value={scenarioData.environment.proxy.host}
                      onChange={(e) => updateProxyField("host", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="proxy-port" className="text-sm font-medium">
                      Port
                    </Label>
                    <Input
                      id="proxy-port"
                      type="number"
                      min="1"
                      max="65535"
                      placeholder="8080"
                      value={scenarioData.environment.proxy.port}
                      onChange={(e) => {
                        const port = parseInt(e.target.value)
                        if (!isNaN(port)) {
                          updateProxyField("port", port)
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="proxy-username" className="text-sm font-medium">
                      Username (optional)
                    </Label>
                    <Input
                      id="proxy-username"
                      placeholder="username"
                      value={scenarioData.environment.proxy.username}
                      onChange={(e) => updateProxyField("username", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="proxy-password" className="text-sm font-medium">
                      Password (optional)
                    </Label>
                    <Input
                      id="proxy-password"
                      type="password"
                      placeholder="••••••••"
                      value={scenarioData.environment.proxy.password}
                      onChange={(e) => updateProxyField("password", e.target.value)}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 