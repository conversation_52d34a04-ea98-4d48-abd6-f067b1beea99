"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Filter,
  Search,
  X,
  ChevronDown,
  Smartphone,
  Globe,
  ServerIcon,
  FolderIcon,
  Tag,
  Calendar,
  FileCheck
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import type { FolderType } from "@/types/scenario"
import type { ScenarioFilters } from "./modern-table"

interface FilterBoxProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  filters: ScenarioFilters
  onFilterChange: (filters: ScenarioFilters) => void
  folders: FolderType[]
  availableTags: string[]
}

export function FilterBox({
  searchQuery,
  onSearchChange,
  filters,
  onFilterChange,
  folders,
  availableTags,
}: FilterBoxProps) {
  const [calendarOpen, setCalendarOpen] = useState(false)
  const [tempDateRange, setTempDateRange] = useState<{ from?: Date; to?: Date }>({
    from: filters.dateRange?.from,
    to: filters.dateRange?.to
  })

  // Helper to get folder name
  const getSelectedFolderName = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId)
    return folder?.name || "Kategorisiz"
  }

  // Update filters
  const handleFilterChange = (key: keyof ScenarioFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFilterChange(newFilters);
  }

  // Clear all filters
  const clearAllFilters = () => {
    console.log("FilterBox: Clearing all filters");
    // Only call onFilterChange with an empty object
    // This will clear all filters including search
    onFilterChange({});
  }

  // Apply custom date range
  const applyCustomDateRange = () => {
    handleFilterChange('dateRange', tempDateRange)
    setCalendarOpen(false)
  }

  // Cancel custom date selection
  const cancelCustomDateRange = () => {
    setTempDateRange({
      from: filters.dateRange?.from,
      to: filters.dateRange?.to
    })
    setCalendarOpen(false)
  }

  // Format date range for display
  const formatDateRange = () => {
    if (filters.dateRange?.from && filters.dateRange?.to) {
      return `${format(filters.dateRange.from, "MMM d")} - ${format(filters.dateRange.to, "MMM d, yyyy")}`
    } else if (filters.dateRange?.from) {
      return `${format(filters.dateRange.from, "MMM d, yyyy")} -`
    } else if (filters.dateRange?.to) {
      return `- ${format(filters.dateRange.to, "MMM d, yyyy")}`
    }
    return null
  }

  // Check if any filters are active
  const hasActiveFilters = !!(
    searchQuery ||
    (filters.status && filters.status.length > 0) ||
    (filters.testType && filters.testType.length > 0) ||
    (filters.tags && filters.tags.length > 0) ||
    filters.testrailStatus ||
    filters.sortDate ||
    filters.dateRange?.from ||
    filters.dateRange?.to
  )

  return (
    <div className="space-y-2 mb-3">
      <div className="flex flex-wrap items-center gap-2">
        <div className="relative flex-1 min-w-[300px]">
          <div className="flex items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <div className={`flex items-center flex-wrap gap-1 pl-9 pr-4 py-1 h-auto min-h-9 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md ${hasActiveFilters ? 'pt-1.5' : ''}`}>
                <Input
                  placeholder="Senaryoları ara..."
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="border-0 p-0 h-6 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-sm flex-1 min-w-[150px]"
                />

                {/* Inline filter badges */}
                {filters.status && filters.status.length > 0 && (
                  <Badge className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800 py-0.5 px-2 h-6 text-xs font-normal">
                    <div className="flex items-center">
                      <span className="font-medium">Durum: {filters.status.join(', ')}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-green-100 dark:hover:bg-green-900/50 p-0"
                        onClick={() => handleFilterChange('status', [])}
                      >
                        <X className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </Badge>
                )}

                {filters.testType && filters.testType.length > 0 && (
                  <Badge className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800 py-0.5 px-2 h-6 text-xs font-normal">
                    <div className="flex items-center">
                      <span className="font-medium">Tip: {filters.testType.join(', ')}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-purple-100 dark:hover:bg-purple-900/50 p-0"
                        onClick={() => handleFilterChange('testType', [])}
                      >
                        <X className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </Badge>
                )}

                {filters.tags && filters.tags.length > 0 && (
                  <Badge className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800 py-0.5 px-2 h-6 text-xs font-normal">
                    <div className="flex items-center">
                      <span className="font-medium">Etiket: {filters.tags.length > 1 ? `${filters.tags.length} etiket` : filters.tags[0]}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-amber-100 dark:hover:bg-amber-900/50 p-0"
                        onClick={() => handleFilterChange('tags', [])}
                      >
                        <X className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </Badge>
                )}

                {(filters.dateRange?.from || filters.dateRange?.to) && (
                  <Badge className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800 py-0.5 px-2 h-6 text-xs font-normal">
                    <div className="flex items-center">
                      <span className="font-medium">Tarih: {formatDateRange()}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-blue-100 dark:hover:bg-blue-900/50 p-0"
                        onClick={() => handleFilterChange('dateRange', undefined)}
                      >
                        <X className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </Badge>
                )}

                {filters.sortDate && (
                  <Badge className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800 py-0.5 px-2 h-6 text-xs font-normal">
                    <div className="flex items-center">
                      <span className="font-medium">Tarih: {filters.sortDate === 'asc' ? 'Eskiden Yeniye' : 'Yeniden Eskiye'}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-blue-100 dark:hover:bg-blue-900/50 p-0"
                        onClick={() => handleFilterChange('sortDate', null)}
                      >
                        <X className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </Badge>
                )}

                {filters.testrailStatus && (
                  <Badge className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-800 py-0.5 px-2 h-6 text-xs font-normal">
                    <div className="flex items-center">
                      <span className="font-medium">Test Management: {filters.testrailStatus}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-indigo-100 dark:hover:bg-indigo-900/50 p-0"
                        onClick={() => handleFilterChange('testrailStatus', null)}
                      >
                        <X className="h-2.5 w-2.5" />
                      </Button>
                    </div>
                  </Badge>
                )}

                {(searchQuery || hasActiveFilters) && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="ml-auto h-7 px-2 text-xs text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    onClick={clearAllFilters}
                  >
                    <X className="h-3.5 w-3.5 mr-1" />
                    Tüm Filtreleri Temizle
                  </Button>
                )}
              </div>
            </div>

            {/* Filter buttons next to search input */}
            <div className="flex items-center gap-1 ml-2">
              {/* Date Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                  >
                    <span className="flex items-center">
                      <Calendar className="h-3.5 w-3.5 mr-1" />
                      Tarih
                      {(filters.sortDate || filters.dateRange?.from || filters.dateRange?.to) && (
                        <Badge className="ml-1 bg-blue-500 text-white">
                          {filters.sortDate ? (filters.sortDate === 'asc' ? 'Sıralama' : 'Sıralama') : 'Aralık'}
                        </Badge>
                      )}
                    </span>
                    <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px] dark:bg-gray-800 dark:border-gray-700">
                  {/* Date Range Section */}
                  <div className="px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Tarih Aralığı
                  </div>
                  <DropdownMenuItem onClick={() => setCalendarOpen(true)}>
                    <Calendar className="h-3.5 w-3.5 mr-2" />
                    Tarih Aralığı Seç
                    {(filters.dateRange?.from || filters.dateRange?.to) && (
                      <Badge className="ml-auto bg-blue-500 text-white text-xs">Aktif</Badge>
                    )}
                  </DropdownMenuItem>
                  {(filters.dateRange?.from || filters.dateRange?.to) && (
                    <DropdownMenuItem onClick={() => handleFilterChange('dateRange', undefined)}>
                      <X className="h-3.5 w-3.5 mr-2" />
                      Tarih Aralığını Temizle
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  {/* Sort Section */}
                  <div className="px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    Sıralama
                  </div>
                  <DropdownMenuItem onClick={() => handleFilterChange('sortDate', 'asc')}
                    className={filters.sortDate === 'asc' ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                  >
                    Eskiden Yeniye
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleFilterChange('sortDate', 'desc')}
                    className={filters.sortDate === 'desc' ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                  >
                    Yeniden Eskiye
                  </DropdownMenuItem>
                  {filters.sortDate && (
                    <DropdownMenuItem onClick={() => handleFilterChange('sortDate', null)}>
                      <X className="h-3.5 w-3.5 mr-2" />
                      Sıralamayı Temizle
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Test Type filter button */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                  >
                    <span className="flex items-center">
                      <Filter className="h-3.5 w-3.5 mr-1" />
                      Test Tipi
                      {filters.testType && filters.testType.length > 0 && (
                        <Badge className="ml-1 bg-blue-500 text-white">{filters.testType.length}</Badge>
                      )}
                    </span>
                    <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px] dark:bg-gray-800 dark:border-gray-700">
                  <div className="p-2 space-y-1">
                    {['web', 'mobile', 'api'].map((type) => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={`type-filter-${type}`}
                          checked={filters.testType?.includes(type)}
                          onCheckedChange={(checked) => {
                            // Tek bir test tipi seçimi için
                            // ScenarioManagement bileşeni sadece ilk elemanı kullanıyor
                            handleFilterChange('testType', checked ? [type] : []);
                          }}
                        />
                        <label
                          htmlFor={`type-filter-${type}`}
                          className="text-sm text-gray-700 dark:text-gray-300 capitalize"
                        >
                          {type}
                        </label>
                      </div>
                    ))}
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Status filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                  >
                    <span className="flex items-center">
                      <Filter className="h-3.5 w-3.5 mr-1" />
                      Durum
                      {filters.status && filters.status.length > 0 && (
                        <Badge className="ml-1 bg-blue-500 text-white">{filters.status.length}</Badge>
                      )}
                    </span>
                    <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px] dark:bg-gray-800 dark:border-gray-700">
                  <div className="p-2 space-y-1">
                    {['passed', 'failed', 'running', 'queue', 'active', 'inactive'].map((status) => (
                      <div key={status} className="flex items-center space-x-2">
                        <Checkbox
                          id={`status-filter-${status}`}
                          checked={filters.status?.includes(status)}
                          onCheckedChange={(checked) => {
                            const currentStatus = filters.status || [];
                            const newStatus = checked
                              ? [...currentStatus, status]
                              : currentStatus.filter(s => s !== status);
                            handleFilterChange('status', newStatus);
                          }}
                        />
                        <label
                          htmlFor={`status-filter-${status}`}
                          className="text-sm text-gray-700 dark:text-gray-300 capitalize"
                        >
                          {status}
                        </label>
                      </div>
                    ))}
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Tags filter */}
              {availableTags.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                    >
                      <span className="flex items-center">
                        <Tag className="h-3.5 w-3.5 mr-1" />
                        Etiket
                        {filters.tags && filters.tags.length > 0 && (
                          <Badge className="ml-1 bg-blue-500 text-white">{filters.tags.length}</Badge>
                        )}
                      </span>
                      <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[180px] max-h-[300px] overflow-y-auto dark:bg-gray-800 dark:border-gray-700">
                    <div className="p-2 space-y-1">
                      {availableTags.map((tag) => (
                        <div key={tag} className="flex items-center space-x-2">
                          <Checkbox
                            id={`tag-filter-${tag}`}
                            checked={filters.tags?.some(t =>
                              t.toLowerCase() === tag.toLowerCase()
                            )}
                            onCheckedChange={(checked) => {
                              const currentTags = filters.tags || [];
                              // Remove any case-insensitive matches first
                              const filteredTags = currentTags.filter(
                                t => t.toLowerCase() !== tag.toLowerCase()
                              );
                              const newTags = checked
                                ? [...filteredTags, tag]
                                : filteredTags;
                              handleFilterChange('tags', newTags);
                            }}
                          />
                          <label
                            htmlFor={`tag-filter-${tag}`}
                            className="text-sm text-gray-700 dark:text-gray-300"
                          >
                            {tag}
                          </label>
                        </div>
                      ))}
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Test Management Status */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                  >
                    <span className="flex items-center">
                      <FileCheck className="h-3.5 w-3.5 mr-1" />
                      Test Management
                      {filters.testrailStatus && (
                        <Badge className="ml-1 bg-blue-500 text-white">{filters.testrailStatus}</Badge>
                      )}
                    </span>
                    <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px] dark:bg-gray-800 dark:border-gray-700">
                  <DropdownMenuItem onClick={() => handleFilterChange('testrailStatus', 'active')}
                    className={filters.testrailStatus === 'active' ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                  >
                    Aktif
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleFilterChange('testrailStatus', 'inactive')}
                    className={filters.testrailStatus === 'inactive' ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                  >
                    Pasif
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleFilterChange('testrailStatus', null)}>
                    Filtreyi Temizle
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Date Range Popover */}
      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            <div className="text-sm font-medium mb-2">Tarih Aralığı Seç</div>
            <div className="flex gap-2 mb-2">
              <div>
                <div className="text-xs text-gray-500 mb-1">Başlangıç Tarihi</div>
                <div className="text-sm font-medium">
                  {tempDateRange.from ? format(tempDateRange.from, "MMM d, yyyy") : "Seç..."}
                </div>
              </div>
              <div className="text-gray-400 self-end mb-1">→</div>
              <div>
                <div className="text-xs text-gray-500 mb-1">Bitiş Tarihi</div>
                <div className="text-sm font-medium">
                  {tempDateRange.to ? format(tempDateRange.to, "MMM d, yyyy") : "Seç..."}
                </div>
              </div>
            </div>

            <CalendarComponent
              mode="range"
              selected={{
                from: tempDateRange.from,
                to: tempDateRange.to,
              }}
              onSelect={(range) => {
                setTempDateRange({
                  from: range?.from,
                  to: range?.to,
                })
              }}
              className="rounded-md border"
              initialFocus
            />

            <div className="flex justify-between mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={cancelCustomDateRange}
              >
                İptal
              </Button>
              <Button
                size="sm"
                onClick={applyCustomDateRange}
                disabled={!tempDateRange.from && !tempDateRange.to}
              >
                Uygula
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
