/**
 * Schedule Types
 * Type definitions for schedule-related functionality
 */

// Schedule Status Enum
export enum ScheduleStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  DELETED = 'deleted'
}

// Schedule Type Enum
export enum ScheduleType {
  ONCE = 'once',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  HOURLY = 'hourly'
}

// Hourly Interval Options
export enum HourlyInterval {
  ONE = 1,
  TWO = 2,
  FOUR = 4,
  EIGHT = 8,
  TWELVE = 12
}

// Days of the week for weekly schedules
export type WeekDay = 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';

// Notification settings
export interface ScheduleNotifications {
  email: boolean;
  slack: boolean;
  inApp: boolean;
  onSuccess: boolean;
  onFailure: boolean;
}

// Schedule interface
export interface Schedule {
  id: string;
  name: string;
  description?: string;
  scheduleType: ScheduleType;
  hourlyInterval?: HourlyInterval;
  startDate: string;
  startTime: string;
  endDate?: string;
  endTime?: string;
  timezone?: string; // Saat dilimi alanı eklendi
  repeatDays?: WeekDay[];
  runIds: string[];  // Changed from runId to runIds array
  userId: string;
  teamId?: string;
  companyId?: string;
  status: ScheduleStatus;
  createdAt: Date;
  updatedAt: Date;
  lastRunAt?: Date;
  nextRunAt?: Date;
  notifications?: ScheduleNotifications;
  emailRecipients?: string;
  slackChannel?: string;
}

// Schedule Run interface - simplified to only track schedule-run relationship
export interface ScheduleRun {
  id: string;
  scheduleId: string;
  runId: string;
  createdAt: Date;
  executionId?: string;
}

// Schedule creation request
export interface CreateScheduleRequest {
  name: string;
  description?: string;
  scheduleType: string;
  hourlyInterval?: number;
  startDate: string;
  startTime: string;
  endDate?: string;
  endTime?: string;
  timezone?: string; // Saat dilimi alanı eklendi
  repeatDays?: WeekDay[];
  runIds: string[];  // Changed from runId to runIds array
  userId: string;
  teamId?: string;
  companyId?: string;
  status?: ScheduleStatus;
  notifications?: ScheduleNotifications;
  emailRecipients?: string;
  slackChannel?: string;
}

// Schedule update request
export interface UpdateScheduleRequest {
  name?: string;
  description?: string;
  scheduleType?: string;
  hourlyInterval?: number;
  startDate?: string;
  startTime?: string;
  endDate?: string;
  endTime?: string;
  timezone?: string; // Saat dilimi alanı eklendi
  repeatDays?: WeekDay[];
  runIds?: string[];  // Changed from runId to runIds array
  status?: ScheduleStatus;
  notifications?: ScheduleNotifications;
  emailRecipients?: string;
  slackChannel?: string;
}

// Schedule service response
export interface ScheduleServiceResponse {
  success: boolean;
  message?: string;
  scheduleId?: string;
  schedule?: Schedule;
  schedules?: Schedule[];
}

// Schedule with run details
export interface ScheduleWithDetails extends Schedule {
  runs?: Array<{
    id: string;
    name: string;
    scenarioIds: string[];
    scenarioCount: number;
  }>;
  scenarios?: Array<{
    id: string;
    name: string;
  }>;
}
