"use client"

import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"

interface ThemeAnimationProps {
  isAnimating: boolean
  theme: string | undefined
  onAnimationComplete?: () => void
}

export function ThemeAnimation({ isAnimating, theme, onAnimationComplete }: ThemeAnimationProps) {
  const stripeCount = 5
  const stripes = Array.from({ length: stripeCount }, (_, i) => i)
  
  return (
    <AnimatePresence>
      {isAnimating && (
        <div className="fixed inset-0 z-50 overflow-hidden pointer-events-none">
          <div className="flex h-full">
            {stripes.map((i) => (
              <motion.div
                key={i}
                initial={{ 
                  scaleY: 0,
                  originY: theme === "dark" ? 1 : 0,
                }}
                animate={{ 
                  scaleY: 1,
                }}
                exit={{ 
                  scaleY: 0,
                  originY: theme === "dark" ? 0 : 1,
                  transition: { 
                    delay: 0.1 * i,
                    duration: 0.5
                  }
                }}
                transition={{ 
                  duration: 0.5,
                  delay: 0.1 * i,
                  ease: [0.22, 1, 0.36, 1]
                }}
                onAnimationComplete={() => {
                  if (i === stripeCount - 1 && onAnimationComplete) {
                    onAnimationComplete()
                  }
                }}
                className={`flex-1 ${theme === "dark" ? "bg-gray-900" : "bg-white"}`}
              />
            ))}
          </div>
        </div>
      )}
    </AnimatePresence>
  )
} 