/**
 * User Routes
 * User related endpoints
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import {
  getUserProfile,
  updateUserProfile
} from '../../services/mongo/userService.js';


const router = Router();

/**
 * Kullanıcı profil işlemleri
 */

// Kullanıcı profilini getir
router.get('/profile', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Profil bilgilerini getir
    const result = await getUserProfile(userId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      data: {
        profile: result.profile
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /users/profile: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Kullanıcı profilini güncelle
router.put('/profile', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Profili güncelle
    const result = await updateUserProfile(userId, req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Profile updated successfully'
      }
    });
  } catch (error: any) {
    logger.error(`API Error in PUT /users/profile: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;