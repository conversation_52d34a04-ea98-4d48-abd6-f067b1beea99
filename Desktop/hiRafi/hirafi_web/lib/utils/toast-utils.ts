/**
 * Toast deduplication utility to prevent duplicate toast notifications
 */

import { toast as sonnerToast } from "sonner"

// Store recent toast messages to prevent duplicates
const recentToasts = new Map<string, number>()
const TOAST_DEDUP_WINDOW = 3000 // 3 seconds

/**
 * Generate a key for toast deduplication based on type and message
 */
function generateToastKey(type: string, message: string): string {
  return `${type}:${message.toLowerCase().trim()}`
}

/**
 * Check if a toast with the same message was recently shown
 */
function isDuplicateToast(key: string): boolean {
  const now = Date.now()
  const lastShown = recentToasts.get(key)
  
  if (lastShown && (now - lastShown) < TOAST_DEDUP_WINDOW) {
    return true
  }
  
  return false
}

/**
 * Record that a toast was shown
 */
function recordToast(key: string): void {
  const now = Date.now()
  recentToasts.set(key, now)
  
  // Clean up old entries to prevent memory leaks
  for (const [storedKey, timestamp] of recentToasts.entries()) {
    if (now - timestamp > TOAST_DEDUP_WINDOW * 2) {
      recentToasts.delete(storedKey)
    }
  }
}

/**
 * Deduplicated toast functions that prevent showing the same message multiple times
 */
export const toast = {
  success: (message: string, options?: any) => {
    const key = generateToastKey('success', message)
    if (isDuplicateToast(key)) return
    
    recordToast(key)
    return sonnerToast.success(message, options)
  },
  
  error: (message: string, options?: any) => {
    const key = generateToastKey('error', message)
    if (isDuplicateToast(key)) return
    
    recordToast(key)
    return sonnerToast.error(message, options)
  },
  
  info: (message: string, options?: any) => {
    const key = generateToastKey('info', message)
    if (isDuplicateToast(key)) return
    
    recordToast(key)
    return sonnerToast.info(message, options)
  },
  
  warning: (message: string, options?: any) => {
    const key = generateToastKey('warning', message)
    if (isDuplicateToast(key)) return
    
    recordToast(key)
    return sonnerToast.warning(message, options)
  },
  
  // For custom toasts or when you want to bypass deduplication
  custom: (message: string, options?: any) => {
    return sonnerToast(message, options)
  },
  
  // Direct access to original sonner toast for special cases
  raw: sonnerToast
}

/**
 * Clear all recorded toasts (useful for testing or manual cleanup)
 */
export function clearToastHistory(): void {
  recentToasts.clear()
}
