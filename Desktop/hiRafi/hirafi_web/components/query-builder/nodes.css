.react-flow__node-insert,
.react-flow__node-update,
.react-flow__node-delete,
.query-builder-node {
  width: 300px !important;
  font-size: 12px;
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.dark .react-flow__node-insert,
.dark .react-flow__node-update,
.dark .react-flow__node-delete {
  background: #1a202c;
  border-color: #2d3748;
}

.dark .query-builder-node {
  background: #1a202c;
  border-color: #2d3748;
}

.node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.dark .node-header {
  background: #2d3748;
  border-color: #4a5568;
}

.node-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 600;
}

.node-body {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.node-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.node-section-label {
  font-size: 10px;
  font-weight: 600;
  color: #718096;
  text-transform: uppercase;
}

.dark .node-section-label {
  color: #a0aec0;
}

.node-row {
  display: flex;
  align-items: center;
  gap: 6px;
}

.node-row .react-select-trigger,
.node-row .react-input,
.node-row .btn {
  height: 30px !important;
  font-size: 12px;
  padding: 0 8px;
}

.node-row .btn[data-size="icon"] {
    width: 30px;
}

.react-flow__handle {
  width: 12px !important;
  height: 12px !important;
  background: #cbd5e1 !important;
  border: 2px solid #fff !important;
  transition: all 0.2s ease;
}

.dark .react-flow__handle {
  border-color: #2d3748 !important;
}

.react-flow__handle:hover {
  transform: scale(1.2);
  background: #6366f1 !important; /* Indigo-500 */
} 