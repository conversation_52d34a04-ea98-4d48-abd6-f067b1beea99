"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { useRouter } from "next/navigation"
import { toast } from "@/lib/utils/toast-utils"
import { useScenarioManager } from "@/hooks/useScenarioManager"
import { useScenarioStore } from "@/store/scenarioStore"
import { ModernLayout } from "./scenarios/modern-layout"
import { ModernTable } from "./scenarios/modern-table"
import { Button } from "@/components/ui/button"
import { Sidebar } from "@/components/sidebar/sidebar"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"
import dynamic from 'next/dynamic'

// Optimized lazy loading for better performance
import type { FolderType } from "@/types/scenario"
import type { ScenarioWithMeta } from "@/types/scenario-extended"


// Interface for export dialog props
interface ExportScenariosDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  scenarios: ScenarioWithMeta[];
  folders: FolderType[];
  selectedScenarios?: string[];
}

// Lazy load dialog components to prevent unnecessary API calls
const ExportScenariosDialog = dynamic<ExportScenariosDialogProps>(
  () => import('@/components/export-scenarios-dialog').then(mod => ({ default: mod.ExportScenariosDialog })),
  { ssr: false, loading: () => <div>Loading...</div> }
)

const DuplicateScenarioDialog = dynamic(
  () => import('@/components/duplicate-scenario-dialog').then(mod => ({ default: mod.DuplicateScenarioDialog })),
  { ssr: false, loading: () => <div>Loading...</div> }
)

const ImportScenariosDialog = dynamic(
  () => import('@/components/import-scenarios-dialog').then(mod => ({ default: mod.ImportScenariosDialog })),
  { ssr: false, loading: () => <div>Loading...</div> }
)

const BulkDuplicateScenarioDialog = dynamic(
  () => import('@/components/bulk-duplicate-scenario-dialog').then(mod => ({ default: mod.BulkDuplicateScenarioDialog })),
  { ssr: false, loading: () => <div>Loading...</div> }
)

const BulkDeleteScenarioDialog = dynamic(
  () => import('@/components/bulk-delete-scenario-dialog').then(mod => ({ default: mod.BulkDeleteScenarioDialog })),
  { ssr: false, loading: () => <div>Loading...</div> }
)

export function ScenarioManagement() {
  const router = useRouter()

  // Get UI state from Zustand store (only what's not in optimized manager)
  const {
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    tagFilter,
    setTagFilter,
    testTypeFilter,
    setTestTypeFilter,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    testManagementStatus,
    setTestManagementStatus,
    toggleOpenFolder,
    sidebarCollapsed,
    setSidebarCollapsed,
    clearFilters
  } = useScenarioStore()

  // We no longer need to debounce the search query here
  // SWR will handle the debouncing for us through its configuration

  // Get scenario and folder data with optimized hook
  const {
    scenarios,
    visibleScenarios,
    folders,
    scenariosLoading,
    foldersLoading,
    scenariosError,
    foldersError,
    deleteScenario: apiDeleteScenario,
    duplicateScenario,
    createFolder: apiCreateFolder,
    renameFolder,
    deleteFolder,
    refreshAllData, // Get refresh function from optimized manager
    selectedScenarios,
    selectScenario: handleSelectScenario,
    selectAllScenarios: handleSelectAllScenarios,
    deleteSelectedScenarios,
    duplicateSelectedScenarios,
    hasSelectedScenarios,
    allVisibleScenariosSelected,
    moveScenarioToFolder,
    selectFolder,
    selectedFolder,
    addSelectedScenario,
    removeSelectedScenario,
    clearSelectedScenarios
  } = useScenarioManager({
    autoFetch: true // Enable auto-fetch to ensure data is loaded
  })

  // Memoized computed values for performance
  const computedValues = useMemo(() => ({
    hasScenarios: scenarios.length > 0,
    hasFolders: folders.length > 0,
    hasVisibleScenarios: visibleScenarios.length > 0,
    isAnyScenarioSelected: selectedScenarios.length > 0,
    selectedCount: selectedScenarios.length,
    visibleCount: visibleScenarios.length
  }), [scenarios.length, folders.length, visibleScenarios.length, selectedScenarios.length])

  // Local state for UI
  const [draggedScenario, setDraggedScenario] = useState<string | null>(null)
  const [sortColumn, setSortColumn] = useState<string>("updatedAt")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")

  // State for dialogs
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false)
  const [scenarioToDelete, setScenarioToDelete] = useState<any | null>(null)
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false)
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false)
  const [duplicateDialogOpen, setDuplicateDialogOpen] = useState<boolean>(false)
  const [scenarioToDuplicate, setScenarioToDuplicate] = useState<ScenarioWithMeta | null>(null)
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState<boolean>(false)
  const [bulkDeleteInUseScenarios, setBulkDeleteInUseScenarios] = useState<Array<{
    scenarioId: string;
    inUseByRuns: Array<{id: string, name: string}>
  }>>([])
  const [bulkDeleteErrorMessage, setBulkDeleteErrorMessage] = useState<string | null>(null)
  
  // State for bulk duplicate dialog
  const [bulkDuplicateDialogOpen, setBulkDuplicateDialogOpen] = useState<boolean>(false)
  const [scenariosToDuplicate, setScenariosToDuplicate] = useState<string[]>([])
  
  // State for bulk delete confirmation dialog
  const [bulkDeleteConfirmDialogOpen, setBulkDeleteConfirmDialogOpen] = useState<boolean>(false)
  const [scenariosToDelete, setScenariosToDelete] = useState<string[]>([])
  
  // State for single duplicate confirmation dialog
  const [singleDuplicateDialogOpen, setSingleDuplicateDialogOpen] = useState<boolean>(false)
  const [singleScenarioToDuplicate, setSingleScenarioToDuplicate] = useState<ScenarioWithMeta | null>(null)

  // Extract all unique tags from scenarios
  const availableTags = useMemo(() => {
    return Array.from(
      new Set(
        scenarios
          .flatMap(scenario => scenario.tags || [])
          .filter(Boolean)
      )
    ).sort()
  }, [scenarios])

  // Optimized folder selection handler with pagination reset
  const handleSelectFolder = useCallback((folderId: string | null) => {
    selectFolder(folderId) // Use optimized manager's selectFolder

    // Reset to first page when changing folders - use requestAnimationFrame for better performance
    requestAnimationFrame(() => {
      const firstPageButton = document.querySelector('.pagination-first-page') as HTMLButtonElement
      if (firstPageButton) {
        firstPageButton.click()
      }
    })
  }, [selectFolder])

  // Handlers for drag and drop
  const handleDragStart = useCallback((scenarioId: string, _e: React.DragEvent) => {
    setDraggedScenario(scenarioId)
  }, [])

  const handleDragOver = useCallback((_folderId: string) => {
    // Drag over handler - no state update needed
  }, [])

  const handleDragLeave = useCallback(() => {
    // Drag leave handler - no state update needed
  }, [])

  const handleDrop = useCallback((folderId: string | null) => {
    if (draggedScenario && folderId !== undefined) {
      // Call the moveScenarioToFolder function to update the scenario's folder
      moveScenarioToFolder(draggedScenario, folderId)
        .then(() => {
          // Clear the dragged scenario state after the move
          setDraggedScenario(null)
        })
        .catch((error) => {
          console.error("Error moving scenario:", error)
          // Clear the dragged scenario state even if there's an error
          setDraggedScenario(null)
        })
    }
  }, [draggedScenario, moveScenarioToFolder])

  // Handlers for scenario operations
  const handleNewScenario = useCallback(() => {
    router.push("/scenarios/new")
  }, [router])

  const handleEditScenario = useCallback((id: string) => {
    router.push(`/scenarios/edit/${id}`)
  }, [router])

  const handleDuplicateScenario = useCallback((id: string) => {
    const scenario = scenarios.find(s => s.id === id)
    if (scenario) {
      setSingleScenarioToDuplicate(scenario)
      setSingleDuplicateDialogOpen(true)
    }
  }, [scenarios])

  // Handler for confirming single duplicate
  const handleSingleDuplicateConfirm = useCallback(async () => {
    if (singleScenarioToDuplicate) {
      setScenarioToDuplicate(singleScenarioToDuplicate)
      setDuplicateDialogOpen(true)
      setSingleDuplicateDialogOpen(false)
      setSingleScenarioToDuplicate(null)
    }
  }, [singleScenarioToDuplicate])

  const handleDeleteScenario = useCallback((id: string) => {
    const scenario = scenarios.find(s => s.id === id)
    if (scenario) {
      setScenarioToDelete(scenario)
      setDeleteDialogOpen(true)
    }
  }, [scenarios])

  const handleDuplicateSelectedScenarios = useCallback(async () => {
    if (selectedScenarios.length === 0) {
      toast.error("Lütfen kopyalanacak senaryoları seçin")
      return
    }

    try {
      let successCount = 0
      let errorCount = 0

      for (const scenarioId of selectedScenarios) {
        try {
          const result = await duplicateScenario(scenarioId, { namePrefix: "Copy" })
          if (result.success) {
            successCount++
          } else {
            errorCount++
          }
        } catch (error) {
          errorCount++
        }
      }

      if (successCount > 0) {
        toast.success(`${successCount} senaryo başarıyla kopyalandı`)
        handleSelectAllScenarios(false) // Clear selections using optimized manager
      }

      if (errorCount > 0) {
        toast.error(`${errorCount} senaryo kopyalanırken hata oluştu`)
      }
    } catch (error: any) {
      console.error("Bulk duplicate error:", error)
      toast.error("Senaryolar kopyalanırken bir hata oluştu")
    }
  }, [selectedScenarios, duplicateScenario, handleSelectAllScenarios])

  const [inUseByRuns, setInUseByRuns] = useState<Array<{id: string, name: string}>>([])
  const [deleteErrorMessage, setDeleteErrorMessage] = useState<string | null>(null)

  const handleDeleteConfirm = useCallback(async () => {
    if (!scenarioToDelete) return

    try {
      const response = await apiDeleteScenario(scenarioToDelete.id)

      if (response.success) {
        // Başarılı silme - tek toast göster
        toast.success(`"${scenarioToDelete.name}" senaryosu silindi`)

        // Dialog'u kapat ve state'i temizle
        setDeleteDialogOpen(false)
        setScenarioToDelete(null)
        setInUseByRuns([])
        setDeleteErrorMessage(null)

        // Seçili senaryolardan da kaldır
        removeSelectedScenario(scenarioToDelete.id)

      } else if (response.inUseByRuns && response.inUseByRuns.length > 0) {
        // Senaryo aktif run'larda kullanılıyor
        setInUseByRuns(response.inUseByRuns)
        setDeleteErrorMessage(response.error || "Bu senaryo aktif run'larda kullanıldığı için silinemiyor.")

      } else {
        // Diğer hata durumları
        setDeleteErrorMessage(response.error || "Senaryo silinirken hata oluştu")
        setInUseByRuns([])
        toast.error(response.error || "Senaryo silinirken hata oluştu")
      }

    } catch (error: any) {
      console.error("Delete scenario error:", error)

      // API error response kontrolü
      if (error.response?.data?.inUseByRuns) {
        setInUseByRuns(error.response.data.inUseByRuns)
        setDeleteErrorMessage(error.response.data.error || "Bu senaryo aktif run'larda kullanıldığı için silinemiyor.")
      } else {
        const errorMessage = error.message || "Senaryo silinirken hata oluştu"
        setDeleteErrorMessage(errorMessage)
        setInUseByRuns([])
        toast.error(errorMessage)
      }
    }
  }, [scenarioToDelete, apiDeleteScenario, removeSelectedScenario])

  // Note: handleSelectScenario and handleSelectAll are now provided by useScenarioManager

  // Handlers for sorting
  const handleSort = useCallback((column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortColumn(column)
      setSortDirection("asc")
    }
  }, [sortColumn, sortDirection])

  // Handlers for dialog operations
  const handleExportScenarios = useCallback(() => {
    setExportDialogOpen(true)
  }, [])

  const handleImportScenarios = useCallback(() => {
    setImportDialogOpen(true)
  }, [])

  // Optimized handler for bulk delete scenarios
  const handleBulkDeleteScenarios = useCallback(async (scenarioIds?: string[]) => {
    // Use selected scenarios if no specific IDs provided
    const idsToDelete = scenarioIds || selectedScenarios

    if (idsToDelete.length === 0) {
      toast.error("Lütfen silinecek senaryoları seçin")
      return
    }

    // Show bulk delete confirmation dialog
    setScenariosToDelete(idsToDelete)
    setBulkDeleteConfirmDialogOpen(true)
  }, [selectedScenarios])

  // Optimized handler for bulk delete success/result
  const handleBulkDeleteResult = useCallback(async (deletedCount: number, inUseScenarios?: any[]) => {
    // Clear selections and close dialogs
    setBulkDeleteConfirmDialogOpen(false)
    setScenariosToDelete([])

    // If there are scenarios that couldn't be deleted, show result dialog
    if (inUseScenarios && inUseScenarios.length > 0) {
      setBulkDeleteInUseScenarios(inUseScenarios)
      if (deletedCount > 0) {
        setBulkDeleteErrorMessage(`${deletedCount} senaryo silindi, ${inUseScenarios.length} senaryo aktif run'larda kullanıldığı için silinemedi.`)
      } else {
        setBulkDeleteErrorMessage("Seçilen senaryolar aktif run'larda kullanıldığı için silinemedi.")
      }
      setBulkDeleteDialogOpen(true)
    }

    // Clear all selections after bulk delete
    clearSelectedScenarios()
  }, [clearSelectedScenarios])

  // Optimized handler for bulk duplicate scenarios
  const handleBulkDuplicateScenarios = useCallback(async (scenarioIds?: string[], options?: { namePrefix?: string; folderId?: string }) => {
    // Use selected scenarios if no specific IDs provided
    const idsToDuplicate = scenarioIds || selectedScenarios

    if (idsToDuplicate.length === 0) {
      toast.error("Lütfen kopyalanacak senaryoları seçin")
      return
    }

    // Show comprehensive bulk duplicate dialog
    setScenariosToDuplicate(idsToDuplicate)
    setBulkDuplicateDialogOpen(true)
  }, [selectedScenarios])

  // Optimized handler for bulk duplicate success
  const handleBulkDuplicateSuccess = useCallback((duplicatedCount: number) => {
    setBulkDuplicateDialogOpen(false)
    setScenariosToDuplicate([])
    // Note: No need to call refreshAllData() as optimized manager handles cache invalidation automatically
    // Note: Selection clearing is handled by the optimized manager's bulk operations
  }, [])

  // Handler for filter changes from ModernTable
  const handleFilterChange = useCallback((filters: {
    search?: string;
    status?: string[];
    testType?: string[];
    tags?: string[];
    dateRange?: { from?: Date; to?: Date };
    testrailStatus?: 'active' | 'inactive' | null;
    sortDate?: 'asc' | 'desc' | null;
  }) => {
    console.log("Filter changed in ModernTable:", filters)

    // Check if this is a clear all filters operation (empty object)
    if (Object.keys(filters).length === 0) {
      console.log("ScenarioManagement: Clearing all filters")
      // Use the clearFilters function from the store to reset all filters
      clearFilters()
      // Return early to prevent further processing
      return
    }

    // Update search query if it changed
    if (filters.search !== undefined) {
      setSearchQuery(filters.search)
    }

    // Update date range if it changed
    if (filters.dateRange !== undefined) {
      if (filters.dateRange.from) {
        setStartDate(filters.dateRange.from.toISOString())
      } else {
        setStartDate(null)
      }

      if (filters.dateRange.to) {
        setEndDate(filters.dateRange.to.toISOString())
      } else {
        setEndDate(null)
      }
    }

    // Update status filter if it changed
    if (filters.status !== undefined) {
      // Convert array to single value for our state
      const newStatusFilter = filters.status && filters.status.length > 0 ? filters.status[0] : null
      setStatusFilter(newStatusFilter)
    }

    // Update test type filter if it changed
    if (filters.testType !== undefined) {
      // Convert array to single value for our state
      const newTestTypeFilter = filters.testType && filters.testType.length > 0 ? filters.testType[0] : null
      setTestTypeFilter(newTestTypeFilter)
    }

    // Update tag filter if it changed
    if (filters.tags !== undefined) {
      // Convert array to single value for our state
      const newTagFilter = filters.tags && filters.tags.length > 0 ? filters.tags[0] : null
      setTagFilter(newTagFilter)
    }

    // Update test management status filter if it changed
    if (filters.testrailStatus !== undefined) {
      setTestManagementStatus(filters.testrailStatus)
    }
  }, [setSearchQuery, setStatusFilter, setTestTypeFilter, setTagFilter, setStartDate, setEndDate, setTestManagementStatus, clearFilters])

  // Convert individual filter states to ScenarioFilters format for ModernTable
  const currentFilters = useMemo(() => {
    const dateRange = (startDate || endDate) ? {
      from: startDate ? new Date(startDate) : undefined,
      to: endDate ? new Date(endDate) : undefined
    } : undefined

    return {
      search: searchQuery || undefined,
      status: statusFilter ? [statusFilter] : undefined,
      testType: testTypeFilter ? [testTypeFilter] : undefined,
      tags: tagFilter ? [tagFilter] : undefined,
      dateRange,
      testrailStatus: testManagementStatus || undefined
    }
  }, [searchQuery, statusFilter, testTypeFilter, tagFilter, startDate, endDate, testManagementStatus])

  // Sort scenarios
  const sortedScenarios = useMemo(() => {
    return [...visibleScenarios].sort((a, b) => {
      let comparison = 0

      switch (sortColumn) {
        case "id":
          comparison = a.id.localeCompare(b.id)
          break
        case "name":
          comparison = a.name.localeCompare(b.name)
          break
        case "folderId":
          const folderA = folders.find(f => f.id === a.folderId)?.name || "Kategorisiz"
          const folderB = folders.find(f => f.id === b.folderId)?.name || "Kategorisiz"
          comparison = folderA.localeCompare(folderB)
          break
        case "status":
          const statusA = a.runStatus || a.status || "unknown"
          const statusB = b.runStatus || b.status || "unknown"
          comparison = statusA.localeCompare(statusB)
          break
        case "updatedAt":
        case "lastRun":
          // Try to get valid dates
          const dateA = a.updatedAt ? new Date(a.updatedAt).getTime() :
                       a.lastRun ? new Date(a.lastRun).getTime() : 0
          const dateB = b.updatedAt ? new Date(b.updatedAt).getTime() :
                       b.lastRun ? new Date(b.lastRun).getTime() : 0
          comparison = dateA - dateB
          break
        default:
          comparison = 0
      }

      return sortDirection === "asc" ? comparison : -comparison
    })
  }, [visibleScenarios, sortColumn, sortDirection, folders])

  // We no longer need a centralized data fetching control
  // SWR will handle all data fetching automatically based on the filters
  // The key will change when filters change, triggering a new fetch
  useEffect(() => {
    console.log("Component mounted - SWR will handle data fetching")

    // No need to call refreshAllData here since autoFetch is true
    // SWR will automatically fetch data on mount and when filters change

  }, []) // Empty dependency array means this only runs once on mount

  // Handle error states
  if (scenariosError || foldersError) {
    return (
      <div className="flex h-screen bg-white dark:bg-gray-950">
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md p-6">
            <div className="h-12 w-12 text-red-500 mx-auto mb-4">
              <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-gray-100">Failed to load data</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {scenariosError || foldersError || "There was an error loading the scenarios. Please try again."}
            </p>
            <Button onClick={() => refreshAllData()} className="mr-2">
              Try Again
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex h-screen bg-white dark:bg-gray-950">
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

        <div className="flex-1 h-full overflow-auto">
          <ModernLayout
            scenarios={scenarios}
            folders={folders}
            selectedFolder={selectedFolder}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            onSelectFolder={handleSelectFolder}
            onToggleFolder={toggleOpenFolder}
            onCreateFolder={(name) => apiCreateFolder({ name })}
            onRenameFolder={renameFolder}
            onDeleteFolder={deleteFolder}
            onDragScenario={handleDragStart}
            onDragOverFolder={handleDragOver}
            onDragLeaveFolder={handleDragLeave}
            onDropOnFolder={handleDrop}
            draggedScenario={draggedScenario}
            onNewScenario={handleNewScenario}
            onImportScenarios={handleImportScenarios}
            onExportScenarios={handleExportScenarios}
            isLoading={scenariosLoading || foldersLoading}
            selectedScenarios={selectedScenarios}
            onApplyStatusFilter={setStatusFilter}
            onApplyTestTypeFilter={setTestTypeFilter}
            onApplyTagFilter={setTagFilter}
            statusFilter={statusFilter}
            testTypeFilter={testTypeFilter}
            tagFilter={tagFilter}
            allTags={availableTags}
            onClearFilters={clearFilters}
          >
            <ModernTable
              scenarios={sortedScenarios as any}
              folders={folders}
              loading={scenariosLoading || foldersLoading}
              selectedScenarios={selectedScenarios}
              onSelectScenario={handleSelectScenario}
              onSelectAll={handleSelectAllScenarios}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
              onEditScenario={handleEditScenario}
              onDuplicateScenario={handleDuplicateScenario}
              onDeleteScenario={handleDeleteScenario}
              onDragScenario={handleDragStart}
              filters={currentFilters}
              onFilterChange={handleFilterChange}
              onAddScenario={handleNewScenario}
              onImportScenarios={handleImportScenarios}
              selectedFolder={selectedFolder}
              onClearSelectedFolder={() => selectFolder(null)}
              onBulkDeleteScenarios={handleBulkDeleteScenarios}
              onBulkDuplicateScenarios={handleBulkDuplicateScenarios}
            />
          </ModernLayout>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          setDeleteDialogOpen(open)
          if (!open) {
            setDeleteErrorMessage(null)
            setInUseByRuns([])
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Senaryoyu Sil</DialogTitle>
            <DialogDescription>
              {!deleteErrorMessage ? (
                "Bu işlem geri alınamaz. Bu senaryoyu silmek istediğinizden emin misiniz?"
              ) : (
                <span className="text-rose-500 font-medium">{deleteErrorMessage}</span>
              )}
            </DialogDescription>
          </DialogHeader>

          {scenarioToDelete && (
            <div className="p-4 border rounded-md bg-gray-50 dark:bg-gray-900 dark:border-gray-800">
              <p className="font-medium">{scenarioToDelete.name}</p>
              {scenarioToDelete.description && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{scenarioToDelete.description}</p>
              )}
            </div>
          )}

          {/* Show list of runs that are using the scenario */}
          {inUseByRuns.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-sm mb-2">Bu senaryo aşağıdaki aktif run'larda kullanılıyor:</p>
              <div className="max-h-40 overflow-y-auto border rounded-md p-2 bg-gray-50 dark:bg-gray-900 dark:border-gray-800">
                <ul className="list-disc pl-5 space-y-1">
                  {inUseByRuns.map(run => (
                    <li key={run.id} className="text-sm">
                      <button
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline focus:outline-none"
                        onClick={() => router.push(`/runs/edit/${run.id}`)}
                      >
                        {run.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Bu senaryoyu silebilmek için önce yukarıdaki run'lardan bu senaryoyu kaldırmalısınız.
                Run adına tıklayarak ilgili run'a gidebilirsiniz.
              </p>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false)
                setDeleteErrorMessage(null)
                setInUseByRuns([])
              }}
            >
              {inUseByRuns.length > 0 ? "Tamam" : "İptal"}
            </Button>
            {inUseByRuns.length === 0 && (
              <Button
                variant="destructive"
                onClick={handleDeleteConfirm}
              >
                Sil
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Export scenarios dialog */}
      {exportDialogOpen && (
        <ExportScenariosDialog
          open={exportDialogOpen}
          onOpenChange={setExportDialogOpen}
          selectedScenarios={selectedScenarios}
          scenarios={scenarios}
          folders={folders}
        />
      )}

      {/* Import scenarios dialog */}
      {importDialogOpen && (
        <ImportScenariosDialog
          open={importDialogOpen}
          onOpenChange={setImportDialogOpen}
          onImportComplete={() => {
            // Refresh scenarios after import
            refreshAllData()
          }}
        />
      )}

      {/* Duplicate scenario dialog */}
      {duplicateDialogOpen && scenarioToDuplicate && (
        <DuplicateScenarioDialog
          open={duplicateDialogOpen}
          onOpenChange={setDuplicateDialogOpen}
          scenarioId={scenarioToDuplicate.id}
          scenarioName={scenarioToDuplicate.name}
          onSuccess={() => {
            // Refresh scenarios after duplication
            refreshAllData()
          }}
        />
      )}

      {/* Bulk delete result dialog */}
      <Dialog
        open={bulkDeleteDialogOpen}
        onOpenChange={(open) => {
          setBulkDeleteDialogOpen(open)
          if (!open) {
            setBulkDeleteErrorMessage(null)
            setBulkDeleteInUseScenarios([])
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Senaryo Silme Sonucu</DialogTitle>
            <DialogDescription>
              {bulkDeleteErrorMessage && (
                <span className={bulkDeleteInUseScenarios.length > 0 ? "text-amber-500" : "text-rose-500"} style={{ fontWeight: 500 }}>
                  {bulkDeleteErrorMessage}
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          {/* Show list of scenarios that are in use by active runs */}
          {bulkDeleteInUseScenarios.length > 0 && (
            <div className="mt-4">
              <p className="font-medium text-sm mb-2">Aşağıdaki senaryolar aktif run'larda kullanıldığı için silinemedi:</p>
              <div className="max-h-60 overflow-y-auto border rounded-md p-2 bg-gray-50 dark:bg-gray-900 dark:border-gray-800">
                {bulkDeleteInUseScenarios.map(scenario => (
                  <div key={scenario.scenarioId} className="mb-3">
                    <p className="text-sm font-medium">Senaryo ID: {scenario.scenarioId}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Kullanıldığı run'lar:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      {scenario.inUseByRuns.map(run => (
                        <li key={run.id} className="text-xs text-gray-600 dark:text-gray-400">
                          <button
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline focus:outline-none"
                            onClick={() => router.push(`/runs/edit/${run.id}`)}
                          >
                            {run.name}
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Bu senaryoları silebilmek için önce yukarıdaki run'lardan bu senaryoları kaldırmalısınız.
                Run adına tıklayarak ilgili run'a gidebilirsiniz.
              </p>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setBulkDeleteDialogOpen(false)
                setBulkDeleteErrorMessage(null)
                setBulkDeleteInUseScenarios([])
              }}
            >
              Tamam
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Single duplicate confirmation dialog */}
      <Dialog
        open={singleDuplicateDialogOpen}
        onOpenChange={(open) => {
          setSingleDuplicateDialogOpen(open)
          if (!open) {
            setSingleScenarioToDuplicate(null)
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Senaryoyu Kopyala</DialogTitle>
            <DialogDescription>
              Bu senaryoyu kopyalamak istediğinizden emin misiniz?
              Kopyalanan senaryo "Copy" öneki ile oluşturulacaktır.
            </DialogDescription>
          </DialogHeader>

          {singleScenarioToDuplicate && (
            <div className="p-4 border rounded-md bg-gray-50 dark:bg-gray-900 dark:border-gray-800">
              <p className="font-medium">{singleScenarioToDuplicate.name}</p>
              {singleScenarioToDuplicate.description && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{singleScenarioToDuplicate.description}</p>
              )}
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setSingleDuplicateDialogOpen(false)
                setSingleScenarioToDuplicate(null)
              }}
            >
              İptal
            </Button>
            <Button
              onClick={handleSingleDuplicateConfirm}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Kopyala
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk duplicate dialog */}
      {bulkDuplicateDialogOpen && scenariosToDuplicate.length > 0 && (
        <BulkDuplicateScenarioDialog
          open={bulkDuplicateDialogOpen}
          onOpenChange={(open) => {
            setBulkDuplicateDialogOpen(open)
            if (!open) {
              setScenariosToDuplicate([])
            }
          }}
          scenarioIds={scenariosToDuplicate}
          onSuccess={handleBulkDuplicateSuccess}
        />
      )}

      {/* Bulk delete confirmation dialog */}
      {bulkDeleteConfirmDialogOpen && scenariosToDelete.length > 0 && (
        <BulkDeleteScenarioDialog
          open={bulkDeleteConfirmDialogOpen}
          onOpenChange={(open) => {
            setBulkDeleteConfirmDialogOpen(open)
            if (!open) {
              setScenariosToDelete([])
            }
          }}
          scenarioIds={scenariosToDelete}
          scenarios={scenarios.filter(s => scenariosToDelete.includes(s.id)).map(s => ({ id: s.id, name: s.name }))}
          onSuccess={handleBulkDeleteResult}
        />
      )}
    </>
  )
}
