import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import { Pause, Play, AlertCircle, CheckCircle } from "lucide-react";
import { statusApi } from '@/lib/api';
import { useInterval } from '@/hooks/useInterval';

/**
 * System Pause Control Component
 * Allows admins to pause/resume the entire system or specific nodes
 */
const SystemPauseControl: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemStatus, setSystemStatus] = useState<any>(null);
  const [pausingSystem, setPausingSystem] = useState(false);
  const [resumingSystem, setResumingSystem] = useState(false);
  const [pausingNode, setPausingNode] = useState<string | null>(null);
  const [resumingNode, setResumingNode] = useState<string | null>(null);

  // Fetch system status
  const fetchSystemStatus = async () => {
    try {
      setError(null);
      const response = await statusApi.getSystemStatus();

      if (response.success) {
        setSystemStatus(response);
      } else {
        setError(response.error || 'Failed to fetch system status');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while fetching system status');
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchSystemStatus();
  }, []);

  // Refresh status every 10 seconds
  useInterval(() => {
    fetchSystemStatus();
  }, 10000);

  // Pause the entire system
  const handlePauseSystem = async () => {
    try {
      setPausingSystem(true);
      setError(null);

      const response = await statusApi.pauseSystem();

      if (response.success) {
        // Refresh status
        await fetchSystemStatus();
      } else {
        setError(response.error || 'Failed to pause system');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while pausing the system');
    } finally {
      setPausingSystem(false);
    }
  };

  // Resume the entire system
  const handleResumeSystem = async () => {
    try {
      setResumingSystem(true);
      setError(null);

      const response = await statusApi.resumeSystem();

      if (response.success) {
        // Refresh status
        await fetchSystemStatus();
      } else {
        setError(response.data?.error || response.error || 'Failed to resume system');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred while resuming the system');
    } finally {
      setResumingSystem(false);
    }
  };

  // Pause a specific node
  const handlePauseNode = async (nodeId: string) => {
    try {
      setPausingNode(nodeId);
      setError(null);

      const response = await statusApi.pauseNode(nodeId);

      if (response.success) {
        // Refresh status
        await fetchSystemStatus();
      } else {
        setError(response.error || `Failed to pause node ${nodeId}`);
      }
    } catch (err: any) {
      setError(err.message || `An error occurred while pausing node ${nodeId}`);
    } finally {
      setPausingNode(null);
    }
  };

  // Resume a specific node
  const handleResumeNode = async (nodeId: string) => {
    try {
      setResumingNode(nodeId);
      setError(null);

      const response = await statusApi.resumeNode(nodeId);

      if (response.success) {
        // Refresh status
        await fetchSystemStatus();
      } else {
        setError(response.error || `Failed to resume node ${nodeId}`);
      }
    } catch (err: any) {
      setError(err.message || `An error occurred while resuming node ${nodeId}`);
    } finally {
      setResumingNode(null);
    }
  };

  // Toggle node pause status
  const handleToggleNode = (nodeId: string, isPaused: boolean) => {
    if (isPaused) {
      handleResumeNode(nodeId);
    } else {
      handlePauseNode(nodeId);
    }
  };

  if (loading) {
    return (
      <Card className="mb-4">
        <CardContent className="pt-6">
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-2">Loading system status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const isSystemPaused = systemStatus?.system?.paused;
  const nodes = systemStatus?.nodes || [];
  const queueStatus = systemStatus?.queues || {};

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* System Pause Control */}
      <Card>
        <CardHeader>
          <CardTitle>System Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center">
            <span className="mr-2">System Status:</span>
            <div className="flex items-center">
              {isSystemPaused ? (
                <>
                  <div className="h-2 w-2 rounded-full bg-red-500 mr-2"></div>
                  <span>Paused</span>
                </>
              ) : (
                <>
                  <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                  <span>Running</span>
                </>
              )}
            </div>
          </div>

          <div className="flex space-x-2">
            <Button
              variant={isSystemPaused ? "outline" : "destructive"}
              onClick={handlePauseSystem}
              disabled={isSystemPaused || pausingSystem}
              className="flex items-center"
            >
              {pausingSystem ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
              ) : (
                <Pause className="mr-2 h-4 w-4" />
              )}
              Pause System
            </Button>

            <Button
              variant="default"
              onClick={handleResumeSystem}
              disabled={!isSystemPaused || resumingSystem}
              className="flex items-center"
            >
              {resumingSystem ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
              ) : (
                <Play className="mr-2 h-4 w-4" />
              )}
              Resume System
            </Button>
          </div>

          <p className="text-sm text-muted-foreground">
            When the system is paused, all tests will be delayed until the system is resumed.
            Existing tests will complete, but new tests won't start.
          </p>
        </CardContent>
      </Card>

      {/* Queue Status */}
      <Card>
        <CardHeader>
          <CardTitle>Queue Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div>Tests in Queue: {queueStatus.tests?.queued || 0}</div>
          <div>Running Tests: {queueStatus.tests?.running || 0}</div>
        </CardContent>
      </Card>

      {/* Node Pause Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Node Status</CardTitle>
        </CardHeader>
        <CardContent>
          {nodes.length === 0 ? (
            <p className="text-sm text-muted-foreground">No nodes connected</p>
          ) : (
            <div className="space-y-4">
              {nodes.map((node: any) => (
                <Card key={node.id} className="border">
                  <CardHeader className="py-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-sm font-medium">
                        {node.name} ({node.id.substring(0, 8)})
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div>
                              <Switch
                                checked={!node.isPaused}
                                onCheckedChange={() => handleToggleNode(node.id, node.isPaused)}
                                disabled={isSystemPaused || pausingNode === node.id || resumingNode === node.id}
                              />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{node.isPaused ? 'Resume Node' : 'Pause Node'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="py-2 space-y-2">
                    <div className="flex items-center">
                      <span className="text-sm mr-2">Status:</span>
                      <div className="flex items-center">
                        <div className={`h-2 w-2 rounded-full ${node.status === 'available' ? 'bg-green-500' : 'bg-blue-500'} mr-2`}></div>
                        <span className="text-sm">{node.status}</span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm mr-2">Pause Status:</span>
                      <div className="flex items-center">
                        <div className={`h-2 w-2 rounded-full ${node.isPaused ? 'bg-red-500' : 'bg-green-500'} mr-2`}></div>
                        <span className="text-sm">{node.isPaused ? 'Paused' : 'Running'}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemPauseControl;
