/**
 * HTTP Registration Service
 * 
 * Handles HTTP-based node registration with dependency injection.
 * Replaces the singleton HttpRegistrationService with a proper service implementation.
 */

import { BaseService } from '../base/base-service.js';
import { IHttpRegistrationService } from '../../interfaces/http-connector.interface.js';
import { IRegistrationService } from '../../interfaces/registration-service.interface.js';
import { IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';

/**
 * HTTP Registration Service Class
 * Implements HTTP-specific registration functionality with dependency injection
 */
export class HttpRegistrationService extends BaseService implements IHttpRegistrationService {
  private hubUrl: string = '';
  private nodeSecretKey: string = '';

  constructor(
    logger: any,
    config: IServiceConfig,
    private registrationService: IRegistrationService
  ) {
    super('HttpRegistrationService', logger, config);
  }

  /**
   * Initialize the service
   * @param config Service configuration
   */
  protected async doInitialize(config: IServiceConfig): Promise<void> {
    this.validateConfig(config, ['hubUrl', 'secretKey']);
    
    this.hubUrl = config.hubUrl!;
    this.nodeSecretKey = config.secretKey!;
    
    this.logger.debug('HttpRegistrationService: Initialized with hub URL:', this.hubUrl);
  }

  /**
   * Close the service
   */
  protected async doClose(): Promise<void> {
    this.hubUrl = '';
    this.nodeSecretKey = '';
    this.logger.debug('HttpRegistrationService: Closed successfully');
  }

  /**
   * Initialize the service (backward compatibility)
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  async initialize(config?: IServiceConfig): Promise<void>;
  async initialize(hubUrl: string, nodeSecretKey: string): Promise<void>;
  async initialize(configOrHubUrl?: IServiceConfig | string, nodeSecretKey?: string): Promise<void> {
    if (typeof configOrHubUrl === 'string') {
      // Legacy signature
      this.hubUrl = configOrHubUrl;
      this.nodeSecretKey = nodeSecretKey!;
      await super.initialize({ hubUrl: this.hubUrl, secretKey: this.nodeSecretKey });
    } else {
      // New signature
      await super.initialize(configOrHubUrl);
    }
  }

  /**
   * Initialize the service (backward compatibility)
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  async initializeLegacy(hubUrl: string, nodeSecretKey: string): Promise<void> {
    await super.initialize({
      hubUrl,
      secretKey: nodeSecretKey
    });
  }

  /**
   * Register node with hub via HTTP
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Optional client ID
   */
  async registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null> {
    try {
      // Generate a fresh AI-themed random name instead of using the provided name
      const { NodeIdentityService } = await import('../../../node-manager/services/identity/node-identity-service.js');
      const identityManager = new NodeIdentityService();
      const randomName = identityManager.generateAIThemedName();

      this.registrationService.logRegistrationAttempt(randomName, capabilities, 'HTTP');

      const registrationData = this.registrationService.prepareRegistrationData(
        randomName,
        capabilities,
        clientId
      );

      const headers = this.registrationService.createRegistrationHeaders(this.nodeSecretKey);

      this.logOperation('httpRegistrationRequest', {
        url: `${this.hubUrl}/api/nodes`,
        name: randomName,
        capabilities
      });

      const response = await fetch(`${this.hubUrl}/api/nodes`, {
        method: 'POST',
        headers,
        body: JSON.stringify(registrationData),
      });

      if (!response.ok) {
        throw new Error(`Failed to register with hub: ${response.statusText}`);
      }

      const data = await response.json();

      // Import validation function
      const { isNodeRegistrationResponse } = await import('../../../../models/types.js');
      
      if (!this.registrationService.validateRegistrationResponse(data, isNodeRegistrationResponse)) {
        throw new Error('Unexpected response format from hub registration');
      }

      const nodeId = this.registrationService.determineNodeId(undefined, data.nodeId);

      if (nodeId) {
        this.registrationService.handleRegistrationSuccess(nodeId);
        this.logOperation('httpRegistrationSuccess', { nodeId });
        return nodeId;
      }

      throw new Error('Failed to determine valid node ID');
    } catch (error) {
      this.registrationService.handleRegistrationFailure(error, 'HTTP');
      this.handleError(error, 'registerNode');
      return null;
    }
  }

  /**
   * Close the service
   */
  async close(): Promise<void> {
    // HTTP registration service doesn't need special cleanup
    this.hubUrl = '';
    this.nodeSecretKey = '';
  }

  /**
   * Get service statistics
   */
  getStats(): IServiceStats {
    return super.getStats();
  }

  /**
   * Get detailed HTTP registration statistics
   */
  getDetailedStats(): Record<string, any> {
    const baseStats = super.getStats();
    return {
      ...baseStats,
      hubUrl: this.hubUrl,
      initialized: !!this.hubUrl,
      registrationServiceStats: this.registrationService.getStats()
    };
  }
}
