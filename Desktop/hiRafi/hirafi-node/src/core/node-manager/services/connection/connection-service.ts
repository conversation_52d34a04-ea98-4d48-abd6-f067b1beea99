/**
 * Connection Service
 * 
 * Handles WebSocket and Redis connections with dependency injection.
 * Replaces the singleton ConnectionManager with a proper service implementation.
 */

import { config } from '../../../../config/index.js';
import { ConnectorFactory } from '../../../connectors/factories/connector-factory.js';
import { IWebSocketConnector } from '../../../connectors/interfaces/websocket-connector.interface.js';
import { RedisConnector } from '../../../connectors/redis/redis-connector.js';
import { EventEmitter } from 'events';
import { IConnectionService } from '../../interfaces/connection-service.interface.js';
import { INodeIdentityService } from '../../interfaces/node-identity-service.interface.js';
import { ConnectionStatus, ConnectionType } from '../../types/node-manager-types.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

export class ConnectionService implements IConnectionService {
  private readonly WEBSOCKET_CONNECT_TIMEOUT = 60000; // 60 seconds
  private readonly MAX_REGISTRATION_RETRIES = 3; // Add retry limit
  private readonly RETRY_DELAY = 5000; // 5 seconds between retries
  private websocketConnector: IWebSocketConnector | null = null;
  private redisConnector: RedisConnector | null = null;
  private logger: ILoggerService;
  private identityService: INodeIdentityService;
  private nodeEvents: EventEmitter;

  // Single source of truth for registration state
  private registrationState: 'idle' | 'registering' | 'registered' | 'failed' = 'idle';
  private registrationPromise: Promise<string> | null = null;
  private registrationAttempts: number = 0; // Track retry attempts

  constructor(
    identityService: INodeIdentityService,
    nodeEvents: EventEmitter
  ) {
    this.logger = LoggerServiceFactory.createServiceLogger('ConnectionService');
    this.identityService = identityService;
    this.nodeEvents = nodeEvents;
    this.logger.debug('ConnectionService: Initialized with new dependency injection architecture');
  }

  /**
   * Initialize all connections - SINGLE REGISTRATION FLOW
   */
  async initialize(): Promise<boolean> {
    // SINGLE REGISTRATION AUTHORITY: Only WebSocket registration
    // Redis initialization happens AFTER WebSocket registration succeeds

    if (!config.websocketEnabled) {
      this.logger.error('ConnectionService: WebSocket is required for node registration and is disabled');
      return false;
    }

    // Initialize WebSocket connection and registration (single truth source)
    const wsSuccess = await this.initializeWebSocket();
    if (!wsSuccess) {
      this.logger.error('ConnectionService: WebSocket initialization failed');
      return false;
    }

    // Redis is initialized automatically in performSingleRegistration if enabled
    // No separate Redis initialization here to prevent race conditions

    // Log connection summary
    const nodeId = this.identityService.getNodeId() || 'unknown';
    const redisSuccess = this.isRedisConnected();
    this.logger.connectionSummary(nodeId, { websocket: wsSuccess, redis: redisSuccess });

    this.logger.info('ConnectionService: Single registration flow completed successfully');
    return true;
  }

  /**
   * Initialize WebSocket connection - SINGLE REGISTRATION AUTHORITY with retry logic
   */
  async initializeWebSocket(): Promise<boolean> {
    // Reset attempts counter for new initialization
    this.registrationAttempts = 0;
    
    return this.attemptWebSocketRegistration();
  }

  /**
   * Attempt WebSocket registration with retry logic
   */
  private async attemptWebSocketRegistration(): Promise<boolean> {
    try {
      this.registrationAttempts++;
      
      this.logger.info(`ConnectionService: Registration attempt ${this.registrationAttempts}/${this.MAX_REGISTRATION_RETRIES}`);

      // Prevent concurrent registrations
      if (this.registrationState === 'registering' && this.registrationAttempts === 1) {
        this.logger.debug('ConnectionService: Registration already in progress, waiting...');
        if (this.registrationPromise) {
          const result = await this.registrationPromise;
          // Check if registration was successful by checking the result
          return result !== null && result !== undefined;
        }
      }

      // If already registered, return success
      if (this.registrationState === 'registered') {
        this.logger.debug('ConnectionService: Already registered, skipping');
        return true;
      }

      this.registrationState = 'registering';

      const nodeId = this.identityService.getNodeId();
      const clientId = this.identityService.getClientId();

      // Create WebSocket connector using proper factory pattern
      if (!this.websocketConnector) {
        const factory = new ConnectorFactory();
        this.websocketConnector = await factory.createConnector('websocket', {
          nodeId: nodeId || undefined,
          clientId,
          enableLogging: false,
          hubUrl: config.hubUrl,
          secretKey: config.nodeSecretKey
        }) as IWebSocketConnector;
      }

      // Create single registration promise to prevent race conditions
      this.registrationPromise = this.performSingleRegistration(nodeId, clientId);

      const registeredNodeId = await this.registrationPromise;

      if (registeredNodeId) {
        await this.identityService.updateNodeId(registeredNodeId);
        this.registrationState = 'registered';
        this.registrationAttempts = 0; // Reset on success
        this.logger.info(`ConnectionService: Registration completed successfully: ${registeredNodeId}`);
        return true;
      } else {
        throw new Error('Registration returned null node ID');
      }
    } catch (error) {
      this.logger.error(`ConnectionService: Registration attempt ${this.registrationAttempts} failed: ${error}`);
      
      // Retry logic
      if (this.registrationAttempts < this.MAX_REGISTRATION_RETRIES) {
        this.logger.warn(`ConnectionService: Retrying registration in ${this.RETRY_DELAY}ms (attempt ${this.registrationAttempts + 1}/${this.MAX_REGISTRATION_RETRIES})`);
        
        // Reset state for retry
        this.registrationState = 'idle';
        this.registrationPromise = null;
        
        // Close existing connector to ensure clean retry
        if (this.websocketConnector) {
          try {
            await this.websocketConnector.close();
            this.websocketConnector = null;
          } catch (closeError) {
            this.logger.warn(`ConnectionService: Error closing connector for retry: ${closeError}`);
          }
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY));
        
        // Recursive retry
        return this.attemptWebSocketRegistration();
      } else {
        // Max retries exceeded
        this.registrationState = 'failed';
        this.logger.error(`ConnectionService: Registration failed after ${this.MAX_REGISTRATION_RETRIES} attempts`);
        this.logger.error('ConnectionService: Possible solutions:');
        this.logger.error('  1. Check hub status and availability');
        this.logger.error('  2. Verify network connectivity');
        this.logger.error('  3. Check configuration: hubUrl, nodeSecretKey');
        this.logger.error('  4. Try restarting the hub service');
        return false;
      }
    } finally {
      this.registrationPromise = null;
    }
  }

  /**
   * Initialize Redis connection - ONLY AFTER WEBSOCKET REGISTRATION
   */
  async initializeRedis(): Promise<boolean> {
    try {
      // Ensure WebSocket registration is completed first
      if (this.registrationState !== 'registered') {
        this.logger.warn('ConnectionService: Cannot initialize Redis - WebSocket registration not completed');
        return false;
      }

      const nodeId = this.identityService.getNodeId();
      if (!nodeId) {
        this.logger.error('ConnectionService: Cannot initialize Redis - no node ID available');
        return false;
      }

      // Redis will be initialized for queues only (no registration flag needed)

      // Close any existing Redis connection first
      if (this.redisConnector) {
        await this.redisConnector.close().catch(() => {});
      }

      // Create and initialize Redis connector
      this.redisConnector = new RedisConnector();
      const redisSuccess = await this.redisConnector.initializeRedis(nodeId);

      if (redisSuccess) {
        // Redis initialized for queues only (NO node registration)
        this.logger.info('ConnectionService: Redis queues initialized (no node registration)');
        return true;
      } else {
        this.logger.warn('ConnectionService: Failed to initialize Redis client');
        return false;
      }
    } catch (error) {
      this.logger.error(`ConnectionService: Error initializing Redis: ${error}`);
      return false;
    }
  }

  /**
   * Get connection status for all or specific connection
   */
  getConnectionStatus(type?: ConnectionType): ConnectionStatus | boolean {
    const websocketStatus = config.websocketEnabled &&
                           this.websocketConnector &&
                           this.websocketConnector.isConnected();

    const redisStatus = config.redisEnabled && this.redisConnector ? this.redisConnector.isConnected() : false;

    if (type === ConnectionType.WEBSOCKET) {
      return websocketStatus || false;
    }

    if (type === ConnectionType.REDIS) {
      return redisStatus;
    }

    return {
      websocket: websocketStatus || false,
      redis: redisStatus || false,
      overall: (websocketStatus || false) && (!config.redisEnabled || (redisStatus || false))
    };
  }

  /**
   * Check if WebSocket is connected
   */
  public isWebSocketConnected(): boolean {
    return this.websocketConnector?.isConnected() ?? false;
  }

  /**
   * Get the underlying WebSocket connector instance
   */
  public getWebSocketConnector(): IWebSocketConnector | null {
    return this.websocketConnector;
  }

  /**
   * Check if Redis is connected
   */
  isRedisConnected(): boolean {
    return this.getConnectionStatus(ConnectionType.REDIS) as boolean;
  }

  /**
   * Reconnect all failed connections
   */
  async reconnectAll(): Promise<void> {
    this.logger.info('ConnectionService: Reconnecting all failed connections');

    if (!this.isWebSocketConnected()) {
      await this.reconnect(ConnectionType.WEBSOCKET);
    }

    if (config.redisEnabled && !this.isRedisConnected()) {
      await this.reconnect(ConnectionType.REDIS);
    }
  }

  /**
   * Reconnect specific connection type - MAINTAIN TRUTH OF SOURCE
   */
  async reconnect(type: ConnectionType): Promise<boolean> {
    this.logger.info(`ConnectionService: Reconnecting ${type} with truth of source consistency`);

    if (type === ConnectionType.WEBSOCKET) {
      // CRITICAL FIX: Don't completely reset registration state, allow re-registration
      this.logger.info('ConnectionService: Preparing for WebSocket reconnection and re-registration');
      
      // Preserve existing nodeId if available for smoother re-registration
      const existingNodeId = this.identityService.getNodeId();
      
      if (existingNodeId) {
        this.logger.info(`ConnectionService: Preserving existing nodeId for reconnection: ${existingNodeId}`);
      }

      // Reset registration state to allow re-registration
      this.registrationState = 'idle';
      this.registrationPromise = null;

      // CRITICAL FIX: Ensure automatic re-registration after reconnection
      const success = await this.initializeWebSocket();
      
      if (success) {
        this.logger.info('ConnectionService: WebSocket reconnection successful, registration should be automatic');
      } else {
        this.logger.error('ConnectionService: WebSocket reconnection failed');
      }
      
      return success;
    } else if (type === ConnectionType.REDIS) {
      return await this.initializeRedis();
    }

    return false;
  }

  /**
   * Handle registration required from hub - CENTRALIZED RE-REGISTRATION
   */
  async handleRegistrationRequired(): Promise<boolean> {
    this.logger.warn('ConnectionService: Hub requested re-registration, initiating controlled re-registration');

    try {
      // Reset registration state to allow re-registration
      this.registrationState = 'idle';
      this.registrationPromise = null;

      // Perform re-registration through WebSocket reconnection
      const success = await this.reconnect(ConnectionType.WEBSOCKET);

      if (success) {
        this.logger.info('ConnectionService: Re-registration completed successfully');
      } else {
        this.logger.error('ConnectionService: Re-registration failed');
      }

      return success;
    } catch (error) {
      this.logger.error(`ConnectionService: Error during re-registration: ${error}`);
      return false;
    }
  }

  /**
   * Close all connections
   */
  async close(): Promise<void> {
    this.logger.info('ConnectionService: Closing all connections');

    try {
      if (this.websocketConnector) {
        await this.websocketConnector.close();
        this.websocketConnector = null;
      }
    } catch (error) {
      this.logger.warn(`ConnectionService: Error closing WebSocket: ${error}`);
    }

    try {
      if (this.redisConnector) {
        await this.redisConnector.close();
        this.redisConnector = null;
      }
    } catch (error) {
      this.logger.warn(`ConnectionService: Error closing Redis: ${error}`);
    }

    // Dispose connector manager if needed
    try {
      await ConnectorFactory.disposeAll();
    } catch (error) {
      this.logger.warn(`ConnectionService: Error disposing connector factory: ${error}`);
    }
  }

  /**
   * Initialize Redis for queues only (NO node registration)
   */
  async initializeRedisQueuesOnly(): Promise<boolean> {
    try {
      if (!config.redisEnabled) {
        this.logger.debug('ConnectionService: Redis disabled, skipping queue initialization');
        return false;
      }

      const nodeId = this.identityService.getNodeId();
      if (!nodeId) {
        this.logger.warn('ConnectionService: Cannot initialize Redis queues - no node ID assigned yet');
        return false;
      }

      // Close any existing Redis connection first
      if (this.redisConnector) {
        await this.redisConnector.close().catch(() => {});
      }

      // Create and initialize Redis connector for queues only
      this.redisConnector = new RedisConnector();
      const redisSuccess = await this.redisConnector.initializeRedis(nodeId);

      if (redisSuccess) {
        this.logger.info('ConnectionService: Redis queues initialized successfully (no node registration)');
        return true;
      } else {
        this.logger.warn('ConnectionService: Failed to initialize Redis queues');
        return false;
      }
    } catch (error) {
      this.logger.error(`ConnectionService: Error initializing Redis queues: ${error}`);
      return false;
    }
  }

  /**
   * Get registration state
   */
  getRegistrationState(): string {
    return this.registrationState;
  }

  /**
   * Reset registration state
   */
  resetRegistrationState(): void {
    this.registrationState = 'idle';
    this.registrationPromise = null;
    this.logger.debug('ConnectionService: Registration state reset to idle');
  }

  /**
   * Perform single registration - TRUTH SOURCE
   */
  private async performSingleRegistration(nodeId: string | null, clientId: string): Promise<string> {
    this.logger.info('ConnectionService: Starting single registration process...');

    if (!this.websocketConnector) {
      throw new Error('WebSocket connector not initialized');
    }

    // Connect to WebSocket server
    await this.websocketConnector.connect();

    // Wait for connection to be established
    await this.waitForWebSocketConnection();

    // Generate a fresh AI-themed random name for registration
    const randomName = this.identityService.generateAIThemedName();

    // Send registration message after connection is established
    this.logger.info('ConnectionService: WebSocket connected, sending registration message...');
    const registrationResult = await this.websocketConnector.registerNode(
      randomName,
      config.capabilities,
      clientId
    );

    // Always wait for the actual registration response, ignore the "pending" return value
    if (registrationResult === 'pending') {
      this.logger.debug('ConnectionService: Registration message sent, waiting for hub response...');
    } else if (registrationResult === null) {
      throw new Error('Failed to send registration message');
    }

    // Wait for WebSocket registration to complete - this is the ONLY source of truth for node ID
    const finalNodeId = await this.waitForWebSocketRegistration();

    // After successful WebSocket registration, initialize Redis if enabled (WITHOUT registration)
    if (finalNodeId && config.redisEnabled) {
      this.logger.info('ConnectionService: WebSocket registration successful, now initializing Redis for queues only...');
      // Update node ID first
      await this.identityService.updateNodeId(finalNodeId);
      // Set registration state to registered BEFORE Redis initialization
      this.registrationState = 'registered';

      // PHASE 5 FIX: Removed event emission - now using explicit sequential initialization
      // Event-based timing was causing race conditions with TestQueueWorker
      // this.nodeEvents.emit('registration-success', finalNodeId);

      // Initialize Redis for queues only (NO node registration)
      await this.initializeRedisQueuesOnly();
    }

    return finalNodeId;
  }

  /**
   * Wait for WebSocket connection to be established
   */
  private async waitForWebSocketConnection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (!this.websocketConnector) {
        reject(new Error('WebSocket connector not initialized'));
        return;
      }

      // Check if already connected
      if (this.websocketConnector.isConnected()) {
        this.logger.debug('ConnectionService: WebSocket already connected');
        resolve();
        return;
      }

      const connectionTimeout = setTimeout(() => {
        this.logger.error(`ConnectionService: WebSocket connection timeout after ${this.WEBSOCKET_CONNECT_TIMEOUT}ms`);
        reject(new Error('WebSocket connection timeout'));
      }, this.WEBSOCKET_CONNECT_TIMEOUT);

      const connectedHandler = () => {
        clearTimeout(connectionTimeout);
        this.logger.debug('ConnectionService: WebSocket connection established');
        resolve();
      };

      this.websocketConnector.once('connected', connectedHandler);
    });
  }

  /**
   * Wait for WebSocket registration - SINGLE TRUTH SOURCE
   */
  private async waitForWebSocketRegistration(): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      if (!this.websocketConnector) {
        reject(new Error('WebSocket connector not initialized'));
        return;
      }

      // Enhanced diagnostic logging
      this.logger.info(`ConnectionService: Waiting for registration response from hub (timeout: ${this.WEBSOCKET_CONNECT_TIMEOUT}ms)`);
      this.logger.info(`ConnectionService: Hub URL: ${config.hubUrl}`);
      this.logger.info(`ConnectionService: Node capabilities: ${JSON.stringify(config.capabilities)}`);

      const registrationTimeout = setTimeout(() => {
        this.logger.error(`ConnectionService: WebSocket registration timeout after ${this.WEBSOCKET_CONNECT_TIMEOUT}ms`);
        this.logger.error('ConnectionService: Possible causes:');
        this.logger.error('  1. Hub is not responding or is down');
        this.logger.error('  2. Network connectivity issues');
        this.logger.error('  3. Incorrect hub URL or credentials');
        this.logger.error('  4. Hub is overloaded or busy');
        this.logger.error(`ConnectionService: WebSocket connection state: ${this.websocketConnector?.isConnected() ? 'connected' : 'disconnected'}`);
        
        // Enhanced error with more context
        const error = new Error(`WebSocket registration timeout - hub not responding after ${this.WEBSOCKET_CONNECT_TIMEOUT}ms`);
        reject(error);
      }, this.WEBSOCKET_CONNECT_TIMEOUT);

      const registeredHandler = (nodeId: string) => {
        if (nodeId) {
          clearTimeout(registrationTimeout);
          this.logger.info(`ConnectionService: ✅ Received node ID from registration: ${nodeId}`);
          resolve(nodeId);
        } else {
          clearTimeout(registrationTimeout);
          this.logger.error('ConnectionService: Received empty node ID from registration');
          reject(new Error('Registration completed but received empty node ID'));
        }
      };

      // Add error handler for WebSocket disconnection during registration
      const disconnectedHandler = (reason: string) => {
        clearTimeout(registrationTimeout);
        this.logger.error(`ConnectionService: WebSocket disconnected during registration: ${reason}`);
        reject(new Error(`WebSocket disconnected during registration: ${reason}`));
      };

      // Add registration failed handler
      const registrationFailedHandler = (error: any) => {
        clearTimeout(registrationTimeout);
        this.logger.error(`ConnectionService: Registration failed from hub: ${error}`);
        reject(new Error(`Registration failed: ${error}`));
      };

      this.websocketConnector.once('registered', registeredHandler);
      this.websocketConnector.once('disconnected', disconnectedHandler);
      this.websocketConnector.once('registration-failed', registrationFailedHandler);
      
      // Cleanup handlers on completion
      const cleanup = () => {
        this.websocketConnector?.removeListener('registered', registeredHandler);
        this.websocketConnector?.removeListener('disconnected', disconnectedHandler);
        this.websocketConnector?.removeListener('registration-failed', registrationFailedHandler);
      };

      // Ensure cleanup happens
      registrationTimeout && (() => {
        const originalTimeout = registrationTimeout;
        clearTimeout(originalTimeout);
        setTimeout(() => cleanup(), 100);
      });
    });
  }

  /**
   * Get the current node ID
   */
  getNodeId(): string | null {
    return this.identityService.getNodeId();
  }
}
