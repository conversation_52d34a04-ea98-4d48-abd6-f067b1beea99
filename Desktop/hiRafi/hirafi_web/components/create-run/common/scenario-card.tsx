"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  CheckSquare,
  Tag,
  Clock,
  Info,
  Smartphone,
  Globe,
  Code,
  Calendar,
  ChevronDown,
  ChevronUp
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatDistanceToNow } from "date-fns"

interface ScenarioCardProps {
  id: string
  name: string
  description?: string
  tags?: string[]
  stepsCount?: number
  updatedAt?: string
  platform?: string
  isSelected: boolean
  onToggle: (id: string) => void
}

export function ScenarioCard({
  id,
  name,
  description,
  tags = [],
  stepsCount = 0,
  updatedAt,
  platform = "web",
  isSelected,
  onToggle
}: ScenarioCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Format the updated date
  const formattedDate = updatedAt
    ? formatDistanceToNow(new Date(updatedAt), { addSuffix: true })
    : "Unknown date"

  // Get platform icon
  const getPlatformIcon = () => {
    switch (platform.toLowerCase()) {
      case 'android':
      case 'ios':
      case 'mobile':
        return <Smartphone className="h-3.5 w-3.5 text-green-500" />
      case 'api':
        return <Code className="h-3.5 w-3.5 text-purple-500" />
      case 'web':
      default:
        return <Globe className="h-3.5 w-3.5 text-blue-500" />
    }
  }

  // Get platform label
  const getPlatformLabel = () => {
    switch (platform.toLowerCase()) {
      case 'android':
      case 'ios':
        return 'Mobile'
      case 'api':
        return 'API'
      case 'web':
      default:
        return 'Web'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={cn(
        "relative rounded-lg border p-4 transition-all",
        isSelected
          ? "border-blue-300 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20"
          : "border-gray-200 bg-white hover:border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:hover:border-gray-600"
      )}
    >
      {/* Selection checkbox */}
      <div className="absolute right-3 top-3">
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onToggle(id)}
          className={cn(
            "h-5 w-5 rounded-md border-2",
            isSelected
              ? "border-blue-500 bg-blue-500 text-white"
              : "border-gray-300 dark:border-gray-600"
          )}
        />
      </div>

      {/* Header */}
      <div className="mb-2 pr-8">
        <h3 className="font-medium text-gray-900 dark:text-gray-100 line-clamp-1">{name}</h3>
      </div>

      {/* Meta information */}
      <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mb-2">
        <div className="flex items-center gap-1">
          {getPlatformIcon()}
          <span>{getPlatformLabel()}</span>
        </div>

        {updatedAt && (
          <div className="flex items-center gap-1">
            <Clock className="h-3.5 w-3.5 text-gray-400" />
            <span>{formattedDate}</span>
          </div>
        )}
      </div>

      {/* Tags */}
      {tags.length > 0 && (
        <div className="flex flex-wrap gap-1.5 mb-2">
          {tags.slice(0, isExpanded ? tags.length : 3).map((tag, index) => (
            <Badge
              key={index}
              variant="outline"
              className="bg-gray-50 text-xs px-1.5 py-0 border-gray-200 text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              {tag}
            </Badge>
          ))}

          {!isExpanded && tags.length > 3 && (
            <Badge
              variant="outline"
              className="bg-gray-50 text-xs px-1.5 py-0 border-gray-200 text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer"
              onClick={() => setIsExpanded(true)}
            >
              +{tags.length - 3} more
            </Badge>
          )}
        </div>
      )}

      {/* Description (expandable) */}
      {description && (
        <div className="mt-2">
          <div
            className={cn(
              "text-xs text-gray-600 dark:text-gray-300",
              !isExpanded && "line-clamp-2"
            )}
          >
            {description}
          </div>

          {description.length > 100 && (
            <Button
              variant="ghost"
              size="sm"
              className="mt-1 h-6 px-2 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <span className="flex items-center gap-1">
                  <ChevronUp className="h-3 w-3" />
                  Show less
                </span>
              ) : (
                <span className="flex items-center gap-1">
                  <ChevronDown className="h-3 w-3" />
                  Show more
                </span>
              )}
            </Button>
          )}
        </div>
      )}
    </motion.div>
  )
}
