"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Users,
  Building2,
  Settings,
  Shield,
  Activity,
  Cog,
  Pause,
  Clock,
  Play,
  Database
} from "lucide-react"

interface AdminSidebarProps {
  isOpen: boolean
}

export function AdminSidebar({ isOpen }: AdminSidebarProps) {
  const pathname = usePathname()

  const navItems = [
    {
      name: "Dashboard",
      href: "/admin/dashboard",
      icon: LayoutDashboard,
    },
    {
      name: "Users",
      href: "/admin/users",
      icon: Users,
    },
    {
      name: "Companies",
      href: "/admin/companies",
      icon: Building2,
    },
    {
      name: "Hub-Node Health",
      href: "/admin/health",
      icon: Activity,
    },
    {
      name: "Queue Monitor",
      href: "/admin/queue-monitor",
      icon: Clock,
    },
    {
      name: "Active Tests",
      href: "/admin/active-tests",
      icon: Play,
    },
    {
      name: "System Control",
      href: "/admin/system-control",
      icon: Pause,
    },
    {
      name: "Redis Management",
      href: "/admin/redis-management",
      icon: Database,
    },
    {
      name: "Settings",
      href: "/admin/settings",
      icon: Settings,
    },
    {
      name: "System Settings",
      href: "/admin/settings/system",
      icon: Cog,
    },
  ]

  return (
    <aside
      className={cn(
        "fixed inset-y-0 left-0 z-20 flex w-64 flex-col border-r bg-white dark:bg-gray-800 shadow-md transition-transform duration-300 md:translate-x-0 md:relative",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}
    >
      <div className="flex h-16 items-center border-b px-6 bg-gradient-to-r from-blue-600 to-indigo-600">
        <Link href="/admin/dashboard" className="flex items-center gap-2 font-semibold">
          <Shield className="h-6 w-6 text-white" />
          <span className="text-lg font-bold text-white">Admin Panel</span>
        </Link>
      </div>
      <nav className="flex-1 overflow-auto py-4">
        <ul className="grid gap-1 px-2">
          {navItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50 dark:hover:bg-gray-700",
                  pathname === item.href &&
                    "bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.name}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      {/* Admin Mode bilgisi kaldırıldı */}
    </aside>
  )
}
