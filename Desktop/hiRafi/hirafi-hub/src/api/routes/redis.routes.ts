/**
 * Redis Routes
 * API routes for Redis queue management
 */

import { Router } from 'express';
import * as redisController from '../controllers/redis.controller.js';
import { authenticateAdmin } from '../middleware/adminAuthMiddleware.js';

const router = Router();

// Get queue names
router.get('/queues/names', authenticateAdmin, redisController.getQueueNames);

// Get all queues status
router.get('/queues', authenticateAdmin, redisController.getAllQueuesStatus);

// Get specific queue status
router.get('/queues/:queueName', authenticateAdmin, redisController.getQueueStatus);

// Purge a queue
router.delete('/queues/:queueName', authenticateAdmin, redisController.purgeQueue);

// Add a job to a queue
router.post('/queues/job', authenticateAdmin, redisController.addJob);

// Force complete a stuck job (now uses centralized monitoring)
router.post('/queues/force-complete-stuck-job', authenticateAdmin, redisController.forceCompleteStuckJob);

// Get centralized monitoring statistics
router.get('/monitoring/stats', authenticateAdmin, redisController.getMonitoringStats);

export default router;
