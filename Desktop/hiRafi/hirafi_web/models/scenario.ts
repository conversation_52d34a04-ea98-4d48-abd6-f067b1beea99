// Scenario Types
export type ActionType = 
  | "aiAction" 
  | "aiAssertion" 
  | "aiWaitElement" 
  | "sleep" 
  | "goto"
  | "aiQuery"
  | "aiBoolean"
  | "aiKeyboardPress"
  | "aiHover"
  | "aiScroll"
  | "aiTap"
  | "aiInput"
  | "aiRightClick"
  | "aiNumber"
  | "aiString"
  | "aiLocate"
  | "ifElse"
  | "forLoop"
  | "whileLoop"

export type PlatformType = "web" | "android"
export type ScenarioStatus = "active" | "inactive" | "passed" | "failed" | "running" | "queue"

/**
 * Test Step Interface
 * Represents a single step in a test scenario
 */
export interface TestStep {
  id: string
  type: ActionType
  name: string
  description?: string // Optional: for AI-generated steps
  deepThink?: boolean // For AI methods supporting deep think

  // Step type specific fields
  // For goto steps
  url?: string

  // For sleep steps
  duration?: number

  // For aiInput steps
  value?: string // Text to input
  target?: string // Target element to input into

  // For other AI steps (aiTap, aiAssertion, aiWaitElement, etc.)
  prompt?: string // Main instruction/prompt

  // AI Wait specific parameters
  timeoutMs?: number // For aiWaitElement: timeout in milliseconds
  checkIntervalMs?: number // For aiWaitElement: check interval in milliseconds

  // Control Flow specific fields
  condition?: string // For if-else and while: the condition to evaluate
  trueSteps?: TestStep[] // For if-else: steps to execute when condition is true
  falseSteps?: TestStep[] // For if-else: steps to execute when condition is false
  loopSteps?: TestStep[] // For for/while: steps to execute in the loop
  maxIterations?: number // For while: maximum number of iterations to prevent infinite loops
  iterationCount?: string // For for: the number or query to determine iteration count
  aiQueryFields?: AIQueryField[] // For aiQuery: the fields to extract
}

/**
 * AI Query Field Interface
 * Represents a single field in an aiQuery step
 */
export interface AIQueryField {
  id: string;
  key: string;
  description: string;
}

/**
 * TestRail Integration Interface
 * Configuration for TestRail integration
 */
export interface TestRailIntegration {
  caseIds: number[] | string[]
  sync: boolean
}

/**
 * Zephyr Scale Integration Interface
 * Configuration for Zephyr Scale integration
 */
export interface ZephyrScaleIntegration {
  caseIds: string[]
  sync: boolean
}

/**
 * Scenario Interface
 * Represents the core scenario data structure as stored in the database
 */
export interface Scenario {
  _id?: string
  id: string
  name: string
  description: string
  status: ScenarioStatus
  tags: string[]
  steps: TestStep[]
  folderId: string
  userId: string
  teamId: string
  companyId: string
  createdAt?: string
  updatedAt?: string
  runStatus?: ScenarioStatus
  title?: string
  testrailIntegration?: TestRailIntegration
  zephyrscaleIntegration?: ZephyrScaleIntegration
  testType?: "web-ui" | "api" | "mobile"
  testDataSetId?: string    // Dataset ID for variable resolution
  metadata?: {              // Dataset variables metadata
    variables?: any[]
  }
}

/**
 * Scenario Form Data Interface
 * Used for creating and editing scenarios in the UI
 */
export interface ScenarioFormData {
  // Basic info
  name: string
  description: string
  tags: string[]
  folderId: string

  // Platform selection
  platform: PlatformType

  // Steps
  steps: TestStep[]

  // Test Management integration
  testrailCases: string[]
  testrailSync: boolean
  zephyrscaleCases: string[]
  zephyrscaleSync: boolean

  // Temporary form fields
  newTag: string
  aiPrompt?: string
  useAIForDetails?: boolean
}

/**
 * Default scenario form data
 */
export const defaultScenarioFormData: ScenarioFormData = {
  name: "",
  description: "",
  tags: [],
  folderId: "",
  platform: "web",
  steps: [],
  testrailCases: [],
  testrailSync: false,
  zephyrscaleCases: [],
  zephyrscaleSync: false,
  newTag: "",
  aiPrompt: "",
  useAIForDetails: false,
}