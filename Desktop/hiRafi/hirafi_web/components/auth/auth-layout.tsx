"use client"

import { <PERSON><PERSON>N<PERSON>, useEffect, useState } from "react"
import { motion } from "framer-motion"
import { AnimatedBackground, AnimatedWaves } from "./animated-background"

interface AuthLayoutProps {
  children: ReactNode
  backgroundImage?: string
}

function TypewriterEffect() {
  const phrases = [
    "The next generation of automated testing",
    "Test automation powered by AI",
    "Create tests without writing code",
    "Automate with confidence",
    "Run tests across any browser or device"
  ]
  
  const [currentPhrase, setCurrentPhrase] = useState(0)
  const [currentText, setCurrentText] = useState("")
  const [isDeleting, setIsDeleting] = useState(false)
  const [isBlinking, setIsBlinking] = useState(true)

  useEffect(() => {
    const typeSpeed = isDeleting ? 30 : 70
    
    const timer = setTimeout(() => {
      // Handle cursor blinking while paused at the end of a phrase
      if (currentText === phrases[currentPhrase] && !isDeleting) {
        // Pause at the end of typing before starting deletion
        setTimeout(() => {
          setIsDeleting(true)
        }, 2000)
        return
      }
      
      // Handle typing and deleting logic
      if (!isDeleting && currentText !== phrases[currentPhrase]) {
        // Still typing the current phrase
        setCurrentText(phrases[currentPhrase].substring(0, currentText.length + 1))
      } else if (isDeleting && currentText !== "") {
        // Deleting the current phrase
        setCurrentText(currentText.substring(0, currentText.length - 1))
      } else if (isDeleting && currentText === "") {
        // Move to the next phrase after deletion
        setIsDeleting(false)
        setCurrentPhrase((prev) => (prev + 1) % phrases.length)
      }
    }, typeSpeed)

    // Blink cursor effect
    const blinkTimer = setInterval(() => {
      setIsBlinking(prev => !prev)
    }, 500)

    return () => {
      clearTimeout(timer)
      clearInterval(blinkTimer)
    }
  }, [currentText, currentPhrase, isDeleting, phrases])

  return (
    <span className="inline-flex h-[36px] items-center">
      <span>{currentText}</span>
      <span className={`ml-1 h-5 w-[2px] bg-white ${isBlinking ? "opacity-100" : "opacity-0"} transition-opacity duration-200`}></span>
    </span>
  )
}

export function AuthLayout({ children, backgroundImage = "/placeholder.svg?height=1080&width=1920" }: AuthLayoutProps) {
  return (
    <div className="flex min-h-screen w-full">
      {/* Left side - Form */}
      <div className="flex w-full flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:w-[600px] xl:px-12">
        <div className="mx-auto w-full max-w-sm lg:max-w-md pl-8 sm:pl-12 md:pl-16">{children}</div>
      </div>

      {/* Right side - Image */}
      <div className="hidden lg:block relative flex-1 bg-gradient-to-br from-indigo-600 via-violet-600 to-purple-700">
        <AnimatedBackground />
        <div className="absolute inset-0 z-20 flex flex-col items-center justify-center p-12 text-white">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight mb-6">HiRafi</h1>
            <div className="text-xl max-w-md mx-auto text-white/80 h-[36px]">
              <TypewriterEffect />
            </div>
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2, duration: 1 }}
              className="mt-8"
            >
              <p className="text-base text-white/70">Experience the future of test automation today</p>
            </motion.div>
          </motion.div>
          <div className="absolute bottom-0 left-0 right-0 h-40 z-10">
            <AnimatedWaves />
          </div>
        </div>
      </div>
    </div>
  )
}

export function AuthToggle({
  activeView,
  onToggle,
}: {
  activeView: "login" | "register"
  onToggle: () => void
}) {
  return (
    <div className="flex items-center justify-center space-x-2 mt-6">
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {activeView === "login" ? "Don't have an account?" : "Already have an account?"}
      </span>
      <button
        onClick={onToggle}
        className="text-sm font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
      >
        {activeView === "login" ? "Sign up" : "Sign in"}
      </button>
    </div>
  )
} 