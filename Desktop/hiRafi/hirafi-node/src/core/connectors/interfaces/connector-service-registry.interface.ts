/**
 * Connector Service Registry Interface
 * Defines the contract for connector service registries
 */

import { IConnectorManager } from './connector-manager.interface.js';
import { IHeartbeatService } from './heartbeat-service.interface.js';
import { IRegistrationService } from './registration-service.interface.js';

/**
 * Connector service registry interface
 */
export interface IConnectorServiceRegistry {
  /**
   * Initialize the service registry
   */
  initialize(): Promise<void>;

  /**
   * Get the connector manager
   */
  getConnectorManager(): Promise<IConnectorManager>;

  /**
   * Get the heartbeat service
   */
  getHeartbeatService(): Promise<IHeartbeatService>;

  /**
   * Get the registration service
   */
  getRegistrationService(): Promise<IRegistrationService>;

  /**
   * Register an external service
   * @param name Service name
   * @param instance Service instance
   */
  registerExternalService<T>(name: string, instance: T): void;

  /**
   * Dispose all services
   */
  dispose(): Promise<void>;

  /**
   * Check if registry is initialized
   */
  isInitialized(): boolean;
}

/**
 * Service registry configuration interface
 */
export interface IServiceRegistryConfig {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  maxResolutionDepth?: number;
  nodeId?: string;
  hubUrl?: string;
  secretKey?: string;
}

/**
 * Service registration interface
 */
export interface IServiceRegistration<T = any> {
  name: string;
  factory: (...args: any[]) => Promise<T> | T;
  dependencies?: string[];
  lifecycle: 'singleton' | 'transient';
  instance?: T;
  initialized?: boolean;
}

/**
 * Service dependency graph interface
 */
export interface IServiceDependencyGraph {
  /**
   * Add service to graph
   * @param name Service name
   * @param dependencies Service dependencies
   */
  addService(name: string, dependencies: string[]): void;

  /**
   * Remove service from graph
   * @param name Service name
   */
  removeService(name: string): void;

  /**
   * Check for circular dependencies
   * @param startService Starting service name
   */
  hasCircularDependency(startService: string): boolean;

  /**
   * Get dependency order
   * @param serviceName Service name
   */
  getDependencyOrder(serviceName: string): string[];

  /**
   * Get all services
   */
  getAllServices(): string[];

  /**
   * Get service dependencies
   * @param serviceName Service name
   */
  getServiceDependencies(serviceName: string): string[];
}
