"use client"

import { useState, useEffect } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { MoreHorizontal, Edit, Trash2, Eye, Users } from "lucide-react"
import Link from "next/link"
import { getCompanyTeams, deleteTeam } from "@/lib/api/admin-api"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"

interface Team {
  id: string
  name: string
  description?: string
  status: 'active' | 'inactive' | 'archived'
  createdAt: string
  membersCount: number
  leaderId?: string
  leaderName?: string
}

interface CompanyTeamsListProps {
  companyId: string
}

export function CompanyTeamsList({ companyId }: CompanyTeamsListProps) {
  const [teams, setTeams] = useState<Team[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [teamToDelete, setTeamToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  useEffect(() => {
    if (companyId) {
      fetchTeams()
    }
  }, [companyId])

  const fetchTeams = async () => {
    if (!companyId) {
      setTeams([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    try {
      const response = await getCompanyTeams(companyId)

      if (response.success && response.teams) {
        setTeams(response.teams)
      } else {
        console.error("Failed to fetch teams:", response.error)
        toast({
          title: "Hata",
          description: `Takımlar yüklenemedi: ${response.error || 'Bilinmeyen hata'}`,
          variant: "destructive",
        })
        setTeams([])
      }
    } catch (error) {
      console.error("Error fetching teams:", error)
      toast({
        title: "Hata",
        description: "Takımlar yüklenirken bir hata oluştu.",
        variant: "destructive",
      })
      setTeams([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteTeam = async () => {
    if (!teamToDelete) return

    setIsDeleting(true)
    try {
      const response = await deleteTeam(teamToDelete)

      if (response.success) {
        toast({
          title: "Takım silindi",
          description: "Takım başarıyla silindi.",
        })
        fetchTeams()
      } else {
        toast({
          title: "Hata",
          description: response.error || "Takım silinirken bir hata oluştu.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting team:", error)
      toast({
        title: "Hata",
        description: "Takım silinirken bir hata oluştu.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setTeamToDelete(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'secondary'
      case 'archived':
        return 'outline'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif'
      case 'inactive':
        return 'Pasif'
      case 'archived':
        return 'Arşivlenmiş'
      default:
        return status
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Takımlar yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (teams.length === 0) {
    return (
      <div className="flex h-40 flex-col items-center justify-center gap-4">
        <p className="text-muted-foreground">Bu şirkete henüz takım eklenmemiş.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Takım Adı</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Üye Sayısı</TableHead>
              <TableHead>Takım Lideri</TableHead>
              <TableHead>Oluşturulma Tarihi</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {teams.map((team) => (
              <TableRow key={team.id}>
                <TableCell>
                  <div className="font-medium">{team.name}</div>
                  {team.description && (
                    <div className="text-sm text-muted-foreground">
                      {team.description}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(team.status) as any}>
                    {getStatusText(team.status)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{team.membersCount}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {team.leaderName || "Atanmamış"}
                </TableCell>
                <TableCell>{formatDate(team.createdAt)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">İşlemler</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/teams/${team.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          Görüntüle
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/teams/${team.id}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Düzenle
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/teams/${team.id}/members`}>
                          <Users className="mr-2 h-4 w-4" />
                          Üyeleri Yönet
                        </Link>
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem onSelect={(e) => {
                            e.preventDefault()
                            setTeamToDelete(team.id)
                          }}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Sil
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Takımı silmek istediğinize emin misiniz?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bu işlem geri alınamaz. Bu takım ve ilişkili tüm veriler kalıcı olarak silinecektir.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel onClick={() => setTeamToDelete(null)}>İptal</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleDeleteTeam}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              disabled={isDeleting}
                            >
                              {isDeleting ? (
                                <div className="flex items-center">
                                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-current"></div>
                                  Siliniyor...
                                </div>
                              ) : (
                                "Takımı Sil"
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
