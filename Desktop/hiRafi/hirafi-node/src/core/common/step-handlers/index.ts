/**
 * Common Step Handlers
 * Exports all common step handlers and registry
 */

// AI Step Handlers
export * from './ai-action-handler.js';
export * from './ai-wait-element-handler.js';
export * from './ai-assertion-handler.js';
export * from './ai-query-handler.js';
export * from './ai-boolean-handler.js';
export * from './ai-keyboard-press-handler.js';
export * from './ai-hover-handler.js';
export * from './ai-scroll-handler.js';
export * from './ai-tap-handler.js';
export * from './ai-input-handler.js';
export * from './ai-right-click-handler.js';
export * from './ai-number-handler.js';
export * from './ai-string-handler.js';
export * from './ai-locate-handler.js';

// Control Flow Step Handlers
export * from './if-else-handler.js';
export * from './for-loop-handler.js';
export * from './while-loop-handler.js';

// Platform Step Handlers (consolidated from web/android)
export * from './goto-handler.js';
export * from './sleep-handler.js';

// Step Handler Registry
export * from './step-handler-registry.js';

import { CommonAiActionHandler } from './ai-action-handler.js';
import { CommonAiWaitElementHandler } from './ai-wait-element-handler.js';
import { CommonAiAssertionHandler } from './ai-assertion-handler.js';
import { CommonAiQueryHandler } from './ai-query-handler.js';
import { CommonAiBooleanHandler } from './ai-boolean-handler.js';
import { CommonAiKeyboardPressHandler } from './ai-keyboard-press-handler.js';
import { CommonAiHoverHandler } from './ai-hover-handler.js';
import { CommonAiScrollHandler } from './ai-scroll-handler.js';
import { CommonAiTapHandler } from './ai-tap-handler.js';
import { CommonAiInputHandler } from './ai-input-handler.js';
import { CommonAiRightClickHandler } from './ai-right-click-handler.js';
import { CommonAiNumberHandler } from './ai-number-handler.js';
import { CommonAiStringHandler } from './ai-string-handler.js';
import { CommonAiLocateHandler } from './ai-locate-handler.js';
import { IfElseHandler } from './if-else-handler.js';
import { ForLoopHandler } from './for-loop-handler.js';
import { WhileLoopHandler } from './while-loop-handler.js';
import { GotoStepHandler } from './goto-handler.js';
import { SleepStepHandler } from './sleep-handler.js';
import { StepHandlerRegistry } from './step-handler-registry.js';

/**
 * Create common AI step handlers
 * @returns Object containing all common AI step handlers
 */
export function createCommonAIStepHandlers() {
  return {
    aiAction: new CommonAiActionHandler(),
    aiWaitElement: new CommonAiWaitElementHandler(),
    aiAssertion: new CommonAiAssertionHandler(),
    aiQuery: new CommonAiQueryHandler(),
    aiBoolean: new CommonAiBooleanHandler(),
    aiKeyboardPress: new CommonAiKeyboardPressHandler(),
    aiHover: new CommonAiHoverHandler(),
    aiScroll: new CommonAiScrollHandler(),
    aiTap: new CommonAiTapHandler(),
    aiInput: new CommonAiInputHandler(),
    aiRightClick: new CommonAiRightClickHandler(),
    aiNumber: new CommonAiNumberHandler(),
    aiString: new CommonAiStringHandler(),
    aiLocate: new CommonAiLocateHandler()
  };
}

/**
 * Create control flow step handlers
 * @returns Object containing control flow step handlers
 */
export function createControlFlowStepHandlers() {
  return {
    ifElse: new IfElseHandler(),
    forLoop: new ForLoopHandler(),
    whileLoop: new WhileLoopHandler()
  };
}

/**
 * Create platform-specific step handlers
 * @returns Object containing platform-specific step handlers
 */
export function createPlatformStepHandlers() {
  return {
    goto: new GotoStepHandler(),
    sleep: new SleepStepHandler()
  };
}

/**
 * Create all step handlers (AI + control flow + platform-specific)
 * @returns Object containing all step handlers
 */
export function createAllStepHandlers() {
  return {
    ...createCommonAIStepHandlers(),
    ...createControlFlowStepHandlers(),
    ...createPlatformStepHandlers()
  };
}

/**
 * Create and populate a StepHandlerRegistry with all handlers
 * @param platform Platform identifier for the registry
 * @returns Fully populated StepHandlerRegistry
 */
export function createPopulatedStepHandlerRegistry(platform: string = 'common'): StepHandlerRegistry {
  const registry = new StepHandlerRegistry(platform);
  const allHandlers = createAllStepHandlers();

  // Register all handlers with the registry
  const handlerInstances = Object.values(allHandlers);
  registry.registerHandlers(handlerInstances);

  return registry;
}


