/**
 * Common AI Query Handler
 * Handles executing AI query steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Query Handler
 * Handles executing AI query steps using the AI agent
 */
export class CommonAiQueryHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiQuery';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiQueryHandler';
  }

  /**
   * Execute an AI query step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiQuery, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const queryText = step.prompt || step.value || step.name || step.description;

    if (!queryText) {
      return { success: false, error: 'Missing prompt, value, name or description for aiQuery step' };
    }

    try {

      // Log the query for debugging
      logger.debug(`CommonAiQueryHandler: Executing query "${queryText}"`);

      // Use the AI agent to execute the query directly
      const result = await agent.aiQuery(queryText || '');
      logger.debug(`aiQuery result: ${JSON.stringify(result)}`);

      // Check if the query was successful
      const isSuccess = result && !result.error;

      // Format the result to standard log format
      const logEntry = formatAIResultForLog('aiQuery', queryText, result);

      // Update query-specific fields
      logEntry.success = isSuccess;

      // Add the query result to the metadata
      logEntry.metadata.result = result.result;

      // If there's an error in the result, add it to the log entry
      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      // Add the formatted log entry to logs
      logs.push(JSON.stringify(logEntry));

      // If the query failed, return the error
      if (!isSuccess) {
        const errorMessage = result.error || `Query failed: ${queryText}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          ...result.result,
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      // Extract error message and reason
      const errorMessage = error.message || error.toString();
      const reasonMessage = error.Reason || '';

      // Log only the error message, not the full error object to avoid stack traces
      logger.info('AI Query Error:', errorMessage);

      // Simplify error messages
      const simplifiedError = simplifyErrorMessage(errorMessage);
      const simplifiedReason = simplifyErrorMessage(reasonMessage);

      // Create an error log entry with both error and reason
      const errorLogEntry = {
        operation: 'aiQuery',
        description: queryText,
        success: false,
        error: simplifiedError,
        reason: simplifiedReason,
        metadata: {
          status: 'failed'
        }
      };

      // Add the formatted error log entry to logs
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiQueryHandler: Query failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError,
        data: null
      };
    }
  }
}
