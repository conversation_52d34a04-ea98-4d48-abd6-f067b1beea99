/**
 * Schedule Routes
 * API endpoints for schedule management
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { checkPermission } from '../middleware/permissionMiddleware.js';
import { ScheduleStatus } from '../../models/schedule-types.js';
import {
  createSchedule,
  getScheduleById,
  getSchedulesByUser,
  getSchedulesByTeam,
  getSchedulesByCompany,
  updateSchedule,
  deleteSchedule,
  updateScheduleStatus,
  getSchedulesWithDetails
} from '../../services/mongo/scheduleService.js';
import { getRunById } from '../../services/mongo/runService.js';
import { db } from '../../services/mongo/dbConnection.js';

const router = Router();

/**
 * GET /api/schedules/runs
 * Get runs for schedule selection
 */
router.get('/runs', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;
    const isAdmin = req.user?.accountType === 'admin';

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get runs'
      });
    }

    // MongoDB aggregation to get runs with scenario details
    if (!db) {
      return res.status(500).json({
        success: false,
        error: 'Database connection not available'
      });
    }

    const runsCollection = db.collection('runs');

    // Build filter criteria
    const filter: any = {};

    // Admin users can see all runs in the company
    if (isAdmin) {
      if (companyId) {
        filter.companyId = companyId;
      }
    } else {
      // Regular users can only see runs in their team
      if (teamId) {
        filter.teamId = teamId;
      }
      if (companyId) {
        filter.companyId = companyId;
      }
    }

    // Get runs with scenario details
    const runs = await runsCollection.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'scenarios',
          localField: 'scenarioIds',
          foreignField: 'id',
          as: 'scenarios'
        }
      },
      {
        $project: {
          _id: 0,
          id: 1,
          name: 1,
          description: 1,
          createdAt: 1,
          userId: 1,
          scenarioIds: 1,
          scenarioCount: { $size: '$scenarioIds' },
          scenarios: {
            $map: {
              input: '$scenarios',
              as: 'scenario',
              in: {
                id: '$$scenario.id',
                name: '$$scenario.name'
              }
            }
          }
        }
      },
      { $sort: { createdAt: -1 } },
      { $limit: 100 }
    ]).toArray();

    return res.json({
      success: true,
      runs
    });
  } catch (error: any) {
    logger.error(`API: Error getting runs for schedule: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to get runs: ${error.message}`
    });
  }
});

/**
 * GET /api/schedules
 * Get all schedules for the user's team/company
 */
router.get('/', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;
    const isAdmin = req.user?.accountType === 'admin';

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to access schedules'
      });
    }

    // Get filter parameters
    const filter: any = {};

    // Admin users can see all schedules in the company
    if (isAdmin) {
      if (companyId) {
        filter.companyId = companyId;
      }
    } else {
      // Regular users can only see schedules in their team
      if (teamId) {
        filter.teamId = teamId;
      }
      if (companyId) {
        filter.companyId = companyId;
      }
    }

    // Get schedules with run and scenario details
    const result = await getSchedulesWithDetails(filter);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to get schedules'
      });
    }

    return res.json({
      success: true,
      schedules: result.schedules || []
    });
  } catch (error: any) {
    logger.error(`API: Error getting schedules: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to get schedules: ${error.message}`
    });
  }
});

/**
 * GET /api/schedules/:id
 * Get a specific schedule by ID
 */
router.get('/:id', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    const scheduleId = req.params.id;
    const userId = req.user?.id;
    const isAdmin = req.user?.accountType === 'admin';

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to access schedule'
      });
    }

    // Get schedule
    const result = await getScheduleById(scheduleId);

    if (!result.success || !result.schedule) {
      return res.status(404).json({
        success: false,
        error: result.message || 'Schedule not found'
      });
    }

    // Check if user has permission to access this schedule
    if (!isAdmin && result.schedule.userId !== userId) {
      // For non-admin users, check if they belong to the same team
      if (result.schedule.teamId !== req.user?.teamId) {
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to access this schedule'
        });
      }
    }

    return res.json({
      success: true,
      schedule: result.schedule
    });
  } catch (error: any) {
    logger.error(`API: Error getting schedule: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to get schedule: ${error.message}`
    });
  }
});

/**
 * POST /api/schedules
 * Create a new schedule
 */
router.post('/', authenticate as any, checkPermission('Schedule', 'create') as any, async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to create schedule'
      });
    }

    const {
      name,
      description,
      scheduleType,
      hourlyInterval,
      startDate,
      startTime,
      endDate,
      endTime,
      timezone,
      repeatDays,
      runIds,
      notifications,
      emailRecipients,
      slackChannel
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Schedule name is required'
      });
    }

    if (!scheduleType) {
      return res.status(400).json({
        success: false,
        error: 'Schedule type is required'
      });
    }

    if (!startDate || !startTime) {
      return res.status(400).json({
        success: false,
        error: 'Start date and time are required'
      });
    }

    if (!runIds || !Array.isArray(runIds) || runIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'At least one Run ID is required'
      });
    }

    // Validate all runs exist
    for (const runId of runIds) {
      const runResult = await getRunById(runId);
      if (!runResult.success) {
        return res.status(400).json({
          success: false,
          error: `Run with ID ${runId} not found`
        });
      }
    }

    // Create schedule
    const result = await createSchedule({
      name,
      description,
      scheduleType,
      hourlyInterval,
      startDate,
      startTime,
      endDate,
      endTime,
      timezone,
      repeatDays,
      runIds,
      userId,
      teamId,
      companyId,
      status: ScheduleStatus.ACTIVE,
      notifications,
      emailRecipients,
      slackChannel
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to create schedule'
      });
    }

    return res.status(201).json({
      success: true,
      scheduleId: result.scheduleId,
      message: 'Schedule created successfully'
    });
  } catch (error: any) {
    logger.error(`API: Error creating schedule: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to create schedule: ${error.message}`
    });
  }
});

/**
 * PUT /api/schedules/:id
 * Update a schedule
 */
router.put('/:id', authenticate as any, checkPermission('Schedule', 'update') as any, async (req: AuthRequest, res: Response) => {
  try {
    const scheduleId = req.params.id;
    const userId = req.user?.id;
    const isAdmin = req.user?.accountType === 'admin';

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to update schedule'
      });
    }

    // Get schedule to check permissions
    const scheduleResult = await getScheduleById(scheduleId);
    if (!scheduleResult.success || !scheduleResult.schedule) {
      return res.status(404).json({
        success: false,
        error: scheduleResult.message || 'Schedule not found'
      });
    }

    // Check if user has permission to update this schedule
    if (!isAdmin && scheduleResult.schedule.userId !== userId) {
      // For non-admin users, check if they belong to the same team
      if (scheduleResult.schedule.teamId !== req.user?.teamId) {
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to update this schedule'
        });
      }
    }

    const {
      name,
      description,
      scheduleType,
      hourlyInterval,
      startDate,
      startTime,
      endDate,
      endTime,
      timezone,
      repeatDays,
      runIds,
      status,
      notifications,
      emailRecipients,
      slackChannel
    } = req.body;

    // Validate name
    if (name === '') {
      return res.status(400).json({
        success: false,
        error: 'Schedule name cannot be empty'
      });
    }

    // If run IDs changed, validate all new runs exist
    if (runIds && Array.isArray(runIds) && runIds.length > 0) {
      // Check if run IDs have changed
      const currentRunIds = scheduleResult.schedule.runIds || [];
      const hasChanges = runIds.length !== currentRunIds.length ||
                         runIds.some(id => !currentRunIds.includes(id));

      if (hasChanges) {
        // Validate all runs exist
        for (const runId of runIds) {
          const runResult = await getRunById(runId);
          if (!runResult.success) {
            return res.status(400).json({
              success: false,
              error: `Run with ID ${runId} not found`
            });
          }
        }
      }
    }

    // Update schedule
    const updateResult = await updateSchedule(scheduleId, {
      name,
      description,
      scheduleType,
      hourlyInterval,
      startDate,
      startTime,
      endDate,
      endTime,
      timezone,
      repeatDays,
      runIds,
      status,
      notifications,
      emailRecipients,
      slackChannel
    });

    if (!updateResult.success) {
      return res.status(500).json({
        success: false,
        error: updateResult.message || 'Failed to update schedule'
      });
    }

    return res.json({
      success: true,
      message: 'Schedule updated successfully'
    });
  } catch (error: any) {
    logger.error(`API: Error updating schedule: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to update schedule: ${error.message}`
    });
  }
});

/**
 * DELETE /api/schedules/:id
 * Delete a schedule
 */
router.delete('/:id', authenticate as any, checkPermission('Schedule', 'delete') as any, async (req: AuthRequest, res: Response) => {
  try {
    const scheduleId = req.params.id;
    const userId = req.user?.id;
    const isAdmin = req.user?.accountType === 'admin';

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to delete schedule'
      });
    }

    // Get schedule to check permissions
    const scheduleResult = await getScheduleById(scheduleId);
    if (!scheduleResult.success || !scheduleResult.schedule) {
      return res.status(404).json({
        success: false,
        error: scheduleResult.message || 'Schedule not found'
      });
    }

    // Check if user has permission to delete this schedule
    if (!isAdmin && scheduleResult.schedule.userId !== userId) {
      // For non-admin users, check if they belong to the same team
      if (scheduleResult.schedule.teamId !== req.user?.teamId) {
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to delete this schedule'
        });
      }
    }

    // Delete schedule
    const deleteResult = await deleteSchedule(scheduleId);
    if (!deleteResult.success) {
      return res.status(500).json({
        success: false,
        error: deleteResult.message || 'Failed to delete schedule'
      });
    }

    return res.json({
      success: true,
      message: 'Schedule deleted successfully'
    });
  } catch (error: any) {
    logger.error(`API: Error deleting schedule: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to delete schedule: ${error.message}`
    });
  }
});

/**
 * PATCH /api/schedules/:id/status
 * Update schedule status (activate/pause)
 */
router.patch('/:id/status', authenticate as any, checkPermission('Schedule', 'update') as any, async (req: AuthRequest, res: Response) => {
  try {
    const scheduleId = req.params.id;
    const userId = req.user?.id;
    const isAdmin = req.user?.accountType === 'admin';
    const { status } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to update schedule status'
      });
    }

    // Validate status
    if (!status || (status !== ScheduleStatus.ACTIVE && status !== ScheduleStatus.PAUSED)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Must be "active" or "paused"'
      });
    }

    // Get schedule to check permissions
    const scheduleResult = await getScheduleById(scheduleId);
    if (!scheduleResult.success || !scheduleResult.schedule) {
      return res.status(404).json({
        success: false,
        error: scheduleResult.message || 'Schedule not found'
      });
    }

    // Check if user has permission to update this schedule
    if (!isAdmin && scheduleResult.schedule.userId !== userId) {
      // For non-admin users, check if they belong to the same team
      if (scheduleResult.schedule.teamId !== req.user?.teamId) {
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to update this schedule'
        });
      }
    }

    // Update schedule status
    const updateResult = await updateScheduleStatus(scheduleId, status);
    if (!updateResult.success) {
      return res.status(500).json({
        success: false,
        error: updateResult.message || 'Failed to update schedule status'
      });
    }

    return res.json({
      success: true,
      message: `Schedule ${status === ScheduleStatus.ACTIVE ? 'activated' : 'paused'} successfully`
    });
  } catch (error: any) {
    logger.error(`API: Error updating schedule status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to update schedule status: ${error.message}`
    });
  }
});

export default router;
