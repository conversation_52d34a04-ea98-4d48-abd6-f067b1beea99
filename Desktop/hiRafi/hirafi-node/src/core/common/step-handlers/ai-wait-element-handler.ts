/**
 * Common AI Wait Element Handler
 * Handles executing AI wait element steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';


/**
 * Common AI Wait Element Handler
 * Handles executing AI wait element steps using the AI agent
 */
export class CommonAiWaitElementHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiWaitElement';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiWaitElementHandler';
  }

  /**
   * Execute an AI wait element step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiWaitElement, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const elementDescription = step.prompt || step.value || step.name || step.description;

    if (!elementDescription) {
      return { success: false, error: 'Missing wait condition for aiWaitElement step. Please provide a prompt field.' };
    }

    try {
      // Prepare options with timeout, check interval, and deepThink parameters
      const options: any = {};

      if (step.timeoutMs) {
        options.timeoutMs = step.timeoutMs;
      }

      if (step.checkIntervalMs) {
        options.checkIntervalMs = step.checkIntervalMs;
      }

      if (step.deepThink) {
        options.deepThink = true;
      }

      // Log the wait for debugging
      logger.debug(`CommonAiWaitElementHandler: Waiting for element "${elementDescription}" with options: ${JSON.stringify(options)}`);

      // Use the AI agent to wait for the element directly with options
      const result = await agent.aiWaitFor(elementDescription || '', options);
      logger.debug(`aiWaitFor result: ${JSON.stringify(result)}`);

      // Format the result to standard log format
      const logEntry = formatAIResultForLog('aiWaitFor', elementDescription, result);

      // Check if the element was found
      const isFound = typeof result.result === 'object' ?
                     result.result?.found === true :
                     !!result.result;

      // Update wait-specific fields
      logEntry.success = isFound;

      // Add the formatted log entry to logs
      logs.push(JSON.stringify(logEntry));

      return {
        success: isFound,
        error: isFound ? null : `Element not found: ${elementDescription}`,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      // Extract error message and reason
      const errorMessage = error.message || error.toString();
      const reasonMessage = error.Reason || '';

      // Log only the error message, not the full error object to avoid stack traces
      logger.info('AI Wait Element Error:', errorMessage);

      // Simplify error messages
      const simplifiedError = simplifyErrorMessage(errorMessage);
      const simplifiedReason = simplifyErrorMessage(reasonMessage);

      // Create an error log entry with both error and reason
      const errorLogEntry = {
        operation: 'aiWaitFor',
        description: step.value || step.name || step.description,
        success: false,
        error: simplifiedError,
        reason: simplifiedReason,
        metadata: {
          status: 'failed'
        }
      };

      // Add the formatted error log entry to logs
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiWaitElementHandler: Element not found: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
}
