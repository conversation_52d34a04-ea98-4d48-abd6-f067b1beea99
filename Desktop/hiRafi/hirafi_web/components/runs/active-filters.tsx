"use client"

import { X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface ActiveFiltersProps {
  searchQuery: string
  onClearSearch: () => void
  activeFilter: string
  onClearStatusFilter: () => void
  activePlatform?: string
  onClearPlatformFilter?: () => void
  dateFilter: string
  onClearDateFilter: () => void
  onClearAllFilters: () => void
}

// Status options with their display properties
const statusOptions = [
  { value: "all", label: "All Statuses" },
  { value: "running", label: "Running", color: "green" },
  { value: "completed", label: "Passed", color: "green" },
  { value: "failed", label: "Failed", color: "red" },
  { value: "stopped", label: "Stopped", color: "yellow" },
  { value: "created", label: "Created", color: "blue" },
]

// Platform options
const platformOptions = [
  { value: "all", label: "All Platforms" },
  { value: "web", label: "Web", color: "blue" },
  { value: "android", label: "Mobile", color: "green" },
]

// Date filter options
const dateOptions = [
  { value: "all", label: "All Dates" },
  { value: "today", label: "Today" },
  { value: "yesterday", label: "Yesterday" },
  { value: "last7days", label: "Last 7 Days" },
  { value: "last30days", label: "Last 30 Days" },
  { value: "custom", label: "Custom Range" },
]

export function ActiveFilters({
  searchQuery,
  onClearSearch,
  activeFilter,
  onClearStatusFilter,
  activePlatform,
  onClearPlatformFilter,
  dateFilter,
  onClearDateFilter,
  onClearAllFilters,
}: ActiveFiltersProps) {
  // Check if any filters are active
  const hasActiveFilters = !!(
    searchQuery ||
    (activeFilter && activeFilter !== "all") ||
    (activePlatform && activePlatform !== "all") ||
    (dateFilter && dateFilter !== "all")
  )

  if (!hasActiveFilters) {
    return null
  }

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "running":
      case "completed":
        return "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800"
      case "failed":
        return "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800"
      case "stopped":
        return "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-800"
      case "created":
        return "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700"
    }
  }

  // Get platform badge color
  const getPlatformBadgeColor = (platform: string) => {
    switch (platform) {
      case "web":
        return "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800"
      case "android":
        return "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700"
    }
  }

  // Get date badge color
  const getDateBadgeColor = () => {
    return "bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-400 dark:border-purple-800"
  }

  return (
    <div className="flex flex-wrap items-center gap-2 pt-2 border-t border-gray-100 dark:border-gray-800">
      <div className="text-xs text-gray-500 dark:text-gray-400 mr-1">Active Filters:</div>

      {/* Search filter badge */}
      {searchQuery && (
        <Badge
          variant="outline"
          className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800 py-1 px-2 h-6 text-xs font-normal rounded-md"
        >
          <div className="flex items-center">
            <span className="font-medium mr-1">Search:</span>
            <span className="max-w-[150px] truncate">{searchQuery}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1.5 hover:bg-blue-100 dark:hover:bg-blue-900/50 p-0 rounded-full"
              onClick={onClearSearch}
            >
              <X className="h-2.5 w-2.5" />
            </Button>
          </div>
        </Badge>
      )}

      {/* Status filter badge */}
      {activeFilter && activeFilter !== "all" && (
        <Badge
          variant="outline"
          className={cn(
            "py-1 px-2 h-6 text-xs font-normal rounded-md",
            getStatusBadgeColor(activeFilter)
          )}
        >
          <div className="flex items-center">
            <span className="font-medium mr-1">Status:</span>
            <span>
              {statusOptions.find(s => s.value === activeFilter)?.label || activeFilter}
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1.5 hover:bg-opacity-20 p-0 rounded-full"
              onClick={onClearStatusFilter}
            >
              <X className="h-2.5 w-2.5" />
            </Button>
          </div>
        </Badge>
      )}

      {/* Platform filter badge */}
      {activePlatform && activePlatform !== "all" && onClearPlatformFilter && (
        <Badge
          variant="outline"
          className={cn(
            "py-1 px-2 h-6 text-xs font-normal rounded-md",
            getPlatformBadgeColor(activePlatform)
          )}
        >
          <div className="flex items-center">
            <span className="font-medium mr-1">Platform:</span>
            <span>
              {platformOptions.find(p => p.value === activePlatform)?.label || activePlatform}
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1.5 hover:bg-opacity-20 p-0 rounded-full"
              onClick={onClearPlatformFilter}
            >
              <X className="h-2.5 w-2.5" />
            </Button>
          </div>
        </Badge>
      )}

      {/* Date filter badge */}
      {dateFilter && dateFilter !== "all" && (
        <Badge
          variant="outline"
          className={cn(
            "py-1 px-2 h-6 text-xs font-normal rounded-md",
            getDateBadgeColor()
          )}
        >
          <div className="flex items-center">
            <span className="font-medium mr-1">Date:</span>
            <span>
              {dateOptions.find(d => d.value === dateFilter)?.label || dateFilter}
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 ml-1.5 hover:bg-opacity-20 p-0 rounded-full"
              onClick={onClearDateFilter}
            >
              <X className="h-2.5 w-2.5" />
            </Button>
          </div>
        </Badge>
      )}

      {/* Clear all button */}
      {hasActiveFilters && (
        <Button
          variant="ghost"
          size="sm"
          className="h-6 text-xs ml-auto text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
          onClick={onClearAllFilters}
        >
          Clear All
        </Button>
      )}
    </div>
  )
}
