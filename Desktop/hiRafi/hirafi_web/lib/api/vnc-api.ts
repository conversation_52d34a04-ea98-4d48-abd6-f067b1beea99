/**
 * VNC API
 *
 * VNC ile ilgili API isteklerini yönetir.
 */

import { get, post } from './fetch-wrapper';
import { VNC_ENDPOINTS } from './constants';

/**
 * VNC token oluşturur
 *
 * @param testId Test ID'si
 * @param nodeId Node ID'si
 */
export async function createVncToken(testId: string, nodeId: string) {
  return post(VNC_ENDPOINTS.TOKEN, { testId, nodeId });
}

/**
 * VNC durumunu kontrol eder
 *
 * @param testId Test ID'si
 */
export async function checkVncStatus(testId: string) {
  return get(VNC_ENDPOINTS.CHECK_STATUS(testId));
}

/**
 * Node bilgilerini getirir
 *
 * @param testId Test ID'si
 */
export async function getNodeInfo(testId: string) {
  return get(VNC_ENDPOINTS.NODE_INFO(testId));
}

/**
 * VNC oturumunu sonlandırır
 *
 * @param testId Test ID'si
 * @param token VNC token
 */
export async function closeVncSession(testId: string, token: string) {
  return post(VNC_ENDPOINTS.CLOSE_SESSION, { testId, token });
}

/**
 * VNC görüntüleyici URL'sini oluşturur
 *
 * @param token VNC token
 * @returns VNC görüntüleyici URL'si
 */
export function getVncViewerUrl(token: string) {
  return VNC_ENDPOINTS.VIEWER(token);
}
