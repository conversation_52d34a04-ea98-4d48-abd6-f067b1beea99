"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  Search,
  Building,
  Edit,
  Trash2,
  Eye,
  Users
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { adminApi } from "@/lib/api"
import { toast } from "@/components/ui/use-toast"
import { ManageCompanyUsersDialog } from "./manage-company-users-dialog"

interface Company {
  id: string
  name: string
  description?: string
  status: 'active' | 'inactive' | 'suspended'
  industry?: string
  size?: string
  website?: string
  contactEmail?: string
  createdAt: string
  teamsCount: number
  usersCount: number
}

export function CompanyList() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCompanies, setTotalCompanies] = useState(0)
  const [isManageUsersOpen, setIsManageUsersOpen] = useState(false)
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [error, setError] = useState<string | null>(null)
  const pageSize = 10

  useEffect(() => {
    fetchCompanies()
  }, [currentPage]) // Only trigger on page change, not on searchTerm change

  const fetchCompanies = async () => {
    setIsLoading(true)
    setError(null) // Clear previous errors
    try {
      // Gerçek API'den şirketleri getir
      const response = await adminApi.getCompanies({
        page: currentPage,
        limit: pageSize,
        search: searchTerm
      })

      // Debug log to see the actual response
      console.log('Companies response:', response)

      if (response.success && (response.data?.companies || response.companies)) {
        const companies = response.data?.companies || response.companies;
        setCompanies(companies)
        setTotalPages(response.data?.totalPages || response.totalPages || 1)
        setTotalCompanies(response.data?.total || response.total || companies.length)
      } else {
        // API başarısız olursa veya şirket yoksa, boş liste göster
        setCompanies([])
        setTotalPages(1)
        setTotalCompanies(0)
        const errorMessage = response.data?.error || response.error || "Unknown error occurred while fetching companies"
        console.error("Failed to fetch companies:", errorMessage)
        setError(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error("Failed to fetch companies:", errorMessage)
      setError(errorMessage)
      // Hata durumunda boş liste göster
      setCompanies([])
      setTotalPages(1)
      setTotalCompanies(0)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    fetchCompanies()
  }

  // Handle Enter key press in search input
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const handleDeleteCompany = async (companyId: string) => {
    if (!confirm("Are you sure you want to delete this company?")) {
      return
    }

    try {
      // Gerçek API'den şirketi sil
      const response = await adminApi.deleteCompany(companyId)

      if (response.success) {
        // Başarılı silme işleminden sonra şirket listesini yenile
        toast({
          title: "Success",
          description: "Company deleted successfully",
        })
        fetchCompanies()
      } else {
        console.error("Failed to delete company:", response.error)
        toast({
          title: "Error",
          description: `Failed to delete company: ${response.error || 'Unknown error'}`,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Failed to delete company:", error)
      toast({
        title: "Error",
        description: "An error occurred while deleting the company",
        variant: "destructive"
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'secondary'
      case 'suspended':
        return 'destructive'
      default:
        return 'default'
    }
  }

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Search companies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={handleSearchKeyPress}
            className="w-[250px]"
          />
          <Button variant="outline" size="icon" onClick={handleSearch}>
            <Search className="h-4 w-4" />
          </Button>
        </div>
        {/* Şirket Ekle butonu sayfanın üst kısmında zaten var */}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Company Name</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Industry</TableHead>
              <TableHead>Teams</TableHead>
              <TableHead>Users</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : companies.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No companies found
                </TableCell>
              </TableRow>
            ) : (
              companies.map((company) => (
                <TableRow key={company.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{company.name}</div>
                      {company.website && (
                        <div className="text-sm text-muted-foreground">
                          <a href={company.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                            {company.website.replace(/^https?:\/\//, '')}
                          </a>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(company.status) as any}>
                      {company.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {company.industry || "N/A"}
                    {company.size && (
                      <span className="text-sm text-muted-foreground block">
                        {company.size}
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <span className="font-medium">{company.teamsCount}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <span className="font-medium">{company.usersCount}</span>
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(company.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedCompany(company)
                          setIsManageUsersOpen(true)
                        }}
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Manage Users
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/companies/${company.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/companies/${company.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteCompany(company.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {companies.length} of {totalCompanies} companies
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(1)}
            disabled={currentPage === 1}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setCurrentPage(totalPages)}
            disabled={currentPage === totalPages}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Manage Users Dialog */}
      {selectedCompany && (
        <ManageCompanyUsersDialog
          open={isManageUsersOpen}
          onOpenChange={setIsManageUsersOpen}
          companyId={selectedCompany.id}
          companyName={selectedCompany.name}
          onSuccess={() => {
            toast({
              title: "Success",
              description: "Company users updated successfully",
            })
            fetchCompanies()
          }}
        />
      )}
    </div>
  )
}
