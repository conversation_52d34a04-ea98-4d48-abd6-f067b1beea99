/**
 * Connectors Module Entry Point
 * Exports all connector interfaces and implementations
 */

// Export interfaces
export * from './interfaces.js';

// Export factory
export * from './factory.js';

// Export adapted connectors (implementing the interfaces)
export { redisAdapter } from './redis/adapter.js';

// Export WebSocket connector
import { WebSocketConnector } from './websocket/connector.js';
export const webSocketConnector = WebSocketConnector.getInstance();

// Default export is the connector factory
export { ConnectorFactory as default } from './factory.js';