/**
 * Plugin API Routes
 * Eklenti işlemleri için API rotaları
 */

import { Router, Request, Response } from 'express';
import multer from 'multer';
import { logger } from '../../utils/logger.js';
import {
  isMongoDBInitialized,
  pluginsCollection
} from '../../services/mongo/dbConnection.js';
import {
  getUserPlugins,
  updatePluginStatus,
  updatePluginConfig,
  createPlugin,
  deletePlugin
} from '../../services/mongo/pluginService.js';
import {
  getTestRailProjects,
  getTestRailSuites,
  updateTestRailConfig,
  verifyTestRailConnection,
  getTestRailConfig,
  deleteTestRailConfig,
  getTestRailCasesMultipleSuites,
  getTestRailCaseSteps,
  updateTestRailProjectAndSuite
} from '../../services/mongo/testRailService.js';
import {
  verifyJiraConnection,
  getJiraProjects,
  getJiraIssueTypes,
  updateJiraConfig,
  getJiraConfig,
  deleteJiraConfig,
  createJiraIssue,
  sendReportToJira
} from '../../services/mongo/jiraService.js';
import {
  verifySauceLabsConnection,
  updateSauceLabsConfig,
  getSauceLabsConfig,
  getSauceLabsDevices,
  getSauceLabsAccountInfo,
  getSauceLabsApps,
  getSauceLabsAppGroups,
  uploadSauceLabsApp,
  deleteSauceLabsApp,
  updateSauceLabsAppSettings
} from '../../services/mongo/sauceLabsService.js';
import {
  verifyTestiniumConnection,
  updateTestiniumConfig,
  getTestiniumConfig,
  getTestiniumDevices,
  createTestiniumAllocation,
  startTestiniumSession,
  closeTestiniumSession,
  uploadTestiniumApp,
  getTestiniumApps,
  deleteTestiniumApp,
  downloadTestiniumApp,
  deleteTestiniumConfig
} from '../../services/mongo/testiniumService.js';
import {
  verifyZephyrScaleConnection,
  getZephyrScaleProjects,
  getZephyrScaleTestCases,
  getZephyrScaleFolders,
  updateZephyrScaleConfig,
  getZephyrScaleConfig,
  deleteZephyrScaleConfig,
  getZephyrScaleTestCaseDetails,
  createZephyrScaleExecution
} from '../../services/mongo/zephyrScaleService.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { handleSauceLabsAppUpload } from '../middleware/uploadMiddleware.js';

// Define a custom interface that extends Request to include file property
interface FileRequest extends Request {
  file?: Express.Multer.File;
}

const router = Router();

/**
 * GET /api/plugins
 * Kullanıcıya ait eklentileri listeler
 */
router.get('/', authenticate as any, async (req: Request, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    // Eğer BYPASS_AUTH aktifse userId parametresi kullanılabilir
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id || req.body.userId?.toString() || req.query.userId?.toString();
    const teamId = authReq.user?.teamId?.toString() || '';
    const companyId = authReq.user?.companyId?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Takım veya şirket kimliği bulunamadı'
      });
    }

    logger.info(`[API] Listing plugins for user: ${userId}, team: ${teamId}, company: ${companyId}`);

    // MongoDB servisinden kullanıcının eklentilerini çek
    const result = await getUserPlugins(teamId, companyId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      data: {
        count: result.plugins?.length || 0,
        plugins: result.plugins
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /plugins: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins
 * Yeni bir eklenti kaydı oluşturur
 */
router.post('/', authenticate as any, async (req: Request, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id || req.body.userId?.toString();

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    const pluginData = req.body;

    if (!pluginData || !pluginData.pluginName) {
      return res.status(400).json({
        success: false,
        error: 'pluginName alanı zorunludur'
      });
    }

    // Kullanıcı ID'sini ekle
    pluginData.userId = userId;

    logger.info(`[API] Creating ${pluginData.pluginName} plugin for user: ${userId}`);

    // Eklenti oluştur
    const result = await createPlugin(pluginData);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.status(201).json({
      success: true,
      message: 'Plugin başarıyla oluşturuldu',
      plugin: result.plugin
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/activate
 * Eklenti aktif/pasif durumunu değiştirir
 */
router.post('/activate', authenticate, async (req: Request, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id || req.body.userId?.toString();
    const teamId = authReq.user?.teamId?.toString() || '';
    const companyId = authReq.user?.companyId?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Takım veya şirket kimliği bulunamadı'
      });
    }

    const { pluginName, enabled } = req.body;

    if (!pluginName || enabled === undefined) {
      return res.status(400).json({
        success: false,
        error: 'pluginName ve enabled alanları zorunludur'
      });
    }

    logger.info(`[API] ${enabled ? 'Activating' : 'Deactivating'} ${pluginName} plugin for user: ${userId}, team: ${teamId}, company: ${companyId}`);

    // Eklenti durumunu güncelle
    const result = await updatePluginStatus(pluginName, teamId, companyId, enabled);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/activate: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * PUT /api/plugins/config
 * Eklenti konfigürasyonunu günceller
 */
router.put('/config', authenticate, async (req: Request, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id || req.body.userId?.toString();
    const teamId = authReq.user?.teamId?.toString() || '';
    const companyId = authReq.user?.companyId?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Takım veya şirket kimliği bulunamadı'
      });
    }

    const { pluginName, config } = req.body;

    if (!pluginName || !config) {
      return res.status(400).json({
        success: false,
        error: 'pluginName ve config alanları zorunludur'
      });
    }

    logger.info(`[API] Updating ${pluginName} plugin configuration for user: ${userId}, team: ${teamId}, company: ${companyId}`);

    // Eklenti konfigürasyonunu güncelle
    const result = await updatePluginConfig(teamId, companyId, pluginName, config);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in PUT /plugins/config: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_testrail_projects
 * TestRail projelerini getirir
 */
router.post('/get_testrail_projects', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // Get TestRail configuration from database
    const configResult = await getTestRailConfig(teamId, companyId);
    if (!configResult.success || !configResult.config) {
      return res.status(400).json({
        success: false,
        error: 'TestRail configuration not found. Please configure TestRail first.'
      });
    }

    const { url, username, apiKey } = configResult.config;

    logger.info(`[API] Fetching TestRail projects with stored credentials`);

    // TestRail projelerini getir
    const result = await getTestRailProjects(url, username, apiKey);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      projects: result.projects
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/get_testrail_projects: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_suites
 * TestRail suite'lerini getirir
 */
router.post('/get_suites', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // Get TestRail configuration from database
    const configResult = await getTestRailConfig(teamId, companyId);
    if (!configResult.success || !configResult.config) {
      return res.status(400).json({
        success: false,
        error: 'TestRail configuration not found. Please configure TestRail first.'
      });
    }

    const { url, username, apiKey } = configResult.config;
    const { projectIds } = req.body;

    // ProjectId'leri doğrula
    let projectIdsArray: number[] = [];

    // Çoklu proje ID'leri için dizi olarak gönderildiyse
    if (Array.isArray(projectIds)) {
      projectIdsArray = projectIds
        .map(id => typeof id === 'number' ? id : parseInt(id))
        .filter(id => !isNaN(id) && id > 0);
    }
    // String olarak gönderildiyse (virgülle ayrılmış)
    else if (typeof projectIds === 'string') {
      projectIdsArray = projectIds.split(',')
        .map(id => parseInt(id.trim()))
        .filter(id => !isNaN(id) && id > 0);
    }
    // Tek bir sayı olarak gönderildiyse
    else if (typeof projectIds === 'number') {
      projectIdsArray = [projectIds];
    }

    if (projectIdsArray.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'En az bir projectId parametresi zorunludur'
      });
    }

    logger.info(`[API] Fetching TestRail suites for projects: ${projectIdsArray.join(', ')}`);

    // TestRail suite'lerini getir
    const result = await getTestRailSuites(url, username, apiKey, projectIdsArray);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      data: {
        suites: result.suites,
        suitesByProject: result.suitesByProject
      }
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/get_suites: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_suites_for_project
 * Belirli bir proje için TestRail suite'lerini getirir (optimized endpoint)
 */
router.post('/get_suites_for_project', async (req: AuthRequest, res: Response) => {
  try {
    const { projectId } = req.body;

    if (!projectId) {
      return res.status(400).json({
        success: false,
        error: 'projectId parametresi zorunludur'
      });
    }

    // Kullanıcı, takım ve şirket kimliklerini doğrulanmış kullanıcıdan al
    const userId = req.user?.id?.toString() || '';
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!userId || !teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı kimlik bilgileri bulunamadı'
      });
    }

    // Mevcut TestRail konfigürasyonunu getir
    const config = await getTestRailConfig(teamId, companyId);

    if (!config.success || !config.config) {
      return res.status(404).json({
        success: false,
        error: 'TestRail integration not configured',
        code: 'TESTRAIL_NOT_CONFIGURED'
      });
    }

    const { url, username, apiKey } = config.config;

    logger.info(`[API] Fetching TestRail suites for project: ${projectId}`);

    // Tek proje için suite'leri getir
    const result = await getTestRailSuites(url, username, apiKey, [parseInt(projectId)]);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message,
      data: {
        suites: result.suites,
        suitesByProject: result.suitesByProject
      }
    });

  } catch (error: any) {
    logger.error(`[API] Error getting TestRail suites for project: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting TestRail suites for project: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/testrail-health
 * TestRail bağlantısını test eder
 */
router.post('/testrail-health', async (req: Request, res: Response) => {
  try {
    const { url, email, apiKey } = req.body;

    // Input sanitization
    const trimmedUrl = url?.trim();
    const trimmedEmail = email?.trim();
    const trimmedApiKey = apiKey?.trim();

    // Detailed input validation
    if (!trimmedUrl) {
      return res.status(400).json({
        success: false,
        error: 'TestRail URL alanı zorunludur ve boş olamaz',
        errorCode: 'MISSING_URL',
        field: 'url'
      });
    }

    if (!trimmedEmail) {
      return res.status(400).json({
        success: false,
        error: 'Email alanı zorunludur ve boş olamaz',
        errorCode: 'MISSING_EMAIL',
        field: 'email'
      });
    }

    if (!trimmedApiKey) {
      return res.status(400).json({
        success: false,
        error: 'API Key alanı zorunludur ve boş olamaz',
        errorCode: 'MISSING_API_KEY',
        field: 'apiKey'
      });
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(trimmedEmail)) {
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir email adresi girin',
        errorCode: 'INVALID_EMAIL_FORMAT',
        field: 'email'
      });
    }

    // Basic URL format validation
    if (!trimmedUrl.includes('.') || trimmedUrl.length < 4) {
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir TestRail URL girin (örn: yourdomain.testrail.io)',
        errorCode: 'INVALID_URL_FORMAT',
        field: 'url'
      });
    }

    logger.info(`[API] Testing TestRail connection with URL: ${trimmedUrl}, email: ${trimmedEmail}`);

    // TestRail bağlantısını test et
    const result = await verifyTestRailConnection(trimmedUrl, trimmedEmail, trimmedApiKey);

    if (!result.success) {
      // More specific HTTP status codes based on error type
      let statusCode = 400;
      
      switch (result.errorCode) {
        case 'AUTHENTICATION_FAILED':
          statusCode = 401;
          break;
        case 'ACCESS_DENIED':
          statusCode = 403;
          break;
        case 'INSTANCE_NOT_FOUND':
          statusCode = 404;
          break;
        case 'RATE_LIMITED':
          statusCode = 429;
          break;
        case 'SERVER_ERROR':
          statusCode = 502;
          break;
        case 'TIMEOUT':
        case 'NETWORK_ERROR':
        case 'DNS_RESOLUTION_FAILED':
        case 'CONNECTION_REFUSED':
          statusCode = 503;
          break;
        default:
          statusCode = 400;
      }

      return res.status(statusCode).json({
        success: false,
        error: result.message,
        errorCode: result.errorCode,
        details: result.data
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message || 'TestRail bağlantısı başarılı',
      data: {
        projectCount: result.data?.length || 0,
        connectionVerified: true
      }
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/testrail-health: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası oluştu',
      errorCode: 'INTERNAL_SERVER_ERROR',
      details: error.message
    });
  }
});

/**
 * POST /api/plugins/set_projectandsuite
 * TestRail proje ve suite ID'lerini günceller
 */
router.post('/set_projectandsuite', async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    const userId = req.user?.id || req.body.userId?.toString();
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Takım veya şirket kimliği bulunamadı'
      });
    }

    const { projectId, suiteId } = req.body;

    if (!projectId || !suiteId) {
      return res.status(400).json({
        success: false,
        error: 'projectId ve suiteId alanları zorunludur'
      });
    }

    logger.info(`[API] Setting TestRail project: ${projectId} and suite: ${suiteId} for user: ${userId}, team: ${teamId}, company: ${companyId}`);

    // TestRail proje ve suite ID'lerini güncelle
    const result = await updateTestRailProjectAndSuite(
      teamId,
      companyId,
      parseInt(projectId),
      parseInt(suiteId)
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/set_projectandsuite: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/save_testrail_config
 * Takım ve şirket için TestRail configurasyonunu kaydeder
 */
router.post('/save_testrail_config', async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı, takım ve şirket kimliklerini doğrulanmış kullanıcıdan al
    const userId = req.user?.id?.toString() || '';
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı kimliği bulunamadı'
      });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Takım veya şirket kimliği bulunamadı'
      });
    }

    // Request body'den verileri al
    const {
      url,
      username,
      apiKey,
      projectsData,
      suitesData
    } = req.body;

    // Suite'lere project_id ekle (eğer eksikse)
    let enhancedSuitesData = suitesData || [];
    if (Array.isArray(enhancedSuitesData) && Array.isArray(projectsData) && projectsData.length > 0) {
      enhancedSuitesData = enhancedSuitesData.map((suite: any) => {
        // Suite'de zaten project_id varsa koru, yoksa tüm suite'leri ilk projeye ata
        if (!suite.project_id && projectsData.length > 0) {
          const projectId = parseInt(projectsData[0].id.toString());
          return {
            ...suite,
            project_id: projectId
          };
        }
        return suite;
      });
    }

    // Eğer proje ve suite verileri boşsa, bu ilk konfigürasyon kaydıdır
    const isInitialConfig = (!projectsData || projectsData.length === 0) &&
                           (!enhancedSuitesData || enhancedSuitesData.length === 0);

    // TestRail konfigürasyonunu güncelle
    const result = await updateTestRailConfig(
      teamId,
      companyId,
      url,
      username,
      apiKey,
      // Proje ve suite veri yapılarını doğrudan kullan
      projectsData || [],
      enhancedSuitesData,
      isInitialConfig // İlk konfigürasyonda validasyonu atla
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/save_testrail_config: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/plugins/get_available_test_management_integrations
 * Get all available test management integrations for the team/company
 * Returns only configured integrations without error messages
 */
router.get('/get_available_test_management_integrations', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const availableIntegrations: any[] = [];

    // Check TestRail configuration
    try {
      const testRailConfig = await getTestRailConfig(teamId, companyId);
      if (testRailConfig.success &&
          'config' in testRailConfig &&
          testRailConfig.config &&
          typeof testRailConfig.config === 'object' &&
          testRailConfig.config !== null &&
          'url' in testRailConfig.config &&
          testRailConfig.config.url) {
        availableIntegrations.push({
          provider: 'testrail',
          name: 'TestRail',
          isConnected: true,
          config: testRailConfig.config,
          plugin: testRailConfig.plugin
        });
      }
    } catch (error) {
      // Silently ignore TestRail errors - user might not want it configured
      logger.debug(`TestRail not configured for team ${teamId}, company ${companyId}`);
    }

    // Check Zephyr Scale configuration
    try {
      const zephyrScaleConfig = await getZephyrScaleConfig(teamId, companyId);
      if (zephyrScaleConfig.success &&
          'data' in zephyrScaleConfig &&
          zephyrScaleConfig.data &&
          typeof zephyrScaleConfig.data === 'object' &&
          zephyrScaleConfig.data !== null &&
          'projectsData' in zephyrScaleConfig.data &&
          zephyrScaleConfig.data.projectsData) {
        availableIntegrations.push({
          provider: 'zephyrscale',
          name: 'Zephyr Scale',
          isConnected: true,
          config: zephyrScaleConfig.data,
          plugin: zephyrScaleConfig.plugin
        });
      }
    } catch (error) {
      // Silently ignore Zephyr Scale errors - user might not want it configured
      logger.debug(`Zephyr Scale not configured for team ${teamId}, company ${companyId}`);
    }

    res.json({
      success: true,
      message: `Found ${availableIntegrations.length} available test management integration(s)`,
      data: {
        integrations: availableIntegrations,
        count: availableIntegrations.length
      }
    });

  } catch (error: any) {
    logger.error(`Error getting available test management integrations: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to get available test management integrations'
    });
  }
});

/**
 * GET /api/plugins/get_testrail_config
 * Takım ve şirket için TestRail konfigürasyonunu döndürür
 */
router.get('/get_testrail_config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı, takım ve şirket kimliklerini doğrulanmış kullanıcıdan al
    const userId = req.user?.id || req.body.userId?.toString() || req.query.userId?.toString();
    const teamId = req.user?.teamId?.toString() || req.body.teamId?.toString() || req.query.teamId?.toString();
    const companyId = req.user?.companyId?.toString() || req.body.companyId?.toString() || req.query.companyId?.toString();

    // Öncelikle teamId ve companyId ile arama yap
    if (teamId && companyId) {
      logger.info(`[API] Fetching TestRail configuration for team: ${teamId}, company: ${companyId}`);

      // TestRail konfigürasyonunu getir
      const result = await getTestRailConfig(teamId, companyId);

      // Yanıtı döndür
      return handleTestRailConfigResponse(result, res);
    }

    // TeamId ve companyId yoksa, geriye dönük uyumluluk için userId ile dene
    if (userId) {
      logger.info(`[API] Fetching TestRail configuration for user: ${userId}`);

      // TestRail konfigürasyonunu getir
      const result = await getTestRailConfig(undefined, undefined, userId);

      // Yanıtı döndür
      return handleTestRailConfigResponse(result, res);
    }

    // Hiçbir parametre verilmediyse hata dön
    return res.status(400).json({
      success: false,
      error: 'Takım, şirket veya kullanıcı kimliği bulunamadı',
      config: null,
      plugin: null,
      stats: {
        projectCount: 0,
        suiteCount: 0,
        lastUpdated: null
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /plugins/get_testrail_config: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * TestRail konfigürasyon yanıtını işler
 * @param result TestRail konfigürasyon sonucu
 * @param res Express Response nesnesi
 * @returns Express Response nesnesi
 */
function handleTestRailConfigResponse(result: any, res: Response) {
  // Konfigürasyon bulunamadıysa 200 OK dön, ancak success: false olarak işaretle
  if (!result.success) {
    return res.status(200).json({
      success: false,
      error: result.message,
      config: null,
      plugin: null,
      stats: {
        projectCount: 0,
        suiteCount: 0,
        lastUpdated: null
      }
    });
  }

  // Başarılı yanıt
  return res.json({
    success: true,
    data: {
      config: result.config,
      plugin: {
        id: result.plugin.id,
        name: result.plugin.name,
        description: result.plugin.description,
        active: result.plugin.active,
        createdAt: result.plugin.createdAt,
        updatedAt: result.plugin.updatedAt
      },
      // Konfigürasyon istatistikleri - yeni veri yapısı ile
      stats: {
        projectCount: result.config.projectsData ? result.config.projectsData.length : 0,
        suiteCount: result.config.suitesData ? result.config.suitesData.length : 0,
        lastUpdated: result.plugin.updatedAt
      }
    }
  });
}

/**
 * POST /api/plugins/get_testrail_projects_only
 * Sadece TestRail projelerini getirir (lazy loading için)
 */
router.post('/get_testrail_projects_only', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    const userId = req.user?.id || req.body.userId?.toString() || req.query.userId?.toString();

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    logger.info(`[API] get_testrail_projects_only endpoint called for user: ${userId}`);

    // Takım ve şirket kimliklerini al
    const teamId = req.user?.teamId?.toString() || req.body.teamId?.toString() || req.query.teamId?.toString();
    const companyId = req.user?.companyId?.toString() || req.body.companyId?.toString() || req.query.companyId?.toString();

    // Kullanıcının TestRail konfigürasyonunu getir
    let configResult;
    if (teamId && companyId) {
      configResult = await getTestRailConfig(teamId, companyId);
    } else {
      configResult = await getTestRailConfig(undefined, undefined, userId);
    }

    if (!configResult.success || !configResult.config) {
      return res.status(404).json({
        success: false,
        error: 'TestRail integration not configured',
        code: 'TESTRAIL_NOT_CONFIGURED',
        config: null
      });
    }

    // Başarılı yanıt - sadece konfigürasyon ve proje bilgilerini döndür (hassas bilgiler olmadan)
    // Suite'lere project_id ekle (eğer eksikse)
    let enhancedSuitesData = configResult.config.suitesData || [];
    if (Array.isArray(enhancedSuitesData) && configResult.config.projectsData && configResult.config.projectsData.length > 0) {
      enhancedSuitesData = enhancedSuitesData.map((suite: any) => {
        // Suite'de zaten project_id varsa koru, yoksa ilk projeye ata
        if (!suite.project_id && configResult.config.projectsData.length > 0) {
          const projectId = parseInt(configResult.config.projectsData[0].id.toString());
          return {
            ...suite,
            project_id: projectId
          };
        }
        return suite;
      });
    }

    const safeConfig = {
      url: configResult.config.url,
      projectsData: configResult.config.projectsData || [],
      suitesData: enhancedSuitesData,
      updatedAt: configResult.config.updatedAt
      // username ve apiKey gibi hassas bilgileri çıkardık
    };

    res.json({
      success: true,
      message: 'TestRail configuration loaded successfully',
      data: {
        config: safeConfig,
        plugin: configResult.success ? {
          id: configResult.plugin.id,
          name: configResult.plugin.name,
          description: configResult.plugin.description,
          active: configResult.plugin.active,
          createdAt: configResult.plugin.createdAt,
          updatedAt: configResult.plugin.updatedAt
        } : null
      }
    });
  } catch (error: any) {
    logger.error(`[API] Error in POST /plugins/get_testrail_projects_only: ${error.message}`);
    if (error.stack) {
      logger.error(`[API] Error stack trace: ${error.stack}`);
    }
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_testrail_cases_for_suite
 * Belirli bir suite için TestRail test case'lerini getirir (lazy loading için)
 */
router.post('/get_testrail_cases_for_suite', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    const userId = req.user?.id || req.body.userId?.toString() || req.query.userId?.toString();

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    logger.info(`[API] get_testrail_cases_for_suite endpoint called for user: ${userId}`);

    // İstek body'sinden parametreleri al
    let { url, username, apiKey, projectId, suiteId } = req.body;

    // Takım ve şirket kimliklerini al
    const teamId = req.user?.teamId?.toString() || req.body.teamId?.toString() || req.query.teamId?.toString();
    const companyId = req.user?.companyId?.toString() || req.body.companyId?.toString() || req.query.companyId?.toString();

    // Parametreler eksikse, kullanıcının mevcut TestRail konfigürasyonunu kullan
    if (!url || !username || !apiKey || !projectId) {
      logger.info('[API] Some parameters missing, using stored TestRail configuration');

      let configResult;
      if (teamId && companyId) {
        configResult = await getTestRailConfig(teamId, companyId);
      } else {
        configResult = await getTestRailConfig(undefined, undefined, userId);
      }

      if (!configResult.success || !configResult.config) {
        return res.status(404).json({
          success: false,
          error: 'TestRail integration not configured',
          code: 'TESTRAIL_NOT_CONFIGURED',
          config: null
        });
      }

      // Eksik parametreleri konfigürasyondan tamamla
      url = url || configResult.config.url;
      username = username || configResult.config.username;
      apiKey = apiKey || configResult.config.apiKey;

      // projectId sağlanmamışsa ve yapılandırmada projectsData varsa, ilk projeyi kullan
      if (!projectId && configResult.config.projectsData && configResult.config.projectsData.length > 0) {
        projectId = configResult.config.projectsData[0].id;
        logger.info(`[API] Using default project ID from config: ${projectId}`);
      }
    }

    // Gerekli parametrelerin mevcut olduğunu kontrol et
    if (!url || !username || !apiKey) {
      logger.error('[API] Missing required TestRail credentials');
      return res.status(400).json({
        success: false,
        error: 'TestRail bağlantı bilgileri eksik'
      });
    }

    if (!projectId || !suiteId) {
      logger.error('[API] Missing required projectId or suiteId');
      return res.status(400).json({
        success: false,
        error: 'projectId ve suiteId parametreleri zorunludur'
      });
    }

    // Project ID ve Suite ID'yi doğrula
    const projectIdNum = typeof projectId === 'number' ? projectId : parseInt(projectId);
    const suiteIdNum = typeof suiteId === 'number' ? suiteId : parseInt(suiteId);
    
    if (isNaN(projectIdNum) || projectIdNum <= 0) {
      logger.error(`[API] Invalid projectId: ${projectId}`);
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir projectId parametresi zorunludur'
      });
    }

    if (isNaN(suiteIdNum) || suiteIdNum <= 0) {
      logger.error(`[API] Invalid suiteId: ${suiteId}`);
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir suiteId parametresi zorunludur'
      });
    }

    logger.info(`[API] Fetching TestRail cases for project ${projectIdNum}, suite: ${suiteIdNum}`);

    // TestRail case'lerini getir - sadece tek suite için
    const startTime = Date.now();
    const result = await getTestRailCasesMultipleSuites(url, username, apiKey, projectIdNum, [suiteIdNum]);
    const duration = Date.now() - startTime;

    logger.info(`[API] TestRail API request completed in ${duration}ms with success: ${result.success}`);

    if (!result.success) {
      logger.error(`[API] Failed to fetch TestRail cases: ${result.message}`);
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Cases istatistiklerini logla
    const totalCaseCount = result.cases.length;
    logger.info(`[API] Fetched ${totalCaseCount} cases for suite ${suiteIdNum}`);

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message,
      data: {
        cases: result.cases,
        casesBySuite: result.casesBySuite,
        totalCount: result.cases.length,
        isPaginated: true,
        projectId: projectIdNum,
        suiteId: suiteIdNum
      }
    });
  } catch (error: any) {
    logger.error(`[API] Error in POST /plugins/get_testrail_cases_for_suite: ${error.message}`);
    if (error.stack) {
      logger.error(`[API] Error stack trace: ${error.stack}`);
    }
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_testrail_cases
 * TestRail test case'lerini getirir
 *
 * Not: Bu endpoint artık TestRail API'nin 250 case limitini aşmak için pagination desteği içermektedir.
 * Tüm test case'leri otomatik olarak sayfalama yaparak getirir ve birleştirir.
 */
router.post('/get_testrail_cases', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    logger.info(`[API] get_testrail_cases endpoint called for team: ${teamId}, company: ${companyId}`);

    // İstek body'sinden parametreleri al (eğer sağlanmışsa)
    let { url, username, apiKey, projectId, suiteIds } = req.body;

    // Kullanıcının TestRail konfigürasyonunu getir
    const configResult = await getTestRailConfig(teamId, companyId);

    // Parametreler eksikse, kullanıcının mevcut TestRail konfigürasyonunu kullan
    if (!url || !username || !apiKey || !projectId) {
      logger.info('[API] Some parameters missing, using stored TestRail configuration');

      if (!configResult.success || !configResult.config) {
        return res.status(404).json({
          success: false,
          error: 'TestRail integration not configured',
          code: 'TESTRAIL_NOT_CONFIGURED',
          config: null
        });
      }

      // Eksik parametreleri konfigürasyondan tamamla
      url = url || configResult.config.url;
      username = username || configResult.config.username;
      apiKey = apiKey || configResult.config.apiKey;

      // projectId sağlanmamışsa ve yapılandırmada projectsData varsa, ilk projeyi kullan
      if (!projectId && configResult.config.projectsData && configResult.config.projectsData.length > 0) {
        projectId = configResult.config.projectsData[0].id;
        logger.info(`[API] Using default project ID from config: ${projectId}`);
      }

      // suiteIds sağlanmamışsa ve yapılandırmada suitesData varsa, tüm suiteId'leri kullan
      if (!suiteIds && configResult.config.suitesData && configResult.config.suitesData.length > 0) {
        suiteIds = configResult.config.suitesData.map((suite: { id: string | number }) => suite.id);
        logger.info(`[API] Using suite IDs from config: ${JSON.stringify(suiteIds)}`);
      }
    }

    // Gerekli parametrelerin mevcut olduğunu kontrol et
    if (!url || !username || !apiKey) {
      logger.error('[API] Missing required TestRail credentials');
      return res.status(400).json({
        success: false,
        error: 'TestRail bağlantı bilgileri eksik',
        config: configResult.success ? configResult.config : null
      });
    }

    if (!projectId) {
      logger.error('[API] Missing required projectId');
      return res.status(400).json({
        success: false,
        error: 'projectId bulunamadı',
        config: configResult.success ? configResult.config : null
      });
    }

    // SuiteId'leri doğrula
    let suiteIdsArray: number[] = [];

    // Çoklu suite ID'leri için dizi olarak gönderildiyse
    if (Array.isArray(suiteIds)) {
      suiteIdsArray = suiteIds
        .map(id => typeof id === 'number' ? id : parseInt(id))
        .filter(id => !isNaN(id) && id > 0);
    }
    // String olarak gönderildiyse (virgülle ayrılmış)
    else if (typeof suiteIds === 'string') {
      suiteIdsArray = suiteIds.split(',')
        .map(id => parseInt(id.trim()))
        .filter(id => !isNaN(id) && id > 0);
    }
    // Tek bir sayı olarak gönderildiyse
    else if (typeof suiteIds === 'number') {
      suiteIdsArray = [suiteIds];
    }

    // Eğer suite ID'ler yoksa veya boşsa, API çağrısına ilerlemeden önce hata ver
    if (suiteIdsArray.length === 0) {
      logger.error('[API] No valid suiteIds provided or found in config');
      return res.status(400).json({
        success: false,
        error: 'Geçerli Test Suite bulunamadı',
        config: configResult.success ? configResult.config : null
      });
    }

    // Project ID'yi doğrula
    const projectIdNum = typeof projectId === 'number' ? projectId : parseInt(projectId);
    if (isNaN(projectIdNum) || projectIdNum <= 0) {
      logger.error(`[API] Invalid projectId: ${projectId}`);
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir projectId parametresi zorunludur',
        config: configResult.success ? configResult.config : null
      });
    }

    logger.info(`[API] Fetching TestRail cases for project ${projectIdNum}, suites: ${suiteIdsArray.join(', ')}`);

    // TestRail case'lerini getir
    const startTime = Date.now();
    const result = await getTestRailCasesMultipleSuites(url, username, apiKey, projectIdNum, suiteIdsArray);
    const duration = Date.now() - startTime;

    logger.info(`[API] TestRail API request completed in ${duration}ms with success: ${result.success}`);

    if (!result.success) {
      logger.error(`[API] Failed to fetch TestRail cases: ${result.message}`);
      return res.status(400).json({
        success: false,
        error: result.message,
        config: configResult.success ? configResult.config : null
      });
    }

    // Cases istatistiklerini logla
    const totalCaseCount = result.cases.length;
    const suiteCounts = Object.entries(result.casesBySuite).map(([suiteId, cases]) =>
      `Suite ${suiteId}: ${cases.length} cases`
    );


    logger.info(`[API] Case counts by suite: ${suiteCounts.join(', ')}`);

    // Başarılı yanıt - config bilgisini de ekleyerek döndür
    res.json({
      success: true,
      message: result.message,
      data: {
        cases: result.cases,
        casesBySuite: result.casesBySuite,
        totalCount: result.cases.length,
        isPaginated: true, // Pagination desteği olduğunu belirt
        config: configResult.success ? configResult.config : null,
        plugin: configResult.success ? {
          id: configResult.plugin.id,
          name: configResult.plugin.name,
          description: configResult.plugin.description,
          active: configResult.plugin.active,
          createdAt: configResult.plugin.createdAt,
          updatedAt: configResult.plugin.updatedAt
        } : null
      }
    });
  } catch (error: any) {
    logger.error(`[API] Error in POST /plugins/get_testrail_cases: ${error.message}`);
    if (error.stack) {
      logger.error(`[API] Error stack trace: ${error.stack}`);
    }
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/plugins/disconnect_testrail
 * TestRail konfigürasyonunu siler (disconnect)
 */
router.delete('/disconnect_testrail', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı, takım ve şirket kimliklerini doğrulanmış kullanıcıdan al
    const userId = req.user?.id?.toString() || '';
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı kimliği bulunamadı'
      });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Takım veya şirket kimliği bulunamadı'
      });
    }

    logger.info(`[API] disconnect_testrail endpoint called for team: ${teamId}, company: ${companyId}`);

    // TestRail konfigürasyonunu sil
    const result = await deleteTestRailConfig(teamId, companyId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in DELETE /plugins/disconnect_testrail: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_testrail_case_steps
 * Belirli bir TestRail case'inin detaylarını ve adımlarını getirir
 */
router.post('/get_testrail_case_steps', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Case ID'yi al
    const { caseId } = req.body;

    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    logger.info(`[API] get_testrail_case_steps endpoint called for case: ${caseId} for team: ${teamId}, company: ${companyId}`);

    if (!caseId) {
      logger.error('[API] Missing required caseId parameter');
      return res.status(400).json({
        success: false,
        error: 'caseId parametresi zorunludur'
      });
    }

    // Get TestRail configuration from database
    const testrailConfig = await getTestRailConfig(teamId, companyId);
    if (!testrailConfig.success || !testrailConfig.config) {
      logger.debug('[API] TestRail configuration not found for user - this is normal if not configured');
      return res.status(404).json({
        success: false,
        error: 'TestRail integration not configured',
        code: 'TESTRAIL_NOT_CONFIGURED'
      });
    }

    const { url: testrailUrl, username: testrailUsername, apiKey: testrailApiKey } = testrailConfig.config;

    // Case ID'yi düzgün formata çevir (gerekirse)
    const caseIdNum = typeof caseId === 'string' ? parseInt(caseId) : caseId;

    if (isNaN(caseIdNum) || caseIdNum <= 0) {
      logger.error(`[API] Invalid caseId: ${caseId}`);
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir caseId parametresi zorunludur'
      });
    }

    logger.info(`[API] Fetching TestRail case steps for case ID: ${caseIdNum}`);

    // TestRail case adımlarını getir
    const result = await getTestRailCaseSteps(testrailUrl, testrailUsername, testrailApiKey, caseIdNum);

    if (!result.success) {
      logger.error(`[API] Failed to fetch TestRail case steps: ${result.message}`);
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      caseData: result.case
    });
  } catch (error: any) {
    logger.error(`[API] Error in POST /plugins/get_testrail_case_steps: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== ZEPHYR SCALE ROUTES =====

/**
 * POST /api/plugins/zephyrscale-health
 * Test Zephyr Scale connection
 */
router.post('/zephyrscale-health', async (req: Request, res: Response) => {
  try {
    const { apiToken } = req.body;

    if (!apiToken) {
      return res.status(400).json({
        success: false,
        error: 'API token is required',
        errorCode: 'MISSING_PARAMETERS',
        troubleshooting: ['Please provide your Zephyr Scale API token']
      });
    }

    logger.info(`[API] Testing Zephyr Scale connection`);

    // Test Zephyr Scale connection
    const result = await verifyZephyrScaleConnection(apiToken);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message,
        errorCode: 'CONNECTION_FAILED',
        troubleshooting: [
          'Verify your API token is correct',
          'Check your Zephyr Scale permissions',
          'Ensure you have access to at least one project'
        ]
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      data: {
        projectCount: result.data?.totalProjects || 0,
        connectionVerified: true
      }
    });
  } catch (error: any) {
    logger.error(`[API] Error testing Zephyr Scale connection: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Unexpected error occurred during Zephyr Scale connection test',
      errorCode: 'INTERNAL_ERROR',
      details: error.message,
      troubleshooting: [
        'Wait a few minutes and try again',
        'Contact support if the problem persists'
      ]
    });
  }
});

/**
 * POST /api/plugins/get_zephyrscale_projects
 * Get Zephyr Scale projects
 */
router.post('/get_zephyrscale_projects', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    // Get API token from configuration
    const configResult = await getZephyrScaleConfig(teamId, companyId);
    if (!configResult.success || !configResult.data?.apiToken) {
      return res.status(400).json({
        success: false,
        error: 'Zephyr Scale configuration not found. Please configure Zephyr Scale first.'
      });
    }

    const apiToken = configResult.data.apiToken;

    logger.info(`[API] Fetching Zephyr Scale projects`);

    // Get Zephyr Scale projects
    const result = await getZephyrScaleProjects(apiToken);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response - result already contains success and data
    res.json(result);
  } catch (error: any) {
    logger.error(`[API] Error fetching Zephyr Scale projects: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_zephyrscale_folders
 * Get Zephyr Scale folders for a project
 */
router.post('/get_zephyrscale_folders', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    const { projectKey } = req.body;

    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    if (!projectKey) {
      return res.status(400).json({
        success: false,
        error: 'Project key is required'
      });
    }

    // Get API token from configuration
    const configResult = await getZephyrScaleConfig(teamId, companyId);
    if (!configResult.success || !configResult.data?.apiToken) {
      return res.status(404).json({
        success: false,
        error: 'Zephyr Scale integration not configured',
        code: 'ZEPHYRSCALE_NOT_CONFIGURED'
      });
    }

    const apiToken = configResult.data.apiToken;

    logger.info(`[API] Fetching Zephyr Scale folders for project: ${projectKey}`);

    // Get Zephyr Scale folders
    const result = await getZephyrScaleFolders(apiToken, projectKey);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response - result already contains success and data
    res.json(result);
  } catch (error: any) {
    logger.error(`[API] Error fetching Zephyr Scale folders: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_zephyrscale_testcases
 * Get Zephyr Scale test cases for a project
 */
router.post('/get_zephyrscale_testcases', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    const { projectKey, folderId } = req.body;

    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    if (!projectKey) {
      return res.status(400).json({
        success: false,
        error: 'Project key is required'
      });
    }

    // Get API token from configuration
    const configResult = await getZephyrScaleConfig(teamId, companyId);
    if (!configResult.success || !configResult.data?.apiToken) {
      return res.status(400).json({
        success: false,
        error: 'Zephyr Scale configuration not found. Please configure Zephyr Scale first.'
      });
    }

    const apiToken = configResult.data.apiToken;

    logger.info(`[API] Fetching Zephyr Scale test cases for project: ${projectKey}${folderId ? `, folder: ${folderId}` : ''}`);

    // Get Zephyr Scale test cases
    const result = await getZephyrScaleTestCases(apiToken, projectKey, folderId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response - result already contains success and data
    res.json(result);
  } catch (error: any) {
    logger.error(`[API] Error fetching Zephyr Scale test cases: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/save_zephyrscale_config
 * Save Zephyr Scale configuration
 */
router.post('/save_zephyrscale_config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    const { apiToken, projectsData, foldersData } = req.body;

    if (!apiToken) {
      return res.status(400).json({
        success: false,
        error: 'API token is required'
      });
    }

    logger.info(`[API] Saving Zephyr Scale configuration for team: ${teamId}, company: ${companyId}`);

    // Save Zephyr Scale configuration
    const result = await updateZephyrScaleConfig(
      teamId,
      companyId,
      apiToken,
      projectsData || [],
      foldersData || []
    );

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response - result already contains success, message and data
    res.json(result);
  } catch (error: any) {
    logger.error(`[API] Error saving Zephyr Scale configuration: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/plugins/get_zephyrscale_config
 * Get Zephyr Scale configuration
 */
router.get('/get_zephyrscale_config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    logger.info(`[API] Getting Zephyr Scale configuration for team: ${teamId}, company: ${companyId}`);

    // Get Zephyr Scale configuration
    const result = await getZephyrScaleConfig(teamId, companyId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    // Remove sensitive data from response
    const sanitizedConfig = result.data ? {
      projectsData: result.data.projectsData || [],
      foldersData: result.data.foldersData || [],
      updatedAt: result.data.updatedAt
      // apiToken is intentionally excluded for security
    } : null;

    // Successful response
    res.json({
      success: true,
      config: sanitizedConfig,
      plugin: result.plugin
    });
  } catch (error: any) {
    logger.error(`[API] Error getting Zephyr Scale configuration: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/plugins/disconnect_zephyrscale
 * Delete Zephyr Scale configuration (disconnect)
 */
router.delete('/disconnect_zephyrscale', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    logger.info(`[API] Disconnecting Zephyr Scale for team: ${teamId}, company: ${companyId}`);

    // Delete Zephyr Scale configuration
    const result = await deleteZephyrScaleConfig(teamId, companyId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error disconnecting Zephyr Scale: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/get_zephyrscale_testcase_details
 * Get detailed information for a specific Zephyr Scale test case
 */
router.post('/get_zephyrscale_testcase_details', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    const { testCaseKey, apiToken } = req.body;

    // Get team and company info from authenticated user
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required'
      });
    }

    if (!testCaseKey) {
      return res.status(400).json({
        success: false,
        error: 'Test case key is required'
      });
    }

    // Use provided API token or get from configuration
    let zephyrApiToken = apiToken;
    if (!zephyrApiToken) {
      const configResult = await getZephyrScaleConfig(teamId, companyId);
      if (!configResult.success || !configResult.data?.apiToken) {
        return res.status(400).json({
          success: false,
          error: 'Zephyr Scale configuration not found. Please configure Zephyr Scale first.'
        });
      }
      zephyrApiToken = configResult.data.apiToken;
    }

    logger.info(`[API] Getting Zephyr Scale test case details for: ${testCaseKey}`);

    // Get test case details
    const result = await getZephyrScaleTestCaseDetails(zephyrApiToken, testCaseKey);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`[API] Error getting Zephyr Scale test case details: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/saucelabs-health
 * Test SauceLabs connection
 */
router.post('/saucelabs-health', async (req: Request, res: Response) => {
  try {
    const { username, accessKey, region } = req.body;

    if (!username || !accessKey) {
      return res.status(400).json({
        success: false,
        error: 'username and accessKey parameters are required',
        errorCode: 'MISSING_PARAMETERS',
        troubleshooting: ['SauceLabs kullanıcı adını ve Access Key\'i girin']
      });
    }

    logger.info(`[API] Testing SauceLabs connection with username: ${username}`);

    // Test SauceLabs connection
    const result = await verifySauceLabsConnection(username, accessKey, region);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.error,
        errorCode: result.errorCode,
        details: result.details,
        troubleshooting: result.troubleshooting
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`[API] Error testing SauceLabs connection: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'SauceLabs bağlantı testi sırasında beklenmeyen bir hata oluştu',
      errorCode: 'INTERNAL_ERROR',
      details: error.message,
      troubleshooting: [
        'Birkaç dakika bekleyip tekrar deneyin',
        'Sorun devam ederse destek ekibi ile iletişime geçin'
      ]
    });
  }
});

/**
 * POST /api/plugins/update-saucelabs-config
 * Update SauceLabs configuration
 */
router.post('/update-saucelabs-config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;
    const userId = req.user?.id; // For tracking purposes only

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    const { username, accessKey, region } = req.body;

    if (!username || !accessKey) {
      return res.status(400).json({
        success: false,
        error: 'username and accessKey parameters are required'
      });
    }

    logger.info(`[API] Updating SauceLabs configuration for user: ${userId}`);

    // Update SauceLabs configuration
    const result = await updateSauceLabsConfig(companyId, teamId || '', {
      username,
      accessKey,
      region
    }, userId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error updating SauceLabs configuration: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error updating SauceLabs configuration: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-saucelabs-config
 * Get SauceLabs configuration
 */
router.get('/get-saucelabs-config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Getting SauceLabs configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Get SauceLabs configuration
    const result = await getSauceLabsConfig(companyId, teamId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response (even if no configuration exists)
    res.json({
      success: true,
      message: result.message,
      config: result.config,
      plugin: result.plugin
    });
  } catch (error: any) {
    logger.error(`[API] Error getting SauceLabs configuration: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting SauceLabs configuration: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-saucelabs-devices
 * Get SauceLabs devices
 */
router.get('/get-saucelabs-devices', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Getting SauceLabs devices for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Get SauceLabs devices
    const result = await getSauceLabsDevices(companyId, teamId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      devices: result.devices
    });
  } catch (error: any) {
    logger.error(`[API] Error getting SauceLabs devices: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting SauceLabs devices: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-saucelabs-account-info
 * Get SauceLabs account information
 */
router.get('/get-saucelabs-account-info', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Getting SauceLabs account information for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Get SauceLabs account information
    const result = await getSauceLabsAccountInfo(companyId, teamId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`[API] Error getting SauceLabs account information: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting SauceLabs account information: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-saucelabs-apps
 * Get SauceLabs apps
 */
router.get('/get-saucelabs-apps', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Getting SauceLabs apps for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Get SauceLabs apps
    const result = await getSauceLabsApps(companyId, teamId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response (even if no apps or configuration)
    res.json({
      success: true,
      message: result.message,
      apps: result.apps
    });
  } catch (error: any) {
    logger.error(`[API] Error getting SauceLabs apps: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting SauceLabs apps: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-saucelabs-app-groups
 * Get SauceLabs app groups
 */
router.get('/get-saucelabs-app-groups', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Getting SauceLabs app groups for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Get SauceLabs app groups
    const result = await getSauceLabsAppGroups(companyId, teamId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response (even if no groups or configuration)
    res.json({
      success: true,
      message: result.message,
      groups: result.groups
    });
  } catch (error: any) {
    logger.error(`[API] Error getting SauceLabs app groups: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting SauceLabs app groups: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/upload-saucelabs-app
 * Upload app to SauceLabs
 *
 * The file should be sent as form-data with the field name 'app'.
 */
router.post('/upload-saucelabs-app', authenticate, handleSauceLabsAppUpload, async (req: FileRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;
    const teamId = authReq.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No app file uploaded'
      });
    }

    const fileName = req.file.originalname;
    const fileBuffer = req.file.buffer;
    const description = req.body.description;

    logger.info(`[API] Uploading app ${fileName} to SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Upload app to SauceLabs
    const result = await uploadSauceLabsApp(companyId, teamId, fileBuffer, fileName, description);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      app: result.app
    });
  } catch (error: any) {
    logger.error(`[API] Error uploading app to SauceLabs: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error uploading app to SauceLabs: ${error.message}`
    });
  }
});

/**
 * DELETE /api/plugins/delete-saucelabs-app/:id
 * Delete app from SauceLabs
 */
router.delete('/delete-saucelabs-app/:id', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    const fileId = req.params.id;

    if (!fileId) {
      return res.status(400).json({
        success: false,
        error: 'File ID parameter is required'
      });
    }

    logger.info(`[API] Deleting app ${fileId} from SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Delete app from SauceLabs
    const result = await deleteSauceLabsApp(companyId, teamId, fileId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error deleting app from SauceLabs: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error deleting app from SauceLabs: ${error.message}`
    });
  }
});

/**
 * DELETE /api/plugins/disconnect-saucelabs
 * Disconnect SauceLabs integration
 */
router.delete('/disconnect-saucelabs', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Disconnecting SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Delete SauceLabs plugin from the plugins collection
    const queryFilter: any = { companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    if (!isMongoDBInitialized() || !pluginsCollection) {
      return res.status(500).json({
        success: false,
        message: 'Database connection not established'
      });
    }

    // Remove the saucelabs plugin from the plugins array
    const result = await pluginsCollection.updateOne(
      queryFilter,
      {
        $pull: { plugins: { id: 'saucelabs' } } as any,
        $set: { updatedAt: new Date() }
      }
    );

    if (result.modifiedCount > 0) {
      logger.info(`[API] Successfully disconnected SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);
      return res.json({
        success: true,
        message: 'SauceLabs integration has been disconnected successfully'
      });
    } else {
      return res.status(404).json({
        success: false,
        error: 'No SauceLabs configuration found to delete'
      });
    }
  } catch (error: any) {
    logger.error(`[API] Error disconnecting SauceLabs: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error disconnecting SauceLabs: ${error.message}`
    });
  }
});

/**
 * DELETE /api/plugins/disconnect-testinium
 * Disconnect Testinium integration
 */
router.delete('/disconnect-testinium', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Disconnecting Testinium for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Delete Testinium configuration
    const result = await deleteTestiniumConfig(companyId, teamId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error disconnecting Testinium: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error disconnecting Testinium: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/update-saucelabs-app-settings
 * Update SauceLabs app settings
 */
router.post('/update-saucelabs-app-settings', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    const { groupId, settings } = req.body;

    if (!groupId || !settings) {
      return res.status(400).json({
        success: false,
        error: 'groupId and settings parameters are required'
      });
    }

    logger.info(`[API] Updating app settings for group ${groupId} in SauceLabs for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Update app settings in SauceLabs
    const result = await updateSauceLabsAppSettings(companyId, teamId, groupId, settings);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      settings: result.settings
    });
  } catch (error: any) {
    logger.error(`[API] Error updating app settings in SauceLabs: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error updating app settings in SauceLabs: ${error.message}`
    });
  }
});

// ============================================================================
// JIRA ROUTES
// ============================================================================

/**
 * POST /api/plugins/jira-health
 * Test Jira connection
 */
router.post('/jira-health', async (req: Request, res: Response) => {
  try {
    const { url, email, apiToken } = req.body;

    // Input sanitization
    const trimmedUrl = url?.trim();
    const trimmedEmail = email?.trim();
    const trimmedApiToken = apiToken?.trim();

    // Detailed input validation
    if (!trimmedUrl) {
      return res.status(400).json({
        success: false,
        error: 'Jira URL alanı zorunludur ve boş olamaz',
        errorCode: 'MISSING_URL',
        field: 'url'
      });
    }

    if (!trimmedEmail) {
      return res.status(400).json({
        success: false,
        error: 'Email alanı zorunludur ve boş olamaz',
        errorCode: 'MISSING_EMAIL',
        field: 'email'
      });
    }

    if (!trimmedApiToken) {
      return res.status(400).json({
        success: false,
        error: 'API Token alanı zorunludur ve boş olamaz',
        errorCode: 'MISSING_API_TOKEN',
        field: 'apiToken'
      });
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(trimmedEmail)) {
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir email adresi girin',
        errorCode: 'INVALID_EMAIL_FORMAT',
        field: 'email'
      });
    }

    // Enhanced URL format validation - support both Cloud and Server
    const isValidJiraUrl = (url: string): boolean => {
      // Jira Cloud pattern: https://yourdomain.atlassian.net
      const cloudPattern = /^https:\/\/[a-zA-Z0-9-]+\.atlassian\.net$/;
      // Jira Server pattern: https://your-server.com or https://your-server.com:port
      const serverPattern = /^https?:\/\/[a-zA-Z0-9.-]+(:[0-9]+)?$/;

      return cloudPattern.test(url) || serverPattern.test(url);
    };

    if (!isValidJiraUrl(trimmedUrl)) {
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir Jira URL girin (Cloud: https://yourdomain.atlassian.net veya Server: https://your-server.com)',
        errorCode: 'INVALID_URL_FORMAT',
        field: 'url'
      });
    }

    logger.info(`[API] Testing Jira connection with URL: ${trimmedUrl}, email: ${trimmedEmail}`);

    // Test Jira connection
    const result = await verifyJiraConnection(trimmedUrl, trimmedEmail, trimmedApiToken);

    if (!result.success) {
      // Return specific error codes for different failure types
      const statusCode = (() => {
        switch (result.errorCode) {
          case 'AUTHENTICATION_FAILED':
          case 'ACCESS_DENIED':
            return 401;
          case 'INSTANCE_NOT_FOUND':
            return 404;
          case 'NETWORK_ERROR':
          case 'TIMEOUT':
            return 503;
          case 'INVALID_URL_FORMAT':
            return 400;
          default:
            return 500;
        }
      })();

      return res.status(statusCode).json({
        success: false,
        error: result.message,
        errorCode: result.errorCode
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message || 'Jira bağlantısı başarılı',
      data: result.data
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/jira-health: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Bağlantı testi sırasında beklenmeyen bir hata oluştu'
    });
  }
});

/**
 * POST /api/plugins/get_jira_projects
 * Get Jira projects
 */
router.post('/get_jira_projects', async (req: AuthRequest, res: Response) => {
  try {
    // Get Jira credentials from request body
    const { url, email, apiToken } = req.body;

    // Input validation
    if (!url || !email || !apiToken) {
      return res.status(400).json({
        success: false,
        error: 'url, email ve apiToken parametreleri zorunludur',
        errorCode: 'MISSING_PARAMETERS'
      });
    }

    logger.info(`[API] Fetching Jira projects with provided credentials`);

    // Get Jira projects
    const result = await getJiraProjects(url, email, apiToken);

    if (!result.success) {
      // Map error codes to appropriate HTTP status codes
      const statusCode = (() => {
        switch (result.errorCode) {
          case 'AUTHENTICATION_FAILED':
            return 401;
          case 'ACCESS_DENIED':
            return 403;
          case 'ENDPOINT_NOT_FOUND':
            return 404;
          case 'RATE_LIMITED':
            return 429;
          case 'NETWORK_ERROR':
            return 503;
          default:
            return 400;
        }
      })();

      return res.status(statusCode).json({
        success: false,
        error: result.message,
        errorCode: result.errorCode
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/get_jira_projects: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      errorCode: 'INTERNAL_ERROR'
    });
  }
});

/**
 * POST /api/plugins/get_jira_issue_types
 * Get Jira issue types for a project
 */
router.post('/get_jira_issue_types', async (req: AuthRequest, res: Response) => {
  try {
    // Get Jira credentials and project key from request body
    const { url, email, apiToken, projectKey } = req.body;

    // Input validation
    if (!url || !email || !apiToken || !projectKey) {
      return res.status(400).json({
        success: false,
        error: 'url, email, apiToken ve projectKey parametreleri zorunludur',
        errorCode: 'MISSING_PARAMETERS'
      });
    }

    logger.info(`[API] Fetching Jira issue types for project: ${projectKey}`);

    // Get Jira issue types
    const result = await getJiraIssueTypes(url, email, apiToken, projectKey);

    if (!result.success) {
      // Map error codes to appropriate HTTP status codes
      const statusCode = (() => {
        switch (result.errorCode) {
          case 'AUTHENTICATION_FAILED':
            return 401;
          case 'ACCESS_DENIED':
            return 403;
          case 'PROJECT_NOT_FOUND':
            return 404;
          case 'RATE_LIMITED':
            return 429;
          case 'SERVER_ERROR':
            return 503;
          case 'NETWORK_ERROR':
          case 'TIMEOUT':
            return 503;
          default:
            return 400;
        }
      })();

      return res.status(statusCode).json({
        success: false,
        error: result.message,
        errorCode: result.errorCode
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/get_jira_issue_types: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      errorCode: 'INTERNAL_ERROR'
    });
  }
});

/**
 * POST /api/plugins/save_jira_config
 * Save Jira configuration for team and company
 */
router.post('/save_jira_config', async (req: AuthRequest, res: Response) => {
  try {
    // Get user identities from authenticated user
    const userId = req.user?.id?.toString() || '';
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!userId || !teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı kimlik bilgileri bulunamadı'
      });
    }

    const { url, email, apiToken, projectsData, issueTypesData } = req.body;

    if (!url || !email || !apiToken) {
      return res.status(400).json({
        success: false,
        error: 'url, email ve apiToken parametreleri zorunludur'
      });
    }

    logger.info(`[API] Saving Jira configuration for team: ${teamId}, company: ${companyId}`);

    // Update Jira configuration
    const result = await updateJiraConfig(
      userId,
      teamId,
      companyId,
      url,
      email,
      apiToken,
      projectsData || [],
      issueTypesData || []
    );

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/save_jira_config: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/plugins/get_jira_config
 * Get Jira configuration for team and company
 */
router.get('/get_jira_config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get user identities from authenticated user
    const userId = req.user?.id?.toString() || '';
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      logger.info(`[API] Fetching Jira configuration for user: ${userId}`);

      // Get Jira configuration for user
      const result = await getJiraConfig(undefined, undefined, userId);

      if (!result.success) {
        return res.status(404).json({
          success: false,
          error: result.message
        });
      }

      return res.json({
        success: true,
        message: result.message,
        data: result.data
      });
    }

    logger.info(`[API] Fetching Jira configuration for team: ${teamId}, company: ${companyId}`);

    // Get Jira configuration for team/company
    const result = await getJiraConfig(teamId, companyId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      message: result.message,
      data: result.data
    });

  } catch (error: any) {
    logger.error(`API Error in GET /plugins/get_jira_config: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/plugins/disconnect_jira
 * Disconnect Jira integration
 */
router.delete('/disconnect_jira', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get user identities from authenticated user
    const userId = req.user?.id?.toString() || '';
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!teamId || !companyId) {
      logger.info(`[API] Disconnecting Jira for user: ${userId}`);

      const result = await deleteJiraConfig(undefined, undefined, userId);

      if (!result.success) {
        return res.status(404).json({
          success: false,
          error: result.message
        });
      }

      return res.json({
        success: true,
        message: result.message
      });
    }

    logger.info(`[API] Disconnecting Jira for team: ${teamId}, company: ${companyId}`);

    const result = await deleteJiraConfig(teamId, companyId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    return res.json({
      success: true,
      message: result.message
    });

  } catch (error: any) {
    logger.error(`API Error in DELETE /plugins/disconnect_jira: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/plugins/create_jira_issue
 * Create Jira issue from test report
 */
router.post('/create_jira_issue', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get user ID from authenticated user
    const userId = req.user?.id?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı kimlik bilgileri bulunamadı'
      });
    }

    const { reportId, projectKey, issueTypeId, summary, description } = req.body;

    if (!reportId || !projectKey || !issueTypeId || !summary) {
      return res.status(400).json({
        success: false,
        error: 'reportId, projectKey, issueTypeId ve summary parametreleri zorunludur'
      });
    }

    logger.info(`[API] Creating Jira issue for report: ${reportId}, user: ${userId}`);

    // Send report to Jira
    const result = await sendReportToJira(
      reportId,
      userId,
      projectKey,
      issueTypeId,
      summary,
      description
    );

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`API Error in POST /plugins/create_jira_issue: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ============================================================================
// TESTINIUM ROUTES
// ============================================================================

/**
 * POST /api/plugins/testinium-health
 * Test Testinium connection
 */
router.post('/testinium-health', async (req: Request, res: Response) => {
  try {
    logger.info(`[API] Testinium health check request body:`, req.body);
    const { apiUrl, clientId, clientSecret, issuerUri } = req.body;

    logger.info(`[API] Extracted parameters - apiUrl: ${apiUrl}, clientId: ${clientId}, clientSecret: ${clientSecret ? '[REDACTED]' : 'undefined'}, issuerUri: ${issuerUri}`);

    if (!apiUrl || !clientId || !clientSecret || !issuerUri) {
      logger.warn(`[API] Missing required parameters for Testinium health check`);
      return res.status(400).json({
        success: false,
        error: 'apiUrl, clientId, clientSecret, and issuerUri parameters are required'
      });
    }

    logger.info(`[API] Testing Testinium connection with clientId: ${clientId}`);

    // Test Testinium connection
    const result = await verifyTestiniumConnection(apiUrl, clientId, clientSecret, issuerUri);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message,
        details: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: 'Testinium connection successful'
    });
  } catch (error: any) {
    logger.error(`[API] Error testing Testinium connection: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error testing Testinium connection: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/update-testinium-config
 * Update Testinium configuration
 */
router.post('/update-testinium-config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    const { apiUrl, clientId, clientSecret, issuerUri } = req.body;

    if (!apiUrl || !clientId || !clientSecret || !issuerUri) {
      return res.status(400).json({
        success: false,
        error: 'apiUrl, clientId, clientSecret, and issuerUri parameters are required'
      });
    }

    logger.info(`[API] Updating Testinium configuration for team: ${teamId}, company: ${companyId}`);

    // Update Testinium configuration
    const result = await updateTestiniumConfig(teamId, companyId, {
      apiUrl,
      clientId,
      clientSecret,
      issuerUri
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error updating Testinium configuration: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error updating Testinium configuration: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-testinium-config
 * Get Testinium configuration
 */
router.get('/get-testinium-config', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    logger.info(`[API] Getting Testinium configuration for team: ${teamId}, company: ${companyId}`);

    // Get Testinium configuration
    const result = await getTestiniumConfig(teamId, companyId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      config: result.config,
      plugin: result.plugin
    });
  } catch (error: any) {
    logger.error(`[API] Error getting Testinium configuration: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting Testinium configuration: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-testinium-devices
 * Get Testinium devices
 */
router.get('/get-testinium-devices', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    logger.info(`[API] Getting Testinium devices for team: ${teamId}, company: ${companyId}`);

    // Get Testinium devices
    const result = await getTestiniumDevices(teamId, companyId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      devices: result.devices
    });
  } catch (error: any) {
    logger.error(`[API] Error getting Testinium devices: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting Testinium devices: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/testinium-allocate
 * Create device allocation
 */
router.post('/testinium-allocate', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    const allocationRequest = req.body.allocationRequest;

    if (!allocationRequest) {
      return res.status(400).json({
        success: false,
        error: 'allocationRequest parameter is required'
      });
    }

    logger.info(`[API] Creating Testinium allocation for team: ${teamId}, company: ${companyId}`);

    // Create allocation
    const result = await createTestiniumAllocation(teamId, companyId, allocationRequest);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      allocation: result.allocation
    });
  } catch (error: any) {
    logger.error(`[API] Error creating Testinium allocation: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error creating Testinium allocation: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/testinium-start-session
 * Start device session
 */
router.post('/testinium-start-session', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    const sessionRequest = req.body.sessionRequest;

    if (!sessionRequest) {
      return res.status(400).json({
        success: false,
        error: 'sessionRequest parameter is required'
      });
    }

    logger.info(`[API] Starting Testinium session for team: ${teamId}, company: ${companyId}`);

    // Start session
    const result = await startTestiniumSession(teamId, companyId, sessionRequest);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      session: result.session
    });
  } catch (error: any) {
    logger.error(`[API] Error starting Testinium session: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error starting Testinium session: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/testinium-close-session
 * Close device session
 */
router.post('/testinium-close-session', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get team and company info from authenticated user
    const teamId = req.user?.teamId;
    const companyId = req.user?.companyId;

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required. Please ensure you are associated with a team and company.'
      });
    }

    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'sessionId parameter is required'
      });
    }

    logger.info(`[API] Closing Testinium session for team: ${teamId}, company: ${companyId}`);

    // Close session
    const result = await closeTestiniumSession(teamId, companyId, sessionId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error closing Testinium session: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error closing Testinium session: ${error.message}`
    });
  }
});

/**
 * POST /api/plugins/upload-testinium-app
 * Upload app to Testinium
 *
 * The file should be sent as form-data with the field name 'app'.
 */
router.post('/upload-testinium-app', authenticate, handleSauceLabsAppUpload, async (req: FileRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;
    const teamId = authReq.user?.teamId;
    const userId = authReq.user?.id;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No app file uploaded'
      });
    }

    const fileName = req.file.originalname;
    const fileBuffer = req.file.buffer;
    const description = req.body.description;

    logger.info(`[API] Uploading app ${fileName} to Testinium for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Upload app to Testinium
    const result = await uploadTestiniumApp(companyId, teamId, fileBuffer, fileName, description, userId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      app: result.app
    });
  } catch (error: any) {
    logger.error(`[API] Error uploading app to Testinium: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error uploading app to Testinium: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/get-testinium-apps
 * Get Testinium apps
 */
router.get('/get-testinium-apps', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    logger.info(`[API] Getting Testinium apps for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Get Testinium apps
    const result = await getTestiniumApps(companyId, teamId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message,
      apps: result.apps
    });
  } catch (error: any) {
    logger.error(`[API] Error getting Testinium apps: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error getting Testinium apps: ${error.message}`
    });
  }
});

/**
 * DELETE /api/plugins/delete-testinium-app/:appId
 * Delete app from Testinium
 */
router.delete('/delete-testinium-app/:appId', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;
    const { appId } = req.params;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    if (!appId) {
      return res.status(400).json({
        success: false,
        error: 'App ID is required'
      });
    }

    logger.info(`[API] Deleting Testinium app ${appId} for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Delete app from Testinium
    const result = await deleteTestiniumApp(companyId, teamId, appId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Successful response
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`[API] Error deleting Testinium app: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error deleting Testinium app: ${error.message}`
    });
  }
});

/**
 * GET /api/plugins/download-testinium-app/:appId
 * Download app from Testinium
 */
router.get('/download-testinium-app/:appId', authenticate, async (req: AuthRequest, res: Response) => {
  try {
    // Get company and team info from authenticated user
    const companyId = req.user?.companyId;
    const teamId = req.user?.teamId;
    const { appId } = req.params;

    if (!companyId) {
      return res.status(400).json({
        success: false,
        error: 'Company ID is required. Please ensure you are associated with a company.'
      });
    }

    if (!appId) {
      return res.status(400).json({
        success: false,
        error: 'App ID is required'
      });
    }

    logger.info(`[API] Downloading Testinium app ${appId} for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Download app from Testinium
    const result = await downloadTestiniumApp(companyId, teamId, appId);

    if (!result.success || !result.stream) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Set appropriate headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${result.fileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // Pipe the stream to response
    result.stream.pipe(res);
  } catch (error: any) {
    logger.error(`[API] Error downloading Testinium app: ${error.message}`);
    res.status(500).json({
      success: false,
      error: `Error downloading Testinium app: ${error.message}`
    });
  }
});

// ============================================================================
// GENERIC PLUGIN ROUTES (Must be defined AFTER all specific routes)
// ============================================================================

/**
 * DELETE /api/plugins/:pluginName
 * Eklenti kaydını siler (Generic route - en son tanımlanmalı)
 */
router.delete('/:pluginName', async (req: AuthRequest, res: Response) => {
  try {
    // Kullanıcı kimliğini doğrulanmış kullanıcıdan al
    const userId = req.user?.id || req.body.userId?.toString();
    const teamId = req.user?.teamId?.toString() || '';
    const companyId = req.user?.companyId?.toString() || '';

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'userId parametresi zorunludur'
      });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({
        success: false,
        error: 'Takım veya şirket kimliği bulunamadı'
      });
    }

    const { pluginName } = req.params;

    logger.info(`[API] Deleting ${pluginName} plugin for user: ${userId}, team: ${teamId}, company: ${companyId}`);

    // Eklentiyi sil
    const result = await deletePlugin(pluginName, teamId, companyId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt
    res.json({
      success: true,
      message: result.message
    });
  } catch (error: any) {
    logger.error(`API Error in DELETE /plugins/${req.params.pluginName}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;