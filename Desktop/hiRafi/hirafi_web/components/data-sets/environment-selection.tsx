"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Layers, Loader2 } from "lucide-react"
import type { DataEnvironment } from "@/store/testDataSetsStore"

interface EnvironmentSelectionProps {
  environments: DataEnvironment[]
  selectedEnvironments: string[]
  isLoadingEnvironments: boolean
  onToggleEnvironment: (environmentId: string) => void
}

export function EnvironmentSelection({
  environments,
  selectedEnvironments,
  isLoadingEnvironments,
  onToggleEnvironment,
}: EnvironmentSelectionProps) {
  // Ensure selectedEnvironments is always an array
  const safeSelectedEnvironments = Array.isArray(selectedEnvironments)
    ? selectedEnvironments
    : []

  const activeEnvironments = environments.filter(env => env.isActive)

  if (isLoadingEnvironments) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5 text-indigo-500" />
            Environment Selection
          </CardTitle>
          <CardDescription>Choose which environments to include in this data set</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Loading environments...
          </div>
        </CardContent>
      </Card>
    )
  }

  if (activeEnvironments.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5 text-indigo-500" />
            Environment Selection
          </CardTitle>
          <CardDescription>Choose which environments to include in this data set</CardDescription>
        </CardHeader>
        <CardContent className="text-center py-8">
          <p className="text-muted-foreground">No active environments found</p>
          <p className="text-xs text-muted-foreground mt-1">
            Create environments in Test Data settings
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Layers className="h-5 w-5 text-indigo-500" />
          Environment Selection
        </CardTitle>
        <CardDescription>
          Choose which environments to include in this data set. Only selected environments will be saved and included in copy operations.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {activeEnvironments.map((env) => {
            const isSelected = safeSelectedEnvironments.includes(env.id)
            
            return (
              <div
                key={env.id}
                className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
                  isSelected
                    ? 'border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-900/20'
                    : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`w-3 h-3 rounded-full bg-${env.color || 'gray'}-500`}
                    title={`${env.color || 'gray'} environment`}
                  />
                  <div>
                    <Label
                      htmlFor={`env-${env.id}`}
                      className="text-sm font-medium cursor-pointer"
                    >
                      {env.name}
                    </Label>
                    {env.description && (
                      <p className="text-xs text-muted-foreground mt-0.5">
                        {env.description}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge
                    variant={env.type === 'default' ? 'secondary' : 'outline'}
                    className="text-xs"
                  >
                    {env.type}
                  </Badge>
                  <Switch
                    id={`env-${env.id}`}
                    checked={isSelected}
                    onCheckedChange={() => onToggleEnvironment(env.id)}
                    className="data-[state=checked]:bg-indigo-600"
                  />
                </div>
              </div>
            )
          })}
        </div>

        {safeSelectedEnvironments.length === 0 && (
          <div className="text-center py-4 text-sm text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
            ⚠️ No environments selected. Please select at least one environment to save the data set.
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          Selected: {safeSelectedEnvironments.length} of {activeEnvironments.length} environments
        </div>
      </CardContent>
    </Card>
  )
}
