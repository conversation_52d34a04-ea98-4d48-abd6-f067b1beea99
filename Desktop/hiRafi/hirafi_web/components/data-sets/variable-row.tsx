"use client"

import React, { memo, useCallback, useState, useEffect } from "react"
import { TableRow, TableCell } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"

import { Co<PERSON>, Trash2, Play, Loader2, Database, ChevronDown } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { useDebouncedVariableInput } from "@/hooks/useDebounce"
import type { VariableType } from "@/types/test-data"
import type { EnhancedDataVariable } from "@/types/data-generation"
import { dataSourceApi, testApiRequest, executeDbFlow } from "@/lib/api/test-data"

interface VariableRowProps {
  variable: EnhancedDataVariable
  showValues: boolean
  onUpdateVariable: (id: string, field: keyof EnhancedDataVariable, value: string | VariableType) => void
  onCopyValue: (value: string) => void
  onDuplicateVariable: (variable: any) => void
  onRemoveVariable: (id: string) => void
}

// Memoized component to prevent unnecessary re-renders
export const VariableRow = memo(function VariableRow({
  variable,
  showValues,
  onUpdateVariable,
  onCopyValue,
  onDuplicateVariable,
  onRemoveVariable,
}: VariableRowProps) {
  
  const [isTestingApi, setIsTestingApi] = useState(false)
  const [isTestingDatabase, setIsTestingDatabase] = useState(false)
  const [originalValue, setOriginalValue] = useState<string>('')
  const [isShowingTestResult, setIsShowingTestResult] = useState(false)
  const [availableColumns, setAvailableColumns] = useState<string[]>([])
  const [testNameValue, setTestNameValue] = useState<string>('')
  const [isDbTestMode, setIsDbTestMode] = useState(false)
  
  // Check if this is an API variable
  const isApiVariable = variable.sourceType === 'api' && variable.sourceId && variable.jsonPath
  
  // Check if this is a database variable
  const isDatabaseVariable = variable.sourceType === 'database'
  
  // Debounced inputs for better performance
  const nameInput = useDebouncedVariableInput(
    variable.id,
    "name",
    variable.name,
    onUpdateVariable,
    150
  )

  const valueInput = useDebouncedVariableInput(
    variable.id,
    "value",
    variable.value,
    onUpdateVariable,
    150
  )

  const descriptionInput = useDebouncedVariableInput(
    variable.id,
    "description",
    variable.description || "",
    onUpdateVariable,
    150
  )

  // API test handler
  const handleTestApi = useCallback(async () => {
    if (!isApiVariable) return
    
    setIsTestingApi(true)
    
    try {
      // Get the API data source
      const dataSourceResult = await dataSourceApi.getById(variable.sourceId!)
      if (!dataSourceResult.success || !dataSourceResult.data) {
        throw new Error('Failed to load API data source')
      }
      
      const dataSource = dataSourceResult.data
      
      // Prepare the API request
      const requestPayload = {
        method: dataSource.config?.method || 'GET',
        url: dataSource.config?.url,
        headers: dataSource.config?.headers || [],
        body: dataSource.config?.body || '',
        bodyType: dataSource.config?.bodyType || 'json',
        params: dataSource.config?.params || []
      }
      
      // Use the existing testApiRequest function
      const testResult = await testApiRequest(requestPayload)
      
      if (!testResult.success) {
        throw new Error('API test failed')
      }
      
      // Extract value using JSON path
      console.log('🔍 API Test Result:', testResult)
      console.log('📍 JSON Path:', variable.jsonPath)
      
      // Start from the API response root (testResult.data)
      let extractedValue = testResult.data
      console.log('📦 Initial data (response root):', extractedValue)
      
      const pathParts = variable.jsonPath!.replace(/^\$\./, '').split('.')
      console.log('🛤️ Path parts:', pathParts)
      
      // Parse the JSON path from the response root
      for (const part of pathParts) {
        if (extractedValue && typeof extractedValue === 'object' && part in extractedValue) {
          extractedValue = extractedValue[part]
          console.log(`📂 After extracting "${part}":`, extractedValue)
        } else {
          console.log(`❌ Cannot extract "${part}" from:`, extractedValue)
          console.log(`❌ Available keys:`, extractedValue && typeof extractedValue === 'object' ? Object.keys(extractedValue) : 'N/A')
          break
        }
      }
      
      const resultValue = extractedValue !== undefined && extractedValue !== null ? String(extractedValue) : 'No value found'
      console.log('✅ Final extracted value:', resultValue)
      
      // Store original value and update input with test result
      const currentValue = variable.value
      setOriginalValue(currentValue)
      setIsShowingTestResult(true)
      // Clear input first, then set the API result
      onUpdateVariable(variable.id, 'value', resultValue)
      
      // Restore original value after 10 seconds
      setTimeout(() => {
        onUpdateVariable(variable.id, 'value', currentValue)
        setIsShowingTestResult(false)
      }, 10000)
      
    } catch (error) {
      console.error('API test error:', error)
      // Store original value and show error
      const currentValue = variable.value
      setOriginalValue(currentValue)
      setIsShowingTestResult(true)
      onUpdateVariable(variable.id, 'value', 'Error: Failed to test API')
      
      // Restore original value after 5 seconds for errors
      setTimeout(() => {
        onUpdateVariable(variable.id, 'value', currentValue)
        setIsShowingTestResult(false)
      }, 5000)
    } finally {
      setIsTestingApi(false)
    }
  }, [isApiVariable, variable.sourceId, variable.jsonPath])

  // Load available columns for database variables
  useEffect(() => {
    const loadDatabaseColumns = async () => {
      if (variable.sourceType !== 'database' || !variable.sourceId) return
      
      try {
        const dataSourceResult = await dataSourceApi.getById(variable.sourceId)
        if (!dataSourceResult.success || !dataSourceResult.data) return
        
        const dataSource = dataSourceResult.data
        
        // Execute the query to get column names
        let flowToExecute: any[] = []
        
        if (dataSource.config?.mode === 'visual' && dataSource.config.flow) {
          flowToExecute = dataSource.config.flow.map((node: any) => ({
            id: node.id,
            type: node.type,
            tableName: node.data?.table || node.data?.tableName,
            selectedColumns: node.data?.selectedColumns,
            whereConditions: node.data?.whereConditions,
            orderBy: node.data?.orderBy,
            orderDirection: node.data?.orderDirection,
            limitValue: 1, // Only need 1 row to get column names
            fields: node.data?.fields,
            conditions: node.data?.conditions,
          }))
        } else if (dataSource.config?.mode === 'raw' && dataSource.config.setupQuery) {
          flowToExecute = [{ type: 'raw', query: `${dataSource.config.setupQuery} LIMIT 1` }]
        }
        
        if (flowToExecute.length > 0) {
          const result = await executeDbFlow(dataSource.connectionString, flowToExecute)
          if (result.success && result.data?.results && result.data.results.length > 0) {
            const selectResults = result.data.results.filter((r: any) => r.stepType === 'select')
            if (selectResults.length > 0 && selectResults[0].data && selectResults[0].data.length > 0) {
              const columns = Object.keys(selectResults[0].data[0])
              setAvailableColumns(columns)
            }
          }
        }
      } catch (error) {
        console.error('Failed to load database columns:', error)
      }
    }
    
    loadDatabaseColumns()
  }, [variable.sourceType, variable.sourceId])

  // Database test handler
  const handleTestDatabase = useCallback(async () => {
    if (variable.sourceType !== 'database' || !variable.nameColumn || !variable.valueColumn || !testNameValue.trim()) return
    
    setIsTestingDatabase(true)
    
    try {
      const dataSourceResult = await dataSourceApi.getById(variable.sourceId!)
      if (!dataSourceResult.success || !dataSourceResult.data) {
        throw new Error('Failed to load database source')
      }
      
      const dataSource = dataSourceResult.data?.dataSource || dataSourceResult.data
      
      // Build the lookup query
      let flowToExecute: any[] = []
      
      if (dataSource.config?.mode === 'visual' && dataSource.config.flow) {
        // For visual mode, modify the WHERE conditions to include our lookup
        flowToExecute = dataSource.config.flow.map((node: any) => {
          if (node.type === 'select') {
            return {
              id: node.id,
              type: node.type,
              tableName: node.data?.table || node.data?.tableName,
              selectedColumns: [variable.valueColumn], // Only select the value column
              whereConditions: [
                ...(node.data?.whereConditions || []),
                { col: variable.nameColumn, operator: '=', val: testNameValue.trim() }
              ],
              orderBy: node.data?.orderBy,
              orderDirection: node.data?.orderDirection,
              limitValue: 1,
              fields: node.data?.fields,
              conditions: node.data?.conditions,
            }
          }
          return {
            id: node.id,
            type: node.type,
            tableName: node.data?.table || node.data?.tableName,
            selectedColumns: node.data?.selectedColumns,
            whereConditions: node.data?.whereConditions,
            orderBy: node.data?.orderBy,
            orderDirection: node.data?.orderDirection,
            limitValue: node.data?.limitValue,
            fields: node.data?.fields,
            conditions: node.data?.conditions,
          }
        })
      } else if (dataSource.config?.mode === 'raw' && dataSource.config.setupQuery) {
        // For raw mode, modify the query to include WHERE clause
        const originalQuery = dataSource.config.setupQuery
        const lookupQuery = `${originalQuery} WHERE ${variable.nameColumn} = '${testNameValue.trim()}' LIMIT 1`
        flowToExecute = [{ type: 'raw', query: lookupQuery }]
      }
      
      const result = await executeDbFlow(dataSource.connectionString, flowToExecute)
      
      if (result.success && result.data?.results && result.data.results.length > 0) {
        const selectResults = result.data.results.filter((r: any) => r.stepType === 'select')
        if (selectResults.length > 0 && selectResults[0].data && selectResults[0].data.length > 0) {
          const resultValue = selectResults[0].data[0][variable.valueColumn!] || 'No value found'
          
          // Store original value and show test result
          const currentValue = variable.value
          setOriginalValue(currentValue)
          setIsShowingTestResult(true)
          onUpdateVariable(variable.id, 'value', String(resultValue))
          
          // Restore original value after 10 seconds
          setTimeout(() => {
            onUpdateVariable(variable.id, 'value', currentValue)
            setIsShowingTestResult(false)
          }, 10000)
        } else {
          throw new Error(`No record found with ${variable.nameColumn} = "${testNameValue.trim()}"`)
        }
      } else {
        throw new Error(result.error || 'Database lookup failed')
      }
    } catch (error: any) {
      console.error('Database test error:', error)
      // Store original value and show error
      const currentValue = variable.value
      setOriginalValue(currentValue)
      setIsShowingTestResult(true)
      onUpdateVariable(variable.id, 'value', `Error: ${error.message}`)
      
      // Restore original value after 5 seconds for errors
      setTimeout(() => {
        onUpdateVariable(variable.id, 'value', currentValue)
        setIsShowingTestResult(false)
      }, 5000)
    } finally {
      setIsTestingDatabase(false)
    }
  }, [variable.sourceType, variable.nameColumn, variable.valueColumn, testNameValue, variable.sourceId, variable.id, onUpdateVariable])

  // Memoized handlers
  const handleCopyValue = useCallback(() => {
    onCopyValue(variable.value)
  }, [variable.value, onCopyValue])

  const handleDuplicate = useCallback(() => {
    onDuplicateVariable(variable)
  }, [variable, onDuplicateVariable])

  const handleRemove = useCallback(() => {
    onRemoveVariable(variable.id)
  }, [variable.id, onRemoveVariable])

  // Force update on blur to ensure immediate save
  const handleNameBlur = useCallback(() => {
    nameInput.forceUpdate()
  }, [nameInput])

  const handleValueBlur = useCallback(() => {
    valueInput.forceUpdate()
  }, [valueInput])

  const handleDescriptionBlur = useCallback(() => {
    descriptionInput.forceUpdate()
  }, [descriptionInput])

  return (
    <>
      <TableRow className="group hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-colors">
      <TableCell className="py-3">
        <div className="flex items-center gap-2">
          {isDatabaseVariable && !isDbTestMode ? (
            // Database Normal Mode: Column Dropdown
            <Select 
              value={variable.nameColumn || ''} 
              onValueChange={(value) => onUpdateVariable(variable.id, 'nameColumn' as any, value)}
            >
              <SelectTrigger className="h-9 flex-1">
                <SelectValue placeholder="Select name column..." />
              </SelectTrigger>
              <SelectContent>
                {availableColumns.map(column => (
                  <SelectItem key={column} value={column}>{column}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : isDatabaseVariable && isDbTestMode ? (
            // Database Test Mode: Value Input
            <Input
              placeholder="Enter test value..."
              value={testNameValue}
              onChange={(e) => setTestNameValue(e.target.value)}
              className="h-9 border-gray-200 focus:border-indigo-500 transition-all flex-1"
            />
          ) : (
            // Regular Variable: Name Input
            <Input
              value={nameInput.value}
              onChange={(e) => nameInput.onChange(e.target.value)}
              onBlur={handleNameBlur}
              className="h-9 border-gray-200 focus:border-indigo-500 transition-all flex-1"
              placeholder="Variable name"
            />
          )}
          {isDatabaseVariable && (
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 flex-shrink-0"
              onClick={() => setIsDbTestMode(!isDbTestMode)}
              title={isDbTestMode ? "Exit test mode" : "Test database lookup"}
            >
              <Database className={`h-4 w-4 ${isDbTestMode ? 'text-indigo-600' : 'text-gray-400'}`} />
            </Button>
          )}
        </div>
      </TableCell>
      
      {showValues && (
        <TableCell className="py-3">
          <div className="flex items-center gap-2">
            {isApiVariable ? (
              <>
                <div className="flex-1">
                  <Input
                    value={valueInput.value}
                    onChange={(e) => valueInput.onChange(e.target.value)}
                    onBlur={handleValueBlur}
                    type={variable.type === "secret" ? "password" : "text"}
                    className={`h-9 transition-all ${
                      isShowingTestResult 
                        ? 'bg-green-50 dark:bg-green-900/20 border-green-300 text-green-800 dark:text-green-200' 
                        : 'bg-gray-50 dark:bg-gray-800 border-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                    placeholder={isShowingTestResult ? "Test result..." : `JSON Path: ${variable.jsonPath}`}
                    readOnly={true}
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 flex-shrink-0 gap-1"
                  onClick={handleTestApi}
                  disabled={isTestingApi}
                >
                  {isTestingApi ? (
                    <>
                      <Loader2 className="h-3.5 w-3.5 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    <>
                      <Play className="h-3.5 w-3.5" />
                      Test
                    </>
                  )}
                </Button>
              </>
            ) : (variable.sourceType === 'database') ? (
              <>
                {!isDbTestMode ? (
                  // Database Normal Mode: Value Column Dropdown
                  <Select 
                    value={variable.valueColumn || ''} 
                    onValueChange={(value) => onUpdateVariable(variable.id, 'valueColumn' as any, value)}
                  >
                    <SelectTrigger className="h-9 flex-1">
                      <SelectValue placeholder="Select value column..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableColumns.map(column => (
                        <SelectItem key={column} value={column}>{column}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  // Database Test Mode: Result Input
                  <Input
                    value={valueInput.value}
                    onChange={(e) => valueInput.onChange(e.target.value)}
                    onBlur={handleValueBlur}
                    type={variable.type === "secret" ? "password" : "text"}
                    className={`h-9 border-gray-200 focus:border-indigo-500 transition-all flex-1 ${
                      isShowingTestResult 
                        ? 'bg-green-50 dark:bg-green-900/20 border-green-300 text-green-800 dark:text-green-200' 
                        : ''
                    }`}
                    placeholder={isShowingTestResult ? "Test result..." : "Test result will appear here"}
                    readOnly={isShowingTestResult}
                  />
                )}
                {isDbTestMode ? (
                  // Test Button
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 flex-shrink-0 gap-1"
                    onClick={handleTestDatabase}
                    disabled={isTestingDatabase || !variable.nameColumn || !variable.valueColumn || !testNameValue.trim()}
                  >
                    {isTestingDatabase ? (
                      <>
                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <Play className="h-3.5 w-3.5" />
                        Test
                      </>
                    )}
                  </Button>
                ) : (
                  // Copy Button
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-9 w-9 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => onCopyValue(variable.value)}
                  >
                    <Copy className="h-3.5 w-3.5" />
                  </Button>
                )}
              </>
            ) : (
              <>
                <Input
                  value={valueInput.value}
                  onChange={(e) => valueInput.onChange(e.target.value)}
                  onBlur={handleValueBlur}
                  type={variable.type === "secret" ? "password" : "text"}
                  className="h-9 border-gray-200 focus:border-indigo-500 transition-all flex-1"
                  placeholder="Value"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={handleCopyValue}
                >
                  <Copy className="h-3.5 w-3.5" />
                </Button>
              </>
            )}
          </div>
        </TableCell>
      )}
      
      <TableCell className="py-3">
        <Input
          value={descriptionInput.value}
          onChange={(e) => descriptionInput.onChange(e.target.value)}
          onBlur={handleDescriptionBlur}
          className="h-9 border-gray-200 focus:border-indigo-500 transition-all w-full"
          placeholder="Description"
        />
      </TableCell>
      
      <TableCell className="text-right py-3">
        <div className="flex justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-gray-500 hover:text-gray-600 hover:bg-gray-100"
            onClick={handleDuplicate}
            title="Duplicate Variable"
          >
            <Copy className="h-3.5 w-3.5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
            onClick={handleRemove}
            title="Remove Variable"
          >
            <Trash2 className="h-3.5 w-3.5" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
    

    </>
  )
})
