/**
 * Result Queue Service
 * Manages the result queue for the pull-based model
 */

import { EventEmitter } from 'events';
import { Queue, Job, JobsOptions } from 'bullmq';
import { randomBytes } from 'crypto';
import { logger } from '../../utils/logger.js';
import { queueService, QUEUE_NAMES, JOB_TYPES } from '../redis/queueService.js';
import { config } from '../../config/index.js';

export interface ResultQueueServiceEvents {
  'result:added': (testId: string, data: any) => void;
  'result:error': (testId: string, error: Error) => void;
}

/**
 * Service for managing the result queue
 * Implements the pull-based model for result processing
 */
class ResultQueueService extends EventEmitter {
  private static instance: ResultQueueService;
  private isInitialized: boolean = false;

  private constructor() {
    super();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): ResultQueueService {
    if (!ResultQueueService.instance) {
      ResultQueueService.instance = new ResultQueueService();
    }
    return ResultQueueService.instance;
  }

  /**
   * Initialize the result queue service
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Ensure Redis is enabled
      if (!config.connections?.redis?.enabled) {
        logger.error('ResultQueueService: Redis is not enabled, cannot initialize result queue service');
        return false;
      }

      // Set up event listeners for the queue service
      queueService.on('job:completed', (jobId, result, queue) => {
        if (queue === QUEUE_NAMES.RESULT_QUEUE) {
          logger.debug(`ResultQueueService: Job ${jobId} completed in result queue`);
        }
      });

      queueService.on('job:failed', (jobId, error, queue) => {
        if (queue === QUEUE_NAMES.RESULT_QUEUE) {
          logger.error(`ResultQueueService: Job ${jobId} failed in result queue: ${error}`);
        }
      });

      this.isInitialized = true;
      logger.info('ResultQueueService: Initialized successfully');
      return true;
    } catch (error) {
      logger.error(`ResultQueueService: Initialization error: ${error}`);
      return false;
    }
  }

  /**
   * Add a result to the queue
   * @param testId Test ID
   * @param result Test result
   * @returns Promise resolving to the job ID if successful, null otherwise
   */
  public async addResult(testId: string, result: any): Promise<string | null> {
    try {
      if (!this.isInitialized) {
        logger.error('ResultQueueService: Not initialized, cannot add result');
        return null;
      }

      // Ensure testId is included in the result data for the job
      let finalTransactionId = result.transactionId; // Attempt to use provided transactionId
      let idSource = 'provided';

      if (!finalTransactionId) {
        finalTransactionId = `result-${Date.now()}-${randomBytes(3).toString('hex')}`; // Generate if not provided
        idSource = 'generated';
        logger.debug(`ResultQueueService: No transactionId provided for test ${testId}, executionId: ${result.executionId || 'N/A'}. Generated new one: ${finalTransactionId}`);
      } else {
        logger.debug(`ResultQueueService: Using provided transactionId for test ${testId}, executionId: ${result.executionId || 'N/A'}. Provided ID: ${finalTransactionId}`);
      }

      const resultWithIds = {
        ...result,
        testId: testId, // Ensure testId is part of the job data
        transactionId: finalTransactionId // Use the determined transactionId
      };

      logger.info(`Adding result to queue for test ${testId}. TransactionId (${idSource}): ${resultWithIds.transactionId}, executionId: ${resultWithIds.executionId || 'N/A'}`);
      const job = await queueService.addJob(
        QUEUE_NAMES.RESULT_QUEUE,
        JOB_TYPES.TEST_RESULT,
        resultWithIds, // Use the payload with the new transactionId
        {
          // HIGH PRIORITY: Use priority to ensure test results are processed immediately
          priority: 10, // Higher priority for faster processing
          attempts: 3,
          backoff: {
            type: 'fixed',
            delay: 500 // Reduced delay for faster retries
          },
          removeOnComplete: 50,
          removeOnFail: 100,
          // Add delay: 0 to process immediately
          delay: 0
        }
      );

      if (job) {
        logger.info(`ResultQueueService: Added result for test ${testId} to queue with transactionId ${finalTransactionId}`);
        this.emit('result:added', testId, resultWithIds);
        return job.id || null;
      } else {
        logger.error(`ResultQueueService: Failed to add result for test ${testId} to queue`);
        return null;
      }
    } catch (error) {
      logger.error(`ResultQueueService: Error adding result for test ${testId} to queue: ${error}`);
      this.emit('result:error', testId, error as Error);
      return null;
    }
  }

  /**
   * Get the queue status
   * @returns Promise resolving to the queue status
   */
  public async getQueueStatus(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    try {
      if (!this.isInitialized) {
        logger.error('ResultQueueService: Not initialized, cannot get queue status');
        return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
      }

      const queueStatus = await queueService.getQueueStatus(QUEUE_NAMES.RESULT_QUEUE);

      // Convert the object to the expected format
      if (queueStatus && typeof queueStatus === 'object') {
        const mainQueueStatus = queueStatus[QUEUE_NAMES.RESULT_QUEUE] || { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
        return mainQueueStatus;
      }

      return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
    } catch (error) {
      logger.error(`ResultQueueService: Error getting queue status: ${error}`);
      return { waiting: 0, active: 0, completed: 0, failed: 0, delayed: 0 };
    }
  }

  /**
   * Close the result queue service
   */
  public async close(): Promise<void> {
    if (this.isInitialized) {
      this.isInitialized = false;
      logger.info('ResultQueueService: Closed');
    }
  }
}

// Export singleton instance
export const resultQueueService = ResultQueueService.getInstance();
export default resultQueueService;
