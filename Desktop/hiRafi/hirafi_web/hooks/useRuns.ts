import useSWR, { mutate } from 'swr';
import { toast } from "@/lib/utils/toast-utils";
import { runApi } from "@/lib/api";
import { runsFetcher } from '@/lib/fetchers/runFetcher';
import { useRunsFilter } from './useRunsFilter';
import { useRunStore } from '@/store/runStore';
import { Run, isRunActive } from '@/lib/utils/run-logic';

interface UseRunsOptions {
  autoFetch?: boolean;
}

/**
 * SWR-based useRuns hook - simplified and focused on data fetching
 */
export function useRuns(options: UseRunsOptions = { autoFetch: true }) {
  const { getQueryKey } = useRunsFilter();
  const { setUpdating, clearUpdating } = useRunStore();
  
  // Get the current query key for SWR
  const queryKey = getQueryKey();
  
  // Use SWR for data fetching with automatic refresh for active runs
  // If autoFetch is false, use null as the key to disable automatic fetching
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate: mutateRuns
  } = useSWR(
    options.autoFetch !== false ? ['runs', queryKey] : null,
    () => runsFetcher(queryKey),
    {
      refreshInterval: (data) => {
        // Only refresh if autoFetch is enabled
        if (options.autoFetch === false) return 0;
        
        // Refresh every 5 seconds if there are active runs
        if (data?.runs?.some(run => {
          // Check both run status and test results for active state
          const hasActiveStatus = ['running', 'queued'].includes(run.status);
          const hasActiveTests = run.testResults && (run.testResults.running > 0 || run.testResults.queued > 0);
          const isActive = hasActiveStatus || hasActiveTests || isRunActive(run);
          
          return isActive;
        })) {
          return 5000;
        }
        return 0; // Don't refresh if no active runs
      },
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 5000, // Increased from 2000 to prevent rapid duplicate requests
      errorRetryCount: 3,
      onError: (err) => {
        toast.error(err.message || 'Failed to fetch runs');
      },
      // Add cache control options for better consistency
      revalidateIfStale: true, // Always revalidate stale data
      shouldRetryOnError: true
    }
  );

  // Extract runs and totalCount from data
  const runs = data?.runs || [];
  const totalCount = data?.totalCount || 0;

  /**
   * Execute a run
   */
  const executeRun = async (runId: string) => {
    setUpdating(runId, true);
    
    try {
      const response = await runApi.executeRun(runId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to execute run');
      }
      
      // Optimistically update the run status with better race condition handling
      const optimisticUpdatePromise = mutateRuns(
        (currentData) => {
          if (!currentData) {
            return currentData;
          }
          
          const updatedRuns = currentData.runs.map(run => {
            if (run.id === runId) {
              return { 
                ...run, 
                status: 'queued' as const,
                // Reset test results for fresh execution
                testResults: run.testResults ? {
                  ...run.testResults,
                  running: 0,
                  completed: 0,
                  failed: 0,
                  stopped: 0,
                  queued: run.testResults.total
                } : undefined
              };
            }
            return run;
          });
          
          return {
            ...currentData,
            runs: updatedRuns
          };
        },
        {
          revalidate: false // Don't revalidate immediately to prevent race
        }
      );
      
      // Wait for optimistic update to complete, then schedule revalidation
      await optimisticUpdatePromise;
      
      // Schedule revalidation with proper timing to avoid race conditions
      setTimeout(() => {
        mutateRuns();
      }, 3000); // Increased delay to allow backend processing
      
      toast.success('Run started successfully');
      return { success: true };
    } catch (error: any) {
      toast.error(error.message || 'Failed to execute run');
      
      // Revert optimistic update on error
      mutateRuns();
      
      return { success: false, error: error.message };
    } finally {
      clearUpdating(runId);
    }
  };

  /**
   * Stop a run
   */
  const stopRun = async (runId: string) => {
    setUpdating(runId, true);
    
    try {
      const response = await runApi.stopRun(runId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to stop run');
      }
      
      // Optimistically update the run status
      const optimisticUpdatePromise = mutateRuns(
        (currentData) => {
          if (!currentData) {
            return currentData;
          }
          
          const updatedRuns = currentData.runs.map(run => {
            if (run.id === runId) {
              return { 
                ...run, 
                status: 'stopped' as const,
                // Update test results to reflect stopped state
                testResults: run.testResults ? {
                  ...run.testResults,
                  running: 0,
                  queued: 0,
                  stopped: run.testResults.running + run.testResults.queued + run.testResults.stopped
                } : undefined
              };
            }
            return run;
          });
          
          return {
            ...currentData,
            runs: updatedRuns
          };
        },
        {
          revalidate: false // Don't revalidate immediately
        }
      );
      
      // Wait for optimistic update, then schedule revalidation
      await optimisticUpdatePromise;
      
      // Schedule revalidation with shorter delay for stop operations
      setTimeout(() => {
        mutateRuns();
      }, 1500);
      
      toast.success('Run stopped successfully');
      return { success: true };
    } catch (error: any) {
      toast.error(error.message || 'Failed to stop run');
      
      // Revert optimistic update on error
      mutateRuns();
      
      return { success: false, error: error.message };
    } finally {
      clearUpdating(runId);
    }
  };

  /**
   * Delete a run
   */
  const deleteRun = async (runId: string) => {
    setUpdating(runId, true);
    
    try {
      const response = await runApi.deleteRun(runId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete run');
      }
      
      // Optimistically remove the run from the list
      mutateRuns(
        (currentData) => {
          if (!currentData) return currentData;
          
          return {
            ...currentData,
            runs: currentData.runs.filter(run => run.id !== runId),
            totalCount: Math.max(0, currentData.totalCount - 1)
          };
        },
        false // Don't revalidate immediately
      );
      
      toast.success('Run deleted successfully');
      return { success: true };
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete run');
      return { success: false, error: error.message };
    } finally {
      clearUpdating(runId);
    }
  };

  /**
   * Create a new run
   */
  const createRun = async (runData: Partial<Run>) => {
    try {
      const response = await runApi.createRun({
        name: runData.name || 'New Run',
        description: runData.description,
        scenarios: runData.scenarioIds || [],
        platform: runData.platform || 'web',
        environment: runData.environment,
        reportSettings: runData.reportSettings
      });
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to create run');
      }
      
      // Revalidate to get the new run in the list
      mutateRuns();
      
      toast.success('Run created successfully');
      return { success: true, runId: response.data?.runId };
    } catch (error: any) {
      toast.error(error.message || 'Failed to create run');
      return { success: false, error: error.message };
    }
  };

  /**
   * Update a run
   */
  const updateRun = async (runId: string, updateData: Partial<Run>) => {
    try {
      const response = await runApi.updateRun(runId, updateData);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to update run');
      }
      
      // Revalidate to get the updated run
      mutateRuns();
      
      toast.success('Run updated successfully');
      return { success: true };
    } catch (error: any) {
      toast.error(error.message || 'Failed to update run');
      return { success: false, error: error.message };
    }
  };

  /**
   * Refresh runs data
   */
  const refresh = () => {
    return mutateRuns();
  };

  return {
    // Data
    runs,
    totalCount,
    isLoading,
    isValidating,
    error: error?.message || null,
    
    // Actions
    executeRun,
    stopRun,
    deleteRun,
    createRun,
    updateRun,
    refresh,
    
    // SWR utilities
    mutate: mutateRuns
  };
}
