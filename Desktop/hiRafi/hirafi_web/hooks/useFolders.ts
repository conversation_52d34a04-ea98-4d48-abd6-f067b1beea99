/**
 * useFolders Hook
 *
 * Optimized hook that only fetches folders without scenarios
 * Uses direct API call instead of aggregated endpoint for better performance
 */

import useSWR from 'swr'
import { scenarioApi } from '@/lib/api'

/**
 * Options for the folders hook
 */
interface UseFoldersOptions {
  autoFetch?: boolean
  limit?: number
  skip?: number
}

/**
 * Folder type for backward compatibility
 */
export interface Folder {
  id: string
  name: string
  color?: string
  createdAt?: string
  updatedAt?: string
  teamId?: string
  companyId?: string
}

/**
 * Fetcher function for folders only
 */
const fetchFoldersOnly = async (params: { limit?: number; skip?: number }) => {
  const response = await scenarioApi.getFolders(params)

  if (!response.success) {
    throw new Error(response.error || 'Failed to fetch folders')
  }

  // Extract folders from response
  const foldersData = response.folders || response.data?.folders || []

  return {
    folders: foldersData,
    total: response.data?.total || foldersData.length
  }
}

/**
 * Optimized hook that only fetches folders without scenarios
 * Uses direct API call instead of aggregated endpoint for better performance
 */
export function useFolders(options: UseFoldersOptions = {}) {
  const { autoFetch = true, limit = 50, skip = 0 } = options

  // Create SWR key
  const swrKey = autoFetch ? ['folders-only', { limit, skip }] : null

  // SWR data fetching
  const {
    data,
    error,
    isLoading,
    mutate
  } = useSWR(swrKey, () => fetchFoldersOnly({ limit, skip }), {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
    errorRetryCount: 3,
    onError: (err) => {
      console.error("SWR error fetching folders:", err)
    }
  })

  // Extract data with fallbacks
  const folders = data?.folders || []
  const foldersTotal = data?.total || 0

  // Actions
  const refreshFolders = async () => {
    return await mutate()
  }

  const mutateFoldersCache = async () => {
    return await mutate()
  }

  const createFolder = async (folderData: {
    name: string
    description?: string
    color?: string
    teamId?: string
    companyId?: string
  }) => {
    try {
      const result = await scenarioApi.createFolder(folderData)

      if (result.success) {
        // Refresh folders after successful creation
        await mutate()
        return result
      } else {
        throw new Error(result.error || 'Failed to create folder')
      }
    } catch (error: any) {
      throw error
    }
  }

  return {
    // Data
    folders,

    // Loading states
    foldersLoading: isLoading,
    foldersError: error,

    // Counts
    foldersTotal,

    // Actions
    refreshFolders,
    mutateFoldersCache,
    createFolder,

    // Backward compatibility
    isLoading,
    error,
    mutate
  }
}
