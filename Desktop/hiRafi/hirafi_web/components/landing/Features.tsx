import React from 'react';
import { Bo<PERSON>, Wand2, Code, RefreshCw, Zap, LayoutDashboard, Server, Lock } from 'lucide-react';
import FeatureCard from './FeatureCard';

const Features = () => {
  const features = [
    {
      icon: <Bot size={28} className="text-blue-600" />,
      title: "AI-Powered Test Generation",
      description: "Smart test creation without manual scripting, saving teams hours of work."
    },
    {
      icon: <Wand2 size={28} className="text-purple-600" />,
      title: "Codeless Test Automation",
      description: "Create complex test scenarios without writing a single line of code."
    },
    {
      icon: <RefreshCw size={28} className="text-green-600" />,
      title: "Self-Healing Automation",
      description: "Tests automatically adapt to UI changes, reducing maintenance by 50% or more."
    },
    {
      icon: <Code size={28} className="text-indigo-600" />,
      title: "Dynamic Element Handling",
      description: "Intelligently identifies and interacts with UI elements even when they change."
    },
    {
      icon: <Zap size={28} className="text-amber-600" />,
      title: "Intelligent Anomaly Detection",
      description: "Visual & functional testing with performance insights and predictive analytics."
    },
    {
      icon: <LayoutDashboard size={28} className="text-blue-500" />,
      title: "Real-Time Test Reporting",
      description: "Comprehensive analytics dashboard for tracking test results and trends."
    },
    {
      icon: <Server size={28} className="text-teal-600" />,
      title: "Cross-Platform Testing",
      description: "Run tests across browsers, devices, and platforms with a single configuration."
    },
    {
      icon: <Lock size={28} className="text-red-600" />,
      title: "Security Testing Add-Ons",
      description: "Expand your testing coverage with specialized security testing modules."
    }
  ];

  return (
    <section id="features" className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Powerful Features</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            HiRafi combines cutting-edge AI with robust testing capabilities to revolutionize how teams approach quality assurance.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <FeatureCard 
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;