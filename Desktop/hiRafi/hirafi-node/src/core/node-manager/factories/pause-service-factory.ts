/**
 * Pause Service Factory
 * Creates pause service instances with proper dependency injection
 */

import { logger } from '../../../utils/logger.js';
import { PauseService } from '../services/pause/pause-service.js';
import { IPauseService, PauseServiceConfig } from '../interfaces/pause-service.interface.js';
import { IRedisConnectionManager } from '../../connectors/interfaces/redis-connector.interface.js';

export class PauseServiceFactory {
  /**
   * Create a pause service with dependencies
   */
  static async createPauseService(
    connectionManager: IRedisConnectionManager,
    nodeId: string,
    config?: PauseServiceConfig
  ): Promise<IPauseService> {
    try {
      logger.info(`PauseServiceFactory: Creating pause service for node ${nodeId}`);

      // Create service instance with dependency injection
      const service = new PauseService(connectionManager, config);

      // Initialize the service
      const initialized = await service.initialize(nodeId);
      if (!initialized) {
        throw new Error('Failed to initialize pause service');
      }

      logger.info(`PauseServiceFactory: Successfully created pause service for node ${nodeId}`);
      return service;

    } catch (error: any) {
      logger.error(`PauseServiceFactory: Failed to create pause service for node ${nodeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a pause service with default configuration
   */
  static async createDefaultPauseService(
    connectionManager: IRedisConnectionManager,
    nodeId: string
  ): Promise<IPauseService> {
    const defaultConfig: PauseServiceConfig = {
      nodeId,
      enableLogging: true,
      checkInterval: 5000
    };

    return this.createPauseService(connectionManager, nodeId, defaultConfig);
  }
}

/**
 * Convenience function for creating a pause service
 */
export async function createPauseService(
  connectionManager: IRedisConnectionManager,
  nodeId: string,
  config?: PauseServiceConfig
): Promise<IPauseService> {
  return PauseServiceFactory.createPauseService(connectionManager, nodeId, config);
}
