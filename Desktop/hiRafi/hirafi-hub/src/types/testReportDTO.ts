/**
 * Test Report Data Transfer Object (DTO)
 * 
 * This file defines the single, authoritative structure for test reports
 * ready for database storage. This DTO serves as the "golden" interface
 * that eliminates the need for multiple cleaning steps throughout the pipeline.
 * 
 * Key principles:
 * 1. This is the ONLY structure that should be saved to MongoDB
 * 2. All legacy/temporary fields are explicitly excluded
 * 3. All services must transform their data to this structure
 * 4. No service should modify this structure after creation
 */

import { TestStatus } from '../models/test-types.js';
import { ProcessedMetrics } from './metrics.js';
import { logger } from '../utils/logger.js';

/**
 * Step result structure for test reports
 */
export interface TestReportStepDTO {
  id: string;
  name: string;
  type: string;
  status: 'passed' | 'failed' | 'error' | 'unknown';
  success: boolean;
  duration: number;
  error: string | null;
  timestamp: string;
  beforeScreenshotUrl?: string | null;
  afterScreenshotUrl?: string | null;
  logs?: string[];
  description?: string;
  data?: any;
  startTime?: string;
  endTime?: string;
  
  // Control Flow Results for enhanced report visualization
  controlFlowData?: {
    // IF-ELSE specific
    conditionResult?: boolean;
    executedBranch?: 'true' | 'false';
    trueSteps?: TestReportStepDTO[];
    falseSteps?: TestReportStepDTO[];
    
    // FOR/WHILE LOOP specific  
    iterationCount?: number;
    completedIterations?: number;
    totalStepsExecuted?: number;
    loopSteps?: TestReportStepDTO[][];
    hitMaxIterations?: boolean;
    failedIteration?: number;
    failedStepIndex?: number;
  };
}

/**
 * Test summary structure for test reports
 */
export interface TestReportSummaryDTO {
  total: number;
  passed: number;
  failed: number;
  errors: number;
}

/**
 * Complete Test Report DTO - The authoritative structure for database storage
 * 
 * This interface represents the clean, final structure of a test report
 * that is ready to be saved to MongoDB. It includes all necessary fields
 * and explicitly excludes all legacy or temporary fields.
 */
export interface TestReportDTO {
  // Core identification
  id: string;
  status: string; // TestStatus as string
  
  // Test metadata
  name: string;
  scenarioId: string;
  scenarioName?: string;
  scenarioTitle?: string;
  url?: string;
  
  // Execution context
  runId?: string;
  executionId?: string;
  nodeId?: string;
  
  // User and organization context
  executedUser?: string;
  executedUserName?: string;
  userId?: string;
  teamId?: string | null;
  companyId?: string | null;
  
  // Timing information
  timestamp: string; // ISO string
  date?: string; // ISO string - Optional since it may be removed if redundant with timestamp
  startTime?: string; // ISO string
  endTime?: string; // ISO string
  duration?: number; // milliseconds
  
  // Test execution results
  success?: boolean;
  error?: string | null;
  logs?: string[];
  steps: TestReportStepDTO[];
  summary: TestReportSummaryDTO;
  
  // Media and artifacts
  videoUrl?: string;
  
  // Platform information
  platform?: 'web' | 'android';
  
  // Environment settings including device information for Android tests
  environmentSettings?: any; // Environment settings with device info

  // Testinium session information for Android tests using Testinium provider
  testiniumSessionId?: string; // Testinium session ID for tracking and debugging

  // Enhanced metrics - the clean, processed metrics structure
  enhancedMetrics?: ProcessedMetrics;
  
  // MongoDB metadata (added by database layer)
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Validation function to ensure a test report conforms to the DTO structure
 * This can be used to validate data before saving to database
 */
export function validateTestReportDTO(report: any): report is TestReportDTO {
  if (!report || typeof report !== 'object') {
    logger.error('TestReportDTO validation failed: report is not an object');
    return false;
  }

  // Check required fields
  if (!report.id || typeof report.id !== 'string') {
    logger.error(`TestReportDTO validation failed: missing or invalid id field for report: ${JSON.stringify(report)}`);
    return false;
  }

  if (!report.status || typeof report.status !== 'string') {
    logger.error(`TestReportDTO validation failed: missing or invalid status field for report ID: ${report.id}`);
    return false;
  }

  if (!report.name || typeof report.name !== 'string') {
    logger.error(`TestReportDTO validation failed: missing or invalid name field for report ID: ${report.id}`);
    return false;
  }

  if (!report.scenarioId || typeof report.scenarioId !== 'string') {
    logger.error(`TestReportDTO validation failed: missing or invalid scenarioId field for report ID: ${report.id}`);
    return false;
  }

  if (!report.timestamp || typeof report.timestamp !== 'string') {
    logger.error(`TestReportDTO validation failed: missing or invalid timestamp field for report ID: ${report.id}`);
    return false;
  }

  // Date field is optional since it may be removed if redundant with timestamp
  if (report.date !== undefined && typeof report.date !== 'string') {
    logger.error(`TestReportDTO validation failed: invalid date field type for report ID: ${report.id}`);
    return false;
  }

  // Validate steps array
  if (!Array.isArray(report.steps)) {
    logger.error(`TestReportDTO validation failed: steps is not an array for report ID: ${report.id}`);
    return false;
  }

  // Validate summary object
  if (!report.summary || typeof report.summary !== 'object') {
    logger.error(`TestReportDTO validation failed: missing or invalid summary object for report ID: ${report.id}`);
    return false;
  }

  if (typeof report.summary.total !== 'number' ||
      typeof report.summary.passed !== 'number' ||
      typeof report.summary.failed !== 'number' ||
      typeof report.summary.errors !== 'number') {
    logger.error(`TestReportDTO validation failed: invalid summary fields for report ID: ${report.id}`);
    return false;
  }

  // Log if videoUrl is present for debugging
  if (report.videoUrl) {
    logger.info(`TestReportDTO validation: videoUrl present for report ${report.id}: ${report.videoUrl}`);
  }

  return true;
}

/**
 * Type guard to check if an object is a valid TestReportDTO
 */
export function isTestReportDTO(obj: any): obj is TestReportDTO {
  return validateTestReportDTO(obj);
}

/**
 * Helper function to create a minimal valid TestReportDTO
 * Useful for testing and as a template
 */
export function createMinimalTestReportDTO(
  id: string,
  status: string,
  name: string,
  scenarioId: string
): TestReportDTO {
  const now = new Date().toISOString();
  
  return {
    id,
    status,
    name,
    scenarioId,
    timestamp: now,
    date: now,
    steps: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      errors: 0
    }
  };
}
