/**
 * Node Manager Module
 * Handles node management functionality
 *
 * This is a wrapper around the new NodeRegistry service to maintain backward compatibility
 * All functionality has been moved to the new service
 */

import { EventEmitter } from 'events';
import { TestNode, TestStatus } from '../../models/test-types.js';
import { logger } from '../../utils/logger.js';
import { config } from '../../config/index.js';
import { nodeRegistry } from '../../services/node/index.js';
import { testManager } from '../test-manager/index.js';
import { webSocketConnector } from '../../connectors/index.js';
import { testQueueService } from '../../services/test/index.js';
import { eventHandler } from '../event-handler/index.js';

/**
 * Node Manager class
 * Manages test nodes and their status
 *
 * This is a wrapper around the NodeRegistry service
 * Events are now handled through the centralized EventHandler
 */
class NodeManager extends EventEmitter {
  private static instance: NodeManager;

  private constructor() {
    super();

    // Events are now handled through the centralized EventHandler
    // Subscribe to relevant events from EventHandler instead of direct NodeRegistry events
    eventHandler.on('node:registered', (nodeId, node) => {
      this.emit('nodeAdded', nodeId, node);
    });

    eventHandler.on('node:disconnected', (nodeId) => {
      this.emit('nodeRemoved', nodeId);
    });

    eventHandler.on('node:statusChanged', (nodeId, newStatus, oldStatus) => {
      this.emit('nodeStatusChanged', nodeId, newStatus, oldStatus);
    });

    logger.info('NodeManager: Initialized as wrapper around NodeRegistry with centralized event handling');
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): NodeManager {
    if (!NodeManager.instance) {
      NodeManager.instance = new NodeManager();
    }
    return NodeManager.instance;
  }

  /**
   * Initialize the node manager
   */
  public async initialize(): Promise<boolean> {
    logger.info('NodeManager: Initializing (delegating to NodeRegistry)');
    return nodeRegistry.initialize();
  }

  /**
   * Register a new node
   * @param name Node name
   * @param capabilities Node capabilities
   * @param status Initial status
   * @param vncUrl VNC URL
   * @param vncPort VNC port
   * @param version Node version
   * @returns Node ID
   */
  public registerNode(
    name: string,
    capabilities: string[] = [],
    status: 'available' | 'busy' | 'inactive' = 'available',
    vncUrl?: string,
    vncPort?: number,
    version?: string
  ): string {
    logger.info(`NodeManager: Registering node ${name} (delegating to NodeRegistry)`);

    // Create node data object
    const nodeData = {
      id: '', // Will be generated by NodeRegistry
      name,
      capabilities,
      status,
      vncUrl,
      vncPort,
      version
    };

    // Register with NodeRegistry - this is synchronous in the old API
    try {
      const nodeId = nodeRegistry.registerNode(nodeData);
      // Ensure we return a string, not a Promise
      return typeof nodeId === 'string' ? nodeId : '';
    } catch (error) {
      logger.error(`NodeManager: Error registering node: ${error}`);
      return '';
    }
  }

  /**
   * Update node heartbeat
   * @param nodeId Node ID
   * @returns Success status
   */
  public updateNodeHeartbeat(_nodeId: string): boolean {
    // NodeRegistry handles heartbeats automatically via WebSocket connector
    logger.debug(`NodeManager: updateNodeHeartbeat is deprecated, handled by NodeRegistry`);
    return true;
  }

  /**
   * Mark a node as busy
   * @param nodeId Node ID
   * @param testId Test ID
   * @param source Optional source of the call for logging
   * @returns Success status
   */
  public async markNodeAsBusy(nodeId: string, testId: string, source?: string): Promise<boolean> {
    logger.info(`NodeManager: Marking node ${nodeId} as busy with test ${testId} (delegating to NodeRegistry)`);
    return nodeRegistry.markNodeAsBusy(nodeId, testId, source || 'admin-api');
  }

  /**
   * Mark a node as available
   * @param nodeId Node ID
   * @returns Success status
   */
  public async markNodeAsAvailable(nodeId: string): Promise<boolean> {
    logger.info(`NodeManager: Marking node ${nodeId} as available (delegating to NodeRegistry)`);
    return nodeRegistry.markNodeAsAvailable(nodeId);
  }

  /**
   * Mark a node as inactive
   * @param nodeId Node ID
   * @returns Success status
   */
  public async markNodeAsInactive(nodeId: string): Promise<boolean> {
    logger.info(`NodeManager: Marking node ${nodeId} as inactive (delegating to NodeRegistry)`);

    // Get the node
    const node = this.getNode(nodeId);

    if (!node) {
      logger.warn(`NodeManager: Cannot mark unknown node as inactive: ${nodeId}`);
      return false;
    }

    // If the node has a test running, stop it
    if (node.currentTestId) {
      logger.warn(`NodeManager: Node ${nodeId} marked as inactive while running test ${node.currentTestId}. Handling orphaned test.`);
      await testManager.stopTest(node.currentTestId);
    }

    // Mark as inactive in NodeRegistry - use the status property
    try {
      // NodeRegistry doesn't have markNodeAsInactive, so we need to update the node status
      const node = nodeRegistry.getNode(nodeId);
      if (node) {
        node.status = 'inactive';
        node.currentTestId = null;
        return true;
      }
      return false;
    } catch (error) {
      logger.error(`NodeManager: Error marking node ${nodeId} as inactive: ${error}`);
      return false;
    }
  }

  /**
   * Remove a node
   * @param nodeId Node ID
   * @returns Success status
   */
  public async removeNode(nodeId: string): Promise<boolean> {
    logger.info(`NodeManager: Removing node ${nodeId} (delegating to NodeRegistry)`);
    try {
      return await nodeRegistry.removeNode(nodeId);
    } catch (error) {
      logger.error(`NodeManager: Error removing node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Get a node by ID
   * @param nodeId Node ID
   * @returns Node if found, null otherwise
   */
  public getNode(nodeId: string): TestNode | null {
    return nodeRegistry.getNode(nodeId);
  }

  /**
   * Get all nodes
   * @returns Array of all nodes
   */
  public getAllNodes(): TestNode[] {
    // Convert Map to Array
    const nodesMap = nodeRegistry.getAllNodes();
    return Array.from(nodesMap.values());
  }

  /**
   * Get available nodes
   * @returns Array of available nodes
   */
  public getAvailableNodes(): TestNode[] {
    // Convert Map to Array and filter
    const nodesMap = nodeRegistry.getAllNodes();
    return Array.from(nodesMap.values()).filter((node: TestNode) => node.status === 'available');
  }

  /**
   * Get busy nodes
   * @returns Array of busy nodes
   */
  public getBusyNodes(): TestNode[] {
    // Convert Map to Array and filter
    const nodesMap = nodeRegistry.getAllNodes();
    return Array.from(nodesMap.values()).filter((node: TestNode) => node.status === 'busy');
  }

  /**
   * Get inactive nodes
   * @returns Array of inactive nodes
   */
  public getInactiveNodes(): TestNode[] {
    // Convert Map to Array and filter
    const nodesMap = nodeRegistry.getAllNodes();
    return Array.from(nodesMap.values()).filter((node: TestNode) => node.status === 'inactive');
  }

  /**
   * Stop a test on a node
   * @param testId Test ID
   * @returns Success status
   */
  public async stopTestOnNode(testId: string): Promise<boolean> {
    logger.info(`NodeManager: Stopping test ${testId} (delegating to TestManager)`);
    return testManager.stopTest(testId);
  }

  /**
   * Assign and send a test to a node
   * @param testId Test ID
   * @param requiredCapabilities Required capabilities
   * @param options Additional options
   * @returns Success status
   */
  public async assignAndSendTest(
    testId: string,
    requiredCapabilities: string[] = [],
    options: any = {}
  ): Promise<boolean> {
    try {
      // Get test details
      const test = await testManager.getTest(testId);
      if (!test) {
        logger.error(`NodeManager: Test ${testId} not found`);
        return false;
      }

      // Add platform capability if not already included
      if (test.platform && !requiredCapabilities.includes(test.platform)) {
        requiredCapabilities.push(test.platform);
        logger.info(`NodeManager: Added platform ${test.platform} to required capabilities`);
      }

      // Find an available node with the required capabilities
      const node = nodeRegistry.findAvailableNode(requiredCapabilities, test.platform);
      if (!node) {
        logger.warn(`NodeManager: No available node found with required capabilities: ${requiredCapabilities.join(', ')} for test ${testId} (platform: ${test.platform})`);
        return false;
      }

      // Mark the node as busy with this test - REMOVED. NodeRegistry will be updated by node:testClaimed event.
      // const nodeMarked = await nodeRegistry.markNodeAsBusy(node.id, testId);
      // if (!nodeMarked) {
      //   logger.error(`NodeManager: Failed to mark node ${node.id} as busy with test ${testId} (proactively)`);
      //   return false;
      // }


      // Update the test status to ASSIGNED - This indicates intent and helps tracking.
      // The actual node that runs it might be different if another similar node claims it first.
      const testUpdated = await testManager.updateTestStatus(testId, TestStatus.ASSIGNED, {
        // Tentatively assigning to this node for tracking, but the claim event will be the source of truth.
        nodeId: node.id,
        ...options
      });

      if (!testUpdated) {
        logger.error(`NodeManager: Failed to update test ${testId} status to ASSIGNED`);
        // Do not mark node available here, as it was never marked busy by this flow.
        return false;
      }

      // Convert TestProcess to TestRequest for the queue service
      const testRequest: any = {
        ...test,
        priority: test.priority || 10
      };

      // Add the test to the queue service
      const queueSuccess = await testQueueService.addTest(testRequest, testRequest.priority);
      if (!queueSuccess) {
        logger.error(`NodeManager: Failed to add test ${testId} to queue`);
        // Attempt to revert test status from ASSIGNED if queuing fails?
        // For now, if it was marked ASSIGNED but couldn't be queued, it's an orphaned assignment.
        // The node was not marked busy, so no need to mark available.
        return false;
      }


      return true;
    } catch (error) {
      logger.error(`NodeManager: Error assigning test ${testId}: ${error}`);
      return false;
    }
  }

  /**
   * Stop the node manager
   */
  public async stop(): Promise<void> {
    logger.info('NodeManager: Stopping (delegating to NodeRegistry)');
    await nodeRegistry.close();
  }
}

// Export singleton instance
export const nodeManager = NodeManager.getInstance();
export default nodeManager;
