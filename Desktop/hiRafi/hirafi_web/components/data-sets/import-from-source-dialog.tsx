"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { useTestData } from "@/hooks/useTestData"
import { DataSource } from "@/types/test-data"
import { Loader2, Search, Database, FileText, FileSpreadsheet, Globe } from "lucide-react"

interface ImportFromSourceDialogProps {
  isOpen: boolean
  onClose: () => void
  onImport: (variables: Array<{ 
    name: string; 
    value: string; 
    sourceType?: string; 
    sourceId?: string; 
    jsonPath?: string;
    dbMode?: string;
    needsColumnMapping?: boolean;
  }>) => void
  currentVariables?: Array<{ sourceId?: string }>
}

export function ImportFromSourceDialog({
  isOpen,
  onClose,
  onImport,
  currentVariables = [],
}: ImportFromSourceDialogProps) {
  const { dataSources, isLoadingDataSources, loadDataSources } = useTestData()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSources, setSelectedSources] = useState<string[]>([])
  


  useEffect(() => {
    if (isOpen) {
      loadDataSources()
      
      const importedSourceIds = currentVariables
        .map(variable => variable.sourceId)
        .filter((sourceId): sourceId is string => Boolean(sourceId))
      
      const uniqueSourceIds = [...new Set(importedSourceIds)]
      setSelectedSources(uniqueSourceIds)
    }
  }, [isOpen, loadDataSources, currentVariables])

  // Filter and sort sources
  const filteredSources = dataSources.filter((source: DataSource) => {
    if (source.type === 'csv' || source.type === 'excel') {
      if (!source.variables || source.variables.length === 0) return false
      return source.name.toLowerCase().includes(searchTerm.toLowerCase())
    }
    
    if (source.type === 'database') {
      const hasVariables = source.variables && source.variables.length > 0
      const hasConfigData = source.config?.data && source.config.data.length > 0
      const hasQuery = source.config?.mode === 'visual' || source.config?.mode === 'raw'
      
      if (!hasVariables && !hasConfigData && !hasQuery) return false
      return source.name.toLowerCase().includes(searchTerm.toLowerCase())
    }
    
    if (source.type === 'api') {
      const hasExtractionRules = source.config?.extractionRules && source.config.extractionRules.length > 0
      if (!hasExtractionRules) return false
      return source.name.toLowerCase().includes(searchTerm.toLowerCase())
    }
    
    return false
  }).sort((a, b) => {
    const getPriority = (type: string) => {
      if (type === 'api') return 1
      if (type === 'database') return 2
      if (type === 'csv') return 3
      if (type === 'excel') return 4
      return 5
    }
    
    const priorityA = getPriority(a.type)
    const priorityB = getPriority(b.type)
    
    if (priorityA !== priorityB) {
      return priorityA - priorityB
    }
    
    return a.name.localeCompare(b.name)
  })



  const getVariablesFromSource = (source: DataSource): Array<{ 
    name: string; 
    value: string; 
    sourceType?: string; 
    sourceId?: string; 
    jsonPath?: string;
    dbMode?: string;
    needsColumnMapping?: boolean;
  }> => {
    let variablesToImport: Array<{ 
      name: string; 
      value: string; 
      sourceType?: string; 
      sourceId?: string; 
      jsonPath?: string;
      dbMode?: string;
      needsColumnMapping?: boolean;
    }> = []
    
    if (source.type === 'api' && source.config?.extractionRules) {
      variablesToImport = source.config.extractionRules.map(rule => ({
        name: rule.variableName,
        value: rule.jsonPath,
        sourceType: 'api',
        sourceId: source.id,
        jsonPath: rule.jsonPath
      }))
    } else if (source.type === 'database') {
      if (source.config?.mode === 'read') {
        if (source.config.data && source.config.data.length > 0) {
          variablesToImport = source.config.data.map(item => ({
            ...item,
            sourceType: 'database',
            sourceId: source.id
          }))
        } else if (source.variables && source.variables.length > 0) {
          variablesToImport = source.variables.map(item => ({
            ...item,
            sourceType: 'database',
            sourceId: source.id
          }))
        }
      } else if (source.config?.mode === 'visual' || source.config?.mode === 'raw') {
        // For visual/raw database sources, create a database variable
        variablesToImport = [{
          name: source.name,
          value: '',
          sourceType: 'database',
          sourceId: source.id,
          dbMode: source.config.mode
        }]
      }
    } else if (source.variables && source.variables.length > 0) {
      const sourceType = source.type === 'csv' ? 'csv' : source.type === 'excel' ? 'excel' : undefined
      variablesToImport = source.variables.map(item => ({
        ...item,
        sourceType,
        sourceId: source.id
      }))
    }
    
    return variablesToImport
  }

  const handleSourceToggle = (sourceId: string) => {
    setSelectedSources(prev => 
      prev.includes(sourceId) 
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId]
    )
  }

  const handleImport = () => {
    if (selectedSources.length === 0) return
    
    let allVariablesToImport: Array<{ 
      name: string; 
      value: string; 
      sourceType?: string; 
      sourceId?: string; 
      jsonPath?: string;
      dbMode?: string;
      needsColumnMapping?: boolean;
    }> = []
    
    selectedSources.forEach(sourceId => {
      const source = filteredSources.find(s => s.id === sourceId)
      if (source) {
        const variablesFromSource = getVariablesFromSource(source)
        allVariablesToImport = [...allVariablesToImport, ...variablesFromSource]
      }
    })
    
    if (allVariablesToImport.length > 0) {
      onImport(allVariablesToImport)
      onClose()
      setSelectedSources([])
    }
  }

  const getSourceIcon = (type: string) => {
    switch (type) {
      case 'database':
        return <Database className="h-4 w-4" />
      case 'api':
        return <Globe className="h-4 w-4" />
      case 'csv':
        return <FileText className="h-4 w-4" />
      case 'excel':
        return <FileSpreadsheet className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getSourceTypeLabel = (source: DataSource) => {
    switch (source.type) {
      case 'database':
        const mode = source.config?.mode || 'read'
        return `Database (${mode.charAt(0).toUpperCase() + mode.slice(1)} Mode)`
      case 'api':
        return 'API Source'
      case 'csv':
        return 'CSV File'
      case 'excel':
        return 'Excel File'
      default:
        return source.type.toUpperCase()
    }
  }

  const getVariableCount = (source: DataSource) => {
    if (source.type === 'api' && source.config?.extractionRules) {
      return source.config.extractionRules.length
    } else if (source.type === 'database') {
      if (source.config?.mode === 'read') {
        const configDataCount = source.config.data?.length || 0
        const variablesCount = source.variables?.length || 0
        return Math.max(configDataCount, variablesCount)
      } else {
        return 1
      }
    }
    return source.variables?.length || 0
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Import from Data Sources</DialogTitle>
            <DialogDescription>
              Select one or more data sources to import variables from. API and Database sources are prioritized at the top.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search data sources..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            <ScrollArea className="h-80 border rounded-md">
              <div className="p-2">
                {isLoadingDataSources ? (
                  <div className="flex justify-center items-center h-full">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : filteredSources.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-center p-4">
                    <div className="text-gray-500 mb-2">No compatible data sources found</div>
                    <div className="text-sm text-gray-400">
                      Create CSV, Excel, Database, or API data sources to import from.
                    </div>
                  </div>
                ) : (
                  filteredSources.map((source: DataSource) => (
                    <div
                      key={source.id}
                      className={`p-3 rounded-md border mb-2 cursor-pointer transition-colors ${
                        selectedSources.includes(source.id)
                          ? "bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"
                          : "hover:bg-gray-50 border-gray-200 dark:hover:bg-gray-800 dark:border-gray-700"
                      }`}
                      onClick={() => handleSourceToggle(source.id)}
                    >
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={selectedSources.includes(source.id)}
                          onCheckedChange={() => handleSourceToggle(source.id)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            {getSourceIcon(source.type)}
                            <div className="font-medium">{source.name}</div>
                            <Badge variant="secondary" className="text-xs">
                              {getSourceTypeLabel(source)}
                            </Badge>

                          </div>
                          <div className="text-sm text-gray-500 ml-6">
                            {getVariableCount(source)} variables
                            {source.description && (
                              <span className="block text-xs text-gray-400 mt-1">
                                {source.description}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
          <DialogFooter>
            <div className="flex items-center gap-2 flex-1">
              {selectedSources.length > 0 && (
                <span className="text-sm text-gray-600">
                  {selectedSources.length} source{selectedSources.length > 1 ? 's' : ''} selected
                </span>
              )}
            </div>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleImport} disabled={selectedSources.length === 0}>
              Import ({selectedSources.length})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </>
  )
} 