/**
 * File System Configuration Domain
 * Handles file system paths and directory configuration
 */

import * as path from 'path';
import * as os from 'os';
import { getEnv } from '../utils/env-utils.js';
import { ensureDirectoriesExist, validateDirectoryPath } from '../utils/directory-utils.js';
import { FileSystemConfigDomain } from '../types/config-types.js';

/**
 * Creates file system configuration from environment variables
 * @returns File system configuration object
 */
export function createFileSystemConfig(): FileSystemConfigDomain {
  return {
    screenshotsDir: getEnv('SCREENSHOTS_DIR', path.join(os.tmpdir(), 'screenshots')),
    nodeScenarioDir: getEnv('NODE_SCENARIO_DIR', path.join(os.tmpdir()))
  };
}

/**
 * Validates file system configuration
 * @param config File system configuration to validate
 * @returns Validation result
 */
export function validateFileSystemConfig(config: FileSystemConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate screenshots directory
  if (!config.screenshotsDir || config.screenshotsDir.trim() === '') {
    errors.push('Screenshots directory is required');
  } else {
    try {
      // Ensure directory exists before validation
      ensureDirectoriesExist([config.screenshotsDir]);
      validateDirectoryPath(config.screenshotsDir);
    } catch (error) {
      errors.push(`Screenshots directory error: ${error}`);
    }
  }

  // Validate node scenario directory
  if (!config.nodeScenarioDir || config.nodeScenarioDir.trim() === '') {
    errors.push('Node scenario directory is required');
  } else {
    try {
      // Ensure directory exists before validation
      ensureDirectoriesExist([config.nodeScenarioDir]);
      validateDirectoryPath(config.nodeScenarioDir);
    } catch (error) {
      errors.push(`Node scenario directory error: ${error}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets default file system configuration
 * @returns Default file system configuration
 */
export function getDefaultFileSystemConfig(): FileSystemConfigDomain {
  return {
    screenshotsDir: path.join(os.tmpdir(), 'screenshots'),
    nodeScenarioDir: path.join(os.tmpdir())
  };
}

/**
 * Initializes file system directories
 * @param config File system configuration
 */
export function initializeFileSystemDirectories(config: FileSystemConfigDomain): void {
  const directories = [
    config.screenshotsDir,
    config.nodeScenarioDir
  ];

  ensureDirectoriesExist(directories);
}

/**
 * Merges file system configuration with overrides
 * @param baseConfig Base configuration
 * @param overrides Configuration overrides
 * @returns Merged configuration
 */
export function mergeFileSystemConfig(
  baseConfig: FileSystemConfigDomain,
  overrides: Partial<FileSystemConfigDomain>
): FileSystemConfigDomain {
  return {
    ...baseConfig,
    ...overrides
  };
}

/**
 * Gets relative path from absolute path
 * @param absolutePath Absolute path
 * @param basePath Base path to calculate relative from
 * @returns Relative path
 */
export function getRelativePath(absolutePath: string, basePath: string = process.cwd()): string {
  return path.relative(basePath, absolutePath);
}

/**
 * Resolves a path relative to the current working directory
 * @param relativePath Relative path
 * @returns Absolute path
 */
export function resolvePath(relativePath: string): string {
  return path.resolve(relativePath);
}
