import type React from "react"
import "@/app/globals.css"
import { Inter, Orbitron } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/lib/api/auth"
import { Toaster } from "sonner"
import { Fe<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/feedback-button"
import { GoogleAnalytics } from "@/components/analytics/GoogleAnalytics"
import { StoreProvider } from "@/components/providers/store-provider"

const inter = Inter({ subsets: ["latin"] })
const orbitron = Orbitron({
  subsets: ["latin"],
  variable: '--font-orbitron',
  display: 'swap',
})

export const metadata = {
  title: "HiRafi",
  description: "Autonomous driving testing platform",
  generator: "v1"
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={orbitron.variable}>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <AuthProvider>
            <StoreProvider>
              {children}
              <Toaster position="bottom-right" richColors />
              <FeedbackButton />
            </StoreProvider>
          </AuthProvider>
        </ThemeProvider>
        <GoogleAnalytics />
      </body>
    </html>
  )
}
