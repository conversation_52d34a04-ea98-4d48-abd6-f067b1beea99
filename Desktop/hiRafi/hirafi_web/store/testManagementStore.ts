/**
 * Unified Test Management Store
 *
 * Centralized state management for all test management integrations
 * Eliminates conflicts between TestManagementContext and useUnifiedScenario
 */

import { create } from "zustand";
import { devtools } from "zustand/middleware";

// Test Management Provider Type
export type TestManagementProvider = "testrail" | "zephyrscale";

// Core interfaces
export interface TestCase {
  id: string;
  title: string;
  description?: string;
  suite?: string;
  folder?: string;
  priority?: string;
  status?: string;
  steps?: TestCaseStep[];
}

export interface TestCaseStep {
  description: string;
  expectedResult?: string;
  testData?: string;
}

export interface Project {
  id: string;
  name: string;
  key?: string; // For Jira/Zephyr Scale
}

export interface SuiteOrFolder {
  id: string;
  name: string;
  type: "suite" | "folder";
  parentId?: string;
}

export interface ProviderConfig {
  isConnected: boolean;
  isInitialized: boolean;
  config?: any;
  plugin?: any;
  error?: string;
}

export interface ImportState {
  isEnabled: boolean;
  selectedCaseId: string | null;
  caseDetails: TestCase | null;
  isLoadingDetails: boolean;
}

export interface FilterState {
  selectedProject: string | null;
  selectedSuiteOrFolder: string | null;
  searchTerm: string;
  hasMoreCases: boolean;
  currentPage: number;
}

export interface LoadingState {
  isLoading: boolean;
  isLoadingSuites: boolean;
  isLoadingFolders: boolean;
  isLoadingCases: boolean;
  isLoadingMore: boolean;
  isLoadingImportDetails: boolean;
}

// Main store state interface
export interface TestManagementState {
  // Provider management
  activeProvider: TestManagementProvider | null;
  availableProviders: TestManagementProvider[];
  initializedProviders: Set<TestManagementProvider>;

  // Provider configurations
  providers: Record<TestManagementProvider, ProviderConfig>;

  // Data state
  projects: Project[];
  suitesOrFolders: SuiteOrFolder[];
  testCases: TestCase[];
  selectedCases: string[];

  // Filter and pagination state
  filters: FilterState;

  // Loading states
  loading: LoadingState;

  // Import functionality
  import: ImportState;

  // Form integration
  formData: {
    testrailCases: string[];
    testrailSync: boolean;
    zephyrscaleCases: string[];
    zephyrscaleSync: boolean;
  };

  // Cleanup callbacks for provider switching
  cleanupCallbacks: (() => void)[];
}

// Store actions interface
export interface TestManagementActions {
  // Provider management
  setActiveProvider: (provider: TestManagementProvider) => void;
  switchProvider: (provider: TestManagementProvider) => void;
  initializeProvider: (provider: TestManagementProvider) => Promise<void>;
  setAvailableProviders: (providers: TestManagementProvider[]) => void;

  // Provider configuration
  setProviderConfig: (
    provider: TestManagementProvider,
    config: Partial<ProviderConfig>,
  ) => void;

  // Data management
  setProjects: (projects: Project[]) => void;
  setSuitesOrFolders: (items: SuiteOrFolder[]) => void;
  setTestCases: (cases: TestCase[]) => void;
  addTestCases: (cases: TestCase[]) => void; // For pagination

  // Test case selection
  setSelectedCases: (caseIds: string[]) => void;
  toggleCaseSelection: (caseId: string) => void;
  clearSelectedCases: () => void;

  // Filter management
  setSelectedProject: (projectId: string | null) => void;
  setSelectedSuiteOrFolder: (id: string | null) => void;
  setSearchTerm: (term: string) => void;
  resetFilters: () => void;

  // Loading state management
  setLoading: (key: keyof LoadingState, value: boolean) => void;

  // Import functionality
  enableImportMode: () => void;
  disableImportMode: () => void;
  setSelectedImportCase: (caseId: string | null) => void;
  setImportCaseDetails: (details: TestCase | null) => void;

  // Form integration
  updateFormData: (data: Partial<TestManagementState["formData"]>) => void;
  syncFormDataWithProvider: (
    provider: TestManagementProvider,
    caseIds: string[],
  ) => void;

  // Cleanup management
  registerCleanupCallback: (callback: () => void) => void;
  clearProviderState: (provider?: TestManagementProvider) => void;

  // Utility actions
  reset: () => void;
}

// Combined store type
export type TestManagementStore = TestManagementState & TestManagementActions;

// Initial state
const initialState: TestManagementState = {
  activeProvider: null,
  availableProviders: [],
  initializedProviders: new Set(),

  providers: {
    testrail: {
      isConnected: false,
      isInitialized: false,
    },
    zephyrscale: {
      isConnected: false,
      isInitialized: false,
    },
  },

  projects: [],
  suitesOrFolders: [],
  testCases: [],
  selectedCases: [],

  filters: {
    selectedProject: null,
    selectedSuiteOrFolder: null,
    searchTerm: "",
    hasMoreCases: false,
    currentPage: 1,
  },

  loading: {
    isLoading: false,
    isLoadingSuites: false,
    isLoadingFolders: false,
    isLoadingCases: false,
    isLoadingMore: false,
    isLoadingImportDetails: false,
  },

  import: {
    isEnabled: false,
    selectedCaseId: null,
    caseDetails: null,
    isLoadingDetails: false,
  },

  formData: {
    testrailCases: [],
    testrailSync: false,
    zephyrscaleCases: [],
    zephyrscaleSync: false,
  },

  cleanupCallbacks: [],
};

// Create the store
export const useTestManagementStore = create<TestManagementStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Provider management actions will be implemented in the next section
      setActiveProvider: (provider) => {
        set({ activeProvider: provider });
      },

      // Provider switching with comprehensive state isolation
      switchProvider: (provider) => {
        const state = get();
        console.log(`[TestManagementStore] switchProvider called: ${state.activeProvider} -> ${provider}`);

        if (!state.availableProviders.includes(provider)) {
          console.warn(`Provider ${provider} is not available`);
          return;
        }

        // Don't clear state if switching to the same provider
        if (state.activeProvider === provider) {
          console.log(`[TestManagementStore] Already on provider ${provider}, skipping state clear`);
          return;
        }

        // Check if the target provider is already properly configured
        const targetProviderConfig = state.providers[provider];
        const isTargetProviderReady = targetProviderConfig?.isConnected && targetProviderConfig?.isInitialized;

        console.log(`[TestManagementStore] Switching from ${state.activeProvider} to ${provider}`, {
          isTargetProviderReady,
          targetProviderConfig
        });

        // If target provider is already ready, just switch without clearing state
        if (isTargetProviderReady) {
          console.log(`[TestManagementStore] Target provider ${provider} is ready, switching without clearing state`);
          set({ activeProvider: provider });
          return;
        }

        console.log(`[TestManagementStore] Target provider ${provider} not ready, clearing state and initializing`);

        // 1. Clear import state to prevent cross-contamination
        set(() => ({
          import: {
            ...initialState.import,
            isEnabled: false,
          },
        }));

        // 2. Clear selected test cases
        get().clearSelectedCases();

        // 3. Reset filters
        get().resetFilters();

        // 4. Call all registered cleanup callbacks
        state.cleanupCallbacks.forEach((callback) => {
          try {
            callback();
          } catch (error) {
            console.warn("Error in provider switch cleanup callback:", error);
          }
        });

        // 5. Switch to the new provider
        set({ activeProvider: provider });

        // 6. Initialize the new provider if not already initialized
        if (!state.initializedProviders.has(provider)) {
          get().initializeProvider(provider);
        }
      },

      initializeProvider: async (provider) => {
        const state = get();

        if (state.initializedProviders.has(provider)) {
          return; // Already initialized
        }

        // Mark as initialized to prevent duplicate initialization
        set((state) => ({
          initializedProviders: new Set(state.initializedProviders).add(
            provider,
          ),
        }));

        // Set loading state
        get().setLoading("isLoading", true);

        try {
          // This will be integrated with existing provider hooks
          // For now, just mark as initialized
          get().setProviderConfig(provider, {
            isInitialized: true,
            isConnected: false, // Will be determined by actual provider check
          });
        } catch (error) {
          console.error(`Failed to initialize provider ${provider}:`, error);
          get().setProviderConfig(provider, {
            isInitialized: true,
            isConnected: false,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        } finally {
          get().setLoading("isLoading", false);
        }
      },

      setAvailableProviders: (providers) => {
        set({ availableProviders: providers });
      },

      setProviderConfig: (provider, config) => {
        console.log(`[TestManagementStore] setProviderConfig called for ${provider}:`, config);
        set((state) => {
          const newProviderState = {
            ...state.providers[provider],
            ...config,
          };
          console.log(`[TestManagementStore] New provider state for ${provider}:`, newProviderState);
          return {
            providers: {
              ...state.providers,
              [provider]: newProviderState,
            },
          };
        });
      },

      setProjects: (projects) => {
        console.log('[TestManagementStore] setProjects called with:', projects);
        set({ projects });
      },

      setSuitesOrFolders: (items) => {
        set({ suitesOrFolders: items });
      },

      setTestCases: (cases) => {
        set({ testCases: cases });
      },

      addTestCases: (cases) => {
        set((state) => ({
          testCases: [...state.testCases, ...cases],
        }));
      },

      setSelectedCases: (caseIds) => {
        set({ selectedCases: caseIds });
      },

      toggleCaseSelection: (caseId) => {
        set((state) => ({
          selectedCases: state.selectedCases.includes(caseId)
            ? state.selectedCases.filter((id) => id !== caseId)
            : [...state.selectedCases, caseId],
        }));
      },

      clearSelectedCases: () => {
        set({ selectedCases: [] });
      },

      setSelectedProject: (projectId) => {
        set((state) => ({
          filters: {
            ...state.filters,
            selectedProject: projectId,
          },
        }));
      },

      setSelectedSuiteOrFolder: (id) => {
        set((state) => ({
          filters: {
            ...state.filters,
            selectedSuiteOrFolder: id,
          },
        }));
      },

      setSearchTerm: (term) => {
        set((state) => ({
          filters: {
            ...state.filters,
            searchTerm: term,
          },
        }));
      },

      resetFilters: () => {
        set(() => ({
          filters: {
            ...initialState.filters,
          },
        }));
      },

      setLoading: (key, value) => {
        set((state) => ({
          loading: {
            ...state.loading,
            [key]: value,
          },
        }));
      },

      enableImportMode: () => {
        set((state) => ({
          import: {
            ...state.import,
            isEnabled: true,
          },
        }));
      },

      disableImportMode: () => {
        set(() => ({
          import: {
            ...initialState.import,
            isEnabled: false,
          },
        }));
      },

      setSelectedImportCase: (caseId) => {
        set((state) => ({
          import: {
            ...state.import,
            selectedCaseId: caseId,
          },
        }));
      },

      setImportCaseDetails: (details) => {
        set((state) => ({
          import: {
            ...state.import,
            caseDetails: details,
          },
        }));
      },

      updateFormData: (data) => {
        set((state) => ({
          formData: {
            ...state.formData,
            ...data,
          },
        }));
      },

      syncFormDataWithProvider: (provider, caseIds) => {
        set((state) => {
          const updates: Partial<TestManagementState["formData"]> = {};

          if (provider === "testrail") {
            updates.testrailCases = caseIds;
            updates.testrailSync = caseIds.length > 0;
            // Clear other provider data
            updates.zephyrscaleCases = [];
            updates.zephyrscaleSync = false;
          } else if (provider === "zephyrscale") {
            updates.zephyrscaleCases = caseIds;
            updates.zephyrscaleSync = caseIds.length > 0;
            // Clear other provider data
            updates.testrailCases = [];
            updates.testrailSync = false;
          }

          return {
            formData: {
              ...state.formData,
              ...updates,
            },
            selectedCases: caseIds,
          };
        });
      },

      registerCleanupCallback: (callback) => {
        set((state) => ({
          cleanupCallbacks: [...state.cleanupCallbacks, callback],
        }));
      },

      clearProviderState: (provider) => {
        if (provider) {
          // Clear state for specific provider
          set((state) => ({
            providers: {
              ...state.providers,
              [provider]: {
                ...initialState.providers[provider],
              },
            },
            // Clear data if this was the active provider
            ...(state.activeProvider === provider
              ? {
                  projects: [],
                  suitesOrFolders: [],
                  testCases: [],
                  selectedCases: [],
                  filters: { ...initialState.filters },
                  import: { ...initialState.import },
                }
              : {}),
          }));
        } else {
          // Clear all provider state
          set(() => ({
            ...initialState,
            // Preserve available providers and cleanup callbacks
            availableProviders: get().availableProviders,
            cleanupCallbacks: get().cleanupCallbacks,
          }));
        }
      },

      reset: () => {
        set(initialState);
      },
    }),
    {
      name: "test-management-store",
    },
  ),
);
