"use client"

import React, { useState, useRef } from "react"
import {
  FolderIcon,
  Plus,
  FileText,
  MoreHorizontal,
  Pencil,
  Trash2,
  Check,
  Loader2,
  Search,
  AlertTriangle
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import type { Scenario, FolderType } from "@/types/scenario"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"

interface FolderStructureProps {
  folders: FolderType[]
  scenarios: Scenario[]
  selectedFolder: string | null
  onSelectFolder: (folderId: string | null) => void
  onCreateFolder: (name: string, color: string) => void
  onRenameFolder: (folderId: string, newName: string) => void
  onDeleteFolder: (folderId: string) => void
  onDragOverFolder: (e: React.DragEvent, folderId: string) => void
  onDragLeaveFolder: () => void
  onDropOnFolder: (folderId: string) => void
  dragOverFolder: string | null
  onDeleteScenario?: (scenarioId: string) => void
  className?: string
}

export function FolderStructure({
  folders,
  scenarios,
  selectedFolder,
  onSelectFolder,
  onCreateFolder,
  onRenameFolder,
  onDeleteFolder,
  onDragOverFolder,
  onDragLeaveFolder,
  onDropOnFolder,
  dragOverFolder,
  onDeleteScenario,
  className,
}: FolderStructureProps) {
  // Folder dialog states
  const [folderDialogOpen, setFolderDialogOpen] = useState(false)
  const [newFolderName, setNewFolderName] = useState("")
  const [newFolderColor, setNewFolderColor] = useState("blue")
  const [isCreatingFolderLoading, setIsCreatingFolderLoading] = useState(false)
  const [editingFolderId, setEditingFolderId] = useState<string | null>(null)
  const [editingFolderName, setEditingFolderName] = useState("")
  const editInputRef = useRef<HTMLInputElement>(null)
  const [folderSearchQuery, setFolderSearchQuery] = useState("")

  // Delete confirmation dialog states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [scenarioToDelete, setScenarioToDelete] = useState<{id: string, name: string} | null>(null)

  // Folder delete confirmation dialog states
  const [deleteFolderDialogOpen, setDeleteFolderDialogOpen] = useState(false)
  const [folderToDelete, setFolderToDelete] = useState<string | null>(null)
  const [isDeletingFolder, setIsDeletingFolder] = useState(false)

  const handleCreateFolder = async () => {
    if (newFolderName.trim()) {
      setIsCreatingFolderLoading(true)
      try {
        await onCreateFolder(newFolderName, newFolderColor)
        setNewFolderName("")
        setNewFolderColor("blue")
        setFolderDialogOpen(false)
      } catch (error) {
        console.error("Error creating folder:", error)
      } finally {
        setIsCreatingFolderLoading(false)
      }
    }
  }

  const startRenamingFolder = (folderId: string, currentName: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setEditingFolderId(folderId)
    setEditingFolderName(currentName)
    setTimeout(() => {
      if (editInputRef.current) {
        editInputRef.current.focus()
        editInputRef.current.select()
      }
    }, 10)
  }

  const handleRenameFolder = (e: React.FormEvent) => {
    e.preventDefault()
    if (editingFolderId && editingFolderName.trim()) {
      onRenameFolder(editingFolderId, editingFolderName)
      setEditingFolderId(null)
    }
  }

  // Scenario delete functions

  const handleDeleteConfirm = () => {
    if (scenarioToDelete && onDeleteScenario) {
      onDeleteScenario(scenarioToDelete.id)
      setDeleteDialogOpen(false)
      setScenarioToDelete(null)
    }
  }

  // Folder delete functions
  const handleDeleteFolderClick = (folderId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setFolderToDelete(folderId)
    setDeleteFolderDialogOpen(true)
  }

  const handleDeleteFolderConfirm = async () => {
    if (folderToDelete && onDeleteFolder) {
      setIsDeletingFolder(true)
      try {
        await onDeleteFolder(folderToDelete)
        setDeleteFolderDialogOpen(false)
        setFolderToDelete(null)
      } catch (error) {
        console.error("Error deleting folder:", error)
      } finally {
        setIsDeletingFolder(false)
      }
    }
  }

  // Filter folders and scenarios based on search query
  const filteredFolders = folders.filter(folder =>
    folder.name.toLowerCase().includes(folderSearchQuery.toLowerCase())
  );

  // Count total scenarios
  const totalScenarios = scenarios.length;

  // Helper function to normalize folder ID (consistent with useSWRScenarios)
  const normalizeFolderId = (folderId: string | null | undefined): string => {
    if (!folderId || folderId === '' || folderId === 'none' || folderId === '1') {
      return 'uncategorized'
    }
    return folderId
  }

  // Count uncategorized scenarios (exclude inactive scenarios)
  const uncategorizedScenarios = scenarios.filter(
    s => normalizeFolderId(s.folderId) === 'uncategorized' && s.status !== "inactive"
  ).length;

  return (
    <div className={cn("flex flex-col", className)}>
      <div className="sticky top-0 z-10 p-2 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium text-gray-900 dark:text-gray-100 text-sm">Test Suites</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setFolderDialogOpen(true)}
            className="h-6 w-6 p-0 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <Plus className="h-3.5 w-3.5" />
          </Button>
        </div>

        {/* Search input */}
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
          <Input
            placeholder="Search folders..."
            value={folderSearchQuery}
            onChange={(e) => setFolderSearchQuery(e.target.value)}
            className="pl-7 h-7 text-xs bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 rounded-md"
          />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-2 bg-white dark:bg-gray-800">
        {/* All Scenarios option */}
        <div
          className={cn(
            "flex items-center gap-2 px-2 py-1.5 rounded-md cursor-pointer text-xs mb-1",
            selectedFolder === null
              ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium"
              : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800",
          )}
          onClick={() => onSelectFolder(null)}
        >
          <FileText className={cn("h-3.5 w-3.5", selectedFolder === null ? "text-blue-500" : "text-gray-400")} />
          <span className="flex-1">All Tests</span>
          <Badge
            className={cn(
              "text-xs py-0 px-1.5 h-4",
              selectedFolder === null
                ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
                : "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300",
            )}
          >
            {totalScenarios}
          </Badge>
        </div>

        {/* Uncategorized Folder */}
        <div className="mb-1">
          <div
            className={cn(
              "flex items-center gap-2 px-2 py-1.5 rounded-md cursor-pointer text-xs",
              selectedFolder === "uncategorized"
                ? "bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 font-medium"
                : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800",
              dragOverFolder === "uncategorized" && "ring-1 ring-blue-500",
            )}
            onClick={() => onSelectFolder("uncategorized")}
            onDragOver={(e) => onDragOverFolder(e, "uncategorized")}
            onDragLeave={onDragLeaveFolder}
            onDrop={() => onDropOnFolder("uncategorized")}
          >
            <div className="flex items-center">
              <div className="relative flex items-center">
                <FolderIcon className="h-3.5 w-3.5 text-gray-500 mr-2" />
              </div>
            </div>
            <span className="flex-1 truncate">
              Uncategorized
            </span>
            <Badge className="text-xs py-0 px-1.5 h-4 bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">
              {uncategorizedScenarios}
            </Badge>
          </div>
        </div>

        {/* Folders */}
        <div className="space-y-1">
          {filteredFolders
            .filter(folder => folder.id !== "uncategorized")
            .map((folder) => (
              <div key={folder.id}>
                <div
                  className={cn(
                    "flex items-center gap-2 px-2 py-1.5 rounded-md cursor-pointer text-xs",
                    selectedFolder === folder.id
                      ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium"
                      : "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800",
                    dragOverFolder === folder.id && "ring-1 ring-blue-500",
                  )}
                  onClick={() => onSelectFolder(folder.id)}
                  onDragOver={(e) => onDragOverFolder(e, folder.id)}
                  onDragLeave={onDragLeaveFolder}
                  onDrop={() => onDropOnFolder(folder.id)}
                >
                  <div className="flex items-center">
                    <div className="relative flex items-center">
                      <FolderIcon
                        className="h-3.5 w-3.5 mr-2"
                        style={{ color: folder.color || '#6b7280' }}
                      />
                    </div>
                  </div>

                  {editingFolderId === folder.id ? (
                    <form onSubmit={handleRenameFolder} className="flex-1 min-w-0" onClick={(e) => e.stopPropagation()}>
                      <Input
                        ref={editInputRef}
                        value={editingFolderName}
                        onChange={(e) => setEditingFolderName(e.target.value)}
                        className="h-5 text-xs p-1 min-w-0"
                        onKeyDown={(e) => {
                          if (e.key === "Escape") setEditingFolderId(null)
                        }}
                        autoFocus
                      />
                    </form>
                  ) : (
                    <span className="flex-1 truncate">
                      {folder.name}
                    </span>
                  )}

                  <Badge className="text-xs py-0 px-1.5 h-4 bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">
                    {scenarios.filter((s) => s.folderId === folder.id && s.status !== "inactive").length}
                  </Badge>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" size="icon" className="h-4 w-4 rounded-sm ml-0.5 p-0">
                        <MoreHorizontal className="h-2.5 w-2.5 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" sideOffset={5} className="w-32">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation()
                          startRenamingFolder(folder.id, folder.name, e)
                        }}
                        className="text-xs py-1"
                      >
                        <Pencil className="h-2.5 w-2.5 mr-2" />
                        Rename
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-xs py-1 text-red-600 focus:text-red-600"
                        onClick={(e) => handleDeleteFolderClick(folder.id, e)}
                      >
                        <Trash2 className="h-2.5 w-2.5 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Create Folder Dialog */}
      <Dialog open={folderDialogOpen} onOpenChange={setFolderDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Enter a name and select a color for your new folder.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-folder-name" className="text-sm font-medium">
                Folder Name
              </Label>
              <Input
                id="new-folder-name"
                placeholder="Enter folder name"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Folder Color
              </Label>
              <div className="grid grid-cols-5 gap-3">
                {[
                  { value: "blue", hex: "#3b82f6" },
                  { value: "green", hex: "#10b981" },
                  { value: "red", hex: "#f43f5e" },
                  { value: "purple", hex: "#8b5cf6" },
                  { value: "yellow", hex: "#f59e0b" },
                  { value: "orange", hex: "#f97316" },
                  { value: "pink", hex: "#ec4899" },
                  { value: "indigo", hex: "#6366f1" },
                  { value: "teal", hex: "#14b8a6" },
                  { value: "gray", hex: "#6b7280" }
                ].map(color => (
                  <button
                    key={color.value}
                    type="button"
                    onClick={() => setNewFolderColor(color.value)}
                    className={`
                      w-full aspect-square rounded-full border-2 relative
                      ${newFolderColor === color.value
                        ? 'border-white ring-2 ring-offset-2 ring-black dark:ring-white'
                        : 'border-transparent hover:border-white hover:scale-110'
                      }
                      transition-all duration-200
                    `}
                    title={color.value.charAt(0).toUpperCase() + color.value.slice(1)}
                    style={{ backgroundColor: color.hex }}
                  >
                    {newFolderColor === color.value && (
                      <span className="absolute inset-0 flex items-center justify-center text-white">
                        <Check className="h-4 w-4 stroke-[3]" />
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setFolderDialogOpen(false)}
              className="mr-auto"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleCreateFolder}
              disabled={!newFolderName.trim() || isCreatingFolderLoading}
            >
              {isCreatingFolderLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Folder"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Scenario Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] border-gray-200 dark:border-gray-800">
          <DialogHeader className="space-y-3">
            <DialogTitle className="text-center text-rose-600 dark:text-rose-500 text-xl">
              Delete Scenario
            </DialogTitle>
            <DialogDescription className="text-center text-gray-600 dark:text-gray-400">
              This action cannot be undone. Are you sure you want to permanently delete this scenario?
            </DialogDescription>
          </DialogHeader>

          <div className="p-4 my-2 rounded-lg bg-gray-50 dark:bg-gray-900 border border-gray-100 dark:border-gray-800">
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Scenario name:</h4>
            <p className="text-base font-semibold text-gray-900 dark:text-white break-words">
              {scenarioToDelete?.name}
            </p>
          </div>

          <div className="flex justify-end gap-3 mt-2">
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              className="border-gray-200 text-gray-700 hover:text-gray-900 dark:border-gray-700 dark:text-gray-300 dark:hover:text-white"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              className="bg-gradient-to-r from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700 text-white border-none"
            >
              Delete Scenario
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Folder Delete Confirmation Dialog */}
      <Dialog open={deleteFolderDialogOpen} onOpenChange={setDeleteFolderDialogOpen}>
        <DialogContent className="sm:max-w-[425px] border-gray-200 dark:border-gray-800">
          <DialogHeader className="space-y-3">
            <DialogTitle className="text-center text-rose-600 dark:text-rose-500 text-xl">
              Delete Folder
            </DialogTitle>
            <DialogDescription className="text-center text-gray-600 dark:text-gray-400">
              This action cannot be undone. Are you sure you want to permanently delete this folder?
              {folders.find(f => f.id === folderToDelete)?.id && scenarios.filter(s => s.folderId === folderToDelete && s.status !== "inactive").length > 0 && (
                <div className="mt-2 p-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md text-amber-700 dark:text-amber-400">
                  <AlertTriangle className="h-4 w-4 inline-block mr-1" />
                  This folder contains {scenarios.filter(s => s.folderId === folderToDelete && s.status !== "inactive").length} scenarios that will be moved to Uncategorized.
                </div>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="p-4 my-2 rounded-lg bg-gray-50 dark:bg-gray-900 border border-gray-100 dark:border-gray-800">
            <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Folder name:</h4>
            <div className="text-base font-semibold text-gray-900 dark:text-white break-words flex items-center gap-2">
              <div
                className="h-3 w-3 rounded-sm"
                style={{ backgroundColor: folders.find(f => f.id === folderToDelete)?.color || '#6b7280' }}
              />
              {folders.find(f => f.id === folderToDelete)?.name}
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-2">
            <Button
              variant="outline"
              onClick={() => setDeleteFolderDialogOpen(false)}
              disabled={isDeletingFolder}
              className="border-gray-200 text-gray-700 hover:text-gray-900 dark:border-gray-700 dark:text-gray-300 dark:hover:text-white"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteFolderConfirm}
              disabled={isDeletingFolder}
              className="bg-gradient-to-r from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700 text-white border-none"
            >
              {isDeletingFolder ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Folder"
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
