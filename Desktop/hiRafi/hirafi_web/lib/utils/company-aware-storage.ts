/**
 * Company-Aware Storage Utility
 * Creates company-specific storage keys to prevent data leakage between companies
 */

import { CompanyExpiryStorage } from './expiry-storage';

interface UserInfo {
  id?: string;
  companyId?: string;
  teamId?: string;
}

/**
 * Get current user info from localStorage
 */
function getCurrentUserInfo(): UserInfo | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch {
    return null;
  }
}

/**
 * Generate company-specific storage key
 */
export function getCompanySpecificKey(baseKey: string): string {
  const userInfo = getCurrentUserInfo();
  
  if (!userInfo?.companyId) {
    return baseKey; // Fallback to base key if no company
  }
  
  return `${baseKey}-company-${userInfo.companyId}`;
}

/**
 * Company-aware storage implementation for Zustand persist
 */
export const companyAwareStorage = {
  getItem: (name: string) => {
    if (typeof window === 'undefined') return null;
    
    try {
      const companyKey = getCompanySpecificKey(name);
      const item = localStorage.getItem(companyKey);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.warn(`Failed to get ${name}:`, error);
      return null;
    }
  },
  
  setItem: (name: string, value: any) => {
    if (typeof window === 'undefined') return;
    
    try {
      const companyKey = getCompanySpecificKey(name);
      localStorage.setItem(companyKey, JSON.stringify(value));
    } catch (error) {
      console.warn(`Failed to set ${name}:`, error);
    }
  },
  
  removeItem: (name: string) => {
    if (typeof window === 'undefined') return;
    
    try {
      const companyKey = getCompanySpecificKey(name);
      localStorage.removeItem(companyKey);
    } catch (error) {
      console.warn(`Failed to remove ${name}:`, error);
    }
  }
};

/**
 * Company-aware storage with expiry for scenario creation
 * Automatically expires data after 1 hour
 */
export const companyAwareExpiryStorage = {
  getItem: (name: string): any => {
    return CompanyExpiryStorage.getItem(name);
  },
  
  setItem: (name: string, value: any) => {
    // Default to 1 hour for scenario creation stores
    const defaultExpiry = 60 * 60 * 1000; // 1 hour
    CompanyExpiryStorage.setItem(name, value, defaultExpiry);
  },
  
  removeItem: (name: string) => {
    CompanyExpiryStorage.removeItem(name);
  }
};

/**
 * Clear all company-specific data for current company
 */
export function clearCurrentCompanyData() {
  if (typeof window === 'undefined') return;
  
  const userInfo = getCurrentUserInfo();
  if (!userInfo?.companyId) return;
  
  const companyPrefix = `-company-${userInfo.companyId}`;
  const keysToRemove: string[] = [];
  
  // Find all keys with current company prefix
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.includes(companyPrefix)) {
      keysToRemove.push(key);
    }
  }
  
  // Remove all company-specific keys
  keysToRemove.forEach(key => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`Failed to remove ${key}:`, error);
    }
  });
  
  console.log(`[CompanyStorage] Cleared ${keysToRemove.length} company-specific items`);
}

/**
 * Clear all data for a specific company (useful for admin operations)
 */
export function clearCompanyData(companyId: string) {
  if (typeof window === 'undefined') return;
  
  const companyPrefix = `-company-${companyId}`;
  const keysToRemove: string[] = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.includes(companyPrefix)) {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`Failed to remove ${key}:`, error);
    }
  });
  
  console.log(`[CompanyStorage] Cleared ${keysToRemove.length} items for company ${companyId}`);
} 