"use client"

import * as React from "react"
import { VariantProps, cva } from "class-variance-authority"
import { motion } from "framer-motion"
import { Sun, Moon, SunMoon } from "lucide-react"

import { cn } from "@/lib/utils"

const toggleVariants = cva(
  "relative inline-flex h-[38px] w-[74px] shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-amber-300 to-yellow-500 data-[state=checked]:from-indigo-800 data-[state=checked]:to-violet-900",
        subtle: "bg-gray-200 dark:bg-gray-700 data-[state=checked]:bg-gray-700 dark:data-[state=checked]:bg-gray-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface FancyToggleProps extends React.HTMLAttributes<HTMLButtonElement>, VariantProps<typeof toggleVariants> {
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  disabled?: boolean
}

const FancyToggle = React.forwardRef<HTMLButtonElement, FancyToggleProps>(
  ({ className, variant, checked, onCheckedChange, disabled = false, ...props }, ref) => {
    
    const stars = React.useMemo(() => 
      Array.from({ length: 6 }, (_, i) => ({
        id: i,
        size: Math.random() * 2 + 1,
        top: Math.random() * 20 + 5,
        left: Math.random() * 50 + 10,
        opacity: Math.random() * 0.7 + 0.3
      })), []);
    
    return (
      <button
        ref={ref}
        data-state={checked ? "checked" : "unchecked"}
        onClick={() => !disabled && onCheckedChange(!checked)}
        className={cn(toggleVariants({ variant, className }))}
        disabled={disabled}
        type="button"
        role="switch"
        aria-checked={checked}
        {...props}
      >
        <span className="sr-only">Toggle theme</span>
        
        {/* Background elements */}
        {variant === "default" && (
          <>
            {/* Stars (visible in dark mode) */}
            {stars.map((star) => (
              <motion.span
                key={star.id}
                initial={{ opacity: 0 }}
                animate={{ 
                  opacity: checked ? star.opacity : 0,
                  transition: { delay: checked ? 0.3 : 0 } 
                }}
                className="absolute rounded-full bg-white z-0"
                style={{ 
                  width: star.size, 
                  height: star.size, 
                  top: star.top, 
                  left: star.left
                }}
              />
            ))}
            
            {/* Clouds (visible in light mode) */}
            <motion.span
              className="absolute bg-white rounded-full blur-[2px] z-0"
              initial={{ opacity: 0 }}
              animate={{ 
                opacity: checked ? 0 : 0.8,
                transition: { delay: checked ? 0 : 0.3 } 
              }}
              style={{ width: 15, height: 6, top: 8, left: 10 }}
            />
            <motion.span
              className="absolute bg-white rounded-full blur-[2px] z-0"
              initial={{ opacity: 0 }}
              animate={{ 
                opacity: checked ? 0 : 0.8,
                transition: { delay: checked ? 0 : 0.3 } 
              }}
              style={{ width: 20, height: 7, top: 20, left: 15 }}
            />
          </>
        )}
        
        {/* Toggle thumb */}
        <motion.span
          className={cn(
            "absolute z-10 flex items-center justify-center rounded-full bg-white shadow-sm",
            variant === "default" ? "h-[30px] w-[30px]" : "h-[26px] w-[26px]"
          )}
          style={{
            top: variant === "default" ? 4 : 6,
            left: 4
          }}
          animate={{ 
            x: checked ? (variant === "default" ? 36 : 40) : 0,
            backgroundColor: checked ? "var(--moon-color, #d4d4d8)" : "var(--sun-color, #fcd34d)",
            transition: { 
              type: "spring", 
              stiffness: 300, 
              damping: 15 
            }
          }}
        >
          {checked ? <Moon className="h-4 w-4 text-indigo-900" /> : <Sun className="h-4 w-4 text-amber-500" />}
        </motion.span>
      </button>
    )
  }
)

FancyToggle.displayName = "FancyToggle"

export { FancyToggle }