import type { CustomEnvironment, ColorOption } from "@/types/test-data"

/**
 * Color options for environments
 */
export const colorOptions: ColorOption[] = [
  { value: "blue", label: "Blue", class: "bg-blue-500" },
  { value: "green", label: "Green", class: "bg-green-500" },
  { value: "purple", label: "Purple", class: "bg-purple-500" },
  { value: "red", label: "Red", class: "bg-red-500" },
  { value: "yellow", label: "Yellow", class: "bg-yellow-500" },
  { value: "indigo", label: "Indigo", class: "bg-indigo-500" },
  { value: "pink", label: "Pink", class: "bg-pink-500" },
  { value: "gray", label: "Gray", class: "bg-gray-500" },
]

/**
 * Default environments that cannot be deleted
 */
export const defaultEnvironments: CustomEnvironment[] = [
  {
    id: "development",
    name: "Development",
    description: "Development environment for testing new features",
    color: "blue",
    type: "default",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "testing",
    name: "Testing",
    description: "Testing environment for QA validation",
    color: "purple",
    type: "default",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "staging",
    name: "Staging",
    description: "Staging environment for pre-production testing",
    color: "yellow",
    type: "default",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "production",
    name: "Production",
    description: "Production environment for live data",
    color: "red",
    type: "default",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]
