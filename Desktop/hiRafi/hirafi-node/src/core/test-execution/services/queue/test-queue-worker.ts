/**
 * Test Queue Worker
 * Refactored to use dependency injection and break circular dependencies
 * Implements a BullMQ Worker for processing tests from the queue
 */

import { Worker, Job } from 'bullmq';
import { EventEmitter } from 'events';
import { logger } from '../../../../utils/logger.js';
import { config } from '../../../../config/index.js';
import { TestRequest } from '../../../../models/types.js';
import { 
  ITestQueueWorker, 
  TestQueueWorkerEvents,
  TestQueueWorkerStats,
  TestQueueWorkerConfig
} from '../../interfaces/test-queue-worker.interface.js';
import { IExecutionCoordinator } from '../../interfaces/execution-coordinator.interface.js';
import { REDIS_QUEUE_NAMES } from '../../../connectors/interfaces/redis-connector.interface.js';
import { INodeIdentityService } from '../../../node-manager/interfaces/node-identity-service.interface.js';
import { ITestExecutionContainer } from '../../../common/di/application-container.js';
import { ServiceNames } from '../../../node-manager/types/node-manager-types.js';
import { DependencyContainer } from '../../../common/di/dependency-container.js';

/**
 * Test Queue Worker
 * Processes test requests from the BullMQ queue using dependency injection
 */
export class TestQueueWorker extends EventEmitter implements ITestQueueWorker {
  private worker: Worker<TestRequest, { success: boolean; testId?: string; message?: string; attemptsMade?: number }, string> | null = null;
  private identityService: INodeIdentityService;
  private appContainer: ITestExecutionContainer;
  private config_?: TestQueueWorkerConfig;
  private _isInitialized = false;
  private _isPaused = false;
  private processedJobs = 0;
  private failedJobs = 0;
  private currentJob: string | null = null;
  private startTime = Date.now();
  private lastActivity = new Date().toISOString();

  constructor(
    identityService: INodeIdentityService,
    appContainer: ITestExecutionContainer,
    config?: TestQueueWorkerConfig
  ) {
    super();
    this.identityService = identityService;
    this.appContainer = appContainer;
    this.config_ = config;
    
    logger.info(`TestQueueWorker: Initialized with interface-based container for node ${identityService.getNodeId() || 'unassigned'}`);
  }

  /**
   * Initialize the worker
   */
  public async initialize(): Promise<boolean> {
    if (this._isInitialized) {
      return true;
    }

    const nodeId = this.identityService.getNodeId() || 'unassigned';

    try {
      logger.serviceInit('TestQueueWorker', `Initializing worker for node ${nodeId}`);

      // Create the worker with enhanced configuration for stuck job prevention and device retry logic
      this.worker = new Worker<TestRequest, { success: boolean; testId?: string; message?: string; attemptsMade?: number }, string>(
        REDIS_QUEUE_NAMES.TEST_QUEUE,
        async (job: Job<TestRequest, any, string>) => {
          return await this.processJob(job);
        },
        {
          connection: {
            host: config.redis?.host || 'localhost',
            port: config.redis?.port || 6379,
            password: config.redis?.password || undefined,
            db: config.redis?.db || 0,
            maxRetriesPerRequest: null, // Required for BullMQ
          },
          concurrency: this.config_?.concurrency || 1,
          maxStalledCount: this.config_?.maxStalled || 1,
          stalledInterval: this.config_?.stalledInterval || 30000
        }
      );

      // Set up event handlers
      this.setupEventHandlers();

      // Set node status as available
      await this.setNodeAvailable();

      this._isInitialized = true;
      logger.serviceInit('TestQueueWorker', `Worker initialized for node ${nodeId}`);
      return true;
    } catch (error) {
      logger.error(`TestQueueWorker: Initialization error: ${error}`);
      return false;
    }
  }

  /**
   * Check if the worker is initialized
   */
  public isInitialized(): boolean {
    return this._isInitialized;
  }

  /**
   * Pause the worker
   */
  public async pause(): Promise<void> {
    if (this.worker && !this._isPaused) {
      await this.worker.pause();
      this._isPaused = true;
      logger.info(`TestQueueWorker: Worker paused for node ${this.identityService.getNodeId()}`);
    }
  }

  /**
   * Resume the worker
   */
  public async resume(): Promise<void> {
    if (this.worker && this._isPaused) {
      await this.worker.resume();
      this._isPaused = false;
      logger.info(`TestQueueWorker: Worker resumed for node ${this.identityService.getNodeId()}`);
    }
  }

  /**
   * Check if the worker is paused
   */
  public isPaused(): boolean {
    return this._isPaused;
  }

  /**
   * Close the worker
   */
  public async close(): Promise<void> {
    try {
      if (this.worker) {
        await this.worker.close();
        this.worker = null;
      }
      
      this._isInitialized = false;
      logger.info(`TestQueueWorker: Worker closed for node ${this.identityService.getNodeId()}`);
    } catch (error: any) {
      logger.error(`TestQueueWorker: Error closing worker: ${error.message}`);
    }
  }

  /**
   * Stop a specific test if it's currently running
   */
  public async stopTest(testId: string): Promise<boolean> {
    try {
      logger.info(`TestQueueWorker: Attempting to stop test ${testId}`);

      // Check if this test is currently being processed
      if (this.currentJob !== testId) {
        logger.warn(`TestQueueWorker: Test ${testId} is not currently running on this node (current: ${this.currentJob || 'none'})`);
        return false;
      }

      // Try to get the active scope for this test
      const scope = this.appContainer.getScope(testId);
      if (!scope) {
        logger.warn(`TestQueueWorker: No active DI scope found for test ${testId}`);
        return false;
      }

      try {
        // Resolve ExecutionCoordinator from the test's scope
        const executionCoordinator = await scope.resolve<IExecutionCoordinator>(ServiceNames.EXECUTION_COORDINATOR);
        
        // Stop the specific test
        await executionCoordinator.stopSpecificTest(testId, 'Stop requested via NodeManager');
        
        logger.info(`TestQueueWorker: Successfully sent stop command to ExecutionCoordinator for test ${testId}`);
        return true;
        
      } catch (resolveError: any) {
        logger.error(`TestQueueWorker: Error resolving ExecutionCoordinator for test ${testId}: ${resolveError.message}`);
        return false;
      }

    } catch (error: any) {
      logger.error(`TestQueueWorker: Error stopping test ${testId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get worker statistics
   */
  public getStats(): TestQueueWorkerStats {
    return {
      nodeId: this.identityService.getNodeId() || 'unassigned',
      isInitialized: this._isInitialized,
      isPaused: this._isPaused,
      processedJobs: this.processedJobs,
      failedJobs: this.failedJobs,
      currentJob: this.currentJob,
      uptime: Date.now() - this.startTime,
      lastActivity: this.lastActivity
    };
  }

  /**
   * Process a job from the queue
   */
  private async processJob(job: Job<TestRequest, any, string>): Promise<{ success: boolean; testId?: string; message?: string; attemptsMade?: number }> {
    const jobData = (job.data as any).jobData || job.data;
    const testId = jobData.id;
    const nodeId = this.identityService.getNodeId() || 'unassigned';
    this.currentJob = testId;
    this.lastActivity = new Date().toISOString();

    try {
      logger.info(`TestQueueWorker: Processing job ${job.id} on node ${nodeId} (attempt ${job.attemptsMade + 1}/${job.opts.attempts || 1})`);

      this.validateTestData(jobData);

      // Emit test claimed event
      this.emit('testClaimedByWorker', testId, nodeId);

      // Process the test data using execution coordinator
      const result = await this.processTestData(job, jobData);
      
      this.processedJobs++;
      this.currentJob = null;
      
      return result;

    } catch (error: any) {
      this.failedJobs++;
      this.currentJob = null;
      return await this.handleProcessingError(error, job);
    }
  }

  /**
   * Process test data using execution coordinator with proper DI scope management
   */
  private async processTestData(job: Job<TestRequest, any, string>, testData: TestRequest): Promise<{ success: boolean; testId?: string; message?: string; attemptsMade?: number }> {
    const scopeId = testData.id;
    let scope: DependencyContainer | null = null;

    try {
      logger.info(`TestQueueWorker: Creating DI scope ${scopeId} for test ${testData.id}`);

      // Create a new DI scope for this test execution
      scope = this.appContainer.createScope(scopeId);

      logger.debug(`TestQueueWorker: Scope ${scopeId} created successfully, attempting to resolve ${ServiceNames.EXECUTION_COORDINATOR}`);

      // Resolve ExecutionCoordinator from the scoped container
      const executionCoordinator = await scope.resolve<IExecutionCoordinator>(ServiceNames.EXECUTION_COORDINATOR);

      logger.info(`TestQueueWorker: ExecutionCoordinator resolved successfully for test ${testData.id}, starting execution`);

      // Execute test using the scoped execution coordinator
      const testResult = await executionCoordinator.executeTest(testData, {
        extendLockFunction: async (duration: number) => job.extendLock(job.token!, duration),
        jobToken: job.token
      });

      logger.info(`TestQueueWorker: Test ${testData.id} completed successfully`);

      return {
        success: true,
        testId: testData.id,
        message: 'Test completed successfully',
        attemptsMade: job.attemptsMade + 1
      };

    } catch (error: any) {
      logger.error(`TestQueueWorker: Test execution failed for ${testData.id}: ${error.message}`);
      if (error.stack) {
        logger.debug(`TestQueueWorker: Error stack trace: ${error.stack}`);
      }
      throw error; // Re-throw to be handled by processJob
    } finally {
      // Always dispose the scope to clean up scoped services
      if (scope) {
        try {
          logger.debug(`TestQueueWorker: Disposing DI scope ${scopeId} for test ${testData.id}`);
          // First dispose the scope to clean up all scoped services
          await this.appContainer.disposeScope(scopeId);
          logger.debug(`TestQueueWorker: Successfully disposed DI scope ${scopeId} for test ${testData.id}`);
          
          // ENHANCED CLEANUP: Extended cooling period for comprehensive resource cleanup
          // This ensures complete cleanup of browser processes, network connections, and file handles
          logger.debug(`TestQueueWorker: Starting enhanced cleanup cooling period for test ${testData.id}`);
          await new Promise(resolve => setTimeout(resolve, 3000)); // Extended to 3 seconds
          
          // ENHANCED CLEANUP: Force garbage collection multiple times to ensure memory cleanup
          if (global.gc) {
            global.gc();
            await new Promise(resolve => setTimeout(resolve, 100));
            global.gc(); // Second GC pass for thorough cleanup
            logger.debug(`TestQueueWorker: Triggered enhanced garbage collection after test ${testData.id}`);
          }
          
          // ENHANCED CLEANUP: Force cleanup of any remaining timers, intervals, or listeners
          // This helps prevent resource leaks between tests
          logger.debug(`TestQueueWorker: Enhanced cleanup completed for test ${testData.id}`);
          
        } catch (disposeError: any) {
          logger.error(`TestQueueWorker: Error disposing scope ${scopeId}: ${disposeError.message}`);
        }
      }
    }
  }

  /**
   * Validate test data
   */
  private validateTestData(testData: TestRequest): void {
    if (!testData.id) {
      throw new Error('Test data is missing required field: id');
    }
    if (!testData.scenario) {
      throw new Error(`Test data for test ${testData.id} is missing required field: scenario`);
    }
    if (!testData.scenarioId || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(testData.scenarioId)) {
      throw new Error(`Test data for test ${testData.id} has an invalid or missing scenarioId.`);
    }
  }

  /**
   * Handle processing errors with enhanced retry logic for device allocation failures
   */
  private async handleProcessingError(error: any, job: Job<TestRequest, any, string>): Promise<{ success: boolean; testId?: string; message?: string; attemptsMade?: number }> {
    const testId = job.data?.id || 'unknown';
    const errorMessage = error.message || 'Unknown error occurred';

    logger.error(`TestQueueWorker: Error processing job ${job.id} for test ${testId}: ${errorMessage}`);

    // Check if this is a device-related error that should trigger retry
    const isDeviceError = this.isDeviceRelatedError(error);
    const isRetryableError = this.isRetryableError(error);

    if (isDeviceError) {
      logger.warn(`TestQueueWorker: Device-related error detected for test ${testId}: ${errorMessage}`);

      if (isRetryableError && job.attemptsMade < (job.opts.attempts || 3) - 1) {
        // Calculate exponential backoff delay for device allocation failures
        const baseDelay = 2000; // 2 seconds base delay
        const maxDelay = 30000; // 30 seconds max delay
        const delay = Math.min(baseDelay * Math.pow(2, job.attemptsMade), maxDelay);

        logger.info(`TestQueueWorker: Scheduling retry for test ${testId} in ${delay}ms (attempt ${job.attemptsMade + 1})`);

        // Re-throw the error to trigger BullMQ retry with exponential backoff
        throw error;
      } else {
        logger.error(`TestQueueWorker: Max retries reached for device error on test ${testId}`);
      }
    }

    this.emit('test:error', error);

    return {
      success: false,
      testId: testId,
      message: errorMessage,
      attemptsMade: job.attemptsMade + 1
    };
  }

  /**
   * Check if an error is device-related
   * @param error The error to check
   * @returns True if the error is device-related
   */
  private isDeviceRelatedError(error: any): boolean {
    const deviceErrorMessages = [
      'DeviceUnavailable',
      'device busy',
      'device in use',
      'allocation failed',
      'Failed to allocate device',
      'Failed to start session',
      'TestiniumCredentialsMissing',
      'SauceLabsCredentialsMissing',
      'No devices selected',
      'Device not found'
    ];

    const errorMessage = error.message?.toLowerCase() || '';
    return deviceErrorMessages.some(msg => errorMessage.toLowerCase().includes(msg.toLowerCase()));
  }

  /**
   * Check if an error is retryable
   * @param error The error to check
   * @returns True if the error is retryable
   */
  private isRetryableError(error: any): boolean {
    const retryableMessages = [
      'device busy',
      'device in use',
      'allocation failed',
      'timeout',
      'network error',
      'connection refused',
      'temporarily unavailable',
      'DeviceUnavailable'
    ];

    const nonRetryableMessages = [
      'TestiniumCredentialsMissing',
      'SauceLabsCredentialsMissing',
      'No devices selected',
      'Invalid test data',
      'missing test ID',
      'missing scenario'
    ];

    const errorMessage = error.message?.toLowerCase() || '';

    // Check for non-retryable errors first
    if (nonRetryableMessages.some(msg => errorMessage.includes(msg.toLowerCase()))) {
      return false;
    }

    // Check for retryable errors
    return retryableMessages.some(msg => errorMessage.includes(msg.toLowerCase()));
  }

  /**
   * Set up event handlers
   */
  private setupEventHandlers(): void {
    if (!this.worker) return;

    this.worker.on('completed', (job) => {
      logger.debug(`TestQueueWorker: Job ${job.id} completed`);
    });

    this.worker.on('failed', (job, err) => {
      logger.error(`TestQueueWorker: Job ${job?.id} failed: ${err.message}`);
    });

    this.worker.on('stalled', (jobId) => {
      logger.warn(`TestQueueWorker: Job ${jobId} stalled`);
    });
  }

  /**
   * Set node as available
   */
  private async setNodeAvailable(): Promise<void> {
    // This would typically update node status in Redis
    // For now, just log the availability
    logger.info(`TestQueueWorker: Node ${this.identityService.getNodeId() || 'unassigned'} marked as available for test processing`);
  }
}
