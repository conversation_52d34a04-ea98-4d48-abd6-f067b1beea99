import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Smartphone, Globe, HelpCircle } from 'lucide-react';
import { ReportDetail } from '@/hooks/useReport';
import {
  detectReportPlatform,
  getPlatformDisplayName,
  getPlatformColors,
  getAndroidDeviceInfo,
  getWebBrowserInfo
} from '@/lib/utils/platform-utils';

interface PlatformIndicatorProps {
  report: ReportDetail | null;
  showDetails?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function PlatformIndicator({ 
  report, 
  showDetails = false, 
  size = 'md' 
}: PlatformIndicatorProps) {
  if (!report) return null;

  const platform = detectReportPlatform(report);
  const colors = getPlatformColors(platform);
  const displayName = getPlatformDisplayName(platform);

  // Get platform-specific icon
  const IconComponent = platform === 'android' ? Smartphone : 
                       platform === 'web' ? Globe : HelpCircle;

  // Size classes
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-2.5 py-1.5',
    lg: 'text-base px-3 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <div className="flex items-center gap-2">
      <Badge 
        variant="outline" 
        className={`
          ${colors.bg} ${colors.text} ${colors.border} 
          ${sizeClasses[size]}
          flex items-center gap-1.5 font-medium
        `}
      >
        <IconComponent className={iconSizes[size]} />
        {displayName}
      </Badge>

      {showDetails && (
        <PlatformDetails report={report} platform={platform} />
      )}
    </div>
  );
}

interface PlatformDetailsProps {
  report: ReportDetail;
  platform: 'web' | 'android';
}

function PlatformDetails({ report, platform }: PlatformDetailsProps) {
  if (platform === 'android') {
    const deviceInfo = getAndroidDeviceInfo(report);
    if (!deviceInfo) return null;

    return (
      <div className="text-sm text-gray-600 dark:text-gray-400">
        <span className="font-medium">{deviceInfo.deviceName}</span>
        {deviceInfo.osVersion && (
          <span className="ml-1">({deviceInfo.osVersion})</span>
        )}
        {deviceInfo.provider && (
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded">
            {deviceInfo.provider}
          </span>
        )}
      </div>
    );
  }

  if (platform === 'web') {
    const browserInfo = getWebBrowserInfo(report);
    if (!browserInfo) return null;

    return (
      <div className="text-sm text-gray-600 dark:text-gray-400">
        <span className="font-medium">{browserInfo.browserName}</span>
        {browserInfo.browserVersion && (
          <span className="ml-1">({browserInfo.browserVersion})</span>
        )}
        {browserInfo.viewportSize && (
          <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded">
            {browserInfo.viewportSize}
          </span>
        )}
      </div>
    );
  }

  return null;
}

// Compact version for use in lists
export function PlatformBadge({ report }: { report: ReportDetail | null }) {
  return <PlatformIndicator report={report} size="sm" />;
}

// Detailed version for headers
export function PlatformHeader({ report }: { report: ReportDetail | null }) {
  return <PlatformIndicator report={report} showDetails size="lg" />;
}
