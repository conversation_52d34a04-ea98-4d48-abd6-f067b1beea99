/**
 * Report Storage Service
 * Rapor ve ekran görüntülerinin yerel dosya sistemine kaydedilmesi için servis
 */

import * as fs from 'fs';
import * as path from 'path';
import { logger } from '../utils/logger.js';
import { PATHS } from '../config/index.js';
import { TestReport } from '../models/test-types.js';

// Klasör oluşturma yardımcı fonksiyonu
function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * Save a report to the filesystem
 */
export const saveReportToFile = async (report: TestReport): Promise<string> => {
  try {
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(PATHS.DATA_DIR, 'reports');
    ensureDirectoryExists(reportsDir);

    // Create scenario directory if it doesn't exist
    const scenarioDir = path.join(reportsDir, report.scenarioId);
    ensureDirectoryExists(scenarioDir);

    // Create the report file
    const reportPath = path.join(scenarioDir, `${report.id}.json`);
    
    // Clean up circular references
    const cleanReport = {
      ...report,
      processInfo: undefined
    };
    
    // Write the report to file
    fs.writeFileSync(reportPath, JSON.stringify(cleanReport, null, 2));
    
    logger.info(`Saved report to file: ${reportPath}`);
    return reportPath;
  } catch (error: any) {
    logger.error(`Error saving report to file: ${error.message}`);
    throw error;
  }
};

/**
 * Load a report from the filesystem
 */
export const loadReportFromFile = async (reportId: string, scenarioId: string): Promise<TestReport | null> => {
  try {
    const reportPath = path.join(PATHS.DATA_DIR, 'reports', scenarioId, `${reportId}.json`);
    
    if (!fs.existsSync(reportPath)) {
      logger.warn(`Report file not found: ${reportPath}`);
      return null;
    }
    
    const reportJson = fs.readFileSync(reportPath, 'utf8');
    const report = JSON.parse(reportJson) as TestReport;
    
    return report;
  } catch (error: any) {
    logger.error(`Error loading report from file: ${error.message}`);
    return null;
  }
};

/**
 * Save a screenshot to the filesystem
 */
export const saveScreenshotToFile = async (
  reportId: string,
  scenarioId: string,
  screenshotData: string,
  index: number
): Promise<string> => {
  try {
    // Create screenshots directory if it doesn't exist
    const screenshotsDir = path.join(PATHS.DATA_DIR, 'screenshots', scenarioId, reportId);
    ensureDirectoryExists(screenshotsDir);

    // Remove data URL prefix if present
    const base64Data = screenshotData.replace(/^data:image\/\w+;base64,/, '');
    
    // Create the screenshot file
    const screenshotPath = path.join(screenshotsDir, `${index}.png`);
    fs.writeFileSync(screenshotPath, Buffer.from(base64Data, 'base64'));
    
    logger.info(`Saved screenshot to file: ${screenshotPath}`);
    return screenshotPath;
  } catch (error: any) {
    logger.error(`Error saving screenshot to file: ${error.message}`);
    throw error;
  }
};

/**
 * Get all reports for a scenario
 */
export const getReportsForScenario = async (scenarioId: string): Promise<TestReport[]> => {
  try {
    const scenarioDir = path.join(PATHS.DATA_DIR, 'reports', scenarioId);
    
    if (!fs.existsSync(scenarioDir)) {
      return [];
    }
    
    const reportFiles = fs.readdirSync(scenarioDir)
      .filter(file => file.endsWith('.json'));
    
    const reports: TestReport[] = [];
    
    for (const file of reportFiles) {
      const reportPath = path.join(scenarioDir, file);
      const reportJson = fs.readFileSync(reportPath, 'utf8');
      const report = JSON.parse(reportJson) as TestReport;
      reports.push(report);
    }
    
    // Sort by date (most recent first)
    reports.sort((a, b) => (b.startTime || 0) - (a.startTime || 0));
    
    return reports;
  } catch (error: any) {
    logger.error(`Error getting reports for scenario ${scenarioId}: ${error.message}`);
    return [];
  }
};

/**
 * Get the most recent report for a scenario
 */
export const getLatestReportForScenario = async (scenarioId: string): Promise<TestReport | null> => {
  const reports = await getReportsForScenario(scenarioId);
  
  if (reports.length === 0) {
    return null;
  }
  
  return reports[0];
};

// Export default object for backward compatibility
export default {
  saveReportToFile,
  loadReportFromFile,
  saveScreenshotToFile,
  getReportsForScenario,
  getLatestReportForScenario
}; 