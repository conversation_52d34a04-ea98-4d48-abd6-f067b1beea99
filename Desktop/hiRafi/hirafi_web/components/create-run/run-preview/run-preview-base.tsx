"use client"

import { <PERSON>actN<PERSON>, useState } from "react"
import { useRunForm } from "../run-context"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import {
  Layers,
  Calendar,
  Tag,
  Settings,
  ChevronRight,
  FileText,
  CheckSquare,
  X,
  Save
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"

// RunPreviewBase props için interface
interface RunPreviewBaseProps {
  children: ReactNode;
  isEditing?: boolean;
  runId?: string;
}

export function RunPreviewBase({ children, isEditing = false, runId }: RunPreviewBaseProps) {
  const {
    formData,
    selectedScenarios,
    selectedScenariosDetails,
    environmentSettings,
    reportSettings,
    selectedTestDataEnvironment,
    isSubmitting,
    submitRun
  } = useRunForm()

  const [isConfirming, setIsConfirming] = useState(false)
  const [show<PERSON><PERSON><PERSON><PERSON>, setShow<PERSON>on<PERSON><PERSON>] = useState(false)

  // Get accurate steps count
  const getStepsCount = (scenario: any) => {
    if (!scenario) return "N/A"

    // First check if we have the steps array directly
    if (scenario.steps && Array.isArray(scenario.steps)) {
      return scenario.steps.length
    }

    // Next check for stepsCount property
    if (typeof scenario.stepsCount === 'number') {
      return scenario.stepsCount
    }

    // Look for steps information in other properties
    if (scenario.testsCount) return scenario.testsCount
    if (scenario.actionCount) return scenario.actionCount

    // If we have no steps info but know it's a valid scenario, return at least 1
    return scenario.id ? "1" : "N/A"
  }

  // Run başlatma işlemi
  const handleStartRun = async () => {
    setIsConfirming(true)

    try {
      const result = await submitRun() as unknown as { success: boolean, runId?: string, error?: string };

      if (result && result.success) {
        setShowConfetti(true)
        setTimeout(() => setShowConfetti(false), 3000)
      }
    } catch (error) {
      console.error("Error creating run:", error);
    } finally {
      setIsConfirming(false)
    }
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring', stiffness: 300, damping: 24 }
    }
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Card className="overflow-hidden border-indigo-200 dark:border-indigo-800">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/40 dark:to-blue-950/40">
          <motion.div
            variants={itemVariants}
            className="flex justify-between items-start"
          >
            <div>
              <CardTitle className="text-xl text-indigo-700 dark:text-indigo-300">{formData.name}</CardTitle>
              {formData.description && (
                <CardDescription className="mt-2">
                  {formData.description}
                </CardDescription>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.tags && formData.tags.map(tag => (
                <Badge key={tag} variant="outline" className="bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 border-indigo-200 dark:border-indigo-800">
                  {tag}
                </Badge>
              ))}
            </div>
          </motion.div>
        </CardHeader>

        <CardContent className="p-6 space-y-6">
          <motion.div variants={itemVariants}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left column - Scenarios */}
              <div className="space-y-4">
                <div className="flex items-center text-indigo-700 dark:text-indigo-300 mb-2">
                  <Layers className="h-5 w-5 mr-2" />
                  <h3 className="font-medium">Selected Scenarios ({selectedScenarios.length})</h3>
                </div>

                <div className="divide-y max-h-[250px] overflow-y-auto">
                  <AnimatePresence>
                    {selectedScenariosDetails.map((scenario, index) => (
                      <motion.div
                        key={scenario.id}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 10 }}
                        transition={{
                          duration: 0.2,
                          delay: index * 0.05,
                          type: 'spring',
                          stiffness: 200
                        }}
                        whileHover={{
                          backgroundColor: 'rgba(99, 102, 241, 0.08)',
                          x: 2,
                          transition: { duration: 0.2 }
                        }}
                        className="py-2 px-3 rounded-md"
                      >
                        <div className="flex justify-between items-center">
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-sm truncate">{scenario.name || scenario.title}</p>
                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                              <span className="flex items-center">
                                <CheckSquare className="h-3 w-3 mr-1" />
                                {getStepsCount(scenario)} steps
                              </span>
                              {scenario.tags && scenario.tags.length > 0 && (
                                <span className="flex items-center ml-3">
                                  <Tag className="h-3 w-3 mr-1" />
                                  {scenario.tags.length} tags
                                </span>
                              )}
                            </div>
                          </div>
                          <ChevronRight className="h-4 w-4 text-gray-400" />
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </div>

              {/* Right column - Settings */}
              <div className="space-y-4">
                <div className="flex items-center text-indigo-700 dark:text-indigo-300 mb-2">
                  <Settings className="h-5 w-5 mr-2" />
                  <h3 className="font-medium">Configuration</h3>
                </div>

                {/* Platform-specific preview content */}
                {children}
              </div>
            </div>
          </motion.div>
        </CardContent>

        <CardFooter className="bg-gray-50 dark:bg-gray-900/50 p-4 flex justify-end">
          <Button
            onClick={handleStartRun}
            disabled={isSubmitting || isConfirming}
            className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white h-10 shadow-md transition-all duration-200 font-medium"
          >
            {isSubmitting || isConfirming ? (
              <>
                <div className="mr-2 h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                {isEditing ? "Saving..." : "Starting..."}
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {isEditing ? "Save Run" : "Start Run"}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
