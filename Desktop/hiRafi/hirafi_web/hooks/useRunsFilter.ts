import { create } from 'zustand';
import { storeManager } from '@/lib/utils/store-management';

/**
 * Filter state for runs page
 */
interface RunsFilterState {
  searchQuery: string;
  activeFilter: string;
  activePlatform: string;
  dateFilter: string;
  customDateRange: { start: Date | null; end: Date | null };
  sortBy: string;
  sortOrder: string;
  currentPage: number;
  itemsPerPage: number;
  displayView: "grid" | "list";
}

/**
 * Actions for runs filter store
 */
interface RunsFilterActions {
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status: string) => void;
  setPlatformFilter: (platform: string) => void;
  setDateFilter: (filter: string) => void;
  setCustomDateRange: (range: { start: Date | null; end: Date | null }) => void;
  setSort: (sortBy: string, sortOrder: string) => void;
  setCurrentPage: (page: number) => void;
  setItemsPerPage: (count: number) => void;
  setDisplayView: (view: "grid" | "list") => void;
  resetFilters: () => void;
  resetPage: () => void;
  clearPersistedState: () => void;
  
  // Computed property for SWR key
  getQueryKey: () => string;
}

type RunsFilterStore = RunsFilterState & RunsFilterActions;

const initialState: RunsFilterState = {
  searchQuery: "",
  activeFilter: "all",
  activePlatform: "all",
  dateFilter: "all",
  customDateRange: { start: null, end: null },
  sortBy: "priority",
  sortOrder: "desc",
  currentPage: 1,
  itemsPerPage: 10,
  displayView: "grid"
};

export const useRunsFilter = create<RunsFilterStore>((set, get) => ({
  ...initialState,
  
  setSearchQuery: (query: string) =>
    set((state) => ({ 
      searchQuery: query, 
      currentPage: 1 // Reset page when search changes
    })),
  
  setStatusFilter: (status: string) =>
    set((state) => ({ 
      activeFilter: status, 
      currentPage: 1 // Reset page when filter changes
    })),
  
  setPlatformFilter: (platform: string) =>
    set((state) => ({ 
      activePlatform: platform, 
      currentPage: 1 // Reset page when filter changes
    })),
  
  setDateFilter: (filter: string) =>
    set((state) => ({ 
      dateFilter: filter, 
      currentPage: 1 // Reset page when filter changes
    })),
  
  setCustomDateRange: (range: { start: Date | null; end: Date | null }) =>
    set((state) => ({ 
      customDateRange: range, 
      currentPage: 1 // Reset page when filter changes
    })),
  
  setSort: (sortBy: string, sortOrder: string) =>
    set({ sortBy, sortOrder }),
  
  setCurrentPage: (page: number) =>
    set({ currentPage: page }),
  
  setItemsPerPage: (count: number) =>
    set({ itemsPerPage: count, currentPage: 1 }),
  
  setDisplayView: (view: "grid" | "list") =>
    set({ displayView: view }),
  
  resetFilters: () =>
    set((state) => ({
      ...initialState,
      itemsPerPage: state.itemsPerPage, // Preserve items per page
      displayView: state.displayView // Preserve display view
    })),
  
  resetPage: () =>
    set({ currentPage: 1 }),

  // Clear persisted state for store manager
  clearPersistedState: () => {
    set(initialState);
  },
  
  getQueryKey: () => {
    const state = get();
    
    // Create query parameters object with stable ordering
    const params: Record<string, any> = {};
    
    // Only add filters that are meaningful (not default values)
    if (state.activeFilter && state.activeFilter !== 'all') {
      params.status = state.activeFilter;
    }
    
    if (state.searchQuery && state.searchQuery.trim()) {
      params.search = state.searchQuery.trim();
    }
    
    if (state.activePlatform && state.activePlatform !== 'all') {
      params.platform = state.activePlatform;
    }
    
    if (state.dateFilter && state.dateFilter !== 'all') {
      params.dateFilter = state.dateFilter;
      
      // Add custom date range if applicable
      if (state.dateFilter === 'custom' && state.customDateRange.start && state.customDateRange.end) {
        params.startDate = state.customDateRange.start.toISOString();
        params.endDate = state.customDateRange.end.toISOString();
      }
    }
    
    // Always include pagination and sorting for consistency
    params.sortBy = state.sortBy;
    params.sortOrder = state.sortOrder;
    params.page = state.currentPage;
    params.limit = state.itemsPerPage;
    
    // Create stable key by ordering properties consistently
    const orderedParams = {
      status: params.status,
      search: params.search,
      platform: params.platform,
      dateFilter: params.dateFilter,
      startDate: params.startDate,
      endDate: params.endDate,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
      page: params.page,
      limit: params.limit
    };
    
    // Remove undefined values to keep key clean
    Object.keys(orderedParams).forEach(key => {
      if ((orderedParams as any)[key] === undefined) {
        delete (orderedParams as any)[key];
      }
    });
    
    // Return JSON string for SWR key
    return JSON.stringify(orderedParams);
  }
}));

// Register this store with the store manager for automatic cleanup
if (typeof window !== 'undefined') {
  storeManager.registerStore(useRunsFilter.getState());
}
