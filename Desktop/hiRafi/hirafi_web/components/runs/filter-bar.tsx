"use client"

import { useState, useEffect } from "react"
import { Search, X, Filter, ChevronDown } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { StatusFilter } from "./status-filter"
import { DateFilter } from "./date-filter"
import { PlatformFilter } from "./platform-filter"
import { SortControl } from "./sort-control"
import { MoreFilters } from "./more-filters"
import { ActiveFilters } from "./active-filters"
import { cn } from "@/lib/utils"

export interface FilterBarProps {
  // Search
  searchQuery: string
  onSearchChange: (query: string) => void

  // Status filter
  activeFilter: string
  onStatusFilterChange: (status: string) => void

  // Platform filter
  activePlatform: string
  onPlatformFilterChange: (platform: string) => void

  // Date filter
  dateFilter: string
  customDateRange: { start: Date | null; end: Date | null }
  onDateFilterChange: (filter: string, customRange?: { start: Date | null; end: Date | null }) => void

  // Sorting
  sortBy: string
  sortOrder: string
  onSortChange: (by: string, order: string) => void

  // Display options
  itemsPerPage: number
  onItemsPerPageChange: (count: number) => void
  displayView: "grid" | "list"
  onDisplayViewChange: (view: "grid" | "list") => void

  // Clear all filters
  onClearFilters: () => void

  // Loading state
  isSearching?: boolean
}

export function FilterBar({
  searchQuery,
  onSearchChange,
  activeFilter,
  onStatusFilterChange,
  activePlatform,
  onPlatformFilterChange,
  dateFilter,
  customDateRange,
  onDateFilterChange,
  sortBy,
  sortOrder,
  onSortChange,
  itemsPerPage,
  onItemsPerPageChange,
  displayView,
  onDisplayViewChange,
  onClearFilters,
  isSearching = false
}: FilterBarProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [filtersExpanded, setFiltersExpanded] = useState(false)

  // Check if any filters are active
  const hasActiveFilters = !!(
    searchQuery ||
    (activeFilter && activeFilter !== "all") ||
    (activePlatform && activePlatform !== "all") ||
    (dateFilter && dateFilter !== "all")
  )

  // Handle responsive layout
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    return () => {
      window.removeEventListener("resize", checkMobile)
    }
  }, [])

  // Toggle filters expansion on mobile
  const toggleFilters = () => {
    setFiltersExpanded(!filtersExpanded)
  }

  return (
    <div className="w-full bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="p-4 space-y-4">
        {/* Top row - always visible */}
        <div className="flex flex-col md:flex-row gap-3">
          {/* Search input - always visible */}
          <div className="relative flex-1">
            <Search className={cn(
              "absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4",
              isSearching ? "animate-spin text-primary" : "text-gray-500 dark:text-gray-400"
            )} />
            <Input
              type="text"
              placeholder="Search runs by name..."
              className="pl-9 pr-8 w-full"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                onClick={() => onSearchChange("")}
              >
                <X className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
              </Button>
            )}
          </div>

          {/* Mobile filter toggle */}
          {isMobile && (
            <Button
              variant="outline"
              size="sm"
              className="md:hidden"
              onClick={toggleFilters}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {hasActiveFilters && (
                <span className="ml-1.5 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] font-medium text-primary-foreground">
                  !
                </span>
              )}
            </Button>
          )}

          {/* Desktop filter controls - always visible on desktop */}
          {(!isMobile || filtersExpanded) && (
            <div className={cn(
              "flex flex-col md:flex-row gap-2",
              isMobile ? "mt-2" : ""
            )}>
              <StatusFilter
                activeFilter={activeFilter}
                onStatusFilterChange={onStatusFilterChange}
              />

              <PlatformFilter
                activePlatform={activePlatform}
                onPlatformFilterChange={onPlatformFilterChange}
              />

              <DateFilter
                dateFilter={dateFilter}
                customDateRange={customDateRange}
                onDateFilterChange={onDateFilterChange}
              />

              <MoreFilters
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={onItemsPerPageChange}
                displayView={displayView}
                onDisplayViewChange={onDisplayViewChange}
              />

              <SortControl
                sortBy={sortBy}
                sortOrder={sortOrder}
                onSortChange={onSortChange}
              />
            </div>
          )}
        </div>

        {/* Active filters display */}
        {hasActiveFilters && (
          <ActiveFilters
            searchQuery={searchQuery}
            onClearSearch={() => onSearchChange("")}
            activeFilter={activeFilter}
            onClearStatusFilter={() => onStatusFilterChange("all")}
            activePlatform={activePlatform}
            onClearPlatformFilter={() => onPlatformFilterChange("all")}
            dateFilter={dateFilter}
            onClearDateFilter={() => onDateFilterChange("all")}
            onClearAllFilters={onClearFilters}
          />
        )}
      </div>
    </div>
  )
}
