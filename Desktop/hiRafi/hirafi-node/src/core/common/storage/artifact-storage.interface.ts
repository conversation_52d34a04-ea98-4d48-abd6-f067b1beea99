/**
 * Unified Artifact Storage System
 * Common interfaces for storing and retrieving test artifacts (screenshots, videos, logs, etc.)
 */

/**
 * Metadata for an artifact
 */
export interface IArtifactMetadata {
  testId: string;
  stepName?: string;
  timestamp: Date;
  platform: 'web' | 'mobile' | 'api';
  artifactType: 'screenshot' | 'video' | 'log' | 'other';
  contentType: string; // e.g., 'image/png', 'video/mp4'
  customTags?: Record<string, string>;
}

/**
 * Represents an artifact to be stored
 */
export interface IArtifact {
  metadata: IArtifactMetadata;
  fileName: string; // e.g., test_id_timestamp_screenshot.png
  filePath?: string; // Local path before upload
  contentStream?: NodeJS.ReadableStream; // For direct streaming
  contentBuffer?: Buffer; // For small files
}

/**
 * Result of an upload operation
 */
export interface IUploadResult {
  storagePath: string; // Full path in the storage (e.g., s3://bucket/path/to/artifact)
  url?: string; // Publicly accessible URL, if applicable
}

/**
 * Common interface for artifact storage providers
 */
export interface IArtifactStorage {
  /**
   * Gets the name of the storage provider (e.g., 's3', 'minio')
   * @returns The provider name as a string
   */
  getProviderName(): string;
  /**
   * Initializes the storage provider with necessary configurations.
   * @param config Provider-specific configuration.
   */
  initialize(config: any): Promise<void>;

  /**
   * Uploads an artifact to the storage.
   * @param artifact The artifact to upload.
   * @param destinationPath Optional path prefix within the bucket.
   * @returns A promise resolving to the storage path and URL of the uploaded artifact.
   */
  uploadArtifact(artifact: IArtifact, destinationPath?: string): Promise<IUploadResult>;

  /**
   * Downloads an artifact from the storage.
   * @param storagePath The full path of the artifact in storage.
   * @returns A promise resolving to a readable stream of the artifact content.
   */
  downloadArtifact(storagePath: string): Promise<NodeJS.ReadableStream>;

  /**
   * Deletes an artifact from the storage.
   * @param storagePath The full path of the artifact in storage.
   * @returns A promise resolving when the artifact is deleted.
   */
  deleteArtifact(storagePath: string): Promise<void>;

  /**
   * Lists artifacts in a given path.
   * @param pathPrefix The path prefix to list artifacts from.
   * @returns A promise resolving to a list of artifact names or full paths.
   */
  listArtifacts(pathPrefix: string): Promise<string[]>;

  /**
   * Generates a pre-signed URL for temporary access to an artifact.
   * @param storagePath The full path of the artifact in storage.
   * @param expiresInSeconds The duration for which the URL is valid.
   * @param operation 'getObject' | 'putObject'
   * @returns A promise resolving to the pre-signed URL.
   */
  getPresignedUrl(storagePath: string, expiresInSeconds: number, operation: 'getObject' | 'putObject'): Promise<string>;

  /**
   * For backward compatibility with the original interface
   * @param filePath Path to the file to upload
   * @param destinationKey Key to store the file under
   * @param contentType MIME type of the file
   * @returns Promise resolving to the URL of the stored file
   */
  uploadFile(filePath: string, destinationKey: string, contentType?: string): Promise<string>;

  /**
   * For backward compatibility with the original interface
   * @param buffer Buffer to upload
   * @param destinationKey Key to store the buffer under
   * @param contentType MIME type of the buffer
   * @returns Promise resolving to the URL of the stored buffer
   */
  uploadBuffer(buffer: Buffer, destinationKey: string, contentType: string): Promise<string>;

  /**
   * For backward compatibility with the original interface
   * @param destinationKey Key the file is stored under
   * @returns Promise resolving to the URL of the file
   */
  getFileUrl(destinationKey: string): Promise<string>;
}
