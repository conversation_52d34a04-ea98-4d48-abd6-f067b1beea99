/**
 * Node State Service Interface
 * 
 * Defines the contract for node state management services.
 * Handles node status, test execution state, and statistics.
 */

import { TestRequest, TestResult } from '../../../models/types.js';
import { NodeStatus, TestExecutionInfo } from '../types/node-manager-types.js';

/**
 * Node State Service Interface
 * 
 * Manages node-level state including status, test execution tracking,
 * and statistics. Replaces the singleton NodeStateManager with dependency injection.
 */
export interface INodeStateService {
  /**
   * Atomically transition from one state to another
   * @param fromState Expected current state
   * @param toState Desired new state
   * @param testId Test ID (optional)
   * @param testData Test data (optional)
   * @returns True if transition was successful
   */
  trySetState(fromState: NodeStatus, toState: NodeStatus, testId?: string, testData?: TestRequest): boolean;

  /**
   * Get current test ID
   * @returns Current test ID or null
   */
  getCurrentTestId(): string | null;

  /**
   * Get current test data
   * @returns Current test data or null
   */
  getCurrentTestData(): TestRequest | null;

  /**
   * Check if node is running a test
   * @returns True if running a test
   */
  isRunningTest(): boolean;

  /**
   * Get current node status
   * @returns Current node status
   */
  getStatus(): NodeStatus;

  /**
   * Set stopping state
   * @param stopping Whether node is stopping
   */
  setStopping(stopping: boolean): void;

  /**
   * Check if node is in stopping state
   * @returns True if stopping
   */
  isStopping(): boolean;

  /**
   * Get detailed status including stopping state
   * @returns Detailed node status
   */
  getDetailedStatus(): NodeStatus;

  /**
   * Get full execution state
   * @returns Complete execution information
   */
  getExecutionState(): TestExecutionInfo;

  /**
   * Update test counters based on result
   * @param result Test result to process
   */
  updateTestCounters(result: TestResult): void;

  /**
   * Get test statistics
   * @returns Test execution statistics
   */
  getTestStatistics(): {
    executed: number;
    succeeded: number;
    failed: number;
    uptime: number;
  };

  /**
   * Reset all counters and state
   */
  reset(): void;

  /**
   * Get uptime in seconds
   * @returns Uptime in seconds
   */
  getUptime(): number;
}
