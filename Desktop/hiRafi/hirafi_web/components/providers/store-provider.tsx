"use client"

import { useStoreContext } from "@/hooks/useStoreContext"
import { useExpiryCleanup } from "@/hooks/useExpiryCleanup"

/**
 * Store Provider Component
 * Manages store context at the application level
 */
export function StoreProvider({ children }: { children: React.ReactNode }) {
  // Initialize store context management
  useStoreContext()
  
  // Initialize automatic expiry cleanup
  useExpiryCleanup()
  
  return <>{children}</>
} 