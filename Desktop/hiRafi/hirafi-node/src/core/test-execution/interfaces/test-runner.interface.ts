/**
 * Test Runner Interface
 * Defines the contract for platform-specific test runners
 */

import { ScenarioStep, TestRequest, TestResult, StepResult, StepStatus, EnvironmentSettings } from '../../../models/types.js';

/**
 * Test Runner Interface
 * Defines the contract for platform-specific test runners
 */
export interface ITestRunner {
  // Core test execution methods
  runTest(testRequest: TestRequest): Promise<TestResult>;
  executeTestSteps(target: any, testRequest: TestRequest, steps: ScenarioStep[], totalSteps: number): Promise<StepResult[]>;
  executeTestStep(target: any, step: ScenarioStep, testId: string, stepIndex: number): Promise<StepResult>;

  // Test control methods
  abortTest(): Promise<void>;
  isTestRunning(): boolean;

  // Environment and configuration methods
  initialize(nodeId: string, config: any): Promise<void>;
  applyEnvironmentSettings(target: any, settings: EnvironmentSettings): Promise<void>;
  // Note: AI configuration is now handled centrally by AIConfigurationManager

  // Notification methods
  notifyTestStarted(testRequest: TestRequest, totalSteps: number): void;
  notifyTestCompleted(testRequest: TestRequest, result: TestResult): void;
  sendStepProgress(testId: string, step: ScenarioStep, stepIndex: number, totalSteps: number, status: StepStatus, additionalData?: any): void;
}
