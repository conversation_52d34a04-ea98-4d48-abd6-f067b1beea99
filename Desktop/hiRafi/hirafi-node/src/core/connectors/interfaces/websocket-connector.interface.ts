/**
 * WebSocket Connector Interface
 * Defines the contract for WebSocket-based connectors
 */

import { EventEmitter } from 'events';
import { IBaseConnector } from './base-connector.interface.js';

/**
 * WebSocket connector interface
 */
export interface IWebSocketConnector extends IBaseConnector {
  /**
   * Connect to WebSocket server
   */
  connect(): Promise<void>;

  /**
   * Send a message to the hub
   * @param message Message to send
   */
  sendMessage(message: any): Promise<boolean>;

  /**
   * Register node with hub
   * @param name Node name
   * @param capabilities Node capabilities
   * @param clientId Optional client ID
   */
  registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null>;

  /**
   * Send heartbeat to hub
   */
  sendHeartbeat(): Promise<boolean>;

  /**
   * Start heartbeat interval
   */
  startHeartbeat(): void;

  /**
   * Stop heartbeat interval
   */
  stopHeartbeat(): void;

  /**
   * Get next test from hub
   */
  getNextTest(): Promise<any>;

  /**
   * Request next test from hub
   */
  requestNextTest(): Promise<boolean>;

  /**
   * Update test status
   * @param testId Test ID
   * @param status New status
   * @param result Test result (optional)
   * @param runReportId Run report ID (optional)
   */
  updateTestStatus(testId: string, status: string, result?: any, runReportId?: string): Promise<boolean>;

  /**
   * Send test claimed notification
   * @param testId Test ID
   * @param nodeId Node ID
   */
  sendTestClaimed(testId: string, nodeId?: string): Promise<boolean>;

  /**
   * Send test started notification
   * @param testId Test ID
   * @param testData Test data
   */
  sendTestStarted(testId: string, testData?: any): Promise<boolean>;

  /**
   * Send test completed notification
   * @param testId Test ID
   * @param result Test result
   */
  sendTestCompleted(testId: string, result?: any): Promise<boolean>;

  /**
   * Send test failed notification
   * @param testId Test ID
   * @param error Error details
   */
  sendTestFailed(testId: string, error?: any): Promise<boolean>;

  /**
   * Send test running notification
   * @param testId Test ID
   */
  sendTestRunning(testId: string): Promise<boolean>;

  /**
   * Send video upload completed notification
   * @param testId Test ID
   * @param videoUrl Video URL
   */
  sendVideoUploadCompleted(testId: string, videoUrl: string): Promise<boolean>;

  /**
   * Send registration message
   */
  sendRegistration(): Promise<void>;

  /**
   * Reset reconnection state
   */
  resetReconnectionState(): void;

  /**
   * Send node status response to hub
   * @param isRunning Whether node is running a test
   * @param testId Current test ID (if any)
   */
  sendNodeStatusResponse(isRunning: boolean, testId?: string): Promise<boolean>;

  /**
   * Send current test ID response to hub
   * @param testId Current test ID or 'none'
   */
  sendCurrentTestIdResponse(testId: string): Promise<boolean>;
}

/**
 * WebSocket connection manager interface
 */
export interface IWebSocketConnectionManager extends EventEmitter {
  /**
   * Connect to WebSocket server
   * @param url WebSocket URL
   * @param headers Connection headers
   */
  connect(url: string, headers?: Record<string, string>): Promise<void>;

  /**
   * Send message
   * @param message Message to send
   */
  send(message: string): Promise<boolean>;

  /**
   * Close connection
   */
  close(): Promise<void>;

  /**
   * Check if connected
   */
  isConnected(): boolean;

  /**
   * Get connection statistics
   */
  getConnectionStats(): Record<string, any>;

  /**
   * Get stats (alias for getConnectionStats)
   */
  getStats(): Record<string, any>;

  /**
   * Update pong timestamp
   * @param timestamp Timestamp
   */
  updatePongTimestamp(timestamp: number): void;

  /**
   * Reset reconnection state
   */
  resetReconnectionState(): void;

  /**
   * Initialize the connection manager
   * @param config Configuration
   */
  initialize?(config: any): Promise<void>;
}

/**
 * WebSocket message handler interface
 */
export interface IWebSocketMessageHandler {
  /**
   * Handle incoming message
   * @param message Message data
   */
  handleMessage(message: any): void;

  /**
   * Register message handler for specific type
   * @param messageType Message type
   * @param handler Handler function
   */
  registerHandler(messageType: string, handler: (data: any) => void): void;

  /**
   * Get handler statistics
   */
  getStats(): Record<string, any>;

  /**
   * Initialize the message handler
   * @param config Configuration
   */
  initialize?(config: any): Promise<void>;

  /**
   * Close the message handler
   */
  close?(): Promise<void>;
}
