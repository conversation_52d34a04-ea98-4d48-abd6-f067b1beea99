/**
 * Connection Service Interface
 * 
 * Defines the contract for connection management services.
 * Handles WebSocket and Redis connections with proper lifecycle management.
 */

import { ConnectionStatus, ConnectionType } from '../types/node-manager-types.js';

/**
 * Connection Service Interface
 * 
 * Manages WebSocket and Redis connections with proper initialization,
 * reconnection, and lifecycle management. Replaces the singleton
 * ConnectionManager with dependency injection.
 */
export interface IConnectionService {
  /**
   * Initialize all connections
   * @returns Promise resolving to true if successful
   */
  initialize(): Promise<boolean>;

  /**
   * Initialize WebSocket connection
   * @returns Promise resolving to true if successful
   */
  initializeWebSocket(): Promise<boolean>;

  /**
   * Initialize Redis connection
   * @returns Promise resolving to true if successful
   */
  initializeRedis(): Promise<boolean>;

  /**
   * Get connection status for all or specific connection
   * @param type Optional connection type to check
   * @returns Connection status
   */
  getConnectionStatus(type?: ConnectionType): ConnectionStatus | boolean;

  /**
   * Check if WebSocket is connected
   * @returns True if WebSocket is connected
   */
  isWebSocketConnected(): boolean;

  /**
   * Get the underlying WebSocket connector instance
   * @returns The WebSocket connector instance or null if not available
   */
  getWebSocketConnector(): any | null;

  /**
   * Check if Redis is connected
   * @returns True if Redis is connected
   */
  isRedisConnected(): boolean;

  /**
   * Reconnect all failed connections
   * @returns Promise that resolves when reconnection attempts complete
   */
  reconnectAll(): Promise<void>;

  /**
   * Reconnect specific connection type
   * @param type Connection type to reconnect
   * @returns Promise resolving to true if successful
   */
  reconnect(type: ConnectionType): Promise<boolean>;

  /**
   * Handle registration required from hub
   * @returns Promise resolving to true if successful
   */
  handleRegistrationRequired(): Promise<boolean>;

  /**
   * Close all connections
   * @returns Promise that resolves when closed
   */
  close(): Promise<void>;

  // registerNodeWithRedis removed - using WebSocket-only registration

  /**
   * Get registration state
   * @returns Current registration state
   */
  getRegistrationState(): string;

  /**
   * Reset registration state
   */
  resetRegistrationState(): void;

  /**
   * Get the current node ID
   * @returns Current node ID or null if not available
   */
  getNodeId(): string | null;
}
