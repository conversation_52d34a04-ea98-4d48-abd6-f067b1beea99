/**
 * Configuration Module
 * Centralizes all configuration settings for the application
 */

import * as dotenv from 'dotenv';
import * as path from 'path';
import { NodeConfig } from '../models/types.js';
import { ConfigurationManager } from './core/config-manager.js';
import { ConfigurationService } from './services/configuration-service.js';
import { ConfigurationFactory } from './core/config-factory.js';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// Create configuration using the factory
const config: NodeConfig = ConfigurationFactory.createFromEnvironment();

// Initialize modern configuration service (DI pattern)
const configService = new ConfigurationService({
  enableWatchers: true,
  enableHistory: true,
  maxHistorySize: 100,
  enableLogging: true
});
configService.initialize(config);

// Initialize legacy configuration manager for backward compatibility
const configManager = ConfigurationManager.getInstance();
configManager.initialize(config);

// Initialize configuration (create directories, etc.)
ConfigurationFactory.initializeConfiguration(config);

/**
 * Update a configuration value at runtime
 * @param key Configuration key to update
 * @param value New value
 */
export function updateConfig<K extends keyof NodeConfig>(key: K, value: NodeConfig[K]): void {
  config[key] = value;
  // Update both modern service and legacy manager for consistency
  configService.set(key, value, 'runtime');
  configManager.set(key, value, 'runtime');
}

// Export the configuration and related utilities
export { config };
export { configManager }; // Legacy export for backward compatibility
export { configService }; // Modern DI-based service

// Export new DI-based services
export * from './services/index.js';

export default config;