"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { X } from 'lucide-react'

interface DashboardPopupProps {
  isAuthenticated: boolean
}

export default function DashboardPopup({ isAuthenticated }: DashboardPopupProps) {
  const router = useRouter()
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Kullanıcı giriş yapmışsa ve daha önce popup'ı gizlemeyi seçmemişse popup'ı göster
    if (isAuthenticated) {
      const hidePopup = localStorage.getItem('hideAppPopup') === 'true'
      if (!hidePopup) {
        // Popup'ı biraz gecikmeyle göster (sayfa yüklendikten sonra)
        const timer = setTimeout(() => {
          setIsVisible(true)
        }, 1500)
        return () => clearTimeout(timer)
      }
    }
  }, [isAuthenticated])

  // Popup'ı kapat
  const handleClose = () => {
    setIsVisible(false)
  }

  // Popup'ı bir daha gösterme
  const handleDontShowAgain = () => {
    localStorage.setItem('hideAppPopup', 'true')
    setIsVisible(false)
  }

  // Dashboard'a git
  const handleGoToDashboard = () => {
    router.push('/dashboard')
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-6 right-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 w-80 z-50 border border-gray-200 dark:border-gray-700 animate-fade-in">
      <div className="flex justify-between items-start mb-3">
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">Uygulama Açık</h3>
        <button 
          onClick={handleClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <X size={18} />
        </button>
      </div>
      
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
        Giriş yapmış durumdasınız. Uygulama dashboard'ına gitmek ister misiniz?
      </p>
      
      <div className="flex flex-col space-y-2">
        <button
          onClick={handleGoToDashboard}
          className="w-full py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg text-sm hover:shadow-md transition-shadow"
        >
          Dashboard'a Git
        </button>
        
        <button
          onClick={handleDontShowAgain}
          className="w-full py-2 text-gray-600 dark:text-gray-400 text-sm hover:text-gray-800 dark:hover:text-gray-200"
        >
          Bir daha gösterme
        </button>
      </div>
    </div>
  )
}
