/**
 * Resource Manager Interface
 * 
 * Defines the contract for managing test execution resources including
 * processes, browser instances, temporary files, and network connections.
 */

/**
 * Resource cleanup options
 */
export interface ResourceCleanupOptions {
  testId: string;
  reason?: string;
  forceCleanup?: boolean;
  timeout?: number;
  cleanupTypes?: ResourceType[];
}

/**
 * Resource cleanup result
 */
export interface ResourceCleanupResult {
  success: boolean;
  cleanedResources: string[];
  errors: Error[];
  duration: number;
}

/**
 * Resource type enumeration
 */
export enum ResourceType {
  BROWSER = 'browser',
  PROCESS = 'process',
  TEMPORARY_FILES = 'temporary_files',
  NETWORK_CONNECTIONS = 'network_connections',
  LOCK_EXTENSIONS = 'lock_extensions'
}

/**
 * Browser cleanup information
 */
export interface BrowserCleanupInfo {
  browserType?: string;
  profilePath?: string;
  tempDirs?: string[];
  userDataDir?: string;
  processId?: number;
}

/**
 * Process cleanup information
 */
export interface ProcessCleanupInfo {
  processId: number;
  processType: 'browser' | 'ffmpeg' | 'other';
  description?: string;
  startTime: Date;
}

/**
 * Resource statistics
 */
export interface ResourceStatistics {
  activeTests: number;
  totalResources: number;
  resourcesByType: Record<ResourceType, number>;
  resourcesByTest: Record<string, number>;
}

/**
 * Interface for managing test execution resources
 * 
 * This interface provides comprehensive resource management including
 * tracking, cleanup, and monitoring of all test-related resources.
 */
export interface IResourceManager {
  /**
   * Initialize the resource manager
   * @returns Promise that resolves when initialization is complete
   */
  initialize(): Promise<void>;

  /**
   * Register browser cleanup information for a test
   * @param testId Test ID
   * @param cleanupInfo Browser cleanup information
   */
  registerBrowserCleanup(testId: string, cleanupInfo: BrowserCleanupInfo): void;

  /**
   * Register process cleanup information for a test
   * @param testId Test ID
   * @param cleanupInfo Process cleanup information
   */
  registerProcessCleanup(testId: string, cleanupInfo: ProcessCleanupInfo): void;

  /**
   * Add a resource to track for a test
   * @param testId Test ID
   * @param resourceId Resource identifier
   * @param resourceType Type of resource
   */
  addResource(testId: string, resourceId: string, resourceType: ResourceType): void;

  /**
   * Remove a resource from tracking
   * @param testId Test ID
   * @param resourceId Resource identifier
   */
  removeResource(testId: string, resourceId: string): void;

  /**
   * Cleanup all resources for a specific test
   * @param options Cleanup options
   * @returns Promise resolving to cleanup result
   */
  cleanupTestResources(options: ResourceCleanupOptions): Promise<ResourceCleanupResult>;

  /**
   * Cleanup specific resource types for a test
   * @param testId Test ID
   * @param resourceTypes Types of resources to cleanup
   * @param options Additional cleanup options
   * @returns Promise resolving to cleanup result
   */
  cleanupResourceTypes(
    testId: string,
    resourceTypes: ResourceType[],
    options?: Partial<ResourceCleanupOptions>
  ): Promise<ResourceCleanupResult>;

  /**
   * Emergency cleanup of all resources
   * @param reason Reason for emergency cleanup
   * @returns Promise that resolves when cleanup is complete
   */
  emergencyCleanupAll(reason?: string): Promise<void>;

  /**
   * Get resource statistics
   * @returns Current resource statistics
   */
  getResourceStatistics(): ResourceStatistics;

  /**
   * Get resources for a specific test
   * @param testId Test ID
   * @returns Array of resource identifiers
   */
  getResourcesForTest(testId: string): string[];

  /**
   * Check if a test has any active resources
   * @param testId Test ID
   * @returns True if test has active resources, false otherwise
   */
  hasActiveResources(testId: string): boolean;

  /**
   * Wait for pending operations to complete
   * @param testId Test ID
   * @param timeout Maximum time to wait in milliseconds
   * @returns Promise that resolves when operations are complete
   */
  waitForPendingOperations(testId: string, timeout?: number): Promise<void>;

  /**
   * Dispose of the resource manager
   * @returns Promise that resolves when disposal is complete
   */
  dispose(): Promise<void>;
}
