/**
 * Scenario API Routes
 * MongoDB üzerinden senaryo işlemleri için API rotaları
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import {
  getAllScenarios,
  getScenarioById,
  createScenario,
  updateScenario,
  deleteScenario,
  getUserScenarios,
  createFolder,
  getUserFolders,
  renameFolder,
  deleteFolder,
  duplicateScenario,
  bulkDeleteScenarios,
  bulkDuplicateScenarios,
  getScenariosWithFolders
} from '../../services/mongo/scenarioService.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { checkPermission } from '../middleware/permissionMiddleware.js';
import { createObjectCsvStringifier } from 'csv-writer';
import { getCompanyById } from '../../services/mongo/companyService.js';
import { createPdfFromScenarios } from '../../utils/pdfExporter.js';

const router = Router();

/**
 * GET /api/scenarios
 * Tüm senaryoları listeler, limit ve tag filtresi destekler
 */
router.get('/', authenticate, async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit?.toString() || '100');
    const tag = req.query.tag?.toString();
    const authReq = req as AuthRequest;
    // Use authenticated user ID if no userId query param is provided
    const userId = req.query.userId?.toString() || authReq.user?.id;
    const status = req.query.status?.toString() as 'active' | 'inactive' | 'archived' | undefined;

    // Ensure we have a userId
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // MongoDB servisinden tüm senaryoları çek (steps hariç)
    const result = await getAllScenarios({
      limit,
      userId,
      status,
      tags: tag ? [tag] : undefined,
      excludeSteps: true // Performans için steps alanını hariç tut
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        count: result.count || 0,
        total: result.count,
        scenarios: result.scenarios
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /scenarios: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /scenarios/basic
 * Fetch user's scenarios without steps field
 */
router.get('/basic', authenticate, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const limit = parseInt(req.query.limit?.toString() || '100');
    const tag = req.query.tag?.toString();
    const status = req.query.status?.toString() as 'active' | 'inactive' | 'archived' | undefined;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // MongoDB servisinden kullanıcının senaryolarının özet bilgilerini çek (steps hariç)
    const result = await getUserScenarios(userId, {
      limit,
      status,
      tags: tag ? [tag] : undefined,
      excludeSteps: true // steps alanını hariç tutmak için
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        count: result.count || 0,
        total: result.count,
        scenarios: result.scenarios
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /scenarios/basic: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /scenarios/folders
 * Kullanıcının klasörlerini listeler
 */
router.get('/folders', authenticate, async (req: Request, res: Response) => {
  try {
    // req.user middleware tarafından eklendi
    const authReq = req as AuthRequest;
    const user = authReq.user;
    const limit = parseInt(req.query.limit?.toString() || '50');
    const skip = parseInt(req.query.skip?.toString() || '0');



    if (!user?.id) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    const teamId = user.teamId;
    const companyId = user.companyId;

    // Kullanıcının klasörlerini getir
    const result = await getUserFolders(user.id, { limit, skip, teamId, companyId });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        count: result.count || 0,
        total: result.count,
        folders: result.folders
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /scenarios/folders: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /scenarios/folders
 * Kullanıcı için yeni klasör oluşturur
 */
router.post('/folders', authenticate, checkPermission('Folder', 'create'), async (req: Request, res: Response) => {
  try {
    // req.user middleware tarafından eklendi
    const user = req.user;
    const { name, color, description } = req.body;



    if (!user?.id) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    if (!name || typeof name !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Folder name is required'
      });
    }

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    const teamId = user.teamId;
    const companyId = user.companyId;

    // Klasör oluşturma servisini çağır
    const result = await createFolder({
      name,
      userId: user.id,
      color,
      description,
      teamId,
      companyId
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.status(201).json({
      success: true,
      data: {
        message: result.message,
        folderId: result.folderId,
        folder: result.folder
      }
    });
  } catch (error: any) {
    logger.error(`API Error in POST /scenarios/folders: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /scenarios/my-scenarios
 * Kullanıcının takımına ait senaryoları getiren endpoint
 */
router.get('/my-scenarios', authenticate, async (req: Request, res: Response) => {
  try {
    // req.user artık middleware tarafından eklendi
    const user = req.user;
    const limit = parseInt(req.query.limit?.toString() || '100');
    const tag = req.query.tag?.toString();



    if (!user?.id) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    if (!user.teamId || !user.companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required to fetch scenarios'
      });
    }



    // Takımın senaryolarını getir
    const result = await getUserScenarios(user.id, {
      limit,
      tags: tag ? [tag] : undefined,
      excludeSteps: true, // Performans için
      teamId: user.teamId,
      companyId: user.companyId
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    res.json({
      success: true,
      data: {
        count: result.count || 0,
        total: result.count,
        scenarios: result.scenarios
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /scenarios/my-scenarios: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /scenarios/aggregated
 * Senaryoları ve klasörleri birlikte getiren endpoint
 * CompanyId ve TeamId'ye göre filtreleme yapar
 */
router.get('/aggregated', authenticate, async (req: Request, res: Response) => {
  try {
    const user = req.user;
    const limit = parseInt(req.query.limit?.toString() || '1000');
    const skip = parseInt(req.query.skip?.toString() || '0');
    const status = req.query.status?.toString();
    const search = req.query.search?.toString();
    const tag = req.query.tag?.toString();
    const testType = req.query.testType?.toString();
    const startDate = req.query.startDate?.toString();
    const endDate = req.query.endDate?.toString();
    const testManagementStatus = req.query.testManagementStatus?.toString();





    if (!user?.id) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    if (!user.teamId || !user.companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required to fetch scenarios and folders'
      });
    }



    // Senaryoları ve klasörleri birlikte getir
    const result = await getScenariosWithFolders({
      userId: user.id,
      teamId: user.teamId,
      companyId: user.companyId,
      limit,
      skip,
      status,
      search,
      tag,
      testType,
      startDate,
      endDate,
      testManagementStatus,
      excludeSteps: true // Performans için
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: result.data
    });
  } catch (error: any) {
    logger.error(`API Error in GET /scenarios/aggregated: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/scenarios/:scenarioId
 * ID'ye göre tek bir senaryoyu getirir
 */
router.get('/:scenarioId', async (req: Request, res: Response) => {
  try {
    const { scenarioId } = req.params;



    // MongoDB servisinden senaryoyu çek
    const result = await getScenarioById(scenarioId);

    // Senaryo bulunamadıysa 404 hatası döndür
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Senaryo bulunamadı: ${scenarioId}`
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        scenario: result.scenario
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /scenarios/${req.params.scenarioId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/scenarios
 * Yeni bir senaryo oluşturur
 */
router.post('/', authenticate, checkPermission('Scenario', 'create'), async (req: Request, res: Response) => {
  try {
    const scenarioData = req.body;
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;



    // Kullanıcı kimliği kontrolü
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Gerekli alanları kontrol et
    if (!scenarioData || !scenarioData.name) {
      return res.status(400).json({
        success: false,
        error: 'Geçerli bir senaryo ve isim zorunludur'
      });
    }

    // Kullanıcı ID'sini senaryo verisine ekle
    scenarioData.userId = userId;

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    if (!req.user?.teamId || !req.user?.companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required to create a scenario'
      });
    }

    // Takım ve şirket bilgilerini senaryo verisine ekle
    scenarioData.teamId = req.user.teamId;
    scenarioData.companyId = req.user.companyId;



    // MongoDB servisinden senaryo oluştur
    const result = await createScenario(scenarioData);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.status(201).json({
      success: true,
      data: {
        scenarioId: result.scenarioId,
        message: 'Senaryo başarıyla oluşturuldu'
      }
    });
  } catch (error: any) {
    logger.error(`API Error in POST /scenarios: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * PUT /api/scenarios/:scenarioId
 * Bir senaryoyu günceller
 */
router.put('/:scenarioId', authenticate, checkPermission('Scenario', 'update'), async (req: Request, res: Response) => {
  try {
    const { scenarioId } = req.params;
    const updateData = req.body;



    // Gerekli alanları kontrol et
    if (!updateData) {
      return res.status(400).json({
        success: false,
        error: 'Güncelleme için veri gönderilmedi'
      });
    }

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    if (!req.user?.teamId || !req.user?.companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required to update a scenario'
      });
    }

    // Takım ve şirket bilgilerini güncelleme verisine ekle
    updateData.teamId = req.user.teamId;
    updateData.companyId = req.user.companyId;

    // MongoDB servisinden senaryoyu güncelle
    const result = await updateScenario(scenarioId, updateData);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Senaryo bulunamadı veya güncellenemedi: ${scenarioId}`
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        message: 'Senaryo başarıyla güncellendi'
      }
    });
  } catch (error: any) {
    logger.error(`API Error in PUT /scenarios/${req.params.scenarioId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/scenarios/:scenarioId
 * Bir senaryoyu siler
 */
router.delete('/:scenarioId', authenticate, checkPermission('Scenario', 'delete'), async (req: Request, res: Response) => {
  try {
    const { scenarioId } = req.params;

    // MongoDB servisinden senaryoyu sil
    const result = await deleteScenario(scenarioId);

    // Silmenin başarılı olup olmadığını kontrol et
    if (!result.success) {
      // Check if the scenario is in use by active runs
      if (result.inUseByRuns && result.inUseByRuns.length > 0) {
        return res.status(409).json({
          success: false,
          error: result.message || `Senaryo aktif run'larda kullanıldığı için silinemedi: ${scenarioId}`,
          inUseByRuns: result.inUseByRuns
        });
      }

      // Other error cases
      return res.status(404).json({
        success: false,
        error: result.message || `Senaryo bulunamadı veya silinemedi: ${scenarioId}`
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        message: `Senaryo başarıyla silindi: ${scenarioId}`
      }
    });
  } catch (error: any) {
    logger.error(`API Error in DELETE /scenarios/${req.params.scenarioId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * PUT /scenarios/folders/:folderId
 * Kullanıcının klasörünü günceller (adını değiştirir)
 */
router.put('/folders/:folderId', authenticate, checkPermission('Folder', 'edit'), async (req: Request, res: Response) => {
  try {
    const { folderId } = req.params;
    const { name } = req.body;
    const user = req.user;

    if (!user?.id) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    if (!name || typeof name !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Folder name is required'
      });
    }

    // Klasör güncelleme servisini çağır
    const result = await renameFolder(folderId, name, user.id);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        message: result.message,
        folder: result.folder
      }
    });
  } catch (error: any) {
    logger.error(`API Error in PUT /scenarios/folders/${req.params.folderId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /scenarios/folders/:folderId
 * Kullanıcının klasörünü siler
 */
router.delete('/folders/:folderId', authenticate as any, checkPermission('Folder', 'delete'), async (req: Request, res: Response) => {
  try {
    const { folderId } = req.params;
    const user = req.user;

    if (!user?.id) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: User ID is missing'
      });
    }

    // Klasör silme servisini çağır
    const result = await deleteFolder(folderId, user.id);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.message
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        message: result.message
      }
    });
  } catch (error: any) {
    logger.error(`API Error in DELETE /scenarios/folders/${req.params.folderId}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/scenarios/:scenarioId/duplicate
 * Bir senaryonun kopyasını oluşturur
 */
router.post('/:scenarioId/duplicate', authenticate, checkPermission('Scenario', 'create'), async (req: Request, res: Response) => {
  try {
    const { scenarioId } = req.params;
    const { namePrefix, folderId, newName } = req.body;

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    if (!req.user?.teamId || !req.user?.companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required to duplicate a scenario'
      });
    }

    // Duplication options
    const duplicateOptions = {
      namePrefix: namePrefix || '',
      folderId: folderId || null,
      newName: newName || null
    };

    // MongoDB servisinden senaryo kopyalama işlemi
    const result = await duplicateScenario(scenarioId, req.user.teamId, req.user.companyId, duplicateOptions);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || `Senaryo bulunamadı veya kopyalanamadı: ${scenarioId}`
      });
    }

    // Başarılı yanıt - sadece gerekli bilgileri döndür
    res.status(201).json({
      success: true,
      newScenarioId: result.newScenarioId,
      message: 'Senaryo başarıyla kopyalandı'
    });
  } catch (error: any) {
    logger.error(`API Error in POST /scenarios/${req.params.scenarioId}/duplicate: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/scenarios/bulk-delete
 * Birden fazla senaryoyu toplu olarak siler
 */
router.post('/bulk-delete', authenticate, checkPermission('Scenario', 'delete'), async (req: Request, res: Response) => {
  try {
    const { scenarioIds } = req.body;

    if (!scenarioIds || !Array.isArray(scenarioIds) || scenarioIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Scenario IDs array is required'
      });
    }

    // MongoDB servisinden senaryoları toplu sil
    const result = await bulkDeleteScenarios(scenarioIds);

    // Check if some scenarios are in use by active runs
    if (result.inUseScenarios && result.inUseScenarios.length > 0) {
      // If some scenarios were deleted but others were skipped
      if (result.success && result.deletedCount && result.deletedCount > 0) {
        return res.status(207).json({
          success: true,
          message: result.message,
          deletedCount: result.deletedCount,
          inUseScenarios: result.inUseScenarios
        });
      }
      // If no scenarios were deleted because all are in use
      else {
        return res.status(409).json({
          success: false,
          error: result.message || 'All selected scenarios are in use by active runs',
          inUseScenarios: result.inUseScenarios
        });
      }
    }

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to delete scenarios'
      });
    }

    // Başarılı yanıt - data wrapper ile tutarlılık için
    res.json({
      success: true,
      data: {
        message: result.message || `${result.deletedCount || 0} scenarios deleted successfully`,
        deletedCount: result.deletedCount || 0
      }
    });
  } catch (error: any) {
    logger.error(`API Error in POST /scenarios/bulk-delete: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/scenarios/bulk-duplicate
 * Birden fazla senaryoyu toplu olarak kopyalar
 */
router.post('/bulk-duplicate', authenticate, checkPermission('Scenario', 'create'), async (req: Request, res: Response) => {
  try {
    const { scenarioIds, namePrefix, folderId, customNamePattern, addNumbers } = req.body;

    if (!scenarioIds || !Array.isArray(scenarioIds) || scenarioIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Scenario IDs array is required'
      });
    }

    // Kullanıcının takım ve şirket bilgilerini kontrol et
    if (!req.user?.teamId || !req.user?.companyId) {
      return res.status(400).json({
        success: false,
        error: 'Team ID and Company ID are required to duplicate scenarios'
      });
    }

    // Duplication options
    const duplicateOptions = {
      namePrefix: namePrefix || 'Copy',
      folderId: folderId || null,
      customNamePattern: customNamePattern || null,
      addNumbers: addNumbers || false
    };

    // MongoDB servisinden senaryoları toplu kopyala
    const result = await bulkDuplicateScenarios(scenarioIds, req.user.teamId, req.user.companyId, duplicateOptions);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to duplicate scenarios'
      });
    }

    // Başarılı yanıt
    res.status(201).json({
      success: true,
      message: result.message || `${result.duplicatedCount || 0} scenarios duplicated successfully`,
      duplicatedCount: result.duplicatedCount || 0,
      duplicatedScenarios: result.duplicatedScenarios || []
    });
  } catch (error: any) {
    logger.error(`API Error in POST /scenarios/bulk-duplicate: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/scenarios/export
 * Export scenarios as CSV
 * @access Private
 */
router.post('/export', authenticate, async (req: Request, res: Response) => {
  try {
    const {
      format = 'csv',
      folderId = 'all',
      status = 'all',
      fileName = 'scenarios-export',
      selectedScenarioIds = null,
      includeFields = {
        steps: true,
        description: true,
        tags: true,
        status: true,
        lastRun: true,
        duration: true
      },
      teamId,
      companyId
    } = req.body;

    // Get user ID from token
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const userTeamId = authReq.user?.teamId;
    const userCompanyId = authReq.user?.companyId;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }

    // Use provided teamId/companyId or fall back to user's values
    const effectiveTeamId = teamId || userTeamId;
    const effectiveCompanyId = companyId || userCompanyId;

    // Kullanıcı ID'si yeterli, şirket ID'sine gerek yok
    // Doğrudan filtreleme yapabiliriz

    // Get scenarios based on filters or selected IDs
    let result;

    if (selectedScenarioIds && Array.isArray(selectedScenarioIds) && selectedScenarioIds.length > 0) {
      // Export only selected scenarios
      result = await getUserScenarios(userId, {
        teamId: effectiveTeamId,
        companyId: effectiveCompanyId,
        scenarioIds: selectedScenarioIds, // Pass selected IDs
        limit: 1000
      });
    } else {
      // Export based on folder/status filters (existing behavior)
      result = await getUserScenarios(userId, {
        teamId: effectiveTeamId,
        companyId: effectiveCompanyId,
        status: status !== 'all' ? status : undefined,
        folderId: folderId !== 'all' ? folderId : undefined,
        limit: 1000 // Yüksek limit ile tüm senaryoları al
      });
    }
    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to get scenarios'
      });
    }

    const scenarios = result.scenarios || [];

    // Get folders for mapping folder IDs to names
    const foldersResult = await getUserFolders(userId);
    const folders = foldersResult.success ? foldersResult.folders || [] : [];
    const folderMap = new Map(folders.map(folder => [folder.id, folder.name]));

    // Format scenarios for export
    const formattedScenarios = scenarios.map(scenario => {
      const result: any = {
        name: scenario.name
      };

      // Add optional fields based on includeFields
      if (includeFields.description) {
        result.description = scenario.description || '';
      }

      if (includeFields.tags) {
        result.tags = Array.isArray(scenario.tags) ? scenario.tags.join(';') : '';
      }

      if (includeFields.status) {
        result.status = scenario.status || 'unknown';
      }

      // Removed lastRun and duration fields

      // Export steps as JSON string
      if (includeFields.steps && Array.isArray(scenario.steps)) {
        try {
          result.steps = JSON.stringify(scenario.steps);
        } catch (e) {
          result.steps = JSON.stringify([]);
          logger.error(`Error stringifying steps for scenario ${scenario.id}:`, e);
        }
      } else {
        result.steps = JSON.stringify([]);
      }

      // Add only created date
      result.createdAt = scenario.createdAt
        ? new Date(scenario.createdAt).toISOString()
        : new Date().toISOString();

      return result;
    });

    // Handle different export formats
    if (format === 'csv') {
      // Create CSV header based on included fields
      const header = [
        { id: 'name', title: 'Name' }
      ];

      if (includeFields.description) header.push({ id: 'description', title: 'Description' });
      if (includeFields.tags) header.push({ id: 'tags', title: 'Tags' });
      if (includeFields.status) header.push({ id: 'status', title: 'Status' });
      if (includeFields.steps) header.push({ id: 'steps', title: 'Steps' });

      // Create CSV stringifier
      const csvStringifier = createObjectCsvStringifier({
        header,
        fieldDelimiter: ','
      });

      // Generate CSV content
      const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(formattedScenarios);

      // Set response headers for CSV download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}.csv"`);

      // Send CSV content
      return res.send(csvContent);
    }
    // PDF formatı için
    else if (format === 'pdf') {
      try {
        // PDF oluştur
        const pdfBuffer = await createPdfFromScenarios(formattedScenarios, includeFields);

        // Set response headers for PDF download
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}.pdf"`);

        // Send PDF content
        return res.send(pdfBuffer);
      } catch (pdfError) {
        logger.error('Error generating PDF:', pdfError);
        return res.status(500).json({ success: false, error: 'Failed to generate PDF' });
      }
    }
    // Diğer formatlar için JSON döndür
    else {
      return res.json({
        success: true,
        data: formattedScenarios
      });
    }
  } catch (error: any) {
    logger.error('Error exporting scenarios:', error);
    return res.status(500).json({ success: false, error: 'Failed to export scenarios' });
  }
});

/**
 * POST /api/scenarios/import
 * Import scenarios from CSV
 * @access Private
 */
router.post('/import', authenticate, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const teamId = authReq.user?.teamId;
    const companyId = authReq.user?.companyId;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }

    if (!teamId || !companyId) {
      return res.status(400).json({ success: false, error: 'Team ID and Company ID are required' });
    }

    // Get the CSV data from the request
    const { csvData, folderId, teamId: requestTeamId, companyId: requestCompanyId, selectedScenarioIndices } = req.body;

    // Use provided teamId/companyId or fall back to user's values
    const effectiveTeamId = requestTeamId || teamId;
    const effectiveCompanyId = requestCompanyId || companyId;

    if (!csvData) {
      return res.status(400).json({ success: false, error: 'CSV data is required' });
    }

    // If no folderId provided, use default "uncategorized" folder
    let targetFolderId = folderId;
    if (!targetFolderId) {
      targetFolderId = 'uncategorized'; // Default folder for imports
    }

    // CSV verilerini daha güvenilir bir şekilde parse etmek için
    // Önce CSV'yi temizleyelim
    let cleanCsvData = csvData.trim();

    // Satır sonlarını normalize et
    cleanCsvData = cleanCsvData.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // Satırlara böl
    let rows: string[] = cleanCsvData.split('\n');

    // Boş satırları temizle
    rows = rows.filter((row: string) => row.trim().length > 0);

    if (rows.length === 0) {
      return res.status(400).json({ success: false, error: 'CSV file is empty' });
    }

    // Başlık satırını parse et
    const headerRow = rows[0];
    const headers = headerRow.split(',').map(h => h.trim());

    // Validate headers - case insensitive kontrol
    const requiredHeaders = ['name', 'steps'];
    for (const requiredHeader of requiredHeaders) {
      const headerExists = headers.some(h => h.toLowerCase() === requiredHeader.toLowerCase());
      if (!headerExists) {
        return res.status(400).json({
          success: false,
          error: `CSV must include ${requiredHeaders.join(', ')} columns`
        });
      }
    }

    // Başlıkların indekslerini bul
    const headerIndices: Record<string, number> = {};
    headers.forEach((header, index) => {
      headerIndices[header.toLowerCase()] = index;
    });

    // Exclude system fields that should not be imported
    const excludedFields = ['createdAt', 'updatedAt', 'id', '_id', 'userId', 'teamId', 'companyId', 'Created At'];

    // Parse scenarios from CSV
    const allScenarios: any[] = [];
    for (let i = 1; i < rows.length; i++) {
      const row = rows[i];
      if (!row.trim()) continue; // Skip empty rows

      // CSV satırını daha güvenilir bir şekilde parse et
      // Bu basit bir CSV parser, tırnak içindeki virgülleri dikkate almaz
      // Daha karmaşık CSV'ler için tam bir CSV parser kullanılmalıdır
      const values: string[] = [];
      let currentValue = '';
      let inQuotes = false;

      for (let j = 0; j < row.length; j++) {
        const char = row[j];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(currentValue.trim());
          currentValue = '';
        } else {
          currentValue += char;
        }
      }

      // Son değeri ekle
      values.push(currentValue.trim());

      const scenario: any = {};

      // Map CSV columns to scenario properties
      headers.forEach((header, index) => {
        const trimmedHeader = header.trim();
        const lowerHeader = trimmedHeader.toLowerCase();

        // Skip excluded fields
        if (excludedFields.includes(trimmedHeader) || excludedFields.includes(lowerHeader)) {
          return;
        }

        if (values[index]) {
          // Eğer başlık name, description veya steps ise, lowercase kontrolü yap
          if (lowerHeader === 'name') {
            scenario.name = values[index].trim();
          } else if (lowerHeader === 'description') {
            scenario.description = values[index].trim();
          } else if (lowerHeader === 'steps') {
            scenario.steps = values[index].trim();
          } else if (lowerHeader === 'status') {
            scenario.status = values[index].trim();
          } else if (lowerHeader === 'tags') {
            scenario.tags = values[index].trim();
          } else {
            scenario[trimmedHeader] = values[index].trim();
          }
        }
      });

      // Validate required fields
      if (!scenario.name || !scenario.steps) {
        continue; // Skip this scenario if required fields are missing
      }

      // Add required fields
      scenario.userId = userId;
      scenario.teamId = effectiveTeamId;
      scenario.companyId = effectiveCompanyId;
      scenario.folderId = targetFolderId;

      // Status alanı yoksa veya boşsa 'active' olarak ayarla
      if (!scenario.status || scenario.status.trim() === '') {
        scenario.status = 'active';
      }



      // JSON parse işlemini try-catch içinde yap
      try {
        // Steps alanını JSON olarak parse et
        if (scenario.steps) {
          // Bazen JSON string'in başında veya sonunda fazladan karakterler olabilir
          // Bu karakterleri temizleyelim
          let stepsStr = scenario.steps.trim();

          // Eğer string '[' ile başlamıyorsa, ilk '[' karakterini bul
          if (!stepsStr.startsWith('[')) {
            const startIndex = stepsStr.indexOf('[');
            if (startIndex !== -1) {
              stepsStr = stepsStr.substring(startIndex);
            }
          }

          // Eğer string ']' ile bitmiyorsa, son ']' karakterini bul
          if (!stepsStr.endsWith(']')) {
            const endIndex = stepsStr.lastIndexOf(']');
            if (endIndex !== -1) {
              stepsStr = stepsStr.substring(0, endIndex + 1);
            }
          }

          scenario.steps = JSON.parse(stepsStr);
        } else {
          scenario.steps = [];
        }
      } catch (error: any) {

        logger.error(`Error parsing steps JSON for scenario ${scenario.name}: ${error.message}`);
        // Hata durumunda boş bir dizi kullan
        scenario.steps = [];
      }

      // Tags alanını parse et
      scenario.tags = scenario.tags ? scenario.tags.split(';').map((tag: string) => tag.trim()) : [];

      allScenarios.push(scenario);
    }

    // Filter scenarios based on selectedScenarioIndices if provided
    const scenarios = selectedScenarioIndices && Array.isArray(selectedScenarioIndices)
      ? selectedScenarioIndices.map(index => allScenarios[index]).filter(Boolean)
      : allScenarios;

    // Validate scenarios
    if (scenarios.length === 0) {
      return res.status(400).json({ success: false, error: 'No valid scenarios found in CSV' });
    }

    // Create scenarios
    const createdScenarios = [];
    for (const scenario of scenarios) {
      const result = await createScenario(scenario);
      if (result.success && result.scenarioId) {
        // Orijinal senaryo verilerini ID ile birleştir
        createdScenarios.push({
          ...scenario,
          id: result.scenarioId
        });
      }
    }

    return res.json({
      success: true,
      data: {
        message: `${createdScenarios.length} scenarios imported successfully`,
        scenarios: createdScenarios
      }
    });
  } catch (error: any) {
    logger.error(`Error importing scenarios: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Error importing scenarios: ${error.message}`
    });
  }
});

export default router;
