"use client"

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Laptop, Smartphone } from "lucide-react"
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"
import { PlatformType } from "@/models/scenario"

export function PlatformSelection() {
  const { formData, updateFormField } = useUnifiedScenarioContext()

  return (
    <div className="space-y-4">
      <Label className="text-base mb-3 block">Platform</Label>
      <RadioGroup
        value={formData.platform || "web"}
        onValueChange={(value) => updateFormField("platform", value as PlatformType)}
        className="grid grid-cols-2 gap-4"
      >
        <div>
          <RadioGroupItem value="web" id="platform-web" className="peer sr-only" />
          <Label
            htmlFor="platform-web"
            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-blue-500 peer-data-[state=checked]:bg-blue-50 [&:has([data-state=checked])]:border-blue-500 [&:has([data-state=checked])]:bg-blue-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-blue-950/20 dark:[&:has([data-state=checked])]:bg-blue-950/20"
          >
            <Laptop className="mb-2 h-6 w-6 text-blue-500" />
            <div className="text-center">
              <p className="text-sm font-medium">Web</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Browser-based testing</p>
            </div>
          </Label>
        </div>

        <div>
          <RadioGroupItem value="android" id="platform-android" className="peer sr-only" />
          <Label
            htmlFor="platform-android"
            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-green-500 peer-data-[state=checked]:bg-green-50 [&:has([data-state=checked])]:border-green-500 [&:has([data-state=checked])]:bg-green-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-green-950/20 dark:[&:has([data-state=checked])]:bg-green-950/20"
          >
            <Smartphone className="mb-2 h-6 w-6 text-green-500" />
            <div className="text-center">
              <p className="text-sm font-medium">Android</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Mobile app testing</p>
            </div>
          </Label>
        </div>
      </RadioGroup>
    </div>
  )
}
