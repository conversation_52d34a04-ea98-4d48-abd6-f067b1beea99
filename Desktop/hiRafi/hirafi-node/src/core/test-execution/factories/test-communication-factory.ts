/**
 * Test Communication Factory
 * 
 * Provides factory methods for creating test communication service instances
 * with proper dependency injection and configuration.
 */

import { ILoggerService, LoggerServiceFactory } from '../../../utils/logger-service.js';
import type {
  ITestCommunicationService,
  TestCommunicationConfig
} from '../interfaces/test-communication.interface.js';

/**
 * Test Communication Factory Configuration
 */
export interface TestCommunicationFactoryConfig {
  /** Enable detailed logging */
  enableLogging?: boolean;
  /** Default timeout for operations */
  defaultTimeout?: number;
  /** Maximum retry attempts */
  maxRetries?: number;
  /** Retry delay in milliseconds */
  retryDelay?: number;
}

/**
 * Test Communication Factory
 * 
 * Creates and configures test communication service instances following
 * dependency injection patterns and providing backward compatibility.
 */
export class TestCommunicationFactory {
  /**
   * Create a test communication service instance
   * @param config Factory configuration
   * @returns Promise resolving to configured test communication service
   */
  static async createTestCommunicationService(
    config: TestCommunicationFactoryConfig = {}
  ): Promise<ITestCommunicationService> {
    const logger = LoggerServiceFactory.createServiceLogger('TestCommunicationFactory');
    try {
      // Import the service implementation
      const { TestCommunicationService } = await import('../services/communication/test-communication-service.js');
      
      // Create service instance
      const service = new TestCommunicationService();
      
      // Prepare service configuration
      const serviceConfig: TestCommunicationConfig = {
        enableLogging: config.enableLogging || false,
        defaultTimeout: config.defaultTimeout || 30000,
        maxRetries: config.maxRetries || 3,
        retryDelay: config.retryDelay || 1000
      };
      
      // Initialize the service
      await service.initialize(serviceConfig);
      
      if (config.enableLogging) {
        logger.info('TestCommunicationFactory: Created and initialized test communication service');
      }
      
      return service;
    } catch (error: any) {
      logger.error(`TestCommunicationFactory: Failed to create test communication service: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a test communication service from service registry
   * @param serviceRegistry Service registry instance
   * @returns Promise resolving to test communication service
   */
  static async createFromServiceRegistry(serviceRegistry: any): Promise<ITestCommunicationService> {
    const logger = LoggerServiceFactory.createServiceLogger('TestCommunicationFactory');
    try {
      if (!serviceRegistry || typeof serviceRegistry.getTestCommunicationService !== 'function') {
        throw new Error('Invalid service registry provided');
      }

      const service = await serviceRegistry.getTestCommunicationService();
      
      if (!service) {
        throw new Error('Test communication service not found in registry');
      }

      logger.debug('TestCommunicationFactory: Retrieved test communication service from registry');
      return service;
    } catch (error: any) {
      logger.error(`TestCommunicationFactory: Failed to get service from registry: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a test communication service instance (DEPRECATED - Use DI instead)
   * @deprecated Use dependency injection through ServiceRegistry instead
   * @param config Factory configuration
   * @returns Promise resolving to test communication service
   */
  static async createCompatibilityInstance(
    config: TestCommunicationFactoryConfig = {}
  ): Promise<ITestCommunicationService> {
    // DEPRECATED: This method is kept for backward compatibility only
    // New code should use ServiceRegistry and proper dependency injection
    const logger = LoggerServiceFactory.createServiceLogger('TestCommunicationFactory');
    logger.warn('TestCommunicationFactory.createCompatibilityInstance is deprecated. Use ServiceRegistry instead.');
    return TestCommunicationFactory.createTestCommunicationService(config);
  }
}
