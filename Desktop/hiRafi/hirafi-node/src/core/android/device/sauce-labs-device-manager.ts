/**
 * Sauce Labs Device Manager
 * Manages Android devices using Sauce Labs
 */

import {
  AndroidAgent,
  AppiumBaseCapabilities,
  SauceLabsConfig,
  SauceLabsCapabilities
} from './device-manager.interface.js';
import { BaseDeviceManager } from './base-device-manager.js';
import { TestRequest } from '../../../models/types.js';
import { ILoggerService } from '../../../utils/logger-service.js';
import { ConnectorFactory } from '../../connectors/factories/connector-factory.js';
import { DeviceManagerUtils } from './shared/device-manager-utils.js';



// Import from misoai-android package
import * as androidModule from 'rfi-ai-android';

// Extract the agentFromSauceLabs function
const agentFromSauceLabs = androidModule.agentFromSauceLabs;

/**
 * Sauce Labs Device Manager
 * Manages Android devices using Sauce Labs with DI compliance
 */
export class SauceLabsDeviceManager extends BaseDeviceManager {
  private sauceConfig: SauceLabsConfig;
  private capabilities: AppiumBaseCapabilities & SauceLabsCapabilities;
  private logger: ILoggerService;
  private utils: DeviceManagerUtils;

  /**
   * Create a new SauceLabsDeviceManager
   * @param sauceConfig Sauce Labs configuration
   * @param capabilities Appium and Sauce Labs capabilities
   * @param logger Logger service
   */
  constructor(
    sauceConfig: SauceLabsConfig,
    capabilities: AppiumBaseCapabilities & SauceLabsCapabilities,
    logger: ILoggerService
  ) {
    super();
    this.sauceConfig = sauceConfig;
    this.capabilities = capabilities;
    this.logger = logger;
    this.utils = new DeviceManagerUtils(logger);

    // Check if the function exists
    if (!agentFromSauceLabs) {
      this.logger.warn('agentFromSauceLabs not found in misoai-android module');
    }
  }

  /**
   * Initialize the device manager
   * @param config Configuration object
   */
  async initialize(config: any): Promise<void> {
    await super.initialize(config);

    // Update configuration if provided
    if (config?.sauceLabs) {
      // Handle both naming conventions for credentials
      const updatedConfig = {
        ...config.sauceLabs
      };

      // Map username to user and accessKey to key if needed
      if (config.sauceLabs.username && !config.sauceLabs.user) {
        updatedConfig.user = config.sauceLabs.username;
      }

      if (config.sauceLabs.accessKey && !config.sauceLabs.key) {
        updatedConfig.key = config.sauceLabs.accessKey;
      }

      this.sauceConfig = {
        ...this.sauceConfig,
        ...updatedConfig
      };
    }

    if (config?.capabilities) {
      this.capabilities = {
        ...this.capabilities,
        ...config.capabilities
      };
    }
  }

  /**
   * Check if a device is available
   * @param deviceId Device ID
   * @returns Promise resolving to true if device is available, false otherwise
   */
  async checkDeviceAvailability(deviceId: string): Promise<boolean> {
    try {
      const { user, key, region } = this.sauceConfig;

      if (!user || !key) {
        throw new Error('SauceLabsCredentialsMissing');
      }

      // Construct the SauceLabs API URL based on region
      const validRegion = (region === "eu-central-1" || region === "us-east-1" || region === "us-west-1") ?
        (region as "eu-central-1" | "us-east-1" | "us-west-1") :
        "us-west-1";
      const baseUrl = `https://api.${validRegion}.saucelabs.com`;

      // Make a request to the SauceLabs API to get device status
      const response = await fetch(`${baseUrl}/v1/rdc/devices/status`, {
        headers: {
          'Authorization': 'Basic ' + Buffer.from(`${user}:${key}`).toString('base64')
        }
      });

      if (!response.ok) {
        throw new Error('SauceLabsApiTemporaryError');
      }

      const responseData = await response.json();

      if (!responseData.devices || !Array.isArray(responseData.devices)) {
        throw new Error('SauceLabsApiInvalidResponse');
      }

      // Find the device with the matching descriptor
      const device = responseData.devices.find((d: any) => d.descriptor === deviceId);

      if (!device) {
        throw new Error(`SauceLabsDeviceNotFound: ${deviceId}`);
      }

      // Check if device state is "AVAILABLE"
      const isAvailable = device.state === "AVAILABLE";

      if (!isAvailable) {
        throw new Error('SauceLabsDeviceUnavailable');
      }

      return true;
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Connect to a device
   * @param deviceId Optional device identifier
   * @param scenarioId Optional scenario identifier for reporting (should be scenarioId, not testId)
   * @param platformVersion Optional platform version to override default
   * @param appId Optional app ID for SauceLabs app storage
   * @returns Promise resolving to a device agent
   */
  async connectToDevice(deviceId?: string, scenarioId?: string, platformVersion?: string, appId?: string): Promise<AndroidAgent> {
    try {
      // Check device availability if we have a device ID and scenario ID
      if (deviceId && scenarioId) {
        try {
          await this.checkDeviceAvailability(deviceId);
        } catch (availabilityError: any) {
          // Report device unavailability to Test Hub (still using testId for reporting compatibility)
          await this.reportDeviceUnavailability(scenarioId, deviceId);
          throw availabilityError;
        }
      }

      // Prepare capabilities with W3C protocol compliance for Appium 2
      const caps = {
        ...this.capabilities,
        platformName: "Android" as const,
        // Ensure automationName is set for Appium 2 (required)
        'appium:automationName': this.capabilities['appium:automationName'] || 'UiAutomator2',
        // Auto-accept alerts and permissions for Android
        'appium:autoAcceptAlerts': true,
        'appium:autoGrantPermissions': true
      };

      if (deviceId) {
        caps['appium:deviceName'] = deviceId;
      }

      // Set platform version from test request if provided
      if (platformVersion) {
        caps['appium:platformVersion'] = platformVersion;
      }

      // Set app ID for SauceLabs app storage if provided (W3C protocol requires appium: prefix)
      if (appId) {
        (caps as any)['appium:app'] = `storage:${appId}`;
      }

      // Add Sauce Labs options with Appium 2 support
      if (!caps['sauce:options']) {
        caps['sauce:options'] = {};
      }

      // Set Appium version to stable (Appium 2) for Android 14+ compatibility
      if (!caps['sauce:options'].appiumVersion) {
        caps['sauce:options'].appiumVersion = 'stable';
      }

      if (!caps['sauce:options'].build) {
        caps['sauce:options'].build = `Build ${new Date().toISOString()}`;
      }

      if (!caps['sauce:options'].name) {
        caps['sauce:options'].name = `Test ${new Date().toISOString()}`;
      }

      // Set timeout configurations to prevent session timeout issues
      if (!caps['sauce:options'].idleTimeout) {
        caps['sauce:options'].idleTimeout = 120; // 2 minutes idle timeout
      }

      if (!caps['sauce:options'].commandTimeout) {
        caps['sauce:options'].commandTimeout = 300; // 5 minutes command timeout
      }

      if (!caps['sauce:options'].maxDuration) {
        caps['sauce:options'].maxDuration = 1800; // 30 minutes max duration
      }

      // Enable proper session cleanup
      if (!caps['sauce:options'].recordVideo) {
        caps['sauce:options'].recordVideo = true;
      }

      if (!caps['sauce:options'].recordScreenshots) {
        caps['sauce:options'].recordScreenshots = true;
      }

      // Check if agentFromSauceLabs is available
      if (!agentFromSauceLabs) {
        throw new Error('agentFromSauceLabs function not available from misoai-android module');
      }

      // Create agent using misoai-android with Sauce Labs
      const sauceConfig = {
        user: this.sauceConfig.user,
        key: this.sauceConfig.key,
        region: (this.sauceConfig.region === "eu-central-1" || this.sauceConfig.region === "us-east-1" || this.sauceConfig.region === "us-west-1") ?
          this.sauceConfig.region :
          "us-west-1"
      };

      const agent = await agentFromSauceLabs(sauceConfig, caps);

      // Store agent reference
      const deviceKey = deviceId || 'default';
      this.registerAgent(deviceKey, agent);

      // SauceLabs has its own internal queue mechanism, no need to wait for job to start
      this.logger.info('SauceLabsDeviceManager: Agent created successfully, SauceLabs will handle job queuing internally.');

      return agent;
    } catch (error: any) {
      throw error;
    }
  }



  /**
   * Connect to an Appium hub using test request configuration
   * @param testRequest Test request containing Appium configuration
   * @returns Promise resolving to a device agent
   */
  async connectToAppiumHub(testRequest: TestRequest): Promise<AndroidAgent> {
    try {
      if (!testRequest.id) {
        throw new Error('Test request ID is missing');
      }

      const deviceInfo = this.utils.processTestRequest(testRequest, 'sauceLabs');

      // Connect to device using the Sauce Labs API with platform version and app ID
      // Use scenarioId instead of testId for consistency with caching approach
      return this.connectToDevice(deviceInfo.deviceId, testRequest.scenarioId, deviceInfo.osVersion, deviceInfo.appId);
    } catch (error: any) {
      this.logger.error(`SauceLabsDeviceManager: Error connecting to SauceLabs: ${error.message}`);
      throw error;
    }
  }

  /**
   * Disconnect from a device
   * @param device Device agent to disconnect from
   */
  async disconnectFromDevice(device: AndroidAgent): Promise<void> {
    try {
      const deviceKey = this.getKeyForDevice(device);
      if (!deviceKey) {
        return;
      }

      // Close the session properly with enhanced SauceLabs support
      if (device && device.page) {
        await this.performSauceLabsSessionCleanup(device);
      }

      // Unregister the agent
      this.unregisterAgent(deviceKey);
    } catch (error: any) {
      this.logger.error(`SauceLabsDeviceManager: Error during device disconnection: ${error.message}`);
      throw error;
    }
  }

  /**
   * Perform comprehensive SauceLabs session cleanup
   * @param device Device agent to cleanup
   */
  private async performSauceLabsSessionCleanup(device: AndroidAgent): Promise<void> {
    if (!device.page) {
      return;
    }

    try {
      const driver = await device.page.getDriver();
      // Check if this is a SauceLabs session by checking the hostname in the driver options
      const isSauceLabs = driver?.options?.hostname?.includes('saucelabs.com');

      if (isSauceLabs) {
        this.logger.info('SauceLabsDeviceManager: Performing SauceLabs session cleanup');

        // Step 1: Set job result to indicate test completion
        await this.setSauceLabsJobResult(device);

        // Step 2: Send a final command to ensure session is active before quit
        await this.sendFinalKeepAliveCommand(device);

        // Step 3: Perform proper driver quit sequence
        await this.performDriverQuitSequence(device);
      } else {
        // For non-SauceLabs sessions, use standard cleanup
        await device.page.disconnect();
      }
    } catch (error: any) {
      this.logger.error(`SauceLabsDeviceManager: Error during session cleanup: ${error.message}`);
      // Still attempt basic cleanup even if enhanced cleanup fails
      try {
        await device.page.disconnect();
      } catch (fallbackError: any) {
        this.logger.error(`SauceLabsDeviceManager: Fallback cleanup also failed: ${fallbackError.message}`);
      }
    }
  }

  /**
   * Set SauceLabs job result
   * @param device Device agent
   */
  private async setSauceLabsJobResult(device: AndroidAgent): Promise<void> {
    try {
      const driver = await device.page.getDriver();
      if (driver && typeof driver.execute === 'function') {
        await driver.execute('sauce:job-result=passed');
        this.logger.debug('SauceLabsDeviceManager: Set job result to passed');
      }
    } catch (error: any) {
      this.logger.warn(`SauceLabsDeviceManager: Failed to set job result: ${error.message}`);
    }
  }

  /**
   * Send a final keep-alive command to ensure session is responsive
   * @param device Device agent
   */
  private async sendFinalKeepAliveCommand(device: AndroidAgent): Promise<void> {
    try {
      // Send a simple command to ensure the session is still active
      // This helps prevent the "90 seconds without command" timeout
      const driver = await device.page.getDriver();
      if (driver && typeof driver.getPageSource === 'function') {
        await driver.getPageSource();
        this.logger.debug('SauceLabsDeviceManager: Sent final keep-alive command');
      }
    } catch (error: any) {
      this.logger.warn(`SauceLabsDeviceManager: Failed to send keep-alive command: ${error.message}`);
    }
  }

  /**
   * Perform proper driver quit sequence for SauceLabs
   * @param device Device agent
   */
  private async performDriverQuitSequence(device: AndroidAgent): Promise<void> {
    try {
      // The rfi-ai-android library provides a disconnect method on the device page
      // which should handle the driver.quit() call correctly.
      if (device.page && typeof device.page.disconnect === 'function') {
        this.logger.debug('SauceLabsDeviceManager: Calling device.page.disconnect()');
        await device.page.disconnect();
        this.logger.debug('SauceLabsDeviceManager: Successfully called device.page.disconnect()');
      } else {
         this.logger.warn('SauceLabsDeviceManager: device.page.disconnect() not available. Attempting manual cleanup.');
        // Fallback to manual deletion if disconnect is not available
        await this.manualSessionDeletion(device);
      }
    } catch (error: any) {
      this.logger.error(`SauceLabsDeviceManager: Failed to quit driver properly: ${error.message}`);
      throw error;
    }
  }

  /**
   * Manually delete session via HTTP DELETE request
   * @param device Device agent
   */
  private async manualSessionDeletion(device: AndroidAgent): Promise<void> {
    try {
      // Get session ID and server config from the driver
      const driver = await device.page.getDriver();
      const sessionId = driver?.sessionId;
      const serverConfig = driver?.options;

      if (!sessionId) {
        this.logger.warn('SauceLabsDeviceManager: No session ID available for manual deletion');
        return;
      }

      if (serverConfig && serverConfig.hostname) {
        const deleteUrl = `${serverConfig.protocol}://${serverConfig.hostname}:${serverConfig.port}${serverConfig.path}/session/${sessionId}`;

        // Use the same authentication as the original session
        const auth = {
          username: this.sauceConfig.user,
          password: this.sauceConfig.key
        };

        // Send DELETE request to terminate session
        const response = await fetch(deleteUrl, {
          method: 'DELETE',
          headers: {
            'Authorization': `Basic ${Buffer.from(`${auth.username}:${auth.password}`).toString('base64')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          this.logger.info(`SauceLabsDeviceManager: Successfully deleted session ${sessionId} via HTTP DELETE`);
        } else {
          this.logger.warn(`SauceLabsDeviceManager: HTTP DELETE returned status ${response.status} for session ${sessionId}`);
        }
      } else {
        this.logger.warn('SauceLabsDeviceManager: No server config available for manual session deletion');
      }
    } catch (error: any) {
      this.logger.error(`SauceLabsDeviceManager: Manual session deletion failed: ${error.message}`);
    }
  }

  /**
   * Create an AI agent for a device
   * @param device Device agent to create AI agent for
   * @returns Promise resolving to an Android agent
   */
  async createAgent(device: AndroidAgent): Promise<AndroidAgent> {
    // In misoai-android, the device is already an agent
    // This method is provided for consistency with the IDeviceManager interface
    return device;
  }

  /**
   * Update test status in Sauce Labs
   * @param device Device agent
   * @param passed Whether the test passed
   * @param name Test name
   */
  async updateTestStatus(device: AndroidAgent, passed: boolean, name?: string): Promise<void> {
    try {
      const driver = await device.page.getDriver();
      if (driver && typeof driver.execute === 'function') {
        await driver.execute('sauce:job-result=' + (passed ? 'passed' : 'failed'));
        if (name) {
          await driver.execute('sauce:job-name=' + name);
        }
      }
    } catch (error: any) {
      // Ignore errors updating test status
    }
  }

  /**
   * Report device unavailability to Test Hub
   * @param testId Test ID
   * @param deviceId Device ID
   * @returns Promise resolving to true if report was sent, false otherwise
   */
  async reportDeviceUnavailability(testId: string, deviceId: string): Promise<boolean> {
    try {
      // Send message to Test Hub via WebSocket using new factory pattern
      const connectorManager = await ConnectorFactory.createConnectorManager({
        enableLogging: false
      });
      const websocketConnector = await connectorManager.createConnector('websocket');

      const sent = await (websocketConnector as any).sendMessage({
        type: 'device:unavailable',
        data: {
          testId,
          deviceId,
          timestamp: Date.now(),
          error: `SauceLabs device ${deviceId} is not available`
        }
      });

      return sent;
    } catch (error: any) {
      return false;
    }
  }
}
