"use client"

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TestTube, ExternalLink, Settings, AlertTriangle, CheckCircle2 } from 'lucide-react';
import {
  getScenarioTestManagementConfig,
  getFormDataTestManagementConfig,
  getProviderDisplayName,
  getProviderColorScheme,
  validateTestManagementIntegration,
  getIntegrationSummary
} from '@/lib/utils/unified-test-management-utils';
import { Scenario } from '@/models/scenario';
import { ExtendedScenarioFormData } from '@/lib/utils/scenario-form-utils';

interface UnifiedTestManagementStatusProps {
  scenario?: Scenario;
  formData?: ExtendedScenarioFormData;
  onConfigureClick?: () => void;
  showDetails?: boolean;
  className?: string;
}

export function UnifiedTestManagementStatus({
  scenario,
  formData,
  onConfigureClick,
  showDetails = false,
  className = ""
}: UnifiedTestManagementStatusProps) {
  // Get configuration from either scenario or form data
  const config = scenario 
    ? getScenarioTestManagementConfig(scenario)
    : formData 
      ? getFormDataTestManagementConfig(formData)
      : null;

  if (!config) return null;

  const validation = validateTestManagementIntegration(config);
  const summary = getIntegrationSummary(config);

  // No integration configured
  if (!config.hasIntegration) {
    return (
      <Card className={`border-dashed border-gray-300 dark:border-gray-600 ${className}`}>
        <CardContent className="flex flex-col items-center justify-center py-6 text-center">
          <TestTube className="h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            No test management integration configured
          </p>
          {onConfigureClick && (
            <Button variant="outline" size="sm" onClick={onConfigureClick}>
              <Settings className="h-4 w-4 mr-1" />
              Configure Integration
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  // Integration configured
  const activeIntegrations = config.integrations.filter(i => i.isActive);
  const hasWarnings = validation.warnings.length > 0;
  const hasErrors = validation.errors.length > 0;

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base flex items-center justify-between">
          <div className="flex items-center">
            <TestTube className="h-4 w-4 mr-2 text-blue-500" />
            Test Management
          </div>
          <div className="flex items-center gap-2">
            {hasErrors && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            {hasWarnings && !hasErrors && (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
            {!hasErrors && !hasWarnings && activeIntegrations.length > 0 && (
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Summary */}
          <p className="text-sm text-gray-700 dark:text-gray-300">
            {summary}
          </p>

          {/* Active integrations */}
          {activeIntegrations.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {activeIntegrations.map((integration, index) => {
                const colorScheme = getProviderColorScheme(integration.provider);
                const displayName = getProviderDisplayName(integration.provider);
                
                return (
                  <Badge 
                    key={index}
                    variant="secondary" 
                    className={`${colorScheme.bg} ${colorScheme.text} ${colorScheme.border}`}
                  >
                    {displayName} ({integration.caseIds.length})
                  </Badge>
                );
              })}
            </div>
          )}

          {/* Validation messages */}
          {showDetails && (validation.errors.length > 0 || validation.warnings.length > 0) && (
            <div className="space-y-2">
              {validation.errors.map((error, index) => (
                <div key={`error-${index}`} className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {error}
                </div>
              ))}
              {validation.warnings.map((warning, index) => (
                <div key={`warning-${index}`} className="text-xs text-yellow-600 dark:text-yellow-400 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {warning}
                </div>
              ))}
            </div>
          )}

          {/* Configure button */}
          {onConfigureClick && (
            <Button variant="outline" size="sm" onClick={onConfigureClick} className="w-full">
              <Settings className="h-4 w-4 mr-1" />
              {config.hasIntegration ? 'Modify Integration' : 'Configure Integration'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for lists and cards
interface CompactTestManagementStatusProps {
  scenario?: Scenario;
  formData?: ExtendedScenarioFormData;
  className?: string;
}

export function CompactTestManagementStatus({
  scenario,
  formData,
  className = ""
}: CompactTestManagementStatusProps) {
  // Get configuration from either scenario or form data
  const config = scenario 
    ? getScenarioTestManagementConfig(scenario)
    : formData 
      ? getFormDataTestManagementConfig(formData)
      : null;

  if (!config || !config.hasIntegration) {
    return (
      <Badge variant="outline" className={`text-gray-500 ${className}`}>
        <TestTube className="h-3 w-3 mr-1" />
        No integration
      </Badge>
    );
  }

  const activeIntegrations = config.integrations.filter(i => i.isActive);
  
  if (activeIntegrations.length === 0) {
    return (
      <Badge variant="outline" className={`text-yellow-600 ${className}`}>
        <TestTube className="h-3 w-3 mr-1" />
        Configured (sync disabled)
      </Badge>
    );
  }

  if (activeIntegrations.length === 1) {
    const integration = activeIntegrations[0];
    const colorScheme = getProviderColorScheme(integration.provider);
    const displayName = getProviderDisplayName(integration.provider);
    
    return (
      <Badge 
        variant="secondary" 
        className={`${colorScheme.bg} ${colorScheme.text} ${className}`}
      >
        <TestTube className="h-3 w-3 mr-1" />
        {displayName} ({integration.caseIds.length})
      </Badge>
    );
  }

  return (
    <Badge variant="secondary" className={`bg-blue-50 text-blue-700 ${className}`}>
      <TestTube className="h-3 w-3 mr-1" />
      Multiple ({config.totalCases})
    </Badge>
  );
}


