/**
 * Connection Manager Interface
 * Defines the contract for connection managers
 */

import { EventEmitter } from 'events';

/**
 * Connection manager interface
 */
export interface IConnectionManager extends EventEmitter {
  /**
   * Connect to server
   * @param url Server URL
   * @param options Connection options
   */
  connect(url: string, options?: any): Promise<void>;

  /**
   * Disconnect from server
   */
  disconnect(): Promise<void>;

  /**
   * Send data
   * @param data Data to send
   */
  send(data: any): Promise<boolean>;

  /**
   * Check if connected
   */
  isConnected(): boolean;

  /**
   * Get connection status
   */
  getConnectionStatus(): string;

  /**
   * Get connection statistics
   */
  getConnectionStats(): Record<string, any>;

  /**
   * Set reconnection options
   * @param options Reconnection options
   */
  setReconnectionOptions(options: IReconnectionOptions): void;

  /**
   * Enable/disable automatic reconnection
   * @param enabled Whether to enable reconnection
   */
  setAutoReconnect(enabled: boolean): void;

  /**
   * Reset connection state
   */
  resetConnectionState(): void;
}

/**
 * Reconnection options interface
 */
export interface IReconnectionOptions {
  enabled?: boolean;
  maxAttempts?: number;
  initialDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  jitter?: boolean;
}

/**
 * Connection options interface
 */
export interface IConnectionOptions {
  timeout?: number;
  headers?: Record<string, string>;
  protocols?: string[];
  reconnection?: IReconnectionOptions;
  pingInterval?: number;
  pongTimeout?: number;
}

/**
 * Connection statistics interface
 */
export interface IConnectionStats {
  status: string;
  connectedAt?: string;
  disconnectedAt?: string;
  uptime: number;
  reconnectAttempts: number;
  successfulReconnects: number;
  messagesSent: number;
  messagesReceived: number;
  bytesTransferred: number;
  lastPingTime?: string;
  lastPongTime?: string;
  averageLatency: number;
}

/**
 * Connection event types
 */
export enum ConnectionEventType {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  RECONNECTED = 'reconnected',
  ERROR = 'error',
  MESSAGE = 'message',
  PING = 'ping',
  PONG = 'pong',
  TIMEOUT = 'timeout'
}

/**
 * Connection error interface
 */
export interface IConnectionError {
  code: string;
  message: string;
  timestamp: string;
  context?: any;
}
