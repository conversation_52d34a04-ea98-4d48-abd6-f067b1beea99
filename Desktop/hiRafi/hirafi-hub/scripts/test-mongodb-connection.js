/**
 * MongoDB Connection Test Script
 * 
 * This script tests the MongoDB connection using the configuration from .env file
 * Run with: node scripts/test-mongodb-connection.js
 */

import { MongoClient } from 'mongodb';
import * as dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config();

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// MongoDB connection parameters
const host = process.env.MONGODB_HOST || '**********';
const port = parseInt(process.env.MONGODB_PORT || '27017');
const database = process.env.MONGODB_DATABASE || 'testinium_ai';
const username = process.env.MONGODB_USERNAME || 'admin';
const password = process.env.MONGODB_PASSWORD || 'password';
const useSSL = process.env.MONGODB_USE_SSL === 'true';

// Construct the connection URI
const auth = username && password ? `${username}:${encodeURIComponent(password)}@` : '';
const sslOption = useSSL ? '?ssl=true' : '';
const uri = `mongodb://${auth}${host}:${port}/${database}${sslOption}`;

console.log('Testing MongoDB connection with the following parameters:');
console.log(`Host: ${host}`);
console.log(`Port: ${port}`);
console.log(`Database: ${database}`);
console.log(`Username: ${username}`);
console.log(`Password: ${'*'.repeat(password.length)}`);
console.log(`SSL Enabled: ${useSSL}`);
console.log(`Connection URI: mongodb://${auth.replace(password, '*'.repeat(password.length))}${host}:${port}/${database}${sslOption}`);

// Create a new MongoClient
const client = new MongoClient(uri);

async function run() {
  try {
    // Connect to the MongoDB server
    console.log('\nAttempting to connect to MongoDB...');
    await client.connect();
    console.log('✅ Successfully connected to MongoDB!');

    // Get the database
    const db = client.db(database);
    
    // List collections to verify access
    console.log('\nListing collections:');
    const collections = await db.listCollections().toArray();
    
    if (collections.length === 0) {
      console.log('No collections found in the database.');
    } else {
      collections.forEach(collection => {
        console.log(`- ${collection.name}`);
      });
    }

    console.log('\n✅ Connection test completed successfully!');
  } catch (error) {
    console.error('\n❌ MongoDB connection failed:');
    console.error(error);
    
    // Provide more detailed error information
    if (error.name === 'MongoServerSelectionError') {
      console.error('\nThis error typically means the server cannot be reached. Check:');
      console.error('1. Is the MongoDB server running?');
      console.error('2. Is the host/port correct?');
      console.error('3. Are there any network/firewall issues?');
    } else if (error.name === 'MongoError' && error.code === 18) {
      console.error('\nAuthentication failed. Check:');
      console.error('1. Is the username correct?');
      console.error('2. Is the password correct?');
      console.error('3. Does the user have access to the specified database?');
    } else if (error.name === 'MongoError' && error.code === 13) {
      console.error('\nAuthorization failed. Check:');
      console.error('1. Does the user have the necessary permissions?');
    }
    
    // Try alternative connection string
    console.log('\nTrying alternative connection method...');
    try {
      const alternativeUri = `mongodb://${auth}${host}:${port}/${database}`;
      console.log(`Alternative URI: mongodb://${auth.replace(password, '*'.repeat(password.length))}${host}:${port}/${database}`);
      
      const alternativeClient = new MongoClient(alternativeUri);
      await alternativeClient.connect();
      console.log('✅ Alternative connection successful!');
      
      // Get the database
      const db = alternativeClient.db(database);
      
      // List collections to verify access
      console.log('\nListing collections:');
      const collections = await db.listCollections().toArray();
      
      if (collections.length === 0) {
        console.log('No collections found in the database.');
      } else {
        collections.forEach(collection => {
          console.log(`- ${collection.name}`);
        });
      }
      
      await alternativeClient.close();
    } catch (alternativeError) {
      console.error('\n❌ Alternative connection also failed:');
      console.error(alternativeError);
    }
  } finally {
    // Close the connection
    await client.close();
    console.log('\nConnection closed.');
  }
}

run().catch(console.error);
