/**
 * Test Data Service
 * MongoDB service layer for test data management
 */

import { Collection } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import {
  ensureMongoDBConnection,
  isMongoDBInitialized,
  db,
  dataSetsCollection,
  dataSourcesCollection,
  dataEnvironmentsCollection
} from './dbConnection.js';
import {
  DataSet,
  DataSource,
  DataEnvironment,
  CreateDataSetRequest,
  UpdateDataSetRequest,
  CreateDataSourceRequest,
  UpdateDataSourceRequest,
  CreateDataEnvironmentRequest,
  UpdateDataEnvironmentRequest,
  DataSetQueryOptions,
  DataSourceQueryOptions,
  DataEnvironmentQueryOptions,
  TestDataApiResponse,
  DataSetListResponse,
  DataSetKeysResponse,
  DataSourceListResponse,
  DataEnvironmentListResponse
} from '../../models/test-data.js';

// Collection getters with initialization
export async function getDataSetsCollection(): Promise<Collection> {
  if (!dataSetsCollection) {
    await ensureMongoDBConnection();
    if (!db) {
      throw new Error('MongoDB connection not available');
    }
  }
  return dataSetsCollection!;
}

export async function getDataSourcesCollection(): Promise<Collection> {
  if (!dataSourcesCollection) {
    await ensureMongoDBConnection();
    if (!db) {
      throw new Error('MongoDB connection not available');
    }
  }
  return dataSourcesCollection!;
}

export async function getDataEnvironmentsCollection(): Promise<Collection> {
  if (!dataEnvironmentsCollection) {
    await ensureMongoDBConnection();
    if (!db) {
      throw new Error('MongoDB connection not available');
    }
  }
  return dataEnvironmentsCollection!;
}

/**
 * Data Sets Service Functions
 */

export async function createDataSet(
  request: CreateDataSetRequest,
  teamId: string,
  companyId: string,
  createdBy: string
): Promise<TestDataApiResponse<DataSet>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSetsCollection();
    const now = new Date();

    const dataSet: DataSet = {
      id: uuidv4(),
      name: request.name,
      description: request.description,
      tags: request.tags,
      environment: request.environment,
      metadata: request.metadata, // Store comprehensive environment data
      teamId,
      companyId,
      createdBy,
      createdAt: now,
      updatedAt: now
    };

    await collection.insertOne(dataSet);

    logger.info(`[TEST_DATA] Created data set: ${dataSet.id} for team: ${teamId}`);

    return {
      success: true,
      data: dataSet,
      message: 'Data set created successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error creating data set: ${error.message}`);
    return {
      success: false,
      message: `Failed to create data set: ${error.message}`
    };
  }
}

export async function getDataSets(
  teamId: string,
  companyId: string,
  options: DataSetQueryOptions = {}
): Promise<TestDataApiResponse<DataSetListResponse>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSetsCollection();
    const { limit = 50, skip = 0, search, environment, tags } = options;

    // Build query
    const query: any = {
      teamId,
      companyId
    };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    if (environment && environment !== 'all') {
      query.environment = environment;
    }

    if (tags && tags.length > 0) {
      query.tags = { $in: tags };
    }

    // Get total count
    const total = await collection.countDocuments(query);

    // Get data sets
    const dataSets = await collection
      .find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray() as unknown as DataSet[];

    return {
      success: true,
      data: {
        dataSets,
        total
      }
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error getting data sets: ${error.message}`);
    return {
      success: false,
      message: `Failed to get data sets: ${error.message}`
    };
  }
}

/**
 * Get lightweight data set information with variable names and their environments for dropdown performance
 */
export async function getDataSetKeys(
  teamId: string,
  companyId: string,
  options: DataSetQueryOptions = {}
): Promise<TestDataApiResponse<DataSetKeysResponse>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSetsCollection();
    const { limit = 50, skip = 0, search, environment, tags } = options;

    // Build query
    const query: any = {
      teamId,
      companyId
    };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    if (environment && environment !== 'all') {
      query.environment = environment;
    }

    if (tags && tags.length > 0) {
      query.tags = { $in: tags };
    }

    // Project only necessary fields for performance
    const projection = {
      id: 1,
      name: 1,
      description: 1,
      'metadata.variables': 1,
      'metadata.environments': 1
    };

    const dataSets = await collection
      .find(query, { projection })
      .sort({ updatedAt: -1 })
      .limit(limit)
      .skip(skip)
      .toArray();

    // Extract variable names and their environments from metadata
    const lightweightDataSets = dataSets.map(dataSet => {
      const variables: Array<{ name: string; environments: Array<{ id: string; name: string }> }> = [];

      // Get environments from metadata.environments
      const dataSetEnvironments: Array<{ id: string; name: string }> = [];
      if (dataSet.metadata?.environments && Array.isArray(dataSet.metadata.environments)) {
        dataSet.metadata.environments.forEach((env: any) => {
          if (env.id && env.name && env.isActive) {
            dataSetEnvironments.push({
              id: env.id,
              name: env.name
            });
          }
        });
      }

      // Extract variables from metadata.variables
      if (dataSet.metadata?.variables && Array.isArray(dataSet.metadata.variables)) {
        dataSet.metadata.variables.forEach((variable: any) => {
          if (variable.name) {
            const variableEnvironments: Array<{ id: string; name: string }> = [];

            // Check which environments have values for this variable
            if (variable.environmentValues && typeof variable.environmentValues === 'object') {
              Object.keys(variable.environmentValues).forEach(envKey => {
                const envValue = variable.environmentValues[envKey];

                // Check if this environment value has actual data
                if (envValue && typeof envValue === 'object' && envValue.value !== undefined && envValue.value !== '') {
                  // Find the environment info from dataSetEnvironments by name
                  const envInfo = dataSetEnvironments.find(env => env.name === envKey);
                  if (envInfo) {
                    variableEnvironments.push(envInfo);
                  }
                }
              });
            }

            variables.push({
              name: variable.name,
              environments: variableEnvironments
            });
          }
        });
      }

      return {
        id: dataSet.id,
        name: dataSet.name,
        description: dataSet.description,
        variables
      };
    });

    return {
      success: true,
      data: {
        dataSets: lightweightDataSets,
        total: lightweightDataSets.length
      }
    };
  } catch (error) {
    logger.error('Error getting data set keys:', error);
    return { success: false, message: 'Failed to get data set keys' };
  }
}

export async function getDataSetById(
  id: string,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<DataSet>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSetsCollection();
    const dataSet = await collection.findOne({
      id,
      teamId,
      companyId
    }) as unknown as DataSet;

    if (!dataSet) {
      return {
        success: false,
        message: 'Data set not found'
      };
    }

    return {
      success: true,
      data: dataSet
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error getting data set: ${error.message}`);
    return {
      success: false,
      message: `Failed to get data set: ${error.message}`
    };
  }
}

export async function updateDataSet(
  id: string,
  request: UpdateDataSetRequest,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<DataSet>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSetsCollection();
    const now = new Date();

    // Prepare update data
    const updateData: any = {
      updatedAt: now
    };

    if (request.name !== undefined) updateData.name = request.name;
    if (request.description !== undefined) updateData.description = request.description;
    if (request.tags !== undefined) updateData.tags = request.tags;
    if (request.environment !== undefined) updateData.environment = request.environment;
    if (request.metadata !== undefined) updateData.metadata = request.metadata;

    const result = await collection.findOneAndUpdate(
      { id, teamId, companyId },
      { $set: updateData },
      { returnDocument: 'after' }
    );

    if (!result || !result.value) {
      return {
        success: false,
        message: 'Data set not found'
      };
    }

    logger.info(`[TEST_DATA] Updated data set: ${id} for team: ${teamId}`);

    return {
      success: true,
      data: result.value as unknown as DataSet,
      message: 'Data set updated successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error updating data set: ${error.message}`);
    return {
      success: false,
      message: `Failed to update data set: ${error.message}`
    };
  }
}

export async function deleteDataSet(
  id: string,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<void>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSetsCollection();
    const result = await collection.deleteOne({
      id,
      teamId,
      companyId
    });

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: 'Data set not found'
      };
    }

    logger.info(`[TEST_DATA] Deleted data set: ${id} for team: ${teamId}`);

    return {
      success: true,
      message: 'Data set deleted successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error deleting data set: ${error.message}`);
    return {
      success: false,
      message: `Failed to delete data set: ${error.message}`
    };
  }
}

/**
 * Data Sources Service Functions
 */

export async function createDataSource(
  request: CreateDataSourceRequest,
  teamId: string,
  companyId: string,
  createdBy: string
): Promise<TestDataApiResponse<DataSource>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSourcesCollection();
    const now = new Date();

    // For database read mode, copy config.data to variables for easier access
    let variables = request.variables;
    if (request.type === 'database' && request.config?.mode === 'read' && request.config.data) {
      variables = request.config.data.map(item => ({
        name: item.name,
        value: item.value
      }));
    }

    const dataSource: DataSource = {
      id: uuidv4(),
      name: request.name,
      type: request.type,
      description: request.description,
      connectionString: request.connectionString,
      filePath: request.filePath,
      variables: variables,
      config: request.config,
      isActive: request.isActive ?? true,
      teamId,
      companyId,
      createdBy,
      createdAt: now,
      updatedAt: now
    };



    await collection.insertOne(dataSource);

    logger.info(`[TEST_DATA] Created data source: ${dataSource.id} for team: ${teamId}`);

    return {
      success: true,
      data: dataSource,
      message: 'Data source created successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error creating data source: ${error.message}`);
    return {
      success: false,
      message: `Failed to create data source: ${error.message}`
    };
  }
}

export async function getDataSources(
  teamId: string,
  companyId: string,
  options: DataSourceQueryOptions = {}
): Promise<TestDataApiResponse<DataSourceListResponse>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSourcesCollection();
    const { limit = 50, skip = 0, search, type, isActive } = options;

    // Build query
    const query: any = {
      teamId,
      companyId
    };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { type: { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      query.type = type;
    }

    if (isActive !== undefined) {
      query.isActive = isActive;
    }

    // Get total count
    const total = await collection.countDocuments(query);

    // Get data sources
    const dataSources = await collection
      .find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray() as unknown as DataSource[];

    return {
      success: true,
      data: {
        dataSources,
        total
      }
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error getting data sources: ${error.message}`);
    return {
      success: false,
      message: `Failed to get data sources: ${error.message}`
    };
  }
}

export async function getDataSourceById(
  id: string,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<DataSource>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSourcesCollection();
    
    // 🚀 NODE USER BYPASS - If teamId/companyId are empty (node user), search only by id
    let query: any;
    if (!teamId || !companyId) {
      query = { id };
      logger.info(`[TEST_DATA] Node user accessing datasource by id only: ${id}`);
    } else {
      query = { id, teamId, companyId };
      logger.debug(`[TEST_DATA] Regular user accessing datasource: ${id} for team: ${teamId}`);
    }
    
    const dataSource = await collection.findOne(query) as unknown as DataSource;

    if (!dataSource) {
      return {
        success: false,
        message: 'Data source not found'
      };
    }



    return {
      success: true,
      data: dataSource
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error getting data source: ${error.message}`);
    return {
      success: false,
      message: `Failed to get data source: ${error.message}`
    };
  }
}

export async function updateDataSource(
  id: string,
  request: UpdateDataSourceRequest,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<DataSource>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSourcesCollection();
    const now = new Date();

    const updatePayload: { $set: Partial<UpdateDataSourceRequest> & { updatedAt: Date } } = {
      $set: {
        updatedAt: now,
      },
    };

    if (request.name !== undefined) updatePayload.$set.name = request.name;
    if (request.description !== undefined) updatePayload.$set.description = request.description;
    if (request.connectionString !== undefined) updatePayload.$set.connectionString = request.connectionString;
    if (request.filePath !== undefined) updatePayload.$set.filePath = request.filePath;
    if (request.variables !== undefined) updatePayload.$set.variables = request.variables;
    if (request.config !== undefined) {
      updatePayload.$set.config = request.config;
      
      // For database read mode, also update variables field from config.data
      if (request.config.mode === 'read' && request.config.data) {
        updatePayload.$set.variables = request.config.data.map(item => ({
          name: item.name,
          value: item.value
        }));
      }
    }
    if (request.isActive !== undefined) updatePayload.$set.isActive = request.isActive;

    const result = await collection.updateOne({ id, teamId, companyId }, updatePayload);

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: 'Data source not found'
      };
    }

    logger.info(`[TEST_DATA] Updated data source: ${id} for team: ${teamId}`);

    return {
      success: true,
      message: 'Data source updated successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error updating data source: ${error.message}`);
    return {
      success: false,
      message: `Failed to update data source: ${error.message}`
    };
  }
}

export async function deleteDataSource(
  id: string,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<void>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataSourcesCollection();
    const result = await collection.deleteOne({
      id,
      teamId,
      companyId
    });

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: 'Data source not found'
      };
    }

    logger.info(`[TEST_DATA] Deleted data source: ${id} for team: ${teamId}`);

    return {
      success: true,
      message: 'Data source deleted successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error deleting data source: ${error.message}`);
    return {
      success: false,
      message: `Failed to delete data source: ${error.message}`
    };
  }
}

/**
 * Data Environments Service Functions
 */

export async function createDataEnvironment(
  request: CreateDataEnvironmentRequest,
  teamId: string,
  companyId: string,
  createdBy: string
): Promise<TestDataApiResponse<DataEnvironment>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataEnvironmentsCollection();
    const now = new Date();

    const dataEnvironment: DataEnvironment = {
      id: uuidv4(),
      name: request.name,
      description: request.description,
      color: request.color,
      type: request.type ?? 'custom',
      isActive: request.isActive ?? true,
      teamId,
      companyId,
      createdBy,
      createdAt: now,
      updatedAt: now
    };

    await collection.insertOne(dataEnvironment);

    logger.info(`[TEST_DATA] Created data environment: ${dataEnvironment.id} for team: ${teamId}`);

    return {
      success: true,
      data: dataEnvironment,
      message: 'Data environment created successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error creating data environment: ${error.message}`);
    return {
      success: false,
      message: `Failed to create data environment: ${error.message}`
    };
  }
}

export async function getDataEnvironments(
  teamId: string,
  companyId: string,
  options: DataEnvironmentQueryOptions = {}
): Promise<TestDataApiResponse<DataEnvironmentListResponse>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataEnvironmentsCollection();
    const { limit = 50, skip = 0, search, type, isActive } = options;

    // Build query
    const query: any = {
      teamId,
      companyId
    };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (type) {
      query.type = type;
    }

    if (isActive !== undefined) {
      query.isActive = isActive;
    }

    // Get total count
    const total = await collection.countDocuments(query);

    // Get data environments
    const dataEnvironments = await collection
      .find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray() as unknown as DataEnvironment[];

    return {
      success: true,
      data: {
        dataEnvironments,
        total
      }
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error getting data environments: ${error.message}`);
    return {
      success: false,
      message: `Failed to get data environments: ${error.message}`
    };
  }
}

export async function getDataEnvironmentById(
  id: string,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<DataEnvironment>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataEnvironmentsCollection();

    // Debug logging
    logger.info(`[TEST_DATA] Looking for environment with id: ${id}, teamId: ${teamId}, companyId: ${companyId}`);

    const dataEnvironment = await collection.findOne({
      id,
      teamId,
      companyId
    }) as unknown as DataEnvironment;

    if (!dataEnvironment) {
      // Check if environment exists with different teamId/companyId
      const anyEnvironment = await collection.findOne({ id }) as unknown as DataEnvironment;
      if (anyEnvironment) {
        logger.warn(`[TEST_DATA] Environment ${id} exists but with different teamId: ${anyEnvironment.teamId}, companyId: ${anyEnvironment.companyId}`);
      } else {
        logger.warn(`[TEST_DATA] Environment ${id} does not exist at all`);
      }

      return {
        success: false,
        message: 'Data environment not found'
      };
    }

    return {
      success: true,
      data: dataEnvironment
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error getting data environment: ${error.message}`);
    return {
      success: false,
      message: `Failed to get data environment: ${error.message}`
    };
  }
}

export async function updateDataEnvironment(
  id: string,
  request: UpdateDataEnvironmentRequest,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<DataEnvironment>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataEnvironmentsCollection();
    const now = new Date();

    // Debug logging
    logger.info(`[TEST_DATA] Updating environment with id: ${id}, teamId: ${teamId}, companyId: ${companyId}`);

    // Prepare update data
    const updateData: any = {
      updatedAt: now
    };

    if (request.name !== undefined) updateData.name = request.name;
    if (request.description !== undefined) updateData.description = request.description;
    if (request.color !== undefined) updateData.color = request.color;
    if (request.type !== undefined) updateData.type = request.type;
    if (request.isActive !== undefined) updateData.isActive = request.isActive;

    logger.info(`[TEST_DATA] Update data:`, updateData);

    // Debug: First try to find the document to see what's happening
    const existingDoc = await collection.findOne({ id, teamId, companyId });
    logger.info(`[TEST_DATA] Existing document with exact match:`, existingDoc);

    // Also try to find with just id to see the actual document
    const docById = await collection.findOne({ id });
    logger.info(`[TEST_DATA] Document found by ID only:`, docById);

    // First check if document exists (since GET works)
    const existingDocument = await collection.findOne({ id, teamId, companyId });
    if (!existingDocument) {
      logger.warn(`[TEST_DATA] Document not found for update with id: ${id}, teamId: ${teamId}, companyId: ${companyId}`);
      return {
        success: false,
        message: 'Data environment not found'
      };
    }

    // Use findOneAndUpdate like CompanyService
    const result = await collection.findOneAndUpdate(
      { id, teamId, companyId },
      { $set: updateData },
      { returnDocument: 'after' }
    );

    logger.info(`[TEST_DATA] Update result:`, result);

    if (!result) {
      // Check if environment exists with different teamId/companyId
      const anyEnvironment = await collection.findOne({ id }) as unknown as DataEnvironment;
      if (anyEnvironment) {
        logger.warn(`[TEST_DATA] Environment ${id} exists but with different teamId: ${anyEnvironment.teamId}, companyId: ${anyEnvironment.companyId}. Request had teamId: ${teamId}, companyId: ${companyId}`);
      } else {
        logger.warn(`[TEST_DATA] Environment ${id} does not exist at all`);
      }

      return {
        success: false,
        message: 'Data environment not found'
      };
    }

    logger.info(`[TEST_DATA] Updated data environment: ${id} for team: ${teamId}`);

    return {
      success: true,
      data: result as unknown as DataEnvironment,
      message: 'Data environment updated successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error updating data environment: ${error.message}`);
    return {
      success: false,
      message: `Failed to update data environment: ${error.message}`
    };
  }
}

export async function deleteDataEnvironment(
  id: string,
  teamId: string,
  companyId: string
): Promise<TestDataApiResponse<void>> {
  try {
    if (!isMongoDBInitialized() || !db) {
      return { success: false, message: 'Database connection not established' };
    }

    const collection = await getDataEnvironmentsCollection();

    // Check if it's a default environment (should not be deleted)
    const environment = await collection.findOne({
      id,
      teamId,
      companyId
    }) as unknown as DataEnvironment;

    if (!environment) {
      return {
        success: false,
        message: 'Data environment not found'
      };
    }

    if (environment.type === 'default') {
      return {
        success: false,
        message: 'Default environments cannot be deleted'
      };
    }

    const result = await collection.deleteOne({
      id,
      teamId,
      companyId
    });

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: 'Data environment not found'
      };
    }

    logger.info(`[TEST_DATA] Deleted data environment: ${id} for team: ${teamId}`);

    return {
      success: true,
      message: 'Data environment deleted successfully'
    };
  } catch (error: any) {
    logger.error(`[TEST_DATA] Error deleting data environment: ${error.message}`);
    return {
      success: false,
      message: `Failed to delete data environment: ${error.message}`
    };
  }
}
