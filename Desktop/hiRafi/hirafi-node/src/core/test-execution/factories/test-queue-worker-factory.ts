/**
 * Test Queue Worker Factory
 * Creates test queue worker instances with proper dependency injection
 */

import { logger } from '../../../utils/logger.js';
import { TestQueueWorker } from '../services/queue/test-queue-worker.js';
import { ITestQueueWorker, TestQueueWorkerConfig } from '../interfaces/test-queue-worker.interface.js';
import { INodeIdentityService } from '../../node-manager/interfaces/node-identity-service.interface.js';
import { ApplicationContainer } from '../../common/di/application-container.js';

export class TestQueueWorkerFactory {
  /**
   * Create a test queue worker with dependency injection
   */
  static async createTestQueueWorker(
    identityService: INodeIdentityService,
    appContainer: ApplicationContainer,
    config?: TestQueueWorkerConfig
  ): Promise<ITestQueueWorker> {
    const nodeId = identityService.getNodeId() || 'unassigned';
    logger.info(`TestQueueWorkerFactory: Creating test queue worker for node ${nodeId}`);

    try {
      const worker = new TestQueueWorker(identityService, appContainer, config);

      // It's better to initialize the worker outside the factory
      // The caller should be responsible for calling worker.initialize()

      logger.info(`TestQueueWorkerFactory: Successfully created test queue worker for node ${nodeId}`);
      return worker;

    } catch (error: any) {
      logger.error(`TestQueueWorkerFactory: Failed to create test queue worker for node ${nodeId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a default test queue worker
   */
  static async createDefaultTestQueueWorker(
    identityService: INodeIdentityService,
    appContainer: ApplicationContainer
  ): Promise<ITestQueueWorker> {
    const defaultConfig: TestQueueWorkerConfig = {
      concurrency: 1,
      maxStalled: 3,
      stalledInterval: 30000
    };
    return this.createTestQueueWorker(identityService, appContainer, defaultConfig);
  }
}

/**
 * Convenience function for creating a test queue worker
 */
export async function createTestQueueWorker(
  identityService: INodeIdentityService,
  appContainer: ApplicationContainer,
  config?: TestQueueWorkerConfig
): Promise<ITestQueueWorker> {
  return TestQueueWorkerFactory.createTestQueueWorker(identityService, appContainer, config);
}
