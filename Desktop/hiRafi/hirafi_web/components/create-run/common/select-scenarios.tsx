"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from "react"
import { useRunForm } from "../run-context"
import { useScenarioManager } from "@/hooks/useScenarioManager"
import {
  Card,
  CardContent,
  CardHeader
} from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import {
  CheckSquare,
  ListFilter,
  LayoutGrid,
  Globe,
  Smartphone,
  Code,
  Clock
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { formatDistanceToNow } from "date-fns"
import { cn } from "@/lib/utils"

// Import our components
import { FolderNavigation } from "./folder-navigation"
import { SelectedScenariosList } from "./selected-scenarios-list"
import { isScenarioUncategorized } from "@/lib/utils/scenario-utils"
import { Sc<PERSON>rioSearch } from "./scenario-search"

interface SelectScenariosProps {
  editMode?: boolean;
  onToggleScenario?: (scenarioId: string) => void;
}

export function SelectScenarios({ onToggleScenario, editMode = false }: SelectScenariosProps) {
  // Get form context
  const {
    selectedScenarios,
    setSelectedScenarios,
    updateSelectedScenariosDetails,
    formData
  } = useRunForm()

  // Use centralized hook for scenarios and folders
  const {
    scenarios: fetchedScenarios,
    scenariosLoading,
    folders: fetchedFolders,
    foldersLoading,
  } = useScenarioManager({
    editMode: editMode
  })

  // Local state
  const [activeFolder, setActiveFolder] = useState<string | null>('all')
  const [searchQuery, setSearchQuery] = useState("")
  const [tagFilter, setTagFilter] = useState<string | null>(null)
  const [platformFilter, setPlatformFilter] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>("browse")

  // Auto-set platform filter based on the selected platform in the run form
  useEffect(() => {
    if (formData.platform) {
      // If platform is 'android', set filter to 'mobile'
      if (formData.platform === 'android') {
        setPlatformFilter('mobile');
      } else {
        setPlatformFilter(formData.platform);
      }
    }
  }, [formData.platform]);

  // Calculate scenario counts by folder
  const folderScenarioCounts = useMemo(() => {
    const counts: Record<string, number> = {}

    fetchedScenarios.forEach(scenario => {
      if (scenario.folderId) {
        counts[scenario.folderId] = (counts[scenario.folderId] || 0) + 1
      }
    })

    return counts
  }, [fetchedScenarios])

  // Get uncategorized scenarios
  const uncategorizedScenarios = useMemo(() => {
    return fetchedScenarios.filter(s => isScenarioUncategorized(s.folderId))
  }, [fetchedScenarios])

  // Get all unique tags from scenarios
  const availableTags = useMemo(() => {
    return Array.from(
      new Set(
        fetchedScenarios
          .flatMap(scenario => scenario.tags || [])
          .filter(Boolean)
      )
    ).sort()
  }, [fetchedScenarios])

  // Filter scenarios based on active folder, search query, and filters
  const filteredScenarios = useMemo(() => {
    let result = [...fetchedScenarios]

    // Filter by folder
    if (activeFolder && activeFolder !== 'all') {
      if (activeFolder === 'uncategorized') {
        // Use the same comprehensive filter as uncategorizedScenarios for consistency
        result = result.filter(s => isScenarioUncategorized(s.folderId))
      } else {
        result = result.filter(s => s.folderId === activeFolder)
      }
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(s =>
        (s.name?.toLowerCase().includes(query) ||
         s.description?.toLowerCase().includes(query) ||
         s.tags?.some(tag => tag.toLowerCase().includes(query)))
      )
    }

    // Filter by tag
    if (tagFilter) {
      result = result.filter(s =>
        s.tags?.some(tag => tag.toLowerCase() === tagFilter.toLowerCase())
      )
    }

    // Filter by platform
    if (platformFilter) {
      if (platformFilter === 'mobile') {
        result = result.filter(s => {
          const platform = s.platform?.toLowerCase();
          return platform === 'android' || platform === 'ios' || platform === 'mobile';
        });
      } else {
        result = result.filter(s => s.platform?.toLowerCase() === platformFilter.toLowerCase());
      }
    }

    return result
  }, [fetchedScenarios, activeFolder, searchQuery, tagFilter, platformFilter])

  // Calculate if all filtered scenarios are selected
  const areAllFilteredScenariosSelected = useMemo(() => {
    if (filteredScenarios.length === 0) return false;
    return filteredScenarios.every(scenario => selectedScenarios.includes(scenario.id));
  }, [filteredScenarios, selectedScenarios]);

  // Calculate if some (but not all) filtered scenarios are selected
  const areSomeFilteredScenariosSelected = useMemo(() => {
    if (filteredScenarios.length === 0) return false;
    const selectedCount = filteredScenarios.filter(scenario => selectedScenarios.includes(scenario.id)).length;
    return selectedCount > 0 && selectedCount < filteredScenarios.length;
  }, [filteredScenarios, selectedScenarios]);

  // Helper function to get platform icon
  const getPlatformIcon = (platform: string = 'web') => {
    switch (platform?.toLowerCase()) {
      case 'android':
      case 'ios':
      case 'mobile':
        return <Smartphone className="h-3.5 w-3.5 text-green-500" />
      case 'api':
        return <Code className="h-3.5 w-3.5 text-purple-500" />
      case 'web':
      default:
        return <Globe className="h-3.5 w-3.5 text-blue-500" />
    }
  }

  // Helper function to get platform label
  const getPlatformLabel = (platform: string = 'web') => {
    switch (platform?.toLowerCase()) {
      case 'android':
      case 'ios':
        return 'Mobile'
      case 'api':
        return 'API'
      case 'web':
      default:
        return 'Web'
    }
  }

  // Helper function to get steps count
  const getStepsCount = (scenario: any) => {
    if (scenario.stepsCount !== undefined) return scenario.stepsCount
    if (scenario.steps?.total) return scenario.steps.total
    if (scenario.steps?.length) return scenario.steps.length
    return 0
  }

  // Handle folder selection
  const handleFolderSelect = useCallback((folderId: string | null) => {
    setActiveFolder(folderId)
  }, [])

  // Handle scenario toggle
  const handleToggleScenario = useCallback((scenarioId: string) => {
    if (selectedScenarios.includes(scenarioId)) {
      setSelectedScenarios(selectedScenarios.filter(id => id !== scenarioId))
    } else {
      setSelectedScenarios([...selectedScenarios, scenarioId])
    }

    // Call the external toggle handler if provided
    if (onToggleScenario) {
      onToggleScenario(scenarioId)
    }
  }, [selectedScenarios, setSelectedScenarios, onToggleScenario])

  // Handle select/deselect all filtered scenarios
  const handleToggleAllFilteredScenarios = useCallback(() => {
    if (areAllFilteredScenariosSelected) {
      // Deselect all filtered scenarios
      const filteredScenarioIds = filteredScenarios.map(s => s.id);
      setSelectedScenarios(selectedScenarios.filter(id => !filteredScenarioIds.includes(id)));
    } else {
      // Select all filtered scenarios
      const filteredScenarioIds = filteredScenarios.map(s => s.id);
      const newSelection = [...new Set([...selectedScenarios, ...filteredScenarioIds])];
      setSelectedScenarios(newSelection);
    }
  }, [areAllFilteredScenariosSelected, filteredScenarios, selectedScenarios, setSelectedScenarios]);

  // Handle reordering of selected scenarios
  const handleReorderScenarios = useCallback((newOrder: string[]) => {
    setSelectedScenarios(newOrder)
  }, [setSelectedScenarios])

  // Handle removing a scenario from selection
  const handleRemoveScenario = useCallback((scenarioId: string) => {
    setSelectedScenarios(selectedScenarios.filter(id => id !== scenarioId))
  }, [selectedScenarios, setSelectedScenarios])

  // Handle clearing all selected scenarios
  const handleClearSelection = useCallback(() => {
    setSelectedScenarios([])
  }, [setSelectedScenarios])

  // Handle clearing all filters
  const handleClearFilters = useCallback(() => {
    setSearchQuery("")
    setTagFilter(null)
    setPlatformFilter(null)
  }, [])

  // Use a ref to track if we've already updated the details to prevent unnecessary updates
  const updatedDetailsRef = useRef(false);

  // Update selected scenarios details when selection changes
  useEffect(() => {
    // Skip if we don't have scenarios data yet
    if (!fetchedScenarios || fetchedScenarios.length === 0) {
      return;
    }

    // Create a stable version of the details
    const selectedScenariosDetails = selectedScenarios
      .map(id => {
        const scenario = fetchedScenarios.find(s => s.id === id);
        if (!scenario) return null;

        return {
          id: scenario.id,
          name: scenario.name || '',
          description: scenario.description || '',
          steps: scenario.steps || []
        };
      })
      .filter(Boolean);

    // Only update if we have valid details
    if (selectedScenariosDetails.length > 0 || selectedScenarios.length === 0) {
      updateSelectedScenariosDetails(selectedScenariosDetails);
      updatedDetailsRef.current = true;
    }
  }, [selectedScenarios, fetchedScenarios])

  return (
    <Card className="overflow-hidden border-0 shadow-md">
      <CardHeader className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/40 dark:to-indigo-950/40 border-b">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 h-10">
            <TabsTrigger value="browse" className="text-sm font-medium flex items-center justify-center gap-2">
              <LayoutGrid className="h-4 w-4" />
              Browse Scenarios
            </TabsTrigger>
            <TabsTrigger value="selected" className="text-sm font-medium flex items-center justify-center gap-2">
              <CheckSquare className="h-4 w-4" />
              Re-Order Scenarios ({selectedScenarios.length})
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} className="w-full">
          {/* Browse Scenarios Tab */}
          <TabsContent value="browse" className="m-0">
            {/* Global Filter & Search */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-800/30">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Filter &amp; Search</h3>
              <ScenarioSearch
                onSearch={setSearchQuery}
                onFilterByTag={setTagFilter}
                onFilterByPlatform={setPlatformFilter}
                availableTags={availableTags}
                activeTagFilter={tagFilter}
                activePlatformFilter={platformFilter}
                onClearFilters={handleClearFilters}
              />
            </div>

            {/* Main Grid */}
            <div className="grid grid-cols-12 h-[600px]">
              {/* Left sidebar - Folders */}
              <div className="col-span-3 border-r border-gray-200 dark:border-gray-800 h-full overflow-y-auto">
                <FolderNavigation
                  folders={fetchedFolders}
                  uncategorizedCount={uncategorizedScenarios.length}
                  allScenariosCount={fetchedScenarios.length}
                  folderScenarioCounts={folderScenarioCounts}
                  onFolderSelect={handleFolderSelect}
                  selectedFolder={activeFolder}
                  isLoading={foldersLoading}
                />
              </div>

              {/* Right content - Scenarios */}
              <div className="col-span-9 h-full flex flex-col overflow-y-auto">
                {/* Modern Table Layout with Scrolling */}
                <div className="h-full overflow-auto">
                  {scenariosLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    </div>
                  ) : filteredScenarios.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                      <ListFilter className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">No scenarios found</p>
                      <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                        {searchQuery || tagFilter || platformFilter
                          ? "Try adjusting your search or filters"
                          : "Add scenarios to this folder to see them here"}
                      </p>
                      {(searchQuery || tagFilter || platformFilter) && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-4"
                          onClick={handleClearFilters}
                        >
                          Clear All Filters
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="h-full overflow-auto">
                      <table className="w-full caption-bottom text-sm table-auto">
                        <thead className="bg-gray-50 dark:bg-gray-800/50 sticky top-0 [&_tr]:border-b">
                          <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-10 text-center">
                              <Checkbox
                                checked={areAllFilteredScenariosSelected}
                                onCheckedChange={handleToggleAllFilteredScenarios}
                                className={cn(
                                  "h-4 w-4 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500",
                                  areSomeFilteredScenariosSelected && !areAllFilteredScenariosSelected && "data-[state=checked]:bg-blue-400"
                                )}
                                aria-label="Select all scenarios"
                              />
                            </th>
                            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[250px]">Name</th>
                            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[100px]">Platform</th>
                            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[150px]">Tags</th>
                            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[150px]">Last Updated</th>
                          </tr>
                        </thead>
                        <tbody className="[&_tr:last-child]:border-0" style={{minHeight: '500px'}}>
                          {filteredScenarios.map((scenario) => (
                            <tr
                              key={scenario.id}
                              className={`border-b transition-colors cursor-pointer ${selectedScenarios.includes(scenario.id) ?
                                "bg-blue-50 dark:bg-blue-900/20" :
                                "hover:bg-gray-50 dark:hover:bg-gray-800/50"
                              }`}
                              onClick={() => handleToggleScenario(scenario.id)}
                            >
                              <td className="p-4 align-middle text-center">
                                <Checkbox
                                  checked={selectedScenarios.includes(scenario.id)}
                                  onCheckedChange={() => handleToggleScenario(scenario.id)}
                                  className="h-4 w-4 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                                />
                              </td>
                              <td className="p-4 align-middle">
                                <div className="font-medium">{scenario.name || 'Unnamed Scenario'}</div>
                                {scenario.description && (
                                  <div className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1">
                                    {scenario.description}
                                  </div>
                                )}
                              </td>
                              <td className="p-4 align-middle">
                                <div className="flex items-center gap-1.5">
                                  {getPlatformIcon(scenario.platform)}
                                  <span>{getPlatformLabel(scenario.platform)}</span>
                                </div>
                              </td>
                              <td className="p-4 align-middle">
                                {scenario.tags && scenario.tags.length > 0 ? (
                                  <div className="flex flex-wrap gap-1">
                                    {scenario.tags.slice(0, 2).map((tag) => (
                                      <Badge key={tag} variant="outline" className="text-xs px-1.5 py-0 h-5">
                                        {tag}
                                      </Badge>
                                    ))}
                                    {scenario.tags.length > 2 && (
                                      <Badge variant="outline" className="text-xs px-1.5 py-0 h-5">
                                        +{scenario.tags.length - 2}
                                      </Badge>
                                    )}
                                  </div>
                                ) : (
                                  <span className="text-gray-400 text-xs">No tags</span>
                                )}
                              </td>
                              <td className="p-4 align-middle">
                                <div className="flex items-center gap-1.5 text-xs text-gray-500">
                                  <Clock className="h-3.5 w-3.5" />
                                  <span>{scenario.updatedAt ? formatDistanceToNow(new Date(scenario.updatedAt), { addSuffix: true }) : 'Unknown'}</span>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Selected Scenarios Tab */}
          <TabsContent value="selected" className="m-0">
            <div className="h-[600px] overflow-hidden">
              {selectedScenarios.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                  <ListFilter className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">No scenarios selected</p>
                  <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                    Select scenarios from the Browse tab to add them to your test run
                  </p>
                </div>
              ) : (
                <SelectedScenariosList
                  selectedScenarios={selectedScenarios}
                  scenariosData={fetchedScenarios as any}
                  onReorder={handleReorderScenarios}
                  onRemove={handleRemoveScenario}
                  onClear={handleClearSelection}
                />
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
