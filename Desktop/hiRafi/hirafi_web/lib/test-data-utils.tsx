import type { CustomEnvironment, EnvironmentType } from "@/types/test-data"
import { Database, Globe, FileText, Edit, FileSpreadsheet, Code, Hand } from "lucide-react"

/**
 * Get environment color class based on environment type
 */
export function getEnvironmentColor(environment: EnvironmentType, customEnvironments: CustomEnvironment[]): string {
  const env = customEnvironments.find(e => e.id === environment)

  if (env) {
    return `border-${env.color}-200 bg-${env.color}-50 text-${env.color}-700 dark:border-${env.color}-800/30 dark:bg-${env.color}-900/20 dark:text-${env.color}-300`
  }

  // Default colors for standard environments
  const defaultColors: Record<string, string> = {
    development: "border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800/30 dark:bg-blue-900/20 dark:text-blue-300",
    testing: "border-purple-200 bg-purple-50 text-purple-700 dark:border-purple-800/30 dark:bg-purple-900/20 dark:text-purple-300",
    staging: "border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800/30 dark:bg-yellow-900/20 dark:text-yellow-300",
    production: "border-red-200 bg-red-50 text-red-700 dark:border-red-800/30 dark:bg-red-900/20 dark:text-red-300",
  }

  return defaultColors[environment] || "border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-800/30 dark:bg-gray-900/20 dark:text-gray-300"
}

/**
 * Get environment label/name
 */
export function getEnvironmentLabel(environment: EnvironmentType, customEnvironments: CustomEnvironment[]): string {
  const env = customEnvironments.find(e => e.id === environment)

  if (env) {
    return env.name
  }

  // Default labels for standard environments
  const defaultLabels: Record<string, string> = {
    development: "Development",
    testing: "Testing",
    staging: "Staging",
    production: "Production",
    all: "All Environments"
  }

  return defaultLabels[environment] || environment
}

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  if (!dateString) {
    return "Unknown";
  }

  try {
    const date = new Date(dateString);

    // Validate the date
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string received:', dateString);
      return "Unknown";
    }

    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) {
      return "Just now";
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  } catch (error) {
    console.error('Error formatting date:', error, 'Input:', dateString);
    return "Unknown";
  }
}

/**
 * Get data source type label
 */
export function getDataSourceTypeLabel(type: string): string {
  switch (type) {
    case "database":
      return "Veritabanı"
    case "api":
      return "API"
    case "file":
      return "Dosya"
    case "manual":
      return "Manuel"
    case "csv":
      return "CSV"
    case "json":
      return "JSON"
    case "excel":
      return "Excel"
    default:
      return "Bilinmeyen"
  }
}

/**
 * Get data source type color
 */
export function getDataSourceTypeColor(type: string): string {
  const typeColors: Record<string, string> = {
    database: "border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800/30 dark:bg-blue-900/20 dark:text-blue-300",
    api: "border-green-200 bg-green-50 text-green-700 dark:border-green-800/30 dark:bg-green-900/20 dark:text-green-300",
    file: "border-orange-200 bg-orange-50 text-orange-700 dark:border-orange-800/30 dark:bg-orange-900/20 dark:text-orange-300",
    manual: "border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-800/30 dark:bg-gray-900/20 dark:text-gray-300"
  }

  return typeColors[type] || "border-gray-200 bg-gray-50 text-gray-700 dark:border-gray-800/30 dark:bg-gray-900/20 dark:text-gray-300"
}

/**
 * Get data source type icon
 */
export function getDataSourceIcon(type: string, iconProps: any = { className: "w-4 h-4" }) {
  switch (type) {
    case "database":
      return <Database {...iconProps} />
    case "api":
      return <Globe {...iconProps} />
    case "file":
      return <FileText {...iconProps} />
    case "manual":
      return <Hand {...iconProps} />
    case "csv":
      return <FileText {...iconProps} />
    case "json":
      return <Code {...iconProps} />
    case "excel":
      return <FileSpreadsheet {...iconProps} />
    default:
      return <FileText {...iconProps} />
  }
}
