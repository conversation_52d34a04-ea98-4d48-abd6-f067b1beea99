"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Search, Filter, X, ChevronDown, Smartphone, Globe, ServerIcon, FolderIcon } from "lucide-react"
import type { FolderType } from "@/types/scenario"

interface TableFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: string | null
  onStatusFilterChange: (status: string | null) => void
  testTypeFilter: string | null
  onTestTypeFilterChange: (testType: string | null) => void
  tagFilter: string | null
  onTagFilterChange: (tag: string | null) => void
  folders: FolderType[]
  selectedFolder: string | null
  onSelectFolder: (folderId: string | null) => void
  onClearFilters: () => void
  availableTags: string[]
}

export function TableFilters({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  testTypeFilter,
  onTestTypeFilterChange,
  tagFilter,
  onTagFilterChange,
  folders,
  selectedFolder,
  onSelectFolder,
  onClearFilters,
  availableTags
}: TableFiltersProps) {


  // Helper to get folder name
  const getSelectedFolderName = () => {
    if (!selectedFolder) return "All Folders"
    const folder = folders.find(f => f.id === selectedFolder)
    return folder?.name || "Uncategorized"
  }

  // Check if any filters are active
  const hasActiveFilters = !!(searchQuery || statusFilter || tagFilter || testTypeFilter || selectedFolder)

  return (
    <div className="space-y-2 mb-3">
      {/* Main filters row */}
      <div className="flex flex-wrap items-center gap-2">
        <div className="relative flex-1 min-w-[300px]">
          <div className="flex items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
              <div className={`flex items-center flex-wrap gap-1 pl-9 pr-4 py-1 h-auto min-h-9 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md ${hasActiveFilters ? 'pt-1.5' : ''}`}>
                <Input
                  placeholder="Search scenarios..."
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="border-0 p-0 h-6 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-sm flex-1 min-w-[150px]"
                />

            {/* Inline filter badges */}
            {selectedFolder && (
              <Badge className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800 py-0.5 px-2 h-6 text-xs font-normal">
                <div className="flex items-center">
                  <FolderIcon
                    className="h-3 w-3 mr-1"
                    style={{
                      color: folders.find(f => f.id === selectedFolder)?.color || '#6b7280'
                    }}
                  />
                  <span className="font-medium truncate max-w-[80px]">
                    {getSelectedFolderName()}
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 ml-1 hover:bg-blue-100 dark:hover:bg-blue-900/50 p-0"
                    onClick={() => onSelectFolder(null)}
                  >
                    <X className="h-2.5 w-2.5" />
                  </Button>
                </div>
              </Badge>
            )}

            {statusFilter && (
              <Badge className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800 py-0.5 px-2 h-6 text-xs font-normal">
                <div className="flex items-center">
                  <span className="font-medium">{statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 ml-1 hover:bg-green-100 dark:hover:bg-green-900/50 p-0"
                    onClick={() => onStatusFilterChange(null)}
                  >
                    <X className="h-2.5 w-2.5" />
                  </Button>
                </div>
              </Badge>
            )}

            {testTypeFilter && (
              <Badge className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800 py-0.5 px-2 h-6 text-xs font-normal">
                <div className="flex items-center">
                  {testTypeFilter === "web" && <Globe className="h-2.5 w-2.5 mr-1" />}
                  {testTypeFilter === "mobile" && <Smartphone className="h-2.5 w-2.5 mr-1" />}
                  {testTypeFilter === "api" && <ServerIcon className="h-2.5 w-2.5 mr-1" />}
                  <span className="font-medium">{testTypeFilter.charAt(0).toUpperCase() + testTypeFilter.slice(1)}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 ml-1 hover:bg-purple-100 dark:hover:bg-purple-900/50 p-0"
                    onClick={() => onTestTypeFilterChange(null)}
                  >
                    <X className="h-2.5 w-2.5" />
                  </Button>
                </div>
              </Badge>
            )}

            {tagFilter && (
              <Badge className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800 py-0.5 px-2 h-6 text-xs font-normal">
                <div className="flex items-center">
                  <span className="font-medium">{tagFilter}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 ml-1 hover:bg-amber-100 dark:hover:bg-amber-900/50 p-0"
                    onClick={() => onTagFilterChange(null)}
                  >
                    <X className="h-2.5 w-2.5" />
                  </Button>
                </div>
              </Badge>
            )}

            {(searchQuery || hasActiveFilters) && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 p-0 ml-auto hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
                onClick={onClearFilters}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
              </div>
            </div>

            {/* Filter buttons next to search input */}
            <div className="flex items-center gap-1 ml-2">
              {/* Test Type filter button */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                  >
                    {testTypeFilter ? (
                      <span className="flex items-center">
                        {testTypeFilter === "web" && <Globe className="h-3.5 w-3.5 mr-1" />}
                        {testTypeFilter === "mobile" && <Smartphone className="h-3.5 w-3.5 mr-1" />}
                        {testTypeFilter === "api" && <ServerIcon className="h-3.5 w-3.5 mr-1" />}
                        <span className="font-medium">{testTypeFilter.charAt(0).toUpperCase() + testTypeFilter.slice(1)}</span>
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Filter className="h-3.5 w-3.5 mr-1" />
                        Tip
                      </span>
                    )}
                    <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px] dark:bg-gray-800 dark:border-gray-700">
                  <DropdownMenuItem onClick={() => onTestTypeFilterChange(null)} className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
                    All Types
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="dark:bg-gray-700" />
                  <DropdownMenuItem onClick={() => onTestTypeFilterChange("web")} className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
                    <Globe className="h-3.5 w-3.5 mr-2" />
                    Web
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onTestTypeFilterChange("mobile")} className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
                    <Smartphone className="h-3.5 w-3.5 mr-2" />
                    Mobile
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onTestTypeFilterChange("api")} className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
                    <ServerIcon className="h-3.5 w-3.5 mr-2" />
                    API
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Status filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                  >
                    {statusFilter ? (
                      <span className="flex items-center">
                        <span className="font-medium">{statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}</span>
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Filter className="h-3.5 w-3.5 mr-1" />
                        Durum
                      </span>
                    )}
                    <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px] dark:bg-gray-800 dark:border-gray-700">
                  <DropdownMenuItem
                    onClick={() => {
                      console.log("Setting status filter to null");
                      onStatusFilterChange(null);
                    }}
                    className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                  >
                    All Statuses
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="dark:bg-gray-700" />
                  <DropdownMenuItem
                    onClick={() => {
                      console.log("Setting status filter to 'passed'");
                      onStatusFilterChange("passed");
                    }}
                    className="text-sm py-1.5 text-green-600 dark:text-green-400 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                  >
                    Passed
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      console.log("Setting status filter to 'failed'");
                      onStatusFilterChange("failed");
                    }}
                    className="text-sm py-1.5 text-red-600 dark:text-red-400 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                  >
                    Failed
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      console.log("Setting status filter to 'active'");
                      onStatusFilterChange("active");
                    }}
                    className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                  >
                    Active
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      console.log("Setting status filter to 'inactive'");
                      onStatusFilterChange("inactive");
                    }}
                    className="text-sm py-1.5 text-gray-500 dark:text-gray-400 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                  >
                    Inactive
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Folder filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                  >
                    {selectedFolder ? (
                      <span className="flex items-center">
                        <FolderIcon className="h-3.5 w-3.5 mr-1" style={{ color: folders.find(f => f.id === selectedFolder)?.color || '#6b7280' }} />
                        <span className="font-medium truncate max-w-[80px]">{getSelectedFolderName()}</span>
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Filter className="h-3.5 w-3.5 mr-1" />
                        Klasör
                      </span>
                    )}
                    <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[220px] max-h-[400px] overflow-y-auto dark:bg-gray-800 dark:border-gray-700">
                  <DropdownMenuItem onClick={() => onSelectFolder(null)} className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
                    All Folders
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="dark:bg-gray-700" />
                  {folders.map((folder) => (
                    <DropdownMenuItem
                      key={folder.id}
                      onClick={() => onSelectFolder(folder.id)}
                      className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                    >
                      <div className="flex items-center w-full">
                        <FolderIcon
                          className="h-4 w-4 mr-2"
                          style={{ color: folder.color || '#6b7280' }}
                        />
                        <span className="truncate text-xs">
                          {folder.name}
                        </span>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Tags filter */}
              {availableTags.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-9 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-sm text-gray-700 dark:text-gray-300 font-normal"
                    >
                      {tagFilter ? (
                        <span className="flex items-center">
                          <span className="font-medium">{tagFilter}</span>
                        </span>
                      ) : (
                        <span className="flex items-center">
                          <Filter className="h-3.5 w-3.5 mr-1" />
                          Etiket
                        </span>
                      )}
                      <ChevronDown className="ml-2 h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[180px] dark:bg-gray-800 dark:border-gray-700">
                    <DropdownMenuItem onClick={() => onTagFilterChange(null)} className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700">
                      All Tags
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="dark:bg-gray-700" />
                    {availableTags.map((tag) => (
                      <DropdownMenuItem
                        key={tag}
                        onClick={() => onTagFilterChange(tag)}
                        className="text-sm py-1.5 dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                      >
                        {tag}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </div>


      </div>
    </div>
  )
}
