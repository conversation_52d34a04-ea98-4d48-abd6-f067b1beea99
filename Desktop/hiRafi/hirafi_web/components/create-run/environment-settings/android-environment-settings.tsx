"use client"

import { useState, useEffect } from "react"
import { useRunForm } from "../run-context"
import { useAIModels } from "@/hooks/useAIModels"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Smartphone,
  Settings,
  FileText,
  Layers,
  Share2
} from "lucide-react"

// Import tab components
import { DevicesTab } from "./tabs/devices-tab"
import { AppsTab } from "./tabs/apps-tab"
import { AdvancedTab } from "./tabs/advanced-tab"
import { AIModelTab } from "./tabs/ai-model-tab"
import { TestDistributionTab } from "./tabs/test-distribution-tab"

// Import report settings
import { ReportSettings } from "../report-settings"
import { DeviceSelectionModal } from "./device-selection-modal"
import { EnvironmentSettings } from "../run-context"

// Import standardized Android environment types
import {
  AndroidEnvironmentSettings as AndroidSettings,
  AndroidDevice,
  <PERSON>uceLabsConfig,
  AndroidApp
} from "@/types/android-environment"

export function AndroidEnvironmentSettings() {
  const {
    environmentSettings,
    updateEnvironmentSettings,
    errors,
    testDataEnvironments,
    selectedTestDataEnvironment,
    isLoadingTestDataEnvironments,
    loadTestDataEnvironments,
    selectTestDataEnvironment
  } = useRunForm()
  const [activeTab, setActiveTab] = useState("devices")

  // Type guard to check if environmentSettings is AndroidSettings
  const isAndroidSettings = (settings: EnvironmentSettings): settings is AndroidSettings => {
    return settings.platform === 'android';
  }

  // Get the Android-specific settings
  const getAndroidSettings = (): AndroidSettings => {
    if (isAndroidSettings(environmentSettings)) {
      return environmentSettings;
    }

    // If not Android settings, return a default Android settings object
    return {
      platform: 'android',
      sauceLabs: {
        username: '',
        accessKey: '',
        selectedDevices: []
      }
    };
  }

  // We're using the ModifiedEnvironmentSettings interface defined at the top of the file

  // Removed AppiumSettings interface as it's no longer needed

  // ProxySettings is imported from run-context.tsx

  // TestDistributionSettings is used in the TestDistributionTab component

  // Removed EnvironmentSettings interface as it's not needed here

  const { models, isLoading } = useAIModels()
  const [isDeviceModalOpen, setIsDeviceModalOpen] = useState(false)

  // Device provider state - enforce mutual exclusivity
  const [activeProviders, setActiveProviders] = useState<{
    sauceLabs: boolean;
    testinium: boolean;
  }>({
    sauceLabs: true, // Default to SauceLabs as the active provider
    testinium: false, // Mutually exclusive with SauceLabs
  })

  // Set default AI model when models are loaded and initialize device provider
  useEffect(() => {
    if (!isLoading && models.length > 0 && (!environmentSettings.aiModel || environmentSettings.aiModel === "default")) {
      console.log("Setting default AI model for android platform:", models[0].id, models[0].name)

      // Initialize with default device provider if not set
      const updates: any = {
        aiModel: models[0].id,
        aiModelName: models[0].name
      };

      // Set default device provider if not already set
      if (isAndroidSettings(environmentSettings) && !environmentSettings.deviceProvider) {
        updates.deviceProvider = 'sauceLabs';
      }

      updateEnvironmentSettings(updates);
    }
  }, [isLoading, models, environmentSettings.aiModel, updateEnvironmentSettings])

  // Load test data environments when component mounts
  useEffect(() => {
    if (testDataEnvironments.length === 0 && !isLoadingTestDataEnvironments) {
      loadTestDataEnvironments();
    }
  }, []) // Sadece component mount olduğunda çalışsın

  // Device loading is now handled by the device selection modal

  // Handle provider toggle with mutual exclusivity
  const handleProviderToggle = (provider: 'sauceLabs' | 'testinium', checked: boolean) => {
    if (!checked) {
      // Don't allow disabling if it's the only active provider
      const otherProvider = provider === 'sauceLabs' ? 'testinium' : 'sauceLabs';
      if (!activeProviders[otherProvider]) {
        return; // Prevent disabling the only active provider
      }
    }

    // Update active providers with mutual exclusivity
    const newState = checked ? {
      sauceLabs: provider === 'sauceLabs',
      testinium: provider === 'testinium'
    } : {
      ...activeProviders,
      [provider]: false
    };

    setActiveProviders(newState);

    // Update environment settings if Android
    if (isAndroidSettings(environmentSettings) && checked) {
      const updatedSettings = {
        ...environmentSettings,
        deviceProvider: provider,
        // Clear devices from non-selected provider
        sauceLabs: provider === 'sauceLabs' ? environmentSettings.sauceLabs : {
          ...environmentSettings.sauceLabs,
          selectedDevices: []
        },
        testinium: provider === 'testinium' ? environmentSettings.testinium : {
          ...environmentSettings.testinium,
          selectedDevices: []
        }
      };
      updateEnvironmentSettings(updatedSettings);
    }
  }

  // Removed openDeviceSelectionModal function as it's not needed

  // Handle device selection from modal
  const handleDeviceSelection = (selectedDevices: any[]) => {
    if (selectedDevices.length === 0) return

    // Get current Android settings or create default ones
    const androidSettings = getAndroidSettings();

    // Convert selected devices to AndroidDevice type
    const typedSelectedDevices: AndroidDevice[] = selectedDevices.map(d => ({
      id: d.id,
      name: d.name,
      osVersion: d.osVersion,
      provider: d.provider || 'sauceLabs',
      deviceType: d.deviceType || 'phone',
      manufacturer: d.manufacturer,
      // Include additional device details if available
      apiLevel: d.apiLevel,
      screenResolution: d.screenResolution,
      screenSize: d.screenSize,
      ram: d.ram,
      modelNumber: d.modelNumber
    }));

    // Create updated SauceLabs config
    const updatedSauceLabsConfig: SauceLabsConfig = {
      ...androidSettings.sauceLabs,
      selectedDevices: typedSelectedDevices
    };

    // Create new Android settings
    const newSettings: AndroidSettings = {
      ...androidSettings,
      platform: 'android',
      sauceLabs: updatedSauceLabsConfig
    };

    // Update the environment settings
    updateEnvironmentSettings(newSettings);
  }

  // We've removed all the unused functions to clean up the code

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-5 mb-4">
          <TabsTrigger value="devices" className="flex items-center gap-2">
            <Smartphone className="h-4 w-4" />
            Cihazlar
          </TabsTrigger>
          <TabsTrigger value="distribution" className="flex items-center gap-2">
            <Share2 className="h-4 w-4" />
            Test Dağıtımı
          </TabsTrigger>
          <TabsTrigger value="apps" className="flex items-center gap-2">
            <Layers className="h-4 w-4" />
            Uygulamalar
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Rapor Ayarları
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Gelişmiş
          </TabsTrigger>
        </TabsList>

        <TabsContent value="devices" className="space-y-4">
          <DevicesTab
            environmentSettings={environmentSettings}
            updateEnvironmentSettings={updateEnvironmentSettings}
            activeProviders={activeProviders}
            handleProviderToggle={handleProviderToggle}
          />
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <TestDistributionTab
            environmentSettings={environmentSettings}
            updateEnvironmentSettings={updateEnvironmentSettings}
          />
        </TabsContent>

        <TabsContent value="apps" className="space-y-4">
          <AppsTab
            environmentSettings={environmentSettings}
            updateEnvironmentSettings={updateEnvironmentSettings}
            activeProviders={activeProviders}
          />
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-md p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
            <ReportSettings />
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="grid grid-cols-1 gap-6">
            <AIModelTab
              environmentSettings={environmentSettings}
              updateEnvironmentSettings={updateEnvironmentSettings}
              errors={errors}
            />
            <AdvancedTab
              environmentSettings={environmentSettings}
              updateEnvironmentSettings={updateEnvironmentSettings}
            />
          </div>
        </TabsContent>
      </Tabs>

      {/* Device Selection Modal - Keep this outside the tabs */}
      <DeviceSelectionModal
        isOpen={isDeviceModalOpen}
        onClose={() => setIsDeviceModalOpen(false)}
        onSave={handleDeviceSelection}
        initialSelectedDevices={isAndroidSettings(environmentSettings) ?
          environmentSettings.sauceLabs.selectedDevices as any[] : []}
      />
    </div>
  )
}
