/**
 * Heartbeat Service
 * 
 * Handles heartbeat logic for all connector types with dependency injection.
 * Replaces the singleton HeartbeatService with a proper service implementation.
 */

import { BaseService } from '../base/base-service.js';
import { IHeartbeatService, IHeartbeatConfig, IHeartbeatData, IHeartbeatStats } from '../../interfaces/heartbeat-service.interface.js';
import { IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';

/**
 * Heartbeat Service Class
 * Provides common heartbeat functionality with dependency injection
 */
export class HeartbeatService extends BaseService implements IHeartbeatService {
  private heartbeatIntervals: Map<string, NodeJS.Timeout> = new Map();
  private heartbeatStats: Map<string, any> = new Map();
  private defaultInterval = 30000; // 30 seconds
  private heartbeatConfig: IHeartbeatConfig = {};

  constructor(logger: any, config: IServiceConfig = {}) {
    super('HeartbeatService', logger, config);
    this.heartbeatConfig = {
      defaultInterval: 30000,
      maxRetries: 3,
      retryDelay: 5000,
      enableLogging: false,
      ...config
    };
  }

  /**
   * Initialize the heartbeat service
   * @param config Service configuration
   */
  protected async doInitialize(config: IServiceConfig): Promise<void> {
    this.heartbeatConfig = { ...this.heartbeatConfig, ...config };
    this.defaultInterval = this.heartbeatConfig.defaultInterval || 30000;

    this.logger.debug('HeartbeatService: Initialized with config', this.heartbeatConfig);
  }

  /**
   * Close the heartbeat service
   */
  protected async doClose(): Promise<void> {
    this.stopAllHeartbeats();
    this.heartbeatStats.clear();
    this.logger.debug('HeartbeatService: Closed successfully');
  }

  /**
   * Start heartbeat for a specific connector
   * @param connectorId Connector identifier
   * @param heartbeatFunction Function to call for heartbeat
   * @param interval Heartbeat interval in milliseconds
   */
  startHeartbeat(
    connectorId: string,
    heartbeatFunction: () => Promise<boolean>,
    interval?: number
  ): void {
    // Stop existing heartbeat if any
    this.stopHeartbeat(connectorId);

    const heartbeatInterval = interval || this.defaultInterval;

    // Initialize stats for this connector
    this.heartbeatStats.set(connectorId, {
      interval: heartbeatInterval,
      lastSent: null,
      successCount: 0,
      failureCount: 0,
      startedAt: new Date().toISOString()
    });

    const intervalId = setInterval(async () => {
      try {
        const success = await heartbeatFunction();
        const stats = this.heartbeatStats.get(connectorId);
        
        if (stats) {
          stats.lastSent = new Date().toISOString();
          if (success) {
            stats.successCount++;
          } else {
            stats.failureCount++;
          }
        }

        this.logOperation('heartbeat', { connectorId, success });
      } catch (error) {
        const stats = this.heartbeatStats.get(connectorId);
        if (stats) {
          stats.failureCount++;
        }
        this.handleError(error, `heartbeat for ${connectorId}`);
      }
    }, heartbeatInterval);

    this.heartbeatIntervals.set(connectorId, intervalId);
    this.logger.info(`HeartbeatService: Started heartbeat for ${connectorId} with interval ${heartbeatInterval}ms`);
  }

  /**
   * Stop heartbeat for a specific connector
   * @param connectorId Connector identifier
   */
  stopHeartbeat(connectorId: string): void {
    const intervalId = this.heartbeatIntervals.get(connectorId);
    if (intervalId) {
      clearInterval(intervalId);
      this.heartbeatIntervals.delete(connectorId);
      this.logger.info(`HeartbeatService: Stopped heartbeat for ${connectorId}`);
    }
  }

  /**
   * Check if heartbeat is active for a connector
   * @param connectorId Connector identifier
   */
  isHeartbeatActive(connectorId: string): boolean {
    return this.heartbeatIntervals.has(connectorId);
  }

  /**
   * Get heartbeat interval for a connector
   * @param connectorId Connector identifier
   */
  getHeartbeatInterval(connectorId: string): number | null {
    const stats = this.heartbeatStats.get(connectorId);
    return stats ? stats.interval : null;
  }

  /**
   * Prepare heartbeat data
   * @param nodeId Node ID
   * @param status Current node status
   * @param additionalData Additional data to include
   */
  prepareHeartbeatData(
    nodeId: string,
    status: string,
    additionalData: Record<string, any> = {}
  ): IHeartbeatData {
    return {
      nodeId,
      status,
      timestamp: new Date().toISOString(),
      uptime: this.getUptime(),
      ...additionalData
    };
  }

  /**
   * Stop all heartbeats
   */
  stopAllHeartbeats(): void {
    for (const connectorId of this.heartbeatIntervals.keys()) {
      this.stopHeartbeat(connectorId);
    }
    this.logger.info('HeartbeatService: Stopped all heartbeats');
  }

  /**
   * Get active heartbeat connectors
   */
  getActiveHeartbeats(): string[] {
    return Array.from(this.heartbeatIntervals.keys());
  }

  /**
   * Get heartbeat statistics
   */
  getStats(): IServiceStats {
    return super.getStats();
  }

  /**
   * Get heartbeat-specific statistics
   */
  getHeartbeatStats(): IHeartbeatStats {
    const baseStats = super.getStats();
    
    let totalHeartbeatsSent = 0;
    let successfulHeartbeats = 0;
    let failedHeartbeats = 0;
    const connectorStats: Record<string, any> = {};

    for (const [connectorId, stats] of this.heartbeatStats) {
      totalHeartbeatsSent += stats.successCount + stats.failureCount;
      successfulHeartbeats += stats.successCount;
      failedHeartbeats += stats.failureCount;
      
      connectorStats[connectorId] = {
        interval: stats.interval,
        lastSent: stats.lastSent,
        successCount: stats.successCount,
        failureCount: stats.failureCount
      };
    }

    const averageResponseTime = totalHeartbeatsSent > 0 
      ? Math.round((successfulHeartbeats / totalHeartbeatsSent) * 100) / 100 
      : 0;

    return {
      ...baseStats,
      activeHeartbeats: this.heartbeatIntervals.size,
      totalHeartbeatsSent,
      successfulHeartbeats,
      failedHeartbeats,
      averageResponseTime,
      connectorStats
    };
  }
}
