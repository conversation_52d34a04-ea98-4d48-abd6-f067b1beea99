/**
 * Plugin API
 *
 * Eklentiler ile ilgili API isteklerini yönetir.
 */

import { get, post, del } from './fetch-wrapper';
import { PLUGIN_ENDPOINTS, TESTRAIL_ENDPOINTS, ZEPHYR_SCALE_ENDPOINTS, SAUCELABS_ENDPOINTS, TESTINIUM_ENDPOINTS, JIRA_ENDPOINTS, API_BASE_URL } from './constants';

/**
 * Tüm eklentileri getirir
 */
export async function getPlugins() {
  const response = await get(PLUGIN_ENDPOINTS.GET_ALL);

  // Backend artık data wrapper kullanıyor, geriye dönük uyumluluk için
  if (response.success && response.data) {
    return {
      ...response,
      plugins: response.data.plugins || response.plugins,
      count: response.data.count || response.count
    };
  }

  return response;
}

/**
 * Get all available test management integrations
 * Returns only configured integrations without error messages
 */
export async function getAvailableTestManagementIntegrations() {
  return get(PLUGIN_ENDPOINTS.GET_AVAILABLE_TEST_MANAGEMENT);
}

/**
 * <PERSON>irli bir eklentiyi getirir
 *
 * @param id Eklenti ID'si
 */
export async function getPluginById(id: string) {
  return get(PLUGIN_ENDPOINTS.GET_BY_ID(id));
}

/**
 * Bir eklentiyi yükler
 *
 * @param plugin Eklenti verileri
 */
export async function installPlugin(plugin: {
  name: string;
  url: string;
  version?: string;
  description?: string;
  teamId?: string;
  companyId?: string;
}) {
  return post(PLUGIN_ENDPOINTS.INSTALL, plugin, {
    showSuccessToast: true,
    successMessage: 'Eklenti başarıyla yüklendi'
  });
}

/**
 * Bir eklentiyi kaldırır
 *
 * @param id Eklenti ID'si
 */
export async function uninstallPlugin(id: string) {
  return del(PLUGIN_ENDPOINTS.UNINSTALL(id), {
    showSuccessToast: true,
    successMessage: 'Eklenti başarıyla kaldırıldı'
  });
}

/**
 * Bir eklentiyi aktifleştirir
 *
 * @param id Eklenti ID'si
 */
export async function activatePlugin(id: string) {
  return post(PLUGIN_ENDPOINTS.ACTIVATE(id), {}, {
    showSuccessToast: true,
    successMessage: 'Eklenti başarıyla aktifleştirildi'
  });
}

/**
 * Bir eklentiyi devre dışı bırakır
 *
 * @param id Eklenti ID'si
 */
export async function deactivatePlugin(id: string) {
  return post(PLUGIN_ENDPOINTS.DEACTIVATE(id), {}, {
    showSuccessToast: true,
    successMessage: 'Eklenti başarıyla devre dışı bırakıldı'
  });
}

/**
 * TestRail test case'lerini getirir
 *
 * @param params Filtreleme parametreleri (opsiyonel)
 */
export async function getTestRailCases(params?: {
  projectId?: string;
  suiteIds?: string[];
}) {
  const response = await post(TESTRAIL_ENDPOINTS.GET_CASES, params || {});

  // Backend artık data wrapper kullanıyor, geriye dönük uyumluluk için
  if (response.success && response.data) {
    return {
      ...response,
      cases: response.data.cases || response.cases,
      casesBySuite: response.data.casesBySuite || response.casesBySuite,
      totalCount: response.data.totalCount || response.totalCount,
      config: response.data.config || response.config,
      plugin: response.data.plugin || response.plugin
    };
  }

  return response;
}

/**
 * Sadece TestRail projelerini getirir (lazy loading için)
 */
export async function getTestRailProjectsOnly() {
  return post(TESTRAIL_ENDPOINTS.GET_PROJECTS_ONLY, {});
}

/**
 * Belirli bir suite için TestRail test case'lerini getirir (lazy loading için)
 *
 * @param projectId Proje ID'si
 * @param suiteId Suite ID'si
 */
export async function getTestRailCasesForSuite(projectId: string, suiteId: string) {
  return post(TESTRAIL_ENDPOINTS.GET_CASES_FOR_SUITE, {
    projectId,
    suiteId
  });
}

/**
 * TestRail entegrasyonunu keser (disconnect)
 */
export async function disconnectTestRail() {
  return del(TESTRAIL_ENDPOINTS.DISCONNECT);
}

/**
 * TestRail test case adımlarını getirir
 *
 * @param caseId Test case ID'si
 */
export async function getTestRailCaseSteps(caseId: string) {
  try {
    console.log("Calling TestRail API to get case steps for case ID:", caseId);
    const response = await post(TESTRAIL_ENDPOINTS.GET_CASE_STEPS, { caseId });
    console.log("TestRail API response in plugin-api.ts:", response);

    // Yanıt formatını kontrol et ve standardize et
    if (response.success) {
      // Eğer case property varsa, onu kullan
      if ('case' in response && response.case) {
        return {
          success: true,
          case: response.case,
          data: { caseData: response.case }
        };
      }
      // Eğer data property varsa, onu kullan
      else if ('data' in response && response.data) {
        return {
          success: true,
          case: response.data.caseData || response.data,
          data: response.data
        };
      }
      // Hiçbir veri yoksa, başarılı ama boş yanıt döndür
      else {
        console.warn("TestRail API returned success but no case data");
        return {
          success: true,
          case: null,
          data: null,
          message: "No case data found"
        };
      }
    }

    // Başarısız yanıt
    return {
      success: false,
      error: response.error || "Unknown error",
      case: null,
      data: null
    };
  } catch (error) {
    console.error("Error in getTestRailCaseSteps:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      case: null,
      data: null
    };
  }
}

/**
 * TestRail konfigürasyonunu getirir
 */
export async function getTestRailConfig() {
  const response = await get(TESTRAIL_ENDPOINTS.GET_CONFIG, {
    showErrorToast: false // Don't show error toast for unconfigured plugins
  });

  // Backend artık data wrapper kullanıyor, geriye dönük uyumluluk için
  if (response.success && response.data) {
    return {
      ...response,
      config: response.data.config || response.config,
      plugin: response.data.plugin || response.plugin,
      stats: response.data.stats || response.stats
    };
  }

  return response;
}

/**
 * TestRail projelerini getirir
 */
export async function getTestRailProjects() {
  return post(TESTRAIL_ENDPOINTS.GET_PROJECTS, {});
}

/**
 * TestRail suite'lerini getirir
 *
 * @param config Configuration with project IDs
 */
export async function getTestRailSuites(config: {
  projectIds: string[];
}) {
  const response = await post(TESTRAIL_ENDPOINTS.GET_SUITES, config);

  // Backend artık data wrapper kullanıyor, geriye dönük uyumluluk için
  if (response.success && response.data) {
    return {
      ...response,
      suites: response.data.suites || response.suites,
      suitesByProject: response.data.suitesByProject || response.suitesByProject
    };
  }

  return response;
}

/**
 * Belirli bir proje için TestRail suite'lerini getirir (optimized)
 *
 * @param projectId Proje ID'si
 */
export async function getTestRailSuitesForProject(projectId: string) {
  return post(TESTRAIL_ENDPOINTS.GET_SUITES_FOR_PROJECT, { projectId });
}

/**
 * TestRail konfigürasyonunu kaydeder
 *
 * @param config TestRail konfigürasyon verileri
 */
export async function saveTestRailConfig(config: {
  url: string;
  username: string;
  apiKey: string;
  projectsData: any[];
  suitesData: any[];
}) {
  return post(TESTRAIL_ENDPOINTS.SAVE_CONFIG, config);
}

/**
 * TestRail bağlantısını test eder
 *
 * @param config TestRail konfigürasyon verileri
 */
export async function testTestRailConnection(config: {
  url: string;
  email: string;
  apiKey: string;
}) {
  return post(TESTRAIL_ENDPOINTS.TEST_HEALTH, config);
}

/**
 * Test SauceLabs connection
 * @param params Connection parameters
 */
export async function testSauceLabsConnection(params: {
  username: string;
  accessKey: string;
  region?: string;
}) {
  return post(SAUCELABS_ENDPOINTS.TEST_CONNECTION, params);
}

/**
 * Update SauceLabs configuration
 * @param config SauceLabs configuration
 */
export async function updateSauceLabsConfig(config: {
  username: string;
  accessKey: string;
  region?: string;
}) {
  return post(SAUCELABS_ENDPOINTS.UPDATE_CONFIG, config);
}

/**
 * Get SauceLabs configuration
 */
export async function getSauceLabsConfig() {
  const response = await get(SAUCELABS_ENDPOINTS.GET_CONFIG, {
    showErrorToast: false // Don't show error toast for unconfigured plugins
  });

  // Backend response format: { success: true, config: {...}, plugin: {...} }
  // fetch-wrapper puts this in data wrapper, so we need to extract it
  if (response.success && response.data) {
    return {
      ...response,
      config: response.data.config || response.config,
      plugin: response.data.plugin || response.plugin
    };
  }

  return response;
}

/**
 * Get SauceLabs devices
 */
export async function getSauceLabsDevices() {
  try {
    const response = await get(SAUCELABS_ENDPOINTS.GET_DEVICES);

    // Backend response format: { success: true, devices: [...] }
    // fetch-wrapper puts this in data wrapper, so we need to extract it
    if (response.success && response.data) {
      return {
        ...response,
        devices: response.data.devices || response.devices || []
      };
    }

    return response;
  } catch (error) {
    console.error("Error in getSauceLabsDevices:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      devices: []
    };
  }
}

/**
 * Get SauceLabs account information
 */
export async function getSauceLabsAccountInfo() {
  const response = await get(SAUCELABS_ENDPOINTS.GET_ACCOUNT_INFO);

  // Backend response format: { success: true, data: {...} }
  // fetch-wrapper puts this in data wrapper, so we need to extract it
  if (response.success && response.data) {
    return {
      ...response,
      data: response.data.data || response.data
    };
  }

  return response;
}

/**
 * Get SauceLabs apps
 */
export async function getSauceLabsApps() {
  try {
    const response = await get(SAUCELABS_ENDPOINTS.GET_APPS);
    return response;
  } catch (error) {
    console.error("Error in getSauceLabsApps:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      apps: []
    };
  }
}

/**
 * Upload app to SauceLabs
 * @param formData Form data containing the app file
 */
export async function uploadSauceLabsApp(formData: FormData) {
  // Make sure the form data contains a file with the field name 'app'
  // This is required by the backend multer middleware
  if (!formData.has('app')) {
    throw new Error('Form data must contain a file with field name "app"');
  }

  return post(SAUCELABS_ENDPOINTS.UPLOAD_APP, formData, {
    isFormData: true,
    showSuccessToast: true,
    successMessage: 'App uploaded successfully'
  });
}

/**
 * Delete app from SauceLabs
 * @param id App ID
 */
export async function deleteSauceLabsApp(id: string) {
  return del(SAUCELABS_ENDPOINTS.DELETE_APP(id), {
    showSuccessToast: true,
    successMessage: 'App deleted successfully'
  });
}

/**
 * Disconnect SauceLabs integration
 */
export async function disconnectSauceLabs() {
  return del(SAUCELABS_ENDPOINTS.DISCONNECT, {
    showSuccessToast: true,
    successMessage: 'SauceLabs plugin ayarları başarıyla silindi'
  });
}

/**
 * Disconnect Testinium integration
 */
export async function disconnectTestinium() {
  return del(TESTINIUM_ENDPOINTS.DISCONNECT, {
    showSuccessToast: true,
    successMessage: 'Testinium plugin ayarları başarıyla silindi'
  });
}

/**
 * Delete a plugin completely
 * @param pluginName Plugin name (e.g., 'saucelabs', 'testinium')
 */
export async function deletePlugin(pluginName: string) {
  // For SauceLabs, use the specific disconnect endpoint
  if (pluginName === 'saucelabs') {
    return disconnectSauceLabs();
  }

  // For Testinium, use the specific disconnect endpoint
  if (pluginName === 'testinium') {
    return disconnectTestinium();
  }

  // For other plugins, use the generic delete endpoint
  return del(`/plugins/${pluginName}`, {
    showSuccessToast: true,
    successMessage: 'Plugin ayarları başarıyla silindi'
  });
}

/**
 * Get SauceLabs app groups
 */
export async function getSauceLabsAppGroups() {
  return get(SAUCELABS_ENDPOINTS.GET_APP_GROUPS);
}

/**
 * Update SauceLabs app settings
 * @param settings App settings
 */
export async function updateSauceLabsAppSettings(settings: any) {
  return post(SAUCELABS_ENDPOINTS.UPDATE_APP_SETTINGS, settings, {
    showSuccessToast: true,
    successMessage: 'App settings updated successfully'
  });
}

// ============================================================================
// TESTINIUM API FUNCTIONS
// ============================================================================

/**
 * Test Testinium connection
 * @param params Connection parameters
 */
export async function testTestiniumConnection(params: {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  issuerUri: string;
}) {
  return post(TESTINIUM_ENDPOINTS.TEST_CONNECTION, params);
}

/**
 * Update Testinium configuration
 * @param config Testinium configuration
 */
export async function updateTestiniumConfig(config: {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  issuerUri: string;
}) {
  return post(TESTINIUM_ENDPOINTS.UPDATE_CONFIG, config);
}

/**
 * Get Testinium configuration
 */
export async function getTestiniumConfig() {
  const response = await get(TESTINIUM_ENDPOINTS.GET_CONFIG, {
    showErrorToast: false // Don't show error toast for unconfigured plugins
  });

  // Backend response format: { success: true, config: {...}, plugin: {...} }
  // fetch-wrapper puts this in data wrapper, so we need to extract it
  if (response.success && response.data) {
    return {
      ...response,
      config: response.data.config || response.config,
      plugin: response.data.plugin || response.plugin
    };
  }

  return response;
}

/**
 * Get Testinium devices
 */
export async function getTestiniumDevices() {
  try {
    const response = await get(TESTINIUM_ENDPOINTS.GET_DEVICES);

    // Backend response format: { success: true, devices: [...] }
    // fetch-wrapper puts this in data wrapper, so we need to extract it
    if (response.success && response.data) {
      return {
        ...response,
        devices: response.data.devices || response.devices || []
      };
    }

    return response;
  } catch (error) {
    console.error("Error in getTestiniumDevices:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      devices: []
    };
  }
}

/**
 * Create device allocation
 * @param allocationRequest Allocation request
 */
export async function createTestiniumAllocation(allocationRequest: any) {
  return post(TESTINIUM_ENDPOINTS.ALLOCATE, { allocationRequest });
}

/**
 * Start device session
 * @param sessionRequest Session request
 */
export async function startTestiniumSession(sessionRequest: any) {
  return post(TESTINIUM_ENDPOINTS.START_SESSION, { sessionRequest });
}

/**
 * Close device session
 * @param sessionId Session ID
 */
export async function closeTestiniumSession(sessionId: string) {
  return post(TESTINIUM_ENDPOINTS.CLOSE_SESSION, { sessionId });
}

/**
 * Upload app to Testinium
 * @param formData Form data containing the app file
 */
export async function uploadTestiniumApp(formData: FormData) {
  // Make sure the form data contains a file with the field name 'app'
  // This is required by the backend multer middleware
  if (!formData.has('app')) {
    throw new Error('Form data must contain a file with field name "app"');
  }

  return post(TESTINIUM_ENDPOINTS.UPLOAD_APP, formData, {
    isFormData: true,
    showSuccessToast: true,
    successMessage: 'App uploaded successfully'
  });
}

/**
 * Get Testinium apps
 */
export async function getTestiniumApps() {
  const response = await get(TESTINIUM_ENDPOINTS.GET_APPS);

  // Backend response format: { success: true, apps: [...] }
  // fetch-wrapper puts this in data wrapper, so we need to extract it
  if (response.success && response.data) {
    return {
      ...response,
      apps: response.data.apps || response.apps || []
    };
  }

  return response;
}

/**
 * Delete app from Testinium
 * @param id App ID
 */
export async function deleteTestiniumApp(id: string) {
  return del(TESTINIUM_ENDPOINTS.DELETE_APP(id), {
    showSuccessToast: true,
    successMessage: 'App deleted successfully'
  });
}

/**
 * Download app from Testinium
 * @param id App ID
 */
export async function downloadTestiniumApp(id: string) {
  // For download, we need to handle the response differently
  // This will return the blob data for download
  const apiUrl = `${API_BASE_URL}${TESTINIUM_ENDPOINTS.DOWNLOAD_APP(id)}`;
  const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

  const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  if (!response.ok) {
    throw new Error('Failed to download app');
  }

  return response;
}

// ============================================================================
// JIRA API FUNCTIONS
// ============================================================================

/**
 * Test Jira connection
 * @param params Connection parameters
 */
export async function testJiraConnection(params: {
  url: string;
  email: string;
  apiToken: string;
}) {
  return post(JIRA_ENDPOINTS.TEST_CONNECTION, params);
}

/**
 * Get Jira projects
 * @param config Jira configuration
 */
export async function getJiraProjects(config?: {
  url?: string;
  email?: string;
  apiToken?: string;
}) {
  return post(JIRA_ENDPOINTS.GET_PROJECTS, config || {});
}

/**
 * Get Jira issue types for a project
 * @param config Jira configuration and project key
 */
export async function getJiraIssueTypes(config: {
  url: string;
  email: string;
  apiToken: string;
  projectKey: string;
}) {
  return post(JIRA_ENDPOINTS.GET_ISSUE_TYPES, config);
}

/**
 * Save Jira configuration
 * @param config Jira configuration
 */
export async function saveJiraConfig(config: {
  url: string;
  email: string;
  apiToken: string;
  projectsData: any[];
  issueTypesData: any[];
}) {
  return post(JIRA_ENDPOINTS.SAVE_CONFIG, config);
}

/**
 * Get Jira configuration
 */
export async function getJiraConfig() {
  return get(JIRA_ENDPOINTS.GET_CONFIG, {
    showErrorToast: false // Don't show error toast for unconfigured plugins
  });
}

/**
 * Disconnect Jira integration
 */
export async function disconnectJira() {
  return del(JIRA_ENDPOINTS.DISCONNECT);
}

/**
 * Create Jira issue from test report
 * @param params Issue creation parameters
 */
export async function createJiraIssue(params: {
  reportId: string;
  projectKey: string;
  issueTypeId: string;
  summary: string;
  description?: string;
}) {
  return post(JIRA_ENDPOINTS.CREATE_ISSUE, params, {
    showSuccessToast: true,
    successMessage: 'Jira issue created successfully'
  });
}

// ===== ZEPHYR SCALE API FUNCTIONS =====

/**
 * Test Zephyr Scale connection
 * @param config Zephyr Scale configuration data
 */
export async function testZephyrScaleConnection(config: {
  apiToken: string;
}) {
  return post(ZEPHYR_SCALE_ENDPOINTS.TEST_CONNECTION, config);
}

/**
 * Get Zephyr Scale projects
 */
export async function getZephyrScaleProjects() {
  return post(ZEPHYR_SCALE_ENDPOINTS.GET_PROJECTS, {});
}

/**
 * Get Zephyr Scale folders for a project
 * @param config Configuration with project key
 */
export async function getZephyrScaleFolders(config: {
  projectKey: string;
}) {
  return post(ZEPHYR_SCALE_ENDPOINTS.GET_FOLDERS, config);
}

/**
 * Get Zephyr Scale test cases
 * @param config Configuration with project key and optional folder ID
 */
export async function getZephyrScaleTestCases(config: {
  projectKey: string;
  folderId?: string;
}) {
  return post(ZEPHYR_SCALE_ENDPOINTS.GET_TESTCASES, config);
}

/**
 * Get detailed information for a specific Zephyr Scale test case
 * @param config Test case configuration
 */
export async function getZephyrScaleTestCaseDetails(config: {
  testCaseKey: string;
}) {
  return post(ZEPHYR_SCALE_ENDPOINTS.GET_TESTCASE_DETAILS, config);
}

/**
 * Save Zephyr Scale configuration
 * @param config Zephyr Scale configuration data
 */
export async function saveZephyrScaleConfig(config: {
  apiToken: string;
  projectsData: any[];
  foldersData: any[];
}) {
  return post(ZEPHYR_SCALE_ENDPOINTS.SAVE_CONFIG, config, {
    showSuccessToast: true,
    successMessage: 'Zephyr Scale configuration saved successfully'
  });
}

/**
 * Get Zephyr Scale configuration
 */
export async function getZephyrScaleConfig() {
  return get(ZEPHYR_SCALE_ENDPOINTS.GET_CONFIG, {
    showErrorToast: false // Don't show error toast for unconfigured plugins
  });
}

/**
 * Disconnect Zephyr Scale integration
 */
export async function disconnectZephyrScale() {
  return del(ZEPHYR_SCALE_ENDPOINTS.DISCONNECT, {
    showSuccessToast: true,
    successMessage: 'Zephyr Scale plugin disconnected successfully'
  });
}
