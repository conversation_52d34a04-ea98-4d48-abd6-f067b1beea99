/**
 * Admin System Settings API
 *
 * Admin system settings ile ilgili API isteklerini yönetir.
 */

import { adminGet, adminPost, adminPut, adminDel } from './fetch-wrapper';
import { ADMIN_ENDPOINTS } from './constants';

/**
 * Get all system settings
 * @param type Optional filter by setting type
 */
export async function getSystemSettings(type?: string) {
  try {
    const params = type ? { type } : undefined;
    const response = await adminGet(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.GET_ALL, { params });

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch system settings';
      return response;
    }

    // If successful, extract settings from the data property
    if (response.success && response.data) {
      return {
        success: true,
        settings: response.data.settings || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getSystemSettings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get a system setting by ID
 * @param id Setting ID
 */
export async function getSystemSettingById(id: string) {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.GET_BY_ID(id));

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch system setting';
      return response;
    }

    // If successful, extract setting from the data property
    if (response.success && response.data) {
      return {
        success: true,
        setting: response.data.setting || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getSystemSettingById:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get a system setting by type
 * @param type Setting type
 */
export async function getSystemSettingByType(type: string) {
  try {
    const response = await adminGet(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.GET_BY_TYPE(type));

    // Ensure we always have an error message if the request fails
    if (!response.success && !response.error) {
      response.error = 'Failed to fetch system setting';
      return response;
    }

    // If successful, extract setting from the data property
    if (response.success && response.data) {
      return {
        success: true,
        setting: response.data.setting || response.data
      };
    }

    return response;
  } catch (error) {
    console.error('Error in getSystemSettingByType:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Create a new system setting
 * @param settingData Setting data
 */
export async function createSystemSetting(settingData: {
  type: string;
  name: string;
  config: any;
  isActive?: boolean;
}) {
  return adminPost(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.CREATE, settingData, {
    showSuccessToast: true,
    successMessage: 'System setting created successfully'
  });
}

/**
 * Update a system setting
 * @param id Setting ID
 * @param settingData Setting data
 */
export async function updateSystemSetting(id: string, settingData: {
  type?: string;
  name?: string;
  config?: any;
  isActive?: boolean;
}) {
  return adminPut(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.UPDATE(id), settingData, {
    showSuccessToast: true,
    successMessage: 'System setting updated successfully'
  });
}

/**
 * Delete a system setting
 * @param id Setting ID
 */
export async function deleteSystemSetting(id: string) {
  return adminDel(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.DELETE(id), {
    showSuccessToast: true,
    successMessage: 'System setting deleted successfully'
  });
}

/**
 * Test SMTP configuration
 * @param testData Test data
 */
export async function testSMTPConfig(testData: {
  settingId?: string;
  config?: any;
  testEmail: string;
}) {
  return adminPost(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.TEST_SMTP, testData, {
    showSuccessToast: true,
    successMessage: 'Test email sent successfully'
  });
}

/**
 * Test Slack configuration
 * @param testData Test data
 */
export async function testSlackConfig(testData: {
  settingId?: string;
  config?: any;
  testMessage?: string;
}) {
  return adminPost(ADMIN_ENDPOINTS.SETTINGS.SYSTEM.TEST_SLACK, testData, {
    showSuccessToast: true,
    successMessage: 'Test message sent successfully'
  });
}
