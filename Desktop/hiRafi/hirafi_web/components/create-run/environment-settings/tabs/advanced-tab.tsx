"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  RefreshCw,
  Network,
  Globe,
  Server
} from "lucide-react"
import { post } from "@/lib/api/fetch-wrapper"
import { toast } from "sonner"

interface AdvancedTabProps {
  environmentSettings: any;
  updateEnvironmentSettings: (settings: any) => void;
}

export function AdvancedTab({
  environmentSettings,
  updateEnvironmentSettings
}: AdvancedTabProps) {
  const [isTestingProxy, setIsTestingProxy] = useState(false)

  // Proxy settings
  const handleProxyToggle = (enabled: boolean) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, enabled }
    })
  }

  const handleProxyTypeChange = (type: string) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, type }
    })
  }

  const handleProxyHostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, host: e.target.value }
    })
  }

  const handleProxyPortChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const port = parseInt(e.target.value) || 0
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, port }
    })
  }

  const handleProxyUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, username: e.target.value }
    })
  }

  const handleProxyPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateEnvironmentSettings({
      proxy: { ...environmentSettings.proxy, password: e.target.value }
    })
  }

  // Test proxy connection
  const testProxyConnection = async () => {
    setIsTestingProxy(true)

    try {
      const response = await post('/proxy/test', {
        proxy: environmentSettings.proxy
      })

      if (response.success) {
        toast.success('Proxy connection successful', {
          description: 'The proxy connection was tested successfully.'
        })
      } else {
        toast.error('Proxy connection failed', {
          description: response.message || 'Could not connect to the proxy server.'
        })
      }
    } catch (error) {
      toast.error('Proxy connection failed', {
        description: 'Could not connect to the proxy server. Please check your settings.'
      })
      console.error('Proxy test error:', error)
    } finally {
      setIsTestingProxy(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Proxy Settings */}
      <div className="space-y-4">
        <div className="flex items-center">
          <div className="bg-slate-100 dark:bg-slate-800 p-1.5 rounded-full mr-2">
            <Network className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </div>
          <h3 className="font-medium text-slate-700 dark:text-slate-300">Proxy Configuration</h3>
        </div>

        <div className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded-md border border-slate-200 dark:border-slate-700 shadow-sm">
          <Label htmlFor="proxy-enabled" className="cursor-pointer flex items-center">
            <Globe className="h-4 w-4 mr-2 text-slate-500" />
            <span>Enable Proxy</span>
          </Label>
          <Switch
            id="proxy-enabled"
            checked={environmentSettings.proxy?.enabled || false}
            onCheckedChange={handleProxyToggle}
            className="data-[state=checked]:bg-slate-600"
          />
        </div>

        {environmentSettings.proxy?.enabled && (
          <div className="space-y-4 mt-2 p-3 bg-white dark:bg-gray-800 rounded-md border border-slate-200 dark:border-slate-700 shadow-sm">
            <div className="grid gap-2">
              <Label htmlFor="proxy-type" className="flex items-center text-sm font-medium">
                <Network className="h-3.5 w-3.5 mr-1.5 text-slate-500" />
                Proxy Type
              </Label>
              <Select
                value={environmentSettings.proxy?.type || "HTTP"}
                onValueChange={handleProxyTypeChange}
              >
                <SelectTrigger id="proxy-type" className="bg-transparent border-slate-200 dark:border-slate-700">
                  <SelectValue placeholder="Select proxy type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="HTTP">HTTP</SelectItem>
                  <SelectItem value="HTTPS">HTTPS</SelectItem>
                  <SelectItem value="SOCKS4">SOCKS4</SelectItem>
                  <SelectItem value="SOCKS5">SOCKS5</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="proxy-host" className="flex items-center text-sm font-medium">
                  <Server className="h-3.5 w-3.5 mr-1.5 text-slate-500" />
                  Host
                </Label>
                <Input
                  id="proxy-host"
                  placeholder="proxy.example.com"
                  value={environmentSettings.proxy?.host || ""}
                  onChange={handleProxyHostChange}
                  className="bg-transparent border-slate-200 dark:border-slate-700"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="proxy-port" className="flex items-center text-sm font-medium">
                  <Network className="h-3.5 w-3.5 mr-1.5 text-slate-500" />
                  Port
                </Label>
                <Input
                  id="proxy-port"
                  type="number"
                  placeholder="8080"
                  value={environmentSettings.proxy?.port || ""}
                  onChange={handleProxyPortChange}
                  className="bg-transparent border-slate-200 dark:border-slate-700"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="proxy-username" className="flex items-center text-sm font-medium">
                  <Globe className="h-3.5 w-3.5 mr-1.5 text-slate-500" />
                  Username (Optional)
                </Label>
                <Input
                  id="proxy-username"
                  placeholder="Username"
                  value={environmentSettings.proxy?.username || ""}
                  onChange={handleProxyUsernameChange}
                  className="bg-transparent border-slate-200 dark:border-slate-700"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="proxy-password" className="flex items-center text-sm font-medium">
                  <Network className="h-3.5 w-3.5 mr-1.5 text-slate-500" />
                  Password (Optional)
                </Label>
                <Input
                  id="proxy-password"
                  type="password"
                  placeholder="Password"
                  value={environmentSettings.proxy?.password || ""}
                  onChange={handleProxyPasswordChange}
                  className="bg-transparent border-slate-200 dark:border-slate-700"
                />
              </div>
            </div>

            <Button
              onClick={testProxyConnection}
              disabled={isTestingProxy || !environmentSettings.proxy?.host || !environmentSettings.proxy?.port}
              className="w-full bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 text-slate-700 dark:text-slate-300 border border-slate-200 dark:border-slate-700"
              variant="outline"
            >
              {isTestingProxy ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Testing Connection...
                </>
              ) : (
                <>
                  <Network className="h-4 w-4 mr-2" />
                  Test Connection
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
