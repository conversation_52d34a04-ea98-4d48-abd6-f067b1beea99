/**
 * Landing Page API
 *
 * Landing page form submission API requests
 */

import { post } from './fetch-wrapper';
import { LANDING_PAGE_ENDPOINTS } from './constants';

/**
 * Submit landing page contact form
 *
 * @param formData Form data
 */
export async function submitContactForm(formData: {
  name: string;
  email: string;
  company: string;
  message: string;
  phone?: string;
  preferredDate?: string;
}) {
  return post(LANDING_PAGE_ENDPOINTS.SUBMIT_CONTACT, formData);
}
