/**
 * Cleanup Orchestrator Implementation
 * 
 * Orchestrates comprehensive cleanup operations across all test execution components.
 * Replaces the singleton TestCleanupService with a dependency-injected implementation.
 */

import { TestExecutionState } from '../../../node-manager/types/node-manager-types.js';
import { ICleanupOrchestrator, CleanupOptions, CleanupResult, CleanupStep, ShutdownCleanupOptions, CleanupReason } from '../../interfaces/cleanup-orchestrator.interface.js';
import { IResourceManager } from '../../interfaces/resource-manager.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

/**
 * Cleanup statistics tracking
 */
interface CleanupStatistics {
  totalCleanups: number;
  successfulCleanups: number;
  failedCleanups: number;
  totalCleanupTime: number;
}

/**
 * Cleanup Orchestrator Implementation
 * 
 * Provides comprehensive cleanup orchestration with proper error handling,
 * recovery mechanisms, and extensible cleanup steps.
 */
export class CleanupOrchestrator implements ICleanupOrchestrator {
  private logger: ILoggerService;
  private resourceManager: IResourceManager;
  
  // Cleanup tracking
  private activeCleanups: Map<string, Promise<CleanupResult>> = new Map();
  private completedCleanups: Set<string> = new Set();
  private cleanupStatistics: CleanupStatistics = {
    totalCleanups: 0,
    successfulCleanups: 0,
    failedCleanups: 0,
    totalCleanupTime: 0
  };

  // Enhanced Per-Test-ID Locking System
  private testLocks: Map<string, {
    mutex: Promise<void>;
    queue: Array<{
      operation: () => Promise<CleanupResult>;
      resolve: (result: CleanupResult) => void;
      reject: (error: Error) => void;
      timeout: NodeJS.Timeout;
      operationId: string;
    }>;
    isProcessing: boolean;
    operationCounter: number;
  }> = new Map();
  private readonly lockTimeout = 60000; // 60 seconds timeout for cleanup operations
  private globalOperationCounter = 0;

  // Lock extension tracking
  private lockExtensionIntervals: Map<string, NodeJS.Timeout> = new Map();

  // Custom cleanup steps
  private customCleanupSteps: Map<string, CleanupStep> = new Map();

  constructor(resourceManager: IResourceManager) {
    this.logger = LoggerServiceFactory.createServiceLogger('CleanupOrchestrator');
    this.resourceManager = resourceManager;
  }

  /**
   * Initialize the cleanup orchestrator
   */
  async initialize(): Promise<void> {
    this.activeCleanups.clear();
    this.completedCleanups.clear();
    this.lockExtensionIntervals.clear();
    this.customCleanupSteps.clear();

    // Clear per-test locks
    for (const [testId, lockInfo] of this.testLocks) {
      // Clear any pending operations
      for (const queueItem of lockInfo.queue) {
        clearTimeout(queueItem.timeout);
        queueItem.reject(new Error(`CleanupOrchestrator disposed during initialization`));
      }
    }
    this.testLocks.clear();
    this.globalOperationCounter = 0;

    // Reset statistics
    this.cleanupStatistics = {
      totalCleanups: 0,
      successfulCleanups: 0,
      failedCleanups: 0,
      totalCleanupTime: 0
    };

    this.logger.debug('CleanupOrchestrator: Initialized');
  }

  /**
   * Perform comprehensive cleanup for a test with per-test-ID locking
   */
  async cleanupTest(options: CleanupOptions): Promise<CleanupResult> {
    const { testId } = options;

    // Check if cleanup already completed
    if (this.completedCleanups.has(testId)) {
      this.logger.debug(`CleanupOrchestrator: Cleanup already completed for test ${testId}`);
      return {
        success: true,
        stepsCompleted: ['already_completed'],
        errors: [],
        duration: 0,
        testId,
        reason: options.reason
      };
    }

    // Use per-test-ID locking to prevent concurrent cleanup operations
    return this.executeWithTestLock(testId, async () => {
      // Double-check completion status after acquiring lock
      if (this.completedCleanups.has(testId)) {
        this.logger.debug(`CleanupOrchestrator: Cleanup already completed for test ${testId} (double-check)`);
        return {
          success: true,
          stepsCompleted: ['already_completed'],
          errors: [],
          duration: 0,
          testId,
          reason: options.reason
        };
      }

      // Check if cleanup is already in progress (legacy check for backward compatibility)
      const existingCleanup = this.activeCleanups.get(testId);
      if (existingCleanup) {
        this.logger.debug(`CleanupOrchestrator: Cleanup already in progress for test ${testId} (legacy check)`);
        return await existingCleanup;
      }

      // Start new cleanup operation
      const cleanupPromise = this.executeCleanup(options);
      this.activeCleanups.set(testId, cleanupPromise);

      try {
        const result = await cleanupPromise;

        // Update statistics
        this.updateStatistics(result);

        // Mark as completed if successful
        if (result.success) {
          this.completedCleanups.add(testId);
        }

        return result;
      } finally {
        this.activeCleanups.delete(testId);
      }
    });
  }

  /**
   * Register a lock extension interval for cleanup tracking
   */
  registerLockExtensionInterval(testId: string, interval: NodeJS.Timeout): void {
    this.lockExtensionIntervals.set(testId, interval);
    this.logger.debug(`CleanupOrchestrator: Registered lock extension interval for test ${testId}`);
  }

  /**
   * Clear lock extension interval for a test
   */
  clearLockExtensionInterval(testId: string): void {
    const interval = this.lockExtensionIntervals.get(testId);
    if (interval) {
      clearInterval(interval);
      this.lockExtensionIntervals.delete(testId);
      this.logger.info(`CleanupOrchestrator: Cleared lock extension interval for test ${testId}`);
    }
  }

  /**
   * Perform emergency cleanup of all resources
   */
  async emergencyCleanupAll(reason: string = 'Emergency cleanup'): Promise<void> {
    this.logger.warn(`CleanupOrchestrator: Performing emergency cleanup: ${reason}`);

    try {
      // Clear all lock extensions
      for (const [testId, interval] of this.lockExtensionIntervals) {
        clearInterval(interval);
        this.lockExtensionIntervals.delete(testId);
      }

      // Use resource manager for comprehensive cleanup
      await this.resourceManager.emergencyCleanupAll(reason);

      this.logger.info('CleanupOrchestrator: Emergency cleanup completed');
    } catch (error: any) {
      this.logger.error(`CleanupOrchestrator: Error during emergency cleanup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Perform shutdown cleanup
   */
  async performShutdownCleanup(options: ShutdownCleanupOptions = {}): Promise<void> {
    const { reason = 'System shutdown', timeout = 10000 } = options;

    try {
      this.logger.info(`CleanupOrchestrator: Performing shutdown cleanup: ${reason}`);

      // Perform emergency cleanup of all resources
      await this.emergencyCleanupAll(reason);

      // Close connections if provided
      if (options.websocketConnector && typeof options.websocketConnector.close === 'function') {
        try {
          await options.websocketConnector.close();
          this.logger.debug('CleanupOrchestrator: WebSocket connector closed');
        } catch (error: any) {
          this.logger.warn(`CleanupOrchestrator: Error closing WebSocket connector: ${error.message}`);
        }
      }

      if (options.connectionManager && typeof options.connectionManager.close === 'function') {
        try {
          await options.connectionManager.close();
          this.logger.debug('CleanupOrchestrator: Connection manager closed');
        } catch (error: any) {
          this.logger.warn(`CleanupOrchestrator: Error closing connection manager: ${error.message}`);
        }
      }

      this.logger.info('CleanupOrchestrator: Shutdown cleanup completed');
    } catch (error: any) {
      this.logger.error(`CleanupOrchestrator: Error during shutdown cleanup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Perform force cleanup (for crash scenarios)
   */
  async performForceCleanup(reason: string = 'Force cleanup'): Promise<void> {
    try {
      this.logger.warn(`CleanupOrchestrator: Performing force cleanup: ${reason}`);
      await this.emergencyCleanupAll(reason);
      this.logger.info('CleanupOrchestrator: Force cleanup completed');
    } catch (error: any) {
      this.logger.error(`CleanupOrchestrator: Error during force cleanup: ${error.message}`);
      // Don't throw in force cleanup - log and continue
    }
  }

  /**
   * Get cleanup status for a test
   */
  getCleanupStatus(testId: string): { inProgress: boolean; completed: boolean; lastCleanupTime?: Date } {
    return {
      inProgress: this.activeCleanups.has(testId),
      completed: this.completedCleanups.has(testId)
    };
  }

  /**
   * Register a custom cleanup step
   */
  registerCleanupStep(step: CleanupStep): void {
    this.customCleanupSteps.set(step.name, step);
    this.logger.debug(`CleanupOrchestrator: Registered custom cleanup step '${step.name}'`);
  }

  /**
   * Unregister a custom cleanup step
   */
  unregisterCleanupStep(stepName: string): void {
    this.customCleanupSteps.delete(stepName);
    this.logger.debug(`CleanupOrchestrator: Unregistered cleanup step '${stepName}'`);
  }

  /**
   * Get cleanup statistics
   */
  getCleanupStatistics(): {
    totalCleanups: number;
    successfulCleanups: number;
    failedCleanups: number;
    averageCleanupTime: number;
  } {
    return {
      ...this.cleanupStatistics,
      averageCleanupTime: this.cleanupStatistics.totalCleanups > 0 
        ? this.cleanupStatistics.totalCleanupTime / this.cleanupStatistics.totalCleanups 
        : 0
    };
  }

  /**
   * Clear completed cleanup tracking for a test
   */
  clearCompletedCleanup(testId: string): void {
    this.completedCleanups.delete(testId);
    this.logger.debug(`CleanupOrchestrator: Cleared completed cleanup tracking for test ${testId}`);
  }

  /**
   * Dispose of the cleanup orchestrator with proper resource cleanup
   */
  async dispose(): Promise<void> {
    try {
      // Clear all active intervals
      for (const [testId, interval] of this.lockExtensionIntervals) {
        clearInterval(interval);
      }

      // Clear all per-test locks and reject pending operations
      for (const [testId, lockInfo] of this.testLocks) {
        // Clear any pending operations
        for (const queueItem of lockInfo.queue) {
          clearTimeout(queueItem.timeout);
          queueItem.reject(new Error(`CleanupOrchestrator disposed`));
        }
      }

      this.activeCleanups.clear();
      this.completedCleanups.clear();
      this.lockExtensionIntervals.clear();
      this.customCleanupSteps.clear();
      this.testLocks.clear();
      this.globalOperationCounter = 0;

      this.logger.debug('CleanupOrchestrator: Disposed');
    } catch (error: any) {
      this.logger.error(`CleanupOrchestrator: Error during disposal: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute operation with per-test-ID lock protection
   */
  private async executeWithTestLock<T>(testId: string, operation: () => Promise<T>): Promise<T> {
    // Get or create lock info for this test ID
    let lockInfo = this.testLocks.get(testId);
    if (!lockInfo) {
      lockInfo = {
        mutex: Promise.resolve(),
        queue: [],
        isProcessing: false,
        operationCounter: 0
      };
      this.testLocks.set(testId, lockInfo);
    }

    return new Promise<T>((resolve, reject) => {
      const operationId = `${testId}_op_${++lockInfo!.operationCounter}_${++this.globalOperationCounter}_${Date.now()}`;

      // Create timeout for deadlock prevention
      const timeout = setTimeout(() => {
        // Remove from queue if still pending
        const index = lockInfo!.queue.findIndex(item => item.operationId === operationId);
        if (index !== -1) {
          lockInfo!.queue.splice(index, 1);
        }

        this.logger.error(`CleanupOrchestrator: Operation ${operationId} for test ${testId} timed out after ${this.lockTimeout}ms`);
        reject(new Error(`CleanupOrchestrator: Operation ${operationId} for test ${testId} timed out after ${this.lockTimeout}ms`));
      }, this.lockTimeout);

      // Add to test-specific queue
      lockInfo!.queue.push({
        operation: async () => {
          try {
            const result = await operation();
            return result as CleanupResult;
          } catch (error) {
            throw error;
          }
        },
        resolve: (result) => {
          clearTimeout(timeout);
          resolve(result as T);
        },
        reject: (error) => {
          clearTimeout(timeout);
          reject(error);
        },
        timeout,
        operationId
      });

      // Process queue for this test ID if not already processing
      this.processTestQueue(testId);
    });
  }

  /**
   * Process the operation queue for a specific test ID
   */
  private async processTestQueue(testId: string): Promise<void> {
    const lockInfo = this.testLocks.get(testId);
    if (!lockInfo || lockInfo.isProcessing || lockInfo.queue.length === 0) {
      return;
    }

    lockInfo.isProcessing = true;

    try {
      while (lockInfo.queue.length > 0) {
        const queueItem = lockInfo.queue.shift();
        if (!queueItem) continue;

        try {
          this.logger.debug(`CleanupOrchestrator: Processing operation ${queueItem.operationId} for test ${testId}`);
          const result = await queueItem.operation();
          queueItem.resolve(result);
        } catch (error) {
          this.logger.error(`CleanupOrchestrator: Error in operation ${queueItem.operationId} for test ${testId}: ${error instanceof Error ? error.message : String(error)}`);
          queueItem.reject(error instanceof Error ? error : new Error(String(error)));
        }
      }
    } finally {
      lockInfo.isProcessing = false;

      // Clean up lock info if no more operations are queued
      if (lockInfo.queue.length === 0) {
        this.testLocks.delete(testId);
        this.logger.debug(`CleanupOrchestrator: Cleaned up lock info for test ${testId}`);
      }
    }
  }

  /**
   * Get lock status for debugging
   */
  getLockStatus(): { [testId: string]: { queueLength: number; isProcessing: boolean; operationCounter: number } } {
    const status: { [testId: string]: { queueLength: number; isProcessing: boolean; operationCounter: number } } = {};

    for (const [testId, lockInfo] of this.testLocks) {
      status[testId] = {
        queueLength: lockInfo.queue.length,
        isProcessing: lockInfo.isProcessing,
        operationCounter: lockInfo.operationCounter
      };
    }

    return status;
  }

  /**
   * Execute the actual cleanup steps
   */
  private async executeCleanup(options: CleanupOptions): Promise<CleanupResult> {
    const { testId, reason, currentState, timeout = 10000, forceCleanup = false, testRunner } = options;
    const startTime = Date.now();
    const stepsCompleted: string[] = [];
    const errors: Error[] = [];

    this.logger.info(`CleanupOrchestrator: Starting cleanup for test ${testId}. Reason: ${reason}`);

    try {
      // Step 1: Clear lock extensions
      this.clearLockExtensionInterval(testId);
      stepsCompleted.push('clear_lock_extensions');

      // Step 2: Wait for pending operations
      try {
        await this.resourceManager.waitForPendingOperations(testId, Math.min(timeout / 2, 5000));
        stepsCompleted.push('wait_pending_operations');
      } catch (error: any) {
        this.logger.warn(`CleanupOrchestrator: Error waiting for pending operations: ${error.message}`);
        errors.push(error);
      }

      // Step 3: Cleanup test runner
      if (testRunner) {
        try {
          await this.cleanupTestRunner(testRunner, testId);
          stepsCompleted.push('cleanup_test_runner');
        } catch (error: any) {
          this.logger.error(`CleanupOrchestrator: Error cleaning up test runner: ${error.message}`);
          errors.push(error);
        }
      }

      // Step 4: Cleanup resources
      try {
        await this.resourceManager.cleanupTestResources({
          testId,
          reason: `Cleanup orchestrator: ${reason}`,
          forceCleanup: true,
          timeout: Math.min(timeout, 8000)
        });
        stepsCompleted.push('cleanup_resources');
      } catch (error: any) {
        this.logger.error(`CleanupOrchestrator: Error cleaning up resources: ${error.message}`);
        errors.push(error);
      }

      // Step 5: Execute custom cleanup steps
      for (const [stepName, step] of this.customCleanupSteps) {
        try {
          await step.execute(testId, { reason, currentState, testRunner });
          stepsCompleted.push(`custom_${stepName}`);
        } catch (error: any) {
          this.logger.error(`CleanupOrchestrator: Error in custom cleanup step '${stepName}': ${error.message}`);
          errors.push(error);
          
          if (step.critical) {
            break; // Stop on critical step failure
          }
        }
      }

    } catch (error: any) {
      this.logger.error(`CleanupOrchestrator: Critical error during cleanup: ${error.message}`);
      errors.push(error);
    }

    const duration = Date.now() - startTime;
    const success = errors.length === 0;

    this.logger.info(`CleanupOrchestrator: Cleanup ${success ? 'completed successfully' : 'completed with errors'} for test ${testId} in ${duration}ms`);

    return {
      success,
      stepsCompleted,
      errors,
      duration,
      testId,
      reason
    };
  }

  /**
   * Cleanup test runner safely
   *
   * Note: Only calls cleanupTestEnvironment directly to avoid duplicate cleanup calls.
   * The abortTest() and dispose() methods both internally call cleanupTestEnvironment(),
   * so calling all three would result in triple cleanup execution.
   */
  private async cleanupTestRunner(testRunner: any, testId: string): Promise<void> {
    // Set abort flag first to stop any ongoing test execution
    if (testRunner && typeof testRunner.abortRequested !== 'undefined') {
      testRunner.abortRequested = true;
      this.logger.debug(`CleanupOrchestrator: Set abort flag for test ${testId}`);
    }

    // Only call cleanupTestEnvironment directly - this is the core cleanup logic
    // that both abortTest() and dispose() would call anyway
    if (testRunner && typeof testRunner.cleanupTestEnvironment === 'function') {
      try {
        await Promise.race([
          testRunner.cleanupTestEnvironment(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error(`cleanupTestEnvironment timeout`)), 5000)
          )
        ]);
        this.logger.debug(`CleanupOrchestrator: Successfully executed cleanupTestEnvironment for test ${testId}`);
      } catch (error: any) {
        this.logger.warn(`CleanupOrchestrator: Error in cleanupTestEnvironment for test ${testId}: ${error.message}`);
      }
    }

    // Set final disposal flags
    if (testRunner) {
      if (typeof testRunner.isRunning !== 'undefined') {
        testRunner.isRunning = false;
        this.logger.debug(`CleanupOrchestrator: Set isRunning=false for test ${testId}`);
      }
    }
  }

  /**
   * Update cleanup statistics
   */
  private updateStatistics(result: CleanupResult): void {
    this.cleanupStatistics.totalCleanups++;
    this.cleanupStatistics.totalCleanupTime += result.duration;
    
    if (result.success) {
      this.cleanupStatistics.successfulCleanups++;
    } else {
      this.cleanupStatistics.failedCleanups++;
    }
  }
}
