/**
 * Report PDF Exporter
 * Test raporlarını PDF formatında dışa aktarmak için yardımcı fonksiyonlar
 */

import PDFDocument from 'pdfkit';
import { logger } from './logger.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import https from 'https';

// Dosya yolunu al
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Font dosyalarının yolları - Docker container'ında çalışma dizini /app olduğu için process.cwd() kullan
const FONTS = {
  Roboto: {
    normal: path.join(process.cwd(), 'assets/fonts/Roboto-Regular.ttf'),
    bold: path.join(process.cwd(), 'assets/fonts/Roboto-Bold.ttf'),
    italic: path.join(process.cwd(), 'assets/fonts/Roboto-Italic.ttf'),
    bolditalic: path.join(process.cwd(), 'assets/fonts/Roboto-BoldItalic.ttf')
  }
};

// Sayfa boyutları ve kenar boşlukları
const PAGE_WIDTH = 595.28;
const PAGE_MARGIN = 50;
const CONTENT_WIDTH = PAGE_WIDTH - (PAGE_MARGIN * 2);
const ROW_GAP = 5;

/**
 * Test raporunu PDF formatında oluşturur
 * @param {Object} report Test raporu
 * @returns {Promise<Buffer>} PDF buffer
 */
export async function createReportPdf(report) {
  return new Promise(async (resolve, reject) => {
    try {
      // Önce font dosyalarının var olduğunu kontrol et
      for (const fontType in FONTS.Roboto) {
        const fontPath = FONTS.Roboto[fontType];
        if (!fs.existsSync(fontPath)) {
          logger.warn(`Font file not found: ${fontPath}`);
          throw new Error(`Font file not found: ${fontPath}`);
        } else {
          logger.info(`Font file found: ${fontPath}`);
        }
      }

      // PDF dökümanı oluştur - A4 boyutunda ve kenar boşlukları ile
      const doc = new PDFDocument({
        margin: PAGE_MARGIN,
        size: 'A4',
        bufferPages: true,
        autoFirstPage: true,
        info: {
          Title: `Test Report - ${report.scenarioTitle || report.name || 'Unnamed Test'}`,
          Author: 'AI Drive Test Hub',
          Subject: 'Test Report',
          Keywords: 'test, report, automation',
          Creator: 'AI Drive Test Hub',
          Producer: 'PDFKit'
        }
      });

      // Fontları kaydet
      try {
        // Roboto fontunu kaydet (Türkçe karakter desteği için)
        doc.registerFont('Roboto', FONTS.Roboto.normal);
        doc.registerFont('Roboto-Bold', FONTS.Roboto.bold);
        doc.registerFont('Roboto-Italic', FONTS.Roboto.italic);
        doc.registerFont('Roboto-BoldItalic', FONTS.Roboto.bolditalic);

        // Varsayılan fontu ayarla
        doc.font('Roboto');
        logger.info('Fonts registered successfully');
      } catch (fontError) {
        logger.error(`Error registering fonts: ${fontError.message}`);
        throw fontError;
      }

      // Buffer'a yazmak için stream oluştur
      const buffers = [];

      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Başlık ve üst bilgileri ekle
      addHeader(doc, report);

      // Rapor özeti ekle
      addSummary(doc, report);

      // Test adımlarını ekle
      addTestSteps(doc, report);

      // Ekran görüntülerini ekle (async)
      await addScreenshots(doc, report);

      // Sayfa numaralarını ekle
      addPageNumbers(doc);

      // PDF'i sonlandır
      doc.end();

    } catch (error) {
      logger.error(`Error creating PDF for report: ${error.message}`);
      reject(error);
    }
  });
}

/**
 * PDF'e başlık ve üst bilgileri ekler
 */
function addHeader(doc, report) {
  // Başlık alanı
  doc.rect(PAGE_MARGIN, PAGE_MARGIN, CONTENT_WIDTH, 80)
     .fillAndStroke('#f0f4f8', '#e1e8ed');

  // Başlık ekle
  doc.fontSize(24)
     .font('Roboto-Bold')
     .fillColor('#1a365d')
     .text(report.scenarioTitle || report.name || 'Test Report', PAGE_MARGIN + 10, PAGE_MARGIN + 15, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Alt başlık ekle
  doc.fontSize(12)
     .font('Roboto')
     .fillColor('#4a5568')
     .text(`Report ID: ${report.id}`, PAGE_MARGIN + 10, PAGE_MARGIN + 45, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Tarih ekle
  const reportDate = report.date ? new Date(report.date).toLocaleString() : new Date().toLocaleString();
  doc.fontSize(10)
     .text(`Generated on: ${reportDate}`, PAGE_MARGIN + 10, PAGE_MARGIN + 65, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Başlık alanından sonraki y pozisyonu
  doc.y = PAGE_MARGIN + 100;
}

/**
 * PDF'e rapor özeti ekler
 */
function addSummary(doc, report) {
  // Özet başlığı
  doc.fontSize(16)
     .font('Roboto-Bold')
     .fillColor('#2d3748')
     .text('Test Summary', PAGE_MARGIN, doc.y);

  doc.y += 10;

  // Özet tablosu
  const summaryData = [
    { label: 'Status', value: report.status || 'Unknown' },
    { label: 'Duration', value: formatDuration(report.duration) },
    { label: 'Total Steps', value: report.summary?.total || 0 },
    { label: 'Passed Steps', value: report.summary?.passed || 0 },
    { label: 'Failed Steps', value: report.summary?.failed || 0 },
    { label: 'Error Count', value: report.summary?.errors || 0 },
  ];

  // Tablo başlangıç pozisyonu
  const tableTop = doc.y;
  const tableLeft = PAGE_MARGIN;
  const colWidth = CONTENT_WIDTH / 2;

  // Tablo başlığı
  doc.rect(tableLeft, tableTop, CONTENT_WIDTH, 25)
     .fillAndStroke('#e6f0ff', '#b3d1ff');

  doc.fontSize(12)
     .font('Roboto-Bold')
     .fillColor('#1a365d')
     .text('Metric', tableLeft + 10, tableTop + 8, { width: colWidth - 20 })
     .text('Value', tableLeft + colWidth + 10, tableTop + 8, { width: colWidth - 20 });

  // Tablo verileri
  let rowTop = tableTop + 25;
  let rowColor = false;

  summaryData.forEach((item, index) => {
    // Satır arka planı
    if (rowColor) {
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, 25)
         .fillAndStroke('#f8fafc', '#edf2f7');
    }
    rowColor = !rowColor;

    // Satır verileri
    doc.fontSize(10)
       .font('Roboto')
       .fillColor('#2d3748')
       .text(item.label, tableLeft + 10, rowTop + 8, { width: colWidth - 20 });

    // Değer hücresinde durum rengini ayarla
    if (item.label === 'Status') {
      const statusColor = item.value.toLowerCase() === 'completed' ||
                         item.value.toLowerCase() === 'passed' ? '#38a169' : '#e53e3e';
      doc.fillColor(statusColor)
         .font('Roboto-Bold')
         .text(item.value, tableLeft + colWidth + 10, rowTop + 8, { width: colWidth - 20 })
         .fillColor('#2d3748')
         .font('Roboto');
    } else {
      doc.text(String(item.value), tableLeft + colWidth + 10, rowTop + 8, { width: colWidth - 20 });
    }

    rowTop += 25;
  });

  // Tablonun altındaki boşluk
  doc.y = rowTop + 20;
}

/**
 * PDF'e test adımlarını ekler
 */
function addTestSteps(doc, report) {
  // Adımlar başlığı
  doc.fontSize(16)
     .font('Roboto-Bold')
     .fillColor('#2d3748')
     .text('Test Steps', PAGE_MARGIN, doc.y);

  doc.y += 10;

  // Adımlar yoksa bilgi mesajı göster
  if (!report.steps || report.steps.length === 0) {
    doc.fontSize(10)
       .font('Roboto-Italic')
       .fillColor('#718096')
       .text('No test steps available for this report.', PAGE_MARGIN, doc.y);
    return;
  }

  // Adımlar tablosu
  const tableTop = doc.y;
  const tableLeft = PAGE_MARGIN;

  // Sütun genişlikleri
  const colWidths = {
    step: CONTENT_WIDTH * 0.1,
    name: CONTENT_WIDTH * 0.4,
    type: CONTENT_WIDTH * 0.2,
    duration: CONTENT_WIDTH * 0.15,
    status: CONTENT_WIDTH * 0.15
  };

  // Tablo başlığı
  doc.rect(tableLeft, tableTop, CONTENT_WIDTH, 25)
     .fillAndStroke('#e6f0ff', '#b3d1ff');

  doc.fontSize(10)
     .font('Roboto-Bold')
     .fillColor('#1a365d');

  let headerX = tableLeft;
  doc.text('#', headerX + 5, tableTop + 8, { width: colWidths.step - 10 });
  headerX += colWidths.step;

  doc.text('Step Name', headerX + 5, tableTop + 8, { width: colWidths.name - 10 });
  headerX += colWidths.name;

  doc.text('Type', headerX + 5, tableTop + 8, { width: colWidths.type - 10 });
  headerX += colWidths.type;

  doc.text('Duration', headerX + 5, tableTop + 8, { width: colWidths.duration - 10 });
  headerX += colWidths.duration;

  doc.text('Status', headerX + 5, tableTop + 8, { width: colWidths.status - 10 });

  // Tablo verileri
  let rowTop = tableTop + 25;
  let rowColor = false;

  // Önce tüm adımların yüksekliklerini hesapla
  const stepHeights = report.steps.map((step, index) => {
    const stepNameText = step.name || step.description || `Step ${index + 1}`;
    const stepTypeText = step.type || 'Unknown';

    // Adım adı için yükseklik hesapla
    const nameHeight = estimateTextHeight(stepNameText, colWidths.name - 10, 9);

    // Adım tipi için yükseklik hesapla
    const typeHeight = estimateTextHeight(stepTypeText, colWidths.type - 10, 9);

    // Hata mesajı varsa onun için de yükseklik hesapla
    const errorHeight = step.error ?
      estimateTextHeight(step.error, CONTENT_WIDTH - 20, 9) : 0;

    // En büyük yüksekliği kullan ve minimum 25px olarak ayarla
    return Math.max(25, nameHeight, typeHeight) + (errorHeight > 0 ? errorHeight + 10 : 0);
  });

  report.steps.forEach((step, index) => {
    // Satır yüksekliğini al
    const rowHeight = stepHeights[index];
    const stepNameText = step.name || step.description || `Step ${index + 1}`;

    // Sayfa kontrolü
    if (rowTop + rowHeight > doc.page.height - PAGE_MARGIN) {
      doc.addPage();
      rowTop = PAGE_MARGIN;

      // Yeni sayfada başlık satırını tekrar çiz
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, 25)
         .fillAndStroke('#e6f0ff', '#b3d1ff');

      doc.fontSize(10)
         .font('Roboto-Bold')
         .fillColor('#1a365d');

      let headerX = tableLeft;
      doc.text('#', headerX + 5, rowTop + 8, { width: colWidths.step - 10 });
      headerX += colWidths.step;

      doc.text('Step Name', headerX + 5, rowTop + 8, { width: colWidths.name - 10 });
      headerX += colWidths.name;

      doc.text('Type', headerX + 5, rowTop + 8, { width: colWidths.type - 10 });
      headerX += colWidths.type;

      doc.text('Duration', headerX + 5, rowTop + 8, { width: colWidths.duration - 10 });
      headerX += colWidths.duration;

      doc.text('Status', headerX + 5, rowTop + 8, { width: colWidths.status - 10 });

      rowTop += 25;
    }

    // Satır arka planı
    if (rowColor) {
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, rowHeight)
         .fillAndStroke('#f8fafc', '#edf2f7');
    }
    rowColor = !rowColor;

    // Satır verileri
    doc.fontSize(9)
       .font('Roboto')
       .fillColor('#2d3748');

    let cellX = tableLeft;
    const cellPadding = rowHeight > 25 ? 10 : 8; // Daha yüksek satırlar için daha fazla padding

    // Adım numarası
    doc.text(String(index + 1), cellX + 5, rowTop + cellPadding, { width: colWidths.step - 10 });
    cellX += colWidths.step;

    // Adım adı
    doc.text(stepNameText, cellX + 5, rowTop + cellPadding, {
      width: colWidths.name - 10,
      lineGap: 2 // Satırlar arası boşluk
    });
    cellX += colWidths.name;

    // Adım tipi
    doc.text(step.type || 'Unknown', cellX + 5, rowTop + cellPadding, {
      width: colWidths.type - 10,
      lineGap: 2
    });
    cellX += colWidths.type;

    // Süre
    doc.text(formatDuration(step.duration), cellX + 5, rowTop + cellPadding, { width: colWidths.duration - 10 });
    cellX += colWidths.duration;

    // Durum
    const statusText = step.success ? 'Passed' : (step.error ? 'Failed' : 'Unknown');
    const statusColor = step.success ? '#38a169' : (step.error ? '#e53e3e' : '#718096');

    doc.fillColor(statusColor)
       .font('Roboto-Bold')
       .text(statusText, cellX + 5, rowTop + cellPadding, { width: colWidths.status - 10 })
       .fillColor('#2d3748')
       .font('Roboto');

    // Hata mesajı varsa göster
    if (step.error) {
      const errorY = rowTop + rowHeight - estimateTextHeight(step.error, CONTENT_WIDTH - 20, 8) - 5;
      doc.fontSize(8)
         .font('Roboto-Italic')
         .fillColor('#e53e3e')
         .text(step.error, tableLeft + 10, errorY, {
           width: CONTENT_WIDTH - 20,
           lineGap: 1
         });
    }

    rowTop += rowHeight;
  });

  // Tablonun altındaki boşluk
  doc.y = rowTop + 20;
}

/**
 * Ekran görüntülerini ekler
 */
async function addScreenshots(doc, report) {
  // Adımlarda ekran görüntüsü var mı kontrol et
  const hasScreenshots = report.steps && report.steps.some(step =>
    step.beforeScreenshotUrl || step.afterScreenshotUrl
  );

  if (!hasScreenshots) {
    return; // Ekran görüntüsü yoksa fonksiyondan çık
  }

  // Yeni sayfaya geç
  doc.addPage();

  // Başlık ekle
  doc.fontSize(16)
     .font('Roboto-Bold')
     .fillColor('#2d3748')
     .text('Screenshots', PAGE_MARGIN, PAGE_MARGIN);

  doc.y += 15;

  // Ekran görüntülerini ekle
  let currentY = doc.y;

  // Sayfa başına maksimum görüntü sayısı
  const STEPS_PER_PAGE = 2;
  let stepsOnCurrentPage = 0;

  // Her adım için ekran görüntülerini ekle
  for (let index = 0; index < report.steps.length; index++) {
    const step = report.steps[index];

    // Ekran görüntüsü yoksa atla
    if (!step.beforeScreenshotUrl && !step.afterScreenshotUrl) {
      continue;
    }

    // Adım adının uzunluğuna göre başlık yüksekliğini hesapla
    const stepNameText = step.name || step.description || `Step ${index + 1}`;
    const stepNameHeight = estimateTextHeight(stepNameText, CONTENT_WIDTH - 10, 12);

    // Sayfa yüksekliğinin dinamik hesaplanması
    const availableHeight = doc.page.height - currentY - PAGE_MARGIN;
    const estimatedStepHeight = stepNameHeight + 20; // Adım başlığı için tahmini yükseklik
    const estimatedImageHeight = 250; // Bir resim için tahmini yükseklik (başlık dahil)

    // Her iki ekran görüntüsü de varsa yan yana gösterilecek
    const hasBothScreenshots = step.beforeScreenshotUrl && step.afterScreenshotUrl;
    const totalEstimatedHeight = estimatedStepHeight +
                               (hasBothScreenshots ? estimatedImageHeight :
                                (step.beforeScreenshotUrl || step.afterScreenshotUrl ? estimatedImageHeight : 0));

    // Sayfa kontrolü - yeterli alan yoksa veya sayfa başına maksimum adım sayısına ulaşıldıysa
    if (currentY > doc.page.height - 200 ||
        availableHeight < totalEstimatedHeight ||
        stepsOnCurrentPage >= STEPS_PER_PAGE) {
      doc.addPage();
      currentY = PAGE_MARGIN;
      stepsOnCurrentPage = 0;
    }

    // Adım başlığı - arka plan ile vurgulanmış
    doc.rect(PAGE_MARGIN - 5, currentY - 5, CONTENT_WIDTH + 10, stepNameHeight + 10)
       .fillAndStroke('#f0f4f8', '#e1e8ed');

    doc.fontSize(12)
       .font('Roboto-Bold')
       .fillColor('#2d3748')
       .text(`Step ${index + 1}: ${stepNameText}`,
             PAGE_MARGIN, currentY, { width: CONTENT_WIDTH });

    currentY += stepNameHeight + 15; // Başlıktan sonra daha fazla boşluk

    // Her iki ekran görüntüsü de varsa yan yana göster
    if (hasBothScreenshots) {
      try {
        // Ekran görüntülerini indir
        const beforeImageUrl = step.beforeScreenshotUrl;
        const afterImageUrl = step.afterScreenshotUrl;

        if (beforeImageUrl && beforeImageUrl.startsWith('http') &&
            afterImageUrl && afterImageUrl.startsWith('http')) {

          // Yan yana gösterim için genişlik hesapla
          const halfWidth = (CONTENT_WIDTH - 20) / 2; // 20px aralarında boşluk bırak

          // Before Screenshot başlığı
          doc.rect(PAGE_MARGIN - 2, currentY - 2, 120, 16)
             .fillAndStroke('#edf2f7', '#e2e8f0');

          doc.fontSize(10)
             .font('Roboto-Bold')
             .fillColor('#4a5568')
             .text('Before Screenshot:', PAGE_MARGIN, currentY);

          // After Screenshot başlığı
          doc.rect(PAGE_MARGIN + halfWidth + 20 - 2, currentY - 2, 120, 16)
             .fillAndStroke('#edf2f7', '#e2e8f0');

          doc.fontSize(10)
             .font('Roboto-Bold')
             .fillColor('#4a5568')
             .text('After Screenshot:', PAGE_MARGIN + halfWidth + 20, currentY);

          currentY += 20;

          try {
            // Resimleri indir
            const beforeImageBuffer = await fetchImageFromUrl(beforeImageUrl);
            const afterImageBuffer = await fetchImageFromUrl(afterImageUrl);

            // Maksimum yükseklik
            const maxHeight = 250;

            // Before Screenshot için çerçeve
            doc.rect(PAGE_MARGIN - 2, currentY - 2, halfWidth + 4, maxHeight + 4)
               .stroke('#e2e8f0');

            // After Screenshot için çerçeve
            doc.rect(PAGE_MARGIN + halfWidth + 20 - 2, currentY - 2, halfWidth + 4, maxHeight + 4)
               .stroke('#e2e8f0');

            // Before Screenshot'u ekle
            doc.image(beforeImageBuffer, PAGE_MARGIN, currentY, {
              fit: [halfWidth, maxHeight],
              align: 'center'
            });

            // After Screenshot'u ekle
            doc.image(afterImageBuffer, PAGE_MARGIN + halfWidth + 20, currentY, {
              fit: [halfWidth, maxHeight],
              align: 'center'
            });

            // Y pozisyonunu güncelle - her iki resmin yüksekliğinden daha büyük olanı kullan
            const beforeImgHeight = doc.y - currentY;

            // After Screenshot'un y pozisyonunu sıfırla
            doc.y = currentY;

            // After Screenshot'un yüksekliğini hesapla
            const afterImgHeight = doc.y - currentY;

            // En büyük yüksekliği kullan
            currentY += Math.max(beforeImgHeight, afterImgHeight) + 30;
            stepsOnCurrentPage++;
          } catch (imgError) {
            // Resim indirilemezse URL'leri göster
            logger.error(`Error downloading images: ${imgError.message}`);
            doc.text(`Before Screenshot URL: ${beforeImageUrl}`, PAGE_MARGIN, currentY);
            doc.text(`After Screenshot URL: ${afterImageUrl}`, PAGE_MARGIN + halfWidth + 20, currentY);
            currentY += 25;
          }
        }
      } catch (imgError) {
        logger.error(`Error adding screenshots: ${imgError.message}`);
      }
    } else {
      // Sadece bir ekran görüntüsü varsa

      // Önceki ekran görüntüsü
      if (step.beforeScreenshotUrl) {
        try {
          const imageUrl = step.beforeScreenshotUrl;

          // Resim URL'si geçerli mi kontrol et
          if (imageUrl && imageUrl.startsWith('http')) {
            // Etiket arka planı
            doc.rect(PAGE_MARGIN - 2, currentY - 2, 120, 16)
               .fillAndStroke('#edf2f7', '#e2e8f0');

            doc.fontSize(10)
               .font('Roboto-Bold')
               .fillColor('#4a5568')
               .text('Before Screenshot:', PAGE_MARGIN, currentY);

            currentY += 20;

            try {
              // URL'den resmi indir
              const imageBuffer = await fetchImageFromUrl(imageUrl);

              // Resim boyutlarını hesapla
              const maxWidth = CONTENT_WIDTH;
              const maxHeight = 250; // Maksimum yükseklik

              // Resim için çerçeve çiz
              doc.rect(PAGE_MARGIN - 2, currentY - 2, maxWidth + 4, maxHeight + 4)
                 .stroke('#e2e8f0');

              // Resmi PDF'e ekle
              doc.image(imageBuffer, PAGE_MARGIN, currentY, {
                fit: [maxWidth, maxHeight],
                align: 'center'
              });

              // Resim yüksekliğine göre y pozisyonunu güncelle
              const imgHeight = Math.min(maxHeight, doc.y - currentY);
              currentY = doc.y + 30; // Resimden sonra daha fazla boşluk
              stepsOnCurrentPage++;
            } catch (imgError) {
              // Resim indirilemezse URL'yi göster
              logger.error(`Error downloading image: ${imgError.message}`);
              doc.text(`Screenshot URL: ${imageUrl}`, PAGE_MARGIN, currentY);
              currentY += 25;
            }
          }
        } catch (imgError) {
          logger.error(`Error adding before screenshot: ${imgError.message}`);
        }
      }

      // Sayfa kontrolü
      if (currentY > doc.page.height - 200 || stepsOnCurrentPage >= STEPS_PER_PAGE) {
        doc.addPage();
        currentY = PAGE_MARGIN;
        stepsOnCurrentPage = 0;
      }

      // Sonraki ekran görüntüsü
      if (step.afterScreenshotUrl) {
        try {
          const imageUrl = step.afterScreenshotUrl;

          // Resim URL'si geçerli mi kontrol et
          if (imageUrl && imageUrl.startsWith('http')) {
            // Etiket arka planı
            doc.rect(PAGE_MARGIN - 2, currentY - 2, 120, 16)
               .fillAndStroke('#edf2f7', '#e2e8f0');

            doc.fontSize(10)
               .font('Roboto-Bold')
               .fillColor('#4a5568')
               .text('After Screenshot:', PAGE_MARGIN, currentY);

            currentY += 20;

            try {
              // URL'den resmi indir
              const imageBuffer = await fetchImageFromUrl(imageUrl);

              // Resim boyutlarını hesapla
              const maxWidth = CONTENT_WIDTH;
              const maxHeight = 250; // Maksimum yükseklik

              // Resim için çerçeve çiz
              doc.rect(PAGE_MARGIN - 2, currentY - 2, maxWidth + 4, maxHeight + 4)
                 .stroke('#e2e8f0');

              // Resmi PDF'e ekle
              doc.image(imageBuffer, PAGE_MARGIN, currentY, {
                fit: [maxWidth, maxHeight],
                align: 'center'
              });

              // Resim yüksekliğine göre y pozisyonunu güncelle
              const imgHeight = Math.min(maxHeight, doc.y - currentY);
              currentY = doc.y + 30; // Resimden sonra daha fazla boşluk
              stepsOnCurrentPage++;
            } catch (imgError) {
              // Resim indirilemezse URL'yi göster
              logger.error(`Error downloading image: ${imgError.message}`);
              doc.text(`Screenshot URL: ${imageUrl}`, PAGE_MARGIN, currentY);
              currentY += 25;
            }
          }
        } catch (imgError) {
          logger.error(`Error adding after screenshot: ${imgError.message}`);
        }
      }
    }

    // Adımlar arasında ayırıcı çizgi
    if (index < report.steps.length - 1) {
      doc.moveTo(PAGE_MARGIN, currentY + 15)
         .lineTo(PAGE_MARGIN + CONTENT_WIDTH, currentY + 15)
         .strokeColor('#e2e8f0')
         .stroke();
      currentY += 40; // Ayırıcı çizgiden sonra daha fazla boşluk
    }
  }
}

/**
 * Sayfa numaralarını ekler
 */
function addPageNumbers(doc) {
  const pageCount = doc.bufferedPageRange().count;

  for (let i = 0; i < pageCount; i++) {
    doc.switchToPage(i);

    doc.fontSize(8)
       .font('Roboto')
       .fillColor('#718096')
       .text(
         `Page ${i + 1} of ${pageCount}`,
         PAGE_MARGIN,
         doc.page.height - 20,
         { align: 'center', width: CONTENT_WIDTH }
       );
  }
}

/**
 * Süreyi formatlar
 * @param {number} duration Milisaniye cinsinden süre
 * @returns {string} Formatlanmış süre
 */
function formatDuration(duration) {
  if (!duration) return 'N/A';

  // Milisaniyeyi saniyeye çevir
  const seconds = duration / 1000;

  if (seconds < 1) {
    return `${Math.round(duration)}ms`;
  } else if (seconds < 60) {
    return `${seconds.toFixed(2)}s`;
  } else {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = (seconds % 60).toFixed(0);
    return `${minutes}m ${remainingSeconds}s`;
  }
}

/**
 * URL'den resim indirir
 * @param {string} url Resim URL'si
 * @returns {Promise<Buffer>} Resim buffer'ı
 */
async function fetchImageFromUrl(url) {
  try {
    // HTTPS sertifika doğrulamasını devre dışı bırak (geliştirme ortamı için)
    const agent = new https.Agent({
      rejectUnauthorized: false
    });

    // Resmi indir
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      httpsAgent: agent,
      timeout: 10000 // 10 saniye timeout
    });

    // Buffer olarak döndür
    return Buffer.from(response.data, 'binary');
  } catch (error) {
    logger.error(`Error fetching image from URL ${url}: ${error.message}`);
    throw error;
  }
}

/**
 * Metin yüksekliğini tahmin eder
 * @param {string} text Metin
 * @param {number} width Genişlik
 * @param {number} fontSize Font boyutu
 * @returns {number} Tahmini yükseklik
 */
function estimateTextHeight(text, width, fontSize) {
  if (!text) return 25;

  // Ortalama karakter genişliği (font boyutuna göre)
  const avgCharWidth = fontSize * 0.5;

  // Satır başına karakter sayısı
  const charsPerLine = Math.floor(width / avgCharWidth);

  // Satır sayısı
  const lines = Math.ceil(text.length / charsPerLine);

  // Satır yüksekliği (font boyutuna göre)
  const lineHeight = fontSize * 1.2;

  // Toplam yükseklik (minimum 25px)
  return Math.max(25, lines * lineHeight);
}
