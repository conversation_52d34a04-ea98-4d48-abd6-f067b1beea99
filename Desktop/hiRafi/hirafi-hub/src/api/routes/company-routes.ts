/**
 * Company Routes
 * Company related endpoints
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import {
  getCompanyById
} from '../../services/mongo/companyService.js';

const router = Router();

/**
 * Şirket bilgilerini getir
 */
router.get('/info', authenticate as any, async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthRequest;
    const companyId = authReq.user?.companyId;

    if (!companyId) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized: Company ID is missing'
      });
    }

    // Şirket bilgilerini getir
    const result = await getCompanyById(companyId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message || 'Company not found'
      });
    }

    // Hassas bilgileri temizle
    const company = result.company;

    // AI Model API anahtarlarını kaldır
    if (company && company.aiModels && Array.isArray(company.aiModels)) {
      // Use type assertion to handle the company object
      const typedCompany = company as unknown as { aiModels: any[] };

      // Create a new array without the apiKey property
      typedCompany.aiModels = typedCompany.aiModels.map(model => {
        // Destructure to remove apiKey
        const { apiKey, ...rest } = model;
        return rest;
      });
    }

    return res.json({
      success: true,
      data: {
        company
      }
    });
  } catch (error: any) {
    logger.error(`API Error in GET /companies/info: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// AI Model endpoints are now in settings-routes.js

export default router;
