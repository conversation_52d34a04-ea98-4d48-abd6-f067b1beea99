/**
 * Common AI Locate Handler
 * Handles executing AI locate steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Locate Handler
 * Handles executing AI locate steps using the AI agent
 */
export class CommonAiLocateHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiLocate';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiLocateHandler';
  }

  /**
   * Execute an AI locate step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiLocate, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const locateQuery = step.prompt || step.value || step.name || step.description;

    if (!locateQuery) {
      return { success: false, error: 'Missing locate instruction for aiLocate step. Please provide a prompt field.' };
    }

    try {
      const options = step.deepThink ? { deepThink: true } : {};

      logger.debug(`CommonAiLocateHandler: Executing locate query "${locateQuery}" with options: ${JSON.stringify(options)}`);

      const result = await agent.aiLocate(locateQuery || '', options);
      logger.debug(`aiLocate result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error && result.result && result.result.rect;

      const logEntry = formatAIResultForLog('aiLocate', locateQuery, result);
      logEntry.success = isSuccess;
      logEntry.metadata.result = result.result;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Locate query failed: ${locateQuery}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime,
          result: result.result
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Locate Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiLocate',
        description: locateQuery,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiLocateHandler: Locate query failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 