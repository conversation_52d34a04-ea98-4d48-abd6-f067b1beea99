/**
 * Jira service module
 * Handles Jira integration operations
 */

import axios from 'axios';
import { logger } from '../../utils/logger.js';
import {
  isMongoDBInitialized,
  pluginsCollection,
  db,
  reportsCollection
} from './dbConnection.js';

// Jira API endpoints and configuration
export const JIRA_CONFIG = {
  apiCreateIssue: '/rest/api/2/issue',
  apiGetProjects: '/rest/api/2/project',
  apiGetIssueTypes: '/rest/api/2/issue/createmeta', // Legacy endpoint for fallback
  apiGetIssueTypesModern: '/rest/api/2/issue/createmeta/{projectKey}/issuetypes', // Modern endpoint
  apiGetMyself: '/rest/api/2/myself',
  // Timeout configurations
  timeouts: {
    connection: 10000, // 10 seconds for connection test
    projects: 15000,   // 15 seconds for projects
    issueTypes: 15000, // 15 seconds for issue types
    createIssue: 20000 // 20 seconds for issue creation
  }
};

/**
 * Test Jira connection with given credentials
 * @param url Jira instance URL
 * @param email User email
 * @param apiToken API token
 * @returns Result of the connection test
 */
export async function verifyJiraConnection(url: string, email: string, apiToken: string) {
  try {
    logger.info(`[JIRA] Testing connection to: ${url} with email: ${email}`);

    // Clean the URL
    const cleanUrl = url.trim().replace(/\/$/, '');

    // Validate URL format - support both Cloud and Server instances
    const isValidJiraUrl = (url: string): boolean => {
      // Jira Cloud pattern: https://yourdomain.atlassian.net
      const cloudPattern = /^https:\/\/[a-zA-Z0-9-]+\.atlassian\.net$/;
      // Jira Server pattern: https://your-server.com or https://your-server.com:port
      const serverPattern = /^https?:\/\/[a-zA-Z0-9.-]+(:[0-9]+)?$/;

      return cloudPattern.test(url) || serverPattern.test(url);
    };

    if (!isValidJiraUrl(cleanUrl)) {
      return {
        success: false,
        message: 'Invalid Jira URL. Please use format: https://yourdomain.atlassian.net (Cloud) or https://your-server.com (Server)',
        errorCode: 'INVALID_URL_FORMAT'
      };
    }

    // Setup authentication
    const auth = {
      username: email.trim(),
      password: apiToken.trim()
    };

    // Test connection by getting current user info
    const testUrl = `${cleanUrl}${JIRA_CONFIG.apiGetMyself}`;
    
    const response = await axios.get(testUrl, {
      auth,
      timeout: JIRA_CONFIG.timeouts.connection,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 200 && response.data) {
      logger.info(`[JIRA] Connection successful for user: ${response.data.displayName || response.data.name}`);
      return {
        success: true,
        message: 'Jira connection successful',
        data: {
          user: response.data.displayName || response.data.name,
          accountId: response.data.accountId,
          emailAddress: response.data.emailAddress
        }
      };
    } else {
      return {
        success: false,
        message: 'Invalid response from Jira API',
        errorCode: 'INVALID_RESPONSE'
      };
    }

  } catch (error: any) {
    logger.error(`[JIRA] Connection test failed: ${error.message}`);

    // Network-level errors
    if (error.code === 'ENOTFOUND') {
      return {
        success: false,
        message: 'Cannot resolve Jira instance hostname. Please check the URL.',
        errorCode: 'NETWORK_ERROR'
      };
    }

    if (error.code === 'ECONNREFUSED') {
      return {
        success: false,
        message: 'Connection refused by Jira instance. Please check the URL and network connectivity.',
        errorCode: 'NETWORK_ERROR'
      };
    }

    if (error.code === 'ETIMEDOUT' || error.code === 'ECONNRESET') {
      return {
        success: false,
        message: 'Connection timeout. Please check your network connection and try again.',
        errorCode: 'TIMEOUT'
      };
    }

    // HTTP response errors
    if (error.response) {
      const status = error.response.status;
      const responseData = error.response.data;

      switch (status) {
        case 401:
          return {
            success: false,
            message: 'Invalid credentials. Please check your email and API token.',
            errorCode: 'AUTHENTICATION_FAILED'
          };
        case 403:
          return {
            success: false,
            message: 'Access denied. Your account may not have sufficient permissions.',
            errorCode: 'ACCESS_DENIED'
          };
        case 404:
          return {
            success: false,
            message: 'Jira instance not found. Please verify the URL is correct.',
            errorCode: 'INSTANCE_NOT_FOUND'
          };
        case 429:
          return {
            success: false,
            message: 'Rate limit exceeded. Please wait a moment and try again.',
            errorCode: 'RATE_LIMITED'
          };
        case 500:
        case 502:
        case 503:
        case 504:
          return {
            success: false,
            message: 'Jira server error. Please try again later.',
            errorCode: 'SERVER_ERROR'
          };
        default:
          return {
            success: false,
            message: `Jira API error (${status}): ${responseData?.message || responseData?.errorMessages?.[0] || error.message}`,
            errorCode: 'API_ERROR'
          };
      }
    }

    return {
      success: false,
      message: `Connection failed: ${error.message}`,
      errorCode: 'UNKNOWN_ERROR'
    };
  }
}

/**
 * Get all projects from Jira
 * @param url Jira instance URL
 * @param email User email
 * @param apiToken API token
 * @returns List of Jira projects
 */
export async function getJiraProjects(url: string, email: string, apiToken: string) {
  try {
    logger.info(`[JIRA] Fetching projects from: ${url}`);

    const cleanUrl = url.trim().replace(/\/$/, '');
    const auth = {
      username: email.trim(),
      password: apiToken.trim()
    };

    const projectsUrl = `${cleanUrl}${JIRA_CONFIG.apiGetProjects}`;
    
    const response = await axios.get(projectsUrl, {
      auth,
      timeout: JIRA_CONFIG.timeouts.projects,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 200 && response.data) {
      const projects = Array.isArray(response.data) ? response.data : [];
      
      logger.info(`[JIRA] Successfully fetched ${projects.length} projects`);
      
      return {
        success: true,
        message: 'Projects fetched successfully',
        data: projects.map((project: any) => ({
          id: project.id,
          key: project.key,
          name: project.name,
          description: project.description || '',
          lead: project.lead ? {
            displayName: project.lead.displayName,
            accountId: project.lead.accountId
          } : null,
          projectTypeKey: project.projectTypeKey || 'software'
        }))
      };
    } else {
      return {
        success: false,
        message: 'Invalid response from Jira projects API'
      };
    }

  } catch (error: any) {
    logger.error(`[JIRA] Error fetching projects: ${error.message}`);

    // Enhanced error handling for projects API
    if (error.response) {
      switch (error.response.status) {
        case 401:
          return {
            success: false,
            message: 'Authentication failed. Please check your credentials.',
            errorCode: 'AUTHENTICATION_FAILED'
          };
        case 403:
          return {
            success: false,
            message: 'Access denied. You may not have permission to view projects.',
            errorCode: 'ACCESS_DENIED'
          };
        case 404:
          return {
            success: false,
            message: 'Projects endpoint not found. Please check your Jira URL.',
            errorCode: 'ENDPOINT_NOT_FOUND'
          };
        case 429:
          return {
            success: false,
            message: 'Rate limit exceeded. Please wait and try again.',
            errorCode: 'RATE_LIMITED'
          };
        default:
          return {
            success: false,
            message: `Jira API error: ${error.response.data?.message || error.message}`,
            errorCode: 'API_ERROR'
          };
      }
    }

    // Network errors
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return {
        success: false,
        message: 'Cannot reach Jira instance. Please check the URL and network connectivity.',
        errorCode: 'NETWORK_ERROR'
      };
    }

    return {
      success: false,
      message: `Error fetching projects: ${error.message}`,
      errorCode: 'UNKNOWN_ERROR'
    };
  }
}

/**
 * Get issue types for a specific project
 * @param url Jira instance URL
 * @param email User email
 * @param apiToken API token
 * @param projectKey Project key
 * @returns List of issue types for the project
 */
export async function getJiraIssueTypes(url: string, email: string, apiToken: string, projectKey: string) {
  try {
    logger.info(`[JIRA] Fetching issue types for project: ${projectKey}`);

    const cleanUrl = url.trim().replace(/\/$/, '');
    const auth = {
      username: email.trim(),
      password: apiToken.trim()
    };

    // Try modern API endpoint first (recommended for Jira Cloud)
    try {
      const modernUrl = `${cleanUrl}/rest/api/2/issue/createmeta/${projectKey}/issuetypes`;

      const response = await axios.get(modernUrl, {
        auth,
        timeout: JIRA_CONFIG.timeouts.issueTypes,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        params: {
          maxResults: 50
        }
      });

      if (response.status === 200 && response.data) {
        const issueTypes = response.data.values || response.data || [];

        logger.info(`[JIRA] Successfully fetched ${issueTypes.length} issue types for project ${projectKey} using modern API`);

        return {
          success: true,
          message: 'Issue types fetched successfully',
          data: issueTypes.map((issueType: any) => ({
            id: issueType.id,
            name: issueType.name,
            description: issueType.description || '',
            iconUrl: issueType.iconUrl,
            subtask: issueType.subtask || false
          }))
        };
      }
    } catch (modernError: any) {
      logger.warn(`[JIRA] Modern API failed, trying legacy: ${modernError.message}`);

      // If modern API fails, try legacy API as fallback
      const legacyUrl = `${cleanUrl}${JIRA_CONFIG.apiGetIssueTypes}`;
      const legacyResponse = await axios.get(legacyUrl, {
        auth,
        timeout: JIRA_CONFIG.timeouts.issueTypes,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        params: {
          projectKeys: projectKey,
          expand: 'projects.issuetypes'
        }
      });

      if (legacyResponse.status === 200 && legacyResponse.data?.projects?.length > 0) {
        const project = legacyResponse.data.projects[0];
        const issueTypes = project.issuetypes || [];

        logger.info(`[JIRA] Successfully fetched ${issueTypes.length} issue types using legacy API`);

        return {
          success: true,
          message: 'Issue types fetched successfully',
          data: issueTypes.map((issueType: any) => ({
            id: issueType.id,
            name: issueType.name,
            description: issueType.description || '',
            iconUrl: issueType.iconUrl,
            subtask: issueType.subtask || false
          }))
        };
      }
    }

    return {
      success: false,
      message: 'No issue types found for the specified project'
    };

  } catch (error: any) {
    logger.error(`[JIRA] Error fetching issue types: ${error.message}`);

    // Enhanced error handling with specific error codes
    if (error.response) {
      switch (error.response.status) {
        case 401:
          return {
            success: false,
            message: 'Authentication failed. Please check your credentials.',
            errorCode: 'AUTHENTICATION_FAILED'
          };
        case 403:
          return {
            success: false,
            message: 'Access denied. Please check your permissions for this project.',
            errorCode: 'ACCESS_DENIED'
          };
        case 404:
          return {
            success: false,
            message: 'Project not found. Please check the project key.',
            errorCode: 'PROJECT_NOT_FOUND'
          };
        default:
          return {
            success: false,
            message: `Jira API error: ${error.response.data?.message || error.message}`,
            errorCode: 'API_ERROR'
          };
      }
    }

    return {
      success: false,
      message: `Error fetching issue types: ${error.message}`,
      errorCode: 'UNKNOWN_ERROR'
    };
  }
}

/**
 * Save Jira configuration for a user/team/company
 * @param userId User ID
 * @param teamId Team ID (optional)
 * @param companyId Company ID (optional)
 * @param url Jira instance URL
 * @param email User email
 * @param apiToken API token
 * @param projectsData Selected projects data
 * @param issueTypesData Selected issue types data
 * @returns Result of the save operation
 */
export async function updateJiraConfig(
  userId: string,
  teamId: string,
  companyId: string,
  url: string,
  email: string,
  apiToken: string,
  projectsData: Array<{id: string|number, key: string, name: string}> = [],
  issueTypesData: Array<{id: string|number, name: string}> = []
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return { success: false, message: "Database connection not established", data: null };
    }

    logger.info(`[JIRA] Saving configuration for user: ${userId}, team: ${teamId}, company: ${companyId}`);

    const pluginConfig = {
      id: 'jira',
      name: 'Jira Integration',
      active: true,
      config: {
        url: url.trim(),
        email: email.trim(),
        apiToken: apiToken.trim(),
        projectsData: projectsData.map(p => ({
          id: p.id.toString(),
          key: p.key,
          name: p.name
        })),
        issueTypesData: issueTypesData.map(it => ({
          id: it.id.toString(),
          name: it.name
        }))
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Create the filter for finding existing configuration
    const filter: any = {};
    if (teamId && companyId) {
      filter.teamId = teamId;
      filter.companyId = companyId;
    } else {
      filter.userId = userId;
    }
    filter['plugins.id'] = 'jira';

    // Check if plugin configuration already exists
    const existingPlugin = await pluginsCollection.findOne(filter);

    if (existingPlugin) {
      // Update existing configuration
      const result = await pluginsCollection.updateOne(
        filter,
        {
          $set: {
            'plugins.$.config': pluginConfig.config,
            'plugins.$.active': true,
            'plugins.$.updatedAt': new Date()
          }
        }
      );

      if (result.modifiedCount > 0) {
        logger.info(`[JIRA] Successfully updated configuration for user: ${userId}`);
        return {
          success: true,
          message: 'Jira configuration updated successfully',
          data: pluginConfig
        };
      } else {
        return {
          success: false,
          message: 'Failed to update Jira configuration'
        };
      }
    } else {
      // Create new plugin document or add to existing plugins array
      const baseDocument: any = {
        plugins: [pluginConfig],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      if (teamId && companyId) {
        baseDocument.teamId = teamId;
        baseDocument.companyId = companyId;
      } else {
        baseDocument.userId = userId;
      }

      // Check if document exists without this plugin
      const existingDoc = await pluginsCollection.findOne(
        teamId && companyId 
          ? { teamId, companyId }
          : { userId }
      );

      if (existingDoc) {
        // Add plugin to existing document
        const result = await pluginsCollection.updateOne(
          teamId && companyId 
            ? { teamId, companyId }
            : { userId },
          {
            $push: { plugins: pluginConfig } as any,
            $set: { updatedAt: new Date() }
          }
        );

        if (result.modifiedCount > 0) {
          logger.info(`[JIRA] Successfully added configuration to existing document for user: ${userId}`);
          return {
            success: true,
            message: 'Jira configuration saved successfully',
            data: pluginConfig
          };
        }
      } else {
        // Create new document
        const result = await pluginsCollection.insertOne(baseDocument);

        if (result.insertedId) {
          logger.info(`[JIRA] Successfully created new configuration for user: ${userId}`);
          return {
            success: true,
            message: 'Jira configuration saved successfully',
            data: pluginConfig
          };
        }
      }

      return {
        success: false,
        message: 'Failed to save Jira configuration'
      };
    }

  } catch (error: any) {
    logger.error(`[JIRA] Error saving configuration: ${error.message}`);
    return {
      success: false,
      message: `Error saving configuration: ${error.message}`
    };
  }
}

/**
 * Get Jira configuration for a user/team/company
 * @param teamId Team ID (optional)
 * @param companyId Company ID (optional)
 * @param userId User ID (optional)
 * @returns Jira configuration
 */
export async function getJiraConfig(teamId?: string, companyId?: string, userId?: string) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return { success: false, message: "Database connection not established", data: null };
    }

    logger.info(`[JIRA] Getting configuration for team: ${teamId}, company: ${companyId}, user: ${userId}`);

    const filter: any = {};
    if (teamId && companyId) {
      filter.teamId = teamId;
      filter.companyId = companyId;
    } else if (userId) {
      filter.userId = userId;
    } else {
      return {
        success: false,
        message: 'Either teamId+companyId or userId must be provided'
      };
    }

    const pluginDoc = await pluginsCollection.findOne(filter);

    if (!pluginDoc || !pluginDoc.plugins) {
      return {
        success: false,
        message: 'No Jira configuration found'
      };
    }

    const jiraPlugin = pluginDoc.plugins.find((p: any) => p.id === 'jira');

    if (!jiraPlugin) {
      return {
        success: false,
        message: 'Jira plugin not configured'
      };
    }

    logger.info(`[JIRA] Successfully retrieved configuration`);
    
    return {
      success: true,
      message: 'Jira configuration retrieved successfully',
      data: {
        plugin: jiraPlugin,
        lastUpdated: jiraPlugin.updatedAt
      }
    };

  } catch (error: any) {
    logger.error(`[JIRA] Error getting configuration: ${error.message}`);
    return {
      success: false,
      message: `Error getting configuration: ${error.message}`
    };
  }
}

/**
 * Delete Jira configuration
 * @param teamId Team ID
 * @param companyId Company ID
 * @param userId User ID (optional)
 * @returns Result of the delete operation
 */
export async function deleteJiraConfig(teamId?: string, companyId?: string, userId?: string) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection) {
      return { success: false, message: "Database connection not established", data: null };
    }

    logger.info(`[JIRA] Deleting configuration for team: ${teamId}, company: ${companyId}, user: ${userId}`);

    const filter: any = {};
    if (teamId && companyId) {
      filter.teamId = teamId;
      filter.companyId = companyId;
    } else if (userId) {
      filter.userId = userId;
    } else {
      return {
        success: false,
        message: 'Either teamId+companyId or userId must be provided'
      };
    }

    // Remove the jira plugin from the plugins array
    const result = await pluginsCollection.updateOne(
      filter,
      {
        $pull: { plugins: { id: 'jira' } } as any,
        $set: { updatedAt: new Date() }
      }
    );

    if (result.modifiedCount > 0) {
      logger.info(`[JIRA] Successfully deleted configuration`);
      return {
        success: true,
        message: 'Jira configuration deleted successfully'
      };
    } else {
      return {
        success: false,
        message: 'No Jira configuration found to delete'
      };
    }

  } catch (error: any) {
    logger.error(`[JIRA] Error deleting configuration: ${error.message}`);
    return {
      success: false,
      message: `Error deleting configuration: ${error.message}`
    };
  }
}

/**
 * Create a Jira issue from test report
 * @param reportData Test report data
 * @param pluginConfig Jira plugin configuration
 * @param projectKey Jira project key
 * @param issueTypeId Issue type ID
 * @param summary Issue summary
 * @param description Optional description override
 * @returns Result of the issue creation
 */
export async function createJiraIssue(
  reportData: any,
  pluginConfig: any,
  projectKey: string,
  issueTypeId: string,
  summary: string,
  description?: string
) {
  try {
    if (!pluginConfig || !pluginConfig.url || !pluginConfig.email || !pluginConfig.apiToken) {
      return {
        success: false,
        message: "Missing Jira configuration",
        data: null
      };
    }

    logger.info(`[JIRA] Creating issue in project: ${projectKey}, type: ${issueTypeId}`);

    const cleanUrl = pluginConfig.url.trim().replace(/\/$/, '');
    const auth = {
      username: pluginConfig.email.trim(),
      password: pluginConfig.apiToken.trim()
    };

    // Prepare issue description
    let issueDescription = description || `# Test Execution Report\n\n`;
    issueDescription += `**Report ID:** ${reportData.id}\n`;
    issueDescription += `**Status:** ${reportData.status === 'failed' ? '❌ Failed' : reportData.status === 'skipped' ? '⚠️ Skipped' : '✅ Passed'}\n`;
    
    if (reportData.scenarioName) {
      issueDescription += `**Scenario:** ${reportData.scenarioName}\n`;
    }

    if (reportData.duration) {
      const totalSeconds = reportData.duration / 1000;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = Math.floor(totalSeconds % 60);
      issueDescription += `**Duration:** ${minutes > 0 ? `${minutes}m ${seconds}s` : `${totalSeconds.toFixed(2)}s`}\n`;
    }

    if (reportData.summary) {
      issueDescription += `\n## Summary\n`;
      issueDescription += `- Total Steps: ${reportData.summary.total || 0}\n`;
      issueDescription += `- Passed: ${reportData.summary.passed || 0}\n`;
      issueDescription += `- Failed: ${reportData.summary.failed || 0}\n`;
      issueDescription += `- Errors: ${reportData.summary.errors || 0}\n`;
    }

    if (reportData.error) {
      issueDescription += `\n## Error Details\n`;
      issueDescription += `{code}\n${reportData.error}\n{code}\n`;
    }

    // Add link to detailed report
    const reportUrl = `https://hirafi.ai/reports/${reportData.id}`;
    issueDescription += `\n## Links\n`;
    issueDescription += `[View Detailed Report|${reportUrl}]\n`;

    // Create issue payload
    const issueData = {
      fields: {
        project: {
          key: projectKey
        },
        summary: summary,
        description: issueDescription,
        issuetype: {
          id: issueTypeId
        }
      }
    };

    const createUrl = `${cleanUrl}${JIRA_CONFIG.apiCreateIssue}`;
    
    const response = await axios.post(createUrl, issueData, {
      auth,
      timeout: JIRA_CONFIG.timeouts.createIssue,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 201 && response.data) {
      const issue = response.data;
      const issueUrl = `${cleanUrl}/browse/${issue.key}`;
      
      logger.info(`[JIRA] Successfully created issue: ${issue.key}`);
      
      return {
        success: true,
        message: 'Jira issue created successfully',
        data: {
          id: issue.id,
          key: issue.key,
          url: issueUrl,
          self: issue.self
        }
      };
    } else {
      return {
        success: false,
        message: 'Invalid response from Jira create issue API'
      };
    }

  } catch (error: any) {
    logger.error(`[JIRA] Error creating issue: ${error.message}`);
    
    if (error.response) {
      logger.error(`[JIRA] Response status: ${error.response.status}`);
      logger.error(`[JIRA] Response data: ${JSON.stringify(error.response.data)}`);
      
      if (error.response.status === 400 && error.response.data?.errors) {
        const errors = Object.values(error.response.data.errors).join(', ');
        return {
          success: false,
          message: `Jira validation errors: ${errors}`
        };
      }
    }

    return {
      success: false,
      message: `Error creating Jira issue: ${error.message}`
    };
  }
}

/**
 * Send test report to Jira by creating an issue
 * @param reportId Report ID
 * @param userId User ID
 * @param projectKey Jira project key
 * @param issueTypeId Issue type ID
 * @param summary Issue summary
 * @param description Optional description
 * @returns Result of the operation
 */
export async function sendReportToJira(
  reportId: string,
  userId: string,
  projectKey: string,
  issueTypeId: string,
  summary: string,
  description?: string
) {
  try {
    if (!isMongoDBInitialized() || !db || !pluginsCollection || !reportsCollection) {
      return { success: false, message: "Database connection not established", data: null };
    }

    // Get user's Jira configuration
    const configResult = await getJiraConfig(undefined, undefined, userId);

    if (!configResult.success || !configResult.data?.plugin?.config) {
      return {
        success: false,
        message: "Jira plugin not configured for this user",
        data: null
      };
    }

    // Get report data
    const report = await reportsCollection.findOne({ id: reportId });

    if (!report) {
      return {
        success: false,
        message: `Report with ID ${reportId} not found`,
        data: null
      };
    }

    // Create Jira issue
    const result = await createJiraIssue(
      report,
      configResult.data.plugin.config,
      projectKey,
      issueTypeId,
      summary,
      description
    );

    if (result.success) {
      // Update report with Jira issue information
      await reportsCollection.updateOne(
        { id: reportId },
        {
          $set: {
            jiraIssue: {
              key: result.data?.key,
              url: result.data?.url,
              createdAt: new Date()
            },
            updatedAt: new Date()
          }
        }
      );

      logger.info(`[JIRA] Report ${reportId} linked to Jira issue ${result.data?.key}`);
    }

    return result;

  } catch (error: any) {
    logger.error(`[JIRA] Error sending report to Jira: ${error.message}`);
    return {
      success: false,
      message: `Error sending report to Jira: ${error.message}`,
      data: null
    };
  }
} 