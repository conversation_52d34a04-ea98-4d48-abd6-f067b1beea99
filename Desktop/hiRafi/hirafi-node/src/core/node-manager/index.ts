/**
 * Node Manager Module - Refactored Architecture
 * 
 * This module provides a modern, dependency injection-based node manager
 * that eliminates singleton patterns and promotes proper service architecture.
 * 
 * Key improvements:
 * - Dependency injection throughout
 * - Interface-based design
 * - Single responsibility principle
 * - Proper service lifecycle management
 * - Easy to test and extend
 */

// Core exports
export { NodeManager } from './core/node-manager.js';

// Factory exports (primary API)
export { NodeManagerFactory, createNodeManager } from './factories/node-manager-factory.js';
export { PauseServiceFactory, createPauseService } from './factories/pause-service-factory.js';

// Interface exports (for typing and mocking)
export * from './interfaces/connection-service.interface.js';
export * from './interfaces/event-service.interface.js';
export * from './interfaces/node-identity-service.interface.js';
export * from './interfaces/node-manager.interface.js';
export * from './interfaces/node-manager-factory.interface.js';
export * from './interfaces/node-state-service.interface.js';
export * from './interfaces/pause-service.interface.js';
export * from './interfaces/periodic-task-service.interface.js';
export * from './interfaces/service-registry.interface.js';

// Type exports
export * from './types/node-manager-types.js';

// Service exports (for advanced usage)
export { NodeIdentityService } from './services/identity/node-identity-service.js';
export { AtomicStateManager } from './services/state/atomic-state-manager.js';
export { NodeStateService } from './services/state/node-state-service.js';
export { ConnectionService } from './services/connection/connection-service.js';
export { PeriodicTaskService } from './services/tasks/periodic-task-service.js';
export { EventService } from './services/events/event-service.js';
export { PauseService } from './services/pause/pause-service.js';

// Backward compatibility - create a default instance using the factory
import { NodeManagerFactory } from './factories/node-manager-factory.js';
import { logger } from '../../utils/logger.js';
import { ApplicationContainer } from '../common/di/application-container.js';

/**
 * Backward compatibility function that mimics the old singleton pattern
 * while using the new dependency injection architecture under the hood.
 * 
 * This allows existing code to continue working without changes.
 */
class NodeManagerFacade {
  private static nodeManager: any = null;

  /**
   * Initialize the node manager (lazy initialization)
   */
  private async ensureNodeManager(container: ApplicationContainer): Promise<void> {
    if (!NodeManagerFacade.nodeManager) {
      try {
        NodeManagerFacade.nodeManager = await NodeManagerFactory.createNodeManager({
          enableLogging: false
        }, container);
      } catch (error: any) {
        logger.error(`NodeManagerFacade: Error creating node manager: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Start the node manager
   */
  async start(container: ApplicationContainer): Promise<boolean> {
    await this.ensureNodeManager(container);
    return NodeManagerFacade.nodeManager.start();
  }

  /**
   * Stop the node manager
   */
  async stop(): Promise<void> {
    if (NodeManagerFacade.nodeManager) {
      await NodeManagerFacade.nodeManager.stop();
    }
  }

  /**
   * Stop a running test
   */
  async stopTest(testId: string): Promise<boolean> {
    if (!NodeManagerFacade.nodeManager) {
      // If called before start, we can't do anything
      logger.warn(`NodeManagerFacade.stopTest called for ${testId} before node manager was initialized.`);
      return false;
    }
    return NodeManagerFacade.nodeManager.stopTest(testId);
  }

  /**
   * Get current node status
   */
  getStatus(): string {
    if (!NodeManagerFacade.nodeManager) {
      return 'offline';
    }
    return NodeManagerFacade.nodeManager.getStatus();
  }

  /**
   * Get node ID
   */
  getNodeId(): string | null {
    if (!NodeManagerFacade.nodeManager) {
      return null;
    }
    return NodeManagerFacade.nodeManager.getNodeId();
  }

  /**
   * Check if node is running a test
   */
  isRunningTest(): boolean {
    if (!NodeManagerFacade.nodeManager) {
      return false;
    }
    return NodeManagerFacade.nodeManager.isRunningTest();
  }

  /**
   * Get test statistics
   */
  getTestStatistics(): {
    executed: number;
    succeeded: number;
    failed: number;
    uptime: number;
  } {
    if (!NodeManagerFacade.nodeManager) {
      return {
        executed: 0,
        succeeded: 0,
        failed: 0,
        uptime: 0
      };
    }
    return NodeManagerFacade.nodeManager.getTestStatistics();
  }

  /**
   * Get the test execution manager instance
   */
  getTestExecutionManager(): any | null {
    if (!NodeManagerFacade.nodeManager) {
      return null;
    }
    return NodeManagerFacade.nodeManager.getTestExecutionManager();
  }

  /**
   * Add event listener (EventEmitter compatibility)
   */
  on(event: string, listener: (...args: any[]) => void): this {
    if (NodeManagerFacade.nodeManager) {
      NodeManagerFacade.nodeManager.on(event, listener);
    }
    return this;
  }

  /**
   * Remove event listener (EventEmitter compatibility)
   */
  off(event: string, listener: (...args: any[]) => void): this {
    if (NodeManagerFacade.nodeManager) {
      NodeManagerFacade.nodeManager.off(event, listener);
    }
    return this;
  }

  /**
   * Emit event (EventEmitter compatibility)
   */
  emit(event: string, ...args: any[]): boolean {
    if (NodeManagerFacade.nodeManager) {
      return NodeManagerFacade.nodeManager.emit(event, ...args);
    }
    return false;
  }
}

// Export the facade as default for backward compatibility
// Create a single instance directly without using the deprecated getInstance method
const defaultNodeManagerFacade = new NodeManagerFacade();
export default defaultNodeManagerFacade;
