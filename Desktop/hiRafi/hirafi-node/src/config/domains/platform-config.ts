/**
 * Platform Configuration Domain
 * Handles web and Android platform configuration settings
 * Note: Appium and Sauce Labs settings are provided dynamically in test requests
 */

import { getEnvBoolean } from '../utils/env-utils.js';
import {
  PlatformConfigDomain,
  WebPlatformConfigDomain,
  AndroidPlatformConfigDomain
} from '../types/config-types.js';

/**
 * Creates web platform configuration
 * @returns Web platform configuration object
 */
export function createWebPlatformConfig(): WebPlatformConfigDomain {
  return {
    enabled: getEnvBoolean('WEB_PLATFORM_ENABLED', true)
  };
}

/**
 * Creates Android platform configuration
 * Note: Appium and Sauce Labs settings are provided dynamically in test requests
 * @returns Android platform configuration object
 */
export function createAndroidPlatformConfig(): AndroidPlatformConfigDomain {
  return {
    enabled: getEnvBoolean('ANDROID_PLATFORM_ENABLED', false)
  };
}

/**
 * Creates complete platform configuration from environment variables
 * @returns Platform configuration object
 */
export function createPlatformConfig(): PlatformConfigDomain {
  return {
    web: createWebPlatformConfig(),
    android: createAndroidPlatformConfig()
  };
}

/**
 * Validates Android platform configuration
 * Note: Appium and Sauce Labs validation is handled dynamically in test requests
 * @param config Android platform configuration to validate
 * @returns Validation result
 */
export function validateAndroidPlatformConfig(config: AndroidPlatformConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.enabled) {
    return { isValid: true, errors: [] }; // Skip validation if disabled
  }

  // Basic validation - detailed Appium/Sauce Labs validation happens in test requests
  // No additional validation needed here since dynamic settings are provided at runtime

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates platform configuration
 * @param config Platform configuration to validate
 * @returns Validation result
 */
export function validatePlatformConfig(config: PlatformConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate Android platform configuration
  const androidValidation = validateAndroidPlatformConfig(config.android);
  if (!androidValidation.isValid) {
    errors.push(...androidValidation.errors);
  }

  // Check that at least one platform is enabled
  if (!config.web.enabled && !config.android.enabled) {
    errors.push('At least one platform (web or android) must be enabled');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets default platform configuration
 * Note: Appium and Sauce Labs settings are provided dynamically in test requests
 * @returns Default platform configuration
 */
export function getDefaultPlatformConfig(): PlatformConfigDomain {
  return {
    web: { enabled: true },
    android: {
      enabled: false
      // Dynamic settings (appium, capabilities, sauceLabs) are provided in test requests
    }
  };
}
