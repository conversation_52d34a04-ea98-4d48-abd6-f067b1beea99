/**
 * Message Handler Interface
 * Defines the contract for message handlers
 */

import { EventEmitter } from 'events';

/**
 * Message handler interface
 */
export interface IMessageHandler extends EventEmitter {
  /**
   * Handle incoming message
   * @param message Message data
   */
  handleMessage(message: any): void;

  /**
   * Register message handler for specific type
   * @param messageType Message type
   * @param handler Handler function
   */
  registerHandler(messageType: string, handler: (data: any) => void): void;

  /**
   * Unregister message handler for specific type
   * @param messageType Message type
   */
  unregisterHandler(messageType: string): void;

  /**
   * Get registered message types
   */
  getRegisteredTypes(): string[];

  /**
   * Get handler statistics
   */
  getStats(): Record<string, any>;

  /**
   * Enable/disable message validation
   * @param enabled Whether to enable validation
   */
  setValidationEnabled(enabled: boolean): void;

  /**
   * Set message validator
   * @param validator Validation function
   */
  setMessageValidator(validator: (message: any) => boolean): void;
}

/**
 * Message handler configuration interface
 */
export interface IMessageHandlerConfig {
  enableLogging?: boolean;
  enableValidation?: boolean;
  maxMessageSize?: number;
  messageTimeout?: number;
  validator?: (message: any) => boolean;
}

/**
 * Message handler statistics interface
 */
export interface IMessageHandlerStats {
  totalMessages: number;
  messagesByType: Record<string, number>;
  validMessages: number;
  invalidMessages: number;
  handledMessages: number;
  unhandledMessages: number;
  averageProcessingTime: number;
  lastMessageTime?: string;
  errors: number;
}

/**
 * Message validation result interface
 */
export interface IMessageValidationResult {
  valid: boolean;
  errors?: string[];
  warnings?: string[];
}

/**
 * Message processing result interface
 */
export interface IMessageProcessingResult {
  success: boolean;
  messageType: string;
  processingTime: number;
  error?: string;
  data?: any;
}

/**
 * Message event types
 */
export enum MessageEventType {
  MESSAGE_RECEIVED = 'messageReceived',
  MESSAGE_PROCESSED = 'messageProcessed',
  MESSAGE_ERROR = 'messageError',
  VALIDATION_FAILED = 'validationFailed',
  HANDLER_REGISTERED = 'handlerRegistered',
  HANDLER_UNREGISTERED = 'handlerUnregistered'
}
