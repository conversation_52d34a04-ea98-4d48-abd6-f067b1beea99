"use client"

import { useState, useEffect } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { RefreshCw, Server, Activity, Clock, AlertTriangle, CheckCircle, XCircle, Loader2, Trash2, Check, Zap } from "lucide-react"
import { adminApi } from "@/lib/api"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { formatDistanceToNow } from "date-fns"
import { tr } from "date-fns/locale"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  Al<PERSON><PERSON><PERSON>og<PERSON>ontent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON>ialog<PERSON><PERSON><PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { toast } from "@/lib/utils/toast-utils"

interface QueueMonitorData {
  timestamp: string
  queueStatus: {
    queued: number
    running: number
    completed: number
    failed: number
  }
  queuedTests: Array<{
    id: string
    scenarioId: string
    scenarioName: string
    queuedAt: string
    priority: number
    queuedTimeSeconds: number
  }>
  runningTests: Array<{
    id: string
    scenarioId: string
    scenarioName: string
    startedAt: string
    nodeId: string
    runningTimeSeconds: number
  }>
  stuckTests: Array<{
    id: string
    scenarioId: string
    scenarioName: string
    status: string
    queuedAt: string
    startedAt: string
    nodeId: string
    stuckTimeSeconds: number
  }>
  redisQueues: Record<string, any>
  queueHealth: {
    oldestQueuedTest: {
      id: string
      scenarioName: string
      queuedAt: string
      queuedTimeSeconds: number
    } | null
    longestRunningTest: {
      id: string
      scenarioName: string
      startedAt: string
      nodeId: string
      runningTimeSeconds: number
    } | null
    queuedTimeAvg: number
    runningTimeAvg: number
  }
}

// API response wrapper interface
interface QueueMonitorApiResponse {
  success: boolean
  data: QueueMonitorData
  error?: string
}

export default function QueueMonitorPage() {
  const [monitorData, setMonitorData] = useState<QueueMonitorData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [deletingTests, setDeletingTests] = useState<Set<string>>(new Set())
  const [completingTests, setCompletingTests] = useState<Set<string>>(new Set())
  const [nuclearClearing, setNuclearClearing] = useState(false)

  const fetchMonitorData = async () => {
    try {
      setRefreshing(true)
      setError(null) // Clear previous errors
      const response = await adminApi.getQueueMonitorData() as QueueMonitorApiResponse

      if (response.success && response.data) {
        // Access the data through the data property due to fetch-wrapper behavior
        setMonitorData(response.data)
      } else {
        const errorMessage = response.error || "Unknown error occurred while fetching queue monitor data"
        setError(errorMessage)
      }
      setRefreshing(false)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      setError(errorMessage)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      await fetchMonitorData()
      setIsLoading(false)
    }

    loadData()

    // Her 30 saniyede bir otomatik yenile
    const interval = setInterval(() => {
      fetchMonitorData()
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  const handleRefresh = () => {
    fetchMonitorData()
  }

  const handleDeleteStuckTest = async (testId: string) => {
    try {
      setDeletingTests(prev => new Set(prev).add(testId))

      const response = await adminApi.deleteStuckTest(testId)

      if (response.success) {
        toast.success('Sıkışmış test başarıyla silindi')
        // Refresh the data to reflect the changes
        await fetchMonitorData()
      } else {
        toast.error(response.error || 'Test silinirken bir hata oluştu')
      }
    } catch (error) {
      toast.error('Test silinirken beklenmeyen bir hata oluştu')
    } finally {
      setDeletingTests(prev => {
        const newSet = new Set(prev)
        newSet.delete(testId)
        return newSet
      })
    }
  }

  const handleCompleteTest = async (testId: string) => {
    try {
      setCompletingTests(prev => new Set(prev).add(testId))

      const response = await adminApi.completeTest(testId)

      if (response.success) {
        toast.success('Test başarıyla tamamlandı')
        // Refresh the data to reflect the changes
        await fetchMonitorData()
      } else {
        toast.error(response.error || 'Test tamamlanırken bir hata oluştu')
      }
    } catch (error) {
      toast.error('Test tamamlanırken beklenmeyen bir hata oluştu')
    } finally {
      setCompletingTests(prev => {
        const newSet = new Set(prev)
        newSet.delete(testId)
        return newSet
      })
    }
  }

  const handleNuclearClear = async () => {
    try {
      setNuclearClearing(true)

      const response = await adminApi.nuclearClearQueue('test-queue')

      if (response.success) {
        toast.success('Kuyruk başarıyla temizlendi (Nuclear Clear)')
        // Refresh data after nuclear clear
        await fetchMonitorData()
      } else {
        toast.error(response.error || 'Nuclear clear işlemi başarısız oldu')
      }
    } catch (error) {
      toast.error('Nuclear clear işlemi sırasında beklenmeyen bir hata oluştu')
    } finally {
      setNuclearClearing(false)
    }
  }

  // Format time in seconds to a readable format
  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds} saniye`
    if (seconds < 3600) return `${Math.floor(seconds / 60)} dakika ${seconds % 60} saniye`
    return `${Math.floor(seconds / 3600)} saat ${Math.floor((seconds % 3600) / 60)} dakika`
  }

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return <Badge className="bg-blue-500">Çalışıyor</Badge>
      case 'queued':
        return <Badge variant="outline" className="text-yellow-500 border-yellow-500">Kuyrukta</Badge>
      case 'completed':
        return <Badge className="bg-green-500">Tamamlandı</Badge>
      case 'failed':
        return <Badge className="bg-red-500">Başarısız</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get queue state badge color
  const getQueueStateBadge = (jobState: string) => {
    switch (jobState) {
      case 'waiting':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Bekliyor</Badge>
      case 'prioritized':
        return <Badge className="bg-purple-500">Öncelikli</Badge>
      case 'delayed':
        return <Badge variant="outline" className="text-orange-500 border-orange-500">Gecikmeli</Badge>
      default:
        return <Badge variant="outline">{jobState}</Badge>
    }
  }

  // Get time badge color based on duration
  const getTimeBadge = (seconds: number, isStuck: boolean = false) => {
    if (isStuck || seconds > 300) {
      return <Badge className="bg-red-500">{formatTime(seconds)}</Badge>
    }
    if (seconds > 60) {
      return <Badge className="bg-yellow-500">{formatTime(seconds)}</Badge>
    }
    return <Badge className="bg-green-500">{formatTime(seconds)}</Badge>
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Kuyruk İzleme</h1>
            <p className="text-muted-foreground">
              Test kuyruğu durumunu ve test dağıtımını izleyin
            </p>
          </div>
          <Button onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Yenileniyor...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Yenile
              </>
            )}
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-2 text-lg">Yükleniyor...</span>
          </div>
        ) : (
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
              <TabsTrigger value="queued">Kuyrukta Bekleyen Testler</TabsTrigger>
              <TabsTrigger value="running">Çalışan Testler</TabsTrigger>
              <TabsTrigger value="stuck">Takılmış Testler</TabsTrigger>
              <TabsTrigger value="redis">Redis Kuyrukları</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Kuyrukta Bekleyen</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{monitorData?.queueStatus.queued || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Kuyrukta bekleyen test sayısı
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Çalışan Testler</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{monitorData?.queueStatus.running || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Şu anda çalışan test sayısı
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Tamamlanan Testler</CardTitle>
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{monitorData?.queueStatus.completed || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Başarıyla tamamlanan test sayısı
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Başarısız Testler</CardTitle>
                    <XCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{monitorData?.queueStatus.failed || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Başarısız olan test sayısı
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Queue Health Card */}
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Kuyruk Sağlığı</CardTitle>
                    <CardDescription>
                      Kuyruk performansı ve sağlık metrikleri
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {monitorData?.queueHealth?.oldestQueuedTest && (
                        <div>
                          <h3 className="text-sm font-medium mb-2">En Eski Kuyrukta Bekleyen Test</h3>
                          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                            <div className="font-medium">{monitorData.queueHealth.oldestQueuedTest.scenarioName}</div>
                            <div className="text-sm text-muted-foreground mt-1">
                              ID: {monitorData.queueHealth.oldestQueuedTest.id}
                            </div>
                            <div className="flex items-center mt-2">
                              <Clock className="h-4 w-4 text-yellow-500 mr-1" />
                              <span className="text-sm">
                                {formatTime(monitorData.queueHealth.oldestQueuedTest.queuedTimeSeconds)} bekliyor
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {monitorData?.queueHealth?.longestRunningTest && (
                        <div>
                          <h3 className="text-sm font-medium mb-2">En Uzun Çalışan Test</h3>
                          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                            <div className="font-medium">{monitorData.queueHealth.longestRunningTest.scenarioName}</div>
                            <div className="text-sm text-muted-foreground mt-1">
                              ID: {monitorData.queueHealth.longestRunningTest.id}
                            </div>
                            <div className="flex items-center mt-2">
                              <Activity className="h-4 w-4 text-blue-500 mr-1" />
                              <span className="text-sm">
                                {formatTime(monitorData.queueHealth.longestRunningTest.runningTimeSeconds)} çalışıyor
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div>
                          <h3 className="text-sm font-medium mb-2">Ortalama Kuyrukta Bekleme</h3>
                          <div className="text-xl font-bold">
                            {formatTime(monitorData?.queueHealth?.queuedTimeAvg || 0)}
                          </div>
                        </div>
                        <div>
                          <h3 className="text-sm font-medium mb-2">Ortalama Çalışma Süresi</h3>
                          <div className="text-xl font-bold">
                            {formatTime(monitorData?.queueHealth?.runningTimeAvg || 0)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Takılmış Testler</CardTitle>
                    <CardDescription>
                      Uzun süredir kuyrukta bekleyen veya çalışan testler
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {monitorData?.stuckTests && monitorData.stuckTests.length > 0 ? (
                      <div className="space-y-3">
                        {monitorData.stuckTests.slice(0, 5).map((test) => (
                          <div key={test.id} className="bg-red-50 border border-red-200 rounded-md p-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="font-medium">{test.scenarioName}</div>
                                <div className="text-sm text-muted-foreground mt-1">
                                  ID: {test.id}
                                </div>
                                <div className="flex items-center justify-between mt-2">
                                  <div className="flex items-center">
                                    <AlertTriangle className="h-4 w-4 text-red-500 mr-1" />
                                    <span className="text-sm">
                                      {getStatusBadge(test.status)}
                                    </span>
                                  </div>
                                  <span className="text-sm text-red-500">
                                    {formatTime(test.stuckTimeSeconds)}
                                  </span>
                                </div>
                              </div>
                              <div className="flex gap-1 ml-2">
                                {/* Complete Button */}
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-100"
                                      disabled={completingTests.has(test.id)}
                                    >
                                      {completingTests.has(test.id) ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Check className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Sıkışmış Testi Tamamla</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Bu işlem test ID: <code className="bg-muted px-1 py-0.5 rounded text-sm">{test.id}</code> olan sıkışmış testi zorla tamamlayacak ve completed durumuna geçirecek.
                                        <br /><br />
                                        <strong>Senaryo:</strong> {test.scenarioName}
                                        <br />
                                        <strong>Durum:</strong> {test.status}
                                        <br />
                                        <strong>Takılma Süresi:</strong> {formatTime(test.stuckTimeSeconds)}
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>İptal</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleCompleteTest(test.id)}
                                        className="bg-green-600 text-white hover:bg-green-700"
                                      >
                                        Tamamla
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>

                                {/* Delete Button */}
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-100"
                                      disabled={deletingTests.has(test.id)}
                                    >
                                      {deletingTests.has(test.id) ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Sıkışmış Testi Sil</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Bu işlem geri alınamaz. Test ID: <code className="bg-muted px-1 py-0.5 rounded text-sm">{test.id}</code> olan sıkışmış test silinecek ve tüm ilişkili kaynaklar temizlenecek.
                                        <br /><br />
                                        <strong>Senaryo:</strong> {test.scenarioName}
                                        <br />
                                        <strong>Durum:</strong> {test.status}
                                        <br />
                                        <strong>Takılma Süresi:</strong> {formatTime(test.stuckTimeSeconds)}
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>İptal</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleDeleteStuckTest(test.id)}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        Sil
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </div>
                          </div>
                        ))}
                        {monitorData.stuckTests.length > 5 && (
                          <div className="text-center text-sm text-muted-foreground mt-2">
                            +{monitorData.stuckTests.length - 5} daha fazla takılmış test
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                        <CheckCircle className="h-8 w-8 text-green-500 mb-2" />
                        <p>Takılmış test bulunmuyor</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Nuclear Clear Section */}
              <Card className="border-red-200 bg-red-50">
                <CardHeader>
                  <CardTitle className="text-red-700 flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Nuclear Clear (Son Çare)
                  </CardTitle>
                  <CardDescription className="text-red-600">
                    Kuyrukta takılmış testler varsa ve normal yöntemler çalışmıyorsa bu seçeneği kullanın.
                    Bu işlem tüm güvenlik kontrollerini bypass eder ve kuyruktaki HER ŞEYİ siler.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-red-100 border border-red-300 rounded-md p-4">
                      <div className="flex items-start gap-3">
                        <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-red-800">
                          <p className="font-semibold mb-2">⚠️ UYARI: Bu işlem GERİ ALINAMAZ!</p>
                          <ul className="list-disc list-inside space-y-1">
                            <li>Tüm kuyrukta bekleyen testleri siler</li>
                            <li>Tüm aktif testleri zorla sonlandırır</li>
                            <li>Tüm Redis lock'larını temizler</li>
                            <li>Tüm test durumlarını sıfırlar</li>
                            <li>Tüm güvenlik kontrollerini bypass eder</li>
                          </ul>
                          <p className="mt-2 font-semibold">
                            Sadece kuyruk tamamen takıldığında ve başka çözüm kalmadığında kullanın!
                          </p>
                        </div>
                      </div>
                    </div>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="destructive"
                          className="w-full bg-red-600 hover:bg-red-700"
                          disabled={nuclearClearing}
                        >
                          {nuclearClearing ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Nuclear Clear İşlemi Devam Ediyor...
                            </>
                          ) : (
                            <>
                              <Zap className="mr-2 h-4 w-4" />
                              Nuclear Clear - Kuyruğu Tamamen Temizle
                            </>
                          )}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="max-w-2xl">
                        <AlertDialogHeader>
                          <AlertDialogTitle className="text-red-700 flex items-center gap-2">
                            <Zap className="h-5 w-5" />
                            Nuclear Clear - Son Onay
                          </AlertDialogTitle>
                          <AlertDialogDescription className="space-y-4">
                            <div className="bg-red-50 border border-red-200 rounded-md p-4">
                              <p className="text-red-800 font-semibold mb-2">
                                🚨 Bu işlem test kuyruğunu TAMAMEN TEMİZLEYECEK!
                              </p>
                              <p className="text-red-700 text-sm">
                                Bu işlem şunları yapacak:
                              </p>
                              <ul className="list-disc list-inside text-sm text-red-700 mt-2 space-y-1">
                                <li>Test kuyruğunu force=true ile obliterate edecek</li>
                                <li>Tüm Redis key'lerini (bull:test-queue:*) silecek</li>
                                <li>Tüm test lock'larını (test:lock:*, test:claim:*, vb.) temizleyecek</li>
                                <li>Tüm güvenlik kontrollerini ve lock'ları bypass edecek</li>
                              </ul>
                            </div>

                            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                              <p className="text-yellow-800 text-sm">
                                <strong>Ne zaman kullanmalı:</strong> Sadece kuyruk tamamen takıldığında,
                                normal complete/delete işlemleri çalışmadığında ve başka çözüm kalmadığında.
                              </p>
                            </div>

                            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                              <p className="text-blue-800 text-sm">
                                <strong>İşlem sonrası:</strong> Kuyruk temizlenecek ve yeni testler normal şekilde çalışmaya devam edecek.
                              </p>
                            </div>

                            <p className="text-gray-700 text-sm font-semibold">
                              Bu işlemi gerçekleştirmek istediğinizden emin misiniz?
                            </p>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>İptal Et</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleNuclearClear}
                            className="bg-red-600 text-white hover:bg-red-700"
                          >
                            Evet, Nuclear Clear Yap
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="queued" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Kuyrukta Bekleyen Testler</CardTitle>
                  <CardDescription>
                    Şu anda kuyrukta bekleyen testlerin listesi
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {monitorData?.queuedTests && monitorData.queuedTests.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Test ID</TableHead>
                          <TableHead>Senaryo Adı</TableHead>
                          <TableHead>Durum</TableHead>
                          <TableHead>Öncelik</TableHead>
                          <TableHead>Kuyrukta Bekleme Süresi</TableHead>
                          <TableHead>Eklenme Zamanı</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {monitorData.queuedTests.map((test) => (
                          <TableRow key={test.id}>
                            <TableCell className="font-mono text-xs">{test.id}</TableCell>
                            <TableCell>{test.scenarioName}</TableCell>
                            <TableCell>{getQueueStateBadge(test.jobState || 'waiting')}</TableCell>
                            <TableCell>{test.priority}</TableCell>
                            <TableCell>{getTimeBadge(test.queuedTimeSeconds, test.queuedTimeSeconds > 300)}</TableCell>
                            <TableCell className="text-muted-foreground text-sm">
                              {test.queuedAt ? new Date(test.queuedAt).toLocaleString() : 'N/A'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                      <CheckCircle className="h-8 w-8 text-green-500 mb-2" />
                      <p>Kuyrukta bekleyen test bulunmuyor</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="running" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Çalışan Testler</CardTitle>
                  <CardDescription>
                    Şu anda çalışan testlerin listesi
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {monitorData?.runningTests && monitorData.runningTests.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Test ID</TableHead>
                          <TableHead>Senaryo Adı</TableHead>
                          <TableHead>Node ID</TableHead>
                          <TableHead>Çalışma Süresi</TableHead>
                          <TableHead>Başlama Zamanı</TableHead>
                          <TableHead>İşlemler</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {monitorData.runningTests.map((test) => (
                          <TableRow key={test.id}>
                            <TableCell className="font-mono text-xs">{test.id}</TableCell>
                            <TableCell>{test.scenarioName}</TableCell>
                            <TableCell className="font-mono text-xs">{test.nodeId}</TableCell>
                            <TableCell>{getTimeBadge(test.runningTimeSeconds, test.runningTimeSeconds > 1800)}</TableCell>
                            <TableCell className="text-muted-foreground text-sm">
                              {test.startedAt ? new Date(test.startedAt).toLocaleString() : 'N/A'}
                            </TableCell>
                            <TableCell>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-green-600 border-green-600 hover:bg-green-50"
                                    disabled={completingTests.has(test.id)}
                                  >
                                    {completingTests.has(test.id) ? (
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                    ) : (
                                      <Check className="h-4 w-4" />
                                    )}
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Testi Zorla Tamamla</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Bu işlem test ID: <code className="bg-muted px-1 py-0.5 rounded text-sm">{test.id}</code> olan aktif testi zorla tamamlayacak ve completed durumuna geçirecek.
                                      <br /><br />
                                      <strong>Senaryo:</strong> {test.scenarioName}
                                      <br />
                                      <strong>Node:</strong> {test.nodeId}
                                      <br />
                                      <strong>Çalışma Süresi:</strong> {formatTime(test.runningTimeSeconds)}
                                      <br /><br />
                                      <span className="text-amber-600">⚠️ Bu işlem testi aniden sonlandıracaktır.</span>
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>İptal</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleCompleteTest(test.id)}
                                      className="bg-green-600 text-white hover:bg-green-700"
                                    >
                                      Tamamla
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                      <CheckCircle className="h-8 w-8 text-green-500 mb-2" />
                      <p>Çalışan test bulunmuyor</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="stuck" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Takılmış Testler</CardTitle>
                  <CardDescription>
                    Uzun süredir kuyrukta bekleyen veya çalışan testlerin listesi
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {monitorData?.stuckTests && monitorData.stuckTests.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Test ID</TableHead>
                          <TableHead>Senaryo Adı</TableHead>
                          <TableHead>Durum</TableHead>
                          <TableHead>Node ID</TableHead>
                          <TableHead>Takılma Süresi</TableHead>
                          <TableHead>İşlemler</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {monitorData.stuckTests.map((test) => (
                          <TableRow key={test.id}>
                            <TableCell className="font-mono text-xs">{test.id}</TableCell>
                            <TableCell>{test.scenarioName}</TableCell>
                            <TableCell>{getStatusBadge(test.status)}</TableCell>
                            <TableCell className="font-mono text-xs">{test.nodeId || 'N/A'}</TableCell>
                            <TableCell>{getTimeBadge(test.stuckTimeSeconds, true)}</TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                {/* Complete Button */}
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-green-600 border-green-600 hover:bg-green-50"
                                      disabled={completingTests.has(test.id)}
                                    >
                                      {completingTests.has(test.id) ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Check className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Sıkışmış Testi Tamamla</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Bu işlem test ID: <code className="bg-muted px-1 py-0.5 rounded text-sm">{test.id}</code> olan sıkışmış testi zorla tamamlayacak ve completed durumuna geçirecek.
                                        <br /><br />
                                        <strong>Senaryo:</strong> {test.scenarioName}
                                        <br />
                                        <strong>Durum:</strong> {test.status}
                                        <br />
                                        <strong>Takılma Süresi:</strong> {formatTime(test.stuckTimeSeconds)}
                                        <br /><br />
                                        <span className="text-amber-600">⚠️ Bu işlem testi aniden sonlandıracaktır.</span>
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>İptal</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleCompleteTest(test.id)}
                                        className="bg-green-600 text-white hover:bg-green-700"
                                      >
                                        Tamamla
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>

                                {/* Delete Button */}
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      disabled={deletingTests.has(test.id)}
                                    >
                                      {deletingTests.has(test.id) ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Sıkışmış Testi Sil</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Bu işlem geri alınamaz. Test ID: <code className="bg-muted px-1 py-0.5 rounded text-sm">{test.id}</code> olan sıkışmış test silinecek ve tüm ilişkili kaynaklar temizlenecek.
                                        <br /><br />
                                        <strong>Senaryo:</strong> {test.scenarioName}
                                        <br />
                                        <strong>Durum:</strong> {test.status}
                                        <br />
                                        <strong>Takılma Süresi:</strong> {formatTime(test.stuckTimeSeconds)}
                                        <br /><br />
                                        <span className="text-red-600">⚠️ Bu işlem testi tamamen kaldıracaktır.</span>
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>İptal</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleDeleteStuckTest(test.id)}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        Sil
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                      <CheckCircle className="h-8 w-8 text-green-500 mb-2" />
                      <p>Takılmış test bulunmuyor</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="redis" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Redis Kuyrukları</CardTitle>
                  <CardDescription>
                    Redis kuyruk durumu ve istatistikleri
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {monitorData?.redisQueues && Object.keys(monitorData.redisQueues).length > 0 ? (
                    <div className="space-y-6">
                      {Object.entries(monitorData.redisQueues).map(([queueName, queueData]: [string, any]) => (
                        <div key={queueName} className="border-b pb-4 last:border-0 last:pb-0">
                          <h3 className="text-sm font-medium mb-2">{queueName}</h3>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                            <div>
                              <div className="text-xs text-muted-foreground">Bekleyen</div>
                              <div className="text-lg font-semibold">{queueData.waiting || 0}</div>
                            </div>
                            <div>
                              <div className="text-xs text-muted-foreground">Aktif</div>
                              <div className="text-lg font-semibold">{queueData.active || 0}</div>
                            </div>
                            <div>
                              <div className="text-xs text-muted-foreground">Tamamlanan</div>
                              <div className="text-lg font-semibold">{queueData.completed || 0}</div>
                            </div>
                            <div>
                              <div className="text-xs text-muted-foreground">Başarısız</div>
                              <div className="text-lg font-semibold">{queueData.failed || 0}</div>
                            </div>
                          </div>

                          {/* Progress bar for queue status */}
                          <div className="space-y-1">
                            <div className="flex justify-between text-xs">
                              <span>Kuyruk Durumu</span>
                              <span>
                                {queueData.waiting + queueData.active + queueData.delayed || 0} iş
                              </span>
                            </div>
                            <div className="flex gap-1 h-2">
                              {queueData.active > 0 && (
                                <div
                                  className="bg-blue-500 rounded-l-full"
                                  style={{
                                    width: `${queueData.active / (queueData.waiting + queueData.active + queueData.delayed) * 100}%`
                                  }}
                                />
                              )}
                              {queueData.waiting > 0 && (
                                <div
                                  className="bg-yellow-500"
                                  style={{
                                    width: `${queueData.waiting / (queueData.waiting + queueData.active + queueData.delayed) * 100}%`
                                  }}
                                />
                              )}
                              {queueData.delayed > 0 && (
                                <div
                                  className="bg-purple-500 rounded-r-full"
                                  style={{
                                    width: `${queueData.delayed / (queueData.waiting + queueData.active + queueData.delayed) * 100}%`
                                  }}
                                />
                              )}
                            </div>
                            <div className="flex text-xs text-muted-foreground gap-4">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-blue-500 mr-1" />
                                <span>Aktif: {queueData.active || 0}</span>
                              </div>
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-yellow-500 mr-1" />
                                <span>Bekleyen: {queueData.waiting || 0}</span>
                              </div>
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-purple-500 mr-1" />
                                <span>Gecikmeli: {queueData.delayed || 0}</span>
                              </div>
                            </div>
                          </div>

                          {/* Node info if available */}
                          {queueData.nodeName && (
                            <div className="mt-2 text-sm">
                              <span className="text-muted-foreground">Node: </span>
                              <span className="font-medium">{queueData.nodeName}</span>
                              <Badge className="ml-2" variant={queueData.nodeStatus === 'available' ? 'default' : 'outline'}>
                                {queueData.nodeStatus === 'available' ? 'Müsait' : queueData.nodeStatus}
                              </Badge>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                      <AlertTriangle className="h-8 w-8 text-yellow-500 mb-2" />
                      <p>Redis kuyruk bilgisi bulunamadı</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </AdminLayout>
  )
}
