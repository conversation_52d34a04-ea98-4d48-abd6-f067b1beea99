/**
 * Redis Services Module Entry Point
 * Exports all Redis-related services
 */

export { redisConnection } from './redisConnection.js';
export { queueService, QUEUE_NAMES, JOB_TYPES } from './queueService.js';
export { bullBoardService } from './bullBoard.js';
// Legacy result handler has been removed in favor of the asynchronous result queue worker

// Default export is the queue service
export { queueService as default } from './queueService.js';
