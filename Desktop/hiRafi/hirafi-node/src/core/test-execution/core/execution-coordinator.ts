/**
 * Execution Coordinator Implementation
 * 
 * Coordinates test execution by orchestrating all the different services.
 * This replaces the monolithic TestExecutionManager with a focused coordinator.
 */

import { EventEmitter } from 'events';
import { TestRequest, TestResult } from '../../../models/types.js';
import { TestPlatform, PlatformTestRequest } from '../../../models/platform-types.js';
import { TestExecutionState } from '../../node-manager/types/node-manager-types.js';
import { isAndroidEnvironmentSettings, isWebEnvironmentSettings } from '../../../utils/type-guards.js';
import { DependencyContainer } from '../../common/di/dependency-container.js';
import { ITestExecutionContainer } from '../../common/di/application-container.js';
import { ServiceNames } from '../../node-manager/types/node-manager-types.js';

import { IExecutionCoordinator, TestExecutionEvents, TestExecutionOptions, TestExecutionStatus } from '../interfaces/execution-coordinator.interface.js';
import { IStateManager } from '../interfaces/state-manager.interface.js';
import { IResourceManager } from '../interfaces/resource-manager.interface.js';
import { IConfigurationManager } from '../interfaces/configuration-manager.interface.js';
import { ICleanupOrchestrator, CleanupReason } from '../interfaces/cleanup-orchestrator.interface.js';
import { INodeIdentityService } from '../../node-manager/interfaces/node-identity-service.interface.js';
import { IConnectionService } from '../../node-manager/interfaces/connection-service.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../utils/logger-service.js';
import { TestProgressService } from '../services/progress/test-progress-service.js';
import { NodeStepProgressQueueService } from '../services/progress/step-progress-queue-service.js';
import { NodeTimeoutGuardian } from '../services/monitoring/node-timeout-guardian.js';
import { TestRunnerFactory } from '../factories/test-runner-factory.js';

/**
 * Execution Coordinator Implementation
 * 
 * Orchestrates test execution by coordinating between state management,
 * resource management, configuration, and cleanup services.
 * Enhanced with NodeTimeoutGuardian for aggressive timeout monitoring.
 */
export class ExecutionCoordinator extends EventEmitter implements IExecutionCoordinator {
  private nodeId: string;
  private logger: ILoggerService;
  private stateManager: IStateManager;
  private resourceManager: IResourceManager | null = null;
  private configurationManager: IConfigurationManager | null = null;
  private cleanupOrchestrator: ICleanupOrchestrator | null = null;
  private identityService: INodeIdentityService;
  private connectionService: IConnectionService;
  private container: ITestExecutionContainer;
  private progressService: TestProgressService;
  private nodeTimeoutGuardian: NodeTimeoutGuardian;

  // Test runner factory configuration
  private websocketConnector: any = null;
  private factoryConfig: any = null;
  private storage: any = null;
  private currentTestRunner: any = null;

  // Lock extension tracking
  private lockExtensionIntervals: Map<string, NodeJS.Timeout> = new Map();
  private eventHandler: EventEmitter = this;

  constructor(
    identityService: INodeIdentityService,
    stateManager: IStateManager,
    container: ITestExecutionContainer,
    connectionService: IConnectionService
  ) {
    super();
    
    this.logger = LoggerServiceFactory.createServiceLogger('ExecutionCoordinator');
    this.identityService = identityService;
    this.nodeId = identityService.getNodeId() || 'uninitialized';
    this.stateManager = stateManager;
    this.container = container;
    this.connectionService = connectionService;

    // ENHANCED DEBUG: Log ConnectionService injection status
    this.logger.info(`ExecutionCoordinator: Constructor - ConnectionService: ${this.connectionService ? 'INJECTED' : 'NULL'}`);
    if (this.connectionService) {
      const nodeId = this.connectionService.getNodeId();
      const wsConnector = this.connectionService.getWebSocketConnector();
      const wsConnected = this.connectionService.isWebSocketConnected();
      this.logger.info(`ExecutionCoordinator: Constructor ConnectionService status - NodeID: ${nodeId || 'NULL'}, WebSocket Connector: ${wsConnector ? 'AVAILABLE' : 'NULL'}, Connected: ${wsConnected}`);
    }

    // Create shared progress service for NodeManager compatibility
    const stepProgressQueueService = new NodeStepProgressQueueService();
    this.progressService = new TestProgressService(this.connectionService, stepProgressQueueService);

    // CRITICAL FIX: Initialize TestProgressService immediately in constructor
    // This ensures the service is ready when we need to report test claimed status
    this.initializeProgressService().catch(error => {
      this.logger.error(`ExecutionCoordinator: Failed to initialize TestProgressService in constructor: ${error.message}`);
    });

    // Initialize NodeTimeoutGuardian for aggressive timeout monitoring
    this.nodeTimeoutGuardian = new NodeTimeoutGuardian(this, this.container, this.progressService);
    
    // Set up guardian event listeners for monitoring and debugging
    this.setupGuardianEventListeners();

    this.logger.debug(`ExecutionCoordinator: Initialized for node ${this.nodeId} with interface-based container and NodeTimeoutGuardian`);
  }

  /**
   * Initialize TestProgressService asynchronously
   * This is called immediately in constructor to ensure service is ready early
   */
  private async initializeProgressService(): Promise<void> {
    try {
      await this.progressService.initialize({
        enableLogging: true,
        batchReporting: false,
        enableResultQueue: true,
        enableStepProgressQueue: true
      });
      this.logger.info('ExecutionCoordinator: TestProgressService initialized successfully in constructor');
    } catch (error: any) {
      this.logger.error(`ExecutionCoordinator: Failed to initialize TestProgressService: ${error.message}`);
      // Don't throw here to avoid breaking constructor
    }
  }

  /**
   * Setup event listeners for NodeTimeoutGuardian monitoring
   */
  private setupGuardianEventListeners(): void {
    this.nodeTimeoutGuardian.on('guardian:started', (data) => {
      this.logger.info(`🛡️ Guardian: Started monitoring test ${data.testId} with ${data.totalSteps} steps`);
    });

    this.nodeTimeoutGuardian.on('guardian:stopped', (data) => {
      this.logger.info(`🛡️ Guardian: Stopped monitoring test ${data.testId} - reason: ${data.reason}`);
    });

    this.nodeTimeoutGuardian.on('step:timeout', (data) => {
      this.logger.error(`🚨 Guardian: STEP TIMEOUT detected - Test ${data.testId}, Step ${data.stepIndex}, Duration: ${Math.round(data.duration/1000)}s`);
    });

    this.nodeTimeoutGuardian.on('test:timeout', (data) => {
      this.logger.error(`🚨 Guardian: TEST TIMEOUT detected - Test ${data.testId}, Duration: ${Math.round(data.duration/1000)}s, Reason: ${data.reason}`);
    });

    this.nodeTimeoutGuardian.on('termination:started', (data) => {
      this.logger.error(`🔥 Guardian: EMERGENCY TERMINATION started for test ${data.testId} - ${data.reason}`);
    });

    this.nodeTimeoutGuardian.on('termination:completed', (data) => {
      this.logger.error(`🔥 Guardian: Emergency termination completed for test ${data.testId} in ${data.duration}ms`);
    });

    this.nodeTimeoutGuardian.on('termination:failed', (data) => {
      this.logger.error(`🔥 Guardian: Emergency termination FAILED for test ${data.testId} - ${data.error}`);
    });

    this.nodeTimeoutGuardian.on('cleanup:timeout', (data) => {
      this.logger.error(`🔥 Guardian: CLEANUP TIMEOUT - Test ${data.testId} cleanup took ${data.duration}ms, node may be unstable`);
    });
  }

  /**
   * Initialize the execution coordinator
   */
  /**
   * Set the event handler for test execution events.
   * @param eventHandler The event handler to use for emitting events.
   */
  setEventHandler(eventHandler: EventEmitter): void {
    this.logger.info(`ExecutionCoordinator: Overriding default event emitter.`);
    this.eventHandler = eventHandler;
  }

  async initialize(): Promise<void> {
    try {
      this.nodeId = this.identityService.getNodeId() || 'uninitialized';
      
      // Initialize state manager
      await this.stateManager.initialize({
        nodeId: this.nodeId,
        enableStateLogging: true,
        transitionTimeout: 30000
      });

      this.logger.info('ExecutionCoordinator: Initialized successfully');
    } catch (error: any) {
      this.logger.error(`ExecutionCoordinator: Initialization failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Set test runner factory configuration
   */
  setTestRunnerFactoryConfig(websocketConnector: any, config: any, storage: any): void {
    this.websocketConnector = websocketConnector;
    this.factoryConfig = config;
    this.storage = storage;
    this.logger.debug('ExecutionCoordinator: Test runner factory configuration set');
  }

  /**
   * Execute a test request
   */
  async executeTest(testRequest: TestRequest, options: TestExecutionOptions = {}): Promise<TestResult> {
    const { extendLockFunction, jobToken, timeout = 300000 } = options;
    const scopeId = testRequest.id;

    // Try to claim the test by transitioning to PREPARING state
    const transitionResult = await this.stateManager.tryTransitionState(
      TestExecutionState.IDLE,
      TestExecutionState.PREPARING,
      testRequest.id
    );

    if (!transitionResult.success) {
      const errorMessage = `Cannot execute test ${testRequest.id}: ${transitionResult.message}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    this.logger.info(`ExecutionCoordinator: Claimed test ${testRequest.id}`);
    this.logger.info(`ExecutionCoordinator: Test ${testRequest.id} claimed by node ${this.nodeId}`);
    
    // CRITICAL FIX: Register test metadata with TestProgressService immediately after claiming
    // This provides the required runId and executionId for WebSocket status updates
    if (this.progressService.isInitialized() && testRequest.executionId && testRequest.runId) {
      this.progressService.startTest(testRequest.id, testRequest.executionId, testRequest.runId);
      this.logger.info(`ExecutionCoordinator: Registered test ${testRequest.id} metadata with TestProgressService - executionId: ${testRequest.executionId}, runId: ${testRequest.runId}`);
    } else {
      this.logger.warn(`ExecutionCoordinator: Cannot register test metadata - ProgressService initialized: ${this.progressService.isInitialized()}, executionId: ${testRequest.executionId || 'MISSING'}, runId: ${testRequest.runId || 'MISSING'}`);
    }
    
    // ENHANCED SAFETY: Report test claimed with proper error handling and service state check
    if (this.progressService.isInitialized()) {
      try {
        await this.progressService.reportTestClaimed(testRequest.id, this.nodeId);
        this.logger.debug(`ExecutionCoordinator: Successfully reported test claimed for test ${testRequest.id}`);
      } catch (error: any) {
        this.logger.error(`ExecutionCoordinator: Failed to report test claimed for test ${testRequest.id}: ${error.message}`);
        // Don't fail the test execution if progress reporting fails
      }
    } else {
      this.logger.warn(`ExecutionCoordinator: Cannot report test claimed - TestProgressService not initialized for test ${testRequest.id}`);
    }

    // DEBUG: Log critical fields from testRequest for debugging missing fields issue
    this.logger.info(`ExecutionCoordinator: Test ${testRequest.id} metadata - scenarioId: ${testRequest.scenarioId}, runId: ${testRequest.runId}, executionId: ${testRequest.executionId}, executedUser: ${testRequest.executedUser}, executedUserName: ${testRequest.executedUserName}, teamId: ${testRequest.teamId}, companyId: ${testRequest.companyId}`);

    // 🛡️ START GUARDIAN MONITORING before test execution begins
    const totalSteps = testRequest.scenario?.steps?.length || 0;
    this.nodeTimeoutGuardian.startGuarding(testRequest.id, testRequest, totalSteps);

    try {
      // Note: Test tracking is now handled per-test in createTestRunner method
      // This ensures proper test isolation and prevents state pollution between tests

      // Resolve scoped services for this test execution
      this.resourceManager = await this.container.resolve<IResourceManager>(ServiceNames.RESOURCE_MANAGER, scopeId);
      this.configurationManager = await this.container.resolve<IConfigurationManager>(ServiceNames.CONFIGURATION_MANAGER, scopeId);
      this.cleanupOrchestrator = await this.container.resolve<ICleanupOrchestrator>(ServiceNames.CLEANUP_ORCHESTRATOR, scopeId);

      // Initialize the resolved scoped services
      await this.resourceManager.initialize();
      await this.configurationManager.initialize({
        enableCaching: true,
        fallbackToEnvironment: true
      });
      await this.cleanupOrchestrator.initialize();

      // Set up lock extension if provided
      if (extendLockFunction && jobToken) {
        this.setupLockExtension(testRequest.id, extendLockFunction);
      }

      // Validate test request
      const validationResult = this.validateTestRequest(testRequest);
      if (!validationResult.isValid) {
        const skippedResult = this.createSkippedResult(testRequest, validationResult.reason || 'Validation failed');
        
        // ENHANCED SAFETY: Report skipped test completion with proper error handling and service state check
        if (this.progressService.isInitialized()) {
          try {
            await this.progressService.reportTestCompletion(testRequest.id, skippedResult);
            this.logger.debug(`ExecutionCoordinator: Successfully reported skipped test completion for test ${testRequest.id}`);
          } catch (reportError: any) {
            this.logger.error(`ExecutionCoordinator: Failed to report skipped test completion for test ${testRequest.id}: ${reportError.message}`);
          }
        } else {
          this.logger.warn(`ExecutionCoordinator: Cannot report skipped test completion - TestProgressService not initialized for test ${testRequest.id}`);
        }
        
        return skippedResult;
      }

      // Convert to platform test request
      const platformTestRequest = this.convertToPlatformTestRequest(testRequest);

      // Create test runner
      const testRunner = await this.createTestRunner(platformTestRequest);
      this.currentTestRunner = testRunner;

      // Register resources for cleanup
      this.resourceManager!.registerBrowserCleanup(testRequest.id, {
        browserType: platformTestRequest.platform === TestPlatform.ANDROID ? 'android' : 'web'
      });

      // Apply AI configuration
      await this.configurationManager!.applyAIConfiguration(platformTestRequest, platformTestRequest.platform);

      // Transition to RUNNING state
      const runningTransition = await this.stateManager.tryTransitionState(
        TestExecutionState.PREPARING,
        TestExecutionState.RUNNING,
        testRequest.id
      );

      if (!runningTransition.success) {
        throw new Error(`Failed to transition to RUNNING state: ${runningTransition.message}`);
      }

      this.logger.info(`ExecutionCoordinator: Started test ${testRequest.id}`);
      
      // ENHANCED SAFETY: Report test running with proper error handling and service state check
      if (this.progressService.isInitialized()) {
        try {
          await this.progressService.reportTestRunning(testRequest.id);
          this.logger.debug(`ExecutionCoordinator: Successfully reported test running for test ${testRequest.id}`);
        } catch (error: any) {
          this.logger.error(`ExecutionCoordinator: Failed to report test running for test ${testRequest.id}: ${error.message}`);
          // Don't fail the test execution if progress reporting fails
        }
      } else {
        this.logger.warn(`ExecutionCoordinator: Cannot report test running - TestProgressService not initialized for test ${testRequest.id}`);
      }

      // Execute the test
      const startTime = Date.now();
      const result = await testRunner.runTest(platformTestRequest);
      const duration = Date.now() - startTime;

      // Enhance result with additional metadata
      const enhancedResult = this.enhanceTestResult(result, testRequest, duration);

      // ENHANCED SAFETY: Report test completion with proper error handling and service state check
      if (this.progressService.isInitialized()) {
        try {
          await this.progressService.reportTestCompletion(testRequest.id, enhancedResult);
          this.logger.debug(`ExecutionCoordinator: Successfully reported test completion for test ${testRequest.id}`);
        } catch (error: any) {
          this.logger.error(`ExecutionCoordinator: Failed to report test completion for test ${testRequest.id}: ${error.message}`);
          // Don't fail the test execution if progress reporting fails
        }
      } else {
        this.logger.warn(`ExecutionCoordinator: Cannot report test completion - TestProgressService not initialized for test ${testRequest.id}`);
      }

      this.logger.info(`ExecutionCoordinator: Completed test ${testRequest.id} in ${duration}ms`);

      return enhancedResult;

    } catch (error: any) {
      this.logger.error(`ExecutionCoordinator: Test ${testRequest.id} failed: ${error.message}`);
      
      const failedResult = this.createFailedResult(testRequest, error);
      
      // ENHANCED SAFETY: Report failed test completion with proper error handling and service state check
      if (this.progressService.isInitialized()) {
        try {
          await this.progressService.reportTestCompletion(testRequest.id, failedResult);
          this.logger.debug(`ExecutionCoordinator: Successfully reported failed test completion for test ${testRequest.id}`);
        } catch (reportError: any) {
          this.logger.error(`ExecutionCoordinator: Failed to report failed test completion for test ${testRequest.id}: ${reportError.message}`);
        }
      } else {
        this.logger.warn(`ExecutionCoordinator: Cannot report failed test completion - TestProgressService not initialized for test ${testRequest.id}`);
      }
      
      return failedResult;
    } finally {
      this.logger.info(`ExecutionCoordinator: Starting cleanup for test ${testRequest.id}`);
      this.clearLockExtension(testRequest.id);

      // 🛡️ STOP GUARDIAN MONITORING - test finished (normal completion)
      this.nodeTimeoutGuardian.stopGuarding(testRequest.id);

      // Transition to CLEANING_UP state before cleanup
      const currentState = this.stateManager.getCurrentState();
      if (currentState !== TestExecutionState.CLEANING_UP) {
        const transitionResult = await this.stateManager.tryTransitionState(currentState, TestExecutionState.CLEANING_UP, testRequest.id);
        if (!transitionResult.success) {
          this.logger.warn(`ExecutionCoordinator: Failed to transition from ${currentState} to CLEANING_UP for test ${testRequest.id}: ${transitionResult.message}. Proceeding with cleanup anyway.`);
        }
      }

      // Perform cleanup and transition to IDLE state
      await this.performTestCleanup(testRequest.id, 'Test finished');
      
      // Dispose the DI scope for the test to clean up scoped services.
      await this.container.disposeScope(scopeId);

      // Nullify references to the scoped services to prevent memory leaks.
      this.resourceManager = null;
      this.configurationManager = null;
      this.cleanupOrchestrator = null;
    }
  }

  /**
   * Get the current execution status
   */
  getExecutionStatus(): TestExecutionStatus {
    return {
      state: this.stateManager.getCurrentState(),
      currentTestId: this.stateManager.getCurrentTestId(),
      canAcceptNewTest: this.stateManager.canAcceptNewTest(),
      isTestRunning: this.stateManager.isTestRunning()
    };
  }

  /**
   * Check if the coordinator can accept a new test
   */
  canAcceptNewTest(): boolean {
    return this.stateManager.canAcceptNewTest();
  }

  /**
   * Check if a test is currently running
   */
  isTestRunning(): boolean {
    return this.stateManager.isTestRunning();
  }

  /**
   * Check if a specific test is currently running
   */
  isSpecificTestRunning(testId: string): boolean {
    return this.stateManager.isSpecificTestRunning(testId);
  }

  /**
   * Get the current test ID being processed
   */
  getCurrentTestId(): string | null {
    return this.stateManager.getCurrentTestId();
  }

  /**
   * Abort the currently running test
   */
  async abortCurrentTest(): Promise<void> {
    const currentTestId = this.getCurrentTestId();
    if (currentTestId) {
      await this.stopSpecificTest(currentTestId, 'User requested abort');
    }
  }

  /**
   * Stop a specific test if it is currently running
   */
  async stopSpecificTest(testId: string, reason: string): Promise<void> {
    if (!this.isSpecificTestRunning(testId)) {
      this.logger.warn(`ExecutionCoordinator: Cannot stop test ${testId} - not currently running`);
      return;
    }

    this.logger.warn(`ExecutionCoordinator: Stopping test ${testId}. Reason: ${reason}`);

    try {
      // 🛡️ STOP GUARDIAN MONITORING immediately - emergency stop
      this.nodeTimeoutGuardian.stopGuarding(testId);

      // Perform cleanup which will handle state transitions
      await this.performTestCleanup(testId, reason);
      
      this.logger.info(`ExecutionCoordinator: Successfully stopped test ${testId}`);
    } catch (error: any) {
      this.logger.error(`ExecutionCoordinator: Error stopping test ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Handle process crash notification
   */
  handleProcessCrash(reason: string): void {
    const currentTestId = this.getCurrentTestId();
    if (currentTestId) {
      this.logger.error(`ExecutionCoordinator: Process crash detected for test ${currentTestId}: ${reason}`);
      
      // 🛡️ STOP GUARDIAN MONITORING immediately - process crash
      this.nodeTimeoutGuardian.stopGuarding(currentTestId);
      
      this.performTestCleanup(currentTestId, `Process crash: ${reason}`).catch((error: any) => {
        this.logger.error(`ExecutionCoordinator: Error during crash cleanup: ${error.message}`);
      });
    }
  }

  /**
   * Handle graceful shutdown notification
   */
  handleGracefulShutdown(reason: string): void {
    const currentTestId = this.getCurrentTestId();
    if (currentTestId) {
      this.logger.warn(`ExecutionCoordinator: Graceful shutdown for test ${currentTestId}: ${reason}`);
      
      // 🛡️ STOP GUARDIAN MONITORING gracefully - shutdown
      this.nodeTimeoutGuardian.stopGuarding(currentTestId);
      
      this.performTestCleanup(currentTestId, `Graceful shutdown: ${reason}`).catch((error: any) => {
        this.logger.error(`ExecutionCoordinator: Error during shutdown cleanup: ${error.message}`);
      });
    }
  }

  /**
   * Get guardian statistics for monitoring and debugging
   */
  getGuardianStatistics() {
    return this.nodeTimeoutGuardian.getStatistics();
  }

  /**
   * Get guardian active status for debugging
   */
  getGuardianActiveStatus() {
    return this.nodeTimeoutGuardian.getActiveStatus();
  }

  /**
   * Update guardian configuration (for dynamic tuning)
   */
  updateGuardianConfiguration(config: any) {
    this.nodeTimeoutGuardian.updateConfiguration(config);
    this.logger.info(`ExecutionCoordinator: Updated guardian configuration`, config);
  }

  /**
   * Dispose of the execution coordinator
   */
  async dispose(): Promise<void> {
    try {
      // 🛡️ DISPOSE GUARDIAN first
      await this.nodeTimeoutGuardian.dispose();

      // Clear any active lock extensions
      for (const [testId, interval] of this.lockExtensionIntervals) {
        clearInterval(interval);
        this.lockExtensionIntervals.delete(testId);
      }

      // SINGLETON services (like stateManager) are disposed by the container.
      // SCOPED services (resourceManager, etc.) are disposed when their scope ends.
      // This service should not manage their lifecycle.

      this.currentTestRunner = null;
      this.websocketConnector = null;
      this.factoryConfig = null;
      this.storage = null;

      this.logger.info('ExecutionCoordinator: Disposed successfully');
    } catch (error: any) {
      this.logger.error(`ExecutionCoordinator: Error during disposal: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate test request
   */
  private validateTestRequest(testRequest: TestRequest): { isValid: boolean; reason?: string } {
    if (!testRequest || !testRequest.id) {
      return { isValid: false, reason: 'Test request or test ID is missing' };
    }

    if (!testRequest.scenario || typeof testRequest.scenario !== 'object') {
      return { isValid: false, reason: 'Missing scenario data' };
    }

    if (!testRequest.scenario.steps || !Array.isArray(testRequest.scenario.steps)) {
      return { isValid: false, reason: 'Scenario has no steps array' };
    }

    // Enhanced validation: Check for empty steps array
    if (testRequest.scenario.steps.length === 0) {
      this.logger.warn(`ExecutionCoordinator: Rejecting test ${testRequest.id} - scenario '${testRequest.scenario.name || 'Unknown'}' has no steps. This prevents unnecessary resource allocation and video recording.`);
      return { 
        isValid: false, 
        reason: 'Scenario has no test steps - execution cancelled to prevent unnecessary resource usage' 
      };
    }

    // Additional validation: Check for valid step content
    const validSteps = testRequest.scenario.steps.filter((step: any) => 
      step && 
      typeof step === 'object' && 
      step.type && 
      (step.value || step.name || step.prompt)
    );

    if (validSteps.length === 0) {
      this.logger.warn(`ExecutionCoordinator: Rejecting test ${testRequest.id} - scenario '${testRequest.scenario.name || 'Unknown'}' has no valid steps. All steps are empty or malformed.`);
      return { 
        isValid: false, 
        reason: 'Scenario has no valid test steps - all steps are empty or malformed' 
      };
    }

    this.logger.info(`ExecutionCoordinator: Test ${testRequest.id} validation passed - scenario '${testRequest.scenario.name || 'Unknown'}' has ${validSteps.length} valid steps`);
    return { isValid: true };
  }

  /**
   * Convert TestRequest to PlatformTestRequest
   */
  private convertToPlatformTestRequest(testRequest: TestRequest): PlatformTestRequest {
    const platformTestRequest: PlatformTestRequest = {
      ...testRequest,
      platform: testRequest.platform ?
        (testRequest.platform === 'android' ? TestPlatform.ANDROID : TestPlatform.WEB) :
        TestPlatform.WEB
    };

    // Validate environment settings based on platform
    if (platformTestRequest.platform === TestPlatform.ANDROID) {
      if (platformTestRequest.environmentSettings && !isAndroidEnvironmentSettings(platformTestRequest.environmentSettings)) {
        this.logger.warn(`ExecutionCoordinator: Invalid Android environment settings for test ${testRequest.id}, using defaults`);
      }
    } else if (platformTestRequest.platform === TestPlatform.WEB) {
      if (platformTestRequest.environmentSettings && !isWebEnvironmentSettings(platformTestRequest.environmentSettings)) {
        this.logger.warn(`ExecutionCoordinator: Invalid web environment settings for test ${testRequest.id}, using defaults`);
      }
    }

    return platformTestRequest;
  }

  /**
   * Create test runner for the platform using DI container dependencies
   * @returns The created test runner
   */
  private async createTestRunner(platformTestRequest: PlatformTestRequest): Promise<any> {
    const platform = platformTestRequest.platform === TestPlatform.ANDROID ? 'android' : 'web';
    const scopeId = platformTestRequest.id;

    // Combine factoryConfig with environmentSettings for robust configuration
    const runnerConfig = {
      ...(this.factoryConfig || {}),
      ...platformTestRequest.environmentSettings
    };

    try {
      // Resolve test runner service factories from scoped container with correct service names
      const platformManagerFactory = await this.container.resolve('testRunner.platformManager', scopeId) as any;
      const screenshotProviderFactory = await this.container.resolve('testRunner.screenshotProvider', scopeId) as any;
      const videoRecorderFactory = await this.container.resolve('testRunner.videoRecorder', scopeId) as any;
      const metricsCollectorFactory = await this.container.resolve('testRunner.metricsCollector', scopeId) as any;
      const stepHandlerRegistryFactory = await this.container.resolve('testRunner.stepHandlerRegistry', scopeId) as any;

      // Create platform-specific instances using factory methods
      const dependencies = {
        platformManager: await platformManagerFactory.createForPlatform(platform, runnerConfig),
        screenshotProvider: await screenshotProviderFactory.createForPlatform(platform, runnerConfig, this, await this.container.resolve('storageProvider', scopeId)),
        videoRecorder: await videoRecorderFactory.createForPlatform(platform, runnerConfig, this, await this.container.resolve('storageProvider', scopeId)),
        metricsCollector: await metricsCollectorFactory.createForPlatform(platform, runnerConfig),
        stepHandlerRegistry: await stepHandlerRegistryFactory.createForPlatform(platform, runnerConfig),
        progressReporter: this.progressService
      };

      // Create test runner with dependencies
      const testRunner = TestRunnerFactory.createTestRunnerWithDependencies(dependencies, runnerConfig);

      // 🛡️ INJECT GUARDIAN: Set this ExecutionCoordinator as the step guardian
      // This enables coordinated step-level timeout monitoring
      if (testRunner && typeof (testRunner as any).setStepGuardian === 'function') {
        (testRunner as any).setStepGuardian({
          startStepGuarding: (testId: string, stepIndex: number, stepType: string) => {
            this.nodeTimeoutGuardian.startStepGuarding(testId, stepIndex, stepType);
          },
          clearStepTimeout: (testId: string, stepIndex: number) => {
            this.nodeTimeoutGuardian.clearStepTimeout(testId, stepIndex);
          }
        });
        this.logger.info(`ExecutionCoordinator: Step guardian integration enabled for test ${platformTestRequest.id}`);
      } else {
        this.logger.warn(`ExecutionCoordinator: Test runner does not support step guardian integration`);
      }

      // Initialize test runner
      await testRunner.initialize(this.nodeId, runnerConfig);

      this.logger.info(`ExecutionCoordinator: Created and initialized ${platform} test runner for test ${platformTestRequest.id}`);
      return testRunner;

    } catch (error: any) {
      this.logger.error(`ExecutionCoordinator: Failed to create ${platform} test runner: ${error.message}`);
      throw error;
    }
  }

  /**
   * Setup lock extension for a test
   */
  private setupLockExtension(testId: string, extendLockFunction: (duration: number) => Promise<any>): void {
    const interval = setInterval(() => {
      this.logger.debug(`Extending lock for test ${testId}`);
      extendLockFunction(30000).catch(err => {
        this.logger.error(`Failed to extend lock for test ${testId}: ${err.message}`);
        this.clearLockExtension(testId);
        // Optionally, you might want to stop the test here
      });
    }, 15000); // Extend lock every 15 seconds

    this.lockExtensionIntervals.set(testId, interval);
    this.cleanupOrchestrator!.registerLockExtensionInterval(testId, interval);
  }

  /**
   * Clear lock extension interval
   */
  private clearLockExtension(testId: string): void {
    const interval = this.lockExtensionIntervals.get(testId);
    if (interval) {
      clearInterval(interval);
      this.lockExtensionIntervals.delete(testId);
      this.cleanupOrchestrator!.clearLockExtensionInterval(testId);
    }
  }

  /**
   * Sanitize environment settings to remove sensitive data before sending to hub
   * @param environmentSettings Raw environment settings that may contain sensitive data
   * @returns Sanitized environment settings safe for result transmission
   */
  private sanitizeEnvironmentSettingsForResult(environmentSettings: any): any {
    if (!environmentSettings || typeof environmentSettings !== 'object') {
      return environmentSettings;
    }

    // Deep clone to avoid modifying the original object
    const sanitized = JSON.parse(JSON.stringify(environmentSettings));

    // Remove sensitive SauceLabs credentials
    if (sanitized.sauceLabs) {
      delete sanitized.sauceLabs.accessKey;
      delete sanitized.sauceLabs.username;
      // Keep device information for context in reports
      // selectedDevices is useful for understanding test context
    }

    // Remove sensitive Testinium credentials
    if (sanitized.testinium) {
      delete sanitized.testinium.clientId;
      delete sanitized.testinium.clientSecret;
      delete sanitized.testinium.apiUrl;
      delete sanitized.testinium.issuerUri;
      // Keep device information for context in reports
    }

    // Remove sensitive AI model configuration
    if (sanitized.aiModelConfig) {
      delete sanitized.aiModelConfig.OPENAI_API_KEY;
      delete sanitized.aiModelConfig.OPENAI_BASE_URL;
      // Keep only non-sensitive fields like model name
      if (sanitized.aiModelConfig.MIDSCENE_MODEL_NAME) {
        sanitized.aiModelConfig = {
          MIDSCENE_MODEL_NAME: sanitized.aiModelConfig.MIDSCENE_MODEL_NAME
        };
      } else {
        delete sanitized.aiModelConfig;
      }
    }

    // Remove sensitive proxy credentials
    if (sanitized.proxy) {
      delete sanitized.proxy.username;
      delete sanitized.proxy.password;
    }

    this.logger.debug('ExecutionCoordinator: Sanitized environment settings for result transmission by removing sensitive credentials');
    
    return sanitized;
  }

  /**
   * Enhance test result with additional metadata
   */
  private enhanceTestResult(result: TestResult, testRequest: TestRequest, duration: number): TestResult {
    // CRITICAL FIX: Ensure testRequest fields take precedence over result fields
    // Test runner result might have incomplete or missing metadata
    
    // Sanitize environment settings before including in result
    const sanitizedEnvironmentSettings = this.sanitizeEnvironmentSettingsForResult(testRequest.environmentSettings);
    
    const enhancedResult = {
      ...result,
      // Core test identification - always use from testRequest
      id: testRequest.id,
      scenarioId: testRequest.scenarioId,
      scenarioName: testRequest.scenarioName || result.scenarioName || 'Unknown Scenario',
      
      // Execution metadata - always from testRequest
      runId: testRequest.runId,
      executionId: testRequest.executionId || `exec-default-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      
      // User and organization metadata - always from testRequest
      executedUser: testRequest.executedUser || testRequest.userId || 'system',
      executedUserName: testRequest.executedUserName || 'System User',
      teamId: testRequest.teamId,
      companyId: testRequest.companyId,
      
      // Platform and environment information for proper result processing
      // SANITIZED to remove sensitive data
      platform: testRequest.platform,
      environmentSettings: sanitizedEnvironmentSettings,
      
      // Node and timing information
      nodeId: this.nodeId,
      duration,
      
      // Unique transaction ID for tracking
      transactionId: `result-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    };

    // DEBUG: Log the enhanced result metadata
    this.logger.info(`ExecutionCoordinator: Enhanced result for test ${testRequest.id} - scenarioId: ${enhancedResult.scenarioId}, runId: ${enhancedResult.runId}, executionId: ${enhancedResult.executionId}, executedUser: ${enhancedResult.executedUser}, executedUserName: ${enhancedResult.executedUserName}, teamId: ${enhancedResult.teamId}, companyId: ${enhancedResult.companyId}, platform: ${enhancedResult.platform}`);

    // Log sanitization for security audit
    if (testRequest.environmentSettings) {
      this.logger.info(`ExecutionCoordinator: Sanitized environment settings for test result ${testRequest.id} - removed sensitive credentials before transmission to hub`);
    }

    return enhancedResult;
  }

  /**
   * Create a skipped test result
   */
  private createSkippedResult(testRequest: TestRequest, reason: string): TestResult {
    // Sanitize environment settings before including in result
    const sanitizedEnvironmentSettings = this.sanitizeEnvironmentSettingsForResult(testRequest.environmentSettings);
    
    return {
      id: testRequest.id,
      scenarioId: testRequest.scenarioId,
      scenarioName: testRequest.scenarioName || 'Unknown Scenario',
      name: testRequest.scenario?.name || 'Unknown Test',
      url: testRequest.scenario?.url || '',
      date: new Date().toISOString(),
      status: 'skipped',
      success: false,
      duration: 0,
      steps: [],
      summary: { passed: 0, failed: 0, total: 0, errors: 0 },
      error: reason,
      testId: testRequest.id,
      nodeId: this.nodeId,
      runId: testRequest.runId,
      executionId: testRequest.executionId || `exec-default-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      executedUser: testRequest.executedUser || testRequest.userId || 'system',
      executedUserName: testRequest.executedUserName || 'System User',
      teamId: testRequest.teamId,
      companyId: testRequest.companyId,
      platform: testRequest.platform,
      environmentSettings: sanitizedEnvironmentSettings,
      transactionId: `result-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    };
  }

  /**
   * Create a failed test result
   */
  private createFailedResult(testRequest: TestRequest, error: Error): TestResult {
    // Sanitize environment settings before including in result
    const sanitizedEnvironmentSettings = this.sanitizeEnvironmentSettingsForResult(testRequest.environmentSettings);
    
    return {
      id: testRequest.id,
      scenarioId: testRequest.scenarioId,
      scenarioName: testRequest.scenarioName || 'Unknown Scenario',
      name: testRequest.scenario?.name || testRequest.scenarioName || 'Unknown Test',
      url: testRequest.scenario?.url || '',
      date: new Date().toISOString(),
      status: 'failed',
      success: false,
      duration: 0,
      steps: [],
      summary: {
        passed: 0,
        failed: 1,
        total: testRequest.scenario?.totalSteps || testRequest.scenario?.steps?.length || 1,
        errors: 1
      },
      error: error.message,
      testId: testRequest.id,
      nodeId: this.nodeId,
      runId: testRequest.runId,
      executionId: testRequest.executionId || `exec-default-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      executedUser: testRequest.executedUser || testRequest.userId || 'system',
      executedUserName: testRequest.executedUserName || 'System User',
      teamId: testRequest.teamId,
      companyId: testRequest.companyId,
      platform: testRequest.platform,
      environmentSettings: sanitizedEnvironmentSettings,
      transactionId: `result-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    };
  }

  /**
   * Perform test cleanup
   */
  private async performTestCleanup(testId: string, reason: string): Promise<void> {
    // Flush any pending progress reports before cleanup
    if (this.progressService) {
      await this.progressService.flushAllBatches();
    }

    this.logger.info(`ExecutionCoordinator: Performing cleanup for test ${testId}. Reason: ${reason}`);

    if (!this.cleanupOrchestrator) {
      this.logger.warn(`ExecutionCoordinator: Cleanup orchestrator not available for test ${testId}, cannot perform cleanup.`);
      // Clear current test runner
      this.currentTestRunner = null;
      // Attempt to transition to idle anyway to prevent node from getting stuck
      const currentState = this.stateManager.getCurrentState();
      const transitionResult = await this.stateManager.tryTransitionState(currentState, TestExecutionState.IDLE, testId);

      if (!transitionResult.success) {
        this.logger.warn(`ExecutionCoordinator: Failed to transition from ${currentState} to IDLE for test ${testId}: ${transitionResult.message}. Forcing transition.`);
        await this.stateManager.forceTransitionState(TestExecutionState.IDLE, testId, `Forced due to missing cleanup orchestrator`);
      }
      return;
    }
    
    try {
      const cleanupResult = await this.cleanupOrchestrator.cleanupTest({
        testId,
        reason: reason as CleanupReason,
        testRunner: this.currentTestRunner
      });
      this.logger.info(`ExecutionCoordinator: Cleanup completed for test ${testId} with status ${cleanupResult.success ? 'success' : 'failed'}`);

      // Clear current test runner
      this.currentTestRunner = null;

      // Transition to IDLE state - use current state instead of assuming CLEANING_UP
      const currentState = this.stateManager.getCurrentState();
      const transitionResult = await this.stateManager.tryTransitionState(currentState, TestExecutionState.IDLE, testId);

      if (!transitionResult.success) {
        this.logger.warn(`ExecutionCoordinator: Failed to transition from ${currentState} to IDLE for test ${testId}: ${transitionResult.message}. Forcing transition.`);
        await this.stateManager.forceTransitionState(TestExecutionState.IDLE, testId, `Forced after cleanup completion`);
      }

      this.logger.info(`ExecutionCoordinator: Test ${testId} resources released and state transitioned to IDLE`);
    } catch (error: any) {
      this.logger.error(`ExecutionCoordinator: Error during test cleanup for ${testId}: ${error.message}`);
      // Clear current test runner even on error
      this.currentTestRunner = null;
      // Force transition to IDLE to prevent the node from getting stuck
      await this.stateManager.forceTransitionState(TestExecutionState.IDLE, testId, `Error during cleanup: ${error.message}`);
    }
  }

  /**
   * Wait for ConnectionService to be ready with WebSocket connection
   * This ensures that event listeners are setup only after WebSocket is connected
   */
  private async waitForConnectionServiceReady(timeoutMs: number = 30000): Promise<boolean> {
    this.logger.info(`ExecutionCoordinator: Waiting for ConnectionService to be ready (timeout: ${timeoutMs}ms)`);
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      if (this.connectionService) {
        const wsConnector = this.connectionService.getWebSocketConnector();
        const isConnected = this.connectionService.isWebSocketConnected();
        const nodeId = this.connectionService.getNodeId();
        
        this.logger.debug(`ExecutionCoordinator: ConnectionService check - WebSocket Connector: ${wsConnector ? 'AVAILABLE' : 'NULL'}, Connected: ${isConnected}, NodeID: ${nodeId || 'NULL'}`);
        
        if (wsConnector && isConnected && nodeId) {
          this.logger.info(`ExecutionCoordinator: ConnectionService is ready - WebSocket connected with node ID: ${nodeId}`);
          return true;
        }
      }
      
      // Wait 500ms before checking again
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    this.logger.warn(`ExecutionCoordinator: ConnectionService not ready after ${timeoutMs}ms timeout`);
    return false;
  }
}