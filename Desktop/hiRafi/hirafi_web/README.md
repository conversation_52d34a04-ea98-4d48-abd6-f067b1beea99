# HiRafi Web Uygulaması

Bu proje, HiRafi test otomasyon platformunun web arayüzünü içerir. Next.js ile geliştirilmiş bir React uygulamasıdır.

## İçindekiler

- [<PERSON><PERSON>ş](#genel-bak<PERSON><PERSON>)
- [Teknoloji Yığını](#teknoloji-yığını)
- [<PERSON><PERSON><PERSON>](#kurulum)
- [Docker ile Çalıştırma](#docker-ile-çalıştırma)
- [Geliştirme](#geliştirme)
- [Yapı](#yapı)
- [API Entegrasyonu](#api-entegrasyonu)

## Genel Bakış

HiRafi Web Uygulaması, test senaryolarını yönetmek, test koşumlarını başlatmak ve sonuçları görüntülemek için kullanılan modern bir web arayüzüdür. Kullanıcı dostu arayüzü ile test otomasyonu süreçlerini kolaylaştırır.

## Teknoloji Yığını

- **Framework**: Next.js 15
- **Dil**: TypeScript
- **UI Kütüphaneleri**: 
  - Tailwind CSS
  - shadcn/ui
  - Radix UI
- **State Yönetimi**: React Hooks
- **API İletişimi**: Fetch API

## Kurulum

### Gereksinimler

- Node.js 18.0.0 veya üzeri
- npm veya yarn

### Bağımlılıkları Yükleme

```bash
npm install
# veya
yarn install
```

### Geliştirme Modunda Çalıştırma

```bash
npm run dev
# veya
yarn dev
```

### Üretim için Derleme

```bash
npm run build
# veya
yarn build
```

### Üretim Modunda Çalıştırma

```bash
npm run start
# veya
yarn start
```

## Docker ile Çalıştırma

### Sadece Web Uygulamasını Çalıştırma

```bash
# Docker imajını oluşturma
docker compose build

# Çalıştırma
docker compose up -d
```

### Tüm Sistemi Çalıştırma

Ana dizindeki docker-compose.full.yml dosyasını kullanarak tüm sistemi (Test Hub, Test Node, Redis, MongoDB ve Web Uygulaması) başlatabilirsiniz:

```bash
# Ana dizinde
docker-compose -f docker-compose.full.yml up -d
```

## Çevre Değişkenleri

Web uygulaması aşağıdaki çevre değişkenlerini kullanır:

| Değişken | Açıklama | Varsayılan Değer |
|----------|----------|-----------------|
| `NEXT_PUBLIC_API_BASE_URL` | API'nin URL'si | `http://localhost:5000/api` (geliştirme) veya `https://api.hirafi.ai/api` (üretim) |
| `NODE_ENV` | Çalışma ortamı | `development` |

## Yapı

```
web_app/
├── app/                  # Next.js App Router sayfaları
├── components/           # React bileşenleri
├── hooks/                # Özel React hooks
├── lib/                  # Yardımcı fonksiyonlar ve API istemcileri
│   ├── api/              # API istemcileri
│   └── utils/            # Yardımcı fonksiyonlar
├── models/               # TypeScript tipleri ve modeller
├── public/               # Statik dosyalar
├── styles/               # CSS dosyaları
└── types/                # TypeScript tip tanımlamaları
```

## API Entegrasyonu

Web uygulaması, Test Hub API'si ile iletişim kurmak için `lib/api` modülünü kullanır. Tüm API istekleri bu modül üzerinden yapılır.

API URL'si, `NEXT_PUBLIC_API_BASE_URL` çevre değişkeni ile yapılandırılır. Varsayılan olarak, geliştirme ortamında `http://localhost:5000/api`, üretim ortamında `https://api.hirafi.ai/api` kullanılır.
