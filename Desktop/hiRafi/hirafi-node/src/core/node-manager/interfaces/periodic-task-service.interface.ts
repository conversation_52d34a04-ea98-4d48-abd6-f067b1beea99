/**
 * Periodic Task Service Interface
 * 
 * Defines the contract for periodic task management services.
 * Handles scheduling and lifecycle of recurring tasks.
 */

import { PeriodicTask } from '../types/node-manager-types.js';

/**
 * Periodic Task Service Interface
 * 
 * Manages periodic tasks with proper scheduling and lifecycle management.
 * Replaces the singleton PeriodicTaskManager with dependency injection.
 */
export interface IPeriodicTaskService {
  /**
   * Start all periodic tasks
   */
  startAllTasks(): void;

  /**
   * Stop all periodic tasks
   */
  stopAllTasks(): void;

  /**
   * Add a new periodic task
   * @param id Task identifier
   * @param callback Task function to execute
   * @param interval Interval in milliseconds
   * @param description Task description
   */
  addTask(id: string, callback: () => void | Promise<void>, interval: number, description: string): void;

  /**
   * Remove a periodic task
   * @param id Task identifier
   */
  removeTask(id: string): void;

  /**
   * Check if a task exists
   * @param id Task identifier
   * @returns True if task exists
   */
  hasTask(id: string): boolean;

  /**
   * Get all active tasks
   * @returns Array of active tasks
   */
  getActiveTasks(): PeriodicTask[];

  /**
   * Get task by ID
   * @param id Task identifier
   * @returns Task or undefined if not found
   */
  getTask(id: string): PeriodicTask | undefined;

  /**
   * Update task interval
   * @param id Task identifier
   * @param newInterval New interval in milliseconds
   */
  updateTaskInterval(id: string, newInterval: number): void;

  /**
   * Get task statistics
   * @returns Task execution statistics
   */
  getTaskStats(): {
    totalTasks: number;
    activeTasks: number;
    taskList: string[];
  };
}
