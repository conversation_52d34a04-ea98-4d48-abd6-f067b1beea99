/**
 * Common AI Right Click Handler
 * Handles executing AI right click steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Right Click Handler
 * Handles executing AI right click steps using the AI agent
 */
export class CommonAiRightClickHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiRightClick';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiRightClickHandler';
  }

  /**
   * Execute an AI right click step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiRightClick, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const rightClickTarget = step.prompt || step.value || step.name || step.description;

    if (!rightClickTarget) {
      return { success: false, error: 'Missing prompt, value, name or description for aiRightClick step' };
    }

    try {
      const options = step.deepThink ? { deepThink: true } : {};

      logger.debug(`CommonAiRightClickHandler: Executing right click on "${rightClickTarget}" with options: ${JSON.stringify(options)}`);

      const result = await agent.aiRightClick(rightClickTarget || '', options);
      logger.debug(`aiRightClick result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error;

      const logEntry = formatAIResultForLog('aiRightClick', rightClickTarget, result);
      logEntry.success = isSuccess;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Right click failed: ${rightClickTarget}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Right Click Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiRightClick',
        description: rightClickTarget,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiRightClickHandler: Right click failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 