/**
 * For Loop Control Flow Handler
 * Handles for loop control flow steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';

/**
 * For Loop Control Flow Handler
 * Executes a loop for a specified number of iterations based on AI number evaluation
 */
export class ForLoopHandler implements IStepHandler {
  private stepExecutor: any; // Will be injected by the registry
  private progressReporter: any = null; // Will be injected by the registry

  /**
   * Set the step executor (used for executing nested steps)
   * @param executor Function to execute nested steps
   */
  setStepExecutor(executor: any): void {
    this.stepExecutor = executor;
  }

  /**
   * Set the progress reporter (used for reporting nested step progress)
   * @param progressReporter Progress reporter instance
   */
  setProgressReporter(progressReporter: any): void {
    this.progressReporter = progressReporter;
  }

  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'forLoop';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'ForLoopHandler';
  }

  /**
   * Execute a for loop step
   * @param target The target to execute the step on
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    const iterationQuery = step.iterationCount || step.value || step.prompt;
    
    if (!iterationQuery) {
      const errorMessage = 'Missing iteration count query for for loop step';
      logs.push(`❌ For Loop Error: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    if (!step.loopSteps || step.loopSteps.length === 0) {
      const errorMessage = 'For loop step must have loop steps to execute';
      logs.push(`❌ For Loop Error: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    try {
      logs.push(`🔢 Determining iteration count: "${iterationQuery}"`);
      logger.debug(`ForLoopHandler: Determining iteration count "${iterationQuery}"`);

      let iterationCount: number;

      // Check if it's a direct number or needs AI evaluation
      const directNumber = parseInt(iterationQuery);
      if (!isNaN(directNumber) && directNumber > 0) {
        iterationCount = directNumber;
        logs.push(`✅ Using direct iteration count: ${iterationCount}`);
      } else {
        // Check if agent is available for AI operations
        if (!agent) {
          const errorMessage = 'AI agent is not available for iteration count evaluation';
          logs.push(`❌ For Loop Error: ${errorMessage}`);
          logger.error(`ForLoopHandler: Agent is null - this indicates an issue with agent passing in execution`);
          return { success: false, error: errorMessage };
        }

        // Use AI agent to determine the iteration count via aiQuery
        // This allows getting list of items and using their count as iteration
        const queryEvaluation = await agent.aiQuery('string[]', iterationQuery);
        
        if (queryEvaluation.error) {
          const errorMessage = `Iteration count evaluation failed: ${queryEvaluation.error}`;
          logs.push(`❌ For Loop Error: ${errorMessage}`);
          return { success: false, error: errorMessage };
        }

        // Extract the list from query result
        const resultList = queryEvaluation.result;
        if (!Array.isArray(resultList)) {
          const errorMessage = `Query result is not a list: ${typeof resultList}. Expected an array of items.`;
          logs.push(`❌ For Loop Error: ${errorMessage}`);
          return { success: false, error: errorMessage };
        }

        iterationCount = resultList.length;
        logs.push(`✅ AI found ${iterationCount} items: [${resultList.slice(0, 3).join(', ')}${resultList.length > 3 ? '...' : ''}]`);
      }

      // Safety check for reasonable iteration count
      if (iterationCount > 30) {
        const errorMessage = `Iteration count too high (${iterationCount}). Maximum allowed is 30 for safety.`;
        logs.push(`❌ For Loop Error: ${errorMessage}`);
        return { success: false, error: errorMessage };
      }

      if (iterationCount === 0) {
        logs.push(`ℹ️ Iteration count is 0, skipping loop execution`);
        return { success: true, error: null, data: { iterationCount: 0, totalStepsExecuted: 0 } };
      }

      logs.push(`🔄 Starting for loop: ${iterationCount} iterations with ${step.loopSteps.length} steps each`);

      let totalStepsExecuted = 0;

      // Execute the loop
      for (let iteration = 1; iteration <= iterationCount; iteration++) {
        logs.push(`📍 Loop iteration ${iteration}/${iterationCount}`);

        for (let stepIndex = 0; stepIndex < step.loopSteps.length; stepIndex++) {
          const loopStep = step.loopSteps[stepIndex];
          
          if (!this.stepExecutor) {
            const errorMessage = 'Step executor not available for nested step execution';
            logs.push(`❌ For Loop Error: ${errorMessage}`);
            return { success: false, error: errorMessage };
          }

          const stepResult = await this.stepExecutor(target, loopStep, agent, logs, step.id || `for-loop-${Date.now()}`, 1);
          totalStepsExecuted++;

          // Store step result with screenshot URLs for reporting (optional enhancement)
          if (stepResult.beforeScreenshotUrl || stepResult.afterScreenshotUrl) {
            logs.push(`📸 For Loop: Screenshots captured for step ${stepIndex + 1} in iteration ${iteration}`);
          }

          if (!stepResult.success) {
            logs.push(`❌ For Loop: Step ${stepIndex + 1} failed in iteration ${iteration}: ${stepResult.error}`);
            return { 
              success: false, 
              error: `Loop step failed in iteration ${iteration}: ${stepResult.error}`,
              data: { 
                iterationCount, 
                completedIterations: iteration - 1, 
                totalStepsExecuted, 
                failedIteration: iteration,
                failedStepIndex: stepIndex 
              }
            };
          }
        }

        logs.push(`✅ Completed iteration ${iteration}/${iterationCount}`);
      }

      logs.push(`✅ For loop completed successfully (${iterationCount} iterations, ${totalStepsExecuted} total steps executed)`);
      return { 
        success: true, 
        error: null, 
        data: { iterationCount, completedIterations: iterationCount, totalStepsExecuted } 
      };

    } catch (error: any) {
      const errorMessage = `For loop execution failed: ${error.message}`;
      logs.push(`❌ For Loop Error: ${errorMessage}`);
      logger.error(`ForLoopHandler: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }
} 