/**
 * Shared Step Management Utilities
 * 
 * Common functions for managing test steps across creation and editing stores
 * This eliminates duplicate code and ensures consistency
 */

import { v4 as uuidv4 } from 'uuid'
import { TestStep, ActionType } from '@/models/scenario'

/**
 * Creates a new test step with default values
 */
export function createNewStep(type: ActionType = 'aiAction'): TestStep {
  const baseStep: TestStep = {
    id: uuidv4(),
    type,
    name: '',
    deepThink: false,
  }

  // Set type-specific defaults
  switch (type) {
    case 'sleep':
      return {
        ...baseStep,
        duration: 1,
        value: '1000'
      }
    case 'goto':
      return {
        ...baseStep,
        url: '',
        name: 'Navigate to page'
      }
    case 'ifElse':
      return {
        ...baseStep,
        name: 'If-Else Condition',
        condition: '',
        trueSteps: [],
        falseSteps: []
      }
    case 'forLoop':
      return {
        ...baseStep,
        name: 'For Loop',
        iterationCount: '',
        loopSteps: []
      }
    case 'whileLoop':
      return {
        ...baseStep,
        name: 'While Loop',
        condition: '',
        loopSteps: [],
        maxIterations: 50
      }
    case 'aiInput':
      return {
        ...baseStep,
        name: '',
        value: '',  // Text to input
        target: ''  // Target element
      }
    case 'aiAction':
    case 'aiAssertion':
    case 'aiWaitElement':
    case 'aiTap':
    case 'aiHover':
    case 'aiKeyboardPress':
    case 'aiScroll':
    case 'aiRightClick':
    case 'aiQuery':
    case 'aiString':
    case 'aiNumber':
    case 'aiBoolean':
    case 'aiLocate':
      return {
        ...baseStep,
        name: '',
        prompt: ''  // Use prompt field for AI steps
      }
    default:
      return baseStep
  }
}

/**
 * Updates a step's type and resets relevant properties
 */
export function updateStepType(step: TestStep, newType: ActionType): TestStep {
  const resetStep: TestStep = {
    ...step,
    type: newType,
    // Clear all optional fields
    value: undefined,
    description: undefined,
    url: undefined,
    duration: undefined,
    prompt: undefined,
    target: undefined,
    condition: undefined,
    iterationCount: undefined,
  }

  // Set type-specific defaults
  switch (newType) {
    case 'sleep':
      return {
        ...resetStep,
        duration: 1,
        value: '1000'
      }
    case 'goto':
      return {
        ...resetStep,
        url: '',
        name: 'Navigate to page'
      }
    case 'ifElse':
      return {
        ...resetStep,
        name: 'If-Else Condition',
        condition: '',
        trueSteps: [],
        falseSteps: []
      }
    case 'forLoop':
      return {
        ...resetStep,
        name: 'For Loop',
        iterationCount: '',
        loopSteps: []
      }
    case 'whileLoop':
      return {
        ...resetStep,
        name: 'While Loop',
        condition: '',
        loopSteps: [],
        maxIterations: 50
      }
    case 'aiInput':
      return {
        ...resetStep,
        name: '',
        value: '',  // Text to input
        target: ''  // Target element
      }
    case 'aiAction':
    case 'aiAssertion':
    case 'aiWaitElement':
    case 'aiTap':
    case 'aiHover':
    case 'aiKeyboardPress':
    case 'aiScroll':
    case 'aiRightClick':
    case 'aiQuery':
    case 'aiString':
    case 'aiNumber':
    case 'aiBoolean':
    case 'aiLocate':
      return {
        ...resetStep,
        name: '',
        prompt: ''  // Use prompt field for AI steps
      }
    default:
      return resetStep
  }
}

/**
 * Updates a step field - simplified without unnecessary synchronization
 */
export function updateStepField(step: TestStep, field: keyof TestStep, value: any): TestStep {
  const updatedStep = { ...step, [field]: value }

  // Special handling for aiInput steps - sync name with prompt for display purposes
  if (step.type === "aiInput") {
    if (field === "name") {
      updatedStep.prompt = value  // Keep prompt in sync for display
    } else if (field === "prompt") {
      updatedStep.name = value    // Keep name in sync for display
    }
  }

  // For other AI steps - sync name with prompt for display purposes
  else if (step.type === "aiAction" || step.type === "aiAssertion" || step.type === "aiWaitElement" ||
           step.type === "aiTap" || step.type === "aiHover" || step.type === "aiKeyboardPress" ||
           step.type === "aiScroll" || step.type === "aiRightClick" || step.type === "aiQuery" ||
           step.type === "aiString" || step.type === "aiNumber" || step.type === "aiBoolean" ||
           step.type === "aiLocate") {
    if (field === "name") {
      updatedStep.prompt = value  // Keep prompt in sync for backend
    } else if (field === "prompt") {
      updatedStep.name = value    // Keep name in sync for display
    }
  }

  return updatedStep
}

/**
 * Reorders steps in an array
 */
export function reorderSteps(steps: TestStep[], sourceIndex: number, destinationIndex: number): TestStep[] {
  const newSteps = [...steps]
  const [removed] = newSteps.splice(sourceIndex, 1)
  newSteps.splice(destinationIndex, 0, removed)
  return newSteps
}

/**
 * Adds a step at a specific position
 */
export function addStepAtPosition(steps: TestStep[], position?: number, stepType: ActionType = 'aiAction'): TestStep[] {
  const newStep = createNewStep(stepType)
  const newSteps = [...steps]
  
  if (position !== undefined) {
    newSteps.splice(position, 0, newStep)
  } else {
    newSteps.push(newStep)
  }
  
  return newSteps
}

/**
 * Removes a step by ID
 */
export function removeStepById(steps: TestStep[], stepId: string): TestStep[] {
  return steps.filter(step => step.id !== stepId)
}

/**
 * Updates a specific step in an array
 */
export function updateStepInArray(steps: TestStep[], stepId: string, field: keyof TestStep, value: any): TestStep[] {
  return steps.map(step => 
    step.id === stepId 
      ? updateStepField(step, field, value)
      : step
  )
}

/**
 * Updates a step's type in an array
 */
export function updateStepTypeInArray(steps: TestStep[], stepId: string, newType: ActionType): TestStep[] {
  return steps.map(step => 
    step.id === stepId 
      ? updateStepType(step, newType)
      : step
  )
}

/**
 * Validates steps array
 */
export function validateSteps(steps: TestStep[]): string | null {
  if (!steps || steps.length === 0) {
    return 'At least one test step is required'
  }

  // Check for empty steps based on step type
  const emptySteps = steps.filter(step => {
    if (step.type === 'goto') {
      return !step.url?.trim()
    } else if (step.type === 'aiInput') {
      return !step.value?.trim() || !step.target?.trim()
    } else if (step.type === 'sleep') {
      return !step.duration || step.duration <= 0
    } else {
      // For AI steps, check prompt field
      return !step.prompt?.trim() && !step.name?.trim()
    }
  })

  if (emptySteps.length > 0) {
    return 'All test steps must have required fields filled'
  }

  return null
}


