/**
 * Admin Stats Service
 * Admin dashboard için istatistik bilgilerini sağlar
 */

import { logger } from '../../utils/logger.js';
import { ensureMongoDBConnection, isMongoDBInitialized, usersCollection, companiesCollection, scenariosCollection, reportsCollection, runReportsCollection } from './dbConnection.js';

/**
 * Admin dashboard için istatistik bilgilerini hesaplar
 * @returns Dashboard istatistikleri
 */
export async function getDashboardStats() {
  try {
    // MongoDB bağlantısının kurulmasını bekle
    await ensureMongoDBConnection();
    
    if (!isMongoDBInitialized() || !usersCollection || !companiesCollection || !scenariosCollection || !reportsCollection || !runReportsCollection) {
      return { 
        success: false, 
        error: 'Database connection not established' 
      };
    }
    
    // Bugünün başlangıcı (UTC)
    const today = new Date();
    today.setUTCHours(0, 0, 0, 0);
    
    // Son 30 günün baş<PERSON>ı<PERSON>ı (UTC)
    const last30Days = new Date();
    last30Days.setUTCDate(last30Days.getUTCDate() - 30);
    last30Days.setUTCHours(0, 0, 0, 0);
    
    // Toplam kullanıcı sayısı
    const totalUsers = await usersCollection.countDocuments();
    
    // Aktif kullanıcı sayısı
    const activeUsers = await usersCollection.countDocuments({ active: true });
    
    // Bugün kaydolan kullanıcı sayısı
    const newUsersToday = await usersCollection.countDocuments({ 
      createdAt: { $gte: today } 
    });
    
    // Son 30 gün içinde kaydolan kullanıcı sayısı
    const newUsersLast30Days = await usersCollection.countDocuments({ 
      createdAt: { $gte: last30Days } 
    });
    
    // Toplam şirket sayısı
    const totalCompanies = await companiesCollection.countDocuments();
    
    // Aktif şirket sayısı
    const activeCompanies = await companiesCollection.countDocuments({ 
      status: 'active' 
    });
    
    // Bugün oluşturulan şirket sayısı
    const newCompaniesToday = await companiesCollection.countDocuments({ 
      createdAt: { $gte: today } 
    });
    
    // Son 30 gün içinde oluşturulan şirket sayısı
    const newCompaniesLast30Days = await companiesCollection.countDocuments({ 
      createdAt: { $gte: last30Days } 
    });
    
    // Toplam senaryo sayısı
    const totalScenarios = await scenariosCollection.countDocuments();
    
    // Bugün oluşturulan senaryo sayısı
    const newScenariosToday = await scenariosCollection.countDocuments({ 
      createdAt: { $gte: today } 
    });
    
    // Toplam rapor sayısı
    const totalReports = await reportsCollection.countDocuments();
    
    // Bugün oluşturulan rapor sayısı
    const newReportsToday = await reportsCollection.countDocuments({ 
      createdAt: { $gte: today } 
    });
    
    // Toplam çalıştırma sayısı
    const totalRuns = await runReportsCollection.countDocuments();
    
    // Bugün yapılan çalıştırma sayısı
    const runsToday = await runReportsCollection.countDocuments({ 
      createdAt: { $gte: today } 
    });
    
    // Kullanıcı büyüme oranı (son 30 gün)
    const userGrowthRate = totalUsers > 0 ? (newUsersLast30Days / totalUsers) * 100 : 0;
    
    // Şirket büyüme oranı (son 30 gün)
    const companyGrowthRate = totalCompanies > 0 ? (newCompaniesLast30Days / totalCompanies) * 100 : 0;
    
    // İstatistikleri döndür
    return {
      success: true,
      stats: {
        users: {
          total: totalUsers,
          active: activeUsers,
          newToday: newUsersToday,
          growthRate: parseFloat(userGrowthRate.toFixed(2))
        },
        companies: {
          total: totalCompanies,
          active: activeCompanies,
          newToday: newCompaniesToday,
          growthRate: parseFloat(companyGrowthRate.toFixed(2))
        },
        scenarios: {
          total: totalScenarios,
          newToday: newScenariosToday
        },
        reports: {
          total: totalReports,
          newToday: newReportsToday
        },
        runs: {
          total: totalRuns,
          today: runsToday
        },
        // Eski format için geriye dönük uyumluluk
        totalUsers,
        activeUsers,
        totalCompanies,
        newUsersToday,
        newCompaniesToday,
        userGrowth: parseFloat(userGrowthRate.toFixed(2)),
        companyGrowth: parseFloat(companyGrowthRate.toFixed(2))
      }
    };
  } catch (error) {
    logger.error('[ADMIN] Error getting dashboard stats:', error);
    return { 
      success: false, 
      error: 'Failed to get dashboard stats' 
    };
  }
}
