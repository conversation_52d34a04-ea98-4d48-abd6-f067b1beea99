import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  X, 
  LoaderCircle, 
  Clock, 
  Square, 
  AlertTriangle, 
  FileText 
} from 'lucide-react';

export interface RunStatusBadgeProps {
  status: string;
  simulatedStatus?: string;
  className?: string;
}

/**
 * Centralized run status badge component
 */
export function RunStatusBadge({ status, simulatedStatus, className = "" }: RunStatusBadgeProps) {
  const effectiveStatus = simulatedStatus || status;
  
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'queue':
      case 'queued':
        return {
          className: "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
          icon: <Clock className="h-3 w-3 mr-1" />,
          label: "Queued"
        };
      case 'running':
        return {
          className: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
          icon: <LoaderCircle className="h-3 w-3 mr-1 animate-spin" />,
          label: "Running"
        };
      case 'passed':
      case 'completed':
        return {
          className: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
          icon: <CheckCircle className="h-3 w-3 mr-1" />,
          label: "Passed"
        };
      case 'failed':
        return {
          className: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
          icon: <X className="h-3 w-3 mr-1" />,
          label: "Failed"
        };
      case 'stopped':
        return {
          className: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
          icon: <Square className="h-3 w-3 mr-1" />,
          label: "Stopped"
        };
      case 'partial':
        return {
          className: "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
          icon: <AlertTriangle className="h-3 w-3 mr-1" />,
          label: "Partial"
        };
      case 'created':
        return {
          className: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
          icon: <FileText className="h-3 w-3 mr-1" />,
          label: "Created"
        };
      default:
        return {
          className: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
          icon: <Clock className="h-3 w-3 mr-1" />,
          label: status
        };
    }
  };

  const config = getStatusConfig(effectiveStatus);

  return (
    <Badge className={`${config.className} ${className}`}>
      {config.icon}
      {config.label}
    </Badge>
  );
}

/**
 * Format duration in milliseconds to human readable string
 */
export function formatDuration(durationMs: number | undefined): string {
  if (!durationMs || durationMs <= 0) return "0s";
  
  const seconds = Math.floor(durationMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    return `${hours}h ${remainingMinutes}m ${remainingSeconds}s`;
  } else if (minutes > 0) {
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Calculate progress percentage from test results
 */
export function calcProgress(testResults: {
  total: number;
  completed: number;
  failed: number;
  running: number;
  queued: number;
  stopped: number;
}): number {
  if (!testResults.total || testResults.total === 0) return 0;
  
  const finished = testResults.completed + testResults.failed + testResults.stopped;
  return Math.round((finished / testResults.total) * 100);
}

/**
 * Get progress bar color based on test results
 */
export function getProgressColor(testResults: {
  completed: number;
  failed: number;
  stopped: number;
}): string {
  if (testResults.failed > 0) return "bg-red-500 dark:bg-red-600";
  if (testResults.stopped > 0) return "bg-amber-500 dark:bg-amber-600";
  return "bg-emerald-500 dark:bg-emerald-600";
}
