/**
 * Test Distributor
 * Implements the pull-based model for test distribution
 */

import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { testQueueService } from './TestQueueService.js';
import { nodeRegistry } from '../node/NodeRegistry.js';
import { TestProcess, TestStatus, TestRequest, AndroidEnvironmentSettings } from '../../models/test-types.js';
import { config } from '../../config/index.js';

export interface TestDistributorEvents {
  'test:assigned': (testId: string, nodeId: string) => void;
  'test:unassigned': (testId: string) => void;
  'test:error': (testId: string, error: Error) => void;
}

/**
 * Service for distributing tests to nodes
 * Implements the pull-based model for test distribution
 */
class TestDistributor extends EventEmitter {
  private static instance: TestDistributor;
  private isInitialized: boolean = false;
  // private testAssignments: Map<string, string> = new Map(); // REMOVED - Obsolete with worker-pull model

  private constructor() {
    super();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): TestDistributor {
    if (!TestDistributor.instance) {
      TestDistributor.instance = new TestDistributor();
    }
    return TestDistributor.instance;
  }

  /**
   * Initialize the test distributor
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Initialize the test queue service
      const queueInitialized = await testQueueService.initialize();
      if (!queueInitialized) {
        logger.error('TestDistributor: Failed to initialize test queue service');
        return false;
      }

      // Event listeners are now handled by the centralized EventHandler
      // Direct event handling removed to prevent duplicate event processing

      this.isInitialized = true;
      logger.info('TestDistributor: Initialized successfully');
      return true;
    } catch (error) {
      logger.error(`TestDistributor: Initialization error: ${error}`);
      return false;
    }
  }

  /**
   * Claim a test for a node
   * This is called by nodes in the pull-based model
   * @param nodeId Node ID
   * @returns Promise resolving to the test if successful, null otherwise
   * @deprecated This method is part of the old pull model and should no longer be used. Nodes now claim tests directly from BullMQ.
   */
  public async claimTest(nodeId: string): Promise<TestRequest | null> {
    logger.warn(`TestDistributor: claimTest(${nodeId}) is deprecated and part of the old pull model. Nodes should claim tests directly via BullMQ. Returning null.`);
    // try {
    //   if (!this.isInitialized) {
    //     logger.error('TestDistributor: Not initialized, cannot claim test');
    //     return null;
    //   }

    //   // Check if the node is registered and available
    //   const node = nodeRegistry.getNode(nodeId);
    //   if (!node) {
    //     logger.warn(`TestDistributor: Node ${nodeId} not found, cannot claim test`);
    //     return null;
    //   }

    //   if (node.status !== 'available') {
    //     logger.warn(`TestDistributor: Node ${nodeId} is not available (status: ${node.status}), cannot claim test`);
    //     return null;
    //   }

    //   // Get all tests in the queue
    //   const tests = await testQueueService.getAllTests();
    //   if (tests.length === 0) {
    //     logger.debug(`TestDistributor: No tests available for node ${nodeId}`);
    //     return null;
    //   }

    //   // Get the node's capabilities
    //   const nodeCapabilities = node.capabilities || [];

    //   // Find a test that matches the node's capabilities
    //   // First, try to find a test with a matching platform
    //   let matchingTest = null;

    //   // Check each test for platform compatibility with the node
    //   for (const test of tests) {
    //     // If the test has a platform specified, check if the node supports it
    //     if (test.platform) {
    //       if (nodeCapabilities.includes(test.platform)) {
    //         matchingTest = test;
    //         logger.info(`TestDistributor: Found test ${test.id} with matching platform ${test.platform} for node ${nodeId}`);
    //         break;
    //       }
    //     } else {
    //       // If the test doesn't specify a platform, assume it's a web test
    //       if (nodeCapabilities.includes('web')) {
    //         matchingTest = test;
    //         logger.info(`TestDistributor: Found test ${test.id} with no platform specified, assuming web for node ${nodeId}`);
    //         break;
    //       }
    //     }
    //   }

    //   // If no matching test found, fall back to the first test in the queue
    //   const test = matchingTest || tests[0];

    //   if (!matchingTest) {
    //     logger.warn(`TestDistributor: No test with matching platform found for node ${nodeId}, falling back to first test ${test.id}`);
    //   }

    //   // Mark the node as busy with this test
    //   const nodeMarked = await nodeRegistry.markNodeAsBusy(nodeId, test.id);
    //   if (!nodeMarked) {
    //     logger.error(`TestDistributor: Failed to mark node ${nodeId} as busy with test ${test.id}`);
    //     return null;
    //   }

    //   // Record the test assignment - REMOVED
    //   // this.testAssignments.set(test.id, nodeId);

    //   // Log SauceLabs credentials if this is an Android test
    //   if (test.platform === 'android' && test.environmentSettings) {
    //     // Type guard to check if environmentSettings is AndroidEnvironmentSettings
    //     const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
    //       return settings && settings.platform === 'android';
    //     };

    //     if (isAndroidSettings(test.environmentSettings)) {
    //       const androidSettings = test.environmentSettings;
    //       logger.info(`TestDistributor: SauceLabs credentials for test ${test.id} before assignment: ${
    //         androidSettings.sauceLabs ?
    //         `username: ${androidSettings.sauceLabs.username ? 'PRESENT' : 'MISSING'},
    //          accessKey: ${androidSettings.sauceLabs.accessKey ? 'PRESENT' : 'MISSING'},
    //          region: ${androidSettings.sauceLabs.region || 'N/A'}` :
    //         'MISSING'
    //       }`);
    //     } else {
    //       logger.warn(`TestDistributor: Test ${test.id} has platform 'android' but environmentSettings is not AndroidEnvironmentSettings`);
    //     }
    //   }

    //   // Emit event - REMOVED (test:assigned)
    //   // this.emit('test:assigned', test.id, nodeId);

    //   logger.info(`TestDistributor: Assigned test ${test.id} to node ${nodeId}`);
    //   return test;
    // } catch (error) {
    //   logger.error(`TestDistributor: Error claiming test for node ${nodeId}: ${error}`);
    //   return null;
    // }
    return null; // Deprecated, always return null
  }

  // /**
  //  * Unassign a test from a node
  //  * @param testId Test ID
  //  * @returns Promise resolving to true if successful, false otherwise
  //  * @deprecated This method is obsolete. Node availability is managed by NodeRegistry and TestManager.
  //  */
  // public async unassignTest(testId: string): Promise<boolean> {
  //   logger.warn(`TestDistributor: unassignTest(${testId}) is deprecated and obsolete. Returning false.`);
  //   // try {
  //   //   if (!this.isInitialized) {
  //   //     logger.error('TestDistributor: Not initialized, cannot unassign test');
  //   //     return false;
  //   //   }

  //   //   // Check if the test is assigned - REMOVED
  //   //   const nodeId = this.testAssignments.get(testId);
  //   //   if (!nodeId) {
  //   //     logger.warn(`TestDistributor: Test ${testId} not assigned to any node (according to old map)`);
  //   //     return false; // Or true if we consider it already unassigned
  //   //   }

  //   //   // Mark the node as available
  //   //   const nodeMarked = await nodeRegistry.markNodeAsAvailable(nodeId);
  //   //   if (!nodeMarked) {
  //   //     logger.error(`TestDistributor: Failed to mark node ${nodeId} as available after test ${testId}`);
  //   //     return false;
  //   //   }

  //   //   // Remove the test assignment - REMOVED
  //   //   this.testAssignments.delete(testId);

  //   //   // Emit event - REMOVED (test:unassigned)
  //   //   this.emit('test:unassigned', testId);

  //   //   logger.info(`TestDistributor: Unassigned test ${testId} from node ${nodeId}`);
  //   //   return true;
  //   // } catch (error) {
  //   //   logger.error(`TestDistributor: Error unassigning test ${testId}: ${error}`);
  //   //   return false;
  //   // }
  //   return false;
  // }

  // /**
  //  * Get the node ID for a test
  //  * @param testId Test ID
  //  * @returns Node ID if the test is assigned, null otherwise
  //  * @deprecated This method is obsolete. TestManager retrieves nodeId from test data.
  //  */
  // public getNodeForTest(testId: string): string | null {
  //   logger.warn(`TestDistributor: getNodeForTest(${testId}) is deprecated and obsolete. Returning null.`);
  //   // return this.testAssignments.get(testId) || null;
  //   return null;
  // }

  /**
   * Close the test distributor
   */
  public async close(): Promise<void> {
    if (this.isInitialized) {
      await testQueueService.close();
      this.isInitialized = false;
      logger.info('TestDistributor: Closed');
    }
  }
}

// Export singleton instance
export const testDistributor = TestDistributor.getInstance();
export default testDistributor;
