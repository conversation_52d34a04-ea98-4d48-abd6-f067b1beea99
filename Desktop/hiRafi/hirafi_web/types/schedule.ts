/**
 * Schedule Types
 * Type definitions for schedule-related functionality
 */

// Schedule Status
export type ScheduleStatus = 'active' | 'paused' | 'completed' | 'deleted';

// Schedule Type
export type ScheduleType = 'once' | 'daily' | 'weekly' | 'monthly' | 'hourly';

// Hourly Interval Options
export type HourlyInterval = 1 | 2 | 4 | 8 | 12;

// Days of the week for weekly schedules
export type WeekDay = 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday';

// Notification settings
export interface ScheduleNotifications {
  email: boolean;
  slack: boolean;
  inApp: boolean;
  onSuccess: boolean;
  onFailure: boolean;
}

// Schedule interface
export interface Schedule {
  id: string;
  name: string;
  description?: string;
  scheduleType: ScheduleType;
  hourlyInterval?: HourlyInterval;
  startDate: string;
  startTime: string;
  endDate?: string;
  endTime?: string;
  timezone?: string;
  repeatDays?: WeekDay[];
  runId: string;
  userId: string;
  teamId?: string;
  companyId?: string;
  status: ScheduleStatus;
  createdAt: string;
  updatedAt: string;
  lastRunAt?: string;
  nextRunAt?: string;
  notifications?: ScheduleNotifications;
  emailRecipients?: string;
  slackChannel?: string;
  run?: {
    id: string;
    name: string;
    scenarioIds: string[];
    scenarioCount: number;
  };
  scenarios?: Array<{
    id: string;
    name: string;
  }>;
}

// Schedule creation request
export interface CreateScheduleRequest {
  name: string;
  description?: string;
  scheduleType: ScheduleType;
  hourlyInterval?: HourlyInterval;
  startDate: string;
  startTime: string;
  endDate?: string;
  endTime?: string;
  timezone?: string;
  repeatDays?: WeekDay[];
  runId: string;
  notifications?: ScheduleNotifications;
  emailRecipients?: string;
  slackChannel?: string;
}

// Schedule update request
export interface UpdateScheduleRequest {
  name?: string;
  description?: string;
  scheduleType?: ScheduleType;
  hourlyInterval?: HourlyInterval;
  startDate?: string;
  startTime?: string;
  endDate?: string;
  endTime?: string;
  timezone?: string;
  repeatDays?: WeekDay[];
  runId?: string;
  status?: ScheduleStatus;
  notifications?: ScheduleNotifications;
  emailRecipients?: string;
  slackChannel?: string;
}
