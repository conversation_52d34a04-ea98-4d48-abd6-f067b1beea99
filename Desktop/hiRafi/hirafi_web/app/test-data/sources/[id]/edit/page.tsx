"use client"

import { useState, useTransition, useRef, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { uploadDataSourceCsv, dataSourceApi, inspectExcel, parseExcel, testDbConnection, getDbTables, getDbColumns, getDbPreviewData, executeDbFlow, testApiRequest } from "@/lib/api/test-data"
import {
  Database,
  FileSpreadsheet,
  FileText,
  Variable,
  Key,
  Upload,
  ArrowLeft,
  Loader2,
  Save,
  CheckCircle2,
  X,
  Plus,
  Wand2,
  Code,
  Trash2,
  PlusCircle,
  FilePlus,
  Pencil,
  AtSign,
  RefreshCw,
  Globe,
  Search,
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import ReactFlow, {
  Controls,
  Background,
  applyNodeChanges,
  applyEdgeChanges,
  addEdge,
  Node,
  Edge,
  OnNodesChange,
  OnEdgesChange,
  OnConnect,
  getIncomers,
  getOutgoers,
} from 'reactflow';
import 'reactflow/dist/style.css';
import '@/components/query-builder/nodes.css';
import { CreateDataSourceRequest, UpdateDataSourceRequest, DataSource } from "@/types/test-data";
import InsertNode from '@/components/query-builder/InsertNode';
import UpdateNode from '@/components/query-builder/UpdateNode';
import DeleteNode from '@/components/query-builder/DeleteNode';
import SelectNode from '@/components/query-builder/SelectNode';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

// Tipleri buraya da taşıyalım
type DataSourceType = "database" | "csv" | "excel" | "api"
type FlowStepOperation = 'INSERT' | 'UPDATE' | 'DELETE' | 'SELECT';
type FlowStepCondition = { id: string; col: string; op: string; val: string; };
type FlowStepField = { id: string; col: string; val: string; };
interface FlowStep {
  id: string;
  type: FlowStepOperation;
  tableName: string;
  referenceName: string;
  fields: FlowStepField[];
  conditions: FlowStepCondition[];
  columns: string[]; 
  isFetchingColumns: boolean;
}

let id = 0;
const getId = () => `dndnode_${Date.now()}_${id++}`;

const nodeTypes = { 
  insert: InsertNode,
  update: UpdateNode,
  delete: DeleteNode,
  select: SelectNode,
};

export default function EditDataSourcePage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const [isPending, startTransition] = useTransition()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [activeTab, setActiveTab] = useState<DataSourceType>("database")
  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false)

  const [dataSource, setDataSource] = useState<DataSource | null>(null)

  // DB States
  const [connectionSuccess, setConnectionSuccess] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [dbTables, setDbTables] = useState<string[]>([]);
  const [dbConfigMode, setDbConfigMode] = useState<'read' | 'visual' | 'raw'>('read');
  const [rawSetupQuery, setRawSetupQuery] = useState('');
  
  // DB connection details states (like in new page)
  const [dbDetails, setDbDetails] = useState({
    type: 'mysql',
    user: 'root',
    password: '',
    host: 'localhost',
    port: '3306',
    databaseName: ''
  });
  
  // React Flow States
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [queryPreview, setQueryPreview] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionLogs, setExecutionLogs] = useState<string[]>([]);

  const onNodesChange: OnNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes]
  );
  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );
  const onConnect: OnConnect = useCallback(
    (connection) => {
      setEdges((eds) => addEdge(connection, eds));

      setNodes((nds) => {
        const sourceNode = nds.find((node) => node.id === connection.source);
        const targetNode = nds.find((node) => node.id === connection.target);

        if (!sourceNode || !targetNode) {
          return nds;
        }

        let newTargetData = { ...targetNode.data, version: (targetNode.data.version || 0) + 1 };
        let hasUpdate = false;
        
        // INSERT -> UPDATE or INSERT -> DELETE
        if (sourceNode.type === 'insert' && (targetNode.type === 'update' || targetNode.type === 'delete')) {
          const firstField = sourceNode.data.fields?.[0];
          if (firstField && firstField.col) {
            newTargetData.conditions = [{ ...targetNode.data.conditions[0], col: firstField.col, val: firstField.val }];
            if((targetNode.type === 'delete' || targetNode.type === 'update') && sourceNode.data.table) {
                newTargetData.table = sourceNode.data.table;
            }
            hasUpdate = true;
          }
        }
        // UPDATE -> DELETE
        else if (sourceNode.type === 'update' && targetNode.type === 'delete') {
          const firstCondition = sourceNode.data.conditions?.[0];
           if (firstCondition && firstCondition.col) {
            newTargetData.conditions = [{ ...targetNode.data.conditions[0], ...firstCondition }];
            if(sourceNode.data.table) {
                newTargetData.table = sourceNode.data.table;
            }
            hasUpdate = true;
          }
        }

        if (hasUpdate) {
            return nds.map(node => {
                if (node.id === connection.target) {
                    return { ...node, data: newTargetData };
                }
                return node;
            });
        }

        return nds;
      });
    },
    [setEdges]
  );
  
  const onNodesDelete: (nodes: Node[]) => void = useCallback(
    (deleted) => {
      setEdges(
        deleted.reduce((acc, node) => {
          const incomers = getIncomers(node, nodes, edges);
          const outgoers = getOutgoers(node, nodes, edges);
          const connectedEdges = incomers.concat(outgoers);
          return acc.filter((edge) => !connectedEdges.find((ce) => ce.id === edge.id));
        }, edges)
      );
    },
    [nodes, edges, setEdges]
  );

  // CSV file handling states
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvVariables, setCsvVariables] = useState<Array<{
    id: string;
    name: string;
    value: string;
  }>>([])
  const [isUploadingCsv, setIsUploadingCsv] = useState(false)
  const csvFileInputRef = useRef<HTMLInputElement>(null)
  
  // API states for edit page
  const [apiUrl, setApiUrl] = useState('');
  const [apiMethod, setApiMethod] = useState<'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'>('GET');
  const [apiHeaders, setApiHeaders] = useState([{ key: '', value: '' }]);
  const [apiParams, setApiParams] = useState([{ key: '', value: '', description: '', enabled: true }]);
  const [apiBodyType, setApiBodyType] = useState<'none' | 'json' | 'formdata' | 'urlencoded' | 'raw'>('json');
  const [apiBody, setApiBody] = useState('');
  const [apiFormData, setApiFormData] = useState([{ key: '', value: '', enabled: true }]);
  const [apiUrlEncoded, setApiUrlEncoded] = useState([{ key: '', value: '', enabled: true }]);
  const [apiRawBody, setApiRawBody] = useState('');
  const [apiAuthType, setApiAuthType] = useState<'none' | 'bearer' | 'basic' | 'apikey'>('none');
  const [apiAuth, setApiAuth] = useState({
    bearer: { token: '' },
    basic: { username: '', password: '' },
    apikey: { key: '', value: '', addTo: 'header' as 'header' | 'query' }
  });
  const [apiConfigActiveTab, setApiConfigActiveTab] = useState('params');
  const [apiTestResult, setApiTestResult] = useState<any>(null);
  const [isTestingApi, setIsTestingApi] = useState(false);
  const [apiParsingRules, setApiParsingRules] = useState<Array<{
    id: string;
    variableName: string;
    jsonPath: string;
    type?: 'string' | 'number' | 'boolean';
    transform?: 'uppercase' | 'lowercase' | 'trim' | 'none';
  }>>([]);
  const [extractedVariables, setExtractedVariables] = useState<Array<{
    name: string;
    value: string;
  }>>([]);
  const [selectedJsonPath, setSelectedJsonPath] = useState<string>('');
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());
  const [isGeneratingVariables, setIsGeneratingVariables] = useState(false);
  const [testingVariable, setTestingVariable] = useState<string>('');
  const [variableLastValues, setVariableLastValues] = useState<Record<string, string>>({});
  const [temporaryValues, setTemporaryValues] = useState<Record<string, string>>({});
  
  const dataSourceId = params.id as string;

  const updateNodeData = (nodeId: string, data: any) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          node.data = { ...node.data, ...data };
        }
        return node;
      })
    );
  };
  
  const onTableSelectForNode = async (tableName: string) => {
    console.log('onTableSelectForNode called with:', { 
      tableName, 
      connectionString: dataSource?.connectionString,
      dataSource: dataSource 
    });
    
    if (!dataSource?.connectionString) {
      console.log('No connection string available');
      return [];
    }
    
    try {
      console.log('Calling getDbColumns with:', dataSource.connectionString, tableName);
      const result = await getDbColumns(dataSource.connectionString, tableName);
      console.log('getDbColumns result:', result);
      
      if (result.success && result.data?.columns) {
        console.log('Returning columns:', result.data.columns);
        return result.data.columns;
      }
      console.log('getDbColumns failed:', result.error);
      toast({ title: "Sütun Hatası", description: result.error || "Sütunlar getirilemedi.", variant: "destructive" });
    } catch (error: any) {
      console.log('getDbColumns exception:', error);
      toast({ title: "Hata", description: error.message, variant: "destructive" });
    }
    return [];
  };

  const addNode = useCallback((type: string) => {
    const id = getId();
    const position = {
        x: (Math.random() * 400) + 50,
        y: (Math.random() * 400) + 50,
      };
    
    const commonData = {
      onDelete: () => {
          setNodes((nds) => nds.filter((node) => node.id !== id));
          onNodesDelete([{id, position, data: {}} as Node]);
      },
      dbTables: dbTables,
      connectionString: dataSource?.connectionString, // Yeni node'lara da connection string ver
      updateData: (data: any) => updateNodeData(id, data),
      onTableSelect: onTableSelectForNode,
      version: Date.now(),
    };

    let nodeData;
    switch(type) {
        case 'insert':
            nodeData = { ...commonData, table: '', fields: [{ col: '', val: '' }] };
            break;
        case 'update':
            nodeData = { ...commonData, table: '', fields: [{ col: '', val: '' }], conditions: [{ col: '', op: '=', val: '' }] };
            break;
        case 'delete':
            nodeData = { ...commonData, table: '', conditions: [{ col: '', op: '=', val: '' }] };
            break;
        case 'select':
            nodeData = { 
              ...commonData, 
              table: '', 
              selectedColumns: ['*'],
              whereConditions: [{ col: '', operator: '=', val: '' }],
              orderBy: '',
              orderDirection: 'ASC',
              limitValue: ''
            };
            break;
        default:
            nodeData = { ...commonData, label: 'Unknown Node' };
    }

    const newNode = {
      id,
      type,
      position,
      data: nodeData,
      className: 'query-builder-node',
    };
    setNodes((nds) => nds.concat(newNode));
  }, [setNodes, onNodesDelete, dbTables, onTableSelectForNode]);

  const clearCanvas = () => {
    setNodes([]);
    setEdges([]);
  };

  const generateQueryPreview = (nodesToProcess: Node<any>[]) => {
    let preview = '';
    nodesToProcess.forEach((node, index) => {
      const { type, data } = node;
      const stepName = data.table || `step_${index + 1}`;
      preview += `-- Adım ${index + 1}: ${stepName} (${type})\n`;
      
      switch (type) {
        case 'insert':
          const insertCols = data.fields.filter((f: any) => f.col).map((f: any) => f.col).join(', ');
          const insertVals = data.fields.filter((f: any) => f.col).map((f: any) => `'${f.val}'`).join(', ');
          preview += `INSERT INTO ${data.table || '[Tablo Seçilmedi]'} (${insertCols || '...'}) VALUES (${insertVals || '...'});`;
          break;
        case 'update':
          preview += `UPDATE ${data.table || '[Tablo Seçilmedi]'}\n`;
          const setClauses = data.fields.filter((f: any) => f.col).map((f: any) => `SET ${f.col} = '${f.val}'`).join(',\n');
          preview += `${setClauses || 'SET ...'}\n`;
          const whereClausesUpdate = data.conditions.filter((c: any) => c.col).map((c: any) => `${c.col} ${c.op} '${c.val}'`).join(' AND ');
          preview += `WHERE ${whereClausesUpdate || '...'};`;
          break;
        case 'delete':
           preview += `DELETE FROM ${data.table || '[Tablo Seçilmedi]'}\n`;
           const whereClausesDelete = data.conditions.filter((c: any) => c.col).map((c: any) => `${c.col} ${c.op} '${c.val}'`).join(' AND ');
           preview += `WHERE ${whereClausesDelete || '...'};`;
          break;
        case 'select':
          const selectedCols = data.selectedColumns && data.selectedColumns.length > 0 ? data.selectedColumns.join(', ') : '*';
          preview += `SELECT ${selectedCols}\n`;
          preview += `FROM ${data.table || '[Tablo Seçilmedi]'}`;
          
          // WHERE conditions
          const whereClausesSelect = data.whereConditions?.filter((c: any) => c.col).map((c: any) => `${c.col} ${c.operator} '${c.val}'`).join(' AND ');
          if (whereClausesSelect) {
            preview += `\nWHERE ${whereClausesSelect}`;
          }
          
          // ORDER BY
          if (data.orderBy) {
            preview += `\nORDER BY ${data.orderBy} ${data.orderDirection || 'ASC'}`;
          }
          
          // LIMIT
          if (data.limitValue) {
            preview += `\nLIMIT ${data.limitValue}`;
          }
          
          preview += ';';
          break;
        default:
          preview += '-- Bilinmeyen işlem türü';
      }
      preview += '\n\n';
    });
    return preview;
  };

  useEffect(() => {
    const preview = generateQueryPreview(nodes);
    setQueryPreview(preview);
  }, [nodes]);

  useEffect(() => {
    if (!dataSourceId) return;
    const fetchDataSource = async () => {
      setIsLoading(true);
      try {
      const result = await dataSourceApi.getById(dataSourceId);
      if (result.success && result.data) {
          const fetchedData = result.data;

          setDataSource(fetchedData);
          setActiveTab(fetchedData.type);
          console.log('Data source loaded:', fetchedData);

          // CSV ve Excel için variables verilerini yükle
          if ((fetchedData.type === 'csv' || fetchedData.type === 'excel') && fetchedData.variables) {
            const formattedVariables = fetchedData.variables.map((variable: any, index: number) => ({
              id: variable.id || Math.random().toString(),
              name: variable.name || '',
              value: variable.value || ''
            }));
            setCsvVariables(formattedVariables);
          }

          // API için configuration'ı yükle
          if (fetchedData.type === 'api' && fetchedData.config) {
            console.log('Loading API config:', fetchedData.config);
            
            // Basic API config
            setApiUrl(fetchedData.config.url || '');
            setApiMethod(fetchedData.config.method || 'GET');
            setApiBodyType(fetchedData.config.bodyType || 'json');
            setApiBody(fetchedData.config.body || '');
            setApiRawBody(fetchedData.config.body || '');
            
            // Headers
            if (fetchedData.config.headers && Array.isArray(fetchedData.config.headers)) {
              setApiHeaders(fetchedData.config.headers.length > 0 ? fetchedData.config.headers : [{ key: '', value: '' }]);
            }
            
            // Params
            if (fetchedData.config.params && Array.isArray(fetchedData.config.params)) {
              setApiParams(fetchedData.config.params.length > 0 ? fetchedData.config.params : [{ key: '', value: '', description: '', enabled: true }]);
            }
            
            // Form data
            if (fetchedData.config.formData && Array.isArray(fetchedData.config.formData)) {
              setApiFormData(fetchedData.config.formData.length > 0 ? fetchedData.config.formData : [{ key: '', value: '', enabled: true }]);
            }
            
            // URL encoded
            if (fetchedData.config.urlEncoded && Array.isArray(fetchedData.config.urlEncoded)) {
              setApiUrlEncoded(fetchedData.config.urlEncoded.length > 0 ? fetchedData.config.urlEncoded : [{ key: '', value: '', enabled: true }]);
            }
            
            // Auth
            if (fetchedData.config.authType) {
              setApiAuthType(fetchedData.config.authType);
              if (fetchedData.config.authConfig) {
                setApiAuth(fetchedData.config.authConfig);
              }
            }
            
            // Extraction rules
            if (fetchedData.config.extractionRules && Array.isArray(fetchedData.config.extractionRules)) {
              setApiParsingRules(fetchedData.config.extractionRules);
            }
            
            // Last test result
            if (fetchedData.config.lastTestResult) {
              setApiTestResult(fetchedData.config.lastTestResult);
            }
            
            // Variables
            if (fetchedData.variables && Array.isArray(fetchedData.variables)) {
              setExtractedVariables(fetchedData.variables.map((v: any) => ({
                name: v.name,
                value: v.value || ''
              })));
              
              // Load last values if available
              if (fetchedData.config.variableLastValues) {
                setVariableLastValues(fetchedData.config.variableLastValues);
              }
            }
          }

          if (fetchedData.type === 'database' && fetchedData.config) {
            setDbConfigMode(fetchedData.config.mode || 'read');
            
            // Connection string'i parse et ve dbDetails'e yükle
            if (fetchedData.connectionString) {
              const parsed = parseConnectionString(fetchedData.connectionString);
              if (parsed) {
                setDbDetails(parsed);
              }
            }
            
            // Database mode'a göre verileri yükle
            if (fetchedData.config.mode === 'read' && fetchedData.config.data) {
              // read mode: name-value pairs'i csvVariables state'ine yükle
              const formattedVariables = fetchedData.config.data.map((variable: any, index: number) => ({
                id: variable.id || Math.random().toString(),
                name: variable.name || '',
                value: variable.value || ''
              }));
              setCsvVariables(formattedVariables);
            } else if (fetchedData.config.mode === 'raw') {
              setRawSetupQuery(fetchedData.config.setupQuery || '');
            } else if (fetchedData.config.mode === 'visual' && fetchedData.config.flow) {
              // React Flow node'larını oluştur
              const flowNodes = fetchedData.config.flow.map((step: any) => {
                // Mevcut ID'yi koru, yoksa yeni ID oluştur
                const nodeId = step.id || getId();
                return {
                  id: nodeId,
                  type: step.type,
                  position: step.position,
                  data: { 
                    ...step.data, 
                    dbTables: [], // başlangıçta boş, sonra doldurulacak
                    connectionString: fetchedData.connectionString, // Connection string'i doğrudan ver
                    onTableSelect: onTableSelectForNode, 
                    updateData: (data: any) => updateNodeData(nodeId, data),
                    version: Date.now(), // Node'ları yeniden render etmek için
                    onDelete: () => {
                      setNodes((nds) => nds.filter((node) => node.id !== nodeId));
                      onNodesDelete([{id: nodeId, position: step.position, data: {}} as Node]);
                    }
                  }
                };
              });
              setNodes(flowNodes);
              
              // ID counter'ını mevcut node'lardan sonraya ayarla
              const maxId = Math.max(...flowNodes.map((node: any) => {
                const match = node.id.match(/dndnode_\d+_(\d+)$/);
                return match ? parseInt(match[1]) : 0;
              }));
              id = maxId + 1;
            }
            
            // Otomatik bağlantı kur (tüm database modları için)
            if(fetchedData.connectionString) {
                // Otomatik bağlantı kur
                testDbConnection(fetchedData.connectionString).then(result => {
                    if (result.success) {
                        setConnectionSuccess(true);
                        // Tabloları getir
                        getDbTables(fetchedData.connectionString).then(tablesResult => {
                            console.log('Tables result:', tablesResult);
                            if (tablesResult.success && tablesResult.data?.tables) {
                                console.log('Setting dbTables:', tablesResult.data.tables);
                                setDbTables(tablesResult.data.tables);
                                
                                // Eğer visual mode'da node'lar varsa, onlara dbTables'ı güncelle
                                if (fetchedData.config?.mode === 'visual') {
                                  console.log('Updating nodes with dbTables');
                                  setNodes((prevNodes) => {
                                    console.log('Previous nodes:', prevNodes);
                                    const updatedNodes = prevNodes.map(node => ({
                                      ...node,
                                      data: { 
                                        ...node.data, 
                                        dbTables: tablesResult.data.tables,
                                        connectionString: fetchedData.connectionString, // Connection string'i güncelle
                                        version: Date.now() // Node'ları yeniden render etmek için
                                      }
                                    }));
                                    console.log('Updated nodes:', updatedNodes);
                                    return updatedNodes;
                                  });
                                }
                            }
                        });
                    }
                });
            }
          }
      } else {
        toast({
          title: "Hata",
          description: "Veri kaynağı bilgileri yüklenemedi.",
          variant: "destructive",
        });
        router.push("/test-data");
      }
      } catch (error: any) {
        toast({
          title: "Veri Kaynağı Yükleme Hatası",
          description: error.message || "Veri kaynağı bilgileri yüklenirken bir hata oluştu.",
          variant: "destructive",
        });
        router.push("/test-data");
      } finally {
      setIsLoading(false);
      }
    };
    fetchDataSource();
  }, [dataSourceId, router, toast]);

  // Update connection string when dbDetails change
  useEffect(() => {
    const { type, user, password, host, port, databaseName } = dbDetails;
    if (host && port && databaseName && dataSource) {
      const newConnectionString = `${type}://${user}${password ? `:${password}` : ''}@${host}:${port}/${databaseName}`;
      setDataSource(prev => prev ? { ...prev, connectionString: newConnectionString } : null);
    }
  }, [dbDetails, dataSource?.id]); // Only trigger when dbDetails change or when dataSource.id changes

  const handleVariableChange = (index: number, field: 'name' | 'value', value: string) => {
    const newVariables = [...csvVariables];
    newVariables[index] = { ...newVariables[index], [field]: value };
    setCsvVariables(newVariables);
  };

  const handleRemoveVariable = (index: number) => {
    const newVariables = csvVariables.filter((_, i) => i !== index);
    setCsvVariables(newVariables);
  };

  const handleAddVariable = () => {
    setCsvVariables([...csvVariables, { id: Math.random().toString(), name: '', value: '' }]);
  };

  // Database query execution functions
  const handleExecuteQuery = async () => {
    if (!dataSource?.connectionString) {
      toast({
        title: "Hata",
        description: "Bağlantı dizesi bulunamadı.",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);
    try {
      // Node'ları backend formatına çevir
      const formattedNodes = nodes.map(node => {
        const { 
          onDelete, 
          onTableSelect, 
          dbTables,
          updateData,
          connectionString,
          version,
          table,
          fields,
          conditions,
          selectedColumns,
          whereConditions,
          orderBy,
          orderDirection,
          limitValue,
          referenceName,
          ...restOfData 
        } = node.data;
        
        const baseStep = {
          id: node.id,
          type: node.type,
          position: node.position,
          tableName: table,
          referenceName: referenceName
        };

        // Node tipine göre farklı veri ekle
        if (node.type === 'select') {
          return {
            ...baseStep,
            selectedColumns: selectedColumns,
            whereConditions: whereConditions,
            orderBy: orderBy,
            orderDirection: orderDirection,
            limitValue: limitValue
          };
        } else {
          return {
            ...baseStep,
            fields: fields,
            conditions: conditions
          };
        }
      });

      const result = await executeDbFlow(dataSource.connectionString, formattedNodes);
      if (result.success) {
        toast({
          title: "Sorgu Başarılı",
          description: "Query başarıyla çalıştırıldı.",
        });
        setExecutionLogs(result.data?.logs || []);
        
        // SELECT sonuçları zaten backend loglarında temiz bir şekilde gösteriliyor
      } else {
        toast({
          title: "Sorgu Hatası",
          description: result.error || "Query çalıştırılırken bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Hata",
        description: error.message || "Beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const handleTestQuery = async () => {
    if (!dataSource?.connectionString || !rawSetupQuery) {
      toast({
        title: "Hata",
        description: "Bağlantı dizesi veya SQL sorgusu bulunamadı.",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);
    try {
      // Raw SQL query test için executeDbFlow kullanabiliriz
      const result = await executeDbFlow(dataSource.connectionString, [{
        type: 'raw',
        query: rawSetupQuery
      }]);
      
      if (result.success) {
        toast({
          title: "Test Başarılı",
          description: "SQL sorgusu başarıyla test edildi.",
        });
        setExecutionLogs(result.data?.logs || []);
      } else {
        toast({
          title: "Test Hatası",
          description: result.error || "SQL sorgusu test edilirken bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Hata",
        description: error.message || "Beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  // Database connection functions (like in new page)
  const parseConnectionString = (connectionString: string) => {
    try {
      // Parse connection string like: mysql://user:password@host:port/database
      const url = new URL(connectionString);
      return {
        type: url.protocol.replace(':', ''),
        user: url.username || 'root',
        password: url.password || '',
        host: url.hostname || 'localhost',
        port: url.port || '3306',
        databaseName: url.pathname.substring(1) // Remove leading slash
      };
    } catch {
      return null;
    }
  };

  const handleDbDetailChange = (field: keyof typeof dbDetails, value: string) => {
    setDbDetails(prev => ({ ...prev, [field]: value }));
  };

  const handleTestConnection = async () => {
    if (!dataSource?.connectionString) {
      toast({
        title: "Hata",
        description: "Bağlantı dizesi bulunamadı.",
        variant: "destructive",
      });
      return;
    }

    setIsTesting(true);
    try {
      const result = await testDbConnection(dataSource.connectionString);
      if (result.success) {
        setConnectionSuccess(true);
        toast({
          title: "Bağlantı Başarılı",
          description: "Veritabanı bağlantısı başarıyla kuruldu.",
        });
        
        // Get tables
        const tablesResult = await getDbTables(dataSource.connectionString);
        if (tablesResult.success && tablesResult.data?.tables) {
          setDbTables(tablesResult.data.tables);
          
          // Eğer visual mode'da node'lar varsa, onlara dbTables'ı güncelle
          if (dbConfigMode === 'visual') {
            setNodes((prevNodes) => 
              prevNodes.map(node => ({
                ...node,
                data: { 
                  ...node.data, 
                  dbTables: tablesResult.data.tables,
                  connectionString: dataSource?.connectionString, // Connection string'i güncelle
                  version: Date.now() // Node'ları yeniden render etmek için
                }
              }))
            );
          }
        }
      } else {
        toast({
          title: "Bağlantı Hatası",
          description: result.error || "Veritabanı bağlantısı kurulamadı.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Hata",
        description: error.message || "Beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleClearConnection = () => {
    setConnectionSuccess(false);
    setDbTables([]);
  };

  // DB'den veri güncelleme fonksiyonu (smart comparison)
  const handleRefreshFromDb = async () => {
    if (!dataSource?.connectionString || !dataSource?.config?.table || !dataSource?.config?.nameColumn || !dataSource?.config?.valueColumn) {
      toast({
        title: "Hata",
        description: "Database bağlantı bilgileri veya tablo/sütun bilgileri eksik.",
        variant: "destructive",
      });
      return;
    }

    setIsExecuting(true);
    try {
      const result = await getDbPreviewData(
        dataSource.connectionString,
        dataSource.config.table,
        dataSource.config.nameColumn,
        dataSource.config.valueColumn
      );

      if (result.success && result.data) {
        const refreshedVariables = result.data.map((item: any, index: number) => ({
          id: `db-var-${Date.now()}-${index}`,
          name: String(item.name || ''),
          value: String(item.value || '')
        }));

        // Mevcut veriler ile karşılaştır
        const currentData = csvVariables.map((v: any) => ({ name: v.name, value: v.value }));
        const newData = refreshedVariables.map((v: any) => ({ name: v.name, value: v.value }));
        
        // Veri karşılaştırması
        const isDataEqual = currentData.length === newData.length && 
          currentData.every((current: any) => 
            newData.some((fresh: any) => fresh.name === current.name && fresh.value === current.value)
          );

        if (isDataEqual) {
          toast({
            title: "Veri Güncel",
            description: "Database'deki veriler ile mevcut veriler aynı. Değişiklik tespit edilmedi.",
          });
        } else {
          // Değişiklikleri analiz et
          const addedItems = newData.filter((fresh: any) => 
            !currentData.some((current: any) => current.name === fresh.name)
          );
          const removedItems = currentData.filter((current: any) => 
            !newData.some((fresh: any) => fresh.name === current.name)
          );
          const modifiedItems = newData.filter((fresh: any) => 
            currentData.some((current: any) => current.name === fresh.name && current.value !== fresh.value)
          );

          setCsvVariables(refreshedVariables);

          // Detaylı bildirim
          let description = `Database'den ${refreshedVariables.length} adet veri çekildi.`;
          const changes = [];
          if (addedItems.length > 0) changes.push(`${addedItems.length} yeni`);
          if (modifiedItems.length > 0) changes.push(`${modifiedItems.length} değiştirilmiş`);
          if (removedItems.length > 0) changes.push(`${removedItems.length} kaldırılmış`);
          
          if (changes.length > 0) {
            description += ` (${changes.join(', ')})`;
          }

          toast({
            title: "Veriler Güncellendi",
            description: description,
          });
        }
      } else {
        toast({
          title: "Güncelleme Hatası",
          description: result.error || "Veriler database'den çekilirken bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Hata",
        description: error.message || "Beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  // Veri kaynağı türünü değiştirme
  const handleTypeChange = (type: DataSourceType) => {
    // Prevent changing the type if it's 'file' from an existing source
    if (dataSource?.type === 'file' && type !== 'csv') {
      toast({
        title: "Tür Değiştirilemez",
        description: "Dosya tabanlı bir veri kaynağının türü değiştirilemez.",
        variant: "default",
      });
      return;
    }
    if(dataSource) {
      setDataSource({ ...dataSource, type: type as any })
    }
    setActiveTab(type)
  }

  const handleClearCsv = () => {
    setCsvFile(null);
    setCsvVariables([]);
    if(dataSource) {
       setDataSource({ ...dataSource, filePath: "" });
    }
    if (csvFileInputRef.current) {
      csvFileInputRef.current.value = "";
    }
    toast({
      title: "CSV Verileri Temizlendi",
      description: "Yükleme iptal edildi. Yeni bir dosya seçebilirsiniz.",
    })
  };

  // CSV file handling functions
  const handleCsvFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast({
        title: "Geçersiz dosya türü",
        description: "Lütfen sadece .csv uzantılı dosyalar yükleyin.",
        variant: "destructive",
      })
      return
    }

    setCsvFile(file)
    if(dataSource) {
      setDataSource({ ...dataSource, filePath: file.name })
    }
    setIsUploadingCsv(true)
    
    try {
      const result = await uploadDataSourceCsv(file)
      
      if (result.success && result.data) {
        setCsvVariables(result.data.variables)
        toast({
          title: "CSV dosyası yüklendi",
          description: `${result.data.totalRows} değişken başarıyla bulundu.`,
          variant: "default",
        })
      } else {
        toast({
          title: "CSV yükleme hatası",
          description: result.error || "Dosya yüklenirken bir hata oluştu.",
          variant: "destructive",
        })
        setCsvFile(null)
        if(dataSource){
          setDataSource({ ...dataSource, filePath: "" })
        }
      }
    } catch (error) {
      toast({
        title: "CSV yükleme hatası",
        description: "Dosya yüklenirken beklenmeyen bir hata oluştu.",
        variant: "destructive",
      })
      setCsvFile(null)
      if(dataSource){
        setDataSource({ ...dataSource, filePath: "" })
      }
    } finally {
      setIsUploadingCsv(false)
    }
  }

  const handleCsvUploadClick = () => {
    csvFileInputRef.current?.click()
  }

  // API handler functions
  const handleHeaderChange = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...apiHeaders];
    newHeaders[index][field] = value;
    setApiHeaders(newHeaders);
  };

  const addHeader = () => {
    setApiHeaders([...apiHeaders, { key: '', value: '' }]);
  };

  const removeHeader = (index: number) => {
    const newHeaders = apiHeaders.filter((_, i) => i !== index);
    setApiHeaders(newHeaders.length > 0 ? newHeaders : [{ key: '', value: '' }]);
  };

  const handleParamChange = (index: number, field: 'key' | 'value' | 'description', value: string) => {
    const newParams = [...apiParams];
    newParams[index][field] = value;
    setApiParams(newParams);
  };

  const toggleParamEnabled = (index: number) => {
    const newParams = [...apiParams];
    newParams[index].enabled = !newParams[index].enabled;
    setApiParams(newParams);
  };

  const addParam = () => {
    setApiParams([...apiParams, { key: '', value: '', description: '', enabled: true }]);
  };

  const removeParam = (index: number) => {
    const newParams = apiParams.filter((_, i) => i !== index);
    setApiParams(newParams.length > 0 ? newParams : [{ key: '', value: '', description: '', enabled: true }]);
  };

  // Form data handlers
  const handleFormDataChange = (index: number, field: 'key' | 'value', value: string) => {
    const newFormData = [...apiFormData];
    newFormData[index][field] = value;
    setApiFormData(newFormData);
  };

  const toggleFormDataEnabled = (index: number) => {
    const newFormData = [...apiFormData];
    newFormData[index].enabled = !newFormData[index].enabled;
    setApiFormData(newFormData);
  };

  const addFormData = () => {
    setApiFormData([...apiFormData, { key: '', value: '', enabled: true }]);
  };

  const removeFormData = (index: number) => {
    const newFormData = apiFormData.filter((_, i) => i !== index);
    setApiFormData(newFormData.length > 0 ? newFormData : [{ key: '', value: '', enabled: true }]);
  };

  // URL encoded handlers
  const handleUrlEncodedChange = (index: number, field: 'key' | 'value', value: string) => {
    const newUrlEncoded = [...apiUrlEncoded];
    newUrlEncoded[index][field] = value;
    setApiUrlEncoded(newUrlEncoded);
  };

  const toggleUrlEncodedEnabled = (index: number) => {
    const newUrlEncoded = [...apiUrlEncoded];
    newUrlEncoded[index].enabled = !newUrlEncoded[index].enabled;
    setApiUrlEncoded(newUrlEncoded);
  };

  const addUrlEncoded = () => {
    setApiUrlEncoded([...apiUrlEncoded, { key: '', value: '', enabled: true }]);
  };

  const removeUrlEncoded = (index: number) => {
    const newUrlEncoded = apiUrlEncoded.filter((_, i) => i !== index);
    setApiUrlEncoded(newUrlEncoded.length > 0 ? newUrlEncoded : [{ key: '', value: '', enabled: true }]);
  };

  const buildFinalHeaders = () => {
    const headers = [...apiHeaders.filter(h => h.key && h.value)];
    
    // Add auth header based on type
    switch (apiAuthType) {
      case 'bearer':
        if (apiAuth.bearer.token) {
          headers.push({ key: 'Authorization', value: `Bearer ${apiAuth.bearer.token}` });
        }
        break;
      case 'basic':
        if (apiAuth.basic.username && apiAuth.basic.password) {
          const encoded = btoa(`${apiAuth.basic.username}:${apiAuth.basic.password}`);
          headers.push({ key: 'Authorization', value: `Basic ${encoded}` });
        }
        break;
      case 'apikey':
        if (apiAuth.apikey.key && apiAuth.apikey.value && apiAuth.apikey.addTo === 'header') {
          headers.push({ key: apiAuth.apikey.key, value: apiAuth.apikey.value });
        }
        break;
    }
    
    return headers;
  };

  const buildFinalBody = () => {
    switch (apiBodyType) {
      case 'json':
        return apiBody;
      case 'raw':
        return apiRawBody;
      default:
        return '';
    }
  };

  const handleTestApi = async () => {
    if (!apiUrl) {
      toast({
        title: "URL Gerekli",
        description: "Lütfen API URL'sini girin.",
        variant: "destructive",
      });
      return;
    }

    setIsTestingApi(true);
    
    try {
      const result = await testApiRequest({
        method: apiMethod,
        url: apiUrl,
        headers: buildFinalHeaders(),
        body: buildFinalBody() || '',
        bodyType: apiBodyType,
        params: apiParams.filter(p => p.enabled).map(p => ({ key: p.key, value: p.value }))
      });

      if (result.success) {
        setApiTestResult(result);
        
        // Reset expanded paths to refresh the JSON view
        setExpandedPaths(new Set());
        
        toast({
          title: "API Test Başarılı",
          description: `Status: ${result.data?.status || 'Unknown'}`,
        });
      } else {
        toast({
          title: "API Test Başarısız",
          description: result.message || "API test edilemedi.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "API Test Hatası",
        description: error.message || "API test edilirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsTestingApi(false);
    }
  };

  const addParsingRule = () => {
    const newRule = {
      id: Date.now().toString(),
      variableName: '',
      jsonPath: '',
      type: 'string' as const,
      transform: 'none' as const
    };
    setApiParsingRules(prev => [...prev, newRule]);
  };

  const removeParsingRule = (id: string) => {
    setApiParsingRules(prev => prev.filter(rule => rule.id !== id));
  };

  const updateParsingRule = (id: string, field: string, value: string) => {
    setApiParsingRules(prev => prev.map(rule => 
      rule.id === id ? { ...rule, [field]: value } : rule
    ));
  };

  const extractVariablesFromResponse = () => {
    setIsGeneratingVariables(true);
    const newExtracted: Array<{ name: string; value: string; originalValue?: string }> = [];

    apiParsingRules.forEach(rule => {
      if (!rule.variableName) return;

      try {
        let originalValue: string;
        
        // Extract from API response
        if (!apiTestResult || !apiTestResult.data) {
          originalValue = '';
        } else {
          // Fix JSON path - remove 'data.' prefix if it exists since we're already in apiTestResult.data
          let cleanPath = rule.jsonPath;
          if (cleanPath.startsWith('data.')) {
            cleanPath = cleanPath.substring(5); // Remove 'data.' prefix
          }
          
          const rawValue = getValueByPath(apiTestResult.data, cleanPath);
          originalValue = rawValue !== undefined && rawValue !== null ? String(rawValue) : '';
        }

        newExtracted.push({
          name: rule.variableName,
          value: '', // Don't populate value here, only on test
          originalValue: originalValue
        });
      } catch (error) {
        console.error(`Error extracting ${rule.variableName}:`, error);
        newExtracted.push({
          name: rule.variableName,
          value: '',
          originalValue: ''
        });
      }
    });

    // Merge with existing variables instead of replacing
    setExtractedVariables(prev => {
      const merged = [...prev];
      
      newExtracted.forEach(newVar => {
        const existingIndex = merged.findIndex(v => v.name === newVar.name);
        if (existingIndex >= 0) {
          // Update existing variable but keep current value if it exists
          merged[existingIndex] = {
            ...newVar,
            value: merged[existingIndex].value // Keep current value
          };
        } else {
          // Add new variable
          merged.push(newVar);
        }
      });
      
      return merged;
    });

    // Set initial lastValues from extracted originalValues
    const initialLastValues: Record<string, string> = {};
    newExtracted.forEach(newVar => {
      if (newVar.originalValue) {
        initialLastValues[newVar.name] = newVar.originalValue;
      }
    });
    
    setVariableLastValues(prev => ({
      ...prev,
      ...initialLastValues
    }));
    
    // Force a re-render to ensure the UI updates
    setTimeout(() => {
      setIsGeneratingVariables(false);
      toast({
        title: "Variables Added",
        description: `Added ${newExtracted.length} variables to the list.`,
      });
    }, 100);
  };

  const getValueByPath = (obj: any, path: string): any => {
    if (!path) return obj;
    
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current == null) return undefined;
      
      if (key.includes('[') && key.includes(']')) {
        const [arrayKey, indexStr] = key.split('[');
        const index = parseInt(indexStr.replace(']', ''));
        current = current[arrayKey];
        if (Array.isArray(current) && index >= 0 && index < current.length) {
          current = current[index];
        } else {
          return undefined;
        }
      } else {
        current = current[key];
      }
    }
    
    return current;
  };

  const updateVariableValue = (variableName: string, newValue: string) => {
    setExtractedVariables(prev => 
      prev.map(variable => 
        variable.name === variableName 
          ? { ...variable, value: newValue }
          : variable
      )
    );
  };

  // Interactive JSON viewer functions
  const toggleExpanded = (path: string) => {
    const newExpanded = new Set(expandedPaths);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedPaths(newExpanded);
  };

  const handleJsonPathSelect = (path: string) => {
    setSelectedJsonPath(path);
  };

  const addSelectedPathAsRule = () => {
    if (!selectedJsonPath) return;
    
    // Generate a better variable name from the path
    const pathParts = selectedJsonPath.split('.');
    const lastPart = pathParts[pathParts.length - 1];
    const suggestedName = lastPart.replace(/\[.*\]/, ''); // Remove array indices
    
    const newRule = {
      id: `rule-${Date.now()}`,
      variableName: suggestedName || 'variable',
      jsonPath: selectedJsonPath,
      type: 'string' as const,
      transform: 'none' as const
    };
    setApiParsingRules([...apiParsingRules, newRule]);
    setSelectedJsonPath('');
    
    toast({
      title: "Variable Rule Added",
      description: `Added variable "${suggestedName}" for path: ${selectedJsonPath}`,
    });
  };

  // Recursive JSON renderer component
  const renderJsonValue = (value: any, path: string = '', depth: number = 0): React.ReactNode => {
    if (value === null) {
      return <span className="text-gray-500 italic">null</span>;
    }
    
    if (typeof value === 'string') {
      return (
        <span 
          className={`text-green-600 cursor-pointer hover:bg-green-50 px-1 rounded 
            ${selectedJsonPath === path ? 'bg-blue-100' : ''}`}
          onClick={() => handleJsonPathSelect(path)}
          title={`Click to select path: ${path}`}
        >
          "{value}"
        </span>
      );
    }
    
    if (typeof value === 'number') {
      return (
        <span 
          className={`text-blue-600 cursor-pointer hover:bg-blue-50 px-1 rounded 
            ${selectedJsonPath === path ? 'bg-blue-100' : ''}`}
          onClick={() => handleJsonPathSelect(path)}
          title={`Click to select path: ${path}`}
        >
          {value}
        </span>
      );
    }
    
    if (typeof value === 'boolean') {
      return (
        <span 
          className={`text-purple-600 cursor-pointer hover:bg-purple-50 px-1 rounded 
            ${selectedJsonPath === path ? 'bg-blue-100' : ''}`}
          onClick={() => handleJsonPathSelect(path)}
          title={`Click to select path: ${path}`}
        >
          {value.toString()}
        </span>
      );
    }
    
    if (Array.isArray(value)) {
      const isExpanded = expandedPaths.has(path);
      return (
        <div className="ml-4">
          <span 
            className="cursor-pointer text-gray-600 hover:text-gray-800"
            onClick={() => toggleExpanded(path)}
          >
            {isExpanded ? '▼' : '▶'} [{value.length} items]
          </span>
          {isExpanded && (
            <div className="ml-4 border-l-2 border-gray-200 pl-2">
              {value.map((item, index) => (
                <div key={index} className="my-1">
                  <span className="text-gray-500">[{index}]: </span>
                  {renderJsonValue(item, `${path}[${index}]`, depth + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    if (typeof value === 'object') {
      const isExpanded = expandedPaths.has(path);
      const keys = Object.keys(value);
      return (
        <div className="ml-4">
          <span 
            className="cursor-pointer text-gray-600 hover:text-gray-800"
            onClick={() => toggleExpanded(path)}
          >
            {isExpanded ? '▼' : '▶'} {'{' + keys.length + ' keys}'}
          </span>
          {isExpanded && (
            <div className="ml-4 border-l-2 border-gray-200 pl-2">
              {keys.map((key) => (
                <div key={key} className="my-1">
                  <span className="text-blue-800 font-medium">{key}: </span>
                  {renderJsonValue(value[key], path ? `${path}.${key}` : key, depth + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    return <span className="text-gray-500">{String(value)}</span>;
  };

  const testSingleVariable = async (variableName: string) => {
    if (!apiUrl) {
      toast({
        title: "URL Gerekli",
        description: "API URL'si tanımlı değil.",
        variant: "destructive",
      });
      return;
    }

    setTestingVariable(variableName);
    
    try {
      // Build query string
      const enabledParams = apiParams.filter(p => p.enabled && p.key && p.value);
      const queryString = enabledParams.length > 0 
        ? '?' + enabledParams.map(p => `${encodeURIComponent(p.key)}=${encodeURIComponent(p.value)}`).join('&')
        : '';
      
      const fullUrl = apiUrl + queryString;
      
      // Prepare body based on body type
      let body: string | undefined;
      if (apiMethod !== 'GET') {
        switch (apiBodyType) {
          case 'json':
            body = apiBody.trim() || undefined;
            break;
          case 'formdata':
            const formData = new FormData();
            apiFormData.filter(item => item.enabled && item.key).forEach(item => {
              formData.append(item.key, item.value);
            });
            body = formData as any;
            break;
          case 'urlencoded':
            const urlEncodedData = apiUrlEncoded
              .filter(item => item.enabled && item.key)
              .map(item => `${encodeURIComponent(item.key)}=${encodeURIComponent(item.value)}`)
              .join('&');
            body = urlEncodedData || undefined;
            break;
          case 'raw':
            body = apiRawBody.trim() || undefined;
            break;
        }
      }
      
      const result = await testApiRequest({
        method: apiMethod,
        url: fullUrl,
        headers: buildFinalHeaders(),
        body: body || '',
        bodyType: apiBodyType,
        params: apiParams.filter(p => p.enabled).map(p => ({ key: p.key, value: p.value }))
      });
      
      if (result.success && result.data) {
        // Find the rule for this variable
        const rule = apiParsingRules.find(r => r.variableName === variableName);
        if (rule) {
          // Extract the value using jsonPath
          const extractedValue = getValueByPath(result.data, rule.jsonPath);
          
          if (extractedValue !== undefined) {
            let transformedValue = String(extractedValue);
            
            // Apply transformation
            switch (rule.transform) {
              case 'uppercase':
                transformedValue = transformedValue.toUpperCase();
                break;
              case 'lowercase':
                transformedValue = transformedValue.toLowerCase();
                break;
              case 'trim':
                transformedValue = transformedValue.trim();
                break;
            }
            
            // Update the last value immediately
            updateVariableValue(variableName, transformedValue);
            
            // Show current value temporarily
            setTemporaryValues(prev => ({
              ...prev,
              [variableName]: transformedValue
            }));
            
            // Auto-clear current value after 10 seconds
            setTimeout(() => {
              setTemporaryValues(prev => {
                const newValues = { ...prev };
                delete newValues[variableName];
                return newValues;
              });
            }, 10000);
            
          } else {
            toast({
              title: "Value Not Found",
              description: `Could not extract value from path: ${rule.jsonPath}`,
              variant: "destructive",
            });
          }
        }
      } else {
        toast({
          title: "Test Failed",
          description: result.message || "Variable test failed.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Test Error",
        description: error.message || "Error testing variable.",
        variant: "destructive",
      });
    } finally {
      setTestingVariable('');
    }
  };

  // Veri kaynağını kaydetme
  const handleSave = async () => {
    if (!dataSource) return;
    setIsSaving(true);
    
    const payload: UpdateDataSourceRequest = {
      name: dataSource.name,
      description: dataSource.description,
      isActive: dataSource.isActive,
      connectionString: dataSource.connectionString,
    };

    if (activeTab === 'database') {
        payload.config = {
            provider: 'database',
            mode: dbConfigMode,
        };
        if (dbConfigMode === 'read') {
            // read mode: name-value pairs'i kaydet
            payload.config.data = csvVariables.map(variable => ({
                name: variable.name,
                value: variable.value
            }));
        } else if (dbConfigMode === 'visual') {
            payload.config.flow = nodes.map(node => {
                const { onDelete, onTableSelect, dbTables, updateData, ...data } = node.data;
                return { id: node.id, type: node.type, position: node.position, data: data };
            });
        } else if (dbConfigMode === 'raw') {
            payload.config.setupQuery = rawSetupQuery;
        }
    } else if (activeTab === 'csv' || activeTab === 'excel') {
        // CSV ve Excel için variables verilerini kaydet
        payload.variables = csvVariables.map(variable => ({
            name: variable.name,
            value: variable.value
        }));
    } else if (activeTab === 'api') {
        // API configuration with comprehensive data
        payload.config = {
            provider: 'api',
            
            // API Request Configuration (flattened)
            url: apiUrl,
            method: apiMethod,
            headers: buildFinalHeaders(),
            bodyType: apiBodyType,
            body: buildFinalBody(),
            authType: apiAuthType,
            authConfig: apiAuth,
            
            // Variable Extraction Rules
            extractionRules: apiParsingRules.filter(rule => rule.variableName && rule.jsonPath).map(rule => ({
                id: rule.id,
                variableName: rule.variableName,
                jsonPath: rule.jsonPath,
                type: rule.type || 'string',
                transform: rule.transform || 'none'
            })),
            
            // Test Results and Metadata
            lastTestResult: apiTestResult,
            lastTestDate: new Date().toISOString(),
            variableLastValues: variableLastValues,
        } as any;
        
        // Add extracted variables (variables that will be available as @variable_name)
        payload.variables = extractedVariables.map(v => ({ 
            name: v.name, 
            value: v.value || '',
            lastUpdated: new Date().toISOString()
        }));
    }
    
    try {
      await dataSourceApi.update(dataSourceId, payload);
      toast({
        title: "Veri Kaynağı Güncellendi",
        description: `"${dataSource.name}" başarıyla güncellendi.`,
        variant: "default",
      });
      setSaveSuccess(true);
      setTimeout(() => {
        router.push("/test-data");
      }, 1500);
    } catch (e: any) {
      toast({
        title: "Kaydetme Hatası",
        description: e.message || "Veri kaynağı güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading || !dataSource) {
    return (
        <div className="flex h-screen w-full items-center justify-center bg-white dark:bg-gray-950">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        </div>
    );
  }

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Veri Kaynağını Düzenle</h1>
              <p className="text-white/80">"{dataSource.name}" kaynağının detaylarını düzenleyin.</p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="bg-white/10 text-white border-white/20 hover:bg-white/20 hover:text-white"
                onClick={() => router.push("/test-data")}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Geri Dön
              </Button>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900 p-6">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Genel Bilgiler</CardTitle>
                <CardDescription>Veri kaynağınızın temel detaylarını düzenleyin.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                  <div className="space-y-2">
                  <Label htmlFor="name">İsim</Label>
                    <Input
                      id="name"
                      value={dataSource.name}
                      onChange={(e) => setDataSource({ ...dataSource, name: e.target.value })}
                    />
                  </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Açıklama</Label>
                  <Textarea
                    id="description"
                    value={dataSource.description || ""}
                    onChange={(e) => setDataSource({ ...dataSource, description: e.target.value })}
                  />
                </div>

                <Tabs value={activeTab} onValueChange={() => { /* Type change is disabled */ }}>
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="database" disabled={activeTab !== 'database'}>
                      <Database className="h-4 w-4 mr-2" />
                      Veritabanı
                    </TabsTrigger>
                    <TabsTrigger value="csv" disabled={activeTab !== 'csv'}>
                      <FileText className="h-4 w-4 mr-2" />
                      CSV Dosyası
                    </TabsTrigger>
                    <TabsTrigger value="excel" disabled={activeTab !== 'excel'}>
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      Excel
                    </TabsTrigger>
                    <TabsTrigger value="api" disabled={activeTab !== 'api'}>
                      <Globe className="h-4 w-4 mr-2" />
                      API
                    </TabsTrigger>
                    </TabsList>

                  <TabsContent value="csv">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-center">Variables</CardTitle>
                        <CardDescription className="text-center">
                          The key-value pairs imported from the CSV file.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-end mb-4">
                          <Button onClick={handleAddVariable}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Variable
                          </Button>
                        </div>

                        <div className="max-h-96 overflow-auto border rounded-lg">
                          <Table>
                            <TableHeader className="sticky top-0 bg-white dark:bg-gray-950 z-10">
                              <TableRow>
                                <TableHead className="text-center">Name</TableHead>
                                <TableHead className="text-center">Value</TableHead>
                                <TableHead className="w-12"></TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {csvVariables.map((variable, index) => (
                                <TableRow key={index}>
                                  <TableCell className="text-center">
                          <Input
                                      value={variable.name}
                                      onChange={(e) => handleVariableChange(index, 'name', e.target.value)}
                                      className="bg-gray-50 dark:bg-gray-800"
                                    />
                                  </TableCell>
                                  <TableCell className="text-center">
                            <Input
                                      value={variable.value}
                                      onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                                      className="bg-gray-50 dark:bg-gray-800"
                            />
                                  </TableCell>
                                  <TableCell>
                          <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleRemoveVariable(index)}
                          >
                                      <X className="h-4 w-4" />
                          </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </CardContent>
                    </Card>
                    </TabsContent>
                  <TabsContent value="excel">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-center">Variables</CardTitle>
                        <CardDescription className="text-center">
                          The key-value pairs imported from the Excel file.
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-end mb-4">
                          <Button onClick={handleAddVariable}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Variable
                          </Button>
                      </div>

                        <div className="max-h-96 overflow-auto border rounded-lg">
                          <Table>
                            <TableHeader className="sticky top-0 bg-white dark:bg-gray-950 z-10">
                              <TableRow>
                                <TableHead className="text-center">Name</TableHead>
                                <TableHead className="text-center">Value</TableHead>
                                <TableHead className="w-12"></TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {csvVariables.map((variable, index) => (
                                <TableRow key={index}>
                                  <TableCell className="text-center">
                                    <Input
                                      value={variable.name}
                                      onChange={(e) => handleVariableChange(index, 'name', e.target.value)}
                                      className="bg-gray-50 dark:bg-gray-800"
                                    />
                                  </TableCell>
                                  <TableCell className="text-center">
                            <Input
                                      value={variable.value}
                                      onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                                      className="bg-gray-50 dark:bg-gray-800"
                            />
                                  </TableCell>
                                  <TableCell>
                            <Button
                                      variant="ghost"
                              size="icon"
                                      onClick={() => handleRemoveVariable(index)}
                            >
                                <X className="h-4 w-4" />
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </CardContent>
                    </Card>
                    </TabsContent>

                  <TabsContent value="database">
                    <div className="space-y-4">
                      {/* Connection Details Section */}
                      <Card>
                        <CardHeader>
                          <CardTitle>Database Connection</CardTitle>
                          <CardDescription>
                            Current database connection details. Test connection to enable query execution.
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="flex items-center gap-2 p-2 border rounded-lg bg-slate-50 dark:bg-slate-800/50">
                            <Select value={dbDetails.type} onValueChange={(v) => handleDbDetailChange('type', v)}>
                              <SelectTrigger className="w-[120px]">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="mysql">MySQL</SelectItem>
                                <SelectItem value="postgresql">PostgreSQL</SelectItem>
                                <SelectItem value="sqlite3" disabled>SQLite (Yakında)</SelectItem>
                              </SelectContent>
                            </Select>
                          <Input
                              className="flex-1 min-w-[80px]" 
                              placeholder="user" 
                              value={dbDetails.user} 
                              onChange={(e) => handleDbDetailChange('user', e.target.value)} 
                            />
                            <Input 
                              className="flex-1 min-w-[80px]" 
                              type="password" 
                              placeholder="password" 
                              value={dbDetails.password} 
                              onChange={(e) => handleDbDetailChange('password', e.target.value)} 
                            />
                            <AtSign className="text-slate-400" />
                            <Input 
                              className="w-[120px]" 
                              placeholder="host" 
                              value={dbDetails.host} 
                              onChange={(e) => handleDbDetailChange('host', e.target.value)} 
                            />
                            <span className="text-slate-400">:</span>
                            <Input 
                              className="w-[70px]" 
                              placeholder="port" 
                              value={dbDetails.port} 
                              onChange={(e) => handleDbDetailChange('port', e.target.value)} 
                            />
                            <span className="text-slate-400">/</span>
                            <Input 
                              className="flex-1 min-w-[80px]" 
                              placeholder="database" 
                              value={dbDetails.databaseName} 
                              onChange={(e) => handleDbDetailChange('databaseName', e.target.value)} 
                            />
                          </div>
                          
                          {/* Tüm modlarda bağlantı test butonu göster */}
                          <div className="flex justify-end">
                            {connectionSuccess ? (
                              <div className="flex gap-2">
                                <Button variant="default" className="bg-green-600 hover:bg-green-700 text-white pointer-events-none">
                                  <CheckCircle2 className="h-4 w-4 mr-2" />
                                  Bağlantı Başarılı
                                </Button>
                                <Button variant="outline" size="icon" onClick={handleClearConnection}>
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                          <Button
                            variant="outline"
                                disabled={isTesting || !dataSource?.connectionString}
                                onClick={handleTestConnection}
                              >
                                {isTesting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Key className="h-4 w-4 mr-2" />}
                                Bağlantıyı Test Et
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>

                      {/* Data Display based on mode */}
                      {dbConfigMode === 'read' && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-center">Name-Value Pairs</CardTitle>
                            <CardDescription className="text-center">
                              Data from table: <strong>{dataSource?.config?.table || 'Unknown'}</strong> 
                              (Name: <strong>{dataSource?.config?.nameColumn || 'Unknown'}</strong>, 
                              Value: <strong>{dataSource?.config?.valueColumn || 'Unknown'}</strong>)
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            {/* Action buttons */}
                            <div className="flex justify-between items-center mb-4">
                              <Button 
                                onClick={handleRefreshFromDb}
                                disabled={isExecuting || !dataSource?.connectionString}
                                variant="outline"
                              >
                                {isExecuting ? (
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                  <RefreshCw className="h-4 w-4 mr-2" />
                                )}
                                Refresh from DB
                              </Button>
                              
                              <Button onClick={handleAddVariable}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Variable
                              </Button>
                            </div>

                            {/* Scrollable table */}
                            <div className="max-h-96 overflow-auto border rounded-lg">
                              <Table>
                                <TableHeader className="sticky top-0 bg-white dark:bg-gray-950 z-10">
                                  <TableRow>
                                    <TableHead className="text-center">Name</TableHead>
                                    <TableHead className="text-center">Value</TableHead>
                                    <TableHead className="w-12"></TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {csvVariables.map((variable, index) => (
                                    <TableRow key={index}>
                                      <TableCell className="text-center">
                                        <Input
                                          value={variable.name}
                                          onChange={(e) => handleVariableChange(index, 'name', e.target.value)}
                                          className="bg-gray-50 dark:bg-gray-800"
                                        />
                                      </TableCell>
                                      <TableCell className="text-center">
                                        <Input
                                          value={variable.value}
                                          onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                                          className="bg-gray-50 dark:bg-gray-800"
                                        />
                                      </TableCell>
                                      <TableCell>
                                        <Button
                                          variant="ghost"
                            size="icon"
                                          onClick={() => handleRemoveVariable(index)}
                          >
                              <X className="h-4 w-4" />
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>

                            {/* Empty state */}
                            {csvVariables.length === 0 && (
                              <div className="text-center py-8 text-gray-500">
                                <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>No data found. Click "Refresh from DB" to load data from database.</p>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      )}

                      {dbConfigMode === 'visual' && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-center">Query Builder</CardTitle>
                            <CardDescription className="text-center">
                              Visual query builder with saved flow.
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="flex flex-col h-[700px] gap-4">
                              <div className="flex-grow border rounded-lg relative">
                                <ReactFlow
                                  nodes={nodes}
                                  edges={edges}
                                  onNodesChange={onNodesChange}
                                  onEdgesChange={onEdgesChange}
                                  onConnect={onConnect}
                                  nodeTypes={nodeTypes}
                                  onNodesDelete={onNodesDelete}
                                  proOptions={{ hideAttribution: true }}
                                  fitView
                                >
                                  <div className="absolute top-4 right-4 z-10 p-1 bg-white dark:bg-gray-800 border rounded-lg shadow-lg flex gap-1">
                                    <Button variant="ghost" size="icon" onClick={() => addNode('select')} title="SELECT Node">
                                       <Search className="h-5 w-5" />
                                    </Button>
                                    <Button variant="ghost" size="icon" onClick={() => addNode('insert')} title="INSERT Node">
                                       <FilePlus className="h-5 w-5" />
                                    </Button>
                                    <Button variant="ghost" size="icon" onClick={() => addNode('update')} title="UPDATE Node">
                                       <Pencil className="h-5 w-5" />
                                    </Button>
                                     <Button variant="ghost" size="icon" onClick={() => addNode('delete')} title="DELETE Node">
                                       <Trash2 className="h-5 w-5" />
                                    </Button>
                                    <div className="border-l mx-1"></div>
                                    <Button variant="ghost" size="icon" onClick={clearCanvas} title="Clear Canvas">
                                       <X className="h-5 w-5 text-red-500" />
                          </Button>
                        </div>
                                  <Controls />
                                  <Background />
                                </ReactFlow>
                              </div>
                              <Accordion type="single" collapsible className="w-full">
                                <AccordionItem value="item-1">
                                  <AccordionTrigger>Oluşturulan Sorgu Reçetesi</AccordionTrigger>
                                  <AccordionContent>
                                    <Textarea 
                                      readOnly
                                      value={queryPreview}
                                      className="font-mono h-48 bg-gray-100 dark:bg-gray-900"
                                    />
                                    <div className="mt-2 flex justify-end">
                                        <Button onClick={handleExecuteQuery} disabled={isExecuting}>
                                            {isExecuting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            Akışı Çalıştır
                                        </Button>
                      </div>
                                    {executionLogs.length > 1 && (
                                        <div className="mt-4 p-2 bg-gray-900 text-white rounded-md">
                                            <pre className="text-xs whitespace-pre-wrap">
                                                {executionLogs.join('\n')}
                                            </pre>
                                        </div>
                                    )}
                                  </AccordionContent>
                                </AccordionItem>
                              </Accordion>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {dbConfigMode === 'raw' && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-center">SQL Terminal</CardTitle>
                            <CardDescription className="text-center">
                              Raw SQL query saved in the data source.
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              <Textarea
                                value={rawSetupQuery}
                                onChange={(e) => setRawSetupQuery(e.target.value)}
                                className="min-h-[200px] font-mono"
                                placeholder="SQL query will be displayed here..."
                              />
                              <div className="flex justify-end">
                                <Button 
                                  onClick={handleTestQuery}
                                  disabled={isExecuting || !rawSetupQuery.trim()}
                                >
                                  {isExecuting ? (
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  ) : (
                                    <Code className="h-4 w-4 mr-2" />
                                  )}
                                  Test Query
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Execution logs for raw mode only (visual mode shows logs in accordion) */}
                      {dbConfigMode === 'raw' && executionLogs.length > 0 && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-center">Execution Logs</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="bg-gray-900 text-white rounded-md p-4">
                              <pre className="text-xs whitespace-pre-wrap">
                                {executionLogs.join('\n')}
                              </pre>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                    </TabsContent>

                  <TabsContent value="api">
                    <div className="space-y-6">
                      {/* API Configuration */}
                      <Card>
                        <CardHeader>
                          <CardTitle>🌐 API Configuration</CardTitle>
                          <CardDescription>
                            Configure your API endpoint details and test the connection.
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          {/* Method and URL */}
                          <div className="grid grid-cols-4 gap-4">
                            <div className="col-span-1">
                              <Label>Method</Label>
                              <Select value={apiMethod} onValueChange={(value: any) => setApiMethod(value)}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="GET">GET</SelectItem>
                                  <SelectItem value="POST">POST</SelectItem>
                                  <SelectItem value="PUT">PUT</SelectItem>
                                  <SelectItem value="DELETE">DELETE</SelectItem>
                                  <SelectItem value="PATCH">PATCH</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="col-span-3">
                              <Label>API URL</Label>
                              <Input
                                placeholder="https://api.example.com/users"
                                value={apiUrl}
                                onChange={(e) => setApiUrl(e.target.value)}
                              />
                            </div>
                          </div>

                          {/* Configuration Tabs */}
                          <Tabs value={apiConfigActiveTab} onValueChange={setApiConfigActiveTab}>
                            <TabsList className="grid w-full grid-cols-4">
                              <TabsTrigger value="params">📋 Params</TabsTrigger>
                              <TabsTrigger value="authorization">🔒 Authorization</TabsTrigger>
                              <TabsTrigger value="headers">🏷️ Headers</TabsTrigger>
                              <TabsTrigger value="body">📄 Body</TabsTrigger>
                            </TabsList>

                            <TabsContent value="params" className="space-y-4">
                              <div className="space-y-4 w-full">
                                {/* Header Row */}
                                <div className="grid grid-cols-11 gap-3 text-xs font-medium text-gray-500 px-2">
                                  <div className="col-span-4">Key</div>
                                  <div className="col-span-4">Value</div>
                                  <div className="col-span-2">Description</div>
                                  <div className="col-span-1"></div>
                                </div>
                                
                                <div className="space-y-3">
                                  {apiParams.map((param, index) => (
                                    <div key={index} className="grid grid-cols-11 gap-3 items-center">
                                      <div className="col-span-4">
                                        <Input
                                          placeholder="parameter_name"
                                          value={param.key}
                                          onChange={(e) => handleParamChange(index, 'key', e.target.value)}
                                          className="w-full"
                                        />
                                      </div>
                                      <div className="col-span-4">
                                        <Input
                                          placeholder="parameter_value"
                                          value={param.value}
                                          onChange={(e) => handleParamChange(index, 'value', e.target.value)}
                                          className="w-full"
                                        />
                                      </div>
                                      <div className="col-span-2">
                                        <Input
                                          placeholder="description"
                                          value={param.description}
                                          onChange={(e) => handleParamChange(index, 'description', e.target.value)}
                                          className="w-full text-sm"
                                        />
                                      </div>
                                      <div className="col-span-1 flex justify-center">
                                        <Button variant="ghost" size="icon" onClick={() => removeParam(index)}>
                                          <Trash2 className="h-4 w-4 text-red-500" />
                                        </Button>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                                
                                <div className="pt-2">
                                  <Button variant="outline" size="sm" onClick={addParam} className="w-full">
                                    <Plus className="h-4 w-4 mr-2" /> Add Parameter
                                  </Button>
                                </div>
                              </div>
                            </TabsContent>

                            <TabsContent value="headers" className="space-y-4">
                              <div className="space-y-3">
                                {apiHeaders.map((header, index) => (
                                  <div key={index} className="grid grid-cols-12 gap-2 items-center">
                                    <div className="col-span-5">
                                      <Input
                                        placeholder="Header Name"
                                        value={header.key}
                                        onChange={(e) => handleHeaderChange(index, 'key', e.target.value)}
                                      />
                                    </div>
                                    <div className="col-span-6">
                                      <Input
                                        placeholder="Header Value"
                                        value={header.value}
                                        onChange={(e) => handleHeaderChange(index, 'value', e.target.value)}
                                      />
                                    </div>
                                    <div className="col-span-1">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => removeHeader(index)}
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                                                              <Button variant="outline" onClick={addHeader} className="w-full">
                                <Plus className="h-4 w-4 mr-2" />
                                Add Header
                              </Button>
                            </div>
                          </TabsContent>

                          <TabsContent value="authorization" className="space-y-4">
                            <div className="space-y-4">
                              <Select value={apiAuthType} onValueChange={(value: any) => setApiAuthType(value)}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="none">No Auth</SelectItem>
                                  <SelectItem value="bearer">Bearer Token</SelectItem>
                                  <SelectItem value="basic">Basic Auth</SelectItem>
                                  <SelectItem value="apikey">API Key</SelectItem>
                                </SelectContent>
                              </Select>

                              {apiAuthType === 'bearer' && (
                                <div className="space-y-2">
                                  <Label>Bearer Token</Label>
                                  <Input
                                    placeholder="Enter your bearer token"
                                    value={apiAuth.bearer.token}
                                    onChange={(e) => setApiAuth(prev => ({
                                      ...prev,
                                      bearer: { token: e.target.value }
                                    }))}
                                  />
                                </div>
                              )}

                              {apiAuthType === 'basic' && (
                                <div className="space-y-3">
                                  <div>
                                    <Label>Username</Label>
                                    <Input
                                      placeholder="Enter username"
                                      value={apiAuth.basic.username}
                                      onChange={(e) => setApiAuth(prev => ({
                                        ...prev,
                                        basic: { ...prev.basic, username: e.target.value }
                                      }))}
                                    />
                                  </div>
                                  <div>
                                    <Label>Password</Label>
                                    <Input
                                      type="password"
                                      placeholder="Enter password"
                                      value={apiAuth.basic.password}
                                      onChange={(e) => setApiAuth(prev => ({
                                        ...prev,
                                        basic: { ...prev.basic, password: e.target.value }
                                      }))}
                                    />
                                  </div>
                                </div>
                              )}

                              {apiAuthType === 'apikey' && (
                                <div className="space-y-3">
                                  <div>
                                    <Label>Key</Label>
                                    <Input
                                      placeholder="e.g., X-API-Key"
                                      value={apiAuth.apikey.key}
                                      onChange={(e) => setApiAuth(prev => ({
                                        ...prev,
                                        apikey: { ...prev.apikey, key: e.target.value }
                                      }))}
                                    />
                                  </div>
                                  <div>
                                    <Label>Value</Label>
                                    <Input
                                      placeholder="Enter API key value"
                                      value={apiAuth.apikey.value}
                                      onChange={(e) => setApiAuth(prev => ({
                                        ...prev,
                                        apikey: { ...prev.apikey, value: e.target.value }
                                      }))}
                                    />
                                  </div>
                                  <div>
                                    <Label>Add to</Label>
                                    <Select 
                                      value={apiAuth.apikey.addTo} 
                                      onValueChange={(value: any) => setApiAuth(prev => ({
                                        ...prev,
                                        apikey: { ...prev.apikey, addTo: value }
                                      }))}
                                    >
                                      <SelectTrigger>
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="header">Header</SelectItem>
                                        <SelectItem value="query">Query Params</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              )}
                            </div>
                          </TabsContent>

                            <TabsContent value="body" className="space-y-4">
                              <div className="space-y-6 w-full">
                                <div className="space-y-3">
                                  <Label className="text-sm font-medium flex items-center gap-2">
                                    📦 Request Body Type
                                  </Label>
                                  <Select value={apiBodyType} onValueChange={(v) => setApiBodyType(v as any)}>
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder="Select body format" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="none">🚫 No Body</SelectItem>
                                      <SelectItem value="json">📋 JSON</SelectItem>
                                      <SelectItem value="formdata">📁 form-data</SelectItem>
                                      <SelectItem value="urlencoded">🔗 x-www-form-urlencoded</SelectItem>
                                      <SelectItem value="raw">📝 raw</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>

                                {/* Body Content */}
                                <div className="bg-gray-50 p-4 rounded-lg border">
                                  {apiBodyType === 'none' && (
                                    <div className="text-center py-8 text-gray-500">
                                      <div className="text-4xl mb-2">📭</div>
                                      <p>No request body</p>
                                      <p className="text-sm text-gray-400 mt-1">GET requests typically don't have a body</p>
                                    </div>
                                  )}

                                  {apiBodyType === 'json' && (
                                    <div className="space-y-4">
                                      <div>
                                        <Label className="text-sm font-medium mb-2 block">JSON Content</Label>
                                        <Textarea
                                          placeholder={`{
  "name": "John Doe",
  "email": "<EMAIL>",
  "age": 30
}`}
                                          value={apiBody}
                                          onChange={(e) => setApiBody(e.target.value)}
                                          className="font-mono text-sm min-h-[200px] w-full"
                                        />
                                      </div>
                                    </div>
                                  )}

                                  {apiBodyType === 'formdata' && (
                                    <div className="space-y-4">
                                      {/* Header Row */}
                                      <div className="grid grid-cols-11 gap-3 text-xs font-medium text-gray-500 px-2">
                                        <div className="col-span-5">Key</div>
                                        <div className="col-span-5">Value</div>
                                        <div className="col-span-1"></div>
                                      </div>
                                      
                                      <div className="space-y-3">
                                        {apiFormData.map((field, index) => (
                                          <div key={index} className="grid grid-cols-11 gap-3 items-center">
                                            <div className="col-span-5">
                                              <Input
                                                placeholder="field_name"
                                                value={field.key}
                                                onChange={(e) => handleFormDataChange(index, 'key', e.target.value)}
                                                className="w-full"
                                              />
                                            </div>
                                            <div className="col-span-5">
                                              <Input
                                                placeholder="field_value"
                                                value={field.value}
                                                onChange={(e) => handleFormDataChange(index, 'value', e.target.value)}
                                                className="w-full"
                                              />
                                            </div>
                                            <div className="col-span-1 flex justify-center">
                                              <Button variant="ghost" size="icon" onClick={() => removeFormData(index)}>
                                                <Trash2 className="h-4 w-4 text-red-500" />
                                              </Button>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                      
                                      <Button variant="outline" size="sm" onClick={addFormData} className="w-full">
                                        <Plus className="h-4 w-4 mr-2" /> Add Field
                                      </Button>
                                    </div>
                                  )}

                                  {apiBodyType === 'urlencoded' && (
                                    <div className="space-y-4">
                                      {/* Header Row */}
                                      <div className="grid grid-cols-11 gap-3 text-xs font-medium text-gray-500 px-2">
                                        <div className="col-span-5">Key</div>
                                        <div className="col-span-5">Value</div>
                                        <div className="col-span-1"></div>
                                      </div>
                                      
                                      <div className="space-y-3">
                                        {apiUrlEncoded.map((field, index) => (
                                          <div key={index} className="grid grid-cols-11 gap-3 items-center">
                                            <div className="col-span-5">
                                              <Input
                                                placeholder="parameter_name"
                                                value={field.key}
                                                onChange={(e) => handleUrlEncodedChange(index, 'key', e.target.value)}
                                                className="w-full"
                                              />
                                            </div>
                                            <div className="col-span-5">
                                              <Input
                                                placeholder="parameter_value"
                                                value={field.value}
                                                onChange={(e) => handleUrlEncodedChange(index, 'value', e.target.value)}
                                                className="w-full"
                                              />
                                            </div>
                                            <div className="col-span-1 flex justify-center">
                                              <Button variant="ghost" size="icon" onClick={() => removeUrlEncoded(index)}>
                                                <Trash2 className="h-4 w-4 text-red-500" />
                                              </Button>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                      
                                      <Button variant="outline" size="sm" onClick={addUrlEncoded} className="w-full">
                                        <Plus className="h-4 w-4 mr-2" /> Add Field
                                      </Button>
                                    </div>
                                  )}

                                  {apiBodyType === 'raw' && (
                                    <div className="space-y-4">
                                      <div>
                                        <Label className="text-sm font-medium mb-2 block">Raw Content</Label>
                                        <Textarea
                                          placeholder="Enter raw content here..."
                                          value={apiRawBody}
                                          onChange={(e) => setApiRawBody(e.target.value)}
                                          className="font-mono text-sm min-h-[200px] w-full"
                                        />
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </TabsContent>
                          </Tabs>

                          {/* Test API Button */}
                          <div className="flex justify-end pt-4 border-t">
                            <Button 
                              onClick={handleTestApi} 
                              disabled={isTestingApi || !apiUrl}
                              className="bg-indigo-600 hover:bg-indigo-700"
                            >
                              {isTestingApi ? (
                                <>
                                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                  Testing API...
                                </>
                              ) : (
                                <>
                                  <Globe className="h-4 w-4 mr-2" />
                                  Test API
                                </>
                              )}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>

                      {/* API Response & Variable Extraction */}
                      {apiTestResult && (
                        <Card>
                          <CardContent className="pt-6 space-y-6">
                            {/* Interactive Response Explorer */}
                            {apiTestResult && (
                              <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                  <Label className="text-sm font-medium">API Response Explorer</Label>
                                  <div className="flex items-center gap-2">
                                    {selectedJsonPath && (
                                      <Button 
                                        size="sm" 
                                        onClick={addSelectedPathAsRule}
                                        className="bg-indigo-600 hover:bg-indigo-700"
                                      >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add "{selectedJsonPath}" as Rule
                                      </Button>
                                    )}
                                  </div>
                                </div>
                                <div className="p-4 bg-gray-50 border rounded-md max-h-60 overflow-auto">
                                  <div className="text-sm">
                                    <div className="text-gray-600 mb-2 text-xs">
                                      💡 Click on any value to select its path for parsing
                                    </div>
                                    {selectedJsonPath && (
                                      <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                                        <strong>Selected Path:</strong> <code className="bg-blue-100 px-1 rounded">{selectedJsonPath}</code>
                                      </div>
                                    )}
                                    {renderJsonValue(apiTestResult.data, '')}
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Variable Extraction Rules */}
                            <div className="space-y-4">
                              <div className="flex items-center justify-between">
                                <div className="space-y-1">
                                  <Label className="text-sm font-medium">Variable Extraction Rules</Label>
                                  <p className="text-xs text-gray-500">Define which values to extract as test variables</p>
                                </div>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={addParsingRule}
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add Variable
                                </Button>
                              </div>

                              {apiParsingRules.length === 0 ? (
                                <div className="text-center py-8 text-gray-500">
                                  <Wand2 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                                  <p className="text-sm">No extraction rules defined yet.</p>
                                  <p className="text-xs">Add rules to extract variables from the API response.</p>
                                </div>
                              ) : (
                                <div className="space-y-3">
                                  {apiParsingRules.map((rule) => (
                                    <div key={rule.id} className="p-4 border rounded-lg space-y-3 bg-gray-50 relative">
                                      {/* Delete button in top right */}
                                      <Button 
                                        variant="ghost" 
                                        size="icon" 
                                        onClick={() => removeParsingRule(rule.id)}
                                        className="absolute top-2 right-2 h-6 w-6 text-gray-400 hover:text-red-500 hover:bg-red-50"
                                      >
                                        <Trash2 className="h-3 w-3" />
                                      </Button>
                                      
                                      {/* Single Row: Variable Name, JSON Path */}
                                      <div className="grid grid-cols-2 gap-3 items-end pr-8">
                                        <div className="col-span-1">
                                          <Label className="text-xs text-gray-600 mb-1 block">Variable Name (for @tags)</Label>
                                          <div className="relative">
                                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm pointer-events-none">@</span>
                                            <Input
                                              placeholder="user_id"
                                              value={rule.variableName}
                                              onChange={(e) => updateParsingRule(rule.id, 'variableName', e.target.value)}
                                              className="text-sm pl-7"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-span-1">
                                          <Label className="text-xs text-gray-600 mb-1 block">JSON Path</Label>
                                          <Input
                                            placeholder="e.g., data.user.name or data.products[0].id"
                                            value={rule.jsonPath}
                                            onChange={(e) => updateParsingRule(rule.id, 'jsonPath', e.target.value)}
                                            className="text-sm"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}

                              {/* Generate Variables Button */}
                              {apiParsingRules.length > 0 && (
                                <div className="flex justify-center pt-2">
                                  <Button 
                                    onClick={extractVariablesFromResponse}
                                    disabled={isGeneratingVariables}
                                    className="bg-indigo-600 hover:bg-indigo-700"
                                  >
                                    {isGeneratingVariables ? (
                                      <>
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        Generating Variables...
                                      </>
                                    ) : (
                                      <>
                                        <Wand2 className="h-4 w-4 mr-2" />
                                        Generate Variables ({apiParsingRules.length} rules)
                                      </>
                                    )}
                                  </Button>
                                </div>
                              )}
                            </div>

                            {/* Generated Test Variables */}
                            {extractedVariables.length > 0 && (
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <Label className="text-sm font-medium">Generated Test Variables</Label>
                                  <div className="text-xs text-gray-500 bg-blue-50 px-2 py-1 rounded">
                                    💡 Use these variables in scenarios with @variable_name
                                  </div>
                                </div>
                                <div className="border rounded-lg">
                                  <div className="grid grid-cols-4 gap-4 p-3 bg-gray-50 border-b">
                                    <div className="font-medium text-sm text-gray-600 text-center">Variable Tag</div>
                                    <div className="font-medium text-sm text-gray-600 text-center">Last Value</div>
                                    <div className="font-medium text-sm text-gray-600 text-center">Current Value</div>
                                    <div className="font-medium text-sm text-gray-600 text-center">Actions</div>
                                  </div>
                                  <div className="divide-y">
                                    {extractedVariables.map((variable, index) => (
                                      <div key={variable.name} className="grid grid-cols-4 gap-4 p-3 items-center">
                                        <div className="flex items-center justify-center">
                                          <code className="text-sm bg-gray-100 px-2 py-1 rounded font-mono text-indigo-600">
                                            @{variable.name}
                                          </code>
                                        </div>
                                        <div className="text-sm text-gray-700 break-all font-mono flex items-center justify-center">
                                          <span className="bg-gray-100 px-2 py-1 rounded text-gray-800">
                                            {variableLastValues[variable.name] || variable.value}
                                          </span>
                                        </div>
                                        <div className="flex items-center justify-center">
                                          {temporaryValues[variable.name] ? (
                                            <span className="text-sm bg-green-100 px-2 py-1 rounded text-green-800 font-mono">
                                              {temporaryValues[variable.name]}
                                            </span>
                                          ) : (
                                            <Button
                                              size="sm"
                                              variant="outline"
                                              onClick={() => testSingleVariable(variable.name)}
                                              disabled={testingVariable === variable.name}
                                              className="text-xs"
                                            >
                                              {testingVariable === variable.name ? (
                                                <>
                                                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                                  Testing...
                                                </>
                                              ) : (
                                                <>
                                                  <RefreshCw className="h-3 w-3 mr-1" />
                                                  Test
                                                </>
                                              )}
                                            </Button>
                                          )}
                                        </div>
                                        <div className="flex justify-center">
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            className="h-8 w-8 p-0 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-colors"
                                            onClick={() => {
                                              setExtractedVariables(prev => 
                                                prev.filter(v => v.name !== variable.name)
                                              );
                                              toast({
                                                title: "Variable Deleted",
                                                description: `@${variable.name} removed from variables list`,
                                              });
                                            }}
                                          >
                                            <Trash2 className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                <div className="text-xs text-gray-600 bg-green-50 border border-green-200 rounded p-3">
                                  <div className="font-medium mb-2">📝 Usage in Scenarios:</div>
                                  <div className="space-y-1.5">
                                    <div>• <code className="bg-green-100 px-1 rounded">@{extractedVariables[0]?.name || 'variable_name'}</code> will be replaced with actual values during test execution</div>
                                    <div>• Variables are requested fresh from the API each time a test runs</div>
                                    <div>• Transformations (uppercase, lowercase, trim) are applied automatically</div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  </TabsContent>
                  </Tabs>
              </CardContent>

              <CardFooter className="flex justify-between border-t pt-6">
                 <Button variant="secondary" onClick={() => router.push("/test-data")}>
                  İptal
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving || saveSuccess || !dataSource.name}
                  className={saveSuccess ? "bg-green-600 hover:bg-green-700" : ""}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Kaydediliyor...
                    </>
                  ) : saveSuccess ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Kaydedildi
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Değişiklikleri Kaydet
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>
      <Toaster />
    </div>
  )
} 