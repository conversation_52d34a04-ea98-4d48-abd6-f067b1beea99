/**
 * Android Environment Settings Types
 * 
 * This file defines the standardized interfaces for Android environment settings
 * to be used across the entire codebase (frontend and backend).
 * 
 * IMPORTANT: This is the single source of truth for Android test request data structure.
 * All components should use these interfaces when working with Android environment settings.
 */

/**
 * Device information for Android tests
 * Contains all necessary information about a device to be used in a test
 */
export interface AndroidDevice {
  /** Unique identifier for the device */
  id: string;
  
  /** Human-readable name of the device */
  name: string;
  
  /** Device serial number (required for DevicePark API) */
  serial?: string;
  
  /** Android OS version (e.g., "12", "13") */
  osVersion: string;
  
  /** Device provider (e.g., 'sauceLabs', 'testinium') */
  provider: string;
  
  /** Device type classification */
  deviceType?: 'tablet' | 'phone' | 'other';
  
  /** Device manufacturer (string or array of strings) */
  manufacturer?: string | string[];
  
  /** Android API level (e.g., "API 31") */
  apiLevel?: string;
  
  /** Screen resolution (e.g., "1080x2400") */
  screenResolution?: string;
  
  /** Screen size in inches (e.g., "6.1") */
  screenSize?: string;
  
  /** RAM amount (e.g., "6 GB") */
  ram?: string;
  
  /** Model number (e.g., "SM-G991B") */
  modelNumber?: string;
}

/**
 * App information for Android tests
 * Contains all necessary information about an app to be used in a test
 */
export interface AndroidApp {
  /** Unique identifier for the app */
  id: string;

  /** Human-readable name of the app */
  name: string;

  /** App version (e.g., "1.2.3") */
  version?: string;

  /** File key for Testinium apps (DevicePark storage key) */
  fileKey?: string;
}

/**
 * SauceLabs configuration for Android tests
 * Contains all necessary information for connecting to SauceLabs
 */
export interface SauceLabsConfig {
  /** SauceLabs username */
  username: string;

  /** SauceLabs access key */
  accessKey: string;

  /** SauceLabs region */
  region?: 'us-west-1' | 'eu-central-1' | 'apac-southeast-1';

  /** Selected devices for the test */
  selectedDevices: AndroidDevice[];

  /** Selected app for the test */
  selectedApp?: AndroidApp;
}

/**
 * Testinium configuration for Android tests
 * Contains all necessary information for connecting to Testinium via DevicePark SDK
 */
export interface TestiniumConfig {
  /** Testinium API URL */
  apiUrl: string;

  /** Testinium client ID */
  clientId: string;

  /** Testinium client secret */
  clientSecret: string;

  /** Testinium issuer URI */
  issuerUri: string;

  /** Selected devices for the test */
  selectedDevices: AndroidDevice[];

  /** Selected app for the test */
  selectedApp?: AndroidApp;
}

/**
 * Test distribution strategy for Android tests
 * Defines how tests are distributed across multiple devices
 */
export interface TestDistributionConfig {
  /** Distribution strategy */
  strategy: 'all-on-all' | 'distribute';
}

/**
 * AI model configuration for Android tests
 */
export interface AIModelConfig {
  /** OpenAI base URL */
  OPENAI_BASE_URL: string;
  
  /** OpenAI API key */
  OPENAI_API_KEY: string;
  
  /** Model name to use for midscene processing */
  MIDSCENE_MODEL_NAME: string;
  
  /** Additional configuration parameters */
  [key: string]: string;
}

/**
 * Complete Android environment settings
 * This is the standardized interface for Android environment settings
 */
export interface AndroidEnvironmentSettings {
  /** Platform identifier (always 'android' for Android tests) */
  platform: 'android';

  /** AI model identifier */
  aiModel?: string;

  /** AI model name */
  aiModelName?: string;

  /** Selected device provider for mutually exclusive selection */
  deviceProvider?: 'sauceLabs' | 'testinium';

  /** SauceLabs configuration */
  sauceLabs: SauceLabsConfig;

  /** Testinium configuration */
  testinium: TestiniumConfig;

  /** Test distribution configuration */
  testDistribution?: TestDistributionConfig;

  /** AI model configuration */
  aiModelConfig?: AIModelConfig;
}
