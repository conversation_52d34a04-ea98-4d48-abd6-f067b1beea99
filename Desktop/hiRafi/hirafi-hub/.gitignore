# Dependency directories
node_modules/
dist/

# Log files
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage directory used by tools like istanbul
coverage

# Cache directories
.npm
.eslintcache
.tsbuildinfo

# Visual Studio Code files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# OS specific files
.DS_Store
Thumbs.db 
obfuscated/
