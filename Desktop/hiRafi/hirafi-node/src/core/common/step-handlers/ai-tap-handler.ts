/**
 * Common AI Tap Handler
 * Handles executing AI tap steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Tap Handler
 * Handles executing AI tap steps using the AI agent
 */
export class CommonAiTapHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiTap';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiTapHandler';
  }

  /**
   * Execute an AI tap step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiTap, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const tapTarget = step.prompt || step.value || step.name || step.description;

    if (!tapTarget) {
      return { success: false, error: 'Missing tap target for aiTap step. Please provide a prompt field.' };
    }

    try {
      const options = step.deepThink ? { deepThink: true } : {};

      logger.debug(`CommonAiTapHandler: Executing tap on "${tapTarget}" with options: ${JSON.stringify(options)}`);

      const result = await agent.aiTap(tapTarget || '', options);
      logger.debug(`aiTap result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error;

      const logEntry = formatAIResultForLog('aiTap', tapTarget, result);
      logEntry.success = isSuccess;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Tap failed: ${tapTarget}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Tap Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiTap',
        description: tapTarget,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiTapHandler: Tap failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 