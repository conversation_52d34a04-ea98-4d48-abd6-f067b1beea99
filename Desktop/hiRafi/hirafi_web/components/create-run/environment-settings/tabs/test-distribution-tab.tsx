"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Layers, ArrowRight, Smartphone, Share2 } from "lucide-react"
import { useRunForm } from "../../run-context"

// Define test distribution strategy type
type TestDistributionStrategy = 'all-on-all' | 'distribute';

// Define the device type for TypeScript
interface Device {
  id: string;
  name: string;
  osVersion: string;
  provider: string;
  deviceType?: 'tablet' | 'phone' | 'other';
  manufacturer?: string | string[];
}

// Define props for the TestDistributionTab component
interface TestDistributionTabProps {
  environmentSettings: any;
  updateEnvironmentSettings: (settings: any) => void;
}

export function TestDistributionTab({
  environmentSettings,
  updateEnvironmentSettings
}: TestDistributionTabProps) {
  // Get the current strategy from environment settings or default to 'all-on-all'
  const [strategy, setStrategy] = useState<TestDistributionStrategy>(
    environmentSettings.testDistribution?.strategy || 'all-on-all'
  );

  // Get selected scenarios from run context
  const { selectedScenarios } = useRunForm();

  // Use the actual scenario count from the run context
  const scenarioCount = selectedScenarios.length;

  // Get selected devices from environment settings
  const selectedDevices = environmentSettings.sauceLabs?.selectedDevices || [];

  // Ensure we have at least one device for visualization
  const effectiveDeviceCount = Math.max(1, selectedDevices.length);

  // Update environment settings when strategy changes
  const handleStrategyChange = (newStrategy: TestDistributionStrategy) => {
    setStrategy(newStrategy);

    // Ensure testDistribution is properly set in environment settings
    updateEnvironmentSettings({
      ...environmentSettings,
      testDistribution: {
        ...environmentSettings.testDistribution,
        strategy: newStrategy
      }
    });

    // Log the strategy change for debugging
    console.log(`Test distribution strategy changed to: ${newStrategy}`);
    console.log(`Updated environment settings:`, JSON.stringify({
      ...environmentSettings,
      testDistribution: {
        ...environmentSettings.testDistribution,
        strategy: newStrategy
      }
    }, null, 2));
  };

  // Determine if we need to show a warning about missing scenarios or devices
  const showWarning = scenarioCount === 0 || effectiveDeviceCount === 0;
  const warningMessage = scenarioCount === 0
    ? "Please select at least one scenario to visualize test distribution"
    : "Please select at least one device to visualize test distribution";

  return (
    <div className="space-y-4">
      {/* Test Distribution Strategy Section */}
      <div className="space-y-4">
        <div className="flex items-center">
          <div className="bg-indigo-100 dark:bg-indigo-900/50 p-1.5 rounded-full mr-2">
            <Share2 className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
          </div>
          <div>
            <h3 className="text-base font-medium text-indigo-700 dark:text-indigo-300">
              Test Distribution Strategy
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Choose how to distribute test scenarios across devices
            </p>
          </div>
        </div>

        {/* Warning message when no scenarios or devices are selected */}
        {showWarning && (
          <div className="bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-800 rounded-md p-3 text-sm text-amber-700 dark:text-amber-300">
            {warningMessage}
          </div>
        )}

        {/* Strategy selector */}
        <div className="p-4 border rounded-md bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/30 dark:to-purple-950/30 border-indigo-200 dark:border-indigo-800 shadow-sm">
          <div className="flex flex-col space-y-6">
            <div className="flex justify-center items-center space-x-4 py-2">
              <span className={`text-sm ${strategy === 'all-on-all' ? 'font-medium text-indigo-700 dark:text-indigo-300' : 'text-gray-500 dark:text-gray-400'}`}>
                Run All Scenarios on All Devices
              </span>

              <div
                className="relative w-14 h-7 bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer"
                onClick={() => handleStrategyChange(strategy === 'all-on-all' ? 'distribute' : 'all-on-all')}
              >
                <motion.div
                  className="absolute top-1 w-5 h-5 rounded-full bg-indigo-600 dark:bg-indigo-400"
                  animate={{
                    x: strategy === 'all-on-all' ? 2 : 30
                  }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                />
              </div>

              <span className={`text-sm ${strategy === 'distribute' ? 'font-medium text-indigo-700 dark:text-indigo-300' : 'text-gray-500 dark:text-gray-400'}`}>
                Distribute Scenarios Across Devices
              </span>
            </div>

            {/* Visualization */}
            <div className="h-[200px] relative bg-white dark:bg-gray-800/50 rounded-md p-4 overflow-hidden">
              {strategy === 'all-on-all' ? (
                <AllOnAllVisualization
                  deviceCount={effectiveDeviceCount}
                  scenarioCount={scenarioCount}
                />
              ) : (
                <DistributeVisualization
                  deviceCount={effectiveDeviceCount}
                  scenarioCount={scenarioCount}
                />
              )}
            </div>

            {/* Summary text */}
            <div className="text-center text-sm">
              {strategy === 'all-on-all' ? (
                <p className="text-gray-600 dark:text-gray-300">
                  <span className="font-medium text-indigo-700 dark:text-indigo-300">
                    {scenarioCount} {scenarioCount === 1 ? 'scenario' : 'scenarios'}
                  </span> ×
                  <span className="font-medium text-green-700 dark:text-green-300">
                    {effectiveDeviceCount} {effectiveDeviceCount === 1 ? 'device' : 'devices'}
                  </span> =
                  <span className="font-medium text-purple-700 dark:text-purple-300">
                    {scenarioCount * effectiveDeviceCount} test {scenarioCount * effectiveDeviceCount === 1 ? 'run' : 'runs'}
                  </span>
                </p>
              ) : (
                <p className="text-gray-600 dark:text-gray-300">
                  <span className="font-medium text-indigo-700 dark:text-indigo-300">
                    {scenarioCount} {scenarioCount === 1 ? 'scenario' : 'scenarios'}
                  </span> distributed across
                  <span className="font-medium text-green-700 dark:text-green-300">
                    {effectiveDeviceCount} {effectiveDeviceCount === 1 ? 'device' : 'devices'}
                  </span>
                  {effectiveDeviceCount > 1 && (
                    <span> (approx. {Math.ceil(scenarioCount / effectiveDeviceCount)} per device)</span>
                  )}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Strategy explanation */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* All on All Strategy */}
          <div className={`p-4 border rounded-md bg-white dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 shadow-sm ${strategy === 'all-on-all' ? 'ring-2 ring-indigo-200 dark:ring-indigo-800' : ''}`}>
            <h4 className="text-sm font-medium flex items-center text-indigo-700 dark:text-indigo-300 mb-2">
              <div className="bg-indigo-100 dark:bg-indigo-900/50 p-1 rounded-full mr-2">
                <Layers className="h-3.5 w-3.5 text-indigo-600 dark:text-indigo-400" />
              </div>
              Run All Scenarios on All Devices
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Each test scenario will run on every selected device. This ensures maximum coverage but increases total test execution time.
            </p>
            <div className="mt-2 text-xs text-gray-500 dark:text-gray-500">
              <strong>Best for:</strong> Thorough testing across different device configurations, ensuring consistent behavior on all devices.
            </div>
            {strategy === 'all-on-all' && scenarioCount > 0 && effectiveDeviceCount > 0 && (
              <div className="mt-2 text-xs bg-indigo-50 dark:bg-indigo-900/30 p-2 rounded">
                <strong>Total test runs:</strong> {scenarioCount * effectiveDeviceCount} ({scenarioCount} × {effectiveDeviceCount})
              </div>
            )}
          </div>

          {/* Distribute Strategy */}
          <div className={`p-4 border rounded-md bg-white dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 shadow-sm ${strategy === 'distribute' ? 'ring-2 ring-green-200 dark:ring-green-800' : ''}`}>
            <h4 className="text-sm font-medium flex items-center text-green-700 dark:text-green-300 mb-2">
              <div className="bg-green-100 dark:bg-green-900/50 p-1 rounded-full mr-2">
                <Share2 className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
              </div>
              Distribute Scenarios Across Devices
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Test scenarios will be distributed evenly across all selected devices. This reduces total execution time but each scenario runs on only one device.
            </p>
            <div className="mt-2 text-xs text-gray-500 dark:text-gray-500">
              <strong>Best for:</strong> Faster test execution when you need to run many scenarios and have confidence in device consistency.
            </div>
            {strategy === 'distribute' && scenarioCount > 0 && effectiveDeviceCount > 0 && (
              <div className="mt-2 text-xs bg-green-50 dark:bg-green-900/30 p-2 rounded">
                <strong>Total test runs:</strong> {scenarioCount} (distributed across {effectiveDeviceCount} devices)
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Visualization for "Run All Scenarios on All Devices" strategy
function AllOnAllVisualization({
  deviceCount,
  scenarioCount
}: {
  deviceCount: number;
  scenarioCount: number;
}) {
  // Handle edge cases for better visualization
  const displayScenarioCount = scenarioCount || 1;
  const displayDeviceCount = deviceCount || 1;

  // Calculate total test runs
  const totalRuns = displayScenarioCount * displayDeviceCount;

  // Limit the number of scenarios and devices to display for clarity
  const maxScenariosToShow = Math.min(4, displayScenarioCount);
  const maxDevicesToShow = Math.min(3, displayDeviceCount);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center">
      <div className="flex items-center justify-center w-full">
        {/* Scenarios box */}
        <motion.div
          className="bg-indigo-100 dark:bg-indigo-900/50 p-3 rounded-md flex items-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Layers className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-2" />
          <span className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
            {displayScenarioCount} {displayScenarioCount === 1 ? 'Scenario' : 'Scenarios'}
          </span>
        </motion.div>

        {/* Arrow pointing to devices */}
        <motion.div
          className="mx-4 text-gray-400"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <ArrowRight className="h-6 w-6" />
        </motion.div>

        {/* Devices box */}
        <motion.div
          className="bg-green-100 dark:bg-green-900/50 p-3 rounded-md flex items-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Smartphone className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
          <span className="text-sm font-medium text-green-700 dark:text-green-300">
            {displayDeviceCount} {displayDeviceCount === 1 ? 'Device' : 'Devices'}
          </span>
        </motion.div>
      </div>

      {/* Animated connections */}
      <div className="mt-6 relative w-full">
        <svg className="w-full h-[120px]" viewBox="0 0 300 120">
          {/* Scenario indicators on the left */}
          {Array.from({ length: maxScenariosToShow }).map((_, i) => {
            const y = 20 + i * 25;
            return (
              <motion.g key={`scenario-${i}`}>
                <motion.circle
                  cx="50"
                  cy={y}
                  r="6"
                  fill="#818cf8"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.1 + (i * 0.1) }}
                />
                <motion.text
                  x="65"
                  y={y + 4}
                  fontSize="10"
                  fill="currentColor"
                  className="text-indigo-700 dark:text-indigo-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 + (i * 0.1) }}
                >
                  Scenario {i + 1}
                </motion.text>
              </motion.g>
            );
          })}

          {/* Device indicators on the right */}
          {Array.from({ length: maxDevicesToShow }).map((_, i) => {
            const y = 20 + i * 40;
            return (
              <motion.g key={`device-${i}`}>
                <motion.circle
                  cx="250"
                  cy={y}
                  r="6"
                  fill="#10b981"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.1 + (i * 0.1) }}
                />
                <motion.text
                  x="235"
                  y={y + 4}
                  textAnchor="end"
                  fontSize="10"
                  fill="currentColor"
                  className="text-green-700 dark:text-green-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 + (i * 0.1) }}
                >
                  Device {i + 1}
                </motion.text>
              </motion.g>
            );
          })}

          {/* Draw connections from scenarios to devices */}
          {Array.from({ length: maxScenariosToShow }).map((_, i) => (
            Array.from({ length: maxDevicesToShow }).map((_, j) => {
              const startY = 20 + i * 25;
              const endY = 20 + j * 40;

              return (
                <motion.path
                  key={`path-${i}-${j}`}
                  d={`M 50 ${startY} C 120 ${startY}, 180 ${endY}, 250 ${endY}`}
                  stroke="url(#gradient)"
                  strokeWidth="1.5"
                  fill="none"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 0.7 }}
                  transition={{ duration: 0.8, delay: 0.3 + (i * 0.05) + (j * 0.05) }}
                />
              );
            })
          ))}

          {/* Show ellipsis if there are more scenarios than we're displaying */}
          {displayScenarioCount > maxScenariosToShow && (
            <motion.g>
              <motion.text
                x="50"
                y={20 + maxScenariosToShow * 25 + 15}
                textAnchor="middle"
                fontSize="12"
                fill="currentColor"
                className="text-gray-500 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                ⋮
              </motion.text>
              <motion.text
                x="65"
                y={20 + maxScenariosToShow * 25 + 15}
                fontSize="9"
                fill="currentColor"
                className="text-gray-500 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                +{displayScenarioCount - maxScenariosToShow} more
              </motion.text>
            </motion.g>
          )}

          {/* Show ellipsis if there are more devices than we're displaying */}
          {displayDeviceCount > maxDevicesToShow && (
            <motion.g>
              <motion.text
                x="250"
                y={20 + maxDevicesToShow * 40 + 15}
                textAnchor="middle"
                fontSize="12"
                fill="currentColor"
                className="text-gray-500 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                ⋮
              </motion.text>
              <motion.text
                x="235"
                y={20 + maxDevicesToShow * 40 + 15}
                textAnchor="end"
                fontSize="9"
                fill="currentColor"
                className="text-gray-500 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.5 }}
              >
                +{displayDeviceCount - maxDevicesToShow} more
              </motion.text>
            </motion.g>
          )}

          {/* Total runs indicator */}
          <motion.g>
            <motion.circle
              cx="150"
              cy="60"
              r="18"
              fill="rgba(139, 92, 246, 0.15)"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 1.2 }}
            />
            <motion.text
              x="150"
              y="64"
              textAnchor="middle"
              fontSize="12"
              fontWeight="bold"
              fill="currentColor"
              className="text-purple-700 dark:text-purple-300"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.4 }}
            >
              {totalRuns}
            </motion.text>
          </motion.g>

          {/* Gradient definition */}
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#818cf8" />
              <stop offset="100%" stopColor="#34d399" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
}

// Visualization for "Distribute Scenarios Across Devices" strategy
function DistributeVisualization({
  deviceCount,
  scenarioCount
}: {
  deviceCount: number;
  scenarioCount: number;
}) {
  // Handle edge cases for better visualization
  const displayScenarioCount = scenarioCount || 1;
  const displayDeviceCount = deviceCount || 1;

  // Calculate scenarios per device with more accurate distribution
  const scenariosPerDevice = displayScenarioCount / displayDeviceCount;

  // Create an array of actual distribution (how many scenarios per device)
  const distribution = Array(displayDeviceCount).fill(0).map((_, i) => {
    if (i < displayDeviceCount - 1) {
      return Math.ceil(scenariosPerDevice);
    } else {
      // Last device gets the remainder to ensure total equals scenarioCount
      const remainingScenarios = displayScenarioCount - (Math.ceil(scenariosPerDevice) * (displayDeviceCount - 1));
      return Math.max(1, remainingScenarios);
    }
  });

  // Limit the number of devices to display for clarity
  const maxDevicesToShow = Math.min(4, displayDeviceCount);

  // Calculate total scenarios assigned to each device for display
  const scenariosAssigned = distribution.slice(0, maxDevicesToShow);

  // Create scenario groups for visualization
  const scenarioGroups: number[][] = [];
  let scenarioIndex = 0;

  // Distribute scenarios into groups for each device
  for (let i = 0; i < maxDevicesToShow; i++) {
    const deviceScenarios: number[] = [];
    for (let j = 0; j < scenariosAssigned[i]; j++) {
      if (scenarioIndex < displayScenarioCount) {
        deviceScenarios.push(scenarioIndex);
        scenarioIndex++;
      }
    }
    scenarioGroups.push(deviceScenarios);
  }

  return (
    <div className="w-full h-full flex flex-col items-center justify-center">
      <div className="flex items-center justify-center w-full">
        {/* Scenarios box */}
        <motion.div
          className="bg-indigo-100 dark:bg-indigo-900/50 p-3 rounded-md flex items-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Layers className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-2" />
          <span className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
            {displayScenarioCount} {displayScenarioCount === 1 ? 'Scenario' : 'Scenarios'}
          </span>
        </motion.div>
      </div>

      {/* Distribution visualization */}
      <div className="mt-6 flex justify-center items-center w-full">
        <svg className="w-full h-[120px]" viewBox="0 0 300 120">
          {/* Central scenarios container */}
          <motion.rect
            x="125"
            y="10"
            width="50"
            height="25"
            rx="4"
            fill="#818cf8"
            fillOpacity="0.2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />

          <motion.text
            x="150"
            y="26"
            textAnchor="middle"
            fontSize="10"
            fontWeight="bold"
            fill="currentColor"
            className="text-indigo-700 dark:text-indigo-300"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            Scenarios
          </motion.text>

          {/* Device columns */}
          {Array.from({ length: maxDevicesToShow }).map((_, i) => {
            // Calculate position for better distribution visualization
            const totalWidth = 240;
            const deviceSpacing = totalWidth / maxDevicesToShow;
            const x = 30 + (i * deviceSpacing) + (deviceSpacing / 2);
            const y = 80;

            // Get the actual number of scenarios for this device
            const deviceScenarios = scenariosAssigned[i] || 0;

            return (
              <motion.g key={`device-${i}`}>
                {/* Distribution line */}
                <motion.path
                  d={`M 150 35 C 150 ${45 + i * 5}, ${x} ${55}, ${x} ${y - 25}`}
                  stroke="url(#gradient)"
                  strokeWidth="1.5"
                  fill="none"
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{ pathLength: 1, opacity: 0.7 }}
                  transition={{ duration: 0.8, delay: 0.3 + (i * 0.15) }}
                />

                {/* Device container */}
                <motion.rect
                  x={x - 20}
                  y={y - 20}
                  width="40"
                  height="30"
                  rx="4"
                  fill="#10b981"
                  fillOpacity="0.2"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 + (i * 0.1) }}
                />

                {/* Device label */}
                <motion.text
                  x={x}
                  y={y - 5}
                  textAnchor="middle"
                  fontSize="9"
                  fill="currentColor"
                  className="text-green-700 dark:text-green-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 + (i * 0.1) }}
                >
                  Device {i + 1}
                </motion.text>

                {/* Scenario count badge */}
                <motion.circle
                  cx={x}
                  cy={y - 30}
                  r="8"
                  fill="#10b981"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.7 + (i * 0.1) }}
                />

                <motion.text
                  x={x}
                  y={y - 27}
                  textAnchor="middle"
                  fontSize="9"
                  fontWeight="bold"
                  fill="white"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.8 + (i * 0.1) }}
                >
                  {deviceScenarios}
                </motion.text>
              </motion.g>
            );
          })}

          {/* Show ellipsis if there are more devices than we're displaying */}
          {displayDeviceCount > maxDevicesToShow && (
            <motion.g>
              <motion.text
                x="270"
                y="80"
                textAnchor="middle"
                fontSize="12"
                fill="currentColor"
                className="text-gray-500 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.9 }}
              >
                ⋯
              </motion.text>
              <motion.text
                x="270"
                y="95"
                textAnchor="middle"
                fontSize="9"
                fill="currentColor"
                className="text-gray-500 dark:text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.9 }}
              >
                +{displayDeviceCount - maxDevicesToShow} more
              </motion.text>
            </motion.g>
          )}

          {/* Gradient definition */}
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#818cf8" />
              <stop offset="100%" stopColor="#10b981" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
}
