/**
 * Node Identity Service
 * 
 * Handles node ID generation, persistence, and retrieval with dependency injection.
 * Replaces the singleton NodeIdentityManager with a proper service implementation.
 */

import { v4 as uuidv4 } from 'uuid';
import { config } from '../../../../config/index.js';
import { RedisConnector } from '../../../connectors/redis/redis-connector.js';
import { INodeIdentityService } from '../../interfaces/node-identity-service.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

export class NodeIdentityService implements INodeIdentityService {
  private id: string | null = null;
  private clientId: string;
  private persistentNodeId: string | null = null;
  private persistentClientId: string | null = null;
  private redisClient: RedisConnector | null = null;
  private logger: ILoggerService;

  // AI-themed name components
  private static readonly AI_PREFIXES = [
    'ai', 'neural', 'quantum', 'cyber', 'smart', 'auto', 'robo', 'intel',
    'cogni', 'synth', 'logic', 'brain', 'mind', 'think', 'learn'
  ];

  private static readonly AI_SUFFIXES = [
    'bot', 'core', 'mind', 'brain', 'node', 'unit', 'agent', 'worker',
    'runner', 'engine', 'processor', 'analyzer', 'executor', 'tester'
  ];

  private static readonly RANDOM_WORDS = [
    'alpha', 'beta', 'gamma', 'delta', 'omega', 'prime', 'nova', 'stellar',
    'cosmic', 'fusion', 'matrix', 'nexus', 'vertex', 'apex', 'zenith'
  ];

  constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('NodeIdentityService');
    this.clientId = '';
  }

  /**
   * Initialize the node identity service
   */
  public async initialize(): Promise<void> {
    this.logger.serviceInit('NodeIdentityService', 'Initializing...');
    // Initialize Redis client if enabled
    if (config.redisEnabled) {
      this.redisClient = new RedisConnector(undefined, this.logger);
      try {
        await this.redisClient.initialize();
      } catch (error) {
        this.logger.warn(`NodeIdentityService: Failed to initialize Redis client: ${error}`);
        this.redisClient = null;
      }
    }

    // Load or generate persistent client ID first
    await this.loadOrGenerateClientId();

    this.logger.serviceInit('NodeIdentityService', `Initializing with client ID: ${this.clientId}`);

    // Try to load existing node ID first, only generate new if none exists
    await this.loadPersistedNodeId();

    if (!this.persistentNodeId) {
      // Generate new node ID only if none exists
      this.id = this.generateNewNodeId();
      this.persistentNodeId = this.id;
      await this.persistNodeId(this.id);
      this.logger.serviceInit('NodeIdentityService', `Generated fresh random node ID: ${this.id}`);
    } else {
      // Use existing persisted node ID
      this.id = this.persistentNodeId;
      this.logger.serviceInit('NodeIdentityService', `Using existing node ID: ${this.id}`);
    }
  }

  /**
   * Get the current node ID
   */
  getNodeId(): string | null {
    return this.id;
  }

  /**
   * Get the client ID
   */
  getClientId(): string {
    return this.clientId;
  }

  /**
   * Get the persistent node ID
   */
  getPersistentNodeId(): string | null {
    return this.persistentNodeId;
  }

  /**
   * Update the node ID (typically from hub registration)
   */
  async updateNodeId(nodeId: string): Promise<void> {
    if (nodeId && nodeId !== this.id) {
      this.logger.info(`NodeIdentityService: Updating node ID from ${this.id} to ${nodeId}`);
      this.id = nodeId;
      this.persistentNodeId = nodeId;

      // Persist the updated node ID
      await this.persistNodeId(nodeId);
    }
  }

  /**
   * Generate a new random node ID
   */
  generateNewNodeId(): string {
    // Always generate a random UUID-based node ID
    const randomId = uuidv4().replace(/-/g, '').substring(0, 12);
    return `node-${randomId}`;
  }

  /**
   * Generate an AI-themed random name
   */
  generateAIThemedName(): string {
    const prefix = NodeIdentityService.AI_PREFIXES[Math.floor(Math.random() * NodeIdentityService.AI_PREFIXES.length)];
    const suffix = NodeIdentityService.AI_SUFFIXES[Math.floor(Math.random() * NodeIdentityService.AI_SUFFIXES.length)];
    const word = NodeIdentityService.RANDOM_WORDS[Math.floor(Math.random() * NodeIdentityService.RANDOM_WORDS.length)];
    const number = Math.floor(Math.random() * 999) + 1;

    return `${prefix}-${word}-${suffix}-${number}`;
  }

  /**
   * Load or generate persistent client ID
   */
  private async loadOrGenerateClientId(): Promise<void> {
    try {
      // Check if we already have a persistent client ID
      if (process.env.PERSISTED_CLIENT_ID) {
        this.persistentClientId = process.env.PERSISTED_CLIENT_ID;
        this.clientId = this.persistentClientId;
        this.logger.info(`NodeIdentityService: Loaded client ID from environment: ${this.clientId}`);
        return;
      }

      // Try loading from Redis
      if (config.redisEnabled && this.redisClient && this.redisClient.isConnected()) {
        try {
          // Note: This would need to be implemented in RedisConnector as getNodeInfo method
          // For now, we'll skip Redis loading and generate new client ID
          this.logger.debug('NodeIdentityService: Redis client available but getNodeInfo method not implemented yet');
        } catch (err) {
          this.logger.warn(`NodeIdentityService: Error loading client ID from Redis: ${err}`);
        }
      }

      // Generate new client ID if none found
      this.clientId = this.generateClientId();
      this.persistentClientId = this.clientId;
      await this.persistClientId(this.clientId);
      this.logger.info(`NodeIdentityService: Generated new client ID: ${this.clientId}`);
    } catch (error) {
      this.logger.warn(`NodeIdentityService: Error loading/generating client ID: ${error}`);
      // Fallback to generating a new one
      this.clientId = this.generateClientId();
    }
  }

  /**
   * Generate a client ID based on UUID - STABLE VERSION FOR DOCKER
   */
  generateClientId(): string {
    // Always use UUID-based identifier for Docker compatibility
    // This ensures consistent client IDs across reconnections
    const uuid = uuidv4().replace(/-/g, '');
    return `client-${uuid.substring(0, 16)}`;
  }

  /**
   * Persist client ID to storage
   */
  private async persistClientId(clientId: string): Promise<void> {
    if (!clientId) return;

    try {
      // Save to environment variable for current process
      process.env.PERSISTED_CLIENT_ID = clientId;

      // Save to Redis if available
      if (config.redisEnabled && this.redisClient && this.redisClient.isConnected()) {
        try {
          // Note: This would need to be implemented in RedisConnector as setNodeInfo method
          // For now, we'll skip Redis persistence
          this.logger.debug(`NodeIdentityService: Redis client available but setNodeInfo method not implemented yet`);
        } catch (err) {
          this.logger.warn(`NodeIdentityService: Error persisting client ID to Redis: ${err}`);
        }
      }
    } catch (error) {
      this.logger.warn(`NodeIdentityService: Error persisting client ID: ${error}`);
    }
  }

  /**
   * Load persisted node ID from storage
   */
  async loadPersistedNodeId(): Promise<void> {
    try {
      // Check environment variable first
      if (process.env.PERSISTED_NODE_ID) {
        this.persistentNodeId = process.env.PERSISTED_NODE_ID;
        this.logger.info(`NodeIdentityService: Loaded node ID from environment: ${this.persistentNodeId}`);
        return;
      }

      // Try loading from Redis
      if (config.redisEnabled && this.redisClient && this.redisClient.isConnected()) {
        try {
          // Note: This would need to be implemented in RedisConnector as getNodeInfo method
          // For now, we'll skip Redis loading
          this.logger.debug('NodeIdentityService: Redis client available but getNodeInfo method not implemented yet');
        } catch (err) {
          this.logger.warn(`NodeIdentityService: Error loading node ID from Redis: ${err}`);
        }
      } else {
        this.logger.info('NodeIdentityService: No persisted node ID found');
      }
    } catch (error) {
      this.logger.warn(`NodeIdentityService: Error loading persisted node ID: ${error}`);
    }
  }

  /**
   * Persist node ID to storage
   */
  async persistNodeId(nodeId: string): Promise<void> {
    if (!nodeId) return;

    try {
      // Save to environment variable for current process
      process.env.PERSISTED_NODE_ID = nodeId;

      // Save to Redis if available
      if (config.redisEnabled && this.redisClient && this.redisClient.isConnected()) {
        try {
          // Note: This would need to be implemented in RedisConnector as setNodeInfo method
          // For now, we'll skip Redis persistence
          this.logger.debug(`NodeIdentityService: Redis client available but setNodeInfo method not implemented yet`);
        } catch (err) {
          this.logger.warn(`NodeIdentityService: Error persisting node ID to Redis: ${err}`);
        }
      }
    } catch (error) {
      this.logger.warn(`NodeIdentityService: Error persisting node ID: ${error}`);
    }
  }

  /**
   * Check if node has a valid ID
   */
  hasValidNodeId(): boolean {
    return this.id !== null && this.id.length > 0;
  }
}
