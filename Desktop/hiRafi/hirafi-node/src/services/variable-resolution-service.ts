/**
 * Variable Resolution Service
 * 
 * Resolves @Variable patterns in test steps using database queries, API requests, or manual values.
 * Supports three variable types:
 * - Database: @Variable/lookupValue (executes SQL queries)
 * - API: @Variable (executes HTTP requests)  
 * - Manual: @Variable (retrieves static values)
 */

import { logger } from '../utils/logger.js';
import { config } from '../config/index.js';

interface VariableResolutionConfig {
  hubUrl: string;
  nodeSecretKey: string;
}

interface TestRequestContext {
  scenarioData?: any;
  teamId?: string | null;
  companyId?: string | null;
  environmentSettings?: any;
}

interface DatabaseVariable {
  sourceType: 'database';
  name: string;
  sourceId: string;
  connectionString: string;
  tableName: string;
  nameColumn: string;
  valueColumn: string;
  mode: 'visual' | 'raw';
  flow?: any[];
  setupQuery?: string; // For raw mode queries
}

interface ApiVariable {
  sourceType: 'api';
  name: string;
  sourceId: string;
  method: string;
  url: string;
  headers: Array<{ key: string; value: string }>;
  params?: Array<{ key: string; value: string; enabled: boolean }>;
  jsonPath: string;
}

interface ManualVariable {
  sourceType?: 'manual' | 'csv' | 'excel' | undefined;
  name: string;
  environmentValues: Record<string, any>;
}

export class VariableResolutionService {
  private config: VariableResolutionConfig;

  constructor() {
    this.config = {
      hubUrl: config.hubUrl || process.env.HUB_URL || 'http://localhost:5000',
      nodeSecretKey: config.nodeSecretKey || process.env.NODE_SECRET_KEY || 'test-node-secret-key-for-secure-communication'
    };

    logger.debug('VariableResolutionService: Initialized with hub URL:', this.config.hubUrl);
  }

  /**
   * Main method to resolve all variables in a step text
   */
  async resolveVariables(stepText: string, testRequest: TestRequestContext): Promise<string> {
    // Quick exit if no @ symbols
    if (!stepText?.includes('@')) {
      return stepText;
    }

    // Parse @Variable patterns: @VariableName or @VariableName/lookupValue
    const variablePattern = /@(\w+)(?:\/([^\s]+))?/g;
    const matches = [...stepText.matchAll(variablePattern)];
    
    if (matches.length === 0) {
      return stepText;
    }

    logger.debug(`VariableResolutionService: Found ${matches.length} variable(s) to resolve in step`);

    let resolvedText = stepText;

    // Resolve each variable
    for (const match of matches) {
      const [fullMatch, varName, lookupValue] = match;
      
      try {
        logger.debug(`VariableResolutionService: Resolving variable: ${varName}${lookupValue ? `/${lookupValue}` : ''}`);

        // Find variable in test data
        const variable = this.findVariable(varName, testRequest);
        
        if (!variable) {
          logger.warn(`VariableResolutionService: Variable '${varName}' not found in test data`);
          continue;
        }

        let resolvedValue: string;

        // Resolve by type
        switch (variable.sourceType) {
          case 'database':
            resolvedValue = await this.resolveDatabaseVariable(variable as DatabaseVariable, lookupValue);
            break;
          case 'api': 
            resolvedValue = await this.resolveApiVariable(variable as ApiVariable);
            break;
          default:
            // Manual/static variable
            resolvedValue = await this.resolveManualVariable(variable as ManualVariable, testRequest);
            break;
        }

        // Replace in text
        resolvedText = resolvedText.replace(fullMatch, resolvedValue);
        
        logger.info(`VariableResolutionService: Resolved '${fullMatch}' → '${resolvedValue}'`);

      } catch (error: any) {
        logger.error(`VariableResolutionService: Error resolving variable '${varName}': ${error.message}`);
        // Keep original text on error - don't break test execution
      }
    }

    return resolvedText;
  }

  /**
   * Find variable in test request data
   */
  private findVariable(varName: string, testRequest: TestRequestContext): DatabaseVariable | ApiVariable | ManualVariable | null {
    const testData = testRequest.scenarioData;
    
    // ENHANCED DEBUG: Log everything we have
    logger.info(`VariableResolutionService: DEBUG - Looking for variable '${varName}'`);
    logger.info(`VariableResolutionService: DEBUG - testRequest.scenarioData exists: ${!!testData}`);
    logger.info(`VariableResolutionService: DEBUG - testData.metadata exists: ${!!testData?.metadata}`);
    logger.info(`VariableResolutionService: DEBUG - testData.metadata.variables exists: ${!!testData?.metadata?.variables}`);
    
    if (testData?.metadata?.variables) {
      logger.info(`VariableResolutionService: DEBUG - Available variables count: ${testData.metadata.variables.length}`);
      logger.info(`VariableResolutionService: DEBUG - Available variables: ${JSON.stringify(testData.metadata.variables.map((v: any) => ({ name: v.name, sourceType: v.sourceType || 'manual' })), null, 2)}`);
      
      // Separate by type for clarity
      const manualVars = testData.metadata.variables.filter((v: any) => !v.sourceType || v.sourceType === 'manual');
      const dbVars = testData.metadata.variables.filter((v: any) => v.sourceType === 'database');
      const apiVars = testData.metadata.variables.filter((v: any) => v.sourceType === 'api');
      
      logger.info(`VariableResolutionService: DEBUG - Variable breakdown: manual: ${manualVars.length}, database: ${dbVars.length}, api: ${apiVars.length}`);
      
      // 🔍 DATABASE VARIABLE DETAILED ANALYSIS
      if (dbVars.length > 0) {
        logger.info(`VariableResolutionService: DEBUG - Database variables detailed:`);
        dbVars.forEach((v: any) => {
          logger.info(`  Variable '${v.name}':`, {
            sourceType: v.sourceType,
            sourceId: v.sourceId,
            // Field mapping fields
            tableName: v.tableName,
            nameColumn: v.nameColumn, 
            valueColumn: v.valueColumn,
            // Connection fields
            connectionString: v.connectionString ? '***EXISTS***' : 'UNDEFINED',
            flow: v.flow ? `${v.flow.length} steps` : 'UNDEFINED',
            mode: v.mode
          });
        });
      }
    }
    
    if (!testData?.metadata?.variables) {
      logger.debug(`VariableResolutionService: No variables found in test data metadata`);
      return null;
    }

    const variable = testData.metadata.variables.find((v: any) => v.name === varName);
    
    if (!variable) {
      logger.debug(`VariableResolutionService: Variable '${varName}' not found in ${testData.metadata.variables.length} available variables`);
      return null;
    }

    logger.info(`VariableResolutionService: Found variable '${varName}' with sourceType: ${variable.sourceType || 'manual'}, sourceId: ${variable.sourceId || 'none'}`);
    
    logger.debug(`VariableResolutionService: Found variable '${varName}' with sourceType: ${variable.sourceType || 'manual'}`);
    return variable;
  }

  /**
   * Get datasource config from Hub
   */
  private async getDataSourceConfig(sourceId: string): Promise<any> {
    try {
      const response = await fetch(`${this.config.hubUrl}/api/test-data/data-sources/${sourceId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.config.nodeSecretKey
        }
      });

      if (!response.ok) {
        throw new Error(`Hub API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`Get datasource failed: ${result.message}`);
      }

      return result.data?.dataSource || result.data;
    } catch (error: any) {
      logger.error(`VariableResolutionService: Error getting datasource config: ${error.message}`);
      throw error;
    }
  }

  /**
   * Resolve database variable by getting fresh datasource config from Hub
   */
  private async resolveDatabaseVariable(variable: DatabaseVariable, lookupValue: string): Promise<string> {
    if (!lookupValue) {
      throw new Error(`Database variable '${variable.name}' requires a lookup value (e.g., @Variable/value)`);
    }

    logger.info(`VariableResolutionService: Resolving database variable '${variable.name}' with sourceId: ${variable.sourceId}, lookup: ${lookupValue}`);

    if (!variable.sourceId) {
      throw new Error(`Database variable '${variable.name}' missing sourceId - cannot get config from Hub`);
    }

    // 🚀 DIREKT HUB'DAN DATASOURCE CONFIG'İNİ ÇEK
    let connectionString: string;
    let configFlow: any[];
    let configSetupQuery: string | undefined;

    try {
      const response = await fetch(`${this.config.hubUrl}/api/test-data/data-sources/${variable.sourceId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.config.nodeSecretKey
        }
      });

      if (!response.ok) {
        throw new Error(`Hub API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(`Get datasource failed: ${result.message || 'No data'}`);
      }

      const dataSource = result.data.dataSource || result.data;
      connectionString = dataSource.connectionString;
      configFlow = dataSource.config?.flow || [];
      configSetupQuery = dataSource.config?.setupQuery;

      logger.info(`VariableResolutionService: Hub'dan DataSource config çekildi - connectionString: ${!!connectionString}, flow: ${!!configFlow?.length}, setupQuery: ${!!configSetupQuery}`);

      if (!connectionString) {
        throw new Error(`DataSource'ta connectionString yok`);
      }
    } catch (error: any) {
      logger.error(`VariableResolutionService: Hub'dan DataSource çekilemedi: ${error.message}`);
      throw new Error(`Database variable '${variable.name}' - Hub'dan config çekilemedi: ${error.message}`);
    }

    let requestPayload: any;
    let flow: any[];

    // 🚀 STEP 2A: Use existing flow if available (visual mode)
    if (configFlow && Array.isArray(configFlow) && configFlow.length > 0) {
      // 🔧 CRITICAL FIX: Use variable metadata fields to override flow configuration
      flow = configFlow.map(node => {
        if (node.data && node.data.whereConditions) {
          // 🎯 OVERRIDE: Use variable metadata fields for correct field mapping
          const correctedConditions = [{
            col: variable.nameColumn,     // Use correct nameColumn from variable metadata
            operator: "=",
            val: lookupValue
          }];
          
          // 🎯 OVERRIDE: Use correct valueColumn if specified  
          const correctedSelectedColumns = variable.valueColumn ? [variable.valueColumn] : node.data.selectedColumns;
          
          logger.info(`VariableResolutionService: CORRECTING flow - nameColumn: ${variable.nameColumn}, valueColumn: ${variable.valueColumn}`);
          logger.info(`VariableResolutionService: Original flow conditions: ${JSON.stringify(node.data.whereConditions)}`);
          logger.info(`VariableResolutionService: Corrected conditions: ${JSON.stringify(correctedConditions)}`);
          
          return {
            ...node,
            data: {
              ...node.data,
              whereConditions: correctedConditions,
              selectedColumns: correctedSelectedColumns
            }
          };
        }
        return node;
      });
      
      // 🔧 CRITICAL FIX: Convert Node flow structure to Hub expected format
      const hubFlow = flow.map(node => {
        if (node.data) {
          // Extract data fields to root level for Hub compatibility
          return {
            type: node.type,
            tableName: node.data.table,              // data.table → tableName
            selectedColumns: node.data.selectedColumns,
            whereConditions: node.data.whereConditions,
            orderBy: node.data.orderBy,
            orderDirection: node.data.orderDirection,
            limitValue: node.data.limitValue
          };
        }
        return node;
      });
      
      logger.info(`VariableResolutionService: CONVERTED flow structure for Hub: ${JSON.stringify(hubFlow, null, 2)}`);
      
      requestPayload = {
        connectionString: connectionString,
        flow: hubFlow
      };
      
      logger.info(`VariableResolutionService: Using CORRECTED VISUAL mode flow with lookup: ${lookupValue}`);
    }
    // 🚀 STEP 2B: Use setupQuery if available (raw mode)  
    else if (configSetupQuery) {
      // Replace placeholder with lookup value
      const query = configSetupQuery.replace(/\$\{value\}/g, lookupValue).replace(/\$\{lookup\}/g, lookupValue);
      
      flow = [{
        type: "raw",
        query: query
      }];
      
      requestPayload = {
        connectionString: connectionString,
        flow
      };
      
      logger.info(`VariableResolutionService: Using RAW mode query: ${query}`);
    }
    // 🚀 STEP 2C: Fallback to manual flow creation
    else {
      flow = [
        {
          type: "select",
          tableName: variable.tableName,     // Use tableName not table for Hub compatibility
          selectedColumns: [variable.valueColumn],
          whereConditions: [
            {
              col: variable.nameColumn,
              operator: "=",
              val: lookupValue
            }
          ]
        }
      ];
      
      requestPayload = {
        connectionString: connectionString,
        flow
      };
        
        logger.info(`VariableResolutionService: Using FALLBACK mode with tableName: ${variable.tableName}`);
    }

    logger.info(`VariableResolutionService: DEBUG - Database request payload: ${JSON.stringify(requestPayload, null, 2)}`);

    try {
      const response = await fetch(`${this.config.hubUrl}/api/test-data/data-sources/db/execute-flow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.config.nodeSecretKey
        },
        body: JSON.stringify(requestPayload)
      });

      if (!response.ok) {
        throw new Error(`Hub API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      // 🔍 DEBUG: Log full Hub response
      logger.info(`VariableResolutionService: DEBUG - Hub DB response: ${JSON.stringify(result, null, 2)}`);
      
      if (!result.success) {
        throw new Error(`Database query failed: ${result.message}`);
      }

      // 🔍 ADIM 1: DETAILED HUB RESPONSE ANALYSIS
      logger.info(`VariableResolutionService: ADIM 1 - Analyzing Hub response structure...`);
      logger.info(`VariableResolutionService: ADIM 1 - result.results: ${JSON.stringify(result.results)}`);
      logger.info(`VariableResolutionService: ADIM 1 - result.results length: ${result.results?.length || 0}`);
      
      if (result.results && result.results[0]) {
        const firstResultWrapper = result.results[0];
        logger.info(`VariableResolutionService: ADIM 1 - firstResultWrapper keys: ${Object.keys(firstResultWrapper)}`);
        logger.info(`VariableResolutionService: ADIM 1 - firstResultWrapper.data exists: ${!!firstResultWrapper.data}`);
        logger.info(`VariableResolutionService: ADIM 1 - firstResultWrapper.data length: ${firstResultWrapper.data?.length || 0}`);
        
        if (firstResultWrapper.data && firstResultWrapper.data[0]) {
          logger.info(`VariableResolutionService: ADIM 1 - actual data record: ${JSON.stringify(firstResultWrapper.data[0])}`);
          logger.info(`VariableResolutionService: ADIM 1 - data record keys: ${Object.keys(firstResultWrapper.data[0])}`);
        }
      }
      
      // 🚀 ADIM 2: CORRECT RESPONSE PARSING FIX
      logger.info(`VariableResolutionService: ADIM 2 - Fixing response parsing...`);
      
      // OLD WAY: result.results[0] (wrapper object)
      const oldFirstResult = result.results?.[0];
      logger.info(`VariableResolutionService: ADIM 2 - OLD firstResult: ${JSON.stringify(oldFirstResult)}`);
      
      // NEW WAY: result.results[0].data[0] (actual data record)
      const firstResult = result.results?.[0]?.data?.[0];
      logger.info(`VariableResolutionService: ADIM 2 - NEW firstResult: ${JSON.stringify(firstResult)}`);
      
      let value;
      
      if (firstResult && typeof firstResult === 'object') {
        // Find value in result object (try valueColumn first, then any column)
        value = firstResult[variable.valueColumn];
        logger.info(`VariableResolutionService: ADIM 2 - trying variable.valueColumn '${variable.valueColumn}': ${value}`);
        
        if (value === undefined || value === null) {
          value = Object.values(firstResult)[0];
          logger.info(`VariableResolutionService: ADIM 2 - fallback to first column value: ${value}`);
        }
        
        logger.info(`VariableResolutionService: ADIM 2 - final extracted value: ${value}`);
      } else {
        logger.info(`VariableResolutionService: ADIM 2 - firstResult is not a valid object`);
      }
      
      if (value === undefined || value === null) {
        throw new Error(`No record found with lookup value: "${lookupValue}"`);
      }

      logger.debug(`VariableResolutionService: Database query returned: ${value}`);
      return String(value);

    } catch (error: any) {
      logger.error(`VariableResolutionService: Database variable resolution failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Resolve API variable by getting fresh datasource config from Hub
   */
  private async resolveApiVariable(variable: ApiVariable): Promise<string> {
    logger.info(`VariableResolutionService: Resolving API variable '${variable.name}' with sourceId: ${variable.sourceId}`);

    if (!variable.sourceId) {
      throw new Error(`API variable '${variable.name}' missing sourceId - cannot get config from Hub`);
    }

    // 🚀 DIREKT HUB'DAN DATASOURCE CONFIG'İNİ ÇEK
    let method: string;
    let url: string; 
    let headers: any[];

    try {
      const response = await fetch(`${this.config.hubUrl}/api/test-data/data-sources/${variable.sourceId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.config.nodeSecretKey
        }
      });

      if (!response.ok) {
        throw new Error(`Hub API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success || !result.data) {
        throw new Error(`Get datasource failed: ${result.message || 'No data'}`);
      }

      const dataSource = result.data.dataSource || result.data;
      method = dataSource.config?.method;
      url = dataSource.config?.url;
      headers = dataSource.config?.headers || [];

      logger.info(`VariableResolutionService: Hub'dan DataSource config çekildi - method: ${method}, url: ${url}`);

      if (!method || !url) {
        throw new Error(`DataSource'ta method veya url yok - method: ${method}, url: ${url}`);
      }
    } catch (error: any) {
      logger.error(`VariableResolutionService: Hub'dan DataSource çekilemedi: ${error.message}`);
      throw new Error(`API variable '${variable.name}' - Hub'dan config çekilemedi: ${error.message}`);
    }

    const requestPayload = {
      method: method,
      url: url,
      headers: headers,
      params: []
    };
    
    logger.info(`VariableResolutionService: Using Hub API config - method: ${method}, url: ${url}`);
    logger.info(`VariableResolutionService: DEBUG - API request payload: ${JSON.stringify(requestPayload, null, 2)}`);

    try {
      const response = await fetch(`${this.config.hubUrl}/api/test-data/data-sources/api/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.config.nodeSecretKey
        },
        body: JSON.stringify(requestPayload)
      });

      if (!response.ok) {
        throw new Error(`Hub API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`API request failed: ${result.message}`);
      }

      // 🚀 DEBUG: Log API response structure
      logger.info(`VariableResolutionService: DEBUG - Full API response: ${JSON.stringify(result, null, 2)}`);
      logger.info(`VariableResolutionService: DEBUG - Variable jsonPath: ${variable.jsonPath}`);
      
      // Use original JSONPath from Hub (no auto-fix needed)
      const jsonPath = variable.jsonPath || 'data';
      logger.info(`VariableResolutionService: DEBUG - Using original jsonPath: ${jsonPath}`);
      
      // Extract value using simple JSONPath implementation from FULL result
      const value = this.extractJsonPath(result, jsonPath);
      
      logger.info(`VariableResolutionService: DEBUG - JSONPath extraction result: ${value}`);
      
      if (value === undefined) {
        throw new Error(`JSONPath '${jsonPath}' returned no results from response`);
      }
      logger.debug(`VariableResolutionService: API request returned: ${value}`);
      return String(value);

    } catch (error: any) {
      logger.error(`VariableResolutionService: API variable resolution failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Resolve manual variable using Hub's manual variable endpoint or direct access
   */
  private async resolveManualVariable(variable: ManualVariable, testRequest: TestRequestContext): Promise<string> {
    logger.debug(`VariableResolutionService: Resolving manual variable: ${variable.name}`);

    // Get current environment
    const currentEnvironmentId = this.getCurrentEnvironmentId(testRequest);
    
    // Method 1: Direct access from scenarioData (if available)
    if (variable.environmentValues && variable.environmentValues[currentEnvironmentId] !== undefined) {
      const envValue = variable.environmentValues[currentEnvironmentId];
      
      // Handle nested value structure
      let value;
      if (typeof envValue === 'object' && envValue !== null && envValue.value !== undefined) {
        value = envValue.value;
      } else {
        value = envValue;
      }
      
      logger.debug(`VariableResolutionService: Manual variable resolved from cache: ${value}`);
      return String(value);
    }

    // Method 2: Hub API call for fresh data (if we have dataset info)
    const testData = testRequest.scenarioData;
    if (testData?.datasetId) {
      try {
        const response = await fetch(`${this.config.hubUrl}/api/test-data/node/resolve-manual-variable`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Node-Key': this.config.nodeSecretKey
          },
          body: JSON.stringify({
            variableName: variable.name,
            datasetId: testData.datasetId,
            environmentId: currentEnvironmentId,
            teamId: testRequest.teamId,
            companyId: testRequest.companyId
          })
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            logger.debug(`VariableResolutionService: Manual variable resolved from Hub: ${result.value}`);
            return String(result.value);
          }
        }
      } catch (error: any) {
        logger.warn(`VariableResolutionService: Hub API call failed, falling back to default: ${error.message}`);
      }
    }

    // Method 3: Fallback to any available environment value
    if (variable.environmentValues) {
      const availableEnvs = Object.keys(variable.environmentValues);
      if (availableEnvs.length > 0) {
        const fallbackEnv = availableEnvs[0];
        const envValue = variable.environmentValues[fallbackEnv];
        
        let value;
        if (typeof envValue === 'object' && envValue !== null && envValue.value !== undefined) {
          value = envValue.value;
        } else {
          value = envValue;
        }
        
        logger.warn(`VariableResolutionService: Using fallback environment '${fallbackEnv}' for variable '${variable.name}': ${value}`);
        return String(value);
      }
    }

    throw new Error(`No value found for manual variable '${variable.name}' in any environment`);
  }

  /**
   * Simple JSONPath implementation for basic path extraction
   * Supports paths like: $.data.user.name, $.items[0].value, etc.
   */
  private extractJsonPath(data: any, path: string): any {
    if (!path || !data) {
      return undefined;
    }

    // Remove leading $. if present
    const cleanPath = path.startsWith('$.') ? path.substring(2) : path.startsWith('$') ? path.substring(1) : path;
    
    if (!cleanPath) {
      return data;
    }

    const segments = cleanPath.split('.');
    let current = data;

    for (const segment of segments) {
      if (current === null || current === undefined) {
        return undefined;
      }

      // Handle array indices like items[0]
      if (segment.includes('[') && segment.includes(']')) {
        const arrayMatch = segment.match(/^([^[]+)\[(\d+)\]$/);
        if (arrayMatch) {
          const [, propertyName, indexStr] = arrayMatch;
          const index = parseInt(indexStr, 10);
          
          if (propertyName) {
            current = current[propertyName];
          }
          
          if (Array.isArray(current) && index >= 0 && index < current.length) {
            current = current[index];
          } else {
            return undefined;
          }
        } else {
          return undefined;
        }
      } else {
        // Simple property access
        current = current[segment];
      }
    }

    return current;
  }

  /**
   * Get current environment ID from test request
   */
  private getCurrentEnvironmentId(testRequest: TestRequestContext): string {
    // Try to get from environment settings
    if (testRequest.environmentSettings?.activeEnvironmentId) {
      return testRequest.environmentSettings.activeEnvironmentId;
    }

    // Try to get from scenario data
    if (testRequest.scenarioData?.activeEnvironmentId) {
      return testRequest.scenarioData.activeEnvironmentId;
    }

    // Fallback to 'production' or first available environment
    const testData = testRequest.scenarioData;
    if (testData?.metadata?.environments && Array.isArray(testData.metadata.environments)) {
      const activeEnv = testData.metadata.environments.find((env: any) => env.isActive);
      if (activeEnv) {
        return activeEnv.id;
      }
      
      // Return first environment if no active one
      if (testData.metadata.environments.length > 0) {
        return testData.metadata.environments[0].id;
      }
    }

    logger.warn('VariableResolutionService: No environment ID found, using default: production');
    return 'production';
  }
} 