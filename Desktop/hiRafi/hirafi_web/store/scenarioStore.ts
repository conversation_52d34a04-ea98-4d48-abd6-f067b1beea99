/**
 * Scenario Store
 *
 * Zustand store for managing scenario UI state
 * Separates UI state from server state (which will be managed by SWR)
 */

import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

import { storeManager } from "@/lib/utils/store-management";
import { companyAwareStorage } from "@/lib/utils/company-aware-storage";

// User info type from localStorage
interface UserInfo {
  teamId?: string;
  companyId?: string;
  id?: string;
  name?: string;
  email?: string;
  role?: string;
}

// Scenario store state interface
interface ScenarioState {
  // Filters
  searchQuery: string;
  statusFilter: string | null;
  tagFilter: string | null;
  testTypeFilter: string | null;
  startDate: string | null;
  endDate: string | null;
  testManagementStatus: string | null;

  // UI State
  selectedScenarios: string[];
  selectedFolder: string | null;
  openFolders: Set<string>;
  sidebarCollapsed: boolean;

  // Actions for filters
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status: string | null) => void;
  setTagFilter: (tag: string | null) => void;
  setTestTypeFilter: (testType: string | null) => void;
  setStartDate: (date: string | null) => void;
  setEndDate: (date: string | null) => void;
  setTestManagementStatus: (status: string | null) => void;
  clearFilters: () => void;

  // Actions for UI state
  setSelectedScenarios: (ids: string[]) => void;
  addSelectedScenario: (id: string) => void;
  removeSelectedScenario: (id: string) => void;
  toggleSelectedScenario: (id: string) => void;
  clearSelectedScenarios: () => void;
  setSelectedFolder: (id: string | null) => void;
  toggleOpenFolder: (id: string) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;

  // User Info (from localStorage)
  userInfo: UserInfo;
  setUserInfo: (info: UserInfo) => void;

  // Clear persisted state for store manager
  clearPersistedState: () => void;
}

// Create the store with persist middleware for filters
export const useScenarioStore = create<ScenarioState>()(
  devtools(
    persist(
      (set) => ({
        // Initial state
        searchQuery: "",
        statusFilter: null,
        tagFilter: null,
        testTypeFilter: null,
        startDate: null,
        endDate: null,
        testManagementStatus: null,
        selectedScenarios: [],
        selectedFolder: null,
        openFolders: new Set<string>(),
        sidebarCollapsed: false,
        userInfo: { teamId: undefined, companyId: undefined },

        // Actions for filters
        setSearchQuery: (query) => set({ searchQuery: query }),
        setStatusFilter: (status) => {
          set({ statusFilter: status });
        },
        setTagFilter: (tag) => set({ tagFilter: tag }),
        setTestTypeFilter: (testType) => set({ testTypeFilter: testType }),
        setStartDate: (date) => set({ startDate: date }),
        setEndDate: (date) => set({ endDate: date }),
        setTestManagementStatus: (status) =>
          set({ testManagementStatus: status }),
        clearFilters: () => {
          // Reset all filter-related state to initial values
          set({
            searchQuery: "",
            statusFilter: null,
            tagFilter: null,
            testTypeFilter: null,
            startDate: null,
            endDate: null,
            testManagementStatus: null,
            selectedFolder: null,
          });
        },

        // Actions for UI state
        setSelectedScenarios: (ids) => set({ selectedScenarios: ids }),
        addSelectedScenario: (id) =>
          set((state) => ({
            selectedScenarios: [...state.selectedScenarios, id],
          })),
        removeSelectedScenario: (id) =>
          set((state) => ({
            selectedScenarios: state.selectedScenarios.filter(
              (scenarioId) => scenarioId !== id,
            ),
          })),
        toggleSelectedScenario: (id) =>
          set((state) => {
            if (state.selectedScenarios.includes(id)) {
              return {
                selectedScenarios: state.selectedScenarios.filter(
                  (scenarioId) => scenarioId !== id,
                ),
              };
            } else {
              return {
                selectedScenarios: [...state.selectedScenarios, id],
              };
            }
          }),
        clearSelectedScenarios: () => set({ selectedScenarios: [] }),
        setSelectedFolder: (id) =>
          set((state) => {
            // Clear selected scenarios when changing folders
            return {
              selectedFolder: id,
              selectedScenarios: [],
            };
          }),
        toggleOpenFolder: (id) =>
          set((state) => {
            const newOpenFolders = new Set(state.openFolders);
            if (newOpenFolders.has(id)) {
              newOpenFolders.delete(id);
            } else {
              newOpenFolders.add(id);
            }
            return { openFolders: newOpenFolders };
          }),
        setSidebarCollapsed: (collapsed) =>
          set({ sidebarCollapsed: collapsed }),

        // User info
        setUserInfo: (info) => set({ userInfo: info }),

        // Clear persisted state for store manager
        clearPersistedState: () => {
          set({
            searchQuery: "",
            statusFilter: null,
            tagFilter: null,
            testTypeFilter: null,
            startDate: null,
            endDate: null,
            testManagementStatus: null,
            selectedScenarios: [],
            selectedFolder: null,
            openFolders: new Set<string>(),
            sidebarCollapsed: false,
            userInfo: { teamId: undefined, companyId: undefined },
          });
        },
      }),
      {
        name: "scenario-store",
        storage: companyAwareStorage,
        // Only persist filters and UI state, not selected items or user info
        partialize: (state) => ({
          searchQuery: state.searchQuery,
          statusFilter: state.statusFilter,
          tagFilter: state.tagFilter,
          testTypeFilter: state.testTypeFilter,
          startDate: state.startDate,
          endDate: state.endDate,
          sidebarCollapsed: state.sidebarCollapsed,
          // Convert Set to Array for serialization
          openFolders: Array.from(state.openFolders),
        }),
        // Custom merge function to handle Set serialization
        merge: (persistedState: any, currentState: ScenarioState) => ({
          ...currentState,
          ...persistedState,
          // Convert Array back to Set
          openFolders: new Set(persistedState.openFolders || []),
        }),
      },
    ),
    { name: "scenario-store" },
  ),
);

// Register this store with the store manager for automatic cleanup
if (typeof window !== "undefined") {
  storeManager.registerStore({
    clearPersistedState: () =>
      useScenarioStore.getState().clearPersistedState(),
  });
}
