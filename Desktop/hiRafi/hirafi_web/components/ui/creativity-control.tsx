"use client"

import React from "react"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Brain, Zap, Target, Shuffle, Info } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export interface CreativityLevel {
  value: number
  label: string
  description: string
  icon: React.ElementType
  color: string
  bgColor: string
  borderColor: string
}

export const CREATIVITY_LEVELS: CreativityLevel[] = [
  {
    value: 0,
    label: "Tutarlı",
    description: "Her seferinde aynı sonuçlar - tekrarlanabilir testler için mükemmel",
    icon: Target,
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-900/20",
    borderColor: "border-blue-200 dark:border-blue-800"
  },
  {
    value: 1,
    label: "Dengel<PERSON>",
    description: "Güvenilirliği korurken hafif varyasyonlar",
    icon: Brain,
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-900/20",
    borderColor: "border-green-200 dark:border-green-800"
  },
  {
    value: 2,
    label: "Yaratıcı",
    description: "Daha çeşitli ve farklı test senaryoları",
    icon: Zap,
    color: "text-orange-600",
    bgColor: "bg-orange-50 dark:bg-orange-900/20",
    borderColor: "border-orange-200 dark:border-orange-800"
  },
  {
    value: 3,
    label: "Çok Yaratıcı",
    description: "Maksimum çeşitlilik ve yaratıcı test case üretimi",
    icon: Shuffle,
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-900/20",
    borderColor: "border-purple-200 dark:border-purple-800"
  }
]

export interface CreativityControlProps {
  value: number
  onChange: (value: number) => void
  disabled?: boolean
  className?: string
  showDescription?: boolean
  compact?: boolean
}

export function CreativityControl({
  value,
  onChange,
  disabled = false,
  className = "",
  showDescription = true,
  compact = false
}: CreativityControlProps) {
  const currentLevel = CREATIVITY_LEVELS.find(level => level.value === value) || CREATIVITY_LEVELS[1]
  const IconComponent = currentLevel.icon

  const handleSliderChange = (values: number[]) => {
    onChange(values[0])
  }

  if (compact) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Label className="text-sm font-medium">Yaratıcılık Seviyesi</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-3 w-3 text-gray-400 hover:text-gray-600" />
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <div className="space-y-2">
                    <p className="font-medium text-xs">AI yaratıcılığını kontrol eder:</p>
                    <ul className="text-xs space-y-1">
                      <li>• <strong>Tutarlı:</strong> Aynı sonuçlar için sabit seed</li>
                      <li>• <strong>Dengeli:</strong> Hafif varyasyonlar</li>
                      <li>• <strong>Yaratıcı:</strong> Çeşitli çıktılar</li>
                      <li>• <strong>Çok Yaratıcı:</strong> Maksimum çeşitlilik</li>
                    </ul>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Badge
            variant="outline"
            className={`${currentLevel.bgColor} ${currentLevel.borderColor} ${currentLevel.color} border`}
          >
            <IconComponent className="h-3 w-3 mr-1" />
            {currentLevel.label}
          </Badge>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <Slider
                  value={[value]}
                  onValueChange={handleSliderChange}
                  max={3}
                  min={0}
                  step={1}
                  disabled={disabled}
                  className="w-full"
                />
              </div>
            </TooltipTrigger>
            <TooltipContent side="top">
              <p className="text-xs">
                <strong>{currentLevel.label}:</strong> {currentLevel.description}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Level Labels for compact mode with Tooltips */}
        <div className="flex justify-between text-xs text-gray-500">
          <TooltipProvider>
            {CREATIVITY_LEVELS.map((level) => (
              <Tooltip key={level.value}>
                <TooltipTrigger asChild>
                  <span
                    className={`cursor-help transition-colors ${
                      value === level.value ? level.color + " font-medium" : "hover:text-gray-700 dark:hover:text-gray-300"
                    }`}
                  >
                    {level.label}
                  </span>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <div className="space-y-1">
                    <p className="font-medium text-xs">{level.label}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {level.description}
                    </p>
                    <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      {level.value === 0 && "Temperature: 0.1, Seed: Sabit"}
                      {level.value === 1 && "Temperature: 0.4, Seed: Yok"}
                      {level.value === 2 && "Temperature: 0.7, Seed: Yok"}
                      {level.value === 3 && "Temperature: 0.9, Seed: Yok"}
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
          </TooltipProvider>
        </div>
      </div>
    )
  }

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`p-2 rounded-full ${currentLevel.bgColor}`}>
              <IconComponent className={`h-4 w-4 ${currentLevel.color}`} />
            </div>
            <div>
              <CardTitle className="text-base">Yaratıcılık Seviyesi</CardTitle>
              {showDescription && (
                <CardDescription className="text-sm">
                  AI üretiminin ne kadar yaratıcı ve çeşitli olması gerektiğini kontrol eder
                </CardDescription>
              )}
            </div>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </TooltipTrigger>
              <TooltipContent side="left" className="max-w-xs">
                <div className="space-y-2">
                  <p className="font-medium">Nasıl çalışır:</p>
                  <ul className="text-xs space-y-1">
                    <li>• <strong>Tutarlı:</strong> Aynı sonuçlar için sabit seed kullanır</li>
                    <li>• <strong>Dengeli:</strong> Hafif varyasyonlarla düşük rastgelelik</li>
                    <li>• <strong>Yaratıcı:</strong> Çeşitli çıktılar için yüksek rastgelelik</li>
                    <li>• <strong>Çok Yaratıcı:</strong> Maksimum rastgelelik ve çeşitlilik</li>
                  </ul>
                  <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      <strong>İpucu:</strong> Test tekrarlanabilirliği için "Tutarlı",
                      çeşitli test senaryoları için "Yaratıcı" seçin.
                    </p>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Level Display */}
        <div className={`p-3 rounded-lg border ${currentLevel.bgColor} ${currentLevel.borderColor}`}>
          <div className="flex items-center space-x-2 mb-1">
            <IconComponent className={`h-4 w-4 ${currentLevel.color}`} />
            <span className={`font-medium ${currentLevel.color}`}>
              {currentLevel.label}
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {currentLevel.description}
          </p>
        </div>

        {/* Slider */}
        <div className="space-y-3">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <Slider
                    value={[value]}
                    onValueChange={handleSliderChange}
                    max={3}
                    min={0}
                    step={1}
                    disabled={disabled}
                    className="w-full"
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                <p className="text-xs">
                  <strong>{currentLevel.label}:</strong> {currentLevel.description}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          {/* Level Labels with Tooltips */}
          <div className="flex justify-between text-xs text-gray-500">
            <TooltipProvider>
              {CREATIVITY_LEVELS.map((level) => (
                <Tooltip key={level.value}>
                  <TooltipTrigger asChild>
                    <span
                      className={`cursor-help transition-colors ${
                        value === level.value ? level.color + " font-medium" : "hover:text-gray-700 dark:hover:text-gray-300"
                      }`}
                    >
                      {level.label}
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-xs">
                    <div className="space-y-1">
                      <p className="font-medium text-xs">{level.label}</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {level.description}
                      </p>
                      <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        {level.value === 0 && "Temperature: 0.1, Seed: Sabit"}
                        {level.value === 1 && "Temperature: 0.4, Seed: Yok"}
                        {level.value === 2 && "Temperature: 0.7, Seed: Yok"}
                        {level.value === 3 && "Temperature: 0.9, Seed: Yok"}
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              ))}
            </TooltipProvider>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Convert creativity level to OpenAI parameters
 */
export function creativityToParameters(creativityLevel: number): {
  temperature: number
  seed?: number
} {
  switch (creativityLevel) {
    case 0: // Consistent
      return {
        temperature: 0.1,
        seed: 42 // Fixed seed for reproducible results
      }
    case 1: // Balanced  
      return {
        temperature: 0.4,
        seed: undefined // No seed for slight variations
      }
    case 2: // Creative
      return {
        temperature: 0.7,
        seed: undefined
      }
    case 3: // Highly Creative
      return {
        temperature: 0.9,
        seed: undefined
      }
    default:
      return {
        temperature: 0.4,
        seed: undefined
      }
  }
}

export default CreativityControl
