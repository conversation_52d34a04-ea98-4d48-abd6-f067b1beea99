[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log
loglevel=info
user=root

[program:xvfb]
command=/usr/bin/Xvfb %(ENV_DISPLAY)s -screen 0 %(ENV_RESOLUTION)s -ac +extension GLX +render -noreset -nolisten tcp -dpi 96 -fbdir /tmp
autostart=true
autorestart=true
startretries=5
startsecs=3
stdout_logfile=/var/log/xvfb.log
stderr_logfile=/var/log/xvfb.err.log
priority=1
user=root

[program:fluxbox]
command=/usr/bin/fluxbox -rc /app/fluxbox-init
environment=DISPLAY=%(ENV_DISPLAY)s
autostart=true
autorestart=true
startretries=3
startsecs=5
stdout_logfile=/var/log/fluxbox.log
stderr_logfile=/dev/null
priority=100
depends_on=xvfb
user=root

[program:test-node]
; Node.js test runner (Puppeteer tabanlı testler)
command=node /app/dist/index.js
directory=/app
environment=DISPLAY=%(ENV_DISPLAY)s,NODE_ENV=production,PUPPETEER_ARGS="--disable-dbus --no-sandbox --disable-gpu",DBUS_SESSION_BUS_ADDRESS="/dev/null",DBUS_SYSTEM_BUS_ADDRESS="/dev/null",LIBGL_ALWAYS_INDIRECT="1",LIBGL_ALWAYS_SOFTWARE="1",TMPDIR="/tmp",LOG_LEVEL="debug",QUIET_STARTUP="false"
autostart=true
autorestart=true
startretries=3
startsecs=10
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
priority=500
depends_on=xvfb
user=root
