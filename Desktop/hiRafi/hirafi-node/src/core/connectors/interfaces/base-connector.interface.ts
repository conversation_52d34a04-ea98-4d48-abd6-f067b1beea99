/**
 * Base Connector Interface
 * Defines common functionality for all connector types
 */

import { EventEmitter } from 'events';

/**
 * Base connector interface that all connectors must implement
 */
export interface IBaseConnector extends EventEmitter {
  /**
   * Initialize the connector
   * @param config Configuration object
   */
  initialize(config?: any): Promise<void>;

  /**
   * Close the connector and cleanup resources
   */
  close(): Promise<void>;

  /**
   * Check if the connector is connected
   */
  isConnected(): boolean;

  /**
   * Get the current node ID
   */
  getNodeId(): string | null;

  /**
   * Update the node ID
   * @param nodeId New node ID
   */
  updateNodeId(nodeId: string): void;

  /**
   * Get connector statistics
   */
  getStats(): Record<string, any>;

  /**
   * Get connector type
   */
  getConnectorType(): string;
}

/**
 * Connector configuration interface
 */
export interface IConnectorConfig {
  nodeId?: string;
  hubUrl?: string;
  secretKey?: string;
  clientId?: string;
  enableLogging?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
}

/**
 * Connector statistics interface
 */
export interface IConnectorStats {
  connectorType: string;
  nodeId: string | null;
  isInitialized: boolean;
  isConnected: boolean;
  uptime: number;
  connectionAttempts: number;
  successfulConnections: number;
  errors: number;
  createdAt: string;
}

/**
 * Connector event types
 */
export enum ConnectorEventType {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  REGISTERED = 'registered',
  NODE_ID_UPDATED = 'nodeIdUpdated',
  MESSAGE_RECEIVED = 'messageReceived',
  MESSAGE_SENT = 'messageSent'
}

/**
 * Connector error interface
 */
export interface IConnectorError {
  error: any;
  context: string;
  connectorType: string;
  timestamp: string;
}
