/**
 * Common AI Keyboard Press Handler
 * Handles executing AI keyboard press steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Keyboard Press Handler
 * Handles executing AI keyboard press steps using the AI agent
 */
export class CommonAiKeyboardPressHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiKeyboardPress';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiKeyboardPressHandler';
  }

  /**
   * Execute an AI keyboard press step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiKeyboardPress, we expect:
    // - prompt: the key to press instruction (preferred) or fallback to value/name/description for backward compatibility
    // - target: target element (optional) or fallback to name/description
    const keyToPress = step.prompt || step.value || step.name || step.description;
    const targetElement = step.target || step.name || step.description;

    if (!keyToPress) {
      return { success: false, error: 'Missing key to press for aiKeyboardPress step' };
    }

    try {
      const options = step.deepThink ? { deepThink: true } : {};

      // If we have a target element and it's different from the key, use it as locate parameter
      const locateParam = (targetElement && targetElement !== keyToPress) ? targetElement : undefined;

      const description = locateParam
        ? `Press "${keyToPress}" on "${locateParam}"`
        : `Press "${keyToPress}"`;

      logger.debug(`CommonAiKeyboardPressHandler: Executing: ${description} with options: ${JSON.stringify(options)}`);

      const result = await agent.aiKeyboardPress(keyToPress, locateParam, options);
      logger.debug(`aiKeyboardPress result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error;

      const logEntry = formatAIResultForLog('aiKeyboardPress', description, result);
      logEntry.success = isSuccess;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Keyboard press failed: ${description}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Keyboard Press Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const description = (step.target && step.target !== keyToPress)
        ? `Press "${keyToPress}" on "${step.target}"`
        : `Press "${keyToPress}"`;

      const errorLogEntry = {
        operation: 'aiKeyboardPress',
        description: description,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiKeyboardPressHandler: Keyboard press failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 