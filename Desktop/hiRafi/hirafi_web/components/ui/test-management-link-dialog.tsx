import React from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export type TestManagementProvider = 'testrail' | 'zephyrscale'

interface TestManagementLinkDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  caseId: string | null
  provider: TestManagementProvider | null
  onConfirm: () => void
}

export function TestManagementLinkDialog({
  open,
  onOpenChange,
  caseId,
  provider,
  onConfirm,
}: TestManagementLinkDialogProps) {
  const getProviderName = () => {
    switch (provider) {
      case 'testrail':
        return 'TestRail'
      case 'zephyrscale':
        return 'Zephyr Scale'
      default:
        return 'Test Management'
    }
  }

  const getCaseDisplayId = () => {
    if (!caseId) return ''
    
    switch (provider) {
      case 'testrail':
        return `C${caseId}`
      case 'zephyrscale':
        return caseId
      default:
        return caseId
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Link with {getProviderName()}</AlertDialogTitle>
          <AlertDialogDescription>
            Would you like to link this scenario with {getProviderName()} case {getCaseDisplayId()}? 
            This will allow you to sync test results with {getProviderName()}.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>No, thanks</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm}>
            Link with {getProviderName()}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
