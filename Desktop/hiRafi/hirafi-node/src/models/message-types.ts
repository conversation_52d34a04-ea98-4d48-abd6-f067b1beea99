/**
 * Message Types
 * Standardized message types for communication between hub and nodes
 */

export enum MessageType {
  // Registration and status
  REGISTER = 'register',
  REGISTERED = 'registered',
  HEARTBEAT = 'heartbeat',
  HEARTBEAT_ACK = 'heartbeat-ack',
  HEARTBEAT_ERROR = 'heartbeat-error',
  NODE_ID_CORRECTION = 'node-id-correction',
  REGISTRATION_REQUIRED = 'registration-required',

  // Test execution
  TEST_ASSIGNMENT = 'test-assignment',
  TEST_CLAIMED = 'test-claimed',
  TEST_CLAIMED_ACK = 'test-claimed-ack',
  TEST_STARTED = 'test-started',
  TEST_COMPLETED = 'test-completed',
  TEST_FAILED = 'test-failed',
  TEST_STOPPED = 'test-stopped',
  TEST_RESULT = 'test-result',
  TEST_RESULT_RECEIVED = 'test-result-received',
  // Step progress handling removed - now handled by Redis queue worker
  GET_STEP_PROGRESS = 'get-step-progress',
  STEP_PROGRESS_RESPONSE = 'step-progress-response',
  UPDATE_TEST_VIDEO = 'update-test-video',
  VIDEO_UPLOAD_COMPLETED = 'video-upload-completed',
  STOP_TEST = 'stop-test',

  // Test queue management
  REQUEST_NEXT_TEST = 'request-next-test',
  NEXT_TEST_RESPONSE = 'next-test-response',

  // Connection management
  PING = 'ping',
  PONG = 'pong',

  // UI specific
  ADD_TEST = 'add-test',
  TEST_ADDED = 'test-added',
  GET_TESTS = 'get-tests',
  TESTS = 'tests',
  GET_QUEUE_STATUS = 'get-queue-status',
  QUEUE_STATUS = 'queue-status',
  GET_NODES = 'get-nodes',
  NODES = 'nodes',

  // Test status updates
  TEST_STATUS_UPDATE = 'test-status-update',

  // Node coordination (replaces Redis coordination events)
  TEST_RELEASED = 'test-released',
  NODE_STATUS_QUERY = 'node-status-query',
  NODE_STATUS_RESPONSE = 'node-status-response',
  GET_CURRENT_TEST_ID = 'get-current-test-id',
  CURRENT_TEST_ID_RESPONSE = 'current-test-id-response'
}

/**
 * Standard message interface
 */
export interface Message {
  type: MessageType | string;
  data?: any;
  timestamp?: number;
}

/**
 * Create a standardized message
 */
export function createMessage(type: MessageType, data?: any): Message {
  return {
    type,
    data,
    timestamp: Date.now()
  };
}
