"use client";

import { ScenarioManagement } from "@/components/scenario-management";
import { ProtectedRoute } from "@/components/protected-route";
import { PermissionGuard } from "@/components/permission-guard";
import ErrorBoundary from "@/components/error-boundary";

import { useScenarioStoreContext } from "@/hooks/useStoreContext";

export default function ScenariosPage() {
  // Store context management for company changes
  useScenarioStoreContext();

  // Remove redundant data fetching - ScenarioManagement handles all data fetching
  // This eliminates duplicate API calls and useEffect dependency issues

  return (
    <ProtectedRoute>
      <PermissionGuard resource="Scenario" action="view">
        <ErrorBoundary>
          <div className="h-screen">
            <ScenarioManagement />
          </div>
        </ErrorBoundary>
      </PermissionGuard>
    </ProtectedRoute>
  );
}
