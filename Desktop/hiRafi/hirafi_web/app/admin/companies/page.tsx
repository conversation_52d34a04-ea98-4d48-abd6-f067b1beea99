"use client"

import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminRouteGuard } from "@/lib/api/admin-auth"
import { CompanyList } from "@/components/admin/company-list"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"

export default function AdminCompaniesPage() {
  return (
    <AdminRouteGuard>
      <AdminLayout>
        <div className="flex flex-col gap-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Şirket Yönetimi</h1>
              <p className="text-muted-foreground">
                Sistemdeki şirketleri yönetin
              </p>
            </div>
            <Button asChild>
              <Link href="/admin/companies/new">
                <Plus className="mr-2 h-4 w-4" />
                Şirket Ekle
              </Link>
            </Button>
          </div>

          <CompanyList />
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  )
}
