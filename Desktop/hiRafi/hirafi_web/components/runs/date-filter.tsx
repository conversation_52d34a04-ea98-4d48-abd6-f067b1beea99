"use client"

import { useState } from "react"
import { Calendar as CalendarIcon, Check, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

interface DateFilterProps {
  dateFilter: string
  customDateRange: { start: Date | null; end: Date | null }
  onDateFilterChange: (filter: string, customRange?: { start: Date | null; end: Date | null }) => void
}

// Date filter options
const dateOptions = [
  { value: "all", label: "All Dates" },
  { value: "today", label: "Today" },
  { value: "yesterday", label: "Yesterday" },
  { value: "last7days", label: "Last 7 Days" },
  { value: "last30days", label: "Last 30 Days" },
  { value: "custom", label: "Custom Range..." },
]

export function DateFilter({ 
  dateFilter, 
  customDateRange, 
  onDateFilterChange 
}: DateFilterProps) {
  const [open, setOpen] = useState(false)
  const [calendarOpen, setCalendarOpen] = useState(false)
  const [tempDateRange, setTempDateRange] = useState<{ start: Date | null; end: Date | null }>(customDateRange)
  
  // Get the currently selected date option
  const selectedDate = dateOptions.find(option => option.value === dateFilter) || dateOptions[0]
  
  // Handle date option selection
  const handleDateSelect = (value: string) => {
    if (value === "custom") {
      setCalendarOpen(true)
    } else {
      onDateFilterChange(value)
      setOpen(false)
    }
  }
  
  // Apply custom date range
  const applyCustomDateRange = () => {
    onDateFilterChange("custom", tempDateRange)
    setCalendarOpen(false)
    setOpen(false)
  }
  
  // Cancel custom date selection
  const cancelCustomDateRange = () => {
    setTempDateRange(customDateRange)
    setCalendarOpen(false)
  }
  
  // Format date range for display
  const formatDateRange = () => {
    if (dateFilter === "custom" && customDateRange.start && customDateRange.end) {
      return `${format(customDateRange.start, "MMM d")} - ${format(customDateRange.end, "MMM d, yyyy")}`
    }
    return selectedDate.label
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-9 border-gray-200 dark:border-gray-700 min-w-[120px] justify-between",
            dateFilter !== "all" ? "bg-gray-50 dark:bg-gray-800" : ""
          )}
        >
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
            <span className="text-sm font-medium">
              {dateFilter !== "all" ? formatDateRange() : "Date"}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[220px]">
        {dateOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            className={cn(
              "flex items-center px-3 py-2 cursor-pointer",
              dateFilter === option.value ? "bg-gray-100 dark:bg-gray-800" : ""
            )}
            onClick={() => handleDateSelect(option.value)}
          >
            <div className="flex items-center flex-1">
              <span>{option.label}</span>
            </div>
            {dateFilter === option.value && (
              <Check className="h-4 w-4 ml-2 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
      
      {/* Custom Date Range Popover */}
      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            <div className="text-sm font-medium mb-2">Select Date Range</div>
            <div className="flex gap-2 mb-2">
              <div>
                <div className="text-xs text-gray-500 mb-1">Start Date</div>
                <div className="text-sm font-medium">
                  {tempDateRange.start ? format(tempDateRange.start, "MMM d, yyyy") : "Select..."}
                </div>
              </div>
              <div className="text-gray-400 self-end mb-1">→</div>
              <div>
                <div className="text-xs text-gray-500 mb-1">End Date</div>
                <div className="text-sm font-medium">
                  {tempDateRange.end ? format(tempDateRange.end, "MMM d, yyyy") : "Select..."}
                </div>
              </div>
            </div>
            
            <Calendar
              mode="range"
              selected={{
                from: tempDateRange.start || undefined,
                to: tempDateRange.end || undefined,
              }}
              onSelect={(range) => {
                setTempDateRange({
                  start: range?.from || null,
                  end: range?.to || null,
                })
              }}
              className="rounded-md border"
              initialFocus
            />
            
            <div className="flex justify-between mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={cancelCustomDateRange}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={applyCustomDateRange}
                disabled={!tempDateRange.start || !tempDateRange.end}
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </DropdownMenu>
  )
}
