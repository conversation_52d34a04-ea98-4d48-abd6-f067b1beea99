/**
 * Status normalization utilities
 * Centralizes status conversion logic to eliminate duplicate code
 */

export type RunStatus = 'created' | 'queued' | 'running' | 'completed' | 'failed' | 'stopped' | 'partial';
export type UIStatus = 'created' | 'queue' | 'running' | 'passed' | 'failed' | 'stopped' | 'partial';

/**
 * Normalizes API status to consistent format
 * @param status Raw status from API
 * @returns Normalized status
 */
export function normalizeApiStatus(status: string): RunStatus {
  const upperStatus = status?.toUpperCase();
  
  switch (upperStatus) {
    case 'COMPLETED':
      return 'completed';
    case 'RUNNING':
      return 'running';
    case 'FAILED':
      return 'failed';
    case 'STOPPED':
      return 'stopped';
    case 'QUEUED':
      return 'queued';
    case 'PARTIAL':
      return 'partial';
    case 'CREATED':
      return 'created';
    default:
      // Return lowercase version as fallback
      return (status?.toLowerCase() || 'created') as RunStatus;
  }
}

/**
 * Converts API status to UI status
 * @param apiStatus API status
 * @returns UI status for display
 */
export function apiStatusToUIStatus(apiStatus: RunStatus): UIStatus {
  switch (apiStatus) {
    case 'completed':
      return 'passed';
    case 'queued':
      return 'queue';
    default:
      return apiStatus as UIStatus;
  }
}

/**
 * Converts UI status to API status
 * @param uiStatus UI status
 * @returns API status
 */
export function uiStatusToApiStatus(uiStatus: UIStatus): RunStatus {
  switch (uiStatus) {
    case 'passed':
      return 'completed';
    case 'queue':
      return 'queued';
    default:
      return uiStatus as RunStatus;
  }
}

/**
 * Calculates test results from scenario statuses
 * @param scenarioStatuses Array of scenario status objects
 * @returns Calculated test results
 */
export function calculateTestResultsFromStatuses(scenarioStatuses: any[]): {
  total: number;
  completed: number;
  failed: number;
  stopped: number;
  running: number;
  queued: number;
} {
  const results = {
    total: scenarioStatuses.length,
    completed: 0,
    failed: 0,
    stopped: 0,
    running: 0,
    queued: 0
  };

  scenarioStatuses.forEach(scenario => {
    const normalizedStatus = normalizeApiStatus(scenario.status);
    switch (normalizedStatus) {
      case 'completed':
        results.completed++;
        break;
      case 'failed':
        results.failed++;
        break;
      case 'stopped':
        results.stopped++;
        break;
      case 'running':
        results.running++;
        break;
      case 'queued':
      case 'created':
        results.queued++;
        break;
    }
  });

  return results;
}

/**
 * Generates default test results based on run status and scenario count
 * @param runStatus Current run status
 * @param scenarioCount Total number of scenarios
 * @param existingResults Existing test results (optional)
 * @returns Default test results
 */
export function generateDefaultTestResults(
  runStatus: RunStatus,
  scenarioCount: number,
  existingResults?: any
) {
  const baseResults = {
    total: scenarioCount,
    completed: existingResults?.completed || 0,
    failed: existingResults?.failed || 0,
    stopped: existingResults?.stopped || 0,
    running: existingResults?.running || 0,
    queued: existingResults?.queued || 0
  };

  switch (runStatus) {
    case 'created':
      return {
        ...baseResults,
        queued: 0,
        running: 0
      };
    case 'running':
      if (!existingResults) {
        return {
          ...baseResults,
          queued: scenarioCount,
          running: 0
        };
      }
      return baseResults;
    case 'completed':
      if (!existingResults) {
        return {
          ...baseResults,
          completed: scenarioCount,
          queued: 0,
          running: 0
        };
      }
      return baseResults;
    case 'failed':
      if (!existingResults) {
        return {
          ...baseResults,
          failed: scenarioCount,
          queued: 0,
          running: 0
        };
      }
      return baseResults;
    case 'stopped':
      if (!existingResults) {
        return {
          ...baseResults,
          stopped: scenarioCount,
          queued: 0,
          running: 0
        };
      }
      return baseResults;
    default:
      return baseResults;
  }
}
