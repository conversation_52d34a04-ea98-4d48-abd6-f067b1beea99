"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import type { CustomEnvironment, ColorOption } from "@/types/test-data"

interface EnvironmentDialogsProps {
  // New Environment Dialog
  newEnvironmentDialogOpen: boolean
  onNewEnvironmentDialogOpenChange: (open: boolean) => void
  newEnvironment: Partial<CustomEnvironment>
  onNewEnvironmentChange: (environment: Partial<CustomEnvironment>) => void
  onCreateEnvironment: () => void

  // Edit Environment Dialog
  editEnvironmentDialogOpen: boolean
  onEditEnvironmentDialogOpenChange: (open: boolean) => void
  selectedEnvironment: CustomEnvironment | null
  onSelectedEnvironmentChange: (environment: CustomEnvironment) => void
  onUpdateEnvironment: () => void

  // Color options
  colorOptions: ColorOption[]
}

export function EnvironmentDialogs({
  newEnvironmentDialogOpen,
  onNewEnvironmentDialogOpenChange,
  newEnvironment,
  onNewEnvironmentChange,
  onCreateEnvironment,
  editEnvironmentDialogOpen,
  onEditEnvironmentDialogOpenChange,
  selectedEnvironment,
  onSelectedEnvironmentChange,
  onUpdateEnvironment,
  colorOptions,
}: EnvironmentDialogsProps) {
  return (
    <>
      {/* New Environment Dialog */}
      <Dialog open={newEnvironmentDialogOpen} onOpenChange={onNewEnvironmentDialogOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Environment</DialogTitle>
            <DialogDescription>Add a new environment for your test data management.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="env-name">Environment Name</Label>
              <Input
                id="env-name"
                placeholder="e.g. Pre-Production, Demo, Integration"
                value={newEnvironment.name || ""}
                onChange={(e) => onNewEnvironmentChange({ ...newEnvironment, name: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="env-description">Description</Label>
              <Textarea
                id="env-description"
                placeholder="Describe the purpose of this environment"
                value={newEnvironment.description || ""}
                onChange={(e) => onNewEnvironmentChange({ ...newEnvironment, description: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="env-color">Color</Label>
              <Select
                value={newEnvironment.color || "blue"}
                onValueChange={(value) => onNewEnvironmentChange({ ...newEnvironment, color: value })}
              >
                <SelectTrigger id="env-color">
                  <SelectValue placeholder="Select a color" />
                </SelectTrigger>
                <SelectContent>
                  {colorOptions.map((color) => (
                    <SelectItem key={color.value} value={color.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${color.class}`}></div>
                        <span>{color.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="env-active"
                checked={newEnvironment.isActive ?? true}
                onCheckedChange={(checked) => onNewEnvironmentChange({ ...newEnvironment, isActive: checked })}
              />
              <Label htmlFor="env-active">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => onNewEnvironmentDialogOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={onCreateEnvironment}>Create Environment</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Environment Dialog */}
      <Dialog open={editEnvironmentDialogOpen} onOpenChange={onEditEnvironmentDialogOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Environment</DialogTitle>
            <DialogDescription>Update the environment details.</DialogDescription>
          </DialogHeader>
          {selectedEnvironment && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-env-name">Environment Name</Label>
                <Input
                  id="edit-env-name"
                  value={selectedEnvironment.name}
                  onChange={(e) => onSelectedEnvironmentChange({ ...selectedEnvironment, name: e.target.value })}
                  disabled={selectedEnvironment.type === "default"}
                />
                {selectedEnvironment.type === "default" && (
                  <p className="text-xs text-gray-500">Default environment names cannot be changed</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-env-description">Description</Label>
                <Textarea
                  id="edit-env-description"
                  value={selectedEnvironment.description}
                  onChange={(e) => onSelectedEnvironmentChange({ ...selectedEnvironment, description: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-env-color">Color</Label>
                <Select
                  value={selectedEnvironment.color}
                  onValueChange={(value) => onSelectedEnvironmentChange({ ...selectedEnvironment, color: value })}
                >
                  <SelectTrigger id="edit-env-color">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {colorOptions.map((color) => (
                      <SelectItem key={color.value} value={color.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${color.class}`}></div>
                          <span>{color.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-env-active"
                  checked={selectedEnvironment.isActive}
                  onCheckedChange={(checked) =>
                    onSelectedEnvironmentChange({ ...selectedEnvironment, isActive: checked })
                  }
                />
                <Label htmlFor="edit-env-active">Active</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => onEditEnvironmentDialogOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={onUpdateEnvironment}>Update Environment</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
