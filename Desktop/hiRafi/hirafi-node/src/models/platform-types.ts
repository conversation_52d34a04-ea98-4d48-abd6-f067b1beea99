/**
 * Platform Types
 * Defines types related to test platforms
 */

import { TestRequest as BaseTestRequest, EnvironmentSettings, WebEnvironmentSettings } from './types.js';
import { AndroidEnvironmentSettings } from './android-environment.js';

/**
 * Test Platform Enum
 * Defines the available test platforms
 */
export enum TestPlatform {
  WEB = 'web',
  ANDROID = 'android'
}

/**
 * Extended Test Request Interface
 * Extends the base TestRequest interface with platform-specific fields
 * Uses the discriminated union pattern for environment settings
 */
export interface PlatformTestRequest extends Omit<BaseTestRequest, 'environmentSettings'> {
  /**
   * Platform to run the test on
   * Required field to specify the test platform
   */
  platform: TestPlatform;

  /**
   * Environment settings for the test
   * Uses the discriminated union pattern from EnvironmentSettings
   */
  environmentSettings?: EnvironmentSettings;
}
