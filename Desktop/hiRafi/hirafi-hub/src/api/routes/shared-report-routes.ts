/**
 * Shared Report API Routes
 * Paylaşılan raporlar için public API rotaları
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import {
  getReportByShareToken,
  updateReportShareAccess
} from '../../services/mongo/reportService.js';

const router = Router();

/**
 * GET /api/shared-reports/:token
 * Paylaşılan raporu getirir (public endpoint, auth gerektirmez)
 */
router.get('/:token', async (req: Request, res: Response) => {
  try {
    const { token } = req.params;
    const { password } = req.query;
    
    if (!token) {
      return res.status(400).json({
        success: false,
        error: 'Share token is required'
      });
    }

    logger.info(`[API] GET /shared-reports/${token} - Accessing shared report`);

    // Paylaşılan raporu getir
    const result = await getReportByShareToken(token, password as string);

    if (!result.success) {
      // Ş<PERSON>re gerekliyse özel bir yanıt döndür
      if (result.passwordRequired) {
        return res.status(401).json({
          success: false,
          error: 'Password required',
          passwordRequired: true
        });
      }

      return res.status(404).json({
        success: false,
        error: result.message || `Shared report not found or expired`
      });
    }

    // Erişim sayacını güncelle
    await updateReportShareAccess(token);

    // Başarılı yanıt
    res.json({
      success: true,
      report: result.report
    });
  } catch (error: any) {
    logger.error(`API Error in GET /shared-reports/${req.params.token}: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
