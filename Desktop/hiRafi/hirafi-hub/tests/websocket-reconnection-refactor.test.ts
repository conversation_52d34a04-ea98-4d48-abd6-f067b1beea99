/**
 * Test suite for WebSocket reconnection refactor
 * 
 * This test verifies that the refactored reconnection logic:
 * 1. Centralizes re-registration through ConnectionManager
 * 2. <PERSON><PERSON><PERSON> handles clientId-based node identification
 * 3. Clears registration state on disconnection
 * 4. Prevents spaghetti code with multiple reconnection triggers
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { EventEmitter } from 'events';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
};

// Mock NodeRegistry
class MockNodeRegistry extends EventEmitter {
  private nodes = new Map();
  private registrationInProgress = new Set();
  private registrationTimestamps = new Map();
  private REGISTRATION_COOLDOWN = 5000;

  async registerNode(nodeData: any): Promise<boolean> {
    const { id, clientId } = nodeData;
    
    // Check cooldown but allow re-registration if clientId matches
    const lastRegistration = this.registrationTimestamps.get(id);
    const now = Date.now();
    if (lastRegistration && (now - lastRegistration) < this.REGISTRATION_COOLDOWN) {
      const existingNode = this.nodes.get(id);
      if (!existingNode || existingNode.clientId !== clientId) {
        return false;
      }
    }

    this.registrationTimestamps.set(id, now);
    this.nodes.set(id, { ...nodeData, lastSeen: new Date() });
    this.emit('node:registered', id, nodeData);
    return true;
  }

  removeNode(nodeId: string): void {
    this.nodes.delete(nodeId);
    this.registrationInProgress.delete(nodeId);
    this.registrationTimestamps.delete(nodeId);
    this.emit('node:unregistered', nodeId);
  }

  getNode(nodeId: string) {
    return this.nodes.get(nodeId);
  }

  getAllNodes() {
    return this.nodes;
  }
}

// Mock ConnectionManager
class MockConnectionManager {
  private registrationState = 'idle';
  private registrationPromise: Promise<boolean> | null = null;

  async reconnect(type: string): Promise<boolean> {
    this.registrationState = 'idle';
    this.registrationPromise = null;
    // Simulate successful reconnection
    return true;
  }

  async handleRegistrationRequired(): Promise<boolean> {
    mockLogger.warn('ConnectionManager: Hub requested re-registration, initiating controlled re-registration');
    
    this.registrationState = 'idle';
    this.registrationPromise = null;
    
    const success = await this.reconnect('WEBSOCKET');
    
    if (success) {
      mockLogger.info('ConnectionManager: Re-registration completed successfully');
    } else {
      mockLogger.error('ConnectionManager: Re-registration failed');
    }
    
    return success;
  }
}

// Mock EventCoordinator
class MockEventCoordinator extends EventEmitter {
  private connectionManager: MockConnectionManager;
  private websocketConnector: EventEmitter;

  constructor(connectionManager: MockConnectionManager) {
    super();
    this.connectionManager = connectionManager;
    this.websocketConnector = new EventEmitter();
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    // Handle registration required events - CENTRALIZED THROUGH CONNECTION MANAGER
    this.websocketConnector.on('registrationRequired', (oldNodeId) => {
      mockLogger.warn(`EventCoordinator: Hub indicated that node (${oldNodeId || 'unknown'}) needs to re-register`);
      
      // Route through ConnectionManager for centralized re-registration
      this.connectionManager.handleRegistrationRequired().catch(error => {
        mockLogger.error(`EventCoordinator: Error handling registration required: ${error}`);
      });
    });
  }

  triggerRegistrationRequired(nodeId: string) {
    this.websocketConnector.emit('registrationRequired', nodeId);
  }
}

describe('WebSocket Reconnection Refactor', () => {
  let mockNodeRegistry: MockNodeRegistry;
  let mockConnectionManager: MockConnectionManager;
  let mockEventCoordinator: MockEventCoordinator;

  beforeEach(() => {
    jest.clearAllMocks();
    mockNodeRegistry = new MockNodeRegistry();
    mockConnectionManager = new MockConnectionManager();
    mockEventCoordinator = new MockEventCoordinator(mockConnectionManager);
  });

  afterEach(() => {
    // Clean up any remaining listeners
    mockNodeRegistry.removeAllListeners();
    mockEventCoordinator.removeAllListeners();
  });

  describe('Centralized Re-registration', () => {
    it('should handle registration-required through ConnectionManager', async () => {
      const handleRegistrationRequiredSpy = jest.spyOn(mockConnectionManager, 'handleRegistrationRequired');
      
      // Trigger registration required event
      mockEventCoordinator.triggerRegistrationRequired('test-node-1');
      
      // Wait for async handling
      await new Promise(resolve => setTimeout(resolve, 10));
      
      expect(handleRegistrationRequiredSpy).toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'ConnectionManager: Hub requested re-registration, initiating controlled re-registration'
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'ConnectionManager: Re-registration completed successfully'
      );
    });

    it('should reset registration state during re-registration', async () => {
      const reconnectSpy = jest.spyOn(mockConnectionManager, 'reconnect');
      
      const result = await mockConnectionManager.handleRegistrationRequired();
      
      expect(result).toBe(true);
      expect(reconnectSpy).toHaveBeenCalledWith('WEBSOCKET');
    });
  });

  describe('ClientId-based Node Identification', () => {
    it('should allow re-registration with matching clientId despite cooldown', async () => {
      const nodeData = {
        id: 'test-node-1',
        name: 'Test Node',
        clientId: 'client-123',
        capabilities: ['web']
      };

      // First registration
      const firstResult = await mockNodeRegistry.registerNode(nodeData);
      expect(firstResult).toBe(true);

      // Immediate re-registration with same clientId should succeed
      const secondResult = await mockNodeRegistry.registerNode(nodeData);
      expect(secondResult).toBe(true);
    });

    it('should reject re-registration with different clientId during cooldown', async () => {
      const nodeData1 = {
        id: 'test-node-1',
        name: 'Test Node',
        clientId: 'client-123',
        capabilities: ['web']
      };

      const nodeData2 = {
        id: 'test-node-1',
        name: 'Test Node',
        clientId: 'client-456',
        capabilities: ['web']
      };

      // First registration
      const firstResult = await mockNodeRegistry.registerNode(nodeData1);
      expect(firstResult).toBe(true);

      // Immediate re-registration with different clientId should fail
      const secondResult = await mockNodeRegistry.registerNode(nodeData2);
      expect(secondResult).toBe(false);
    });
  });

  describe('Registration State Cleanup', () => {
    it('should clear registration state when node is removed', () => {
      const nodeId = 'test-node-1';
      
      // Add node to registry
      mockNodeRegistry.registerNode({
        id: nodeId,
        name: 'Test Node',
        clientId: 'client-123'
      });

      // Remove node
      mockNodeRegistry.removeNode(nodeId);

      // Verify node is removed
      expect(mockNodeRegistry.getNode(nodeId)).toBeUndefined();
    });

    it('should emit proper events during node lifecycle', async () => {
      const registeredSpy = jest.fn();
      const unregisteredSpy = jest.fn();

      mockNodeRegistry.on('node:registered', registeredSpy);
      mockNodeRegistry.on('node:unregistered', unregisteredSpy);

      const nodeData = {
        id: 'test-node-1',
        name: 'Test Node',
        clientId: 'client-123'
      };

      // Register node
      await mockNodeRegistry.registerNode(nodeData);
      expect(registeredSpy).toHaveBeenCalledWith('test-node-1', nodeData);

      // Remove node
      mockNodeRegistry.removeNode('test-node-1');
      expect(unregisteredSpy).toHaveBeenCalledWith('test-node-1');
    });
  });
});
