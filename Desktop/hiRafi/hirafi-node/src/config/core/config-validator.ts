/**
 * Configuration Validator
 * Comprehensive validation for configuration objects
 */

import { NodeConfig } from '../../models/types.js';
import { ConfigValidationResult } from '../types/config-types.js';
import { validateNodeConfig } from '../domains/node-config.js';
import { validateFileSystemConfig } from '../domains/filesystem-config.js';
import { validateRedisConfig } from '../domains/redis-config.js';
import { validateMinioConfig, validateS3Config } from '../domains/storage-config.js';
import { validateTestConfig } from '../domains/test-config.js';
// import { validatePlatformConfig } from '../domains/platform-config.js'; // TODO: Use when platform validation is needed

/**
 * Configuration validator with comprehensive validation rules
 */
export class ConfigurationValidator {
  /**
   * Validates a complete NodeConfig object
   * @param config Configuration to validate
   * @returns Comprehensive validation result
   */
  public static validateComplete(config: NodeConfig): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Validate node configuration
      const nodeValidation = validateNodeConfig({
        name: config.name,
        hubUrl: config.hubUrl,
        capabilities: config.capabilities,
        version: config.version,
        environment: config.environment,
        useSystemInfo: config.useSystemInfo || false,
        port: config.port || 0,
        nodeId: config.nodeId || ''
      });

      if (!nodeValidation.isValid) {
        errors.push(...nodeValidation.errors.map(e => `Node: ${e}`));
      }

      // Validate file system configuration
      const fileSystemValidation = validateFileSystemConfig({
        screenshotsDir: config.screenshotsDir,
        nodeScenarioDir: config.nodeScenarioDir
      });

      if (!fileSystemValidation.isValid) {
        errors.push(...fileSystemValidation.errors.map(e => `FileSystem: ${e}`));
      }

      // Validate Redis configuration if enabled
      if (config.redisEnabled && config.redis) {
        const redisValidation = validateRedisConfig({
          enabled: config.redisEnabled,
          host: config.redis.host,
          port: config.redis.port,
          password: config.redis.password,
          username: '', // Redis config doesn't have username in NodeConfig
          db: config.redis.db,
          prefix: config.redis.prefix,
          url: config.redis.url
        });

        if (!redisValidation.isValid) {
          errors.push(...redisValidation.errors.map(e => `Redis: ${e}`));
        }
      }

      // Validate MinIO configuration if enabled
      if (config.minioEnabled && config.minio) {
        const minioValidation = validateMinioConfig({
          enabled: config.minioEnabled,
          endPoint: config.minio.endPoint,
          port: config.minio.port,
          useSSL: config.minio.useSSL,
          accessKey: config.minio.accessKey,
          secretKey: config.minio.secretKey,
          region: config.minio.region || '',
          screenshotBucket: config.minio.screenshotBucket,
          reportBucket: config.minio.reportBucket,
          publicBaseUrl: config.minio.publicBaseUrl
        });

        if (!minioValidation.isValid) {
          errors.push(...minioValidation.errors.map(e => `MinIO: ${e}`));
        }
      }

      // Validate S3 configuration if enabled
      if (config.s3Enabled && config.s3) {
        const s3Validation = validateS3Config({
          enabled: config.s3Enabled,
          region: config.s3.region,
          accessKey: config.s3.accessKey,
          secretKey: config.s3.secretKey,
          endpoint: config.s3.endpoint,
          forcePathStyle: config.s3.forcePathStyle || false,
          screenshotBucket: config.s3.screenshotBucket,
          reportBucket: config.s3.reportBucket,
          publicBaseUrl: config.s3.publicBaseUrl
        });

        if (!s3Validation.isValid) {
          errors.push(...s3Validation.errors.map(e => `S3: ${e}`));
        }
      }

      // Validate test configuration
      if (config.testOptions) {
        const testValidation = validateTestConfig({
          maxRetries: config.testOptions.maxRetries || 1,
          timeout: config.testOptions.timeout || 30000
        });

        if (!testValidation.isValid) {
          errors.push(...testValidation.errors.map(e => `Test: ${e}`));
        }
      }

      // Validate platform configuration
      if (config.platforms) {
        // Basic platform validation without using domain validators
        // since the types don't match exactly
        if (!config.platforms.web?.enabled && !config.platforms.android?.enabled) {
          errors.push('Platform: At least one platform (web or android) must be enabled');
        }

        if (config.platforms.android?.enabled) {
          if (!config.platforms.android.appium?.hostname) {
            errors.push('Platform: Appium hostname is required when Android platform is enabled');
          }

          if (config.platforms.android.appium?.port &&
              (config.platforms.android.appium.port < 1 || config.platforms.android.appium.port > 65535)) {
            errors.push('Platform: Appium port must be between 1 and 65535');
          }
        }
      }

      // Cross-domain validation
      const crossDomainValidation = ConfigurationValidator.validateCrossDomain(config);
      errors.push(...crossDomainValidation.errors);
      warnings.push(...crossDomainValidation.warnings);

    } catch (error) {
      errors.push(`Validation error: ${error}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validates configuration for a specific environment
   * @param config Configuration to validate
   * @param environment Target environment
   * @returns Environment-specific validation result
   */
  public static validateForEnvironment(config: NodeConfig, environment: string): ConfigValidationResult {
    const baseValidation = ConfigurationValidator.validateComplete(config);
    const errors = [...baseValidation.errors];
    const warnings = [...baseValidation.warnings];

    switch (environment) {
      case 'production':
        // Production-specific validation
        if (config.redisEnabled && config.redis && !config.redis.password) {
          warnings.push('Redis password should be set in production');
        }

        if (config.s3Enabled && config.s3 && (!config.s3.accessKey || !config.s3.secretKey)) {
          errors.push('S3 credentials are required in production');
        }

        if (config.environment !== 'production') {
          warnings.push('Environment should be set to "production" for production deployment');
        }
        break;

      case 'development':
        // Development-specific validation
        if (!config.useSystemInfo) {
          warnings.push('System info collection is recommended in development');
        }
        break;

      case 'test':
        // Test-specific validation
        if (config.redisEnabled) {
          warnings.push('Redis should typically be disabled in test environment');
        }

        if (config.minioEnabled || config.s3Enabled) {
          warnings.push('External storage should typically be disabled in test environment');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validates cross-domain configuration consistency
   * @param config Configuration to validate
   * @returns Cross-domain validation result
   */
  private static validateCrossDomain(config: NodeConfig): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Storage validation
    if (config.minioEnabled && config.s3Enabled) {
      warnings.push('Both MinIO and S3 are enabled - only one storage provider should typically be used');
    }

    if (!config.minioEnabled && !config.s3Enabled) {
      warnings.push('No storage provider is enabled - screenshots and reports may not be stored');
    }

    // Platform validation
    // Note: Appium validation is handled dynamically in test requests
    if (config.platforms?.android?.enabled && config.platforms.android.appium?.hostname) {
      // Only validate if appium config is provided (optional)
      if (!config.platforms.android.appium.hostname.trim()) {
        errors.push('Appium hostname cannot be empty when provided');
      }
    }

    // Redis and WebSocket validation
    if (config.websocketEnabled && !config.redisEnabled) {
      warnings.push('WebSocket is enabled but Redis is disabled - this may limit functionality');
    }

    // Port validation
    if (config.port && config.platforms?.android?.enabled &&
        config.platforms.android.appium?.port &&
        config.platforms.android.appium.port === config.port) {
      errors.push('Node port and Appium port cannot be the same');
    }

    // Environment consistency
    if (config.environment === 'production' && config.useSystemInfo) {
      warnings.push('System info collection should typically be disabled in production');
    }

    return { errors, warnings };
  }

  /**
   * Validates configuration against a schema
   * @param config Configuration to validate
   * @param schema Validation schema
   * @returns Schema validation result
   */
  public static validateAgainstSchema(config: NodeConfig, schema: any): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Comprehensive schema validation implementation
      const validationResult = this.performSchemaValidation(config, schema || this.getDefaultSchema());
      errors.push(...validationResult.errors);
      warnings.push(...validationResult.warnings);
    } catch (error: any) {
      errors.push(`Schema validation error: ${error.message}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Perform comprehensive schema validation
   * @param config Configuration to validate
   * @param schema Validation schema
   * @returns Validation result
   */
  private static performSchemaValidation(config: NodeConfig, schema: any): { errors: string[], warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required fields
    if (schema.required) {
      for (const field of schema.required) {
        if (!(field in config) || config[field as keyof NodeConfig] === undefined) {
          errors.push(`Required field '${field}' is missing`);
        }
      }
    }

    // Validate field types and constraints
    if (schema.properties) {
      for (const [fieldName, fieldSchema] of Object.entries(schema.properties)) {
        const fieldValue = config[fieldName as keyof NodeConfig];
        const validationResult = this.validateField(fieldName, fieldValue, fieldSchema as any);
        errors.push(...validationResult.errors);
        warnings.push(...validationResult.warnings);
      }
    }

    return { errors, warnings };
  }

  /**
   * Validate individual field
   * @param fieldName Field name
   * @param fieldValue Field value
   * @param fieldSchema Field schema
   * @returns Validation result
   */
  private static validateField(fieldName: string, fieldValue: any, fieldSchema: any): { errors: string[], warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (fieldValue === undefined || fieldValue === null) {
      return { errors, warnings };
    }

    // Type validation
    if (fieldSchema.type) {
      const expectedType = fieldSchema.type;
      const actualType = Array.isArray(fieldValue) ? 'array' : typeof fieldValue;

      if (actualType !== expectedType) {
        errors.push(`Field '${fieldName}' must be of type ${expectedType}, got ${actualType}`);
        return { errors, warnings };
      }
    }

    // String validations
    if (fieldSchema.type === 'string') {
      if (fieldSchema.minLength && fieldValue.length < fieldSchema.minLength) {
        errors.push(`Field '${fieldName}' must be at least ${fieldSchema.minLength} characters long`);
      }
      if (fieldSchema.maxLength && fieldValue.length > fieldSchema.maxLength) {
        errors.push(`Field '${fieldName}' must be at most ${fieldSchema.maxLength} characters long`);
      }
      if (fieldSchema.pattern && !new RegExp(fieldSchema.pattern).test(fieldValue)) {
        errors.push(`Field '${fieldName}' does not match required pattern`);
      }
      if (fieldSchema.format === 'url' && !this.isValidUrl(fieldValue)) {
        errors.push(`Field '${fieldName}' must be a valid URL`);
      }
    }

    // Number validations
    if (fieldSchema.type === 'number') {
      if (fieldSchema.minimum !== undefined && fieldValue < fieldSchema.minimum) {
        errors.push(`Field '${fieldName}' must be at least ${fieldSchema.minimum}`);
      }
      if (fieldSchema.maximum !== undefined && fieldValue > fieldSchema.maximum) {
        errors.push(`Field '${fieldName}' must be at most ${fieldSchema.maximum}`);
      }
    }

    // Array validations
    if (fieldSchema.type === 'array') {
      if (fieldSchema.minItems && fieldValue.length < fieldSchema.minItems) {
        errors.push(`Field '${fieldName}' must have at least ${fieldSchema.minItems} items`);
      }
      if (fieldSchema.maxItems && fieldValue.length > fieldSchema.maxItems) {
        errors.push(`Field '${fieldName}' must have at most ${fieldSchema.maxItems} items`);
      }
      if (fieldSchema.items) {
        fieldValue.forEach((item: any, index: number) => {
          const itemValidation = this.validateField(`${fieldName}[${index}]`, item, fieldSchema.items);
          errors.push(...itemValidation.errors);
          warnings.push(...itemValidation.warnings);
        });
      }
    }

    // Object validations
    if (fieldSchema.type === 'object' && fieldSchema.properties) {
      for (const [propName, propSchema] of Object.entries(fieldSchema.properties)) {
        const propValue = fieldValue[propName];
        const propValidation = this.validateField(`${fieldName}.${propName}`, propValue, propSchema as any);
        errors.push(...propValidation.errors);
        warnings.push(...propValidation.warnings);
      }
    }

    return { errors, warnings };
  }

  /**
   * Get default validation schema
   * @returns Default schema
   */
  private static getDefaultSchema(): any {
    return {
      type: 'object',
      required: ['name', 'hubUrl', 'capabilities'],
      properties: {
        name: {
          type: 'string',
          minLength: 1,
          maxLength: 100
        },
        hubUrl: {
          type: 'string',
          format: 'url'
        },
        capabilities: {
          type: 'array',
          minItems: 1,
          items: {
            type: 'string',
            enum: ['web', 'android', 'ios']
          }
        },
        version: {
          type: 'string',
          pattern: '^\\d+\\.\\d+\\.\\d+$'
        },
        environment: {
          type: 'string',
          enum: ['development', 'staging', 'production']
        },
        port: {
          type: 'number',
          minimum: 0,
          maximum: 65535
        }
      }
    };
  }

  /**
   * Check if string is a valid URL
   * @param url URL to validate
   * @returns True if valid URL
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validates configuration security aspects
   * @param config Configuration to validate
   * @returns Security validation result
   */
  public static validateSecurity(config: NodeConfig): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check for default credentials
    if (config.minio?.accessKey === 'minioadmin' && config.minio?.secretKey === 'minioadmin') {
      warnings.push('MinIO is using default credentials - change them for security');
    }

    if (config.redis?.password === '') {
      warnings.push('Redis has no password set - consider setting one for security');
    }

    // Check for insecure protocols
    if (config.hubUrl.startsWith('http://') && config.environment === 'production') {
      warnings.push('Hub URL uses HTTP in production - consider using HTTPS');
    }

    if (config.platforms?.android?.appium?.protocol === 'http' && config.environment === 'production') {
      warnings.push('Appium uses HTTP in production - consider using HTTPS');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
