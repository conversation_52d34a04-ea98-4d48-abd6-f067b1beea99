"use client"

import { RunFormProvider } from "@/components/create-run/run-context"
import { CreateRunLayout } from "@/components/create-run/create-run-layout"
import { useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { ProtectedRoute } from "@/components/protected-route"
import { useState } from "react"

export default function CreateRunPage() {
  return (
    <ProtectedRoute>
      <CreateRunPageContent />
    </ProtectedRoute>
  )
}

function CreateRunPageContent() {
  const router = useRouter()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  
  const handleRunCreated = (runId: string) => {
    // Run oluşturulduktan sonra runs sayfasına dön
    router.push('/runs')
  }

  return (
    <div className="flex min-h-screen h-screen overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed} 
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />
      
      {/* Main Content */}
      <div className="flex-1 overflow-hidden flex flex-col">
        <RunFormProvider onRunCreated={handleRunCreated} isEditMode={false}>
          <CreateRunLayout />
        </RunFormProvider>
      </div>
    </div>
  )
} 