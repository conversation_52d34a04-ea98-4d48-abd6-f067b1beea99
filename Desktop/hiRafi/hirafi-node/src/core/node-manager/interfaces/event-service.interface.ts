/**
 * Event Service Interface
 * 
 * Defines the contract for event handling services.
 * Manages Redis events, WebSocket events, and internal coordination.
 * 
 * NOTE: Test lifecycle events are now handled by Event Bus pattern.
 */

import { EventEmitter } from 'events';

/**
 * Event Service Interface
 * 
 * Manages WebSocket events and Redis events for coordination.
 * Test lifecycle events (test:claimed, test:started, etc.) are now 
 * handled by the Event Bus pattern.
 */
export interface IEventService extends EventEmitter {
  /**
   * Initialize the EventService and its connectors
   * @returns Promise that resolves when initialization is complete
   */
  initialize(): Promise<void>;

  /**
   * Check if events are setup
   * @returns True if events are configured
   */
  areEventsSetup(): boolean;

  /**
   * Setup all event handlers
   */
  setupAllEvents(): void;

  /**
   * Setup Redis event handlers
   */
  setupRedisEvents(): void;

  /**
   * Setup WebSocket event handlers
   */
  setupWebSocketEvents(): void;

  /**
   * @deprecated Test lifecycle events are now handled by Event Bus pattern
   * Setup test processor event handlers
   */
  setupTestProcessorEvents(): void;

  /**
   * Setup internal event handlers
   */
  setupInternalEventHandlers(): void;

  /**
   * Teardown all event handlers
   */
  teardownAllEvents(): void;

  /**
   * Teardown Redis event handlers
   */
  teardownRedisEvents(): void;

  /**
   * Teardown WebSocket event handlers
   */
  teardownWebSocketEvents(): void;

  /**
   * @deprecated Test lifecycle events are now handled by Event Bus pattern
   * Teardown test processor event handlers
   */
  teardownTestProcessorEvents(): void;

  /**
   * Teardown internal event handlers
   */
  teardownInternalEventHandlers(): void;

  /**
   * Emit test released event
   * @param testId Test ID that was released
   */
  emitTestReleased(testId: string): void;
}
