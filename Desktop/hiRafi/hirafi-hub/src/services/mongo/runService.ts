/**
 * Run Service
 * Test Runs için MongoDB servis fonksiyonları
 */

import { Collection } from 'mongodb';
import { db, ensureMongoDBConnection, runsCollection } from './dbConnection.js';
import { logger } from '../../utils/logger.js';
import { RunStatus, TestRun, AndroidEnvironmentSettings, WebEnvironmentSettings, EnvironmentSettings } from '../../models/test-types.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Yeni bir test run oluşturur
 * @param runData Run verileri
 * @returns Oluşturulan run ID'si ve başarı durumu
 */
export async function createRun(runData: Partial<TestRun>): Promise<{ success: boolean; runId?: string; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    // Run nesnesini oluştur
    const runId = runData.id || uuidv4();

    // userId kontrolü
    if (!runData.userId) {
      throw new Error('User ID is required for creating a run');
    }

    // Log platform value for debugging
    logger.info(`Creating run with platform: ${runData.platform || 'web'}`);

    // Log detailed environment settings if available
    if (runData.environment) {
      // For web platform, ensure platform field is set
      if (runData.platform === 'web') {
        // Type guard to check if environmentSettings is WebEnvironmentSettings
        const isWebSettings = (settings: any): settings is WebEnvironmentSettings => {
          return settings && (settings.platform === 'web' || !settings.platform);
        };

        // Ensure platform field is set in the environment object
        if (isWebSettings(runData.environment) && !runData.environment.platform) {
          (runData.environment as WebEnvironmentSettings).platform = 'web';
          logger.info('Added platform field to web environment object');
        }
      }
      // For Android platform, ensure all required fields are properly handled
      else if (runData.platform === 'android') {
        logger.info(`Android environment settings: ${JSON.stringify(runData.environment, null, 2)}`);

        // Check for web-specific environment settings that should not be present
        const webSpecificFields = ['browser', 'viewport', 'device', 'userAgent', 'geolocation'];
        const includedWebFields = webSpecificFields.filter(field => runData.environment && field in runData.environment);

        if (includedWebFields.length > 0) {
          logger.warn(`Warning: Android platform contains web-specific environment settings: ${includedWebFields.join(', ')}`);
        } else {
          logger.info('Verified: No web-specific environment settings included for Android platform');
        }

        // Type guard to check if environmentSettings is AndroidEnvironmentSettings
        const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
          return settings && (settings.platform === 'android' || !settings.platform);
        };

        // Convert to AndroidEnvironmentSettings if needed
        if (!isAndroidSettings(runData.environment)) {
          logger.info('Converting environment settings to AndroidEnvironmentSettings format');

          // Create a new Android environment settings object
          const androidSettings: AndroidEnvironmentSettings = {
            platform: 'android',
            sauceLabs: {
              username: '',
              accessKey: '',
              selectedDevices: []
            },
            testinium: {
              apiUrl: '',
              clientId: '',
              clientSecret: '',
              issuerUri: '',
              selectedDevices: []
            }
          };

          // Copy any existing properties that might be useful
          if (runData.environment.aiModel) {
            androidSettings.aiModel = runData.environment.aiModel;
          }

          // Replace the environment settings
          runData.environment = androidSettings;
        }

        // The appium object has been removed from the EnvironmentSettings interface
        // This code is kept for backward compatibility with existing data
        if ('appium' in runData.environment) {
          const appiumData = (runData.environment as any).appium;

          // If there are any important settings in the appium object, move them to sauceLabs
          if (appiumData && appiumData.useSauceLabs === true) {
            // Ensure sauceLabs object exists
            if (!runData.environment.sauceLabs) {
              runData.environment.sauceLabs = {
                username: '',
                accessKey: '',
                selectedDevices: []
              };
            }

            // Move any relevant settings from appium to sauceLabs
            if (appiumData.sauceLabsDeviceId && !('deviceId' in runData.environment.sauceLabs)) {
              (runData.environment.sauceLabs as any).deviceId = appiumData.sauceLabsDeviceId;
            }

            if (appiumData.sauceLabsAppId && !('appId' in runData.environment.sauceLabs)) {
              (runData.environment.sauceLabs as any).appId = appiumData.sauceLabsAppId;
            }
          }

          // Now remove the appium object as it's no longer needed
          delete (runData.environment as any).appium;
          logger.info('Removed redundant appium object from environment settings');
        }

        // Ensure platform field is set in the environment object
        if (isAndroidSettings(runData.environment)) {
          // We've already confirmed this is an AndroidEnvironmentSettings
          const androidSettings = runData.environment as AndroidEnvironmentSettings;
          if (!androidSettings.platform) {
            androidSettings.platform = 'android';
            logger.info('Added platform field to environment object');
          }
        }

        // Ensure SauceLabs settings are properly handled
        if (runData.environment.sauceLabs) {
          const sauceLabsSettings = runData.environment.sauceLabs;

          // Ensure required fields exist
          if (!sauceLabsSettings.username || !sauceLabsSettings.accessKey) {
            logger.warn('SauceLabs configuration is missing required username or accessKey');
            // Initialize with empty strings to satisfy TypeScript
            sauceLabsSettings.username = sauceLabsSettings.username || '';
            sauceLabsSettings.accessKey = sauceLabsSettings.accessKey || '';
          }

          // Log device selection details
          if (sauceLabsSettings.selectedDevices) {
            logger.info(`Selected devices count: ${sauceLabsSettings.selectedDevices.length}`);

            // Remove deviceIds field if it exists (it's redundant)
            if ('deviceIds' in sauceLabsSettings) {
              delete sauceLabsSettings.deviceIds;
              logger.info('Removed redundant deviceIds field from SauceLabs settings');
            }

            // Remove deviceId field if it exists (it's redundant)
            if ('deviceId' in sauceLabsSettings) {
              delete sauceLabsSettings.deviceId;
              logger.info('Removed redundant deviceId field from SauceLabs settings');
            }
          }

          // Log app selection details
          if ('appId' in sauceLabsSettings) {
            logger.info(`Selected app ID: ${(sauceLabsSettings as any).appId}`);
          }
        }

        // Log test distribution strategy if available
        if (isAndroidSettings(runData.environment) && runData.environment.testDistribution) {
          logger.info(`Test distribution strategy: ${runData.environment.testDistribution.strategy}`);
        }
      }
    }

    // Verify report settings for Android platform
    if (runData.platform === 'android' && runData.reportSettings) {
      logger.info(`Android report settings: ${JSON.stringify(runData.reportSettings, null, 2)}`);

      // Check for web-specific report settings that should not be present
      const webSpecificFields = ['pageMetrics', 'networkData', 'tracingData', 'accessibilityData'];
      const includedWebFields = webSpecificFields.filter(field => runData.reportSettings && field in runData.reportSettings);

      if (includedWebFields.length > 0) {
        logger.warn(`Warning: Android platform contains web-specific report settings: ${includedWebFields.join(', ')}`);

        // Remove web-specific fields from report settings
        webSpecificFields.forEach(field => {
          if (runData.reportSettings && field in runData.reportSettings) {
            delete (runData.reportSettings as any)[field];
            logger.info(`Removed web-specific field '${field}' from Android report settings`);
          }
        });
      } else {
        logger.info('Verified: No web-specific report settings included for Android platform');
      }
    }

    const newRun: TestRun = {
      id: runId,
      name: runData.name || `Run ${new Date().toISOString()}`,
      description: runData.description,
      status: RunStatus.CREATED,
      createdAt: new Date(),
      userId: runData.userId,
      companyId: runData.companyId || null,
      teamId: runData.teamId || null,
      platform: runData.platform || 'web', // Default to web if not specified
      scenarioIds: runData.scenarioIds || [],
      environment: runData.environment,
      reportSettings: runData.reportSettings,
      lastExecutionId: runData.lastExecutionId,
      tags: runData.tags || [],
      options: runData.options || {}
    };

    // MongoDB'ye ekle
    await runsCollection.insertOne(newRun);

    logger.info(`Created new test run with ID: ${runId}`);

    return {
      success: true,
      runId: runId
    };
  } catch (error: any) {
    logger.error(`Error creating test run: ${error.message}`);
    return {
      success: false,
      message: `Failed to create test run: ${error.message}`
    };
  }
}

/**
 * Run'a test ID'lerini ekler
 * @param runId Run ID'si
 * @param testIds Test ID'leri
 * @returns Başarı durumu
 */
export async function addTestsToRun(
  runId: string,
  testIds: string[]
): Promise<{ success: boolean; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    // Only update the status to QUEUED without adding testIds
    const result = await runsCollection.updateOne(
      { id: runId },
      {
        $set: { status: RunStatus.QUEUED }
      }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: `Run with ID ${runId} not found`
      };
    }

    logger.info(`Updated run ${runId} to QUEUED status - testIds are no longer stored`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error updating run status: ${error.message}`);
    return {
      success: false,
      message: `Failed to update run status: ${error.message}`
    };
  }
}


/**
 * Run'a bir rapor ekler
 * @param runId Run ID'si
 * @param reportId Rapor ID'si
 * @returns Başarı durumu
 */
export async function addReportToRun(
  runId: string,
  reportId: string
): Promise<{ success: boolean; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    const result = await runsCollection.updateOne(
      { id: runId },
      { $addToSet: { reportIds: reportId } }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: `Run with ID ${runId} not found`
      };
    }

    logger.info(`Added report ${reportId} to run ${runId}`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error adding report to run: ${error.message}`);
    return {
      success: false,
      message: `Failed to add report to run: ${error.message}`
    };
  }
}

/**
 * Run'ı ID'ye göre getirir ve son run raporundan durum bilgilerini günceller
 * @param runId Run ID'si
 * @returns Run ve başarı durumu
 */
export async function getRunById(
  runId: string
): Promise<{ success: boolean; run?: TestRun; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    const run = await runsCollection.findOne({ id: runId });

    if (!run) {
      return {
        success: false,
        message: `Run with ID ${runId} not found`
      };
    }

    // Run raporlarından durum bilgilerini güncelle
    try {
      // Always check for reports, even for 'created' runs, as they might have been executed
      let shouldCheckReport = true;
      
      if (run.status === 'created') {
        logger.debug(`RunService: Checking for reports even for created run ${run.id} - it might have been executed`);
      }
      
      if (shouldCheckReport) {
        // For non-created runs, try to get report data
        const { getRunReportByRunId } = await import('./atomicReportService.js');
        const { calculateTestResults } = await import('../../models/test-types.js');
        
        const reportResult = await getRunReportByRunId(run.id);

        if (reportResult.success && reportResult.report) {
          // Run durumunu ve test sonuçlarını run raporundan al
          run.status = reportResult.report.status;

          // Calculate test results from scenarioStatuses instead of using raw testResults
          if (reportResult.report.scenarioStatuses && reportResult.report.scenarioStatuses.length > 0) {
            // Create a temporary Run object with the required fields
            const tempRun = {
              id: run.id,
              name: run.name,
              status: run.status,
              createdAt: run.createdAt,
              userId: run.userId,
              scenarioIds: run.scenarioIds,
              scenarioStatuses: reportResult.report.scenarioStatuses
            };
            run.testResults = calculateTestResults(tempRun);
          } else {
            // Fallback to raw testResults if no scenarioStatuses
            run.testResults = reportResult.report.testResults;
          }
        } else {
          // If report not found for non-created runs, provide fallback test results
          const scenarioCount = run.scenarioIds?.length || 0;
          run.testResults = {
            total: scenarioCount,
            completed: 0,
            failed: 0,
            stopped: 0,
            running: 0,
            queued: 0
          };
          logger.debug(`RunService: No report found for run ${run.id} (status: ${run.status}), using fallback test results`);
        }
      }
    } catch (reportError) {
      // Run raporlarından bilgi alınamadıysa, mevcut run bilgilerini kullan
      logger.warn(`Could not get run report information for run ${run.id}: ${reportError}. Using existing run data.`);
    }

    return {
      success: true,
      run: run as unknown as TestRun
    };
  } catch (error: any) {
    logger.error(`Error getting run by ID: ${error.message}`);
    return {
      success: false,
      message: `Failed to get run: ${error.message}`
    };
  }
}

/**
 * Kullanıcının run'larını getirir
 * @param userId Kullanıcı ID'si
 * @param limit Limit (varsayılan: 50)
 * @param skip Atlama sayısı (varsayılan: 0)
 * @param filter Filtre seçenekleri
 * @returns Run'lar ve başarı durumu
 * @deprecated Use getRunsByTeamId instead to get runs by team
 */
export async function getRunsByUserId(
  userId: string,
  limit: number = 50,
  skip: number = 0,
  filter?: {
    status?: RunStatus | RunStatus[];
    startDate?: Date;
    endDate?: Date;
    tags?: string[];
    search?: string;
  }
): Promise<{ success: boolean; runs?: TestRun[]; total?: number; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    // Temel filtre
    const query: any = { userId };

    // İlave filtreler
    if (filter) {
      // Status filtering - We'll apply this after getting run reports
      // Store the status filter for later use
      const statusFilter = filter.status;

      // Don't apply status filter to database query since we need to update status from reports first
      // if (filter.status !== undefined && filter.status !== null) {
      //   if (Array.isArray(filter.status)) {
      //     // Make sure array is not empty and has valid values
      //     const validStatuses = filter.status.filter(s => s !== undefined && s !== null);
      //     if (validStatuses.length > 0) {
      //       query.status = { $in: validStatuses };
      //     }
      //   } else {
      //     // Single status value
      //     query.status = filter.status;
      //   }
      // }

      // Date range filtering with null checks
      if (filter.startDate || filter.endDate) {
        query.createdAt = {};

        if (filter.startDate instanceof Date) {
          query.createdAt.$gte = filter.startDate;
        }

        if (filter.endDate instanceof Date) {
          query.createdAt.$lte = filter.endDate;
        }
      }

      // Tags filtering with null checks
      if (filter.tags && Array.isArray(filter.tags) && filter.tags.length > 0) {
        const validTags = filter.tags.filter(tag => typeof tag === 'string' && tag.trim().length > 0);
        if (validTags.length > 0) {
          query.tags = { $in: validTags };
        }
      }

      // Search filtering with null checks
      if (filter.search && typeof filter.search === 'string' && filter.search.trim().length > 0) {
        const searchTerm = filter.search.trim();
        query.$or = [
          { name: { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } }
        ];
      }
    }

    logger.debug(`Executing run query with filters: ${JSON.stringify(query)}`);

    // Toplam sayıyı al
    const total = await runsCollection.countDocuments(query);

    // Run'ları getir
    const runs = await runsCollection
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Run raporlarından durum bilgilerini güncelle - BATCH OPTIMIZATION
    try {
      // Import utility functions
      const { getMultipleRunReportsByRunIds } = await import('./atomicReportService.js');
      const { calculateTestResults } = await import('../../models/test-types.js');

      // Check reports for all runs, including created ones (they might have been executed)
      // Batch fetch reports for all runs (SINGLE QUERY instead of N queries!)
      if (runs.length > 0) {
        const allRunIds = runs.map(run => run.id);
        const batchReportsResult = await getMultipleRunReportsByRunIds(allRunIds);

        if (batchReportsResult.success && batchReportsResult.reports) {
          const reportsByRunId = batchReportsResult.reports;

          // Process each run with its report data
          for (const run of runs) {
            const report = reportsByRunId[run.id];

            if (report) {
              // Update run status and test results from report
              run.status = report.status;

              // Add last run date from report
              if (report.createdAt) {
                run.lastRunDate = report.createdAt;
              }

              if (report.scenarioStatuses && report.scenarioStatuses.length > 0) {
                const tempRun = {
                  id: run.id,
                  name: run.name,
                  status: run.status,
                  createdAt: run.createdAt,
                  userId: run.userId,
                  scenarioIds: run.scenarioIds,
                  scenarioStatuses: report.scenarioStatuses
                };
                run.testResults = calculateTestResults(tempRun);
              } else {
                run.testResults = report.testResults || {
                  total: run.scenarioIds?.length || 0,
                  completed: 0,
                  failed: 0,
                  stopped: 0,
                  running: 0,
                  queued: 0
                };
              }
              logger.debug(`RunService: Updated run ${run.id} from batch report data`);
            } else {
              // No report found for this run, provide fallback
              const scenarioCount = run.scenarioIds?.length || 0;
              run.testResults = {
                total: scenarioCount,
                completed: 0,
                failed: 0,
                stopped: 0,
                running: 0,
                queued: 0
              };
              logger.debug(`RunService: No report found for run ${run.id}, using fallback test results`);
            }
          }

          logger.info(`RunService: Batch processed ${runs.length} runs with ${Object.keys(reportsByRunId).length} reports found`);
        } else {
          logger.warn(`RunService: Batch report fetch failed, using fallback for ${runs.length} runs`);
          // Provide fallback for all runs
          for (const run of runs) {
            const scenarioCount = run.scenarioIds?.length || 0;
            run.testResults = {
              total: scenarioCount,
              completed: 0,
              failed: 0,
              stopped: 0,
              running: 0,
              queued: 0
            };
          }
        }
      }

      logger.info(`RunService: Optimized processing completed - checked reports for all ${runs.length} runs`);
    } catch (reportError) {
      // Run raporlarından bilgi alınamadıysa, mevcut run bilgilerini kullan
      logger.warn(`Could not get run report information for runs: ${reportError}. Using existing run data.`);
    }

    // Apply status filtering after updating status from reports
    let filteredRuns = runs;
    if (filter && filter.status !== undefined && filter.status !== null) {
      if (Array.isArray(filter.status)) {
        // Make sure array is not empty and has valid values
        const validStatuses = filter.status.filter(s => s !== undefined && s !== null);
        if (validStatuses.length > 0) {
          filteredRuns = runs.filter(run => validStatuses.includes(run.status));
          logger.debug(`Filtered runs by status array ${JSON.stringify(validStatuses)}: ${filteredRuns.length} of ${runs.length} runs match`);
        }
      } else {
        // Single status value
        filteredRuns = runs.filter(run => run.status === filter.status);
        logger.debug(`Filtered runs by status "${filter.status}": ${filteredRuns.length} of ${runs.length} runs match`);
      }
    }

    return {
      success: true,
      runs: filteredRuns as unknown as TestRun[],
      total: filteredRuns.length
    };
  } catch (error: any) {
    logger.error(`Error getting runs by user ID: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get runs: ${error.message}`
    };
  }
}

/**
 * Takımın run'larını getirir
 * @param teamId Takım ID'si
 * @param limit Limit (varsayılan: 50)
 * @param skip Atlama sayısı (varsayılan: 0)
 * @param filter Filtre seçenekleri
 * @returns Run'lar ve başarı durumu
 */
export async function getRunsByTeamId(
  teamId: string,
  limit: number = 50,
  skip: number = 0,
  filter?: {
    status?: RunStatus | RunStatus[];
    startDate?: Date;
    endDate?: Date;
    tags?: string[];
    search?: string;
    userId?: string; // Opsiyonel olarak belirli bir kullanıcının run'larını filtreleme
    platform?: string; // Platform filtresi
  },
  sortOptions?: any // Sorting parametreleri
): Promise<{ success: boolean; runs?: TestRun[]; total?: number; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    // Temel filtre - teamId'ye göre filtrele
    const query: any = { teamId };

    // İlave filtreler
    if (filter) {
      // Belirli bir kullanıcının run'larını filtreleme
      if (filter.userId) {
        query.userId = filter.userId;
      }

      // Status filtering - We'll apply this after getting run reports
      // Store the status filter for later use
      const statusFilter = filter.status;

      // Don't apply status filter to database query since we need to update status from reports first
      // if (filter.status !== undefined && filter.status !== null) {
      //   if (Array.isArray(filter.status)) {
      //     // Make sure array is not empty and has valid values
      //     const validStatuses = filter.status.filter(s => s !== undefined && s !== null);
      //     if (validStatuses.length > 0) {
      //       query.status = { $in: validStatuses };
      //     }
      //   } else {
      //     // Single status value
      //     query.status = filter.status;
      //   }
      // }

      // Date range filtering with null checks
      if (filter.startDate || filter.endDate) {
        query.createdAt = {};

        if (filter.startDate instanceof Date) {
          query.createdAt.$gte = filter.startDate;
        }

        if (filter.endDate instanceof Date) {
          query.createdAt.$lte = filter.endDate;
        }
      }

      // Tags filtering with null checks
      if (filter.tags && Array.isArray(filter.tags) && filter.tags.length > 0) {
        const validTags = filter.tags.filter(tag => typeof tag === 'string' && tag.trim().length > 0);
        if (validTags.length > 0) {
          query.tags = { $in: validTags };
        }
      }

      // Search filtering with null checks
      if (filter.search && typeof filter.search === 'string' && filter.search.trim().length > 0) {
        const searchTerm = filter.search.trim();
        query.$or = [
          { name: { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } }
        ];
      }

      // Platform filtering
      if (filter.platform && typeof filter.platform === 'string') {
        query.platform = filter.platform;
        logger.debug(`Adding platform filter: ${filter.platform}`);
      }
    }

    logger.debug(`Executing team run query with filters: ${JSON.stringify(query)}`);

    // Toplam sayıyı al
    const total = await runsCollection.countDocuments(query);

    // Run'ları getir - sorting parametreleri ile
    // For priority sorting, we need to get all data first, then sort in JavaScript
    const isPrioritySort = sortOptions && sortOptions.priority === true;

    let runs;
    if (isPrioritySort) {
      // For priority sorting, get all runs without MongoDB sorting, then sort in JavaScript
      runs = await runsCollection
        .find(query)
        .toArray();
    } else {
      // Use MongoDB sorting for other sort types
      const sortCriteria = sortOptions || { createdAt: -1 }; // Default sort
      runs = await runsCollection
        .find(query)
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .toArray();
    }

    // Run raporlarından durum bilgilerini güncelle - BATCH OPTIMIZATION
    try {
      // Import utility functions
      const { getMultipleRunReportsByRunIds } = await import('./atomicReportService.js');
      const { calculateTestResults } = await import('../../models/test-types.js');

      // Check reports for all runs, including created ones (they might have been executed)
      // Batch fetch reports for all runs (SINGLE QUERY instead of N queries!)
      if (runs.length > 0) {
        const allRunIds = runs.map(run => run.id);
        const batchReportsResult = await getMultipleRunReportsByRunIds(allRunIds);

        if (batchReportsResult.success && batchReportsResult.reports) {
          const reportsByRunId = batchReportsResult.reports;

          // Process each run with its report data
          for (const run of runs) {
            const report = reportsByRunId[run.id];

            if (report) {
              // Update run status and test results from report
              run.status = report.status;

              // Add last run date from report
              if (report.createdAt) {
                run.lastRunDate = report.createdAt;
              }

              if (report.scenarioStatuses && report.scenarioStatuses.length > 0) {
                const tempRun = {
                  id: run.id,
                  name: run.name,
                  status: run.status,
                  createdAt: run.createdAt,
                  userId: run.userId,
                  scenarioIds: run.scenarioIds,
                  scenarioStatuses: report.scenarioStatuses
                };
                run.testResults = calculateTestResults(tempRun);
              } else {
                run.testResults = report.testResults || {
                  total: run.scenarioIds?.length || 0,
                  completed: 0,
                  failed: 0,
                  stopped: 0,
                  running: 0,
                  queued: 0
                };
              }
              logger.debug(`RunService: Updated run ${run.id} from batch report data`);
            } else {
              // No report found for this run, provide fallback
              const scenarioCount = run.scenarioIds?.length || 0;
              run.testResults = {
                total: scenarioCount,
                completed: 0,
                failed: 0,
                stopped: 0,
                running: 0,
                queued: 0
              };
              logger.debug(`RunService: No report found for run ${run.id}, using fallback test results`);
            }
          }

          logger.info(`RunService: Batch processed ${runs.length} runs with ${Object.keys(reportsByRunId).length} reports found`);
        } else {
          logger.warn(`RunService: Batch report fetch failed, using fallback for ${runs.length} runs`);
          // Provide fallback for all runs
          for (const run of runs) {
            const scenarioCount = run.scenarioIds?.length || 0;
            run.testResults = {
              total: scenarioCount,
              completed: 0,
              failed: 0,
              stopped: 0,
              running: 0,
              queued: 0
            };
          }
        }
      }

      logger.info(`RunService: Optimized processing completed - checked reports for all ${runs.length} runs`);
    } catch (reportError) {
      // Run raporlarından bilgi alınamadıysa, mevcut run bilgilerini kullan
      logger.warn(`Could not get run report information for runs: ${reportError}. Using existing run data.`);
    }

    // Apply status filtering after updating status from reports
    let filteredRuns = runs;
    if (filter && filter.status !== undefined && filter.status !== null) {
      if (Array.isArray(filter.status)) {
        // Make sure array is not empty and has valid values
        const validStatuses = filter.status.filter(s => s !== undefined && s !== null);
        if (validStatuses.length > 0) {
          filteredRuns = runs.filter(run => validStatuses.includes(run.status));
          logger.debug(`Filtered runs by status array ${JSON.stringify(validStatuses)}: ${filteredRuns.length} of ${runs.length} runs match`);
        }
      } else {
        // Single status value
        filteredRuns = runs.filter(run => run.status === filter.status);
        logger.debug(`Filtered runs by status "${filter.status}": ${filteredRuns.length} of ${runs.length} runs match`);
      }
    }

    // Apply priority sorting: running tests first, then by most recent execution time
    if (isPrioritySort) {
      filteredRuns.sort((a, b) => {
        // Primary sort: Running status first
        const aIsRunning = a.status === 'running' || a.status === 'queued';
        const bIsRunning = b.status === 'running' || b.status === 'queued';

        if (aIsRunning && !bIsRunning) return -1;
        if (!aIsRunning && bIsRunning) return 1;

        // Secondary sort: Most recent execution time (use startedAt if available, otherwise createdAt)
        const aTime = a.startedAt || a.createdAt;
        const bTime = b.startedAt || b.createdAt;

        // Convert to timestamps for comparison (most recent first)
        const aTimestamp = aTime instanceof Date ? aTime.getTime() : new Date(aTime).getTime();
        const bTimestamp = bTime instanceof Date ? bTime.getTime() : new Date(bTime).getTime();

        return bTimestamp - aTimestamp; // Descending order (most recent first)
      });

      logger.debug(`Applied priority sorting: running tests first, then by most recent execution time`);

      // Apply pagination after sorting for priority sort
      const totalBeforePagination = filteredRuns.length;
      filteredRuns = filteredRuns.slice(skip, skip + limit);

      logger.debug(`Applied pagination for priority sort: ${filteredRuns.length} runs returned from ${totalBeforePagination} total`);
    }

    return {
      success: true,
      runs: filteredRuns as unknown as TestRun[],
      total: isPrioritySort ? total : filteredRuns.length
    };
  } catch (error: any) {
    logger.error(`Error getting runs by team ID: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get runs: ${error.message}`
    };
  }
}

/**
 * Run'ı siler
 * @param runId Run ID'si
 * @returns Başarı durumu
 */
export async function deleteRun(
  runId: string
): Promise<{ success: boolean; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    const result = await runsCollection.deleteOne({ id: runId });

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: `Run with ID ${runId} not found`
      };
    }

    logger.info(`Deleted run ${runId}`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error deleting run: ${error.message}`);
    return {
      success: false,
      message: `Failed to delete run: ${error.message}`
    };
  }
}

/**
 * Run'ı günceller
 * @param runId Run ID'si
 * @param updateData Güncellenecek veriler
 * @returns Başarı durumu
 */
export async function updateRun(
  runId: string,
  updateData: Partial<TestRun>
): Promise<{ success: boolean; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    // Gelen verileri loglayalım
    logger.info(`Updating run ${runId} with data: ${JSON.stringify(updateData)}`);

    // Güncellenemeyecek alanları kaldır
    const safeUpdateData = { ...updateData };
    delete safeUpdateData.id;
    delete safeUpdateData.createdAt;
    delete safeUpdateData.userId;

    // Tags alanı var mı kontrol et ve temizle
    if (safeUpdateData.tags !== undefined) {
      // Boş dizi yerine null gelirse dizi olarak ayarla
      if (safeUpdateData.tags === null) {
        safeUpdateData.tags = [];
      }
      // String gelirse dizi içine al
      else if (typeof safeUpdateData.tags === 'string') {
        safeUpdateData.tags = [safeUpdateData.tags];
      }
      // Eğer zaten dizi ise olduğu gibi bırak
      logger.info(`Tags after processing: ${JSON.stringify(safeUpdateData.tags)}`);
    }

    // Perform update with retry logic for write conflicts
    let result;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        result = await runsCollection.updateOne(
          { id: runId },
          { $set: safeUpdateData }
        );
        break; // Success, exit retry loop
      } catch (error: any) {
        retryCount++;
        if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
          // Duplicate key error or write conflict, or max retries reached
          logger.error(`Run update failed for runId ${runId} after ${retryCount} attempts: ${error.message}`);
          throw error;
        }
        // Wait before retry with exponential backoff
        const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
        logger.warn(`Retrying run update for runId ${runId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!result || result.matchedCount === 0) {
      return {
        success: false,
        message: `Run with ID ${runId} not found`
      };
    }

    logger.info(`Updated run ${runId} successfully. Modified count: ${result.modifiedCount}`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error updating run: ${error.message}`);
    return {
      success: false,
      message: `Failed to update run: ${error.message}`
    };
  }
}

/**
 * Run'ın tamamlanma zamanını günceller
 * @param runId Run ID
 * @param completionTime Tamamlanma zamanı
 * @returns İşlem sonucu
 */
export async function updateRunCompletionTime(runId: string, completionTime: Date): Promise<{ success: boolean, message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    // Update completion time with retry logic for write conflicts
    let result;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        result = await runsCollection.updateOne(
          { id: runId },
          { $set: { completedAt: completionTime } }
        );
        break; // Success, exit retry loop
      } catch (error: any) {
        retryCount++;
        if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
          // Duplicate key error or write conflict, or max retries reached
          logger.error(`Run completion time update failed for runId ${runId} after ${retryCount} attempts: ${error.message}`);
          throw error;
        }
        // Wait before retry with exponential backoff
        const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
        logger.warn(`Retrying run completion time update for runId ${runId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!result || result.matchedCount === 0) {
      return {
        success: false,
        message: `Run with ID ${runId} not found`
      };
    }

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`MongoDB - Error updating run completion time: ${error.message}`);
    return {
      success: false,
      message: error.message
    };
  }
}

/**
 * Yeni bir execution ID oluşturur
 * @returns Oluşturulan benzersiz execution ID
 */
export function generateExecutionId(): string {
  // "runexec_" prefix ile execution ID oluştur
  const executionId = `runexec_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  logger.debug(`Generated new execution ID: ${executionId}`);
  return executionId;
}

/**
 * Aktif durumda olan (running veya queued) run'ları getirir
 * @returns Aktif run'lar ve başarı durumu
 */
export async function getActiveRuns(): Promise<{ success: boolean; runs?: TestRun[]; message?: string }> {
  try {
    if (!runsCollection) {
      return { success: false, message: 'runsCollection not initialized' };
    }

    // Aktif durumda olan run'ları getir (running veya queued)
    const runs = await runsCollection
      .find({
        status: { $in: ['running', 'queued'] }
      })
      .sort({ createdAt: -1 })
      .toArray();

    logger.debug(`Found ${runs.length} active runs`);

    return {
      success: true,
      runs: runs as unknown as TestRun[]
    };
  } catch (error: any) {
    logger.error(`Error getting active runs: ${error.message}`);
    return {
      success: false,
      message: `Failed to get active runs: ${error.message}`
    };
  }
}

/**
 * Get runs collection
 * @returns Runs collection
 */
export async function getRunsCollection(): Promise<Collection> {
  if (!runsCollection) {
    throw new Error('runsCollection not initialized');
  }
  return runsCollection;
}

