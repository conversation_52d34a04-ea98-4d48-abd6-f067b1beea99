/**
 * Device Provider Registry Service
 * Implements registry pattern for device providers with DI compliance
 */

import {
  IDeviceProviderRegistry,
  DeviceProviderRegistration,
  DeviceProviderDependencies,
  DeviceProviderFactory
} from '../interfaces/device-provider-registry.interface.js';
import { ProviderConfiguration, DeviceProviderConfigService } from './device-provider-config.service.js';
import { ILoggerService } from '../../../../utils/logger-service.js';
import { SauceLabsDeviceManager } from '../sauce-labs-device-manager.js';
import { TestiniumDeviceManager } from '../testinium-device-manager.js';

/**
 * Device Provider Registry Service
 * Manages registration and creation of device providers using registry pattern
 */
export class DeviceProviderRegistryService implements IDeviceProviderRegistry {
  private providers = new Map<string, DeviceProviderRegistration>();
  private logger: ILoggerService;
  private configService: DeviceProviderConfigService;

  constructor(logger: ILoggerService, configService: DeviceProviderConfigService) {
    this.logger = logger;
    this.configService = configService;
    this.registerBuiltInProviders();
  }

  /**
   * Register a device provider
   */
  registerProvider(registration: DeviceProviderRegistration): void {
    if (this.providers.has(registration.name)) {
      this.logger.warn(`DeviceProviderRegistry: Provider '${registration.name}' is already registered, overwriting`);
    }

    this.providers.set(registration.name, registration);
    this.logger.info(`DeviceProviderRegistry: Registered provider '${registration.name}'`);
  }

  /**
   * Unregister a device provider
   */
  unregisterProvider(name: string): void {
    if (this.providers.delete(name)) {
      this.logger.info(`DeviceProviderRegistry: Unregistered provider '${name}'`);
    } else {
      this.logger.warn(`DeviceProviderRegistry: Provider '${name}' was not registered`);
    }
  }

  /**
   * Check if a provider is registered
   */
  isProviderRegistered(name: string): boolean {
    return this.providers.has(name);
  }

  /**
   * Get all registered provider names
   */
  getRegisteredProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Create a device manager for the specified provider
   */
  createDeviceManager(
    providerName: string,
    config: ProviderConfiguration,
    dependencies: DeviceProviderDependencies
  ): import('../device-manager.interface.js').IDeviceManager {
    const registration = this.providers.get(providerName);
    if (!registration) {
      throw new Error(`DeviceProviderRegistry: Provider '${providerName}' is not registered`);
    }

    this.logger.info(`DeviceProviderRegistry: Creating device manager for provider '${providerName}'`);

    try {
      return registration.factory(config, dependencies);
    } catch (error: any) {
      this.logger.error(`DeviceProviderRegistry: Failed to create device manager for provider '${providerName}': ${error.message}`);
      throw error;
    }
  }

  /**
   * Get provider registration information
   */
  getProviderRegistration(name: string): DeviceProviderRegistration | undefined {
    return this.providers.get(name);
  }

  /**
   * Validate provider configuration
   */
  validateProviderConfig(providerName: string, config: ProviderConfiguration): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    if (!this.isProviderRegistered(providerName)) {
      return {
        valid: false,
        errors: [`Provider '${providerName}' is not registered`],
        warnings: []
      };
    }

    return this.configService.validateConfiguration(config);
  }

  /**
   * Register built-in providers
   */
  private registerBuiltInProviders(): void {
    // Register SauceLabs provider
    this.registerProvider({
      name: 'sauceLabs',
      factory: this.createSauceLabsDeviceManager.bind(this),
      supportedCapabilities: ['appium:automationName', 'appium:deviceName', 'appium:platformVersion', 'sauce:options'],
      description: 'SauceLabs device provider with Appium 2 support'
    });

    // Register Testinium provider
    this.registerProvider({
      name: 'testinium',
      factory: this.createTestiniumDeviceManager.bind(this),
      supportedCapabilities: ['appium:automationName', 'appium:deviceName', 'appium:platformVersion'],
      description: 'Testinium device provider with DevicePark SDK'
    });
  }

  /**
   * Create SauceLabs device manager
   */
  private createSauceLabsDeviceManager(
    config: ProviderConfiguration,
    dependencies: DeviceProviderDependencies
  ): import('../device-manager.interface.js').IDeviceManager {
    return new SauceLabsDeviceManager(
      config.config as import('../device-manager.interface.js').SauceLabsConfig,
      config.capabilities,
      dependencies.logger
    );
  }

  /**
   * Create Testinium device manager
   */
  private createTestiniumDeviceManager(
    config: ProviderConfiguration,
    dependencies: DeviceProviderDependencies
  ): import('../device-manager.interface.js').IDeviceManager {
    return new TestiniumDeviceManager(
      config.config as import('../testinium-device-manager.js').TestiniumConfig,
      config.capabilities,
      dependencies.logger
    );
  }
}
