/**
 * Common connector interfaces for all communication methods
 * These interfaces standardize the communication layer
 */

/**
 * Base connector interface for all communication methods
 */
export interface MessageConnector {
  // Connection management
  initialize(config?: any): Promise<boolean>;
  close(): Promise<void>;
  isConnected(): boolean;
  
  // Messaging
  sendMessage(topic: string, data: any): Promise<boolean>;
}

/**
 * Queue-based connector interface (RabbitMQ, etc.)
 */
export interface QueueConnector extends MessageConnector {
  // Queue operations
  createQueue(queueName: string, options?: any): Promise<void>;
  purgeQueue(queueName: string): Promise<void>;
  
  // Consumption
  consumeQueue(queueName: string, callback: (message: any) => void): Promise<void>;
  
  // Prefetch control for load balancing
  setPrefetchCount(count: number): Promise<void>;
  
  // Test operations
  sendTestToNode(nodeId: string, test: any): Promise<boolean>;
  sendStopTestToNode(nodeId: string, testId: string): Promise<boolean>;
}

/**
 * Real-time connector interface (WebSocket, etc.)
 */
export interface RealTimeConnector extends MessageConnector {
  // Broadcast operations
  broadcastToAll(event: string, data: any): void;
  broadcastToGroup(group: string, event: string, data: any): void;
  
  // Client management
  getConnectedClients(): Map<string, any>;
  disconnectClient(clientId: string): boolean;
  isClientConnected(clientId: string): boolean;
  
  // Group management
  addClientToGroup(clientId: string, group: string): void;
  removeClientFromGroup(clientId: string, group: string): void;
  
  // Node operations
  sendTestToNode(nodeId: string, test: any): boolean;
  sendStopTestToNode(nodeId: string, testId: string): boolean;
}

/**
 * Error types for connectors
 */
export class ConnectorError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConnectorError';
  }
}

export class ConnectionError extends ConnectorError {
  constructor(message: string) {
    super(message);
    this.name = 'ConnectionError';
  }
}

export class MessageError extends ConnectorError {
  constructor(message: string) {
    super(message);
    this.name = 'MessageError';
  }
} 