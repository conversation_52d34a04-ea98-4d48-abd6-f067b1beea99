/**
 * User API
 *
 * Kullanıcı ile ilgili API isteklerini yönetir.
 */

import { get, post, put, del } from './fetch-wrapper';
import { USER_ENDPOINTS } from './constants';

/**
 * <PERSON>llanıcı profilini getirir
 */
export async function getUserProfile() {
  return get(USER_ENDPOINTS.PROFILE);
}

/**
 * <PERSON>llanıcı profilini günceller
 *
 * @param profileData Güncellenecek profil verileri
 */
export async function updateUserProfile(profileData: {
  name?: string;
  email?: string;
  jobTitle?: string;
  company?: string;
  phone?: string;
  avatar?: string;
}) {
  return put(USER_ENDPOINTS.PROFILE, profileData);
}

/**
 * Kullanıcı şifresini değiştirir
 *
 * @param passwordData Şifre değiştirme verileri
 */
export async function changePassword(passwordData: {
  currentPassword: string;
  newPassword: string;
}) {
  return put(USER_ENDPOINTS.CHANGE_PASSWORD, passwordData);
}

/**
 * <PERSON>llanıcı bildirim ayarlarını getirir
 */
export async function getNotificationSettings() {
  return get(USER_ENDPOINTS.NOTIFICATION_SETTINGS);
}

/**
 * Kullanıcı bildirim ayarlarını günceller
 *
 * @param settings Güncellenecek bildirim ayarları
 */
export async function updateNotificationSettings(settings: any) {
  return put(USER_ENDPOINTS.NOTIFICATION_SETTINGS, settings);
}
