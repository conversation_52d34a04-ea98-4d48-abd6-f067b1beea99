/**
 * State Manager Implementation
 * 
 * Manages test execution state with atomic transitions and race condition prevention.
 * Replaces the singleton AtomicStateManager with a dependency-injected implementation.
 */

import { TestExecutionState } from '../../node-manager/types/node-manager-types.js';
import type {
  IStateManager,
  StateTransitionCallback,
  StateTransitionResult,
  StateManagerConfig
} from '../interfaces/state-manager.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../utils/logger-service.js';

/**
 * State transition history entry
 */
interface StateTransitionHistory {
  timestamp: Date;
  fromState: TestExecutionState;
  toState: TestExecutionState;
  testId?: string;
  success: boolean;
  reason?: string;
}

/**
 * State Manager Implementation
 * 
 * Provides atomic state management for test execution with proper
 * race condition prevention and transition tracking.
 */
export class StateManager implements IStateManager {
  private currentState: TestExecutionState = TestExecutionState.IDLE;
  private currentTestId: string | null = null;
  private config: StateManagerConfig | null = null;
  private logger: ILoggerService;
  
  // State transition tracking
  private transitionCallbacks: StateTransitionCallback[] = [];
  private transitionHistory: StateTransitionHistory[] = [];
  private readonly maxHistorySize = 100;
  
  // Enhanced Synchronization - Atomic Mutex Implementation with Race Condition Prevention
  private transitionMutex: Promise<void> = Promise.resolve();
  private transitionQueue: Array<{
    operation: () => Promise<StateTransitionResult>;
    resolve: (result: StateTransitionResult) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
    operationId: string;
    timestamp: number;
    priority: 'normal' | 'force';
  }> = [];
  private isProcessingQueue = false;
  private readonly mutexTimeout = 30000; // 30 seconds timeout
  private operationCounter = 0;
  private readonly maxQueueSize = 100; // Prevent memory leaks from excessive queuing
  private queueProcessingLock = false; // Additional lock to prevent queue processing race conditions

  constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('StateManager');
  }

  /**
   * Initialize the state manager
   */
  async initialize(config: StateManagerConfig): Promise<void> {
    this.config = config;
    this.currentState = TestExecutionState.IDLE;
    this.currentTestId = null;
    this.transitionHistory = [];

    if (config.enableStateLogging) {
      this.logger.info(`StateManager: Initialized for node ${config.nodeId}`);
    }
  }

  /**
   * Get the current execution state
   */
  getCurrentState(): TestExecutionState {
    return this.currentState;
  }

  /**
   * Get the current test ID being processed
   */
  getCurrentTestId(): string | null {
    return this.currentTestId;
  }

  /**
   * Atomically transition from one state to another with proper mutex
   */
  async tryTransitionState(
    fromState: TestExecutionState,
    toState: TestExecutionState,
    testId?: string
  ): Promise<StateTransitionResult> {
    return this.executeWithMutex(async () => {
      return this.performStateTransition(fromState, toState, testId);
    });
  }



  /**
   * Force transition to a specific state (use with caution) - async version
   */
  async forceTransitionState(
    toState: TestExecutionState,
    testId?: string,
    reason?: string
  ): Promise<StateTransitionResult> {
    return this.executeWithMutex(async () => {
      return this.performForceTransition(toState, testId, reason);
    }, 'force'); // Use force priority to jump to front of queue
  }



  /**
   * Check if the manager can accept a new test
   */
  canAcceptNewTest(): boolean {
    return this.currentState === TestExecutionState.IDLE;
  }

  /**
   * Check if a test is currently running
   */
  isTestRunning(): boolean {
    return this.currentState === TestExecutionState.RUNNING;
  }

  /**
   * Check if a specific test is currently running
   */
  isSpecificTestRunning(testId: string): boolean {
    return this.isTestRunning() && this.currentTestId === testId;
  }

  /**
   * Register a callback for state transitions
   */
  onStateTransition(callback: StateTransitionCallback): void {
    this.transitionCallbacks.push(callback);
  }

  /**
   * Remove a state transition callback
   */
  offStateTransition(callback: StateTransitionCallback): void {
    const index = this.transitionCallbacks.indexOf(callback);
    if (index > -1) {
      this.transitionCallbacks.splice(index, 1);
    }
  }

  /**
   * Get state transition history
   */
  getStateHistory(limit: number = 10): StateTransitionHistory[] {
    return this.transitionHistory.slice(-limit);
  }

  /**
   * Reset the state manager to initial state with proper mutex
   */
  async reset(reason?: string): Promise<void> {
    await this.executeWithMutex(async () => {
      const previousState = this.currentState;
      const previousTestId = this.currentTestId;

      this.currentState = TestExecutionState.IDLE;
      this.currentTestId = null;

      this.recordTransition(
        previousState,
        TestExecutionState.IDLE,
        previousTestId || undefined,
        true,
        `Reset: ${reason || 'Manual reset'}`
      );

      this.logger.info(`StateManager: Reset to IDLE state${reason ? `: ${reason}` : ''}`);

      return {
        success: true,
        previousState,
        currentState: this.currentState,
        message: `Reset completed: ${reason || 'Manual reset'}`
      };
    });
  }

  /**
   * Execute operation with enhanced mutex protection and race condition prevention
   */
  private async executeWithMutex<T>(operation: () => Promise<T> | T, priority: 'normal' | 'force' = 'normal'): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const operationId = `op_${++this.operationCounter}_${Date.now()}`;
      const timestamp = Date.now();

      // Check queue size to prevent memory leaks
      if (this.transitionQueue.length >= this.maxQueueSize) {
        reject(new Error(`StateManager: Queue is full (${this.maxQueueSize} operations). Operation ${operationId} rejected.`));
        return;
      }

      // Create timeout for deadlock prevention
      const timeout = setTimeout(() => {
        // Atomically remove from queue if still pending
        const index = this.transitionQueue.findIndex(item => item.operationId === operationId);
        if (index !== -1) {
          this.transitionQueue.splice(index, 1);
          this.logger.warn(`StateManager: Operation ${operationId} timed out and removed from queue after ${this.mutexTimeout}ms`);
        }
        reject(new Error(`StateManager: Operation ${operationId} timed out after ${this.mutexTimeout}ms`));
      }, this.mutexTimeout);

      // Create queue item with enhanced metadata
      const queueItem = {
        operation: async () => {
          try {
            const result = await operation();
            return result as StateTransitionResult;
          } catch (error) {
            this.logger.error(`StateManager: Operation ${operationId} failed: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
          }
        },
        resolve: (result: StateTransitionResult) => {
          clearTimeout(timeout);
          resolve(result as T);
        },
        reject: (error: Error) => {
          clearTimeout(timeout);
          reject(error);
        },
        timeout,
        operationId,
        timestamp,
        priority
      };

      // Add to queue with priority handling (force operations go to front)
      if (priority === 'force') {
        this.transitionQueue.unshift(queueItem);
      } else {
        this.transitionQueue.push(queueItem);
      }

      // Process queue if not already processing (with atomic check)
      this.processQueue();
    });
  }

  /**
   * Process the operation queue sequentially with enhanced race condition protection
   */
  private async processQueue(): Promise<void> {
    // Atomic check and set to prevent multiple queue processors
    if (this.isProcessingQueue || this.queueProcessingLock || this.transitionQueue.length === 0) {
      return;
    }

    // Double-checked locking pattern for race condition prevention
    this.queueProcessingLock = true;
    if (this.isProcessingQueue) {
      this.queueProcessingLock = false;
      return;
    }

    this.isProcessingQueue = true;
    this.queueProcessingLock = false;

    try {
      let processedCount = 0;
      const maxProcessingTime = this.mutexTimeout / 2; // Prevent long-running queue processing
      const startTime = Date.now();

      while (this.transitionQueue.length > 0) {
        // Check for processing timeout to prevent blocking
        if (Date.now() - startTime > maxProcessingTime) {
          this.logger.warn(`StateManager: Queue processing timeout reached after ${maxProcessingTime}ms. Remaining items: ${this.transitionQueue.length}`);
          break;
        }

        const queueItem = this.transitionQueue.shift();
        if (!queueItem) continue;

        try {
          // Log operation start for debugging
          this.logger.debug(`StateManager: Processing operation ${queueItem.operationId} (priority: ${queueItem.priority})`);

          const result = await queueItem.operation();
          queueItem.resolve(result);
          processedCount++;

          this.logger.debug(`StateManager: Operation ${queueItem.operationId} completed successfully`);
        } catch (error) {
          this.logger.error(`StateManager: Operation ${queueItem.operationId} failed: ${error instanceof Error ? error.message : String(error)}`);
          queueItem.reject(error instanceof Error ? error : new Error(String(error)));
        }
      }

      if (processedCount > 0) {
        this.logger.debug(`StateManager: Processed ${processedCount} operations from queue`);
      }
    } catch (error) {
      this.logger.error(`StateManager: Critical error in queue processing: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      this.isProcessingQueue = false;

      // If there are still items in queue and we're not at timeout, schedule another processing cycle
      if (this.transitionQueue.length > 0) {
        // Use setImmediate to prevent stack overflow and allow other operations
        setImmediate(() => this.processQueue());
      }
    }
  }

  /**
   * Perform the actual state transition logic with enhanced validation
   */
  private performStateTransition(
    fromState: TestExecutionState,
    toState: TestExecutionState,
    testId?: string
  ): StateTransitionResult {
    const previousState = this.currentState;
    const operationId = `transition_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.logger.debug(`StateManager: [${operationId}] Attempting transition from ${fromState} to ${toState}${testId ? ` for test ${testId}` : ''}`);

    // Enhanced state validation - check if current state matches expected state
    if (this.currentState !== fromState) {
      const result: StateTransitionResult = {
        success: false,
        previousState,
        currentState: this.currentState,
        message: `Expected state ${fromState}, but current state is ${this.currentState}`
      };

      this.logger.warn(`StateManager: [${operationId}] State mismatch: ${result.message}`);
      this.recordTransition(fromState, toState, testId, false, result.message);
      return result;
    }

    // Validate transition rules
    if (!this.isValidTransition(fromState, toState, testId)) {
      const result: StateTransitionResult = {
        success: false,
        previousState,
        currentState: this.currentState,
        message: `Invalid transition from ${fromState} to ${toState}`
      };

      this.logger.warn(`StateManager: [${operationId}] Invalid transition: ${result.message}`);
      this.recordTransition(fromState, toState, testId, false, result.message);
      return result;
    }

    // Additional safety check: prevent concurrent test execution
    if (toState !== TestExecutionState.IDLE && this.currentTestId && testId && this.currentTestId !== testId) {
      const result: StateTransitionResult = {
        success: false,
        previousState,
        currentState: this.currentState,
        message: `Cannot start test ${testId}: another test ${this.currentTestId} is already running`
      };

      this.logger.warn(`StateManager: [${operationId}] Concurrent test prevention: ${result.message}`);
      this.recordTransition(fromState, toState, testId, false, result.message);
      return result;
    }

    // Perform the atomic state transition
    this.currentState = toState;

    // Update test ID based on transition with validation
    if (toState === TestExecutionState.IDLE) {
      this.currentTestId = null;
    } else if (testId) {
      this.currentTestId = testId;
    }

    // Record successful transition
    this.recordTransition(fromState, toState, testId, true);

    // Notify callbacks with error handling
    this.notifyTransitionCallbacks(fromState, toState, testId);

    const result: StateTransitionResult = {
      success: true,
      previousState,
      currentState: this.currentState,
      message: `Successfully transitioned from ${fromState} to ${toState}`
    };

    if (this.config?.enableStateLogging) {
      this.logger.info(`StateManager: [${operationId}] ${result.message}${testId ? ` for test ${testId}` : ''}`);
    }

    return result;
  }

  /**
   * Perform the actual force transition logic with enhanced safety checks
   */
  private performForceTransition(
    toState: TestExecutionState,
    testId?: string,
    reason?: string
  ): StateTransitionResult {
    const previousState = this.currentState;
    const operationId = `force_transition_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullReason = `Force transition: ${reason || 'No reason provided'}`;

    this.logger.warn(`StateManager: [${operationId}] Initiating force transition from ${previousState} to ${toState}${testId ? ` for test ${testId}` : ''} - Reason: ${fullReason}`);

    // Safety check: warn about potential data loss
    if (this.currentTestId && testId && this.currentTestId !== testId) {
      this.logger.warn(`StateManager: [${operationId}] Force transition will override current test ${this.currentTestId} with ${testId}`);
    }

    // Perform the atomic force transition
    this.currentState = toState;

    // Update test ID with validation
    if (toState === TestExecutionState.IDLE) {
      const previousTestId = this.currentTestId;
      this.currentTestId = null;
      if (previousTestId) {
        this.logger.info(`StateManager: [${operationId}] Cleared test ID ${previousTestId} during force transition to IDLE`);
      }
    } else if (testId) {
      this.currentTestId = testId;
    }

    // Record forced transition with detailed reason
    this.recordTransition(previousState, toState, testId, true, fullReason);

    // Notify callbacks with error handling
    this.notifyTransitionCallbacks(previousState, toState, testId);

    const result: StateTransitionResult = {
      success: true,
      previousState,
      currentState: this.currentState,
      message: `Force transitioned from ${previousState} to ${toState}${reason ? `: ${reason}` : ''}`
    };

    this.logger.warn(`StateManager: [${operationId}] ${result.message}${testId ? ` for test ${testId}` : ''}`);
    return result;
  }

  /**
   * Dispose of the state manager with enhanced cleanup
   */
  async dispose(): Promise<void> {
    this.logger.info('StateManager: Starting disposal process...');

    // Set processing locks to prevent new operations
    this.isProcessingQueue = true;
    this.queueProcessingLock = true;

    // Clear any pending operations with proper cleanup
    const pendingOperations = this.transitionQueue.length;
    for (const queueItem of this.transitionQueue) {
      clearTimeout(queueItem.timeout);
      queueItem.reject(new Error('StateManager disposed'));
    }
    this.transitionQueue = [];

    if (pendingOperations > 0) {
      this.logger.warn(`StateManager: Disposed with ${pendingOperations} pending operations`);
    }

    // Clear callbacks and history
    this.transitionCallbacks = [];
    this.transitionHistory = [];

    // Reset state
    this.currentState = TestExecutionState.IDLE;
    this.currentTestId = null;
    this.config = null;

    // Reset processing flags
    this.isProcessingQueue = false;
    this.queueProcessingLock = false;

    this.logger.info('StateManager: Disposal completed');
  }

  /**
   * Validate if a state transition is allowed
   */
  private isValidTransition(fromState: TestExecutionState, toState: TestExecutionState, testId?: string): boolean {
    // Define valid transitions
    const validTransitions: Record<TestExecutionState, TestExecutionState[]> = {
      [TestExecutionState.IDLE]: [TestExecutionState.PREPARING],
      [TestExecutionState.PREPARING]: [TestExecutionState.RUNNING, TestExecutionState.CLEANING_UP, TestExecutionState.IDLE],
      [TestExecutionState.RUNNING]: [TestExecutionState.CLEANING_UP, TestExecutionState.IDLE],
      [TestExecutionState.CLEANING_UP]: [TestExecutionState.IDLE],
      [TestExecutionState.ERROR]: [TestExecutionState.CLEANING_UP, TestExecutionState.IDLE]
    };

    const allowedStates = validTransitions[fromState] || [];
    
    // Check if transition is allowed
    if (!allowedStates.includes(toState)) {
      return false;
    }

    // Additional validation: testId required for non-IDLE states
    if (toState !== TestExecutionState.IDLE && !testId) {
      return false;
    }

    return true;
  }

  /**
   * Record a state transition in history
   */
  private recordTransition(
    fromState: TestExecutionState,
    toState: TestExecutionState,
    testId?: string,
    success: boolean = true,
    reason?: string
  ): void {
    const entry: StateTransitionHistory = {
      timestamp: new Date(),
      fromState,
      toState,
      testId,
      success,
      reason
    };

    this.transitionHistory.push(entry);

    // Limit history size
    if (this.transitionHistory.length > this.maxHistorySize) {
      this.transitionHistory = this.transitionHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Notify all registered callbacks about state transition
   */
  private notifyTransitionCallbacks(fromState: TestExecutionState, toState: TestExecutionState, testId?: string): void {
    for (const callback of this.transitionCallbacks) {
      try {
        callback(fromState, toState, testId);
      } catch (error: any) {
        this.logger.error(`StateManager: Error in transition callback: ${error.message}`);
      }
    }
  }
}
