/**
 * Team related types
 */

// Permission definition
export interface Permission {
  resource: string;
  action: string;
}

// Role definition
export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  color?: string;
  description?: string;
}

// Team member definition
export interface TeamMember {
  id: string;
  name?: string;
  email?: string;
  role: string | Role | null;
  roleId?: string;
  status: "active" | "inactive" | "pending";
  createdAt?: string;
  addedAt?: string;
  lastLogin?: string;
  lastActive?: string;
  avatar?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

// Team definition
export interface Team {
  id: string;
  name: string;
  description: string;
  members: TeamMember[];
  roles: Role[];
  createdAt: string;
} 