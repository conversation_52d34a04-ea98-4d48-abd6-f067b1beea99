/**
 * Test Communication Service Implementation
 *
 * Migrated from the old singleton-based service to the test execution module
 * following dependency injection patterns and clean architecture principles.
 */

import type {
  ITestCommunicationService,
  TestCommunicationConfig,
  TestStatusData,
  TestResultData,
  TestCommunicationHeaders,
  TestCommunicationStats
} from '../../interfaces/test-communication.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

/**
 * Test Communication Service Implementation
 *
 * Handles test-related communication operations with proper error handling,
 * logging, and integration with the test execution architecture.
 *
 * This replaces the old singleton-based implementations in:
 * - test_node/src/core/services/communication/test-communication-service.ts
 * - test_node/src/core/connectors/services/test-communication-service.ts
 */
export class TestCommunicationService implements ITestCommunicationService {
  private config: TestCommunicationConfig;
  private initialized = false;
  private logger: ILoggerService;
  private operationsCount = 0;
  private successfulOperations = 0;
  private failedOperations = 0;

  constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('TestCommunicationService');
    this.config = {
      enableLogging: false,
      defaultTimeout: 30000,
      maxRetries: 3,
      retryDelay: 1000
    };
  }

  /**
   * Initialize the service with configuration
   */
  async initialize(config: TestCommunicationConfig): Promise<void> {
    this.config = { ...this.config, ...config };
    this.initialized = true;

    if (this.config.enableLogging) {
      this.logger.info('TestCommunicationService: Initialized with config', this.config);
    }
  }

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Prepare test status update data
   */
  prepareTestStatusData(testId: string, status: string, result: any, nodeId: string): TestStatusData {
    this.incrementOperations();

    const statusData: TestStatusData = {
      status,
      result,
      nodeId,
      timestamp: new Date().toISOString()
    };

    if (this.config.enableLogging) {
      this.logger.debug(`TestCommunicationService: Prepared test status data for ${testId}`, {
        status,
        nodeId,
        hasResult: !!result
      });
    }

    return statusData;
  }

  /**
   * Validate test status update parameters
   */
  validateTestStatusParams(testId: string, status: string, nodeId: string): boolean {
    this.incrementOperations();

    if (!testId) {
      this.logger.error('TestCommunicationService: Test ID is required for status update');
      this.incrementFailures();
      return false;
    }

    if (!status) {
      this.logger.error('TestCommunicationService: Status is required for test update');
      this.incrementFailures();
      return false;
    }

    if (!nodeId) {
      this.logger.error('TestCommunicationService: Node ID is required for test update');
      this.incrementFailures();
      return false;
    }

    this.incrementSuccesses();
    return true;
  }

  /**
   * Handle test status update success
   */
  handleTestStatusSuccess(testId: string, status: string): void {
    this.incrementSuccesses();
    
    if (this.config.enableLogging) {
      this.logger.info(`TestCommunicationService: Updated test ${testId} status to ${status}`);
    }
  }

  /**
   * Handle test status update failure
   */
  handleTestStatusFailure(testId: string, status: string, error: any): void {
    this.incrementFailures();
    this.logger.error(`TestCommunicationService: Failed to update test ${testId} status to ${status}: ${error}`);
  }

  /**
   * Validate test response
   */
  validateTestResponse(response: any, validator: (data: any) => boolean): boolean {
    this.incrementOperations();

    if (!response) {
      this.logger.error('TestCommunicationService: Test response is null or undefined');
      this.incrementFailures();
      return false;
    }

    if (!validator(response)) {
      this.logger.error('TestCommunicationService: Test response format is invalid');
      this.incrementFailures();
      return false;
    }

    this.incrementSuccesses();
    return true;
  }

  /**
   * Handle next test response
   */
  handleNextTestResponse(response: any): any | null {
    this.incrementOperations();

    if (!response) {
      if (this.config.enableLogging) {
        this.logger.debug('TestCommunicationService: No test available');
      }
      return null;
    }

    if (!response.test) {
      if (this.config.enableLogging) {
        this.logger.debug('TestCommunicationService: Response successful but no test available');
      }
      return null;
    }

    this.incrementSuccesses();
    this.logger.info(`TestCommunicationService: Retrieved next test ${response.test.id}`);
    return response;
  }

  /**
   * Handle next test failure
   */
  handleNextTestFailure(error: any, context: string): void {
    this.incrementFailures();
    this.logger.error(`TestCommunicationService: Failed to get next test in ${context}: ${error}`);
  }

  /**
   * Create test communication headers
   */
  createTestHeaders(nodeSecretKey: string, additionalHeaders: Record<string, string> = {}): TestCommunicationHeaders {
    this.incrementOperations();

    const headers: TestCommunicationHeaders = {
      'Content-Type': 'application/json',
      'x-node-key': nodeSecretKey,
      ...additionalHeaders
    };

    this.incrementSuccesses();
    return headers;
  }

  /**
   * Prepare test result data
   */
  prepareTestResultData(testId: string, result: any, status: string, nodeId: string): TestResultData {
    this.incrementOperations();

    const resultData: TestResultData = {
      testId,
      result,
      status,
      nodeId,
      timestamp: new Date().toISOString(),
      completedAt: new Date().toISOString()
    };

    if (this.config.enableLogging) {
      this.logger.debug(`TestCommunicationService: Prepared test result data for ${testId}`, {
        status,
        nodeId,
        hasResult: !!result
      });
    }

    this.incrementSuccesses();
    return resultData;
  }

  /**
   * Log test communication activity
   */
  logActivity(activity: string, testId?: string, details?: any): void {
    const message = testId 
      ? `TestCommunicationService: ${activity} for test ${testId}`
      : `TestCommunicationService: ${activity}`;
    
    if (details) {
      this.logger.debug(`${message} - Details: ${JSON.stringify(details)}`);
    } else {
      this.logger.debug(message);
    }
  }

  /**
   * Get test communication statistics
   */
  getStats(): TestCommunicationStats {
    return {
      serviceType: 'TestCommunicationService',
      createdAt: new Date().toISOString(),
      operationsCount: this.operationsCount,
      successfulOperations: this.successfulOperations,
      failedOperations: this.failedOperations
    };
  }

  /**
   * Cleanup resources and reset state
   */
  async cleanup(): Promise<void> {
    this.operationsCount = 0;
    this.successfulOperations = 0;
    this.failedOperations = 0;
    this.initialized = false;

    if (this.config.enableLogging) {
      this.logger.info('TestCommunicationService: Cleaned up resources');
    }
  }

  /**
   * Increment operations counter
   */
  private incrementOperations(): void {
    this.operationsCount++;
  }

  /**
   * Increment successful operations counter
   */
  private incrementSuccesses(): void {
    this.successfulOperations++;
  }

  /**
   * Increment failed operations counter
   */
  private incrementFailures(): void {
    this.failedOperations++;
  }
}
