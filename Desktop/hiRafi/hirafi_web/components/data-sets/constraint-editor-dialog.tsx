"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Settings, Check, AlertCircle } from "lucide-react"
import type { EnhancedDataVariable, ValueConstraints } from "@/types/data-generation"

interface ConstraintEditorDialogProps {
  variable: EnhancedDataVariable | null
  constraints: ValueConstraints
  onConstraintChange: (key: keyof ValueConstraints, value: any) => void
  onSave: () => void
  onCancel: () => void
}

export function ConstraintEditorDialog({
  variable,
  constraints,
  onConstraintChange,
  onSave,
  onCancel,
}: ConstraintEditorDialogProps) {
  if (!variable) return null

  const renderConstraintFields = () => {
    const type = variable.type

    return (
      <div className="space-y-4 mt-4">
        {/* Common fields for string types */}
        {(type === "string" || type === "email" || type === "phone" || type === "address" || type === "secret") && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minLength">Minimum Length</Label>
                <Input
                  id="minLength"
                  type="number"
                  min="0"
                  value={constraints.minLength || ""}
                  onChange={(e) => onConstraintChange("minLength", Number.parseInt(e.target.value) || 0)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxLength">Maximum Length</Label>
                <Input
                  id="maxLength"
                  type="number"
                  min="0"
                  value={constraints.maxLength || ""}
                  onChange={(e) => onConstraintChange("maxLength", Number.parseInt(e.target.value) || 0)}
                />
              </div>
            </div>

            {type === "string" && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="pattern">Pattern (Regex)</Label>
                  <Input
                    id="pattern"
                    placeholder="e.g. [A-Z]{3}[0-9]{4}"
                    value={constraints.pattern || ""}
                    onChange={(e) => onConstraintChange("pattern", e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Example: [A-Z]{"{3}"}[0-9]{"{4}"} will generate something like ABC1234
                  </p>
                </div>

                <div className="space-y-3">
                  <Label>Character Requirements</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeUppercase"
                        checked={constraints.includeUppercase || false}
                        onCheckedChange={(checked) => onConstraintChange("includeUppercase", checked)}
                      />
                      <Label htmlFor="includeUppercase" className="text-sm font-normal">
                        Include uppercase letters (A-Z)
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeLowercase"
                        checked={constraints.includeLowercase || false}
                        onCheckedChange={(checked) => onConstraintChange("includeLowercase", checked)}
                      />
                      <Label htmlFor="includeLowercase" className="text-sm font-normal">
                        Include lowercase letters (a-z)
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeNumbers"
                        checked={constraints.includeNumbers || false}
                        onCheckedChange={(checked) => onConstraintChange("includeNumbers", checked)}
                      />
                      <Label htmlFor="includeNumbers" className="text-sm font-normal">
                        Include numbers (0-9)
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeSpecial"
                        checked={constraints.includeSpecial || false}
                        onCheckedChange={(checked) => onConstraintChange("includeSpecial", checked)}
                      />
                      <Label htmlFor="includeSpecial" className="text-sm font-normal">
                        Include special characters (!@#$%^&*)
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mustInclude">Must Include Characters</Label>
                  <Input
                    id="mustInclude"
                    placeholder="e.g. @#$"
                    value={(constraints.mustInclude || []).join("")}
                    onChange={(e) => onConstraintChange("mustInclude", e.target.value.split(""))}
                  />
                  <p className="text-xs text-gray-500 mt-1">Characters that must be included in the generated value</p>
                </div>
              </>
            )}

            {type === "email" && (
              <div className="space-y-2">
                <Label htmlFor="mustInclude">Must Include</Label>
                <Input
                  id="mustInclude"
                  placeholder="e.g. <EMAIL>"
                  value={(constraints.mustInclude || []).join("")}
                  onChange={(e) => onConstraintChange("mustInclude", [e.target.value])}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Specific text to include in the email (e.g. domain or username)
                </p>
              </div>
            )}

            {type === "phone" && (
              <div className="space-y-2">
                <Label htmlFor="pattern">Pattern</Label>
                <Input
                  id="pattern"
                  placeholder="e.g. +90XXXXXXXXXX"
                  value={constraints.pattern || ""}
                  onChange={(e) => onConstraintChange("pattern", e.target.value)}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Use X for random digits, other characters will be preserved
                </p>
              </div>
            )}
          </>
        )}

        {/* Number specific fields */}
        {type === "number" && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minValue">Minimum Value</Label>
                <Input
                  id="minValue"
                  type="number"
                  value={constraints.minValue || ""}
                  onChange={(e) => onConstraintChange("minValue", Number.parseFloat(e.target.value) || 0)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxValue">Maximum Value</Label>
                <Input
                  id="maxValue"
                  type="number"
                  value={constraints.maxValue || ""}
                  onChange={(e) => onConstraintChange("maxValue", Number.parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="decimals">Decimal Places</Label>
              <Input
                id="decimals"
                type="number"
                min="0"
                max="10"
                value={constraints.decimals || ""}
                onChange={(e) => onConstraintChange("decimals", Number.parseInt(e.target.value) || 0)}
              />
            </div>
          </>
        )}

        {/* Date specific fields */}
        {type === "date" && (
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={constraints.startDate || ""}
                onChange={(e) => onConstraintChange("startDate", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={constraints.endDate || ""}
                onChange={(e) => onConstraintChange("endDate", e.target.value)}
              />
            </div>
          </div>
        )}

        {/* Array specific fields */}
        {type === "array" && (
          <>
            <div className="space-y-2">
              <Label htmlFor="length">Array Length</Label>
              <Input
                id="length"
                type="number"
                min="0"
                value={constraints.length || ""}
                onChange={(e) => onConstraintChange("length", Number.parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="items">Possible Items (comma separated)</Label>
              <Input
                id="items"
                placeholder="item1, item2, item3"
                value={Array.isArray(constraints.items) ? constraints.items.join(", ") : ""}
                onChange={(e) =>
                  onConstraintChange(
                    "items",
                    e.target.value.split(",").map((item) => item.trim()),
                  )
                }
              />
            </div>
          </>
        )}

        {/* Object specific fields */}
        {type === "object" && (
          <div className="space-y-2">
            <Label htmlFor="properties">Number of Properties</Label>
            <Input
              id="properties"
              type="number"
              min="1"
              value={constraints.properties || ""}
              onChange={(e) => onConstraintChange("properties", Number.parseInt(e.target.value) || 1)}
            />
          </div>
        )}

        <div className="pt-4 flex justify-end">
          <Button onClick={onSave} className="bg-indigo-600 hover:bg-indigo-700 text-white">
            <Check className="h-4 w-4 mr-2" />
            Save Constraints & Generate
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Dialog open={!!variable} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-indigo-500" />
            Configure Value Constraints
          </DialogTitle>
          <DialogDescription>
            Define constraints for how values should be generated for {variable.name || "this variable"}
          </DialogDescription>
        </DialogHeader>

        <div className="bg-indigo-50 dark:bg-indigo-950/20 p-3 rounded-md mb-4 flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-indigo-500 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
              Constraints for {variable.type} type
            </h4>
            <p className="text-xs text-indigo-600/80 dark:text-indigo-400/80 mt-1">
              Define how this {variable.type} value should be generated. The system will automatically create values
              that match these constraints.
            </p>
          </div>
        </div>

        {renderConstraintFields()}

        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={onSave} className="bg-indigo-600 hover:bg-indigo-700 text-white">
            <Check className="h-4 w-4 mr-2" />
            Save Constraints
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
