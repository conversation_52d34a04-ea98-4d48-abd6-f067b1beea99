import type { TeamMember, Role, Permission } from "@/types/team"
import { v4 as uuidv4 } from "uuid"

// Mock permissions
const scenarioPermissions: Permission[] = [
  {
    id: "scenarios-create",
    name: "Senaryo Oluşturma",
    description: "Yeni senaryolar oluşturabilir",
    module: "scenarios",
    action: "create",
  },
  {
    id: "scenarios-read",
    name: "Senaryo Görüntüleme",
    description: "Senaryoları görüntüleyebilir",
    module: "scenarios",
    action: "read",
  },
  {
    id: "scenarios-update",
    name: "Senaryo Düzenleme",
    description: "Mevcut senaryoları düzenleyebilir",
    module: "scenarios",
    action: "update",
  },
  {
    id: "scenarios-delete",
    name: "<PERSON>ary<PERSON>lm<PERSON>",
    description: "Senaryoları silebilir",
    module: "scenarios",
    action: "delete",
  },
]

const runPermissions: Permission[] = [
  {
    id: "runs-create",
    name: "Koşum Oluşturma",
    description: "<PERSON>ni koşumlar oluşturabilir",
    module: "runs",
    action: "create",
  },
  {
    id: "runs-read",
    name: "Koşum Görü<PERSON>",
    description: "Koşumları görüntüleyebilir",
    module: "runs",
    action: "read",
  },
  {
    id: "runs-update",
    name: "Koşum Düzenleme",
    description: "Mevcut koşumları düzenleyebilir",
    module: "runs",
    action: "update",
  },
  { id: "runs-delete", name: "Koşum Silme", description: "Koşumları silebilir", module: "runs", action: "delete" },
  {
    id: "runs-execute",
    name: "Koşum Çalıştırma",
    description: "Koşumları çalıştırabilir",
    module: "runs",
    action: "execute",
  },
]

const reportPermissions: Permission[] = [
  {
    id: "reports-read",
    name: "Rapor Görüntüleme",
    description: "Raporları görüntüleyebilir",
    module: "reports",
    action: "read",
  },
  {
    id: "reports-create",
    name: "Rapor Oluşturma",
    description: "Özel raporlar oluşturabilir",
    module: "reports",
    action: "create",
  },
  {
    id: "reports-delete",
    name: "Rapor Silme",
    description: "Raporları silebilir",
    module: "reports",
    action: "delete",
  },
]

const schedulePermissions: Permission[] = [
  {
    id: "schedule-create",
    name: "Schedule Oluşturma",
    description: "Yeni schedule görevleri oluşturabilir",
    module: "schedule",
    action: "create",
  },
  {
    id: "schedule-read",
    name: "Schedule Görüntüleme",
    description: "Schedule görevleri görüntüleyebilir",
    module: "schedule",
    action: "read",
  },
  {
    id: "schedule-update",
    name: "Schedule Düzenleme",
    description: "Schedule görevleri düzenleyebilir",
    module: "schedule",
    action: "update",
  },
  {
    id: "schedule-delete",
    name: "Schedule Silme",
    description: "Schedule görevleri silebilir",
    module: "schedule",
    action: "delete",
  },
]

// Takım izinleri kaldırıldı

// API izinleri kaldırıldı

// Mock roles
export const mockRoles: Role[] = [
  {
    id: "role-1",
    name: "Admin",
    description: "Tam yetkili yönetici rolü",
    permissions: [
      ...scenarioPermissions,
      ...runPermissions,
      ...reportPermissions,
      ...schedulePermissions,
    ],
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    memberCount: 2,
  },
  {
    id: "role-2",
    name: "Test Mühendisi",
    description: "Test senaryoları oluşturma ve çalıştırma yetkisi",
    permissions: [
      ...scenarioPermissions,
      ...runPermissions,
      {
        id: "reports-read",
        name: "Rapor Görüntüleme",
        description: "Raporları görüntüleyebilir",
        module: "reports",
        action: "read",
      },
      {
        id: "team-read",
        name: "Takım Görüntüleme",
        description: "Takım üyelerini görüntüleyebilir",
        module: "team",
        action: "read",
      },
    ],
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    memberCount: 3,
  },
  {
    id: "role-3",
    name: "Geliştirici",
    description: "Raporları görüntüleme ve API erişimi",
    permissions: [
      {
        id: "scenarios-read",
        name: "Senaryo Görüntüleme",
        description: "Senaryoları görüntüleyebilir",
        module: "scenarios",
        action: "read",
      },
      {
        id: "runs-read",
        name: "Koşum Görüntüleme",
        description: "Koşumları görüntüleyebilir",
        module: "runs",
        action: "read",
      },
      {
        id: "reports-read",
        name: "Rapor Görüntüleme",
        description: "Raporları görüntüleyebilir",
        module: "reports",
        action: "read",
      },
      ...schedulePermissions,
    ],
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    memberCount: 5,
  },
  {
    id: "role-4",
    name: "Gözlemci",
    description: "Sadece görüntüleme yetkisi",
    permissions: [
      {
        id: "scenarios-read",
        name: "Senaryo Görüntüleme",
        description: "Senaryoları görüntüleyebilir",
        module: "scenarios",
        action: "read",
      },
      {
        id: "runs-read",
        name: "Koşum Görüntüleme",
        description: "Koşumları görüntüleyebilir",
        module: "runs",
        action: "read",
      },
      {
        id: "reports-read",
        name: "Rapor Görüntüleme",
        description: "Raporları görüntüleyebilir",
        module: "reports",
        action: "read",
      },
    ],
    color: "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300",
    memberCount: 2,
  },
]

// Mock team members
export const mockTeamMembers: TeamMember[] = [
  {
    id: uuidv4(),
    name: "Ahmet Yılmaz",
    email: "<EMAIL>",
    role: "Admin",
    status: "active",
    createdAt: "2023-01-15T10:30:00Z",
    lastLogin: "2023-06-10T08:45:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Mehmet Demir",
    email: "<EMAIL>",
    role: "Admin",
    status: "active",
    createdAt: "2023-01-20T11:15:00Z",
    lastLogin: "2023-06-09T14:30:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Ayşe Kaya",
    email: "<EMAIL>",
    role: "Test Mühendisi",
    status: "active",
    createdAt: "2023-02-05T09:45:00Z",
    lastLogin: "2023-06-08T10:15:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Fatma Şahin",
    email: "<EMAIL>",
    role: "Test Mühendisi",
    status: "active",
    createdAt: "2023-02-10T13:20:00Z",
    lastLogin: "2023-06-07T16:40:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Ali Yıldız",
    email: "<EMAIL>",
    role: "Test Mühendisi",
    status: "active",
    createdAt: "2023-02-15T15:10:00Z",
    lastLogin: "2023-06-09T11:30:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Zeynep Çelik",
    email: "<EMAIL>",
    role: "Geliştirici",
    status: "active",
    createdAt: "2023-03-01T08:30:00Z",
    lastLogin: "2023-06-10T09:20:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Mustafa Aydın",
    email: "<EMAIL>",
    role: "Geliştirici",
    status: "active",
    createdAt: "2023-03-05T10:45:00Z",
    lastLogin: "2023-06-08T14:15:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Elif Arslan",
    email: "<EMAIL>",
    role: "Geliştirici",
    status: "active",
    createdAt: "2023-03-10T14:20:00Z",
    lastLogin: "2023-06-07T11:50:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Hasan Kara",
    email: "<EMAIL>",
    role: "Geliştirici",
    status: "inactive",
    createdAt: "2023-03-15T16:30:00Z",
    lastLogin: "2023-05-20T10:10:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Selin Koç",
    email: "<EMAIL>",
    role: "Geliştirici",
    status: "active",
    createdAt: "2023-03-20T09:15:00Z",
    lastLogin: "2023-06-09T15:40:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Emre Özkan",
    email: "<EMAIL>",
    role: "Gözlemci",
    status: "active",
    createdAt: "2023-04-01T11:30:00Z",
    lastLogin: "2023-06-08T09:30:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Deniz Yılmaz",
    email: "<EMAIL>",
    role: "Gözlemci",
    status: "active",
    createdAt: "2023-04-05T13:45:00Z",
    lastLogin: "2023-06-07T14:20:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Burak Aksoy",
    email: "<EMAIL>",
    role: "Test Mühendisi",
    status: "pending",
    createdAt: "2023-05-15T10:00:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: uuidv4(),
    name: "Ceren Demir",
    email: "<EMAIL>",
    role: "Geliştirici",
    status: "pending",
    createdAt: "2023-05-20T14:30:00Z",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]
