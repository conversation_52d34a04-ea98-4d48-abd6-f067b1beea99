import { <PERSON><PERSON>, Position } from 'reactflow';
import { Button } from '@/components/ui/button';
import { X, Plus, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState, useEffect } from 'react';

function DeleteNode({ data, id }: { data: any, id: string }) {
  const [columns, setColumns] = useState<string[]>([]);
  const [isFetchingColumns, setIsFetchingColumns] = useState(false);

  useEffect(() => {
    // If the node already has a table name when it's first rendered (e.g., from onConnect),
    // fetch its columns.
    if (data.table && data.connectionString) {
      setIsFetchingColumns(true);
      
      // <PERSON><PERSON><PERSON>an getDbColumns API'sini çağır
      import('@/lib/api/test-data').then(({ getDbColumns }) => {
        return getDbColumns(data.connectionString, data.table);
      }).then((result: any) => {
        if (result.success && result.data?.columns) {
          setColumns(result.data.columns);
        } else {
          setColumns([]);
        }
        setIsFetchingColumns(false);
      }).catch((error: any) => {
        console.error("Failed to fetch columns directly", error);
        setColumns([]);
        setIsFetchingColumns(false);
      });
    }
  }, [data.version, data.dbTables, data.table, data.connectionString]); // Listen for changes to the version prop

  const handleTableChange = async (tableName: string, resetConditions = true) => {
    if(resetConditions) {
        data.updateData({ table: tableName, conditions: [{ col: '', op: '=', val: '' }] });
    } else {
        data.updateData({ table: tableName });
    }
    
    setIsFetchingColumns(true);
    try {
      const fetchedColumns = await data.onTableSelect(tableName);
      setColumns(fetchedColumns || []);
    } catch (error) {
      console.error("Failed to fetch columns", error);
    } finally {
        setIsFetchingColumns(false);
    }
  };
  
  const handleConditionChange = (index: number, field: 'col' | 'op' | 'val', value: string) => {
      const newConditions = [...data.conditions];
      newConditions[index][field] = value;
      data.updateData({ conditions: newConditions });
  };
  
  const addCondition = () => {
      data.updateData({ conditions: [...data.conditions, { col: '', op: '=', val: '' }] });
  };
  
  const removeCondition = (index: number) => {
      const newConditions = data.conditions.filter((_: any, i: number) => i !== index);
      data.updateData({ conditions: newConditions.length > 0 ? newConditions : [{ col: '', op: '=', val: '' }] });
  };

  return (
    <div className="query-builder-node">
      <Handle type="target" position={Position.Left} />
      
      <div className="node-header">
        <div className="node-title"><Trash2 className="h-4 w-4 text-red-500" /><span>DELETE Data</span></div>
        <Button variant="ghost" size="icon" className="h-6 w-6 btn" onClick={data.onDelete}><X className="h-4 w-4" /></Button>
      </div>

      <div className="node-body">
        <div className="node-section">
            <Label className="node-section-label">FROM</Label>
            <Select onValueChange={handleTableChange} value={data.table}>
                <SelectTrigger className="react-select-trigger"><SelectValue placeholder="Select table..." /></SelectTrigger>
                <SelectContent>{data.dbTables.map((t: string) => <SelectItem key={t} value={t}>{t}</SelectItem>)}</SelectContent>
            </Select>
        </div>

        {isFetchingColumns && <div>Loading...</div>}

        {data.table && !isFetchingColumns && (
            <div className="node-section border-t pt-2">
                <Label className="node-section-label">WHERE</Label>
                 {data.conditions.map((cond: any, index: number) => (
                    <div key={index} className="node-row">
                        <Select value={cond.col} onValueChange={(val) => handleConditionChange(index, 'col', val)}><SelectTrigger className="react-select-trigger"><SelectValue placeholder="Column" /></SelectTrigger><SelectContent>{columns.map(c => <SelectItem key={c} value={c}>{c}</SelectItem>)}</SelectContent></Select>
                        <Select value={cond.op} onValueChange={(val) => handleConditionChange(index, 'op', val)}><SelectTrigger className="w-20 react-select-trigger"><SelectValue /></SelectTrigger><SelectContent><SelectItem value="=">=</SelectItem><SelectItem value="!=">!=</SelectItem></SelectContent></Select>
                        <Input className="react-input" placeholder="Value" value={cond.val} onChange={(e) => handleConditionChange(index, 'val', e.target.value)} />
                        <Button variant="ghost" size="icon" className="btn" onClick={() => removeCondition(index)}><X className="h-4 w-4" /></Button>
                    </div>
                ))}
                <Button variant="outline" size="sm" className="btn" onClick={addCondition}><Plus className="h-4 w-4 mr-2" />Add Condition</Button>
            </div>
        )}
      </div>
    </div>
  );
}

export default DeleteNode; 