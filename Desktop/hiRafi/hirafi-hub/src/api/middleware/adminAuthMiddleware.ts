/**
 * Admin Authentication Middleware
 * Admin API route'ları için authentication middleware
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger.js';
import { validateAdminToken } from '../../services/mongo/adminService.js';
import { AdminRole } from '../../models/admin.js';

// Admin request interface
export interface AdminRequest extends Request {
  admin?: {
    id: string;
    email: string;
    name?: string;
    role: string;
  };
}

/**
 * Admin authentication middleware
 * Gelen isteklerde JWT token kontrolü yapar
 */
export async function authenticateAdmin(req: AdminRequest, res: Response, next: NextFunction) {
  try {
    // Authorization header'ı al
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authorization header is required' 
      });
    }
    
    // Bearer token formatını kontrol et
    const parts = authHeader.split(' ');
    
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid authorization format. Use: Bearer <token>' 
      });
    }
    
    const token = parts[1];
    
    // Token doğrula
    const validation = await validateAdminToken(token);
    
    if (!validation.success || !validation.admin) {
      return res.status(401).json({ 
        success: false, 
        error: validation.error || 'Invalid token' 
      });
    }
    
    // Admin bilgilerini request'e ekle
    req.admin = validation.admin;
    
    // Sonraki middleware'e geç
    next();
  } catch (error) {
    logger.error('[ADMIN] Authentication error:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Authentication failed' 
    });
  }
}

/**
 * Admin role authorization middleware
 * Admin kullanıcısının belirli bir role sahip olup olmadığını kontrol eder
 * @param requiredRole Required admin role
 */
export function authorizeAdmin(requiredRole: AdminRole) {
  return (req: AdminRequest, res: Response, next: NextFunction) => {
    try {
      // Admin bilgilerini kontrol et
      if (!req.admin) {
        return res.status(401).json({ 
          success: false, 
          error: 'Authentication required' 
        });
      }
      
      const adminRole = req.admin.role;
      
      // Super admin her zaman erişebilir
      if (adminRole === AdminRole.SUPER_ADMIN) {
        return next();
      }
      
      // Rol hiyerarşisi kontrolü
      if (requiredRole === AdminRole.SUPER_ADMIN) {
        // Sadece super admin erişebilir
        return res.status(403).json({ 
          success: false, 
          error: 'Insufficient permissions' 
        });
      }
      
      if (requiredRole === AdminRole.ADMIN) {
        // Admin ve super admin erişebilir
        if (adminRole === AdminRole.ADMIN) {
          return next();
        }
        
        return res.status(403).json({ 
          success: false, 
          error: 'Insufficient permissions' 
        });
      }
      
      if (requiredRole === AdminRole.MODERATOR) {
        // Tüm roller erişebilir
        return next();
      }
      
      // Bilinmeyen rol
      return res.status(403).json({ 
        success: false, 
        error: 'Insufficient permissions' 
      });
    } catch (error) {
      logger.error('[ADMIN] Authorization error:', error);
      return res.status(500).json({ 
        success: false, 
        error: 'Authorization failed' 
      });
    }
  };
}
