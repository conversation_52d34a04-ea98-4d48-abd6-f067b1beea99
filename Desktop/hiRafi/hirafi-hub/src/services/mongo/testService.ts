/**
 * Test Service
 * Provides functions for working with tests in MongoDB
 */

import { Collection } from 'mongodb';
import { db, ensureMongoDBConnection, testsCollection } from './dbConnection.js';
import { logger } from '../../utils/logger.js';
import { TestProcess, TestStatus } from '../../models/test-types.js';
import { operationQueue } from '../../utils/operationQueue.js';

/**
 * Get test by ID
 * @param testId Test ID
 * @returns Test object if found, null otherwise
 */
export async function getTestById(testId: string): Promise<{
  success: boolean;
  test?: TestProcess;
  message?: string;
}> {
  try {
    return await operationQueue.enqueue(
      `get-test-${testId}`,
      async () => {
        if (!testsCollection) throw new Error('testsCollection not initialized');
        const test = await testsCollection.findOne({ id: testId });

        if (!test) {
          return {
            success: false,
            message: `Test with ID ${testId} not found`
          };
        }

        return {
          success: true,
          test: test as unknown as TestProcess
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting test: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get test: ${error.message}`
    };
  }
}

/**
 * Get tests by run ID
 * @param runId Run ID
 * @returns Array of tests for the run
 */
export async function getTestsByRunId(runId: string): Promise<{
  success: boolean;
  tests?: TestProcess[];
  message?: string;
}> {
  try {
    return await operationQueue.enqueue(
      `get-tests-by-run-${runId}`,
      async () => {
        if (!testsCollection) throw new Error('testsCollection not initialized');
        const tests = await testsCollection.find({ runId }).toArray();

        return {
          success: true,
          tests: tests as unknown as TestProcess[]
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting tests by run ID: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get tests by run ID: ${error.message}`
    };
  }
}

/**
 * Get tests by user ID
 * @param userId User ID
 * @returns Array of tests for the user
 */
export async function getTestsByUserId(userId: string): Promise<{
  success: boolean;
  tests?: TestProcess[];
  message?: string;
}> {
  try {
    return await operationQueue.enqueue(
      `get-tests-by-user-${userId}`,
      async () => {
        if (!testsCollection) throw new Error('testsCollection not initialized');
        const tests = await testsCollection.find({ userId }).toArray();

        return {
          success: true,
          tests: tests as unknown as TestProcess[]
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting tests by user ID: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get tests by user ID: ${error.message}`
    };
  }
}

/**
 * Get tests by scenario ID
 * @param scenarioId Scenario ID
 * @returns Array of tests for the scenario
 */
export async function getTestsByScenarioId(scenarioId: string): Promise<{
  success: boolean;
  tests?: TestProcess[];
  message?: string;
}> {
  try {
    return await operationQueue.enqueue(
      `get-tests-by-scenario-${scenarioId}`,
      async () => {
        if (!testsCollection) throw new Error('testsCollection not initialized');
        const tests = await testsCollection.find({ scenarioId }).toArray();

        return {
          success: true,
          tests: tests as unknown as TestProcess[]
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting tests by scenario ID: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get tests by scenario ID: ${error.message}`
    };
  }
}

/**
 * Get all tests
 * @param limit Maximum number of tests to return
 * @param skip Number of tests to skip
 * @returns Array of all tests
 */
export async function getAllTests(limit: number = 100, skip: number = 0): Promise<{
  success: boolean;
  tests?: TestProcess[];
  total?: number;
  message?: string;
}> {
  try {
    return await operationQueue.enqueue(
      `get-all-tests-${limit}-${skip}`,
      async () => {
        if (!testsCollection) throw new Error('testsCollection not initialized');
        // Get total count
        const total = await testsCollection.countDocuments();

        // Get tests with pagination
        const tests = await testsCollection.find({})
          .sort({ queuedAt: -1 })
          .skip(skip)
          .limit(limit)
          .toArray();

        return {
          success: true,
          tests: tests as unknown as TestProcess[],
          total
        };
      },
      { priority: 'NORMAL', maxRetries: 2 }
    );
  } catch (error: any) {
    logger.error(`Error getting all tests: ${error.message}`, error);
    return {
      success: false,
      message: `Failed to get all tests: ${error.message}`
    };
  }
}

/**
 * Get tests collection
 * @returns Tests collection
 */
export async function getTestsCollection(): Promise<Collection> {
  if (!testsCollection) {
    throw new Error('testsCollection not initialized');
  }
  return testsCollection;
}
