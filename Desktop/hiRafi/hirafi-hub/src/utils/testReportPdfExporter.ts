/**
 * Test Report PDF Exporter
 * Utility functions for exporting test reports in PDF format
 */

import PDFDocument from 'pdfkit';
import { logger } from './logger.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import https from 'https';

// Get file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Font paths - Use process.cwd() for Docker container where working directory is /app
const FONTS = {
  Roboto: {
    normal: path.join(process.cwd(), 'assets/fonts/Roboto-Regular.ttf'),
    bold: path.join(process.cwd(), 'assets/fonts/Roboto-Bold.ttf'),
    italic: path.join(process.cwd(), 'assets/fonts/Roboto-Italic.ttf'),
    bolditalic: path.join(process.cwd(), 'assets/fonts/Roboto-BoldItalic.ttf')
  }
};

// Page dimensions and margins
const PAGE_WIDTH = 595.28;
const PAGE_MARGIN = 50;
const CONTENT_WIDTH = PAGE_WIDTH - (PAGE_MARGIN * 2);

// Define interfaces for report and step objects
interface TestStep {
  id?: string;
  name?: string;
  description?: string;
  type?: string;
  duration?: number;
  success?: boolean;
  error?: string;
  beforeScreenshotUrl?: string;
  afterScreenshotUrl?: string;
}

interface TestReport {
  id: string;
  name?: string;
  scenarioTitle?: string;
  date?: string;
  status?: string;
  duration?: number;
  summary?: {
    total?: number;
    passed?: number;
    failed?: number;
    errors?: number;
  };
  steps?: TestStep[];
}

/**
 * Creates a PDF report from test data
 * @param report Test report object
 * @returns Promise<Buffer> PDF buffer
 */
export async function createReportPdf(report: TestReport): Promise<Buffer> {
  return new Promise<Buffer>(async (resolve, reject) => {
    try {
      // Check if font files exist
      for (const fontType in FONTS.Roboto) {
        const fontPath = FONTS.Roboto[fontType as keyof typeof FONTS.Roboto];
        if (!fs.existsSync(fontPath)) {
          logger.warn(`Font file not found: ${fontPath}`);
          throw new Error(`Font file not found: ${fontPath}`);
        } else {
          logger.info(`Font file found: ${fontPath}`);
        }
      }

      // Create PDF document - A4 size with margins
      const doc = new PDFDocument({
        margin: PAGE_MARGIN,
        size: 'A4',
        bufferPages: true,
        autoFirstPage: true,
        info: {
          Title: `Test Report - ${report.scenarioTitle || report.name || 'Unnamed Test'}`,
          Author: 'AI Drive Test Hub',
          Subject: 'Test Report',
          Keywords: 'test, report, automation',
          Creator: 'AI Drive Test Hub',
          Producer: 'PDFKit'
        }
      });

      // Register fonts
      try {
        // Register Roboto font (for Turkish character support)
        doc.registerFont('Roboto', FONTS.Roboto.normal);
        doc.registerFont('Roboto-Bold', FONTS.Roboto.bold);
        doc.registerFont('Roboto-Italic', FONTS.Roboto.italic);
        doc.registerFont('Roboto-BoldItalic', FONTS.Roboto.bolditalic);

        // Set default font
        doc.font('Roboto');
        logger.info('Fonts registered successfully');
      } catch (fontError: any) {
        logger.error(`Error registering fonts: ${fontError.message}`);
        throw fontError;
      }

      // Create buffer stream
      const buffers: Buffer[] = [];

      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Add header and title
      addHeader(doc, report);

      // Add report summary
      addSummary(doc, report);

      // Add test steps
      addTestSteps(doc, report);

      // Add screenshots (async)
      await addScreenshots(doc, report);

      // Add page numbers
      addPageNumbers(doc);

      // Finalize PDF
      doc.end();

    } catch (error: any) {
      logger.error(`Error creating PDF for report: ${error.message}`);
      reject(error);
    }
  });
}

/**
 * Adds header and title to the PDF
 */
function addHeader(doc: any, report: TestReport): void {
  // Header area
  doc.rect(PAGE_MARGIN, PAGE_MARGIN, CONTENT_WIDTH, 80)
     .fillAndStroke('#f0f4f8', '#e1e8ed');

  // Add title
  doc.fontSize(24)
     .font('Roboto-Bold')
     .fillColor('#1a365d')
     .text(report.scenarioTitle || report.name || 'Test Report', PAGE_MARGIN + 10, PAGE_MARGIN + 15, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Add subtitle
  doc.fontSize(12)
     .font('Roboto')
     .fillColor('#4a5568')
     .text(`Report ID: ${report.id}`, PAGE_MARGIN + 10, PAGE_MARGIN + 45, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Add date
  const reportDate = report.date ? new Date(report.date).toLocaleString() : new Date().toLocaleString();
  doc.fontSize(10)
     .text(`Generated on: ${reportDate}`, PAGE_MARGIN + 10, PAGE_MARGIN + 65, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Y position after header area
  doc.y = PAGE_MARGIN + 100;
}

/**
 * Adds report summary to the PDF
 */
function addSummary(doc: any, report: TestReport): void {
  // Summary title
  doc.fontSize(16)
     .font('Roboto-Bold')
     .fillColor('#2d3748')
     .text('Test Summary', PAGE_MARGIN, doc.y);

  doc.y += 10;

  // Summary table
  const summaryData = [
    { label: 'Status', value: report.status || 'Unknown' },
    { label: 'Duration', value: formatDuration(report.duration) },
    { label: 'Total Steps', value: report.summary?.total || 0 },
    { label: 'Passed Steps', value: report.summary?.passed || 0 },
    { label: 'Failed Steps', value: report.summary?.failed || 0 },
    { label: 'Error Count', value: report.summary?.errors || 0 },
  ];

  // Table start position
  const tableTop = doc.y;
  const tableLeft = PAGE_MARGIN;
  const colWidth = CONTENT_WIDTH / 2;

  // Table header
  doc.rect(tableLeft, tableTop, CONTENT_WIDTH, 25)
     .fillAndStroke('#e6f0ff', '#b3d1ff');

  doc.fontSize(12)
     .font('Roboto-Bold')
     .fillColor('#1a365d')
     .text('Metric', tableLeft + 10, tableTop + 8, { width: colWidth - 20 })
     .text('Value', tableLeft + colWidth + 10, tableTop + 8, { width: colWidth - 20 });

  // Table data
  let rowTop = tableTop + 25;
  let rowColor = false;

  summaryData.forEach((item) => {
    // Row background
    if (rowColor) {
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, 25)
         .fillAndStroke('#f8fafc', '#edf2f7');
    }
    rowColor = !rowColor;

    // Row data
    doc.fontSize(10)
       .font('Roboto')
       .fillColor('#2d3748')
       .text(item.label, tableLeft + 10, rowTop + 8, { width: colWidth - 20 });

    // Set status color in value cell
    if (item.label === 'Status') {
      const valueStr = String(item.value).toLowerCase();
      const statusColor = valueStr === 'completed' || valueStr === 'passed' ? '#38a169' : '#e53e3e';
      doc.fillColor(statusColor)
         .font('Roboto-Bold')
         .text(String(item.value), tableLeft + colWidth + 10, rowTop + 8, { width: colWidth - 20 })
         .fillColor('#2d3748')
         .font('Roboto');
    } else {
      doc.text(String(item.value), tableLeft + colWidth + 10, rowTop + 8, { width: colWidth - 20 });
    }

    rowTop += 25;
  });

  // Space after table
  doc.y = rowTop + 20;
}

/**
 * Adds test steps to the PDF
 */
function addTestSteps(doc: any, report: TestReport): void {
  // Steps title
  doc.fontSize(16)
     .font('Roboto-Bold')
     .fillColor('#2d3748')
     .text('Test Steps', PAGE_MARGIN, doc.y);

  doc.y += 10;

  // Show info message if no steps
  if (!report.steps || report.steps.length === 0) {
    doc.fontSize(10)
       .font('Roboto-Italic')
       .fillColor('#718096')
       .text('No test steps available for this report.', PAGE_MARGIN, doc.y);
    return;
  }

  // Steps table
  const tableTop = doc.y;
  const tableLeft = PAGE_MARGIN;

  // Column widths
  const colWidths = {
    step: CONTENT_WIDTH * 0.1,
    name: CONTENT_WIDTH * 0.4,
    type: CONTENT_WIDTH * 0.2,
    duration: CONTENT_WIDTH * 0.15,
    status: CONTENT_WIDTH * 0.15
  };

  // Table header
  doc.rect(tableLeft, tableTop, CONTENT_WIDTH, 25)
     .fillAndStroke('#e6f0ff', '#b3d1ff');

  doc.fontSize(10)
     .font('Roboto-Bold')
     .fillColor('#1a365d');

  let headerX = tableLeft;
  doc.text('#', headerX + 5, tableTop + 8, { width: colWidths.step - 10 });
  headerX += colWidths.step;

  doc.text('Step Name', headerX + 5, tableTop + 8, { width: colWidths.name - 10 });
  headerX += colWidths.name;

  doc.text('Type', headerX + 5, tableTop + 8, { width: colWidths.type - 10 });
  headerX += colWidths.type;

  doc.text('Duration', headerX + 5, tableTop + 8, { width: colWidths.duration - 10 });
  headerX += colWidths.duration;

  doc.text('Status', headerX + 5, tableTop + 8, { width: colWidths.status - 10 });

  // Table data
  let rowTop = tableTop + 25;
  let rowColor = false;

  // Calculate all step heights first
  const stepHeights = report.steps.map((step, index) => {
    const stepNameText = step.name || step.description || `Step ${index + 1}`;
    const stepTypeText = step.type || 'Unknown';

    // Calculate height for step name
    const nameHeight = estimateTextHeight(stepNameText, colWidths.name - 10, 9);

    // Calculate height for step type
    const typeHeight = estimateTextHeight(stepTypeText, colWidths.type - 10, 9);

    // Calculate height for error message if present
    const errorHeight = step.error ?
      estimateTextHeight(step.error, CONTENT_WIDTH - 20, 9) : 0;

    // Use the largest height and set minimum to 25px
    return Math.max(25, nameHeight, typeHeight) + (errorHeight > 0 ? errorHeight + 10 : 0);
  });

  report.steps.forEach((step, index) => {
    // Get row height
    const rowHeight = stepHeights[index];
    const stepNameText = step.name || step.description || `Step ${index + 1}`;

    // Page check
    if (rowTop + rowHeight > doc.page.height - PAGE_MARGIN) {
      doc.addPage();
      rowTop = PAGE_MARGIN;

      // Redraw header row on new page
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, 25)
         .fillAndStroke('#e6f0ff', '#b3d1ff');

      doc.fontSize(10)
         .font('Roboto-Bold')
         .fillColor('#1a365d');

      let headerX = tableLeft;
      doc.text('#', headerX + 5, rowTop + 8, { width: colWidths.step - 10 });
      headerX += colWidths.step;

      doc.text('Step Name', headerX + 5, rowTop + 8, { width: colWidths.name - 10 });
      headerX += colWidths.name;

      doc.text('Type', headerX + 5, rowTop + 8, { width: colWidths.type - 10 });
      headerX += colWidths.type;

      doc.text('Duration', headerX + 5, rowTop + 8, { width: colWidths.duration - 10 });
      headerX += colWidths.duration;

      doc.text('Status', headerX + 5, rowTop + 8, { width: colWidths.status - 10 });

      rowTop += 25;
    }

    // Row background
    if (rowColor) {
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, rowHeight)
         .fillAndStroke('#f8fafc', '#edf2f7');
    }
    rowColor = !rowColor;

    // Row data
    doc.fontSize(9)
       .font('Roboto')
       .fillColor('#2d3748');

    let cellX = tableLeft;
    const cellPadding = rowHeight > 25 ? 10 : 8; // More padding for taller rows

    // Step number
    doc.text(String(index + 1), cellX + 5, rowTop + cellPadding, { width: colWidths.step - 10 });
    cellX += colWidths.step;

    // Step name
    doc.text(stepNameText, cellX + 5, rowTop + cellPadding, {
      width: colWidths.name - 10,
      lineGap: 2 // Space between lines
    });
    cellX += colWidths.name;

    // Step type
    doc.text(step.type || 'Unknown', cellX + 5, rowTop + cellPadding, {
      width: colWidths.type - 10,
      lineGap: 2
    });
    cellX += colWidths.type;

    // Duration
    doc.text(formatDuration(step.duration), cellX + 5, rowTop + cellPadding, { width: colWidths.duration - 10 });
    cellX += colWidths.duration;

    // Status
    const statusText = step.success ? 'Passed' : (step.error ? 'Failed' : 'Unknown');
    const statusColor = step.success ? '#38a169' : (step.error ? '#e53e3e' : '#718096');

    doc.fillColor(statusColor)
       .font('Roboto-Bold')
       .text(statusText, cellX + 5, rowTop + cellPadding, { width: colWidths.status - 10 })
       .fillColor('#2d3748')
       .font('Roboto');

    // Show error message if present
    if (step.error) {
      const errorY = rowTop + rowHeight - estimateTextHeight(step.error, CONTENT_WIDTH - 20, 8) - 5;
      doc.fontSize(8)
         .font('Roboto-Italic')
         .fillColor('#e53e3e')
         .text(step.error, tableLeft + 10, errorY, {
           width: CONTENT_WIDTH - 20,
           lineGap: 1
         });
    }

    rowTop += rowHeight;
  });

  // Space after table
  doc.y = rowTop + 20;
}

/**
 * Adds screenshots to the PDF with improved layout
 */
async function addScreenshots(doc: any, report: TestReport): Promise<void> {
  // Check if any steps have screenshots
  const hasScreenshots = report.steps && report.steps.some(step =>
    step.beforeScreenshotUrl || step.afterScreenshotUrl
  );

  if (!hasScreenshots) {
    return; // Exit if no screenshots
  }

  // Add a new page for screenshots
  doc.addPage();

  // Add title
  doc.fontSize(16)
     .font('Roboto-Bold')
     .fillColor('#2d3748')
     .text('Screenshots', PAGE_MARGIN, PAGE_MARGIN);

  doc.y += 15;

  // Current Y position for adding screenshots
  let currentY = doc.y;

  // Maximum steps per page - dynamic based on content
  let stepsOnCurrentPage = 0;

  // For each step, add screenshots
  for (let index = 0; index < (report.steps?.length || 0); index++) {
    const step = report.steps?.[index];

    if (!step) continue;

    // Skip if no screenshots
    if (!step.beforeScreenshotUrl && !step.afterScreenshotUrl) {
      continue;
    }

    // Calculate step name height based on length
    const stepNameText = step.name || step.description || `Step ${index + 1}`;
    const stepNameHeight = estimateTextHeight(stepNameText, CONTENT_WIDTH - 10, 12);

    // Dynamic page height calculation
    const availableHeight = doc.page.height - currentY - PAGE_MARGIN;
    const estimatedStepHeight = stepNameHeight + 20; // Estimated height for step title

    // Determine if we have both screenshots or just one
    const hasBothScreenshots = step.beforeScreenshotUrl && step.afterScreenshotUrl;

    // Calculate total height needed for this step
    let totalEstimatedHeight = estimatedStepHeight;

    // Height for screenshots - use a more dynamic approach
    const singleImageMaxHeight = 250; // Maximum height for a single image

    if (hasBothScreenshots) {
      // For side-by-side layout, use the same height
      totalEstimatedHeight += singleImageMaxHeight + 30; // Image height + padding
    } else {
      // For single image, use standard height
      totalEstimatedHeight += singleImageMaxHeight + 30; // Image height + padding
    }

    // Page check - if not enough space or max steps reached, add new page
    if (currentY > doc.page.height - 200 ||
        availableHeight < totalEstimatedHeight ||
        stepsOnCurrentPage >= 1) { // Limit to 1 step per page for better spacing
      doc.addPage();
      currentY = PAGE_MARGIN;
      stepsOnCurrentPage = 0;
    }

    // Step title with background highlight
    doc.rect(PAGE_MARGIN - 5, currentY - 5, CONTENT_WIDTH + 10, stepNameHeight + 10)
       .fillAndStroke('#f0f4f8', '#e1e8ed');

    doc.fontSize(12)
       .font('Roboto-Bold')
       .fillColor('#2d3748')
       .text(`Step ${index + 1}: ${stepNameText}`,
             PAGE_MARGIN, currentY, { width: CONTENT_WIDTH });

    currentY += stepNameHeight + 15; // More space after title

    // If both screenshots exist, show them side by side with better spacing
    if (hasBothScreenshots) {
      try {
        const beforeImageUrl = step.beforeScreenshotUrl;
        const afterImageUrl = step.afterScreenshotUrl;

        if (beforeImageUrl && beforeImageUrl.startsWith('http') &&
            afterImageUrl && afterImageUrl.startsWith('http')) {

          // Calculate width for side-by-side display
          const halfWidth = (CONTENT_WIDTH - 40) / 2; // 40px spacing between images

          // Before Screenshot label
          doc.rect(PAGE_MARGIN - 2, currentY - 2, 120, 16)
             .fillAndStroke('#edf2f7', '#e2e8f0');

          doc.fontSize(10)
             .font('Roboto-Bold')
             .fillColor('#4a5568')
             .text('Before Screenshot:', PAGE_MARGIN, currentY);

          // After Screenshot label - position with more spacing
          doc.rect(PAGE_MARGIN + halfWidth + 40 - 2, currentY - 2, 120, 16)
             .fillAndStroke('#edf2f7', '#e2e8f0');

          doc.fontSize(10)
             .font('Roboto-Bold')
             .fillColor('#4a5568')
             .text('After Screenshot:', PAGE_MARGIN + halfWidth + 40, currentY);

          currentY += 20;

          try {
            // Download images
            const beforeImageBuffer = await fetchImageFromUrl(beforeImageUrl);
            const afterImageBuffer = await fetchImageFromUrl(afterImageUrl);

            // Get image dimensions
            const beforeImg = await getImageDimensions(beforeImageBuffer);
            const afterImg = await getImageDimensions(afterImageBuffer);

            // Calculate aspect ratios
            const beforeAspectRatio = beforeImg.width / beforeImg.height;
            const afterAspectRatio = afterImg.width / afterImg.height;

            // Calculate optimal heights based on aspect ratio
            const maxHeight = 300; // Maximum height allowed
            const minHeight = 150; // Minimum height to ensure visibility

            // Calculate heights based on aspect ratios
            let beforeHeight = halfWidth / beforeAspectRatio;
            let afterHeight = halfWidth / afterAspectRatio;

            // Constrain heights
            beforeHeight = Math.min(maxHeight, Math.max(minHeight, beforeHeight));
            afterHeight = Math.min(maxHeight, Math.max(minHeight, afterHeight));

            // Use the maximum height for both frames to keep alignment
            const frameHeight = Math.max(beforeHeight, afterHeight);

            // Frame for Before Screenshot
            doc.rect(PAGE_MARGIN - 2, currentY - 2, halfWidth + 4, frameHeight + 4)
               .stroke('#e2e8f0');

            // Frame for After Screenshot - with increased spacing
            doc.rect(PAGE_MARGIN + halfWidth + 40 - 2, currentY - 2, halfWidth + 4, frameHeight + 4)
               .stroke('#e2e8f0');

            // Add Before Screenshot
            doc.image(beforeImageBuffer, PAGE_MARGIN, currentY, {
              fit: [halfWidth, beforeHeight],
              align: 'center',
              valign: 'center'
            });

            // Save the Y position after the first image
            const beforeImageEndY = currentY + frameHeight;

            // Reset Y position to add the second image at the same height
            doc.y = currentY;

            // Add After Screenshot with increased spacing
            doc.image(afterImageBuffer, PAGE_MARGIN + halfWidth + 40, currentY, {
              fit: [halfWidth, afterHeight],
              align: 'center',
              valign: 'center'
            });

            // Get the Y position after the second image
            const afterImageEndY = currentY + frameHeight;

            // Use the maximum height of both images plus padding
            currentY = Math.max(beforeImageEndY, afterImageEndY) + 30;
            stepsOnCurrentPage++;
          } catch (imgError: any) {
            // Show URLs if images can't be downloaded
            logger.error(`Error downloading images: ${imgError.message}`);
            doc.text(`Before Screenshot URL: ${beforeImageUrl}`, PAGE_MARGIN, currentY);
            doc.text(`After Screenshot URL: ${afterImageUrl}`, PAGE_MARGIN + halfWidth + 40, currentY);
            currentY += 25;
          }
        }
      } catch (imgError: any) {
        logger.error(`Error adding screenshots: ${imgError.message}`);
      }
    } else {
      // If only one screenshot exists, show it full width

      // Before Screenshot
      if (step.beforeScreenshotUrl) {
        try {
          const imageUrl = step.beforeScreenshotUrl;

          // Check if image URL is valid
          if (imageUrl && imageUrl.startsWith('http')) {
            // Label background
            doc.rect(PAGE_MARGIN - 2, currentY - 2, 120, 16)
               .fillAndStroke('#edf2f7', '#e2e8f0');

            doc.fontSize(10)
               .font('Roboto-Bold')
               .fillColor('#4a5568')
               .text('Before Screenshot:', PAGE_MARGIN, currentY);

            currentY += 20;

            try {
              // Download image from URL
              const imageBuffer = await fetchImageFromUrl(imageUrl);

              // Get image dimensions
              const img = await getImageDimensions(imageBuffer);

              // Calculate aspect ratio
              const aspectRatio = img.width / img.height;

              // Calculate optimal height based on aspect ratio
              const maxWidth = CONTENT_WIDTH;
              const maxHeight = 350; // Maximum height allowed
              const minHeight = 150; // Minimum height to ensure visibility

              // Calculate height based on aspect ratio
              let imgHeight = maxWidth / aspectRatio;

              // Constrain height
              imgHeight = Math.min(maxHeight, Math.max(minHeight, imgHeight));

              // Draw frame for image
              doc.rect(PAGE_MARGIN - 2, currentY - 2, maxWidth + 4, imgHeight + 4)
                 .stroke('#e2e8f0');

              // Add image to PDF
              doc.image(imageBuffer, PAGE_MARGIN, currentY, {
                fit: [maxWidth, imgHeight],
                align: 'center',
                valign: 'center'
              });

              // Update Y position based on image height plus padding
              currentY = currentY + imgHeight + 30; // More space after image
              stepsOnCurrentPage++;
            } catch (imgError: any) {
              // Show URL if image can't be downloaded
              logger.error(`Error downloading image: ${imgError.message}`);
              doc.text(`Screenshot URL: ${imageUrl}`, PAGE_MARGIN, currentY);
              currentY += 25;
            }
          }
        } catch (imgError: any) {
          logger.error(`Error adding before screenshot: ${imgError.message}`);
        }
      }

      // Page check
      if (currentY > doc.page.height - 200 || stepsOnCurrentPage >= 1) {
        doc.addPage();
        currentY = PAGE_MARGIN;
        stepsOnCurrentPage = 0;
      }

      // After Screenshot
      if (step.afterScreenshotUrl) {
        try {
          const imageUrl = step.afterScreenshotUrl;

          // Check if image URL is valid
          if (imageUrl && imageUrl.startsWith('http')) {
            // Label background
            doc.rect(PAGE_MARGIN - 2, currentY - 2, 120, 16)
               .fillAndStroke('#edf2f7', '#e2e8f0');

            doc.fontSize(10)
               .font('Roboto-Bold')
               .fillColor('#4a5568')
               .text('After Screenshot:', PAGE_MARGIN, currentY);

            currentY += 20;

            try {
              // Download image from URL
              const imageBuffer = await fetchImageFromUrl(imageUrl);

              // Get image dimensions
              const img = await getImageDimensions(imageBuffer);

              // Calculate aspect ratio
              const aspectRatio = img.width / img.height;

              // Calculate optimal height based on aspect ratio
              const maxWidth = CONTENT_WIDTH;
              const maxHeight = 350; // Maximum height allowed
              const minHeight = 150; // Minimum height to ensure visibility

              // Calculate height based on aspect ratio
              let imgHeight = maxWidth / aspectRatio;

              // Constrain height
              imgHeight = Math.min(maxHeight, Math.max(minHeight, imgHeight));

              // Draw frame for image
              doc.rect(PAGE_MARGIN - 2, currentY - 2, maxWidth + 4, imgHeight + 4)
                 .stroke('#e2e8f0');

              // Add image to PDF
              doc.image(imageBuffer, PAGE_MARGIN, currentY, {
                fit: [maxWidth, imgHeight],
                align: 'center',
                valign: 'center'
              });

              // Update Y position based on image height plus padding
              currentY = currentY + imgHeight + 30; // More space after image
              stepsOnCurrentPage++;
            } catch (imgError: any) {
              // Show URL if image can't be downloaded
              logger.error(`Error downloading image: ${imgError.message}`);
              doc.text(`Screenshot URL: ${imageUrl}`, PAGE_MARGIN, currentY);
              currentY += 25;
            }
          }
        } catch (imgError: any) {
          logger.error(`Error adding after screenshot: ${imgError.message}`);
        }
      }
    }

    // Add separator line between steps
    if (index < (report.steps?.length || 0) - 1) {
      doc.moveTo(PAGE_MARGIN, currentY + 15)
         .lineTo(PAGE_MARGIN + CONTENT_WIDTH, currentY + 15)
         .strokeColor('#e2e8f0')
         .stroke();
      currentY += 40; // More space after separator line
    }
  }
}

/**
 * Adds page numbers to the PDF
 */
function addPageNumbers(doc: any): void {
  const pageCount = doc.bufferedPageRange().count;

  for (let i = 0; i < pageCount; i++) {
    doc.switchToPage(i);

    doc.fontSize(8)
       .font('Roboto')
       .fillColor('#718096')
       .text(
         `Page ${i + 1} of ${pageCount}`,
         PAGE_MARGIN,
         doc.page.height - 20,
         { align: 'center', width: CONTENT_WIDTH }
       );
  }
}

/**
 * Formats duration
 * @param duration Duration in milliseconds
 * @returns Formatted duration string
 */
function formatDuration(duration?: number): string {
  if (!duration) return 'N/A';

  // Convert milliseconds to seconds
  const seconds = duration / 1000;

  if (seconds < 1) {
    return `${Math.round(duration)}ms`;
  } else if (seconds < 60) {
    return `${seconds.toFixed(2)}s`;
  } else {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = (seconds % 60).toFixed(0);
    return `${minutes}m ${remainingSeconds}s`;
  }
}

/**
 * Downloads an image from URL
 * @param url Image URL
 * @returns Promise<Buffer> Image buffer
 */
async function fetchImageFromUrl(url: string): Promise<Buffer> {
  try {
    // Disable HTTPS certificate validation (for development environment)
    const agent = new https.Agent({
      rejectUnauthorized: false
    });

    // Download image
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      httpsAgent: agent,
      timeout: 10000 // 10 second timeout
    });

    // Return as buffer
    return Buffer.from(response.data, 'binary');
  } catch (error: any) {
    logger.error(`Error fetching image from URL ${url}: ${error.message}`);
    throw error;
  }
}

/**
 * Gets image dimensions from buffer
 * @param imageBuffer Image buffer
 * @returns Promise<{width: number, height: number}> Image dimensions
 */
async function getImageDimensions(imageBuffer: Buffer): Promise<{width: number, height: number}> {
  try {
    // Create a temporary PDFDocument
    const tempDoc = new PDFDocument();

    // Use PDFKit's image handling capabilities
    // PDFDocument doesn't have openImage method directly in its type definition
    // We need to use a workaround to access the image dimensions

    // Create a temporary buffer to avoid writing to disk
    const tempBuffers: Buffer[] = [];
    tempDoc.on('data', tempBuffers.push.bind(tempBuffers));

    // Add the image to the document to get its dimensions
    // The image method returns information about the image
    // We need to use type assertion since the TypeScript definitions are incomplete
    const imgDimensions = tempDoc.image(imageBuffer, 0, 0, { fit: [1000, 1000] }) as unknown as { width: number, height: number };

    // End the document
    tempDoc.end();

    return {
      width: imgDimensions?.width || 800, // Default width if not available
      height: imgDimensions?.height || 600 // Default height if not available
    };
  } catch (error: any) {
    logger.error(`Error getting image dimensions: ${error.message}`);
    // Return default dimensions if we can't determine actual size
    return {
      width: 800,
      height: 600
    };
  }
}

/**
 * Estimates text height
 * @param text Text
 * @param width Width
 * @param fontSize Font size
 * @returns Estimated height
 */
function estimateTextHeight(text: string | undefined, width: number, fontSize: number): number {
  if (!text) return 25;

  // Average character width (based on font size)
  const avgCharWidth = fontSize * 0.6; // Increased for better estimation

  // Characters per line
  const charsPerLine = Math.floor(width / avgCharWidth);

  // Count actual lines by simulating text wrapping
  let lines = 0;
  let currentLineLength = 0;

  // Split by words to simulate wrapping
  const words = text.split(' ');

  for (const word of words) {
    // If adding this word would exceed the line width, start a new line
    if (currentLineLength + word.length + 1 > charsPerLine) {
      lines++;
      currentLineLength = word.length;
    } else {
      // Add word length plus a space
      currentLineLength += word.length + 1;
    }
  }

  // Add the last line if there's any content
  if (currentLineLength > 0) {
    lines++;
  }

  // Ensure at least one line
  lines = Math.max(1, lines);

  // Line height (based on font size)
  const lineHeight = fontSize * 1.5; // Increased line height for better spacing

  // Total height (minimum 30px)
  return Math.max(30, lines * lineHeight);
}
