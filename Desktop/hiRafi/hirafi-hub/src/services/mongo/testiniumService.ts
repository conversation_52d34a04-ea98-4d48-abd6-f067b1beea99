/**
 * Testinium service module
 * Handles operations related to Testinium plugin using @devicepark/sdk and storage-client
 */

import { logger } from '../../utils/logger.js';
import {
  isMongoDBInitialized,
  pluginsCollection,
  testiniumAppsCollection
} from './dbConnection.js';
import { DeviceParkSDK } from '@devicepark/sdk';
import { FileStorageClient } from '@devicepark/storage-client';
import { init } from '@devicepark/shared';
import fs from 'fs';
import { Readable } from 'stream';

/**
 * Sanitize filename for DevicePark Storage
 * Removes Turkish characters and special characters that might cause issues
 * @param fileName Original filename
 * @returns Sanitized filename
 */
function sanitizeFileName(fileName: string): string {
  // Turkish character mapping
  const turkishCharMap: { [key: string]: string } = {
    'ç': 'c', 'Ç': 'C',
    'ğ': 'g', 'Ğ': 'G',
    'ı': 'i', 'I': 'I',
    'İ': 'I', 'i': 'i',
    'ö': 'o', 'Ö': 'O',
    'ş': 's', 'Ş': 'S',
    'ü': 'u', 'Ü': 'U'
  };

  let sanitized = fileName;

  // Replace Turkish characters
  Object.keys(turkishCharMap).forEach(turkishChar => {
    const regex = new RegExp(turkishChar, 'g');
    sanitized = sanitized.replace(regex, turkishCharMap[turkishChar]);
  });

  // Remove or replace other special characters, keep only alphanumeric, dots, hyphens, and underscores
  sanitized = sanitized.replace(/[^a-zA-Z0-9.\-_]/g, '_');

  // Remove multiple consecutive underscores
  sanitized = sanitized.replace(/_+/g, '_');

  // Remove leading/trailing underscores
  sanitized = sanitized.replace(/^_+|_+$/g, '');

  // Ensure the file has an extension
  if (!sanitized.includes('.')) {
    const originalExt = fileName.split('.').pop();
    if (originalExt) {
      sanitized += '.' + originalExt;
    }
  }

  return sanitized;
}

/**
 * Testinium configuration interface
 */
interface TestiniumConfig {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  issuerUri: string;
}

/**
 * Testinium app interface for database storage
 */
interface TestiniumApp {
  id: string;
  name: string;
  fileName: string;
  fileKey: string; // DevicePark storage file key
  version?: string;
  description?: string;
  fileSize?: number;
  uploadedAt: Date;
  uploadedBy?: string;
  companyId: string;
  teamId?: string;
  metadata?: {
    platform?: string;
    bundleId?: string;
    versionCode?: string;
    minSdkVersion?: string;
    targetSdkVersion?: string;
    [key: string]: any;
  };
}

/**
 * Verify Testinium connection
 * @param config Testinium configuration
 * @returns Promise resolving to verification result
 */
export async function verifyTestiniumConnection(
  apiUrl: string,
  clientId: string,
  clientSecret: string,
  issuerUri: string
): Promise<{ success: boolean; message: string }> {
  try {
    logger.info(`[TESTINIUM] Testing connection with parameters:`);
    logger.info(`[TESTINIUM] - apiUrl: ${apiUrl}`);
    logger.info(`[TESTINIUM] - clientId: ${clientId}`);
    logger.info(`[TESTINIUM] - clientSecret: ${clientSecret ? '[REDACTED]' : 'undefined'}`);
    logger.info(`[TESTINIUM] - issuerUri: ${issuerUri}`);

    // Initialize SDK with provided configuration
    const sdk = DeviceParkSDK.init({
      apiUrl,
      authentication: {
        clientId,
        clientSecret,
        issuerUri
      }
    });

    logger.info(`[TESTINIUM] SDK initialized, attempting to get devices...`);

    // Test connection by trying to get devices
    const devices = await sdk.management.getAllDevices();

    logger.info(`[TESTINIUM] Connection successful, found ${devices?.length || 0} devices`);

    return {
      success: true,
      message: 'Testinium connection successful'
    };
  } catch (error: any) {
    logger.error(`[TESTINIUM] Connection failed with error:`, error);
    logger.error(`[TESTINIUM] Error message: ${error.message}`);
    logger.error(`[TESTINIUM] Error stack: ${error.stack}`);

    // Provide more specific error messages based on error type
    let errorMessage = error.message || 'Unknown error occurred';

    if (error.message && error.message.includes('401')) {
      errorMessage = 'Authentication failed. Please check your credentials.';
    } else if (error.message && error.message.includes('404')) {
      errorMessage = 'API endpoint not found. Please check your API URL.';
    } else if (error.message && error.message.includes('timeout')) {
      errorMessage = 'Connection timeout. Please check your network connection and API URL.';
    } else if (error.message && error.message.includes('ENOTFOUND')) {
      errorMessage = 'Cannot resolve API URL. Please check if the URL is correct.';
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}

/**
 * Update Testinium configuration
 * @param teamId Team ID
 * @param companyId Company ID
 * @param config Testinium configuration
 * @returns Promise resolving to update result
 */
export async function updateTestiniumConfig(
  teamId: string,
  companyId: string,
  config: TestiniumConfig
): Promise<{ success: boolean; message: string }> {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database not initialized'
      };
    }

    logger.info(`[TESTINIUM] Updating configuration for team: ${teamId}, company: ${companyId}`);

    const now = new Date();

    // Create the Testinium plugin object
    const testiniumPlugin = {
      id: 'testinium',
      name: 'Testinium Integration',
      description: 'Integrate with Testinium for mobile device testing using DevicePark SDK',
      active: true,
      config: {
        apiUrl: config.apiUrl,
        clientId: config.clientId,
        clientSecret: config.clientSecret,
        issuerUri: config.issuerUri,
        updatedAt: now
      },
      createdAt: now,
      updatedAt: now
    };

    // Check if the team/company already has plugins
    const userPlugins = await pluginsCollection.findOne({ teamId, companyId });

    if (!userPlugins) {
      // Create new plugins entry
      await pluginsCollection.insertOne({
        teamId,
        companyId,
        plugins: [testiniumPlugin]
      });

      logger.info(`Created Testinium plugin for team: ${teamId}, company: ${companyId}`);
      return {
        success: true,
        message: 'Testinium configuration created successfully'
      };
    }

    // Check if Testinium plugin already exists
    const existingPluginIndex = userPlugins.plugins.findIndex((plugin: any) => plugin.id === 'testinium');

    if (existingPluginIndex >= 0) {
      // Update existing Testinium plugin
      userPlugins.plugins[existingPluginIndex] = testiniumPlugin;
    } else {
      // Add new Testinium plugin
      userPlugins.plugins.push(testiniumPlugin);
    }

    // Update the plugins document
    const result = await pluginsCollection.updateOne(
      { teamId, companyId },
      {
        $set: {
          plugins: userPlugins.plugins
        }
      }
    );

    if (!result.acknowledged) {
      return {
        success: false,
        message: 'Failed to update Testinium configuration'
      };
    }

    logger.info(`Updated Testinium plugin for team: ${teamId}, company: ${companyId}`);
    return {
      success: true,
      message: 'Testinium configuration updated successfully'
    };
  } catch (error: any) {
    logger.error(`Error updating Testinium configuration: ${error.message}`);
    return {
      success: false,
      message: `Error updating Testinium configuration: ${error.message}`
    };
  }
}

/**
 * Get Testinium configuration
 * @param teamId Team ID
 * @param companyId Company ID
 * @returns Promise resolving to configuration result
 */
export async function getTestiniumConfig(teamId: string, companyId: string): Promise<{
  success: boolean;
  message: string;
  config: any;
  plugin: any;
}> {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database not initialized',
        config: null,
        plugin: null
      };
    }

    logger.info(`[TESTINIUM] Getting configuration for team: ${teamId}, company: ${companyId}`);

    // Get team/company plugins
    const pluginData = await pluginsCollection.findOne({ teamId, companyId });

    if (!pluginData) {
      return {
        success: true,
        message: 'No plugins found for team',
        config: null,
        plugin: null
      };
    }

    // Find the Testinium plugin
    const testiniumPlugin = pluginData.plugins.find((plugin: any) => plugin.id === 'testinium');

    if (!testiniumPlugin) {
      return {
        success: true,
        message: 'No Testinium configuration found',
        config: null,
        plugin: null
      };
    }

    return {
      success: true,
      message: 'Testinium configuration retrieved successfully',
      config: testiniumPlugin.config,
      plugin: testiniumPlugin
    };
  } catch (error: any) {
    logger.error(`Error getting Testinium configuration: ${error.message}`);
    return {
      success: false,
      message: `Error getting Testinium configuration: ${error.message}`,
      config: null,
      plugin: null
    };
  }
}

/**
 * Get Testinium credentials by company ID and team ID
 * This function retrieves Testinium credentials for a company or team
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to Testinium credentials
 */
export async function getTestiniumCredentialsByCompanyAndTeam(
  companyId: string,
  teamId?: string | null
) {
  try {
    // Validate input parameters
    if (!companyId) {
      logger.warn('[TESTINIUM] getTestiniumCredentialsByCompanyAndTeam: companyId is required');
      return {
        success: false,
        message: 'Company ID is required',
        credentials: null
      };
    }

    if (!isMongoDBInitialized() || !pluginsCollection) {
      logger.error('[TESTINIUM] Database connection not established');
      return {
        success: false,
        message: 'Database connection not established',
        credentials: null
      };
    }

    logger.info(`[TESTINIUM] Getting credentials for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // First, try to find a plugin document that directly matches both companyId and teamId
    if (teamId) {
      try {
        // Look for a plugin document with matching companyId and teamId
        const pluginDoc = await pluginsCollection.findOne({
          companyId: companyId,
          teamId: teamId
        });

        if (pluginDoc) {
          // Find the Testinium plugin in the plugins array
          const testiniumPlugin = pluginDoc.plugins.find((plugin: any) => plugin.id === 'testinium');

          if (testiniumPlugin && testiniumPlugin.config) {
            // Validate the credentials
            if (!testiniumPlugin.config.apiUrl || !testiniumPlugin.config.clientId ||
                !testiniumPlugin.config.clientSecret || !testiniumPlugin.config.issuerUri) {
              logger.warn(`[TESTINIUM] Incomplete Testinium credentials found in plugin document for companyId: ${companyId}, teamId: ${teamId}`);
            } else {
              return {
                success: true,
                message: 'Testinium credentials found in plugin document',
                credentials: {
                  apiUrl: testiniumPlugin.config.apiUrl,
                  clientId: testiniumPlugin.config.clientId,
                  clientSecret: testiniumPlugin.config.clientSecret,
                  issuerUri: testiniumPlugin.config.issuerUri
                }
              };
            }
          } else {
            logger.info(`[TESTINIUM] Plugin document found but no Testinium plugin for companyId: ${companyId}, teamId: ${teamId}`);
          }
        } else {
          logger.info(`[TESTINIUM] No plugin document found with matching companyId: ${companyId} and teamId: ${teamId}`);
        }
      } catch (error: any) {
        logger.error(`[TESTINIUM] Error searching for plugin document with teamId: ${error.message}`);
      }
    }

    // If no teamId provided or no team-specific config found, try company-level config
    try {
      // Look for a plugin document with matching companyId only
      const pluginDoc = await pluginsCollection.findOne({
        companyId: companyId
      });

      if (pluginDoc) {
        // Find the Testinium plugin in the plugins array
        const testiniumPlugin = pluginDoc.plugins.find((plugin: any) => plugin.id === 'testinium');

        if (testiniumPlugin && testiniumPlugin.config) {
          // Validate the credentials
          if (!testiniumPlugin.config.apiUrl || !testiniumPlugin.config.clientId ||
              !testiniumPlugin.config.clientSecret || !testiniumPlugin.config.issuerUri) {
            logger.warn(`[TESTINIUM] Incomplete Testinium credentials found in plugin document for companyId: ${companyId}`);
          } else {
            return {
              success: true,
              message: 'Testinium credentials found in plugin document',
              credentials: {
                apiUrl: testiniumPlugin.config.apiUrl,
                clientId: testiniumPlugin.config.clientId,
                clientSecret: testiniumPlugin.config.clientSecret,
                issuerUri: testiniumPlugin.config.issuerUri
              }
            };
          }
        } else {
          logger.info(`[TESTINIUM] Plugin document found but no Testinium plugin for companyId: ${companyId}`);
        }
      } else {
        logger.info(`[TESTINIUM] No plugin document found with matching companyId: ${companyId}`);
      }
    } catch (error: any) {
      logger.error(`[TESTINIUM] Error searching for plugin document with companyId: ${error.message}`);
    }

    return {
      success: false,
      message: `No Testinium credentials found for company: ${companyId}, team: ${teamId || 'N/A'}. Please configure Testinium credentials in the plugins section.`,
      credentials: null
    };
  } catch (error: any) {
    // Log detailed error information
    logger.error(`[TESTINIUM] Error getting Testinium credentials: ${error.message}`, {
      error: error,
      stack: error.stack,
      companyId: companyId,
      teamId: teamId
    });

    return {
      success: false,
      message: `Error getting Testinium credentials: ${error.message}`,
      credentials: null
    };
  }
}

/**
 * Get Testinium devices
 * @param teamId Team ID
 * @param companyId Company ID
 * @returns Promise resolving to devices result
 */
export async function getTestiniumDevices(teamId: string, companyId: string): Promise<{
  success: boolean;
  message: string;
  devices: any[];
}> {
  try {
    logger.info(`[TESTINIUM] Getting devices for team: ${teamId}, company: ${companyId}`);

    // Get configuration first
    const configResult = await getTestiniumConfig(teamId, companyId);

    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        message: 'Testinium configuration not found',
        devices: []
      };
    }

    const { apiUrl, clientId, clientSecret, issuerUri } = configResult.config;

    // Validate credentials before SDK initialization
    if (!apiUrl || !clientId || !clientSecret || !issuerUri) {
      return {
        success: false,
        message: 'Incomplete Testinium configuration. Please check your API URL, client ID, client secret, and issuer URI.',
        devices: []
      };
    }

    // Initialize SDK
    const sdk = DeviceParkSDK.init({
      apiUrl,
      authentication: {
        clientId,
        clientSecret,
        issuerUri
      }
    });

    // Get devices
    const devices = await sdk.management.getAllDevices();

    logger.info(`[TESTINIUM] Retrieved ${devices?.length || 0} devices for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: 'Testinium devices retrieved successfully',
      devices: devices || []
    };
  } catch (error: any) {
    logger.error(`Error getting Testinium devices: ${error.message}`);
    
    // Provide more specific error messages
    let errorMessage = error.message || 'Unknown error occurred';
    
    if (error.message && error.message.includes('401')) {
      errorMessage = 'Authentication failed. Please check your Testinium credentials.';
    } else if (error.message && error.message.includes('404')) {
      errorMessage = 'API endpoint not found. Please check your API URL.';
    } else if (error.message && error.message.includes('timeout')) {
      errorMessage = 'Connection timeout. Please check your network connection and API URL.';
    } else if (error.message && error.message.includes('ENOTFOUND')) {
      errorMessage = 'Cannot resolve API URL. Please check if the URL is correct.';
    }
    
    return {
      success: false,
      message: `Error getting Testinium devices: ${errorMessage}`,
      devices: []
    };
  }
}

/**
 * Create device allocation
 * @param teamId Team ID
 * @param companyId Company ID
 * @param allocationRequest Allocation request
 * @returns Promise resolving to allocation result
 */
export async function createTestiniumAllocation(
  teamId: string,
  companyId: string,
  allocationRequest: any
): Promise<{ success: boolean; message: string; allocation?: any }> {
  try {
    logger.info(`[TESTINIUM] Creating allocation for team: ${teamId}, company: ${companyId}`);

    // Get configuration first
    const configResult = await getTestiniumConfig(teamId, companyId);

    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        message: 'Testinium configuration not found'
      };
    }

    const { apiUrl, clientId, clientSecret, issuerUri } = configResult.config;

    // Initialize SDK
    const sdk = DeviceParkSDK.init({
      apiUrl,
      authentication: {
        clientId,
        clientSecret,
        issuerUri
      }
    });

    // Create allocation
    const allocation = await sdk.allocation.createAllocation(allocationRequest);

    logger.info(`[TESTINIUM] Created allocation for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: 'Device allocation created successfully',
      allocation
    };
  } catch (error: any) {
    logger.error(`Error creating Testinium allocation: ${error.message}`);
    return {
      success: false,
      message: `Error creating allocation: ${error.message}`
    };
  }
}

/**
 * Start device session
 * @param teamId Team ID
 * @param companyId Company ID
 * @param sessionRequest Session request
 * @returns Promise resolving to session result
 */
export async function startTestiniumSession(
  teamId: string,
  companyId: string,
  sessionRequest: any
): Promise<{ success: boolean; message: string; session?: any }> {
  try {
    logger.info(`[TESTINIUM] Starting session for team: ${teamId}, company: ${companyId}`);

    // Get configuration first
    const configResult = await getTestiniumConfig(teamId, companyId);

    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        message: 'Testinium configuration not found'
      };
    }

    const { apiUrl, clientId, clientSecret, issuerUri } = configResult.config;

    // Initialize SDK
    const sdk = DeviceParkSDK.init({
      apiUrl,
      authentication: {
        clientId,
        clientSecret,
        issuerUri
      }
    });

    // Start session
    const session = await sdk.session.startSession(sessionRequest);

    logger.info(`[TESTINIUM] Started session for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: 'Device session started successfully',
      session
    };
  } catch (error: any) {
    logger.error(`Error starting Testinium session: ${error.message}`);
    return {
      success: false,
      message: `Error starting session: ${error.message}`
    };
  }
}

/**
 * Close device session
 * @param teamId Team ID
 * @param companyId Company ID
 * @param sessionId Session ID
 * @returns Promise resolving to close result
 */
export async function closeTestiniumSession(
  teamId: string,
  companyId: string,
  sessionId: string
): Promise<{ success: boolean; message: string }> {
  try {
    logger.info(`[TESTINIUM] Closing session ${sessionId} for team: ${teamId}, company: ${companyId}`);

    // Get configuration first
    const configResult = await getTestiniumConfig(teamId, companyId);

    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        message: 'Testinium configuration not found'
      };
    }

    const { apiUrl, clientId, clientSecret, issuerUri } = configResult.config;

    // Initialize SDK
    const sdk = DeviceParkSDK.init({
      apiUrl,
      authentication: {
        clientId,
        clientSecret,
        issuerUri
      }
    });

    // Close session
    await sdk.session.closeSession(sessionId);

    logger.info(`[TESTINIUM] Closed session ${sessionId} for team: ${teamId}, company: ${companyId}`);

    return {
      success: true,
      message: 'Device session closed successfully'
    };
  } catch (error: any) {
    logger.error(`Error closing Testinium session: ${error.message}`);
    return {
      success: false,
      message: `Error closing session: ${error.message}`
    };
  }
}

/**
 * Initialize DevicePark Storage Client with Testinium config
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to initialization result
 */
async function initializeStorageClient(companyId: string, teamId?: string): Promise<{
  success: boolean;
  message: string;
  config?: TestiniumConfig;
}> {
  try {
    // Get the Testinium configuration
    const configResult = await getTestiniumConfig(teamId || '', companyId || '');

    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        message: 'Testinium configuration not found'
      };
    }

    const { apiUrl, clientId, clientSecret, issuerUri } = configResult.config;

    // Initialize DevicePark shared config
    init({
      apiUrl,
      authentication: {
        clientId,
        clientSecret,
        issuerUri
      }
    });

    return {
      success: true,
      message: 'Storage client initialized successfully',
      config: configResult.config
    };
  } catch (error: any) {
    logger.error(`Error initializing storage client: ${error.message}`);
    return {
      success: false,
      message: `Error initializing storage client: ${error.message}`
    };
  }
}

/**
 * Upload app to Testinium using DevicePark Storage Client
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @param fileBuffer File buffer
 * @param fileName File name
 * @param description File description
 * @param userId User ID who uploaded the file
 * @returns Promise resolving to upload result
 */
export async function uploadTestiniumApp(
  companyId: string,
  teamId: string | undefined,
  fileBuffer: Buffer,
  fileName: string,
  description?: string,
  userId?: string
): Promise<{
  success: boolean;
  message: string;
  app?: TestiniumApp;
}> {
  try {
    if (!isMongoDBInitialized() || !testiniumAppsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    // Initialize storage client
    const initResult = await initializeStorageClient(companyId, teamId);
    if (!initResult.success) {
      return {
        success: false,
        message: initResult.message
      };
    }

    // Sanitize filename to avoid issues with Turkish characters and special characters
    const sanitizedFileName = sanitizeFileName(fileName);

    // Create readable stream from buffer
    const stream = Readable.from(fileBuffer);

    // Upload file to DevicePark Storage using sanitized filename
    const uploadResponse = await FileStorageClient.uploadFileStream(sanitizedFileName, stream);

    if (!uploadResponse.fileKey) {
      return {
        success: false,
        message: 'Failed to upload app to Testinium storage'
      };
    }

    // Get file metadata
    const metadata = await FileStorageClient.getFileMetadata(uploadResponse.fileKey);

    // Create app record for our database
    const appId = `testinium_app_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const appRecord: TestiniumApp = {
      id: appId,
      name: fileName.replace(/\.[^/.]+$/, ""), // Remove file extension for name (use original for display)
      fileName: fileName, // Keep original filename for display purposes
      fileKey: uploadResponse.fileKey,
      description: description,
      fileSize: fileBuffer.length,
      uploadedAt: new Date(),
      uploadedBy: userId,
      companyId: companyId,
      teamId: teamId,
      metadata: {
        ...metadata || {},
        originalFileName: fileName,
        sanitizedFileName: sanitizedFileName
      }
    };

    // Save app record to our database
    await testiniumAppsCollection.insertOne(appRecord);

    return {
      success: true,
      message: 'App uploaded to Testinium successfully',
      app: appRecord
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Error uploading app to Testinium: ${error.message}`
    };
  }
}

/**
 * Get Testinium apps from our database
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to apps result
 */
export async function getTestiniumApps(companyId: string, teamId?: string): Promise<{
  success: boolean;
  message: string;
  apps: TestiniumApp[];
}> {
  try {
    if (!isMongoDBInitialized() || !testiniumAppsCollection) {
      return {
        success: false,
        message: 'Database connection not established',
        apps: []
      };
    }

    // Build query filter based on data isolation requirements
    const queryFilter: any = { companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    // Get apps from our database
    const apps = await testiniumAppsCollection
      .find(queryFilter)
      .sort({ uploadedAt: -1 })
      .toArray();

    return {
      success: true,
      message: 'Testinium apps retrieved successfully',
      apps: apps.map(app => ({
        id: app.id,
        name: app.name,
        fileName: app.fileName,
        fileKey: app.fileKey,
        version: app.version,
        description: app.description,
        fileSize: app.fileSize,
        uploadedAt: app.uploadedAt,
        uploadedBy: app.uploadedBy,
        companyId: app.companyId,
        teamId: app.teamId,
        metadata: app.metadata
      })) as TestiniumApp[]
    };
  } catch (error: any) {
    logger.error(`Error getting Testinium apps: ${error.message}`);
    return {
      success: false,
      message: `Error getting Testinium apps: ${error.message}`,
      apps: []
    };
  }
}

/**
 * Delete app from Testinium and our database
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @param appId App ID
 * @returns Promise resolving to delete result
 */
export async function deleteTestiniumApp(
  companyId: string,
  teamId: string | undefined,
  appId: string
): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    if (!isMongoDBInitialized() || !testiniumAppsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    // Build query filter for data isolation
    const queryFilter: any = { id: appId, companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    // Find the app record first
    const appRecord = await testiniumAppsCollection.findOne(queryFilter);

    if (!appRecord) {
      return {
        success: false,
        message: 'App not found'
      };
    }

    // Initialize storage client
    const initResult = await initializeStorageClient(companyId, teamId);
    if (!initResult.success) {
      return {
        success: false,
        message: initResult.message
      };
    }

    try {
      // Delete file from DevicePark Storage
      await FileStorageClient.deleteFile(appRecord.fileKey);
    } catch (storageError: any) {
      logger.warn(`Failed to delete file from storage: ${storageError.message}`);
      // Continue with database deletion even if storage deletion fails
    }

    // Delete app record from our database
    const deleteResult = await testiniumAppsCollection.deleteOne(queryFilter);

    if (deleteResult.deletedCount === 0) {
      return {
        success: false,
        message: 'Failed to delete app from database'
      };
    }

    logger.info(`Deleted app ${appRecord.fileName} from Testinium for company: ${companyId}, team: ${teamId || 'N/A'}`);

    return {
      success: true,
      message: 'App deleted successfully'
    };
  } catch (error: any) {
    logger.error(`Error deleting Testinium app: ${error.message}`);
    return {
      success: false,
      message: `Error deleting app: ${error.message}`
    };
  }
}

/**
 * Delete Testinium configuration
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @returns Promise resolving to delete result
 */
export async function deleteTestiniumConfig(companyId: string, teamId?: string) {
  try {
    if (!isMongoDBInitialized() || !pluginsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    if (!companyId) {
      return {
        success: false,
        message: 'Company ID is required'
      };
    }

    logger.info(`Deleting Testinium configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);

    // Build query filter
    const queryFilter: any = { companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    // Remove the testinium plugin from the plugins array
    const result = await pluginsCollection.updateOne(
      queryFilter,
      {
        $pull: { plugins: { id: 'testinium' } } as any,
        $set: { updatedAt: new Date() }
      }
    );

    if (result.modifiedCount > 0) {
      logger.info(`Successfully deleted Testinium configuration for company: ${companyId}, team: ${teamId || 'N/A'}`);
      return {
        success: true,
        message: 'Testinium configuration deleted successfully'
      };
    } else {
      logger.warn(`No Testinium configuration found to delete for company: ${companyId}, team: ${teamId || 'N/A'}`);
      return {
        success: false,
        message: 'No Testinium configuration found to delete'
      };
    }
  } catch (error: any) {
    logger.error(`Error deleting Testinium configuration: ${error.message}`);
    return {
      success: false,
      message: `Error deleting Testinium configuration: ${error.message}`
    };
  }
}

/**
 * Download app from Testinium storage
 * @param companyId Company ID
 * @param teamId Team ID (optional)
 * @param appId App ID
 * @returns Promise resolving to download stream
 */
export async function downloadTestiniumApp(
  companyId: string,
  teamId: string | undefined,
  appId: string
): Promise<{
  success: boolean;
  message: string;
  stream?: NodeJS.ReadableStream;
  fileName?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !testiniumAppsCollection) {
      return {
        success: false,
        message: 'Database connection not established'
      };
    }

    // Build query filter for data isolation
    const queryFilter: any = { id: appId, companyId };
    if (teamId) {
      queryFilter.teamId = teamId;
    }

    // Find the app record
    const appRecord = await testiniumAppsCollection.findOne(queryFilter);

    if (!appRecord) {
      return {
        success: false,
        message: 'App not found'
      };
    }

    // Initialize storage client
    const initResult = await initializeStorageClient(companyId, teamId);
    if (!initResult.success) {
      return {
        success: false,
        message: initResult.message
      };
    }

    // Download file stream from DevicePark Storage
    const fileStream = await FileStorageClient.downloadFileStream(appRecord.fileKey);

    return {
      success: true,
      message: 'App download stream created successfully',
      stream: fileStream,
      fileName: appRecord.fileName
    };
  } catch (error: any) {
    logger.error(`Error downloading Testinium app: ${error.message}`);
    return {
      success: false,
      message: `Error downloading app: ${error.message}`
    };
  }
}
