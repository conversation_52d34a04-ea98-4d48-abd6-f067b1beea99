"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { TableIcon, Plus, Edit, Copy, MoreHorizontal, Download, RefreshCw, Trash2, Globe, Clock, AlertCircle } from "lucide-react"
import type { DataSet, CustomEnvironment } from "@/types/test-data"
import { getEnvironmentColor, getEnvironmentLabel, formatDate } from "@/lib/test-data-utils"

interface DataSetsTableProps {
  dataSets: DataSet[]
  customEnvironments: CustomEnvironment[]
  onNewDataSet: () => void
  onEditDataSet: (id: string) => void
  onDeleteDataSet: (id: string) => void
  isLoading?: boolean
  error?: string | null
  onRetry?: () => void
}

export function DataSetsTable({
  dataSets,
  customEnvironments,
  onNewDataSet,
  onEditDataSet,
  onDeleteDataSet,
  isLoading = false,
  error = null,
  onRetry
}: DataSetsTableProps) {

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-5 w-48" />
                  <Skeleton className="h-4 w-96" />
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                </div>
                <Skeleton className="h-8 w-8" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>{error}</span>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  if (dataSets.length === 0) {
    return (
      <Card className="border-dashed">
        <CardContent className="pt-6 text-center">
          <TableIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium">No Data Sets Found</h3>
          <p className="text-sm text-gray-500 mt-1 mb-4">No data sets match your search criteria or filter.</p>
          <Button
            onClick={onNewDataSet}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Data Set
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-950 rounded-lg border shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <div className="min-w-full">
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b px-4 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            <div className="col-span-4">Name</div>
            <div className="col-span-2">Environment</div>
            <div className="col-span-1 text-center">Variables</div>
            <div className="col-span-2">Last Updated</div>
            <div className="col-span-2">Tags</div>
            <div className="col-span-1 text-right">Actions</div>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-800">
            {dataSets.map((dataSet, index) => {
              const visibleTags = (dataSet.tags || []).slice(0, 3)
              const remainingTagsCount = (dataSet.tags || []).length - 3

              return (
                <div
                  key={dataSet.id || `dataset-${index}`}
                  className="px-4 py-3 grid grid-cols-12 gap-4 items-center bg-white dark:bg-gray-950 hover:bg-gray-50 dark:hover:bg-gray-900/50"
                >
                  <div className="col-span-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{dataSet.name || 'Unnamed Data Set'}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate">{dataSet.description || 'No description provided'}</div>
                  </div>
                  <div className="col-span-2">
                    {/* Show environments from metadata */}
                    {dataSet.metadata?.environments && dataSet.metadata.environments.length > 0 ? (
                      <div className="flex flex-wrap gap-1 items-center">
                        {dataSet.metadata.environments.slice(0, 2).map((env: any, envIndex: number) => (
                          <Badge
                            key={`${dataSet.id}-env-${envIndex}-${env.id}`}
                            variant="outline"
                            className={`border-${env.color}-200 bg-${env.color}-50 text-${env.color}-700 dark:border-${env.color}-800/30 dark:bg-${env.color}-900/20 dark:text-${env.color}-300`}
                          >
                            <div className="flex items-center gap-1.5">
                              <Globe className="h-3 w-3" />
                              {env.name}
                            </div>
                          </Badge>
                        ))}
                        {dataSet.metadata.environments.length > 2 && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Badge
                                variant="outline"
                                className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30 text-xs cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800/30"
                              >
                                +{dataSet.metadata.environments.length - 2} more
                              </Badge>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start" className="w-48">
                              <DropdownMenuLabel>All Environments</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              {dataSet.metadata.environments.map((env: any, envIndex: number) => (
                                <DropdownMenuItem key={`${dataSet.id}-dropdown-env-${envIndex}-${env.id}`} className="text-xs">
                                  <Badge
                                    variant="outline"
                                    className={`border-${env.color}-200 bg-${env.color}-50 text-${env.color}-700 dark:border-${env.color}-800/30 dark:bg-${env.color}-900/20 dark:text-${env.color}-300`}
                                  >
                                    <div className="flex items-center gap-1.5">
                                      <Globe className="h-3 w-3" />
                                      {env.name}
                                    </div>
                                  </Badge>
                                </DropdownMenuItem>
                              ))}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </div>
                    ) : (
                      <Badge variant="outline" className={getEnvironmentColor(dataSet.environment, customEnvironments)}>
                        <div className="flex items-center gap-1.5">
                          <Globe className="h-3 w-3" />
                          {getEnvironmentLabel(dataSet.environment, customEnvironments)}
                        </div>
                      </Badge>
                    )}
                  </div>
                  <div className="col-span-1 flex justify-center">
                    <div className="h-6 w-6 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 flex items-center justify-center text-xs font-medium">
                      {dataSet.metadata?.totalVariables || dataSet.metadata?.variables?.length || (dataSet.variables || []).length}
                    </div>
                  </div>
                  <div className="col-span-2">
                    <div className="flex items-center gap-1.5">
                      <Clock className="h-3.5 w-3.5 text-gray-400" />
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(dataSet.updatedAt || dataSet.lastUpdated)}
                      </span>
                    </div>
                  </div>
                  <div className="col-span-2">
                    <div className="flex flex-wrap gap-1 items-center">
                      {visibleTags.map((tag, index) => (
                        <Badge
                          key={`${dataSet.id}-tag-${index}-${tag}`}
                          variant="outline"
                          className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-300 dark:border-indigo-800/30 text-xs"
                        >
                          {tag || 'Unnamed Tag'}
                        </Badge>
                      ))}
                      {remainingTagsCount > 0 && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Badge
                              variant="outline"
                              className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30 text-xs cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800/30"
                            >
                              +{remainingTagsCount} more
                            </Badge>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="start" className="w-48">
                            <DropdownMenuLabel>All Tags</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {(dataSet.tags || []).map((tag, index) => (
                              <DropdownMenuItem key={`${dataSet.id}-dropdown-tag-${index}-${tag}`} className="text-xs">
                                <Badge
                                  variant="outline"
                                  className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-300 dark:border-indigo-800/30"
                                >
                                  {tag || 'Unnamed Tag'}
                                </Badge>
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                  <div className="col-span-1 flex justify-end">
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                        onClick={() => onEditDataSet(dataSet.id)}
                        title="Edit data set"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Download className="h-4 w-4 mr-2" />
                            Export
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Refresh Data
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600" onClick={() => onDeleteDataSet(dataSet.id)}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}
