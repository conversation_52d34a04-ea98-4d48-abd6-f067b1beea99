/**
 * Test Communication Interface
 * 
 * Defines the contract for test communication services within the test execution module.
 * This interface provides a clean abstraction for test-related communication operations.
 */

/**
 * Test communication service configuration
 */
export interface TestCommunicationConfig {
  /** Enable detailed logging for communication operations */
  enableLogging?: boolean;
  /** Default timeout for communication operations in milliseconds */
  defaultTimeout?: number;
  /** Maximum retry attempts for failed operations */
  maxRetries?: number;
  /** Retry delay in milliseconds */
  retryDelay?: number;
}

/**
 * Test status data structure
 */
export interface TestStatusData {
  /** Test status */
  status: string;
  /** Test result data */
  result: any;
  /** Node ID that processed the test */
  nodeId: string;
  /** Timestamp when the status was updated */
  timestamp: string;
}

/**
 * Test result data structure
 */
export interface TestResultData {
  /** Test ID */
  testId: string;
  /** Test result data */
  result: any;
  /** Test status */
  status: string;
  /** Node ID that processed the test */
  nodeId: string;
  /** Timestamp when the test was completed */
  timestamp: string;
  /** Completion timestamp */
  completedAt: string;
}

/**
 * Test communication headers structure
 */
export interface TestCommunicationHeaders {
  /** Content type header */
  'Content-Type': string;
  /** Node authentication key */
  'x-node-key': string;
  /** Additional headers */
  [key: string]: string;
}

/**
 * Test communication statistics
 */
export interface TestCommunicationStats {
  /** Service type identifier */
  serviceType: string;
  /** Service creation timestamp */
  createdAt: string;
  /** Number of operations performed */
  operationsCount?: number;
  /** Number of successful operations */
  successfulOperations?: number;
  /** Number of failed operations */
  failedOperations?: number;
}

/**
 * Interface for test communication service
 * 
 * This interface defines the contract for handling test-related communication
 * operations within the test execution module. It provides methods for data
 * preparation, validation, and response handling.
 */
export interface ITestCommunicationService {
  /**
   * Initialize the service with configuration
   * @param config Service configuration
   */
  initialize(config: TestCommunicationConfig): Promise<void>;

  /**
   * Check if the service is initialized
   */
  isInitialized(): boolean;

  /**
   * Prepare test status update data
   * @param testId Test ID
   * @param status Test status
   * @param result Test result
   * @param nodeId Node ID
   */
  prepareTestStatusData(testId: string, status: string, result: any, nodeId: string): TestStatusData;

  /**
   * Validate test status update parameters
   * @param testId Test ID
   * @param status Test status
   * @param nodeId Node ID
   */
  validateTestStatusParams(testId: string, status: string, nodeId: string): boolean;

  /**
   * Handle test status update success
   * @param testId Test ID
   * @param status Test status
   */
  handleTestStatusSuccess(testId: string, status: string): void;

  /**
   * Handle test status update failure
   * @param testId Test ID
   * @param status Test status
   * @param error Error details
   */
  handleTestStatusFailure(testId: string, status: string, error: any): void;

  /**
   * Validate test response
   * @param response Test response
   * @param validator Response validator function
   */
  validateTestResponse(response: any, validator: (data: any) => boolean): boolean;

  /**
   * Handle next test response
   * @param response Next test response
   */
  handleNextTestResponse(response: any): any | null;

  /**
   * Handle next test failure
   * @param error Error details
   * @param context Request context
   */
  handleNextTestFailure(error: any, context: string): void;

  /**
   * Create test communication headers
   * @param nodeSecretKey Node secret key
   * @param additionalHeaders Additional headers
   */
  createTestHeaders(nodeSecretKey: string, additionalHeaders?: Record<string, string>): TestCommunicationHeaders;

  /**
   * Prepare test result data
   * @param testId Test ID
   * @param result Test result
   * @param status Test status
   * @param nodeId Node ID
   */
  prepareTestResultData(testId: string, result: any, status: string, nodeId: string): TestResultData;

  /**
   * Log test communication activity
   * @param activity Activity description
   * @param testId Test ID (optional)
   * @param details Additional details (optional)
   */
  logActivity(activity: string, testId?: string, details?: any): void;

  /**
   * Get test communication statistics
   */
  getStats(): TestCommunicationStats;

  /**
   * Cleanup resources and reset state
   */
  cleanup(): Promise<void>;
}
