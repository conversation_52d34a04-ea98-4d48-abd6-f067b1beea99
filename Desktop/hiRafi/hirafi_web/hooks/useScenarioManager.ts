/**
 * Scenario Manager Hook (Optimized)
 *
 * Single source of truth for scenario and folder management
 * Combines data fetching and actions in an optimized, unified interface
 */

import { useCallback, useEffect, useMemo } from 'react'
import { useSWRScenarios } from './useSWRScenarios'
import { useScenariosActions } from './useScenariosActions'
import { useScenarioStore } from '@/store/scenarioStore'
import type { ScenarioWithMeta } from '@/types/scenario-extended'

/**
 * Options for the scenario manager
 */
interface ScenarioManagerOptions {
  searchQuery?: string
  tagFilter?: string
  testTypeFilter?: string
  scenarioStatus?: string
  autoFetch?: boolean
  editMode?: boolean
}

/**
 * Optimized scenario manager hook
 * Provides a unified interface for all scenario and folder operations
 */
export function useScenarioManager(options: ScenarioManagerOptions = {}) {
  const {
    searchQuery: optionsSearchQuery,
    tagFilter: optionsTagFilter,
    testTypeFilter: optionsTestTypeFilter,
    scenarioStatus: optionsScenarioStatus,
    autoFetch = true,
    editMode = false // eslint-disable-line @typescript-eslint/no-unused-vars
  } = options

  // Get store actions
  const {
    setSearchQuery,
    setTagFilter,
    setTestTypeFilter,
    setStatusFilter,
    selectedFolder,
    selectedScenarios,
    setSelectedScenarios,
    setSelectedFolder,
    addSelectedScenario,
    removeSelectedScenario,
    clearSelectedScenarios
  } = useScenarioStore()

  // Initialize store with options if provided
  useEffect(() => {
    if (optionsSearchQuery !== undefined) {
      setSearchQuery(optionsSearchQuery)
    }
    if (optionsTagFilter !== undefined) {
      setTagFilter(optionsTagFilter)
    }
    if (optionsTestTypeFilter !== undefined) {
      setTestTypeFilter(optionsTestTypeFilter)
    }
    if (optionsScenarioStatus !== undefined) {
      setStatusFilter(optionsScenarioStatus)
    }
  }, [
    optionsSearchQuery,
    optionsTagFilter,
    optionsTestTypeFilter,
    optionsScenarioStatus,
    setSearchQuery,
    setTagFilter,
    setTestTypeFilter,
    setStatusFilter
  ])

  // Get unified data
  const {
    scenarios,
    folders,
    uncategorizedScenarios,
    scenariosByFolder,
    isLoading,
    isValidating,
    error,
    scenariosTotal,
    foldersTotal,
    refresh,
    optimisticDeleteScenario,
    optimisticBulkDeleteScenarios
  } = useSWRScenarios({ autoFetch })

  // Get actions
  const {
    createFolder,
    renameFolder,
    deleteFolder,
    deleteScenario,
    duplicateScenario,
    moveScenarioToFolder,
    bulkDeleteScenarios,
    bulkDuplicateScenarios,
    isOperationInProgress
  } = useScenariosActions()

  // Compute visible scenarios based on selected folder
  const visibleScenarios = useMemo(() => {
    if (!selectedFolder) {
      return scenarios
    }
    
    if (selectedFolder === 'uncategorized') {
      return uncategorizedScenarios
    }
    
    return scenariosByFolder[selectedFolder] || []
  }, [scenarios, uncategorizedScenarios, scenariosByFolder, selectedFolder])

  // Folder selection handler
  const handleSelectFolder = useCallback((folderId: string | null) => {
    setSelectedFolder(folderId)
    // Clear selected scenarios when changing folders
    setSelectedScenarios([])
  }, [setSelectedFolder, setSelectedScenarios])

  // Scenario selection handlers
  const handleSelectScenario = useCallback((scenarioId: string, selected: boolean) => {
    if (selected) {
      addSelectedScenario(scenarioId)
    } else {
      removeSelectedScenario(scenarioId)
    }
  }, [addSelectedScenario, removeSelectedScenario])

  const handleSelectAllScenarios = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedScenarios(visibleScenarios.map(s => s.id))
    } else {
      clearSelectedScenarios()
    }
  }, [setSelectedScenarios, clearSelectedScenarios, visibleScenarios])

  // Bulk operations with selected scenarios
  const deleteSelectedScenarios = useCallback(async () => {
    if (selectedScenarios.length === 0) return { success: false, error: 'No scenarios selected' }

    const result = await bulkDeleteScenarios(selectedScenarios)
    if (result.success) {
      clearSelectedScenarios()
    }
    return result
  }, [selectedScenarios, bulkDeleteScenarios, clearSelectedScenarios])

  const duplicateSelectedScenarios = useCallback(async (targetFolderId?: string) => {
    if (selectedScenarios.length === 0) return { success: false, error: 'No scenarios selected' }

    const result = await bulkDuplicateScenarios(selectedScenarios, { targetFolderId })
    if (result.success) {
      clearSelectedScenarios()
    }
    return result
  }, [selectedScenarios, bulkDuplicateScenarios, clearSelectedScenarios])

  // Enhanced refresh function
  const refreshAllData = useCallback(async () => {
    await refresh()
  }, [refresh])

  // Optimistic delete wrapper
  const optimisticDeleteScenarioWrapper = useCallback(async (scenarioId: string) => {
    // Immediately update UI
    await optimisticDeleteScenario(scenarioId)

    // Call the actual delete API
    const result = await deleteScenario(scenarioId)

    // If delete failed, refresh to restore the scenario
    if (!result.success) {
      await refresh()
    }

    return result
  }, [optimisticDeleteScenario, deleteScenario, refresh])

  // Optimistic bulk delete wrapper
  const optimisticBulkDeleteScenariosWrapper = useCallback(async (scenarioIds: string[]) => {
    // Immediately update UI
    await optimisticBulkDeleteScenarios(scenarioIds)

    // Call the actual bulk delete API
    const result = await bulkDeleteScenarios(scenarioIds)

    // If delete failed, refresh to restore the scenarios
    if (!result.success) {
      await refresh()
    }

    return result
  }, [optimisticBulkDeleteScenarios, bulkDeleteScenarios, refresh])

  // Computed values
  const hasSelectedScenarios = selectedScenarios.length > 0
  const allVisibleScenariosSelected = visibleScenarios.length > 0 && 
    selectedScenarios.length === visibleScenarios.length

  return {
    // Data
    scenarios,
    visibleScenarios: visibleScenarios as ScenarioWithMeta[],
    folders,
    uncategorizedScenarios,
    scenariosByFolder,

    // Loading states
    scenariosLoading: isLoading,
    foldersLoading: isLoading,
    scenariosError: error,
    foldersError: error,
    isValidating,

    // Counts
    scenariosTotal,
    foldersTotal,
    visibleScenariosCount: visibleScenarios.length,

    // Selection state
    selectedFolder,
    selectedScenarios,
    hasSelectedScenarios,
    allVisibleScenariosSelected,

    // Operation states
    isUpdating: isValidating,
    isCreatingFolder: isOperationInProgress('createFolder'),
    isUpdatingFolder: isOperationInProgress('renameFolder'),
    isDeletingFolder: isOperationInProgress('deleteFolder'),

    // Folder actions
    createFolder,
    renameFolder,
    deleteFolder,
    selectFolder: handleSelectFolder,

    // Scenario actions (with optimistic updates)
    deleteScenario: optimisticDeleteScenarioWrapper,
    duplicateScenario,
    moveScenarioToFolder,

    // Selection actions
    selectScenario: handleSelectScenario,
    selectAllScenarios: handleSelectAllScenarios,
    addSelectedScenario,
    removeSelectedScenario,
    clearSelectedScenarios,

    // Bulk actions
    deleteSelectedScenarios,
    duplicateSelectedScenarios,
    bulkDeleteScenarios: optimisticBulkDeleteScenariosWrapper,
    bulkDuplicateScenarios,

    // Refresh actions
    refreshScenarios: refreshAllData,
    refreshFolders: refreshAllData,
    refreshAllData,

    // Utilities
    isOperationInProgress
  }
}
