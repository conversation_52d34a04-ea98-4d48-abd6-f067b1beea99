/**
 * Redis Connector Interface
 * Defines the contract for Redis connection and queue management
 * Follows dependency injection patterns instead of singleton
 */

import { EventEmitter } from 'events';
import { Queue } from 'bullmq';
import { RedisClientType } from 'redis';

export interface IRedisConnector extends EventEmitter {
  /**
   * Initialize the Redis connector
   */
  initialize(nodeId?: string): Promise<boolean>;

  /**
   * Check if Redis is connected
   */
  isConnected(): boolean;

  /**
   * Get the underlying Redis client
   */
  getClient(): RedisClientType | null;

  /**
   * Get a queue instance
   */
  getQueue(queueName: string): Queue | null;

  // Node registration removed - using WebSocket-only registration

  /**
   * Close the Redis connector
   */
  close(): Promise<void>;
}

export interface IRedisConnectionManager {
  /**
   * Get the Redis client instance
   */
  getClient(): RedisClientType | null;

  /**
   * Check if Redis is connected
   */
  isConnected(): boolean;

  /**
   * Connect to Redis
   */
  connect(): Promise<boolean>;

  /**
   * Disconnect from Redis
   */
  disconnect(): Promise<void>;

  /**
   * Get connection status
   */
  getConnectionStatus(): 'connected' | 'disconnected' | 'connecting' | 'error';
}

export interface RedisConnectorEvents {
  'connected': () => void;
  'disconnected': () => void;
  'error': (error: Error) => void;
  'testClaimedByNode': (testId: string, nodeId: string) => void;
}

export const REDIS_QUEUE_NAMES = {
  TEST_QUEUE: 'test-queue',
  RESULT_QUEUE: 'result-queue',
  STEP_PROGRESS_QUEUE: 'step-progress-queue',
  TEST_STATUS_QUEUE: 'test-status-queue',
  NODE_STATUS_QUEUE: 'node-status-queue'
} as const;

export const REDIS_JOB_TYPES = {
  TEST_EXECUTION: 'test-execution',
  TEST_RESULT: 'test-result',
  STEP_PROGRESS: 'step-progress',
  TEST_STATUS_UPDATE: 'test-status-update',
  NODE_STATUS_UPDATE: 'node-status-update'
} as const;
