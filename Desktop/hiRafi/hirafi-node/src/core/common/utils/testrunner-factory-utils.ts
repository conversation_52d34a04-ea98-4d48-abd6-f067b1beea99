/**
 * TestRunner Factory Utilities
 * 
 * PHASE 4 FIX: Consolidates duplicate createForPlatform patterns and disposal tracking code
 * Eliminates duplication across 5 TestRunner service factory methods
 */

/**
 * Platform type for TestRunner services
 */
export type TestRunnerPlatform = 'android' | 'web';

/**
 * Disposal tracking metadata interface
 */
export interface DisposalTrackingMetadata {
  created: string;
  platform: TestRunnerPlatform;
  type: string;
  hasDispose?: boolean;
  hasStreamManagement?: boolean;
  storageProvider?: string;
  deviceProvider?: string;
}

/**
 * Factory configuration for platform-specific services
 */
export interface PlatformServiceConfig {
  platform: TestRunnerPlatform;
  config: any;
  executionCoordinator?: any;
  artifactStorage?: any;
  deviceManagerFactory?: any;
}

/**
 * Factory function type for platform services
 */
export type PlatformServiceFactory<T = any> = (config: PlatformServiceConfig) => Promise<T> | T;

/**
 * Enhanced disposal method wrapper
 */
export interface EnhancedDisposableService {
  dispose?: () => Promise<void>;
  __disposeTracker?: DisposalTrackingMetadata;
}

/**
 * TestRunner Factory Utilities Class
 * Provides consolidated patterns for creating platform-specific services
 */
export class TestRunnerFactoryUtils {
  
  /**
   * Add disposal tracking metadata to a service instance
   * Consolidates the repeated disposal tracking pattern
   */
  static addDisposalTracking<T extends EnhancedDisposableService>(
    instance: T,
    metadata: Omit<DisposalTrackingMetadata, 'created'>
  ): T {
    if (instance && typeof instance === 'object') {
      (instance as any).__disposeTracker = {
        created: new Date().toISOString(),
        ...metadata,
        hasDispose: typeof (instance as any).dispose === 'function'
      };
    }
    return instance;
  }

  /**
   * Enhance disposal method with logging
   * Consolidates the repeated disposal enhancement pattern
   */
  static enhanceDisposal<T extends EnhancedDisposableService>(
    instance: T,
    serviceType: string,
    platform: TestRunnerPlatform
  ): T {
    const originalDispose = (instance as any).dispose?.bind?.(instance);
    if (originalDispose && typeof originalDispose === 'function') {
      (instance as any).dispose = async () => {
        try {
          console.log(`[${serviceType}] Disposing ${serviceType.toLowerCase()} for platform ${platform}`);
          await originalDispose();
          console.log(`[${serviceType}] Successfully disposed ${serviceType.toLowerCase()} for platform ${platform}`);
        } catch (error: any) {
          console.error(`[${serviceType}] Error disposing ${serviceType.toLowerCase()} for platform ${platform}: ${error.message}`);
          throw error;
        }
      };
    }
    return instance;
  }

  /**
   * Create platform service factory with consolidated pattern
   * Eliminates duplication in createForPlatform methods
   */
  static createPlatformServiceFactory<T>(
    factoryFunction: PlatformServiceFactory<T>,
    serviceType: string,
    enhanceDisposal: boolean = false
  ) {
    return {
      createForPlatform: async (platform: TestRunnerPlatform, config: any, ...additionalArgs: any[]) => {
        // Build configuration object
        const serviceConfig: PlatformServiceConfig = {
          platform,
          config,
          executionCoordinator: additionalArgs[0],
          artifactStorage: additionalArgs[1],
          deviceManagerFactory: additionalArgs[2]
        };

        // Create the service instance
        const instance = await factoryFunction(serviceConfig) as EnhancedDisposableService;

        // Add disposal tracking
        const metadata: Omit<DisposalTrackingMetadata, 'created'> = {
          platform,
          type: serviceType
        };

        TestRunnerFactoryUtils.addDisposalTracking(instance, metadata);

        // Enhance disposal if requested
        if (enhanceDisposal) {
          TestRunnerFactoryUtils.enhanceDisposal(instance, serviceType, platform);
        }

        return instance as T;
      }
    };
  }

  /**
   * Detect device provider from configuration
   * Consolidates repeated device provider detection logic
   */
  static detectDeviceProvider(config: any): 'sauceLabs' | 'testinium' | undefined {
    // Check explicit provider setting
    if (config.deviceProvider) {
      return config.deviceProvider;
    }
    
    // Check for SauceLabs configuration (support both paths)
    if (config.sauceLabs && (config.sauceLabs.username || config.sauceLabs.accessKey)) {
      return 'sauceLabs';
    }
    
    if (config.android?.sauceLabs && (config.android.sauceLabs.username || config.android.sauceLabs.accessKey)) {
      return 'sauceLabs';
    }
    
    // Check for Testinium configuration (support both paths)
    if (config.testinium && (config.testinium.apiUrl || config.testinium.clientId)) {
      return 'testinium';
    }
    
    if (config.android?.testinium && (config.android.testinium.apiUrl || config.android.testinium.clientId)) {
      return 'testinium';
    }
    
    // Legacy fallback checks
    if (config.username && config.accessKey) {
      return 'sauceLabs';
    }
    
    return 'sauceLabs'; // Default fallback
  }

  /**
   * Create step handler registry factory
   * Specialized factory for step handler registries
   */
  static createStepHandlerRegistryFactory(createRegistryFunction: (platform: TestRunnerPlatform) => any) {
    return {
      createForPlatform: (platform: TestRunnerPlatform) => {
        const registry = createRegistryFunction(platform);
        
        return TestRunnerFactoryUtils.addDisposalTracking(registry, {
          platform,
          type: 'StepHandlerRegistry'
        });
      }
    };
  }

  /**
   * Create metrics collector factory
   * Specialized factory for metrics collectors
   */
  static createMetricsCollectorFactory(
    createCollectorFunction: (platform: 'mobile' | 'web', config: any) => Promise<any>
  ) {
    return TestRunnerFactoryUtils.createPlatformServiceFactory(
      async (serviceConfig: PlatformServiceConfig) => {
        const collector = await createCollectorFunction(
          serviceConfig.platform === 'android' ? 'mobile' : 'web',
          serviceConfig.config
        );
        await collector.initialize(serviceConfig.config);
        return collector;
      },
      'MetricsCollector',
      false
    );
  }

  /**
   * Create screenshot provider factory
   * Specialized factory for screenshot providers
   */
  static createScreenshotProviderFactory(
    createProviderFunction: (artifactStorage: any, platform: 'mobile' | 'web', config: any) => Promise<any>
  ) {
    return TestRunnerFactoryUtils.createPlatformServiceFactory(
      async (serviceConfig: PlatformServiceConfig) => {
        const provider = await createProviderFunction(
          serviceConfig.artifactStorage,
          serviceConfig.platform === 'android' ? 'mobile' : 'web',
          serviceConfig.config
        );
        await provider.initialize(serviceConfig.config);
        
        // Add storage provider info to tracking
        const metadata = (provider as any).__disposeTracker || {};
        metadata.storageProvider = serviceConfig.artifactStorage?.getProviderName?.() || 'unknown';
        
        return provider;
      },
      'ScreenshotProvider',
      false
    );
  }

  /**
   * Create video recorder factory
   * Specialized factory for video recorders with enhanced disposal
   */
  static createVideoRecorderFactory(
    createRecorderFunction: (artifactStorage: any, platform: 'mobile' | 'web', executionCoordinator: any, config: any) => Promise<any>
  ) {
    return TestRunnerFactoryUtils.createPlatformServiceFactory(
      async (serviceConfig: PlatformServiceConfig) => {
        const recorder = await createRecorderFunction(
          serviceConfig.artifactStorage,
          serviceConfig.platform === 'android' ? 'mobile' : 'web',
          serviceConfig.executionCoordinator,
          serviceConfig.config
        );
        await recorder.initialize(serviceConfig.config);
        
        // Add enhanced tracking for video streams
        const metadata = (recorder as any).__disposeTracker || {};
        metadata.hasStreamManagement = true;
        metadata.storageProvider = serviceConfig.artifactStorage?.getProviderName?.() || 'unknown';
        
        return recorder;
      },
      'VideoRecorder',
      true // Enable enhanced disposal for video streams
    );
  }

  /**
   * Create platform manager factory
   * Specialized factory for platform managers (browser/device)
   */
  static createPlatformManagerFactory(
    createWebManagerFunction: () => Promise<any>,
    createDeviceManagerFunction: (config: any, deviceProvider: string) => Promise<any>
  ) {
    return {
      createForPlatform: async (platform: TestRunnerPlatform, config: any) => {
        const serviceConfig: PlatformServiceConfig = { platform, config };
        let platformManager: any;
        let deviceProvider: string;

        if (platform === 'web') {
          platformManager = await createWebManagerFunction();
          await platformManager.initialize(config);
          deviceProvider = 'browser';
        } else {
          const detectedProvider = TestRunnerFactoryUtils.detectDeviceProvider(config);
          deviceProvider = detectedProvider || 'sauceLabs'; // Ensure always string
          platformManager = await createDeviceManagerFunction(config, deviceProvider);
        }

        // Add disposal tracking
        TestRunnerFactoryUtils.addDisposalTracking(platformManager, {
          platform,
          type: platform === 'web' ? 'BrowserManager' : 'DeviceManager',
          deviceProvider
        });

        // Enhance disposal for platform cleanup
        TestRunnerFactoryUtils.enhanceDisposal(
          platformManager, 
          platform === 'web' ? 'BrowserManager' : 'DeviceManager', 
          platform
        );

        return platformManager;
      }
    };
  }
} 