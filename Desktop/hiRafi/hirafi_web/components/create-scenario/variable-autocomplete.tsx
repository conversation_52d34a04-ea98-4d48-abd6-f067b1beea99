"use client"

import { useState, useRef, useEffect, forwardRef, useMemo, useCallback } from "react"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useScenarioTestDataStore, useScenarioTestDataSelectors } from "@/store/scenarioTestDataStore"

// Simple debounce utility
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }) as T
}

interface VariableAutocompleteProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  rows?: number
  maxLength?: number
  showCharacterCount?: boolean
}

interface AutocompleteSuggestion {
  name: string
  description?: string
  type: string
}

export const VariableAutocomplete = forwardRef<HTMLTextAreaElement, VariableAutocompleteProps>(
  ({ value, onChange, placeholder, className, disabled, rows = 4, maxLength = 2000, showCharacterCount = true }, ref) => {
    // Local state for immediate UI updates
    const [localValue, setLocalValue] = useState(value)

    // Debounced callback for parent updates
    const debouncedOnChange = useCallback(
      debounce((newValue: string) => {
        onChange(newValue)
      }, 300),
      [onChange]
    )

    // Sync local value with prop value when it changes externally
    useEffect(() => {
      setLocalValue(value)
    }, [value])

    // Simplified state management - use controlled component pattern
    const [showSuggestions, setShowSuggestions] = useState(false)
    const [suggestions, setSuggestions] = useState<AutocompleteSuggestion[]>([])
    const [selectedIndex, setSelectedIndex] = useState(0)
    const [cursorPosition, setCursorPosition] = useState(0)
    const [currentQuery, setCurrentQuery] = useState('')
    const [scrollTop, setScrollTop] = useState(0)

    // Use ref to track if we're in the middle of a suggestion insertion
    const isInsertingSuggestionRef = useRef(false)

    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const suggestionsRef = useRef<HTMLDivElement>(null)
    const overlayRef = useRef<HTMLDivElement>(null)

    const { variables } = useScenarioTestDataStore()
    const { getVariableSuggestions } = useScenarioTestDataSelectors()

    const variableNames = useMemo(() => variables.map(v => v.name), [variables])

    const variableRegex = useMemo(() => {
      if (variableNames.length === 0) return null
      return new RegExp(`@(${variableNames.join('|')})\\b`, 'g')
    }, [variableNames])

    // Create a variable lookup map for O(1) access
    const variableMap = useMemo(() => {
      const map = new Map()
      variables.forEach(v => map.set(v.name, v))
      return map
    }, [variables])

    // Immediate scroll synchronization without throttling for smooth swipe gestures
    const handleScroll = useCallback(() => {
      const textarea = textareaRef.current
      const overlay = overlayRef.current
      if (!textarea || !overlay) return

      // Immediate synchronization - no throttling to prevent visual lag during swipe gestures
      const newScrollTop = textarea.scrollTop

      // Use transform instead of changing scrollTop for better performance
      // This prevents the overlay from moving outside container boundaries
      overlay.style.transform = `translateY(-${newScrollTop}px)`

      // Update state for any components that might need the scroll position
      setScrollTop(newScrollTop)
    }, [])

    // Optimized text change handler with immediate UI update and debounced parent callback
    const handleTextChange = useCallback((newValue: string) => {
      // Fast length check
      if (maxLength && newValue.length > maxLength) {
        newValue = newValue.substring(0, maxLength)
      }

      // Immediate local state update for responsive UI
      setLocalValue(newValue)

      // Debounced parent callback for performance
      debouncedOnChange(newValue)

      // Skip all suggestion processing if inserting
      if (isInsertingSuggestionRef.current) {
        return
      }

      // Defer heavy operations to avoid blocking input
      requestAnimationFrame(() => {
        const textarea = textareaRef.current
        if (!textarea) return

        const cursorPos = textarea.selectionStart
        setCursorPosition(cursorPos)

        // Only process suggestions if we're near an @ symbol
        const textBeforeCursor = newValue.substring(Math.max(0, cursorPos - 20), cursorPos)
        const lastAtIndex = textBeforeCursor.lastIndexOf('@')

        if (lastAtIndex !== -1) {
          const queryAfterAt = textBeforeCursor.substring(lastAtIndex + 1)

          // Quick validation without regex
          if (queryAfterAt.length > 0 && queryAfterAt.length < 15 && !/[\s\n]/.test(queryAfterAt)) {
            const slashIndex = queryAfterAt.indexOf('/')
            const variableName = slashIndex !== -1 ? queryAfterAt.substring(0, slashIndex) : queryAfterAt

            if (variableName.length > 0) {
              setCurrentQuery(variableName)
              const filteredSuggestions = getVariableSuggestions(variableName)
              setSuggestions(filteredSuggestions)
              setShowSuggestions(filteredSuggestions.length > 0)
              setSelectedIndex(0)
              return
            }
          }
        }

        setShowSuggestions(false)
        setCurrentQuery('')
      })
    }, [maxLength, getVariableSuggestions, debouncedOnChange])

    // Ultra-fast suggestion insertion
    const insertSuggestion = useCallback((suggestion: AutocompleteSuggestion) => {
      const textarea = textareaRef.current
      if (!textarea) return

      // Set flag to prevent handleTextChange from interfering
      isInsertingSuggestionRef.current = true

      // Get the current cursor position from the textarea
      const currentCursorPos = textarea.selectionStart
      const textBeforeCursor = localValue.substring(0, currentCursorPos)
      const textAfterCursor = localValue.substring(currentCursorPos)
      const lastAtIndex = textBeforeCursor.lastIndexOf('@')

      if (lastAtIndex !== -1) {
        // Use Map for O(1) variable lookup
        const variable = variableMap.get(suggestion.name)

        let insertText = suggestion.name
        let extraSpace = ' '

        // If it's a database variable, add the lookup placeholder
        if (variable?.sourceType === 'database') {
          const placeholder = variable.nameColumn || 'lookupValue'
          insertText = `${suggestion.name}/${placeholder}`
        }

        // Build the new value by replacing the query part after @
        const newValue =
          textBeforeCursor.substring(0, lastAtIndex + 1) + // Keep everything up to and including @
          insertText + extraSpace + // Insert the variable name (and placeholder if database)
          textAfterCursor // Keep everything after cursor

        // Batch state updates for better performance
        setShowSuggestions(false)
        setCurrentQuery('')
        setSuggestions([])
        setSelectedIndex(0)

        // Immediate local state update
        setLocalValue(newValue)
        // Immediate parent callback for suggestion insertion
        onChange(newValue)

        // Calculate cursor position for after insertion
        const newCursorPos = lastAtIndex + 1 + insertText.length + 1

        // Use requestAnimationFrame for better performance
        requestAnimationFrame(() => {
          // If it's a database variable, select the placeholder text for easy replacement
          if (variable?.sourceType === 'database') {
            const slashPos = lastAtIndex + 1 + suggestion.name.length + 1
            const placeholderEnd = slashPos + (variable.nameColumn || 'lookupValue').length
            textarea.setSelectionRange(slashPos, placeholderEnd)
            setCursorPosition(placeholderEnd)
          } else {
            // Position cursor after the variable name and space
            textarea.setSelectionRange(newCursorPos, newCursorPos)
            setCursorPosition(newCursorPos)
          }
          textarea.focus()

          // Reset the flag after insertion is complete
          isInsertingSuggestionRef.current = false
        })
      } else {
        // Reset flag even if insertion fails
        isInsertingSuggestionRef.current = false
      }
    }, [value, onChange, variableMap])

    // Optimized keyboard handler - minimal operations
    const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (!showSuggestions || suggestions.length === 0) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % suggestions.length)
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length)
          break
        case 'Enter':
        case 'Tab':
          e.preventDefault()
          e.stopPropagation()
          const selectedSuggestion = suggestions[selectedIndex]
          if (selectedSuggestion) {
            insertSuggestion(selectedSuggestion)
          }
          break
        case 'Escape':
          e.preventDefault()
          setShowSuggestions(false)
          setCurrentQuery('')
          setSuggestions([])
          setSelectedIndex(0)
          break
      }
    }, [showSuggestions, suggestions.length, selectedIndex, insertSuggestion, suggestions])

    // Optimized click handler - direct reference
    const handleSuggestionClick = useCallback((suggestion: AutocompleteSuggestion) => {
      insertSuggestion(suggestion)
    }, [insertSuggestion])

    // Simplified suggestion positioning - use textarea position
    const suggestionPosition = useMemo(() => {
      if (!showSuggestions) return { top: 0, left: 0 }

      const textarea = textareaRef.current
      if (!textarea) return { top: 0, left: 0 }

      const rect = textarea.getBoundingClientRect()
      return {
        top: rect.bottom + 5,
        left: rect.left
      }
    }, [showSuggestions])

    // Optimized click outside handler
    useEffect(() => {
      if (!showSuggestions) return

      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node
        if (
          suggestionsRef.current &&
          !suggestionsRef.current.contains(target) &&
          textareaRef.current &&
          !textareaRef.current.contains(target)
        ) {
          setShowSuggestions(false)
        }
      }

      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [showSuggestions])

    // Highly optimized overlay rendering with minimal DOM updates
    const overlayContent = useMemo(() => {
      if (!localValue || variableNames.length === 0) {
        return <span className="text-foreground">{localValue}</span>
      }

      // Use a more efficient approach - only highlight when @ is present
      if (!localValue.includes('@')) {
        return <span className="text-foreground">{localValue}</span>
      }

      // Split only on @ to minimize operations
      const parts = localValue.split('@')
      const result = []

      result.push(<span key="0" className="text-foreground">{parts[0]}</span>)

      for (let i = 1; i < parts.length; i++) {
        const part = parts[i]
        const spaceIndex = part.search(/[\s\n]/)
        const variablePart = spaceIndex === -1 ? part : part.substring(0, spaceIndex)
        const restPart = spaceIndex === -1 ? '' : part.substring(spaceIndex)

        // Quick check if this could be a variable
        const slashIndex = variablePart.indexOf('/')
        const varName = slashIndex === -1 ? variablePart : variablePart.substring(0, slashIndex)
        const variable = variableMap.get(varName)

        if (variable) {
          const lookupPart = slashIndex === -1 ? '' : variablePart.substring(slashIndex + 1)
          result.push(
            <span key={`var-${i}`}>
              <span className="bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 py-0.5 rounded-sm font-semibold">
                @{varName}
              </span>
              {lookupPart && (
                <>
                  <span className="text-gray-400 dark:text-gray-500">/</span>
                  <span className="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 py-0.5 rounded-sm font-semibold">
                    {lookupPart}
                  </span>
                </>
              )}
              <span className="text-foreground">{restPart}</span>
            </span>
          )
        } else {
          result.push(<span key={`text-${i}`} className="text-foreground">@{part}</span>)
        }
      }

      return result
    }, [localValue, variableNames.length, variableMap])

    // Optimized character count calculation
    const characterStats = useMemo(() => {
      const currentLength = localValue.length
      const remainingChars = maxLength ? maxLength - currentLength : 0
      const isNearLimit = maxLength ? currentLength > maxLength * 0.8 : false
      const isAtLimit = maxLength ? currentLength >= maxLength : false

      return { currentLength, remainingChars, isNearLimit, isAtLimit }
    }, [localValue.length, maxLength])

    return (
      <div className="relative variable-autocomplete-container">
        <div className="absolute inset-0 z-0 bg-background border border-input rounded-md" />

        <div
          ref={overlayRef}
          className="absolute pointer-events-none z-10 whitespace-pre-wrap break-words variable-autocomplete-overlay"
          style={{
            top: '1px',
            left: '1px',
            right: '1px',
            bottom: '1px',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
            fontSize: '14px',
            lineHeight: '20px',
            padding: '8px 12px',
            borderRadius: '6px',
            overflow: 'hidden',
          }}
        >
          {overlayContent}
        </div>

        <Textarea
          ref={(node) => {
            textareaRef.current = node
            if (typeof ref === 'function') {
              ref(node)
            } else if (ref) {
              ref.current = node
            }
          }}
          value={localValue}
          onChange={(e) => handleTextChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onScroll={handleScroll}
          placeholder={placeholder}
          className={`relative z-20 font-mono text-sm border-none bg-transparent focus:ring-0 focus:outline-none resize-none text-foreground variable-autocomplete-textarea`}
          disabled={disabled}
          rows={rows}
          maxLength={maxLength}
          style={{
            backgroundColor: 'transparent',
            color: 'transparent', // Keep text transparent to show overlay highlighting
            caretColor: 'hsl(var(--foreground))',
            border: 'none',
            outline: 'none',
            boxShadow: 'none',
            margin: '1px',
            padding: '8px 12px',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
            fontSize: '14px',
            lineHeight: '20px',
            width: 'calc(100% - 2px)',
            height: 'calc(100% - 2px)',
            overflow: 'auto',
            scrollbarWidth: 'thin',
          }}
        />

        {showCharacterCount && maxLength && (
          <div className={`absolute bottom-2 right-3 text-xs z-30 px-2 py-1 rounded-md ${
            characterStats.isAtLimit
              ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 border border-red-200 dark:border-red-800'
              : characterStats.isNearLimit
                ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700'
          }`}>
            {characterStats.currentLength}/{maxLength}
            {characterStats.isAtLimit && (
              <span className="ml-1 font-semibold">⚠️</span>
            )}
          </div>
        )}
        
        {showSuggestions && suggestions.length > 0 && (
          <div
            ref={suggestionsRef}
            className="fixed z-50 w-96 max-h-48 overflow-y-auto bg-white dark:bg-gray-950 border border-gray-200 dark:border-gray-800 rounded-md shadow-lg"
            style={{
              top: suggestionPosition.top,
              left: suggestionPosition.left,
            }}
          >
            <div className="p-3">
              <div className="flex items-center gap-2 text-xs font-semibold text-indigo-600 dark:text-indigo-400 mb-3 pb-2 border-b border-indigo-100 dark:border-indigo-800">
                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-indigo-400 to-blue-400"></div>
                Test Data Variables matching "@{currentQuery}"
                <Badge variant="secondary" className="text-xs bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400">
                  {suggestions.length}
                </Badge>
              </div>
              {suggestions.map((suggestion, index) => (
                <div
                  key={suggestion.name}
                  className={`px-3 py-3 rounded-lg cursor-pointer transition-all duration-200 ${
                    index === selectedIndex
                      ? 'bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/50 dark:to-blue-950/50 border border-indigo-200 dark:border-indigo-700 shadow-sm transform scale-[1.02]'
                      : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-indigo-50/30 dark:hover:from-gray-900 dark:hover:to-indigo-950/20 hover:shadow-sm hover:transform hover:scale-[1.01]'
                  }`}
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <div className="flex items-center gap-2">
                    {(() => {
                      const variable = variables.find(v => v.name === suggestion.name)
                      const isDatabaseVariable = variable?.sourceType === 'database'
                      const placeholder = variable?.nameColumn || 'lookupValue'
                      
                      return (
                        <span className="font-mono text-sm font-semibold text-indigo-600 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 px-2 py-1 rounded-md">
                          @{suggestion.name}
                          {isDatabaseVariable && (
                            <span className="text-gray-500 dark:text-gray-400">
                              /{placeholder}
                            </span>
                          )}
                        </span>
                      )
                    })()}
                    <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                  </div>
                  
                  {suggestion.description && (
                    <p className="text-xs text-gray-600 dark:text-gray-300 mt-2 ml-1 leading-relaxed">
                      {suggestion.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }
)

VariableAutocomplete.displayName = "VariableAutocomplete"
