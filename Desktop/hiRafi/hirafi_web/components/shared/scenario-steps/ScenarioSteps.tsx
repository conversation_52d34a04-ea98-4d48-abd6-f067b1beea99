"use client"

import React, { useState, useR<PERSON>, use<PERSON>ffect, use<PERSON><PERSON>back } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Plus,
  Sparkles,
  MousePointer,
  Clock,
  ArrowRight,
  Trash2,
  HelpCircle,
  Type as TypeIcon,
  Search,
  Binary,
  Keyboard,
  ChevronsUpDown,
  Locate,
  Hash,
  ClipboardCheck,
  Info,
  BrainCircuit,
  Target,
  GitBranch,
  RotateCcw,
  Repeat,
} from "lucide-react"
import { VariableAutocomplete } from "../../create-scenario/variable-autocomplete"
import { NestedStepEditor } from "../../create-scenario/nested-step-editor"
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd"
import { TestStep, ActionType, AIQueryField } from "@/models/scenario"
import { useDebounce } from "@/hooks/useDebounce"
import { v4 as uuidv4 } from "uuid"
import { createNewStep, updateStepField, updateStepType as updateStepTypeUtil } from '@/lib/utils/step-management'

import { Switch } from "@/components/ui/switch"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"



const actionConfig: {
  [key: string]: {
    label: string;
    icon: React.ElementType;
    description: string;
    supportsDeepThink?: boolean;
    valueLabel: string;
    valuePlaceholder: string;
    valueAsTextarea?: boolean;
    info: string;
    examples: string[];
  };
} = {
  // --- Instant Actions ---
  aiTap: {
    label: "AI Tap/Click",
    icon: MousePointer,
    description: "Click or tap on a specific element.",
    supportsDeepThink: true,
    valueLabel: "Element to Tap",
    valuePlaceholder: "e.g., The 'Submit' button",
    info: "Directly performs a tap/click on a UI element described by natural language. Faster and more reliable than AI Action for simple clicks.",
    examples: ["The 'Submit' button", "The user profile picture at the top right"],
  },
  aiInput: {
    label: "AI Input",
    icon: TypeIcon,
    description: "Type text into an input field.",
    supportsDeepThink: true,
    valueLabel: "Text to Input",
    valuePlaceholder: "e.g., my-secret-password",
    info: "Types text into a specified input field. Requires a target element and the text to input.",
    examples: [
      "<EMAIL>",
      "my-secret-password",
      "John Doe"
    ],
  },
  aiHover: {
    label: "AI Hover",
    icon: MousePointer,
    description: "Move the mouse cursor over an element.",
    supportsDeepThink: true,
    valueLabel: "Element to Hover Over",
    valuePlaceholder: "e.g., The user profile picture",
    info: "Moves the mouse cursor over a specified element.",
    examples: ["The user profile picture", "The 'File' menu item"],
  },
  aiKeyboardPress: {
    label: "AI Keyboard Press",
    icon: Keyboard,
    description: "Simulate pressing a single keyboard key.",
    supportsDeepThink: true,
    valueLabel: "Key to Press",
    valuePlaceholder: "e.g., Enter",
    info: "Simulates pressing a single keyboard key on the page or focused element. Optionally specify a target element.",
    examples: [
      "Enter",
      "ArrowDown",
      "Escape"
    ],
  },
  aiScroll: {
    label: "AI Scroll",
    icon: ChevronsUpDown,
    description: "Scroll the page or an element.",
    supportsDeepThink: true,
    valueLabel: "Direction & Element (optional)",
    valuePlaceholder: "e.g., down, or 'the user list' down",
    info: "Scrolls the page or a specific scrollable element in a given direction.",
    examples: ["down", "up", '"the user list" down'],
  },
  aiRightClick: {
    label: "AI Right Click",
    icon: MousePointer,
    description: "Perform a right-click on an element.",
    supportsDeepThink: true,
    valueLabel: "Element to Right-Click",
    valuePlaceholder: "e.g., The file icon",
    info: "Performs a right-click (context menu click) on a specified element.",
    examples: ["The file icon", "The image in the gallery"],
  },

  // --- Data Extraction ---
  aiQuery: {
    label: "AI Query",
    icon: Search,
    description: "Extract structured data (JSON) from the UI.",
    supportsDeepThink: false,
    valueLabel: "Query Object Definition",
    valuePlaceholder: `e.g., { "title": "The page title" }`,
    info: "Extracts structured data (JSON) from the UI based on a query object. Keys are data names, values are natural language descriptions of what to extract.",
    examples: [
      '{\n  "pageTitle": "The title of the current page",\n  "userName": "The currently logged in user\'s name"\n}',
      '{\n  "productNames": "A list of all product names on the page as string[]"\n}',
    ],
  },
  aiString: {
    label: "AI String",
    icon: TypeIcon,
    description: "Extract a single text value from the UI.",
    supportsDeepThink: false, // aiString does not support deepThink parameter
    valueLabel: "Text to Extract",
    valuePlaceholder: "e.g., The welcome message text",
    info: "Extracts a single text (string) value from the UI. Note: Deep Focus is not available for this action type.",
    examples: ["The welcome message text", "The text of the first news headline"],
  },
  aiNumber: {
    label: "AI Number",
    icon: Hash,
    description: "Extract a single numerical value from the UI.",
    supportsDeepThink: false, // aiNumber does not support deepThink parameter
    valueLabel: "Number to Extract",
    valuePlaceholder: "e.g., The number of items in the cart",
    info: "Extracts a single numerical value from the UI. Note: Deep Focus is not available for this action type.",
    examples: ["The number of items in the cart", "The price of the selected item"],
  },
  aiBoolean: {
    label: "AI Boolean",
    icon: Binary,
    description: "Ask a yes/no question about the UI.",
    supportsDeepThink: false, // aiBoolean does not support deepThink parameter
    valueLabel: "Question to Ask",
    valuePlaceholder: "e.g., Is the 'Save' button disabled?",
    info: "Asks a yes/no question about the UI and returns a boolean (true/false) result. Note: Deep Focus is not available for this action type.",
    examples: ["Is the 'Save' button disabled?", "Is the error message visible?"],
  },
  aiLocate: {
    label: "AI Locate",
    icon: Locate,
    description: "Find element coordinates (position/size).",
    supportsDeepThink: true,
    valueLabel: "Element to Locate",
    valuePlaceholder: "e.g., The logo in the header",
    info: "Finds the coordinates (position and size) of a UI element.",
    examples: ["The logo in the header", "The 'Add to Cart' button for the first product"],
  },

  // --- High-Level Goal ---
  aiAction: {
    label: "AI Action",
    icon: Sparkles,
    description: "Perform multi-step actions using natural language.",
    supportsDeepThink: false,
    valueLabel: "Action Description",
    valuePlaceholder: "e.g., Type 'hello' into the search box and click search",
    valueAsTextarea: true,
    info: "Performs multi-step actions using natural language. The AI will plan and execute a sequence of sub-steps. Best for complex goals. Note: Deep Focus is not available for this action type.",
    examples: [
      "Type 'hello' into the search box and click search",
      "Log in with username 'test' and password 'test'",
    ],
  },
  aiAssertion: {
    label: "AI Assertion",
    icon: ClipboardCheck,
    description: "Verify a condition on the page is true.",
    supportsDeepThink: false,
    valueLabel: "Assertion to Verify",
    valuePlaceholder: "e.g., The login button should be visible",
    valueAsTextarea: true,
    info: "Verifies that a condition on the page is true. The test will fail if the assertion is false. Note: Deep Focus is not available for this action type.",
    examples: [
      "The login button should be visible",
      "There should be 3 items in the shopping cart",
    ],
  },
  
  // --- Browser Control ---
  goto: {
    label: "Goto URL",
    icon: ArrowRight,
    description: "Navigate the browser to a specific URL.",
    supportsDeepThink: false,
    valueLabel: "URL to Navigate To",
    valuePlaceholder: "e.g., https://hirafi.com",
    info: "Navigates the browser to a specific URL.",
    examples: ["https://hirafi.com", "{{baseURL}}/login"],
  },
  sleep: {
    label: "Sleep",
    icon: Clock,
    description: "Pause execution for a set time.",
    supportsDeepThink: false,
    valueLabel: "Duration (seconds)",
    valuePlaceholder: "e.g., 5",
    info: "Pauses the test execution for a specified number of seconds.",
    examples: ["5", "0.5"],
  },
  aiWaitElement: {
    label: "AI Wait",
    icon: Clock,
    description: "Wait for a condition to be true.",
    supportsDeepThink: true,
    valueLabel: "Condition to Wait For",
    valuePlaceholder: "e.g., The success message appears",
    valueAsTextarea: true,
    info: "Waits for a condition on the page to become true before proceeding. The test will wait up to a timeout period.",
    examples: [
      "The success message appears",
      "The loading spinner disappears",
      "The shopping cart icon shows a quantity of 2",
    ],
  },

  // --- Control Flow ---
  ifElse: {
    label: "If-Else",
    icon: GitBranch,
    description: "Execute different steps based on a condition.",
    supportsDeepThink: false,
    valueLabel: "Condition to Check",
    valuePlaceholder: "e.g., Is the user already logged in to the system?",
    info: "Evaluates a boolean condition using AI and executes different sets of steps based on whether it's true or false.",
    examples: [
      "Is the user already logged in to the system?",
      "Does the shopping cart contain any items?",
      "Is this a mobile or desktop view?",
      "Has the form been submitted successfully?",
      "Are there validation errors visible on the page?",
    ],
  },
  forLoop: {
    label: "For Loop",
    icon: RotateCcw,
    description: "Repeat steps for each item in a list.",
    supportsDeepThink: false,
    valueLabel: "List to Iterate Over",
    valuePlaceholder: "e.g., List of product cards on the page",
    info: "Gets a list of items using AI and repeats steps for each item. Use direct numbers (e.g., '3') for fixed iterations.",
    examples: [
      "List of product cards on the page",
      "Menu items in the navigation bar",
      "Search results displayed",
      "Table rows with data",
      "3", // Direct number example
      "Available filter options",
    ],
  },
  whileLoop: {
    label: "While Loop",
    icon: Repeat,
    description: "Repeat steps while a condition is true.",
    supportsDeepThink: false,
    valueLabel: "Condition to Check",
    valuePlaceholder: "e.g., Is the 'Load More' button still visible and clickable?",
    info: "Continuously evaluates a boolean condition using AI and repeats steps while it remains true. Has a safety limit to prevent infinite loops.",
    examples: [
      "Is the 'Load More' button still visible and clickable?",
      "Are there more pages to navigate through?",
      "Is the loading spinner still showing on the page?",
      "Does the infinite scroll still have more content?",
      "Are there still unprocessed items in the queue?",
    ],
  },
};

const actionGroups = [
  {
    label: "Instant Actions",
    actions: ["aiTap", "aiInput", "aiHover", "aiKeyboardPress", "aiScroll", "aiRightClick", "aiAssertion", "aiWaitElement"] as ActionType[],
  },
  {
    label: "Data Extraction",
    actions: ["aiQuery", "aiString", "aiNumber", "aiBoolean", "aiLocate"] as ActionType[],
  },
  {
    label: "Control Flow",
    actions: ["ifElse", "forLoop", "whileLoop"] as ActionType[],
  },
  {
    label: "High-Level Goal",
    actions: ["aiAction"] as ActionType[],
  },
  {
    label: "Browser Control",
    actions: ["goto", "sleep"] as ActionType[],
  },
];

interface ScenarioStepsProps {
  steps: TestStep[];
  onStepsChange: (steps: TestStep[]) => void;
}

export function ScenarioSteps({
  steps,
  onStepsChange,
}: ScenarioStepsProps) {
  // Modern dialog için state
  const [showClearDialog, setShowClearDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [stepToDelete, setStepToDelete] = useState<string | null>(null)
  const [showExampleDialog, setShowExampleDialog] = useState(false)
  const [stepForExamples, setStepForExamples] = useState<TestStep | null>(null)

  // IF-ELSE toggle state for each step
  const [ifElseToggleStates, setIfElseToggleStates] = useState<{[stepId: string]: boolean}>({})

  // Create reference for scrolling to bottom
  const bottomRef = useRef<HTMLDivElement>(null)

  // Debounced AI Query field updates to improve performance
  const [aiQueryFieldUpdates, setAiQueryFieldUpdates] = useState<{[key: string]: {field: 'key' | 'description', value: string}}>({})
  const debouncedAiQueryUpdates = useDebounce(aiQueryFieldUpdates, 300)

  // Apply debounced updates to store
  useEffect(() => {
    if (Object.keys(debouncedAiQueryUpdates).length === 0) {
      return
    }

    Object.entries(debouncedAiQueryUpdates).forEach(([fieldId, update]) => {
      // This is a bit complex now, we need to find the step and field
      const stepId = fieldId.split('_')[0];
      const queryFieldId = fieldId.split('_')[1];

      // Use functional update to avoid dependency on steps
      onStepsChange(prevSteps => {
        return prevSteps.map(step => {
          if (step.id === stepId && step.type === 'aiQuery' && step.aiQueryFields) {
            const newQueryFields = step.aiQueryFields.map(qf => {
              if (qf.id === queryFieldId) {
                return { ...qf, [update.field]: update.value };
              }
              return qf;
            });
            return { ...step, aiQueryFields: newQueryFields };
          }
          return step;
        });
      });
    })

    // Clear updates after applying them
    setAiQueryFieldUpdates({})
  }, [debouncedAiQueryUpdates, onStepsChange])

  // Optimized AI Query field update handler
  const handleAIQueryFieldUpdate = useCallback((stepId: string, fieldId: string, field: 'key' | 'description', value: string) => {
    const newSteps = steps.map(step => {
      if (step.id === stepId && step.type === 'aiQuery') {
        const newQueryFields = (step.aiQueryFields || []).map(qf => 
          qf.id === fieldId ? { ...qf, [field]: value } : qf
        );
        return { ...step, aiQueryFields: newQueryFields };
      }
      return step;
    });
    onStepsChange(newSteps);
  }, [steps, onStepsChange])

  // Optimized addAIQueryField with useCallback for performance
  const addAIQueryField = useCallback((stepId: string) => {
    const newSteps = steps.map(step => {
      if (step.id === stepId && step.type === 'aiQuery') {
        const newField: AIQueryField = { id: uuidv4(), key: "", description: "" };
        const newQueryFields = [...(step.aiQueryFields || []), newField];
        return { ...step, aiQueryFields: newQueryFields };
      }
      return step;
    });
    onStepsChange(newSteps);
  }, [steps, onStepsChange]);

  // Optimized removeAIQueryField with useCallback for performance
  const removeAIQueryField = useCallback((stepId: string, fieldId: string) => {
    const newSteps = steps.map(step => {
      if (step.id === stepId && step.type === 'aiQuery') {
        const newQueryFields = (step.aiQueryFields || []).filter(qf => qf.id !== fieldId);
        return { ...step, aiQueryFields: newQueryFields };
      }
      return step;
    });
    onStepsChange(newSteps);
  }, [steps, onStepsChange]);

  // IF-ELSE toggle helper functions
  const getIfElseToggleState = (stepId: string) => {
    return ifElseToggleStates[stepId] ?? true // Default to true (show TRUE branch)
  }

  const setIfElseToggleState = (stepId: string, showTrueBranch: boolean) => {
    setIfElseToggleStates(prev => ({
      ...prev,
      [stepId]: showTrueBranch
    }))
  }

  // Get display name for a step type
  const getStepTypeLabel = (type: ActionType) => {
    return actionConfig[type]?.label || "Unknown Action"
  }

  // Get icon for a step type
  const getStepTypeIcon = (type: ActionType) => {
    const Icon = actionConfig[type]?.icon || HelpCircle
    return <Icon className="h-4 w-4 mr-2" />
  }

  // Tüm adımları temizleme fonksiyonu - Her adımı tek tek silmek yerine adımların tamamını temizler
  const clearAllSteps = () => {
    if (steps.length === 0) return;

    // Modern dialog göster - confirm yerine
    setShowClearDialog(true);
  };

  // Onaylandığında tüm adımları temizle
  const confirmClearAllSteps = () => {
    onStepsChange([]);
    // Dialog'u kapat
    setShowClearDialog(false);
  };

  // Show delete confirmation for a single step
  const showStepDeleteConfirmation = (stepId: string) => {
    setStepToDelete(stepId);
    setShowDeleteDialog(true);
  };

  // Confirm deletion of a single step
  const confirmDeleteStep = () => {
    if (stepToDelete) {
      removeStep(stepToDelete);
      setStepToDelete(null);
    }
    setShowDeleteDialog(false);
  };

  // Optimized handleDragEnd with useCallback for performance
  const handleDragEnd = useCallback((result: DropResult) => {
    // Drop outside of droppable area
    if (!result.destination) return

    // Reorder steps
    const newSteps = Array.from(steps);
    const [reorderedItem] = newSteps.splice(result.source.index, 1);
    newSteps.splice(result.destination.index, 0, reorderedItem);
    onStepsChange(newSteps);
  }, [steps, onStepsChange]);

  // Optimized addStep with useCallback for performance
  const addStep = useCallback((position?: number) => {
    const newStep = createNewStep("aiTap");
    const newSteps = [...steps];
    if (position !== undefined) {
      newSteps.splice(position, 0, newStep);
    } else {
      newSteps.push(newStep);
    }
    onStepsChange(newSteps);
  }, [steps, onStepsChange]);

  // Optimized removeStep with useCallback for performance
  const removeStep = useCallback((stepId: string) => {
    const newSteps = steps.filter((step) => step.id !== stepId);
    onStepsChange(newSteps);
  }, [steps, onStepsChange]);

  // Optimized updateStep with useCallback for performance
  const updateStep = useCallback((stepId: string, field: keyof TestStep, value: any) => {
    const newSteps = steps.map((step) =>
      step.id === stepId ? updateStepField(step, field, value) : step
    );
    onStepsChange(newSteps);
  }, [steps, onStepsChange]);

  // Optimized updateStepType with useCallback for performance
  const updateStepType = useCallback((stepId: string, type: ActionType) => {
    const newSteps = steps.map((step) =>
      step.id === stepId ? updateStepTypeUtil(step, type) : step
    );
    onStepsChange(newSteps);
  }, [steps, onStepsChange]);

  return (
    <>
      <Card className="overflow-hidden border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b flex flex-row justify-between items-center">
          <div>
            <CardTitle>Test Steps</CardTitle>
            <CardDescription>Define the steps to execute in your test</CardDescription>
          </div>

          {/* Clear All button */}
          {steps.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllSteps}
              className="gap-1 border-rose-200 text-rose-600 hover:bg-rose-50 hover:text-rose-700 dark:border-rose-800 dark:text-rose-400 dark:hover:bg-rose-950/30"
            >
              <Trash2 className="h-4 w-4" />
              Clear All Steps
            </Button>
          )}
        </CardHeader>
        <CardContent className="p-6 pt-4">
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="steps">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                  {steps.length === 0 ? (
                    <div className="flex items-center justify-center h-48 border-2 border-dashed rounded-md border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/40 p-8 text-center">
                      <div>
                        <Sparkles className="h-8 w-8 text-indigo-400 mx-auto mb-2 opacity-50" />
                        <h3 className="text-lg font-medium text-gray-600 dark:text-gray-200 mb-1">No Steps Added Yet</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4 max-w-sm">
                          Start by adding test steps manually, or use AI to generate steps based on your test description.
                        </p>
                        <Button
                          onClick={() => addStep()}
                          className="gap-1 bg-indigo-600 hover:bg-indigo-700 text-white"
                          variant="default"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Step
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <>
                      {/* İlk adımın üstüne adım ekleme butonu */}
                      <div className="flex justify-center my-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 px-2 text-xs border-dashed border-gray-300 dark:border-gray-600 hover:bg-indigo-50 hover:text-indigo-600 hover:border-indigo-300 dark:hover:bg-indigo-900/30 dark:hover:border-indigo-500 transition-colors"
                          onClick={() => addStep(0)}
                        >
                          <Plus className="h-3.5 w-3.5 mr-1" />
                          <span>Insert Step Here</span>
                        </Button>
                      </div>

                      {steps.map((step, index) => (
                        <React.Fragment key={step.id || `step-${index}`}>
                          <Draggable draggableId={step.id || `step-${index}`} index={index}>
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className="flex items-start space-x-4 py-4"
                              >
                                <div 
                                  className="flex flex-col items-center pt-3 relative h-full"
                                  {...provided.dragHandleProps}
                                >
                                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 font-bold text-sm ring-4 ring-white dark:ring-gray-800 cursor-grab">
                                    {index + 1}
                                  </div>
                                  {index < steps.length - 1 && (
                                    <div className="w-0.5 grow bg-gray-200 dark:bg-gray-700" />
                                  )}
                                </div>

                                <div className="flex-1 bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm transition-shadow hover:shadow-md">
                                  <div className="p-4">
                                    <div className="flex-1 space-y-3">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                          <Select
                                            value={step.type}
                                            onValueChange={(newType) => {
                                              updateStepType(step.id, newType as ActionType);
                                            }}
                                          >
                                            <SelectTrigger className="w-auto min-w-[220px] max-w-sm h-auto py-2 px-3 border-dashed font-normal text-sm bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                              <SelectValue>
                                                  <div className="flex items-center">
                                                      {getStepTypeIcon(step.type)}
                                                      <span className="truncate">{getStepTypeLabel(step.type)}</span>
                                                  </div>
                                              </SelectValue>
                                            </SelectTrigger>
                                            <SelectContent>
                                              {actionGroups.map(group => (
                                                <React.Fragment key={group.label}>
                                                  <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 dark:text-gray-400">{group.label}</div>
                                                  {group.actions.map(actionType => {
                                                      const config = actionConfig[actionType];
                                                      const Icon = config.icon;
                                                      const isSelected = step.type === actionType;
                                                      return (
                                                        <SelectItem 
                                                          key={actionType} 
                                                          value={actionType} 
                                                          className={`py-2 pl-3 pr-4 focus:bg-accent focus:text-accent-foreground ${isSelected ? 'bg-indigo-50 dark:bg-indigo-900/50' : ''}`}
                                                        >
                                                            <div className="flex items-center">
                                                              <Icon className="h-5 w-5 mr-3 text-gray-600 dark:text-gray-300" />
                                                              <div className="flex flex-col">
                                                                <span className="font-medium text-sm">{config.label}</span>
                                                                <span className="text-xs text-gray-500 dark:text-gray-400">{config.description}</span>
                                                              </div>
                                                            </div>
                                                        </SelectItem>
                                                      )
                                                  })}
                                                </React.Fragment>
                                              ))}
                                            </SelectContent>
                                          </Select>
                                          <TooltipProvider>
                                            <Tooltip>
                                              <TooltipTrigger asChild>
                                                <Info className="h-4 w-4 text-gray-400 dark:text-gray-500 cursor-help" />
                                              </TooltipTrigger>
                                              <TooltipContent>
                                                <p className="max-w-xs">{actionConfig[step.type].info}</p>
                                              </TooltipContent>
                                            </Tooltip>
                                          </TooltipProvider>

                                          <Button
                                            variant="link"
                                            size="sm"
                                            className="text-xs h-auto p-0 text-indigo-600 hover:text-indigo-700"
                                            onClick={() => {
                                                setStepForExamples(step);
                                                setShowExampleDialog(true);
                                            }}
                                          >
                                            Examples
                                          </Button>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                        {actionConfig[step.type]?.supportsDeepThink && (
                                            <div className="flex items-center space-x-2 p-2 rounded-md bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/30 dark:to-purple-950/30 border border-indigo-100 dark:border-indigo-800/50">
                                              <div className="flex items-center space-x-1.5">
                                                <BrainCircuit className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                                                <Switch
                                                  checked={!!step.deepThink}
                                                  onCheckedChange={(checked) =>
                                                    updateStep(step.id, 'deepThink', checked)
                                                  }
                                                  id={`deep-think-${step.id}`}
                                                />
                                                <Label htmlFor={`deep-think-${step.id}`} className="text-sm font-medium text-indigo-700 dark:text-indigo-300 flex items-center cursor-pointer">
                                                  Deep Focus
                                                </Label>
                                              </div>
                                              <TooltipProvider>
                                                <Tooltip>
                                                  <TooltipTrigger asChild>
                                                    <Target className="h-4 w-4 text-indigo-500 dark:text-indigo-400 cursor-help" />
                                                  </TooltipTrigger>
                                                  <TooltipContent className="max-w-sm">
                                                    <div className="space-y-2">
                                                      <p className="font-medium text-indigo-600 dark:text-indigo-400">Enhanced Element Detection</p>
                                                      <p className="text-sm">
                                                        Enables advanced AI analysis that carefully examines small elements and performs
                                                        precise targeting. Improves accuracy for complex interfaces at the cost of speed.
                                                      </p>
                                                    </div>
                                                  </TooltipContent>
                                                </Tooltip>
                                              </TooltipProvider>
                                            </div>
                                          )}
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-8 w-8 text-gray-500 hover:text-red-500 dark:hover:text-red-400"
                                            onClick={() => showStepDeleteConfirmation(step.id)}
                                          >
                                            <Trash2 className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                      
                                      <div className="space-y-3">
                                        {step.type === "aiQuery" ? (
                                          <div className="space-y-3 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-md border border-gray-200 dark:border-gray-700">
                                            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 block">
                                              {actionConfig[step.type].valueLabel}
                                            </Label>

                                            <div className="space-y-2">
                                              {(step.aiQueryFields || []).map((field) => (
                                                <div key={field.id} className="flex items-center space-x-2">
                                                  <Input
                                                    placeholder="Key (e.g., pageTitle)"
                                                    value={field.key}
                                                    onChange={(e) => handleAIQueryFieldUpdate(step.id, field.id, 'key', e.target.value)}
                                                  />
                                                  <VariableAutocomplete
                                                    placeholder="Description of data to extract"
                                                    value={field.description}
                                                    onChange={(value: string) => handleAIQueryFieldUpdate(step.id, field.id, 'description', value)}
                                                  />
                                                  <Button variant="ghost" size="icon" className="text-gray-500 hover:text-red-500" onClick={() => removeAIQueryField(step.id, field.id)}>
                                                    <Trash2 className="h-4 w-4" />
                                                  </Button>
                                                </div>
                                              ))}
                                            </div>

                                            <Button variant="outline" size="sm" className="gap-1" onClick={() => addAIQueryField(step.id)}>
                                              <Plus className="h-4 w-4" />
                                              Add Field
                                            </Button>
                                          </div>
                                        ) : step.type === "aiInput" ? (
                                          <div className="space-y-4">
                                            <div>
                                              <Label htmlFor={`step-value-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                Text to Input
                                              </Label>
                                              <VariableAutocomplete
                                                  placeholder="e.g., my-secret-password"
                                                  value={step.value || ""}
                                                  onChange={(value: string) => updateStep(step.id, 'value', value)}
                                              />
                                            </div>
                                            <div>
                                              <Label htmlFor={`step-target-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                Target Element
                                              </Label>
                                              <VariableAutocomplete
                                                  placeholder="e.g., The password field"
                                                  value={step.target || ""}
                                                  onChange={(value: string) => updateStep(step.id, 'target', value)}
                                              />
                                            </div>
                                          </div>
                                        ) : step.type === "goto" ? (
                                          <div className="space-y-4">
                                            <div>
                                              <Label htmlFor={`step-url-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                URL to Navigate To
                                              </Label>
                                              <VariableAutocomplete
                                                  placeholder="e.g., https://hirafi.com"
                                                  value={step.url || ""}
                                                  onChange={(value: string) => updateStep(step.id, 'url', value)}
                                              />
                                            </div>
                                          </div>
                                        ) : step.type === "aiKeyboardPress" ? (
                                          <div className="space-y-4">
                                            <div>
                                              <Label htmlFor={`step-prompt-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                Key to Press
                                              </Label>
                                              <VariableAutocomplete
                                                  placeholder="e.g., Press Enter key"
                                                  value={step.prompt || ""}
                                                  onChange={(value: string) => updateStep(step.id, 'prompt', value)}
                                              />
                                            </div>
                                            <div>
                                              <Label htmlFor={`step-target-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                Target Element (optional)
                                              </Label>
                                              <VariableAutocomplete
                                                  placeholder="e.g., The search input field"
                                                  value={step.target || ""}
                                                  onChange={(value: string) => updateStep(step.id, 'target', value)}
                                              />
                                            </div>
                                          </div>
                                        ) : step.type === "aiWaitElement" ? (
                                          <div className="space-y-4">
                                            <div>
                                              <Label htmlFor={`step-prompt-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                {actionConfig[step.type].valueLabel}
                                              </Label>
                                              <VariableAutocomplete
                                                  placeholder={actionConfig[step.type].valuePlaceholder}
                                                  value={step.prompt || ""}
                                                  onChange={(value: string) => updateStep(step.id, 'prompt', value)}
                                                  rows={3}
                                              />
                                            </div>

                                            {/* AI Wait Parameters */}
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                              <div>
                                                <Label htmlFor={`step-timeout-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                  Timeout (ms)
                                                </Label>
                                                <Input
                                                  id={`step-timeout-${step.id}`}
                                                  type="number"
                                                  placeholder="15000"
                                                  value={step.timeoutMs || ""}
                                                  onChange={(e) => updateStep(step.id, 'timeoutMs', e.target.value ? parseInt(e.target.value) : undefined)}
                                                />
                                              </div>
                                              <div>
                                                <Label htmlFor={`step-interval-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                  Check Interval (ms)
                                                </Label>
                                                <Input
                                                  id={`step-interval-${step.id}`}
                                                  type="number"
                                                  placeholder="3000"
                                                  value={step.checkIntervalMs || ""}
                                                  onChange={(e) => updateStep(step.id, 'checkIntervalMs', e.target.value ? parseInt(e.target.value) : undefined)}
                                                />
                                              </div>
                                            </div>
                                          </div>
                                                                ) : step.type === "ifElse" ? (
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor={`step-condition-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                Condition
                              </Label>
                              <VariableAutocomplete
                                placeholder="e.g., Is the user already logged in to the system?"
                                value={step.condition || step.value || ""}
                                onChange={(value: string) => {
                                  updateStep(step.id, 'condition', value)
                                  updateStep(step.id, 'value', value)
                                }}
                              />
                            </div>
                            
                            {/* Branch Toggle */}
                            <div className="flex items-center justify-center py-2">
                              <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                                <Button
                                  variant={getIfElseToggleState(step.id) ? "default" : "ghost"}
                                  size="sm"
                                  onClick={() => setIfElseToggleState(step.id, true)}
                                  className={`
                                    h-8 px-3 text-xs font-medium transition-all
                                    ${getIfElseToggleState(step.id)
                                      ? 'bg-green-500 hover:bg-green-600 text-white shadow-sm' 
                                      : 'text-gray-600 hover:text-green-600 hover:bg-green-50 dark:text-gray-400 dark:hover:text-green-400 dark:hover:bg-green-900/20'
                                    }
                                  `}
                                >
                                  <div className="w-2 h-2 bg-current rounded-full mr-2"></div>
                                  TRUE ({(step.trueSteps || []).length} steps)
                                </Button>
                                <Button
                                  variant={!getIfElseToggleState(step.id) ? "default" : "ghost"}
                                  size="sm"
                                  onClick={() => setIfElseToggleState(step.id, false)}
                                  className={`
                                    h-8 px-3 text-xs font-medium transition-all ml-1
                                    ${!getIfElseToggleState(step.id)
                                      ? 'bg-red-500 hover:bg-red-600 text-white shadow-sm' 
                                      : 'text-gray-600 hover:text-red-600 hover:bg-red-50 dark:text-gray-400 dark:hover:text-red-400 dark:hover:bg-red-900/20'
                                    }
                                  `}
                                >
                                  <div className="w-2 h-2 bg-current rounded-full mr-2"></div>
                                  FALSE ({(step.falseSteps || []).length} steps)
                                </Button>
                              </div>
                            </div>

                            {/* Conditional Branch Display */}
                            <div className="w-full">
                              {getIfElseToggleState(step.id) ? (
                                <div className="space-y-2">
                                  <Label className="text-sm font-medium text-green-700 dark:text-green-300 flex items-center gap-2">
                                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                    IF TRUE - Execute these steps:
                                  </Label>
                                  <NestedStepEditor
                                    steps={step.trueSteps || []}
                                    onStepsChange={(newSteps: TestStep[]) => updateStep(step.id, "trueSteps", newSteps)}
                                    title="IF TRUE Steps"
                                    emptyMessage="Add steps to execute when condition is true..."
                                    depth={1}
                                  />
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  <Label className="text-sm font-medium text-red-700 dark:text-red-300 flex items-center gap-2">
                                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                    IF FALSE - Execute these steps:
                                  </Label>
                                  <NestedStepEditor
                                    steps={step.falseSteps || []}
                                    onStepsChange={(newSteps: TestStep[]) => updateStep(step.id, "falseSteps", newSteps)}
                                    title="IF FALSE Steps"
                                    emptyMessage="Add steps to execute when condition is false..."
                                    depth={1}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                                        ) : step.type === "forLoop" ? (
                                          <div className="space-y-4">
                                            <div>
                                              <Label htmlFor={`step-iterations-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                Number of Iterations
                                              </Label>
                                                                                          <VariableAutocomplete
                                              placeholder="e.g., List of product cards on the page"
                                              value={step.iterationCount || step.value || ""}
                                              onChange={(value: string) => {
                                                updateStep(step.id, 'iterationCount', value)
                                                updateStep(step.id, 'value', value)
                                              }}
                                            />
                                            </div>
                                            
                                            <NestedStepEditor
                                              steps={step.loopSteps || []}
                                              onStepsChange={(newSteps: TestStep[]) => updateStep(step.id, 'loopSteps', newSteps)}
                                              title="🔄 Loop Steps - Execute For Each Iteration"
                                              emptyMessage="No steps in the loop"
                                              depth={1}
                                              maxDepth={3}
                                            />
                                          </div>
                                        ) : step.type === "whileLoop" ? (
                                          <div className="space-y-4">
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                                              <div className="md:col-span-2">
                                                <Label htmlFor={`step-condition-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                  Condition
                                                </Label>
                                                <VariableAutocomplete
                                                  placeholder="e.g., Is the 'Load More' button still visible and clickable?"
                                                  value={step.condition || step.value || ""}
                                                  onChange={(value: string) => {
                                                    updateStep(step.id, 'condition', value)
                                                    updateStep(step.id, 'value', value)
                                                  }}
                                                />
                                              </div>
                                              <div>
                                                <Label htmlFor={`step-max-iterations-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                  Max Iterations
                                                </Label>
                                                <Input
                                                  id={`step-max-iterations-${step.id}`}
                                                  type="number"
                                                  placeholder="50"
                                                  value={step.maxIterations || 50}
                                                  onChange={(e) => updateStep(step.id, 'maxIterations', parseInt(e.target.value) || 50)}
                                                />
                                              </div>
                                            </div>
                                            
                                            <NestedStepEditor
                                              steps={step.loopSteps || []}
                                              onStepsChange={(newSteps: TestStep[]) => updateStep(step.id, 'loopSteps', newSteps)}
                                              title="⚪ While Steps - Execute While Condition is TRUE"
                                              emptyMessage="No steps in the while loop"
                                              depth={1}
                                              maxDepth={3}
                                            />
                                          </div>
                                        ) : (
                                          <div className="space-y-4">
                                            {actionConfig[step.type]?.valueLabel && (
                                               <div>
                                                <Label htmlFor={`step-prompt-${step.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-1.5">
                                                  {actionConfig[step.type].valueLabel}
                                                </Label>
                                                <VariableAutocomplete
                                                    placeholder={actionConfig[step.type].valuePlaceholder}
                                                    value={step.prompt || ""}
                                                    onChange={(value: string) => updateStep(step.id, 'prompt', value)}
                                                    rows={actionConfig[step.type]?.valueAsTextarea ? 3 : 1}
                                                />
                                              </div>
                                            )}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </Draggable>

                          {/* Add Step button */}
                          <div className="flex justify-center -mt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 px-2 text-xs border-dashed border-gray-300 dark:border-gray-600 hover:bg-indigo-50 hover:text-indigo-600 hover:border-indigo-300 dark:hover:bg-indigo-900/30 dark:hover:border-indigo-500 transition-colors"
                              onClick={() => addStep(index + 1)}
                            >
                              <Plus className="h-3.5 w-3.5 mr-1" />
                              <span>Insert Step Here</span>
                            </Button>
                          </div>
                        </React.Fragment>
                      ))}
                    </>
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          {steps.length > 0 && (
            <div className="flex items-center gap-2 mt-4">
              <Button
                onClick={() => addStep()}
                className="w-full bg-indigo-50 text-indigo-600 hover:bg-indigo-100 dark:bg-indigo-900/20 dark:text-indigo-400 dark:hover:bg-indigo-900/30 border-indigo-200 dark:border-indigo-800"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Step at End
              </Button>

              {/* Reference div for scrolling to bottom */}
              <div ref={bottomRef} />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modern Dialog for Clear All Steps Confirmation */}
      <Dialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-rose-500" />
              Clear All Steps
            </DialogTitle>
            <DialogDescription>
              This will remove all steps from your test scenario. This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="py-3">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Are you sure you want to clear all {steps.length} steps?
            </p>
          </div>

          <DialogFooter className="flex space-x-2 sm:justify-end">
            <Button
              variant="outline"
              onClick={() => setShowClearDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmClearAllSteps}
              className="bg-rose-600 hover:bg-rose-700"
            >
              Clear All Steps
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog for Single Step Deletion Confirmation */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-rose-500" />
              Delete Step
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this step? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex space-x-2 sm:justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setStepToDelete(null);
                setShowDeleteDialog(false);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteStep}
              className="bg-rose-600 hover:bg-rose-700"
            >
              Delete Step
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog for Prompt Examples */}
      <Dialog open={showExampleDialog} onOpenChange={setShowExampleDialog}>
        <DialogContent className="sm:max-w-lg">
            <DialogHeader>
                <DialogTitle>
                    Prompt Examples for: <span className="text-indigo-600">{stepForExamples ? getStepTypeLabel(stepForExamples.type) : ''}</span>
                </DialogTitle>
                <DialogDescription>
                    Use these examples as a guide for writing effective prompts.
                </DialogDescription>
            </DialogHeader>
            {stepForExamples && actionConfig[stepForExamples.type] && (
                <div className="py-4 space-y-4 max-h-[60vh] overflow-y-auto">
                    {actionConfig[stepForExamples.type].examples.map((example, index) => (
                        <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md border dark:border-gray-700">
                            <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono">
                                <code>{example}</code>
                            </pre>
                        </div>
                    ))}
                </div>
            )}
            <DialogFooter>
                <Button variant="outline" onClick={() => setShowExampleDialog(false)}>Close</Button>
            </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
 