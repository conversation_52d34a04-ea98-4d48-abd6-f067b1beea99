/**
 * Test Manager Module
 * Handles core test management functionality
 *
 * This is a wrapper around the new services to maintain backward compatibility
 * All functionality has been moved to the new services
 */

import { EventEmitter } from 'events';
import { TestProcess, TestRequest, TestStatus, AndroidEnvironmentSettings, defaultReportSettings } from '../../models/test-types.js';
import { config } from '../../config/index.js';
import { logger } from '../../utils/logger.js';
import { nodeRegistry } from '../../services/node/index.js';
import { testQueueService, testDistributor } from '../../services/test/index.js';
import { resultQueueService } from '../../services/result/ResultQueueService.js';
import { centralResultProcessor } from '../../services/result/centralResultProcessor.js';
import { RunCoordinator } from './run-coordinator.js';
import { getTestsCollection } from '../../services/mongo/testService.js';
import { queueService, QUEUE_NAMES } from '../../services/redis/queueService.js';
import { redisConnection } from '../../services/redis/redisConnection.js';
import { eventHandler } from '../event-handler/index.js';

export class TestManager extends EventEmitter {
  private static instance: TestManager;
  private runCoordinator: RunCoordinator;

  private constructor() {
    super();

    // Initialize RunCoordinator
    this.runCoordinator = new RunCoordinator(this);

    logger.info('TestManager: Initialized with new service architecture');
  }

  /**
   * Initialize event listeners and other dependencies
   */
  public async initialize(): Promise<void> {
    logger.info('TestManager: Initializing with new service architecture');

    // Initialize the new services if they haven't been initialized yet
    if (config.connections?.redis?.enabled) {
      await testQueueService.initialize();
      await testDistributor.initialize();
      await resultQueueService.initialize();
    }

    // Initialize EventHandler with dependencies
    await eventHandler.initialize({
      testQueueService,
      testDistributor,
      resultQueueService,
      nodeRegistry
    });

    // Setup centralized event handling
    eventHandler.setupAllEvents();

    // Subscribe to events from EventHandler instead of direct service events
    eventHandler.on('test:queued', (data) => {
      this.emit('testAdded', data);
    });

    eventHandler.on('test:assigned', (testId, nodeId) => {
      this.emit('testAssigned', { testId, nodeId });
    });

    eventHandler.on('test:completed', (_testId, data) => {
      this.emit('testResultReceived', data);
    });

    eventHandler.on('test:recovered', (testId, recoveryMethod) => {
      logger.info(`TestManager: Test ${testId} was successfully recovered using method: ${recoveryMethod}`);
      this.emit('testRecovered', { testId, recoveryMethod });
    });

    eventHandler.on('test:failed', (testId, error) => {
      logger.error(`TestManager: Failed to recover test ${testId}: ${error}`);
      this.emit('testRecoveryFailed', { testId, error });
    });

    // Listen for stuck test events through EventHandler
    eventHandler.on('test:stuck', async (testId: string, nodeId: string, details: any) => {
      logger.error(`TestManager: Detected stuck test ${testId} on node ${nodeId}: ${details.reason}`);
      await this.handleStuckTest(testId, nodeId, details);
    });

    logger.info('TestManager: Initialized event listeners');
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): TestManager {
    if (!TestManager.instance) {
      TestManager.instance = new TestManager();
    }
    return TestManager.instance;
  }

  /**
   * Get the run coordinator instance
   */
  public getRunCoordinator(): RunCoordinator {
    return this.runCoordinator;
  }

  /**
   * Add a new test to the queue
   * @param scenarioId Scenario identifier
   * @param scenarioName Name of the scenario
   * @param scenarioData Test scenario data
   * @param priority Test priority (lower runs first)
   * @param userId ID of the user who started the test (optional)
   * @param options Additional options for the test (optional)
   */
  public async addTest(
    scenarioId: string,
    scenarioName: string,
    scenarioData: any,
    priority: number,
    userId: string,
    options: Record<string, any> = {}
  ): Promise<string> {
    const testId = `test-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const runId = options.runId || `run-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    const executionId = options.executionId || `exec-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

    // ENHANCED VALIDATION: Prevent empty scenarios from being queued
    if (!scenarioData || !scenarioData.steps || !Array.isArray(scenarioData.steps)) {
      const errorMessage = `Scenario '${scenarioName}' has no steps array - cannot create test`;
      logger.error(`TestManager.addTest: ${errorMessage}`);
      throw new Error(errorMessage);
    }

    if (scenarioData.steps.length === 0) {
      const errorMessage = `Scenario '${scenarioName}' has no test steps - cannot create test. This prevents unnecessary resource allocation and video recording on nodes.`;
      logger.error(`TestManager.addTest: ${errorMessage}`);
      throw new Error(errorMessage);
    }

    // Additional validation: Check for meaningful steps
    const validSteps = scenarioData.steps.filter((step: any) => 
      step && 
      typeof step === 'object' && 
      step.type && 
      (step.value || step.name || step.prompt)
    );

    if (validSteps.length === 0) {
      const errorMessage = `Scenario '${scenarioName}' has no valid test steps - all steps are empty or malformed. Cannot create test.`;
      logger.error(`TestManager.addTest: ${errorMessage}`);
      throw new Error(errorMessage);
    }

    logger.info(`TestManager.addTest: Creating test for scenario '${scenarioName}' with ${validSteps.length} valid steps out of ${scenarioData.steps.length} total steps`);

    // Extract user context fields
    const executedUser = options.executedUser || userId;
    const teamId = options.teamId || null;
    const companyId = options.companyId || null;

    // Create a test request object with the necessary fields
    const testRequest: TestRequest = {
      id: testId,
      runId: runId,
      executionId: executionId,
      scenarioId,
      scenarioName,
      platform: options.platform || 'web', // Add platform field with default to web
      status: TestStatus.QUEUED,
      queuedAt: new Date(),
      priority: priority || 10,
      userId,
      executedUser,
      executedUserName: options.executedUserName, // Explicitly add executedUserName
      teamId: teamId, // Add teamId
      companyId: companyId, // Add companyId
      environmentSettings: options.environmentSettings,
      reportSettings: options.reportSettings || defaultReportSettings, // Add default reportSettings
      // Add scenario data in the format expected by test_node
      // The test_node expects a 'scenario' field with steps directly in it
      scenario: {
        id: scenarioId,
        name: scenarioName,
        title: scenarioName,
        steps: scenarioData.steps || [],
        capabilities: scenarioData.capabilities || [],
        totalSteps: scenarioData.steps?.length || 0,
        // DATASET INTEGRATION: Include dataset information for variable resolution
        testDataSetId: scenarioData.testDataSetId,
        metadata: scenarioData.metadata
      }
    };

    // Log important fields for debugging
    logger.info(`TestManager: Creating test request with executionId: ${executionId}, userId: ${userId}, executedUser: ${executedUser}, executedUserName: ${options.executedUserName}, teamId: ${teamId}, companyId: ${companyId}`);

    // Check if this is an Android test
    if (testRequest.platform === 'android') {
      logger.info(`TestManager: Android test detected, retrieving credentials for companyId: ${companyId}, teamId: ${teamId}`);

      try {
        // Type guard to check if environmentSettings is AndroidEnvironmentSettings
        const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
          return settings && settings.platform === 'android';
        };

        // NOTE: Device provider credentials are now handled at RunCoordinator level
        // Tests inherit the already-updated environment settings from the run
        logger.info(`TestManager: Using environment settings inherited from run (credentials already resolved at RunCoordinator level)`)

        // Validate device and app information for Android tests
        if (testRequest.environmentSettings && testRequest.environmentSettings.platform === 'android') {
          const androidSettings = testRequest.environmentSettings as AndroidEnvironmentSettings;
          const currentProvider = androidSettings.deviceProvider || 'sauceLabs';

          // Validate device information based on current provider
          if (currentProvider === 'sauceLabs') {
            if (!androidSettings.sauceLabs.selectedDevices ||
                !Array.isArray(androidSettings.sauceLabs.selectedDevices) ||
                androidSettings.sauceLabs.selectedDevices.length === 0) {

              logger.warn(`TestManager: No SauceLabs devices found in test request ${testId}. Checking for legacy device information.`);

              // Check for legacy device information
              if ('deviceId' in androidSettings.sauceLabs) {
                const deviceId = (androidSettings.sauceLabs as any).deviceId;
                logger.info(`TestManager: Converting legacy deviceId ${deviceId} to selectedDevices array`);

                androidSettings.sauceLabs.selectedDevices = [{
                  id: deviceId,
                  name: `Device ${deviceId}`,
                  osVersion: 'Unknown',
                  provider: 'sauceLabs'
                }];

                logger.info(`TestManager: Converted legacy deviceId to selectedDevices: ${JSON.stringify(androidSettings.sauceLabs.selectedDevices)}`);
              } else if ('deviceIds' in androidSettings.sauceLabs &&
                        Array.isArray((androidSettings.sauceLabs as any).deviceIds) &&
                        (androidSettings.sauceLabs as any).deviceIds.length > 0) {
                const deviceId = (androidSettings.sauceLabs as any).deviceIds[0];
                logger.info(`TestManager: Converting legacy deviceIds[0] ${deviceId} to selectedDevices array`);

                androidSettings.sauceLabs.selectedDevices = [{
                  id: deviceId,
                  name: `Device ${deviceId}`,
                  osVersion: 'Unknown',
                  provider: 'sauceLabs'
                }];

                logger.info(`TestManager: Converted legacy deviceIds to selectedDevices: ${JSON.stringify(androidSettings.sauceLabs.selectedDevices)}`);
              } else {
                logger.warn(`TestManager: No SauceLabs device information found in test request ${testId}. This may cause issues with test execution.`);
                androidSettings.sauceLabs.selectedDevices = [];
              }
            }

            // Validate SauceLabs app information
            if (!androidSettings.sauceLabs.selectedApp && 'appId' in androidSettings.sauceLabs) {
              const appId = (androidSettings.sauceLabs as any).appId;
              logger.info(`TestManager: Converting legacy appId ${appId} to selectedApp object`);

              androidSettings.sauceLabs.selectedApp = {
                id: appId,
                name: `App ${appId}`,
                version: 'Unknown'
              };

              logger.info(`TestManager: Converted legacy appId to selectedApp: ${JSON.stringify(androidSettings.sauceLabs.selectedApp)}`);
            } else if (!androidSettings.sauceLabs.selectedApp) {
              logger.warn(`TestManager: No SauceLabs app information found in test request ${testId}. This may cause issues with test execution.`);
            }

            logger.info(`TestManager: Added SauceLabs credentials to test request ${testId}`);
          } else if (currentProvider === 'testinium') {
            if (!androidSettings.testinium.selectedDevices ||
                !Array.isArray(androidSettings.testinium.selectedDevices) ||
                androidSettings.testinium.selectedDevices.length === 0) {

              logger.warn(`TestManager: No Testinium devices found in test request ${testId}. This may cause issues with test execution.`);
              androidSettings.testinium.selectedDevices = [];
            }

            // Validate Testinium app information
            if (!androidSettings.testinium.selectedApp) {
              logger.warn(`TestManager: No Testinium app information found in test request ${testId}. This may cause issues with test execution.`);
            }

            logger.info(`TestManager: Added Testinium credentials to test request ${testId}`);
          }
        }
      } catch (error: any) {
        logger.error(`TestManager: Error retrieving device provider credentials: ${error.message}`);
      }
    }

    // Add the test to the queue service
    if (config.connections?.redis?.enabled) {
      try {
        await testQueueService.addTest(testRequest, priority);
        logger.info(`TestManager: Added test ${testId} to queue for scenario: ${scenarioName}`);
      } catch (error) {
        logger.error(`TestManager: Error adding test ${testId} to queue: ${error}`);
        throw error;
      }
    } else {
      logger.error(`TestManager: Redis is required for the new service architecture`);
      throw new Error('Redis is required for the new service architecture');
    }

    // Update run status if this test is part of a run
    if (runId) {
      await this.updateRunStatus(runId, 'running');
    }

    return testId;
  }

  /**
   * Stop a test by ID with complete cleanup workflow
   * @param testId Test identifier
   */
  public async stopTest(testId: string): Promise<boolean> {
    logger.info(`TestManager: Attempting to stop test ${testId} with complete cleanup workflow`);

    try {
      const test = await this.getTest(testId);

      if (!test) {
        logger.warn(`TestManager: Test ${testId} not found, cannot stop.`);
        return false;
      }

      // Check if already in a terminal state
      if ([TestStatus.COMPLETED, TestStatus.FAILED, TestStatus.STOPPED].includes(test.status)) {
        logger.info(`TestManager: Test ${testId} is already in a terminal state (${test.status}). No action needed.`);
        return true; // Consider it a success if already stopped/completed
      }

      // ENHANCED STOP WORKFLOW: Complete cleanup for all test states
      logger.info(`TestManager: Starting complete stop workflow for test ${testId} in status ${test.status}`);

      // 1. Send stop command to node if test is running
      if (test.nodeId && (test.status === TestStatus.RUNNING || test.status === TestStatus.ASSIGNING)) {
        logger.info(`TestManager: Test ${testId} is running on node ${test.nodeId}. Sending stop command.`);

        let stoppedOnNode = false;
        try {
          stoppedOnNode = await nodeRegistry.stopTestOnNode(test.nodeId, testId);
          if (stoppedOnNode) {

          } else {
            logger.warn(`TestManager: Failed to send stop command for test ${testId} to node ${test.nodeId} (node might be offline or command failed).`);
          }
        } catch (nodeError) {
            logger.error(`TestManager: Error sending stop command to node ${test.nodeId} for test ${testId}: ${nodeError}`);
        }

        // 2. Wait briefly for node to acknowledge stop (with timeout)
        if (stoppedOnNode) {
          logger.info(`TestManager: Waiting for node ${test.nodeId} to acknowledge stop for test ${testId}`);
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second grace period
        }

        // 3. Mark node as available immediately
        /* try {
            await nodeRegistry.markNodeAsAvailable(test.nodeId);
            logger.info(`TestManager: Marked node ${test.nodeId} as available after stopping test ${testId}.`);
        } catch (markAvailableError) {
            logger.error(`TestManager: Failed to mark node ${test.nodeId} as available after stopping test ${testId}: ${markAvailableError}`);
            // Continue, as test itself is stopped in DB
        } */
      }

      // 4. Move BullMQ job to completed status with stopped result
      await this.completeBullMQJobForStoppedTest(testId, test);

      // 5. Clean up all Redis locks for this test
      await this.cleanupTestLocks(testId);

      // 6. Clean up step progress data for this test
      await this.cleanupTestStepProgress(testId);

      // 7. Update test status in database
      const stoppedResult = {
        success: false,
        message: 'Test stopped by user request',
        status: 'stopped',
        stoppedAt: new Date().toISOString(),
        stoppedReason: 'manual_stop'
      };

      await this.updateTestStatus(testId, TestStatus.STOPPED, {
        stoppedReason: 'manual_stop',
        completedAt: new Date(),
        result: stoppedResult
      });

      // 8. Send stopped result to result queue
      await this.sendStoppedResultToQueue(testId, test, stoppedResult);

      logger.info(`TestManager: Successfully completed stop workflow for test ${testId}`);
      return true;

    } catch (error) {
      logger.error(`TestManager: General error stopping test ${testId}: ${error}`);
      return false;
    }
  }

  /**
   * Stop all tests for a run with comprehensive cleanup
   * @param runId Run ID to stop all tests for
   * @returns Promise resolving to stop results
   */
  public async stopAllTestsForRun(runId: string): Promise<{
    success: boolean;
    stoppedTests: string[];
    failedToStop: string[];
    totalStopped: number;
    totalFailed: number;
    queueCleanupCount: number;
  }> {
    logger.info(`TestManager: Starting comprehensive stop workflow for all tests in run ${runId}`);

    const stoppedTests: string[] = [];
    const failedToStop: string[] = [];
    let queueCleanupCount = 0;

    try {
      // CRITICAL FIX: Get tests from both MongoDB AND BullMQ queue
      logger.info(`TestManager: Searching for tests to stop in run ${runId} from multiple sources`);

      // 1. Get all tests for this run from database
      const { getTestsByRunId } = await import('../../services/mongo/testService.js');
      const testsResult = await getTestsByRunId(runId);

      let testsFromDB: any[] = [];
      if (testsResult.success && testsResult.tests) {
        // Filter tests that are not already in terminal states
        testsFromDB = testsResult.tests.filter((test: any) =>
          test && !['completed', 'failed', 'stopped'].includes(test.status)
        );
      }
      logger.info(`TestManager: Found ${testsFromDB.length} non-terminal tests in MongoDB for run ${runId}`);

      // 2. Get active tests from BullMQ queue (these are the ones actually running on nodes)
      const activeTestsFromQueue = await this.getRunningTests();
      const queueTestsForRun = activeTestsFromQueue.filter((test: any) => test.runId === runId);
      logger.info(`TestManager: Found ${queueTestsForRun.length} active tests in BullMQ queue for run ${runId}`);

      // 3. Combine both sources and deduplicate
      const allTestIds = new Set([
        ...testsFromDB.map(t => t.id),
        ...queueTestsForRun.map(t => t.id)
      ]);

      const testsToStop = Array.from(allTestIds).map(testId => {
        return testsFromDB.find(t => t.id === testId) || queueTestsForRun.find(t => t.id === testId);
      }).filter(Boolean);

      logger.info(`TestManager: Total unique tests to stop for run ${runId}: ${testsToStop.length}`);

      // 2. Stop each individual test
      for (const test of testsToStop) {
        try {
          logger.info(`TestManager: Stopping test ${test.id} (status: ${test.status}) for run ${runId}`);
          const stopped = await this.stopTest(test.id);
          if (stopped) {
            stoppedTests.push(test.id);
            logger.info(`TestManager: Successfully stopped test ${test.id} for run ${runId}`);
          } else {
            failedToStop.push(test.id);
            logger.warn(`TestManager: Failed to stop test ${test.id} for run ${runId}`);
          }
        } catch (err) {
          logger.error(`TestManager: Error stopping test ${test.id} for run ${runId}: ${err}`);
          failedToStop.push(test.id);
        }
      }

      // 3. Clean up any remaining queued tests from BullMQ
      try {
        await testQueueService.initialize();
        queueCleanupCount = await testQueueService.removeTestsByRunId(runId);
        logger.info(`TestManager: Removed ${queueCleanupCount} remaining tests for run ${runId} from Redis queue`);
      } catch (queueError) {
        logger.error(`TestManager: Error removing tests from Redis queue for run ${runId}: ${queueError}`);
      }

      // 4. Clean up step progress data for the entire run
      try {
        const { stepProgressService } = await import('../../services/step-progress/stepProgressService.js');
        await stepProgressService.cleanupRunStepProgress(runId);
        logger.info(`TestManager: Cleaned up step progress data for run ${runId}`);
      } catch (stepProgressError) {
        logger.error(`TestManager: Error cleaning up step progress data for run ${runId}: ${stepProgressError}`);
      }

      const totalStopped = stoppedTests.length;
      const totalFailed = failedToStop.length;
      const success = totalFailed === 0;

      logger.info(`TestManager: Completed comprehensive stop workflow for run ${runId}. Stopped: ${totalStopped}, Failed: ${totalFailed}, Queue cleanup: ${queueCleanupCount}`);

      return {
        success,
        stoppedTests,
        failedToStop,
        totalStopped,
        totalFailed,
        queueCleanupCount
      };

    } catch (error) {
      logger.error(`TestManager: Error in comprehensive stop workflow for run ${runId}: ${error}`);
      return {
        success: false,
        stoppedTests,
        failedToStop,
        totalStopped: stoppedTests.length,
        totalFailed: failedToStop.length,
        queueCleanupCount
      };
    }
  }

  /**
   * Complete BullMQ job for stopped test
   * @param testId Test ID
   * @param test Test data
   */
  private async completeBullMQJobForStoppedTest(testId: string, _test: any): Promise<void> {
    try {
      if (!config.connections?.redis?.enabled) {
        logger.debug(`TestManager: Redis not enabled, skipping BullMQ job completion for stopped test ${testId}`);
        return;
      }

      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (!queue) {
        logger.warn(`TestManager: Could not get test queue for stopped test ${testId}`);
        return;
      }

      const job = await queue.getJob(testId);
      if (!job) {
        logger.info(`TestManager: No BullMQ job found for stopped test ${testId}, may have already been processed`);
        return;
      }

      const jobState = await job.getState();
      logger.info(`TestManager: Moving BullMQ job ${job.id} for stopped test ${testId} from state '${jobState}' to completed`);

      const stoppedResult = {
        success: false,
        message: 'Test stopped by user request',
        status: 'stopped',
        testId: testId,
        stoppedAt: new Date().toISOString(),
        stoppedReason: 'manual_stop'
      };

      // Handle different job states appropriately
      if (jobState === 'active') {
        // For active jobs, move to completed with stopped result
        try {
          await job.moveToCompleted(stoppedResult, job.token || '', false);
          logger.info(`TestManager: Successfully moved active job ${job.id} to completed for stopped test ${testId}`);
        } catch (moveError) {
          logger.warn(`TestManager: Failed to move active job ${job.id} to completed, attempting removal: ${moveError}`);
          await job.remove();
          logger.info(`TestManager: Successfully removed active job ${job.id} for stopped test ${testId}`);
        }
      } else if (jobState === 'waiting' || jobState === 'delayed') {
        // For waiting/delayed jobs, direct removal is appropriate
        await job.remove();
        logger.info(`TestManager: Successfully removed ${jobState} job ${job.id} for stopped test ${testId}`);
      } else if (jobState === 'completed' || jobState === 'failed') {
        // Job is already in terminal state, no action needed
        logger.info(`TestManager: Job ${job.id} for test ${testId} is already in terminal state '${jobState}', no action needed`);
      } else {
        // Unknown state, try to move to completed
        logger.warn(`TestManager: Unknown job state '${jobState}' for job ${job.id}, attempting to move to completed`);
        await job.moveToCompleted(stoppedResult, job.token || '', false);
      }

    } catch (error) {
      logger.error(`TestManager: Error completing BullMQ job for stopped test ${testId}: ${error}`);
      // Don't throw - this is cleanup, main stop workflow should continue
    }
  }

  /**
   * Clean up all Redis locks for a test
   * @param testId Test ID
   */
  private async cleanupTestLocks(testId: string): Promise<void> {
    try {
      const lockKeys = [
        `test:lock:${testId}`,
        `test:claim:${testId}`,
        `test:reservation:${testId}`,
        `test:status:${testId}`,
        `test:node:${testId}`,
        `lock:test:${testId}`,
        `lock:${testId}`
      ];

      const redisClient = redisConnection.getClient();
      if (redisClient) {
        const deletedCount = await redisClient.del(...lockKeys);
        logger.info(`TestManager: Cleaned up ${deletedCount} Redis locks for stopped test ${testId}`);
      } else {
        logger.warn(`TestManager: Redis client not available for lock cleanup of stopped test ${testId}`);
      }
    } catch (error) {
      logger.error(`TestManager: Error cleaning up locks for stopped test ${testId}: ${error}`);
      // Don't throw - this is cleanup, main stop workflow should continue
    }
  }

  /**
   * Clean up step progress data for a test
   * @param testId Test ID
   */
  private async cleanupTestStepProgress(testId: string): Promise<void> {
    try {
      // Import step progress service
      const { stepProgressService } = await import('../../services/step-progress/stepProgressService.js');

      // Clean up step progress data for this test
      await stepProgressService.cleanupTestStepProgress(testId);
      logger.info(`TestManager: Cleaned up step progress data for stopped test ${testId}`);
    } catch (error) {
      logger.error(`TestManager: Error cleaning up step progress for stopped test ${testId}: ${error}`);
      // Don't throw - this is cleanup, main stop workflow should continue
    }
  }

  /**
   * Send stopped result to result queue
   * @param testId Test ID
   * @param test Test data
   * @param stoppedResult Stopped result data
   */
  private async sendStoppedResultToQueue(testId: string, test: any, stoppedResult: any): Promise<void> {
    try {
      // Import result queue service
      const { resultQueueService } = await import('../../services/result/ResultQueueService.js');

      // Prepare complete result data
      const completeResult = {
        ...stoppedResult,
        testId: testId,
        runId: test.runId,
        scenarioId: test.scenarioId,
        executionId: test.executionId || `exec-${Date.now()}`,
        executedUser: test.executedUser || test.userId,
        executedUserName: test.executedUserName || 'Unknown',
        teamId: test.teamId,
        companyId: test.companyId,
        nodeId: test.nodeId,
        timestamp: new Date().toISOString(),
        transactionId: `stop-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
      };

      // Add to result queue
      const jobId = await resultQueueService.addResult(testId, completeResult);
      if (jobId) {

      } else {
        logger.warn(`TestManager: Failed to add stopped result for test ${testId} to result queue`);
      }

    } catch (error) {
      logger.error(`TestManager: Error sending stopped result to queue for test ${testId}: ${error}`);
      // Don't throw - this is cleanup, main stop workflow should continue
    }
  }

  /**
   * Update run status
   * @param runId Run identifier
   * @param status New status
   * @param testResults Optional test results
   */
  public async updateRunStatus(runId: string, status?: string, testResults?: any): Promise<boolean> {
    try {
      // Delegate to the run coordinator
      return this.runCoordinator.updateRunStatus(runId, status, testResults);
    } catch (error) {
      logger.error(`TestManager: Error updating run status: ${error}`);
      return false;
    }
  }

  /**
   * Update scenario status
   * @param runId Run identifier
   * @param scenarioId Scenario identifier
   * @param status New status
   * @param testId Optional test identifier
   */
  public async updateScenarioStatus(
    runId: string,
    scenarioId: string,
    status: 'queued' | 'running' | 'passed' | 'failed' | 'stopped' | 'idle',
    testId?: string
  ): Promise<boolean> {
    try {
      // Delegate to the run coordinator
      return this.runCoordinator.updateScenarioStatus(runId, scenarioId, status, testId);
    } catch (error) {
      logger.error(`TestManager: Error updating scenario status: ${error}`);
      return false;
    }
  }

  /**
   * Ensure tests are in the central queue
   */
  public async ensureTestsInCentralQueue(): Promise<boolean> {
    // This is a no-op in the new architecture
    // Tests are always in the central queue
    return true;
  }

  /**
   * Handle WebSocket test result
   * @param resultMessage Result message
   */
  public async handleWebSocketTestResult(resultMessage: any): Promise<boolean> {
    try {
      const { testId, status, result, nodeId } = resultMessage;

      if (!testId) {
        logger.warn('TestManager: Received WebSocket test result without testId');
        return false;
      }

      logger.info(`TestManager: Handling WebSocket test result for test ${testId} with status ${status}`);

      // Create a standardized result object for the central processor
      const standardizedResult = {
        testId,
        nodeId,
        status,
        result
      };

      // Process the result through the central result processor
      const success = await centralResultProcessor.processResult(standardizedResult, 'WebSocket-Result');

      if (success) {

        return true;
      } else {
        logger.warn(`TestManager: WebSocket result for test ${testId} was not processed by central processor`);
        return false;
      }
    } catch (error) {
      logger.error(`TestManager: Error handling WebSocket test result: ${error}`);
      return false;
    }
  }

  /**
   * Get queue status
   * @returns Queue status object
   */
  public async getQueueStatus(): Promise<{ waiting: number; active: number; completed: number; failed: number; delayed: number; queued: number; running: number }> {
    try {
      // Get status from TestQueueService
      const queueStatus = await testQueueService.getQueueStatus();

      // Map to the expected format
      return {
        waiting: queueStatus.waiting || 0,
        active: queueStatus.active || 0,
        completed: queueStatus.completed || 0,
        failed: queueStatus.failed || 0,
        delayed: queueStatus.delayed || 0,
        queued: queueStatus.waiting || 0, // Alias for waiting
        running: queueStatus.active || 0   // Alias for active
      };
    } catch (error) {
      logger.error(`TestManager: Error getting queue status: ${error}`);
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        queued: 0,
        running: 0
      };
    }
  }

  /**
   * Get all tests (primarily non-final states for monitoring)
   * @returns Array of all tests in QUEUED, ASSIGNING, or RUNNING states
   */
  public async getAllTests(): Promise<TestProcess[]> {

    try {
      const { getAllTests: getAllTestsFromDB } = await import('../../services/mongo/testService.js');
      
      // Define the statuses we are interested in for active tests
      const activeStatuses = [TestStatus.QUEUED, TestStatus.ASSIGNING, TestStatus.RUNNING];
      
      // Fetch a large number of recent tests (e.g., last 1000) and filter client-side.
      // testService.getAllTests sorts by queuedAt: -1 by default.
      const result = await getAllTestsFromDB(1000, 0); 

      if (result.success && result.tests) {
        // Filter the tests to include only those with active statuses
        const filteredTests = result.tests.filter((test: any) => 
          test && typeof test.status !== 'undefined' && activeStatuses.includes(test.status)
        );

        return filteredTests as TestProcess[];
      } else {
        logger.warn('TestManager: Failed to retrieve tests from MongoDB or no tests found.');
        return [];
      }
    } catch (error) {
      logger.error(`TestManager: Error getting all tests from MongoDB: ${error}`);
      return [];
    }
  }

  /**
   * Get tests by user ID
   * @param userId User ID
   * @returns Array of tests for the user
   */
  public getTestsByUserId(_userId: string): TestProcess[] {
    logger.warn(`TestManager: getTestsByUserId is deprecated in the new service architecture`);
    return [];
  }

  /**
   * Get tests by scenario ID
   * @param scenarioId Scenario ID
   * @returns Array of tests for the scenario
   */
  public getTestsByScenarioId(_scenarioId: string): TestProcess[] {
    logger.warn(`TestManager: getTestsByScenarioId is deprecated in the new service architecture`);
    return [];
  }

  /**
   * Get tests by run ID (required by TestManagerInterface)
   * @param _runId Run ID (unused in this deprecated method)
   * @returns Array of tests for the run
   */
  public getTestsByRunId(_runId: string): TestProcess[] {
    logger.warn(`TestManager: getTestsByRunId synchronous method is deprecated. Use async version from testService instead.`);
    // Return empty array for interface compliance, actual implementation should use async version
    return [];
  }

  /**
   * Remove a test (required by TestManagerInterface)
   * @param testId Test ID
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async removeTest(testId: string): Promise<boolean> {
    logger.info(`TestManager: Attempting to remove test ${testId}`);

    try {
      // Use the existing stopTest method which handles complete cleanup
      const stopped = await this.stopTest(testId);

      if (stopped) {
        logger.info(`TestManager: Successfully removed test ${testId}`);
        return true;
      } else {
        logger.warn(`TestManager: Failed to remove test ${testId}`);
        return false;
      }
    } catch (error) {
      logger.error(`TestManager: Error removing test ${testId}: ${error}`);
      return false;
    }
  }

  /**
   * Get queued tests (only waiting tests from BullMQ)
   * @returns Array of queued tests
   */
  public async getQueuedTests(): Promise<TestRequest[]> {
    try {
      // Get only waiting tests from TestQueueService
      return await testQueueService.getWaitingTests();
    } catch (error) {
      logger.error(`TestManager: Error getting queued tests: ${error}`);
      return [];
    }
  }

  /**
   * Get running tests (only active tests from BullMQ)
   * @returns Array of running tests
   */
  public async getRunningTests(): Promise<TestRequest[]> {
    try {
      // Get only active tests from TestQueueService
      return await testQueueService.getActiveTests();
    } catch (error) {
      logger.error(`TestManager: Error getting running tests: ${error}`);
      return [];
    }
  }

  /**
   * Get stuck tests (tests that have been active for too long)
   * @returns Array of stuck tests
   */
  public async getStuckTests(): Promise<any[]> {
    try {
      // Get stuck tests from TestQueueService
      return await testQueueService.getStuckTests();
    } catch (error) {
      logger.error(`TestManager: Error getting stuck tests: ${error}`);
      return [];
    }
  }

  /**
   * Get next queued test
   * @returns Next queued test or null if none
   */
  public async getNextQueuedTest(): Promise<TestProcess | null> {
    logger.warn(`TestManager: getNextQueuedTest is deprecated in the new service architecture`);
    return null;
  }

  /**
   * Get a test by ID
   * @param testId Test identifier
   * @returns Test if found, null otherwise
   */
  public async getTest(testId: string): Promise<TestProcess | null> {

    try {
      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (queue) {
        const job = await queue.getJob(testId);
        if (job && job.data) {

          // Cast TestRequest (job.data) to TestProcess for compatibility
          // This might need further refinement based on what TestMonitoringService expects
          return job.data as TestProcess;
        }
      } else {
        logger.warn(`TestManager: Could not get queue ${QUEUE_NAMES.TEST_QUEUE} to fetch test ${testId}.`);
      }

      // Fallback: Try to get from MongoDB via testService as a last resort,
      // though this primarily stores completed/archived test data.
      // The active test data should be in the queue.
      const { getTestById } = await import('../../services/mongo/testService.js');
      const dbResult = await getTestById(testId);
      if (dbResult.success && dbResult.test) {
        return dbResult.test as TestProcess;
      }

      logger.warn(`TestManager: Test ${testId} not found in queue or MongoDB.`);
      return null;
    } catch (error) {
      logger.error(`TestManager: Error getting test ${testId}: ${error}`);
      return null;
    }
  }

  /**
   * Legacy method for updating test status
   * @param testId Test ID
   * @param status New status
   * @param result Test result
   * @returns Success status
   * @deprecated This method is deprecated in the new service architecture
   */
  public async updateTestStatusLegacy(_testId: string, _status: TestStatus, _result: any): Promise<boolean> {
    // Silently return true to maintain backward compatibility
    // without generating log spam
    return true;
  }

  /**
   * Update test progress
   * @param testId Test ID
   * @param step Current step
   * @param totalSteps Total steps
   * @param stepData Step data
   * @returns Success status
   */
  public async updateTestProgress(testId: string, step: number, totalSteps: number, stepData: any): Promise<boolean> {
    logger.warn(`TestManager: updateTestProgress is deprecated in the new service architecture`);

    // Create a standardized result object for the central processor
    const standardizedResult = {
      testId,
      status: 'progress',
      result: {
        step,
        totalSteps,
        stepData
      }
    };

    // Process the result through the central processor
    return centralResultProcessor.processResult(standardizedResult, 'API-Progress-Update');
  }

  /**
   * Assign test to node (atomic version)
   * @param testId Test ID
   * @param nodeId Node ID
   * @param options Additional options
   * @returns Success status
   */
  public async assignTestToNodeAtomic(_testId: string, _nodeId: string, _options: any = {}): Promise<boolean> {
    logger.warn(`TestManager: assignTestToNodeAtomic is deprecated in the new service architecture`);
    return false;
  }

  /**
   * Requeue a test
   * @param testId Test ID
   * @param priority Priority
   * @returns Success status
   */
  public async requeueTest(testId: string, priority: number = 1): Promise<boolean> { // Defaulting priority to 1 (high)
    logger.info(`TestManager: Re-queueing test ${testId} with priority ${priority}`);
    try {
      const testData = await this.getTest(testId) as TestRequest; // Get the original test data

      if (!testData) {
        logger.warn(`TestManager: Test ${testId} not found, cannot re-queue.`);
        return false;
      }

      // 1. Remove the old job from the queue
      const queue = queueService.getQueue(QUEUE_NAMES.TEST_QUEUE);
      if (queue) {
        const job = await queue.getJob(testId);
        if (job) {
          try {
            await job.remove();

          } catch (removeError) {
            logger.error(`TestManager: Error removing job ${testId} from queue during re-queue: ${removeError}. Proceeding to re-add anyway.`);
            // Proceeding because re-adding with the same job ID might overwrite or BullMQ handles it.
          }
        } else {
          logger.warn(`TestManager: Job ${testId} not found in queue for removal, it might have been processed or already removed.`);
        }
      } else {
        logger.error(`TestManager: Could not get queue ${QUEUE_NAMES.TEST_QUEUE} to remove job ${testId}.`);
        // Don't necessarily fail here, re-adding might still be desired.
      }

      // 2. Add the test back to the queue using testQueueService
      // Ensure critical fields are preserved or reset for a fresh queue entry
      const testToRequeue: TestRequest = {
        ...testData,
        status: TestStatus.QUEUED, // Explicitly set status to QUEUED
        queuedAt: new Date(),     // Update queuedAt time
        // Clear any node-specific or runtime-specific data that shouldn't persist
        nodeId: undefined,
        startedAt: undefined,
        completedAt: undefined,
        // executionDetails: undefined, // If such a field exists
      };


      const newJobId = await testQueueService.addTest(testToRequeue, priority);

      if (newJobId) {

        this.emit('testRequeued', { testId, newJobId, priority });
        return true;
      } else {
        logger.error(`TestManager: Failed to re-queue test ${testId} via testQueueService.`);
        return false;
      }
    } catch (error) {
      logger.error(`TestManager: Error re-queueing test ${testId}: ${error}`);
      return false;
    }
  }

  /**
   * Get tests by status
   * @param status Test status
   * @returns Promise resolving to array of tests
   */
  public async getTestsByStatus(status: TestStatus): Promise<any[]> {
    try {
      // Get tests collection
      const collection = await getTestsCollection();

      // Get tests from database
      const tests = await collection.find({
        status
      }).toArray();

      return tests;
    } catch (error) {
      logger.error(`TestManager: Error getting tests by status: ${error}`);
      return [];
    }
  }

  /**
   * Validate if a status transition is allowed
   * @param currentStatus Current test status
   * @param newStatus New test status
   * @returns True if transition is valid, false otherwise
   */
  private isValidStatusTransition(currentStatus: string, newStatus: TestStatus): boolean {
    // Allow any transition from unknown status (initial state)
    if (currentStatus === 'unknown') {
      return true;
    }

    // Define valid transitions
    const validTransitions: Record<string, TestStatus[]> = {
      [TestStatus.QUEUED]: [TestStatus.RUNNING, TestStatus.ASSIGNED, TestStatus.ASSIGNING, TestStatus.STOPPED, TestStatus.FAILED],
      [TestStatus.ASSIGNED]: [TestStatus.RUNNING, TestStatus.STOPPED, TestStatus.FAILED],
      [TestStatus.ASSIGNING]: [TestStatus.ASSIGNED, TestStatus.RUNNING, TestStatus.STOPPED, TestStatus.FAILED],
      [TestStatus.RUNNING]: [TestStatus.COMPLETED, TestStatus.FAILED, TestStatus.STOPPED, TestStatus.STOPPING],
      [TestStatus.STOPPING]: [TestStatus.STOPPED, TestStatus.FAILED],
      [TestStatus.COMPLETED]: [], // Terminal state
      [TestStatus.FAILED]: [], // Terminal state
      [TestStatus.STOPPED]: [], // Terminal state
      [TestStatus.TIMEOUT]: [], // Terminal state
      [TestStatus.ERROR]: [TestStatus.FAILED, TestStatus.STOPPED] // Allow recovery from error
    };

    const allowedTransitions = validTransitions[currentStatus] || [];
    return allowedTransitions.includes(newStatus);
  }

  /**
   * Update test status with additional fields and race condition protection
   * @param testId Test ID
   * @param status New status
   * @param additionalFields Additional fields to update
   * @returns Promise resolving to true if successful, false otherwise
   */
  public async updateTestStatus(testId: string, status: TestStatus, additionalFields: Record<string, any> = {}): Promise<boolean> {
    // Extract transaction ID for tracking if available
    const transactionId = additionalFields.transactionId || `tm-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    // Enhanced logging for status update verification
    logger.info(`[STATUS-VERIFICATION] TestManager.updateTestStatus called:
      - Test ID: ${testId}
      - Status: ${status}
      - Transaction ID: ${transactionId}
      - Additional Fields: ${JSON.stringify(Object.keys(additionalFields))}
      - Timestamp: ${new Date().toISOString()}`);

    // Use Redis lock to prevent race conditions on test status updates
    logger.info(`[STATUS-VERIFICATION] Attempting to acquire lock for test ${testId} (transaction: ${transactionId})`);
    const { lockService } = await import('../../services/redis/lockService.js');
    const lockResource = `test-status-${testId}`;
    const lockValue = await lockService.acquireLock(lockResource, 10000, 5, 200); // 10s TTL, 5 retries, 200ms delay

    if (!lockValue) {
      logger.error(`[STATUS-VERIFICATION] CRITICAL: Failed to acquire lock for test ${testId} status update (transaction: ${transactionId})`);
      return false;
    }

    logger.info(`[STATUS-VERIFICATION] Successfully acquired lock for test ${testId} (transaction: ${transactionId}, lockValue: ${lockValue})`)

    try {
      // Get tests collection
      logger.info(`[STATUS-VERIFICATION] Getting tests collection for test ${testId} (transaction: ${transactionId})`);
      const collection = await getTestsCollection();

      // Get current test status for comparison
      logger.info(`[STATUS-VERIFICATION] Querying database for current test status ${testId} (transaction: ${transactionId})`);
      const currentTest = await collection.findOne({ id: testId });
      const currentStatus = currentTest?.status || 'unknown';

      logger.info(`[STATUS-VERIFICATION] Database query result - Current test status: ${currentStatus}, updating to: ${status} (transaction: ${transactionId})`);

      if (!currentTest) {
        logger.error(`[STATUS-VERIFICATION] CRITICAL: Test ${testId} not found in database (transaction: ${transactionId})`);
        return false;
      }

      // Validate status transition to prevent invalid updates
      logger.info(`[STATUS-VERIFICATION] Validating status transition from ${currentStatus} to ${status} for test ${testId} (transaction: ${transactionId})`);
      if (!this.isValidStatusTransition(currentStatus, status)) {
        logger.error(`[STATUS-VERIFICATION] CRITICAL: Invalid status transition from ${currentStatus} to ${status} for test ${testId} (transaction: ${transactionId})`);
        return false;
      }

      logger.info(`[STATUS-VERIFICATION] Status transition validation passed for test ${testId} (transaction: ${transactionId})`)

      // Prepare update fields
      logger.info(`[STATUS-VERIFICATION] Preparing update fields for test ${testId} (transaction: ${transactionId})`);
      const updateFields: Record<string, any> = {
        status,
        lastUpdated: new Date() // Always add lastUpdated timestamp
      };

      // Add timestamp based on status
      if (status === TestStatus.RUNNING) {
        updateFields.startedAt = new Date();
        logger.info(`[STATUS-VERIFICATION] Setting startedAt timestamp for test ${testId} (transaction: ${transactionId})`);
      } else if (status === TestStatus.COMPLETED || status === TestStatus.FAILED || status === TestStatus.STOPPED) {
        updateFields.completedAt = new Date();
        logger.info(`[STATUS-VERIFICATION] Setting completedAt timestamp for test ${testId} (transaction: ${transactionId})`);
      }

      // Add additional fields (excluding transactionId to avoid storing it in the document)
      Object.keys(additionalFields).forEach(key => {
        if (key !== 'transactionId') {
          updateFields[key] = additionalFields[key];
        }
      });

      logger.info(`[STATUS-VERIFICATION] Update fields prepared: ${JSON.stringify(updateFields)} for test ${testId} (transaction: ${transactionId})`);

      // Update test in database with retry logic
      let result;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          logger.info(`[STATUS-VERIFICATION] Executing database update for test ${testId} (transaction: ${transactionId}, attempt: ${retryCount + 1})`);
          result = await collection.updateOne(
            { id: testId },
            { $set: updateFields }
          );
          logger.info(`[STATUS-VERIFICATION] Database update completed for test ${testId} (transaction: ${transactionId}, matchedCount: ${result.matchedCount}, modifiedCount: ${result.modifiedCount})`);
          break; // Success, exit retry loop
        } catch (error: any) {
          retryCount++;
          logger.error(`[STATUS-VERIFICATION] Database update attempt ${retryCount} failed for test ${testId}: ${error.message} (transaction: ${transactionId})`);
          if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
            // Duplicate key error or write conflict, or max retries reached
            logger.error(`[STATUS-VERIFICATION] CRITICAL: Test status update failed for test ${testId} after ${retryCount} attempts: ${error.message} (transaction: ${transactionId})`);
            throw error;
          }
          // Wait before retry with exponential backoff
          const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
          logger.warn(`[STATUS-VERIFICATION] Retrying test status update for test ${testId} in ${delay}ms (attempt ${retryCount}/${maxRetries}, transaction: ${transactionId})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      if (result!.matchedCount === 0) {
        logger.error(`[STATUS-VERIFICATION] CRITICAL: Test ${testId} not found in database, cannot update status (transaction: ${transactionId})`);
        return false;
      }

      if (result!.modifiedCount === 0) {
        logger.warn(`[STATUS-VERIFICATION] Test ${testId} status was already ${status}, no update needed (transaction: ${transactionId})`);
        return true; // Not an error, just no change needed
      }

      logger.info(`[STATUS-VERIFICATION] SUCCESS: Updated test ${testId} status to ${status} (transaction: ${transactionId}, matchedCount: ${result!.matchedCount}, modifiedCount: ${result!.modifiedCount})`);

      // If this is a RUNNING status update, also update the run status if runReportId is provided
      if (status === TestStatus.RUNNING && additionalFields.runReportId) {
        try {
          logger.info(`[STATUS-VERIFICATION] Updating run ${additionalFields.runReportId} status to RUNNING (transaction: ${transactionId})`);
          await this.runCoordinator.updateRunStatus(additionalFields.runReportId, 'running');
        } catch (runError) {
          logger.error(`[STATUS-VERIFICATION] Error updating run status: ${runError} (transaction: ${transactionId})`);
          // Don't fail the test status update if run update fails
        }
      }

      // Emit event for status change
      this.emit('testStatusChanged', {
        testId,
        oldStatus: currentStatus,
        newStatus: status,
        transactionId,
        timestamp: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error(`[STATUS-VERIFICATION] Error updating test status: ${error} (transaction: ${transactionId})`);
      return false;
    } finally {
      // Always release the lock
      await lockService.releaseLock(lockResource, lockValue);
      logger.debug(`[STATUS-VERIFICATION] Released lock for test ${testId} status update (transaction: ${transactionId})`);
    }
  }

  /**
   * Check if a test requires SauceLabs device and if it's available
   * @param test Test data
   * @returns Promise resolving to true if device is available or not required, false otherwise
   */
  public async checkSauceLabsDeviceAvailability(test: any): Promise<boolean> {
    try {
      // Check if test is for Android platform and uses SauceLabs
      if (test.platform !== 'android' ||
          !test.environmentSettings?.appium?.useSauceLabs ||
          !test.environmentSettings?.appium?.sauceLabsDeviceId) {
        // Not a SauceLabs test or no device specified
        return true;
      }

      const deviceId = test.environmentSettings.appium.sauceLabsDeviceId;
      const userId = test.userId || test.executedUser;

      if (!userId) {
        logger.warn(`TestManager: Test ${test.id} missing userId, cannot check device availability`);
        return true; // Assume available if we can't check
      }

      // Import dynamically to avoid circular dependencies
      const { sauceLabsDeviceAvailabilityManager } = await import('../../services/saucelabs/SauceLabsDeviceAvailabilityManager.js');

      // Check device availability
      return sauceLabsDeviceAvailabilityManager.isDeviceAvailable(userId, deviceId);
    } catch (error) {
      logger.error(`TestManager: Error checking SauceLabs device availability: ${error}`);
      return true; // Assume available on error to avoid blocking tests
    }
  }

  /**
   * Handle stuck test recovery
   * @param testId Test ID that is stuck
   * @param nodeId Node ID where test was stuck
   * @param details Details about why the test is stuck
   */
  private async handleStuckTest(testId: string, nodeId: string, details: any): Promise<void> {
    try {
      logger.error(`TestManager: Handling stuck test ${testId} on node ${nodeId}: ${details.reason}`);

      // 1. Get test information
      const test = await this.getTest(testId);
      if (!test) {
        logger.warn(`TestManager: Stuck test ${testId} not found in database`);
        return;
      }

      // 2. Force stop the test with stuck reason
      logger.info(`TestManager: Force stopping stuck test ${testId}`);

      // Create a failed result for the stuck test
      const stuckResult = {
        success: false,
        message: `Test stuck and terminated: ${details.reason}`,
        status: 'failed',
        failedAt: new Date().toISOString(),
        failureReason: 'test_stuck',
        stuckDetails: {
          nodeId: nodeId,
          reason: details.reason,
          timeSinceLastSeen: details.timeSinceLastSeen,
          lastActivity: details.lastActivity
        }
      };

      // 3. Update test status in database
      await this.updateTestStatus(testId, TestStatus.FAILED, {
        failureReason: 'test_stuck',
        completedAt: new Date(),
        result: stuckResult
      });

      // 4. Clean up BullMQ job
      await this.completeBullMQJobForStoppedTest(testId, test);

      // 5. Clean up Redis locks and step progress
      await this.cleanupTestLocks(testId);
      await this.cleanupTestStepProgress(testId);

      // 6. Send failed result to result queue
      await this.sendStoppedResultToQueue(testId, test, stuckResult);

      // 7. Emit event for monitoring
      this.emit('testStuck', { testId, nodeId, details, result: stuckResult });

      logger.info(`TestManager: Successfully handled stuck test ${testId} recovery`);

    } catch (error) {
      logger.error(`TestManager: Error handling stuck test ${testId}: ${error}`);

      // Emit error event for monitoring
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.emit('testStuckHandlingFailed', { testId, nodeId, details, error: errorMessage });
    }
  }

  /**
   * Execute a run with all its scenarios
   * @param runId Run identifier
   * @param options Additional options for the run
   */
  public async executeRun(runId: string, options: {
    userId?: string;
    executionId?: string;
    isScheduled?: boolean;
    scheduleId?: string;
    scheduleRunId?: string;
  } = {}): Promise<{ success: boolean; message?: string }> {
    try {
      // Delegate to the run coordinator
      return this.runCoordinator.executeRun(runId, options);
    } catch (error: any) {
      logger.error(`TestManager: Error executing run ${runId}: ${error.message}`);
      return { success: false, message: `Error executing run: ${error.message}` };
    }
  }
}

// Export singleton instance
export const testManager = TestManager.getInstance();
export default testManager;
