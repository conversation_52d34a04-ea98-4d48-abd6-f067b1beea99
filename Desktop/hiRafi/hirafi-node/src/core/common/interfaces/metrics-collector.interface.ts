/**
 * Metrics Collector Interface
 * Defines the contract for platform-specific metrics collectors
 */

import { EnhancedMetrics } from '../../../models/types.js';

/**
 * Metrics Collector Interface
 * Handles collecting performance metrics during test execution
 */
export interface IMetricsCollector {
  /**
   * Set up metrics collection for a test
   * @param target The target to collect metrics from (Puppeteer Page, WebdriverIO Browser, etc.)
   * @param testId Test identifier
   * @param reportSettings Settings for what metrics to collect
   */
  setup(target: any, testId: string, reportSettings: any): Promise<void>;

  /**
   * Start collecting metrics
   * @param target The target to collect metrics from
   */
  startCollection(target: any): Promise<void>;

  /**
   * Stop collecting metrics
   */
  stopCollection(): Promise<void>;

  /**
   * Collect metrics from the target
   * @param target The target to collect metrics from
   * @param options Options for metrics collection
   * @returns Promise resolving to collected metrics
   */
  collect(target: any, options: any): Promise<Partial<EnhancedMetrics>>;

  /**
   * Collect final metrics before cleanup to prevent race conditions
   * @param target The target to collect metrics from
   * @param options Options for metrics collection
   * @returns Promise resolving to collected metrics
   */
  collectMetrics(target: any, options: any): Promise<Partial<EnhancedMetrics>>;

  /**
   * Collect metrics with enhanced coordination to prevent race conditions
   * @param target The target to collect metrics from
   * @param options Options for metrics collection
   * @returns Promise resolving to collected metrics
   */
  collectWithCoordination(target: any, options: any): Promise<Partial<EnhancedMetrics>>;

  /**
   * Initialize the metrics collector with configuration
   * @param config Configuration object
   */
  initialize(config: any): Promise<void>;

  /**
   * Check if metrics collection is currently active
   * @returns True if collecting metrics, false otherwise
   */
  isCollecting(): boolean;
}
