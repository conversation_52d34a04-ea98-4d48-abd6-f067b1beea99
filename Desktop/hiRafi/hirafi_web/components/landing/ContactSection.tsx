import React, { useState } from 'react';
import { Calendar, Phone, MessageSquare } from 'lucide-react';
import { submitContactForm } from '@/lib/api/landing-page-api';

const ContactSection = () => {
  const [activeTab, setActiveTab] = useState('poc');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: '',
    phone: '',
    preferredDate: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [thankYouMessage, setThankYouMessage] = useState('Thank you for your interest! We will contact you shortly.');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Validate email is a business email (not gmail, hotmail, etc.)
  const validateBusinessEmail = (email: string): boolean => {
    const personalEmailDomains = [
      'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
      'icloud.com', 'mail.com', 'protonmail.com', 'zoho.com', 'yandex.com',
      'gmx.com', 'live.com', 'msn.com', 'me.com', 'inbox.com'
    ];

    const domain = email.split('@')[1]?.toLowerCase();
    return domain ? !personalEmailDomains.includes(domain) : false;
  };

  // Validate date is not in the past
  const validateFutureDate = (date: string): boolean => {
    if (!date) return true; // If no date is provided, it's valid

    const selectedDate = new Date(date);
    selectedDate.setHours(0, 0, 0, 0); // Reset time part

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time part

    return selectedDate >= today;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError('');

    // Validate business email
    if (!validateBusinessEmail(formData.email)) {
      setSubmitError('Please use your company email address.');
      setIsSubmitting(false);
      return;
    }

    // Validate future date
    if (formData.preferredDate && !validateFutureDate(formData.preferredDate)) {
      setSubmitError('Please select a future date for your meeting.');
      setIsSubmitting(false);
      return;
    }

    try {
      // Submit form data to API
      const response = await submitContactForm(formData);

      if (response.success) {
        setSubmitSuccess(true);
        // Use custom thank you message if provided from server - check data wrapper
        const thankYouMsg = response.data?.thankYouMessage || response.thankYouMessage;
        if (thankYouMsg) {
          setThankYouMessage(thankYouMsg);
        }

        // Reset form
        setFormData({
          name: '',
          email: '',
          company: '',
          message: '',
          phone: '',
          preferredDate: '',
        });
      } else {
        setSubmitError(response.data?.error || response.error || 'Failed to submit form. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setSubmitError('An unexpected error occurred. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Get Started with HiRafi</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Schedule a personalized demo or reach out with your questions. Our team is ready to help.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="flex flex-col sm:flex-row">
              <div className="sm:w-2/5 bg-gradient-to-br from-blue-600 to-purple-600 text-white p-8">
                <h3 className="text-2xl font-bold mb-6">Contact Information</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <Phone className="mr-4 flex-shrink-0" size={24} />
                    <div>
                      <p className="font-medium">Call us</p>
                      <p className="opacity-80">+90 216 504 56 55</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <MessageSquare className="mr-4 flex-shrink-0" size={24} />
                    <div>
                      <p className="font-medium">Email us</p>
                      <p className="opacity-80"><EMAIL></p>
                    </div>
                  </div>
                </div>

                <div className="mt-12">
                  <h4 className="text-lg font-semibold mb-4">What happens next?</h4>
                  <ol className="space-y-4">
                    <li className="flex items-start">
                      <div className="bg-white/20 w-6 h-6 rounded-full flex items-center justify-center text-sm mr-3 flex-shrink-0">1</div>
                      <p>We'll contact you within 24 hours</p>
                    </li>
                    <li className="flex items-start">
                      <div className="bg-white/20 w-6 h-6 rounded-full flex items-center justify-center text-sm mr-3 flex-shrink-0">2</div>
                      <p>Discuss your testing needs and challenges</p>
                    </li>
                    <li className="flex items-start">
                      <div className="bg-white/20 w-6 h-6 rounded-full flex items-center justify-center text-sm mr-3 flex-shrink-0">3</div>
                      <p>Get a customized POC plan</p>
                    </li>
                  </ol>
                </div>
              </div>

              <div className="sm:w-3/5 p-8">
                <div className="mb-6 border-b">
                  <div className="flex">
                    <button
                      onClick={() => setActiveTab('poc')}
                      className={`px-4 py-2 mr-4 font-medium ${
                        activeTab === 'poc'
                          ? 'border-b-2 border-blue-600 text-blue-600'
                          : 'text-gray-500'
                      }`}
                    >
                      <Calendar size={18} className="inline mr-2" />
                      Schedule POC
                    </button>
                    <button
                      onClick={() => setActiveTab('contact')}
                      className={`px-4 py-2 font-medium ${
                        activeTab === 'contact'
                          ? 'border-b-2 border-blue-600 text-blue-600'
                          : 'text-gray-500'
                      }`}
                    >
                      <MessageSquare size={18} className="inline mr-2" />
                      Contact Us
                    </button>
                  </div>
                </div>

                {submitSuccess ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <div className="bg-green-100 p-4 rounded-full mb-4">
                      <svg className="h-12 w-12 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-medium mb-2">Thank You!</h3>
                    <p className="text-gray-600 max-w-md mb-6">
                      {thankYouMessage}
                    </p>
                    <button
                      onClick={() => setSubmitSuccess(false)}
                      className="px-5 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg shadow-md hover:shadow-xl transition-all"
                    >
                      Send Another Message
                    </button>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit}>
                    {submitError && (
                      <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-600">{submitError}</p>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                            Full Name
                          </label>
                        </div>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          required
                          disabled={isSubmitting}
                        />
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                            Email
                          </label>
                          <span className="text-xs text-gray-500">Please use your company email</span>
                        </div>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          required
                          disabled={isSubmitting}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <label htmlFor="company" className="block text-sm font-medium text-gray-700">
                            Company
                          </label>
                        </div>
                        <input
                          type="text"
                          id="company"
                          name="company"
                          value={formData.company}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          required
                          disabled={isSubmitting}
                        />
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                            Phone Number
                          </label>
                        </div>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>

                    {activeTab === 'poc' && (
                      <div className="mt-6">
                        <div className="flex justify-between items-center mb-1">
                          <label htmlFor="preferredDate" className="block text-sm font-medium text-gray-700">
                            Preferred POC Date
                          </label>
                        </div>
                        <input
                          type="date"
                          id="preferredDate"
                          name="preferredDate"
                          value={formData.preferredDate}
                          onChange={handleChange}
                          min={new Date().toISOString().split('T')[0]} // Set minimum date to today
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          disabled={isSubmitting}
                        />
                      </div>
                    )}

                    <div className="mt-6">
                      <div className="flex justify-between items-center mb-1">
                        <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                          {activeTab === 'poc' ? 'Tell us about your testing needs' : 'Message'}
                        </label>
                      </div>
                      <textarea
                        id="message"
                        name="message"
                        rows={4}
                        value={formData.message}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                        disabled={isSubmitting}
                      ></textarea>
                    </div>

                    <div className="mt-6">
                      <button
                        type="submit"
                        className="w-full py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg shadow-md hover:shadow-xl transition-all"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <span className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Submitting...
                          </span>
                        ) : (
                          activeTab === 'poc' ? 'Schedule My POC' : 'Send Message'
                        )}
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;