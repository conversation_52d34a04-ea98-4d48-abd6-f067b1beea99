/**
 * Team Access Control Middleware
 * Provides consistent team-based access control across all API endpoints
 */

import { Response, NextFunction } from 'express';
import { AuthRequest } from './authMiddleware.js';
import { logger } from '../../utils/logger.js';

/**
 * Middleware to check if user has access to a specific team resource
 * @param getTeamIdFromRequest Function to extract team ID from request
 * @param resourceName Name of the resource for error messages
 */
export function checkTeamAccess(
  getTeamIdFromRequest: (req: AuthRequest) => string | null,
  resourceName: string = 'resource'
) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const userTeamId = req.user?.teamId;
      const accountType = req.user?.accountType;
      
      // Admin ve company_owner her zaman erişebilir
      if (accountType === 'admin' || accountType === 'company_owner') {
        logger.debug(`[TEAM_ACCESS] User ${userId} is ${accountType}, granting access to ${resourceName}`);
        return next();
      }

      if (!userId || !userTeamId) {
        logger.warn(`[TEAM_ACCESS] User ID or Team ID missing for ${resourceName} access`);
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const resourceTeamId = getTeamIdFromRequest(req);
      
      if (!resourceTeamId) {
        logger.warn(`[TEAM_ACCESS] Could not determine team ID for ${resourceName}`);
        return res.status(400).json({
          success: false,
          error: `Team ID is required for ${resourceName} access`
        });
      }

      if (resourceTeamId !== userTeamId) {
        logger.warn(`[TEAM_ACCESS] User ${userId} (team: ${userTeamId}) attempted to access ${resourceName} from team ${resourceTeamId}`);
        return res.status(403).json({
          success: false,
          error: `You can only access ${resourceName}s from your team`
        });
      }

      logger.debug(`[TEAM_ACCESS] User ${userId} granted access to ${resourceName} from team ${resourceTeamId}`);
      next();
    } catch (error: any) {
      logger.error(`[TEAM_ACCESS] Error in team access check: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: 'Internal server error during team access check'
      });
    }
  };
}

/**
 * Helper function to create team access middleware for run resources
 */
export function checkRunTeamAccess() {
  return checkTeamAccess(
    (req: AuthRequest) => {
      // Extract team ID from run data that should be attached to req by previous middleware
      return (req as any).runTeamId || null;
    },
    'run'
  );
}

/**
 * Helper function to create team access middleware for scenario resources
 */
export function checkScenarioTeamAccess() {
  return checkTeamAccess(
    (req: AuthRequest) => {
      // Extract team ID from scenario data that should be attached to req by previous middleware
      return (req as any).scenarioTeamId || null;
    },
    'scenario'
  );
}

/**
 * Middleware to fetch run data and attach team ID to request
 * This should be used before checkRunTeamAccess()
 */
export function attachRunTeamId(getRunById: (runId: string) => Promise<any>) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const { runId } = req.params;
      
      if (!runId) {
        return res.status(400).json({
          success: false,
          error: 'Run ID is required'
        });
      }

      const runResult = await getRunById(runId);
      
      if (!runResult.success || !runResult.run) {
        return res.status(404).json({
          success: false,
          error: 'Run not found'
        });
      }

      // Attach run team ID to request for team access check
      (req as any).runTeamId = runResult.run.teamId;
      (req as any).runData = runResult.run;
      
      next();
    } catch (error: any) {
      logger.error(`[ATTACH_RUN_TEAM] Error fetching run data: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
}

/**
 * Middleware to fetch scenario data and attach team ID to request
 * This should be used before checkScenarioTeamAccess()
 */
export function attachScenarioTeamId(getScenarioById: (scenarioId: string) => Promise<any>) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const { scenarioId } = req.params;
      
      if (!scenarioId) {
        return res.status(400).json({
          success: false,
          error: 'Scenario ID is required'
        });
      }

      const scenarioResult = await getScenarioById(scenarioId);
      
      if (!scenarioResult.success || !scenarioResult.scenario) {
        return res.status(404).json({
          success: false,
          error: 'Scenario not found'
        });
      }

      // Attach scenario team ID to request for team access check
      (req as any).scenarioTeamId = scenarioResult.scenario.teamId;
      (req as any).scenarioData = scenarioResult.scenario;
      
      next();
    } catch (error: any) {
      logger.error(`[ATTACH_SCENARIO_TEAM] Error fetching scenario data: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
}
