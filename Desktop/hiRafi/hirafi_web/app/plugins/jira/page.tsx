"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react"
import { useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Check, ChevronRight, Puzzle, AlertCircle, ArrowRight, CheckCircle2, LoaderCircle } from "lucide-react"
import { CompanyOwnerRoute } from "@/components/company-owner-route"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import { 
  testJiraConnection, 
  getJiraProjects, 
  getJiraIssueTypes, 
  saveJiraConfig, 
  getJiraConfig, 
  disconnectJira 
} from "@/lib/api/plugin-api"

function JiraPlugin() {
  const router = useRouter()
  const { toast } = useToast()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [configStep, setConfigStep] = useState(1)
  const [configData, setConfigData] = useState({
    url: "",
    apiToken: "",
    email: "",
    selectedProjects: [] as string[],
    selectedIssueTypes: [] as string[],
    projectsData: [] as {id: string, key: string, name: string}[],
    issueTypesData: [] as {id: string, name: string}[],
  })
  const [isActive, setIsActive] = useState(false)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<"idle" | "success" | "error">("idle")
  const [connectionError, setConnectionError] = useState("")
  const [projects, setProjects] = useState<any[]>([])
  const [isLoadingProjects, setIsLoadingProjects] = useState(false)
  const [issueTypes, setIssueTypes] = useState<any[]>([])
  const [issueTypesByProject, setIssueTypesByProject] = useState<Record<string, any[]>>({})
  const [isLoadingIssueTypes, setIsLoadingIssueTypes] = useState(false)
  const [activeConfigData, setActiveConfigData] = useState<any>(null)
  const [isLoadingConfig, setIsLoadingConfig] = useState(false)
  const [lastSyncedTime, setLastSyncedTime] = useState<string>('')
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false)
  const [isDisconnecting, setIsDisconnecting] = useState(false)

  // Load existing configuration on component mount
  useEffect(() => {
    fetchActiveConfig()
  }, [])

  // Fetch issue types when projects are selected (OPTIMIZED)
  useEffect(() => {
    if (configData.selectedProjects.length > 0 && 
        configData.url?.trim() && 
        configData.email?.trim() && 
        configData.apiToken?.trim()) {
      fetchIssueTypesForSelectedProjects()
    }
  }, [configData.selectedProjects])  // Only depend on selected projects, not credentials

  // 🚀 PERFORMANCE: Memoized input handlers
  const handleUrlChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setConfigData(prev => ({ ...prev, url: e.target.value }))
  }, [])

  const handleApiTokenChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setConfigData(prev => ({ ...prev, apiToken: e.target.value }))
  }, [])

  const handleEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setConfigData(prev => ({ ...prev, email: e.target.value }))
  }, [])

  // 🚀 PERFORMANCE: Memoized project selection handler
  const handleProjectToggle = useCallback((project: any) => {
    setConfigData(prev => {
      const isSelected = prev.selectedProjects.includes(project.key)
      
      const updatedProjects = isSelected
        ? prev.selectedProjects.filter(key => key !== project.key)
        : [...prev.selectedProjects, project.key]
      
      const updatedProjectsData = isSelected
        ? prev.projectsData.filter(p => p.key !== project.key)
        : [...prev.projectsData, { id: project.id, key: project.key, name: project.name }]
      
      return {
        ...prev,
        selectedProjects: updatedProjects,
        projectsData: updatedProjectsData
      }
    })
  }, [])

  // 🚀 PERFORMANCE: Memoized issue type selection handler  
  const handleIssueTypeToggle = useCallback((type: any) => {
    setConfigData(prev => {
      const isSelected = prev.selectedIssueTypes.includes(type.id)
      
      const updatedIssueTypes = isSelected
        ? prev.selectedIssueTypes.filter(id => id !== type.id)
        : [...prev.selectedIssueTypes, type.id]
      
      const updatedIssueTypesData = isSelected
        ? prev.issueTypesData.filter(t => t.id !== type.id)
        : [...prev.issueTypesData, { id: type.id, name: type.name }]
      
      return {
        ...prev,
        selectedIssueTypes: updatedIssueTypes,
        issueTypesData: updatedIssueTypesData
      }
    })
  }, [])

  const fetchActiveConfig = async () => {
    setIsLoadingConfig(true)
    try {
      const response = await getJiraConfig()
      if (response.success && response.data) {
        setActiveConfigData(response.data)
        setIsActive(true)
        setConfigData({
          url: response.data.url || '',
          email: response.data.email || '',
          apiToken: response.data.apiToken || '',
          selectedProjects: response.data.projectsData?.map((p: any) => p.key) || [],
          selectedIssueTypes: response.data.issueTypesData?.map((t: any) => t.id) || [],
          projectsData: response.data.projectsData || [],
          issueTypesData: response.data.issueTypesData || []
        })
        if (response.data.lastSyncedAt) {
          setLastSyncedTime(new Date(response.data.lastSyncedAt).toLocaleString())
        }
      }
    } catch (error) {
      console.error('Failed to fetch Jira config:', error)
    } finally {
      setIsLoadingConfig(false)
    }
  }

  const fetchIssueTypesForSelectedProjects = useCallback(async () => {
    if (configData.selectedProjects.length === 0) return

    setIsLoadingIssueTypes(true)
    try {
      const allIssueTypes: any[] = []
      const issueTypesByProj: Record<string, any[]> = {}

      for (const projectKey of configData.selectedProjects) {
        const response = await getJiraIssueTypes({
          url: configData.url,
          email: configData.email,
          apiToken: configData.apiToken,
          projectKey
        })

        if (response.success && response.data) {
          const projectIssueTypes = response.data
          issueTypesByProj[projectKey] = projectIssueTypes
          
          // Add unique issue types to the combined list
          projectIssueTypes.forEach((issueType: any) => {
            if (!allIssueTypes.find(t => t.id === issueType.id)) {
              allIssueTypes.push(issueType)
            }
          })
        }
      }

      setIssueTypes(allIssueTypes)
      setIssueTypesByProject(issueTypesByProj)
    } catch (error) {
      toast({
        title: "Hata",
        description: "Issue type'lar alınırken bir hata oluştu.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingIssueTypes(false)
    }
  }, [configData.selectedProjects, configData.url, configData.email, configData.apiToken, toast])

  const handleDisconnect = async () => {
    setIsDisconnecting(true)
    try {
      const response = await disconnectJira()
      if (response.success) {
        setIsActive(false)
        setActiveConfigData(null)
        setConfigData({
          url: "",
          apiToken: "",
          email: "",
          selectedProjects: [],
          selectedIssueTypes: [],
          projectsData: [],
          issueTypesData: []
        })
        setConfigStep(1)
        setConnectionStatus("idle")
        setProjects([])
        setIssueTypes([])
        setShowDisconnectDialog(false)
        toast({
          title: "Başarılı",
          description: "Jira entegrasyonu başarıyla kesildi.",
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Jira entegrasyonu kesilemedi.",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Jira entegrasyonu kesilirken bir hata oluştu.",
        variant: "destructive"
      })
    } finally {
      setIsDisconnecting(false)
    }
  }

  const fetchProjects = useCallback(async () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('=== DEBUG: fetchProjects START ===')
      console.log('Current projects.length before fetch:', projects.length)
    }
    
    setIsLoadingProjects(true)
    try {
      const response = await getJiraProjects({
        url: configData.url,
        email: configData.email,
        apiToken: configData.apiToken
      })

      if (process.env.NODE_ENV === 'development') {
        console.log('=== API Response (FIXED) ===')
        console.log('response.success:', response.success)
        console.log('response.data:', response.data)
        console.log('response.data is Array:', Array.isArray(response.data))
        console.log('response.data.length:', response.data?.length)
      }

      if (response.success) {
        const projectsArray = response.data || []
        
        if (process.env.NODE_ENV === 'development') {
          console.log('=== Setting Projects (FIXED) ===')
          console.log('projectsArray.length:', projectsArray.length)
          console.log('projectsArray:', projectsArray)
          console.log('response.data directly:', Array.isArray(response.data))
        }
        
        setProjects(projectsArray)

        if (projectsArray.length > 0) {
          if (process.env.NODE_ENV === 'development') {
            console.log('=== fetchProjects returning TRUE ===')
          }
          return true
        } else {
          toast({
            title: "Uyarı",
            description: "Hiç proje bulunamadı.",
            variant: "default"
          })
          if (process.env.NODE_ENV === 'development') {
            console.log('=== fetchProjects returning FALSE (no projects) ===')
          }
          return false
        }
      } else {
        toast({
          title: "Hata",
          description: response.error || "Jira projeleri alınamadı.",
          variant: "destructive"
        })
        if (process.env.NODE_ENV === 'development') {
          console.log('=== fetchProjects returning FALSE (API error) ===')
        }
        return false
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Jira projeleri alınırken bir hata oluştu.",
        variant: "destructive"
      })
      if (process.env.NODE_ENV === 'development') {
        console.log('=== fetchProjects returning FALSE (exception) ===', error)
      }
      return false
    } finally {
      setIsLoadingProjects(false)
      if (process.env.NODE_ENV === 'development') {
        console.log('=== DEBUG: fetchProjects END ===')
      }
    }
  }, [configData.url, configData.email, configData.apiToken, toast])

  const handleNextStep = async () => {
    // Debug: Mevcut durumu logla (sadece development'ta)
    if (process.env.NODE_ENV === 'development') {
      console.log('=== DEBUG: handleNextStep ===')
      console.log('Current configStep:', configStep)
      console.log('connectionStatus:', connectionStatus)
      console.log('configData.selectedProjects:', configData.selectedProjects)
      console.log('configData.selectedIssueTypes:', configData.selectedIssueTypes)
      console.log('configData.projectsData:', configData.projectsData)
      console.log('configData.issueTypesData:', configData.issueTypesData)
      console.log('projects.length:', projects.length)
      console.log('issueTypes.length:', issueTypes.length)
      console.log('===========================')
    }
    
    try {
      if (configStep === 1 && connectionStatus === "success") {
        if (process.env.NODE_ENV === 'development') {
          console.log('=== Calling fetchProjects ===')
        }
        const success = await fetchProjects()
        if (process.env.NODE_ENV === 'development') {
          console.log('=== fetchProjects result:', success, '===')
        }
        if (success) {
          if (process.env.NODE_ENV === 'development') {
            console.log('=== Setting configStep to 2 ===')
          }
          setConfigStep(2)
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log('=== fetchProjects failed, NOT setting configStep ===')
          }
        }
      } else if (configStep === 2) {
        // Seçilen proje sayısını configData.selectedProjects yerine projects içinden kontrol et
        const selectedProjectsCount = configData.selectedProjects.length
        
        if (selectedProjectsCount > 0) {
          if (issueTypes.length > 0) {
            setConfigStep(3)
          } else {
            toast({
              title: "Issue Type Bulunamadı",
              description: "Seçilen projelerde hiç issue type bulunamadı. Lütfen farklı projeler seçin.",
              variant: "default"
            })
          }
        } else {
          toast({
            title: "Proje Seçimi Gerekli",
            description: "Devam etmek için en az bir proje seçmelisiniz.",
            variant: "destructive"
          })
        }
      } else if (configStep === 3) {
        // Issue type seçimi kontrolü
        const selectedIssueTypesCount = configData.selectedIssueTypes.length
        
        if (selectedIssueTypesCount > 0) {
          await saveConfiguration()
        } else {
          toast({
            title: "Issue Type Seçimi Gerekli",
            description: "Devam etmek için en az bir issue type seçmelisiniz.",
            variant: "destructive"
          })
        }
      } else {
        // Fallback hata mesajları
        if (configStep === 1 && connectionStatus !== "success") {
          toast({
            title: "Bağlantı Gerekli",
            description: "Devam etmek için önce bağlantıyı test edin ve başarılı olduğundan emin olun.",
            variant: "destructive"
          })
        }
      }
    } catch (error) {
      toast({
        title: "İşlem Hatası",
        description: "İşlem sırasında beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive"
      })
    }
  }

  const saveConfiguration = async () => {
    try {
      const trimmedUrl = configData.url?.trim() || ''
      const trimmedEmail = configData.email?.trim() || ''
      const trimmedApiToken = configData.apiToken?.trim() || ''

      if (!trimmedUrl || !trimmedEmail || !trimmedApiToken) {
        toast({
          title: "Hata",
          description: "Jira bağlantı bilgileri eksik.",
          variant: "destructive"
        })
        return false
      }

      if (!configData.projectsData || configData.projectsData.length === 0) {
        toast({
          title: "Hata", 
          description: "En az bir proje seçmelisiniz.",
          variant: "destructive"
        })
        return false
      }

      if (!configData.issueTypesData || configData.issueTypesData.length === 0) {
        toast({
          title: "Hata",
          description: "En az bir issue type seçmelisiniz.", 
          variant: "destructive"
        })
        return false
      }

      const response = await saveJiraConfig({
        url: trimmedUrl,
        email: trimmedEmail,
        apiToken: trimmedApiToken,
        projectsData: configData.projectsData,
        issueTypesData: configData.issueTypesData
      })

      if (response.success) {
        toast({
          title: "Başarılı",
          description: response.message || "Jira entegrasyonu başarıyla kaydedildi.",
        })
        setIsActive(true)
        await fetchActiveConfig()
        return true
      } else {
        toast({
          title: "Hata",
          description: response.error || "Jira yapılandırması kaydedilemedi.",
          variant: "destructive"
        })
        return false
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Jira yapılandırması kaydedilirken bir hata oluştu.",
        variant: "destructive"
      })
      return false
    }
  }

  const handlePrevStep = () => {
    if (configStep > 1) {
      setConfigStep(configStep - 1)
      if (configStep === 2) {
        setConnectionStatus("idle")
        setConnectionError("")
      }
    }
  }

  const testConnection = useCallback(async () => {
    setIsTestingConnection(true)
    setConnectionStatus("idle")
    setConnectionError("")

    try {
      const trimmedUrl = configData.url?.trim() || ''
      const trimmedEmail = configData.email?.trim() || ''
      const trimmedApiToken = configData.apiToken?.trim() || ''

      if (!trimmedUrl || !trimmedEmail || !trimmedApiToken) {
        setConnectionStatus("error")
        setConnectionError("Tüm alanlar doldurulmalıdır.")
        toast({
          title: "Eksik Bilgi",
          description: "Lütfen tüm alanları doldurunuz.",
          variant: "destructive"
        })
        return
      }

      const response = await testJiraConnection({
        url: trimmedUrl,
        email: trimmedEmail,
        apiToken: trimmedApiToken
      })

      if (response.success) {
        setConnectionStatus("success")
        toast({
          title: "Bağlantı Başarılı",
          description: "Jira bağlantısı başarıyla kuruldu.",
        })
      } else {
        setConnectionStatus("error")
        
        let errorMessage = response.error || "Bağlantı sırasında bir hata oluştu."
        let toastTitle = "Bağlantı Hatası"
        
        if ((response as any).errorCode) {
          switch ((response as any).errorCode) {
            case 'AUTHENTICATION_FAILED':
              toastTitle = "Kimlik Doğrulama Hatası"
              break
            case 'ACCESS_DENIED':
              toastTitle = "Erişim Reddedildi"
              break
            case 'INSTANCE_NOT_FOUND':
              toastTitle = "Jira Bulunamadı"
              break
            case 'INVALID_URL_FORMAT':
              toastTitle = "Geçersiz URL"
              break
            case 'TIMEOUT':
              toastTitle = "Zaman Aşımı"
              break
            case 'NETWORK_ERROR':
              toastTitle = "Ağ Hatası"
              break
            default:
              toastTitle = "Bağlantı Hatası"
          }
        }
        
        setConnectionError(errorMessage)
        toast({
          title: toastTitle,
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error) {
      setConnectionStatus("error")
      setConnectionError("Sunucuya bağlanırken bir hata oluştu.")
      toast({
        title: "Bağlantı Hatası",
        description: "Sunucu ile iletişim kurulamadı.",
        variant: "destructive"
      })
    } finally {
      setIsTestingConnection(false)
    }
  }, [configData.url, configData.email, configData.apiToken, toast])

  // 🚀 PERFORMANCE: Memoized expensive computations
  const isConnectionFormValid = useMemo(() => {
    return !!(configData.url?.trim() && configData.apiToken?.trim() && configData.email?.trim())
  }, [configData.url, configData.apiToken, configData.email])

  const isStep2NextDisabled = useMemo(() => {
    return configData.selectedProjects.length === 0
  }, [configData.selectedProjects.length])

  const isStep3NextDisabled = useMemo(() => {
    return configData.selectedIssueTypes.length === 0
  }, [configData.selectedIssueTypes.length])

  const projectsDisplayText = useMemo(() => {
    return configData.projectsData.length > 0 
      ? configData.projectsData.map(p => p.name).join(', ')
      : "No projects configured"
  }, [configData.projectsData])

  const issueTypesDisplayText = useMemo(() => {
    return configData.issueTypesData.length > 0 
      ? configData.issueTypesData.map(t => t.name).join(', ')
      : "No issue types configured"
  }, [configData.issueTypesData])

  const stepTitle = useMemo(() => {
    switch (configStep) {
      case 1: return "Connection Details"
      case 2: return "Select Project"
      case 3: return "Select Issue Type"
      default: return "Configure Jira"
    }
  }, [configStep])

  const stepDescription = useMemo(() => {
    switch (configStep) {
      case 1: return "Enter your Jira connection details to integrate with the service."
      case 2: return "Select the project you want to connect with."
      case 3: return "Select the issue type you want to use."
      default: return ""
    }
  }, [configStep])

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex items-center gap-3">
            <Puzzle className="h-6 w-6 text-blue-500" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Jira Integration</h1>
              <p className="text-gray-500 dark:text-gray-400">Connect your Jira account to link test cases to issues</p>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-3xl mx-auto">
            {isActive ? (
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Check className="h-6 w-6 text-emerald-500" />
                    <CardTitle>Jira Integration Active</CardTitle>
                  </div>
                  <CardDescription>Your Jira integration is set up and working correctly.</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Jira URL</h3>
                        <p className="text-sm font-medium">{configData.url || "https://yourdomain.atlassian.net"}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</h3>
                        <p className="text-sm font-medium">{configData.email || "<EMAIL>"}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Projects</h3>
                        <p className="text-sm font-medium">
                          {projectsDisplayText}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Issue Types</h3>
                        <p className="text-sm font-medium">
                          {issueTypesDisplayText}
                        </p>
                      </div>
                    </div>

                    <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-800">
                      <h3 className="text-sm font-medium mb-2">Sync Status</h3>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-gray-500">
                          {lastSyncedTime ? `Last synced ${lastSyncedTime}` : 'Not synced yet'}
                        </span>
                        <span className="text-xs font-medium">
                          {configData.projectsData.length} projects, {configData.issueTypesData.length} issue types
                        </span>
                      </div>
                      <Progress value={100} className="h-2" />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between border-t border-gray-200 dark:border-gray-800 px-6 py-4">
                  <Button variant="outline" onClick={() => setIsActive(false)}>
                    Reconfigure
                  </Button>
                  <Button variant="destructive" onClick={() => setShowDisconnectDialog(true)}>
                    Disconnect
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>{stepTitle}</CardTitle>
                  <CardDescription>
                    {stepDescription}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <div className="mb-6">
                    <Progress value={(configStep / 3) * 100} className="h-2" />
                    <div className="flex justify-between mt-2">
                      <div
                        className={`flex flex-col items-center ${configStep >= 1 ? "text-blue-600" : "text-gray-400"}`}
                      >
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${configStep >= 1 ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-400"}`}
                        >
                          1
                        </div>
                        <span className="text-xs mt-1">Connection</span>
                      </div>
                      <div
                        className={`flex flex-col items-center ${configStep >= 2 ? "text-blue-600" : "text-gray-400"}`}
                      >
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${configStep >= 2 ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-400"}`}
                        >
                          2
                        </div>
                        <span className="text-xs mt-1">Project</span>
                      </div>
                      <div
                        className={`flex flex-col items-center ${configStep >= 3 ? "text-blue-600" : "text-gray-400"}`}
                      >
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${configStep >= 3 ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-400"}`}
                        >
                          3
                        </div>
                        <span className="text-xs mt-1">Issue Type</span>
                      </div>
                    </div>
                  </div>

                  {configStep === 1 && (
                    <div className="space-y-4">
                      <div className="grid gap-2">
                        <Label htmlFor="url">Jira URL</Label>
                        <Input
                          id="url"
                          placeholder="https://yourdomain.atlassian.net"
                          value={configData.url}
                          onChange={handleUrlChange}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="apiToken">API Token</Label>
                        <Input
                          id="apiToken"
                          type="password"
                          placeholder="Enter your API token"
                          value={configData.apiToken}
                          onChange={handleApiTokenChange}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={configData.email}
                          onChange={handleEmailChange}
                        />
                      </div>
                    </div>
                  )}

                  {configStep === 2 && (
                    <div className="space-y-4">
                      <Label>Select Projects</Label>
                      {isLoadingProjects ? (
                        <div className="flex items-center justify-center py-8">
                          <LoaderCircle className="h-6 w-6 animate-spin text-blue-500" />
                          <span className="ml-2 text-sm text-gray-500">Loading projects...</span>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {projects.map((project: any) => {
                            const isSelected = configData.selectedProjects.includes(project.key)
                            return (
                              <div
                                key={project.id}
                                className={cn(
                                  "p-3 border rounded-md cursor-pointer transition-colors",
                                  isSelected
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                                    : "border-gray-200 dark:border-gray-800 hover:border-blue-300 dark:hover:border-blue-700",
                                )}
                                onClick={() => handleProjectToggle(project)}
                              >
                                <div className="flex items-center justify-between">
                                  <div>
                                    <span className="font-medium">{project.name}</span>
                                    <span className="text-sm text-gray-500 ml-2">({project.key})</span>
                                  </div>
                                  {isSelected && <Check className="h-5 w-5 text-blue-500" />}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )}

                  {configStep === 3 && (
                    <div className="space-y-4">
                      <Label>Select Issue Types</Label>
                      {isLoadingIssueTypes ? (
                        <div className="flex items-center justify-center py-8">
                          <LoaderCircle className="h-6 w-6 animate-spin text-blue-500" />
                          <span className="ml-2 text-sm text-gray-500">Loading issue types...</span>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {issueTypes.map((type: any) => {
                            const isSelected = configData.selectedIssueTypes.includes(type.id)
                            return (
                              <div
                                key={type.id}
                                className={cn(
                                  "p-3 border rounded-md cursor-pointer transition-colors",
                                  isSelected
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                                    : "border-gray-200 dark:border-gray-800 hover:border-blue-300 dark:hover:border-blue-700",
                                )}
                                onClick={() => handleIssueTypeToggle(type)}
                              >
                                <div className="flex items-center justify-between">
                                  <span className="font-medium">{type.name}</span>
                                  {isSelected && <Check className="h-5 w-5 text-blue-500" />}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>

                <CardFooter className="flex justify-between border-t border-gray-200 dark:border-gray-800 px-6 py-4">
                  {configStep > 1 ? (
                    <Button variant="outline" onClick={handlePrevStep}>
                      Back
                    </Button>
                  ) : (
                    <Button variant="outline" onClick={() => router.push("/")}>
                      Cancel
                    </Button>
                  )}
                  
                  {configStep === 1 && (
                    <>
                      {connectionStatus !== "success" ? (
                        <Button
                          onClick={testConnection}
                          disabled={!isConnectionFormValid || isTestingConnection}
                          className="gap-1"
                        >
                          {isTestingConnection ? (
                            <>
                              <LoaderCircle className="h-4 w-4 animate-spin" />
                              Testing...
                            </>
                          ) : (
                            "Test Connection"
                          )}
                        </Button>
                      ) : (
                        <Button
                          onClick={handleNextStep}
                          className="gap-1"
                        >
                          <CheckCircle2 className="h-4 w-4" />
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      )}
                    </>
                  )}
                  
                  {configStep > 1 && (
                    <Button
                      onClick={handleNextStep}
                      disabled={
                        (configStep === 2 && isStep2NextDisabled) ||
                        (configStep === 3 && isStep3NextDisabled)
                      }
                      className="gap-1"
                    >
                      {configStep < 3 ? (
                        <>
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </>
                      ) : (
                        "Save & Activate"
                      )}
                    </Button>
                  )}
                </CardFooter>
              </Card>
            )}
          </div>
        </main>
      </div>

      {/* Disconnect Dialog */}
      <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Disconnect Jira Integration</DialogTitle>
            <DialogDescription>
              Are you sure you want to disconnect your Jira integration? This will remove all saved configuration and you'll need to set it up again.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDisconnectDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDisconnect} disabled={isDisconnecting}>
              {isDisconnecting ? (
                <>
                  <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                  Disconnecting...
                </>
              ) : (
                "Disconnect"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default function JiraPluginPage() {
  return (
    <CompanyOwnerRoute>
      <JiraPlugin />
    </CompanyOwnerRoute>
  )
}

