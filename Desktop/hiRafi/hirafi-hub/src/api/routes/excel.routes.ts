import { Router } from 'express';
import multer from 'multer';
import { inspect, parse } from '../controllers/excel.controller.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = Router();
const upload = multer({ storage: multer.memoryStorage() });

// @route   POST /api/excel/inspect
// @desc    Inspect an excel file and return its sheet names
// @access  Private
router.post('/inspect', authenticate, upload.single('file'), inspect);

// @route   POST /api/excel/parse
// @desc    Parse a specific sheet from an excel file
// @access  Private
router.post('/parse', authenticate, upload.single('file'), parse);

export default router; 