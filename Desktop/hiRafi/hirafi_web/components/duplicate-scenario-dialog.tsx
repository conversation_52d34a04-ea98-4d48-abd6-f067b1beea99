"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/lib/utils/toast-utils"
import { Copy, Loader2, FolderIcon } from "lucide-react"
import { useScenarioManager } from "@/hooks/useScenarioManager"
import * as scenarioApi from "@/lib/api/scenario-api"

interface DuplicateScenarioDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  scenarioId: string | null
  scenarioName?: string
  onSuccess?: (newScenarioId: string) => void
}

export function DuplicateScenarioDialog({
  open,
  onOpenChange,
  scenarioId,
  scenarioName = "",
  onSuccess
}: DuplicateScenarioDialogProps) {
  const [isDuplicating, setIsDuplicating] = useState(false)
  const [namePrefix, setNamePrefix] = useState("")
  const [selectedFolder, setSelectedFolder] = useState<string>("keep-original")
  const [customName, setCustomName] = useState("")
  const [useCustomName, setUseCustomName] = useState(false)

  // Fetch folders
  const {
    folders,
    foldersLoading,
    refreshAllData
  } = useScenarioManager({
    folderLimit: 50,
    autoFetch: true
  })

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setNamePrefix("")
      setSelectedFolder("keep-original")
      setCustomName("")
      setUseCustomName(false)
    }
  }, [open])

  // Generate preview name
  const getPreviewName = () => {
    if (useCustomName && customName.trim()) {
      return customName.trim()
    }
    
    if (namePrefix.trim()) {
      return `${namePrefix.trim()} ${scenarioName}`
    }
    
    return `${scenarioName} (Copy)`
  }

  // Handle duplication
  const handleDuplicate = async () => {
    if (!scenarioId) return

    setIsDuplicating(true)

    try {
      const options: any = {}

      // Set name options
      if (useCustomName && customName.trim()) {
        options.newName = customName.trim()
      } else if (namePrefix.trim()) {
        options.namePrefix = namePrefix.trim()
      }

      // Set folder options
      if (selectedFolder !== "keep-original") {
        options.folderId = selectedFolder === "uncategorized" ? null : selectedFolder
      }

      // Call API
      const response = await scenarioApi.duplicateScenario(scenarioId, options)

      if (!response.success) {
        toast.error("Senaryo Kopyalama Hatası", {
          description: response.error || "Senaryo kopyalanırken bir hata oluştu"
        })
        return
      }

      // Trigger data refresh
      refreshAllData()

      toast.success("Senaryo Kopyalandı", {
        description: `"${getPreviewName()}" başarıyla oluşturuldu`
      })

      // Call success callback
      if (onSuccess && response.newScenarioId) {
        onSuccess(response.newScenarioId)
      }

      // Close dialog
      onOpenChange(false)
    } catch (error) {
      console.error("Duplication error:", error)
      toast.error("Senaryo Kopyalama Hatası", {
        description: "Senaryo kopyalanırken bir hata oluştu"
      })
    } finally {
      setIsDuplicating(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] border-0 shadow-lg bg-white dark:bg-gray-900 rounded-xl">
        <DialogHeader className="pb-4 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
              <Copy className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Duplicate Scenario
              </DialogTitle>
              <DialogDescription className="text-gray-500 dark:text-gray-400">
                Create a copy of "{scenarioName}" with custom settings.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-5 py-5">
          {/* Naming Options */}
          <div className="space-y-3">
            <Label className="text-gray-700 dark:text-gray-300 font-medium">Naming Options</Label>
            
            {/* Custom Name Toggle */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="useCustomName"
                checked={useCustomName}
                onChange={(e) => setUseCustomName(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <Label htmlFor="useCustomName" className="text-sm text-gray-600 dark:text-gray-400">
                Use custom name instead of prefix
              </Label>
            </div>

            {useCustomName ? (
              <div className="space-y-2">
                <Label htmlFor="customName" className="text-sm font-medium">Custom Name</Label>
                <Input
                  id="customName"
                  value={customName}
                  onChange={(e) => setCustomName(e.target.value)}
                  placeholder="Enter custom scenario name"
                  className="border-gray-200 dark:border-gray-700 rounded-md"
                />
              </div>
            ) : (
              <div className="space-y-2">
                <Label htmlFor="namePrefix" className="text-sm font-medium">Name Prefix (Optional)</Label>
                <Input
                  id="namePrefix"
                  value={namePrefix}
                  onChange={(e) => setNamePrefix(e.target.value)}
                  placeholder="e.g., 'Test', 'Dev', 'V2'"
                  className="border-gray-200 dark:border-gray-700 rounded-md"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Leave empty to use default "(Copy)" suffix
                </p>
              </div>
            )}
          </div>

          {/* Folder Selection */}
          <div className="space-y-3">
            <Label className="text-gray-700 dark:text-gray-300 font-medium">Destination Folder</Label>
            <Select
              value={selectedFolder}
              onValueChange={setSelectedFolder}
              disabled={foldersLoading}
            >
              <SelectTrigger className="border-gray-200 dark:border-gray-700 rounded-md">
                <SelectValue placeholder="Select destination folder" />
              </SelectTrigger>
              <SelectContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700">
                <SelectItem value="keep-original">
                  <div className="flex items-center gap-2">
                    <FolderIcon className="h-4 w-4 text-gray-500" />
                    Keep Original Folder
                  </div>
                </SelectItem>
                <SelectItem value="uncategorized">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-gray-500"></div>
                    Uncategorized
                  </div>
                </SelectItem>
                {folders.map(folder => (
                  <SelectItem key={folder.id} value={folder.id}>
                    <div className="flex items-center gap-2">
                      <div 
                        className="h-2 w-2 rounded-full" 
                        style={{ backgroundColor: folder.color ? `var(--${folder.color}-500)` : '#6b7280' }}
                      ></div>
                      {folder.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Preview */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800/30">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30">
                  <Copy className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Preview
                </span>
              </div>
              <div className="pl-8">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">New scenario name:</span> "{getPreviewName()}"
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Destination:</span> {
                    selectedFolder === "keep-original" ? "Original folder" :
                    selectedFolder === "uncategorized" ? "Uncategorized" :
                    folders.find(f => f.id === selectedFolder)?.name || "Selected folder"
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="border-t border-gray-100 dark:border-gray-800 pt-4 mt-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            Cancel
          </Button>
          <Button
            onClick={handleDuplicate}
            disabled={isDuplicating || !scenarioId || (useCustomName && !customName.trim())}
            className="gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 border-none"
          >
            {isDuplicating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Duplicating...
              </>
            ) : (
              <>
                <Copy className="h-4 w-4" />
                Duplicate Scenario
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
