/**
 * Application Dependency Injection Container
 * 
 * Central DI container for the entire test node application.
 * Manages all services and their dependencies with a flexible, recursive resolution strategy.
 * Service registration order is no longer important.
 */

import { EventEmitter } from 'events';
import {
  DependencyContainer,
  ServiceLifecycle,
  DependencyValidationResult
} from './dependency-container.js';
import { ILoggerService, LoggerServiceFactory } from '../../../utils/logger-service.js';
import { IConfigurationService, ConfigurationServiceFactory } from '../../../config/services/configuration-service.js';
import { StorageFactory, StorageFactoryInstance, StorageConfig } from '../storage/storage-factory.js';
import { IArtifactStorage } from '../storage/artifact-storage.interface.js';
import { NodeConfig } from '../../../models/types.js';
import { ServiceNames } from '../../node-manager/types/node-manager-types.js';
import { NodeIdentityService } from '../../node-manager/services/identity/node-identity-service.js';
import { INodeIdentityService } from '../../node-manager/interfaces/node-identity-service.interface.js';
import { AtomicStateManager } from '../../node-manager/services/state/atomic-state-manager.js';
import { NodeStateService } from '../../node-manager/services/state/node-state-service.js';
import { ConnectionService } from '../../node-manager/services/connection/connection-service.js';
import { PeriodicTaskService } from '../../node-manager/services/tasks/periodic-task-service.js';
import { EventService } from '../../node-manager/services/events/event-service.js';
import { NodeManager } from '../../node-manager/core/node-manager.js';

// Test Execution Service Imports
import { ExecutionCoordinator } from '../../test-execution/core/execution-coordinator.js';
import { StateManager } from '../../test-execution/core/state-manager.js';
import { ResourceManager } from '../../test-execution/services/resource-management/resource-cleanup-service.js';
import { ConfigurationManager } from '../../test-execution/services/configuration/ai-configuration-service.js';
import { CleanupOrchestrator } from '../../test-execution/services/cleanup/cleanup-orchestrator.js';
import { TestCommunicationService } from '../../test-execution/services/communication/test-communication-service.js';
import { TestQueueWorker } from '../../test-execution/services/queue/test-queue-worker.js';
import { createNodeStepProgressQueueService } from '../../test-execution/services/progress/step-progress-queue-service.js';

import { IPeriodicTaskService } from '../../node-manager/interfaces/periodic-task-service.interface.js';
import { ITestQueueWorker } from '../../test-execution/interfaces/test-queue-worker.interface.js';
import { IConnectionService } from '../../node-manager/interfaces/connection-service.interface.js';
import { IExecutionCoordinator } from '../../test-execution/interfaces/execution-coordinator.interface.js';
import { IStateManager } from '../../test-execution/interfaces/state-manager.interface.js';
import { INodeManager } from '../../node-manager/interfaces/node-manager.interface.js';
import { RedisConnector } from '../../connectors/redis/redis-connector.js';

// Static imports for DI compliance - replacing dynamic imports
import { DeviceManagerFactory } from '../../android/device/device-manager-factory.js';
import { DeviceProviderConfigService } from '../../android/device/services/device-provider-config.service.js';
import { DeviceProviderRegistryService } from '../../android/device/services/device-provider-registry.service.js';
import { DeviceManagerUtils } from '../../android/device/shared/device-manager-utils.js';
import { createPopulatedStepHandlerRegistry } from '../step-handlers/index.js';
import { UnifiedMetricsCollector } from '../providers/base-metrics-collector.js';
import { UnifiedScreenshotProvider } from '../providers/base-screenshot-provider.js';
import { UnifiedVideoRecorder } from '../providers/base-video-recorder.js';
import { BrowserManager } from '../../web/browser/browser-manager.js';

/**
 * Test Execution Container Interface
 * 
 * CIRCULAR DEPENDENCY FIX: This interface breaks the circular dependency
 * by providing only the methods needed by services, rather than the full container.
 */
export interface ITestExecutionContainer {
  /**
   * Resolve a service from the container
   */
  resolve<T>(serviceName: string, scopeId?: string): Promise<T>;

  /**
   * Dispose of a DI scope and all its services
   */
  disposeScope(scopeId: string): Promise<void>;

  /**
   * Create a new DI scope for test execution
   */
  createScope(scopeId: string): DependencyContainer;

  /**
   * Get an existing DI scope
   */
  getScope(scopeId: string): DependencyContainer | null;
}

/**
 * Application container configuration
 */
export interface ApplicationContainerConfig {
  enableLogging?: boolean;
  loggerServiceName?: string;
  storageConfig?: StorageConfig;
  nodeConfig?: NodeConfig;
}

/**
 * Application DI Container
 *
 * Provides centralized dependency injection for the entire application.
 * Manages core services like logging, configuration, and storage.
 */
export class ApplicationContainer implements ITestExecutionContainer {
  private container: DependencyContainer;
  private initialized: boolean = false;
  private readonly config: ApplicationContainerConfig;
  private logger!: ILoggerService;
  private scopes: Map<string, DependencyContainer>;

  constructor(config: ApplicationContainerConfig = {}) {
    this.config = {
      enableLogging: process.env.DI_VERBOSE_LOGGING === 'true' || config.enableLogging === true, // Enable verbose DI logging only when explicitly requested
      loggerServiceName: 'ApplicationContainer',
      ...config
    };

    this.container = new DependencyContainer({
      enableLogging: this.config.enableLogging,
      enableCircularDependencyDetection: true,
      maxResolutionDepth: 10
    });

    this.scopes = new Map<string, DependencyContainer>();
  }

  /**
   * Initialize the application container with all services
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      throw new Error('ApplicationContainer is already initialized');
    }

    try {
      this.registerServices();
      
      // PHASE 4 FIX: Ensure async TestRunner services registration completes
      await this.container.resolve('_testRunnerServicesRegistration');
      
      this.initialized = true;

      this.logger = await this.getLoggerService();
      const stats = this.container.getStatistics();
      this.logger.serviceInit('ApplicationContainer', `Initialized successfully with ${stats.serviceCount} services (${stats.singletonCount} singletons, ${stats.transientCount} transient, ${stats.scopedCount} scoped)`);
    } catch (error: any) {
      // Get a logger without relying on the container, as it might have failed
      const emergencyLogger = LoggerServiceFactory.createServiceLogger('AppContainer-Emergency');
      emergencyLogger.error(`Failed to initialize ApplicationContainer: ${error.message}`, { error });
      throw new Error(`Failed to initialize ApplicationContainer: ${error.message}`);
    }
  }

  private registerServices(): void {
    // =================================================================
    // Core Services
    // =================================================================
    this.container.register(
      'loggerService',
      () => LoggerServiceFactory.createServiceLogger(this.config.loggerServiceName || 'App'),
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      'configurationService',
      () => {
        const configService = ConfigurationServiceFactory.createDefaultService();
        if (this.config.nodeConfig) {
          configService.initialize(this.config.nodeConfig);
        }
        return configService;
      },
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      'storageFactory',
      () => StorageFactory.createFactory({
        enableLogging: this.config.enableLogging,
        defaultProvider: 's3',
        fallbackToNoOp: true
      }),
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      'storageProvider',
      () => StorageFactory.createStorageProvider(this.config.storageConfig || {}),
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      'nodeEvents',
      () => new EventEmitter(),
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      'redisConnector',
      () => new RedisConnector(),
      ServiceLifecycle.SINGLETON
    );

    // =================================================================
    // CIRCULAR DEPENDENCY FIX: Register container interface
    // =================================================================
    this.container.register(
      'executionContainer',
      () => ({
        resolve: this.resolve.bind(this),
        disposeScope: this.disposeScope.bind(this),
        createScope: this.createScope.bind(this),
        getScope: this.getScope.bind(this)
      } as ITestExecutionContainer),
      ServiceLifecycle.SINGLETON
    );

    // =================================================================
    // Node Manager Services
    // =================================================================
    this.container.register(
      ServiceNames.NODE_IDENTITY_SERVICE,
      () => new NodeIdentityService(),
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      ServiceNames.ATOMIC_STATE_MANAGER,
      () => new AtomicStateManager(),
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      ServiceNames.PERIODIC_TASK_SERVICE,
      () => new PeriodicTaskService(),
      ServiceLifecycle.SINGLETON
    );

    this.container.register(
      ServiceNames.NODE_STATE_SERVICE,
      (stateManager: AtomicStateManager) => new NodeStateService(stateManager),
      ServiceLifecycle.SINGLETON,
      [ServiceNames.ATOMIC_STATE_MANAGER]
    );

    this.container.register(
      ServiceNames.CONNECTION_SERVICE,
      (identityService: INodeIdentityService, events: EventEmitter) =>
        new ConnectionService(identityService, events),
      ServiceLifecycle.SINGLETON,
      [ServiceNames.NODE_IDENTITY_SERVICE, 'nodeEvents']
    );

    // Event Service - handles WebSocket coordination only (no dependencies needed)
    this.container.register(
      ServiceNames.EVENT_SERVICE,
      (nodeEvents: EventEmitter) => new EventService(nodeEvents),
      ServiceLifecycle.SINGLETON,
      ['nodeEvents']
    );

    // =================================================================
    // Test Execution Services (moved before Node Manager to fix dependency order)
    // =================================================================
    this.container.register(
      ServiceNames.STATE_MANAGER,
      () => new StateManager(),
      ServiceLifecycle.SCOPED
    );

    this.container.register(
      ServiceNames.RESOURCE_MANAGER,
      () => new ResourceManager(),
      ServiceLifecycle.SCOPED
    );

    this.container.register(
      ServiceNames.CONFIGURATION_MANAGER,
      () => new ConfigurationManager(),
      ServiceLifecycle.SCOPED
    );

    this.container.register(
      ServiceNames.CLEANUP_ORCHESTRATOR,
      (resourceManager: ResourceManager) => new CleanupOrchestrator(resourceManager),
      ServiceLifecycle.SCOPED,
      [ServiceNames.RESOURCE_MANAGER]
    );

    this.container.register(
      ServiceNames.TEST_COMMUNICATION_SERVICE,
      () => new TestCommunicationService(),
      ServiceLifecycle.SCOPED
    );

    // CIRCULAR DEPENDENCY FIX: Use interface instead of concrete ApplicationContainer
    this.container.register(
      ServiceNames.EXECUTION_COORDINATOR,
      (
        nodeIdentity: INodeIdentityService,
        stateManager: IStateManager,
        connectionService: IConnectionService,
        nodeEvents: EventEmitter,
        executionContainer: ITestExecutionContainer
      ) => {
        const coordinator = new ExecutionCoordinator(nodeIdentity, stateManager, executionContainer, connectionService);
        // Automatically set the event handler for fresh ExecutionCoordinator instances
        coordinator.setEventHandler(nodeEvents);
        return coordinator;
      },
      ServiceLifecycle.SCOPED,  // SCOPED for test isolation while preserving event listeners within test scope
      [
        ServiceNames.NODE_IDENTITY_SERVICE,
        ServiceNames.STATE_MANAGER,
        ServiceNames.CONNECTION_SERVICE,
        'nodeEvents',
        'executionContainer'
      ]
    );

    // CIRCULAR DEPENDENCY FIX: Use interface instead of concrete ApplicationContainer
    this.container.register(
      ServiceNames.TEST_QUEUE_WORKER,
      async (
        identityService: INodeIdentityService,
        executionContainer: ITestExecutionContainer
      ) => {
        return new TestQueueWorker(identityService, executionContainer);
      },
      ServiceLifecycle.SINGLETON,
      [
        ServiceNames.NODE_IDENTITY_SERVICE,
        'executionContainer'
      ]
    );

    this.container.register(
      'stepProgressQueueService',
      () => createNodeStepProgressQueueService(),
      ServiceLifecycle.SINGLETON
    );

    // =================================================================
    // Test Runner Services (for TestRunnerFactory DI integration)
    // PHASE 4 FIX: Register async to handle dynamic imports
    // =================================================================
    this.container.register(
      '_testRunnerServicesRegistration',
      async () => {
        await this.registerTestRunnerServices();
        return { status: 'registered' };
      },
      ServiceLifecycle.SINGLETON
    );

    // =================================================================
    // Node Manager (main orchestrator)
    // PHASE 5 FIX: Use composition pattern to reduce over-dependencies
    // =================================================================
    this.container.register(
      ServiceNames.NODE_MANAGER,
      (
        identityService: INodeIdentityService,
        stateService: NodeStateService,
        connectionService: ConnectionService,
        periodicTaskService: IPeriodicTaskService,
        eventService: EventService,
        testQueueWorker: ITestQueueWorker,
        nodeEvents: EventEmitter
      ) => NodeManager.createFromServices(
        identityService,
        stateService,
        connectionService,
        periodicTaskService,
        eventService,
        testQueueWorker,
        nodeEvents
      ),
      ServiceLifecycle.SINGLETON,
      [
        ServiceNames.NODE_IDENTITY_SERVICE,
        ServiceNames.NODE_STATE_SERVICE,
        ServiceNames.CONNECTION_SERVICE,
        ServiceNames.PERIODIC_TASK_SERVICE,
        ServiceNames.EVENT_SERVICE,
        ServiceNames.TEST_QUEUE_WORKER,
        'nodeEvents'
      ]
    );
  }

  /**
   * Register TestRunner services in the main DI container
   * This eliminates the need for TestRunnerServiceRegistry's isolated container
   * 
   * PHASE 4 FIX: Updated to use consolidated factory utilities to eliminate code duplication
   */
  private async registerTestRunnerServices(): Promise<void> {
    // Import consolidated utilities using dynamic imports
    const { TestRunnerFactoryUtils } = await import('../utils/testrunner-factory-utils.js');
    const { UnifiedMetricsCollector } = await import('../providers/base-metrics-collector.js');
    const { UnifiedScreenshotProvider } = await import('../providers/base-screenshot-provider.js');
    const { UnifiedVideoRecorder } = await import('../providers/base-video-recorder.js');
    const { BrowserManager } = await import('../../web/browser/browser-manager.js');
    const { createPopulatedStepHandlerRegistry } = await import('../step-handlers/index.js');

    // Register TestRunner service names as constants
    const TestRunnerServiceNames = {
      SCREENSHOT_PROVIDER: 'testRunner.screenshotProvider',
      VIDEO_RECORDER: 'testRunner.videoRecorder',
      METRICS_COLLECTOR: 'testRunner.metricsCollector',
      STEP_HANDLER_REGISTRY: 'testRunner.stepHandlerRegistry',
      PLATFORM_MANAGER: 'testRunner.platformManager',
      DEVICE_MANAGER_FACTORY: 'testRunner.deviceManagerFactory'
    };

    // Register device manager services with proper DI
    this.container.register(
      'deviceProviderConfigService',
      (logger: ILoggerService) => new DeviceProviderConfigService(logger),
      ServiceLifecycle.SINGLETON,
      ['loggerService']
    );

    this.container.register(
      'deviceManagerUtils',
      (logger: ILoggerService) => new DeviceManagerUtils(logger),
      ServiceLifecycle.SINGLETON,
      ['loggerService']
    );

    this.container.register(
      'deviceProviderRegistry',
      (logger: ILoggerService, configService: DeviceProviderConfigService) =>
        new DeviceProviderRegistryService(logger, configService),
      ServiceLifecycle.SINGLETON,
      ['loggerService', 'deviceProviderConfigService']
    );

    // Register device manager factory with all dependencies
    this.container.register(
      TestRunnerServiceNames.DEVICE_MANAGER_FACTORY,
      async (
        logger: ILoggerService,
        configService: DeviceProviderConfigService,
        registry: DeviceProviderRegistryService,
        utils: DeviceManagerUtils
      ) => {
        return new DeviceManagerFactory(logger, configService, registry, utils);
      },
      ServiceLifecycle.SINGLETON,
      ['loggerService', 'deviceProviderConfigService', 'deviceProviderRegistry', 'deviceManagerUtils']
    );

    // PHASE 4 FIX: Use consolidated step handler registry factory
    this.container.register(
      TestRunnerServiceNames.STEP_HANDLER_REGISTRY,
      () => TestRunnerFactoryUtils.createStepHandlerRegistryFactory(createPopulatedStepHandlerRegistry),
      ServiceLifecycle.SCOPED
    );

    // PHASE 4 FIX: Use consolidated metrics collector factory
    this.container.register(
      TestRunnerServiceNames.METRICS_COLLECTOR,
      () => TestRunnerFactoryUtils.createMetricsCollectorFactory(
        async (platform: 'mobile' | 'web', config: any) => new UnifiedMetricsCollector(platform, config)
      ),
      ServiceLifecycle.SCOPED
    );

    // PHASE 4 FIX: Use consolidated screenshot provider factory
    this.container.register(
      TestRunnerServiceNames.SCREENSHOT_PROVIDER,
      (artifactStorage: IArtifactStorage) => TestRunnerFactoryUtils.createScreenshotProviderFactory(
        async (artifactStorage: any, platform: 'mobile' | 'web', config: any) => 
          new UnifiedScreenshotProvider(artifactStorage, platform, config)
      ),
      ServiceLifecycle.SCOPED,
      ['storageProvider']
    );

    // PHASE 4 FIX: Use consolidated video recorder factory
    this.container.register(
      TestRunnerServiceNames.VIDEO_RECORDER,
      (artifactStorage: IArtifactStorage) => TestRunnerFactoryUtils.createVideoRecorderFactory(
        async (artifactStorage: any, platform: 'mobile' | 'web', executionCoordinator: any, config: any) =>
          new UnifiedVideoRecorder(artifactStorage, platform, config, executionCoordinator)
      ),
      ServiceLifecycle.SCOPED,
      ['storageProvider']
    );

    // PHASE 4 FIX: Use consolidated platform manager factory
    this.container.register(
      TestRunnerServiceNames.PLATFORM_MANAGER,
      async (deviceManagerFactory: any, logger: ILoggerService) => {
        return TestRunnerFactoryUtils.createPlatformManagerFactory(
          async () => new BrowserManager(logger),
          async (config: any, deviceProvider: string) => {
            return deviceManagerFactory.createDeviceManager(config, deviceProvider as 'sauceLabs' | 'testinium');
          }
        );
      },
      ServiceLifecycle.SCOPED,
      [TestRunnerServiceNames.DEVICE_MANAGER_FACTORY, 'loggerService']
    );
  }

  /**
   * Get the logger service
   */
  async getLoggerService(): Promise<ILoggerService> {
    this.ensureInitialized();
    return this.container.resolve<ILoggerService>('loggerService');
  }

  /**
   * Get the configuration service
   */
  async getConfigurationService(): Promise<IConfigurationService> {
    this.ensureInitialized();
    return this.container.resolve<IConfigurationService>('configurationService');
  }

  /**
   * Get the storage factory
   */
  async getStorageFactory(): Promise<StorageFactoryInstance> {
    this.ensureInitialized();
    return this.container.resolve<StorageFactoryInstance>('storageFactory');
  }

  /**
   * Get the storage provider
   */
  async getStorageProvider(): Promise<IArtifactStorage> {
    this.ensureInitialized();
    return this.container.resolve<IArtifactStorage>('storageProvider');
  }

  async getRedisConnector(): Promise<RedisConnector> {
    this.ensureInitialized();
    return this.container.resolve<RedisConnector>('redisConnector');
  }

  /**
   * Resolve a service from the container
   */
  async resolve<T>(serviceName: string, scopeId?: string): Promise<T> {
    return await this.container.resolve<T>(serviceName, scopeId);
  }

  /**
   * Get TestRunner service factories for creating platform-specific instances
   * All factories now return fresh instances per call (TRANSIENT lifecycle)
   */
  async getTestRunnerServiceFactories(): Promise<{
    screenshotProviderFactory: any;
    videoRecorderFactory: any;
    metricsCollectorFactory: any;
    stepHandlerRegistries: any;
    platformManagerFactory: any;
    deviceManagerFactory: any;
  }> {
    this.ensureInitialized();
    return {
      screenshotProviderFactory: await this.container.resolve('testRunner.screenshotProvider'),
      videoRecorderFactory: await this.container.resolve('testRunner.videoRecorder'),
      metricsCollectorFactory: await this.container.resolve('testRunner.metricsCollector'),
      stepHandlerRegistries: await this.container.resolve('testRunner.stepHandlerRegistry'),
      platformManagerFactory: await this.container.resolve('testRunner.platformManager'),
      deviceManagerFactory: await this.container.resolve('testRunner.deviceManagerFactory')
    };
  }

  /**
   * Register an external service
   */
  registerService<T>(
    name: string,
    factory: (...args: any[]) => T | Promise<T>,
    lifecycle: ServiceLifecycle = ServiceLifecycle.SINGLETON,
    dependencies: string[] = []
  ): void {
    this.container.register(name, factory, lifecycle, dependencies);
  }

  /**
   * Resolve a service by name
   */
  async resolveService<T>(name: string): Promise<T> {
    this.ensureInitialized();
    return this.container.resolve<T>(name);
  }

  /**
   * Check if a service is registered
   */
  hasService(name: string): boolean {
    return this.container.isRegistered(name);
  }

  /**
   * Get container statistics
   */
  getStatistics(): any {
    return {
      registeredServices: this.container.getRegisteredServices(),
      serviceCount: this.container.getRegisteredServices().length,
      initialized: this.initialized
    };
  }

  /**
   * Validate dependency graph and return detailed results
   */
  validateDependencies(): DependencyValidationResult {
    return this.container.validateDependencyGraph();
  }

  /**
   * Get dependency graph for analysis
   */
  getDependencyGraph(): Map<string, string[]> {
    return this.container.getDependencyGraph();
  }

  /**
   * Get service registration order
   */
  getRegistrationOrder(): string[] {
    return this.container.getRegistrationOrder();
  }

  /**
   * Analyze dependency graph and provide insights
   */
  analyzeDependencies(): {
    totalServices: number;
    registrationOrder: string[];
    dependencyGraph: Map<string, string[]>;
    validation: DependencyValidationResult;
    insights: {
      servicesWithNoDependencies: string[];
      servicesWithMostDependencies: { service: string; count: number }[];
      maxDependencyDepth: number;
      potentialIssues: string[];
    };
  } {
    const validation = this.validateDependencies();
    const graph = this.getDependencyGraph();
    const registrationOrder = this.getRegistrationOrder();

    // Analyze services with no dependencies
    const servicesWithNoDependencies = Array.from(graph.entries())
      .filter(([, deps]) => deps.length === 0)
      .map(([service]) => service);

    // Find services with most dependencies
    const servicesWithMostDependencies = Array.from(graph.entries())
      .map(([service, deps]) => ({ service, count: deps.length }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Calculate max dependency depth
    let maxDepth = 0;
    const calculateDepth = (service: string, visited = new Set<string>()): number => {
      if (visited.has(service)) return 0; // Avoid infinite recursion
      visited.add(service);

      const deps = graph.get(service) || [];
      if (deps.length === 0) return 1;

      return 1 + Math.max(...deps.map(dep => calculateDepth(dep, new Set(visited))));
    };

    for (const service of graph.keys()) {
      maxDepth = Math.max(maxDepth, calculateDepth(service));
    }

    // Identify potential issues
    const potentialIssues: string[] = [];
    if (servicesWithMostDependencies[0]?.count > 5) {
      potentialIssues.push(`Service '${servicesWithMostDependencies[0].service}' has ${servicesWithMostDependencies[0].count} dependencies - consider breaking it down`);
    }
    if (maxDepth > 6) {
      potentialIssues.push(`Maximum dependency depth is ${maxDepth} - consider flattening the dependency hierarchy`);
    }
    if (validation.issues.length > 0) {
      potentialIssues.push(`${validation.issues.length} dependency validation issues found`);
    }

    return {
      totalServices: graph.size,
      registrationOrder,
      dependencyGraph: graph,
      validation,
      insights: {
        servicesWithNoDependencies,
        servicesWithMostDependencies,
        maxDependencyDepth: maxDepth,
        potentialIssues
      }
    };
  }

  /**
   * Dispose of the container and all services
   */
  async dispose(): Promise<void> {
    if (this.initialized) {
      try {
        const logger = await this.getLoggerService();
        logger.serviceInit('ApplicationContainer', 'Disposing container and services');
      } catch (error) {
        // Logger might not be available during disposal
      }

      await this.container.dispose();
      this.initialized = false;
    }
  }

  /**
   * Reset the container (for testing purposes)
   */
  async reset(): Promise<void> {
    await this.dispose();
    this.container = new DependencyContainer({
      enableLogging: this.config.enableLogging,
      enableCircularDependencyDetection: true,
      maxResolutionDepth: 10
    });
  }

  /**
   * Check if the container is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Ensure the container is initialized
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error('ApplicationContainer must be initialized before use');
    }
  }

  /**
   * Create a new DI scope for a test execution.
   *
   * @param scopeId - A unique identifier for the scope (e.g., test run ID).
   * @returns A new `DependencyContainer` instance representing the scope.
   */
  createScope(scopeId: string): DependencyContainer {
    if (this.scopes.has(scopeId)) {
      this.logger.warn(`Scope ${scopeId} already exists. Returning existing scope.`);
      return this.scopes.get(scopeId)!;
    }

    const childContainer = this.container.createChild();
    
    // Store reference to the original resolve method before overriding
    const originalResolve = childContainer.resolve.bind(childContainer);
    
    // Override the resolve method to automatically pass the scope ID and provide enhanced error logging
    childContainer.resolve = async function<T>(name: string, scope?: string): Promise<T> {
      // Use the provided scope, or default to the container's scope ID
      const effectiveScope = scope || scopeId;
      
      try {
        const result = await originalResolve<T>(name, effectiveScope);
        return result;
      } catch (error: any) {
        // Enhanced error logging for scoped resolution failures
        const errorMessage = `Failed to resolve service '${name}' in scope '${effectiveScope}': ${error.message}`;
        throw new Error(errorMessage);
      }
    };

    this.scopes.set(scopeId, childContainer);
    this.logger.debug(`Created new scope: ${scopeId} with automatic scope resolution`);
    return childContainer;
  }

  /**
   * Dispose of a DI scope.
   *
   * @param scopeId - The identifier of the scope to dispose.
   */
  async disposeScope(scopeId: string): Promise<void> {
    const scope = this.scopes.get(scopeId);
    if (scope) {
      await this.container.disposeScope(scopeId);
      this.scopes.delete(scopeId);
      this.logger.debug(`Disposed scope: ${scopeId}`);
    } else {
      // Silently ignore if scope doesn't exist (idempotent behavior)
      // This can happen if the scope was already disposed by another caller
      this.logger.debug(`Scope ${scopeId} already disposed or never existed - skipping disposal`);
    }
  }

  // PHASE 3: Enhanced disposal tracking methods delegation
  
  /**
   * Get active scopes from the underlying container
   */
  getActiveScopes(): string[] {
    return this.container.getActiveScopes();
  }

  /**
   * Get disposal tracking statistics for a scope
   */
  getScopeCleanupStats(scopeId: string): any {
    return this.container.getScopeCleanupStats(scopeId);
  }

  /**
   * Enhanced dispose scope with comprehensive tracking
   */
  async disposeScopeWithTracking(scopeId: string): Promise<any> {
    const stats = await this.container.disposeScopeWithTracking(scopeId);
    
    // Also clean up our local scope tracking
    if (this.scopes.has(scopeId)) {
      this.scopes.delete(scopeId);
      this.logger.debug(`Cleaned up local scope tracking for: ${scopeId}`);
    }
    
    return stats;
  }

  /**
   * Get comprehensive disposal report
   */
  getDisposalReport(): any {
    return this.container.getDisposalReport();
  }

  /**
   * Get an existing DI scope
   */
  getScope(scopeId: string): DependencyContainer | null {
    return this.scopes.get(scopeId) || null;
  }
}
