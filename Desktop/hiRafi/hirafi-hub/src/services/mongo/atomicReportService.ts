/**
 * Atomic Report Service
 *
 * This service provides a centralized, atomic way to update reports
 * to prevent race conditions and duplicate updates.
 *
 * It ensures that:
 * 1. All report updates go through a single entry point
 * 2. Updates are performed atomically
 * 3. Duplicate updates are detected and prevented
 * 4. Related updates are performed in a transaction when possible
 */

import { Collection } from 'mongodb';
import { logger } from '../../utils/logger.js';
import { operationQueue } from '../../utils/operationQueue.js';
import { db, ensureMongoDBConnection, runReportsCollection, runsCollection, reportsCollection } from './dbConnection.js';
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';
import { TestReportDTO, validateTestReportDTO } from '../../types/testReportDTO.js';

// Run report events için event emitter
export const runReportEvents = new EventEmitter();
export const runStatusEvents = new EventEmitter();

/**
 * Run reports koleksiyonunu getir
 */
export async function getRunReportsCollection(): Promise<Collection> {
  if (!runReportsCollection) {
    await ensureMongoDBConnection();

    if (!db) {
      throw new Error('MongoDB connection not available');
    }

    if (!runReportsCollection) {
      throw new Error('runReportsCollection not available from dbConnection');
    }
  }

  return runReportsCollection;
}

/**
 * Update a scenario status in a run report atomically
 *
 * @param runId Run ID
 * @param executionId Execution ID
 * @param scenarioId Scenario ID
 * @param status New status
 * @param options Additional options
 * @returns Success status
 */
export async function updateScenarioStatusAtomic(
  runId: string,
  executionId: string,
  scenarioId: string,
  status: 'queued' | 'running' | 'passed' | 'failed' | 'stopped' | 'idle',
  options: {
    testId?: string;
    startTime?: Date;
    completionTime?: Date;
    duration?: number; // Add duration parameter to store actual test duration
    source?: string; // Source of the update (for tracking)
    transactionId?: string; // Unique transaction ID for duplicate detection
    previousStatus?: string; // Previous status for counter updates
    deviceInfo?: { // Add device information support
      id: string;
      name: string;
      osVersion?: string;
      provider?: 'sauceLabs' | 'testinium';
    };
  } = {},
  existingReport?: any // Optional existing report to avoid redundant database lookup
): Promise<{ success: boolean; message?: string; previousStatus?: string }> {
  // Generate a unique transaction ID if not provided
  const transactionId = options.transactionId || `update-scenario-${runId}-${scenarioId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;
  const source = options.source || 'unknown';

  logger.info(`AtomicReportService: Updating scenario ${scenarioId} status to ${status} in run ${runId}`);

  try {
    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `update-scenario-status-${runId}-${scenarioId}-${transactionId}`,
      async () => {
        if (!runReportsCollection) throw new Error('runReportsCollection not available');

        // Use existing report if provided to avoid redundant database lookup
        let report = existingReport;
        
        if (!report) {
          // Find the run report with execution ID fallback
          report = await runReportsCollection.findOne({ runId, executionId });
          
          // If not found with provided execution ID, try to recover
          if (!report) {
            logger.warn(`AtomicReportService: Run report not found for runId ${runId} and executionId ${executionId}, attempting execution ID recovery`);
            
            const executionIdResult = await getRunExecutionId(runId);
            
            if (executionIdResult.success && executionIdResult.executionId) {
              // Try with the correct execution ID
              report = await runReportsCollection.findOne({ runId, executionId: executionIdResult.executionId });
              
              if (report) {
                // Update the execution ID for subsequent operations
                executionId = executionIdResult.executionId;
              }
            }
          }
        }

        if (!report) {
          logger.warn(`AtomicReportService: Run report not found for scenario ${scenarioId} update in run ${runId}`);
          return {
            success: false,
            message: `Run report not found for run ${runId} with executionId ${executionId}`
          };
        }

        // Get current scenario statuses
        const scenarioStatuses = report.scenarioStatuses || [];

        // Find existing scenario status
        const scenarioIndex = scenarioStatuses.findIndex((s: any) => s.scenarioId === scenarioId);
        let previousStatus = 'queued'; // Default previous status

        if (scenarioIndex >= 0) {
          previousStatus = scenarioStatuses[scenarioIndex].status;
          
          // Check if status is actually changing
          if (scenarioStatuses[scenarioIndex].status === status) {
            return {
              success: true,
              message: `Scenario already has status ${status}`,
              previousStatus
            };
          }

          // Update existing scenario status
          scenarioStatuses[scenarioIndex] = {
            ...scenarioStatuses[scenarioIndex],
            status,
            updatedAt: new Date(),
            ...(options.startTime && { startTime: options.startTime }),
            ...(options.completionTime && { completionTime: options.completionTime }),
            ...(options.duration && { duration: options.duration }),
            ...(options.testId && { testId: options.testId }),
            ...(options.deviceInfo && { deviceInfo: options.deviceInfo })
          };
        } else {
          // Add new scenario status
          const newScenarioStatus = {
            scenarioId,
            status,
            updatedAt: new Date(),
            ...(options.startTime && { startTime: options.startTime }),
            ...(options.completionTime && { completionTime: options.completionTime }),
            ...(options.duration && { duration: options.duration }),
            ...(options.testId && { testId: options.testId }),
            ...(options.deviceInfo && { deviceInfo: options.deviceInfo })
          };

          scenarioStatuses.push(newScenarioStatus);
        }

        // Use the report's ID for more reliable updates
        const updateQuery = { id: report.id };

        // Update the run report with new scenario statuses
        const updateResult = await runReportsCollection.findOneAndUpdate(
          updateQuery,
          {
            $set: {
              scenarioStatuses,
              lastUpdated: new Date()
            }
          },
          { returnDocument: 'after' }
        );

        if (!updateResult || !updateResult.value) {
          // Try direct update as fallback
          const directUpdateResult = await runReportsCollection.updateOne(
            updateQuery,
            {
              $set: {
                scenarioStatuses,
                lastUpdated: new Date()
              }
            }
          );
          
          if (directUpdateResult.matchedCount > 0) {
            // After updating scenario status, check if we need to update the overall run status
            const checkResult = await checkAndUpdateRunStatus(runId, executionId);
            
            if (!checkResult.success) {
              logger.warn(`AtomicReportService: checkAndUpdateRunStatus failed after updating scenario ${scenarioId}: ${checkResult.message}`);
            }

            return {
              success: true,
              previousStatus
            };
          }

          logger.error(`AtomicReportService: Failed to update scenario ${scenarioId} status in run ${runId}`);
          return {
            success: false,
            message: `Failed to update scenario status in run report`
          };
        }

        logger.info(`AtomicReportService: Successfully updated scenario ${scenarioId} status to ${status} in run ${runId}`);

        // After updating scenario status, check if we need to update the overall run status
        const checkResult = await checkAndUpdateRunStatus(runId, executionId);
        
        if (!checkResult.success) {
          logger.warn(`AtomicReportService: checkAndUpdateRunStatus failed after updating scenario ${scenarioId}: ${checkResult.message}`);
        }

        // Emit events for status changes
        runReportEvents.emit(`scenario:${scenarioId}:status`, {
          runId,
          executionId,
          scenarioId,
          status,
          previousStatus,
          testId: options.testId
        });

        return {
          success: true,
          previousStatus
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`AtomicReportService: Error updating scenario status: ${error.message}`, {
      error: error.stack,
      runId,
      executionId,
      scenarioId,
      status
    });

    return {
      success: false,
      message: `Error updating scenario status: ${error.message}`
    };
  }
}

/**
 * Create a new run report atomically
 *
 * @param runId Run ID
 * @param executionId Execution ID
 * @param options Additional options
 * @returns Success status with report ID and report object
 */
export async function createRunReportAtomic(
  runId: string,
  executionId: string,
  options: {
    userId?: string;
    teamId?: string;
    companyId?: string;
    source?: string;
    transactionId?: string;
    executedUser?: string;
    executedUserName?: string;
  } = {}
): Promise<{
  success: boolean;
  reportId?: string;
  report?: any;
  message?: string;
}> {
  // Generate a unique transaction ID if not provided
  const transactionId = options.transactionId || `create-${runId}-${executionId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;
  const source = options.source || 'unknown';

  logger.info(`AtomicReportService: Creating run report for run ${runId} with executionId ${executionId} (transaction: ${transactionId}, source: ${source})`);

  try {
    // Use operation queue to ensure atomic creation
    return await operationQueue.enqueue(
      `create-run-report-${runId}-${executionId}-${transactionId}`,
      async () => {
        if (!runReportsCollection) throw new Error('runReportsCollection not available');

        // Check if a report already exists for this run and execution
        const existingReport = await runReportsCollection.findOne({ runId, executionId });

        if (existingReport) {
          logger.info(`AtomicReportService: Run report already exists for run ${runId} with executionId ${executionId}, returning existing report`);
          return {
            success: true,
            reportId: existingReport.id,
            report: existingReport
          };
        }

        // Get scenario IDs from the run
        let scenarioIds: string[] = [];
        let scenarioStatuses: any[] = [];
        let totalScenarios = 0;

        // Get run details from the runs collection
        await ensureMongoDBConnection();
        if (!db || !runsCollection) {
          return {
            success: false,
            message: "MongoDB not initialized"
          };
        }

        // Get run details
        const run = await runsCollection.findOne({ id: runId });

        if (run && run.scenarioIds && Array.isArray(run.scenarioIds)) {
          totalScenarios = run.scenarioIds.length;
          scenarioIds = run.scenarioIds;

          // Initialize all scenarios as queued
          scenarioStatuses = scenarioIds.map(scenarioId => ({
            scenarioId,
            status: 'queued',
            updatedAt: new Date()
          }));


        } else {
          logger.warn(`AtomicReportService: Run ${runId} not found or has no scenarios when creating report`);
        }

        // Generate a unique report ID
        const reportId = uuidv4();

        // Create the run report object
        const newReport = {
          id: reportId,
          runId,
          executionId,
          createdAt: new Date(),
          startedAt: new Date(),
          lastUpdated: new Date(),
          status: 'queued',  // Start with queued status
          progress: 0,       // Initial progress
          scenarioIds: scenarioIds,
          scenarioStatuses: scenarioStatuses,
          // Track processed results to prevent duplicates
          processedResults: {},
          userId: options.userId,
          teamId: options.teamId,
          companyId: options.companyId,
          executedUser: options.executedUser || options.userId, // Use executedUser if provided, otherwise fall back to userId
          executedUserName: options.executedUserName || 'System', // Use executedUserName if provided, otherwise use 'System'
          testrailRunId: null,
          testrailRunLink: null,
          transactionId
        };

        // Insert the new report
        await runReportsCollection.insertOne(newReport);

        logger.info(`AtomicReportService: Created new run report with ID ${reportId} for run ${runId} with executionId ${executionId}`);

        // Emit event for report creation
        runReportEvents.emit(`run:${runId}:report:created`, {
          runId,
          executionId,
          reportId,
          status: newReport.status
        });

        return {
          success: true,
          reportId,
          report: newReport
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`AtomicReportService: Error creating run report: ${error.message}`, {
      error: error.stack,
      runId,
      executionId
    });

    return {
      success: false,
      message: `Error creating run report: ${error.message}`
    };
  }
}

/**
 * Update run report status atomically
 *
 * @param runId Run ID
 * @param executionId Execution ID
 * @param status New status
 * @param options Additional options
 * @returns Success status
 */
export async function updateRunStatusAtomic(
  runId: string,
  executionId: string,
  status: string,
  options: {
    noStartTime?: boolean;
    noCompletionTime?: boolean;
    source?: string;
    transactionId?: string;
  } = {}
): Promise<{ success: boolean; message?: string; report?: any }> {
  // Generate a truly unique transaction ID if not provided to prevent collisions
  const transactionId = options.transactionId || `update-status-${runId}-${executionId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;
  const source = options.source || 'unknown';

  try {
    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `update-run-status-${runId}-${executionId}-${transactionId}`,
      async () => {
        if (!runReportsCollection) throw new Error('runReportsCollection not available');

        // Find the current report
        const report = await runReportsCollection.findOne({ runId, executionId });

        if (!report) {
          logger.warn(`AtomicReportService: Run report not found for run ${runId} with executionId ${executionId}`);

          // Try to create a new report
          try {
            const createResult = await createRunReportAtomic(runId, executionId, {
              source,
              transactionId: `create-from-update-${transactionId}`
            });

            if (!createResult.success) {
              return {
                success: false,
                message: `Failed to create run report: ${createResult.message}`
              };
            }

            // Update the newly created report
            const updateData: any = {
              status,
              lastUpdated: new Date()
            };

            // Add timestamps based on status
            if (status === 'running' && !options.noStartTime) {
              updateData.startedAt = new Date();
            }

            if (['completed', 'failed', 'stopped', 'partial'].includes(status) && !options.noCompletionTime) {
              updateData.completedAt = new Date();
            }

            // Update the report
            const result = await runReportsCollection.findOneAndUpdate(
              { id: createResult.reportId },
              { $set: updateData },
              { returnDocument: 'after' }
            );

            if (!result || !result.value) {
              return {
                success: false,
                message: `Newly created run report not found for update`
              };
            }

            logger.info(`AtomicReportService: Created and updated run ${runId} status to ${status}`);

            // Get the updated report
            const updatedReport = result?.value || {};

            // Import the calculateTestResults function
            const { calculateTestResults } = await import('../../models/test-types.js');

            // Calculate test results from scenarioStatuses
            const statusCounts = calculateTestResults(updatedReport);

            // Emit status change event
            runStatusEvents.emit(`run:${runId}:${executionId}:status`, {
              runId,
              executionId,
              status,
              statusCounts
            });

            // Emit completion event for terminal statuses
            if (['completed', 'failed', 'stopped', 'partial'].includes(status)) {
              const runResult = {
                success: status === 'completed',
                status,
                total: statusCounts.total || 0,
                passed: statusCounts.completed || 0,
                failed: statusCounts.failed || 0
              };

              runStatusEvents.emit(`run:${runId}:${executionId}:completed`, runResult);
            }

            return {
              success: true,
              report: result?.value || {}
            };
          } catch (createError: any) {
            logger.error(`AtomicReportService: Error creating run report during update: ${createError.message}`);
            return {
              success: false,
              message: `Failed to create run report: ${createError.message}`
            };
          }
        }

        // Check if this is a duplicate status update to prevent unnecessary operations
        if (status === report.status) {
          return {
            success: true,
            message: `Run already has status ${status}`,
            report: report
          };
        }

        // Prepare update data
        const updateData: any = {
          status,
          lastUpdated: new Date()
        };

        // Add timestamps based on status
        if (status === 'running' && !options.noStartTime && !report.startedAt) {
          updateData.startedAt = new Date();
        }

        if (['completed', 'failed', 'stopped', 'partial'].includes(status) && !options.noCompletionTime && !report.completedAt) {
          updateData.completedAt = new Date();
        }

        // Update the report with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await runReportsCollection.findOneAndUpdate(
              { runId, executionId },
              { $set: updateData },
              { returnDocument: 'after' }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`AtomicReportService: Run status update failed for runId ${runId} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!result || !result.value) {
          logger.error(`AtomicReportService: Run status update returned null for runId ${runId}, executionId ${executionId}`);
          return {
            success: false,
            message: `Run report not found for update - runId: ${runId}, executionId: ${executionId}`
          };
        }

        logger.info(`AtomicReportService: Updated run ${runId} status to ${status}`);

        // Get the updated report
        const updatedReport = result?.value || {};

        // Import the calculateTestResults function
        const { calculateTestResults } = await import('../../models/test-types.js');

        // Calculate test results from scenarioStatuses
        const statusCounts = calculateTestResults(updatedReport);

        // Emit status change event
        runStatusEvents.emit(`run:${runId}:${executionId}:status`, {
          runId,
          executionId,
          status,
          statusCounts
        });

        // Emit completion event for terminal statuses
        if (['completed', 'failed', 'stopped', 'partial'].includes(status)) {
          const runResult = {
            success: status === 'completed',
            status,
            total: statusCounts.total || 0,
            passed: statusCounts.completed || 0,
            failed: statusCounts.failed || 0
          };

          runStatusEvents.emit(`run:${runId}:${executionId}:completed`, runResult);
        }

        return {
          success: true,
          report: result?.value || {}
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`AtomicReportService: Error updating run status: ${error.message}`, {
      error: error.stack,
      runId,
      executionId,
      status
    });

    return {
      success: false,
      message: `Error updating run status: ${error.message}`
    };
  }
}

/**
 * Get run report by run ID
 *
 * @param runId Run ID
 * @param executionId Optional execution ID
 * @returns Success status with report object
 */
export async function getRunReportByRunId(
  runId: string,
  executionId?: string
): Promise<{
  success: boolean;
  report?: any;
  message?: string;
}> {
  try {
    if (!runReportsCollection) throw new Error('runReportsCollection not available');

    // Build query
    const query: any = { runId };

    if (executionId) {
      query.executionId = executionId;
    }

    // Find the report
    let report = await runReportsCollection.findOne(
      query,
      { sort: { createdAt: -1 } }
    );

    // If we didn't find a report and we were searching with executionId, try fallback search by runId only
    if (!report && executionId) {
      logger.warn(`AtomicReportService: Run report not found with specific executionId ${executionId}, attempting fallback search by runId only for run ${runId}`);
      
      // Fallback: search by runId only (get the most recent)
      const fallbackQuery = { runId };
      report = await runReportsCollection.findOne(
        fallbackQuery,
        { sort: { createdAt: -1 } }
      );

      if (report) {
        logger.warn(`AtomicReportService: Found run report via fallback search - runId: ${runId}, found executionId: ${report.executionId}, requested executionId: ${executionId}`);
        
        // Log detailed mismatch information for debugging
        logger.warn(`AtomicReportService: EXECUTION ID MISMATCH DETECTED:
        - Run ID: ${runId}
        - Requested Execution ID: ${executionId}
        - Found Execution ID: ${report.executionId}
        - Report ID: ${report.id}
        - Report Created At: ${report.createdAt}
        - This indicates an execution ID synchronization issue in the system`);
        
        return {
          success: true,
          report
        };
      }
    }

    if (!report) {
      return {
        success: false,
        message: `Run report not found for run ${runId}${executionId ? ` with executionId ${executionId}` : ''}`
      };
    }

    return {
      success: true,
      report
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting run report: ${error.message}`, {
      error: error.stack,
      runId,
      executionId
    });

    return {
      success: false,
      message: `Error getting run report: ${error.message}`
    };
  }
}

/**
 * Get run status
 *
 * @param runId Run ID
 * @param executionId Optional execution ID
 * @returns Success status with status object
 */
export async function getRunStatus(
  runId: string,
  executionId?: string
): Promise<{
  success: boolean;
  status?: any;
  message?: string;
}> {
  try {
    // Get the run report
    const reportResult = await getRunReportByRunId(runId, executionId);

    if (!reportResult.success) {
      return {
        success: false,
        message: reportResult.message
      };
    }

    const report = reportResult.report;

    // Use our new scenario-based calculation functions (single source of truth)
    const statusCounts = calculateResultsFromScenarios(report.scenarioStatuses || [], report.scenarioIds?.length);

    // Extract status information
    const status = {
      runId: report.runId,
      executionId: report.executionId,
      status: report.status,
      startedAt: report.startedAt,
      completedAt: report.completedAt,
      testResults: statusCounts, // Use scenario-based calculated counts
      scenarioStatuses: report.scenarioStatuses || []
    };

    return {
      success: true,
      status
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting run status: ${error.message}`, {
      error: error.stack,
      runId,
      executionId
    });

    return {
      success: false,
      message: `Error getting run status: ${error.message}`
    };
  }
}

// Helper types for better type safety
interface TestResult {
  status: string;
  testId: string;
  scenarioId: string;
  previousStatus?: string;
}

interface CounterUpdateOptions {
  source?: string;
  transactionId?: string;
  skipDuplicateCheck?: boolean;
}

interface CounterUpdateResult {
  success: boolean;
  message?: string;
  report?: any;
  wasCompleted?: boolean;
}

interface CounterUpdates {
  [key: string]: number;
}

interface SetOperations {
  lastUpdated: Date;
  [key: string]: any;
}

/**
 * Generate a unique transaction ID for updates
 */
function generateCounterTransactionId(runId: string, testId: string, providedId?: string): string {
  return providedId || `update-run-${runId}-${testId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;
}

/**
 * Check if a test result has already been processed to prevent duplicates
 */
async function checkForDuplicateProcessing(
  collection: Collection,
  runId: string,
  executionId: string,
  testId: string
): Promise<any | null> {
  return await collection.findOne({
    runId,
    executionId,
    [`processedResults.${testId}`]: { $exists: true }
  });
}

/**
 * Create set operations for tracking processed results
 */
function createSetOperations(testResult: TestResult, transactionId: string, source: string): SetOperations {
  return {
    lastUpdated: new Date(),
    [`processedResults.${testResult.testId}`]: {
      status: testResult.status,
      processedAt: new Date(),
      transactionId,
      source
    }
  };
}

/**
 * Build the MongoDB update operation
 */
function buildUpdateOperation(setOperations: SetOperations, counterUpdates: CounterUpdates): any {
  const updateOperation: any = {
    $set: setOperations
  };

  if (Object.keys(counterUpdates).length > 0) {
    updateOperation.$inc = counterUpdates;
  }

  return updateOperation;
}

/**
 * Check if a run is completed based on counters
 */
function isRunCompleted(counters: any): boolean {
  const total = counters.total || 0;
  const completed = (counters.passed || 0) + (counters.failed || 0) + (counters.stopped || 0) + (counters.skipped || 0);
  return total > 0 && completed >= total;
}

/**
 * Calculate progress percentage
 */
function calculateProgress(counters: any): number {
  const total = counters.total || 0;
  const completed = (counters.passed || 0) + (counters.failed || 0) + (counters.stopped || 0) + (counters.skipped || 0);
  return total > 0 ? Math.round((completed / total) * 100) : 0;
}

/**
 * Determine final status based on counters with proper logic
 */
function determineFinalStatus(counters: any): string {
  const total = counters.total || 0;
  const passed = counters.passed || 0;
  const failed = counters.failed || 0;
  const stopped = counters.stopped || 0;
  const skipped = counters.skipped || 0;
  
  // Calculate completed count
  const completed = passed + failed + stopped + skipped;
  
  logger.info(`AtomicReportService: determineFinalStatus - Total: ${total}, Passed: ${passed}, Failed: ${failed}, Stopped: ${stopped}, Skipped: ${skipped}, Completed: ${completed}`);
  
  // Ensure we have valid data
  if (total === 0 || completed === 0) {
    return 'completed'; // Fallback for edge cases
  }
  
  // Priority order for status determination (matching checkAndUpdateRunStatus logic):
  // 1. If all tests are stopped -> stopped
  // 2. If all tests are failed -> failed  
  // 3. If all tests are passed -> completed
  // 4. Mixed results -> partial
  
  if (stopped > 0 && stopped === total) {
    // All tests were stopped
    logger.info(`AtomicReportService: determineFinalStatus result: 'stopped' - all tests stopped (${stopped}/${total})`);
    return 'stopped';
  } else if (failed > 0 && (failed + skipped) === total) {
    // All non-skipped tests failed (skipped tests don't count as "failed" for overall status)
    logger.info(`AtomicReportService: determineFinalStatus result: 'failed' - all tests failed/skipped (failed: ${failed}, skipped: ${skipped}, total: ${total})`);
    return 'failed';
  } else if (passed > 0 && (passed + skipped) === total) {
    // All non-skipped tests passed
    logger.info(`AtomicReportService: determineFinalStatus result: 'completed' - all tests passed/skipped (passed: ${passed}, skipped: ${skipped}, total: ${total})`);
    return 'completed';
  } else if (failed > 0 || stopped > 0) {
    // Mixed results - some passed, some failed/stopped
    logger.info(`AtomicReportService: determineFinalStatus result: 'partial' - mixed results (passed: ${passed}, failed: ${failed}, stopped: ${stopped}, skipped: ${skipped})`);
    return 'partial';
  } else {
    // All tests passed (fallback)
    logger.info(`AtomicReportService: determineFinalStatus result: 'completed' - fallback case`);
    return 'completed';
  }
}

/**
 * Validate and fix counter integrity
 */
function validateAndFixCounters(counters: any, transactionId: string): any {
  const total = counters.total || 0;
  const passed = counters.passed || 0;
  const failed = counters.failed || 0;
  const stopped = counters.stopped || 0;
  const skipped = counters.skipped || 0;
  const running = counters.running || 0;
  let pending = counters.pending || 0;
  
  // Calculate expected completed count
  const completed = passed + failed + stopped + skipped;
  
  // Detect counter corruption
  const hasNegativeValues = passed < 0 || failed < 0 || stopped < 0 || skipped < 0 || running < 0 || pending < 0;
  const completedExceedsTotal = completed > total;
  const runningExceedsTotal = running > total;
  
  if (hasNegativeValues || completedExceedsTotal || runningExceedsTotal) {
    logger.error(`AtomicReportService: COUNTER CORRUPTION DETECTED (transaction: ${transactionId}):
    - Total: ${total}
    - Passed: ${passed} (negative: ${passed < 0})
    - Failed: ${failed} (negative: ${failed < 0})
    - Stopped: ${stopped} (negative: ${stopped < 0})
    - Skipped: ${skipped} (negative: ${skipped < 0})
    - Running: ${running} (negative: ${running < 0})
    - Pending: ${pending} (negative: ${pending < 0})
    - Completed: ${completed} (exceeds total: ${completedExceedsTotal})
    - Running exceeds total: ${runningExceedsTotal}`);
    
    // Fix negative values
    const fixedCounters = {
      total,
      passed: Math.max(0, passed),
      failed: Math.max(0, failed),
      stopped: Math.max(0, stopped),
      skipped: Math.max(0, skipped),
      running: Math.max(0, running),
      pending: Math.max(0, pending)
    };
    
    // Recalculate pending based on total and completed
    const fixedCompleted = fixedCounters.passed + fixedCounters.failed + fixedCounters.stopped + fixedCounters.skipped;
    fixedCounters.pending = Math.max(0, total - fixedCompleted - fixedCounters.running);
    
    logger.warn(`AtomicReportService: Fixed counters (transaction: ${transactionId}):
    - Total: ${fixedCounters.total}
    - Passed: ${fixedCounters.passed}
    - Failed: ${fixedCounters.failed}
    - Stopped: ${fixedCounters.stopped}
    - Skipped: ${fixedCounters.skipped}
    - Running: ${fixedCounters.running}
    - Pending: ${fixedCounters.pending}
    - Fixed Completed: ${fixedCompleted}`);
    
    return fixedCounters;
  }
  
  return counters;
}

/**
 * Update run to completed status atomically
 */
async function updateRunToCompleted(
  collection: Collection,
  runId: string,
  executionId: string,
  finalStatus: string
): Promise<any> {
  return await collection.findOneAndUpdate(
    { runId, executionId },
    {
      $set: {
        status: finalStatus,
        progress: 100,
        completedAt: new Date(),
        lastUpdated: new Date()
      }
    },
    { returnDocument: 'after' }
  );
}

/**
 * Update run progress atomically
 */
async function updateRunProgress(
  collection: Collection,
  runId: string,
  executionId: string,
  progress: number
): Promise<any> {
  return await collection.findOneAndUpdate(
    { runId, executionId },
    {
      $set: {
        progress,
        lastUpdated: new Date()
      }
    },
    { returnDocument: 'after' }
  );
}

/**
 * Get the correct execution ID for a run by finding the latest run report
 * This helps resolve execution ID mismatch issues
 *
 * @param runId Run ID
 * @returns Success status with execution ID
 */
export async function getRunExecutionId(
  runId: string
): Promise<{
  success: boolean;
  executionId?: string;
  message?: string;
}> {
  try {
    if (!runReportsCollection) throw new Error('runReportsCollection not available');

    // Find the most recent run report for this run
    const report = await runReportsCollection.findOne(
      { runId },
      { sort: { createdAt: -1 } }
    );

    if (!report) {
      return {
        success: false,
        message: `No run report found for run ${runId}`
      };
    }

    return {
      success: true,
      executionId: report.executionId
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting run execution ID: ${error.message}`, {
      error: error.stack,
      runId
    });

    return {
      success: false,
      message: `Error getting run execution ID: ${error.message}`
    };
  }
}

/**
 * Update run status and progress based on test result (eliminates counters - scenario-based only)
 *
 * @param runId Run ID
 * @param executionId Execution ID
 * @param testResult Test result data
 * @param options Additional options
 * @param existingReport Optional existing report to avoid redundant database lookup
 * @returns Success status with updated report
 */
export async function updateRunCountersAtomic(
  runId: string,
  executionId: string,
  testResult: TestResult,
  options: CounterUpdateOptions = {},
  existingReport?: any
): Promise<CounterUpdateResult> {
  const transactionId = generateCounterTransactionId(runId, testResult.testId, options.transactionId);
  const source = options.source || 'unknown';

  try {
    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `update-run-status-${runId}-${executionId}-${transactionId}`,
      async () => {
        if (!runReportsCollection) throw new Error('runReportsCollection not available');

        // Get or use existing report
        let currentReport = existingReport;
        
        if (!currentReport) {
          // Check for duplicate processing if not skipped
          if (!options.skipDuplicateCheck) {
            const duplicateCheck = await checkForDuplicateProcessing(runReportsCollection, runId, executionId, testResult.testId);

            if (duplicateCheck) {
              logger.warn(`AtomicReportService: Test result ${testResult.testId} already processed for run ${runId}. Skipping update.`);
              return {
                success: true,
                message: `Test result already processed`,
                report: duplicateCheck
              };
            }
          }
          
          // Get current report
          const reportResult = await runReportsCollection.findOne({ runId, executionId });
          if (!reportResult) {
            // Try execution ID recovery
            const executionIdResult = await getRunExecutionId(runId);
            if (executionIdResult.success && executionIdResult.executionId) {
              const recoveredReport = await runReportsCollection.findOne({ runId, executionId: executionIdResult.executionId });
              currentReport = recoveredReport;
              executionId = executionIdResult.executionId;
            }
          } else {
            currentReport = reportResult;
          }
        } else {
          // Check for duplicate processing
          if (!options.skipDuplicateCheck && currentReport.processedResults && currentReport.processedResults[testResult.testId]) {
            logger.warn(`AtomicReportService: Test result ${testResult.testId} already processed for run ${runId} (found in existing report). Skipping update.`);
            return {
              success: true,
              message: `Test result already processed`,
              report: currentReport
            };
          }
        }

        if (!currentReport) {
          logger.warn(`AtomicReportService: Run report not found for run ${runId} even after execution ID recovery attempt`);
          return {
            success: false,
            message: `Run report not found for run ${runId} with executionId ${executionId}`
          };
        }

        // Update scenario status in scenarioStatuses array
        const scenarioStatuses = currentReport.scenarioStatuses || [];
        const scenarioIndex = scenarioStatuses.findIndex((s: any) => s.scenarioId === testResult.scenarioId);
        
        if (scenarioIndex >= 0) {
          // Update existing scenario
          scenarioStatuses[scenarioIndex] = {
            ...scenarioStatuses[scenarioIndex],
            status: testResult.status,
            updatedAt: new Date()
          };
        } else {
          // Add new scenario status (shouldn't happen but safety)
          scenarioStatuses.push({
            scenarioId: testResult.scenarioId,
            status: testResult.status,
            updatedAt: new Date()
          });
        }

        // Calculate new results and status from scenarios (single source of truth)
        const newResults = calculateResultsFromScenarios(scenarioStatuses, currentReport.scenarioIds?.length);
        const newStatus = determineStatusFromScenarios(scenarioStatuses);
        const newProgress = calculateProgressFromScenarios(scenarioStatuses, currentReport.scenarioIds?.length);
        const isCompleted = isRunCompletedFromScenarios(scenarioStatuses, currentReport.scenarioIds?.length);

        // Prepare update operation
        const updateData = {
          scenarioStatuses,
          status: newStatus,
          progress: newProgress,
          lastUpdated: new Date(),
          [`processedResults.${testResult.testId}`]: {
            status: testResult.status,
            processedAt: new Date(),
            transactionId,
            source
          }
        };

        // Add completion timestamp if completed
        if (isCompleted && !currentReport.completedAt) {
          updateData.completedAt = new Date();
        }

        const reportQuery = { id: currentReport.id };

        // Perform atomic update
        let result = await runReportsCollection.findOneAndUpdate(
          reportQuery,
          { $set: updateData },
          { returnDocument: 'after' }
        );

        // Enhanced fallback with direct update if findOneAndUpdate fails
        if (!result || !result.value) {
          try {
            const directUpdateResult = await runReportsCollection.updateOne(reportQuery, { $set: updateData });
            
            if (directUpdateResult.matchedCount > 0) {
              const updatedDoc = await runReportsCollection.findOne(reportQuery);
              result = { value: updatedDoc } as any;
            }
          } catch (directUpdateError) {
            logger.error(`AtomicReportService: Direct updateOne also failed: ${directUpdateError}`);
          }
        }

        if (!result || !result.value) {
          return {
            success: false,
            message: `Failed to update run report for run ${runId}`
          };
        }

        const updatedReport = result.value;

        logger.info(`AtomicReportService: Successfully updated run ${runId} using scenario-based logic - Status: ${updatedReport.status}, Progress: ${updatedReport.progress}%`);

        return {
          success: true,
          report: updatedReport,
          wasCompleted: isCompleted
        };
      }
    );
  } catch (error: any) {
    logger.error(`AtomicReportService: Error updating run status: ${error.message}`, {
      error: error.stack,
      runId,
      executionId,
      testResult,
      transactionId
    });

    return {
      success: false,
      message: `Error updating run status: ${error.message}`
    };
  }
}

/**
 * Update run report with general data
 *
 * @param reportId Report ID
 * @param updateData Update data
 * @returns Success status with updated report
 */
export async function updateRunReport(
  reportId: string,
  updateData: {
    status?: string;
    completedAt?: Date;
    startedAt?: Date;
    lastUpdated?: Date;
    scenarioStatuses?: any[];
    testrailRunId?: number;
    testrailRunLink?: string;
    $set?: any;
    $inc?: any;
    $push?: any;
    $pull?: any;
  }
): Promise<{
  success: boolean;
  message?: string;
  report?: any;
}> {
  try {
    logger.info(`AtomicReportService: Updating run report ${reportId}`);

    // Generate a truly unique transaction ID to prevent collisions
    const transactionId = `update-run-report-${reportId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;

    // Use operation queue to ensure atomic updates with unique transaction ID
    return await operationQueue.enqueue(
      transactionId,
      async () => {
        if (!runReportsCollection) throw new Error('runReportsCollection not available');

        // Get current report
        const currentReport = await runReportsCollection.findOne({ id: reportId });

        if (!currentReport) {
          logger.warn(`AtomicReportService: Run report ${reportId} not found (transaction: ${transactionId})`);
          return {
            success: false,
            message: `Run report ${reportId} not found`
          };
        }

        // Check if this is a duplicate status update to prevent unnecessary operations
        if (updateData.status && updateData.status === currentReport.status) {
          logger.info(`AtomicReportService: Report ${reportId} already has status ${updateData.status}, skipping update (transaction: ${transactionId})`);
          return {
            success: true,
            message: `Report already has status ${updateData.status}`,
            report: currentReport
          };
        }

        // Prepare update operation
        const updateOperation: any = {};

        // Handle MongoDB operators
        if (updateData.$set || updateData.$inc || updateData.$push || updateData.$pull) {
          // Use provided operators
          if (updateData.$set) updateOperation.$set = updateData.$set;
          if (updateData.$inc) updateOperation.$inc = updateData.$inc;
          if (updateData.$push) updateOperation.$push = updateData.$push;
          if (updateData.$pull) updateOperation.$pull = updateData.$pull;

          // Add lastUpdated to $set
          if (updateOperation.$set) {
            updateOperation.$set.lastUpdated = new Date();
          } else {
            updateOperation.$set = { lastUpdated: new Date() };
          }

          // Remove operators from updateData to avoid duplication
          const filteredUpdateData = { ...updateData };
          delete filteredUpdateData.$set;
          delete filteredUpdateData.$inc;
          delete filteredUpdateData.$push;
          delete filteredUpdateData.$pull;

          // Add remaining fields to $set
          if (Object.keys(filteredUpdateData).length > 0) {
            updateOperation.$set = { ...updateOperation.$set, ...filteredUpdateData };
          }
        } else {
          // Regular update with $set
          updateOperation.$set = {
            ...updateData,
            lastUpdated: new Date()
          };
        }

        logger.info(`AtomicReportService: Attempting findOneAndUpdate for reportId: '${reportId}', operation: ${JSON.stringify(updateOperation)} (transaction: ${transactionId})`);

        // Perform update with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await runReportsCollection.findOneAndUpdate(
              { id: reportId },
              updateOperation,
              { returnDocument: 'after' }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`AtomicReportService: Update failed for reportId ${reportId} after ${retryCount} attempts: ${error.message} (transaction: ${transactionId})`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`AtomicReportService: Retrying update for reportId ${reportId} in ${delay}ms (attempt ${retryCount}/${maxRetries}) (transaction: ${transactionId})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!result || !result.value) {
          // Check if document still exists - it might have been deleted or modified by another operation
          const stillExists = await runReportsCollection.findOne({ id: reportId });
          if (!stillExists) {
            logger.error(`AtomicReportService: findOneAndUpdate returned null for reportId: ${reportId} - document no longer exists (transaction: ${transactionId})`);
            return {
              success: false,
              message: `Failed to update run report ${reportId} - document not found or was deleted`
            };
          } else {
            logger.error(`AtomicReportService: findOneAndUpdate returned null for reportId: ${reportId} - update failed but document exists (transaction: ${transactionId})`);
            return {
              success: false,
              message: `Failed to update run report ${reportId} - update operation failed`
            };
          }
        }

        const updatedReport = result.value;

        logger.info(`AtomicReportService: Successfully updated run report ${reportId} (transaction: ${transactionId})`);

        // Emit events for status changes
        if (updateData.status && updateData.status !== currentReport.status) {
          logger.info(`AtomicReportService: Emitting status change event for run ${updatedReport.runId} from ${currentReport.status} to ${updatedReport.status} (transaction: ${transactionId})`);
          runReportEvents.emit(`run:${updatedReport.runId}:status`, {
            runId: updatedReport.runId,
            executionId: updatedReport.executionId,
            status: updatedReport.status,
            oldStatus: currentReport.status,
            reportId: reportId,
            transactionId: transactionId
          });

          // Emit completion event for terminal statuses
          if (['completed', 'failed', 'stopped', 'partial'].includes(updatedReport.status)) {
            // Import the calculateTestResults function
            const { calculateTestResults } = await import('../../models/test-types.js');

            // Calculate test results from scenarioStatuses
            const statusCounts = calculateTestResults(updatedReport);

            const runResult = {
              success: updatedReport.status === 'completed',
              status: updatedReport.status,
              total: statusCounts.total || 0,
              passed: statusCounts.completed || 0,
              failed: statusCounts.failed || 0
            };

            runReportEvents.emit(`run:${updatedReport.runId}:${updatedReport.executionId}:completed`, runResult);
            logger.info(`AtomicReportService: Emitted completion event for run ${updatedReport.runId} with status ${updatedReport.status} (transaction: ${transactionId})`);
          }
        }

        return {
          success: true,
          report: updatedReport
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`AtomicReportService: Error updating run report: ${error.message}`, {
      error: error.stack,
      reportId
    });

    return {
      success: false,
      message: `Error updating run report: ${error.message}`
    };
  }
}

/**
 * Check and update run status based on scenario statuses
 *
 * @param runId Run ID
 * @param executionId Execution ID
 * @returns Success status
 */
export async function checkAndUpdateRunStatus(
  runId: string,
  executionId: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // Get the run report - this now includes fallback logic for execution ID mismatches
    let reportResult = await getRunReportByRunId(runId, executionId);

    // If we still couldn't find it with the fallback, try getting the correct execution ID
    if (!reportResult.success) {
      const executionIdResult = await getRunExecutionId(runId);
      
      if (executionIdResult.success && executionIdResult.executionId) {
        // Retry with the correct execution ID
        reportResult = await getRunReportByRunId(runId, executionIdResult.executionId);
        
        if (reportResult.success) {
          // Update the executionId parameter for subsequent operations
          executionId = executionIdResult.executionId;
        }
      }
    }

    if (!reportResult.success) {
      return {
        success: false,
        message: reportResult.message
      };
    }

    const report = reportResult.report;

    // If the run is already in a terminal state, don't update
    if (['completed', 'failed', 'stopped', 'partial'].includes(report.status)) {
      return {
        success: true,
        message: `Run already in terminal state ${report.status}`
      };
    }

    // Get scenario statuses for checks
    const scenarioStatuses = report.scenarioStatuses || [];

    // Use our new scenario-based calculation functions (single source of truth)
    const newResults = calculateResultsFromScenarios(scenarioStatuses, report.scenarioIds?.length);
    const newStatus = determineStatusFromScenarios(scenarioStatuses);
    
    // Check if status needs to be updated
    if (newStatus !== report.status) {
      logger.info(`AtomicReportService: Updating run ${runId} status from ${report.status} to ${newStatus}`);

      // Retry logic for race condition handling
      let updateAttempts = 0;
      const maxUpdateAttempts = 3;
      let updateResult;

      while (updateAttempts < maxUpdateAttempts) {
        updateAttempts++;

        // FIXED: Pass the existing report to updateRunStatusAtomic to avoid execution ID mismatch
        // Use updateRunReport instead of updateRunStatusAtomic to avoid redundant lookups
        updateResult = await updateRunReport(report.id, {
          status: newStatus,
          lastUpdated: new Date(),
          ...(newStatus === 'completed' || newStatus === 'failed' || newStatus === 'stopped' || newStatus === 'partial' ? 
              { completedAt: new Date() } : {})
        });

        if (updateResult.success) {
          logger.info(`AtomicReportService: Successfully updated run ${runId} status to ${newStatus}`);
          break;
        } else {
          logger.warn(`AtomicReportService: Failed to update run ${runId} status on attempt ${updateAttempts}: ${updateResult.message}`);
          
          // Wait before retry (exponential backoff)
          if (updateAttempts < maxUpdateAttempts) {
            const waitTime = Math.pow(2, updateAttempts - 1) * 100; // 100ms, 200ms, 400ms
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      }

      if (!updateResult || !updateResult.success) {
        logger.error(`AtomicReportService: Failed to update run ${runId} status after ${maxUpdateAttempts} attempts`);
        return {
          success: false,
          message: updateResult?.message || 'Failed to update run status after multiple attempts'
        };
      }

      return {
        success: true,
        message: `Run status updated to ${newStatus}`
      };
    }

    // No update needed
    return {
      success: true,
      message: `No status update needed`
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error checking and updating run status: ${error.message}`, {
      error: error.stack,
      runId,
      executionId
    });

    return {
      success: false,
      message: `Error checking and updating run status: ${error.message}`
    };
  }
}

/**
 * Reset stuck runs
 *
 * @param maxStuckTimeMinutes Maximum time a run can be stuck (in minutes)
 * @returns Success status with reset count
 */
export async function resetStuckRuns(
  maxStuckTimeMinutes: number = 30
): Promise<{ success: boolean; resetCount: number; message?: string }> {
  try {
    logger.info(`AtomicReportService: Resetting stuck runs (max stuck time: ${maxStuckTimeMinutes} minutes)`);

    if (!runReportsCollection) throw new Error('runReportsCollection not available');

    // Find runs that haven't been updated for a while and are still running
    const cutoffTime = new Date();
    cutoffTime.setMinutes(cutoffTime.getMinutes() - maxStuckTimeMinutes);

    const stuckRuns = await runReportsCollection.find({
      status: 'running',
      lastUpdated: { $lt: cutoffTime }
    }).toArray();



    // Reset each stuck run
    let resetCount = 0;

    for (const run of stuckRuns) {
      try {
        // Update run status to partial
        const updateResult = await updateRunStatusAtomic(run.runId, run.executionId, 'partial', {
          noStartTime: true,
          source: 'resetStuckRuns'
        });

        if (updateResult.success) {
          resetCount++;
          logger.info(`AtomicReportService: Reset stuck run ${run.runId} to partial status`);
        } else {
          logger.error(`AtomicReportService: Failed to reset stuck run ${run.runId}: ${updateResult.message}`);
        }
      } catch (resetError: any) {
        logger.error(`AtomicReportService: Error resetting stuck run ${run.runId}: ${resetError.message}`);
      }
    }

    logger.info(`AtomicReportService: Reset ${resetCount} stuck runs`);

    return {
      success: true,
      resetCount,
      message: `Reset ${resetCount} stuck runs`
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error resetting stuck runs: ${error.message}`, {
      error: error.stack
    });

    return {
      success: false,
      resetCount: 0,
      message: `Error resetting stuck runs: ${error.message}`
    };
  }
}

/**
 * Update TestRail information for a run report
 *
 * @param reportId Report ID
 * @param testrailRunId TestRail run ID
 * @param testrailRunLink TestRail run link
 * @returns Success status
 */
export async function updateRunReportTestRail(
  reportId: string,
  testrailRunId: number,
  testrailRunLink: string
): Promise<{ success: boolean; message?: string }> {
  try {
    logger.info(`AtomicReportService: Updating TestRail info for run report ${reportId}`);

    // Update the run report
    const updateResult = await updateRunReport(reportId, {
      testrailRunId,
      testrailRunLink
    });

    if (!updateResult.success) {
      return {
        success: false,
        message: updateResult.message
      };
    }



    return {
      success: true,
      message: `TestRail info updated successfully`
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error updating TestRail info: ${error.message}`, {
      error: error.stack,
      reportId
    });

    return {
      success: false,
      message: `Error updating TestRail info: ${error.message}`
    };
  }
}

/**
 * Get TestRail information for a run report
 *
 * @param runId Run ID
 * @returns Success status with TestRail info
 */
export async function getRunReportTestRailInfo(
  runId: string
): Promise<{
  success: boolean;
  testrailRunId?: number;
  testrailRunLink?: string;
  message?: string;
}> {
  try {
    logger.info(`AtomicReportService: Getting TestRail info for run ${runId}`);

    // Get the run report
    const reportResult = await getRunReportByRunId(runId);

    if (!reportResult.success) {
      return {
        success: false,
        message: reportResult.message
      };
    }

    const report = reportResult.report;

    // Return TestRail info
    return {
      success: true,
      testrailRunId: report.testrailRunId,
      testrailRunLink: report.testrailRunLink
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting TestRail info: ${error.message}`, {
      error: error.stack,
      runId
    });

    return {
      success: false,
      message: `Error getting TestRail info: ${error.message}`
    };
  }
}

/**
 * Get active run reports for a user
 *
 * @param userId User ID
 * @param teamId Optional team ID
 * @param companyId Optional company ID
 * @returns Success status with active run reports
 */
export async function getActiveRunReportsByUserId(
  userId: string,
  teamId?: string,
  companyId?: string
): Promise<{
  success: boolean;
  reports?: any[];
  message?: string;
}> {
  try {

    await ensureMongoDBConnection();
    if (!db || !runsCollection) {
      return {
        success: false,
        message: "MongoDB not initialized"
      };
    }

    // Build the initial match for the 'runs' collection
    const runsMatchQuery: any = {};
    if (teamId && companyId) {
      runsMatchQuery.$and = [{ $or: [{ userId }, { teamId }, { companyId }] }];
    } else if (teamId) {
      runsMatchQuery.$and = [{ $or: [{ userId }, { teamId }] }];
    } else if (companyId) {
      runsMatchQuery.$and = [{ $or: [{ userId }, { companyId }] }];
    } else {
      runsMatchQuery.userId = userId;
    }

    const aggregationPipeline: any[] = [
      { $match: runsMatchQuery },
      {
        $lookup: {
          from: 'run_reports',
          localField: 'id',
          foreignField: 'runId',
          as: 'associated_reports'
        }
      },
      { $unwind: '$associated_reports' },
      // Look up the executed user's name from the users collection if executedUserName is not already set
      {
        $lookup: {
          from: 'users',
          let: { executedUserId: '$associated_reports.executedUser' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$id', '$$executedUserId'] }
              }
            },
            {
              $project: {
                _id: 0,
                name: 1
              }
            }
          ],
          as: 'executedUserInfo'
        }
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$associated_reports',
              {
                runName: { $ifNull: ['$name', 'unnamed'] },
                runId: '$id',
                executedUserName: {
                  $cond: {
                    if: { $and: [
                      { $eq: [{ $ifNull: ['$associated_reports.executedUserName', null] }, null] },
                      { $gt: [{ $size: '$executedUserInfo' }, 0] }
                    ]},
                    then: { $arrayElemAt: ['$executedUserInfo.name', 0] },
                    else: { $ifNull: ['$associated_reports.executedUserName', 'System'] }
                  }
                }
              }
            ]
          }
        }
      },
      // Filter for active runs (status is 'running')
      { $match: { status: 'running' } },
      // Sort by lastUpdated to get the most recently active runs first
      { $sort: { lastUpdated: -1 } }
    ];

    const result = await runsCollection.aggregate(aggregationPipeline).toArray();

    return {
      success: true,
      reports: result
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting active run reports by user ID: ${error.message}`, {
      error: error.stack,
      userId
    });

    return {
      success: false,
      message: `Error getting active run reports by user ID: ${error.message}`
    };
  }
}

/**
 * Get run reports by user ID
 *
 * @param userId User ID
 * @param limit Limit
 * @param skip Skip
 * @param teamId Optional team ID
 * @param companyId Optional company ID
 * @param filter Filter options
 * @returns Success status with reports
 */
export async function getRunReportsByUserId(
  userId: string,
  limit: number = 50,
  skip: number = 0,
  teamId?: string,
  companyId?: string,
  filter?: {
    status?: string | string[];
    startDate?: Date;
    endDate?: Date;
    search?: string;
  }
): Promise<{
  success: boolean;
  reports?: any[];
  total?: number;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db || !runsCollection) {
      return {
        success: false,
        message: "MongoDB not initialized"
      };
    }

    // 1. Build the initial match for the 'runs' collection
    const runsMatchQuery: any = {};
    if (teamId && companyId) {
      runsMatchQuery.$and = [{ $or: [{ userId }, { teamId }, { companyId }] }];
    } else if (teamId) {
      runsMatchQuery.$and = [{ $or: [{ userId }, { teamId }] }];
    } else if (companyId) {
      runsMatchQuery.$and = [{ $or: [{ userId }, { companyId }] }];
    } else {
      runsMatchQuery.userId = userId;
    }

    const aggregationPipeline: any[] = [
      { $match: runsMatchQuery },
      {
        $lookup: {
          from: 'run_reports', // Name of the run_reports collection
          localField: 'id',    // Field from 'runs' collection (the UUID string)
          foreignField: 'runId', // Corresponding field in 'run_reports' collection storing the run's UUID string
          as: 'associated_reports'
        }
      },
      { $unwind: '$associated_reports' }, // Creates a document for each report
      // Look up the executed user's name from the users collection
      {
        $lookup: {
          from: 'users',
          let: { executedUserId: '$associated_reports.executedUser' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$id', '$$executedUserId'] }
              }
            },
            {
              $project: {
                _id: 0,
                name: 1
              }
            }
          ],
          as: 'executedUserInfo'
        }
      },
      // Current doc: run fields + associated_reports object.
      // We want to make associated_reports fields top-level and add run.name and executedUserName.
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$associated_reports',
              {
                runName: { $ifNull: ['$name', 'unnamed'] }, // Include run name from runs collection with fallback to 'unnamed'
                runId: '$id', // runId is original run's ID
                executedUserName: {
                  $cond: {
                    if: { $and: [
                      { $eq: [{ $ifNull: ['$associated_reports.executedUserName', null] }, null] },
                      { $gt: [{ $size: '$executedUserInfo' }, 0] }
                    ]},
                    then: { $arrayElemAt: ['$executedUserInfo.name', 0] },
                    else: { $ifNull: ['$associated_reports.executedUserName', 'System'] }
                  }
                }
              }
            ]
          }
        }
      }
    ];

    // 2. Build match for report-specific filters (status, date, search on report fields)
    const reportSpecificMatch: any = {};
    if (filter) {
      if (filter.status) {
        reportSpecificMatch.status = Array.isArray(filter.status) ? { $in: filter.status } : filter.status;
      }
      if (filter.startDate || filter.endDate) {
        reportSpecificMatch.createdAt = {}; // Assuming reports have 'createdAt'
        if (filter.startDate) reportSpecificMatch.createdAt.$gte = filter.startDate;
        if (filter.endDate) reportSpecificMatch.createdAt.$lte = filter.endDate;
      }
      if (filter.search) {
        // Search on report fields like title or name (of the report/test)
        reportSpecificMatch.$or = [
          { title: { $regex: filter.search, $options: 'i' } }, // Assuming report has 'title'
          { name: { $regex: filter.search, $options: 'i' } },  // Assuming report has 'name'
          { runName: { $regex: filter.search, $options: 'i' } } // Also search in the run name
        ];
      }
    }

    if (Object.keys(reportSpecificMatch).length > 0) {
      aggregationPipeline.push({ $match: reportSpecificMatch });
    }

    // 3. Add sorting (by report's createdAt)
    aggregationPipeline.push({ $sort: { createdAt: -1 } });

    // 4. Add $facet for pagination and total count
    aggregationPipeline.push({
      $facet: {
        paginatedResults: [
          { $skip: skip },
          { $limit: limit }
        ],
        totalCount: [
          { $count: 'count' }
        ]
      }
    });

    const result = await runsCollection.aggregate(aggregationPipeline).toArray();

    const finalReports = result[0]?.paginatedResults || [];
    const total = result[0]?.totalCount[0]?.count || 0;



    return {
      success: true,
      reports: finalReports,
      total
    };

  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting aggregated run reports by user ID: ${error.message}`, {
      error: error.stack,
      userId
    });

    return {
      success: false,
      message: `Error getting aggregated run reports by user ID: ${error.message}`
    };
  }
}

/**
 * Get run test reports by execution ID
 *
 * @param executionId Execution ID
 * @param runId Optional run ID
 * @returns Success status with reports
 */
export async function getRunTestReportsByExecutionId(
  executionId: string,
  runId?: string
): Promise<{
  success: boolean;
  reports?: any[];
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db || !reportsCollection) {
      return {
        success: false,
        message: "MongoDB not initialized"
      };
    }

    // Check if reportsCollection is available
    if (!reportsCollection) {
      logger.warn(`AtomicReportService: reportsCollection is not initialized`);
      return {
        success: false,
        message: "reportsCollection not initialized"
      };
    }

    // Build query
    const query: any = { executionId };

    if (runId) {
      query.runId = runId;
    }

    // Get reports
    const reports = await reportsCollection
      .find(query)
      .sort({ createdAt: -1 })
      .toArray();



    return {
      success: true,
      reports
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting test reports by execution ID: ${error.message}`, {
      error: error.stack,
      executionId,
      runId
    });

    return {
      success: false,
      message: `Error getting test reports by execution ID: ${error.message}`
    };
  }
}

/**
 * Get latest test reports by run ID
 *
 * @param runId Run ID
 * @returns Success status with reports
 */
export async function getLatestTestReportsByRunId(
  runId: string
): Promise<{
  success: boolean;
  reports?: any[];
  message?: string;
}> {
  try {
    logger.info(`AtomicReportService: Getting latest test reports for run ${runId}`);

    // Get the run report to get the latest execution ID
    const reportResult = await getRunReportByRunId(runId);

    if (!reportResult.success) {
      return {
        success: false,
        message: reportResult.message
      };
    }

    const executionId = reportResult.report.executionId;

    // Get test reports for this execution ID
    return await getRunTestReportsByExecutionId(executionId, runId);
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting latest test reports by run ID: ${error.message}`, {
      error: error.stack,
      runId
    });

    return {
      success: false,
      message: `Error getting latest test reports by run ID: ${error.message}`
    };
  }
}

/**
 * Get run report by ID
 *
 * @param reportId Report ID
 * @returns Success status with report
 */
export async function getRunReportById(
  reportId: string
): Promise<{
  success: boolean;
  report?: any;
  message?: string;
}> {
  try {
    logger.info(`AtomicReportService: Getting run report ${reportId}`);

    if (!runReportsCollection) throw new Error('runReportsCollection not available');

    // Find the report
    const report = await runReportsCollection.findOne({ id: reportId });

    if (!report) {
      logger.warn(`AtomicReportService: Run report ${reportId} not found`);
      return {
        success: false,
        message: `Run report ${reportId} not found`
      };
    }
    return {
      success: true,
      report
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting run report by ID: ${error.message}`, {
      error: error.stack,
      reportId
    });

    return {
      success: false,
      message: `Error getting run report by ID: ${error.message}`
    };
  }
}

/**
 * Delete run report and test reports
 *
 * @param reportId Report ID
 * @param runId Optional run ID
 * @param executionId Optional execution ID
 * @returns Success status with deleted counts
 */
export async function deleteRunReportAndTestReports(
  reportId: string,
  runId?: string,
  executionId?: string
): Promise<{
  success: boolean;
  message?: string;
  deletedTestReports?: number;
}> {
  try {
    logger.info(`AtomicReportService: Deleting run report ${reportId} and associated test reports${runId ? `, runId: ${runId}` : ''}${executionId ? `, executionId: ${executionId}` : ''}`);

    if (!runReportsCollection) throw new Error('runReportsCollection not available');

    // Find the report
    const report = await runReportsCollection.findOne({ id: reportId });

    if (!report) {
      logger.warn(`AtomicReportService: Run report ${reportId} not found for deletion`);
      return {
        success: false,
        message: `Run report ${reportId} not found`
      };
    }

    // Use provided executionId or get it from the report
    const reportExecutionId = executionId || report.executionId;

    // Delete test reports
    await ensureMongoDBConnection();
    if (!db || !reportsCollection) {
      return {
        success: false,
        message: "MongoDB not initialized"
      };
    }

    if (!reportsCollection) {
      logger.warn(`AtomicReportService: reportsCollection is not initialized`);
      return {
        success: false,
        message: "reportsCollection not initialized"
      };
    }

    // Delete test reports for this execution ID
    const deleteTestResult = await reportsCollection.deleteMany({
      executionId: reportExecutionId
    });

    const deletedTestCount = deleteTestResult.deletedCount || 0;
    logger.info(`AtomicReportService: Deleted ${deletedTestCount} test reports for execution ${reportExecutionId}`);

    // Delete run report
    const deleteRunResult = await runReportsCollection.deleteOne({ id: reportId });

    if (deleteRunResult.deletedCount === 0) {
      logger.warn(`AtomicReportService: Run report ${reportId} not found for deletion`);
      return {
        success: false,
        message: `Run report ${reportId} not found`
      };
    }



    return {
      success: true,
      deletedTestReports: deletedTestCount
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error deleting run report and test reports: ${error.message}`, {
      error: error.stack,
      reportId
    });

    return {
      success: false,
      message: `Error deleting run report and test reports: ${error.message}`
    };
  }
}

/**
 * Delete multiple run reports
 *
 * @param reportIds Report IDs
 * @returns Success status with deleted counts and failed report IDs
 */
export async function deleteMultipleRunReports(
  reportIds: string[]
): Promise<{
  success: boolean;
  deletedCount?: number;
  deletedRunReports?: number;
  deletedTestReports?: number;
  failedReportIds?: string[];
  message?: string;
}> {
  try {
    logger.info(`AtomicReportService: Deleting ${reportIds.length} run reports`);

    let deletedCount = 0;
    let totalDeletedTestReports = 0;
    const failedReportIds: string[] = [];

    // Delete each report
    for (const reportId of reportIds) {
      try {
        const result = await deleteRunReportAndTestReports(reportId);

        if (result.success) {
          deletedCount++;
          totalDeletedTestReports += result.deletedTestReports || 0;
        } else {
          logger.warn(`AtomicReportService: Failed to delete run report ${reportId}: ${result.message}`);
          failedReportIds.push(reportId);
        }
      } catch (deleteError: any) {
        logger.error(`AtomicReportService: Error deleting run report ${reportId}: ${deleteError.message}`);
        failedReportIds.push(reportId);
      }
    }



    return {
      success: true,
      deletedCount,
      deletedRunReports: deletedCount,
      deletedTestReports: totalDeletedTestReports,
      failedReportIds: failedReportIds.length > 0 ? failedReportIds : undefined
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error deleting multiple run reports: ${error.message}`, {
      error: error.stack,
      reportIds
    });

    return {
      success: false,
      message: `Error deleting multiple run reports: ${error.message}`
    };
  }
}

/**
 * Force reset run report status
 *
 * @param runId Run ID
 * @param status New status
 * @returns Success status
 */
export async function forceResetRunReportStatus(
  runId: string,
  status: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    logger.info(`AtomicReportService: Force resetting run ${runId} status to ${status}`);

    // Get the run report
    const reportResult = await getRunReportByRunId(runId);

    if (!reportResult.success) {
      return {
        success: false,
        message: reportResult.message
      };
    }

    const report = reportResult.report;

    // Update the report status
    const updateResult = await updateRunReport(report.id, {
      status,
      completedAt: ['completed', 'failed', 'stopped', 'partial'].includes(status) ? new Date() : undefined
    });

    if (!updateResult.success) {
      return {
        success: false,
        message: updateResult.message
      };
    }



    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error force resetting run report status: ${error.message}`, {
      error: error.stack,
      runId,
      status
    });

    return {
      success: false,
      message: `Error force resetting run report status: ${error.message}`
    };
  }
}

/**
 * Create an individual test report atomically
 *
 * @param testReportData Test report data (TestReportDTO)
 * @param options Additional options
 * @returns Success status with report ID
 */
export async function createTestReportAtomic(
  testReportData: TestReportDTO,
  options: {
    source?: string;
    transactionId?: string;
  } = {}
): Promise<{
  success: boolean;
  reportId?: string;
  message?: string;
}> {
  const transactionId = options.transactionId || `create-test-report-${testReportData.id}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;
  const source = options.source || 'unknown';

  logger.info(`AtomicReportService: Creating test report for test ${testReportData.id} (transaction: ${transactionId}, source: ${source})`);

  // Validate the test report data structure
  if (!validateTestReportDTO(testReportData)) {
    logger.error(`AtomicReportService: Invalid TestReportDTO structure for test ${(testReportData as any)?.id || 'unknown'}`);
    return {
      success: false,
      message: "Invalid test report data structure"
    };
  }

  try {
    // Use operation queue to ensure atomic creation
    return await operationQueue.enqueue(
      `create-test-report-${testReportData.id}-${transactionId}`,
      async () => {
        // Import the reports collection from dbConnection
        const { reportsCollection, ensureMongoDBConnection } = await import('./dbConnection.js');

        await ensureMongoDBConnection();

        if (!reportsCollection) {
          return {
            success: false,
            message: "Reports collection not initialized"
          };
        }

        // Check if a report already exists for this test
        const existingReport = await reportsCollection.findOne({ id: testReportData.id });

        if (existingReport) {
          logger.info(`AtomicReportService: Test report already exists for test ${testReportData.id}, updating existing report`);
        }

        // Sanitize environment settings to remove sensitive data
        let sanitizedReportData = { ...testReportData };
        if (sanitizedReportData.environmentSettings) {
          sanitizedReportData.environmentSettings = sanitizeEnvironmentSettings(sanitizedReportData.environmentSettings);
          logger.info(`AtomicReportService: Sanitized environment settings for test ${testReportData.id} - removed sensitive credentials from storage`);
        }

        // Remove redundant fields to optimize storage
        sanitizedReportData = removeRedundantFields(sanitizedReportData);

        // Prepare the report data with metadata
        const reportToSave = {
          ...sanitizedReportData,
          createdAt: existingReport?.createdAt || new Date(),
          updatedAt: new Date(),
          transactionId
        };

        // Check for pending video URL in Redis before saving the report - Atomic operation
        try {
          const { redisConnection } = await import('../../services/redis/redisConnection.js');
          const redis = redisConnection.getClient();

          if (!redis) {
            logger.warn(`AtomicReportService: Redis client not available, skipping pending video URL check for test report ${testReportData.id}`);
          } else {
            const pendingKey = `pending-video-url:${testReportData.id}`;

            // Use atomic GET and DEL operation to prevent race conditions
            const multi = redis.multi();
            multi.get(pendingKey);
            multi.del(pendingKey);
            const results = await multi.exec();

            if (results && results[0] && results[0][1]) {
              const pendingVideoUrl = results[0][1] as string;
              reportToSave.videoUrl = pendingVideoUrl;
              logger.info(`AtomicReportService: Applied and cleaned up pending video URL for test report ${testReportData.id}: ${pendingVideoUrl}`);
            } else {
              logger.debug(`AtomicReportService: No pending video URL found in Redis for test report ${testReportData.id}`);
            }
          }
        } catch (redisError: any) {
          logger.warn(`AtomicReportService: Could not check for pending video URL for test report ${testReportData.id}: ${redisError.message}`);
        }

        // Insert or update the report atomically
        await reportsCollection.replaceOne(
          { id: testReportData.id },
          reportToSave,
          { upsert: true }
        );

        logger.info(`AtomicReportService: Successfully ${existingReport ? 'updated' : 'created'} test report for test ${testReportData.id}`);

        return {
          success: true,
          reportId: testReportData.id,
          message: `Test report ${existingReport ? 'updated' : 'created'} successfully`
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`AtomicReportService: Error creating test report: ${error.message}`, {
      error: error.stack,
      testId: testReportData.id,
      transactionId
    });

    return {
      success: false,
      message: `Error creating test report: ${error.message}`
    };
  }
}

/**
 * Create a run report (alias for createRunReportAtomic)
 *
 * @param runId Run ID
 * @param executionId Execution ID
 * @param options Additional options
 * @returns Success status with report ID
 */
export async function createRunReport(
  runId: string,
  executionId: string,
  options?: {
    userId?: string;
    teamId?: string;
    companyId?: string;
    executedUser?: string;
    executedUserName?: string;
  }
): Promise<{
  success: boolean;
  reportId?: string;
  message?: string;
}> {
  try {
    logger.info(`AtomicReportService: Creating run report for run ${runId} with executionId ${executionId}`);

    // Call the atomic version of the function
    const result = await createRunReportAtomic(runId, executionId, options);

    return {
      success: result.success,
      reportId: result.reportId,
      message: result.message
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error creating run report: ${error.message}`, {
      error: error.stack,
      runId,
      executionId
    });

    return {
      success: false,
      message: `Error creating run report: ${error.message}`
    };
  }
}

/**
 * Get multiple run reports by run IDs in a single query (batch optimization)
 * Eliminates N+1 query problem when fetching reports for multiple runs
 *
 * @param runIds Array of run IDs
 * @returns Success status with reports map keyed by runId
 */
export async function getMultipleRunReportsByRunIds(
  runIds: string[]
): Promise<{
  success: boolean;
  reports?: { [runId: string]: any };
  message?: string;
}> {
  try {
    if (!runIds || runIds.length === 0) {
      return {
        success: true,
        reports: {}
      };
    }

    if (!runReportsCollection) throw new Error('runReportsCollection not available');

    // Find all reports for the given run IDs, sorted by creation date descending
    // to get the latest report for each run
    const allReports = await runReportsCollection
      .find({
        runId: { $in: runIds }
      })
      .sort({ createdAt: -1 })
      .toArray();

    // Group reports by runId and keep only the latest report for each run
    const reportsByRunId: { [runId: string]: any } = {};

    for (const report of allReports) {
      const runId = report.runId;
      
      // Keep only the latest report for each run (first one due to sorting)
      if (!reportsByRunId[runId]) {
        reportsByRunId[runId] = report;
      }
    }

    return {
      success: true,
      reports: reportsByRunId
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting multiple run reports: ${error.message}`, {
      error: error.stack,
      runIds
    });

    return {
      success: false,
      message: `Error getting multiple run reports: ${error.message}`
    };
  }
}

/**
 * SCENARIO-BASED CALCULATION FUNCTIONS
 * Single source of truth - scenarioStatuses instead of corrupted counters
 */

/**
 * Calculate test results from scenarioStatuses (replaces counter-based logic)
 * Multiple device aware: counts actual tests, not just scenarios
 */
function calculateResultsFromScenarios(scenarioStatuses: any[], totalScenarios?: number, runEnvironment?: any): any {
  const scenarios = scenarioStatuses || [];
  
  // For multiple device runs, total should be the actual number of tests (scenarios.length)
  // For fallback when no scenarios yet, use totalScenarios parameter
  let total = scenarios.length;
  
  // If no scenarioStatuses but we have totalScenarios, use it as total
  // This maintains backward compatibility while supporting multiple device counts
  if (total === 0 && totalScenarios) {
    total = totalScenarios;
  }

  const results = {
    total,
    queued: 0,
    running: 0,
    passed: 0,
    failed: 0,
    stopped: 0,
    completed: 0 // For UI compatibility
  };

  // Count individual tests by status (each scenarioStatus represents one test)
  scenarios.forEach(scenario => {
    switch (scenario.status) {
      case 'queued':
        results.queued++;
        break;
      case 'running':
        results.running++;
        break;
      case 'passed':
        results.passed++;
        results.completed++; // UI compatibility
        break;
      case 'failed':
        results.failed++;
        break;
      case 'stopped':
        results.stopped++;
        break;
    }
  });

  return results;
}

/**
 * Determine run status from scenarioStatuses (single source of truth)
 */
function determineStatusFromScenarios(scenarioStatuses: any[]): string {
  const scenarios = scenarioStatuses || [];
  
  if (scenarios.length === 0) {
    return 'queued';
  }

  const statusCounts = {
    queued: scenarios.filter(s => s.status === 'queued').length,
    running: scenarios.filter(s => s.status === 'running').length,
    passed: scenarios.filter(s => s.status === 'passed').length,
    failed: scenarios.filter(s => s.status === 'failed').length,
    stopped: scenarios.filter(s => s.status === 'stopped').length
  };

  const total = scenarios.length;
  const completed = statusCounts.passed + statusCounts.failed + statusCounts.stopped;
  
  // Status determination logic
  if (statusCounts.running > 0) {
    return 'running';
  }
  
  if (completed < total) {
    return 'running'; // Still has queued scenarios
  }
  
  // All scenarios completed
  if (statusCounts.stopped > 0 && statusCounts.stopped === total) {
    return 'stopped'; // All stopped
  }
  
  if (statusCounts.failed > 0 && (statusCounts.failed + statusCounts.stopped) === total) {
    return 'failed'; // All failed/stopped
  }
  
  if (statusCounts.passed > 0 && statusCounts.passed === total) {
    return 'completed'; // All passed
  }
  
  if (statusCounts.failed > 0 || statusCounts.stopped > 0) {
    return 'partial'; // Mixed results
  }
  
  return 'completed'; // Fallback
}

/**
 * Calculate progress percentage from scenarioStatuses
 */
function calculateProgressFromScenarios(scenarioStatuses: any[], totalScenarios?: number): number {
  const scenarios = scenarioStatuses || [];
  const total = totalScenarios || scenarios.length;
  
  if (total === 0) return 0;
  
  const completed = scenarios.filter(s => 
    ['passed', 'failed', 'stopped'].includes(s.status)
  ).length;
  
  return Math.round((completed / total) * 100);
}

/**
 * Check if run is completed based on scenarioStatuses
 */
function isRunCompletedFromScenarios(scenarioStatuses: any[], totalScenarios?: number): boolean {
  const scenarios = scenarioStatuses || [];
  const total = totalScenarios || scenarios.length;
  
  if (total === 0) return false;
  
  const completed = scenarios.filter(s => 
    ['passed', 'failed', 'stopped'].includes(s.status)
  ).length;
  
  return completed >= total;
}

/**
 * Sanitize environment settings to remove sensitive data before storing in test reports
 * @param environmentSettings Raw environment settings that may contain sensitive data
 * @returns Sanitized environment settings safe for storage
 */
function sanitizeEnvironmentSettings(environmentSettings: any): any {
  if (!environmentSettings || typeof environmentSettings !== 'object') {
    return environmentSettings;
  }

  // Deep clone to avoid modifying the original object
  const sanitized = JSON.parse(JSON.stringify(environmentSettings));

  // Remove sensitive SauceLabs credentials
  if (sanitized.sauceLabs) {
    delete sanitized.sauceLabs.accessKey;
    delete sanitized.sauceLabs.username;
  }

  // Remove sensitive Testinium credentials
  if (sanitized.testinium) {
    delete sanitized.testinium.clientId;
    delete sanitized.testinium.clientSecret;
  }

  // Remove sensitive AI model configuration
  if (sanitized.aiModelConfig) {
    delete sanitized.aiModelConfig.OPENAI_API_KEY;
    delete sanitized.aiModelConfig.OPENAI_BASE_URL;
    // Keep only non-sensitive fields like model name
    if (sanitized.aiModelConfig.MIDSCENE_MODEL_NAME) {
      sanitized.aiModelConfig = {
        MIDSCENE_MODEL_NAME: sanitized.aiModelConfig.MIDSCENE_MODEL_NAME
      };
    } else {
      delete sanitized.aiModelConfig;
    }
  }

  // Remove sensitive proxy credentials
  if (sanitized.proxy) {
    delete sanitized.proxy.username;
    delete sanitized.proxy.password;
  }

  logger.debug('AtomicReportService: Sanitized environment settings by removing sensitive credentials');
  
  return sanitized;
}

/**
 * Remove redundant fields from test report data to reduce storage size
 * @param reportData The test report data that may contain redundant fields
 * @returns Clean report data with redundant fields removed
 */
function removeRedundantFields(reportData: any): any {
  if (!reportData || typeof reportData !== 'object') {
    return reportData;
  }

  const cleaned = { ...reportData };

  // Remove redundant fields identified in the requirements
  // Keep timestamp, remove date if they are the same
  if (cleaned.timestamp && cleaned.date && cleaned.timestamp === cleaned.date) {
    delete cleaned.date;
    logger.debug('AtomicReportService: Removed redundant date field (same as timestamp)');
  }

  // Remove redundant executedUser if it's the same as userId
  if (cleaned.executedUser && cleaned.userId && cleaned.executedUser === cleaned.userId) {
    delete cleaned.executedUser;
    logger.debug('AtomicReportService: Removed redundant executedUser field (same as userId)');
  }

  // Remove redundant scenario fields if they have the same value
  if (cleaned.scenarioName && cleaned.scenarioTitle && cleaned.name) {
    if (cleaned.scenarioName === cleaned.name && cleaned.scenarioTitle === cleaned.name) {
      // Keep only name, remove the others
      delete cleaned.scenarioName;
      delete cleaned.scenarioTitle;
      logger.debug('AtomicReportService: Removed redundant scenarioName and scenarioTitle fields (same as name)');
    } else if (cleaned.scenarioName === cleaned.scenarioTitle) {
      // Keep scenarioName, remove scenarioTitle
      delete cleaned.scenarioTitle;
      logger.debug('AtomicReportService: Removed redundant scenarioTitle field (same as scenarioName)');
    }
  }

  // Remove empty arrays that are not useful for storage
  if (Array.isArray(cleaned.logs) && cleaned.logs.length === 0) {
    delete cleaned.logs;
    logger.debug('AtomicReportService: Removed empty logs array');
  }

  if (Array.isArray(cleaned.steps) && cleaned.steps.length === 0) {
    // Only remove empty steps array if there's no meaningful content
    delete cleaned.steps;
    logger.debug('AtomicReportService: Removed empty steps array');
  }

  // Remove unused testinium configuration if all fields are empty
  if (cleaned.environmentSettings?.testinium) {
    const testinium = cleaned.environmentSettings.testinium;
    const hasContent = testinium.apiUrl || testinium.clientId || testinium.clientSecret || 
                      testinium.issuerUri || (testinium.selectedDevices && testinium.selectedDevices.length > 0);
    
    if (!hasContent) {
      delete cleaned.environmentSettings.testinium;
      logger.debug('AtomicReportService: Removed empty testinium configuration');
    }
  }

  return cleaned;
}

/**
 * Update run report with Zephyr Scale test cycle information
 *
 * @param runId Run ID
 * @param zephyrScaleInfo Zephyr Scale test cycle information
 * @returns Success status
 */
export async function updateRunReportZephyrScale(
  runId: string,
  zephyrScaleInfo: {
    testCycleId: string;
    testCycleUrl: string;
    testCycleKey?: string;
    projectKey?: string;
    createdAt: Date;
  }
): Promise<{ success: boolean; message?: string }> {
  try {
    logger.info(`AtomicReportService: Updating run report ${runId} with Zephyr Scale test cycle info`);

    // Get the current run report
    const reportResult = await getRunReportByRunId(runId);

    if (!reportResult.success) {
      return {
        success: false,
        message: reportResult.message
      };
    }

    // Update with Zephyr Scale information
    const updateData = {
      zephyrscaleTestCycleId: zephyrScaleInfo.testCycleId,
      zephyrscaleTestCycleLink: zephyrScaleInfo.testCycleUrl,
      zephyrscaleTestCycleKey: zephyrScaleInfo.testCycleKey,
      zephyrscaleProjectKey: zephyrScaleInfo.projectKey,
      zephyrscaleCreatedAt: zephyrScaleInfo.createdAt,
      updatedAt: new Date()
    };

    // Ensure database connection
    if (!runReportsCollection) {
      await ensureMongoDBConnection();
      if (!runReportsCollection) {
        return {
          success: false,
          message: "Database connection not available"
        };
      }
    }

    const updateResult = await runReportsCollection.updateOne(
      { runId },
      { $set: updateData },
      { upsert: true }
    );

    if (updateResult.acknowledged) {
      logger.info(`AtomicReportService: Updated run report ${runId} with Zephyr Scale test cycle info`);
      return {
        success: true,
        message: "Run report updated with Zephyr Scale test cycle information"
      };
    } else {
      return {
        success: false,
        message: "Failed to update run report with Zephyr Scale test cycle information"
      };
    }
  } catch (error: any) {
    logger.error(`AtomicReportService: Error updating run report with Zephyr Scale info: ${error.message}`, {
      error: error.stack,
      runId,
      zephyrScaleInfo
    });

    return {
      success: false,
      message: `Error updating run report: ${error.message}`
    };
  }
}

/**
 * Get Zephyr Scale information for a run report
 *
 * @param runId Run ID
 * @returns Success status with Zephyr Scale info
 */
export async function getRunReportZephyrScaleInfo(
  runId: string
): Promise<{
  success: boolean;
  zephyrscaleTestCycleId?: string;
  zephyrscaleTestCycleLink?: string;
  zephyrscaleTestCycleKey?: string;
  zephyrscaleProjectKey?: string;
  message?: string;
}> {
  try {
    logger.info(`AtomicReportService: Getting Zephyr Scale info for run ${runId}`);

    // Get the run report
    const reportResult = await getRunReportByRunId(runId);

    if (!reportResult.success) {
      return {
        success: false,
        message: reportResult.message
      };
    }

    const report = reportResult.report;

    // Return Zephyr Scale info
    return {
      success: true,
      zephyrscaleTestCycleId: report.zephyrscaleTestCycleId,
      zephyrscaleTestCycleLink: report.zephyrscaleTestCycleLink,
      zephyrscaleTestCycleKey: report.zephyrscaleTestCycleKey,
      zephyrscaleProjectKey: report.zephyrscaleProjectKey
    };
  } catch (error: any) {
    logger.error(`AtomicReportService: Error getting Zephyr Scale info: ${error.message}`, {
      error: error.stack,
      runId
    });

    return {
      success: false,
      message: `Error getting Zephyr Scale info: ${error.message}`
    };
  }
}
