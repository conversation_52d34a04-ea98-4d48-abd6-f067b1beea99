"use client"

import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  Chrome,
  LayoutGrid,
  LineChart,
  Network,
  HelpCircle,
  Eye,
  Monitor,
  Smartphone,
  Tablet,
  Gauge,
  Sparkles,
  Video,
  Globe,
  Lock
} from "lucide-react"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { AIModel, NetworkSpeed, ProxyType, ViewportSize } from "@/models/scenario"
import { useState } from "react"
import Image from "next/image"

export function EnvironmentSettings() {
  const { formData, updateFormField } = useUnifiedScenarioContext()

  // Helper function to update nested environment fields
  const updateNestedField = (path: string, value: any) => {
    const pathParts = path.split('.')
    const newEnvironment = { ...formData.environment }

    let current = newEnvironment
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i]
      if (!current[part]) {
        current[part] = {}
      }
      current = current[part]
    }

    current[pathParts[pathParts.length - 1]] = value
    updateFormField('environment', newEnvironment)
  }
  const [showCustomSizes, setShowCustomSizes] = useState(
    formData.environment?.viewportSize === "Custom"
  )
  const [showProxySettings, setShowProxySettings] = useState(
    formData.environment?.proxy?.enabled || false
  )

  // Handle viewport change
  const handleViewportChange = (value: ViewportSize) => {
    updateNestedField("environment", "viewportSize", value)
    setShowCustomSizes(value === "Custom")
  }

  // Handle custom viewport changes
  const handleCustomViewportChange = (dimension: "width" | "height", value: string) => {
    const numValue = parseInt(value, 10)
    if (!isNaN(numValue)) {
      const customViewport = formData.environment?.customViewport || { width: 1280, height: 720 }
      updateNestedField("environment", "customViewport", {
        ...customViewport,
        [dimension]: numValue
      })
    }
  }
  
  // Handle proxy enabled toggle
  const handleProxyEnabledChange = (enabled: boolean) => {
    updateNestedField("environment", "proxy", {
      ...(formData.environment?.proxy || {}),
      enabled
    })
    setShowProxySettings(enabled)
  }
  
  // Handle proxy type change
  const handleProxyTypeChange = (type: ProxyType) => {
    updateNestedField("environment", "proxy", {
      ...(formData.environment?.proxy || {}),
      type
    })
  }
  
  // Handle proxy field updates
  const handleProxyFieldChange = (field: string, value: string) => {
    const updates = { ...(formData.environment?.proxy || {}) }
    
    if (field === "port") {
      const portNumber = parseInt(value, 10)
      if (!isNaN(portNumber)) {
        updates.port = portNumber
      }
    } else if (field === "host") {
      updates.host = value
    } else if (field === "username") {
      updates.username = value
    } else if (field === "password") {
      updates.password = value
    }
    
    updateNestedField("environment", "proxy", updates)
  }
  
  // Ensure environment values have defaults if null
  if (!formData.environment) {
    updateNestedField("environment", "browser", "chrome" as "chrome" | "firefox")
    updateNestedField("environment", "viewportSize", "Desktop" as ViewportSize)
    updateNestedField("environment", "customViewport", { width: 1280, height: 720 })
    updateNestedField("environment", "networkSpeed", "Normal" as NetworkSpeed)
    updateNestedField("environment", "aiModel", "GPT-4" as AIModel)
    updateNestedField("environment", "proxy", {
      enabled: false,
      type: "HTTP" as ProxyType,
      host: "",
      port: 0,
      username: "",
      password: ""
    })
    return <div>Loading environment settings...</div>
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card className="overflow-hidden border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
          <CardTitle>Browser Settings</CardTitle>
          <CardDescription>Configure the browser environment for your test</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            <div>
              <Label className="text-base mb-3 block">Browser</Label>
              <RadioGroup 
                value={formData.environment?.browser || "chrome"} 
                onValueChange={(value) => updateNestedField("environment", "browser", value as "chrome" | "firefox")}
                className="grid grid-cols-2 gap-4"
              >
                <div>
                  <RadioGroupItem 
                    value="chrome" 
                    id="browser-chrome" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="browser-chrome"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20 h-[158px]"
                  >
                    <Chrome className="mb-3 h-10 w-10 text-blue-500" />
                    <div className="space-y-1 text-center">
                      <p className="text-base font-medium">Google Chrome</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Recommended browser</p>
                    </div>
                  </Label>
                </div>
                
                <div>
                  <RadioGroupItem 
                    value="firefox" 
                    id="browser-firefox" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="browser-firefox"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20 h-[158px]"
                  >
                    <div className="mb-3 h-10 w-10 relative">
                      <img 
                        src="/images/firefox-logo.png" 
                        alt="Firefox Logo" 
                        className="h-10 w-10 object-contain"
                      />
                    </div>
                    <div className="space-y-1 text-center">
                      <p className="text-base font-medium">Mozilla Firefox</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Alternative browser</p>
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>
            
            {/* Viewport Size Settings */}
            <div>
              <Label className="text-base mb-3 block">Viewport Size</Label>
              <RadioGroup 
                value={formData.environment?.viewportSize || "Desktop"} 
                onValueChange={(value) => handleViewportChange(value as ViewportSize)}
                className="grid grid-cols-4 gap-3"
              >
                <div>
                  <RadioGroupItem 
                    value="Mobile" 
                    id="viewport-mobile" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="viewport-mobile"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
                  >
                    <Smartphone className="mb-2 h-6 w-6 text-indigo-500" />
                    <div className="text-center">
                      <p className="text-sm font-medium">Mobile</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">375 x 667</p>
                    </div>
                  </Label>
                </div>
                
                <div>
                  <RadioGroupItem 
                    value="Tablet" 
                    id="viewport-tablet" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="viewport-tablet"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
                  >
                    <Tablet className="mb-2 h-6 w-6 text-purple-500" />
                    <div className="text-center">
                      <p className="text-sm font-medium">Tablet</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">768 x 1024</p>
                    </div>
                  </Label>
                </div>
                
                <div>
                  <RadioGroupItem 
                    value="Desktop" 
                    id="viewport-desktop" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="viewport-desktop"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
                  >
                    <Monitor className="mb-2 h-6 w-6 text-blue-500" />
                    <div className="text-center">
                      <p className="text-sm font-medium">Desktop</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">1280 x 720</p>
                    </div>
                  </Label>
                </div>
                
                <div>
                  <RadioGroupItem 
                    value="Custom" 
                    id="viewport-custom" 
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="viewport-custom"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-3 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-indigo-500 peer-data-[state=checked]:bg-indigo-50 [&:has([data-state=checked])]:border-indigo-500 [&:has([data-state=checked])]:bg-indigo-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-indigo-950/20 dark:[&:has([data-state=checked])]:bg-indigo-950/20"
                  >
                    <LayoutGrid className="mb-2 h-6 w-6 text-amber-500" />
                    <div className="text-center">
                      <p className="text-sm font-medium">Custom</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Set size</p>
                    </div>
                  </Label>
                </div>
              </RadioGroup>
              
              {/* Custom Viewport Size Inputs */}
              {showCustomSizes && (
                <div className="flex items-center gap-4 mt-3">
                  <div className="flex-1">
                    <Label htmlFor="viewport-width" className="text-sm mb-1 block">Width (px)</Label>
                    <Input
                      id="viewport-width"
                      type="number"
                      min="320"
                      max="3840"
                      value={formData.environment?.customViewport?.width || 1280}
                      onChange={(e) => handleCustomViewportChange("width", e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="flex-1">
                    <Label htmlFor="viewport-height" className="text-sm mb-1 block">Height (px)</Label>
                    <Input
                      id="viewport-height"
                      type="number"
                      min="240"
                      max="2160"
                      value={formData.environment?.customViewport?.height || 720}
                      onChange={(e) => handleCustomViewportChange("height", e.target.value)}
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </div>
            
            {/* Network Speed Settings */}
            <div>
              <Label htmlFor="network-speed" className="text-base mb-2 block">Network Speed</Label>
              <div className="flex items-center gap-2">
                <Gauge className="h-5 w-5 text-gray-500" />
                <Select
                  value={formData.environment?.networkSpeed || "Normal"}
                  onValueChange={(value) => updateNestedField("environment", "networkSpeed", value as NetworkSpeed)}
                >
                  <SelectTrigger id="network-speed" className="w-full">
                    <SelectValue placeholder="Select network speed" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Normal">Normal (No throttling)</SelectItem>
                    <SelectItem value="Fast 4G">Fast 4G</SelectItem>
                    <SelectItem value="Slow 4G">Slow 4G</SelectItem>
                    <SelectItem value="Fast 3G">Fast 3G</SelectItem>
                    <SelectItem value="Slow 3G">Slow 3G</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* AI Model Selection */}
            <div>
              <Label htmlFor="ai-model" className="text-base mb-2 block">AI Model</Label>
              <div className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-amber-500" />
                <Select
                  value={formData.environment?.aiModel || "GPT-4"}
                  onValueChange={(value) => updateNestedField("environment", "aiModel", value as AIModel)}
                >
                  <SelectTrigger id="ai-model" className="w-full">
                    <SelectValue placeholder="Select AI model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GPT-4">GPT-4 (Recommended)</SelectItem>
                    <SelectItem value="GPT-3.5">GPT-3.5 (Faster)</SelectItem>
                    <SelectItem value="Claude">Claude</SelectItem>
                    <SelectItem value="Gemini">Gemini</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            {/* Proxy Settings Toggle */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="proxy-enabled" className="text-base">Proxy Settings</Label>
                <Switch
                  id="proxy-enabled"
                  checked={formData.environment?.proxy?.enabled || false}
                  onCheckedChange={handleProxyEnabledChange}
                />
              </div>
              
              {/* Expanded Proxy Settings */}
              {showProxySettings && (
                <div className="space-y-4 mt-4 p-4 border border-gray-200 rounded-md dark:border-gray-800">
                  <div className="space-y-3">
                    {/* Proxy Type */}
                    <div>
                      <Label htmlFor="proxy-type" className="text-sm block mb-1">Proxy Type</Label>
                      <Select 
                        value={formData.environment?.proxy?.type || "HTTP"}
                        onValueChange={(value) => handleProxyTypeChange(value as ProxyType)}
                      >
                        <SelectTrigger id="proxy-type" className="w-full">
                          <SelectValue placeholder="Select proxy type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="HTTP">HTTP</SelectItem>
                          <SelectItem value="SOCKS">SOCKS</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Proxy Host and Port */}
                    <div className="flex gap-3">
                      <div className="flex-1">
                        <Label htmlFor="proxy-host" className="text-sm block mb-1">Host</Label>
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-gray-500" />
                          <Input
                            id="proxy-host"
                            placeholder="proxy.example.com"
                            value={formData.environment?.proxy?.host || ""}
                            onChange={(e) => handleProxyFieldChange("host", e.target.value)}
                            className="flex-1"
                          />
                        </div>
                      </div>
                      <div className="w-1/3">
                        <Label htmlFor="proxy-port" className="text-sm block mb-1">Port</Label>
                        <Input
                          id="proxy-port"
                          type="number"
                          placeholder="8080"
                          value={formData.environment?.proxy?.port || 0}
                          onChange={(e) => handleProxyFieldChange("port", e.target.value)}
                        />
                      </div>
                    </div>
                    
                    {/* Authentication Section */}
                    <div className="pt-2 border-t border-gray-100 dark:border-gray-800">
                      <div className="flex items-center mb-3">
                        <Lock className="h-4 w-4 text-gray-500 mr-1" />
                        <span className="text-sm font-medium">Authentication (if required)</span>
                      </div>
                      
                      <div className="flex gap-3">
                        <div className="flex-1">
                          <Label htmlFor="proxy-username" className="text-sm block mb-1">Username</Label>
                          <Input
                            id="proxy-username"
                            placeholder="Username"
                            value={formData.environment?.proxy?.username || ""}
                            onChange={(e) => handleProxyFieldChange("username", e.target.value)}
                          />
                        </div>
                        <div className="flex-1">
                          <Label htmlFor="proxy-password" className="text-sm block mb-1">Password</Label>
                          <Input
                            id="proxy-password"
                            type="password"
                            placeholder="Password"
                            value={formData.environment?.proxy?.password || ""}
                            onChange={(e) => handleProxyFieldChange("password", e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="overflow-hidden border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
          <CardTitle>Report Settings</CardTitle>
          <CardDescription>Select what data to collect during test execution</CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          {/* Media Capture Options */}
          <div className="space-y-3 mb-4 pb-4 border-b border-gray-200 dark:border-gray-800">
            <h3 className="text-base font-medium">Media Capture</h3>
            
            {/* Screenshots Option */}
            <div className="flex items-center justify-between space-x-2">
              <div className="flex flex-1 items-start gap-3">
                <div className="rounded-md bg-indigo-100 p-2 dark:bg-indigo-900/30">
                  <Eye className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <Label htmlFor="take-screenshots" className="text-base block mb-0.5">
                    Take Screenshots
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Capture screenshots at each test step
                  </p>
                </div>
              </div>
              <Switch
                id="take-screenshots"
                checked={formData.reporting?.takeScreenshots || false}
                onCheckedChange={(checked) => updateNestedField("reporting", "takeScreenshots", checked)}
              />
            </div>
            
            {/* Video Recording Option */}
            <div className="flex items-center justify-between space-x-2">
              <div className="flex flex-1 items-start gap-3">
                <div className="rounded-md bg-red-100 p-2 dark:bg-red-900/30">
                  <Video className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <Label htmlFor="take-videos" className="text-base block mb-0.5">
                    Record Video
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Record full video of the test execution
                  </p>
                </div>
              </div>
              <Switch
                id="take-videos"
                checked={formData.reporting?.takeVideos || false}
                onCheckedChange={(checked) => updateNestedField("reporting", "takeVideos", checked)}
              />
            </div>
          </div>
          
          <div className="grid gap-4">
            {/* Page Metrics */}
            <div className="flex items-center justify-between space-x-2 py-2">
              <div className="flex flex-1 items-start gap-3">
                <div className="rounded-md bg-blue-100 p-2 dark:bg-blue-900/30">
                  <LineChart className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <Label htmlFor="page-metrics" className="text-base block mb-0.5">
                    Page Metrics
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Collect core web vitals and page performance data
                  </p>
                </div>
              </div>
              <Switch
                id="page-metrics"
                checked={formData.reporting?.pageMetrics || false}
                onCheckedChange={(checked) => updateNestedField("reporting", "pageMetrics", checked)}
              />
            </div>
            
            {/* Network Data */}
            <div className="flex items-center justify-between space-x-2 py-2">
              <div className="flex flex-1 items-start gap-3">
                <div className="rounded-md bg-purple-100 p-2 dark:bg-purple-900/30">
                  <Network className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <Label htmlFor="network-data" className="text-base block mb-0.5">
                    Network Data
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Record all network requests and responses
                  </p>
                </div>
              </div>
              <Switch
                id="network-data"
                checked={formData.reporting?.networkData || false}
                onCheckedChange={(checked) => updateNestedField("reporting", "networkData", checked)}
              />
            </div>
            
            {/* Tracing Data */}
            <div className="flex items-center justify-between space-x-2 py-2">
              <div className="flex flex-1 items-start gap-3">
                <div className="rounded-md bg-amber-100 p-2 dark:bg-amber-900/30">
                  <HelpCircle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                </div>
                <div>
                  <Label htmlFor="tracing-data" className="text-base block mb-0.5">
                    Tracing Data
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Detailed execution tracing for debugging
                  </p>
                </div>
              </div>
              <Switch
                id="tracing-data"
                checked={formData.reporting?.tracingData || false}
                onCheckedChange={(checked) => updateNestedField("reporting", "tracingData", checked)}
              />
            </div>
            
            {/* Accessibility Data */}
            <div className="flex items-center justify-between space-x-2 py-2">
              <div className="flex flex-1 items-start gap-3">
                <div className="rounded-md bg-green-100 p-2 dark:bg-green-900/30">
                  <LayoutGrid className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <Label htmlFor="accessibility-data" className="text-base block mb-0.5">
                    Accessibility Data
                  </Label>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Test for accessibility compliance issues
                  </p>
                </div>
              </div>
              <Switch
                id="accessibility-data"
                checked={formData.reporting?.accessibilityData || false}
                onCheckedChange={(checked) => updateNestedField("reporting", "accessibilityData", checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 