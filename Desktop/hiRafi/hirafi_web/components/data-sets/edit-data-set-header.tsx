"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Save, Loader2, Edit3 } from "lucide-react"

interface EditDataSetHeaderProps {
  dataSetName: string
  onSave: () => void
  onBack: () => void
  isSaving: boolean
  canSave: boolean
}

export function EditDataSetHeader({
  dataSetName,
  onSave,
  onBack,
  isSaving,
  canSave
}: EditDataSetHeaderProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Use a safe disabled state that's consistent between SSR and client
  // During SSR, always disable the button to prevent hydration mismatch
  const isDisabled = !isClient || !canSave || isSaving

  return (
    <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-white/10 rounded-lg">
            <Edit3 className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">
              Edit Data Set
            </h1>
            <p className="text-white/80">
              {dataSetName ? `Editing "${dataSetName}"` : "Update your test data set"}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="bg-white/10 text-white border-white/20 hover:bg-white/20 hover:text-white"
            onClick={onBack}
            disabled={isSaving}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>

          <Button
            onClick={onSave}
            disabled={isDisabled}
            className="bg-white text-indigo-600 hover:bg-gray-100 font-medium"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Update Data Set
              </>
            )}
          </Button>
        </div>
      </div>
    </header>
  )
}
