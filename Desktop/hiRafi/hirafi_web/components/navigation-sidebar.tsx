"use client"

import type React from "react"
import { Home, FileText, BarChart3, <PERSON>, <PERSON>tings, PanelLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { ModeToggle } from "@/components/mode-toggle"

interface NavigationSidebarProps {
  collapsed: boolean
  onToggleCollapse: () => void
}

export function NavigationSidebar({ collapsed, onToggleCollapse }: NavigationSidebarProps) {
  return (
    <div
      className={cn(
        "border-r border-gray-200 dark:border-gray-800 flex flex-col bg-gray-50 dark:bg-gray-900 transition-all duration-300 ease-in-out",
        collapsed ? "w-16" : "w-60",
      )}
    >
      <div className="p-4 border-b border-gray-200 dark:border-gray-800 bg-gradient-to-r from-indigo-600 to-violet-600 text-white flex items-center justify-between">
        {!collapsed && <h2 className="text-xl font-bold">HiRafi</h2>}
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleCollapse}
          className="h-8 w-8 rounded-full text-white hover:bg-white/20"
        >
          <PanelLeft className="h-5 w-5" />
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          <NavItem icon={<Home className="h-5 w-5" />} label="Dashboard" collapsed={collapsed} />
          <NavItem icon={<FileText className="h-5 w-5" />} label="Scenarios" collapsed={collapsed} active />
          <NavItem icon={<BarChart3 className="h-5 w-5" />} label="Reports" collapsed={collapsed} />
          <NavItem icon={<Users className="h-5 w-5" />} label="Team" collapsed={collapsed} />
          <NavItem icon={<Settings className="h-5 w-5" />} label="Settings" collapsed={collapsed} />
        </div>
      </div>
      
      <div className="p-3 border-t border-gray-200 dark:border-gray-800">
        <div className={cn(
          "flex items-center", 
          collapsed 
            ? "justify-center" 
            : "justify-between px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-800/50"
        )}>
          <ModeToggle collapsed={collapsed} />
          {!collapsed && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {typeof window !== "undefined" && localStorage.getItem("theme") === "dark" ? "Dark Mode" : "Light Mode"}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

interface NavItemProps {
  icon: React.ReactNode
  label: string
  collapsed: boolean
  active?: boolean
}

function NavItem({ icon, label, collapsed, active }: NavItemProps) {
  return (
    <div
      className={cn(
        "flex items-center gap-2 px-3 py-3 rounded-md cursor-pointer transition-colors",
        active
          ? "bg-indigo-50 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300"
          : "text-gray-700 dark:text-gray-200 hover:bg-indigo-50 hover:text-indigo-700 dark:hover:bg-indigo-900/30 dark:hover:text-indigo-300",
      )}
    >
      {icon}
      {!collapsed && <span>{label}</span>}
    </div>
  )
}

