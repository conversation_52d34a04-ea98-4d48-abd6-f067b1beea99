"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import {
  Home,
  FileText,
  BarChart2,
  Settings,
  ChevronRight,
  PanelLeft,
  HelpCircle,
  Zap,
  Puzzle,
  FilePlus,
  LogOut,
  User,
  Github,
  PlayCircle,
  Plus,
  Activity,
  LineChart,
  Users,
  Calendar,
  Database,
  Smartphone
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { ModeToggle } from "@/components/mode-toggle"
import { useAuth } from "@/lib/api/auth"
import { usePermissions } from "@/hooks/usePermissions"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface SidebarProps {
  collapsed: boolean
  onToggleCollapse: () => void
}

// Mock plugins data
const plugins = [
  {
    id: "testrail",
    name: "TestRail",
    description: "Integrate with TestRail to sync test cases and results",
    active: false,
    color: "blue",
    icon: (className: string) => (
      <img
        src="/images/testrail-icon.svg"
        alt="TestRail"
        className={className}
        width="24"
        height="24"
      />
    ),
  },
  {
    id: "saucelabs",
    name: "SauceLabs",
    description: "Integrate with SauceLabs for mobile device testing",
    active: false,
    color: "orange",
    icon: (className: string) => (
      <img
        src="/images/sauce-labs.svg"
        alt="SauceLabs"
        className={className}
        width="24"
        height="24"
      />
    ),
  },
  {
    id: "testinium",
    name: "Testinium",
    description: "Integrate with Testinium for mobile device testing using DevicePark SDK",
    active: false,
    color: "blue",
    icon: (className: string) => (
      <img
        src="/images/testinium_logo.svg"
        alt="Testinium"
        className={className}
        width="24"
        height="24"
      />
    ),
  },
  {
    id: "jira",
    name: "Jira",
    description: "Connect with Jira to link test cases to issues",
    active: false,
    color: "indigo",
    icon: (className: string) => (
      <svg className={className} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.0001 1L1 11.9999L6.06064 17.0605L12.0001 11.1211L17.9395 17.0605L23.0001 11.9999L12.0001 1Z" fill="currentColor" />
        <path d="M12.0001 11.1211L6.06064 17.0605L12.0001 23.0001L17.9395 17.0606L12.0001 11.1211Z" fill="currentColor" opacity="0.8" />
      </svg>
    ),
  },
  {
    id: "github",
    name: "GitHub",
    description: "Integrate with GitHub to link test cases to repositories",
    active: false,
    color: "purple",
    icon: (className: string) => <Github className={className} />,
  },
]

export function Sidebar({ collapsed, onToggleCollapse }: SidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [localPlugins, setLocalPlugins] = useState(plugins)
  const { user, logout } = useAuth()
  const { canAccessPage, isAdminOrOwner } = usePermissions()
  const [localUser, setLocalUser] = useState<any>(null)
  const [currentTheme, setCurrentTheme] = useState<string | null>("dark")

  // Kullanıcının company_owner olup olmadığını kontrol et
  const isCompanyOwner = user?.accountType === 'company_owner'

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)

    // Eğer user bilgisi yoksa localStorage veya sessionStorage'dan kullanıcı bilgilerini al
    if (!user?.name) {
      try {
        const storedUser = localStorage.getItem("user") || sessionStorage.getItem("user")
        if (storedUser) {
          setLocalUser(JSON.parse(storedUser))
        }
      } catch (error) {
        console.error("Error retrieving user from storage:", error)
      }
    }

    // Get initial theme
    const savedTheme = localStorage.getItem("theme")
    setCurrentTheme(savedTheme)

    // Listen for theme changes
    const handleStorageChange = () => {
      setCurrentTheme(localStorage.getItem("theme"))
    }
    window.addEventListener("storage", handleStorageChange)

    // Custom event for theme changes within the app
    const handleThemeChange = (e: any) => {
      setCurrentTheme(e.detail.theme)
    }
    window.addEventListener("themeChange", handleThemeChange)

    return () => {
      window.removeEventListener("storage", handleStorageChange)
      window.removeEventListener("themeChange", handleThemeChange)
    }
  }, [user])

  const handlePluginClick = (pluginId: string) => {
    router.push(`/plugins/${pluginId}`)
  }

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    // Önce auth context'ten gelen kullanıcı bilgisini kontrol et
    if (user?.name) {
      const nameParts = user.name.split(" ");
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return user.name.substring(0, 2).toUpperCase();
    }

    // Context'te yoksa lokalden al
    if (localUser?.name) {
      const nameParts = localUser.name.split(" ");
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return localUser.name.substring(0, 2).toUpperCase();
    }

    return "U";
  };

  if (!mounted) return null

  return (
    <div
      className={cn(
        "flex flex-col h-screen bg-gradient-to-b from-gray-900 to-gray-950 text-white border-r border-gray-800 transition-all duration-300 ease-in-out z-20",
        collapsed ? "w-[70px]" : "w-[260px]",
      )}
    >
      <div className={cn(
        "flex items-center justify-between border-b border-gray-800",
        collapsed ? "p-3" : "p-4"
      )}>
        {collapsed ? (
          <div className="flex items-center justify-center w-10 h-8 ml-2">
            <h1 className="font-var-orbitron text-lg font-bold gradient-text bg-gradient-to-r from-blue-400 via-purple-500 to-indigo-400">H</h1>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center ml-3"
          >
            <h1 className="font-var-orbitron text-2xl tracking-wide font-bold gradient-text bg-gradient-to-r from-blue-400 via-purple-500 to-indigo-400">HiRafi</h1>
          </motion.div>
        )}

        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleCollapse}
          className={cn(
            "h-8 w-8 rounded-full text-gray-400 hover:text-white hover:bg-gray-800",
            collapsed && "ml-0"
          )}
        >
          <PanelLeft className={cn("h-5 w-5 transition-transform", collapsed && "rotate-180")} />
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto py-4 px-3">
        <nav className="space-y-1">
          {/* Dashboard - Herkes erişebilir */}
          <NavItem
            href="/dashboard"
            icon={<Home className="h-5 w-5" />}
            label="Dashboard"
            collapsed={collapsed}
            active={pathname === "/dashboard" || pathname === "/"}
          />

          {/* Scenarios - Scenario view permission gerekli */}
          {canAccessPage('SCENARIOS') && (
            <NavItem
              href="/scenarios"
              icon={<FileText className="h-5 w-5" />}
              label="Scenarios"
              collapsed={collapsed}
              active={pathname === "/scenarios" || pathname === "/page" || pathname === "/scenarios/new"}
            />
          )}

          {/* Runs - Run view permission gerekli */}
          {canAccessPage('RUNS') && (
            <NavItem
              href="/runs"
              icon={<PlayCircle className="h-5 w-5" />}
              label="Runs"
              collapsed={collapsed}
              active={pathname?.startsWith("/runs")}
            />
          )}

          {/* Reports - Report view permission gerekli */}
          {canAccessPage('REPORTS') && (
            <NavItem
              href="/reports"
              icon={<Activity className="h-5 w-5" />}
              label="Reports"
              collapsed={collapsed}
              active={pathname?.startsWith("/reports")}
            />
          )}

          {/* Schedule - Schedule view permission gerekli */}
          {canAccessPage('SCHEDULE') && (
            <NavItem
              href="/schedule"
              icon={<Calendar className="h-5 w-5" />}
              label="Schedule"
              collapsed={collapsed}
              active={pathname?.startsWith("/schedule")}
            />
          )}

          {/* Test Data - TestData view permission gerekli */}
          {canAccessPage('TEST_DATA') && (
            <NavItem
              href="/test-data"
              icon={<Database className="h-5 w-5" />}
              label="Test Data"
              collapsed={collapsed}
              active={pathname?.startsWith("/test-data")}
            />
          )}

          {/* Ayraç */}
          <div className="pt-2 pb-2">
            <div className={cn("mx-3 border-t border-gray-800", collapsed ? "w-[calc(100%-24px)]" : "w-full")} />
          </div>

          {/* Organization Section - Team view permission gerekli */}
          {canAccessPage('TEAMS') && (
            <NavGroup label="Organization" collapsed={collapsed}>
              <NavItem
                href="/team"
                icon={<Users className="h-5 w-5" />}
                label="Teams"
                collapsed={collapsed}
                active={pathname?.startsWith("/team")}
                indent
              />
            </NavGroup>
          )}

          {/* Alt ayraç */}
          <div className="pt-2 pb-2">
            <div className={cn("mx-3 border-t border-gray-800", collapsed ? "w-[calc(100%-24px)]" : "w-full")} />
          </div>

          {/* Plugins Section - Sadece company owner'lar için göster */}
          {isCompanyOwner && (
            <NavItem
              href="/plugins"
              icon={<Puzzle className="h-5 w-5" />}
              label="Plugins"
              collapsed={collapsed}
              active={pathname?.startsWith("/plugins")}
              rightElement={
                localPlugins.filter(p => p.active).length > 0 ? (
                  <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 border-0">
                    {localPlugins.filter(p => p.active).length}
                  </Badge>
                ) : null
              }
            />
          )}

          <NavItem
            href="/settings"
            icon={<Settings className="h-5 w-5" />}
            label="Settings"
            collapsed={collapsed}
            active={pathname === "/settings"}
          />
          <NavItem
            href="/help"
            icon={<HelpCircle className="h-5 w-5" />}
            label="Help & Support"
            collapsed={collapsed}
            active={pathname === "/help"}
          />
        </nav>
      </div>

      <div className="p-3 border-t border-gray-800">
        <div className={cn("mb-3", collapsed ? "flex justify-center" : "flex items-center justify-between px-3 py-2 rounded-lg bg-gray-800/50")}>
          <ModeToggle collapsed={collapsed} />
          {!collapsed && (
            <span className="text-sm text-gray-400">
              {currentTheme === "dark" ? "Dark Mode" : "Light Mode"}
            </span>
          )}
        </div>

        {/* User profile with dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div
              className={cn(
                "flex items-center gap-3 p-2 rounded-lg hover:bg-gray-800 transition-colors cursor-pointer",
                collapsed && "justify-center",
              )}
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder.svg?height=32&width=32" alt={user?.name || localUser?.name || "User"} />
                <AvatarFallback>{getUserInitials()}</AvatarFallback>
              </Avatar>

              <AnimatePresence initial={false}>
                {!collapsed && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="flex-1 overflow-hidden"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">{user?.name || localUser?.name || "User"}</span>
                      <span className="text-xs text-gray-400">{user?.email || localUser?.email || ""}</span>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 bg-gray-900 border border-gray-800 text-white">
            <div className="flex items-center gap-2 p-2 border-b border-gray-800">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder.svg?height=32&width=32" alt={user?.name || localUser?.name || "User"} />
                <AvatarFallback>{getUserInitials()}</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="font-medium text-sm truncate">{user?.name || localUser?.name || "User"}</span>
                <span className="text-xs text-gray-400 truncate">{user?.email || localUser?.email || ""}</span>
              </div>
            </div>
            <DropdownMenuItem onClick={() => router.push("/settings")} className="cursor-pointer flex items-center gap-2 text-gray-200 hover:text-white focus:text-white">
              <User className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push("/settings")} className="cursor-pointer flex items-center gap-2 text-gray-200 hover:text-white focus:text-white">
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-800" />
            <DropdownMenuItem onClick={logout} className="cursor-pointer flex items-center gap-2 text-gray-200 hover:text-white focus:text-white">
              <LogOut className="mr-2 h-4 w-4" />
              <span>Logout</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {collapsed && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="sr-only">User Profile</div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <div>
                  <p className="font-medium">{user?.name || localUser?.name || "User"}</p>
                  <p className="text-xs text-gray-400">{user?.email || localUser?.email || ""}</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  )
}

interface NavItemProps {
  href: string
  icon: React.ReactNode
  label: string
  collapsed: boolean
  active?: boolean
  badge?: string
  indent?: boolean
  onClick?: (e: React.MouseEvent) => void
  rightElement?: React.ReactNode
}

function NavItem({ href, icon, label, collapsed, active, badge, indent, onClick, rightElement }: NavItemProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <Link href={href} passHref onClick={onClick}>
          <TooltipTrigger asChild>
            <div
              className={cn(
                "flex items-center gap-3 py-2.5 rounded-lg transition-all duration-200 group",
                active
                  ? "bg-gradient-to-r from-indigo-600 to-indigo-500 text-white"
                  : "text-gray-400 hover:text-white hover:bg-gray-800",
                collapsed ? "justify-center px-2" : "px-4",
                indent && !collapsed && "pl-7",
              )}
            >
              <div className={cn("flex items-center justify-center", active && "text-white")}>{icon}</div>

              <AnimatePresence initial={false}>
                {!collapsed && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    className="flex-1 overflow-hidden whitespace-nowrap"
                  >
                    {label}
                  </motion.div>
                )}
              </AnimatePresence>

              {!collapsed && rightElement && <div className="ml-auto">{rightElement}</div>}

              {badge && !collapsed && (
                <Badge
                  className={cn(
                    "ml-auto text-xs",
                    active ? "bg-white/20 hover:bg-white/30 text-white" : "bg-gray-800 hover:bg-gray-700 text-gray-300",
                  )}
                >
                  {badge}
                </Badge>
              )}
            </div>
          </TooltipTrigger>
        </Link>
        {collapsed && (
          <TooltipContent side="right">
            <p>{label}</p>
            {badge && <Badge className="ml-1 text-xs">{badge}</Badge>}
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  )
}

interface NavGroupProps {
  label: string
  collapsed: boolean
  children: React.ReactNode
}

function NavGroup({ label, collapsed, children }: NavGroupProps) {
  const [open, setOpen] = useState(true)

  return (
    <div className="space-y-1">
      {!collapsed ? (
        <div
          className="flex items-center justify-between px-4 py-2 text-xs font-medium text-gray-400 cursor-pointer"
          onClick={() => setOpen(!open)}
        >
          <span>{label}</span>
          <ChevronRight className={cn("h-4 w-4 transition-transform", open && "rotate-90")} />
        </div>
      ) : (
        <div className="px-3 py-2 text-xs font-medium text-gray-500">
          <div className="w-5 h-0.5 bg-gray-700 rounded-full mx-auto" />
        </div>
      )}

      <AnimatePresence initial={false}>
        {(open || collapsed) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

