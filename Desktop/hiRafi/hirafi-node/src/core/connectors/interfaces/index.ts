/**
 * Connector Interfaces Module
 * Exports all connector interface definitions
 */

// Core interfaces
export * from './base-connector.interface.js';
export * from './http-connector.interface.js';
export * from './redis-connector.interface.js';
export * from './websocket-connector.interface.js';

// Service interfaces
export * from './connector-service.interface.js';
export * from './heartbeat-service.interface.js';
export * from './registration-service.interface.js';
export * from './connection-manager.interface.js';
export * from './message-handler.interface.js';

// Factory interfaces
export * from './connector-factory.interface.js';
export * from './connector-manager.interface.js';

// Registry interfaces
export * from './connector-service-registry.interface.js';

// Missing interface files - create minimal implementations
export interface IServiceRegistryConfig {
  nodeId?: string;
  hubUrl?: string;
  secretKey?: string;
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
}
