"use client"

import React, { useState, useEffect, create<PERSON>ontex<PERSON>, useContext, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { API_BASE_URL, AUTH_ENDPOINTS } from "./constants"
import { authApi } from "@/lib/api"
import { storeManager } from "@/lib/utils/store-management"

interface User {
  id: string
  email: string
  name?: string
  role?: string // Geriye dönük uyumluluk için
  teamRole?: string // Geriye dönük uyumluluk için
  roleId?: string // Yeni roleId field'ı
  accountType: string
  companyId?: string
  teamId?: string
  active?: boolean
  permissions?: Array<{
    resource: string
    action: string
  }>
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>
  logout: () => void
}

// Create context with default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  login: async () => ({ success: false }),
  logout: () => {},
})

export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is already logged in
    const checkAuth = async () => {
      // Öncelikle localStorage'dan token'ı al, yoksa sessionStorage'dan dene
      const token = localStorage.getItem("authToken") || sessionStorage.getItem("authToken")

      // Eğer token sessionStorage'da bulunursa, localStorage'a taşı
      if (!localStorage.getItem("authToken") && sessionStorage.getItem("authToken")) {
        localStorage.setItem("authToken", sessionStorage.getItem("authToken") || "")
        // User bilgisini de taşı
        if (sessionStorage.getItem("user")) {
          localStorage.setItem("user", sessionStorage.getItem("user") || "")
        }
      }
      if (!token) {
        setIsLoading(false)
        return
      }

      try {
        // Use the centralized API module
        const response = await authApi.validateToken(token);

        // With the updated fetch wrapper, we can access response directly
        const data = response;
        console.log("[AUTH] Token Validation Response:", response)

        // Backend artık data wrapper kullanıyor, bu yüzden data.data.user veya data.user kullanmalıyız
        const authData = data.data || data; // Geriye dönük uyumluluk için
        const user = authData.user || data.user; // Geriye dönük uyumluluk için

        if (data.success && user) {
          console.log("[AUTH] Setting user from validate:", user)
          console.log("[AUTH] User has teamId:", user.teamId)
          console.log("[AUTH] User has companyId:", user.companyId)
          console.log("[AUTH] User has teamRole:", user.teamRole)

          // Ensure user object has all required fields
          const userData = {
            id: user.id,
            email: user.email,
            name: user.name || "",
            role: user.role || user.accountType, // Geriye dönük uyumluluk için
            teamRole: user.teamRole || null,
            accountType: user.accountType || user.role, // Geriye dönük uyumluluk için
            teamId: user.teamId || null,
            companyId: user.companyId || null
          }

          setUser(userData)
          
          // Update store manager context
          storeManager.setUserContext({
            id: userData.id,
            companyId: userData.companyId,
            teamId: userData.teamId
          })

          // Also update the stored user data in localStorage
          localStorage.setItem("user", JSON.stringify(userData))
        } else {
          // Token is invalid, clear it
          console.warn("[AUTH] Token validation failed:", data)
          localStorage.removeItem("authToken")
          sessionStorage.removeItem("authToken")
          localStorage.removeItem("user")
          sessionStorage.removeItem("user")
        }
      } catch (error) {
        console.error("Auth validation error:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (email: string, password: string, rememberMe?: boolean) => {
    try {
      // Use the centralized API module
      const response = await authApi.login(email, password);
      console.log("[AUTH] Login Response:", response)

      // With the updated fetch wrapper, we can access both response.success and response.data.success
      // Use the direct response for cleaner code
      const data = response;

      if (!data.success) {
        // Kullanıcı aktif değilse özel bir hata mesajı göster
        if (data.error === 'Account is disabled') {
          return { success: false, error: "Hesabınız devre dışı bırakılmıştır. Lütfen yöneticinizle iletişime geçin." }
        }
        return { success: false, error: data.error || "Login failed" }
      }

      // Backend artık data wrapper kullanıyor, bu yüzden data.data.user ve data.data.token kullanmalıyız
      const authData = data.data || data; // Geriye dönük uyumluluk için
      const user = authData.user || data.user; // Geriye dönük uyumluluk için
      const token = authData.token || data.token; // Geriye dönük uyumluluk için

      // Check if teamId and companyId are present
      console.log("[AUTH] Login User Object:", user)
      console.log("[AUTH] User has teamId:", user.teamId)
      console.log("[AUTH] User has companyId:", user.companyId)
      console.log("[AUTH] User has teamRole:", user.teamRole)

      // Ensure user object includes teamId, companyId and teamRole
      const userData = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role || user.accountType, // Geriye dönük uyumluluk için
        teamRole: user.teamRole || null,
        accountType: user.accountType || user.role, // Geriye dönük uyumluluk için
        teamId: user.teamId || null,
        companyId: user.companyId || null
      }

      // Store token and user data in localStorage (always use localStorage instead of sessionStorage)
      localStorage.setItem("authToken", token)
      localStorage.setItem("token", token) // Permission sistemi için
      localStorage.setItem("user", JSON.stringify(userData))  // Save the enhanced user object

      setUser(userData)  // Use the enhanced user object
      
      // Update store manager context
      storeManager.setUserContext({
        id: userData.id,
        companyId: userData.companyId,
        teamId: userData.teamId
      })
      
      return { success: true }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, error: "Connection error. Please try again." }
    }
  }

  const logout = () => {
    // Clear all stores before logout
    storeManager.clearAllStores()
    storeManager.forceCleanLocalStorage()
    
    localStorage.removeItem("authToken")
    localStorage.removeItem("token") // Permission sistemi için
    sessionStorage.removeItem("authToken")
    localStorage.removeItem("user")
    sessionStorage.removeItem("user")
    setUser(null)
    router.push("/auth")
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  return useContext(AuthContext)
}

// Higher Order Component to protect routes
export function withAuth(Component: React.ComponentType) {
  return function WithAuth(props: any) {
    const { isAuthenticated, isLoading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!isLoading && !isAuthenticated) {
        router.push("/auth")
      }
    }, [isLoading, isAuthenticated, router])

    if (isLoading) {
      return <div>Loading...</div>
    }

    if (!isAuthenticated) {
      return null
    }

    return <Component {...props} />
  }
}

// Route guard component
export function RouteGuard({ children }: { children: ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth")
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  }

  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}

// Helper function for authenticated API requests
export async function fetchWithAuth(url: string, options: RequestInit = {}) {
  // Öncelikle localStorage'dan token'ı al, yoksa sessionStorage'dan dene
  const token = localStorage.getItem("authToken") || sessionStorage.getItem("authToken")

  // Eğer token sessionStorage'da bulunursa, localStorage'a taşı
  if (!localStorage.getItem("authToken") && sessionStorage.getItem("authToken")) {
    localStorage.setItem("authToken", sessionStorage.getItem("authToken") || "")
    // User bilgisini de taşı
    if (sessionStorage.getItem("user")) {
      localStorage.setItem("user", sessionStorage.getItem("user") || "")
    }
  }

  const headers = {
    ...options.headers,
    "Authorization": token ? `Bearer ${token}` : "",
    "Content-Type": options.method === "GET" ? undefined : (options.headers as any)?.["Content-Type"] || "application/json",
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    })

    // Yetkisiz erişimse login sayfasına yönlendir
    if (response.status === 401) {
      localStorage.removeItem("authToken")
      sessionStorage.removeItem("authToken")
      localStorage.removeItem("user")
      sessionStorage.removeItem("user")

      if (typeof window !== "undefined") {
        window.location.href = "/auth"
      }
    }

    return response
  } catch (error: any) {
    // AbortError'ları sessizce geç (bu normal bir durum, kullanıcı sayfayı değiştirdiğinde veya yeni istek yapıldığında oluşur)
    if (error.name === 'AbortError') {
      // console.log('Request aborted:', url)
      throw error
    }

    // Diğer hataları logla
    console.error("Fetch error:", error)
    throw error
  }
}
export { API_BASE_URL }

