import { toast } from "@/lib/utils/toast-utils";
import { fetchWithAuth, API_BASE_URL } from "@/lib/api/auth";

// API isteği için ortak fonksiyon
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {},
  abortSignal?: AbortSignal
): Promise<{ success: boolean; data?: T; error?: string; aborted?: boolean }> {
  try {
    const requestOptions: RequestInit = {
      ...options,
      signal: abortSignal
    };

    const response = await fetchWithAuth(`${API_BASE_URL}/${endpoint}`, requestOptions);
    const data = await response.json();

    if (!response.ok) {
      const errorMessage = data.error || `İstek başarısız oldu: ${response.status}`;

      // Yetki hatası için özel mesaj
      if (
        errorMessage.includes('Bu işlem için yetkiniz bulunmamaktadır') ||
        errorMessage.includes('Forbidden') ||
        errorMessage.includes('yetki') ||
        response.status === 403
      ) {
        const userFriendlyMessage = "Bu işlemi yapmak için gerekli yetkiniz bulunmamaktadır. Lütfen yöneticinize başvurun.";
        handleError("Yetki Hatası", userFriendlyMessage);
        return { success: false, error: userFriendlyMessage };
      }

      handleError("İşlem Başarısız", errorMessage);
      return { success: false, error: errorMessage };
    }

    return { success: true, data: data as T };
  } catch (err: any) {
    // AbortError'ı yoksay (kullanıcı isteği iptal etti)
    if (err.name === 'AbortError') {
      return { success: false, error: "İstek iptal edildi", aborted: true };
    }

    const errorMessage = err.message || "Bilinmeyen bir hata oluştu";
    handleError("İşlem Hatası", errorMessage);
    return { success: false, error: errorMessage };
  }
}

// Hata yönetimi için ortak fonksiyon
export function handleError(title: string, message: string, log: boolean = true): void {
  if (log) {
    console.error(`${title}: ${message}`);
  }

  toast.error(title, {
    description: message
  });
}

// Başarı mesajı için ortak fonksiyon
export function handleSuccess(title: string, message: string): void {
  toast.success(title, {
    description: message
  });
}

// Throttle için yardımcı fonksiyon
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  let lastCall = 0;
  return (...args: Parameters<T>): ReturnType<T> | undefined => {
    const now = Date.now();
    if (now - lastCall < wait) {
      return undefined;
    }
    lastCall = now;
    return func(...args);
  };
}

// AbortController yönetimi için yardımcı fonksiyon
export function createAbortController(): { controller: AbortController; abort: () => void } {
  const controller = new AbortController();
  return {
    controller,
    abort: () => controller.abort()
  };
}
