# Node Configuration
NODE_HUB_URL=http://localhost:3000
NODE_CAPABILITIES=web,android
NODE_VERSION=1.0.0
NODE_ENVIRONMENT=development

# Redis Configuration
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_PREFIX=test_node:

# Display Configuration (Headless)
DISPLAY=:0
RESOLUTION=1920x1080x24

# S3 Configuration
S3_ENABLED=false
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_ENDPOINT=
S3_FORCE_PATH_STYLE=true
S3_SCREENSHOT_BUCKET=screenshots
S3_REPORT_BUCKET=test-reports
S3_PUBLIC_URL=

# Logging Configuration
LOG_LEVEL=info
QUIET_STARTUP=true

# Test Options Configuration
# Note: Test options are now statically configured for optimal performance:
# - Tests always stop on any step failure (immediate feedback)
# - maxRetries: 2 (development), 1 (testing), 3 (production)
# - timeout: 45s (development), 30s (testing), 60s (production)
# No environment variables needed for test options