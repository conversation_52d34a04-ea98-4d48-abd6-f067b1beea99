"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Folder, FolderPlus, X, Plus, Loader2, Check, MousePointer, Eye, Clock, ArrowRight, HelpCircle, Laptop, Smartphone } from "lucide-react"
import { ScenarioFormData, PlatformType, TestStep } from "@/models/scenario"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from "@/components/ui/dialog"
import { ColorPickerButton } from "@/components/ui/color-picker-button"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"
import React from "react"
import { Checkbox } from "@/components/ui/checkbox"

// Popüler tag'ler
const popularTags = [
  "smoke",
  "regression",
  "api",
  "ui",
  "performance",
  "security",
  "integration",
  "functional",
  "acceptance",
  "e2e"
]

interface DetailsTabProps {
  scenarioData: ScenarioFormData & { id: string }
  updateScenarioField: (field: string, value: any) => void
  folders: any[]
  foldersLoading: boolean
  getFolderColorClass: (color?: string) => string
  newFolderOpen: boolean
  setNewFolderOpen: (open: boolean) => void
  newFolderName: string
  setNewFolderName: (name: string) => void
  newFolderColor: string
  setNewFolderColor: (color: string) => void
  isCreatingFolder: boolean
  handleCreateFolder: (name: string, color: string) => Promise<void>
  handleAddTag: () => void
  handleRemoveTag: (tag: string) => void
}

export function DetailsTab({
  scenarioData,
  updateScenarioField,
  folders,
  foldersLoading,
  getFolderColorClass,
  newFolderOpen,
  setNewFolderOpen,
  newFolderName,
  setNewFolderName,
  newFolderColor,
  setNewFolderColor,
  isCreatingFolder,
  handleCreateFolder,
  handleAddTag,
  handleRemoveTag,
}: DetailsTabProps) {
  const [selectedStepType, setSelectedStepType] = useState<string | null>(null)
  const [openFolderDialog, setOpenFolderDialog] = useState(false)

  // Step tipi ikonunu render et
  const renderStepTypeIcon = (type: string) => {
    switch (type) {
      case "aiAction":
        return <MousePointer className="h-4 w-4 text-indigo-500" />
      case "aiAssertion":
        return <Check className="h-4 w-4 text-emerald-500" />
      case "aiWaitElement":
        return <Eye className="h-4 w-4 text-amber-500" />
      case "sleep":
        return <Clock className="h-4 w-4 text-blue-500" />
      case "goto":
        return <ArrowRight className="h-4 w-4 text-purple-500" />
      default:
        return <HelpCircle className="h-4 w-4 text-gray-500" />
    }
  }

  // Folder creation dialog in DetailTab
  const FolderDialog = ({
    open,
    onOpenChange,
    onCreateFolder,
    isCreatingFolder,
  }: {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onCreateFolder: (name: string, color: string) => void;
    isCreatingFolder: boolean;
  }) => {
    const [name, setName] = React.useState("");
    const [color, setColor] = React.useState("#7c3aed"); // Default color
    const [customColor, setCustomColor] = React.useState("");
    const [showCustomColor, setShowCustomColor] = React.useState(false);

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (name.trim()) {
        // Use custom color if enabled, otherwise use the selected predefined color
        const finalColor = showCustomColor && customColor ? customColor : color;
        onCreateFolder(name, finalColor);
        setName("");
        setColor("#7c3aed");
        setCustomColor("");
        setShowCustomColor(false);
      }
    };

    // Predefined colors
    const predefinedColors = [
      "#7c3aed", // Indigo
      "#2563eb", // Blue
      "#059669", // Emerald
      "#d97706", // Amber
      "#dc2626", // Red
      "#8b5cf6", // Violet
      "#ec4899", // Pink
      "#0891b2", // Cyan
    ];

    // Custom color picker component
    const ColorPickerButton = () => (
      <input
        type="color"
        value={customColor || "#7c3aed"}
        onChange={(e) => setCustomColor(e.target.value)}
        className="w-8 h-8 rounded-md border overflow-hidden cursor-pointer"
        title="Choose custom color"
      />
    );

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Enter a name and choose a color for your new folder
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="folder-name">Folder Name</Label>
                <Input
                  id="folder-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter folder name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>Folder Color</Label>
                <div className="grid grid-cols-4 gap-2">
                  {predefinedColors.map((c) => (
                    <button
                      key={c}
                      type="button"
                      className={`w-full h-8 rounded-md border-2 ${
                        color === c && !showCustomColor
                          ? "border-black dark:border-white"
                          : "border-transparent"
                      }`}
                      style={{ backgroundColor: c }}
                      onClick={() => {
                        setColor(c);
                        setShowCustomColor(false);
                      }}
                      title={`Select ${c} color`}
                    />
                  ))}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="custom-color"
                  checked={showCustomColor}
                  onCheckedChange={(checked) => setShowCustomColor(!!checked)}
                />
                <Label htmlFor="custom-color">Use custom color</Label>
              </div>
              {showCustomColor && (
                <div className="flex items-center gap-2">
                  <ColorPickerButton />
                  <Input
                    type="text"
                    value={customColor}
                    onChange={(e) => setCustomColor(e.target.value)}
                    placeholder="#RRGGBB"
                    className="flex-1"
                  />
                </div>
              )}
            </div>
            <DialogFooter className="mt-4">
              <Button type="submit" disabled={isCreatingFolder}>
                {isCreatingFolder ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Folder"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <>
      <Card className="overflow-hidden border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
          <CardTitle>Scenario Details</CardTitle>
          <CardDescription>Edit the basic information about your test scenario</CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-5">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-base">
              Scenario Name
            </Label>
            <Input
              id="name"
              placeholder="Enter scenario name"
              value={scenarioData.name}
              onChange={(e) => updateScenarioField("name", e.target.value)}
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-base">
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Describe what this scenario tests"
              value={scenarioData.description}
              onChange={(e) => updateScenarioField("description", e.target.value)}
              className="min-h-[100px] resize-none"
            />
          </div>

          <div className="space-y-2">
            <Label className="text-base">Folder</Label>
            {foldersLoading ? (
              <div className="flex items-center gap-2 text-sm text-gray-500 mt-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading folders...
              </div>
            ) : (
              <div className="flex space-x-2">
                <div className="flex-1">
                  <Label htmlFor="folder-select" className="text-sm mb-1.5 block">
              Folder
            </Label>
                <Select
                    value={scenarioData.folderId || "none"}
                    onValueChange={(value) =>
                      updateScenarioField("folderId", value === "none" ? "" : value)
                    }
                  >
                    <SelectTrigger id="folder-select">
                    <SelectValue placeholder="Select a folder">
                        {scenarioData.folderId && scenarioData.folderId !== "none" && folders?.length > 0 ? (
                          <div className="flex items-center">
                            <Folder
                              className="h-4 w-4 mr-2"
                              style={{
                                color: folders.find(f => f.id === scenarioData.folderId)?.color || "#7c3aed"
                              }}
                            />
                            <span>{folders.find(f => f.id === scenarioData.folderId)?.name}</span>
                          </div>
                        ) : (
                          "Select a folder"
                        )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                      <SelectItem value="none">
                        <div className="flex items-center">
                          <Folder className="h-4 w-4 mr-2 text-gray-400" />
                          <span>No folder</span>
                      </div>
                      </SelectItem>
                      {folders.map((folder) => (
                        <SelectItem key={folder.id} value={folder.id}>
                          <div className="flex items-center">
                              <Folder
                              className="h-4 w-4 mr-2"
                              style={{ color: folder.color || "#7c3aed" }}
                              />
                            <span>{folder.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-10"
                    onClick={() => setOpenFolderDialog(true)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                          </div>
                        </div>
            )}
          </div>

          <div className="space-y-2">
            <Label className="text-base">Platform</Label>
            <RadioGroup
              value={scenarioData.platform || "web"}
              onValueChange={(value) => updateScenarioField("platform", value as PlatformType)}
              className="grid grid-cols-2 gap-4"
            >
              <div>
                <RadioGroupItem value="web" id="platform-web" className="peer sr-only" />
                <Label
                  htmlFor="platform-web"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-blue-500 peer-data-[state=checked]:bg-blue-50 [&:has([data-state=checked])]:border-blue-500 [&:has([data-state=checked])]:bg-blue-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-blue-950/20 dark:[&:has([data-state=checked])]:bg-blue-950/20 cursor-pointer"
                >
                  <Laptop className="mb-2 h-6 w-6 text-blue-500" />
                  <div className="text-center">
                    <p className="text-sm font-medium">Web</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Browser-based testing</p>
                  </div>
                </Label>
              </div>

              <div>
                <RadioGroupItem value="android" id="platform-android" className="peer sr-only" />
                <Label
                  htmlFor="platform-android"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-green-500 peer-data-[state=checked]:bg-green-50 [&:has([data-state=checked])]:border-green-500 [&:has([data-state=checked])]:bg-green-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-green-950/20 dark:[&:has([data-state=checked])]:bg-green-950/20 cursor-pointer"
                >
                  <Smartphone className="mb-2 h-6 w-6 text-green-500" />
                  <div className="text-center">
                    <p className="text-sm font-medium">Android</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Mobile app testing</p>
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label className="text-base">Tags</Label>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Label htmlFor="tags">Tags</Label>
                <span className="text-xs text-gray-500">
                  {scenarioData.tags.length} / 10
                </span>
              </div>
              <div className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[40px] bg-white dark:bg-gray-800">
                {scenarioData.tags.map((tag: string) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1.5 py-1 px-2 text-sm"
                  >
                    {tag}
                    <button
                      onClick={() => handleRemoveTag(tag)}
                      className="rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
                <Input
                  id="tags"
                  placeholder="Add a tag..."
                  value={scenarioData.newTag}
                  onChange={(e) => updateScenarioField("newTag", e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === ",") {
                      e.preventDefault()
                      handleAddTag()
                    }
                  }}
                  className="flex-1 border-none shadow-none focus-visible:ring-0 p-0 h-auto bg-transparent"
                  disabled={scenarioData.tags.length >= 10}
                />
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {popularTags
                  .filter((t: string) => !scenarioData.tags.includes(t))
                  .map((tag: string) => (
                    <Button
                      key={tag}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        updateScenarioField("newTag", tag)
                        setTimeout(handleAddTag, 50)
                      }}
                      disabled={scenarioData.tags.length >= 10}
                      className="text-xs px-2 py-1 h-auto"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      {tag}
                    </Button>
                  ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {openFolderDialog && (
        <FolderDialog
          open={openFolderDialog}
          onOpenChange={setOpenFolderDialog}
          onCreateFolder={(name, color) => handleCreateFolder(name, color)}
          isCreatingFolder={isCreatingFolder}
        />
      )}
      <style jsx global>{`
        @keyframes scale-in {
          0% {
            transform: scale(0.8);
            opacity: 0;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @keyframes fade-in {
          0% {
            opacity: 0;
          }
          100% {
            opacity: 1;
          }
        }

        @keyframes slide-in {
          0% {
            transform: translateY(-10px);
            opacity: 0;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }

        .tag-animation-enter {
          animation: scale-in 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .tag-hover-animation:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .popular-tags-container {
          animation: fade-in 0.3s ease;
        }

        .popular-tag {
          animation: slide-in 0.3s ease;
          animation-fill-mode: both;
        }

        .popular-tag:nth-child(1) { animation-delay: 0.05s; }
        .popular-tag:nth-child(2) { animation-delay: 0.1s; }
        .popular-tag:nth-child(3) { animation-delay: 0.15s; }
        .popular-tag:nth-child(4) { animation-delay: 0.2s; }
        .popular-tag:nth-child(5) { animation-delay: 0.25s; }
        .popular-tag:nth-child(6) { animation-delay: 0.3s; }
        .popular-tag:nth-child(7) { animation-delay: 0.35s; }
        .popular-tag:nth-child(8) { animation-delay: 0.4s; }
        .popular-tag:nth-child(9) { animation-delay: 0.45s; }
        .popular-tag:nth-child(10) { animation-delay: 0.5s; }
      `}</style>
    </>
  )
}