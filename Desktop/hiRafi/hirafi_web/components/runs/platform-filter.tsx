"use client"

import { useState } from "react"
import { <PERSON>ptop, Smartphone, Check } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

interface PlatformFilterProps {
  activePlatform: string
  onPlatformFilterChange: (platform: string) => void
}

// Platform options with their display properties
const platformOptions = [
  { value: "all", label: "All Platforms", icon: null },
  { value: "web", label: "Web", icon: <Laptop className="h-4 w-4 mr-2" /> },
  { value: "android", label: "Mobile", icon: <Smartphone className="h-4 w-4 mr-2" /> },
]

export function PlatformFilter({ activePlatform, onPlatformFilterChange }: PlatformFilterProps) {
  const [open, setOpen] = useState(false)
  
  // Get the currently selected platform option
  const selectedPlatform = platformOptions.find(option => option.value === activePlatform) || platformOptions[0]
  
  // Handle platform selection
  const handlePlatformSelect = (value: string) => {
    onPlatformFilterChange(value)
    setOpen(false)
  }
  
  // Get platform icon color
  const getIconColor = (platform: string) => {
    switch (platform) {
      case "web":
        return "text-blue-600 dark:text-blue-400"
      case "android":
        return "text-green-600 dark:text-green-400"
      default:
        return "text-gray-600 dark:text-gray-400"
    }
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-9 border-gray-200 dark:border-gray-700 min-w-[120px] justify-between",
            activePlatform !== "all" ? "bg-gray-50 dark:bg-gray-800" : ""
          )}
        >
          <div className="flex items-center">
            {activePlatform !== "all" ? (
              <>
                {selectedPlatform.icon && (
                  <span className={cn(getIconColor(activePlatform))}>
                    {selectedPlatform.icon}
                  </span>
                )}
                <span className="text-sm font-medium">{selectedPlatform.label}</span>
              </>
            ) : (
              <>
                <Laptop className="h-4 w-4 mr-1 text-gray-500 dark:text-gray-400" />
                <Smartphone className="h-4 w-4 mr-2 text-gray-500 dark:text-gray-400" />
                <span className="text-sm font-medium">Platform</span>
              </>
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[200px]">
        {platformOptions.map((platform) => (
          <DropdownMenuItem
            key={platform.value}
            className={cn(
              "flex items-center px-3 py-2 cursor-pointer",
              activePlatform === platform.value ? "bg-gray-100 dark:bg-gray-800" : ""
            )}
            onClick={() => handlePlatformSelect(platform.value)}
          >
            <div className="flex items-center flex-1">
              {platform.icon && (
                <span className={cn(
                  platform.value !== "all" ? getIconColor(platform.value) : "text-gray-500 dark:text-gray-400"
                )}>
                  {platform.icon}
                </span>
              )}
              <span>{platform.label}</span>
            </div>
            {activePlatform === platform.value && (
              <Check className="h-4 w-4 ml-2 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
