import { Router, Request, Response } from 'express';
import { authenticate, AuthRequest } from '../middleware/authMiddleware.js';
import { checkPermission } from '../middleware/permissionMiddleware.js';
import {
  getAllScenarios,
  getUserScenarios,
  getUserFolders
} from '../../services/mongo/scenarioService.js';
import { createObjectCsvStringifier } from 'csv-writer';
import { logger } from '../../utils/logger.js';
import { createPdfFromScenarios } from '../../utils/pdfExporter.js';

const router = Router();

/**
 * @route POST /api/scenario-export/export
 * @desc Export scenarios as CSV or PDF
 * @access Private
 */
router.post('/export', authenticate, checkPermission('Scenario', 'read'), async (req: Request, res: Response) => {
  try {
    const {
      format = 'csv',
      folderId = 'all',
      status = 'all',
      selectedScenarioIds = null,
      includeFields = {
        steps: true,
        description: true,
        tags: true,
        status: true,
        lastRun: true,
        duration: true
      }
    } = req.body;

    // Get user ID from token
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    const teamId = authReq.user?.teamId;
    const companyId = authReq.user?.companyId;

    if (!userId) {
      return res.status(401).json({ success: false, error: 'User not authenticated' });
    }

    // Get scenarios based on filters or selected IDs
    let result;

    if (selectedScenarioIds && Array.isArray(selectedScenarioIds) && selectedScenarioIds.length > 0) {
      // Export only selected scenarios
      result = await getUserScenarios(userId, {
        teamId: teamId,
        companyId: companyId,
        scenarioIds: selectedScenarioIds, // Pass selected IDs correctly
        excludeSteps: !includeFields.steps
      });
    } else {
      // Export based on folder/status filters (existing behavior)
      result = await getUserScenarios(userId, {
        teamId: teamId,
        companyId: companyId,
        status: status !== 'all' ? status : undefined,
        folderId: folderId !== 'all' ? folderId : undefined,
        excludeSteps: !includeFields.steps // Only include steps if requested
      });
    }

    if (!result.success || !result.scenarios) {
      return res.status(500).json({
        success: false,
        error: result.message || 'Failed to fetch scenarios'
      });
    }

    const scenarios = result.scenarios;

    // Get folders for mapping folder IDs to names
    const foldersResult = await getUserFolders(userId, { teamId, companyId });
    const folderMap = new Map(
      foldersResult.success && foldersResult.folders
        ? foldersResult.folders.map(folder => [folder.id, folder.name])
        : []
    );

    // Format scenarios for export
    const formattedScenarios = scenarios.map(scenario => {
      const result: any = {
        name: scenario.name,
        folder: folderMap.get(scenario.folderId) || 'No Folder',
      };

      // Add optional fields based on includeFields
      if (includeFields.description) {
        result.description = scenario.description || '';
      }

      if (includeFields.tags) {
        result.tags = Array.isArray(scenario.tags) ? scenario.tags.join(', ') : '';
      }

      if (includeFields.status) {
        result.status = scenario.status || 'unknown';
      }

      if (includeFields.lastRun) {
        result.lastRun = scenario.lastRun?.date
          ? new Date(scenario.lastRun.date).toISOString()
          : 'Never';
      }

      if (includeFields.duration) {
        result.duration = scenario.lastRun?.duration
          ? `${Math.round(scenario.lastRun.duration / 1000)}s`
          : '0s';
      }

      if (includeFields.steps) {
        result.steps = JSON.stringify(scenario.steps || []);
        result.totalSteps = scenario.lastRun?.totalStep || 0;
        result.completedSteps = scenario.lastRun?.succesStep || 0;
        result.failedSteps = scenario.lastRun?.failedStep || 0;
      }

      // Add created and updated dates
      result.createdAt = scenario.createdAt
        ? new Date(scenario.createdAt).toISOString()
        : new Date().toISOString();

      result.updatedAt = scenario.updatedAt
        ? new Date(scenario.updatedAt).toISOString()
        : new Date().toISOString();

      return result;
    });

    // Handle different export formats
    if (format === 'csv') {
      // Create CSV header based on included fields
      const header = [
        { id: 'name', title: 'Name' },
        { id: 'folder', title: 'Folder' }
      ];

      if (includeFields.description) header.push({ id: 'description', title: 'Description' });
      if (includeFields.tags) header.push({ id: 'tags', title: 'Tags' });
      if (includeFields.status) header.push({ id: 'status', title: 'Status' });
      if (includeFields.lastRun) header.push({ id: 'lastRun', title: 'Last Run' });
      if (includeFields.duration) header.push({ id: 'duration', title: 'Duration' });

      if (includeFields.steps) {
        header.push(
          { id: 'steps', title: 'Steps' },
          { id: 'totalSteps', title: 'Total Steps' },
          { id: 'completedSteps', title: 'Completed Steps' },
          { id: 'failedSteps', title: 'Failed Steps' }
        );
      }

      header.push(
        { id: 'createdAt', title: 'Created At' },
        { id: 'updatedAt', title: 'Updated At' }
      );

      // Create CSV stringifier
      const csvStringifier = createObjectCsvStringifier({
        header,
        fieldDelimiter: ','
      });

      // Generate CSV content
      const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(formattedScenarios);

      // Set response headers for CSV download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="scenarios-export.csv"`);

      // Send CSV content
      return res.send(csvContent);
    }
    else if (format === 'pdf') {
      try {
        // Generate PDF
        const pdfBuffer = await createPdfFromScenarios(formattedScenarios, includeFields);

        // Set response headers for PDF download
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="scenarios-export.pdf"`);

        // Send PDF content
        return res.send(pdfBuffer);
      } catch (error: any) {
        logger.error('Error generating PDF:', error);
        return res.status(500).json({
          success: false,
          error: `Error generating PDF: ${error.message}`
        });
      }
    }
    // For now, just return JSON for other formats
    else {
      return res.json({
        success: true,
        data: formattedScenarios
      });
    }
  } catch (error: any) {
    logger.error('Error exporting scenarios:', error);
    return res.status(500).json({ success: false, error: 'Failed to export scenarios' });
  }
});

export default router;
