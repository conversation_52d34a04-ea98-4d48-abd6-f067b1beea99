"use client";

import React, {
  createContext,
  useContext,
  ReactNode,
  useMemo,
  useEffect,
  useCallback,
  useRef,
} from "react";
import { useTestManagementStore } from "@/store/testManagementStore";
import { useTestRailIntegration } from "@/hooks/useTestRailIntegration";
import { useZephyrScaleIntegration } from "@/hooks/useZephyrScaleIntegration";
import { TestManagementProvider } from "@/store/testManagementStore";
import { ExtendedScenarioFormData } from "@/lib/utils/scenario-form-utils";
import { getAvailableTestManagementIntegrations } from "@/lib/api/plugin-api";

// Type-safe interface for API response
interface TestManagementIntegration {
  provider: TestManagementProvider;
  name: string;
  isConnected: boolean;
  config?: any;
  plugin?: any;
}

interface AvailableIntegrationsResponse {
  success: boolean;
  message?: string;
  data?: {
    integrations: TestManagementIntegration[];
    count: number;
  };
}

// Enhanced context interface that combines store state with provider integrations
interface UnifiedTestManagementContextValue {
  // Store state and actions
  store: ReturnType<typeof useTestManagementStore>;

  // Provider integrations
  testRailIntegration: ReturnType<typeof useTestRailIntegration>;
  zephyrScaleIntegration: ReturnType<typeof useZephyrScaleIntegration>;

  // Enhanced actions that integrate with provider hooks
  initializeProvider: (provider: TestManagementProvider) => Promise<void>;
  fetchProjects: () => Promise<void>;
  fetchSuitesOrFolders: (projectId: string) => Promise<void>;
  fetchTestCases: (
    projectId: string,
    suiteOrFolderId?: string,
  ) => Promise<void>;
  fetchTestCaseDetails: (caseId: string) => Promise<any>;

  // Unified provider management
  fetchAvailableProviders: () => Promise<void>;

  // Form integration helpers
  syncWithFormData: (formData: ExtendedScenarioFormData) => void;
  getFormDataForProvider: (provider: TestManagementProvider) => {
    caseIds: string[];
    sync: boolean;
  };
}

const UnifiedTestManagementContext =
  createContext<UnifiedTestManagementContextValue | null>(null);

interface UnifiedTestManagementProviderProps {
  children: ReactNode;
  scenarioData?: ExtendedScenarioFormData | null;
  autoInitialize?: boolean;
}

export function UnifiedTestManagementProvider({
  children,
  scenarioData,
  autoInitialize = true,
}: UnifiedTestManagementProviderProps) {
  // Use stable store selectors instead of whole store object
  const setAvailableProviders = useTestManagementStore(
    (state) => state.setAvailableProviders,
  );
  const switchProvider = useTestManagementStore(
    (state) => state.switchProvider,
  );
  const activeProvider = useTestManagementStore(
    (state) => state.activeProvider,
  );
  const initializedProviders = useTestManagementStore(
    (state) => state.initializedProviders,
  );
  const setLoading = useTestManagementStore((state) => state.setLoading);
  const setProviderConfig = useTestManagementStore(
    (state) => state.setProviderConfig,
  );
  const setProjects = useTestManagementStore((state) => state.setProjects);
  const setSuitesOrFolders = useTestManagementStore(
    (state) => state.setSuitesOrFolders,
  );
  const setTestCases = useTestManagementStore((state) => state.setTestCases);
  const setImportCaseDetails = useTestManagementStore(
    (state) => state.setImportCaseDetails,
  );
  const updateFormData = useTestManagementStore(
    (state) => state.updateFormData,
  );
  const setSelectedCases = useTestManagementStore(
    (state) => state.setSelectedCases,
  );
  const formData = useTestManagementStore((state) => state.formData);
  const store = useTestManagementStore();

  // Initialize provider integrations without auto-initialization
  const testRailIntegration = useTestRailIntegration({ autoInitialize: false });
  const zephyrScaleIntegration = useZephyrScaleIntegration({
    autoInitialize: false,
  });

  // Use ref to prevent multiple initializations
  const isInitializedRef = useRef(false);

  // Enhanced provider initialization that integrates with existing hooks
  const initializeProvider = useCallback(
    async (provider: TestManagementProvider, configData?: any) => {
      console.log(`[UnifiedTestManagement] initializeProvider called for ${provider}`, {
        configData,
        alreadyInitialized: initializedProviders.has(provider),
        storeProviders: store.providers
      });

      if (initializedProviders.has(provider)) {
        console.log(`[UnifiedTestManagement] Provider ${provider} already initialized, skipping`);
        return;
      }

      setLoading("isLoading", true);

      try {
        // Get config data from store if not provided
        const providerConfig = configData || store.providers[provider];
        console.log(`[UnifiedTestManagement] Using provider config for ${provider}:`, providerConfig);

        let integration;
        if (provider === "testrail") {
          console.log(`[UnifiedTestManagement] Initializing TestRail integration`);
          await testRailIntegration.initialize(providerConfig);
          integration = testRailIntegration;
        } else if (provider === "zephyrscale") {
          console.log(`[UnifiedTestManagement] Initializing Zephyr Scale integration`);
          await zephyrScaleIntegration.initialize(providerConfig as any);
          integration = zephyrScaleIntegration;
        }

        if (integration) {
          const newConfig = {
            isInitialized: true,
            isConnected: integration.isConnected,
            config: (integration as any).config,
            plugin: (integration as any).plugin,
          };
          console.log(`[UnifiedTestManagement] Setting provider config for ${provider}:`, newConfig);
          setProviderConfig(provider, newConfig);
          console.log(
            `[UnifiedTestManagement] Provider ${provider} config updated - isConnected: ${integration.isConnected}`,
          );
        }
      } catch (error) {
        console.error(`Failed to initialize provider ${provider}:`, error);
        setProviderConfig(provider, {
          isInitialized: true,
          isConnected: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      } finally {
        setLoading("isLoading", false);
      }
    },
    [
      initializedProviders,
      setLoading,
      setProviderConfig,
      testRailIntegration,
      zephyrScaleIntegration,
      store.providers,
    ],
  );

  // Fetch available providers with stable dependencies and type-safe parsing
  const fetchAvailableProviders = useCallback(async () => {
    console.log('[UnifiedTestManagement] fetchAvailableProviders called');
    try {
      const response: AvailableIntegrationsResponse = await getAvailableTestManagementIntegrations();
      console.log('[UnifiedTestManagement] API response:', response);
      const providers: TestManagementProvider[] = [];

      // Parse the correct response format
      const integrations = response.data?.integrations || [];
      console.log('[UnifiedTestManagement] Found integrations:', integrations);

      integrations.forEach((integration: TestManagementIntegration) => {
        console.log('[UnifiedTestManagement] Processing integration:', integration);
        if (integration.isConnected && integration.provider) {
          providers.push(integration.provider);

          // Store integration config in the store for hooks to use
          const providerConfig = {
            isInitialized: true,
            isConnected: true,
            config: integration.config,
            plugin: integration.plugin,
          };
          console.log(`[UnifiedTestManagement] Setting provider config for ${integration.provider}:`, providerConfig);
          setProviderConfig(integration.provider, providerConfig);

          // Initialize hook data directly from API response
          if (integration.provider === "zephyrscale" && integration.config) {
            // Set projects data for Zephyr Scale
            if (integration.config.projectsData) {
              console.log(
                "[UnifiedTestManagement] Setting Zephyr Scale projects:",
                integration.config.projectsData,
              );
              setProjects(integration.config.projectsData);
            }
          } else if (
            integration.provider === "testrail" &&
            integration.config
          ) {
            // Set projects data for TestRail
            if (integration.config.projectsData) {
              console.log(
                "[UnifiedTestManagement] Setting TestRail projects:",
                integration.config.projectsData,
              );
              setProjects(integration.config.projectsData);
            }
          }
        }
      });

      console.log('[UnifiedTestManagement] Found providers:', providers);
      setAvailableProviders(providers);

      // Auto-initialize first available provider if requested
      if (autoInitialize && providers.length > 0 && !activeProvider) {
        console.log(
          "[UnifiedTestManagement] Auto-initializing provider:",
          providers[0],
        );
        switchProvider(providers[0]);
        // No need to call initializeProvider since we already set the config above
      }

      console.log('[UnifiedTestManagement] Final store state check:', {
        providers: store.providers,
        projects: store.projects,
        activeProvider: store.activeProvider
      });
    } catch (error) {
      console.error('Failed to fetch available providers:', error);
      setAvailableProviders([]);
    }
  }, [setAvailableProviders, switchProvider, activeProvider, autoInitialize, setProviderConfig, setProjects, store]);

  // Fetch projects for active provider
  const fetchProjects = useCallback(async () => {
    if (!activeProvider) return;

    setLoading("isLoading", true);

    try {
      // Get projects from store - they should already be populated by fetchAvailableProviders
      const currentProjects = store.projects || [];

      // If no projects in store, try to get them from provider config
      if (currentProjects.length === 0) {
        const providerConfig = store.providers[activeProvider];
        if (providerConfig?.config?.projectsData) {
          console.log(
            `[UnifiedTestManagement] Loading projects from ${activeProvider} config:`,
            providerConfig.config.projectsData,
          );
          setProjects(providerConfig.config.projectsData);
        }
      }
    } catch (error) {
      console.error("Failed to fetch projects:", error);
    } finally {
      setLoading("isLoading", false);
    }
  }, [
    activeProvider,
    setLoading,
    setProjects,
    store.projects,
    store.providers,
  ]);

  // Fetch suites or folders
  const fetchSuitesOrFolders = useCallback(
    async (_projectId: string) => {
      if (!activeProvider) return;

      setLoading("isLoadingSuites", true);

      try {
        let items: any[] = [];

        // Get provider config from store
        const providerConfig = store.providers[activeProvider];

        if (activeProvider === "testrail") {
          // TestRail uses suites
          const suitesData = providerConfig?.config?.suitesData || [];
          items = suitesData.map((suite: any) => ({
            id: suite.id,
            name: suite.name,
            type: "suite" as const,
          }));
        } else if (activeProvider === "zephyrscale") {
          // Zephyr Scale uses folders
          const foldersData = providerConfig?.config?.foldersData || [];
          items = foldersData.map((folder: any) => ({
            id: folder.id,
            name: folder.name,
            type: "folder" as const,
          }));
        }

        console.log(
          `[UnifiedTestManagement] Setting ${activeProvider} suites/folders:`,
          items,
        );
        setSuitesOrFolders(items);
      } catch (error) {
        console.error("Failed to fetch suites/folders:", error);
      } finally {
        setLoading("isLoadingSuites", false);
      }
    },
    [activeProvider, setLoading, setSuitesOrFolders, store.providers],
  );

  // Fetch test cases with direct API calls
  const fetchTestCases = useCallback(
    async (projectId: string, suiteOrFolderId?: string) => {
      if (!activeProvider) return;

      setLoading("isLoadingCases", true);
      console.log(
        `[UnifiedTestManagement] Fetching test cases for ${activeProvider}, project: ${projectId}, suite/folder: ${suiteOrFolderId}`,
      );

      try {
        let cases: any[] = [];

        if (activeProvider === "testrail") {
          // Make direct API call for TestRail test cases
          if (projectId && projectId !== "all") {
            try {
              // Import the API function
              const { getTestRailCases } = await import("@/lib/api/plugin-api");
              const response = await getTestRailCases({
                projectId: projectId,
                suiteIds: suiteOrFolderId ? [suiteOrFolderId] : undefined,
              });

              cases = response.data || [];
              console.log(
                `[UnifiedTestManagement] TestRail API returned ${cases.length} test cases`,
              );
            } catch (apiError) {
              console.error("TestRail API error:", apiError);
              cases = [];
            }
          }
        } else if (activeProvider === "zephyrscale") {
          // Make direct API call for Zephyr Scale test cases
          if (projectId && projectId !== "all") {
            try {
              // Import the API function
              const { getZephyrScaleTestCases } = await import(
                "@/lib/api/plugin-api"
              );
              const response = await getZephyrScaleTestCases({
                projectKey: projectId,
                folderId: suiteOrFolderId,
              });

              cases = response.data || [];
              console.log(
                `[UnifiedTestManagement] Zephyr Scale API returned ${cases.length} test cases`,
              );
            } catch (apiError) {
              console.error("Zephyr Scale API error:", apiError);
              cases = [];
            }
          }
        }

        console.log(
          `[UnifiedTestManagement] Setting ${cases.length} test cases in store`,
        );
        setTestCases(cases);
      } catch (error) {
        console.error("Failed to fetch test cases:", error);
        setTestCases([]);
      } finally {
        setLoading("isLoadingCases", false);
      }
    },
    [activeProvider, setLoading, setTestCases],
  );

  // Fetch test case details for import
  const fetchTestCaseDetails = useCallback(
    async (caseId: string) => {
      if (!activeProvider) return null;

      setLoading("isLoadingImportDetails", true);

      try {
        let details = null;
        if (activeProvider === "testrail") {
          // Use TestRail integration to fetch details
          details = await (testRailIntegration as any).fetchTestCaseDetails?.(
            caseId,
          );
        } else if (activeProvider === "zephyrscale") {
          // Use Zephyr Scale integration to fetch details
          details = await (
            zephyrScaleIntegration as any
          ).fetchTestCaseDetails?.(caseId);
        }

        setImportCaseDetails(details);
        return details;
      } catch (error) {
        console.error("Failed to fetch test case details:", error);
        return null;
      } finally {
        setLoading("isLoadingImportDetails", false);
      }
    },
    [
      activeProvider,
      setLoading,
      setImportCaseDetails,
      testRailIntegration,
      zephyrScaleIntegration,
    ],
  );

  // Sync store with form data
  const syncWithFormData = useCallback(
    (formData: ExtendedScenarioFormData) => {
      updateFormData({
        testrailCases: formData.testrailCases || [],
        testrailSync: formData.testrailSync || false,
        zephyrscaleCases: formData.zephyrscaleCases || [],
        zephyrscaleSync: formData.zephyrscaleSync || false,
      });

      // Determine active provider from form data
      if (formData.testrailCases?.length > 0) {
        switchProvider("testrail");
        setSelectedCases(formData.testrailCases);
      } else if (formData.zephyrscaleCases?.length > 0) {
        switchProvider("zephyrscale");
        setSelectedCases(formData.zephyrscaleCases);
      }
    },
    [updateFormData, switchProvider, setSelectedCases],
  );

  // Get form data for specific provider
  const getFormDataForProvider = useCallback(
    (provider: TestManagementProvider) => {
      if (provider === "testrail") {
        return {
          caseIds: formData.testrailCases,
          sync: formData.testrailSync,
        };
      } else if (provider === "zephyrscale") {
        return {
          caseIds: formData.zephyrscaleCases,
          sync: formData.zephyrscaleSync,
        };
      }
      return { caseIds: [], sync: false };
    },
    [formData],
  );

  // Initialize on mount with ref-based control to prevent infinite loops
  useEffect(() => {
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      fetchAvailableProviders();
    }
  }, []); // Empty dependency array - initialization happens only once

  // Sync with scenario data if provided
  useEffect(() => {
    if (scenarioData) {
      syncWithFormData(scenarioData);
    }
  }, [scenarioData, syncWithFormData]);

  // Memoize context value with stable dependencies
  const contextValue = useMemo(() => ({
    store,
    testRailIntegration,
    zephyrScaleIntegration,
    initializeProvider,
    fetchProjects,
    fetchSuitesOrFolders,
    fetchTestCases,
    fetchTestCaseDetails,
    fetchAvailableProviders,
    syncWithFormData,
    getFormDataForProvider,
  }), [
    // Remove store from dependencies as it's unstable
    // store, // Removed - causes unnecessary re-renders
    testRailIntegration,
    zephyrScaleIntegration,
    initializeProvider,
    fetchProjects,
    fetchSuitesOrFolders,
    fetchTestCases,
    fetchTestCaseDetails,
    fetchAvailableProviders,
    syncWithFormData,
    getFormDataForProvider,
  ]);

  return (
    <UnifiedTestManagementContext.Provider value={contextValue}>
      {children}
    </UnifiedTestManagementContext.Provider>
  );
}

// Hook to use the unified context
export function useUnifiedTestManagementContext() {
  const context = useContext(UnifiedTestManagementContext);
  if (!context) {
    throw new Error(
      "useUnifiedTestManagementContext must be used within a UnifiedTestManagementProvider",
    );
  }
  return context;
}
