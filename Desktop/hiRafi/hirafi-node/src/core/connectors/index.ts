/**
 * Core Connectors Module - Refactored Architecture
 * 
 * This module provides a modern, dependency injection-based connector system
 * that eliminates singleton patterns and promotes proper service architecture.
 * 
 * Key improvements:
 * - Dependency injection throughout
 * - Interface-based design
 * - Single responsibility principle
 * - Proper service lifecycle management
 * - Easy to test and extend
 */

// Core exports
export { ConnectorManager } from './core/connector-manager.js';

// Factory exports (primary API)
export { ConnectorFactory, createConnector } from './factories/connector-factory.js';

// Service registry exports
export { ConnectorServiceRegistry } from './registry/connector-service-registry.js';

// Interface exports (for typing and mocking)
export * from './interfaces/index.js';

// Type exports
export * from './types/connector-types.js';
export * from './types/service-types.js';
export * from './types/factory-types.js';
export * from './types/registry-types.js';

// Service exports (for advanced usage) - excluding conflicting exports
export { BaseService } from './services/base/base-service.js';
export { HeartbeatService } from './services/heartbeat/heartbeat-service.js';
export { RegistrationService } from './services/registration/registration-service.js';
export { HttpHeartbeatService } from './services/http/http-heartbeat-service.js';
export { HttpRegistrationService } from './services/http/http-registration-service.js';
export { HttpTestService } from './services/http/http-test-service.js';
export { WebSocketConnectionManager } from './services/websocket/websocket-connection-manager.js';
export { WebSocketMessageHandler } from './services/websocket/websocket-message-handler.js';

// HTTP connector exports
export * from './http/http-connector.js';

// Redis connector exports - specific exports to avoid conflicts
export { RedisConnector as RedisConnectorImpl } from './redis/redis-connector.js';

// WebSocket connector exports
export * from './websocket/websocket-connector.js';

// Backward compatibility - create a default instance using the factory
import { ConnectorFactory } from './factories/connector-factory.js';
import { logger } from '../../utils/logger.js';

/**
 * Backward compatibility function that mimics the old singleton pattern
 * while using the new dependency injection architecture under the hood.
 *
 * This allows existing code to continue working without changes.
 */
class ConnectorFacade {
  private static instance: any = null;
  private static connectorManager: any = null;

  /**
   * Get the singleton-like instance (backward compatibility)
   */
  static getInstance(): any {
    if (!ConnectorFacade.instance) {
      ConnectorFacade.instance = new ConnectorFacade();
    }
    return ConnectorFacade.instance;
  }

  /**
   * Initialize the connector manager (lazy initialization)
   */
  private async ensureConnectorManager(): Promise<void> {
    if (!ConnectorFacade.connectorManager) {
      try {
        ConnectorFacade.connectorManager = await ConnectorFactory.createConnectorManager({
          enableLogging: false
        });
      } catch (error: any) {
        logger.error(`ConnectorFacade: Error creating connector manager: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Create a connector by type
   */
  async createConnector(type: string, config?: any): Promise<any> {
    await this.ensureConnectorManager();
    return ConnectorFacade.connectorManager.createConnector(type, config);
  }

  /**
   * Get WebSocket connector (backward compatibility)
   */
  async getWebSocketConnector(): Promise<any> {
    return this.createConnector('websocket');
  }

  /**
   * Get HTTP connector (backward compatibility)
   */
  async getHttpConnector(): Promise<any> {
    return this.createConnector('http');
  }
}

// Export the facade as default for backward compatibility
export default ConnectorFacade.getInstance();

/**
 * Backward compatibility exports that mimic the old singleton pattern
 */

// Create backward compatible HTTP connector facade
export class HttpConnectorFacade {
  private static instance: any = null;
  private httpConnector: any = null;

  static getInstance(): any {
    if (!HttpConnectorFacade.instance) {
      HttpConnectorFacade.instance = new HttpConnectorFacade();
    }
    return HttpConnectorFacade.instance;
  }

  async initialize(config?: any): Promise<void> {
    // Delegate to the new factory system using proper pattern
    const factory = new ConnectorFactory();
    this.httpConnector = await factory.createConnector('http', config);
  }

  // Delegate all methods to the actual connector
  async registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null> {
    if (!this.httpConnector) await this.initialize();
    return this.httpConnector.registerNode(name, capabilities, clientId);
  }

  async sendHeartbeat(): Promise<boolean> {
    if (!this.httpConnector) await this.initialize();
    return this.httpConnector.sendHeartbeat();
  }

  // Add other methods as needed...
}

// Create backward compatible WebSocket connector facade
export class WebSocketConnectorFacade {
  private static instance: any = null;
  private wsConnector: any = null;

  static getInstance(): any {
    if (!WebSocketConnectorFacade.instance) {
      WebSocketConnectorFacade.instance = new WebSocketConnectorFacade();
    }
    return WebSocketConnectorFacade.instance;
  }

  async initialize(config?: any): Promise<void> {
    // Delegate to the new factory system using proper pattern
    const factory = new ConnectorFactory();
    this.wsConnector = await factory.createConnector('websocket', config);
  }

  // Delegate all methods to the actual connector
  async connect(): Promise<void> {
    if (!this.wsConnector) await this.initialize();
    return this.wsConnector.connect();
  }

  async registerNode(name: string, capabilities: string[], clientId?: string): Promise<string | null> {
    if (!this.wsConnector) await this.initialize();
    return this.wsConnector.registerNode(name, capabilities, clientId);
  }

  // Add other methods as needed...
}

// Export backward compatibility instances
export const httpConnector = HttpConnectorFacade.getInstance();
export const websocketConnector = WebSocketConnectorFacade.getInstance();
