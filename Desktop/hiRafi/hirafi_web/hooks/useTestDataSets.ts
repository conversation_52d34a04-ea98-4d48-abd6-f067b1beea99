/**
 * Test Data Sets Hook
 * 
 * Custom hook that provides a clean interface to the Zustand store
 * and handles side effects like API calls and environment loading
 */

import { useEffect, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from '@/lib/utils/toast-utils'
import { useTestDataSetsStore, useTestDataSetsSelectors, DataEnvironment, TestDataVariable } from '@/store/testDataSetsStore'
import {
  dataEnvironmentApi,
  dataSetApi,
  generateTestData,
  generateVariableDefinitions,
  generateDataSetStructure
} from '@/lib/api/test-data'
import { useAuth } from '@/lib/api/auth'
import type { DataTemplate } from '@/store/testDataSetsStore'
import type { EnvironmentType, CreateDataSetRequest as APICreateDataSetRequest, ValueConstraints, UpdateDataSetRequest, DataVariable as APIDataVariable } from '@/types/test-data'
import { v4 as uuidv4 } from "uuid"

// Define a type that can accept what VariablesTable might pass
interface VariableInput {
  id: string;
  name?: string; // Name is useful for toast messages
  isRequired?: boolean; // Acknowledge that this might be undefined
  // Allow other properties that EnhancedDataVariable might have
  [key: string]: any;
}

export const useTestDataSets = () => {
  const router = useRouter()
  const { user } = useAuth()

  // Extract individual store methods to avoid store reference changes
  const updateFormData = useTestDataSetsStore(state => state.updateFormData)
  const addTag = useTestDataSetsStore(state => state.addTag)
  const removeTag = useTestDataSetsStore(state => state.removeTag)
  const setNewTag = useTestDataSetsStore(state => state.setNewTag)
  const setActiveTab = useTestDataSetsStore(state => state.setActiveTab)
  const setActiveEnvironment = useTestDataSetsStore(state => state.setActiveEnvironment)
  const setShowVariableValues = useTestDataSetsStore(state => state.setShowVariableValues)
  const setShowEnvironmentSelector = useTestDataSetsStore(state => state.setShowEnvironmentSelector)
  const setSelectedTemplate = useTestDataSetsStore(state => state.setSelectedTemplate)
  const setLoadingEnvironments = useTestDataSetsStore(state => state.setLoadingEnvironments)
  const setUserEnvironments = useTestDataSetsStore(state => state.setUserEnvironments)
  const setSelectedEnvironments = useTestDataSetsStore(state => state.setSelectedEnvironments)
  const toggleEnvironmentSelection = useTestDataSetsStore(state => state.toggleEnvironmentSelection)
  const setGenerating = useTestDataSetsStore(state => state.setGenerating)
  const setSaving = useTestDataSetsStore(state => state.setSaving)
  const setVariables = useTestDataSetsStore(state => state.setVariables)
  const addVariable = useTestDataSetsStore(state => state.addVariable)
  const removeVariable = useTestDataSetsStore(state => state.removeVariable)
  const updateVariable = useTestDataSetsStore(state => state.updateVariable)
  const updateVariableValue = useTestDataSetsStore(state => state.updateVariableValue)
  const duplicateVariable = useTestDataSetsStore(state => state.duplicateVariable)
  const setEditingVariable = useTestDataSetsStore(state => state.setEditingVariable)
  const updateTempConstraint = useTestDataSetsStore(state => state.updateTempConstraint)
  const setLoadingDataSet = useTestDataSetsStore(state => state.setLoadingDataSet)
  const setLoadDataSetError = useTestDataSetsStore(state => state.setLoadDataSetError)
  const reset = useTestDataSetsStore(state => state.reset)
  const resetFormAndVariables = useTestDataSetsStore(state => state.resetFormAndVariables)

  // Use selectors from the store
  const {
    getVariablesWithCurrentValues,
    getActiveEnvironment,
    canSave: getCanSave,
    getEditingVariableWithValue
  } = useTestDataSetsSelectors()

  // Get reactive values directly from the store state for those not covered by complex selectors
  // or when the selector itself is simple enough.
  const formData = useTestDataSetsStore(state => state.formData)
  const activeEnvironmentId = useTestDataSetsStore(state => state.activeEnvironmentId)
  const userEnvironments = useTestDataSetsStore(state => state.userEnvironments)
  const selectedEnvironments = useTestDataSetsStore(state => state.selectedEnvironments)
  const isLoadingEnvironments = useTestDataSetsStore(state => state.isLoadingEnvironments)
  const showVariableValues = useTestDataSetsStore(state => state.showVariableValues)
  const showEnvironmentSelector = useTestDataSetsStore(state => state.showEnvironmentSelector)
  const selectedTemplate = useTestDataSetsStore(state => state.selectedTemplate)
  const isGenerating = useTestDataSetsStore(state => state.isGenerating)
  const isSaving = useTestDataSetsStore(state => state.isSaving)
  const tempConstraints = useTestDataSetsStore(state => state.tempConstraints)
  const newTag = useTestDataSetsStore(state => state.newTag)
  const activeTab = useTestDataSetsStore(state => state.activeTab)
  const variables = useTestDataSetsStore(state => state.variables) // raw variables for logic not needing current value
  const editingVariable = useTestDataSetsStore(state => state.editingVariable)
  // New states for loading a single data set
  const isLoadingDataSet = useTestDataSetsStore(state => state.isLoadingDataSet)
  const loadDataSetError = useTestDataSetsStore(state => state.loadDataSetError)

  // Call selectors to get computed values with memoization
  const variablesWithCurrentValues = useMemo(() => getVariablesWithCurrentValues(), [variables, activeEnvironmentId])
  const activeEnvironmentData = useMemo(() => getActiveEnvironment(), [userEnvironments, activeEnvironmentId])
  const editingVariableWithValue = useMemo(() => getEditingVariableWithValue(), [editingVariable, activeEnvironmentId])
  const canSave = useMemo(() => getCanSave(), [formData.name, variables])

  // Load user environments on mount
  useEffect(() => {
    const loadEnvironments = async () => {
      // Ensure user context is available
      if (!user?.teamId || !user?.companyId) {
        // If not loading and no environments yet, it might be an initial state.
        // If already loading, let it proceed. If environments exist, perhaps no reload needed.
        // Consider if setLoadingEnvironments(false) is always correct here.
        // For now, let's assume if no user IDs, we can't load, so set loading to false.
        if (useTestDataSetsStore.getState().isLoadingEnvironments) {
            setLoadingEnvironments(false)
        }
        return
      }

      // Prevent re-loading if environments are already loaded or being loaded
      // unless user context has changed (which is handled by dependency array).
      // This check is more about preventing multiple calls on rapid re-renders.
      if (useTestDataSetsStore.getState().userEnvironments.length > 0 && !useTestDataSetsStore.getState().isLoadingEnvironments) {
        // return; // Optionally, skip if environments are already present
      }

      try {
        setLoadingEnvironments(true)
        // Explicitly type the response if possible, using 'any' can hide issues
        const response: { success: boolean; dataEnvironments?: DataEnvironment[]; error?: string; total?: number } =
          await dataEnvironmentApi.getAll({ isActive: true })

        if (response.success) {
          let environments = response.dataEnvironments || []
          if (!Array.isArray(environments)) {
            environments = []
          }
          setUserEnvironments(environments)
        } else {
          throw new Error(response.error || 'Failed to load environments')
        }
      } catch (error) {
        toast.error("Error loading environments", {
          description: error instanceof Error ? error.message : "Failed to load environments. Please try again."
        })
      } finally {
        setLoadingEnvironments(false)
      }
    }

    loadEnvironments()
  }, [user?.teamId, user?.companyId, setLoadingEnvironments, setUserEnvironments])

  // Auto-select first environment if none is selected
  useEffect(() => {
    const state = useTestDataSetsStore.getState()
    if (!state.isLoadingEnvironments &&
        Array.isArray(state.userEnvironments) &&
        state.userEnvironments.length > 0 &&
        !state.activeEnvironmentId) {
      setActiveEnvironment(state.userEnvironments[0].id)
    }
  }, [userEnvironments, isLoadingEnvironments, activeEnvironmentId, setActiveEnvironment])

  // Form handlers
  const handleFormChange = useCallback((field: string, value: any) => {
    updateFormData({ [field]: value })
  }, [updateFormData])

  const handleAddTag = useCallback(() => {
    if (newTag.trim()) { // Use newTag directly from store state
      addTag(newTag)
      setNewTag('')
    }
  }, [newTag, addTag, setNewTag])

  const handleRemoveTag = useCallback((tag: string) => {
    removeTag(tag)
  }, [removeTag])

  // Variable handlers
  const handleUpdateVariable = useCallback((id: string, field: string, value: any) => {
    if (field === 'value') {
      if (!activeEnvironmentId) {
        toast.info("No active environment", { description: "Please select an environment to set the value." });
        return
      }
      updateVariableValue(id, activeEnvironmentId, value)
    } else {
      updateVariable(id, { [field]: value })
    }
  }, [activeEnvironmentId, updateVariableValue, updateVariable])

  const handleAddVariable = useCallback(() => {
    addVariable()
  }, [addVariable])

  const handleRemoveVariable = useCallback((id: string) => {
    removeVariable(id)
  }, [removeVariable])

  const handleDuplicateVariable = useCallback((variableInput: VariableInput) => {
    const variableToDuplicate = useTestDataSetsStore.getState().variables.find(v => v.id === variableInput.id);

    if (!variableToDuplicate) {
      toast.error("Error duplicating", { description: `Variable with ID "${variableInput.id}" not found.`} );
      return;
    }
    // The variable fetched from store (variableToDuplicate) is guaranteed to have isRequired: boolean
    duplicateVariable(variableToDuplicate);
    toast.success("Variable duplicated", {
      description: `"${variableToDuplicate.name}" variable has been duplicated.`
    })
  }, [duplicateVariable])

  // Environment handlers
  const handleEnvironmentChange = useCallback((environmentId: string) => {
    setActiveEnvironment(environmentId)
  }, [setActiveEnvironment])

  const handleToggleEnvironment = useCallback((environmentId: string) => {
    toggleEnvironmentSelection(environmentId)
  }, [toggleEnvironmentSelection])

  // Import variables from a data source
  const importVariablesFromSource = useCallback((sourceVariables: Array<{ 
    name: string; 
    value: string; 
    sourceType?: string; 
    sourceId?: string; 
    jsonPath?: string;
    dbMode?: string;
    needsColumnMapping?: boolean;
    nameColumn?: string;
    valueColumn?: string;
  }>) => {
    const currentActiveEnvId = useTestDataSetsStore.getState().activeEnvironmentId;
    
    const newVariables: TestDataVariable[] = sourceVariables.map(v => ({
      id: uuidv4(),
      name: v.name,
      type: 'string', // Default type to string
      description: `Imported from ${v.sourceType || 'source'}`,
      isRequired: false,
      format: '',
      constraints: {},
      environmentValues: {
        [currentActiveEnvId]: v.value // Set value for current active environment
      },
      // Add source information for API and Database variables
      sourceType: v.sourceType as any,
      sourceId: v.sourceId,
      jsonPath: v.jsonPath,
      // Database source fields
      dbMode: v.dbMode,
      needsColumnMapping: v.needsColumnMapping,
      nameColumn: v.nameColumn,
      valueColumn: v.valueColumn,
    }));

    setVariables(newVariables);
    toast.success("Variables Imported", {
      description: `${newVariables.length} variables have been imported and replaced existing ones.`,
    });
  }, [setVariables]);

  // Generation handlers
  const handleGenerateData = useCallback(async (selectedGenEnvironmentIds: string[], creativityLevel?: number) => {
    if (!formData.aiPrompt.trim()) {
      toast.error("Prompt required", {
        description: "Please describe what kind of data you want to generate."
      })
      return
    }

    if (selectedGenEnvironmentIds.length === 0) {
      toast.error("No environments selected", {
        description: "Please select at least one environment to generate variables for."
      })
      return
    }

    try {
      setGenerating(true)
      const currentStoreState = useTestDataSetsStore.getState();

      const selectedEnvironmentsList = Array.isArray(currentStoreState.userEnvironments)
        ? currentStoreState.userEnvironments.filter((env: DataEnvironment) => selectedGenEnvironmentIds.includes(env.id))
        : []

      const environmentsForAI = selectedEnvironmentsList.map((env: DataEnvironment) => ({
        id: env.id,
        name: env.name,
        description: env.description || "",
        type: env.type
      }))

      const result = await generateVariableDefinitions({
        prompt: formData.aiPrompt,
        environments: environmentsForAI,
        creativityLevel: creativityLevel
      })

      if (!result.success) {
        throw new Error(result.error || "Failed to generate variable definitions")
      }

      const generatedData = result.data

      if (!generatedData?.variables || !Array.isArray(generatedData.variables)) {
        throw new Error("Invalid response format from AI")
      }

      setVariables([]) // Clear existing variables first

      const newVars: TestDataVariable[] = generatedData.variables.map((generatedVar: any) => {
        const variableId = `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        const environmentValues: Record<string, string> = {}

        if (generatedVar.environmentValues) {
          Object.entries(generatedVar.environmentValues).forEach(([envId, val]) => {
            if (selectedGenEnvironmentIds.includes(envId)) {
              environmentValues[envId] = val as string
            }
          })

          // Fallback: if no environment values were matched, include all environment values
          // This can happen if AI returns different environment IDs than expected
          if (Object.keys(environmentValues).length === 0) {
            Object.entries(generatedVar.environmentValues).forEach(([envId, val]) => {
              environmentValues[envId] = val as string
            })
          }
        }

        return {
          id: variableId,
          name: generatedVar.name || "Untitled Variable",
          type: generatedVar.type || 'string',
          description: generatedVar.description || "",
          isRequired: typeof generatedVar.isRequired === 'boolean' ? generatedVar.isRequired : false,
          constraints: generatedVar.constraints || {},
          format: generatedVar.format || "",
          environmentValues
        }
      })

      setVariables(newVars) // Set all new variables at once

      toast.success("Variables generated!", {
        description: `AI has created ${newVars.length} variables with values for ${selectedEnvironmentsList.length} environments.`
      })

    } catch (error) {
      toast.error("Generation failed", {
        description: error instanceof Error ? error.message : "Failed to generate variables. Please try again."
      })
    } finally {
      setGenerating(false)
    }
  }, [formData.aiPrompt, setGenerating, setVariables])

  const handleGenerateDataSetStructure = useCallback(async (selectedGenEnvironmentIds: string[], creativityLevel?: number) => {
    if (!formData.aiPrompt?.trim()) {
      toast.error("Prompt required", {
        description: "Please describe what kind of data set you want to generate."
      })
      return
    }

    try {
      setGenerating(true)
      const currentStoreState = useTestDataSetsStore.getState();

      const selectedEnvironmentsList = Array.isArray(currentStoreState.userEnvironments)
        ? currentStoreState.userEnvironments.filter((env: DataEnvironment) => selectedGenEnvironmentIds.includes(env.id))
        : []

      const environmentsForAI = selectedEnvironmentsList.map((env: DataEnvironment) => ({
        id: env.id,
        name: env.name,
        description: env.description || "",
        type: env.type
      }))

      const result = await generateDataSetStructure({
        prompt: formData.aiPrompt,
        environments: environmentsForAI,
        creativityLevel: creativityLevel
      })

      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to generate data set structure")
      }

      const generatedDataSet = result.data as APICreateDataSetRequest
      const validEnvironments: EnvironmentType[] = ["development", "testing", "staging", "production", "all"]
      let finalEnvironment: EnvironmentType = 'development'
      if (generatedDataSet.environment && validEnvironments.includes(generatedDataSet.environment as EnvironmentType)) {
        finalEnvironment = generatedDataSet.environment as EnvironmentType
      }

      updateFormData({
        name: generatedDataSet.name,
        description: generatedDataSet.description,
        tags: generatedDataSet.tags || [],
        environment: finalEnvironment,
      })

      if (generatedDataSet.tags && generatedDataSet.tags.length > 0) {
        setNewTag('')
      }

      setVariables([])
      if (generatedDataSet.variables && Array.isArray(generatedDataSet.variables)) {
        const newVars: TestDataVariable[] = generatedDataSet.variables.map((generatedVar: any) => {
          const variableId = `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          const environmentValues: Record<string, string> = {}

          if (generatedVar.environmentValues && typeof generatedVar.environmentValues === 'object') {
             Object.entries(generatedVar.environmentValues).forEach(([envId, val]) => {
                if (selectedGenEnvironmentIds.length === 0 || selectedGenEnvironmentIds.includes(envId)) {
                    environmentValues[envId] = String(val)
                }
             })

             // Fallback: if no environment values were matched and we have selected environments, include all AI-generated values
             if (Object.keys(environmentValues).length === 0 && selectedGenEnvironmentIds.length > 0) {
               Object.entries(generatedVar.environmentValues).forEach(([envId, val]) => {
                 environmentValues[envId] = String(val)
               })
             }
          }

          return {
            id: variableId,
            name: generatedVar.name || "Untitled Variable",
            type: generatedVar.type || 'string',
            description: generatedVar.description || "",
            isRequired: typeof generatedVar.isRequired === 'boolean' ? generatedVar.isRequired : false,
            constraints: (generatedVar.constraints || {}) as ValueConstraints,
            format: generatedVar.format || "",
            environmentValues
          }
        })
        setVariables(newVars)
      }

      toast.success("Data set structure generated!", {
        description: `AI has populated the form for "${generatedDataSet.name}". Please review and save.`
      })

    } catch (error) {
      toast.error("Generation failed", {
        description: error instanceof Error ? error.message : "Failed to generate data set structure. Please try again."
      })
    } finally {
      setGenerating(false)
    }
  }, [formData.aiPrompt, setGenerating, updateFormData, setNewTag, setVariables])

  const handleRandomizeData = useCallback(async (selectedGenEnvironmentIds: string[]) => {
    const currentVariables = useTestDataSetsStore.getState().variables; // Get current variables
    if (currentVariables.length === 0) {
      toast.error("No variables found", { description: "You need to add variables first." })
      return
    }
    if (selectedGenEnvironmentIds.length === 0) {
      toast.error("No environments selected", { description: "Please select at least one environment to randomize data for." })
      return
    }

    try {
      setGenerating(true)
      const currentStoreState = useTestDataSetsStore.getState();

      const selectedEnvironmentsList = Array.isArray(currentStoreState.userEnvironments)
        ? currentStoreState.userEnvironments.filter((env: DataEnvironment) => selectedGenEnvironmentIds.includes(env.id))
        : []

      const variablesForAI = currentVariables.map(variable => ({
        name: variable.name,
        type: variable.type,
        description: variable.description || "",
        constraints: variable.constraints
      }))

      const environmentsForAI = selectedEnvironmentsList.map((env: DataEnvironment) => ({
        id: env.id,
        name: env.name,
        description: env.description || "",
        type: env.type
      }))

      const datasetMetadata = {
        name: formData.name || "Untitled Data Set",
        description: formData.description || "",
        tags: formData.tags || [],
        aiPrompt: formData.aiPrompt || ""
      }

      const result = await generateTestData({
        variables: variablesForAI,
        environments: environmentsForAI,
        datasetMetadata: datasetMetadata
      })

      if (!result.success) {
        throw new Error(result.error || "Failed to generate test data")
      }
      const generatedData = result.data
      if (!generatedData?.variables || !Array.isArray(generatedData.variables)) {
        throw new Error("Invalid response format from AI")
      }

      generatedData.variables.forEach((generatedVar: any) => {
        const variable = currentVariables.find(v => v.name === generatedVar.name)
        if (variable && generatedVar.environmentValues) {
          Object.entries(generatedVar.environmentValues).forEach(([envId, val]) => {
            // Update values for all environments that AI generated, but only if they're in our selected environments
            // This ensures we don't lose data for environments that weren't selected for randomization
            if (selectedGenEnvironmentIds.includes(envId)) {
              updateVariableValue(variable.id, envId, val as string)
            }
          })
        }
      })

      toast.success("Data randomized!", {
        description: `AI has generated test values for ${currentVariables.length} variables across ${selectedEnvironmentsList.length} environments.`
      })

    } catch (error) {
      toast.error("Randomization failed", {
        description: error instanceof Error ? error.message : "Failed to generate test data. Please try again."
      })
    } finally {
      setGenerating(false)
    }
  }, [formData, setGenerating, updateVariableValue])

  // Constraint handlers
  const handleEditConstraints = useCallback((variableInput: VariableInput) => {
    const variableToEdit = useTestDataSetsStore.getState().variables.find(v => v.id === variableInput.id);

    if (!variableToEdit) {
        toast.error("Error editing constraints", { description: `Variable with ID "${variableInput.id}" not found.`} );
        return;
    }
    // The variable fetched from store (variableToEdit) is guaranteed to have isRequired: boolean
    setEditingVariable(variableToEdit);
  }, [setEditingVariable])

  const handleUpdateConstraint = useCallback((key: keyof ValueConstraints, value: any) => {
    updateTempConstraint(key, value)
  }, [updateTempConstraint])

  const handleSaveConstraints = useCallback(() => {
    const currentEditingVariable = useTestDataSetsStore.getState().editingVariable;
    if (!currentEditingVariable) return

    updateVariable(currentEditingVariable.id, {
      constraints: tempConstraints as ValueConstraints
    })
    setEditingVariable(null)

    toast.success("Constraints saved", {
      description: "Variable constraints have been successfully updated."
    })
  }, [tempConstraints, updateVariable, setEditingVariable])

  const handleCancelConstraints = useCallback(() => {
    setEditingVariable(null)
  }, [setEditingVariable])

  // Utility handlers
  const handleCopyValue = useCallback(async (value: string) => {
    try {
      await navigator.clipboard.writeText(value)
      toast.success("Value copied!", {
        description: "Variable value has been copied to clipboard."
      })
    } catch (error) {
      toast.error("Copy failed", {
        description: "Failed to copy value to clipboard. Please try again."
      })
    }
  }, [])
  
  const mapStoreEnvTypeToApiEnvType = (storeEnvType: 'default' | 'custom' | undefined): EnvironmentType => {
    if (storeEnvType === 'custom') return 'development'; // Or some other mapping rule for 'custom'
    // For 'default' or undefined, map to a valid API EnvironmentType, e.g., 'development'
    // This ensures we always pass a valid EnvironmentType to the API.
    // Check if activeEnvironmentData itself has a valid mapping or default to 'development'
    const currentActiveEnv = getActiveEnvironment();
    if(currentActiveEnv && currentActiveEnv.type !== 'default' && 
       currentActiveEnv.type !== 'custom' &&
       ["development", "testing", "staging", "production", "all"].includes(currentActiveEnv.type)) {
        return currentActiveEnv.type as EnvironmentType;
    }
    return 'development'; // Fallback default
  };

  const prepareComprehensiveData = useCallback(() => {
    const currentStoreState = useTestDataSetsStore.getState();
    const selectedEnvs = Array.isArray(currentStoreState.userEnvironments)
      ? currentStoreState.userEnvironments.filter((env: DataEnvironment) => currentStoreState.selectedEnvironments.includes(env.id))
      : []
    return {
      name: formData.name.trim(),
      description: formData.description.trim(),
      tags: formData.tags,
      environments: selectedEnvs.map(env => ({
        id: env.id, name: env.name, description: env.description, color: env.color, type: env.type, isActive: env.isActive
      })),
      variables: currentStoreState.variables.map(variable => ({
        id: variable.id, // Include ID for proper loading
        name: variable.name, type: variable.type, description: variable.description || "",
        isRequired: variable.isRequired || false, constraints: variable.constraints, format: variable.format,
        // Source integration fields
        sourceType: variable.sourceType,
        sourceId: variable.sourceId,
        jsonPath: variable.jsonPath,
        // Database source fields
        dbMode: variable.dbMode,
        needsColumnMapping: variable.needsColumnMapping,
        nameColumn: variable.nameColumn,
        valueColumn: variable.valueColumn,
        environmentValues: Object.fromEntries(
          Object.entries(variable.environmentValues)
            .filter(([envId]) => currentStoreState.selectedEnvironments.includes(envId))
            .map(([envId, value]) => {
              const env = selectedEnvs.find(e => e.id === envId)
              return [env?.name || envId, { value, environmentId: envId, environmentName: env?.name || "Unknown" }]
            })
        )
      })),
      totalVariables: currentStoreState.variables.length,
      totalEnvironments: selectedEnvs.length
    };
  }, [formData]);

  // Prepares variables for API create (Omit<APIDataVariable, 'id'>[])
  const prepareApiCreateVariables = useCallback((): Omit<APIDataVariable, 'id'>[] => {
      const currentStoreState = useTestDataSetsStore.getState();
      const currentActiveEnvId = currentStoreState.activeEnvironmentId;
      return currentStoreState.variables.map(variable => ({
        name: variable.name,
        type: variable.type,
        value: variable.environmentValues[currentActiveEnvId] || "", 
        description: variable.description || "",
        format: variable.format,
        constraints: variable.constraints as ValueConstraints
      }));
  }, []);

  // Prepares variables for API update (APIDataVariable[])
  const prepareApiUpdateVariables = useCallback((): APIDataVariable[] => {
    const currentStoreState = useTestDataSetsStore.getState();
    const currentActiveEnvId = currentStoreState.activeEnvironmentId;
    return currentStoreState.variables.map(variable => ({
      id: variable.id,
      name: variable.name,
      type: variable.type,
      value: variable.environmentValues[currentActiveEnvId] || "", 
      description: variable.description || "",
      format: variable.format,
      constraints: variable.constraints as ValueConstraints
    }));
  }, []);

  const handleCopyAllEnvironmentsAsJson = useCallback(async () => {
    try {
      const comprehensiveData = prepareComprehensiveData();
      const jsonData = { ...comprehensiveData, copiedAt: new Date().toISOString() };
      await navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2))
      toast.success("Complete JSON copied!", {
        description: `Data set with ${jsonData.totalVariables} variables across ${jsonData.totalEnvironments} selected environments copied to clipboard.`
      })
    } catch (error) {
      toast.error("Copy failed", {
        description: "Failed to copy JSON to clipboard. Please try again."
      })
    }
  }, [prepareComprehensiveData])

  const handleUpdateDataSet = useCallback(async (dataSetId?: string) => {
    if (!formData.name.trim()) {
      toast.error("Name required", { description: "Please enter a name for the data set." }); return;
    }
    if (variables.length === 0) { // Using raw variables from store state
      toast.error("Variables required", { description: "Please add at least one variable." }); return;
    }
    if (selectedEnvironments.length === 0) { // Using selectedEnvironments from store state
      toast.error("Environment selection required", { description: "Please select at least one environment for the data set." }); return;
    }
    if (!user?.teamId || !user?.companyId) {
      toast.error("Authentication required", { description: "Team ID and Company ID are required. Please log in again." }); return;
    }
    if (!dataSetId) {
      toast.error("Data set ID required", { description: "Data set ID is required for update operation." }); return;
    }

    try {
      setSaving(true)

      const comprehensiveData = prepareComprehensiveData();
      const apiUpdateVariables = prepareApiUpdateVariables();

      // Ensure activeEnvironmentData is available for determining environment type
      const currentActiveEnvData = getActiveEnvironment();

      const apiEnvType = mapStoreEnvTypeToApiEnvType(currentActiveEnvData?.type);

      const requestData: UpdateDataSetRequest = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        tags: formData.tags,
        environment: apiEnvType,
        metadata: comprehensiveData
      }

      const result = await dataSetApi.update(dataSetId, requestData)

      if (!result.success) {
        throw new Error(result.error || "Failed to update data set")
      }

      toast.success("Data set updated!", {
        description: `"${formData.name}" has been successfully updated with ${comprehensiveData.totalVariables} variables across ${comprehensiveData.totalEnvironments} selected environments.`
      })

      setTimeout(() => {
        router.push("/test-data")
      }, 1000)

    } catch (error) {
      setSaving(false)
      toast.error("Update failed", {
        description: error instanceof Error ? error.message : "Failed to update data set. Please try again."
      })
    } finally {
      setSaving(false)
    }
  }, [router, user, formData, variables, selectedEnvironments, activeEnvironmentId, getActiveEnvironment, prepareComprehensiveData, prepareApiUpdateVariables, setSaving, mapStoreEnvTypeToApiEnvType])

  const handleSaveDataSet = useCallback(async () => {
    if (!formData.name.trim()) {
      toast.error("Name required", { description: "Please enter a name for the data set." }); return;
    }
    if (variables.length === 0) { // Using raw variables from store state
      toast.error("Variables required", { description: "Please add at least one variable." }); return;
    }
    if (selectedEnvironments.length === 0) { // Using selectedEnvironments from store state
      toast.error("Environment selection required", { description: "Please select at least one environment for the data set." }); return;
    }
    if (!user?.teamId || !user?.companyId) {
      toast.error("Authentication required", { description: "Team ID and Company ID are required. Please log in again." }); return;
    }

    try {
      setSaving(true)

      const comprehensiveData = prepareComprehensiveData();
      const apiCreateVariables = prepareApiCreateVariables();
      const currentActiveEnvData = getActiveEnvironment();

      const apiEnvType = mapStoreEnvTypeToApiEnvType(currentActiveEnvData?.type);

      const requestData: APICreateDataSetRequest = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        variables: apiCreateVariables,
        tags: formData.tags,
        environment: apiEnvType,
        metadata: comprehensiveData
      }

      const result = await dataSetApi.create(requestData)

      if (!result.success) {
        throw new Error(result.error || "Failed to save data set")
      }

      toast.success("Data set saved!", {
        description: `"${formData.name}" has been successfully created with ${apiCreateVariables.length} variables across ${comprehensiveData.totalEnvironments} selected environments.`
      })

      resetFormAndVariables()

      setTimeout(() => {
        router.push("/test-data")
      }, 1000)

    } catch (error) {
      toast.error("Save failed", {
        description: error instanceof Error ? error.message : "Failed to save data set. Please try again."
      })
    } finally {
      setSaving(false)
    }
  }, [router, user, formData, variables, selectedEnvironments, activeEnvironmentId, getActiveEnvironment, prepareComprehensiveData, prepareApiCreateVariables, setSaving, resetFormAndVariables, mapStoreEnvTypeToApiEnvType])

  // Clear all variables handler
  const handleClearAll = useCallback(() => {
    setVariables([])
    toast.success("Variables cleared", {
      description: "All variables have been removed from the data set."
    })
  }, [setVariables])

  // Navigation handlers
  const handleBack = useCallback(() => {
    router.push("/test-data")
  }, [router])

  const loadDataSetForEditing = useCallback(async (dataSetId: string) => {
    if (!dataSetId) {
      setLoadDataSetError("Data set ID is required");
      return;
    }

    setLoadingDataSet(true);
    setLoadDataSetError(null);

    try {
      console.log('Loading data set for editing:', dataSetId);
      const result = await dataSetApi.getById(dataSetId);
      console.log('Data set API result:', result);

      if (!result.success || !result.data) {
        throw new Error(result.error || "Failed to load data set or data set not found");
      }

      // Extract data set from response
      const dataSet = result.data;
      console.log('Loaded data set:', dataSet);
      console.log('Data set structure:', JSON.stringify(dataSet, null, 2));

      // Populate store with loaded data
      updateFormData({
        name: dataSet.name,
        description: dataSet.description,
        tags: dataSet.tags || [],
        aiPrompt: dataSet.metadata?.aiPrompt || "", // Get aiPrompt from metadata if available
        environment: dataSet.environment,
      });

      let loadedVariables: TestDataVariable[] = [];
      console.log('Processing variables. Metadata:', dataSet.metadata);
      console.log('Metadata variables:', dataSet.metadata?.variables);
      console.log('Basic variables:', dataSet.variables);
      
      if (dataSet.metadata?.variables && Array.isArray(dataSet.metadata.variables)) {
        console.log('Using metadata variables');
        loadedVariables = dataSet.metadata.variables.map((variable: any, index: number) => {
          // Try to use existing ID from metadata if it's a string, otherwise generate one
          const id = typeof variable.id === 'string' ? variable.id : `var-loaded-${Date.now()}-${index}`;
          
          // Reconstruct environmentValues, ensuring keys are environment IDs
          const environmentValues: Record<string, string> = {};
          if (variable.environmentValues && typeof variable.environmentValues === 'object') {
            Object.entries(variable.environmentValues).forEach(([envIdentifier, envValueObj]: [string, any]) => {
              // The metadata stores envValueObj as { value, environmentId, environmentName }
              // We need to ensure we use the environmentId as the key here.
              const envIdToUse = envValueObj?.environmentId || envIdentifier; // Prefer specific environmentId if present
              environmentValues[envIdToUse] = envValueObj?.value || "";
            });
          }
          
          return {
            id: id,
            name: variable.name || "Untitled Variable",
            type: variable.type || 'string',
            description: variable.description || "",
            isRequired: typeof variable.isRequired === 'boolean' ? variable.isRequired : false,
            format: variable.format || "",
            constraints: (variable.constraints || {}) as ValueConstraints,
            environmentValues: environmentValues,
            // Database source fields
            sourceType: variable.sourceType,
            sourceId: variable.sourceId,
            jsonPath: variable.jsonPath,
            dbMode: variable.dbMode,
            needsColumnMapping: variable.needsColumnMapping,
            nameColumn: variable.nameColumn,
            valueColumn: variable.valueColumn,
          };
        });
      } else if (dataSet.variables && Array.isArray(dataSet.variables)) {
        // Fallback to basic variables structure if metadata.variables is not present
        console.log('Using basic variables structure');
        const currentActiveEnvId = useTestDataSetsStore.getState().activeEnvironmentId;
        loadedVariables = dataSet.variables.map((variable: APIDataVariable, index: number) => ({
          id: variable.id || `var-basic-${Date.now()}-${index}`, // Use existing ID if available
          name: variable.name,
          type: variable.type,
          description: variable.description || "",
          isRequired: true, // Default for basic variables from older structure
          format: variable.format || "",
          constraints: (variable.constraints || {}) as ValueConstraints,
          environmentValues: {
            // Initialize all known environments from store, and set value for active one
            ...useTestDataSetsStore.getState().userEnvironments.reduce((acc, env) => {
              acc[env.id] = "";
              return acc;
            }, {} as Record<string, string>),
            [currentActiveEnvId]: variable.value || ""
          },
          // Database source fields (from variable if available)
          sourceType: (variable as any).sourceType,
          sourceId: (variable as any).sourceId,
          jsonPath: (variable as any).jsonPath,
          dbMode: (variable as any).dbMode,
          needsColumnMapping: (variable as any).needsColumnMapping,
          nameColumn: (variable as any).nameColumn,
          valueColumn: (variable as any).valueColumn,
        }));
      }
      console.log('Final loaded variables:', loadedVariables);
      setVariables(loadedVariables);

      // Load selected environments from metadata if available
      if (dataSet.metadata?.environments && Array.isArray(dataSet.metadata.environments)) {
        const selectedEnvIds = dataSet.metadata.environments.map((env: any) => env.id).filter((id: any) => typeof id === 'string');
        setSelectedEnvironments(selectedEnvIds);
      } else {
        // Default to all active environments if no metadata or not an array
        const activeUserEnvs = useTestDataSetsStore.getState().userEnvironments;
        const activeEnvIds = Array.isArray(activeUserEnvs)
            ? activeUserEnvs.filter(env => env.isActive).map(env => env.id)
            : [];
        setSelectedEnvironments(activeEnvIds);
      }

      // If aiPrompt was in metadata, update form data again (or ensure initial updateFormData includes it)
      if (dataSet.metadata?.aiPrompt) {
        updateFormData({ aiPrompt: dataSet.metadata.aiPrompt });
      }

      // Set the active environment based on loaded data or a sensible default
      if (dataSet.metadata?.activeEnvironment?.id) {
        setActiveEnvironment(dataSet.metadata.activeEnvironment.id);
      } else if (useTestDataSetsStore.getState().selectedEnvironments.length > 0) {
        setActiveEnvironment(useTestDataSetsStore.getState().selectedEnvironments[0]);
      }

      toast.success("Data set loaded", { description: `"${dataSet.name}" has been loaded for editing.` });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error loading data set";
      setLoadDataSetError(errorMessage);
      toast.error("Load failed", { description: errorMessage });
    } finally {
      setLoadingDataSet(false);
    }
  }, [setLoadDataSetError, setLoadingDataSet, updateFormData, setVariables, setSelectedEnvironments, setActiveEnvironment]); // Dependencies: individual store methods

  // Define what's returned from the hook
  return {
    // Store state values (prefer direct access where selectors aren't complex transformations)
    formData,
    variables: variablesWithCurrentValues, // This is the memoized, computed version for UI
    rawVariables: variables, // Expose raw variables if needed for logic not requiring current env value
    activeEnvironmentId,
    userEnvironments: Array.isArray(userEnvironments) ? userEnvironments : [],
    selectedEnvironments: Array.isArray(selectedEnvironments) ? selectedEnvironments : [],
    isLoadingEnvironments,
    showVariableValues,
    showEnvironmentSelector,
    selectedTemplate,
    isGenerating,
    isSaving,
    editingVariable: editingVariableWithValue, // This is the memoized, computed version
    tempConstraints,
    newTag,
    activeTab,
    isLoadingDataSet, // Expose new loading state
    loadDataSetError, // Expose new error state

    // Store actions (using extracted methods to avoid store reference changes)
    setNewTag,
    setActiveTab,
    setActiveEnvironment,
    setShowVariableValues,
    setShowEnvironmentSelector,
    setSelectedTemplate,
    reset,
    
    // Computed values (already derived using selectors)
    canSave,
    activeEnvironmentData,

    // Callback Handlers
    handleFormChange,
    handleAddTag,
    handleRemoveTag,
    handleUpdateVariable,
    handleAddVariable,
    handleRemoveVariable,
    handleDuplicateVariable,
    handleEnvironmentChange,
    handleToggleEnvironment,
    handleGenerateData,
    handleGenerateDataSetStructure,
    handleRandomizeData,
    handleEditConstraints,
    handleUpdateConstraint,
    handleSaveConstraints,
    handleCancelConstraints,
    handleCopyValue,
    handleCopyAllEnvironmentsAsJson,
    handleSaveDataSet,
    handleUpdateDataSet,
    handleBack,
    handleClearAll,
    loadDataSetForEditing, // Expose the new function
    importVariablesFromSource, // Expose the new handler
    resetFormAndVariables, // Expose the reset function
  }
}
