"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye, EyeOff, Loader2, Check, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/lib/utils/toast-utils"
import { AuthCard } from "./auth-card"
import { authApi } from "@/lib/api"

interface RegisterFormProps {
  onSuccess?: () => void
}

export function RegisterForm({ onSuccess }: RegisterFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    agreeToTerms: false,
  })
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    password: "",
    agreeToTerms: "",
  })

  // Password strength criteria
  const passwordCriteria = [
    { label: "At least 8 characters", test: (pass: string) => pass.length >= 8 },
    { label: "At least one uppercase letter", test: (pass: string) => /[A-Z]/.test(pass) },
    { label: "At least one number", test: (pass: string) => /[0-9]/.test(pass) },
    { label: "At least one special character", test: (pass: string) => /[^A-Za-z0-9]/.test(pass) },
  ]

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, agreeToTerms: checked }))
    if (checked && errors.agreeToTerms) {
      setErrors((prev) => ({ ...prev, agreeToTerms: "" }))
    }
  }

  const validateForm = () => {
    let valid = true
    const newErrors = { ...errors }

    if (!formData.name.trim()) {
      newErrors.name = "Name is required"
      valid = false
    }

    if (!formData.email) {
      newErrors.email = "Email is required"
      valid = false
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid"
      valid = false
    }

    if (!formData.password) {
      newErrors.password = "Password is required"
      valid = false
    } else {
      // Check if password meets all criteria
      const meetsAllCriteria = passwordCriteria.every((criteria) => criteria.test(formData.password))
      if (!meetsAllCriteria) {
        newErrors.password = "Password doesn't meet all requirements"
        valid = false
      }
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the terms and conditions"
      valid = false
    }

    setErrors(newErrors)
    return valid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Kayıt işlemi devre dışı olduğu için uyarı göster
    toast.error("Kayıt işlemi devre dışı", {
      description: "Kayıt işlemi şu anda devre dışıdır."
    })

    return

    // Aşağıdaki kod devre dışı bırakıldı
    if (!validateForm()) return

    setIsLoading(true)

    try {
      // Make API call to the backend registration endpoint using centralized API
      console.log("Sending registration data:", {
        email: formData.email,
        password: formData.password,
        name: formData.name.trim(),
        accountType: "personal"
      });

      // Use the centralized API module
      const response = await authApi.register({
        email: formData.email,
        password: formData.password,
        name: formData.name.trim(),
        accountType: "personal",
        role: "user"
      });

      if (!response.success) {
        throw new Error(response.error || "Registration failed")
      }

      // Get the data from the response
      const data = response.data;

      // Show success toast
      toast.success("Registration successful!", {
        description: "Your account has been created successfully.",
      })

      // Success
      if (onSuccess) {
        onSuccess()
      } else {
        // Short delay to allow toast to be seen
        setTimeout(() => {
          router.push("/auth/login")
        }, 1000)
      }
    } catch (error: any) {
      console.error("Registration failed:", error)
      // Show error toast
      toast.error("Registration failed", {
        description: error.message || "Something went wrong. Please try again.",
      })

      // Handle specific API errors if returned
      if (error.message.includes("email") || error.message.toLowerCase().includes("already exists")) {
        setErrors(prev => ({
          ...prev,
          email: "This email is already registered"
        }))
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthCard>
      <div className="mb-8">
        <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Create an account</h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Sign up to get started with HiRafi</p>
        </motion.div>

        {/* Kayıt devre dışı uyarısı */}
        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-800">Kayıt işlemi devre dışı</h3>
              <p className="text-sm text-amber-700 mt-1">
                Bireysel Kullanıcılar için kayıt işlemi şu anda devre dışıdır.
              </p>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name</Label>
          <Input
            id="name"
            name="name"
            type="text"
            placeholder="John Doe"
            autoComplete="name"
            value={formData.name}
            onChange={handleChange}
            className={errors.name ? "border-red-500 focus-visible:ring-red-500" : ""}
            disabled={isLoading}
          />
          {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            autoComplete="email"
            value={formData.email}
            onChange={handleChange}
            className={errors.email ? "border-red-500 focus-visible:ring-red-500" : ""}
            disabled={isLoading}
          />
          {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <Input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              placeholder="••••••••"
              autoComplete="new-password"
              value={formData.password}
              onChange={handleChange}
              className={errors.password ? "border-red-500 focus-visible:ring-red-500 pr-10" : "pr-10"}
              disabled={isLoading}
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}

          {/* Password strength indicators */}
          <div className="space-y-2 mt-2">
            {passwordCriteria.map((criteria, index) => (
              <div key={index} className="flex items-center">
                {criteria.test(formData.password) ? (
                  <Check size={14} className="text-green-500 mr-2" />
                ) : (
                  <X size={14} className="text-gray-400 mr-2" />
                )}
                <span
                  className={`text-xs ${
                    criteria.test(formData.password)
                      ? "text-green-600 dark:text-green-400"
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {criteria.label}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-start space-x-2">
            <Checkbox
              id="terms"
              checked={formData.agreeToTerms}
              onCheckedChange={handleCheckboxChange}
              disabled={isLoading}
              className={errors.agreeToTerms ? "border-red-500 text-red-500" : ""}
            />
            <div className="grid gap-1.5 leading-none">
              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700 dark:text-gray-300"
              >
                I agree to the{" "}
                <a href="#" className="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
                  terms of service
                </a>{" "}
                and{" "}
                <a href="#" className="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">
                  privacy policy
                </a>
              </label>
            </div>
          </div>
          {errors.agreeToTerms && <p className="text-sm text-red-500">{errors.agreeToTerms}</p>}
        </div>

        <Button
          type="submit"
          className="w-full bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating account...
            </>
          ) : (
            "Create account"
          )}
        </Button>
      </form>
    </AuthCard>
  )
}

