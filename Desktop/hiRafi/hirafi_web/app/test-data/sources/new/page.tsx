"use client"

import { useState, useTransition, useRef, useEffect, useCallback } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { uploadDataSourceCsv, dataSourceApi, inspectExcel, parseExcel, testDbConnection, getDbTables, getDbColumns, getDbPreviewData, executeDbFlow, testApiRequest } from "@/lib/api/test-data"
import {
  Database,
  FileSpreadsheet,
  FileText,
  Variable,
  Key,
  Upload,
  ArrowLeft,
  Loader2,
  Save,
  CheckCircle2,
  X,
  Plus,
  Wand2,
  Code,
  Trash2,
  PlusCircle,
  FilePlus,
  Pencil,
  Globe,
  AtSign,
  RefreshCw,
  Search,
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import ReactFlow, {
  Controls,
  Background,
  applyNodeChanges,
  applyEdgeChanges,
  addEdge,
  Node,
  Edge,
  OnNodesChange,
  OnEdgesChange,
  OnConnect,
  getIncomers,
  getOutgoers,
} from 'reactflow';
import 'reactflow/dist/style.css';
import '@/components/query-builder/nodes.css';
import { CreateDataSourceRequest } from "@/types/test-data"
import InsertNode from '@/components/query-builder/InsertNode';
import UpdateNode from '@/components/query-builder/UpdateNode';
import DeleteNode from '@/components/query-builder/DeleteNode';
import SelectNode from '@/components/query-builder/SelectNode';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

// VariableRow component for testing individual variables
const VariableRow = ({ 
  variable, 
  apiUrl, 
  apiMethod, 
  apiHeaders, 
  apiParams, 
  apiBodyType, 
  apiBody, 
  apiFormData, 
  apiUrlEncoded, 
  apiRawBody, 
  jsonPath, 
  transform, 
  onDelete,
  onUpdate
}: {
  variable: { name: string; value: string };
  apiUrl: string;
  apiMethod: string;
  apiHeaders: Array<{ key: string; value: string }>;
  apiParams: Array<{ key: string; value: string; enabled: boolean }>;
  apiBodyType: string;
  apiBody: string;
  apiFormData: Array<{ key: string; value: string; enabled: boolean }>;
  apiUrlEncoded: Array<{ key: string; value: string; enabled: boolean }>;
  apiRawBody: string;
  jsonPath: string;
  transform?: string;
  onDelete: () => void;
  onUpdate: (name: string, newValue: string) => void;
}) => {
  const [currentValue, setCurrentValue] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const testCurrentValue = async () => {
    setIsLoading(true);
    setCurrentValue(null);
    
    try {
      // Build query string
      const enabledParams = apiParams.filter(p => p.enabled && p.key && p.value);
      const queryString = enabledParams.length > 0 
        ? '?' + enabledParams.map(p => `${encodeURIComponent(p.key)}=${encodeURIComponent(p.value)}`).join('&')
        : '';
      
      const fullUrl = apiUrl + queryString;
      
      // Prepare body based on body type
      let body: string | undefined;
      if (apiMethod !== 'GET' && apiMethod !== 'HEAD') {
        switch (apiBodyType) {
          case 'json':
            body = apiBody.trim() || undefined;
            break;
          case 'formdata':
            const formData = new FormData();
            apiFormData.filter(item => item.enabled && item.key).forEach(item => {
              formData.append(item.key, item.value);
            });
            body = formData as any;
            break;
          case 'urlencoded':
            const urlEncodedData = apiUrlEncoded
              .filter(item => item.enabled && item.key)
              .map(item => `${encodeURIComponent(item.key)}=${encodeURIComponent(item.value)}`)
              .join('&');
            body = urlEncodedData || undefined;
            break;
          case 'raw':
            body = apiRawBody.trim() || undefined;
            break;
        }
      }
      
      const result = await testApiRequest({
        method: apiMethod,
        url: fullUrl,
        headers: apiHeaders.filter(h => h.key && h.value),
        body: body || '',
        bodyType: apiBodyType,
        params: apiParams.filter(p => p.enabled).map(p => ({ key: p.key, value: p.value }))
      });
      
      if (result.success && result.data) {
        // Extract the value using jsonPath
        const extractedValue = getValueByPath(result.data, jsonPath);
        
        if (extractedValue !== undefined) {
          let transformedValue = String(extractedValue);
          
          // Apply transformation
          switch (transform) {
            case 'uppercase':
              transformedValue = transformedValue.toUpperCase();
              break;
            case 'lowercase':
              transformedValue = transformedValue.toLowerCase();
              break;
            case 'trim':
              transformedValue = transformedValue.trim();
              break;
          }
          
          // Update the last value immediately
          onUpdate(variable.name, transformedValue);
          
          // Show current value temporarily
          setCurrentValue(transformedValue);
          
          // Auto-clear current value after 3 seconds
          setTimeout(() => {
            setCurrentValue(null);
          }, 3000);
          
        } else {
          toast({
            title: "Value Not Found",
            description: `Could not extract value from path: ${jsonPath}`,
            variant: "destructive",
          });
        }
      } else {
        throw new Error(result.error || 'API request failed');
      }
      
    } catch (error) {
      toast({
        title: "Test Failed",
        description: `Failed to fetch current value: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getValueByPath = (obj: any, path: string): any => {
    if (!path) return obj;
    
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current == null) return undefined;
      
      if (key.includes('[') && key.includes(']')) {
        const [arrayKey, indexStr] = key.split('[');
        const index = parseInt(indexStr.replace(']', ''));
        current = current[arrayKey];
        if (Array.isArray(current) && index >= 0 && index < current.length) {
          current = current[index];
        } else {
          return undefined;
        }
      } else {
        current = current[key];
      }
    }
    
    return current;
  };

  return (
    <div className="grid grid-cols-4 gap-4 p-3 items-center">
      <div className="flex items-center justify-center">
        <code className="text-sm bg-gray-100 px-2 py-1 rounded font-mono text-indigo-600">
          @{variable.name}
        </code>
      </div>
      <div className="text-sm text-gray-700 break-all font-mono flex items-center justify-center">
        <span className="bg-gray-100 px-2 py-1 rounded text-gray-800">
          {variable.value}
        </span>
      </div>
      <div className="flex items-center justify-center">
        {currentValue ? (
          <span className="text-sm bg-green-100 px-2 py-1 rounded text-green-800 font-mono">
            {currentValue}
          </span>
        ) : (
          <Button
            size="sm"
            variant="outline"
            onClick={testCurrentValue}
            disabled={isLoading}
            className="text-xs"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
                Testing...
              </>
            ) : (
              <>
                <RefreshCw className="h-3 w-3 mr-1" />
                Test
              </>
            )}
          </Button>
        )}
      </div>
      <div className="flex justify-center">
        <Button
          size="sm"
          variant="outline"
          className="h-8 w-8 p-0 bg-red-50 border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 transition-colors"
          onClick={onDelete}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

// Veri kaynağı türleri
type DataSourceType = "database" | "csv" | "excel" | "api"

// --- Görsel Sihirbaz Tipleri ---
type FlowStepOperation = 'INSERT' | 'UPDATE' | 'DELETE' | 'SELECT';
type FlowStepCondition = { id: string; col: string; op: string; val: string; };
type FlowStepField = { id: string; col: string; val: string; };

interface FlowStep {
  id: string;
  type: FlowStepOperation;
  tableName: string;
  referenceName: string;
  fields: FlowStepField[];
  conditions: FlowStepCondition[];
  columns: string[]; 
  isFetchingColumns: boolean;
}

interface DataSource {
  id: string
  name: string
  type: DataSourceType
  description: string
  connectionString?: string
  filePath?: string
  variables?: Array<{ name: string; value: string }>;
  isActive: boolean
  lastUpdated: string
}

let id = 0;
const getId = () => `dndnode_${id++}`;

const nodeTypes = { 
  insert: InsertNode,
  update: UpdateNode,
  delete: DeleteNode,
  select: SelectNode,
};

export default function NewDataSourcePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isPending, startTransition] = useTransition()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [activeTab, setActiveTab] = useState<DataSourceType>("database")
  const [isSaving, setIsSaving] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [saveSuccess, setSaveSuccess] = useState(false)
  const [connectionSuccess, setConnectionSuccess] = useState(false)

  // DB connection states
  const [dbTables, setDbTables] = useState<string[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [isFetchingTables, setIsFetchingTables] = useState(false);
  const [dbColumns, setDbColumns] = useState<string[]>([]);
  const [selectedNameColumn, setSelectedNameColumn] = useState<string>('');
  const [selectedValueColumn, setSelectedValueColumn] = useState<string>('');
  const [isFetchingColumns, setIsFetchingColumns] = useState(false);
  const [dbPreviewData, setDbPreviewData] = useState<{name: string, value: string}[]>([]);
  const [isFetchingPreview, setIsFetchingPreview] = useState(false);

  const [dbConfigMode, setDbConfigMode] = useState<'read' | 'visual' | 'raw'>('read');

  // Raw SQL Tab State
  const [rawSetupQuery, setRawSetupQuery] = useState('');

  // --- React Flow State Yönetimi ---
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [queryPreview, setQueryPreview] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionLogs, setExecutionLogs] = useState<string[]>([]);

  const onNodesChange: OnNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes]
  );
  const onEdgesChange: OnEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );
  const onConnect: OnConnect = useCallback(
    (connection) => {
      setEdges((eds) => addEdge(connection, eds));

      setNodes((nds) => {
        const sourceNode = nds.find((node) => node.id === connection.source);
        const targetNode = nds.find((node) => node.id === connection.target);

        if (!sourceNode || !targetNode) {
          return nds;
        }

        let newTargetData = { ...targetNode.data, version: (targetNode.data.version || 0) + 1 };
        let hasUpdate = false;
        
        // INSERT -> UPDATE or INSERT -> DELETE
        if (sourceNode.type === 'insert' && (targetNode.type === 'update' || targetNode.type === 'delete')) {
          const firstField = sourceNode.data.fields?.[0];
          if (firstField && firstField.col) {
            newTargetData.conditions = [{ ...targetNode.data.conditions[0], col: firstField.col, val: firstField.val }];
            if((targetNode.type === 'delete' || targetNode.type === 'update') && sourceNode.data.table) {
                newTargetData.table = sourceNode.data.table;
            }
            hasUpdate = true;
          }
        }
        // UPDATE -> DELETE
        else if (sourceNode.type === 'update' && targetNode.type === 'delete') {
          const firstCondition = sourceNode.data.conditions?.[0];
           if (firstCondition && firstCondition.col) {
            newTargetData.conditions = [{ ...targetNode.data.conditions[0], ...firstCondition }];
            if(sourceNode.data.table) {
                newTargetData.table = sourceNode.data.table;
            }
            hasUpdate = true;
          }
        }

        if (hasUpdate) {
            return nds.map(node => {
                if (node.id === connection.target) {
                    return { ...node, data: newTargetData };
                }
                return node;
            });
        }

        return nds;
      });
    },
    [setEdges]
  );
  
  const onNodesDelete: (nodes: Node[]) => void = useCallback(
    (deleted) => {
      setEdges(
        deleted.reduce((acc, node) => {
          const incomers = getIncomers(node, nodes, edges);
          const outgoers = getOutgoers(node, nodes, edges);
          const connectedEdges = incomers.concat(outgoers);
          return acc.filter((edge) => !connectedEdges.find((ce) => ce.id === edge.id));
        }, edges)
      );
    },
    [nodes, edges, setEdges]
  );

  const updateNodeData = (nodeId: string, data: any) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          node.data = { ...node.data, ...data };
        }
        return node;
      })
    );
  };
  
  const onTableSelectForNode = async (tableName: string) => {
    if (!dataSource.connectionString) return [];
    try {
      const result = await getDbColumns(dataSource.connectionString, tableName);
      if (result.success && result.data?.columns) {
        return result.data.columns;
      }
      toast({ title: "Sütun Hatası", description: result.error || "Sütunlar getirilemedi.", variant: "destructive" });
    } catch (error: any) {
      toast({ title: "Hata", description: error.message, variant: "destructive" });
    }
    return [];
  };

  const addNode = useCallback((type: string) => {
    const id = getId();
    const position = {
        x: (Math.random() * 400) + 50,
        y: (Math.random() * 400) + 50,
      };
    
    const commonData = {
      onDelete: () => {
          setNodes((nds) => nds.filter((node) => node.id !== id));
          onNodesDelete([{id, position, data: {}} as Node]);
      },
      dbTables: dbTables,
      connectionString: dataSource.connectionString,
      updateData: (data: any) => updateNodeData(id, data),
      onTableSelect: onTableSelectForNode,
      version: Date.now(),
    };

    let nodeData;
    switch(type) {
        case 'insert':
            nodeData = { ...commonData, table: '', fields: [{ col: '', val: '' }] };
            break;
        case 'update':
            nodeData = { ...commonData, table: '', fields: [{ col: '', val: '' }], conditions: [{ col: '', op: '=', val: '' }] };
            break;
        case 'delete':
            nodeData = { ...commonData, table: '', conditions: [{ col: '', op: '=', val: '' }] };
            break;
        case 'select':
            nodeData = { 
              ...commonData, 
              table: '', 
              selectedColumns: ['*'],
              whereConditions: [{ col: '', operator: '=', val: '' }],
              orderBy: '',
              orderDirection: 'ASC',
              limitValue: ''
            };
            break;
        default:
            nodeData = { ...commonData, label: 'Unknown Node' };
    }

    const newNode = {
      id,
      type,
      position,
      data: nodeData,
      className: 'query-builder-node',
    };
    setNodes((nds) => nds.concat(newNode));
  }, [setNodes, onNodesDelete, dbTables, onTableSelectForNode]);

  const clearCanvas = () => {
    setNodes([]);
    setEdges([]);
  };

  // --- Bitiş: React Flow State ---

  const [dataSource, setDataSource] = useState<Partial<DataSource>>({
    name: "",
    type: "database",
    description: "",
    connectionString: "",
    filePath: "",
    variables: [],
    isActive: true,
  })

  // CSV file handling states
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvVariables, setCsvVariables] = useState<Array<{
    id: string;
    name: string;
    value: string;
  }>>([])
  const [isUploadingCsv, setIsUploadingCsv] = useState(false)
  const csvFileInputRef = useRef<HTMLInputElement>(null)

  // Excel file handling states
  const [excelFile, setExcelFile] = useState<File | null>(null);
  const [excelSheets, setExcelSheets] = useState<string[]>([]);
  const [selectedSheet, setSelectedSheet] = useState<string>('');
  const [excelVariables, setExcelVariables] = useState<Array<{ id: string; name: string; value: string; }>>([]);
  const [excelHeaders, setExcelHeaders] = useState<string[]>([]);
  const [isProcessingExcel, setIsProcessingExcel] = useState(false);
  const excelFileInputRef = useRef<HTMLInputElement>(null);

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);
  
  // Veri kaynağı türünü değiştirme
  const handleTypeChange = (type: DataSourceType) => {
    // Eğer API tab'ından çıkılıyorsa, tüm API state'lerini temizle
    if (activeTab === 'api' && type !== 'api') {
      // API Test sonuçlarını temizle
      setApiTestResult(null);
      
      // Parsing rule'larını temizle
      setApiParsingRules([]);
      
      // Generated variables'ları temizle
      setExtractedVariables([]);
      
      // JSON path selection'ı temizle
      setSelectedJsonPath('');
      setExpandedPaths(new Set());
      
      // Test state'lerini temizle
      setTestingVariables(new Set());
      setTemporaryValues({});
      setLastValues({});
      
      // Step'i 1'e resetle
      setApiCurrentStep(1);
      
      // Step completion state'lerini temizle
      setApiStepCompleted({
        request: false,
        response: false,
        parsing: false
      });
      
      console.log('🧹 API states cleared - switched away from API tab');
    }
    
    setDataSource({ ...dataSource, type })
    setActiveTab(type)
  }

  const handleClearCsv = () => {
    setCsvFile(null);
    setCsvVariables([]);
    setDataSource(prev => ({ ...prev, filePath: "" }));
    if (csvFileInputRef.current) {
      csvFileInputRef.current.value = "";
    }
    toast({
      title: "CSV Verileri Temizlendi",
      description: "Yükleme iptal edildi. Yeni bir dosya seçebilirsiniz.",
    })
  };

  // CSV file handling functions
  const handleCsvFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast({
        title: "Geçersiz dosya türü",
        description: "Lütfen sadece .csv uzantılı dosyalar yükleyin.",
        variant: "destructive",
      })
      return
    }

    setCsvFile(file)
    setDataSource({ ...dataSource, filePath: file.name })
    setIsUploadingCsv(true)
    
    try {
      const result = await uploadDataSourceCsv(file)
      
      if (result.success && result.data) {
        // The fetch wrapper might be double-wrapping the data object.
        const variables = result.data.variables || (result.data as any).data?.variables;
        setCsvVariables(variables || []);
        toast({
          title: "CSV dosyası yüklendi",
          description: `${result.data.totalRows || variables?.length || 0} değişken başarıyla bulundu.`,
          variant: "default",
        })
      } else {
        toast({
          title: "CSV yükleme hatası",
          description: result.error || "Dosya yüklenirken bir hata oluştu.",
          variant: "destructive",
        })
        setCsvFile(null)
        setDataSource({ ...dataSource, filePath: "" })
      }
    } catch (error) {
      toast({
        title: "CSV yükleme hatası",
        description: "Dosya yüklenirken beklenmeyen bir hata oluştu.",
        variant: "destructive",
      })
      setCsvFile(null)
      setDataSource({ ...dataSource, filePath: "" })
    } finally {
      setIsUploadingCsv(false)
    }
  }

  const handleCsvUploadClick = () => {
    csvFileInputRef.current?.click()
  }

  const handleAddCsvVariable = () => {
    setCsvVariables([...csvVariables, { id: Math.random().toString(), name: '', value: '' }]);
  };

  // Excel file handling functions
  const handleExcelFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls')) {
      toast({
        title: "Geçersiz dosya türü",
        description: "Lütfen sadece .xlsx veya .xls uzantılı dosyalar yükleyin.",
        variant: "destructive",
      });
      return;
    }

    setExcelFile(file);
    setDataSource(prev => ({ ...prev, filePath: file.name }));
    setIsProcessingExcel(true);
    setExcelSheets([]);
    setSelectedSheet('');
    setExcelVariables([]);
    setExcelHeaders([]);

    try {
      const result = await inspectExcel(file);

      if (result.success && result.data?.sheetNames) {
        setExcelSheets(result.data.sheetNames);
        if (result.data.sheetNames.length === 1) {
          const sheetName = result.data.sheetNames[0];
          setSelectedSheet(sheetName);
          // Automatically parse the single sheet
          await handleSheetParse(file, sheetName);
        }
      } else {
        toast({
          title: "Excel dosyası incelenemedi",
          description: result.error || "Dosya yapısı okunurken bir hata oluştu.",
          variant: "destructive",
        });
        setExcelFile(null);
        setDataSource(prev => ({ ...prev, filePath: "" }));
      }
    } catch (error: any) {
      toast({
        title: "Excel işleme hatası",
        description: error.message || "Dosya işlenirken beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsProcessingExcel(false);
    }
  };

  const handleClearExcel = () => {
    setExcelFile(null);
    setExcelSheets([]);
    setSelectedSheet('');
    setExcelVariables([]);
    setExcelHeaders([]);
    setDataSource(prev => ({ ...prev, filePath: "" }));
    if (excelFileInputRef.current) {
      excelFileInputRef.current.value = "";
    }
    toast({
      title: "Excel Verileri Temizlendi",
      description: "Yükleme iptal edildi. Yeni bir dosya seçebilirsiniz.",
    });
  };

  const handleClearConnection = () => {
    setConnectionSuccess(false);
    setDataSource(prev => ({ ...prev, connectionString: '' }));
    setDbTables([]);
    setSelectedTable('');
    setDbColumns([]);
    setSelectedNameColumn('');
    setSelectedValueColumn('');
    setDbPreviewData([]);
    toast({
      title: "Veritabanı Bağlantısı Kapatıldı",
      description: "Yeni bir bağlantı dizesi girebilirsiniz.",
    });
  };

  const handleTestConnection = async () => {
    if (!dataSource.connectionString) {
      toast({
        title: "Bağlantı Dizesi Eksik",
        description: "Lütfen test etmeden önce bir bağlantı dizesi girin.",
        variant: "destructive",
      });
      return;
    }

    setIsTesting(true);
    setConnectionSuccess(false);
    setDbTables([]);
    setSelectedTable('');
    setDbColumns([]);
    setSelectedNameColumn('');
    setSelectedValueColumn('');
    setDbPreviewData([]);

    try {
      const result = await testDbConnection(dataSource.connectionString);
      if (result.success) {
        toast({
          title: "Bağlantı Başarılı",
          description: "Veritabanı tabloları getiriliyor...",
          variant: "default",
        });
        setConnectionSuccess(true);
        
        // Fetch tables
        setIsFetchingTables(true);
        const tablesResult = await getDbTables(dataSource.connectionString);
        if (tablesResult.success && tablesResult.data?.tables) {
          setDbTables(tablesResult.data.tables);
          toast({
            title: "Tablolar Getirildi",
            description: `${tablesResult.data.tables.length} tablo bulundu. Lütfen birini seçin.`,
          });
        } else {
          toast({
            title: "Tablo Hatası",
            description: tablesResult.error || "Tablolar getirilirken bir hata oluştu.",
            variant: "destructive",
          });
        }
        setIsFetchingTables(false);

      } else {
        toast({
          title: "Bağlantı Hatası",
          description: result.error || "Bağlantı kurulamadı. Lütfen bilgileri kontrol edin.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "İstek Hatası",
        description: error.message || "Bağlantı testi sırasında bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleSheetParse = async (file: File, sheetName: string) => {
    setIsProcessingExcel(true);
    setExcelVariables([]);
    try {
      const result = await parseExcel(file, sheetName);
      
      if (result.success && result.data?.variables) {
        setExcelVariables(result.data.variables);
        toast({
          title: "Sayfa başarıyla okundu",
          description: `'${sheetName}' sayfasından ${result.data.totalRows} değişken bulundu.`,
        });
      } else {
        toast({
          title: "Sayfa okuma hatası",
          description: result.error || "Sayfa verileri okunurken bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Excel parse hatası",
        description: error.message || "Sayfa işlenirken beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsProcessingExcel(false);
    }
  };

  const handleExcelUploadClick = () => {
    excelFileInputRef.current?.click();
  };

  const handleAddExcelVariable = () => {
    setExcelVariables([...excelVariables, { id: Math.random().toString(), name: '', value: '' }]);
  };

  const handleSheetSelectionChange = (sheetName: string) => {
    if (excelFile && sheetName) {
      setSelectedSheet(sheetName);
      handleSheetParse(excelFile, sheetName);
    }
  };

  const handleTableSelection = async (table: string) => {
    setSelectedTable(table);
    setDbColumns([]);
    setSelectedNameColumn('');
    setSelectedValueColumn('');
    setDbPreviewData([]);
    setIsFetchingColumns(true);

    if (!dataSource.connectionString) {
      toast({ title: "Hata", description: "Bağlantı dizesi bulunamadı.", variant: "destructive" });
      setIsFetchingColumns(false);
      return;
    }

    try {
      const result = await getDbColumns(dataSource.connectionString, table);
      if (result.success && result.data?.columns) {
        setDbColumns(result.data.columns);
        toast({
          title: "Sütunlar Getirildi",
          description: `'${table}' tablosu için ${result.data.columns.length} sütun bulundu.`,
        });
      } else {
        toast({
          title: "Sütun Hatası",
          description: result.error || "Sütunlar getirilirken bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({ title: "Hata", description: error.message, variant: "destructive" });
    } finally {
      setIsFetchingColumns(false);
    }
  };

  const [variables, setVariables] = useState<{ name: string; value: string }[]>([]);

  useEffect(() => {
    const fetchPreviewData = async () => {
      if (dataSource.connectionString && selectedTable && selectedNameColumn && selectedValueColumn) {
        setIsFetchingPreview(true);
        setDbPreviewData([]);
        try {
          const result = await getDbPreviewData(dataSource.connectionString, selectedTable, selectedNameColumn, selectedValueColumn);
          if (result.success && result.data) {
            const transformedData = result.data.map((item: {name: any, value: any}) => ({ name: String(item.name), value: String(item.value) }));
            setDbPreviewData(transformedData);
            setVariables(transformedData); // Veriyi ana 'variables' state'ine de aktarıyoruz.
            toast({
              title: "Veri Yüklendi",
              description: `Tablodan ${transformedData.length} satır veri yüklendi.`
            });
          } else {
            toast({
              title: "Veri Hatası",
              description: result.error || "Veri getirilirken bir hata oluştu.",
              variant: "destructive",
            });
          }
        } catch (error: any) {
          toast({ title: "Hata", description: error.message, variant: "destructive" });
        } finally {
          setIsFetchingPreview(false);
        }
      }
    };
    fetchPreviewData();
  }, [selectedNameColumn, selectedValueColumn, selectedTable, dataSource.connectionString]);

  // Veri kaynağını kaydetme
  const handleSave = async () => {
    // Validation
    if (!dataSource.name) {
      toast({
        title: "Geçersiz veri",
        description: "Veri kaynağı adı gereklidir.",
        variant: "destructive",
      })
      return
    }

    if (!dataSource.type) {
      toast({
        title: "Geçersiz veri", 
        description: "Veri kaynağı türü seçilmelidir.",
        variant: "destructive",
      })
      return
    }

    // API specific validation
    if (activeTab === 'api') {
      if (!apiUrl) {
        toast({
          title: "Geçersiz veri",
          description: "API URL gereklidir.",
          variant: "destructive",
        })
        return
      }
      
      if (!apiTestResult) {
        toast({
          title: "Geçersiz veri",
          description: "API test edilmelidir. Lütfen 'Test API' butonuna tıklayın.",
          variant: "destructive",
        })
        return
      }
      
      if (extractedVariables.length === 0) {
        toast({
          title: "Geçersiz veri",
          description: "En az bir variable extraction rule tanımlanmalıdır.",
          variant: "destructive",
        })
        return
      }
    }

    setIsSaving(true);

    const finalPayload: CreateDataSourceRequest = {
        name: dataSource.name || '',
        description: dataSource.description || '',
        type: dataSource.type || 'database',
        isActive: dataSource.isActive,
    };

    if (activeTab === 'csv' || activeTab === 'excel') {
      finalPayload.filePath = dataSource.filePath;
      finalPayload.variables = activeTab === 'csv' ? csvVariables.map(v => ({ name: v.name, value: v.value })) : excelVariables.map(v => ({ name: v.name, value: v.value }));
      finalPayload.config = {
        provider: activeTab,
        filePath: dataSource.filePath,
        sheetName: activeTab === 'excel' ? selectedSheet : undefined,
        data: activeTab === 'csv' ? csvVariables : excelVariables,
      };
    } else if (activeTab === 'database') {
        finalPayload.connectionString = dataSource.connectionString;
        let dbConfig: any = {
            provider: 'database',
            mode: dbConfigMode,
        };

        switch (dbConfigMode) {
            case 'read':
                dbConfig.table = selectedTable;
                dbConfig.nameColumn = selectedNameColumn;
                dbConfig.valueColumn = selectedValueColumn;
                dbConfig.data = dbPreviewData; 
                break;
            case 'visual':
                dbConfig.flow = nodes.map(node => {
                  const { 
                    onDelete, 
                    onTableSelect, 
                    dbTables,
                    updateData,
                    ...data 
                  } = node.data;
                  return {
                    id: node.id,
                    type: node.type,
                    position: node.position,
                    data: data, // Temizlenmiş data objesini sakla
                  };
                });
                break;
            case 'raw':
                dbConfig.setupQuery = rawSetupQuery;
                break;
        }
        finalPayload.config = dbConfig;
    } else if (activeTab === 'api') {
        // API configuration with comprehensive data
        finalPayload.config = {
            provider: 'api',
            
            // API Request Configuration (flattened)
            url: apiUrl,
            method: apiMethod,
            headers: buildFinalHeaders(),
            params: apiParams.filter(p => p.enabled && p.key && p.value),
            bodyType: apiBodyType,
            body: buildFinalBody() as any,
            authType: apiAuthType,
            authConfig: apiAuth,
            
            // Variable Extraction Rules
            extractionRules: apiParsingRules.filter(rule => rule.variableName && rule.jsonPath).map(rule => ({
                id: rule.id,
                variableName: rule.variableName,
                jsonPath: rule.jsonPath,
                type: rule.type || 'string',
                transform: rule.transform || 'none'
            })),
            
            // Test Results and Metadata
            lastTestResult: apiTestResult,
            lastTestDate: new Date().toISOString(),
            
            // Step Completion State
            currentStep: apiCurrentStep,
            stepsCompleted: apiStepCompleted
        } as any;
        
        // Add extracted variables (variables that will be available as @variable_name)
        finalPayload.variables = extractedVariables.map(v => ({ 
            name: v.name, 
            value: v.value || '',
            lastUpdated: new Date().toISOString()
        }));
    }

    try {
      const result = await dataSourceApi.create(finalPayload)
      if (result.success) {
      toast({
          title: "Kaydedildi!",
          description: "Veri Kaynağı başarıyla oluşturuldu.",
      });
        setTimeout(() => router.push('/test-data'), 1500);
      } else {
        toast({
          title: "Hata",
          description: "Veri Kaynağı kaydedilirken bir hata oluştu.",
        variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Hata",
        description: error.message || "Failed to save data source.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const generateQueryPreview = (nodesToProcess: Node<any>[]) => {
    let preview = '';
    nodesToProcess.forEach((node, index) => {
      const { type, data } = node;
      const stepName = data.table || `step_${index + 1}`;
      preview += `-- Adım ${index + 1}: ${stepName} (${type})\n`;
      
      switch (type) {
        case 'insert':
          const insertCols = data.fields.filter((f: any) => f.col).map((f: any) => f.col).join(', ');
          const insertVals = data.fields.filter((f: any) => f.col).map((f: any) => `'${f.val}'`).join(', ');
          preview += `INSERT INTO ${data.table || '[Tablo Seçilmedi]'} (${insertCols || '...'}) VALUES (${insertVals || '...'});`;
          break;
        case 'update':
          preview += `UPDATE ${data.table || '[Tablo Seçilmedi]'}\n`;
          const setClauses = data.fields.filter((f: any) => f.col).map((f: any) => `SET ${f.col} = '${f.val}'`).join(',\n');
          preview += `${setClauses || 'SET ...'}\n`;
          const whereClausesUpdate = data.conditions.filter((c: any) => c.col).map((c: any) => `${c.col} ${c.op} '${c.val}'`).join(' AND ');
          preview += `WHERE ${whereClausesUpdate || '...'};`;
          break;
        case 'delete':
           preview += `DELETE FROM ${data.table || '[Tablo Seçilmedi]'}\n`;
           const whereClausesDelete = data.conditions.filter((c: any) => c.col).map((c: any) => `${c.col} ${c.op} '${c.val}'`).join(' AND ');
           preview += `WHERE ${whereClausesDelete || '...'};`;
          break;
        case 'select':
          const selectedCols = data.selectedColumns && data.selectedColumns.length > 0 ? data.selectedColumns.join(', ') : '*';
          preview += `SELECT ${selectedCols}\n`;
          preview += `FROM ${data.table || '[Tablo Seçilmedi]'}`;
          
          // WHERE conditions
          const whereClausesSelect = data.whereConditions?.filter((c: any) => c.col).map((c: any) => `${c.col} ${c.operator} '${c.val}'`).join(' AND ');
          if (whereClausesSelect) {
            preview += `\nWHERE ${whereClausesSelect}`;
          }
          
          // ORDER BY
          if (data.orderBy) {
            preview += `\nORDER BY ${data.orderBy} ${data.orderDirection || 'ASC'}`;
          }
          
          // LIMIT
          if (data.limitValue) {
            preview += `\nLIMIT ${data.limitValue}`;
          }
          
          preview += ';';
          break;
        default:
          preview += '-- Bilinmeyen işlem türü';
      }
      preview += '\n\n';
    });
    return preview;
  };
  
  useEffect(() => {
    const preview = generateQueryPreview(nodes);
    setQueryPreview(preview);
  }, [nodes]);

  const handleExecuteFlow = async () => {
    setIsExecuting(true);
    setExecutionLogs(['Executing flow...']);
    
    let flowToExecute: any[] = [];
    if (dbConfigMode === 'visual') {
        flowToExecute = nodes.map(node => {
            const { 
              onDelete, 
              onTableSelect, 
              dbTables,
              updateData,
              table,
              fields,
              conditions,
              selectedColumns,
              whereConditions,
              orderBy,
              orderDirection,
              limitValue,
              referenceName,
              ...restOfData 
            } = node.data;
            
            const baseStep = {
              id: node.id,
              type: node.type,
              position: node.position,
              tableName: table,
              referenceName: referenceName
            };

            // Node tipine göre farklı veri ekle
            if (node.type === 'select') {
              return {
                ...baseStep,
                selectedColumns: selectedColumns,
                whereConditions: whereConditions,
                orderBy: orderBy,
                orderDirection: orderDirection,
                limitValue: limitValue
              };
            } else {
              return {
                ...baseStep,
                fields: fields,
                conditions: conditions
              };
            }
        });
    } else if (dbConfigMode === 'raw') {
        flowToExecute = [{ type: 'raw', query: rawSetupQuery }];
    }

    try {
      const payload = {
        connectionString: dataSource.connectionString!,
        flow: flowToExecute
      };
      
      const result: any = await executeDbFlow(payload.connectionString, payload.flow);
      
      // Backend'den gelen logları execution logs'a ekle
      const backendLogs = result.logs || result.data?.logs || ['No logs returned.'];
      setExecutionLogs(backendLogs);
      
                    // SELECT sonuçları zaten backend loglarında temiz bir şekilde gösteriliyor
      // Ekstra JSON dump'a gerek yok

      if (result.success) {
        toast({
          title: "Flow executed successfully!",
          description: "Check the logs for details.",
          variant: "default",
        });
      } else {
         toast({
          title: "Flow execution failed.",
          description: result.error || 'An unknown error occurred. See logs for more details.',
          variant: "destructive",
        });
      }
    } catch (error: any) {
      setExecutionLogs(prev => [...prev, `[FE] Exception: ${error.message}`]);
      toast({
        title: "Failed to execute flow.",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const [flowSteps, setFlowSteps] = useState<FlowStep[]>([]);

  const addFlowStep = (index: number) => {
    const newStep: FlowStep = {
      id: `step-${Date.now()}`, type: 'INSERT', tableName: '', referenceName: '',
      fields: [{ id: `field-${Date.now()}`, col: '', val: '' }],
      conditions: [{ id: `cond-${Date.now()}`, col: '', op: '=', val: '' }],
      columns: [], isFetchingColumns: false,
    };
    const newSteps = [...flowSteps];
    newSteps.splice(index, 0, newStep);
    setFlowSteps(newSteps);
  };

  const removeFlowStep = (id: string) => {
    setFlowSteps(flowSteps.filter(step => step.id !== id));
  };

  const updateFlowStep = (id: string, updates: Partial<FlowStep>) => {
    setFlowSteps(prevSteps =>
      prevSteps.map(step => (step.id === id ? { ...step, ...updates } : step))
    );
  };

  const handleFlowStepTableChange = async (id: string, tableName: string) => {
    updateFlowStep(id, { 
      tableName, isFetchingColumns: true, columns: [],
      fields: [{ id: `field-${Date.now()}`, col: '', val: '' }],
      conditions: [{ id: `cond-${Date.now()}`, col: '', op: '=', val: '' }] 
    });
    // ... (API call)
  };

  const addFieldToStep = (stepId: string) => {
    const step = flowSteps.find(s => s.id === stepId);
    if (step) {
      const newField: FlowStepField = { id: `field-${Date.now()}`, col: '', val: '' };
      updateFlowStep(stepId, { fields: [...step.fields, newField] });
    }
  };
  
  const removeFieldFromStep = (stepId: string, fieldId: string) => {
    const step = flowSteps.find(s => s.id === stepId);
    if (step) {
      const newFields = step.fields.filter(f => f.id !== fieldId);
      updateFlowStep(stepId, { fields: newFields.length > 0 ? newFields : [{ id: `field-${Date.now()}`, col: '', val: '' }] });
    }
  };

  const updateFieldInStep = (stepId: string, fieldId: string, updates: Partial<FlowStepField>) => {
    const step = flowSteps.find(s => s.id === stepId);
    if (step) {
      const updatedFields = step.fields.map(f => (f.id === fieldId ? { ...f, ...updates } : f));
      updateFlowStep(stepId, { fields: updatedFields });
    }
  };

  const addConditionToStep = (stepId: string) => {
    const step = flowSteps.find(s => s.id === stepId);
    if (step) {
        const newCondition: FlowStepCondition = { id: `cond-${Date.now()}`, col: '', op: '=', val: '' };
        updateFlowStep(stepId, { conditions: [...step.conditions, newCondition] });
    }
  };
  
  const removeConditionFromStep = (stepId: string, conditionId: string) => {
    const step = flowSteps.find(s => s.id === stepId);
    if (step) {
        const newConditions = step.conditions.filter(c => c.id !== conditionId);
        updateFlowStep(stepId, { conditions: newConditions.length > 0 ? newConditions : [{ id: `cond-${Date.now()}`, col: '', op: '=', val: '' }] });
    }
  };

  const updateConditionInStep = (stepId: string, conditionId: string, updates: Partial<FlowStepCondition>) => {
    const step = flowSteps.find(s => s.id === stepId);
    if (step) {
        const updatedConditions = step.conditions.map(c => (c.id === conditionId ? { ...c, ...updates } : c));
        updateFlowStep(stepId, { conditions: updatedConditions });
    }
  };

  // --- API Tab State ---
  const [apiUrl, setApiUrl] = useState('');
  const [apiMethod, setApiMethod] = useState<'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'>('GET');
  const [apiHeaders, setApiHeaders] = useState([{ key: '', value: '' }]);
  const [apiBody, setApiBody] = useState('');
  const [isTestingApi, setIsTestingApi] = useState(false);
  const [apiTestResult, setApiTestResult] = useState<any>(null);
  const [apiActiveTab, setApiActiveTab] = useState('request');

  // Enhanced API Tab States - Postman-like interface
  const [apiParams, setApiParams] = useState([{ key: '', value: '', description: '', enabled: true }]);
  const [apiAuthType, setApiAuthType] = useState<'none' | 'bearer' | 'basic' | 'apikey'>('none');
  const [apiAuth, setApiAuth] = useState({
    bearer: { token: '' },
    basic: { username: '', password: '' },
    apikey: { key: '', value: '', addTo: 'header' as 'header' | 'query' }
  });
  const [apiBodyType, setApiBodyType] = useState<'none' | 'json' | 'formdata' | 'urlencoded' | 'raw'>('json');
  const [apiFormData, setApiFormData] = useState([{ key: '', value: '', type: 'text' as 'text' | 'file', enabled: true }]);
  const [apiUrlEncoded, setApiUrlEncoded] = useState([{ key: '', value: '', enabled: true }]);
  const [apiRawBody, setApiRawBody] = useState('');
  const [apiConfigActiveTab, setApiConfigActiveTab] = useState('params');
  const [showCurlImport, setShowCurlImport] = useState(false);
  const [curlCommand, setCurlCommand] = useState('');

  // Add new step-based states for API wizard
  const [apiCurrentStep, setApiCurrentStep] = useState(1);
  const [apiStepCompleted, setApiStepCompleted] = useState({
    request: false,
    response: false,
    parsing: false
  });

  // API Parsing states
  const [apiParsingRules, setApiParsingRules] = useState<Array<{
    id: string;
    variableName: string;
    jsonPath: string;
    type: 'string' | 'number' | 'boolean';
    transform?: 'uppercase' | 'lowercase' | 'trim' | 'none';
  }>>([]);
  const [extractedVariables, setExtractedVariables] = useState<Array<{
    name: string;
    value: string;
    originalValue?: string;
  }>>([]);
  const [selectedJsonPath, setSelectedJsonPath] = useState<string>('');
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());
  
  // Test functionality states
  const [testingVariables, setTestingVariables] = useState<Set<string>>(new Set());
  const [temporaryValues, setTemporaryValues] = useState<Record<string, string>>({});
  const [lastValues, setLastValues] = useState<Record<string, string>>({});
  const [isGeneratingVariables, setIsGeneratingVariables] = useState(false);

  // --- API Tab State Fonksiyonları ---
  const handleHeaderChange = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...apiHeaders];
    newHeaders[index][field] = value;
    setApiHeaders(newHeaders);
  };
  const addHeader = () => {
    setApiHeaders([...apiHeaders, { key: '', value: '' }]);
  };
  const removeHeader = (index: number) => {
    const newHeaders = apiHeaders.filter((_, i) => i !== index);
    setApiHeaders(newHeaders.length > 0 ? newHeaders : [{ key: '', value: '' }]);
  };

  // Enhanced API Tab Handlers
  const handleParamChange = (index: number, field: 'key' | 'value' | 'description', value: string) => {
    const newParams = [...apiParams];
    newParams[index][field] = value;
    setApiParams(newParams);
  };
  
  const toggleParamEnabled = (index: number) => {
    const newParams = [...apiParams];
    newParams[index].enabled = !newParams[index].enabled;
    setApiParams(newParams);
  };
  
  const addParam = () => {
    setApiParams([...apiParams, { key: '', value: '', description: '', enabled: true }]);
  };
  
  const removeParam = (index: number) => {
    const newParams = apiParams.filter((_, i) => i !== index);
    setApiParams(newParams.length > 0 ? newParams : [{ key: '', value: '', description: '', enabled: true }]);
  };

  const handleFormDataChange = (index: number, field: 'key' | 'value', value: string) => {
    const newFormData = [...apiFormData];
    newFormData[index][field] = value;
    setApiFormData(newFormData);
  };
  
  const toggleFormDataEnabled = (index: number) => {
    const newFormData = [...apiFormData];
    newFormData[index].enabled = !newFormData[index].enabled;
    setApiFormData(newFormData);
  };
  
  const addFormData = () => {
    setApiFormData([...apiFormData, { key: '', value: '', type: 'text', enabled: true }]);
  };
  
  const removeFormData = (index: number) => {
    const newFormData = apiFormData.filter((_, i) => i !== index);
    setApiFormData(newFormData.length > 0 ? newFormData : [{ key: '', value: '', type: 'text', enabled: true }]);
  };

  const handleUrlEncodedChange = (index: number, field: 'key' | 'value', value: string) => {
    const newUrlEncoded = [...apiUrlEncoded];
    newUrlEncoded[index][field] = value;
    setApiUrlEncoded(newUrlEncoded);
  };
  
  const toggleUrlEncodedEnabled = (index: number) => {
    const newUrlEncoded = [...apiUrlEncoded];
    newUrlEncoded[index].enabled = !newUrlEncoded[index].enabled;
    setApiUrlEncoded(newUrlEncoded);
  };
  
  const addUrlEncoded = () => {
    setApiUrlEncoded([...apiUrlEncoded, { key: '', value: '', enabled: true }]);
  };
  
  const removeUrlEncoded = (index: number) => {
    const newUrlEncoded = apiUrlEncoded.filter((_, i) => i !== index);
    setApiUrlEncoded(newUrlEncoded.length > 0 ? newUrlEncoded : [{ key: '', value: '', enabled: true }]);
  };

  // Build final URL with query parameters
  const buildFinalUrl = () => {
    const enabledParams = apiParams.filter(p => p.enabled && p.key);
    if (enabledParams.length === 0) return apiUrl;
    
    const url = new URL(apiUrl);
    enabledParams.forEach(param => {
      url.searchParams.set(param.key, param.value);
    });
    return url.toString();
  };

  // Build final headers with auth
  const buildFinalHeaders = () => {
    const headers = [...apiHeaders.filter(h => h.key && h.value)];
    
    // Add auth header based on type
    switch (apiAuthType) {
      case 'bearer':
        if (apiAuth.bearer.token) {
          headers.push({ key: 'Authorization', value: `Bearer ${apiAuth.bearer.token}` });
        }
        break;
      case 'basic':
        if (apiAuth.basic.username && apiAuth.basic.password) {
          const encoded = btoa(`${apiAuth.basic.username}:${apiAuth.basic.password}`);
          headers.push({ key: 'Authorization', value: `Basic ${encoded}` });
        }
        break;
      case 'apikey':
        if (apiAuth.apikey.key && apiAuth.apikey.value && apiAuth.apikey.addTo === 'header') {
          headers.push({ key: apiAuth.apikey.key, value: apiAuth.apikey.value });
        }
        break;
    }
    
    return headers;
  };

  // Build final body based on type
  const buildFinalBody = () => {
    switch (apiBodyType) {
      case 'json':
        return apiBody;
      case 'formdata':
        const formData = new FormData();
        apiFormData.filter(fd => fd.enabled && fd.key).forEach(fd => {
          formData.append(fd.key, fd.value);
        });
        return formData;
      case 'urlencoded':
        const params = new URLSearchParams();
        apiUrlEncoded.filter(ue => ue.enabled && ue.key).forEach(ue => {
          params.append(ue.key, ue.value);
        });
        return params.toString();
      case 'raw':
        return apiRawBody;
      default:
        return '';
    }
  };

  // Parse cURL command
  const parseCurlCommand = (curl: string) => {
    try {
      // Clean up the curl command
      const cleanCurl = curl.trim().replace(/\\\s*\n\s*/g, ' ').replace(/\s+/g, ' ');
      
      // Reset states first
      setApiHeaders([{ key: '', value: '' }]);
      setApiParams([{ key: '', value: '', description: '', enabled: true }]);
      setApiBody('');
      setApiBodyType('none');
      
      // Extract method first (default to GET if not specified)
      const methodMatch = cleanCurl.match(/-X\s+([A-Z]+)/i);
      if (methodMatch) {
        setApiMethod(methodMatch[1].toUpperCase() as any);
      } else {
        // If no method specified, check for data (-d) which implies POST
        if (cleanCurl.includes('-d ') || cleanCurl.includes('--data')) {
          setApiMethod('POST');
        } else {
          setApiMethod('GET');
        }
      }

      // Extract URL - simplified and more reliable approach
      let extractedUrl = '';
      
      // Try multiple URL extraction patterns
      const urlPatterns = [
        // Pattern 1: curl 'https://...' or curl "https://..."
        /curl\s+['"]([^'"]+)['"]/,
        // Pattern 2: curl https://... (without quotes)
        /curl\s+([^\s-][^\s]*)/,
        // Pattern 3: find any http/https URL in the command
        /(https?:\/\/[^\s'"<>]+)/,
        // Pattern 4: URL after various flags
        /\s(['"]?)(https?:\/\/[^'"\s]+)\1/
      ];
      
      for (const pattern of urlPatterns) {
        const match = cleanCurl.match(pattern);
        if (match) {
          extractedUrl = match[1] || match[2] || match[0];
          break;
        }
      }
      
      if (extractedUrl) {
        // Clean the URL
        extractedUrl = extractedUrl.replace(/['"]/g, '').trim();
        
        // Split URL and query parameters
        const [baseUrl, queryString] = extractedUrl.split('?');
        
        setApiUrl(baseUrl);
        
        // Parse query parameters
        if (queryString) {
          const params = new URLSearchParams(queryString);
          const paramArray: Array<{ key: string; value: string; description: string; enabled: boolean }> = [];
          
          params.forEach((value, key) => {
            paramArray.push({
              key: key,
              value: value,
              description: '',
              enabled: true
            });
          });
          
          if (paramArray.length > 0) {
            setApiParams(paramArray);
          }
        }
      }

      // Extract headers - improved regex
      const headerMatches = [...cleanCurl.matchAll(/-H\s+(['"]?)([^'"]+)\1/g)];
      const newHeaders: { key: string; value: string }[] = [];
      
      for (const match of headerMatches) {
        const headerContent = match[2];
        const colonIndex = headerContent.indexOf(':');
        
        if (colonIndex > 0) {
          const key = headerContent.substring(0, colonIndex).trim();
          const value = headerContent.substring(colonIndex + 1).trim();
          newHeaders.push({ key, value });
        }
      }
      
      if (newHeaders.length > 0) {
        setApiHeaders(newHeaders);
      }

      // Extract body data - simplified and more reliable approach
      let bodyContent = '';
      
      // First try to find quoted body content
      const quotedPatterns = [
        /-d\s+'([^']+)'/g,        // -d 'content'
        /-d\s+"([^"]+)"/g,        // -d "content" 
        /--data\s+'([^']+)'/g,    // --data 'content'
        /--data\s+"([^"]+)"/g,    // --data "content"
        /--data-raw\s+'([^']+)'/g,
        /--data-raw\s+"([^"]+)"/g,
        /--json\s+'([^']+)'/g,
        /--json\s+"([^"]+)"/g
      ];
      
      // Try quoted patterns first
      for (const pattern of quotedPatterns) {
        const matches = [...cleanCurl.matchAll(pattern)];
        if (matches.length > 0) {
          bodyContent = matches[0][1];
          console.log('Found quoted body:', bodyContent);
          break;
        }
      }
      
      // If no quoted content found, try unquoted
      if (!bodyContent) {
        const unquotedPatterns = [
          /-d\s+([^'"\s][^\s]*)/,
          /--data\s+([^'"\s][^\s]*)/,
          /--data-raw\s+([^'"\s][^\s]*)/,
          /--json\s+([^'"\s][^\s]*)/
        ];
        
        for (const pattern of unquotedPatterns) {
          const match = cleanCurl.match(pattern);
          if (match) {
            bodyContent = match[1];
            console.log('Found unquoted body:', bodyContent);
            break;
          }
        }
      }
      
              if (bodyContent) {
          // Clean up the body content
          bodyContent = bodyContent.trim();
          
          // Handle escaped quotes and other escape sequences
          bodyContent = bodyContent.replace(/\\"/g, '"').replace(/\\'/g, "'");
          
          // Try to determine body type and set appropriate state
          if (bodyContent.startsWith('{') && bodyContent.endsWith('}')) {
            // Looks like JSON
            try {
              const parsedJson = JSON.parse(bodyContent);
              
              setApiBodyType('json');
              setApiBody(JSON.stringify(parsedJson, null, 2)); // Format JSON nicely
              
            } catch (jsonError) {
              // If JSON parsing fails, treat as raw
              setApiBodyType('raw');
              setApiRawBody(bodyContent);
            }
          } else if (bodyContent.includes('=') && !bodyContent.includes('{') && !bodyContent.includes('<')) {
            // Looks like form data
            setApiBodyType('urlencoded');
            
            try {
              // Parse as URL encoded data
              const params = new URLSearchParams(bodyContent);
              const formArray: Array<{ key: string; value: string; enabled: boolean }> = [];
              
              params.forEach((value, key) => {
                formArray.push({
                  key: key,
                  value: value,
                  enabled: true
                });
              });
              
              if (formArray.length > 0) {
                setApiUrlEncoded(formArray);
              }
            } catch (urlError) {
              // If URL parsing fails, treat as raw
              setApiBodyType('raw');
              setApiRawBody(bodyContent);
            }
          } else {
            // Treat as raw data
            setApiBodyType('raw');
            setApiRawBody(bodyContent);
          }
        }

      setShowCurlImport(false);
      setCurlCommand('');
      
      toast({
        title: "cURL Imported Successfully",
        description: "Request configuration imported from cURL command",
      });
    } catch (error) {
      toast({
        title: "Import Failed", 
        description: "Could not parse cURL command. Please check the format.",
        variant: "destructive",
      });
    }
  };

  const handleTestApi = async () => {
    setIsTestingApi(true);
    setApiTestResult(null);
    
    try {
      const finalUrl = buildFinalUrl();
      const finalHeaders = buildFinalHeaders();
      let finalBody = buildFinalBody();
      
      // Add API key to query params if needed
      const urlWithApiKey = apiAuthType === 'apikey' && apiAuth.apikey.addTo === 'query' && apiAuth.apikey.key && apiAuth.apikey.value
        ? (() => {
            const url = new URL(finalUrl);
            url.searchParams.set(apiAuth.apikey.key, apiAuth.apikey.value);
            return url.toString();
          })()
        : finalUrl;

      const result = await testApiRequest({
        method: apiMethod,
        url: urlWithApiKey,
        headers: finalHeaders,
        body: typeof finalBody === 'string' ? finalBody : JSON.stringify(finalBody),
        bodyType: apiBodyType,
        params: apiParams
      });
      setApiTestResult(result);
      
      // Mark request step as completed and move to response step
      setApiStepCompleted(prev => ({ ...prev, request: true, response: true }));
      setApiCurrentStep(2);
      
    } catch (error: any) {
      setApiTestResult(error.response?.data || { success: false, error: error.message });
      setApiStepCompleted(prev => ({ ...prev, request: true, response: true }));
      setApiCurrentStep(2);
    } finally {
      setIsTestingApi(false);
    }
  };

  // Add new functions for step navigation
  const handleApiStepNext = () => {
    if (apiCurrentStep < 3) {
      setApiCurrentStep(apiCurrentStep + 1);
    }
  };

  const handleApiStepPrev = () => {
    if (apiCurrentStep > 1) {
      setApiCurrentStep(apiCurrentStep - 1);
    }
  };

  const handleApiStepClick = (step: number) => {
    // Allow navigation to completed steps or current step
    if (step <= apiCurrentStep || (step === 2 && apiStepCompleted.request)) {
      setApiCurrentStep(step);
    }
  };

  const isApiStepAccessible = (step: number) => {
    switch (step) {
      case 1:
        return true;
      case 2:
        return apiStepCompleted.request || apiCurrentStep >= 2;
      case 3:
        return apiStepCompleted.response || apiCurrentStep >= 3;
      default:
        return false;
    }
  };

  // API Parsing functions
  const addParsingRule = () => {
    const newRule = {
      id: `rule-${Date.now()}`,
      variableName: '',
      jsonPath: '',
      type: 'string' as const,
      transform: 'none' as const
    };
    setApiParsingRules([...apiParsingRules, newRule]);
  };

  const removeParsingRule = (id: string) => {
    setApiParsingRules(apiParsingRules.filter(rule => rule.id !== id));
  };

  const updateParsingRule = (id: string, field: string, value: string | boolean | number) => {
    setApiParsingRules(apiParsingRules.map(rule => 
      rule.id === id ? { ...rule, [field]: value } : rule
    ));
  };

  // Helper function to apply transformations
  const applyTransformations = (value: string, rule: any): string => {
    let transformedValue = value;

    // Apply string transformations
    switch (rule.transform) {
      case 'uppercase':
        transformedValue = transformedValue.toUpperCase();
        break;
      case 'lowercase':
        transformedValue = transformedValue.toLowerCase();
        break;
      case 'trim':
        transformedValue = transformedValue.trim();
        break;
      default:
        // 'none' - no transformation
        break;
    }

    return transformedValue;
  };

  const extractVariablesFromResponse = () => {
    setIsGeneratingVariables(true);
    const newExtracted: Array<{ name: string; value: string; originalValue?: string }> = [];

    apiParsingRules.forEach(rule => {
      if (!rule.variableName) return;

      try {
        let originalValue: string;
        
        // Extract from API response
        if (!apiTestResult || !apiTestResult.data) {
          originalValue = '';
        } else {
          // Fix JSON path - remove 'data.' prefix if it exists since we're already in apiTestResult.data
          let cleanPath = rule.jsonPath;
          if (cleanPath.startsWith('data.')) {
            cleanPath = cleanPath.substring(5); // Remove 'data.' prefix
          }
          
          const rawValue = getValueByPath(apiTestResult.data, cleanPath);
          originalValue = rawValue !== undefined && rawValue !== null ? String(rawValue) : '';
        }

        // Apply transformations
        let transformedValue = applyTransformations(originalValue, rule);

        // Type conversion
        if (rule.type === 'number') {
          transformedValue = String(Number(transformedValue) || 0);
        } else if (rule.type === 'boolean') {
          transformedValue = String(Boolean(transformedValue));
        }

        newExtracted.push({
          name: rule.variableName,
          value: '', // Don't populate value here, only on test
          originalValue: originalValue
        });
      } catch (error) {
        console.error(`Error extracting ${rule.variableName}:`, error);
        newExtracted.push({
          name: rule.variableName,
          value: '',
          originalValue: ''
        });
      }
    });

    // Merge with existing variables instead of replacing
    setExtractedVariables(prev => {
      const merged = [...prev];
      
      newExtracted.forEach(newVar => {
        const existingIndex = merged.findIndex(v => v.name === newVar.name);
        if (existingIndex >= 0) {
          // Update existing variable but keep current value if it exists
          merged[existingIndex] = {
            ...newVar,
            value: merged[existingIndex].value // Keep current value
          };
        } else {
          // Add new variable
          merged.push(newVar);
        }
      });
      
      return merged;
    });

    // Set initial lastValues from extracted originalValues
    const initialLastValues: Record<string, string> = {};
    newExtracted.forEach(newVar => {
      if (newVar.originalValue) {
        initialLastValues[newVar.name] = newVar.originalValue;
      }
    });
    
    setLastValues(prev => ({
      ...prev,
      ...initialLastValues
    }));
    
    // Force a re-render to ensure the UI updates
    setTimeout(() => {
      setIsGeneratingVariables(false);
      toast({
        title: "Variables Added",
        description: `Added ${newExtracted.length} variables to the list.`,
      });
    }, 100);
  };

  const updateVariableValue = (variableName: string, newValue: string) => {
    setExtractedVariables(prev => 
      prev.map(variable => 
        variable.name === variableName 
          ? { ...variable, value: newValue }
          : variable
      )
    );
  };

  // Helper function to get value by JSON path
  const getValueByPath = (obj: any, path: string): any => {
    console.log('🔍 getValueByPath called with:');
    console.log('  📊 Object type:', typeof obj);
    console.log('  📊 Object keys:', obj && typeof obj === 'object' ? Object.keys(obj) : 'Not an object');
    console.log('  🎯 Path:', path);
    
    if (!path) {
      console.log('  ❌ Empty path');
      return undefined;
    }
    
    if (!obj || typeof obj !== 'object') {
      console.log('  ❌ Invalid object:', obj);
      return undefined;
    }
    
    // Handle empty path - return the object itself
    if (path === '') {
      console.log('  ✅ Empty path, returning object itself');
      return obj;
    }
    
    try {
      const pathParts = path.split('.');
      console.log('  📝 Path parts:', pathParts);
      
      const result = pathParts.reduce((current, key, index) => {
        console.log(`  📍 Step ${index + 1}/${pathParts.length}: Processing key "${key}"`);
        console.log(`    📦 Current object type:`, typeof current);
        console.log(`    📦 Current object:`, current);
        
        if (current === null || current === undefined) {
          console.log(`    ❌ Current is null/undefined`);
          return undefined;
        }
        
        if (typeof current !== 'object') {
          console.log(`    ❌ Current is not an object, type: ${typeof current}`);
          return undefined;
        }
        
        // Handle array notation like data[0].name
        if (key.includes('[') && key.includes(']')) {
          const arrayKey = key.substring(0, key.indexOf('['));
          const indexStr = key.substring(key.indexOf('[') + 1, key.indexOf(']'));
          const arrayIndex = parseInt(indexStr);
          console.log(`    🔢 Array access: ${arrayKey}[${arrayIndex}]`);
          
          if (current[arrayKey] && Array.isArray(current[arrayKey])) {
            const value = current[arrayKey][arrayIndex];
            console.log(`    ✅ Array value found:`, value);
            return value;
          } else {
            console.log(`    ❌ Array "${arrayKey}" not found or not an array:`, current[arrayKey]);
            console.log(`    🗝️ Available keys:`, Object.keys(current));
            return undefined;
          }
        }
        
        // Regular object property access
        const value = current[key];
        console.log(`    🔑 Property "${key}" value:`, value);
        console.log(`    🔑 Property "${key}" type:`, typeof value);
        
        if (value === undefined) {
          console.log(`    ⚠️ Property "${key}" not found!`);
          console.log(`    🗝️ Available keys:`, Object.keys(current));
          
          // Try case-insensitive search
          const lowerKey = key.toLowerCase();
          const foundKey = Object.keys(current).find(k => k.toLowerCase() === lowerKey);
          if (foundKey) {
            console.log(`    🔍 Found case-insensitive match: "${foundKey}"`);
            return current[foundKey];
          }
        }
        
        return value;
      }, obj);
      
      console.log('  🎉 Final getValueByPath result:', result);
      console.log('  🎉 Final result type:', typeof result);
      return result;
    } catch (error) {
      console.error('  💥 Error in getValueByPath:', error);
      return undefined;
    }
  };

  // Interactive JSON viewer functions
  const toggleExpanded = (path: string) => {
    const newExpanded = new Set(expandedPaths);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedPaths(newExpanded);
  };

  const handleJsonPathSelect = (path: string) => {
    setSelectedJsonPath(path);
  };

  const addSelectedPathAsRule = () => {
    if (!selectedJsonPath) return;
    
    // Generate a better variable name from the path
    const pathParts = selectedJsonPath.split('.');
    const lastPart = pathParts[pathParts.length - 1];
    const suggestedName = lastPart.replace(/\[.*\]/, ''); // Remove array indices
    
    const newRule = {
      id: `rule-${Date.now()}`,
      variableName: suggestedName || 'variable',
      jsonPath: selectedJsonPath,
      type: 'string' as const,
      transform: 'none' as const
    };
    setApiParsingRules([...apiParsingRules, newRule]);
    setSelectedJsonPath('');
    
    toast({
      title: "Variable Rule Added",
      description: `Added variable "${suggestedName}" for path: ${selectedJsonPath}`,
    });
  };

  // Recursive JSON renderer component
  const renderJsonValue = (value: any, path: string = '', depth: number = 0): React.ReactNode => {
    if (value === null) {
      return <span className="text-gray-500 italic">null</span>;
    }
    
    if (typeof value === 'string') {
      return (
        <span 
          className={`text-green-600 cursor-pointer hover:bg-green-50 px-1 rounded 
            ${selectedJsonPath === path ? 'bg-blue-100' : ''}`}
          onClick={() => handleJsonPathSelect(path)}
          title={`Click to select path: ${path}`}
        >
          "{value}"
        </span>
      );
    }
    
    if (typeof value === 'number') {
      return (
        <span 
          className={`text-blue-600 cursor-pointer hover:bg-blue-50 px-1 rounded 
            ${selectedJsonPath === path ? 'bg-blue-100' : ''}`}
          onClick={() => handleJsonPathSelect(path)}
          title={`Click to select path: ${path}`}
        >
          {value}
        </span>
      );
    }
    
    if (typeof value === 'boolean') {
      return (
        <span 
          className={`text-purple-600 cursor-pointer hover:bg-purple-50 px-1 rounded 
            ${selectedJsonPath === path ? 'bg-blue-100' : ''}`}
          onClick={() => handleJsonPathSelect(path)}
          title={`Click to select path: ${path}`}
        >
          {value.toString()}
        </span>
      );
    }
    
    if (Array.isArray(value)) {
      const isExpanded = expandedPaths.has(path);
      return (
        <div className="ml-4">
          <span 
            className="cursor-pointer text-gray-600 hover:text-gray-800"
            onClick={() => toggleExpanded(path)}
          >
            {isExpanded ? '▼' : '▶'} [{value.length} items]
          </span>
          {isExpanded && (
            <div className="ml-4 border-l-2 border-gray-200 pl-2">
              {value.map((item, index) => (
                <div key={index} className="my-1">
                  <span className="text-gray-500">[{index}]: </span>
                  {renderJsonValue(item, `${path}[${index}]`, depth + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    if (typeof value === 'object') {
      const isExpanded = expandedPaths.has(path);
      const keys = Object.keys(value);
      return (
        <div className="ml-4">
          <span 
            className="cursor-pointer text-gray-600 hover:text-gray-800"
            onClick={() => toggleExpanded(path)}
          >
            {isExpanded ? '▼' : '▶'} {'{' + keys.length + ' keys}'}
          </span>
          {isExpanded && (
            <div className="ml-4 border-l-2 border-gray-200 pl-2">
              {keys.map((key) => (
                <div key={key} className="my-1">
                  <span className="text-blue-800 font-medium">{key}: </span>
                  {renderJsonValue(value[key], path ? `${path}.${key}` : key, depth + 1)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    return <span className="text-gray-500">{String(value)}</span>;
  };

  // --- Ayrı Bağlantı Dizesi State'leri ---
  const [dbDetails, setDbDetails] = useState({
    type: 'mysql',
    user: 'root',
    password: '',
    host: 'localhost',
    port: '3306',
    databaseName: ''
  });
  // --- Bitiş ---

  useEffect(() => {
    // Ayrı alanlar değiştikçe ana connectionString state'ini güncelle
    const { type, user, password, host, port, databaseName } = dbDetails;
    if (host && port && databaseName) {
      const newConnectionString = `${type}://${user}${password ? `:${password}` : ''}@${host}:${port}/${databaseName}`;
      setDataSource(prev => ({ ...prev, connectionString: newConnectionString }));
    }
  }, [dbDetails]);

  // API step değiştiğinde state'leri temizle
  useEffect(() => {
    // Step 1'e geçildiğinde (yani başka bir step'ten geri dönüldüğünde) tüm API state'lerini temizle
    if (apiCurrentStep === 1) {
      // API Test sonuçlarını temizle
      setApiTestResult(null);
      
      // Parsing rule'larını temizle
      setApiParsingRules([]);
      
      // Generated variables'ları temizle
      setExtractedVariables([]);
      
      // JSON path selection'ı temizle
      setSelectedJsonPath('');
      setExpandedPaths(new Set());
      
      // Test state'lerini temizle
      setTestingVariables(new Set());
      setTemporaryValues({});
      setLastValues({});
      
      // Step completion state'lerini temizle
      setApiStepCompleted({
        request: false,
        response: false,
        parsing: false
      });
      
      console.log('🧹 API states cleared - returned to step 1');
    }
  }, [apiCurrentStep]);

  const handleDbDetailChange = (field: keyof typeof dbDetails, value: string) => {
    setDbDetails(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">New Data Source</h1>
              <p className="text-white/80">Create a new data source for your test data</p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="bg-white/10 text-white border-white/20 hover:bg-white/20 hover:text-white"
                onClick={() => router.push("/test-data")}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Data Source Information</CardTitle>
                <CardDescription>
                  Enter the details of the data source you will use for your test data.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Data Source Name</Label>
                    <Input
                      id="name"
                      placeholder="Enter data source name"
                      value={dataSource.name}
                      onChange={(e) => setDataSource({ ...dataSource, name: e.target.value })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Active Status</Label>
                    <div className="flex items-center space-x-2 pt-2">
                      <Switch
                        checked={dataSource.isActive}
                        onCheckedChange={(checked) => setDataSource({ ...dataSource, isActive: checked })}
                      />
                      <Label>{dataSource.isActive ? "Active" : "Inactive"}</Label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Enter description about the data source"
                    value={dataSource.description}
                    onChange={(e) => setDataSource({ ...dataSource, description: e.target.value })}
                  />
                </div>

                <div className="space-y-4">
                  <Label>Data Source Type</Label>
                  <Tabs value={activeTab} onValueChange={(value) => handleTypeChange(value as DataSourceType)}>
                    <TabsList className="grid grid-cols-4 mb-4">
                      <TabsTrigger value="database" className="flex items-center gap-2">
                        <Database className="h-4 w-4" />
                        <span>Database</span>
                      </TabsTrigger>
                      <TabsTrigger value="csv" className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span>CSV File</span>
                      </TabsTrigger>
                      <TabsTrigger value="excel" className="flex items-center gap-2">
                        <FileSpreadsheet className="h-4 w-4" />
                        <span>Excel File</span>
                      </TabsTrigger>
                      <TabsTrigger value="api" className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        <span>API</span>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="database" className="space-y-4">
                      <div className="space-y-2">
                        <Label>Connection Details</Label>
                        <div className="flex items-center gap-2 p-2 border rounded-lg bg-slate-50 dark:bg-slate-800/50">
                            <Select value={dbDetails.type} onValueChange={(v) => handleDbDetailChange('type', v)}>
                                <SelectTrigger className="w-[120px]"><SelectValue /></SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="mysql">MySQL</SelectItem>
                                    <SelectItem value="postgresql">PostgreSQL</SelectItem>
                                    <SelectItem value="sqlite3" disabled>SQLite (Yakında)</SelectItem>
                                </SelectContent>
                            </Select>
                            <Input className="flex-1 min-w-[80px]" placeholder="user" value={dbDetails.user} onChange={(e) => handleDbDetailChange('user', e.target.value)} />
                            <Input className="flex-1 min-w-[80px]" type="password" placeholder="password" value={dbDetails.password} onChange={(e) => handleDbDetailChange('password', e.target.value)} />
                            <AtSign className="text-slate-400" />
                            <Input className="w-[120px]" placeholder="host" value={dbDetails.host} onChange={(e) => handleDbDetailChange('host', e.target.value)} />
                            <span className="text-slate-400">:</span>
                            <Input className="w-[70px]" placeholder="port" value={dbDetails.port} onChange={(e) => handleDbDetailChange('port', e.target.value)} />
                            <span className="text-slate-400">/</span>
                            <Input className="flex-1 min-w-[80px]" placeholder="database" value={dbDetails.databaseName} onChange={(e) => handleDbDetailChange('databaseName', e.target.value)} />
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                          {connectionSuccess ? (
                            <div className="flex gap-2">
                              <Button variant="default" className="bg-green-600 hover:bg-green-700 text-white pointer-events-none">
                                <CheckCircle2 className="h-4 w-4 mr-2" />
                                Connection Successful
                              </Button>
                              <Button variant="outline" size="icon" onClick={handleClearConnection}>
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              disabled={isTesting || !dataSource.connectionString}
                              onClick={handleTestConnection}
                            >
                              {isTesting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Key className="h-4 w-4 mr-2" />}
                              Test Connection
                            </Button>
                          )}
                      </div>

                      {connectionSuccess && (
                        <div className="pt-6 space-y-4 border-t border-gray-200 dark:border-gray-800">
                           <Tabs value={dbConfigMode} onValueChange={(value) => setDbConfigMode(value as any)}>
                            <TabsList className="grid w-full grid-cols-3">
                              <TabsTrigger value="read" className="flex items-center gap-2">
                                <FileText className="h-4 w-4" />
                                <span>Create Data Set</span>
                              </TabsTrigger>
                              <TabsTrigger value="visual" className="flex items-center gap-2">
                                <Wand2 className="h-4 w-4" />
                                <span>Query Builder</span>
                              </TabsTrigger>
                              <TabsTrigger value="raw" className="flex items-center gap-2">
                                <Code className="h-4 w-4" />
                                <span>SQL Terminal</span>
                              </TabsTrigger>
                            </TabsList>
                            <TabsContent value="read" className="pt-4">
                           {isFetchingTables ? (
                            <div className="flex items-center space-x-2 text-gray-500">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>Loading tables...</span>
                            </div>
                          ) : dbTables.length > 0 ? (
                            <div className="space-y-6">
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                 <div className="space-y-2">
                                  <Label htmlFor="table-select">Select Table</Label>
                                  <Select value={selectedTable} onValueChange={handleTableSelection}>
                                    <SelectTrigger id="table-select">
                                      <SelectValue placeholder="Select a table..." />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {dbTables.map(table => (
                                        <SelectItem key={table} value={table}>{table}</SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                                
                                {isFetchingColumns ? (
                                  <div className="flex items-center space-x-2 text-gray-500 pt-8 md:col-span-2">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Loading columns...</span>
                                  </div>
                                ) : selectedTable && dbColumns.length > 0 && (
                                  <>
                                    <div className="space-y-2">
                                      <Label htmlFor="name-column-select">"Name" Column</Label>
                                      <Select value={selectedNameColumn} onValueChange={setSelectedNameColumn}>
                                        <SelectTrigger id="name-column-select">
                                          <SelectValue placeholder="Select column for Name" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {dbColumns.map(col => (
                                            <SelectItem key={`name-${col}`} value={col}>{col}</SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                    <div className="space-y-2">
                                      <Label htmlFor="value-column-select">"Value" Column</Label>
                                      <Select value={selectedValueColumn} onValueChange={setSelectedValueColumn}>
                                        <SelectTrigger id="value-column-select">
                                          <SelectValue placeholder="Select column for Value" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {dbColumns.map(col => (
                                            <SelectItem key={`value-${col}`} value={col}>{col}</SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </>
                                )}
                              </div>

                              {isFetchingPreview ? (
                                <div className="flex items-center space-x-2 text-gray-500 pt-4">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span>Loading data...</span>
                                </div>
                              ) : dbPreviewData.length > 0 && (
                                <div className="space-y-4 pt-4">
                                  <h4 className="text-md font-medium text-gray-800">Data Preview</h4>
                                  <div className="border rounded-lg max-h-60 overflow-y-auto">
                                    <div className="grid grid-cols-[1fr_1fr] gap-x-4 p-4 bg-gray-50 border-b">
                                      <p className="font-semibold text-sm text-gray-600">Name</p>
                                      <p className="font-semibold text-sm text-gray-600">Value</p>
                                    </div>
                                    <div className="divide-y">
                                      {dbPreviewData.map((item, index) => (
                                        <div key={index} className="grid grid-cols-[1fr_1fr] gap-x-4 p-4">
                                          <p className="text-sm truncate" title={String(item.name)}>{String(item.name)}</p>
                                          <p className="text-sm truncate" title={String(item.value)}>{String(item.value)}</p>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          ) : (
                            <p className="text-sm text-gray-500">No tables found in this database or tables could not be loaded.</p>
                          )}
                            </TabsContent>
                             <TabsContent value="visual" className="mt-4">
                               <div className="flex flex-col h-[700px] gap-4">
                                 <div className="flex-grow border rounded-lg relative">
                                   <ReactFlow
                                     nodes={nodes}
                                     edges={edges}
                                     onNodesChange={onNodesChange}
                                     onEdgesChange={onEdgesChange}
                                     onConnect={onConnect}
                                     nodeTypes={nodeTypes}
                                     onNodesDelete={onNodesDelete}
                                     proOptions={{ hideAttribution: true }}
                                     fitView
                                   >
                                     <div className="absolute top-4 right-4 z-10 p-1 bg-white dark:bg-gray-800 border rounded-lg shadow-lg flex gap-1">
                                       <Button variant="ghost" size="icon" onClick={() => addNode('select')} title="SELECT Node">
                                          <Search className="h-5 w-5" />
                                       </Button>
                                       <Button variant="ghost" size="icon" onClick={() => addNode('insert')} title="INSERT Node">
                                          <FilePlus className="h-5 w-5" />
                                       </Button>
                                       <Button variant="ghost" size="icon" onClick={() => addNode('update')} title="UPDATE Node">
                                          <Pencil className="h-5 w-5" />
                                       </Button>
                                        <Button variant="ghost" size="icon" onClick={() => addNode('delete')} title="DELETE Node">
                                          <Trash2 className="h-5 w-5" />
                                       </Button>
                                       <div className="border-l mx-1"></div>
                                       <Button variant="ghost" size="icon" onClick={clearCanvas} title="Clear Canvas">
                                          <X className="h-5 w-5 text-red-500" />
                                       </Button>
                                     </div>
                                     <Controls />
                                     <Background />
                                   </ReactFlow>
                                 </div>
                                 <Accordion type="single" collapsible className="w-full">
                                   <AccordionItem value="item-1">
                                     <AccordionTrigger>Generated Query Recipe</AccordionTrigger>
                                     <AccordionContent>
                                       <Textarea 
                                         readOnly
                                         value={queryPreview}
                                         className="font-mono h-48 bg-gray-100 dark:bg-gray-900"
                                       />
                                       <div className="mt-2 flex justify-end">
                                           <Button onClick={handleExecuteFlow} disabled={isExecuting}>
                                               {isExecuting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                               Execute Flow
                                           </Button>
                                       </div>
                                       {executionLogs.length > 1 && (
                                           <div className="mt-4 p-2 bg-gray-900 text-white rounded-md">
                                               <pre className="text-xs whitespace-pre-wrap">
                                                   {executionLogs.join('\n')}
                                               </pre>
                                           </div>
                                       )}
                                     </AccordionContent>
                                   </AccordionItem>
                                 </Accordion>
                               </div>
                             </TabsContent>
                             <TabsContent value="raw" className="pt-4 space-y-4">
                               <div className="space-y-2">
                                  <Label htmlFor="setup-query">SQL Query</Label>
                                  <Textarea
                                    id="setup-query"
                                    placeholder="Enter the SQL query to execute. Ex: INSERT INTO users (name) VALUES ('test_user');"
                                    value={rawSetupQuery}
                                    onChange={(e) => setRawSetupQuery(e.target.value)}
                                    className="min-h-[250px] font-mono"
                                  />
                               </div>
                               <div className="mt-2 flex justify-end">
                                    <Button onClick={handleExecuteFlow} disabled={isExecuting || !rawSetupQuery}>
                                        {isExecuting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                        Execute Flow
                                    </Button>
                                </div>
                                {executionLogs.length > 1 && dbConfigMode === 'raw' && (
                                    <div className="mt-4 p-2 bg-gray-900 text-white rounded-md">
                                        <pre className="text-xs whitespace-pre-wrap">
                                            {executionLogs.join('\n')}
                                        </pre>
                                    </div>
                                )}
                             </TabsContent>
                           </Tabs>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="csv" className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="filepath">File Path</Label>
                        <div className="flex gap-2">
                          <Input
                            id="filepath"
                            placeholder="Click button to upload CSV file"
                            value={dataSource.filePath || ""}
                            readOnly
                            className="flex-1"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={csvFile ? handleClearCsv : handleCsvUploadClick}
                            disabled={isUploadingCsv}
                            title={csvFile ? "Cancel Upload" : "Upload CSV File"}
                          >
                            {isUploadingCsv ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : csvFile ? (
                              <X className="h-4 w-4" />
                            ) : (
                              <Upload className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <input
                          ref={csvFileInputRef}
                          type="file"
                          accept=".csv"
                          onChange={handleCsvFileSelect}
                          className="hidden"
                        />
                      </div>

                      {/* CSV Preview Section with Animation */}
                      <div
                        className={`transition-all duration-500 ease-in-out overflow-hidden ${
                          csvFile && csvVariables && csvVariables.length > 0 ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                        }`}
                      >
                        {csvFile && csvVariables && csvVariables.length > 0 && (
                          <div className="mt-4 border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-950/50">
                            <h4 className="text-center font-semibold mb-4 text-lg text-gray-800 dark:text-gray-200">CSV Preview</h4>
                            
                            <div className="max-h-60 overflow-auto space-y-1 pr-1">
                                <div className="grid grid-cols-2 gap-x-4 pb-2 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-gray-50 dark:bg-gray-950/50 z-10">
                                  <Label className="font-bold text-sm text-gray-600 dark:text-gray-300 px-2 text-center">Name</Label>
                                  <Label className="font-bold text-sm text-gray-600 dark:text-gray-300 px-2 text-center">Value</Label>
                                </div>

                                <div className="divide-y divide-gray-200 dark:divide-gray-800">
                                  {csvVariables.map((variable, index) => (
                                    <div key={variable.id} className="grid grid-cols-2 gap-x-4 items-center py-1.5 px-1 hover:bg-gray-100 dark:hover:bg-gray-800/50">
                                      <div>
                                        <Input
                                          type="text"
                                          value={variable.name}
                                          onChange={(e) => {
                                            const updated = [...csvVariables];
                                            updated[index].name = e.target.value;
                                            setCsvVariables(updated);
                                          }}
                                          className="w-full h-9 bg-transparent border-gray-300 dark:border-gray-600 focus:bg-white dark:focus:bg-gray-800 focus:ring-1 focus:ring-indigo-500"
                                          placeholder="Variable name"
                                        />
                                      </div>
                                      <div>
                                        <Input
                                          type="text"
                                          value={variable.value}
                                          onChange={(e) => {
                                            const updated = [...csvVariables];
                                            updated[index].value = e.target.value;
                                            setCsvVariables(updated);
                                          }}
                                          className="w-full h-9 bg-transparent border-gray-300 dark:border-gray-600 focus:bg-white dark:focus:bg-gray-800 focus:ring-1 focus:ring-indigo-500"
                                          placeholder="Variable value"
                                        />
                                      </div>
                                    </div>
                                  ))}
                                </div>
                            </div>
                            <div className="flex justify-between items-center mt-3">
                              <div className="text-xs font-medium text-white bg-indigo-500 rounded-full px-2.5 py-1">
                                {csvVariables.length} Variables
                              </div>
                               <Button onClick={handleAddCsvVariable} size="sm">
                                <Plus className="h-4 w-4 mr-2" />
                                Add Variable
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>

                      {!csvFile && (
                        <div className="mt-4 bg-green-50 border border-green-200 rounded-md p-4 text-sm text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300">
                          <h4 className="font-medium mb-1">About CSV Files</h4>
                          <p>
                            CSV (Comma-Separated Values) files are plain text files that store data as comma-separated values.
                          </p>
                          <p className="mt-2">The first two columns of each row are taken as <code className="font-bold">name</code> and <code className="font-bold">value</code> respectively. Other columns are ignored.</p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="excel" className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="excelpath">File Path</Label>
                        <div className="flex gap-2">
                          <Input
                            id="excelpath"
                            placeholder="Click button to upload Excel file"
                            value={dataSource.filePath || ""}
                            readOnly
                            className="flex-1"
                          />
                          <Button 
                            variant="outline" 
                            size="icon" 
                            onClick={excelFile ? handleClearExcel : handleExcelUploadClick}
                            disabled={isProcessingExcel}
                            title={excelFile ? "Clear/Replace Current File" : "Upload Excel File"}
                          >
                            {isProcessingExcel ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : excelFile ? (
                              <X className="h-4 w-4" />
                            ) : (
                              <Upload className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                         <input
                          ref={excelFileInputRef}
                          type="file"
                          accept=".xlsx, .xls"
                          onChange={handleExcelFileSelect}
                          className="hidden"
                        />
                      </div>

                      {/* Sheet Selector */}
                      {excelFile && excelSheets.length > 1 && (
                        <div className="space-y-2">
                          <Label htmlFor="sheet-selector">Select Worksheet</Label>
                          <Select
                            value={selectedSheet}
                            onValueChange={handleSheetSelectionChange}
                          >
                            <SelectTrigger id="sheet-selector">
                              <SelectValue placeholder="Select a worksheet..." />
                            </SelectTrigger>
                            <SelectContent>
                              {excelSheets.map(sheet => (
                                <SelectItem key={sheet} value={sheet}>
                                  {sheet}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {/* Excel Preview Section */}
                      <div
                        className={`transition-all duration-500 ease-in-out overflow-hidden ${
                          excelFile && excelVariables.length > 0 ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
                        }`}
                      >
                        {excelFile && excelVariables.length > 0 && (
                          <div className="mt-4 border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-950/50">
                            <h4 className="text-center font-semibold mb-4 text-lg text-gray-800 dark:text-gray-200">Excel Preview</h4>
                            
                            <div className="max-h-60 overflow-auto space-y-1 pr-1">
                                <div className="grid grid-cols-2 gap-x-4 pb-2 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-gray-50 dark:bg-gray-950/50 z-10">
                                  <Label className="font-bold text-sm text-gray-600 dark:text-gray-300 px-2 text-center">Name</Label>
                                  <Label className="font-bold text-sm text-gray-600 dark:text-gray-300 px-2 text-center">Value</Label>
                                </div>

                                <div className="divide-y divide-gray-200 dark:divide-gray-800">
                                  {excelVariables.map((variable, index) => (
                                    <div key={variable.id} className="grid grid-cols-2 gap-x-4 items-center py-1.5 px-1 hover:bg-gray-100 dark:hover:bg-gray-800/50">
                                      <div>
                                        <Input
                                          type="text"
                                          value={variable.name}
                                          onChange={(e) => {
                                            const updated = [...excelVariables];
                                            updated[index].name = e.target.value;
                                            setExcelVariables(updated);
                                          }}
                                          className="w-full h-9 bg-transparent border-gray-300 dark:border-gray-600 focus:bg-white dark:focus:bg-gray-800 focus:ring-1 focus:ring-indigo-500"
                                          placeholder="Variable name"
                                        />
                                      </div>
                                      <div>
                                        <Input
                                          type="text"
                                          value={variable.value}
                                          onChange={(e) => {
                                            const updated = [...excelVariables];
                                            updated[index].value = e.target.value;
                                            setExcelVariables(updated);
                                          }}
                                          className="w-full h-9 bg-transparent border-gray-300 dark:border-gray-600 focus:bg-white dark:focus:bg-gray-800 focus:ring-1 focus:ring-indigo-500"
                                          placeholder="Variable value"
                                        />
                                      </div>
                                    </div>
                                  ))}
                                </div>
                            </div>
                            <div className="flex justify-between items-center mt-3">
                              <div className="text-xs font-medium text-white bg-indigo-500 rounded-full px-2.5 py-1">
                                {excelVariables.length} Variables
                              </div>
                              <Button onClick={handleAddExcelVariable} size="sm">
                                <Plus className="h-4 w-4 mr-2" />
                                Add Variable
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>

                      {!excelFile && (
                        <div className="bg-green-50 border border-green-200 rounded-md p-4 text-sm text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300">
                          <h4 className="font-medium mb-1">About Excel Files</h4>
                          <p>
                            Excel (.xlsx, .xls) files store data in tables with rows and columns. 
                            They are ideal for complex datasets and formulas.
                          </p>
                          <p className="mt-2">
                            Data from the <strong>first worksheet (first sheet)</strong> of your uploaded Excel file will be read.
                            The first row of data is considered as headers.
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="api" className="space-y-4 pt-4">
                      <div className="space-y-6">
                        {/* Step Progress Indicator - Spread Across */}
                        <div className="mb-8">
                          <div className="flex justify-between items-start max-w-2xl mx-auto">
                            {[
                              { step: 1, title: "Request Configuration" },
                              { step: 2, title: "API Testing" },
                              { step: 3, title: "Response Parsing" }
                            ].map(({ step, title }, index) => (
                              <div key={step} className="flex items-center flex-1">
                                <div className="flex flex-col items-center w-full">
                                  <button
                                    onClick={() => handleApiStepClick(step)}
                                    disabled={!isApiStepAccessible(step)}
                                    className={`
                                      flex items-center justify-center w-8 h-8 rounded-full border-2 font-medium transition-all mb-2 text-sm
                                      ${apiCurrentStep === step 
                                        ? 'border-indigo-600 bg-indigo-600 text-white' 
                                        : isApiStepAccessible(step)
                                          ? 'border-indigo-600 text-indigo-600 hover:bg-indigo-50 cursor-pointer'
                                          : 'border-gray-300 text-gray-300 cursor-not-allowed'
                                      }
                                      ${apiStepCompleted.request && step === 1 ? 'bg-green-600 border-green-600 text-white' : ''}
                                      ${apiStepCompleted.response && step === 2 ? 'bg-green-600 border-green-600 text-white' : ''}
                                      ${apiStepCompleted.parsing && step === 3 ? 'bg-green-600 border-green-600 text-white' : ''}
                                    `}
                                  >
                                    {(apiStepCompleted.request && step === 1) || 
                                     (apiStepCompleted.response && step === 2) || 
                                     (apiStepCompleted.parsing && step === 3) ? (
                                      <CheckCircle2 className="h-4 w-4" />
                                    ) : (
                                      step
                                    )}
                                  </button>
                                  <div className="text-center px-2">
                                    <div className={`text-xs font-medium ${
                                      apiCurrentStep === step ? 'text-indigo-600' : 
                                      (apiStepCompleted.request && step === 1) || 
                                      (apiStepCompleted.response && step === 2) || 
                                      (apiStepCompleted.parsing && step === 3) ? 'text-green-600' : 'text-gray-500'
                                    }`}>
                                      {title}
                                    </div>
                                  </div>
                                </div>
                                {index < 2 && (
                                  <div className={`
                                    flex-1 h-0.5 mx-4 mt-[-16px]
                                    ${step < apiCurrentStep || (step === 1 && apiStepCompleted.request) || (step === 2 && apiStepCompleted.response)
                                      ? 'bg-indigo-600' 
                                      : 'bg-gray-300'
                                    }
                                  `} />
                                )}
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Step 1: Enhanced Request Configuration - Postman Style */}
                        {apiCurrentStep === 1 && (
                          <Card>
                            <CardContent className="space-y-4 pt-6">
                              {/* URL and Method Row */}
                              <div className="flex gap-3 items-center p-4 bg-gray-50 rounded-lg border">
                                <Select value={apiMethod} onValueChange={(v) => setApiMethod(v as any)}>
                                  <SelectTrigger className="w-[130px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="GET">GET</SelectItem>
                              <SelectItem value="POST">POST</SelectItem>
                              <SelectItem value="PUT">PUT</SelectItem>
                              <SelectItem value="PATCH">PATCH</SelectItem>
                                    <SelectItem value="DELETE">DELETE</SelectItem>
                            </SelectContent>
                          </Select>
                                <Input
                                  placeholder="https://api.example.com/data"
                                  value={apiUrl}
                                  onChange={(e) => setApiUrl(e.target.value)}
                                  className="flex-1 font-mono text-sm"
                                />
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={() => setShowCurlImport(true)}
                                  className="whitespace-nowrap bg-white hover:bg-gray-100"
                                >
                                  <Code className="h-4 w-4 mr-2" />
                                  Import cURL
                                </Button>
                              </div>

                              {/* cURL Import Dialog */}
                              {showCurlImport && (
                                <div className="p-6 border rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 space-y-4 shadow-sm">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <Code className="h-4 w-4 text-blue-600" />
                        </div>
                              <div>
                                        <Label className="text-sm font-semibold text-gray-800">Import from cURL</Label>
                                        <p className="text-xs text-gray-600">Parse cURL command and populate request fields</p>
                                      </div>
                                    </div>
                                    <Button variant="ghost" size="icon" onClick={() => setShowCurlImport(false)} className="hover:bg-blue-100">
                                      <X className="h-4 w-4 text-gray-500" />
                                    </Button>
                                  </div>
                                  
                                  <div className="space-y-2">
                                    <Label className="text-xs font-medium text-gray-600">cURL Command</Label>
                                    <Textarea
                                      placeholder={`curl -X POST 'https://api.example.com/users' \\
  -H 'Content-Type: application/json' \\
  -H 'Authorization: Bearer your-token' \\
  -d '{"name": "John", "email": "<EMAIL>"}'`}
                                      value={curlCommand}
                                      onChange={(e) => setCurlCommand(e.target.value)}
                                      className="font-mono text-xs bg-white border-blue-200 min-h-[100px] resize-none"
                                    />
                                  </div>
                                  
                                  <div className="flex gap-3 justify-end">
                                    <Button 
                                      variant="outline" 
                                      size="sm" 
                                      onClick={() => {
                                        setShowCurlImport(false);
                                        setCurlCommand('');
                                      }}
                                      className="bg-white hover:bg-gray-50"
                                    >
                                      Cancel
                                    </Button>
                                    <Button 
                                      size="sm" 
                                      onClick={() => parseCurlCommand(curlCommand)}
                                      disabled={!curlCommand.trim()}
                                      className="bg-blue-600 hover:bg-blue-700 text-white"
                                    >
                                      <Code className="h-3 w-3 mr-2" />
                                      Import & Parse
                                    </Button>
                                  </div>
                                </div>
                              )}

                              {/* Postman-like Tabs */}
                              <div className="border rounded-lg w-full">
                                {/* Tab Headers */}
                                <div className="flex border-b bg-gray-50">
                                  {[
                                    { key: 'params', label: 'Params', icon: '' },
                                    { key: 'authorization', label: 'Authorization', icon: '' },
                                    { key: 'headers', label: 'Headers', icon: '' },
                                    { key: 'body', label: 'Body', icon: '' }
                                  ].map((tab) => (
                                    <button
                                      key={tab.key}
                                      className={`flex-1 px-6 py-3 text-sm font-medium transition-all duration-200 relative ${
                                        apiConfigActiveTab === tab.key
                                          ? 'bg-white text-indigo-700 border-b-2 border-indigo-600 shadow-sm -mb-px'
                                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                                      }`}
                                      onClick={() => setApiConfigActiveTab(tab.key)}
                                    >
                                      <div className="flex items-center justify-center gap-2">
                                        <span className="text-base">{tab.icon}</span>
                                        <span>{tab.label}</span>
                                      </div>
                                      {apiConfigActiveTab === tab.key && (
                                        <div className="absolute inset-x-0 bottom-0 h-0.5 bg-indigo-600 rounded-t-sm"></div>
                                      )}
                                    </button>
                                  ))}
                                </div>

                                {/* Tab Content */}
                                <div className="p-6 bg-white min-h-[300px] w-full">
                                  {/* Params Tab */}
                                  {apiConfigActiveTab === 'params' && (
                                    <div className="space-y-4 w-full">
                                      {/* Header Row */}
                                      <div className="grid grid-cols-11 gap-3 text-xs font-medium text-gray-500 px-2">
                                        <div className="col-span-4">Key</div>
                                        <div className="col-span-4">Value</div>
                                        <div className="col-span-2">Description</div>
                                        <div className="col-span-1"></div>
                                      </div>
                                      
                                      <div className="space-y-3">
                                        {apiParams.map((param, index) => (
                                          <div key={index} className="grid grid-cols-11 gap-3 items-center">
                                            <div className="col-span-4">
                                              <Input
                                                placeholder="parameter_name"
                                                value={param.key}
                                                onChange={(e) => handleParamChange(index, 'key', e.target.value)}
                                                className="w-full"
                                              />
                                            </div>
                                            <div className="col-span-4">
                                              <Input
                                                placeholder="parameter_value"
                                                value={param.value}
                                                onChange={(e) => handleParamChange(index, 'value', e.target.value)}
                                                className="w-full"
                                              />
                                            </div>
                                            <div className="col-span-2">
                                              <Input
                                                placeholder="description"
                                                value={param.description}
                                                onChange={(e) => handleParamChange(index, 'description', e.target.value)}
                                                className="w-full text-sm"
                                              />
                                            </div>
                                            <div className="col-span-1 flex justify-center">
                                              <Button variant="ghost" size="icon" onClick={() => removeParam(index)}>
                                                <Trash2 className="h-4 w-4 text-red-500" />
                                              </Button>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                      
                                      <div className="pt-2">
                                        <Button variant="outline" size="sm" onClick={addParam} className="w-full">
                                          <Plus className="h-4 w-4 mr-2" /> Add Parameter
                                        </Button>
                                      </div>
                                    </div>
                                  )}

                                  {/* Authorization Tab */}
                                  {apiConfigActiveTab === 'authorization' && (
                                    <div className="space-y-6 w-full">
                                      <div className="space-y-3">
                                        <Label className="text-sm font-medium flex items-center gap-2">
                                          Authorization Type
                                        </Label>
                                        <Select value={apiAuthType} onValueChange={(v) => setApiAuthType(v as any)}>
                                          <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select authentication method" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="none">No Authentication</SelectItem>
                                            <SelectItem value="bearer">Bearer Token</SelectItem>
                                            <SelectItem value="basic">Basic Auth</SelectItem>
                                            <SelectItem value="apikey">API Key</SelectItem>
                                          </SelectContent>
                                        </Select>
                                      </div>

                                      {/* Auth Content */}
                                      <div className="bg-gray-50 p-4 rounded-lg border">
                                        {apiAuthType === 'none' && (
                                          <div className="text-center py-8 text-gray-500">
                                            <p>No authentication required</p>
                                            <p className="text-sm text-gray-400 mt-1">Your request will be sent without authentication headers</p>
                                          </div>
                                        )}

                                        {apiAuthType === 'bearer' && (
                                          <div className="space-y-4">
                                            <div>
                                              <Label className="text-sm font-medium">Token</Label>
                                              <Input
                                                placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                                                value={apiAuth.bearer.token}
                                                onChange={(e) => setApiAuth(prev => ({
                                                  ...prev,
                                                  bearer: { token: e.target.value }
                                                }))}
                                                className="mt-2 font-mono text-sm"
                                                type="password"
                                              />
                                            </div>
                                          </div>
                                        )}

                                        {apiAuthType === 'basic' && (
                                          <div className="space-y-4">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                              <div>
                                                <Label className="text-sm font-medium">Username</Label>
                                                <Input
                                                  placeholder="Enter username"
                                                  value={apiAuth.basic.username}
                                                  onChange={(e) => setApiAuth(prev => ({
                                                    ...prev,
                                                    basic: { ...prev.basic, username: e.target.value }
                                                  }))}
                                                  className="mt-2"
                                                />
                                              </div>
                                              <div>
                                                <Label className="text-sm font-medium">Password</Label>
                                                <Input
                                                  type="password"
                                                  placeholder="Enter password"
                                                  value={apiAuth.basic.password}
                                                  onChange={(e) => setApiAuth(prev => ({
                                                    ...prev,
                                                    basic: { ...prev.basic, password: e.target.value }
                                                  }))}
                                                  className="mt-2"
                                                />
                                              </div>
                                            </div>
                                          </div>
                                        )}

                                        {apiAuthType === 'apikey' && (
                                          <div className="space-y-4">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                              <div>
                                                <Label className="text-sm font-medium">Key Name</Label>
                                                <Input
                                                  placeholder="X-API-Key, Authorization, api_key..."
                                                  value={apiAuth.apikey.key}
                                                  onChange={(e) => setApiAuth(prev => ({
                                                    ...prev,
                                                    apikey: { ...prev.apikey, key: e.target.value }
                                                  }))}
                                                  className="mt-2"
                                                />
                                              </div>
                                              <div>
                                                <Label className="text-sm font-medium">Value</Label>
                                                <Input
                                                  placeholder="Enter API key value"
                                                  value={apiAuth.apikey.value}
                                                  onChange={(e) => setApiAuth(prev => ({
                                                    ...prev,
                                                    apikey: { ...prev.apikey, value: e.target.value }
                                                  }))}
                                                  className="mt-2"
                                                  type="password"
                                                />
                                              </div>
                                            </div>
                                            <div>
                                              <Label className="text-sm font-medium">Add to</Label>
                                              <Select 
                                                value={apiAuth.apikey.addTo} 
                                                onValueChange={(v) => setApiAuth(prev => ({
                                                  ...prev,
                                                  apikey: { ...prev.apikey, addTo: v as 'header' | 'query' }
                                                }))}
                                              >
                                                <SelectTrigger className="w-full mt-2">
                                                  <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                  <SelectItem value="header">Request Header</SelectItem>
                                                  <SelectItem value="query">Query Parameter</SelectItem>
                                                </SelectContent>
                                              </Select>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}

                                  {/* Headers Tab */}
                                  {apiConfigActiveTab === 'headers' && (
                                    <div className="space-y-4 w-full">
                                      {/* Common Headers Quick Add */}
                                      <div className="bg-gray-50 p-4 rounded-lg border">
                                        <Label className="text-sm font-medium mb-3 block">Common Headers</Label>
                                        <div className="flex flex-wrap gap-2">
                                          {[
                                            { key: 'Content-Type', value: 'application/json' },
                                            { key: 'Accept', value: 'application/json' },
                                            { key: 'User-Agent', value: 'HiraFi-TestTool/1.0' },
                                            { key: 'Cache-Control', value: 'no-cache' }
                                          ].map((commonHeader) => (
                                            <Button
                                              key={commonHeader.key}
                                              variant="outline"
                                              size="sm"
                                              onClick={() => {
                                                const newHeaders = [...apiHeaders];
                                                const emptyIndex = newHeaders.findIndex(h => !h.key && !h.value);
                                                if (emptyIndex !== -1) {
                                                  newHeaders[emptyIndex] = commonHeader;
                                                } else {
                                                  newHeaders.push(commonHeader);
                                                }
                                                setApiHeaders(newHeaders);
                                              }}
                                              className="text-xs"
                                            >
                                              {commonHeader.key}
                                            </Button>
                                          ))}
                                        </div>
                                      </div>
                                      
                                      {/* Header List */}
                                      <div className="space-y-3">
                                        {/* Header Row */}
                                        <div className="grid grid-cols-12 gap-3 text-xs font-medium text-gray-500 px-2">
                                          <div className="col-span-5">Header Name</div>
                                          <div className="col-span-6">Header Value</div>
                                          <div className="col-span-1"></div>
                                        </div>
                                        
                        {apiHeaders.map((header, index) => (
                                          <div key={index} className="grid grid-cols-12 gap-3 items-center">
                                            <div className="col-span-5">
                                      <Input
                                                placeholder="e.g., Content-Type, Authorization"
                                        value={header.key}
                                        onChange={(e) => handleHeaderChange(index, 'key', e.target.value)}
                                                className="w-full"
                                      />
                                            </div>
                                            <div className="col-span-6">
                                      <Input
                                                placeholder="e.g., application/json, Bearer token"
                                        value={header.value}
                                        onChange={(e) => handleHeaderChange(index, 'value', e.target.value)}
                                                className="w-full"
                                      />
                                            </div>
                                            <div className="col-span-1 flex justify-center">
                                      <Button variant="ghost" size="icon" onClick={() => removeHeader(index)}>
                                                <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                                            </div>
                          </div>
                        ))}
                                      </div>
                                      
                                      <div className="pt-2">
                                        <Button variant="outline" size="sm" onClick={addHeader} className="w-full">
                                    <Plus className="h-4 w-4 mr-2" /> Add Header
                                  </Button>
                      </div>
                              </div>
                                  )}

                                  {/* Body Tab */}
                                  {apiConfigActiveTab === 'body' && (
                                    <div className="space-y-6 w-full">
                                      <div className="space-y-3">
                                        <Label className="text-sm font-medium flex items-center gap-2">
                                          Request Body Type
                                        </Label>
                                        <Select value={apiBodyType} onValueChange={(v) => setApiBodyType(v as any)}>
                                          <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select body format" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="none">No Body</SelectItem>
                                            <SelectItem value="json">JSON</SelectItem>
                                            <SelectItem value="formdata">form-data</SelectItem>
                                            <SelectItem value="urlencoded">x-www-form-urlencoded</SelectItem>
                                            <SelectItem value="raw">raw</SelectItem>
                                          </SelectContent>
                                        </Select>
                                      </div>

                                      {/* Body Content */}
                                      <div className="bg-gray-50 p-4 rounded-lg border">
                                        {apiBodyType === 'none' && (
                                          <div className="text-center py-8 text-gray-500">
                                            <p>No request body</p>
                                            <p className="text-sm text-gray-400 mt-1">GET requests typically don't have a body</p>
                                          </div>
                                        )}

                                        {apiBodyType === 'json' && (
                                          <div className="space-y-4">
                                <div>
                                              <Label className="text-sm font-medium mb-2 block">JSON Content</Label>
                                  <Textarea
                                                placeholder={`{
  "name": "John Doe",
  "email": "<EMAIL>",
  "age": 30
}`}
                                    value={apiBody}
                                    onChange={(e) => setApiBody(e.target.value)}
                                                className="font-mono text-sm min-h-[200px] w-full"
                                  />
                                            </div>
                        </div>
                      )}

                                        {apiBodyType === 'formdata' && (
                                          <div className="space-y-4">
                                            {/* Header Row */}
                                            <div className="grid grid-cols-11 gap-3 text-xs font-medium text-gray-500 px-2">
                                              <div className="col-span-5">Key</div>
                                              <div className="col-span-5">Value</div>
                                              <div className="col-span-1"></div>
                                            </div>
                                            
                                            <div className="space-y-3">
                                              {apiFormData.map((field, index) => (
                                                <div key={index} className="grid grid-cols-11 gap-3 items-center">
                                                  <div className="col-span-5">
                                                    <Input
                                                      placeholder="field_name"
                                                      value={field.key}
                                                      onChange={(e) => handleFormDataChange(index, 'key', e.target.value)}
                                                      className="w-full"
                                                    />
                                                  </div>
                                                  <div className="col-span-5">
                                                    <Input
                                                      placeholder="field_value"
                                                      value={field.value}
                                                      onChange={(e) => handleFormDataChange(index, 'value', e.target.value)}
                                                      className="w-full"
                                                    />
                                                  </div>
                                                  <div className="col-span-1 flex justify-center">
                                                    <Button variant="ghost" size="icon" onClick={() => removeFormData(index)}>
                                                      <Trash2 className="h-4 w-4 text-red-500" />
                                                    </Button>
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                            
                                            <Button variant="outline" size="sm" onClick={addFormData} className="w-full">
                                              <Plus className="h-4 w-4 mr-2" /> Add Field
                                            </Button>
                                          </div>
                                        )}

                                        {apiBodyType === 'urlencoded' && (
                                          <div className="space-y-4">
                                            {/* Header Row */}
                                            <div className="grid grid-cols-11 gap-3 text-xs font-medium text-gray-500 px-2">
                                              <div className="col-span-5">Key</div>
                                              <div className="col-span-5">Value</div>
                                              <div className="col-span-1"></div>
                                            </div>
                                            
                                            <div className="space-y-3">
                                              {apiUrlEncoded.map((field, index) => (
                                                <div key={index} className="grid grid-cols-11 gap-3 items-center">
                                                  <div className="col-span-5">
                                                    <Input
                                                      placeholder="parameter_name"
                                                      value={field.key}
                                                      onChange={(e) => handleUrlEncodedChange(index, 'key', e.target.value)}
                                                      className="w-full"
                                                    />
                                                  </div>
                                                  <div className="col-span-5">
                                                    <Input
                                                      placeholder="parameter_value"
                                                      value={field.value}
                                                      onChange={(e) => handleUrlEncodedChange(index, 'value', e.target.value)}
                                                      className="w-full"
                                                    />
                                                  </div>
                                                  <div className="col-span-1 flex justify-center">
                                                    <Button variant="ghost" size="icon" onClick={() => removeUrlEncoded(index)}>
                                                      <Trash2 className="h-4 w-4 text-red-500" />
                                                    </Button>
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                            
                                            <Button variant="outline" size="sm" onClick={addUrlEncoded} className="w-full">
                                              <Plus className="h-4 w-4 mr-2" /> Add Field
                                            </Button>
                                          </div>
                                        )}

                                        {apiBodyType === 'raw' && (
                                          <div className="space-y-4">
                                            <div>
                                              <Label className="text-sm font-medium mb-2 block">Raw Content</Label>
                                              <Textarea
                                                placeholder="Enter raw content here..."
                                                value={apiRawBody}
                                                onChange={(e) => setApiRawBody(e.target.value)}
                                                className="font-mono text-sm min-h-[200px] w-full"
                                              />
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                            <CardFooter className="flex justify-between">
                               <div></div>
                              <Button 
                                onClick={handleTestApi} 
                                disabled={isTestingApi || !apiUrl}
                                className="bg-indigo-600 hover:bg-indigo-700"
                              >
                                {isTestingApi ? (
                                  <>
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    Testing API...
                                  </>
                                ) : (
                                  <>
                                    <Key className="h-4 w-4 mr-2" />
                                    Test & Continue
                                  </>
                                )}
                        </Button>
                            </CardFooter>
                          </Card>
                        )}

                        {/* Step 2: API Testing & Response */}
                        {apiCurrentStep === 2 && (
                          <Card>
                            <CardContent className="pt-6">
                              {isTestingApi ? (
                                <div className="flex flex-col items-center justify-center h-64 space-y-4">
                                  <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
                                  <p className="text-gray-600">Testing your API request...</p>
                      </div>
                              ) : apiTestResult ? (
                                <div className="space-y-4">
                                  <div className={`p-3 rounded-md ${apiTestResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                                    <div className="flex items-center gap-2">
                                      {apiTestResult.success ? (
                                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                                      ) : (
                                        <X className="h-5 w-5 text-red-600" />
                                      )}
                                      <span className={`font-medium ${apiTestResult.success ? 'text-green-800' : 'text-red-800'}`}>
                                        {apiTestResult.success ? 'Request Successful' : 'Request Failed'}
                                      </span>
                                    </div>
                                  </div>
                                  <div className="p-4 bg-gray-900 text-white rounded-md max-h-96 overflow-auto">
                                    <pre className="text-sm whitespace-pre-wrap">
                                      {JSON.stringify(apiTestResult, null, 2)}
                                    </pre>
                                  </div>
                                </div>
                              ) : (
                                <div className="flex flex-col items-center justify-center h-64 space-y-4 text-gray-500">
                                  <Globe className="h-12 w-12" />
                                  <p>No API response data available</p>
                                </div>
                              )}
                            </CardContent>
                            <CardFooter className="flex justify-between">
                              <Button 
                                variant="outline" 
                                onClick={handleApiStepPrev}
                              >
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Request
                              </Button>
                              <Button 
                                onClick={handleApiStepNext}
                                disabled={!apiTestResult}
                                className="bg-indigo-600 hover:bg-indigo-700"
                              >
                                Continue to Parsing
                                <ArrowLeft className="h-4 w-4 ml-2 rotate-180" />
                              </Button>
                            </CardFooter>
                          </Card>
                        )}

                        {/* Step 3: Response Parsing */}
                        {apiCurrentStep === 3 && (
                          <Card>
                            <CardContent className="pt-6 space-y-6">
                              {/* Interactive Response Explorer */}
                      {apiTestResult && (
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                      <Label className="text-sm font-medium">API Response Explorer</Label>
                                      <div className="flex items-center gap-2">
                                        {selectedJsonPath && (
                                          <Button 
                                            size="sm" 
                                            onClick={addSelectedPathAsRule}
                                            className="bg-indigo-600 hover:bg-indigo-700"
                                          >
                                            <Plus className="h-4 w-4 mr-2" />
                                            Add "{selectedJsonPath}" as Rule
                                          </Button>
                                        )}
                                      </div>
                                    </div>
                                                                      <div className="p-4 bg-gray-50 border rounded-md max-h-60 overflow-auto">
                                                                              <div className="text-sm">
                                          <div className="text-gray-600 mb-2 text-xs">
                                            💡 Click on any value to select its path for parsing
                                          </div>
                                      {selectedJsonPath && (
                                        <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                                          <strong>Selected Path:</strong> <code className="bg-blue-100 px-1 rounded">{selectedJsonPath}</code>
                                        </div>
                                      )}
                                      {renderJsonValue(apiTestResult.data, '')}
                                    </div>
                            </div>
                        </div>
                      )}

                              {/* Variable Extraction Rules */}
                              <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                  <div className="space-y-1">
                                    <Label className="text-sm font-medium">Variable Extraction Rules</Label>
                                    <p className="text-xs text-gray-500">Define which values to extract as test variables</p>
                                  </div>
                                  <Button 
                                    variant="outline" 
                                    size="sm" 
                                    onClick={addParsingRule}
                                  >
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Variable
                                  </Button>
                                </div>

                                {apiParsingRules.length === 0 ? (
                                  <div className="text-center py-8 text-gray-500">
                                    <Wand2 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                                    <p className="text-sm">No extraction rules defined yet.</p>
                                    <p className="text-xs">Add rules to extract variables from the API response.</p>
                                  </div>
                                ) : (
                                  <div className="space-y-3">
                                    {apiParsingRules.map((rule) => (
                                      <div key={rule.id} className="p-4 border rounded-lg space-y-3 bg-gray-50 relative">
                                        {/* Delete button in top right */}
                                        <Button 
                                          variant="ghost" 
                                          size="icon" 
                                          onClick={() => removeParsingRule(rule.id)}
                                          className="absolute top-2 right-2 h-6 w-6 text-gray-400 hover:text-red-500 hover:bg-red-50"
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </Button>
                                        
                                        {/* Single Row: Variable Name, JSON Path */}
                                        <div className="grid grid-cols-2 gap-3 items-end pr-8">
                                          <div className="col-span-1">
                                            <Label className="text-xs text-gray-600 mb-1 block">Variable Name (for @tags)</Label>
                                            <div className="relative">
                                              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm pointer-events-none">@</span>
                                              <Input
                                                placeholder="user_id"
                                                value={rule.variableName}
                                                onChange={(e) => updateParsingRule(rule.id, 'variableName', e.target.value)}
                                                className="text-sm pl-7"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-span-1">
                                            <Label className="text-xs text-gray-600 mb-1 block">JSON Path</Label>
                                            <Input
                                              placeholder="e.g., data.user.name or data.products[0].id"
                                              value={rule.jsonPath}
                                              onChange={(e) => updateParsingRule(rule.id, 'jsonPath', e.target.value)}
                                              className="text-sm"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}

                                {/* Generate Variables Button */}
                                {apiParsingRules.length > 0 && (
                                  <div className="flex justify-center pt-2">
                                    <Button 
                                      onClick={extractVariablesFromResponse}
                                      disabled={isGeneratingVariables}
                                      className="bg-indigo-600 hover:bg-indigo-700"
                                    >
                                      {isGeneratingVariables ? (
                                        <>
                                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                          Generating Variables...
                                        </>
                                      ) : (
                                        <>
                                          <Wand2 className="h-4 w-4 mr-2" />
                                          Generate Variables ({apiParsingRules.length} rules)
                                        </>
                                      )}
                                    </Button>
                                  </div>
                                )}
                              </div>

                              {/* Generated Test Variables */}
                              {extractedVariables.length > 0 && (
                                <div className="space-y-3">
                                  <div className="flex items-center justify-between">
                                    <Label className="text-sm font-medium">Generated Test Variables</Label>
                                    <div className="text-xs text-gray-500 bg-blue-50 px-2 py-1 rounded">
                                      💡 Use these variables in scenarios with @variable_name
                                    </div>
                                  </div>
                                  <div className="border rounded-lg">
                                    <div className="grid grid-cols-4 gap-4 p-3 bg-gray-50 border-b">
                                      <div className="font-medium text-sm text-gray-600 text-center">Variable Tag</div>
                                      <div className="font-medium text-sm text-gray-600 text-center">Last Value</div>
                                      <div className="font-medium text-sm text-gray-600 text-center">Current Value</div>
                                      <div className="font-medium text-sm text-gray-600 text-center">Actions</div>
                                    </div>
                                    <div className="divide-y">
                                      {extractedVariables.map((variable, index) => (
                                        <VariableRow
                                          key={variable.name}
                                          variable={variable}
                                          apiUrl={apiUrl}
                                          apiMethod={apiMethod}
                                          apiHeaders={buildFinalHeaders()}
                                          apiParams={apiParams}
                                          apiBodyType={apiBodyType}
                                          apiBody={apiBody}
                                          apiFormData={apiFormData}
                                          apiUrlEncoded={apiUrlEncoded}
                                          apiRawBody={apiRawBody}
                                          jsonPath={apiParsingRules.find(rule => rule.variableName === variable.name)?.jsonPath || ''}
                                          transform={apiParsingRules.find(rule => rule.variableName === variable.name)?.transform}
                                          onDelete={() => {
                                                  setExtractedVariables(prev => 
                                                    prev.filter(v => v.name !== variable.name)
                                                  );
                                                  toast({
                                                    title: "Variable Deleted",
                                                    description: `@${variable.name} removed from variables list`,
                                                  });
                                                }}
                                          onUpdate={updateVariableValue}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                  <div className="text-xs text-gray-600 bg-green-50 border border-green-200 rounded p-3">
                                    <div className="font-medium mb-2">📝 Usage in Scenarios:</div>
                                    <div className="space-y-1.5">
                                      <div>• <code className="bg-green-100 px-1 rounded">@{extractedVariables[0]?.name || 'variable_name'}</code> will be replaced with actual values during test execution</div>
                                      <div>• Variables are requested fresh from the API each time a test runs</div>
                                      <div>• Transformations (uppercase, lowercase, trim) are applied automatically</div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </CardContent>
                            <CardFooter className="flex justify-start">
                              <Button 
                                variant="outline" 
                                onClick={handleApiStepPrev}
                              >
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Response
                              </Button>
                            </CardFooter>
                          </Card>
                        )}
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t p-6">
                <Button variant="outline" onClick={() => router.push("/test-data")}>
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving || saveSuccess || !dataSource.name}
                  className={saveSuccess ? "bg-green-600 hover:bg-green-700" : ""}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Saving...
                    </>
                  ) : saveSuccess ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Saved
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>
      <Toaster />
    </div>
  )
}