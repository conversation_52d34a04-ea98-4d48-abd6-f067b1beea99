"use client"

export function TestDataHeader() {
  return (
    <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Test Data Management</h1>
          <p className="text-white/80">Manage your test data, variables, and connections</p>
        </div>
      </div>
    </header>
  )
}
