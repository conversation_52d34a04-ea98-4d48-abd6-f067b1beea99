"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { useSchedules } from "@/hooks/useSchedules"

import {
  Calendar,
  Clock,
  Save,
  Bell,
  Mail,
  MessageSquare,
  ChevronRight,
  ChevronDown,
  FileText,
  Loader2,
  ArrowL<PERSON><PERSON>,
} from "lucide-react"
import type { WeekDay } from "@/types/schedule"

export default function EditSchedulePage() {
  const router = useRouter()
  const params = useParams()
  const scheduleId = params.id as string
  const { toast } = useToast()
  const { updateSchedule, fetchRunsForSchedule, fetchScheduleById, runs, loading: apiLoading } = useSchedules()

  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [expandedRuns, setExpandedRuns] = useState<Record<string, boolean>>({})
  const [errors, setErrors] = useState({
    name: "",
    runIds: "",
    startDate: "",
    startTime: "",
    endDate: "",
    repeatDays: "",
  })
  const [scheduleData, setScheduleData] = useState({
    name: "",
    description: "",
    scheduleType: "once",
    hourlyInterval: 1,
    startDate: "",
    startTime: "",
    endDate: "",
    endTime: "",
    timezone: "Europe/Istanbul", // Default to UTC+3 (Istanbul)
    repeatDays: [] as WeekDay[],
    runIds: [] as string[],
    notifications: {
      email: false,
      slack: false,
      inApp: true,
      onSuccess: true,
      onFailure: true,
    },
    emailRecipients: "",
    slackChannel: "",
  })

  // Fetch runs on component mount
  useEffect(() => {
    fetchRunsForSchedule()
  }, [fetchRunsForSchedule])

  // Load schedule data
  useEffect(() => {
    const loadScheduleData = async () => {
      setIsLoading(true)
      try {
        const response = await fetchScheduleById(scheduleId) as any

        // API yanıt yapısını kontrol et
        const scheduleData = response?.data?.schedule || response?.schedule || null

        if (scheduleData) {
          // Eğer schedule.runs varsa, ilk run'ın ID'sini kullan
          const runIds = scheduleData.runIds ||
            (scheduleData.runs && scheduleData.runs.length > 0 ? [scheduleData.runs[0].id] : []) ||
            (scheduleData.run ? [scheduleData.run.id] : [])

          setScheduleData({
            name: scheduleData.name,
            description: scheduleData.description || "",
            scheduleType: scheduleData.scheduleType,
            hourlyInterval: scheduleData.hourlyInterval || 1,
            startDate: scheduleData.startDate,
            startTime: scheduleData.startTime,
            endDate: scheduleData.endDate || "",
            endTime: scheduleData.endTime || "",
            timezone: scheduleData.timezone || "Europe/Istanbul", // Timezone bilgisini ekle
            repeatDays: scheduleData.repeatDays || [],
            runIds: runIds,
            notifications: scheduleData.notifications || {
              email: false,
              slack: false,
              inApp: true,
              onSuccess: true,
              onFailure: true,
            },
            emailRecipients: scheduleData.emailRecipients || "",
            slackChannel: scheduleData.slackChannel || "",
          })
        } else {
          toast({
            title: "Error",
            description: "Schedule not found",
            variant: "destructive",
          })
          // Yönlendirmeyi geciktir
          setTimeout(() => {
            router.push("/schedule")
          }, 1000)
        }
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to load schedule",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadScheduleData()
  }, [scheduleId, fetchScheduleById, router, toast])

  const validateForm = () => {
    const newErrors = {
      name: "",
      runIds: "",
      startDate: "",
      startTime: "",
      endDate: "",
      repeatDays: "",
    }

    let isValid = true

    // Validate schedule name
    if (!scheduleData.name.trim()) {
      newErrors.name = "Schedule name is required"
      isValid = false
    }

    // Validate run selection
    if (!scheduleData.runIds || scheduleData.runIds.length === 0) {
      newErrors.runIds = "Please select at least one run to schedule"
      isValid = false
    }

    // Validate start date
    if (!scheduleData.startDate) {
      newErrors.startDate = "Start date is required"
      isValid = false
    } else {
      // Check if start date is in the past
      const today = new Date().toISOString().split('T')[0]
      if (scheduleData.startDate < today) {
        newErrors.startDate = "Start date cannot be in the past"
        isValid = false
      }
    }

    // Validate start time
    if (!scheduleData.startTime) {
      newErrors.startTime = "Start time is required"
      isValid = false
    }

    // Validate end date if provided
    if (scheduleData.endDate && scheduleData.startDate && scheduleData.endDate < scheduleData.startDate) {
      newErrors.endDate = "End date cannot be before start date"
      isValid = false
    }

    // Validate repeat days for weekly schedule
    if (scheduleData.scheduleType === "weekly" && scheduleData.repeatDays.length === 0) {
      newErrors.repeatDays = "Please select at least one day of the week"
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  const handleScheduleSubmit = async () => {
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors above and try again",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)
      const result = await updateSchedule(scheduleId, scheduleData)
      if (result.success) {
        toast({
          title: "Schedule Updated",
          description: "Your schedule has been updated successfully",
        })

        // 1 saniye sonra yönlendirme yap
        setTimeout(() => {
          router.push("/schedule")
        }, 1000)
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update schedule",
          variant: "destructive",
        })
        setIsLoading(false)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      })
      setIsLoading(false)
    }
    // Başarılı durumda setIsLoading(false) çağrılmıyor, böylece buton disabled kalıyor
  }

  if (isLoading || apiLoading) {
    return (
      <div className="flex h-screen bg-white dark:bg-gray-950">
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            <p className="text-gray-500 dark:text-gray-400">Loading schedule...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" className="rounded-full" onClick={() => router.push("/schedule")}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Schedule</h1>
                <p className="text-gray-500 dark:text-gray-400">Update your scheduled test run</p>
              </div>
            </div>
            <Button
              onClick={handleScheduleSubmit}
              disabled={isLoading}
              className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Update Schedule
                </>
              )}
            </Button>
          </div>
        </header>

        <main className="flex-1 p-6 overflow-auto bg-gray-50 dark:bg-gray-950 space-y-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Run Selection - Full Width */}
            <Card className="overflow-hidden border-0 shadow-md">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
                <CardTitle>Select Run</CardTitle>
                <CardDescription>Choose which test run to schedule</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="p-4 max-h-[400px] overflow-y-auto">
                  {apiLoading ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 text-indigo-500 animate-spin mb-4" />
                      <p className="text-sm text-gray-500">Loading runs...</p>
                    </div>
                  ) : runs && runs.length > 0 ? (
                    <div className="space-y-3">
                      {runs.map((run) => (
                        <div
                          key={run.id}
                          className={`flex flex-col rounded-md border transition-colors ${
                            scheduleData.runIds.includes(run.id)
                              ? "border-indigo-300 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-900/30"
                              : "border-gray-200 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800/50"
                          }`}
                        >
                          <div
                            className="flex items-start gap-3 p-3 cursor-pointer"
                            onClick={() => {
                              const newRunIds = scheduleData.runIds.includes(run.id)
                                ? scheduleData.runIds.filter(id => id !== run.id)
                                : [...scheduleData.runIds, run.id];
                              setScheduleData({ ...scheduleData, runIds: newRunIds });
                              // Clear error when user selects runs
                              if (errors.runIds) {
                                setErrors({ ...errors, runIds: "" })
                              }
                            }}
                          >
                            <div className="flex-shrink-0 mt-1">
                              <Checkbox
                                checked={scheduleData.runIds.includes(run.id)}
                                onCheckedChange={() => {
                                  const newRunIds = scheduleData.runIds.includes(run.id)
                                    ? scheduleData.runIds.filter(id => id !== run.id)
                                    : [...scheduleData.runIds, run.id];
                                  setScheduleData({ ...scheduleData, runIds: newRunIds });
                                  // Clear error when user selects runs
                                  if (errors.runIds) {
                                    setErrors({ ...errors, runIds: "" })
                                  }
                                }}
                              />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">{run.name}</h4>
                                <button
                                  type="button"
                                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setExpandedRuns(prev => ({
                                      ...prev,
                                      [run.id]: !prev[run.id]
                                    }));
                                  }}
                                >
                                  {expandedRuns[run.id] ? (
                                    <ChevronDown className="h-4 w-4" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                              {run.description && (
                                <p className="text-xs text-gray-500 mt-1 line-clamp-2">{run.description}</p>
                              )}
                              <div className="flex items-center gap-2 mt-2">
                                <Badge variant="outline" className="text-xs py-0 h-5">
                                  {run.scenarioCount} scenarios
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  Created: {new Date(run.createdAt).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Expanded scenarios */}
                          {expandedRuns[run.id] && run.scenarios && run.scenarios.length > 0 && (
                            <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 p-3 pl-10 rounded-b-md">
                              <h5 className="text-xs font-medium text-gray-500 mb-2">Scenarios:</h5>
                              <ul className="space-y-1">
                                {run.scenarios.map((scenario: any) => (
                                  <li key={scenario.id} className="flex items-center gap-2">
                                    <FileText className="h-3 w-3 text-gray-400" />
                                    <span className="text-xs text-gray-600 dark:text-gray-300">{scenario.name}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                        <FileText className="h-5 w-5 text-gray-400" />
                      </div>
                      <h4 className="text-sm font-medium mb-1">No runs available</h4>
                      <p className="text-xs text-gray-500 max-w-xs">
                        You need to create a run first before you can schedule it.
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-3"
                        onClick={() => router.push("/runs/new")}
                      >
                        Create a Run
                      </Button>
                    </div>
                  )}
                </div>

                <div className={`p-4 border-t border-gray-200 dark:border-gray-800 ${
                  errors.runIds ? "bg-red-50 dark:bg-red-950/20" : "bg-gray-50 dark:bg-gray-900/50"
                }`}>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Selected Runs:</span>
                    <div className="flex flex-wrap gap-1 justify-end">
                      {scheduleData.runIds.length > 0 ? (
                        scheduleData.runIds.map(runId => (
                          <Badge key={runId} className="ml-1">
                            {runs?.find(r => r.id === runId)?.name || "Unknown"}
                          </Badge>
                        ))
                      ) : (
                        <Badge variant="outline">None</Badge>
                      )}
                    </div>
                  </div>
                  {errors.runIds && (
                    <p className="text-sm text-red-600 dark:text-red-400 mt-2">{errors.runIds}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Schedule Details and Notifications - Combined in one card */}
            <Card className="overflow-hidden border-0 shadow-md">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
                <CardTitle>Schedule Configuration</CardTitle>
                <CardDescription>Configure schedule details and notifications. Fields marked with * are required.</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column - Schedule Details */}
                  <div className="space-y-6">
                    <div className="flex items-center gap-2 pb-2 border-b border-gray-200 dark:border-gray-700">
                      <Calendar className="h-5 w-5 text-indigo-500" />
                      <h3 className="text-lg font-medium">Schedule Details</h3>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="schedule-name">Schedule Name *</Label>
                        <Input
                          id="schedule-name"
                          placeholder="Enter a name for this schedule"
                          value={scheduleData.name}
                          onChange={(e) => {
                            setScheduleData({ ...scheduleData, name: e.target.value })
                            // Clear error when user starts typing
                            if (errors.name) {
                              setErrors({ ...errors, name: "" })
                            }
                          }}
                          className={errors.name ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""}
                        />
                        {errors.name && (
                          <p className="text-sm text-red-600 dark:text-red-400">{errors.name}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="schedule-type">Schedule Type</Label>
                        <Select
                          value={scheduleData.scheduleType}
                          onValueChange={(value) => setScheduleData({ ...scheduleData, scheduleType: value })}
                        >
                          <SelectTrigger id="schedule-type">
                            <SelectValue placeholder="Select schedule type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="once">Run Once</SelectItem>
                            <SelectItem value="hourly">Hourly</SelectItem>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="schedule-description">Description (Optional)</Label>
                        <Textarea
                          id="schedule-description"
                          placeholder="Enter a description for this schedule"
                          value={scheduleData.description}
                          onChange={(e) => setScheduleData({ ...scheduleData, description: e.target.value })}
                          className="min-h-[80px]"
                        />
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="start-date">Start Date *</Label>
                          <div className="relative">
                            <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              id="start-date"
                              type="date"
                              className={`pl-10 ${errors.startDate ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""}`}
                              value={scheduleData.startDate}
                              onChange={(e) => {
                                const selectedDate = e.target.value
                                setScheduleData({ ...scheduleData, startDate: selectedDate })
                                // Clear error when user selects a date
                                if (errors.startDate) {
                                  setErrors({ ...errors, startDate: "" })
                                }
                              }}
                            />
                          </div>
                          {errors.startDate && (
                            <p className="text-sm text-red-600 dark:text-red-400">{errors.startDate}</p>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="start-time">Start Time *</Label>
                          <div className="relative">
                            <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              id="start-time"
                              type="time"
                              className={`pl-10 ${errors.startTime ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""}`}
                              value={scheduleData.startTime}
                              onChange={(e) => {
                                setScheduleData({ ...scheduleData, startTime: e.target.value })
                                // Clear error when user selects a time
                                if (errors.startTime) {
                                  setErrors({ ...errors, startTime: "" })
                                }
                              }}
                            />
                          </div>
                          {errors.startTime && (
                            <p className="text-sm text-red-600 dark:text-red-400">{errors.startTime}</p>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="timezone">Time Zone</Label>
                        <Select
                          value={scheduleData.timezone}
                          onValueChange={(value) => setScheduleData({ ...scheduleData, timezone: value })}
                        >
                          <SelectTrigger id="timezone">
                            <SelectValue placeholder="Select time zone" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Europe/Istanbul">Istanbul (UTC+3)</SelectItem>
                            <SelectItem value="Europe/London">London (UTC+0/+1)</SelectItem>
                            <SelectItem value="America/New_York">New York (UTC-5/-4)</SelectItem>
                            <SelectItem value="America/Los_Angeles">Los Angeles (UTC-8/-7)</SelectItem>
                            <SelectItem value="Asia/Tokyo">Tokyo (UTC+9)</SelectItem>
                            <SelectItem value="Australia/Sydney">Sydney (UTC+10/+11)</SelectItem>
                            <SelectItem value="UTC">UTC</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-gray-500 mt-1">All scheduled times will be in this time zone</p>
                      </div>

                      {scheduleData.scheduleType !== "once" && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="end-date">End Date (Optional)</Label>
                            <div className="relative">
                              <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="end-date"
                                type="date"
                                className={`pl-10 ${errors.endDate ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""}`}
                                min={scheduleData.startDate || new Date().toISOString().split('T')[0]}
                                value={scheduleData.endDate}
                                onChange={(e) => {
                                  const selectedDate = e.target.value
                                  setScheduleData({ ...scheduleData, endDate: selectedDate })
                                  // Clear error when user selects a date
                                  if (errors.endDate) {
                                    setErrors({ ...errors, endDate: "" })
                                  }
                                }}
                              />
                            </div>
                            {errors.endDate && (
                              <p className="text-sm text-red-600 dark:text-red-400">{errors.endDate}</p>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="end-time">End Time (Optional)</Label>
                            <div className="relative">
                              <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="end-time"
                                type="time"
                                className="pl-10"
                                value={scheduleData.endTime}
                                onChange={(e) => setScheduleData({ ...scheduleData, endTime: e.target.value })}
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {scheduleData.scheduleType === "weekly" && (
                        <div className="space-y-2">
                          <Label>Repeat on Days *</Label>
                          <div className={`flex flex-wrap gap-2 p-3 rounded-md border ${
                            errors.repeatDays ? "border-red-500 bg-red-50 dark:bg-red-950/20" : "border-gray-200 dark:border-gray-700"
                          }`}>
                            {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].map((day) => {
                              // WeekDay tipi büyük harfle başlayan günleri bekliyor
                              const dayKey = day as WeekDay;
                              return (
                                <Badge
                                  key={day}
                                  variant={scheduleData.repeatDays.includes(dayKey) ? "default" : "outline"}
                                  className={`cursor-pointer ${
                                    scheduleData.repeatDays.includes(dayKey)
                                      ? "bg-indigo-500 hover:bg-indigo-600"
                                      : "hover:bg-gray-100 dark:hover:bg-gray-800"
                                  }`}
                                  onClick={() => {
                                    const newDays = scheduleData.repeatDays.includes(dayKey)
                                      ? scheduleData.repeatDays.filter((d) => d !== dayKey)
                                      : [...scheduleData.repeatDays, dayKey];
                                    setScheduleData({ ...scheduleData, repeatDays: newDays });
                                    // Clear error when user selects days
                                    if (errors.repeatDays) {
                                      setErrors({ ...errors, repeatDays: "" })
                                    }
                                  }}
                                >
                                  {day.substring(0, 3)}
                                </Badge>
                              );
                            })}
                          </div>
                          {errors.repeatDays && (
                            <p className="text-sm text-red-600 dark:text-red-400">{errors.repeatDays}</p>
                          )}
                        </div>
                      )}

                      {scheduleData.scheduleType === "hourly" && (
                        <div className="space-y-2">
                          <Label htmlFor="hourly-interval">Hourly Interval</Label>
                          <Select
                            value={scheduleData.hourlyInterval.toString()}
                            onValueChange={(value) => setScheduleData({ ...scheduleData, hourlyInterval: parseInt(value) })}
                          >
                            <SelectTrigger id="hourly-interval">
                              <SelectValue placeholder="Select interval" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">Every hour</SelectItem>
                              <SelectItem value="2">Every 2 hours</SelectItem>
                              <SelectItem value="4">Every 4 hours</SelectItem>
                              <SelectItem value="8">Every 8 hours</SelectItem>
                              <SelectItem value="12">Every 12 hours</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Right Column - Notifications */}
                  <div className="space-y-6">
                    <div className="flex items-center gap-2 pb-2 border-b border-gray-200 dark:border-gray-700">
                      <Bell className="h-5 w-5 text-indigo-500" />
                      <h3 className="text-lg font-medium">Notifications</h3>
                    </div>

                    <div className="space-y-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 rounded-md bg-gray-50 dark:bg-gray-800/50">
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-indigo-500" />
                            <Label htmlFor="email-notification" className="cursor-pointer">
                              Email Notifications
                            </Label>
                          </div>
                          <Switch
                            id="email-notification"
                            checked={scheduleData.notifications.email}
                            onCheckedChange={(checked) =>
                              setScheduleData({
                                ...scheduleData,
                                notifications: { ...scheduleData.notifications, email: !!checked },
                              })
                            }
                          />
                        </div>

                        {scheduleData.notifications.email && (
                          <div className="space-y-2 pl-2 border-l-2 border-indigo-200 dark:border-indigo-800">
                            <Label htmlFor="email-recipients">Email Recipients</Label>
                            <Textarea
                              id="email-recipients"
                              placeholder="Enter email addresses (one per line)"
                              value={scheduleData.emailRecipients}
                              onChange={(e) => setScheduleData({ ...scheduleData, emailRecipients: e.target.value })}
                              className="min-h-[80px]"
                            />
                          </div>
                        )}

                        <div className="flex items-center justify-between p-3 rounded-md bg-gray-50 dark:bg-gray-800/50">
                          <div className="flex items-center gap-2">
                            <MessageSquare className="h-4 w-4 text-indigo-500" />
                            <Label htmlFor="slack-notification" className="cursor-pointer">
                              Slack Notifications
                            </Label>
                          </div>
                          <Switch
                            id="slack-notification"
                            checked={scheduleData.notifications.slack}
                            onCheckedChange={(checked) =>
                              setScheduleData({
                                ...scheduleData,
                                notifications: { ...scheduleData.notifications, slack: !!checked },
                              })
                            }
                          />
                        </div>

                        {scheduleData.notifications.slack && (
                          <div className="space-y-2 pl-2 border-l-2 border-indigo-200 dark:border-indigo-800">
                            <Label htmlFor="slack-channel">Slack Channel</Label>
                            <Input
                              id="slack-channel"
                              placeholder="Enter channel name (e.g. #testing)"
                              value={scheduleData.slackChannel}
                              onChange={(e) => setScheduleData({ ...scheduleData, slackChannel: e.target.value })}
                            />
                          </div>
                        )}

                        <div className="flex items-center justify-between p-3 rounded-md bg-gray-50 dark:bg-gray-800/50">
                          <div className="flex items-center gap-2">
                            <Bell className="h-4 w-4 text-indigo-500" />
                            <Label htmlFor="inapp-notification" className="cursor-pointer">
                              In-App Notifications
                            </Label>
                          </div>
                          <Switch
                            id="inapp-notification"
                            checked={scheduleData.notifications.inApp}
                            onCheckedChange={(checked) =>
                              setScheduleData({
                                ...scheduleData,
                                notifications: { ...scheduleData.notifications, inApp: !!checked },
                              })
                            }
                          />
                        </div>

                        <div className="mt-6 space-y-3">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Notification Triggers</h4>
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id="notify-success"
                              checked={scheduleData.notifications.onSuccess}
                              onCheckedChange={(checked) =>
                                setScheduleData({
                                  ...scheduleData,
                                  notifications: { ...scheduleData.notifications, onSuccess: !!checked },
                                })
                              }
                            />
                            <Label htmlFor="notify-success" className="cursor-pointer">
                              Notify on successful runs
                            </Label>
                          </div>
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id="notify-failure"
                              checked={scheduleData.notifications.onFailure}
                              onCheckedChange={(checked) =>
                                setScheduleData({
                                  ...scheduleData,
                                  notifications: { ...scheduleData.notifications, onFailure: !!checked },
                                })
                              }
                            />
                            <Label htmlFor="notify-failure" className="cursor-pointer">
                              Notify on failed runs
                            </Label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-gray-50 dark:bg-gray-900/50 border-t border-gray-200 dark:border-gray-800 flex justify-end">
                {/* Buton header'a taşındı */}
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
