/**
 * Redis Connector Implementation
 * Main Redis connector that integrates with the connector architecture
 * Uses dependency injection instead of singleton pattern
 */

import { EventEmitter } from 'events';
import { Queue } from 'bullmq';
import { config } from '../../../config/index.js';
import { IBaseConnector } from '../interfaces/base-connector.interface.js';
import { RedisConnectionManager } from '../services/redis/redis-connection-manager.js';
import {
  REDIS_QUEUE_NAMES,
  REDIS_JOB_TYPES
} from '../interfaces/redis-connector.interface.js';

export class RedisConnector extends EventEmitter implements IBaseConnector {
  private connectionManager: RedisConnectionManager;
  private queues: Map<string, Queue> = new Map();
  private isInitialized: boolean = false;
  private nodeId: string | null = null;
  private logger: any;

  constructor(connectionManager?: RedisConnectionManager, logger?: any) {
    super();
    this.connectionManager = connectionManager || new RedisConnectionManager();
    this.logger = logger || console; // Fallback to console if no logger provided
    this.logger.debug('RedisConnector: Instance created with dependency injection');
  }

  /**
   * Initialize the Redis connector (IBaseConnector interface)
   */
  async initialize(config?: any): Promise<void> {
    const success = await this.initializeRedis(config);
    if (!success) {
      throw new Error('Redis connector initialization failed');
    }
  }

  /**
   * Initialize the Redis connector (IRedisConnector interface)
   */
  async initializeRedis(configOrNodeId?: any): Promise<boolean> {
    if (this.isInitialized) {
      this.logger.debug('RedisConnector: Already initialized');
      return true;
    }

    try {
      // Handle both config object and direct nodeId parameter
      const nodeId = typeof configOrNodeId === 'string' ? configOrNodeId : configOrNodeId?.nodeId;

      // Store node ID if provided
      if (nodeId) {
        this.nodeId = nodeId;
      }

      // Connect to Redis using the connection manager
      const connected = await this.connectionManager.connect();
      if (!connected) {
        this.logger.error('RedisConnector: Failed to connect to Redis');
        return false;
      }

      // Initialize queues
      this.initializeQueues();

      this.isInitialized = true;
      this.emit('connected');
      if (this.logger.serviceInit) {
        this.logger.serviceInit('RedisConnector', 'Successfully initialized');
      } else {
        this.logger.info('RedisConnector: Successfully initialized');
      }
      return true;

    } catch (error: any) {
      this.logger.error(`RedisConnector: Initialization failed: ${error.message}`);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Check if the connector is connected
   */
  isConnected(): boolean {
    return this.connectionManager.isConnected();
  }

  /**
   * Get the underlying Redis client
   */
  getClient() {
    return this.connectionManager.getClient();
  }

  /**
   * Get a queue instance
   */
  getQueue(queueName: string): Queue | null {
    return this.queues.get(queueName) || null;
  }

  // Node registration removed - using WebSocket-only registration

  /**
   * Close the connector
   */
  async close(): Promise<void> {
    try {
      // Close all queues
      for (const queue of this.queues.values()) {
        await queue.close();
      }
      this.queues.clear();

      // Disconnect from Redis
      await this.connectionManager.disconnect();

      this.isInitialized = false;
      this.emit('disconnected');
      this.logger.info('RedisConnector: Closed successfully');

    } catch (error: any) {
      this.logger.error(`RedisConnector: Error during close: ${error.message}`);
      this.emit('error', error);
    }
  }

  /**
   * Get connector type
   */
  getType(): string {
    return 'redis';
  }

  /**
   * Get connector type (IBaseConnector interface)
   */
  getConnectorType(): string {
    return 'redis';
  }

  /**
   * Get connector status
   */
  getStatus(): 'connected' | 'disconnected' | 'connecting' | 'error' {
    if (!this.isInitialized) return 'disconnected';
    return this.isConnected() ? 'connected' : 'disconnected';
  }

  /**
   * Get node ID
   */
  getNodeId(): string | null {
    return this.nodeId;
  }

  /**
   * Update node ID
   */
  updateNodeId(nodeId: string): void {
    this.nodeId = nodeId;
  }

  /**
   * Get connector statistics
   */
  getStats(): any {
    return {
      connectorType: 'redis',
      nodeId: this.nodeId,
      isInitialized: this.isInitialized,
      isConnected: this.isConnected(),
      status: this.getStatus(),
      queues: Array.from(this.queues.keys()),
      createdAt: new Date().toISOString()
    };
  }

  /**
   * Initialize queues
   */
  private initializeQueues(): void {
    const client = this.connectionManager.getClient();
    if (!client) {
      throw new Error('Redis client not available for queue initialization');
    }

    // Create connection options for BullMQ
    const connectionOptions = {
      host: config.redis?.host || 'localhost',
      port: config.redis?.port || 6379,
      password: config.redis?.password || undefined,
      db: config.redis?.db || 0,
      maxRetriesPerRequest: null, // Required for BullMQ
    };

    // Initialize all queues
    const queueNames: string[] = [];
    Object.values(REDIS_QUEUE_NAMES).forEach(queueName => {
      const queue = new Queue(queueName, {
        connection: connectionOptions,
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
        },
      });

      this.queues.set(queueName, queue);
      queueNames.push(queueName);

      // Log individual queue initialization only if verbose logging is enabled
      if (process.env.REDIS_VERBOSE_LOGGING === 'true') {
        this.logger.debug(`RedisConnector: Initialized queue: ${queueName}`);
      }
    });

    // Log summary instead of individual queue logs
    this.logger.info(`RedisConnector: Initialized ${this.queues.size} queues [${queueNames.join(', ')}]`);
  }
}

// Export constants for convenience
export { REDIS_QUEUE_NAMES, REDIS_JOB_TYPES };
