import knex, { Knex } from 'knex';
import { logger } from '../utils/logger.js';

// Veritabanı istemcilerini belirlemek için bir yardımcı fonksiyon
const getClientFromConnectionString = (connectionString: string): string => {
  if (connectionString.startsWith('postgres://') || connectionString.startsWith('postgresql://')) {
    return 'pg';
  }
  if (connectionString.startsWith('mysql://')) {
    return 'mysql2';
  }
  if (connectionString.startsWith('sqlite://')) {
    return 'sqlite3';
  }
  // Diğer veritabanları için destek eklenebilir
  throw new Error('Desteklenmeyen veya geçersiz bağlantı dizesi formatı.');
};

export const testDatabaseConnection = async (connectionString: string): Promise<{ success: boolean; message: string }> => {
  let db: Knex | null = null;
  try {
    const client = getClientFromConnectionString(connectionString);

    logger.info(`Testing database connection with client: ${client}`);

    db = knex({
      client,
      connection: connectionString,
      pool: { min: 0, max: 1 }, // Test için küçük bir pool yeterli
    });

    // Basit bir sorgu ile bağlantıyı test et
    await db.raw('select 1+1 as result');

    logger.info('Database connection test successful.');
    return { success: true, message: 'Veritabanı bağlantısı başarılı!' };
  } catch (error: any) {
    logger.error('Database connection test failed:', error.message);
    return { success: false, message: `Bağlantı başarısız: ${error.message}` };
  } finally {
    if (db) {
      await db.destroy();
      logger.info('Database connection closed.');
    }
  }
};

export const getTables = async (connectionString: string): Promise<{ success: boolean; tables?: string[]; message: string }> => {
  let db: Knex | null = null;
  try {
    const client = getClientFromConnectionString(connectionString);
    logger.info(`Fetching tables with client: ${client}`);

    db = knex({
      client,
      connection: connectionString,
    });

    let tables: string[];
    if (client === 'pg') {
      const result = await db.raw("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema';");
      tables = result.rows.map((row: { tablename: string }) => row.tablename);
    } else if (client === 'mysql2') {
      const result = await db.raw('SHOW TABLES');
      tables = result[0].map((row: any) => Object.values(row)[0] as string);
    } else if (client === 'sqlite3') {
      const result = await db.raw("SELECT name FROM sqlite_master WHERE type='table';");
      tables = result.map((row: { name: string }) => row.name);
    } else {
      throw new Error('Desteklenmeyen veritabanı istemcisi.');
    }
    
    logger.info(`Found tables: ${tables.join(', ')}`);
    return { success: true, tables, message: 'Tablolar başarıyla getirildi.' };

  } catch (error: any) {
    logger.error('Failed to fetch tables:', error.message);
    return { success: false, message: `Tablolar getirilemedi: ${error.message}` };
  } finally {
    if (db) {
      await db.destroy();
      logger.info('Database connection closed after fetching tables.');
    }
  }
};

export const getColumns = async (connectionString: string, table: string): Promise<{ success: boolean; columns?: string[]; message: string }> => {
  let db: Knex | null = null;
  try {
    const client = getClientFromConnectionString(connectionString);
    logger.info(`Fetching columns for table ${table} with client: ${client}`);

    db = knex({
      client,
      connection: connectionString,
    });

    const columnInfo = await db.table(table).columnInfo();
    const columns = Object.keys(columnInfo);
    
    logger.info(`Found columns for table ${table}: ${columns.join(', ')}`);
    return { success: true, columns, message: 'Sütunlar başarıyla getirildi.' };

  } catch (error: any) {
    logger.error(`Failed to fetch columns for table ${table}:`, error.message);
    return { success: false, message: `Sütunlar getirilemedi: ${error.message}` };
  } finally {
    if (db) {
      await db.destroy();
      logger.info('Database connection closed after fetching columns.');
    }
  }
};

export const getPreviewData = async (
  connectionString: string, 
  table: string, 
  nameColumn: string, 
  valueColumn: string
): Promise<{ success: boolean; data?: { name: any; value: any }[]; message: string }> => {
  let db: Knex | null = null;
  try {
    const client = getClientFromConnectionString(connectionString);
    logger.info(`Fetching preview data for table ${table} with client: ${client}`);

    db = knex({
      client,
      connection: connectionString,
    });

    if (nameColumn === valueColumn) {
        const queryResult = await db.select(nameColumn).from(table).whereNotNull(nameColumn);
        const data = queryResult.map((row: any) => ({
            name: row[nameColumn],
            value: row[nameColumn]
        }));
        logger.info(`Fetched all data for table ${table}.`);
        return { success: true, data, message: 'Veri başarıyla getirildi.' };
    }


    const queryResult = await db.select(nameColumn, valueColumn).from(table).whereNotNull(nameColumn).whereNotNull(valueColumn);
    const data = queryResult.map((row: any) => ({
        name: row[nameColumn],
        value: row[valueColumn]
    }));
    
    logger.info(`Fetched all data for table ${table}.`);
    return { success: true, data, message: 'Veri başarıyla getirildi.' };

  } catch (error: any) {
    logger.error(`Failed to fetch preview data for table ${table}:`, error.message);
    return { success: false, message: `Veri getirilemedi: ${error.message}` };
  } finally {
    if (db) {
      await db.destroy();
      logger.info('Database connection closed after fetching data.');
    }
  }
}; 

export const executeFlow = async (
  connectionString: string, 
  flow: any[]
): Promise<{ success: boolean; logs: string[]; results?: any[]; error?: string }> => {
  let db: Knex | null = null;
  const logs: string[] = [];
  const results: any[] = [];
  const context: Record<string, any> = {};

  try {
    const client = getClientFromConnectionString(connectionString);
    logs.push(`Connecting to database with client: ${client}...`);
    
    db = knex({
      client,
      connection: connectionString,
    });

    logs.push("Connection successful. Starting flow execution...");

    for (const [index, step] of flow.entries()) {
      const stepLogPrefix = `Step ${index + 1} (${step.type} on ${step.tableName || 'N/A'}):`;
      logger.info(`[EXEC-FLOW] Processing step: ${JSON.stringify(step)}`);

      let result: any;

      const resolveValue = (value: string) => {
          if (typeof value !== 'string') return value;
          return value.replace(/{{(.*?)}}/g, (match, p1) => {
            const [refName, refKey] = p1.trim().split('.');
            if (context[refName] && context[refName][refKey] !== undefined) {
              return context[refName][refKey];
            }
            return match; 
          });
      };

      if(step.type === 'raw' && step.query) {
          result = await db.raw(step.query);
          logs.push(`${stepLogPrefix} Success. Raw query executed.`);
          continue; // Diğer switch case'e girmemesi için
      }
      
      if (!step.tableName) {
          logs.push(`${stepLogPrefix} SKIPPED: Table name is missing.`);
          continue;
      }
      
      let queryBuilder = db(step.tableName);

      switch (step.type) {
        case 'insert':
          const insertData: { [key: string]: any } = {};
          for (const field of step.fields) {
            if (field.col) {
              insertData[field.col] = resolveValue(field.val);
            }
          }
          
          if (Object.keys(insertData).length === 0) {
            logs.push(`${stepLogPrefix} SKIPPED: No valid fields to insert.`);
            continue;
          }

          logger.info(`[EXEC-FLOW] Attempting to insert data: ${JSON.stringify(insertData)}`);
          
          if (client === 'pg') {
            result = await queryBuilder.insert(insertData).returning('*');
          } else {
            result = await queryBuilder.insert(insertData);
            if (result && result.length > 0) {
              result = [{ id: result[0], ...insertData }];
            }
          }

          if (step.referenceName && result && result.length > 0) {
            context[step.referenceName] = result[0];
            logs.push(`${stepLogPrefix} Success. 1 row inserted. Ref: ${step.referenceName} (ID: ${result[0].id})`);
          } else {
            logs.push(`${stepLogPrefix} Success. 1 row inserted.`);
          }
          break;

        case 'update':
          const updateData: { [key: string]: any } = {};
           for (const field of step.fields) {
              if (field.col) {
                  updateData[field.col] = resolveValue(field.val);
              }
          }

          if (Object.keys(updateData).length === 0) {
             logs.push(`${stepLogPrefix} SKIPPED: No fields to update.`);
             continue;
          }
          
          for (const cond of step.conditions) {
              if(cond.col) {
                  queryBuilder = queryBuilder.where(cond.col, cond.op, resolveValue(cond.val));
              }
          }
          
          result = await queryBuilder.update(updateData);
          logs.push(`${stepLogPrefix} Success. ${result} row(s) updated.`);
          break;

        case 'delete':
          for (const cond of step.conditions) {
              if(cond.col) {
                  queryBuilder = queryBuilder.where(cond.col, cond.op, resolveValue(cond.val));
              }
          }
          result = await queryBuilder.del();
          logs.push(`${stepLogPrefix} Success. ${result} row(s) deleted.`);
          break;

        case 'select':
          // Sütun seçimi - ['*'] veya boş array ise tüm sütunları seç
          const selectedColumns = step.selectedColumns && step.selectedColumns.length > 0 && !step.selectedColumns.includes('*')
            ? step.selectedColumns
            : ['*'];
          
          queryBuilder = queryBuilder.select(selectedColumns);
          
          // WHERE conditions
          if (step.whereConditions && Array.isArray(step.whereConditions)) {
            for (const cond of step.whereConditions) {
              if (cond.col && cond.operator) {
                const operator = cond.operator;
                const value = resolveValue(cond.val);
                
                if (operator === 'IS NULL') {
                  queryBuilder = queryBuilder.whereNull(cond.col);
                } else if (operator === 'IS NOT NULL') {
                  queryBuilder = queryBuilder.whereNotNull(cond.col);
                } else {
                  queryBuilder = queryBuilder.where(cond.col, operator, value);
                }
              }
            }
          }
          
          // ORDER BY
          if (step.orderBy) {
            const direction = step.orderDirection && step.orderDirection.toLowerCase() === 'desc' ? 'desc' : 'asc';
            queryBuilder = queryBuilder.orderBy(step.orderBy, direction);
          }
          
          // LIMIT
          if (step.limitValue && !isNaN(parseInt(step.limitValue))) {
            queryBuilder = queryBuilder.limit(parseInt(step.limitValue));
          }
          
          result = await queryBuilder;
          
          // SELECT sonuçlarını results array'ine ekle
          results.push({
            stepIndex: index + 1,
            stepType: 'select',
            tableName: step.tableName,
            rowCount: result.length,
            data: result
          });
          
          logs.push(`${stepLogPrefix} Success. ${result.length} row(s) selected.`);
          
          // SELECT sonuçlarını göster
          if (result.length > 0) {
            logs.push(`${stepLogPrefix} Results:`);
            if (result.length <= 10) {
              // 10 veya daha az satır varsa hepsini göster
              result.forEach((row: any, index: number) => {
                logs.push(`  Row ${index + 1}: ${JSON.stringify(row)}`);
              });
            } else {
              // 10'dan fazla satır varsa ilk 5 ve son 2'yi göster
              result.slice(0, 5).forEach((row: any, index: number) => {
                logs.push(`  Row ${index + 1}: ${JSON.stringify(row)}`);
              });
              logs.push(`  ... (${result.length - 7} more rows) ...`);
              result.slice(-2).forEach((row: any, index: number) => {
                logs.push(`  Row ${result.length - 1 + index}: ${JSON.stringify(row)}`);
              });
            }
          }
          break;
      }
    }

    logs.push("Flow executed successfully.");
    return { success: true, logs, results };

  } catch (error: any) {
    logs.push(`ERROR: ${error.message}`);
    return { success: false, logs, error: error.message };
  } finally {
    if (db) {
      await db.destroy();
      logs.push('Database connection closed.');
    }
  }
}; 