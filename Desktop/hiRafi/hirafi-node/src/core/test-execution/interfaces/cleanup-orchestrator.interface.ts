/**
 * Cleanup Orchestrator Interface
 * 
 * Defines the contract for orchestrating comprehensive cleanup operations
 * across all test execution components.
 */

import { TestExecutionState } from '../../node-manager/types/node-manager-types.js';

/**
 * Cleanup reason enumeration
 */
export enum CleanupReason {
  COMPLETED = 'completed',
  FAILED = 'failed',
  STOPPED = 'stopped',
  CRASHED = 'crashed',
  TIMEOUT = 'timeout',
  LOCK_LOST = 'lock_lost',
  SHUTDOWN = 'shutdown'
}

/**
 * Cleanup options
 */
export interface CleanupOptions {
  testId: string;
  reason: CleanupReason;
  currentState?: TestExecutionState;
  timeout?: number;
  forceCleanup?: boolean;
  testRunner?: any;
}

/**
 * Cleanup result
 */
export interface CleanupResult {
  success: boolean;
  stepsCompleted: string[];
  errors: Error[];
  duration: number;
  testId: string;
  reason: CleanupReason;
}

/**
 * Shutdown cleanup options
 */
export interface ShutdownCleanupOptions {
  reason?: string;
  timeout?: number;
  forceCleanup?: boolean;
  connectionManager?: any;
  websocketConnector?: any;
}

/**
 * Cleanup step definition
 */
export interface CleanupStep {
  name: string;
  description: string;
  timeout: number;
  critical: boolean;
  execute: (testId: string, context: any) => Promise<void>;
}

/**
 * Interface for orchestrating cleanup operations
 * 
 * This interface provides comprehensive cleanup orchestration across
 * all test execution components with proper error handling and recovery.
 */
export interface ICleanupOrchestrator {
  /**
   * Initialize the cleanup orchestrator
   * @returns Promise that resolves when initialization is complete
   */
  initialize(): Promise<void>;

  /**
   * Perform comprehensive cleanup for a test
   * @param options Cleanup options
   * @returns Promise resolving to cleanup result
   */
  cleanupTest(options: CleanupOptions): Promise<CleanupResult>;

  /**
   * Register a lock extension interval for cleanup tracking
   * @param testId Test ID
   * @param interval Node.js timeout interval
   */
  registerLockExtensionInterval(testId: string, interval: NodeJS.Timeout): void;

  /**
   * Clear lock extension interval for a test
   * @param testId Test ID
   */
  clearLockExtensionInterval(testId: string): void;

  /**
   * Perform emergency cleanup of all resources
   * @param reason Reason for emergency cleanup
   * @returns Promise that resolves when cleanup is complete
   */
  emergencyCleanupAll(reason?: string): Promise<void>;

  /**
   * Perform shutdown cleanup
   * @param options Shutdown cleanup options
   * @returns Promise that resolves when cleanup is complete
   */
  performShutdownCleanup(options?: ShutdownCleanupOptions): Promise<void>;

  /**
   * Perform force cleanup (for crash scenarios)
   * @param reason Reason for force cleanup
   * @returns Promise that resolves when cleanup is complete
   */
  performForceCleanup(reason?: string): Promise<void>;

  /**
   * Get cleanup status for a test
   * @param testId Test ID
   * @returns Cleanup status information
   */
  getCleanupStatus(testId: string): {
    inProgress: boolean;
    completed: boolean;
    lastCleanupTime?: Date;
  };

  /**
   * Register a custom cleanup step
   * @param step Cleanup step definition
   */
  registerCleanupStep(step: CleanupStep): void;

  /**
   * Unregister a custom cleanup step
   * @param stepName Name of the step to remove
   */
  unregisterCleanupStep(stepName: string): void;

  /**
   * Get cleanup statistics
   * @returns Cleanup operation statistics
   */
  getCleanupStatistics(): {
    totalCleanups: number;
    successfulCleanups: number;
    failedCleanups: number;
    averageCleanupTime: number;
  };

  /**
   * Clear completed cleanup tracking for a test
   * @param testId Test ID
   */
  clearCompletedCleanup(testId: string): void;

  /**
   * Dispose of the cleanup orchestrator
   * @returns Promise that resolves when disposal is complete
   */
  dispose(): Promise<void>;
}
