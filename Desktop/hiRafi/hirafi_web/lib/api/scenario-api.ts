/**
 * Scenario API
 *
 * Senaryolar ile ilgili API isteklerini yönetir.
 */

import { get, post, put, del } from './fetch-wrapper';
import { SCENARIO_ENDPOINTS } from './constants';

/**
 * Tüm senaryoları getirir
 *
 * @param params Filtreleme parametreleri
 */
export async function getScenarios(params?: {
  folderId?: string;
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  teamId?: string;
  companyId?: string;
}) {
  const response = await get(SCENARIO_ENDPOINTS.GET_ALL, { params });
  return response;
}

/**
 * Belirli bir senaryoyu getirir
 *
 * @param id Senaryo ID'si
 */
export async function getScenarioById(id: string) {
  return get(SCENARIO_ENDPOINTS.GET_BY_ID(id));
}

/**
 * <PERSON><PERSON> bir senaryo oluşturur
 *
 * @param scenario Senaryo verileri
 */
export async function createScenario(scenario: {
  name: string;
  description?: string;
  folderId?: string;
  steps: any[];
  tags?: string[];
  status?: string;
  teamId?: string;
  companyId?: string;
  platform?: 'web' | 'android';
}) {
  return post(SCENARIO_ENDPOINTS.CREATE, scenario, {
    showSuccessToast: true,
    successMessage: 'Senaryo başarıyla oluşturuldu'
  });
}

/**
 * Bir senaryoyu günceller
 *
 * @param id Senaryo ID'si
 * @param scenario Güncellenecek senaryo verileri
 */
export async function updateScenario(id: string, scenario: {
  name?: string;
  description?: string;
  folderId?: string;
  steps?: any[];
  tags?: string[];
  status?: string;
  platform?: 'web' | 'android';
}) {
  return put(SCENARIO_ENDPOINTS.UPDATE(id), scenario, {
    showSuccessToast: true,
    successMessage: 'Senaryo başarıyla güncellendi'
  });
}

/**
 * Bir senaryoyu siler
 *
 * @param id Senaryo ID'si
 * @returns Promise resolving to the API response
 */
export async function deleteScenario(id: string) {
  return del(SCENARIO_ENDPOINTS.DELETE(id), {
    showSuccessToast: false, // Toast'ı component katmanında göstereceğiz
    showErrorToast: false    // Error handling'i component'te yapacağız
  });
}

/**
 * Bir senaryoyu çoğaltır
 *
 * @param id Senaryo ID'si
 * @param options Duplication options
 */
export async function duplicateScenario(id: string, options?: {
  namePrefix?: string;
  folderId?: string;
  newName?: string;
}) {
  return post(SCENARIO_ENDPOINTS.DUPLICATE(id), options || {}, {
    showSuccessToast: true,
    successMessage: 'Senaryo başarıyla çoğaltıldı'
  });
}

/**
 * Senaryoları dışa aktarır
 *
 * @param options Dışa aktarma seçenekleri
 */
export async function exportScenarios(options: {
  format: 'csv' | 'pdf';
  folderId?: string;
  status?: string;
  selectedScenarioIds?: string[];
  fileName?: string;
  includeFields: {
    steps: boolean;
    description: boolean;
    tags: boolean;
    status: boolean;
    lastRun: boolean;
    duration: boolean;
  };
  teamId?: string;
  companyId?: string;
}) {
  try {
    // API_BASE_URL'yi constants.ts'den import et
    const { API_BASE_URL } = await import('./constants');

    // Blob olarak indirmek için fetch kullan
    const response = await fetch(`${API_BASE_URL}${SCENARIO_ENDPOINTS.EXPORT}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${localStorage.getItem('authToken') || sessionStorage.getItem('authToken')}`
      },
      body: JSON.stringify(options),
    })

    if (!response.ok) {
      // Hata durumunda JSON yanıtını al
      try {
        const errorData = await response.json()
        return { success: false, error: errorData.error || "Failed to export scenarios" }
      } catch (e) {
        return { success: false, error: `Failed to export scenarios: ${response.status} ${response.statusText}` }
      }
    }

    // Dosyayı blob olarak indir (CSV veya PDF)
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url

    // Formatına göre dosya adı belirle
    const fileName = options.fileName || "scenarios-export"
    if (options.format === "csv") {
      a.download = `${fileName}.csv`
    } else {
      a.download = `${fileName}.pdf`
    }

    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    return { success: true }
  } catch (error) {
    console.error("Error exporting scenarios:", error)
    return { success: false, error: "Failed to export scenarios" }
  }
}

/**
 * Senaryoları içe aktarır
 *
 * @param csvData CSV verisi
 * @param folderId Klasör ID'si
 * @param teamId Takım ID'si (opsiyonel)
 * @param companyId Şirket ID'si (opsiyonel)
 * @param selectedScenarioIndices Seçilen senaryo indeksleri (opsiyonel)
 */
export async function importScenarios(
  csvData: string,
  folderId?: string,
  teamId?: string,
  companyId?: string,
  selectedScenarioIndices?: number[]
) {
  return post(SCENARIO_ENDPOINTS.IMPORT, {
    csvData,
    folderId,
    teamId,
    companyId,
    selectedScenarioIndices
  }, {
    showSuccessToast: true,
    successMessage: 'Senaryolar başarıyla içe aktarıldı'
  });
}

/**
 * Tek bir senaryoyu içe aktarır
 *
 * @param scenario Senaryo verisi
 */
export async function importSingleScenario(scenario: any) {
  return post(SCENARIO_ENDPOINTS.CREATE, scenario, {
    showSuccessToast: false // Toplu import sırasında her senaryo için toast gösterme
  });
}

/**
 * Birden fazla senaryoyu toplu olarak siler
 *
 * @param scenarioIds Silinecek senaryo ID'leri
 * @returns Promise resolving to the API response
 */
export async function bulkDeleteScenarios(scenarioIds: string[]) {
  return post(SCENARIO_ENDPOINTS.BULK_DELETE, { scenarioIds }, {
    showSuccessToast: false, // Don't show success toast here, we'll handle it in the component
    showErrorToast: false // Don't show error toast here, we'll handle errors in the component
  });
}

/**
 * Birden fazla senaryoyu toplu olarak kopyalar
 *
 * @param scenarioIds Kopyalanacak senaryo ID'leri
 * @param options Kopyalama seçenekleri
 * @returns Promise resolving to the API response
 */
export async function bulkDuplicateScenarios(scenarioIds: string[], options?: {
  namePrefix?: string;
  folderId?: string;
  customNamePattern?: string;
  addNumbers?: boolean;
}) {
  return post(SCENARIO_ENDPOINTS.BULK_DUPLICATE, { 
    scenarioIds, 
    ...options 
  }, {
    showSuccessToast: false, // Don't show success toast here, we'll handle it in the component
    showErrorToast: false // Don't show error toast here, we'll handle errors in the component
  });
}

/**
 * CSV'den senaryoları parse et
 *
 * @param csvData CSV verisi
 * @param selectedIndices Seçilen senaryo indeksleri
 * @param userId Kullanıcı ID'si
 * @param teamId Takım ID'si
 * @param companyId Şirket ID'si
 * @param folderId Klasör ID'si
 */
export function parseScenarios(csvData: string, selectedIndices: number[], userId: string, teamId: string, companyId: string, folderId: string) {
  try {
    // CSV'yi satırlara böl, ancak JSON içindeki virgülleri dikkate alma
    // Önce başlık satırını al
    const lines = csvData.split(/\r?\n/);
    if (lines.length <= 1) return { success: false, error: "No data found in CSV" };

    const headerLine = lines[0];
    const headerRow = headerLine.split(',').map(h => h.trim());

    // Hariç tutulacak alanlar
    const excludedFields = ['createdAt', 'updatedAt', 'id', '_id', 'userId', 'teamId', 'companyId', 'Created At'];

    // Senaryoları oluştur
    const scenarios: any[] = [];

    // Satırları işle
    for (const index of selectedIndices) {
      if (index + 1 >= lines.length) continue;

      // Satırı al
      let line = lines[index + 1];

      // Satırı manuel olarak parse et
      const values: string[] = [];
      let inQuotes = false;
      let currentValue = "";
      let bracketCount = 0;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"' && (i === 0 || line[i-1] !== '\\')) {
          inQuotes = !inQuotes;
        }

        if (!inQuotes) {
          if (char === '[') bracketCount++;
          if (char === ']') bracketCount--;
        }

        if (char === ',' && !inQuotes && bracketCount === 0) {
          values.push(currentValue);
          currentValue = "";
        } else {
          currentValue += char;
        }
      }

      // Son değeri ekle
      values.push(currentValue);

      const scenario: any = {};

      // CSV sütunlarını senaryo alanlarına eşle
      headerRow.forEach((header, i) => {
        const trimmedHeader = header.trim();
        const lowerHeader = trimmedHeader.toLowerCase();

        // Hariç tutulan alanları atla
        if (excludedFields.includes(trimmedHeader) || excludedFields.includes(lowerHeader)) {
          return;
        }

        if (values[i]) {
          // Özel alanları işle
          if (lowerHeader === 'name') {
            scenario.name = values[i].trim();
          } else if (lowerHeader === 'description') {
            scenario.description = values[i].trim();
          } else if (lowerHeader === 'steps') {
            try {
              // Tırnak işaretlerini temizle
              let stepsStr = values[i].trim();

              // Dış tırnakları temizle
              if (stepsStr.startsWith('"') && stepsStr.endsWith('"')) {
                stepsStr = stepsStr.substring(1, stepsStr.length - 1);
              }

              // CSV'den gelen çift tırnakları düzelt
              stepsStr = stepsStr.replace(/""/g, '"');

              // Kaçış karakterlerini temizle
              stepsStr = stepsStr.replace(/\\\\n/g, '\n').replace(/\\"/g, '"');

              scenario.steps = JSON.parse(stepsStr);
            } catch (e) {
              console.error(`Error parsing steps for row ${index + 1}:`, e);
              console.error('Original steps string (first 100 chars):', values[i].substring(0, 100));
              console.error('Steps string length:', values[i].length);

              // Alternatif temizleme yöntemi dene
              try {
                // Farklı bir yaklaşım deneyelim
                let altStepsStr = values[i].trim();

                // Dış tırnakları temizle
                if (altStepsStr.startsWith('"') && altStepsStr.endsWith('"')) {
                  altStepsStr = altStepsStr.substring(1, altStepsStr.length - 1);
                }

                // Çift tırnakları düzelt
                altStepsStr = altStepsStr.replace(/""/g, '"');

                // JSON'u manuel olarak düzelt
                // Çift tırnaklı property'leri düzelt
                altStepsStr = altStepsStr.replace(/\{""(\w+)"":/g, '{"$1":');
                altStepsStr = altStepsStr.replace(/,""(\w+)"":/g, ',"$1":');

                // Tekrar parse etmeyi dene
                scenario.steps = JSON.parse(altStepsStr);
              } catch (innerError) {
                console.error('Alternative parsing also failed:', innerError);

                // Son çare: JSON string'i manuel olarak düzeltmeyi dene
                try {
                  // JSON string'i manuel olarak düzelt
                  const manualFixedJson = values[i]
                    .replace(/""/g, '"')  // Çift tırnakları tekli tırnaklara dönüştür
                    .replace(/^"/,'')     // Baştaki tırnağı kaldır
                    .replace(/"$/,'');    // Sondaki tırnağı kaldır

                  // Tekrar parse etmeyi dene
                  scenario.steps = JSON.parse(manualFixedJson);
                } catch (finalError) {
                  console.error('All parsing attempts failed. Using empty steps array.');
                  scenario.steps = [];
                }
              }
            }
          } else if (lowerHeader === 'tags') {
            scenario.tags = values[i].trim().split(';').map((tag: string) => tag.trim());
          } else if (lowerHeader === 'status') {
            scenario.status = values[i].trim();
          } else {
            scenario[trimmedHeader] = values[i].trim();
          }
        }
      });

      // Zorunlu alanları kontrol et
      if (!scenario.name || !scenario.steps) {
        continue;
      }

      // Gerekli alanları ekle
      scenario.userId = userId;
      scenario.teamId = teamId;
      scenario.companyId = companyId;
      scenario.folderId = folderId;

      // Status alanı yoksa 'active' olarak ayarla
      if (!scenario.status) {
        scenario.status = 'active';
      }

      scenarios.push(scenario);
    }

    return { success: true, scenarios };
  } catch (error) {
    console.error("Error parsing scenarios:", error);
    return { success: false, error: "Failed to parse scenarios" };
  }
}

/**
 * AI ile senaryo adımları oluşturur
 *
 * @param params Oluşturma parametreleri
 */
export async function generateSteps(params: {
  prompt: string;
  name?: string;
  description?: string;
  isTestRail?: boolean;
}) {
  return post('/generate-steps', params);
}

/**
 * Klasörleri getirir
 *
 * @param params Filtreleme parametreleri
 */
export async function getFolders(params?: {
  limit?: number;
  skip?: number;
  teamId?: string;
  companyId?: string;
}) {
  return get('/scenarios/folders', { params });
}

/**
 * Senaryoları ve klasörleri birlikte getirir
 * CompanyId ve TeamId'ye göre filtreleme yapar
 *
 * @param params Filtreleme parametreleri
 */
export async function getScenariosWithFolders(params?: {
  status?: string;
  search?: string;
  limit?: number;
  skip?: number;
  tag?: string;
  testType?: string;
}) {
  // Debug logging removed to improve performance
  const response = await get(SCENARIO_ENDPOINTS.AGGREGATED, { params });
  return response;
}

/**
 * Yeni bir klasör oluşturur
 *
 * @param folder Klasör verileri
 */
export async function createFolder(folder: {
  name: string;
  description?: string;
  color?: string;
  teamId?: string;
  companyId?: string;
}) {
  return post('/scenarios/folders', folder, {
    showSuccessToast: true,
    successMessage: `"${folder.name}" klasörü başarıyla oluşturuldu`
  });
}

/**
 * Bir klasörü günceller
 *
 * @param folderId Klasör ID'si
 * @param folder Güncellenecek klasör verileri
 */
export async function updateFolder(folderId: string, folder: {
  name?: string;
  description?: string;
  color?: string;
}) {
  return put(`/scenarios/folders/${folderId}`, folder, {
    showSuccessToast: true,
    successMessage: 'Klasör başarıyla güncellendi'
  });
}

/**
 * Bir klasörü siler
 *
 * @param folderId Klasör ID'si
 */
export async function deleteFolder(folderId: string) {
  return del(`/scenarios/folders/${folderId}`, {
    showSuccessToast: true,
    successMessage: 'Klasör başarıyla silindi'
  });
}
