/**
 * Common AI Boolean Handler
 * Handles executing AI boolean steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Boolean Handler
 * Handles executing AI boolean steps using the AI agent
 */
export class CommonAiBooleanHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiBoolean';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiBooleanHandler';
  }

  /**
   * Execute an AI boolean step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiBoolean, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const booleanQuestion = step.prompt || step.value || step.name || step.description;

    if (!booleanQuestion) {
      return { success: false, error: 'Missing prompt, value, name or description for aiBoolean step' };
    }

    try {
      // Note: aiBoolean does not support deepThink parameter according to Midscene.js API
      // Only domIncluded and screenshotIncluded options are supported

      logger.debug(`CommonAiBooleanHandler: Executing boolean query "${booleanQuestion}"`);

      const result = await agent.aiBoolean(booleanQuestion || '');
      logger.debug(`aiBoolean result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error && typeof result.result === 'boolean';

      const logEntry = formatAIResultForLog('aiBoolean', booleanQuestion, result);
      logEntry.success = isSuccess;
      logEntry.metadata.result = result.result;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Boolean query failed: ${booleanQuestion}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime,
          result: result.result
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Boolean Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiBoolean',
        description: booleanQuestion,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiBooleanHandler: Boolean query failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 