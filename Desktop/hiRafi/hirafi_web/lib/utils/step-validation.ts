/**
 * Step Validation Utilities
 * 
 * Provides validation rules and functions for different step types
 * based on the optimized field structure
 */

import { TestStep, ActionType } from '@/models/scenario'

/**
 * Defines which fields are required for each step type
 */
export const STEP_FIELD_REQUIREMENTS: Record<ActionType, {
  required: (keyof TestStep)[]
  optional: (keyof TestStep)[]
  description: string
}> = {
  // Browser Control
  goto: {
    required: ['url'],
    optional: ['name', 'description'],
    description: 'Navigate to a specific URL'
  },
  sleep: {
    required: ['duration'],
    optional: ['name', 'description'],
    description: 'Pause execution for a specified time'
  },

  // AI Input (special case - uses value and target)
  aiInput: {
    required: ['value', 'target'],
    optional: ['name', 'description', 'deepThink'],
    description: 'Type text into a specified input field'
  },

  // AI Actions (use prompt field)
  aiAction: {
    required: ['prompt'],
    optional: ['name', 'description', 'deepThink'],
    description: 'Perform a high-level action using AI'
  },
  aiTap: {
    required: ['prompt'],
    optional: ['name', 'description', 'deepThink'],
    description: 'Click or tap on a specific element'
  },
  aiAssertion: {
    required: ['prompt'],
    optional: ['name', 'description', 'deepThink'],
    description: 'Verify that a condition is true'
  },
  aiWaitElement: {
    required: ['prompt'],
    optional: ['name', 'description', 'timeoutMs', 'checkIntervalMs'],
    description: 'Wait for an element to appear or meet a condition'
  },
  aiHover: {
    required: ['prompt'],
    optional: ['name', 'description', 'deepThink'],
    description: 'Move mouse cursor over an element'
  },
  aiKeyboardPress: {
    required: ['prompt'],
    optional: ['name', 'description', 'target'],
    description: 'Press a keyboard key'
  },
  aiScroll: {
    required: ['prompt'],
    optional: ['name', 'description', 'deepThink'],
    description: 'Scroll the page or an element'
  },
  aiRightClick: {
    required: ['prompt'],
    optional: ['name', 'description', 'deepThink'],
    description: 'Right-click on an element'
  },

  // Data Extraction (use prompt field)
  aiQuery: {
    required: ['prompt'],
    optional: ['name', 'description', 'aiQueryFields'],
    description: 'Extract data from the page'
  },
  aiString: {
    required: ['prompt'],
    optional: ['name', 'description'],
    description: 'Extract text content from the page'
  },
  aiNumber: {
    required: ['prompt'],
    optional: ['name', 'description'],
    description: 'Extract numeric value from the page'
  },
  aiBoolean: {
    required: ['prompt'],
    optional: ['name', 'description'],
    description: 'Check if a condition is true or false'
  },
  aiLocate: {
    required: ['prompt'],
    optional: ['name', 'description'],
    description: 'Find the location of an element'
  },

  // Control Flow
  ifElse: {
    required: ['condition'],
    optional: ['name', 'description', 'trueSteps', 'falseSteps'],
    description: 'Execute different steps based on a condition'
  },
  forLoop: {
    required: ['iterationCount'],
    optional: ['name', 'description', 'loopSteps'],
    description: 'Repeat steps a specific number of times'
  },
  whileLoop: {
    required: ['condition'],
    optional: ['name', 'description', 'loopSteps', 'maxIterations'],
    description: 'Repeat steps while a condition is true'
  }
}

/**
 * Validates a single step based on its type requirements
 */
export function validateStep(step: TestStep): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []
  
  const requirements = STEP_FIELD_REQUIREMENTS[step.type]
  if (!requirements) {
    errors.push(`Unknown step type: ${step.type}`)
    return { isValid: false, errors, warnings }
  }

  // Check required fields
  for (const field of requirements.required) {
    const value = step[field]
    if (!value || (typeof value === 'string' && !value.trim())) {
      errors.push(`${field} is required for ${step.type} steps`)
    }
  }

  // Type-specific validations
  if (step.type === 'sleep' && step.duration && step.duration <= 0) {
    errors.push('Duration must be greater than 0')
  }

  if (step.type === 'goto' && step.url && !isValidUrl(step.url)) {
    warnings.push('URL format may be invalid')
  }

  if (step.type === 'aiWaitElement') {
    if (step.timeoutMs && step.timeoutMs <= 0) {
      errors.push('Timeout must be greater than 0')
    }
    if (step.checkIntervalMs && step.checkIntervalMs <= 0) {
      errors.push('Check interval must be greater than 0')
    }
  }

  if (step.type === 'whileLoop' && step.maxIterations && step.maxIterations <= 0) {
    errors.push('Max iterations must be greater than 0')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validates an array of steps
 */
export function validateSteps(steps: TestStep[]): {
  isValid: boolean
  errors: string[]
  warnings: string[]
  stepErrors: Record<string, string[]>
} {
  const errors: string[] = []
  const warnings: string[] = []
  const stepErrors: Record<string, string[]> = {}

  if (!steps || steps.length === 0) {
    errors.push('At least one test step is required')
    return { isValid: false, errors, warnings, stepErrors }
  }

  // Validate each step
  for (const step of steps) {
    const stepValidation = validateStep(step)
    if (!stepValidation.isValid) {
      stepErrors[step.id] = stepValidation.errors
    }
    warnings.push(...stepValidation.warnings)
  }

  // Check for duplicate step IDs
  const stepIds = steps.map(s => s.id)
  const duplicateIds = stepIds.filter((id, index) => stepIds.indexOf(id) !== index)
  if (duplicateIds.length > 0) {
    errors.push(`Duplicate step IDs found: ${duplicateIds.join(', ')}`)
  }

  return {
    isValid: errors.length === 0 && Object.keys(stepErrors).length === 0,
    errors,
    warnings,
    stepErrors
  }
}

/**
 * Gets the required fields for a specific step type
 */
export function getRequiredFields(stepType: ActionType): (keyof TestStep)[] {
  return STEP_FIELD_REQUIREMENTS[stepType]?.required || []
}

/**
 * Gets the optional fields for a specific step type
 */
export function getOptionalFields(stepType: ActionType): (keyof TestStep)[] {
  return STEP_FIELD_REQUIREMENTS[stepType]?.optional || []
}

/**
 * Checks if a step has all required fields filled
 */
export function hasRequiredFields(step: TestStep): boolean {
  const validation = validateStep(step)
  return validation.isValid
}

/**
 * Gets user-friendly field names for display
 */
export const FIELD_DISPLAY_NAMES: Record<keyof TestStep, string> = {
  id: 'ID',
  type: 'Type',
  name: 'Name',
  description: 'Description',
  deepThink: 'Deep Think',
  url: 'URL',
  duration: 'Duration (seconds)',
  value: 'Text to Input',
  target: 'Target Element',
  prompt: 'Instruction',
  timeoutMs: 'Timeout (ms)',
  checkIntervalMs: 'Check Interval (ms)',
  condition: 'Condition',
  trueSteps: 'True Steps',
  falseSteps: 'False Steps',
  loopSteps: 'Loop Steps',
  maxIterations: 'Max Iterations',
  iterationCount: 'Iteration Count',
  aiQueryFields: 'Query Fields'
}

/**
 * Simple URL validation
 */
function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    // Check for relative URLs or template variables
    return url.startsWith('/') || url.includes('{{') || url.includes('${')
  }
}
