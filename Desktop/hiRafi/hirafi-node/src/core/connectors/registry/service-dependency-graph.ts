/**
 * Service Dependency Graph
 * 
 * Manages service dependencies and detects circular dependencies.
 */

import { logger } from '../../../utils/logger.js';

/**
 * Service Dependency Graph
 * 
 * Tracks service dependencies and provides utilities for dependency analysis.
 */
export class ServiceDependencyGraph {
  private dependencies: Map<string, Set<string>> = new Map();
  private enableLogging: boolean;

  constructor(enableLogging: boolean = false) {
    this.enableLogging = enableLogging;
  }

  /**
   * Add a service to the dependency graph
   * @param serviceName Service name
   * @param dependencies Service dependencies
   */
  addService(serviceName: string, dependencies: string[] = []): void {
    if (!this.dependencies.has(serviceName)) {
      this.dependencies.set(serviceName, new Set());
    }

    const serviceDeps = this.dependencies.get(serviceName)!;
    dependencies.forEach(dep => serviceDeps.add(dep));

    if (this.enableLogging) {
      logger.debug(`ServiceDependencyGraph: Added service '${serviceName}' with dependencies: [${dependencies.join(', ')}]`);
    }
  }

  /**
   * Remove a service from the dependency graph
   * @param serviceName Service name
   */
  removeService(serviceName: string): void {
    this.dependencies.delete(serviceName);

    // Remove this service as a dependency from other services
    for (const [service, deps] of this.dependencies) {
      if (deps.has(serviceName)) {
        deps.delete(serviceName);
      }
    }

    if (this.enableLogging) {
      logger.debug(`ServiceDependencyGraph: Removed service '${serviceName}'`);
    }
  }

  /**
   * Check if there's a circular dependency starting from a service
   * @param startService Starting service name
   * @returns True if circular dependency exists
   */
  hasCircularDependency(startService: string): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    return this.detectCycle(startService, visited, recursionStack);
  }

  /**
   * Detect cycle using DFS
   * @param service Current service
   * @param visited Visited services
   * @param recursionStack Current recursion stack
   * @returns True if cycle detected
   */
  private detectCycle(
    service: string,
    visited: Set<string>,
    recursionStack: Set<string>
  ): boolean {
    if (recursionStack.has(service)) {
      return true; // Cycle detected
    }

    if (visited.has(service)) {
      return false; // Already processed
    }

    visited.add(service);
    recursionStack.add(service);

    const dependencies = this.dependencies.get(service) || new Set();
    for (const dependency of dependencies) {
      if (this.detectCycle(dependency, visited, recursionStack)) {
        return true;
      }
    }

    recursionStack.delete(service);
    return false;
  }

  /**
   * Get dependency order for a service (topological sort)
   * @param serviceName Service name
   * @returns Array of services in dependency order
   */
  getDependencyOrder(serviceName: string): string[] {
    const visited = new Set<string>();
    const result: string[] = [];

    this.topologicalSort(serviceName, visited, result);

    return result.reverse(); // Reverse to get correct order
  }

  /**
   * Perform topological sort using DFS
   * @param service Current service
   * @param visited Visited services
   * @param result Result array
   */
  private topologicalSort(
    service: string,
    visited: Set<string>,
    result: string[]
  ): void {
    if (visited.has(service)) {
      return;
    }

    visited.add(service);

    const dependencies = this.dependencies.get(service) || new Set();
    for (const dependency of dependencies) {
      this.topologicalSort(dependency, visited, result);
    }

    result.push(service);
  }

  /**
   * Get all services in the graph
   * @returns Array of all service names
   */
  getAllServices(): string[] {
    return Array.from(this.dependencies.keys());
  }

  /**
   * Get direct dependencies of a service
   * @param serviceName Service name
   * @returns Array of direct dependencies
   */
  getServiceDependencies(serviceName: string): string[] {
    const dependencies = this.dependencies.get(serviceName);
    return dependencies ? Array.from(dependencies) : [];
  }

  /**
   * Get all services that depend on a given service
   * @param serviceName Service name
   * @returns Array of dependent services
   */
  getDependentServices(serviceName: string): string[] {
    const dependents: string[] = [];

    for (const [service, deps] of this.dependencies) {
      if (deps.has(serviceName)) {
        dependents.push(service);
      }
    }

    return dependents;
  }

  /**
   * Get transitive dependencies of a service
   * @param serviceName Service name
   * @returns Set of all transitive dependencies
   */
  getTransitiveDependencies(serviceName: string): Set<string> {
    const transitive = new Set<string>();
    const visited = new Set<string>();

    this.collectTransitiveDependencies(serviceName, transitive, visited);

    return transitive;
  }

  /**
   * Collect transitive dependencies recursively
   * @param service Current service
   * @param transitive Transitive dependencies set
   * @param visited Visited services
   */
  private collectTransitiveDependencies(
    service: string,
    transitive: Set<string>,
    visited: Set<string>
  ): void {
    if (visited.has(service)) {
      return;
    }

    visited.add(service);

    const dependencies = this.dependencies.get(service) || new Set();
    for (const dependency of dependencies) {
      transitive.add(dependency);
      this.collectTransitiveDependencies(dependency, transitive, visited);
    }
  }

  /**
   * Validate the entire dependency graph
   * @returns Validation result with any issues found
   */
  validate(): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check for circular dependencies
    for (const service of this.getAllServices()) {
      if (this.hasCircularDependency(service)) {
        issues.push(`Circular dependency detected starting from service '${service}'`);
      }
    }

    // Check for missing dependencies
    for (const [service, deps] of this.dependencies) {
      for (const dependency of deps) {
        if (!this.dependencies.has(dependency)) {
          issues.push(`Service '${service}' depends on unregistered service '${dependency}'`);
        }
      }
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  /**
   * Get graph statistics
   * @returns Graph statistics
   */
  getStats(): Record<string, any> {
    const totalServices = this.dependencies.size;
    let totalDependencies = 0;
    let maxDependencies = 0;
    let servicesWithNoDependencies = 0;

    for (const deps of this.dependencies.values()) {
      const depCount = deps.size;
      totalDependencies += depCount;
      maxDependencies = Math.max(maxDependencies, depCount);
      
      if (depCount === 0) {
        servicesWithNoDependencies++;
      }
    }

    const averageDependencies = totalServices > 0 ? totalDependencies / totalServices : 0;

    return {
      totalServices,
      totalDependencies,
      averageDependencies: Math.round(averageDependencies * 100) / 100,
      maxDependencies,
      servicesWithNoDependencies,
      graphValid: this.validate().valid
    };
  }

  /**
   * Clear the dependency graph
   */
  clear(): void {
    this.dependencies.clear();

    if (this.enableLogging) {
      logger.debug('ServiceDependencyGraph: Cleared dependency graph');
    }
  }
}
