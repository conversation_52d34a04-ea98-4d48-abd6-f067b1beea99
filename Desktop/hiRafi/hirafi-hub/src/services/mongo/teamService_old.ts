/**
 * Team Service
 * Takım yönetimi için MongoDB servis fonksiyonları
 */

import { Collection } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import { db, ensureMongoDBConnection, isMongoDBInitialized, teamsCollection, usersCollection } from './dbConnection.js';
import { Team, TeamStatus, TeamMember, MembershipStatus } from '../../models/team.js';
import { User, AccountType } from '../../models/user.js';

/**
 * Yeni bir takım oluştur
 * @param teamData Takım verileri
 * @returns Oluşturulan takım ID'si ve başarı durumu
 */
export async function createTeam(teamData: Partial<Team>): Promise<{
  success: boolean;
  teamId?: string;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection || !teamMembersCollection || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Takım ID'si oluştur
    const teamId = teamData.id || uuidv4();
    const now = new Date();

    // Gerekli alanları kontrol et
    if (!teamData.name) {
      return { success: false, message: "Team name is required" };
    }

    if (!teamData.createdBy) {
      return { success: false, message: "Creator ID is required" };
    }

    // Takım nesnesini oluştur
    const team: Team = {
      id: teamId,
      name: teamData.name,
      description: teamData.description || '',
      createdAt: now,
      updatedAt: now,
      createdBy: teamData.createdBy,
      status: TeamStatus.ACTIVE,
      tags: teamData.tags || [],
      avatarUrl: teamData.avatarUrl,
      companyId: teamData.companyId, // Şirket ID'sini ekle
      settings: teamData.settings || {
        canMembersInvite: true,
        visibilityLevel: 'private',
        allowExternalMembers: false,
        approvalRequired: false
      }
    };

    // Takımı ekle
    await teamsCollection.insertOne(team);

    // Varsayılan roller artık global olarak role_permissions tablosunda mevcut

    // Oluşturan kullanıcıyı takım üyesi olarak ekle (TeamAdmin rolü)
    const teamMember: TeamMember = {
      id: uuidv4(),
      user_id: teamData.createdBy,
      team_id: teamId,
      role_id: `team_admin_${teamId}`, // Takıma özel team_admin rolü
      status: MembershipStatus.ACTIVE,
      added_by: teamData.createdBy,
      added_at: now,
      last_active: now
    };

    await teamMembersCollection.insertOne(teamMember);

    // Kullanıcının teamId alanını güncelle
    await usersCollection.updateOne(
      { id: teamData.createdBy },
      {
        $set: {
          teamId: teamId
        }
      }
    );

    logger.info(`Created new team with ID: ${teamId} by user: ${teamData.createdBy}`);

    return {
      success: true,
      teamId: teamId
    };
  } catch (error: any) {
    logger.error(`Error creating team: ${error.message}`);
    return {
      success: false,
      message: `Failed to create team: ${error.message}`
    };
  }
}

/**
 * Takımı ID'ye göre getir
 * @param teamId Takım ID'si
 * @returns Takım ve başarı durumu
 */
export async function getTeamById(teamId: string): Promise<{
  success: boolean;
  team?: Team;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const team = await teamsCollection.findOne({ id: teamId });

    if (!team) {
      return {
        success: false,
        message: `Team with ID ${teamId} not found`
      };
    }

    return {
      success: true,
      team: team as unknown as Team
    };
  } catch (error: any) {
    logger.error(`Error getting team by ID: ${error.message}`);
    return {
      success: false,
      message: `Failed to get team: ${error.message}`
    };
  }
}

/**
 * Kullanıcının takımlarını getir
 * @param userId Kullanıcı ID'si
 * @param options Sayfalama seçenekleri
 * @returns Takımlar ve başarı durumu
 */
export async function getTeamsByUserId(
  userId: string,
  options: {
    limit?: number;
    skip?: number;
    includeArchived?: boolean;
  } = {}
): Promise<{
  success: boolean;
  teams?: Team[];
  total?: number;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamMembersCollection || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const { limit = 10, skip = 0, includeArchived = false } = options;

    // Önce bu kullanıcının üye olduğu takımların ID'lerini alıyoruz

    // Sadece aktif üyelikleri getir
    const memberships = await teamMembersCollection
      .find({
        user_id: userId,
        status: MembershipStatus.ACTIVE
      })
      .toArray();

    const teamIds = memberships.map(m => m.team_id);

    if (teamIds.length === 0) {
      return {
        success: true,
        teams: [],
        total: 0
      };
    }

    // Takım verilerini getir

    // Temel sorgu
    const query: any = {
      id: { $in: teamIds }
    };

    // Arşivlenmiş takımları dahil etme seçeneği
    if (!includeArchived) {
      query.status = { $ne: TeamStatus.ARCHIVED };
    }

    // Toplam sayı
    const total = await teamsCollection.countDocuments(query);

    // Takımları getir
    const teams = await teamsCollection
      .find(query)
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    return {
      success: true,
      teams: teams as unknown as Team[],
      total
    };
  } catch (error: any) {
    logger.error(`Error getting teams for user: ${error.message}`);
    return {
      success: false,
      message: `Failed to get teams: ${error.message}`
    };
  }
}

/**
 * Takımı güncelle
 * @param teamId Takım ID'si
 * @param updateData Güncellenecek veriler
 * @returns Başarı durumu
 */
export async function updateTeam(
  teamId: string,
  updateData: Partial<Team>
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Güncellenemeyecek alanları kaldır
    const safeUpdateData = { ...updateData };
    delete safeUpdateData.id;
    delete safeUpdateData.createdAt;
    delete safeUpdateData.createdBy;

    // Güncellenme zamanını ayarla
    safeUpdateData.updatedAt = new Date();

    const result = await teamsCollection.updateOne(
      { id: teamId },
      { $set: safeUpdateData }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: `Team with ID ${teamId} not found`
      };
    }

    logger.info(`Updated team ${teamId}`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error updating team: ${error.message}`);
    return {
      success: false,
      message: `Failed to update team: ${error.message}`
    };
  }
}

/**
 * Takımı sil (arşivle)
 * @param teamId Takım ID'si
 * @returns Başarı durumu
 */
export async function deleteTeam(
  teamId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Takımı tamamen silmek yerine arşivle
    const result = await teamsCollection.updateOne(
      { id: teamId },
      {
        $set: {
          status: TeamStatus.ARCHIVED,
          updatedAt: new Date()
        }
      }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: `Team with ID ${teamId} not found`
      };
    }

    logger.info(`Archived team ${teamId}`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error archiving team: ${error.message}`);
    return {
      success: false,
      message: `Failed to archive team: ${error.message}`
    };
  }
}

/**
 * Takıma üye ekle
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 * @param roleId Rol ID'si
 * @param addedBy Ekleyen kullanıcı ID'si
 * @returns Başarı durumu
 */
export async function addTeamMember(
  teamId: string,
  userId: string,
  roleId: string,
  addedBy: string
): Promise<{
  success: boolean;
  memberId?: string;
  message?: string;
}> {
  // Null kontrolü yap
  if (!teamId || !userId || !roleId) {
    logger.error(`Cannot add team member: required fields missing. teamId=${teamId}, userId=${userId}, roleId=${roleId}`);
    return { success: false, message: 'teamId, userId and roleId are required' };
  }

  try {
    if (!isMongoDBInitialized() || !db || !usersCollection || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    logger.info(`Adding team member - teamId: ${teamId}, userId: ${userId}, roleId: ${roleId}, addedBy: ${addedBy}`);

    // Kullanıcı ve takımın var olup olmadığını kontrol et
    const user = await usersCollection.findOne({ id: userId });
    if (!user) {
      logger.error(`User with ID ${userId} not found`);
      return { success: false, message: `User with ID ${userId} not found` };
    }

    const team = await teamsCollection.findOne({ id: teamId });
    if (!team) {
      logger.error(`Team with ID ${teamId} not found`);
      return { success: false, message: `Team with ID ${teamId} not found` };
    }

    // Kullanıcının zaten üye olup olmadığını kontrol et
    const existingMember = await rolePermissionsCollection.findOne({
      team_id: teamId,
      user_id: userId,
      is_system: false
    });

    // Sistem rollerinden rol bilgilerini al
    const systemRole = await rolePermissionsCollection.findOne({
      role_id: roleId,
      is_system: true
    });

    if (!systemRole) {
      logger.error(`System role with ID ${roleId} not found`);
      return { success: false, message: `Role with ID ${roleId} not found` };
    }

    // Mevcut üye varsa güncelle, yoksa yeni oluştur
    if (existingMember) {
      // Mevcut üyeyi güncelle
      await rolePermissionsCollection.updateOne(
        { id: existingMember.id },
        {
          $set: {
            role_id: roleId,
            role_name: systemRole.role_name,
            permissions: systemRole.permissions,
            status: 'active',
            added_by: addedBy,
            updated_at: new Date()
          }
        }
      );

      logger.info(`Updated user ${userId} role to ${roleId} in team ${teamId}`);
      return {
        success: true,
        memberId: existingMember.id,
        message: `User role updated to ${systemRole.role_name}`
      };
    }

    // Yeni üyelik oluştur
    const memberId = uuidv4();
    const now = new Date();

    const newMember = {
      id: memberId,
      user_id: userId,
      team_id: teamId,
      company_id: team.company_id,
      role_id: roleId,
      role_name: systemRole.role_name,
      permissions: systemRole.permissions,
      status: 'active',
      added_by: addedBy,
      added_at: now,
      created_at: now,
      is_system: false
    };

    await rolePermissionsCollection.insertOne(newMember);

    // Kullanıcının teamId alanını güncelle
    await usersCollection.updateOne(
      { id: userId },
      { $set: { teamId: teamId } }
    );

    logger.info(`Added user ${userId} to team ${teamId} with role ${roleId}`);

    return {
      success: true,
      memberId
    };


  } catch (error: any) {
    logger.error(`Error adding team member: ${error.message}`);
    return {
      success: false,
      message: `Failed to add team member: ${error.message}`
    };
  }
}

/**
 * Takım üyesini güncelle
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 * @param updateData Güncellenecek veriler
 * @returns Başarı durumu
 */
export async function updateTeamMember(
  teamId: string,
  userId: string,
  updateData: {
    roleId?: string;
    status?: MembershipStatus;
  }
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamMembersCollection || !teamRolesCollection || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Güncellenecek verileri hazırla
    const update: any = {
      last_active: new Date()
    };

    // Eğer rol değişiyorsa, rol bilgilerini ve izinleri güncelle
    if (updateData.roleId) {
      if (!teamRolesCollection) throw new Error('teamRolesCollection not available');
      try {
        // Önce takıma özel rol ID'sini kontrol et
        let roleInfo = await teamRolesCollection.findOne({
          $or: [
            { id: updateData.roleId, team_id: teamId },
            { id: `${updateData.roleId}_${teamId}` }
          ]
        });

        // Rol bulunamadıysa, küçük harfle kontrol et
        if (!roleInfo && typeof updateData.roleId === 'string') {
          const lowerCaseRoleId = updateData.roleId.toLowerCase();
          logger.info(`Role with ID ${updateData.roleId} not found, trying lowercase: ${lowerCaseRoleId}`);
          roleInfo = await teamRolesCollection.findOne({ id: lowerCaseRoleId });
        }

        // Hala bulunamadıysa, isim üzerinden kontrol et
        if (!roleInfo && typeof updateData.roleId === 'string') {
          logger.info(`Role with ID ${updateData.roleId} not found by ID, trying to find by name`);
          roleInfo = await teamRolesCollection.findOne({
            name: { $regex: new RegExp(updateData.roleId, 'i') }
          });

          if (roleInfo) {

          }
        }

        // Geriye dönük uyumluluk için roles koleksiyonunu da kontrol et
        if (!roleInfo) {
          logger.warn(`Role with ID ${updateData.roleId} not found in team_roles collection, trying fallback to roles collection`);
          const oldRolesCollection = db.collection('roles');
          roleInfo = await oldRolesCollection.findOne({ id: updateData.roleId });

          // Eski koleksiyonda da küçük harfle ve isimle kontrol et
          if (!roleInfo && typeof updateData.roleId === 'string') {
            const lowerCaseRoleId = updateData.roleId.toLowerCase();
            roleInfo = await oldRolesCollection.findOne({ id: lowerCaseRoleId });

            if (!roleInfo) {
              roleInfo = await oldRolesCollection.findOne({
                name: { $regex: new RegExp(updateData.roleId, 'i') }
              });
            }
          }
        }

        // Hala bulunamadıysa, takıma özel varsayılan rolleri kontrol et
        if (!roleInfo) {
          logger.warn(`Role with ID ${updateData.roleId} not found in any collection, trying team-specific default roles`);

          // Önce takıma özel team_member rolünü dene
          roleInfo = await teamRolesCollection.findOne({ id: `team_member_${teamId}` });

          // Bulunamadıysa takıma özel team_admin rolünü dene
          if (!roleInfo) {
            roleInfo = await teamRolesCollection.findOne({ id: `team_admin_${teamId}` });
          }

          // Hala bulunamadıysa, genel team_member rolünü dene
          if (!roleInfo) {
            roleInfo = await teamRolesCollection.findOne({ id: 'team_member' });
          }

          // Hala bulunamadıysa, takımdaki herhangi bir rolü dene
          if (!roleInfo) {
            roleInfo = await teamRolesCollection.findOne({ team_id: teamId });
          }

          // Hiçbir rol bulunamadıysa hata dön
          if (!roleInfo) {
            return {
              success: false,
              message: `Role with ID ${updateData.roleId} not found in any role collections and no default roles are available for team ${teamId}`
            };
          }

          logger.info(`Using default role ${roleInfo.name} with ID ${roleInfo.id} for team ${teamId}`);
        }

        // Rol ID'sini doğru formatta kullan (sadece roleId)
        const roleIdToUse = roleInfo.id;

        logger.info(`Using role ID: ${roleIdToUse}`);

        update.role_id = roleIdToUse;
        update.role_name = roleInfo.name;
        update.permissions = roleInfo.permissions;
      } catch (roleError: any) {
        logger.error(`Error fetching role information: ${roleError.message}`);
        return {
          success: false,
          message: `Failed to fetch role information: ${roleError.message}`
        };
      }
    }

    if (updateData.status) update.status = updateData.status;

    const result = await teamMembersCollection.updateOne(
      { team_id: teamId, user_id: userId },
      { $set: update }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: `Team membership not found`
      };
    }

    // Üyelik devre dışı bırakıldıysa ve bu kullanıcının varsayılan takımı ise güncelle
    if (updateData.status === MembershipStatus.INACTIVE) {
      if (!usersCollection) throw new Error('usersCollection not available');
      const user = await usersCollection.findOne({ id: userId });

      if (user && user.defaultTeamId === teamId) {
        // Kullanıcının varsayılan takımını sıfırla
        await usersCollection.updateOne(
          { id: userId },
          { $set: { defaultTeamId: null } }
        );
      }
    }

    logger.info(`Updated team membership for user ${userId} in team ${teamId}`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error updating team member: ${error.message}`);
    return {
      success: false,
      message: `Failed to update team member: ${error.message}`
    };
  }
}

/**
 * Takım üyesini kaldır
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 * @returns Başarı durumu
 */
export async function removeTeamMember(
  teamId: string,
  userId: string
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamMembersCollection || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Önce kullanıcıyı ve rolünü kontrol et
    const member = await teamMembersCollection.findOne({ team_id: teamId, user_id: userId });
    if (!member) {
      return {
        success: false,
        message: `Team membership not found`
      };
    }

    // Tamamen silme işlemi yap
    await teamMembersCollection.deleteOne({ team_id: teamId, user_id: userId });

    // Kullanıcı bilgilerini getir
    const userInfo = await usersCollection.findOne({ id: userId });

    // Kullanıcının teams dizisinden kaldır (eğer varsa)
    if (userInfo && userInfo.teams && Array.isArray(userInfo.teams)) {
      // MongoDB'nin $pull operatörü bir dizi içindeki değerleri kaldırmak için kullanılır
      const updatedTeams = userInfo.teams.filter(id => id !== teamId);
      await usersCollection.updateOne(
        { id: userId },
        { $set: { teams: updatedTeams } }
      );
    }

    if (userInfo) {
      if (userInfo.defaultTeamId === teamId) {
        // Kullanıcının varsayılan takımını sıfırla
        await usersCollection.updateOne(
          { id: userId },
          { $set: { defaultTeamId: null } }
        );
      }

      // Kullanıcının teamId ve companyId alanlarını null yap
      await usersCollection.updateOne(
        { id: userId },
        { $set: { teamId: null, companyId: null, updatedAt: new Date() } }
      );
      logger.info(`Updated user ${userId} teamId and companyId to null as they are no longer a member of team ${teamId}`);
    }

    logger.info(`Removed user ${userId} from team ${teamId}`);

    return {
      success: true
    };
  } catch (error: any) {
    logger.error(`Error removing team member: ${error.message}`);
    return {
      success: false,
      message: `Failed to remove team member: ${error.message}`
    };
  }
}

/**
 * Takım üyelerini getir
 * @param teamId Takım ID'si
 * @param options Sayfalama seçenekleri
 * @returns Üyeler ve başarı durumu
 */
export async function getTeamMembers(
  teamId: string,
  options: {
    limit?: number;
    skip?: number;
    includeInactive?: boolean;
  } = {}
): Promise<{
  success: boolean;
  members?: any[];
  total?: number;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const { limit = 10, skip = 0, includeInactive = false } = options;
    const rolePermissionsCollection = db.collection('role_permissions');

    // Takım üyelerini filtrele (role_permissions'dan)
    const query: any = {
      team_id: teamId,
      user_id: { $exists: true, $ne: null }, // Sadece user assignment'ları
      is_system: false // Sistem rolleri değil
    };

    // Aktif üyeleri filtrele
    if (!includeInactive) {
      query.status = 'active';
    }

    // Toplam sayıyı al
    const total = await rolePermissionsCollection.countDocuments(query);

    // Üyeleri getir
    const members = await rolePermissionsCollection
      .find(query)
      .sort({ added_at: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Kullanıcı bilgilerini al
    const extendedMembers = await Promise.all(members.map(async (member) => {
      if (!usersCollection) {
        logger.error('usersCollection is not initialized');
        return { success: false, message: 'Users collection not available' };
      }
      const user = await usersCollection.findOne({ id: member.user_id });

      // role_permissions'da zaten tüm bilgiler var
      const role = {
        id: member.role_id,
        name: member.role_name,
        permissions: member.permissions
      };

      // Kullanıcı verileriyle birleştir
      return {
        id: member.id,
        user_id: member.user_id,
        team_id: member.team_id,
        role_id: member.role_id,
        role_name: member.role_name,
        permissions: member.permissions,
        status: member.status,
        added_by: member.added_by,
        added_at: member.added_at,
        user: user ? {
          id: user.id,
          name: user.name || user.displayName || user.username,
          email: user.email,
          lastLogin: user.lastLogin
        } : null,
        role
      };
    }));

    return {
      success: true,
      members: extendedMembers,
      total
    };
  } catch (error: any) {
    logger.error(`Error getting team members: ${error.message}`);
    return {
      success: false,
      message: `Failed to get team members: ${error.message}`
    };
  }
}

/**
 * Kullanıcının takım üyeliğini kontrol et
 * @param userId Kullanıcı ID'si
 * @param teamId Takım ID'si
 * @returns Üyelik durumu
 */
export async function checkTeamMembership(
  userId: string,
  teamId: string
): Promise<{
  success: boolean;
  isMember: boolean;
  role?: string;
  message?: string;
}> {
  try {
    // Veritabanı bağlantısını kontrol et ve gerekirse yeniden bağlan
    try {
      await ensureMongoDBConnection();
    } catch (connError: any) {
      logger.error(`Failed to ensure MongoDB connection for team membership check: ${connError.message}`);
      return { success: false, isMember: false, message: `Database connection error: ${connError.message}` };
    }

    if (!isMongoDBInitialized() || !db || !teamMembersCollection) {
      logger.error('MongoDB is not initialized for team membership check');
      return { success: false, isMember: false, message: 'Database connection not established' };
    }

    // Takım üyeleri koleksiyonunu al
    // Takım üyeliğini kontrol et
    try {
      if (!teamMembersCollection) throw new Error('teamMembersCollection not available');
      const membership = await teamMembersCollection.findOne({
        team_id: teamId,
        user_id: userId,
        status: MembershipStatus.ACTIVE
      });

      if (!membership) {
        return {
          success: true,
          isMember: false
        };
      }

      return {
        success: true,
        isMember: true,
        role: membership.roleId
      };
    } catch (queryError: any) {
      logger.error(`Error querying team membership: ${queryError.message}`);
      return {
        success: false,
        isMember: false,
        message: `Failed to query team membership: ${queryError.message}`
      };
    }
  } catch (error: any) {
    logger.error(`Unexpected error checking team membership: ${error.message}`);
    return {
      success: false,
      isMember: false,
      message: `Failed to check team membership: ${error.message}`
    };
  }
}

/**
 * Kullanıcının takıma olan yetkilendirmesini kontrol et
 * @param userId Kullanıcı ID'si
 * @param teamId Takım ID'si
 * @param requiredRoles İzin verilen roller
 * @returns Yetkilendirme durumu
 */
export async function authorizeTeamAction(
  userId: string,
  teamId: string,
  requiredRoles: string[] = ['team_admin']
): Promise<{
  success: boolean;
  authorized: boolean;
  message?: string;
}> {
  try {
    // Veritabanı bağlantısını kontrol et ve gerekirse yeniden bağlan
    try {
      await ensureMongoDBConnection();
    } catch (connError: any) {
      logger.error(`Failed to ensure MongoDB connection for team authorization check: ${connError.message}`);
      return { success: false, authorized: false, message: `Database connection error: ${connError.message}` };
    }

    if (!isMongoDBInitialized() || !db || !usersCollection || !teamMembersCollection) {
      logger.error('MongoDB is not initialized for team authorization check');
      return { success: false, authorized: false, message: 'Database connection not established' };
    }

    // Önce kullanıcının hesap türünü kontrol et
    try {
      const user = await usersCollection.findOne({ id: userId });

      // Admin ve company_owner hesap türleri her zaman yetkilidir
      if (user && (user.accountType === 'admin' || user.accountType === 'company_owner')) {
        logger.info(`User ${userId} is a ${user.accountType} and has permission to perform actions on team ${teamId}`);
        return {
          success: true,
          authorized: true
        };
      }
    } catch (userError: any) {
      logger.error(`Error fetching user information: ${userError.message}`);
      // Kullanıcı bilgisi alınamazsa, takım üyeliği kontrolüne devam et
    }

    // Takım üyeleri koleksiyonunu al
    // Üyeliği kontrol et
    try {
      const membership = await teamMembersCollection.findOne({
        team_id: teamId,
        user_id: userId,
        status: MembershipStatus.ACTIVE
      });

      if (!membership) {
        return {
          success: true,
          authorized: false,
          message: "User is not a member of this team"
        };
      }

      // Rolü kontrol et
      if (!requiredRoles.includes(membership.roleId)) {
        return {
          success: true,
          authorized: false,
          message: "User does not have the required role for this action"
        };
      }

      return {
        success: true,
        authorized: true
      };
    } catch (queryError: any) {
      logger.error(`Error querying team membership for authorization: ${queryError.message}`);
      return {
        success: false,
        authorized: false,
        message: `Failed to query team membership for authorization: ${queryError.message}`
      };
    }
  } catch (error: any) {
    logger.error(`Unexpected error authorizing team action: ${error.message}`);
    return {
      success: false,
      authorized: false,
      message: `Failed to authorize team action: ${error.message}`
    };
  }
}

/**
 * Get teams for a company
 * @param companyId Company ID
 * @param options Query options
 * @returns Teams and success status
 */
export async function getCompanyTeams(
  companyId: string,
  options: {
    limit?: number;
    skip?: number;
    includeArchived?: boolean;
  } = {}
): Promise<{
  success: boolean;
  teams?: Team[];
  total?: number;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamsCollection || !teamMembersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    const { limit = 10, skip = 0, includeArchived = false } = options;

    // Build query for company teams
    const query: any = { companyId };

    // Exclude archived teams unless requested
    if (!includeArchived) {
      query.status = { $ne: TeamStatus.ARCHIVED };
    }

    // Get total count
    const total = await teamsCollection.countDocuments(query);

    // Get teams
    const teams = await teamsCollection
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Enhance teams with member count
    const enhancedTeams = await Promise.all(teams.map(async (team) => {
      try {
        if (!teamMembersCollection) {
          logger.error('teamMembersCollection is not initialized');
          return { success: false, message: 'Team members collection not available' };
        }
        const memberCount = await teamMembersCollection.countDocuments({
          team_id: team.id,
          status: MembershipStatus.ACTIVE
        });

        return {
          ...team,
          membersCount: memberCount
        };
      } catch (error: any) {
        logger.warn(`Error getting member count for team ${team.id}: ${error.message}`);
        return {
          ...team,
          membersCount: 0
        };
      }
    }));

    return {
      success: true,
      teams: enhancedTeams as unknown as Team[],
      total
    };
  } catch (error: any) {
    logger.error(`Error getting company teams: ${error.message}`);
    return {
      success: false,
      message: `Failed to get company teams: ${error.message}`
    };
  }
}

/**
 * ✅ DUPLICATE CLEANUP: Duplicate team member kayıtlarını temizle
 * @param teamId Takım ID'si (opsiyonel - belirtilmezse tüm takımları kontrol eder)
 * @returns Temizlik sonucu
 */
export async function cleanupDuplicateTeamMembers(teamId?: string): Promise<{
  success: boolean;
  message?: string;
  removedCount?: number;
  details?: any[];
}> {
  try {
    if (!isMongoDBInitialized() || !db || !teamMembersCollection || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Query oluştur
    const query: any = {};
    if (teamId) {
      query.team_id = teamId;
    }

    // Tüm team member kayıtlarını al
    const allMembers = await teamMembersCollection.find(query).toArray();
    
    const duplicatesToRemove: any[] = [];
    const seenUserTeamPairs = new Map<string, any>();

    for (const member of allMembers) {
      const key = `${member.user_id}-${member.team_id}`;
      
      if (seenUserTeamPairs.has(key)) {
        // Duplicate bulundu
        const existingMember = seenUserTeamPairs.get(key);
        
        // Kullanıcı bilgisi olan kaydı tut, null olanı sil
        const memberUser = await usersCollection.findOne({ id: member.user_id });
        const existingUser = await usersCollection.findOne({ id: existingMember.user_id });
        
        let memberToRemove;
        
        if (!memberUser && existingUser) {
          // Mevcut kaydın kullanıcısı yok, yeni kaydın kullanıcısı var - mevcut kaydı sil
          memberToRemove = member;
        } else if (memberUser && !existingUser) {
          // Yeni kaydın kullanıcısı yok, mevcut kaydın kullanıcısı var - yeni kaydı sil
          memberToRemove = existingMember;
          seenUserTeamPairs.set(key, member); // Güncel kaydı sakla
        } else if (!memberUser && !existingUser) {
          // İkisi de kullanıcı bilgisi yok - daha yeni olanı tut
          if (new Date(member.added_at) > new Date(existingMember.added_at)) {
            memberToRemove = existingMember;
            seenUserTeamPairs.set(key, member);
          } else {
            memberToRemove = member;
          }
        } else {
          // İkisinde de kullanıcı var - daha yeni olanı tut (added_by='system' olanı tercih et)
          if (member.added_by === 'system' && existingMember.added_by !== 'system') {
            memberToRemove = existingMember;
            seenUserTeamPairs.set(key, member);
          } else if (existingMember.added_by === 'system' && member.added_by !== 'system') {
            memberToRemove = member;
          } else {
            // Her ikisi de aynı tipte - daha yeni olanı tut
            if (new Date(member.added_at) > new Date(existingMember.added_at)) {
              memberToRemove = existingMember;
              seenUserTeamPairs.set(key, member);
            } else {
              memberToRemove = member;
            }
          }
        }
        
        if (memberToRemove) {
          duplicatesToRemove.push({
            id: memberToRemove.id,
            user_id: memberToRemove.user_id,
            team_id: memberToRemove.team_id,
            added_by: memberToRemove.added_by,
            added_at: memberToRemove.added_at,
            hasUser: memberToRemove.user_id ? (await usersCollection.findOne({ id: memberToRemove.user_id })) !== null : false
          });
        }
      } else {
        seenUserTeamPairs.set(key, member);
      }
    }

    // Duplicate kayıtları sil
    if (duplicatesToRemove.length > 0) {
      const idsToRemove = duplicatesToRemove.map(d => d.id);
      const deleteResult = await teamMembersCollection.deleteMany({
        id: { $in: idsToRemove }
      });

      logger.info(`[CLEANUP] Removed ${deleteResult.deletedCount} duplicate team member records from ${teamId ? `team ${teamId}` : 'all teams'}`);

      return {
        success: true,
        message: `Removed ${deleteResult.deletedCount} duplicate team member records`,
        removedCount: deleteResult.deletedCount,
        details: duplicatesToRemove
      };
    } else {
      return {
        success: true,
        message: 'No duplicate team member records found',
        removedCount: 0,
        details: []
      };
    }
  } catch (error: any) {
    logger.error(`Error cleaning up duplicate team members: ${error.message}`);
    return {
      success: false,
      message: `Failed to cleanup duplicates: ${error.message}`
    };
  }
}