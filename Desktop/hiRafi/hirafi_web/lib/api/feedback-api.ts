/**
 * Feedback API
 *
 * Geri bildirim ile ilgili API isteklerini yönetir.
 */

import { get, post } from './fetch-wrapper';
import { FEEDBACK_ENDPOINTS } from './constants';

/**
 * Geri bildirim gönderir
 *
 * @param feedbackData Geri bildirim verileri
 */
export async function sendFeedback(feedbackData: {
  feedback: string;
  subject: string;
  email: string;
  screenshot: string | null;
  userId?: string;
}) {
  return post(FEEDBACK_ENDPOINTS.SUBMIT, feedbackData, {
    showSuccessToast: true,
    successMessage: 'Geri bildiriminiz için teşekkürler!'
  });
}

/**
 * Tüm geri bildirimleri getirir (sadece admin kullanıcılar için)
 */
export async function getFeedbacks() {
  return get(FEEDBACK_ENDPOINTS.GET_ALL);
}
