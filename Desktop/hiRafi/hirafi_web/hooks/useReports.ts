import { useState, useEffect } from 'react'
import { fetchWithAuth } from "@/lib/api/auth"
import { useAuth } from "@/lib/api/auth"
import { API_BASE_URL } from "@/lib/api/constants"
import { toast } from "sonner"

// Test report interface
export interface Report {
  id: string
  name: string
  date: string
  status: string
  result: string
  duration: string
  passRate: number
  totalTests: number
  passed: number
  failed: number
  skipped?: number
  environment: string
  triggeredBy: string
  // Raw values for formatting in UI
  createdAt?: string
  rawDate?: string
  rawDuration?: number
  browser?: string
  device?: string
  [key: string]: any
}

interface UseReportsOptions {
  limit?: number
  autoFetch?: boolean
}

export function useReports(options: UseReportsOptions = {}) {
  const { isAuthenticated } = useAuth()
  const [reports, setReports] = useState<Report[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isError, setIsError] = useState(false)
  const [error, setError] = useState<string | undefined>()
  const [total, setTotal] = useState(0)

  const fetchReports = async () => {
    if (!isAuthenticated) return

    setIsLoading(true)
    setIsError(false)
    setError(undefined)

    try {
      // Create query parameters
      const params = new URLSearchParams()
      if (options.limit) params.append("limit", options.limit.toString())

      // Fetch with authentication token
      const response = await fetchWithAuth(
        `${API_BASE_URL}/reports/light?${params.toString()}`
      )

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Error loading reports")
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || "Couldn't retrieve reports")
      }

      // Format reports for UI - check for data wrapper
      const reports = data.data?.reports || data.reports || [];
      const formattedReports = reports.map((report: any) => {
        if (!report) return null;

        try {
          // Store original date for proper formatting in UI
          const dateValue = report.createdAt || report.date;

          // Format display date
          let formattedDate = 'N/A';
          try {
            if (dateValue) {
              const reportDate = new Date(dateValue);

              // Validate the date
              if (isNaN(reportDate.getTime())) {
                console.warn('Invalid date value received:', dateValue);
                formattedDate = 'Invalid date';
              } else {
                formattedDate = reportDate.toLocaleDateString('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric'
                });
              }
            }
          } catch (e) {
            console.error('Date formatting error:', e, 'Input:', dateValue);
          }

          // Determine test status
          let status = 'completed';
          let result = 'passed';

          const reportStatus = typeof report.status === 'string' ? report.status.toLowerCase() : '';

          if (reportStatus === 'running' || reportStatus === 'pending') {
            status = 'running';
            result = 'pending';
          } else if (reportStatus === 'scheduled') {
            status = 'scheduled';
            result = 'pending';
          } else if (
            reportStatus === 'failed' ||
            reportStatus === 'error' ||
            reportStatus === 'failure'
          ) {
            result = 'failed';
          } else if (reportStatus === 'warning') {
            result = 'warning';
          }

          // Store original duration value
          let rawDuration = undefined;
          if (typeof report.duration === 'number') {
            rawDuration = report.duration;
          } else if (typeof report.duration === 'string' && !isNaN(parseFloat(report.duration))) {
            rawDuration = parseFloat(report.duration);
          } else if (typeof report.executionTime === 'number') {
            rawDuration = report.executionTime;
          } else if (typeof report.executionTime === 'string' && !isNaN(parseFloat(report.executionTime))) {
            rawDuration = parseFloat(report.executionTime);
          }

          // Format duration for display
          let duration = 'N/A';
          if (rawDuration !== undefined) {
            // Convert to seconds if in milliseconds (greater than 1000)
            const durInSeconds = rawDuration > 1000 ? rawDuration / 1000 : rawDuration;
            const minutes = Math.floor(durInSeconds / 60);
            const seconds = Math.round(durInSeconds % 60);
            duration = minutes > 0 ? `${minutes}m ${seconds}s` : `${durInSeconds.toFixed(2)}s`;
          } else if (report.duration !== undefined) {
            duration = String(report.duration);
          } else if (report.executionTime !== undefined) {
            duration = String(report.executionTime);
          }

          // Check summary data
          let totalTests = 0;
          let passed = 0;
          let failed = 0;
          let skipped = 0;

          // Get data from summary object
          if (report.summary && typeof report.summary === 'object') {
            totalTests = typeof report.summary.total === 'number' ? report.summary.total : 0;
            passed = typeof report.summary.passed === 'number' ? report.summary.passed : 0;
            failed = typeof report.summary.failed === 'number' ? report.summary.failed : 0;
            skipped = typeof report.summary.skipped === 'number' ? report.summary.skipped : 0;

            // Add errors to failed count if available
            if (typeof report.summary.errors === 'number') {
              failed += report.summary.errors;
            }
          } else {
            // If no summary, check direct fields
            totalTests = typeof report.totalTests === 'number' ? report.totalTests : 0;
            passed = typeof report.passed === 'number' ? report.passed : 0;
            failed = typeof report.failed === 'number' ? report.failed : 0;
            skipped = typeof report.skipped === 'number' ? report.skipped : 0;

            // Add errors to failed count if available
            if (typeof report.errors === 'number') {
              failed += report.errors;
            }
          }

          // Assume at least 1 test if no count is provided
          if (totalTests === 0) {
            if (passed > 0 || failed > 0) {
              totalTests = passed + failed;
            } else {
              totalTests = 1;
              // Set pass/fail based on status
              if (result === 'passed') {
                passed = 1;
              } else if (result === 'failed') {
                failed = 1;
              }
            }
          }

          // Calculate pass rate
          const passRate = totalTests > 0 ? Math.round((passed / totalTests) * 100) : 0;

          // Get browser info
          const browser = report.browser || report.browserName || 'N/A';

          // Create report object with safe values
          return {
            id: (report.id || report._id).toString(),
            name: typeof report.name === 'string' ? report.name :
                 (typeof report.scenarioName === 'string' ? report.scenarioName :
                 (typeof report.scenario?.name === 'string' ? report.scenario.name : 'Unnamed Test')),
            date: formattedDate,
            status,
            result,
            duration,
            passRate,
            totalTests,
            passed,
            failed,
            skipped: skipped || 0,
            environment: typeof report.environment === 'string' ? report.environment :
                        (typeof report.environment === 'object' && report.environment !== null ? 'Custom' : 'Production'),
            triggeredBy: typeof report.user?.name === 'string' ? report.user.name :
                        (typeof report.userName === 'string' ? report.userName : 'System'),
            // Store raw values for UI formatting
            createdAt: dateValue,
            rawDate: dateValue,
            rawDuration,
            browser,
            device: report.device || 'N/A'
          };
        } catch (err) {
          console.error('Report formatting error:', err);
          // Return minimum valid report object in error case
          return {
            id: (report.id || report._id || Date.now()).toString(),
            name: 'Error formatting report',
            date: new Date().toLocaleDateString(),
            status: 'completed',
            result: 'failed',
            duration: 'N/A',
            passRate: 0,
            totalTests: 0,
            passed: 0,
            failed: 0,
            environment: 'Unknown',
            triggeredBy: 'System'
          };
        }
      }).filter(Boolean); // Filter out null values

      setReports(formattedReports)
      setTotal(data.count || 0)
    } catch (err: any) {
      setIsError(true)
      setError(err.message)
      toast.error(`Failed to load reports: ${err.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on component mount (if autoFetch is true)
  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchReports()
    }
  }, [])

  return {
    reports,
    isLoading,
    isError,
    error,
    total,
    refetch: fetchReports
  }
}