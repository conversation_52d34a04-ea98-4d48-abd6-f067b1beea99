/**
 * Testinium Device Manager
 * Manages Android devices using Testinium via DevicePark SDK with DI compliance
 */

import {
  AndroidAgent,
  AppiumBaseCapabilities
} from './device-manager.interface.js';
import { BaseDeviceManager } from './base-device-manager.js';
import { TestRequest } from '../../../models/types.js';
import { ILoggerService } from '../../../utils/logger-service.js';
import { DeviceManagerUtils, DeviceErrorType, DEFAULT_RETRY_CONFIG, TestQueueManager, RetryResult } from './shared/device-manager-utils.js';

// Import DevicePark SDK
import { DeviceParkSDK } from '@devicepark/sdk';
import { DeviceStartSessionRequest } from '@devicepark/sdk';
import { DeviceAllocationRequest } from '@devicepark/sdk';

// Import from misoai-android package for agent creation
import * as androidModule from 'rfi-ai-android';

// Extract the agentFromAppiumServer function for Testinium connections
const agentFromAppiumServer = androidModule.agentFromAppiumServer;

/**
 * Testinium configuration interface
 */
export interface TestiniumConfig {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  issuerUri: string;
}

/**
 * Testinium Device Manager
 * Manages Android devices using Testinium via DevicePark SDK with DI compliance
 */
export class TestiniumDeviceManager extends BaseDeviceManager {
  private testiniumConfig: TestiniumConfig;
  private capabilities: AppiumBaseCapabilities;
  private sdk: any;
  private currentSession: any = null;
  private logger: ILoggerService;
  private utils: DeviceManagerUtils;

  constructor(
    testiniumConfig: TestiniumConfig,
    capabilities: AppiumBaseCapabilities,
    logger: ILoggerService
  ) {
    super();
    this.testiniumConfig = testiniumConfig;
    this.capabilities = capabilities;
    this.logger = logger;
    this.utils = new DeviceManagerUtils(logger);
  }

  /**
   * Initialize the Testinium SDK
   */
  async initialize(config: any): Promise<void> {
    await super.initialize(config);
    
    try {
      // Initialize DevicePark SDK
      this.sdk = DeviceParkSDK.init({
        apiUrl: this.testiniumConfig.apiUrl,
        authentication: {
          clientId: this.testiniumConfig.clientId,
          clientSecret: this.testiniumConfig.clientSecret,
          issuerUri: this.testiniumConfig.issuerUri
        }
      });

      this.logger.info('TestiniumDeviceManager: SDK initialized successfully');
    } catch (error: any) {
      this.logger.error(`TestiniumDeviceManager: Failed to initialize SDK: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if a device is available using DevicePark Management API
   * Per DevicePark documentation: Only devices with isAvailable=true can be allocated
   * @param deviceSerial Device serial ID
   * @returns Promise resolving to true if device is available, false otherwise
   */
  async checkDeviceAvailability(deviceSerial: string): Promise<boolean> {
    try {
      if (!this.sdk) {
        throw new Error('TestiniumCredentialsMissing');
      }

      this.logger.info(`TestiniumDeviceManager: Checking device availability for ${deviceSerial} using Management API`);

      // Get device list from DevicePark Management API
      const devices = await this.sdk.management.getAllDevices();
      
      if (!devices || !Array.isArray(devices)) {
        this.logger.error('TestiniumDeviceManager: Invalid device list received from Management API');
        throw new Error('Invalid device list received from DevicePark Management API');
      }

             // Log all available device serials for debugging
       if (devices && Array.isArray(devices)) {
         const deviceSerials = devices.map((d: any, index: number) => {
           return `[${index}] serial: "${d.serial}" (${typeof d.serial}), isAvailable: ${d.isAvailable}`;
         });
         this.logger.info(`TestiniumDeviceManager: Available devices for availability check: ${deviceSerials.join(', ')}`);
       }

       // Find the device by serial number with multiple matching strategies
       const device = devices.find((d: any) => {
         const dSerial = d.serial;
         // Try exact match, string conversion, and trimmed versions
         return dSerial === deviceSerial || 
                String(dSerial) === String(deviceSerial) ||
                String(dSerial).trim() === String(deviceSerial).trim() ||
                dSerial == deviceSerial; // Loose equality for type coercion
       });

      if (!device) {
         this.logger.warn(`TestiniumDeviceManager: Device with serial "${deviceSerial}" (${typeof deviceSerial}) not found in ${devices?.length || 0} available devices`);
        return false;
      }

       // Check isAvailable flag per DevicePark documentation
       const isAvailable = device.isAvailable === true;
       this.logger.info(`TestiniumDeviceManager: Device ${deviceSerial} availability status: isAvailable=${device.isAvailable}, marketName=${device.marketName || 'Unknown'}, platform=${device.platform || 'Unknown'}`);
       
       return isAvailable;
    } catch (error: any) {
      // Provide more context for different error types
      let errorMessage = error.message || 'Unknown error';
      let errorContext = '';

      if (errorMessage.includes('authentication') || errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
        errorContext = 'Authentication failed - please check Testinium credentials';
      } else if (errorMessage.includes('network') || errorMessage.includes('timeout') || errorMessage.includes('ENOTFOUND')) {
        errorContext = 'Network connection failed - please check API URL and connectivity';
      } else if (errorMessage.includes('403')) {
        errorContext = 'Access forbidden - please check API permissions';
      } else if (errorMessage.includes('500')) {
        errorContext = 'DevicePark server error - please try again later';
      }

      const fullErrorMessage = errorContext ? `${errorMessage} (${errorContext})` : errorMessage;
      this.logger.error(`TestiniumDeviceManager: Error checking device availability: ${fullErrorMessage}`);
      
      // Re-throw the error with additional context
      throw new Error(fullErrorMessage);
    }
  }

  /**
   * Connect to a device with retry logic and proper DevicePark SDK integration
   * @param deviceId Optional device identifier (can be device ID or serial)
   * @param scenarioId Optional scenario identifier for reporting
   * @param platformVersion Optional platform version to override default
   * @param appId Optional app ID for Testinium app storage
   * @param testiniumConfig Optional Testinium config for server hostname extraction
   * @param queueManager Optional queue manager for requeueing tests on device busy
   * @returns Promise resolving to a device agent
   */
  async connectToDevice(deviceId?: string, scenarioId?: string, platformVersion?: string, appId?: string, testiniumConfig?: any, queueManager?: TestQueueManager): Promise<AndroidAgent> {
    // Use enhanced retry logic for device connections
    const retryResult = await this.utils.executeDeviceConnectionWithRetry(
      async () => {
        if (!this.sdk) {
          throw this.utils.createError(DeviceErrorType.ALLOCATION_FAILED, 'Testinium SDK not initialized');
        }

        // Step 1: Determine device serial for allocation
        let actualDeviceSerial = deviceId;
        
        if (deviceId && deviceId !== 'auto' && deviceId !== '' && deviceId !== null && deviceId !== undefined) {
          // Check if deviceId looks like a serial (contains letters and numbers, longer than 5 chars)
          const looksLikeSerial = /^[A-Za-z0-9]{6,}$/.test(deviceId);
          
          if (looksLikeSerial) {
            // Directly use the serial for allocation - no need to fetch all devices
            actualDeviceSerial = deviceId;
            this.logger.info(`TestiniumDeviceManager: Using device identifier "${deviceId}" directly as serial for allocation`);
          } else {
            // Looks like a device ID, need to resolve to serial
            this.logger.info(`TestiniumDeviceManager: Resolving device ID "${deviceId}" to actual device serial`);
            const devices = await this.sdk.management.getAllDevices();
            
            this.logger.info(`TestiniumDeviceManager: Retrieved ${devices?.length || 0} devices from DevicePark API`);
            
            // Log all available devices for debugging
            if (devices && Array.isArray(devices)) {
              const deviceInfo = devices.map((d: any, index: number) => {
                return `[${index}] id: ${d.id}, serial: "${d.serial}", isAvailable: ${d.isAvailable}, marketName: "${d.marketName || 'N/A'}"`;
              });
              this.logger.info(`TestiniumDeviceManager: Available devices: ${deviceInfo.join(', ')}`);
            }
            
            // Find device by ID (numeric ID from UI)
            const device = devices.find((d: any) => {
              return d.id == deviceId || String(d.id) === String(deviceId);
            });
            
            if (!device) {
              this.logger.error(`TestiniumDeviceManager: Device with ID "${deviceId}" not found in ${devices?.length || 0} available devices`);
              throw this.utils.createError(DeviceErrorType.DEVICE_UNAVAILABLE, `Device with ID ${deviceId} not found in available devices`);
            }
            
            // Use the actual serial from the device for allocation
            actualDeviceSerial = device.serial;
            this.logger.info(`TestiniumDeviceManager: Resolved device ID "${deviceId}" to serial "${actualDeviceSerial}" - marketName: "${device.marketName || 'N/A'}", isAvailable: ${device.isAvailable}`);
            
            // Log availability status but don't block allocation - let DevicePark API handle it
            if (!device.isAvailable) {
              this.logger.warn(`TestiniumDeviceManager: Device ${deviceId} (${actualDeviceSerial}) shows isAvailable=${device.isAvailable}, but attempting allocation anyway as DevicePark API will provide definitive response`);
            } else {
              this.logger.info(`TestiniumDeviceManager: Device ${deviceId} (${actualDeviceSerial}) is available for allocation`);
            }
          }
        }

        // Step 2: Create allocation request with actual device serial per DevicePark API documentation
        const allocationRequest: DeviceAllocationRequest = {
          serial: actualDeviceSerial, // Use resolved device serial for allocation
        };

        this.logger.info(`TestiniumDeviceManager: [ALLOCATION-START] Creating allocation for device serial "${actualDeviceSerial}" (original ID: "${deviceId}")`);
        this.logger.info(`TestiniumDeviceManager: [ALLOCATION-START] Allocation request: ${JSON.stringify(allocationRequest)}`);
        this.logger.info(`TestiniumDeviceManager: [ALLOCATION-START] About to call DevicePark SDK allocation.createAllocation()`);

        // Step 3: Allocate device with serial ID - let DevicePark API provide definitive availability
        let allocation;
        try {
          this.logger.info(`TestiniumDeviceManager: [ALLOCATION-CALL] Calling SDK allocation.createAllocation() now...`);
          allocation = await this.sdk.allocation.createAllocation(allocationRequest);
          this.logger.info(`TestiniumDeviceManager: [ALLOCATION-SUCCESS] Allocation completed successfully: ${JSON.stringify(allocation)}`);
        } catch (allocationError: any) {
          // Enhanced error handling for DevicePark allocation errors
          const errorMessage = allocationError.message || 'Unknown allocation error';
          this.logger.error(`TestiniumDeviceManager: [ALLOCATION-ERROR] Allocation failed for device ${actualDeviceSerial}: ${errorMessage}`);
          this.logger.error(`TestiniumDeviceManager: [ALLOCATION-ERROR] Full error details: ${JSON.stringify(allocationError, null, 2)}`);
          
          // Map DevicePark API errors to our error types
          if (errorMessage.includes('already allocated') || 
              errorMessage.includes('device not available') ||
              errorMessage.includes('device busy') ||
              errorMessage.includes('in use')) {
            throw this.utils.createError(DeviceErrorType.DEVICE_BUSY, `Device ${actualDeviceSerial} is currently in use or busy (DevicePark: ${errorMessage})`);
          } else if (errorMessage.includes('not found') || errorMessage.includes('invalid device')) {
            throw this.utils.createError(DeviceErrorType.DEVICE_UNAVAILABLE, `Device ${actualDeviceSerial} not found or invalid (DevicePark: ${errorMessage})`);
          } else if (errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
            throw this.utils.createError(DeviceErrorType.CREDENTIALS_MISSING, `Authorization failed for DevicePark (${errorMessage})`);
          } else {
            throw this.utils.createError(DeviceErrorType.ALLOCATION_FAILED, `DevicePark allocation failed: ${errorMessage}`);
          }
        }

        if (!allocation || !allocation.allocationId) {
          throw this.utils.createError(DeviceErrorType.ALLOCATION_FAILED, 'DevicePark returned invalid allocation response');
        }

        this.logger.info(`TestiniumDeviceManager: Device allocated successfully. AllocationId: ${allocation.allocationId}, DeviceSerial: ${allocation.deviceSerial}, ExpiresAt: ${allocation.expiresAt}`);

        // Step 4: Start session with allocation ID per DevicePark API documentation
        const sessionRequest: DeviceStartSessionRequest = {
          allocationId: allocation.allocationId, // Use the returned allocationId
          userId: 1, // Default user ID - should be configurable
          userEmail: '<EMAIL>', // Default email - should be configurable  
          companyId: 1, // Default company ID - should be configurable
          companyName: 'Hirafi' // Default company name - should be configurable
        };

        // Step 5: Start session with allocation ID
        const session = await this.sdk.session.startSession(sessionRequest);

        if (!session || !session.sessionId) {
          throw this.utils.createError(DeviceErrorType.SESSION_FAILED, 'Failed to start session with Testinium device');
        }

        // Store session with allocation info for cleanup
        this.currentSession = {
          ...session,
          allocationId: allocation.allocationId,
          deviceSerial: allocation.deviceSerial
        };
        this.logger.info(`TestiniumDeviceManager: Session started successfully: ${session.sessionId}`);

        // Step 6: Create Appium capabilities with W3C WebDriver compliance
        const appiumCapabilities = {
          platformName: 'Android' as const,
          'appium:udid': allocation.deviceSerial || actualDeviceSerial, // Use W3C compliant udid
          'appium:automationName': 'UiAutomator2', // Use W3C compliant automationName
          // Auto-accept alerts and permissions for Android (W3C compliant)
          'appium:autoAcceptAlerts': true,
          'appium:autoGrantPermissions': true,
          ...(appId && { 'appium:app': `dp://${appId}` }), // Use W3C compliant app capability
          ...(platformVersion && { 'appium:platformVersion': platformVersion }), // Use W3C compliant platformVersion
          // DevicePark specific options - pass session ID for connection
          'dp:options': {
            sessionId: session.sessionId,
            appiumVersion: '2.5.4' // Default Appium version
          }
        };

        // Log the session details for debugging
        this.logger.info(`TestiniumDeviceManager: Session Details - SessionId: ${session.sessionId}, AllocationId: ${allocation.allocationId}, DeviceSerial: ${allocation.deviceSerial}`);

        // Step 7: Create Appium Gateway server configuration per DevicePark documentation
        let appiumGatewayUrl = 'http://localhost:4723/wd/hub'; // Default fallback
        
        // Extract hostname from testinium config API URL and construct Appium Gateway URL
        if (testiniumConfig && testiniumConfig.apiUrl) {
          try {
            const url = new URL(testiniumConfig.apiUrl);
            const hostname = url.hostname;
            const protocol = url.protocol; // http: or https:
            
            // Construct DevicePark Appium Gateway URL
            // For dev: https://dev-devicepark.testinium.io -> https://dev-devicepark-appium-gw-service.testinium.io/wd/hub
            // For prod: https://devicepark.testinium.io -> https://devicepark-appium-gw-service.testinium.io/wd/hub
            const gatewayHostname = hostname.replace('devicepark.testinium.io', 'devicepark-appium-gw-service.testinium.io')
                                           .replace('dev-devicepark.testinium.io', 'dev-devicepark-appium-gw-service.testinium.io');
            
            appiumGatewayUrl = `${protocol}//${gatewayHostname}/wd/hub`;
            this.logger.info(`TestiniumDeviceManager: Using DevicePark Appium Gateway: ${appiumGatewayUrl}`);
          } catch (urlError: any) {
            this.logger.warn(`TestiniumDeviceManager: Could not parse API URL ${testiniumConfig.apiUrl}: ${urlError.message}, using fallback`);
            appiumGatewayUrl = session.appiumHost ? `http://${session.appiumHost}:${session.appiumPort || 4723}/wd/hub` : appiumGatewayUrl;
          }
        } else {
          // Fallback to session provided server info
          appiumGatewayUrl = session.appiumHost ? `http://${session.appiumHost}:${session.appiumPort || 4723}/wd/hub` : appiumGatewayUrl;
        }

        // Parse the gateway URL for agentFromAppiumServer
        const gatewayUrl = new URL(appiumGatewayUrl);
        const protocol = gatewayUrl.protocol === 'https:' ? 'https' as const : 'http' as const;
        const serverConfig = {
          hostname: gatewayUrl.hostname,
          port: parseInt(gatewayUrl.port) || (protocol === 'https' ? 443 : 80),
          path: gatewayUrl.pathname,
          protocol: protocol
        };

        this.logger.info(`TestiniumDeviceManager: Connecting to DevicePark Appium Gateway at ${serverConfig.protocol}://${serverConfig.hostname}:${serverConfig.port}${serverConfig.path}`);

        // Step 8: Create agent using misoai-android with DevicePark Appium Gateway
        // Using agentFromAppiumServer to connect to remote DevicePark gateway
        const agent = await agentFromAppiumServer(serverConfig, appiumCapabilities);

        // Step 9: Store agent reference with proper device key
        const deviceKey = allocation.deviceSerial || actualDeviceSerial || 'default';
        this.registerAgent(deviceKey, agent);

        this.logger.info(`TestiniumDeviceManager: Successfully connected to device ${deviceKey} (original ID: ${deviceId}) with session ${session.sessionId} and allocation ${allocation.allocationId}`);
        return agent;
      },
      scenarioId, // testId parameter
      queueManager, // queueManager parameter
      `TestiniumDeviceManager.connectToDevice(${deviceId || 'auto'})`
    );

    // Handle retry result
    if (retryResult.success && retryResult.result) {
      return retryResult.result;
    } else if (retryResult.requeued) {
      throw new Error(`Test ${scenarioId} was requeued due to device busy after ${retryResult.attemptsUsed} attempts`);
    } else {
      throw retryResult.error || new Error('Device connection failed after all retry attempts');
    }
  }

  /**
   * Connect to an Appium hub using test request configuration
   * @param testRequest Test request containing Testinium configuration
   * @returns Promise resolving to a device agent
   */
  async connectToAppiumHub(testRequest: TestRequest): Promise<AndroidAgent> {
    try {
      const deviceInfo = this.utils.extractDeviceInfo(testRequest, 'testinium');

      // Extract Testinium config from test request for server hostname
      const androidSettings = testRequest.environmentSettings as any;
      const testiniumConfig = androidSettings?.testinium;
      
      if (!testiniumConfig || !testiniumConfig.apiUrl) {
        throw new Error('Testinium configuration or API URL not found in test request');
      }

      // Prefer device serial from test request if available, otherwise use device ID
      const deviceIdentifier = deviceInfo.deviceSerial || deviceInfo.deviceId;
      this.logger.info(`TestiniumDeviceManager: Using device identifier from test request - serial: ${deviceInfo.deviceSerial}, id: ${deviceInfo.deviceId}, final: ${deviceIdentifier}`);

      // Connect to device using the Testinium API with platform version, app ID, and config
      // Note: queueManager should be passed from higher level if available for automatic requeueing
      return this.connectToDevice(deviceIdentifier, testRequest.scenarioId, deviceInfo.osVersion, deviceInfo.appId, testiniumConfig);
    } catch (error: any) {
      this.logger.error(`TestiniumDeviceManager: Error connecting to Testinium: ${error.message}`);
      throw error;
    }
  }

  /**
   * Disconnect from a device
   * @param device Device to disconnect from
   */
  async disconnectFromDevice(device: AndroidAgent): Promise<void> {
    // Step 1: Close current session if exists
    if (this.currentSession && this.sdk) {
      try {
        await this.sdk.session.closeSession(this.currentSession.sessionId);
        this.logger.info(`TestiniumDeviceManager: Session closed: ${this.currentSession.sessionId}`);
        
        // Step 2: Release allocation if we have allocationId
        if (this.currentSession.allocationId) {
          try {
            await this.sdk.allocation.releaseAllocation(this.currentSession.allocationId);
            this.logger.info(`TestiniumDeviceManager: Allocation released: ${this.currentSession.allocationId}`);
          } catch (allocationError: any) {
            this.logger.warn(`TestiniumDeviceManager: Error releasing allocation: ${allocationError.message}`);
          }
        }
      } catch (sessionError: any) {
        this.logger.warn(`TestiniumDeviceManager: Error closing session: ${sessionError.message}`);
      }
      this.currentSession = null;
    }

    // Step 3: Use shared disconnection logic
    const deviceKey = this.getKeyForDevice(device);
    await this.utils.performCommonDisconnection(
      device,
      deviceKey,
      (key: string) => this.unregisterAgent(key),
      'Testinium'
    );
  }

  /**
   * Get current Testinium session ID
   * @returns Current session ID or null if no session is active
   */
  getCurrentSessionId(): string | null {
    return this.currentSession?.sessionId || null;
  }

  /**
   * Get current Testinium session info
   * @returns Current session info or null if no session is active
   */
  getCurrentSessionInfo(): any | null {
    return this.currentSession || null;
  }

  /**
   * Create an AI agent for a device
   * @param device Device agent to create AI agent for
   * @returns Promise resolving to an Android agent
   */
  async createAgent(device: AndroidAgent): Promise<AndroidAgent> {
    // For Testinium, the device is already an agent
    return device;
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    // Use shared cleanup logic
    await this.utils.performCommonCleanup(
      this.agents,
      (device: AndroidAgent) => this.disconnectFromDevice(device),
      'Testinium'
    );

    // Clear current device reference
    this.currentDevice = null;
  }
}
