/**
 * Connector Manager Factory
 * 
 * Factory for creating and configuring connector managers with proper
 * dependency injection and service management.
 */

import { logger } from '../../../utils/logger.js';
import { ConnectorServiceRegistry } from '../registry/connector-service-registry.js';
import { IConnectorManager, IConnectorManagerConfig } from '../interfaces/connector-manager.interface.js';
import { IConnectorFactoryConfig } from '../interfaces/connector-factory.interface.js';

/**
 * Connector Manager Factory
 * 
 * Creates connector managers with proper dependency injection
 * and service registry management.
 */
export class ConnectorManagerFactory {
  private static serviceRegistries: Map<string, ConnectorServiceRegistry> = new Map();
  private static connectorManagers: Map<string, IConnectorManager> = new Map();

  /**
   * Create a connector manager with all dependencies
   */
  static async createConnectorManager(config: IConnectorFactoryConfig): Promise<IConnectorManager> {
    const managerId = config.nodeId || 'default';

    logger.info(`ConnectorManagerFactory: Creating connector manager for node ${managerId}`);

    try {
      // Create service registry for this manager
      const serviceRegistry = new ConnectorServiceRegistry({
        nodeId: managerId,
        enableLogging: config.enableLogging || false,
        enableCircularDependencyDetection: config.enableCircularDependencyDetection !== false,
        hubUrl: config.hubUrl,
        secretKey: config.secretKey
      });

      // Initialize the service registry
      await serviceRegistry.initialize();

      // Register external services if provided
      if (config.nodeId) {
        serviceRegistry.registerExternalService('nodeId', config.nodeId);
      }
      
      if (config.clientId) {
        serviceRegistry.registerExternalService('clientId', config.clientId);
      }

      // Get the connector manager from the registry
      const connectorManager = await serviceRegistry.getConnectorManager();

      // Initialize the connector manager
      await connectorManager.initialize({
        enableLogging: config.enableLogging || false,
        defaultConnectorType: config.defaultConnectorType,
        hubUrl: config.hubUrl,
        secretKey: config.secretKey,
        nodeId: config.nodeId,
        clientId: config.clientId
      });

      // Store references for management
      ConnectorManagerFactory.serviceRegistries.set(managerId, serviceRegistry);
      ConnectorManagerFactory.connectorManagers.set(managerId, connectorManager);

      logger.info(`ConnectorManagerFactory: Successfully created connector manager for node ${managerId}`);
      return connectorManager;

    } catch (error: any) {
      logger.error(`ConnectorManagerFactory: Failed to create connector manager for node ${managerId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get an existing connector manager by ID
   */
  static getConnectorManager(managerId: string): IConnectorManager | null {
    return ConnectorManagerFactory.connectorManagers.get(managerId) || null;
  }

  /**
   * Dispose a connector manager and its services
   */
  static async disposeConnectorManager(managerId: string): Promise<void> {
    logger.info(`ConnectorManagerFactory: Disposing connector manager for node ${managerId}`);

    try {
      // Get and stop the connector manager
      const connectorManager = ConnectorManagerFactory.connectorManagers.get(managerId);
      if (connectorManager) {
        await connectorManager.dispose();
        ConnectorManagerFactory.connectorManagers.delete(managerId);
      }

      // Dispose the service registry
      const serviceRegistry = ConnectorManagerFactory.serviceRegistries.get(managerId);
      if (serviceRegistry) {
        await serviceRegistry.dispose();
        ConnectorManagerFactory.serviceRegistries.delete(managerId);
      }

      logger.info(`ConnectorManagerFactory: Successfully disposed connector manager for node ${managerId}`);

    } catch (error: any) {
      logger.error(`ConnectorManagerFactory: Error disposing connector manager for node ${managerId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Dispose all connector managers
   */
  static async disposeAll(): Promise<void> {
    logger.info('ConnectorManagerFactory: Disposing all connector managers');

    const managerIds = Array.from(ConnectorManagerFactory.connectorManagers.keys());
    
    for (const managerId of managerIds) {
      try {
        await ConnectorManagerFactory.disposeConnectorManager(managerId);
      } catch (error: any) {
        logger.error(`ConnectorManagerFactory: Error disposing connector manager ${managerId}: ${error.message}`);
      }
    }

    logger.info('ConnectorManagerFactory: All connector managers disposed');
  }

  /**
   * Get all active connector manager IDs
   */
  static getActiveManagerIds(): string[] {
    return Array.from(ConnectorManagerFactory.connectorManagers.keys());
  }

  /**
   * Check if a connector manager exists
   */
  static hasConnectorManager(managerId: string): boolean {
    return ConnectorManagerFactory.connectorManagers.has(managerId);
  }

  /**
   * Create a connector manager with all dependencies (instance method)
   */
  async createConnectorManager(config: IConnectorFactoryConfig): Promise<IConnectorManager> {
    return ConnectorManagerFactory.createConnectorManager(config);
  }

  /**
   * Get an existing connector manager by ID (instance method)
   */
  getConnectorManager(managerId: string): IConnectorManager | null {
    return ConnectorManagerFactory.getConnectorManager(managerId);
  }

  /**
   * Dispose a connector manager and its services (instance method)
   */
  async disposeConnectorManager(managerId: string): Promise<void> {
    return ConnectorManagerFactory.disposeConnectorManager(managerId);
  }

  /**
   * Dispose all connector managers (instance method)
   */
  async disposeAll(): Promise<void> {
    return ConnectorManagerFactory.disposeAll();
  }

  /**
   * Get all active connector manager IDs (instance method)
   */
  getActiveManagerIds(): string[] {
    return ConnectorManagerFactory.getActiveManagerIds();
  }

  /**
   * Check if a connector manager exists (instance method)
   */
  hasConnectorManager(managerId: string): boolean {
    return ConnectorManagerFactory.hasConnectorManager(managerId);
  }
}

/**
 * Convenience function for creating a connector manager
 * Maintains backward compatibility with existing code
 */
export async function createConnectorManager(
  nodeId?: string,
  config?: any
): Promise<IConnectorManager> {
  return ConnectorManagerFactory.createConnectorManager({
    nodeId,
    ...config,
    enableLogging: false
  });
}
