"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Database, 
  ChevronDown, 
  Search, 
  X, 
  Eye, 
  EyeOff,
  Loader2,
  AlertCircle,
  RefreshCw
} from "lucide-react"
import { useScenarioTestDataStore, useScenarioTestDataSelectors } from "@/store/scenarioTestDataStore"
import type { DataSet } from "@/types/test-data"

interface TestDataSelectorProps {
  className?: string
}

export function TestDataSelector({ className }: TestDataSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [variableSearchQuery, setVariableSearchQuery] = useState("")

  const {
    selectedDataSet,
    availableDataSets,
    variables,
    isLoadingDataSets,
    isLoadingVariables,
    dataSetError,
    searchQuery,
    showVariablePreview,
    loadDataSets,
    selectDataSet,
    refreshSelectedDataSet,
    setSearchQuery,
    setShowVariablePreview,
  } = useScenarioTestDataStore()
  
  const { filteredDataSets, hasSelectedDataSet, hasVariables } = useScenarioTestDataSelectors()

  // Load data sets on first interaction (lazy loading)
  useEffect(() => {
    // Only load if data sets haven't been initialized yet
    // This prevents duplicate calls while still ensuring data is available when needed
    const state = useScenarioTestDataStore.getState()
    if (!state.dataSetsInitialized && !state.isLoadingDataSets) {
      loadDataSets()
    }
  }, []) // Empty dependency array - only run once on mount

  // Handle dropdown open/close (no automatic refresh)
  const handleDropdownOpenChange = (open: boolean) => {
    setIsOpen(open)

    // Only load data if not already initialized (first time opening)
    if (open && !isLoadingDataSets && !isLoadingVariables) {
      const state = useScenarioTestDataStore.getState()
      if (!state.dataSetsInitialized) {
        loadDataSets()
      }
    }
  }

  const handleDataSetSelect = async (dataSet: any) => {
    await selectDataSet(dataSet)
    setIsOpen(false)
    setSearchQuery('')
    setVariableSearchQuery('')
  }

  const handleClearSelection = () => {
    selectDataSet(null)
    setShowVariablePreview(false)
  }

  const toggleVariablePreview = () => {
    const newValue = !showVariablePreview
    setShowVariablePreview(newValue)
  }

  const handleRefresh = async () => {
    // Force refresh available data sets (bypass cache)
    await loadDataSets(true)
    // Also refresh selected dataset variables if any dataset is selected
    await refreshSelectedDataSet()
  }

  return (
    <div className={`space-y-3 ${className}`} style={{ overflow: 'visible' }}>
      {/* Data Set Selector */}
      <div className="flex items-center gap-2">
        <DropdownMenu open={isOpen} onOpenChange={handleDropdownOpenChange}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="flex-1 justify-between h-10 px-3 bg-white dark:bg-gray-950 border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-900"
              disabled={isLoadingDataSets}
            >
              <div className="flex items-center gap-2 min-w-0">
                <Database className="h-4 w-4 text-gray-500 flex-shrink-0" />
                <span className="truncate text-sm">
                  {hasSelectedDataSet ? (
                    selectedDataSet?.name
                  ) : (
                    "Select test data set"
                  )}
                </span>
              </div>
              {isLoadingDataSets ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="p-0 w-[var(--radix-dropdown-menu-trigger-width)]"
            align="start"
          >
            <div className="p-3 border-b border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search data sets..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 h-8 text-sm"
                  />
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isLoadingDataSets || isLoadingVariables}
                  className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  title="Refresh data sets and variables"
                >
                  <RefreshCw className={`h-3.5 w-3.5 ${(isLoadingDataSets || isLoadingVariables) ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </div>
            
            <div className="max-h-64 overflow-y-auto">
              {isLoadingDataSets ? (
                <div className="p-6 text-center">
                  <Loader2 className="h-8 w-8 text-blue-500 mx-auto mb-3 animate-spin" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">Loading data sets...</p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">Fetching available test data</p>
                </div>
              ) : dataSetError ? (
                <div className="p-3 text-center">
                  <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
                  <p className="text-sm text-red-600 dark:text-red-400">{dataSetError}</p>
                </div>
              ) : filteredDataSets.length === 0 ? (
                <div className="p-3 text-center text-sm text-gray-500">
                  {searchQuery ? 'No data sets found' : 'No data sets available'}
                </div>
              ) : (
                <>
                  <DropdownMenuLabel className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400">
                    Available Data Sets ({filteredDataSets.length})
                  </DropdownMenuLabel>
                  {filteredDataSets.map((dataSet) => (
                    <DropdownMenuItem
                      key={dataSet.id}
                      className="px-3 py-2 cursor-pointer focus:bg-gray-50 dark:focus:bg-gray-900"
                      onClick={() => handleDataSetSelect(dataSet)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-sm truncate">
                            {dataSet.name}
                          </span>
                          {((dataSet.variables && dataSet.variables.length > 0) || (dataSet.variableNames && dataSet.variableNames.length > 0)) && (
                            <Badge variant="secondary" className="ml-2 text-xs">
                              {(dataSet.variables?.length || dataSet.variableNames?.length || 0)} vars
                            </Badge>
                          )}
                        </div>
                        {dataSet.description && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                            {dataSet.description}
                          </p>
                        )}
                        <div className="flex items-center justify-between mt-2">
                          {/* Variables Preview */}
                          {dataSet.variables && dataSet.variables.length > 0 && (
                            <div className="flex gap-1 flex-wrap">
                              {dataSet.variables.slice(0, 3).map((variable) => (
                                <Badge key={variable.name} variant="outline" className="text-xs px-1 py-0 font-mono bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 border-indigo-200 dark:border-indigo-700">
                                  @{variable.name}
                                </Badge>
                              ))}
                              {dataSet.variables.length > 3 && (
                                <span className="text-xs text-gray-400">
                                  +{dataSet.variables.length - 3}
                                </span>
                              )}
                            </div>
                          )}

                          {/* Active Environments */}
                          {(() => {
                            // Get unique environments from all variables
                            const allEnvironments = new Map();
                            if (dataSet.variables) {
                              dataSet.variables.forEach(variable => {
                                if (variable.environments) {
                                  variable.environments.forEach(env => {
                                    allEnvironments.set(env.id, env.name);
                                  });
                                }
                              });
                            }
                            const uniqueEnvs = Array.from(allEnvironments.values());

                            return uniqueEnvs.length > 0 && (
                              <div className="flex items-center gap-1">
                                <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                                <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                                  {uniqueEnvs.length} aktif env
                                </span>
                              </div>
                            );
                          })()}
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </>
              )}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear Selection Button */}
        {hasSelectedDataSet && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearSelection}
            className="h-10 px-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          >
            <X className="h-4 w-4" />
          </Button>
        )}

        {/* Variable Preview Toggle */}
        {hasSelectedDataSet && hasVariables && (
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleVariablePreview}
            className="h-10 px-3 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          >
            {showVariablePreview ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Variable Preview */}
      {hasSelectedDataSet && showVariablePreview && (
        <Card className="border-gray-200 dark:border-gray-800" style={{ overflow: 'visible' }}>
          <CardContent className="p-3" style={{ overflow: 'visible' }}>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Available Variables
              </h4>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {variables.length} variables
                </Badge>
                {/* Show active environments for this data set */}
                {selectedDataSet?.variables && (() => {
                  const allEnvironments = new Map();
                  selectedDataSet.variables.forEach(variable => {
                    if (variable.environments) {
                      variable.environments.forEach(env => {
                        allEnvironments.set(env.id, env.name);
                      });
                    }
                  });
                  const uniqueEnvs = Array.from(allEnvironments.values());

                  return uniqueEnvs.length > 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                      <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                        {uniqueEnvs.length} aktif env
                      </span>
                    </div>
                  );
                })()}
              </div>
            </div>
            
            {isLoadingVariables ? (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading variables...
              </div>
            ) : variables.length === 0 ? (
              <p className="text-sm text-gray-500">No variables found in this data set</p>
            ) : (
              <div className="space-y-3" style={{ overflow: 'visible' }}>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Use @variableName in your prompt to reference these variables:
                  </p>
                  {/* Show active environments for this data set */}
                  {selectedDataSet?.variables && (() => {
                    const allEnvironments = new Map();
                    selectedDataSet.variables.forEach(variable => {
                      if (variable.environments) {
                        variable.environments.forEach(env => {
                          allEnvironments.set(env.id, env.name);
                        });
                      }
                    });
                    const uniqueEnvs = Array.from(allEnvironments.values());

                    return uniqueEnvs.length > 0 && (
                      <div className="flex gap-1 items-center">
                        <span className="text-xs text-gray-500 dark:text-gray-400">Aktif env:</span>
                        {uniqueEnvs.slice(0, 3).map((envName, envIndex) => (
                          <Badge
                            key={envName}
                            variant="secondary"
                            className={`text-xs px-2 py-1 font-medium ${
                              envIndex === 0 ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300' :
                              envIndex === 1 ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300' :
                              'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300'
                            }`}
                          >
                            {envName}
                          </Badge>
                        ))}
                        {uniqueEnvs.length > 3 && (
                          <span className="text-xs text-gray-400">
                            +{uniqueEnvs.length - 3}
                          </span>
                        )}
                      </div>
                    );
                  })()}
                </div>
                {/* Search input for variables */}
                <div className="relative mb-3">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
                  <Input
                    placeholder="Search variables..."
                    className="pl-8 h-8 text-xs"
                    value={variableSearchQuery}
                    onChange={(e) => setVariableSearchQuery(e.target.value)}
                  />
                </div>

                {/* Variable list - no scroll */}
                <div className="relative" style={{ overflow: 'visible' }}>
                  <div className="flex gap-2 flex-wrap pb-2">
                    {variables
                      .filter(variable => 
                        variable.name.toLowerCase().includes(variableSearchQuery.toLowerCase())
                      )
                      .sort((a, b) => {
                        // Sort by source type: database sources first, then others
                        const sourceOrder = { 'database': 0, 'api': 1, 'csv': 2, 'excel': 3 };
                        const aOrder = sourceOrder[a.sourceType || 'csv'] ?? 4;
                        const bOrder = sourceOrder[b.sourceType || 'csv'] ?? 4;
                        return aOrder - bOrder;
                      })
                      .map((variable) => {
                        // Get source-specific styling
                        const getSourceStyling = (sourceType?: string, dbMode?: string) => {
                          switch (sourceType) {
                            case 'database':
                              if (dbMode === 'visual') {
                                return {
                                  bgClass: 'bg-green-50 dark:bg-green-900/30',
                                  textClass: 'text-green-700 dark:text-green-300', 
                                  borderClass: 'border-green-200 dark:border-green-700',
                                  hoverClass: 'hover:bg-green-100 dark:hover:bg-green-900/50',
                                  icon: '🔗', // Visual DB
                                  label: 'DB Visual'
                                };
                              } else {
                                return {
                                  bgClass: 'bg-blue-50 dark:bg-blue-900/30',
                                  textClass: 'text-blue-700 dark:text-blue-300',
                                  borderClass: 'border-blue-200 dark:border-blue-700', 
                                  hoverClass: 'hover:bg-blue-100 dark:hover:bg-blue-900/50',
                                  icon: '🗄️', // Raw DB
                                  label: 'DB Raw'
                                };
                              }
                            case 'api':
                              return {
                                bgClass: 'bg-purple-50 dark:bg-purple-900/30',
                                textClass: 'text-purple-700 dark:text-purple-300',
                                borderClass: 'border-purple-200 dark:border-purple-700',
                                hoverClass: 'hover:bg-purple-100 dark:hover:bg-purple-900/50',
                                icon: '🌐',
                                label: 'API'
                              };
                            default:
                              return {
                                bgClass: 'bg-indigo-50 dark:bg-indigo-900/30',
                                textClass: 'text-indigo-700 dark:text-indigo-300',
                                borderClass: 'border-indigo-200 dark:border-indigo-700',
                                hoverClass: 'hover:bg-indigo-100 dark:hover:bg-indigo-900/50',
                                icon: '📄',
                                label: 'File'
                              };
                          }
                        };

                        const styling = getSourceStyling(variable.sourceType, variable.dbMode);

                        return (
                          <div key={variable.id} className="group relative">
                            <Badge
                              variant="outline"
                              className={`text-xs px-3 py-1.5 font-mono ${styling.bgClass} ${styling.textClass} ${styling.borderClass} ${styling.hoverClass} transition-colors cursor-pointer whitespace-nowrap flex-shrink-0 flex items-center gap-1.5`}
                              onClick={() => {
                                // Copy variable name to clipboard
                                navigator.clipboard.writeText(`@${variable.name}`)
                              }}
                            >
                              <span className="text-xs">{styling.icon}</span>
                              @{variable.name}
                            </Badge>
                          
                            {/* Enhanced Tooltip with source-specific information */}
                            <div className="absolute left-full top-0 ml-2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-[100] min-w-max max-w-sm">
                              <div className="font-semibold text-center mb-2">@{variable.name}</div>
                              
                              {/* Source Type Badge */}
                              <div className="flex justify-center mb-2">
                                <span className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${
                                  variable.sourceType === 'database' 
                                    ? variable.dbMode === 'visual' 
                                      ? 'bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200'
                                      : 'bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200'
                                    : variable.sourceType === 'api'
                                      ? 'bg-purple-200 dark:bg-purple-800 text-purple-800 dark:text-purple-200'
                                      : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                                }`}>
                                  {styling.icon} {styling.label}
                                </span>
                              </div>

                              {variable.description && (
                                <div className="text-gray-300 dark:text-gray-600 text-center mb-2 text-xs">
                                  {variable.description}
                                </div>
                              )}

                              {/* Source-specific configuration */}
                              {variable.sourceType === 'api' && variable.jsonPath && (
                                <div className="mb-2 p-2 bg-gray-800 dark:bg-gray-200 rounded">
                                  <div className="text-gray-400 dark:text-gray-500 text-xs font-medium mb-1">JSON Path:</div>
                                  <div className="font-mono text-xs text-white dark:text-gray-900 break-all">
                                    {variable.jsonPath}
                                  </div>
                                </div>
                              )}

                                                             {variable.sourceType === 'database' && (
                                 <div className="mb-2 p-2 bg-gray-800 dark:bg-gray-200 rounded">
                                   <div className="text-gray-400 dark:text-gray-500 text-xs font-medium mb-1">Database Mapping:</div>
                                   
                                   {variable.nameColumn ? (
                                     <div className="flex justify-between text-xs mb-1">
                                       <span className="text-gray-400 dark:text-gray-500">Name:</span>
                                       <span className="font-mono text-white dark:text-gray-900">{variable.nameColumn}</span>
                                     </div>
                                   ) : (
                                     <div className="flex justify-between text-xs mb-1">
                                       <span className="text-gray-400 dark:text-gray-500">Name:</span>
                                       <span className="text-red-400 dark:text-red-500 text-xs">Not configured</span>
                                     </div>
                                   )}
                                   
                                   {variable.valueColumn ? (
                                     <div className="flex justify-between text-xs">
                                       <span className="text-gray-400 dark:text-gray-500">Value:</span>
                                       <span className="font-mono text-white dark:text-gray-900">{variable.valueColumn}</span>
                                     </div>
                                   ) : (
                                     <div className="flex justify-between text-xs">
                                       <span className="text-gray-400 dark:text-gray-500">Value:</span>
                                       <span className="text-red-400 dark:text-red-500 text-xs">Not configured</span>
                                     </div>
                                   )}
                                 </div>
                               )}
                              
                              {variable.environmentValues && Object.keys(variable.environmentValues).length > 0 ? (
                                <div className="space-y-1.5">
                                  <div className="text-gray-400 dark:text-gray-500 text-xs font-medium text-center border-b border-gray-700 dark:border-gray-300 pb-1">
                                    Environment Values:
                                  </div>
                                  {Object.entries(variable.environmentValues).slice(0, 4).map(([envId, value]) => {
                                    const envName = variable.environments?.find(e => e.id === envId)?.name || envId;
                                    return (
                                      <div key={envId} className="flex items-center justify-between gap-2 text-xs">
                                        <span className="text-gray-400 dark:text-gray-500 font-medium truncate">
                                          {envName}:
                                        </span>
                                        <span className="font-mono bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-900 px-2 py-0.5 rounded text-xs max-w-32 truncate">
                                          {value || 'empty'}
                                        </span>
                                      </div>
                                    );
                                  })}
                                  {Object.keys(variable.environmentValues).length > 4 && (
                                    <div className="text-xs text-gray-400 dark:text-gray-500 text-center pt-1 border-t border-gray-700 dark:border-gray-300">
                                      +{Object.keys(variable.environmentValues).length - 4} more environments
                                    </div>
                                  )}
                                </div>
                              ) : (
                                <div className="text-gray-400 dark:text-gray-500 text-xs text-center">
                                  No environment values available
                                </div>
                              )}
                              
                              {/* Arrow */}
                              <div className="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900 dark:border-r-gray-100"></div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                  
                  {/* Show filtered count */}
                  {variableSearchQuery && (
                    <div className="text-xs text-gray-500 mt-2">
                      Showing {variables.filter(v => v.name.toLowerCase().includes(variableSearchQuery.toLowerCase())).length} of {variables.length} variables
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
