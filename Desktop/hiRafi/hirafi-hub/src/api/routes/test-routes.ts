/**
 * Test API Routes
 * Handles all test-related endpoints
 */

import { Router, Request, Response } from 'express';
import { testManager } from '../../core/test-manager/index.js';
import { nodeManager } from '../../core/node-manager/index.js';
import { logger } from '../../utils/logger.js';
import { TestStatus } from '../../models/test-types.js';
import { centralResultProcessor } from '../../services/result/centralResultProcessor.js';
import {
  createTestReportAtomic
} from '../../services/mongo/atomicReportService.js';
import {
  updateScenarioRunStatus
} from '../../services/mongo/scenarioService.js';
import { getScenarioById } from '../../services/mongo/scenarioService.js';
import { nodeRegistry } from '../../services/node/index.js';

// Extended Request type with user property
interface AuthRequest extends Request {
  user?: any; // Using any to accommodate various user objects
}

const router = Router();

/**
 * GET /api/test-hub/active-tests
 * Birleştirilmiş endpoint - Kullanıcının tüm testlerini durum ve ilerleme bilgileriyle döndürür
 */
router.get('/active-tests', async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test information'
      });
    }

    // Filtre parametresi varsa kullan, yoksa varsayılan olarak "active" kullan
    const filterParam = req.query.filter?.toString() || 'active';

    // Kullanıcının testlerini getir
    let tests = await testManager.getTestsByUserId(userId);

    // Filtreyi uygula
    if (filterParam === 'active') {
      // Aktif testler (RUNNING, QUEUED) ve yeni tamamlanan testleri de dahil et
      tests = tests.filter(test =>
        test.status === TestStatus.RUNNING ||
        test.status === TestStatus.QUEUED ||
        test.status === TestStatus.COMPLETED ||
        test.status === TestStatus.FAILED ||
        test.status === TestStatus.STOPPED
      );
    } else if (filterParam === 'completed') {
      // Sadece tamamlanmış testler (COMPLETED, FAILED, STOPPED)
      tests = tests.filter(test =>
        test.status === TestStatus.COMPLETED ||
        test.status === TestStatus.FAILED ||
        test.status === TestStatus.STOPPED
      );
    }

    // Testleri işle ve sadece temel bilgileri ekle
    const processedTests = tests.map(test => {
      // Process objesi hariç test bilgileri
      const testInfo = {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        testStatus: (() => {
          // Result üzerinden test sonucunu belirle
          if (test.result?.status) {
            return test.result.status; // 'success', 'failure', etc.
          }

          // Result yoksa status'a göre belirle
          switch (test.status) {
            case TestStatus.COMPLETED:
              return 'success';
            case TestStatus.FAILED:
              return 'failure';
            case TestStatus.STOPPED:
              return 'stopped';
            case TestStatus.RUNNING:
              return 'running';
            case TestStatus.QUEUED:
              return 'queued';
            default:
              return 'unknown';
          }
        })(),
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        completedAt: test.completedAt,
        userId: test.userId,
        logs: 'logs' in test ? test.logs.slice(-5) : [] // Son 5 log yeterli
      };

      return testInfo;
    });

    // Son 24 saate göre sırala (en yeni başta)
    processedTests.sort((a, b) => {
      const timeA = a.startedAt || a.queuedAt || new Date(0);
      const timeB = b.startedAt || b.queuedAt || new Date(0);
      return timeB.getTime() - timeA.getTime();
    });

    return res.json({
      success: true,
      filter: filterParam,
      count: processedTests.length,
      tests: processedTests,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    logger.error(`API Error in unified tests endpoint: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/tests/:testId
 * Birleştirilmiş endpoint - Hem test durumu hem de adım ilerlemesini döndürür
 */
router.get('/tests/:testId', async (req: AuthRequest, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test information'
      });
    }

    // URL'den test ID'sini al
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    const test = await testManager.getTest(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test with ID ${testId} not found`
      });
    }

    // Kullanıcı testi kontrol - kendi testini veya admin ise herhangi bir testi görebilir
    if (test.userId !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view your own tests'
      });
    }

    // Sadece logları al
    const logs = 'logs' in test ? test.logs.slice(-10) : [];

    // Temel test bilgileri (process objesi hariç)
    const testInfo = {
      id: test.id,
      scenarioId: test.scenarioId,
      scenarioName: test.scenarioName,
      status: test.status,
      testStatus: (() => {
        // Result üzerinden test sonucunu belirle
        if (test.result?.status) {
          return test.result.status; // 'success', 'failure', etc.
        }

        // Result yoksa status'a göre belirle
        switch (test.status) {
          case TestStatus.COMPLETED:
            return 'success';
          case TestStatus.FAILED:
            return 'failure';
          case TestStatus.STOPPED:
            return 'stopped';
          case TestStatus.RUNNING:
            return 'running';
          case TestStatus.QUEUED:
            return 'queued';
          default:
            return 'unknown';
        }
      })(),
      queuedAt: test.queuedAt,
      startedAt: test.startedAt,
      completedAt: test.completedAt,
      userId: test.userId
    };

    return res.json({
      success: true,
      test: testInfo,
      status: test.status,
      testStatus: testInfo.testStatus,
      logs: logs,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    logger.error(`API Error in unified test endpoint: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub
 * Get all tests with optional filtering
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const filter = req.query.filter?.toString() || 'all';
    const scenarioId = req.query.scenarioId?.toString();
    const testId = req.query.testid?.toString();

    // Eğer testId belirtilmişse, sadece o testi getir
    if (testId) {
      const test = await testManager.getTest(testId);

      if (!test) {
        return res.status(404).json({
          success: false,
          error: `Test with ID ${testId} not found`
        });
      }

      // Don't send process object in API response
      let filteredTest = { ...test };

      // process alanını kaldır
      if ('process' in filteredTest) {
        filteredTest.process = null;
      }

      // Eğer scenarioData varsa ve steps alanı içeriyorsa, steps alanını kaldır veya sadeleştir
      if (filteredTest.scenarioData && filteredTest.scenarioData.steps) {
        // steps alanını sil veya boş bir dizi ile değiştir
        // Referansı korumak için, tüm scenarioData'yı kopyalayalım ve steps'i çıkaralım
        filteredTest.scenarioData = {
          ...filteredTest.scenarioData,
          steps: [] // Boş bir dizi ile değiştir veya tamamen kaldırmak için bu satırı silebilirsin
        };
      }

      const responseTest = filteredTest;

      return res.json({
        success: true,
        tests: [responseTest],
        queueStatus: await testManager.getQueueStatus()
      });
    }

    let tests = await testManager.getAllTests();

    // Apply filter if not 'all'
    if (filter !== 'all') {
      tests = tests.filter(test => test.status === filter);
    }

    // Filter by scenarioId if provided
    if (scenarioId) {
      tests = tests.filter(test => test.scenarioId === scenarioId);
    }

    // Simplify logs for API response
    const simplifiedTests = tests.map(test => {
      const limitedLogs = test.logs.slice(-50); // Last 50 logs

      // Tamamlanmış testlerde gereksiz detayları kaldır
      const isCompleted = test.status === TestStatus.COMPLETED ||
                         test.status === TestStatus.FAILED ||
                         test.status === TestStatus.STOPPED;

      return {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        completedAt: test.completedAt,
        nodeId: test.nodeId,
        logs: limitedLogs,
        // Sadece gerekli sonuç alanlarını tut
        result: isCompleted && test.result ? {
          status: test.result.status,
          summary: test.result.summary || {},
          reportId: test.result.reportId || test.reportId
        } : test.result,
        reportId: test.reportId,
        userId: test.userId,
        process: null // Don't send process object
      };
    });

    res.json({
      success: true,
      tests: simplifiedTests,
      queueStatus: await testManager.getQueueStatus()
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/test-status/:testId
 * Endpoint for getting a specific test status by ID - Used by frontend
 */
router.get('/test-status/:testId', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test status'
      });
    }

    // URL'den test ID'sini al
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    const test = await testManager.getTest(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test with ID ${testId} not found`
      });
    }

    // Kullanıcı testi kontrol - kendi testini veya admin ise herhangi bir testi görebilir
    if (test.userId !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view your own tests'
      });
    }

    // Don't send process object in API response
    const responseTest = {
      ...test,
      process: null
    };

    return res.json({
      success: true,
      test: responseTest,
      status: responseTest.status
    });
  } catch (error: any) {
    logger.error(`API Error in test-status GET: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/test-status
 * Endpoint for getting tests for authenticated user - Used by frontend
 * This endpoint is called directly by the frontend as /api/test-status
 */
router.get('/test-status', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test status'
      });
    }

    // Filtre parametresi varsa kullan, yoksa varsayılan olarak "active" kullan
    const filterParam = req.query.filter?.toString() || 'active';

    // Kullanıcının testlerini getir
    let tests = await testManager.getTestsByUserId(userId);

    // Filtreyi uygula
    if (filterParam === 'active') {
      // Aktif testler (RUNNING, QUEUED) ve yeni tamamlanan testleri de dahil et
      // Temizlik için 30 saniye bekleme süresi verdiğimiz için, bu testler
      // tamamlansa bile frontend'de 30 saniye kadar görünecekler
      tests = tests.filter(test =>
        test.status === TestStatus.RUNNING ||
        test.status === TestStatus.QUEUED ||
        test.status === TestStatus.COMPLETED ||
        test.status === TestStatus.FAILED ||
        test.status === TestStatus.STOPPED
      );
    } else if (filterParam === 'completed') {
      // Sadece tamamlanmış testler (COMPLETED, FAILED, STOPPED)
      tests = tests.filter(test =>
        test.status === TestStatus.COMPLETED ||
        test.status === TestStatus.FAILED ||
        test.status === TestStatus.STOPPED
      );
    }
    // Filtreleme yoksa veya 'all' ise tüm testleri döndür

    // Simplify logs for API response
    const simplifiedTests = tests.map(test => {
      return {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        completedAt: test.completedAt, // Tamamlanma zamanını da ekle
        nodeId: test.nodeId,
        logs: 'logs' in test ? test.logs.slice(-5) : [], // Only last few logs
        userId: test.userId,
        result: test.result, // Test sonucunu da ekle
        process: null // Don't send process object
      };
    });

    return res.json({
      success: true,
      count: simplifiedTests.length,
      tests: simplifiedTests
    });

  } catch (error: any) {
    logger.error(`API Error in test-status GET: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub/test-status/:testId
 * Endpoint for getting a specific test status by ID using POST
 */
router.post('/test-status/:testId', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test status'
      });
    }

    // URL'den test ID'sini al
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    const test = await testManager.getTest(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test with ID ${testId} not found`
      });
    }

    // Kullanıcı testi kontrol - kendi testini veya admin ise herhangi bir testi görebilir
    if (test.userId !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view your own tests'
      });
    }

    // Don't send process object in API response
    const responseTest = {
      ...test,
      process: null
    };

    return res.json({
      success: true,
      test: responseTest,
      status: responseTest.status
    });
  } catch (error: any) {
    logger.error(`API Error in test-status POST: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub/test-status
 * Endpoint for getting tests for authenticated user using POST
 */
router.post('/test-status', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test status'
      });
    }

    // Filtre parametresi request body'den al, yoksa varsayılan olarak "active" kullan
    const filterParam = req.body.filter?.toString() || 'active';

    // Kullanıcının testlerini getir
    let tests = await testManager.getTestsByUserId(userId);

    // Filtreyi uygula
    if (filterParam === 'active') {
      // Aktif testler (RUNNING, QUEUED) ve yeni tamamlanan testleri de dahil et (GET endpoint ile aynı filtreleme)
      tests = tests.filter(test =>
        test.status === TestStatus.RUNNING ||
        test.status === TestStatus.QUEUED ||
        test.status === TestStatus.COMPLETED ||
        test.status === TestStatus.FAILED ||
        test.status === TestStatus.STOPPED
      );
    } else if (filterParam === 'completed') {
      // Sadece tamamlanmış testler (COMPLETED, FAILED, STOPPED)
      tests = tests.filter(test =>
        test.status === TestStatus.COMPLETED ||
        test.status === TestStatus.FAILED ||
        test.status === TestStatus.STOPPED
      );
    }
    // Filtreleme yoksa veya 'all' ise tüm testleri döndür

    // Simplify logs for API response
    const simplifiedTests = tests.map(test => {
      return {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        completedAt: test.completedAt, // Tamamlanma zamanını da ekle
        nodeId: test.nodeId,
        logs: 'logs' in test ? test.logs.slice(-5) : [], // Only last few logs
        userId: test.userId,
        result: test.result, // Test sonucunu da ekle
        process: null // Don't send process object
      };
    });

    return res.json({
      success: true,
      count: simplifiedTests.length,
      tests: simplifiedTests
    });
  } catch (error: any) {
    logger.error(`API Error in test-status POST: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/:testId/step-progress
 * Get the step progress for a specific test - Used by frontend to track test execution
 * Not: Dashboard'da gösterilmediği için sadece temel test bilgilerini ve logları döndürür
 */
router.get('/:testId/step-progress', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test step progress'
      });
    }

    // URL'den test ID'sini al
    const { testId } = req.params;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required'
      });
    }

    const test = await testManager.getTest(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test with ID ${testId} not found`
      });
    }

    // Kullanıcı testi kontrol - kendi testini veya admin ise herhangi bir testi görebilir
    if (test.userId !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view your own tests'
      });
    }

    // Prepare basic test information for the response
    const testInfo = {
      id: test.id,
      scenarioId: test.scenarioId,
      scenarioName: test.scenarioName,
      status: test.status,
      queuedAt: test.queuedAt,
      startedAt: test.startedAt,
      completedAt: test.completedAt,
      nodeId: test.nodeId
    };

    return res.json({
      success: true,
      test: testInfo,
      status: test.status,
      // Include latest logs if available for better context
      logs: 'logs' in test ? test.logs.slice(-10) : [],
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    logger.error(`API Error in step-progress GET: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub/step-progress
 * Get the step progress for a test using request body instead of URL parameter
 * Not: Dashboard'da gösterilmediği için sadece temel test bilgilerini ve logları döndürür
 */
router.post('/step-progress', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test step progress'
      });
    }

    // Request body'den test ID'sini al
    const { testId } = req.body;

    if (!testId) {
      return res.status(400).json({
        success: false,
        error: 'Test ID is required in request body'
      });
    }

    const test = await testManager.getTest(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test with ID ${testId} not found`
      });
    }

    // Kullanıcı testi kontrol - kendi testini veya admin ise herhangi bir testi görebilir
    if (test.userId !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view your own tests'
      });
    }

    // Prepare basic test information for the response
    const testInfo = {
      id: test.id,
      scenarioId: test.scenarioId,
      scenarioName: test.scenarioName,
      status: test.status,
      queuedAt: test.queuedAt,
      startedAt: test.startedAt,
      completedAt: test.completedAt,
      nodeId: test.nodeId
    };

    return res.json({
      success: true,
      test: testInfo,
      status: test.status,
      // Include latest logs if available for better context
      logs: 'logs' in test ? test.logs.slice(-10) : [],
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    logger.error(`API Error in step-progress POST: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/active-step-progress
 * Get step progress information for all active tests of the authenticated user
 * Not: Dashboard'da gösterilmediği için sadece temel test bilgilerini döndürür
 */
router.get('/active-step-progress', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test step progress'
      });
    }

    // Kullanıcının aktif testlerini getir (running veya queued)
    const activeTestsRaw = await testManager.getTestsByUserId(userId);
    const activeTests = activeTestsRaw.filter(test =>
      test.status === TestStatus.RUNNING || test.status === TestStatus.QUEUED
    );

    if (activeTests.length === 0) {
      return res.json({
        success: true,
        count: 0,
        tests: []
      });
    }

    // Her test için sadece temel bilgileri ekle (step progress olmadan)
    const testsWithBasicInfo = activeTests.map(test => {
      // Temel test bilgilerini al
      return {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        nodeId: test.nodeId,
        logs: 'logs' in test ? test.logs.slice(-5) : [] // Son 5 log yeterli
      };
    });

    return res.json({
      success: true,
      count: testsWithBasicInfo.length,
      tests: testsWithBasicInfo
    });
  } catch (error: any) {
    logger.error(`API Error in active-step-progress: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub/multi-step-progress
 * Get step progress for multiple tests at once using test IDs in request body
 * Not: Dashboard'da gösterilmediği için sadece temel test bilgilerini ve logları döndürür
 */
router.post('/multi-step-progress', async (req: Request, res: Response) => {
  try {
    // İsteği yapan kullanıcının ID'sini al
    const userId = req.user?.id;

    // Eğer ID yoksa auth hatası ver
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to get test step progress'
      });
    }

    // Request body'den test ID'lerini al
    const { testIds } = req.body;

    if (!testIds || !Array.isArray(testIds) || testIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Test IDs array is required in request body'
      });
    }

    // Her test ID için temel bilgileri ve logları topla
    const testsWithBasicInfo = [];
    const notFoundTests = [];
    const notAuthorizedTests = [];

    for (const testId of testIds) {
      const test = await testManager.getTest(testId);

      if (!test) {
        notFoundTests.push(testId);
        continue;
      }

      // Kullanıcı testi kontrol - kendi testini veya admin ise herhangi bir testi görebilir
      if (test.userId !== userId && req.user?.role !== 'admin') {
        notAuthorizedTests.push(testId);
        continue;
      }

      // Temel test bilgilerini hazırla
      const testInfo = {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        completedAt: test.completedAt,
        nodeId: test.nodeId,
        logs: 'logs' in test ? test.logs.slice(-5) : [] // Son 5 log yeterli
      };

      testsWithBasicInfo.push(testInfo);
    }

    return res.json({
      success: true,
      count: testsWithBasicInfo.length,
      tests: testsWithBasicInfo,
      notFound: notFoundTests,
      notAuthorized: notAuthorizedTests,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    logger.error(`API Error in multi-step-progress POST: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/next
 * Get the next queued test for a node
 * NOTE: Prefer using WebSocket connection for improved performance
 * @deprecated This endpoint is part of the old pull model and will be removed.
 */
router.get('/next', async (req: Request, res: Response) => {
  logger.warn('API: /api/test-hub/next endpoint is deprecated and will be removed. Nodes should claim tests via their BullMQ workers and notify hub.');
  try {
    const nodeId = req.query.nodeId?.toString();

    // Log HTTP usage - WebSocket is preferred
    logger.info(`API: HTTP request for next test from node ${nodeId} - WebSocket is recommended for better performance`);

    if (!nodeId) {
      return res.status(400).json({
        success: false,
        error: 'nodeId query parameter is required'
      });
    }

    // Verify node exists and is available
    const node = nodeManager.getNode(nodeId);
    if (!node) {
      return res.status(404).json({
        success: false,
        error: `Node with ID ${nodeId} not found`
      });
    }

    if (node.status !== 'available') {
      return res.status(400).json({
        success: false,
        error: `Node ${nodeId} is not available (status: ${node.status})`
      });
    }

    // Get next test from queue - properly await the Promise
    const nextTest = await testManager.getNextQueuedTest();
    if (!nextTest) {
      return res.status(404).json({
        success: false,
        error: 'No tests available in queue'
      });
    }

    // Add test to central queue for Pull Model
    const transactionId = `api-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const assigned = await testManager.assignTestToNodeAtomic(nextTest.id, nodeId, {
      transactionId,
      source: 'api-next-endpoint'
    });

    if (!assigned) {
      return res.status(500).json({
        success: false,
        error: `Failed to add test ${nextTest.id} to central queue for Pull Model`
      });
    }

    logger.info(`API: Assigned test ${nextTest.id} to node ${nodeId} via /next endpoint`);

    res.json({
      success: true,
      test: nextTest
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub/internal/test/:testId/node/:nodeId/claimed-by-worker
 * Internal endpoint for test_node workers to notify the hub after claiming a job from BullMQ.
 */
router.post('/internal/test/:testId/node/:nodeId/claimed-by-worker', async (req: AuthRequest, res: Response) => {
  const { testId, nodeId } = req.params;
  // No specific request body is expected for this notification, but could be added later if needed.

  logger.info(`API: Node ${nodeId} reported claiming test ${testId} via BullMQ worker.`);

  try {
    // 1. Validate nodeId
    const node = nodeManager.getNode(nodeId);
    if (!node) {
      logger.warn(`API: Node ${nodeId} not found in registry during claimed-by-worker notification for test ${testId}.`);
      return res.status(404).json({ success: false, message: `Node ${nodeId} not registered.` });
    }

    // 2. Update test status in MongoDB to RUNNING and associate with nodeId
    // Assuming testManager.updateTestStatus can handle this. 
    // We might need to fetch the test first to ensure it's in a claimable state (e.g., QUEUED)
    const testDetails = await testManager.getTest(testId); // Use the implemented getTest

    if (!testDetails) {
      logger.warn(`API: Test ${testId} not found during claimed-by-worker notification by node ${nodeId}.`);
      return res.status(404).json({ success: false, message: `Test ${testId} not found.` });
    }
    
    // Optional: Check if test is in a state that can be claimed (e.g., QUEUED)
    if (testDetails.status !== TestStatus.QUEUED && testDetails.status !== TestStatus.ASSIGNING) {
        // ASSIGNING might be a transient state if the old model was somehow still active.
        // For a pure worker-pull, it should typically be QUEUED.
        logger.warn(`API: Test ${testId} is in status ${testDetails.status}, not QUEUED/ASSIGNING. Node ${nodeId} attempt to claim. Possible stale job or race.`);
        // Decide if this should be an error or just a warning. For now, let's allow it but log heavily.
        // return res.status(409).json({ success: false, message: `Test ${testId} is in status ${testDetails.status}, cannot be claimed.` });
    }

    const updateSuccess = await testManager.updateTestStatus(testId, TestStatus.RUNNING, {
      nodeId: nodeId,
      startedAt: new Date(), // Set startedAt when claimed by worker
      source: 'worker-claim' // Add a source for clarity
    });

    if (!updateSuccess) {
      logger.error(`API: Failed to update test ${testId} status to RUNNING for node ${nodeId} in MongoDB.`);
      // This is critical. If the DB update fails, the hub doesn't know the node has it.
      return res.status(500).json({ success: false, message: `Failed to update test ${testId} status in database.` });
    }

    // 3. Mark node as busy in NodeRegistry
    const markedBusy = await nodeRegistry.markNodeAsBusy(nodeId, testId, 'admin-api');
    if (!markedBusy) {
      logger.error(`API: Failed to mark node ${nodeId} as busy for test ${testId} in NodeRegistry. DB was updated.`);
      // DB was updated, but registry is out of sync. This is problematic.
      // Consider how to handle this inconsistency. For now, return error.
      return res.status(500).json({ success: false, message: `Failed to mark node ${nodeId} as busy in registry.` });
    }


    res.status(200).json({ success: true, message: `Test ${testId} claim by node ${nodeId} acknowledged.` });

  } catch (error: any) {
    logger.error(`API: Error in /internal/test/:testId/node/:nodeId/claimed-by-worker for test ${testId}, node ${nodeId}: ${error.message}`);
    res.status(500).json({ success: false, message: 'Internal server error processing test claim.' });
  }
});

/**
 * GET /api/test-hub/:testId
 * Get a specific test by ID
 */
router.get('/:testId', async (req: Request, res: Response) => {
  try {
    const { testId } = req.params;
    const test = await testManager.getTest(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test with ID ${testId} not found`
      });
    }

    // Don't send process object in API response and optimize completed test data
    const isCompleted = test.status === TestStatus.COMPLETED ||
                       test.status === TestStatus.FAILED ||
                       test.status === TestStatus.STOPPED;

    // Use type assertion to handle the reportId property which exists in TestProcess but not in TestRequest
    const testWithReportId = test as { reportId?: string } & typeof test;

    const responseTest = {
      id: test.id,
      scenarioId: test.scenarioId,
      scenarioName: test.scenarioName,
      status: test.status,
      queuedAt: test.queuedAt,
      startedAt: test.startedAt,
      completedAt: test.completedAt,
      priority: 'priority' in test ? test.priority : 10,
      nodeId: test.nodeId,
      logs: 'logs' in test ? test.logs : [],
      // Tamamlanmış testlerde sadece gerekli sonuç alanlarını tut
      result: isCompleted && test.result ? {
        status: test.result.status,
        summary: test.result.summary || {},
        reportId: test.result.reportId || testWithReportId.reportId
      } : test.result,
      reportId: testWithReportId.reportId,
      userId: test.userId,
      process: null
    };

    // Doğru test yanıt formatı, isTestStatusResponse type guard'ına uygun olmalı
    const formattedResponse = {
      success: true,
      status: responseTest.status,
      id: responseTest.id,
      scenarioId: responseTest.scenarioId,
      // Tamamlanmış testlerde senaryo verisi göndermeyi optimizasyonu
      scenario: isCompleted ? {} : ('scenarioData' in test ? test.scenarioData : {}),
      queuedAt: responseTest.queuedAt,
      priority: 'priority' in responseTest ? responseTest.priority : 10,
      nodeId: responseTest.nodeId,
      test: responseTest // Tüm test verilerini de dahil et
    };

    res.json(formattedResponse);
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub
 * Add a test, stop a test, or update settings
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const { action, scenarioId, scenarioIds, testId, maxConcurrent, requiredCapabilities, status, result } = req.body;

    // Kullanıcı bilgilerini al
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authenticated user is required to run tests'
      });
    }

    // Check if userId was explicitly included in request body (for backward compatibility)
    const explicitUserId = req.body.userId;
    const effectiveUserId = explicitUserId || userId;

    switch (action) {
      case 'add-test':
        if (!scenarioId) {
          return res.status(400).json({
            success: false,
            error: 'scenarioId is required'
          });
        }

        // Aynı senaryo için çalışan bir test olup olmadığını kontrol et
        const runningTests = testManager.getTestsByScenarioId(scenarioId);
        const isTestRunning = runningTests.some(test =>
          test.status === TestStatus.QUEUED || test.status === TestStatus.RUNNING
        );

        if (isTestRunning) {
          return res.status(409).json({
            success: false,
            error: 'A test is already running or queued for this scenario',
            runningTestId: runningTests.find(test =>
              test.status === TestStatus.QUEUED || test.status === TestStatus.RUNNING
            )?.id
          });
        }

        // Senaryo verilerini veritabanından al
        const scenarioResult = await getScenarioById(scenarioId);

        if (!scenarioResult.success || !scenarioResult.scenario) {
          return res.status(404).json({
            success: false,
            error: `Scenario with ID ${scenarioId} not found`
          });
        }

        const scenario = scenarioResult.scenario;

        // Testi ekle
        const newTestId = await testManager.addTest(
          scenarioId,
          scenario.title || scenario.name || "Unnamed Scenario",
          scenario,
          10, // default priority
          effectiveUserId // Testi çalıştıran kullanıcı ID'si
        );

        logger.info(`API: Added test ${newTestId} for scenario ${scenarioId} by user ${effectiveUserId}`);

        // Otomatik olarak bir node'a atamayı dene
        if (requiredCapabilities) {
          // Gerekli özelliklere sahip uygun bir node'a testi gönder
          await nodeManager.assignAndSendTest(newTestId, requiredCapabilities as string[]);
        }

        return res.json({
          success: true,
          testId: newTestId,
          message: `Test added for scenario: ${scenario.title || scenario.name || "Unnamed Scenario"}`
        });

      case 'bulk-run-tests':
        // Birden fazla senaryo için toplu test çalıştırma
        if (!scenarioIds || !Array.isArray(scenarioIds) || scenarioIds.length === 0) {
          return res.status(400).json({
            success: false,
            error: 'scenarioIds array is required and cannot be empty'
          });
        }

        // Sonuçları toplamak için diziler
        const successfulTests: { scenarioId: string; testId: string; scenarioName: string }[] = [];
        const failedScenarios: { scenarioId: string; reason: string }[] = [];

        // Her senaryo için test oluştur
        for (const id of scenarioIds) {
          try {
            // Aynı senaryo için çalışan bir test olup olmadığını kontrol et
            const scenarioRunningTests = testManager.getTestsByScenarioId(id);
            const isScenarioTestRunning = scenarioRunningTests.some(test =>
              test.status === TestStatus.QUEUED || test.status === TestStatus.RUNNING
            );

            if (isScenarioTestRunning) {
              failedScenarios.push({
                scenarioId: id,
                reason: 'A test is already running or queued for this scenario'
              });
              continue; // Bu senaryo için işlemi atla ve bir sonrakine geç
            }

            // Senaryo verilerini veritabanından al
            const bulkScenarioResult = await getScenarioById(id);

            if (!bulkScenarioResult.success || !bulkScenarioResult.scenario) {
              failedScenarios.push({
                scenarioId: id,
                reason: 'Scenario not found'
              });
              continue; // Bu senaryo için işlemi atla ve bir sonrakine geç
            }

            const bulkScenario = bulkScenarioResult.scenario;

            // Testi ekle
            const bulkTestId = await testManager.addTest(
              id,
              bulkScenario.title || bulkScenario.name || "Unnamed Scenario",
              bulkScenario,
              10, // default priority
              effectiveUserId // Testi çalıştıran kullanıcı ID'si
            );

            logger.info(`API: Added bulk test ${bulkTestId} for scenario ${id} by user ${effectiveUserId}`);

            // Otomatik olarak bir node'a atamayı dene
            if (requiredCapabilities) {
              // Gerekli özelliklere sahip uygun bir node'a testi gönder
              await nodeManager.assignAndSendTest(bulkTestId, requiredCapabilities as string[]);
            }

            // Başarılı olan testleri kaydet
            successfulTests.push({
              scenarioId: id,
              testId: bulkTestId,
              scenarioName: bulkScenario.title || bulkScenario.name || "Unnamed Scenario"
            });
          } catch (error: any) {
            // Hata durumunda başarısız senaryolara ekle
            failedScenarios.push({
              scenarioId: id,
              reason: error.message || 'Unknown error occurred'
            });
            logger.error(`Error running bulk test for scenario ${id}: ${error.message}`);
          }
        }

        // Sonuçları döndür
        return res.json({
          success: true,
          totalRequested: scenarioIds.length,
          successCount: successfulTests.length,
          failedCount: failedScenarios.length,
          successfulTests,
          failedScenarios,
          message: `Successfully started ${successfulTests.length} tests out of ${scenarioIds.length} requested scenarios`
        });

      case 'update-status':
        // Alternatif status güncelleme yolu
        if (!testId || !status) {
          return res.status(400).json({
            success: false,
            error: 'testId and status are required for update-status'
          });
        }

        let mappedStatus: TestStatus;
        switch (status) {
          case 'running':
            mappedStatus = TestStatus.RUNNING;
            break;
          case 'completed':
            mappedStatus = TestStatus.COMPLETED;
            break;
          case 'failed':
            mappedStatus = TestStatus.FAILED;
            break;
          case 'stopped':
            mappedStatus = TestStatus.STOPPED;
            break;
          default:
            return res.status(400).json({ success: false, error: 'Invalid status' });
        }

        // Create a standardized result object for the central processor
        const standardizedResult = {
          testId,
          status: mappedStatus,
          result: result || {}
        };

        // Process the result through the central result processor
        const statusUpdateSuccess = await centralResultProcessor.processResult(standardizedResult, 'API-UpdateStatus');

        if (!statusUpdateSuccess) {
          return res.status(404).json({
            success: false,
            error: `Test with ID ${testId} not found or could not be updated`
          });
        }

        return res.json({
          success: true,
          message: `Test ${testId} status updated to ${status}`
        });

      case 'stop-test':
        if (!testId) {
          return res.status(400).json({ success: false, error: 'testId is required' });
        }

        logger.info(`Test Stop API: Starting enhanced stop workflow for test ${testId}`);

        // Get test information for logging and validation
        const testToStop = await testManager.getTest(testId);
        if (!testToStop) {
          return res.status(404).json({
            success: false,
            error: `Test ${testId} not found`
          });
        }



        // Use the enhanced stop workflow from TestManager
        const stopped = await testManager.stopTest(testId);

        if (stopped) {

          return res.json({
            success: true,
            message: `Test ${testId} stopped successfully with complete cleanup`,
            testId: testId,
            previousStatus: testToStop.status,
            nodeId: testToStop.nodeId
          });
        } else {
          logger.warn(`Test Stop API: Failed to stop test ${testId}`);
          return res.json({
            success: false,
            message: `Test ${testId} could not be stopped or was not found`,
            testId: testId
          });
        }

      case 'assign-test':
        if (!testId) {
          return res.status(400).json({ success: false, error: 'testId is required' });
        }

        // Use node manager to assign and send test to an available node
        const assigned = await nodeManager.assignAndSendTest(testId, requiredCapabilities || []);

        return res.json({
          success: assigned,
          message: assigned ? 'Test assigned to node' : 'No suitable node available or assignment failed'
        });

      case 'set-max-concurrent':
        if (maxConcurrent === undefined) {
          return res.status(400).json({ success: false, error: 'maxConcurrent is required' });
        }

        // Bu özellik artık kullanılmıyor, Redis tarafından yönetiliyor
        logger.warn(`API: set-max-concurrent is deprecated - concurrency is now managed by Redis`);
        return res.json({
          success: true,
          message: `Concurrency is now managed by Redis, this setting has no effect`
        });

      case 'stop-by-scenario':
        if (!scenarioId) {
          return res.status(400).json({ success: false, error: 'scenarioId is required' });
        }

        const tests = testManager.getTestsByScenarioId(scenarioId);
        const stoppedIds: string[] = [];

        for (const test of tests) {
          if (test.status === TestStatus.QUEUED || test.status === TestStatus.RUNNING) {
            const success = await testManager.stopTest(test.id);
            if (success) {
              stoppedIds.push(test.id);
            }
          }
        }

        return res.json({
          success: true,
          stoppedCount: stoppedIds.length,
          stoppedIds,
          message: `Stopped ${stoppedIds.length} tests for scenario ${scenarioId}`
        });

      default:
        return res.status(400).json({ success: false, error: 'Unknown action' });
    }
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * PUT /api/test-hub/:testId/status
 * Update test status
 */
router.put('/:testId/status', async (req: Request, res: Response) => {
  try {
    const { testId } = req.params;
    const { status, result } = req.body;

    // Validate node key
    const nodeKeyHeader = req.headers['x-node-key'];

    if (!nodeKeyHeader || nodeKeyHeader !== process.env.NODE_SECRET_KEY) {
      logger.warn(`Unauthorized node key used for status update: ${nodeKeyHeader}`);
      return res.status(401).json({ success: false, error: 'Unauthorized' });
    }

    logger.info(`API: Updating test ${testId} status to ${status}`);

    // Create a standardized result object for the central processor
    const standardizedResult = {
      testId,
      status,
      result: result || {}
    };

    // Process the result through the central result processor
    const success = await centralResultProcessor.processResult(standardizedResult, 'API-StatusUpdate');

    if (!success) {
      return res.status(404).json({ success: false, error: `Test with ID ${testId} not found or status update failed` });
    }

    res.json({ success: true });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub/:testId/stop
 * Stop a specific test with complete cleanup workflow
 */
router.post('/:testId/stop', async (req: AuthRequest, res: Response) => {
  try {
    const { testId } = req.params;
    const userId = req.user?.id;

    // Authentication check
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required to stop tests'
      });
    }

    logger.info(`Test Stop API: Starting enhanced stop workflow for test ${testId} by user ${userId}`);

    // Get test information for logging and validation
    const test = await testManager.getTest(testId);
    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test ${testId} not found`
      });
    }

    // Authorization check - users can only stop their own tests unless they're admin
    if (test.userId !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only stop your own tests'
      });
    }

    // Use the enhanced stop workflow from TestManager
    const stopped = await testManager.stopTest(testId);

    if (stopped) {
      return res.json({
        success: true,
        message: `Test ${testId} stopped successfully with complete cleanup`,
        testId: testId,
        previousStatus: test.status,
        nodeId: test.nodeId
      });
    } else {
      logger.warn(`Test Stop API: Failed to stop test ${testId}`);
      return res.json({
        success: false,
        message: `Test ${testId} could not be stopped or was not found`,
        testId: testId
      });
    }
  } catch (error: any) {
    logger.error(`Test Stop API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * DELETE /api/test-hub/:testId
 * Stop and remove a test with complete cleanup workflow
 */
router.delete('/:testId', async (req: Request, res: Response) => {
  try {
    const { testId } = req.params;

    logger.info(`Test Delete API: Starting enhanced stop and remove workflow for test ${testId}`);

    // Get test information first
    const test = await testManager.getTest(testId);
    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test ${testId} not found`
      });
    }



    // Use the enhanced stop workflow from TestManager
    const stopped = await testManager.stopTest(testId);

    // Update scenario status if needed
    if (test.scenarioId) {
      try {
        await updateScenarioRunStatus(
          test.scenarioId,
          'stopped'
        );
        logger.info(`Test Delete API: Updated scenario ${test.scenarioId} status to stopped for test ${testId}`);
      } catch (err) {
        logger.error(`Test Delete API: Error updating scenario status: ${err}`);
      }
    }

    if (stopped) {

      res.json({
        success: true,
        message: `Test ${testId} stopped and removed with complete cleanup`,
        testId: testId,
        previousStatus: test.status,
        scenarioId: test.scenarioId
      });
    } else {
      logger.warn(`Test Delete API: Failed to stop test ${testId}`);
      res.json({
        success: false,
        message: `Test ${testId} not found or could not be removed`
      });
    }
  } catch (error: any) {
    logger.error(`Test Delete API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/by-user/:userId
 * Get all tests for a specific user
 */
router.get('/by-user/:userId', (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    // Yetkili kullanıcı kontrolü
    if (req.user?.id !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view tests for your own user ID or you need admin access'
      });
    }

    const tests = testManager.getTestsByUserId(userId);

    if (tests.length === 0) {
      return res.json({
        success: true,
        count: 0,
        tests: []
      });
    }

    // Simplify logs for API response
    const simplifiedTests = tests.map(test => {
      // Tamamlanmış testlerde gereksiz detayları kaldır
      const isCompleted = test.status === TestStatus.COMPLETED ||
                         test.status === TestStatus.FAILED ||
                         test.status === TestStatus.STOPPED;

      return {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        completedAt: test.completedAt,
        nodeId: test.nodeId,
        logs: 'logs' in test ? test.logs.slice(-20) : [], // Only last 20 logs for summary
        // Sadece gerekli sonuç alanlarını tut
        result: isCompleted && test.result ? {
          status: test.result.status,
          summary: test.result.summary || {},
          reportId: test.result.reportId || test.reportId
        } : test.result,
        reportId: test.reportId,
        userId: test.userId,
        process: null // Don't send process object
      };
    });

    res.json({
      success: true,
      count: simplifiedTests.length,
      tests: simplifiedTests
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * GET /api/test-hub/active-by-user/:userId
 * Get all active tests (running or queued) for a specific user
 */
router.get('/active-by-user/:userId', (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    // Yetkili kullanıcı kontrolü
    if (req.user?.id !== userId && req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'You can only view tests for your own user ID or you need admin access'
      });
    }

    // Tüm testleri getir ve sonra filtrele
    const tests = testManager.getTestsByUserId(userId).filter(test =>
      test.status === TestStatus.RUNNING || test.status === TestStatus.QUEUED
    );

    // Simplify logs for API response
    const simplifiedTests = tests.map(test => {
      return {
        id: test.id,
        scenarioId: test.scenarioId,
        scenarioName: test.scenarioName,
        status: test.status,
        queuedAt: test.queuedAt,
        startedAt: test.startedAt,
        nodeId: test.nodeId,
        logs: 'logs' in test ? test.logs.slice(-5) : [], // Only last few logs
        userId: test.userId
      };
    });

    res.json({
      success: true,
      count: simplifiedTests.length,
      tests: simplifiedTests
    });
  } catch (error: any) {
    logger.error(`API Error: ${error.message}`);
    res.status(500).json({ success: false, error: error.message });
  }
});

/**
 * POST /api/test-hub/:testId/report
 * Upload test report for storage
 */
router.post('/:testId/report', async (req: Request, res: Response) => {
  try {
    const { testId } = req.params;
    const { report, nodeId } = req.body;
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;

    if (!report) {
      return res.status(400).json({ success: false, error: 'Report data is required' });
    }

    // Verify that the test exists
    const test = await testManager.getTest(testId);

    // Even if test is not found (might have been completed and removed),
    // still store the report since it contains valuable data

    logger.info(`Received test report for test ${testId} from node ${nodeId}`);
    logger.info(`Report summary: ${report.summary?.total || 0} steps, ${report.summary?.passed || 0} passed, ${report.summary?.failed || 0} failed`);

    // Determine overall test report status
    let reportStatus = 'completed';
    if (report.status && typeof report.status === 'string') {
      reportStatus = report.status;
    } else {
      if (report.summary) {
        const hasFailed = report.summary.failed > 0 || report.summary.errors > 0;
        reportStatus = hasFailed ? 'failed' : 'passed';
      } else if (Array.isArray(report.steps)) {
        const hasFailedSteps = report.steps.some((step: any) => step.error || step.success === false);
        reportStatus = hasFailedSteps ? 'failed' : 'passed';
      }
    }

    // Kullanıcı ID'sini belirleme öncelik sırası:
    // 1. Oturum açmış kullanıcı (userId)
    // 2. Test nesnesi içindeki kullanıcı (test.userId)
    // 3. Raporda zaten var olan kullanıcı (report.userId)
    let reportUserId = userId;

    if (!reportUserId && test) {
      reportUserId = test.userId;
      logger.info(`Using user ID from test object: ${reportUserId}`);
    } else if (!reportUserId && report.userId) {
      reportUserId = report.userId;
      logger.info(`Using user ID from report data: ${reportUserId}`);
    }

    if (reportUserId) {
      logger.info(`Test report will be associated with user ID: ${reportUserId}`);
    } else {
      logger.warn(`No user ID found for test report. The report will be saved without user association.`);
    }

    // Create a valid TestReport object for storage
    const testReport = {
      id: report.reportId || testId,
      name: report.name || (test ? test.scenarioName : 'Unknown Test'),
      url: report.url || '',
      date: new Date().toISOString(),
      timestamp: new Date().toISOString(), // Add required timestamp field
      duration: report.duration || 0,
      status: reportStatus,
      steps: Array.isArray(report.steps) ? report.steps.map((step: any) => ({
        ...step,
        // Ensure step has a success property aligned with error property
        success: step.success === undefined ? !step.error : step.success,
        // Ensure step has a status string property
        status: step.success ? 'passed' : 'failed'
      })) : [],
      summary: report.summary || { total: 0, passed: 0, failed: 0, errors: 0 },
      scenarioId: report.scenarioId ?? test?.scenarioId,
      scenarioTitle: report.scenarioName ?? report.scenarioTitle ?? test?.scenarioName ?? report.name ?? 'Unknown Scenario Title',
      nodeId: nodeId || report.nodeId,
      userId: reportUserId, // Kullanıcı ID'sini ekle
      platform: report.platform ?? test?.platform ?? 'web',
      runId: report.runId ?? test?.runId,
      executionId: report.executionId ?? test?.executionId
    };

    // Process screenshots - keep base64 data in the report
    if (Array.isArray(testReport.steps)) {
      for (let i = 0; i < testReport.steps.length; i++) {
        const step = testReport.steps[i];

        if (step.base64Screenshot) {
          logger.info(`Keeping base64 screenshot for step ${step.id} in report`);
        } else if (!step.base64Screenshot && step.screenshot) {
          logger.warn(`Step ${step.id} has screenshot path but no base64 data`);
        }
      }
    }

    // Save to storage atomically
    const result = await createTestReportAtomic(testReport, {
      source: 'API-test-routes',
      transactionId: `api-test-report-${testId}-${Date.now()}`
    });

    if (!result.success) {
      logger.error(`Failed to save report to MongoDB atomically: ${result.message}`);
      return res.status(500).json({
        success: false,
        error: `Failed to save report: ${result.message}`
      });
    }

    logger.info(`Saved report to MongoDB with ID: ${result.reportId}, associated with user: ${reportUserId || 'unknown'}`);

    // Return the report ID
    return res.json({
      success: true,
      reportId: result.reportId,
      message: 'Report saved to MongoDB'
    });
  } catch (error: any) {
    logger.error(`Error uploading test report: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: `Failed to upload test report: ${error.message}`
    });
  }
});

/**
 * PUT /api/test-hub/:testId/debug
 * Debug endpoint to check and fix test status
 */
router.put('/:testId/debug', async (req: Request, res: Response) => {
  try {
    const { testId } = req.params;
    logger.info(`Debug request received for test ${testId}`);

    // Get test details
    const test = await testManager.getTest(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        error: `Test with ID ${testId} not found`
      });
    }

    // Get current status
    const status = test.status;
    logger.info(`Current test status: ${status}`);

    // Check if test should be marked as stopped
    let needsStatusUpdate = false;
    let newStatus = status;

    // If the test is still marked as running but the node is available,
    // it might indicate a stuck test
    if (status === 'running' && test.nodeId) {
      const node = nodeManager.getNode(test.nodeId);
      if (node && node.status === 'available') {
        logger.warn(`Test ${testId} is still marked as running but node ${test.nodeId} is available - potential stuck test`);
        newStatus = TestStatus.STOPPED;
        needsStatusUpdate = true;
      }
    }

    // Calculate elapsed time for running tests
    let elapsedMs = 0;
    let elapsedSec = 0;
    if (test.startedAt) {
      elapsedMs = Date.now() - test.startedAt.getTime();
      elapsedSec = Math.floor(elapsedMs / 1000);
    }

    // Apply fix if needed
    let fixed = false;
    if (needsStatusUpdate) {
      logger.info(`Fixing test ${testId} by updating status from ${status} to ${newStatus}`);

      if (newStatus === TestStatus.STOPPED) {
        // Use stopTest to properly clean up
        fixed = await testManager.stopTest(testId);

        // Update scenario status in storage
        if (test.scenarioId) {
          try {
            await updateScenarioRunStatus(
              test.scenarioId,
              'stopped'
            );
            logger.info(`Updated scenario ${test.scenarioId} status to stopped`);
          } catch (err) {
            logger.error(`Error updating scenario status: ${err}`);
          }
        }
      }
    }

    // Return detailed status information
    return res.json({
      success: true,
      testId: testId,
      originalStatus: status,
      needsStatusUpdate,
      newStatus: needsStatusUpdate ? newStatus : status,
      fixed,
      testDetails: {
        ...test,
        elapsedMs,
        elapsedSec,
        process: undefined  // Don't send process object
      }
    });
  } catch (error: any) {
    logger.error(`API Error in debug endpoint: ${error.message}`);
    return res.status(500).json({ success: false, error: error.message });
  }
});

export default router;