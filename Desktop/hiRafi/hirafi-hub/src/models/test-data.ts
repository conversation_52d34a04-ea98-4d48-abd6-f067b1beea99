/**
 * Test Data Models
 * Type definitions for test data management system
 */

export type VariableType = "string" | "number" | "boolean" | "date" | "email" | "phone" | "address" | "secret" | "json" | "array";

export type EnvironmentType = "development" | "testing" | "staging" | "production" | "all";

export type DataSourceType = "database" | "api" | "file" | "manual" | "csv" | "json" | "excel";

export type EnvironmentTypeEnum = "default" | "custom";

export interface ValueConstraints {
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  decimals?: number;
  pattern?: string;
  includeUppercase?: boolean;
  includeLowercase?: boolean;
  includeNumbers?: boolean;
  includeSpecial?: boolean;
  allowedValues?: string[];
  excludedValues?: string[];
  mustInclude?: string[];
  mustExclude?: string[];
  unique?: boolean;
}

export interface DataVariable {
  id: string;
  name: string;
  value: string;
  type: VariableType;
  description?: string;
  format?: string;
  constraints?: ValueConstraints;
}

export interface DataSet {
  id: string;
  name: string;
  description: string;
  tags: string[];
  environment: EnvironmentType;
  metadata?: any; // Comprehensive data structure with all environments
  teamId: string;
  companyId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface DataSource {
  id: string;
  name: string;
  type: DataSourceType;
  description: string;
  connectionString?: string;
  filePath?: string;
  variables?: Array<{ name: string; value: string }>;
  isActive: boolean;
  teamId: string;
  companyId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  config?: any; // Geçici olarak any, daha sonra detaylandırılacak
}

export interface DataEnvironment {
  id: string;
  name: string;
  description: string;
  color: string;
  type: EnvironmentTypeEnum;
  isActive: boolean;
  teamId: string;
  companyId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// New interface for variables generated by AI for the /dataset endpoint
export interface DataSetVariableFromAI {
  name: string;
  type: VariableType;
  description?: string;
  environmentValues: { [environmentId: string]: any };
  // Constraints and format are not typically generated by this specific AI prompt
}

// Request/Response DTOs
export interface CreateDataSetRequest {
  name: string;
  description: string;
  variables: DataSetVariableFromAI[];
  tags: string[];
  environment: EnvironmentType;
  metadata?: {
    ai_prompt?: string;
    [key: string]: any; // Allow other metadata properties
  };
}

export interface UpdateDataSetRequest {
  name?: string;
  description?: string;
  tags?: string[];
  environment?: EnvironmentType;
  metadata?: any; // Comprehensive data structure with all environments
  isActive?: boolean;
}

export interface CreateDataSourceRequest {
  name: string;
  type: DataSourceType;
  description: string;
  connectionString?: string;
  filePath?: string;
  variables?: Array<{ name: string; value: string }>;
  isActive?: boolean;
  config?: {
    provider: 'csv' | 'excel' | 'database' | 'api';
    mode?: 'read' | 'visual' | 'raw';
    filePath?: string;
    sheetName?: string;
    data?: Array<{ name: string; value: string; }>;
    table?: string;
    nameColumn?: string;
    valueColumn?: string;
    setupQuery?: string;
    teardownQuery?: string;
    flow?: any[];
    // API için yeni alanlar
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    url?: string;
    headers?: Array<{ key: string; value: string }>;
    body?: string; // JSON string olarak
  };
}

export interface UpdateDataSourceRequest {
  name?: string;
  type?: DataSourceType;
  description?: string;
  connectionString?: string;
  filePath?: string;
  variables?: Array<{ name: string; value: string }>;
  isActive?: boolean;
  config?: CreateDataSourceRequest['config'];
}

export interface CreateDataEnvironmentRequest {
  name: string;
  description: string;
  color: string;
  type?: EnvironmentTypeEnum;
  isActive?: boolean;
}

export interface UpdateDataEnvironmentRequest {
  name?: string;
  description?: string;
  color?: string;
  type?: EnvironmentTypeEnum;
  isActive?: boolean;
}

// Query options
export interface DataSetQueryOptions {
  limit?: number;
  skip?: number;
  search?: string;
  environment?: EnvironmentType;
  tags?: string[];
}

export interface DataSourceQueryOptions {
  limit?: number;
  skip?: number;
  search?: string;
  type?: DataSourceType;
  isActive?: boolean;
}

export interface DataEnvironmentQueryOptions {
  limit?: number;
  skip?: number;
  search?: string;
  type?: EnvironmentTypeEnum;
  isActive?: boolean;
}

// API Response types
export interface TestDataApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  total?: number;
}

export interface DataSetListResponse {
  dataSets: DataSet[];
  total: number;
}

export interface DataSetKeysResponse {
  dataSets: Array<{
    id: string;
    name: string;
    description: string;
    variables: Array<{
      name: string;
      environments: Array<{
        id: string;
        name: string;
      }>;
    }>;
  }>;
  total: number;
}

export interface DataSourceListResponse {
  dataSources: DataSource[];
  total: number;
}

export interface DataEnvironmentListResponse {
  dataEnvironments: DataEnvironment[];
  total: number;
}
