/**
 * Base Device Manager
 * Abstract base class for Android device managers
 */

import {
  IDeviceManager,
  AndroidAgent
} from './device-manager.interface.js';
import { TestRequest } from '../../../models/types.js';
import { logger } from '../../../utils/logger.js';

/**
 * Base Device Manager
 * Implements common functionality for device managers.
 */
export abstract class BaseDeviceManager implements IDeviceManager {
  protected agents: Map<string, AndroidAgent> = new Map();
  protected currentDevice: AndroidAgent | null = null;
  protected config: any = {};

  /**
   * Initialize the device manager
   * @param config Configuration object
   */
  async initialize(config: any): Promise<void> {
    this.config = config;
  }

  /**
   * Connect to a device
   * @param deviceId Optional device identifier
   * @param scenarioId Optional scenario identifier for reporting (should be scenarioId, not testId)
   * @param platformVersion Optional platform version to override default
   * @returns Promise resolving to a device agent
   */
  abstract connectToDevice(deviceId?: string, scenarioId?: string, platformVersion?: string): Promise<AndroidAgent>;

  /**
   * Connect to an Appium hub using test request configuration
   * @param testRequest Test request containing Appium configuration
   * @returns Promise resolving to a device agent
   */
  abstract connectToAppiumHub(testRequest: TestRequest): Promise<AndroidAgent>;

  /**
   * Disconnect from a device
   * @param device Device agent to disconnect from
   */
  abstract disconnectFromDevice(device: AndroidAgent): Promise<void>;

  /**
   * Disconnect from the current device
   */
  async disconnectFromCurrentDevice(): Promise<void> {
    if (this.currentDevice) {
      try {
        await this.disconnectFromDevice(this.currentDevice);
        logger.info('BaseDeviceManager: Disconnected from current device');
      } catch (error: any) {
        logger.error(`BaseDeviceManager: Error disconnecting from current device: ${error.message}`);
      } finally {
        this.currentDevice = null;
      }
    }
  }

  /**
   * Get the current device agent
   * @returns Current device agent or null if not connected
   */
  getCurrentDevice(): AndroidAgent | null {
    return this.currentDevice;
  }

  /**
   * Get a list of connected devices
   * @returns Promise resolving to a list of device identifiers
   */
  async getConnectedDevices(): Promise<string[]> {
    return Array.from(this.agents.keys());
  }

  /**
   * Create an AI agent for a device
   * @param device Device agent to create AI agent for
   * @returns Promise resolving to an Android agent
   */
  abstract createAgent(device: AndroidAgent): Promise<AndroidAgent>;

  /**
   * Get the AI agent for a device
   * @param device Device agent to get AI agent for
   * @returns Android agent or undefined if not found
   */
  getDeviceAgent(device: AndroidAgent): AndroidAgent | undefined {
    // In most implementations, the device is already an agent
    // This method is provided for consistency with the BrowserManager interface
    return device;
  }

  /**
   * Register an agent
   * @param key Key to register the agent under
   * @param agent Agent to register
   */
  protected registerAgent(key: string, agent: AndroidAgent): void {
    this.agents.set(key, agent);
    this.currentDevice = agent;
  }

  /**
   * Unregister an agent
   * @param key Key to unregister the agent for
   */
  protected unregisterAgent(key: string): void {
    this.agents.delete(key);
  }

  /**
   * Get the key for a device
   * @param device Device to get key for
   * @returns Key for the device or null if not found
   */
  protected getKeyForDevice(device: AndroidAgent): string | null {
    for (const [key, agent] of this.agents.entries()) {
      if (agent === device) {
        return key;
      }
    }
    return null;
  }
}
