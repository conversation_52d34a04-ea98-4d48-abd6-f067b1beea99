/**
 * Test Runner Factory for Test Execution Module
 *
 * Creates test runners with proper dependency injection and service integration.
 * This factory is specifically designed for the new test execution architecture.
 */

import { EventEmitter } from 'events';
import { LoggerServiceFactory } from '../../../utils/logger-service.js';
import type { ITestRunner } from '../interfaces/test-runner.interface.js';
import type { ITestProgressReporter } from '../interfaces/test-progress-reporter.interface.js';
import type { IScreenshotProvider } from '../../common/interfaces/screenshot-provider.interface.js';
import type { IVideoRecorder } from '../../common/interfaces/video-recorder.interface.js';
import type { IMetricsCollector } from '../../common/interfaces/metrics-collector.interface.js';
import type { IStepHandlerRegistry } from '../../common/interfaces/step-handler-registry.interface.js';
import { UnifiedTestRunner } from '../../common/providers/base-test-runner.js';

/**
 * Platform type for test runner creation
 */
export type Platform = 'android' | 'web';



/**
 * Pre-constructed dependencies for TestRunner creation
 * This eliminates the need for isolated DI container
 */
export interface TestRunnerDependencies {
  platformManager: any;
  screenshotProvider: IScreenshotProvider;
  videoRecorder: IVideoRecorder;
  metricsCollector: IMetricsCollector;
  stepHandlerRegistry: IStepHandlerRegistry;
  progressReporter?: ITestProgressReporter;
}

/**
 * @deprecated TestRunnerServiceRegistry has been removed in favor of main DI container integration
 * All TestRunner dependencies are now registered in ApplicationContainer
 */



/**
 * Test Runner Factory for the test execution module
 *
 * Creates test runners with proper dependency injection and integration
 * with the test execution services using the main ApplicationContainer.
 */
export class TestRunnerFactory {
  /**
   * Pure factory method: Create a test runner with pre-constructed dependencies
   * This is the preferred method as it follows pure factory pattern
   * @param dependencies Pre-constructed dependencies from main DI container
   * @param config Configuration object
   * @returns The created test runner
   */
  static createTestRunnerWithDependencies(
    dependencies: TestRunnerDependencies,
    config: any = {}
  ): ITestRunner {
    const logger = LoggerServiceFactory.createServiceLogger('TestRunnerFactory');

    logger.info('TestRunnerFactory: Creating test runner with pre-constructed dependencies');

    // Use provided progress reporter or create default
    const progressReporter = dependencies.progressReporter || new EventEmitter() as ITestProgressReporter;

    // Create the test runner with properly injected dependencies
    const testRunner = new UnifiedTestRunner(
      dependencies.platformManager,
      dependencies.screenshotProvider,
      dependencies.videoRecorder,
      dependencies.metricsCollector,
      progressReporter,
      dependencies.stepHandlerRegistry,
      config
    );

    logger.info('TestRunnerFactory: Successfully created test runner with dependency injection');
    return testRunner;
  }



  /**
   * Get supported platforms
   * @returns Array of supported platform names
   */
  static getSupportedPlatforms(): Platform[] {
    return ['android', 'web'];
  }

  /**
   * Validate platform support
   * @param platform Platform to validate
   * @returns True if platform is supported
   */
  static isPlatformSupported(platform: string): platform is Platform {
    return this.getSupportedPlatforms().includes(platform as Platform);
  }
}
