/**
 * Connector Type Definitions
 * Defines types and enums for connector system
 */

/**
 * Connector types
 */
export enum ConnectorType {
  HTTP = 'http',
  REDIS = 'redis',
  WEBSOCKET = 'websocket',
  WEBSOCKET_SHORT = 'ws'
}

/**
 * Connection status types
 */
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * Service lifecycle types
 */
export enum ServiceLifecycle {
  SINGLETON = 'singleton',
  TRANSIENT = 'transient'
}

/**
 * Service names for dependency injection
 */
export const ServiceNames = {
  // Core services
  LOGGER: 'logger',
  CONFIG: 'config',
  
  // Connector services
  HEARTBEAT_SERVICE: 'heartbeatService',
  REGISTRATION_SERVICE: 'registrationService',
  
  // HTTP services
  HTTP_REGISTRATION_SERVICE: 'httpRegistrationService',
  HTTP_HEARTBEAT_SERVICE: 'httpHeartbeatService',
  HTTP_TEST_SERVICE: 'httpTestService',
  
  // WebSocket services
  WEBSOCKET_CONNECTION_MANAGER: 'websocketConnectionManager',
  WEBSOCKET_MESSAGE_HANDLER: 'websocketMessageHandler',

  // Redis services
  REDIS_CONNECTION_MANAGER: 'redisConnectionManager',
  REDIS_CONNECTOR_SERVICE: 'redisConnectorService',

  // Connectors
  HTTP_CONNECTOR: 'httpConnector',
  REDIS_CONNECTOR: 'redisConnector',
  WEBSOCKET_CONNECTOR: 'websocketConnector',
  
  // Managers
  CONNECTOR_MANAGER: 'connectorManager'
} as const;

/**
 * Service name type
 */
export type ServiceName = typeof ServiceNames[keyof typeof ServiceNames];

/**
 * Connector configuration type
 */
export interface ConnectorConfig {
  type: ConnectorType;
  nodeId?: string;
  hubUrl?: string;
  secretKey?: string;
  clientId?: string;
  enableLogging?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  timeout?: number;
}

/**
 * Service configuration type
 */
export interface ServiceConfig {
  enableLogging?: boolean;
  hubUrl?: string;
  secretKey?: string;
  nodeId?: string;
  clientId?: string;
  [key: string]: any;
}

/**
 * Connector statistics type
 */
export interface ConnectorStats {
  connectorType: string;
  nodeId: string | null;
  isInitialized: boolean;
  isConnected: boolean;
  uptime: number;
  connectionAttempts: number;
  successfulConnections: number;
  errors: number;
  createdAt: string;
  services?: Record<string, any>;
}

/**
 * Service statistics type
 */
export interface ServiceStats {
  serviceName: string;
  isInitialized: boolean;
  createdAt: string;
  uptime: number;
  operationCount: number;
  errorCount: number;
  lastOperation?: string;
  lastError?: string;
}

/**
 * Error context type
 */
export interface ErrorContext {
  service: string;
  operation: string;
  timestamp: string;
  details?: any;
}

/**
 * Message type
 */
export interface Message {
  type: string;
  data: any;
  timestamp?: number;
  id?: string;
}

/**
 * Connection options type
 */
export interface ConnectionOptions {
  timeout?: number;
  headers?: Record<string, string>;
  protocols?: string[];
  reconnection?: {
    enabled?: boolean;
    maxAttempts?: number;
    initialDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
  };
}

/**
 * Connection statistics type
 */
export interface ConnectionStats {
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  uptime: number;
  reconnectAttempts: number;
  successfulReconnects: number;
  messagesSent: number;
  messagesReceived: number;
  bytesTransferred: number;
  averageLatency: number;
  connectedAt?: string;
  disconnectedAt?: string;
  lastPongTime?: string;
}
