# Node.js <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> en son sü<PERSON><PERSON><PERSON><PERSON> kullanıyoruz
FROM node:latest AS base

# pnpm yükle ve auto-install-peers'i etkinleştir
RUN npm install -g pnpm
# Peer bağımlılıklarını otomatik olarak yüklemek için pnpm'i yapılandır
RUN pnpm config set auto-install-peers true

# Çalışma dizinini ayarla
WORKDIR /app

# Bağımlılıkları yüklemek için gerekli dosyaları kopyala
COPY package.json pnpm-lock.yaml* .npmrc pnpm-workspace.yaml* ./

# Geliştirme aşaması
FROM base AS deps
# Debian tabanlı image için gerekli paketleri yükle
RUN apt-get update && apt-get install -y --no-install-recommends \
    libc6 \
    && rm -rf /var/lib/apt/lists/*
# Bağımlılıkları yükle
RUN pnpm install

# Der<PERSON>e aşaması
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
# Kaynak kodları kopyala (.dockerignore dosyası node_modules'ı hariç tutacak)
COPY . .

# Ortam değişkenlerini ayarla
ARG NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL}

# Uygulamayı derle
RUN pnpm run build

# Çalıştırma aşaması
FROM base AS runner
ENV NODE_ENV production

# Kullanıcı oluştur ve izinleri ayarla
RUN groupadd --system --gid 1001 nodejs
RUN useradd --system --uid 1001 --gid nodejs nextjs
USER nextjs

# Derlenen uygulamayı kopyala
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Uygulamayı çalıştır
CMD ["node", "server.js"]
