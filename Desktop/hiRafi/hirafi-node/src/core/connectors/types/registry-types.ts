/**
 * Registry Type Definitions
 * Defines types for registry patterns
 */

/**
 * Registry configuration type
 */
export interface RegistryConfig {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  maxResolutionDepth?: number;
  nodeId?: string;
  hubUrl?: string;
  secretKey?: string;
}

/**
 * Registry entry type
 */
export interface RegistryEntry<T = any> {
  id: string;
  type: string;
  instance: T;
  createdAt: string;
  lastUsed: string;
  usageCount: number;
  metadata?: Record<string, any>;
}

/**
 * Registry statistics type
 */
export interface RegistryStats {
  totalEntries: number;
  entriesByType: Record<string, number>;
  totalUsage: number;
  averageUsage: number;
  oldestEntry?: string;
  newestEntry?: string;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
}

/**
 * Registry query type
 */
export interface RegistryQuery {
  type?: string;
  id?: string;
  metadata?: Record<string, any>;
  createdAfter?: string;
  createdBefore?: string;
  usedAfter?: string;
  usedBefore?: string;
  minUsage?: number;
  maxUsage?: number;
}

/**
 * Registry event type
 */
export interface RegistryEvent {
  type: string;
  entryId: string;
  entryType: string;
  timestamp: string;
  data?: any;
}

/**
 * Registry cleanup options type
 */
export interface RegistryCleanupOptions {
  maxAge?: number;
  maxUnusedTime?: number;
  maxEntries?: number;
  minUsage?: number;
  preserveTypes?: string[];
}

/**
 * Registry backup type
 */
export interface RegistryBackup {
  timestamp: string;
  version: string;
  entries: RegistryEntry[];
  metadata: Record<string, any>;
}

/**
 * Registry restore options type
 */
export interface RegistryRestoreOptions {
  overwriteExisting?: boolean;
  validateEntries?: boolean;
  skipInvalidEntries?: boolean;
  preserveTimestamps?: boolean;
}
