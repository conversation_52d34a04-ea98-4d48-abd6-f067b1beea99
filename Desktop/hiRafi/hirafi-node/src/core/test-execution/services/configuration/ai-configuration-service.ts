/**
 * AI Configuration Service Implementation
 * 
 * Manages AI model configuration across different platforms.
 * Replaces the singleton AIConfigurationManager with a dependency-injected implementation.
 */

import { TestPlatform } from '../../../../models/platform-types.js';
import type {
  IConfigurationManager,
  AIConfiguration,
  ConfigurationResult,
  ConfigurationManagerOptions
} from '../../interfaces/configuration-manager.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

/**
 * Configuration Manager Implementation
 * 
 * Provides AI configuration management with proper platform-specific
 * configuration application and validation.
 */
export class ConfigurationManager implements IConfigurationManager {
  private logger: ILoggerService;
  private initialized = false;
  private options: ConfigurationManagerOptions = {
      enableCaching: true,
      configurationTimeout: 30000,
      fallbackToEnvironment: true
    };
  private platformConfigurations = new Map<TestPlatform, AIConfiguration>();

  constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('ConfigurationManager');
  }

  /**
   * Initialize the configuration manager
   */
  async initialize(options: ConfigurationManagerOptions = {}): Promise<void> {
    this.options = { ...this.options, ...options };
    this.platformConfigurations.clear();
    this.initialized = true;

    this.logger.debug('ConfigurationManager: Initialized with options', this.options);
  }

  /**
   * Apply AI configuration for test execution
   */
  async applyAIConfiguration(testRequest: any, platform: TestPlatform): Promise<ConfigurationResult> {
    if (!this.initialized) {
      throw new Error('ConfigurationManager not initialized');
    }

    const errors: Error[] = [];
    let appliedConfiguration: AIConfiguration = {};

    try {
      this.logger.info(`ConfigurationManager: Applying AI configuration for ${platform} platform`);

      const environmentSettings = testRequest.environmentSettings;
      if (!environmentSettings) {
        this.logger.warn('ConfigurationManager: No environment settings provided');
        return {
          success: true,
          appliedConfiguration,
          errors,
          platform
        };
      }

      // Extract AI configuration from environment settings
      let aiConfig = null;
      if (environmentSettings.aiModelConfig) {
        aiConfig = environmentSettings.aiModelConfig;
        this.logger.debug('ConfigurationManager: Using aiModelConfig from environment settings');
      } else if (environmentSettings.aiModel) {
        aiConfig = { MIDSCENE_MODEL_NAME: environmentSettings.aiModel };
        this.logger.debug(`ConfigurationManager: Using aiModel string: ${environmentSettings.aiModel}`);
      } else {
        this.logger.warn('ConfigurationManager: No AI configuration found in environment settings');
        return {
          success: true,
          appliedConfiguration,
          errors,
          platform
        };
      }

      // Build enhanced configuration
      appliedConfiguration = this.buildConfiguration(aiConfig);

      // Apply the configuration based on platform
      await this.applyPlatformConfiguration(appliedConfiguration, platform);

      // Cache the configuration if enabled
      if (this.options.enableCaching) {
        this.platformConfigurations.set(platform, appliedConfiguration);
      }

      this.logger.info(`ConfigurationManager: Successfully applied AI configuration for ${platform}`);

      return {
        success: true,
        appliedConfiguration,
        errors,
        platform
      };

    } catch (error: any) {
      this.logger.error(`ConfigurationManager: Error applying AI configuration: ${error.message}`);
      errors.push(error);

      return {
        success: false,
        appliedConfiguration,
        errors,
        platform
      };
    }
  }

  /**
   * Build configuration with model-specific settings
   */
  buildConfiguration(baseConfig: any): AIConfiguration {
    if (!baseConfig) {
      return {};
    }

    const config: AIConfiguration = {
      OPENAI_BASE_URL: baseConfig.OPENAI_BASE_URL,
      OPENAI_API_KEY: baseConfig.OPENAI_API_KEY,
      MIDSCENE_MODEL_NAME: baseConfig.MIDSCENE_MODEL_NAME,
      MIDSCENE_CACHE: '1' // Always enable caching
    };

    // Add model-specific flags based on model name
    if (baseConfig.MIDSCENE_MODEL_NAME) {
      const modelName = baseConfig.MIDSCENE_MODEL_NAME.toLowerCase();

      if (modelName.includes('tars') || modelName.includes('ui-tars')) {
        config.MIDSCENE_USE_VLM_UI_TARS = '1.5';
        this.logger.info('ConfigurationManager: Detected TARS model');
      }

      if (modelName.includes('qwen') && modelName.includes('vl')) {
        config.MIDSCENE_USE_QWEN_VL = '1';
        this.logger.info('ConfigurationManager: Detected Qwen VL model');
      }

      if (modelName.includes('gemini') && modelName.includes('pro')) {
        config.MIDSCENE_USE_GEMINI = '1';
        this.logger.info('ConfigurationManager: Detected Gemini Pro model');
      }
    }

    return config;
  }

  /**
   * Apply configuration for web platform
   */
  async applyWebConfiguration(config: AIConfiguration): Promise<void> {
    try {
      const { overrideAIConfig } = await import('rfi-ai-web/puppeteer');
      overrideAIConfig(config);
      this.logger.info(`ConfigurationManager: Web configuration applied with model: ${config.MIDSCENE_MODEL_NAME || 'default'}`);
    } catch (importError: any) {
      this.logger.error(`ConfigurationManager: Error importing overrideAIConfig for web, using fallback: ${importError.message}`);
      
      if (this.options.fallbackToEnvironment) {
        this.applyEnvironmentConfiguration(config);
      } else {
        throw importError;
      }
    }
  }

  /**
   * Apply configuration for Android platform
   */
  async applyAndroidConfiguration(config: AIConfiguration): Promise<void> {
    try {
      const { overrideAIConfig } = await import('rfi-ai-android');
      overrideAIConfig(config);
      this.logger.info(`ConfigurationManager: Android configuration applied with model: ${config.MIDSCENE_MODEL_NAME || 'default'}`);
    } catch (importError: any) {
      this.logger.warn(`ConfigurationManager: Android overrideAIConfig not available, using environment variables: ${importError.message}`);
      
      if (this.options.fallbackToEnvironment) {
        this.applyEnvironmentConfiguration(config);
      } else {
        throw importError;
      }
    }
  }

  /**
   * Apply configuration using environment variables (fallback)
   */
  applyEnvironmentConfiguration(config: AIConfiguration): void {
    try {
      Object.keys(config).forEach(key => {
        if (config[key]) {
          process.env[key] = config[key];
        }
      });
      this.logger.info(`ConfigurationManager: Environment configuration applied with model: ${config.MIDSCENE_MODEL_NAME || 'default'}`);
    } catch (error: any) {
      this.logger.error(`ConfigurationManager: Error applying environment configuration: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get current configuration for a platform
   */
  getCurrentConfiguration(platform: TestPlatform): AIConfiguration | null {
    return this.platformConfigurations.get(platform) || null;
  }

  /**
   * Validate configuration object
   */
  validateConfiguration(config: any): boolean {
    if (!config || typeof config !== 'object') {
      return false;
    }

    // Basic validation - at least one configuration property should be present
    const validKeys = ['OPENAI_BASE_URL', 'OPENAI_API_KEY', 'MIDSCENE_MODEL_NAME'];
    return validKeys.some(key => config[key]);
  }

  /**
   * Reset configuration for a platform
   */
  async resetConfiguration(platform?: TestPlatform): Promise<void> {
    if (platform) {
      this.platformConfigurations.delete(platform);
      this.logger.info(`ConfigurationManager: Reset configuration for ${platform}`);
    } else {
      this.platformConfigurations.clear();
      this.logger.info('ConfigurationManager: Reset all platform configurations');
    }
  }

  /**
   * Check if configuration manager is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Dispose of the configuration manager
   */
  async dispose(): Promise<void> {
    this.platformConfigurations.clear();
    this.initialized = false;
    this.logger.debug('ConfigurationManager: Disposed');
  }

  /**
   * Apply platform-specific configuration
   */
  private async applyPlatformConfiguration(config: AIConfiguration, platform: TestPlatform): Promise<void> {
    // Log safe configuration (without sensitive data)
    const safeConfig = {
      OPENAI_BASE_URL: config.OPENAI_BASE_URL ? '***' : undefined,
      OPENAI_API_KEY: config.OPENAI_API_KEY ? '***' : undefined,
      MIDSCENE_MODEL_NAME: config.MIDSCENE_MODEL_NAME,
      MIDSCENE_CACHE: config.MIDSCENE_CACHE
    };
    this.logger.info(`ConfigurationManager: Applying configuration: ${JSON.stringify(safeConfig)}`);

    switch (platform) {
      case TestPlatform.WEB:
        await this.applyWebConfiguration(config);
        break;
      case TestPlatform.ANDROID:
        await this.applyAndroidConfiguration(config);
        break;
      default:
        this.logger.warn(`ConfigurationManager: Unknown platform ${platform}, using environment configuration`);
        this.applyEnvironmentConfiguration(config);
    }
  }
}
