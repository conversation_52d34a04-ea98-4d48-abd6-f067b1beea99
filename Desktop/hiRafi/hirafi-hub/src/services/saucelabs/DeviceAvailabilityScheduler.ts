/**
 * SauceLabs Device Availability Scheduler
 * Manages periodic tasks for checking device availability
 */

import { logger } from '../../utils/logger.js';

// Track active intervals for cleanup
const activeIntervals: NodeJS.Timeout[] = [];

/**
 * Initialize the device availability scheduler
 */
export async function initializeDeviceAvailabilityScheduler(): Promise<void> {
  try {
    logger.info('DeviceAvailabilityScheduler: Initializing');

    // Set up periodic jobs
    setupPeriodicJobs();

    logger.info('DeviceAvailabilityScheduler: Initialized successfully');
  } catch (error) {
    logger.error(`DeviceAvailabilityScheduler: Error initializing: ${error}`);
    throw error;
  }
}

/**
 * Set up periodic jobs
 */
function setupPeriodicJobs(): void {
  // This periodic job is no longer needed as BullMQ handles retries
  // We'll keep a simple log message for monitoring
  const deviceCheckInterval = setInterval(() => {
    logger.info('DeviceAvailabilityScheduler: BullMQ is now handling device availability retries');
  }, 30 * 60 * 1000); // 30 minutes

  activeIntervals.push(deviceCheckInterval);

  logger.info('DeviceAvailabilityScheduler: Set up monitoring job for BullMQ retry system');
}

/**
 * Shutdown the device availability scheduler
 */
export async function shutdownDeviceAvailabilityScheduler(): Promise<void> {
  try {
    logger.info('DeviceAvailabilityScheduler: Shutting down');

    // Clear all active intervals
    activeIntervals.forEach(interval => clearInterval(interval));
    activeIntervals.length = 0;

    logger.info('DeviceAvailabilityScheduler: Shutdown complete');
  } catch (error) {
    logger.error(`DeviceAvailabilityScheduler: Error during shutdown: ${error}`);
    throw error;
  }
}

// Export the scheduler functions
export default {
  initializeDeviceAvailabilityScheduler,
  shutdownDeviceAvailabilityScheduler
};
