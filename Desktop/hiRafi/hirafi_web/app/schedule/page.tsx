"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Clock, Plus, Search, Repeat, CalendarIcon, Edit, Trash2, MoreHorizontal, Loader2, AlertTriangle } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useToast } from "@/components/ui/use-toast"
import { useSchedules } from "@/hooks/useSchedules"
import { format } from "date-fns"
import { RouteGuard } from "@/components/route-guard"
import { PermissionGuard } from "@/components/permission-guard"
import {
  AlertD<PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

// Fallback mock data for scheduled tests (will be replaced with real data)
const mockScheduledTests = [
  {
    id: "sch-001",
    name: "Daily Smoke Tests",
    description: "Run smoke tests every morning to verify core functionality",
    scheduleType: "daily",
    startDate: "2025-03-28",
    startTime: "08:00",
    endDate: "",
    endTime: "",
    environment: "production",
    browser: "chrome",
    device: "desktop",
    status: "active",
    lastRun: "2025-03-27 08:00",
    nextRun: "2025-03-28 08:00",
    scenarioCount: 5,
    createdBy: "John Doe",
    createdAt: "2025-03-20",
  },
  {
    id: "sch-002",
    name: "Weekly Regression Suite",
    description: "Full regression test suite that runs every weekend",
    scheduleType: "weekly",
    startDate: "2025-03-29",
    startTime: "22:00",
    endDate: "",
    endTime: "",
    environment: "staging",
    browser: "chrome",
    device: "desktop",
    status: "active",
    lastRun: "2025-03-22 22:00",
    nextRun: "2025-03-29 22:00",
    scenarioCount: 120,
    createdBy: "Jane Smith",
    createdAt: "2025-02-15",
  },
  {
    id: "sch-003",
    name: "Monthly Performance Tests",
    description: "Performance benchmark tests that run on the first day of each month",
    scheduleType: "monthly",
    startDate: "2025-04-01",
    startTime: "03:00",
    endDate: "",
    endTime: "",
    environment: "production",
    browser: "chrome",
    device: "desktop",
    status: "active",
    lastRun: "2025-03-01 03:00",
    nextRun: "2025-04-01 03:00",
    scenarioCount: 15,
    createdBy: "System",
    createdAt: "2025-01-10",
  },
  {
    id: "sch-004",
    name: "Pre-release Testing",
    description: "One-time test run before the upcoming release",
    scheduleType: "once",
    startDate: "2025-04-05",
    startTime: "09:00",
    endDate: "",
    endTime: "",
    environment: "staging",
    browser: "firefox",
    device: "desktop",
    status: "scheduled",
    lastRun: "Never",
    nextRun: "2025-04-05 09:00",
    scenarioCount: 85,
    createdBy: "John Doe",
    createdAt: "2025-03-25",
  },
  {
    id: "sch-005",
    name: "Mobile App Tests",
    description: "Tests for the mobile app that run every weekday",
    scheduleType: "daily",
    startDate: "2025-03-28",
    startTime: "10:00",
    endDate: "2025-04-30",
    endTime: "10:00",
    environment: "testing",
    browser: "chrome",
    device: "mobile",
    status: "paused",
    lastRun: "2025-03-27 10:00",
    nextRun: "Paused",
    scenarioCount: 42,
    createdBy: "Jane Smith",
    createdAt: "2025-03-15",
  },
]

export default function SchedulePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [sortBy, setSortBy] = useState("createdAt")
  const [isLoading, setIsLoading] = useState(true)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [scheduleToDelete, setScheduleToDelete] = useState<string | null>(null)

  // Use the schedules hook
  const {
    schedules,
    loading,
    error,
    fetchSchedules,
    updateScheduleStatus,
    deleteSchedule
  } = useSchedules()

  // Fetch schedules on component mount
  useEffect(() => {
    const loadSchedules = async () => {
      setIsLoading(true)
      await fetchSchedules()
      setIsLoading(false)
    }

    loadSchedules()
  }, [fetchSchedules])

  // Handle pause/resume schedule
  const handleToggleStatus = async (e: React.MouseEvent, scheduleId: string, currentStatus: string) => {
    e.stopPropagation()

    const newStatus = currentStatus === 'active' ? 'paused' : 'active'
    const result = await updateScheduleStatus(scheduleId, newStatus as any)

    if (result.success) {
      // Refresh schedules after status change
      fetchSchedules()
    }
  }

  // Handle delete schedule dialog
  const handleDeleteClick = (e: React.MouseEvent, scheduleId: string) => {
    e.stopPropagation()
    setScheduleToDelete(scheduleId)
    setDeleteDialogOpen(true)
  }

  // Handle delete schedule confirmation
  const handleDeleteConfirm = async () => {
    if (scheduleToDelete) {
      setIsLoading(true)
      const result = await deleteSchedule(scheduleToDelete)

      if (result.success) {
        toast({
          title: "Schedule deleted",
          description: "The schedule has been deleted successfully",
        })
        // Refresh schedules after deletion
        fetchSchedules()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete schedule",
          variant: "destructive",
        })
      }
      setIsLoading(false)
      setScheduleToDelete(null)
    }
  }

  // Filter and sort scheduled tests
  const filteredSchedules = schedules
    .filter((schedule) => {
      // Apply search filter
      if (searchQuery && !schedule.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }

      // Apply status filter
      if (statusFilter !== "all" && schedule.status !== statusFilter) {
        return false
      }

      // Apply type filter
      if (typeFilter !== "all" && schedule.scheduleType !== typeFilter) {
        return false
      }

      return true
    })
    .sort((a, b) => {
      // Apply sorting
      if (sortBy === "nextRun") {
        if (!a.nextRunAt) return 1
        if (!b.nextRunAt) return -1
        return new Date(a.nextRunAt).getTime() - new Date(b.nextRunAt).getTime()
      } else if (sortBy === "name") {
        return a.name.localeCompare(b.name)
      } else if (sortBy === "createdAt") {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      }
      return 0
    })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 border-0">
            Active
          </Badge>
        )
      case "paused":
        return (
          <Badge className="bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 border-0">
            Paused
          </Badge>
        )
      case "scheduled":
        return (
          <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 border-0">Scheduled</Badge>
        )
      default:
        return (
          <Badge className="bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300 border-0">Unknown</Badge>
        )
    }
  }

  const getScheduleTypeIcon = (type: string) => {
    switch (type) {
      case "once":
        return <Calendar className="h-4 w-4 text-purple-500" />
      case "daily":
        return <Clock className="h-4 w-4 text-blue-500" />
      case "weekly":
        return <Repeat className="h-4 w-4 text-indigo-500" />
      case "monthly":
        return <CalendarIcon className="h-4 w-4 text-emerald-500" />
      default:
        return <Calendar className="h-4 w-4 text-gray-500" />
    }
  }

  // Bir sonraki koşuma kalan süreyi hesaplayan fonksiyon
  const getTimeRemaining = (nextRunDate: string | undefined) => {
    if (!nextRunDate) return null

    const now = new Date()
    const nextRun = new Date(nextRunDate)

    // Eğer geçmiş bir tarihse null döndür
    if (nextRun <= now) return null

    const diffMs = nextRun.getTime() - now.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))

    if (diffDays > 0) {
      return `${diffDays}d ${diffHours}h`
    } else if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`
    } else {
      return `${diffMinutes}m`
    }
  }

  return (
    <RouteGuard>
      <PermissionGuard resource="Schedule" action="view">
        <div className="flex h-screen bg-white dark:bg-gray-950">
        {/* Sidebar */}
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Scheduled Tests</h1>
              <p className="text-gray-500 dark:text-gray-400">Manage your scheduled test runs</p>
            </div>

            <Button
              className="gap-1 bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 border-none"
              onClick={() => router.push("/schedule/new")}
            >
              <Plus className="h-4 w-4" />
              New Schedule
            </Button>
          </div>

          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800/30 rounded-lg border border-gray-100 dark:border-gray-800">
            <div className="flex flex-col sm:flex-row gap-3 justify-between">
              <div className="relative flex-grow max-w-md">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search schedules..."
                  className="pl-8 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex flex-wrap gap-2 items-center">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500 dark:text-gray-400 whitespace-nowrap">Status:</span>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[130px] h-9 bg-white dark:bg-gray-900">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="paused">Paused</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500 dark:text-gray-400 whitespace-nowrap">Sort by:</span>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-[130px] h-9 bg-white dark:bg-gray-900">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="nextRun">Next Run</SelectItem>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="createdAt">Created Date</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500 dark:text-gray-400 whitespace-nowrap">Type:</span>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[130px] h-9 bg-white dark:bg-gray-900">
                      <SelectValue placeholder="Schedule type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="once">Once</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="flex-1 p-6 overflow-auto bg-gray-50 dark:bg-gray-950">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Loader2 className="h-8 w-8 text-indigo-500 animate-spin mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Loading schedules...</h3>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredSchedules.map((schedule) => (
                <Card
                  key={schedule.id}
                  className="overflow-hidden hover:shadow-md transition-shadow border-0 shadow-sm"
                >
                  <CardContent className="p-0">
                    <div className="flex flex-col sm:flex-row border-l-4 overflow-hidden"
                      style={{
                        borderLeftColor:
                          schedule.status === "active" ? "#10b981" : schedule.status === "paused" ? "#f59e0b" : "#3b82f6",
                      }}
                    >
                      {/* Left section with schedule info */}
                      <div className="flex-1 p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="p-1.5 rounded-md bg-gray-50 dark:bg-gray-800/50">
                            {getScheduleTypeIcon(schedule.scheduleType)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium text-gray-900 dark:text-white">{schedule.name}</h3>
                              {getStatusBadge(schedule.status)}
                            </div>
                            <p className="text-gray-500 dark:text-gray-400 text-xs line-clamp-1 mt-0.5">
                              {schedule.description || "No description"}
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-x-6 gap-y-2 mt-3">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-indigo-400"></div>
                            <div>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Type</p>
                              <p className="text-sm font-medium capitalize">{schedule.scheduleType.toLowerCase()}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-emerald-400"></div>
                            <div>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Next Run</p>
                              <div className="flex items-center gap-1.5">
                                <p className="text-sm font-medium">
                                  {schedule.status === 'paused'
                                    ? 'Paused'
                                    : schedule.nextRunAt
                                      ? format(new Date(schedule.nextRunAt), 'dd MMM HH:mm')
                                      : 'Not scheduled'}
                                </p>
                                {schedule.status !== 'paused' && schedule.nextRunAt && getTimeRemaining(schedule.nextRunAt) && (
                                  <Badge variant="outline" className="text-xs py-0 h-5 bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800">
                                    in {getTimeRemaining(schedule.nextRunAt)}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                            <div>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Run</p>
                              <p className="text-sm font-medium line-clamp-1">
                                {schedule.runs && schedule.runs.length > 0
                                  ? schedule.runs[0].name
                                  : (schedule.run?.name || 'Unknown')}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-purple-400"></div>
                            <div>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Scenarios</p>
                              <p className="text-sm font-medium">
                                {schedule.runs && schedule.runs.length > 0
                                  ? schedule.runs[0].scenarioCount
                                  : (schedule.run?.scenarioCount || 0)}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="mt-3 pt-2 border-t border-gray-100 dark:border-gray-800 text-xs text-gray-500 dark:text-gray-400">
                          Last run: {schedule.lastRunAt
                            ? format(new Date(schedule.lastRunAt), 'dd MMM yyyy HH:mm')
                            : 'Never'}
                        </div>
                      </div>

                      {/* Right section with actions */}
                      <div className="flex sm:flex-col justify-between items-center p-3 sm:p-4 bg-gray-50 dark:bg-gray-800/30 sm:border-l border-gray-100 dark:border-gray-800">
                        <div className="flex sm:flex-col gap-2 items-center">
                          {schedule.status === "active" ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 px-3 justify-center rounded-full shadow-sm border-amber-200 text-amber-600 hover:bg-amber-50 hover:text-amber-700 dark:border-amber-800 dark:text-amber-400 dark:hover:bg-amber-900/30"
                              onClick={(e) => handleToggleStatus(e, schedule.id, schedule.status)}
                            >
                              Pause
                            </Button>
                          ) : schedule.status === "paused" ? (
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 px-3 justify-center rounded-full shadow-sm border-emerald-200 text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700 dark:border-emerald-800 dark:text-emerald-400 dark:hover:bg-emerald-900/30"
                              onClick={(e) => handleToggleStatus(e, schedule.id, schedule.status)}
                            >
                              Resume
                            </Button>
                          ) : null}

                          <Button
                            size="sm"
                            className="h-8 px-3 justify-center rounded-full shadow-sm bg-indigo-50 text-indigo-600 border border-indigo-200 hover:bg-indigo-100 hover:text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400 dark:border-indigo-800 dark:hover:bg-indigo-900/50"
                            onClick={(e) => {
                              e.stopPropagation()
                              router.push(`/schedule/edit/${schedule.id}`)
                            }}
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Edit
                          </Button>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreHorizontal className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                            <DropdownMenuItem onClick={(e) => handleDeleteClick(e, schedule.id)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {!isLoading && filteredSchedules.length === 0 && (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-full mb-4">
                    <Calendar className="h-6 w-6 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">No schedules found</h3>
                  <p className="text-gray-500 dark:text-gray-400 max-w-md">
                    {searchQuery || statusFilter !== "all"
                      ? "Try adjusting your search terms or filters."
                      : "You haven't created any scheduled tests yet."}
                  </p>
                  <Button
                    className="mt-4 gap-1 bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 border-none"
                    onClick={() => router.push("/schedule/new")}
                  >
                    <Plus className="h-4 w-4" />
                    Create Your First Schedule
                  </Button>
                </div>
              )}
            </div>
          )}
        </main>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Delete Schedule
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this schedule? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
    </PermissionGuard>
    </RouteGuard>
  )
}
