"use client";

import { useState, use<PERSON>emo, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/lib/utils/toast-utils";
import {
  Search,
  TestTube,
  Loader2,
  X,
  CheckCircle2,
  Check,
  AlertCircle,
  Filter,
  Folder,
  ListChecks,
  LayoutGrid,
  FileText,
  LoaderCircle,
} from "lucide-react";
import { ScenarioFormData } from "@/models/scenario";
import { TestManagementProvider } from "@/store/testManagementStore";
import { useUnifiedTestManagementContext } from "@/contexts/UnifiedTestManagementContext";
import { getProviderFormFieldName } from "@/lib/utils/test-management-utils";

interface TestManagementTabProps {
  scenarioData: ScenarioFormData;
  handleTestCaseSelection: (
    caseIds: string[],
    provider: TestManagementProvider,
  ) => void;
  handleSyncToggle?: (enabled: boolean) => void;
}

export function TestManagementTab({
  scenarioData,
  handleTestCaseSelection: originalHandleTestCaseSelection,
  handleSyncToggle: originalHandleSyncToggle,
}: TestManagementTabProps) {
  const { store, fetchAvailableProviders, fetchSuitesOrFolders, fetchTestCases } = useUnifiedTestManagementContext();

  // Extract values from store for easier access
  const {
    activeProvider,
    availableProviders,
    projects,
    suitesOrFolders,
    testCases,
    selectedCases,
    filters: { selectedProject, selectedSuiteOrFolder, searchTerm },
    loading: { isLoading, isLoadingSuites, isLoadingCases, isLoadingMore },
    providers,
  } = store;

  // Derived values
  const isConnected = activeProvider ? providers[activeProvider]?.isConnected || false : false;
  const displayedCases = testCases; // For now, use all test cases
  const hasMoreCases = false; // Will be implemented with pagination

  // Actions from store
  const switchProvider = store.switchProvider;
  const setSearchTerm = store.setSearchTerm;
  const setSelectedCases = store.setSelectedCases;
  const resetFilters = store.resetFilters;

  // Provider display name helper
  const getProviderDisplayName = (provider: TestManagementProvider) => {
    return provider === 'testrail' ? 'TestRail' : 'Zephyr Scale';
  };

  // Enhanced functions with proper test case fetching
  const handleProjectChange = async (projectId: string) => {
    console.log(`[TestManagementTab] Project changed to: ${projectId}`);
    store.setSelectedProject(projectId);
    store.setSelectedSuiteOrFolder('all'); // Reset folder selection

    // Fetch suites/folders for the new project
    if (projectId !== 'all') {
      await fetchSuitesOrFolders(projectId);
    }

    // Fetch test cases for the project
    await fetchTestCases(projectId);
  };

  const handleSuiteOrFolderChange = async (id: string) => {
    console.log(`[TestManagementTab] Folder/Suite changed to: ${id}`);
    store.setSelectedSuiteOrFolder(id);

    // Fetch test cases for the selected folder/suite
    const projectId = selectedProject || 'all';
    const suiteOrFolderId = id === 'all' ? undefined : id;

    console.log(`[TestManagementTab] Fetching test cases for project: ${projectId}, folder/suite: ${suiteOrFolderId}`);
    await fetchTestCases(projectId, suiteOrFolderId);
  };
  const handleTestCaseSelection = (caseId: string, isSelected: boolean) => {
    store.toggleCaseSelection(caseId);
  };
  const loadMoreCases = () => {
    // Will be implemented with pagination
  };
  const ensureProviderInitialized = async (provider: TestManagementProvider) => {
    await store.initializeProvider(provider);
  };
  const initializedProviders = store.initializedProviders;

  // Local state for view mode
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");

  // Note: Available providers are fetched by the TestManagementProvider context
  // No need to fetch them again here

  // Initialize active provider when component mounts (lazy loading)
  useEffect(() => {
    if (activeProvider && !initializedProviders.has(activeProvider)) {
      ensureProviderInitialized(activeProvider);
    }
  }, [activeProvider]); // Only depend on activeProvider

  // Fetch suites/folders when provider is connected
  useEffect(() => {
    if (activeProvider && isConnected) {
      console.log(`[TestManagementTab] Fetching suites/folders for provider: ${activeProvider}`);
      fetchSuitesOrFolders('all');
    }
  }, [activeProvider, isConnected, fetchSuitesOrFolders]);

  // Fetch initial test cases when provider is connected and has projects
  useEffect(() => {
    if (activeProvider && isConnected && projects.length > 0) {
      console.log(`[TestManagementTab] Fetching initial test cases for provider: ${activeProvider}`);
      const projectId = selectedProject || (projects.length > 0 ? projects[0].id : 'all');
      fetchTestCases(projectId);
    }
  }, [activeProvider, isConnected, projects, selectedProject, fetchTestCases]);

  // Handle test case selection with provider information
  const handleTestCaseSelectionWithProvider = (
    caseId: string,
    isSelected: boolean,
  ) => {
    handleTestCaseSelection(caseId, isSelected);

    // Update parent component with selected cases and provider
    if (activeProvider) {
      const updatedCases = isSelected
        ? [...selectedCases, caseId]
        : selectedCases.filter((id) => id !== caseId);

      originalHandleTestCaseSelection(updatedCases, activeProvider);
    }
  };

  // Get the appropriate field name for selected cases based on provider
  const getSelectedCasesFieldName = () => {
    return activeProvider
      ? getProviderFormFieldName(activeProvider)
      : "selectedTestCases";
  };

  // Get selected cases from scenario data
  const scenarioSelectedCases = useMemo(() => {
    const fieldName = getSelectedCasesFieldName();
    return (scenarioData as any)[fieldName] || [];
  }, [scenarioData, activeProvider]);

  // Sync selected cases with scenario data
  useEffect(() => {
    if (activeProvider && scenarioSelectedCases.length > 0) {
      setSelectedCases(scenarioSelectedCases);
    }
  }, [scenarioSelectedCases, activeProvider, setSelectedCases]);

  // Debug: Log available providers (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('[TestManagementTab] Render state:', {
      availableProviders,
      activeProvider,
      isConnected,
      isLoading,
      availableProvidersLength: availableProviders.length
    });
  }

  // Show loading state while providers are being fetched
  if (isLoading && availableProviders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
        <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-6 mb-6">
          <TestTube className="h-12 w-12 text-gray-400 animate-pulse" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          Loading Test Management Integrations...
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
          Checking for configured test management tools.
        </p>
      </div>
    );
  }

  // No providers available
  if (availableProviders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
        <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-6 mb-6">
          <TestTube className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          No Test Management Integration
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
          To use test management integration, you need to configure at least one
          test management tool first.
        </p>
        <div className="flex gap-3">
          <Button
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => (window.location.href = "/plugins/testrail")}
          >
            Configure TestRail
          </Button>
          <Button
            size="lg"
            variant="outline"
            onClick={() => (window.location.href = "/plugins/zephyrscale")}
          >
            Configure Zephyr Scale
          </Button>
        </div>
      </div>
    );
  }

  // Provider selection (when multiple providers are available)
  if (availableProviders.length > 1 && !activeProvider) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
        <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-6 mb-6">
          <TestTube className="h-12 w-12 text-blue-500" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          Choose Test Management Provider
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
          You have multiple test management tools configured. Please select
          which one to use for this scenario.
        </p>
        <div className="flex gap-3">
          {availableProviders.map((provider) => (
            <Button
              key={provider}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => switchProvider(provider)}
            >
              Use {getProviderDisplayName(provider)}
            </Button>
          ))}
        </div>
      </div>
    );
  }

  // Show loading state if provider is being initialized
  if (activeProvider && !initializedProviders.has(activeProvider)) {
    return (
      <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
        <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-6 mb-6">
          <LoaderCircle className="h-12 w-12 text-blue-500 animate-spin" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          Initializing {getProviderDisplayName(activeProvider)}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
          Please wait while we load your test management integration...
        </p>
      </div>
    );
  }

  // Main integration interface
  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-6">
      {/* Header with provider selection */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6">
        <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
          <TestTube className="h-6 w-6 text-blue-500" />
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Test Management Integration
            </h3>
            {availableProviders.length > 1 && activeProvider && (
              <Select value={activeProvider} onValueChange={switchProvider}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableProviders.map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      {getProviderDisplayName(provider)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {activeProvider ? (
              <>
                Connected to {getProviderDisplayName(activeProvider)} • Select
                test cases to link with this scenario
              </>
            ) : (
              "Loading test management integration..."
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-green-600 dark:text-green-400 font-medium">
              Connected
            </span>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {/* Project Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
            <Folder className="h-3.5 w-3.5" />
            Project
          </Label>
          <Select
            value={selectedProject}
            onValueChange={handleProjectChange}
            disabled={isLoadingSuites}
          >
            <SelectTrigger className="h-9 text-sm border-blue-200 dark:border-blue-800/50 focus:border-blue-400 focus:ring-blue-400 bg-white/90 dark:bg-gray-900/60">
              <SelectValue placeholder="Select Project" />
            </SelectTrigger>
            <SelectContent className="max-h-[200px]">
              {projects.map((project: any) => (
                <SelectItem key={project.id} value={project.id.toString()}>
                  <div className="flex items-center">
                    <Folder className="h-3.5 w-3.5 mr-2 text-blue-500 flex-shrink-0" />
                    <span className="truncate">{project.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Suite/Folder Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
            <ListChecks className="h-3.5 w-3.5" />
            {activeProvider === "testrail" ? "Suite" : "Folder"}
          </Label>
          <Select
            value={selectedSuiteOrFolder}
            onValueChange={handleSuiteOrFolderChange}
            disabled={suitesOrFolders.length === 0 || isLoadingSuites}
          >
            <SelectTrigger className="h-9 text-sm border-blue-200 dark:border-blue-800/50 focus:border-blue-400 focus:ring-blue-400 bg-white/90 dark:bg-gray-900/60">
              <SelectValue
                placeholder={
                  isLoadingSuites
                    ? `Loading ${activeProvider === "testrail" ? "suites" : "folders"}...`
                    : suitesOrFolders.length > 0
                      ? `Select ${activeProvider === "testrail" ? "Suite" : "Folder"}`
                      : `No ${activeProvider === "testrail" ? "suites" : "folders"} available`
                }
              />
            </SelectTrigger>
            <SelectContent className="max-h-[200px]">
              {isLoadingSuites ? (
                <SelectItem value="loading" disabled>
                  <div className="flex items-center text-blue-600">
                    <LoaderCircle className="h-3.5 w-3.5 mr-2 flex-shrink-0 animate-spin" />
                    <span>Loading...</span>
                  </div>
                </SelectItem>
              ) : (
                <>
                  <SelectItem value="all">
                    <div className="flex items-center">
                      <LayoutGrid className="h-3.5 w-3.5 mr-2 text-gray-500 flex-shrink-0" />
                      <span>
                        All{" "}
                        {activeProvider === "testrail" ? "Suites" : "Folders"}
                      </span>
                    </div>
                  </SelectItem>
                  {suitesOrFolders.map((item: any) => (
                    <SelectItem key={item.id} value={item.id.toString()}>
                      <div className="flex items-center">
                        <ListChecks className="h-3.5 w-3.5 mr-2 text-blue-500 flex-shrink-0" />
                        <span className="truncate">{item.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Search */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1">
            <Search className="h-3.5 w-3.5" />
            Search Test Cases
          </Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by name, ID, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9 text-sm border-blue-200 dark:border-blue-800/50 focus:border-blue-400 focus:ring-blue-400 bg-white/90 dark:bg-gray-900/60"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                onClick={() => setSearchTerm("")}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* View Mode Toggle */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {testCases.length} test case{testCases.length !== 1 ? "s" : ""}{" "}
            found
          </span>
          {selectedCases.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {selectedCases.length} selected
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1 border rounded-md p-1">
          <Button
            variant={viewMode === "list" ? "default" : "ghost"}
            size="sm"
            className="h-7 px-2"
            onClick={() => setViewMode("list")}
          >
            <ListChecks className="h-3.5 w-3.5" />
          </Button>
          <Button
            variant={viewMode === "grid" ? "default" : "ghost"}
            size="sm"
            className="h-7 px-2"
            onClick={() => setViewMode("grid")}
          >
            <LayoutGrid className="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>

      {/* Test Cases Display */}
      {isLoadingCases ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-3" />
            <p className="text-gray-600 dark:text-gray-400">
              Loading test cases...
            </p>
          </div>
        </div>
      ) : testCases.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Test Cases Found
          </h4>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {searchTerm
              ? `No test cases match your search "${searchTerm}"`
              : `No test cases available in the selected ${activeProvider === "testrail" ? "suite" : "folder"}`}
          </p>
          {searchTerm && (
            <Button variant="outline" onClick={() => setSearchTerm("")}>
              Clear Search
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {/* Test Cases List/Grid */}
          <div
            className={
              viewMode === "grid"
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                : "space-y-2"
            }
          >
            {displayedCases.map((testCase) => (
              <TestCaseCard
                key={testCase.id}
                testCase={testCase}
                isSelected={selectedCases.includes(testCase.id)}
                onSelectionChange={(isSelected) =>
                  handleTestCaseSelectionWithProvider(testCase.id, isSelected)
                }
                viewMode={viewMode}
                provider={activeProvider!}
              />
            ))}
          </div>

          {/* Load More Button */}
          {hasMoreCases && (
            <div className="text-center pt-4">
              <Button
                variant="outline"
                onClick={loadMoreCases}
                disabled={isLoadingMore}
                className="min-w-[120px]"
              >
                {isLoadingMore ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Load More"
                )}
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Selected Cases Summary */}
      {scenarioSelectedCases && scenarioSelectedCases.length > 0 && (
        <div className="mt-4 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/10 border border-blue-100 dark:border-blue-800/20">
          <div className="flex items-center gap-2 mb-2">
            <Check className="h-4 w-4 text-blue-500" />
            <h4 className="font-medium text-gray-800 dark:text-gray-200 text-sm">
              Selected Test Cases ({scenarioSelectedCases.length})
            </h4>
          </div>
          <div className="flex flex-wrap gap-2">
            {scenarioSelectedCases.map((caseId: string) => {
              const testCase = testCases.find((tc) => tc.id === caseId);
              return (
                <div
                  key={caseId}
                  className="inline-flex items-center gap-1 bg-white dark:bg-gray-800 px-2 py-1 rounded-full text-xs border border-blue-200 dark:border-blue-800"
                >
                  <span className="truncate max-w-[200px]">
                    {testCase ? testCase.name : `Case ${caseId}`}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-red-100 dark:hover:bg-red-900/20"
                    onClick={() =>
                      handleTestCaseSelectionWithProvider(caseId, false)
                    }
                  >
                    <X className="h-3 w-3 text-red-500" />
                  </Button>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

// Test Case Card Component
interface TestCaseCardProps {
  testCase: any;
  isSelected: boolean;
  onSelectionChange: (isSelected: boolean) => void;
  viewMode: "grid" | "list";
  provider: TestManagementProvider;
}

function TestCaseCard({
  testCase,
  isSelected,
  onSelectionChange,
  viewMode,
  provider,
}: TestCaseCardProps) {
  if (viewMode === "grid") {
    return (
      <Card
        className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
          isSelected
            ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/10"
            : "hover:bg-gray-50 dark:hover:bg-gray-800/50"
        }`}
        onClick={() => onSelectionChange(!isSelected)}
      >
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-1">
              {isSelected ? (
                <CheckCircle2 className="h-5 w-5 text-blue-500" />
              ) : (
                <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-gray-900 dark:text-white text-sm mb-1 truncate">
                {testCase.name}
              </h4>
              <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 mb-2">
                <span>ID: {testCase.id}</span>
                {testCase.priority && (
                  <>
                    <span>•</span>
                    <span>{testCase.priority}</span>
                  </>
                )}
              </div>
              {testCase.description && (
                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
                  {testCase.description}
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div
      className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
        isSelected
          ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800"
          : "border-gray-200 dark:border-gray-700"
      }`}
      onClick={() => onSelectionChange(!isSelected)}
    >
      <div className="flex-shrink-0">
        {isSelected ? (
          <CheckCircle2 className="h-5 w-5 text-blue-500" />
        ) : (
          <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
        )}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-medium text-gray-900 dark:text-white text-sm truncate">
            {testCase.name}
          </h4>
          <Badge variant="outline" className="text-xs">
            {testCase.id}
          </Badge>
        </div>
        <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
          {testCase.priority && <span>{testCase.priority}</span>}
          {provider === "testrail" && testCase.suite && (
            <>
              <span>•</span>
              <span>Suite: {testCase.suite}</span>
            </>
          )}
          {provider === "zephyrscale" && testCase.folder && (
            <>
              <span>•</span>
              <span>Folder: {testCase.folder}</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
