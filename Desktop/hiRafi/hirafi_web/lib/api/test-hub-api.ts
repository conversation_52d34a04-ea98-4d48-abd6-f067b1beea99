/**
 * Test Hub API
 *
 * Test Hub ile ilgili API isteklerini yönetir.
 */

import { get, post, del } from './fetch-wrapper';
import { TEST_HUB_ENDPOINTS } from './constants';

/**
 * Aktif testleri getirir
 */
export async function getActiveTests() {
  return get(TEST_HUB_ENDPOINTS.ACTIVE_TESTS);
}

/**
 * Belirli bir testin detaylarını getirir
 *
 * @param testId Test ID'si
 */
export async function getTestDetails(testId: string) {
  return get(TEST_HUB_ENDPOINTS.TEST_DETAILS(testId));
}

/**
 * Bir testi başlatır
 *
 * @param scenarioId Senaryo ID'si
 * @param userId Kullanıcı ID'si (opsiyonel)
 * @param platform Test platformu (web veya android)
 * @param environmentSettings Çevre ayarları
 */
export async function startTest(
  scenarioId: string,
  userId?: string,
  platform?: 'web' | 'android',
  environmentSettings?: any
) {
  return post(TEST_HUB_ENDPOINTS.START_TEST, {
    action: 'add-test',
    scenarioId,
    userId,
    platform,
    environmentSettings
  });
}

/**
 * Bir testi durdurur
 *
 * @param testId Test ID'si
 */
export async function stopTest(testId: string) {
  return post(TEST_HUB_ENDPOINTS.STOP_TEST(testId), {});
}

/**
 * Toplu test işlemleri yapar
 *
 * @param action İşlem ('add-test', 'stop-test', vb.)
 * @param testIds Test ID'leri
 */
export async function bulkTestOperation(action: string, testIds: string[]) {
  return post(TEST_HUB_ENDPOINTS.BULK_TESTS, {
    action,
    testIds
  });
}
