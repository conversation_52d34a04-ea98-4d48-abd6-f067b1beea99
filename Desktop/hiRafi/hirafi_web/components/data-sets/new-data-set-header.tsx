"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Save, Loader2 } from "lucide-react"

interface NewDataSetHeaderProps {
  onBack: () => void
  onSave: () => void
  isSaving: boolean
  canSave: boolean
}

export function NewDataSetHeader({ onBack, onSave, isSaving, canSave }: NewDataSetHeaderProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Use a safe disabled state that's consistent between SSR and client
  // During SSR, always disable the button to prevent hydration mismatch
  const isDisabled = !isClient || isSaving || !canSave

  return (
    <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="icon" className="rounded-full text-white hover:bg-white/20" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Create New Data Set</h1>
            <p className="text-white/80">Define your test data variables and generate data with AI</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="gap-1 bg-white/10 text-white border-white/20 hover:bg-white/20 hover:text-white"
            onClick={onBack}
          >
            Cancel
          </Button>
          <Button
            onClick={onSave}
            disabled={isDisabled}
            className="gap-1 bg-white text-indigo-600 hover:bg-white/90 border-none"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Save Data Set
              </>
            )}
          </Button>
        </div>
      </div>
    </header>
  )
}
