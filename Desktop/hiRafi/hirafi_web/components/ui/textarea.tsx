import * as React from "react"

import { cn } from "@/lib/utils"

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(({ className, value, ...props }, ref) => {
  // Ensure value is always defined to prevent controlled/uncontrolled input warnings
  // If value is undefined, we don't pass it at all to keep the textarea uncontrolled
  const textareaProps = value === undefined ? props : { ...props, value };

  return (
    <textarea
      className={cn(
        "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      ref={ref}
      {...textareaProps}
    />
  )
})
Textarea.displayName = "Textarea"

export { Textarea }

