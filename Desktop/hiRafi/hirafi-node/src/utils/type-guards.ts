/**
 * Type Guards Utility
 * 
 * This file contains type guard functions for safely checking types
 * across the codebase, particularly for discriminated union types.
 */

import { EnvironmentSettings } from '../models/types.js';
import { AndroidEnvironmentSettings } from '../models/android-environment.js';

/**
 * Type guard to check if environment settings are for Android platform
 * @param settings Environment settings to check
 * @returns True if settings are for Android platform
 */
export function isAndroidEnvironmentSettings(
  settings: EnvironmentSettings | undefined
): settings is AndroidEnvironmentSettings {
  return !!settings && settings.platform === 'android';
}

/**
 * Type guard to check if environment settings are for Web platform
 * @param settings Environment settings to check
 * @returns True if settings are for Web platform
 */
export function isWebEnvironmentSettings(
  settings: EnvironmentSettings | undefined
): settings is Exclude<EnvironmentSettings, AndroidEnvironmentSettings> {
  return !!settings && settings.platform === 'web';
}
