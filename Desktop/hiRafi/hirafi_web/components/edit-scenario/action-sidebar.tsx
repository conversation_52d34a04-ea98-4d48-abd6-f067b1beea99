"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2, Save, Trash2 } from "lucide-react"
import { useRouter } from "next/navigation"

interface ActionSidebarProps {
  onSave: () => Promise<void>
  isLoading: boolean
}

export function ActionSidebar({ onSave, isLoading }: ActionSidebarProps) {
  const router = useRouter()

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <Button
            className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 border-none rounded-full"
            onClick={onSave}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>

          <Button 
            variant="outline" 
            className="w-full rounded-full" 
            onClick={() => router.push("/scenarios")}
          >
            Cancel
          </Button>

          <Button 
            variant="destructive" 
            className="w-full rounded-full"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Scenario
          </Button>
        </CardContent>
      </Card>
    </div>
  )
} 