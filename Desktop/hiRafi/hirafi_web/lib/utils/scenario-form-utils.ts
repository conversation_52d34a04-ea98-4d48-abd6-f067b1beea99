/**
 * Unified Scenario Form Utilities
 * 
 * Shared utilities for scenario creation and editing to ensure consistency
 */

import { ScenarioFormData, Scenario } from '@/models/scenario'
import { TestManagementProvider } from '@/store/testManagementStore'

/**
 * Extended form data interface that includes creation-specific fields
 */
export interface ExtendedScenarioFormData extends ScenarioFormData {
  // AI generation fields
  aiPrompt?: string
  useAIForDetails?: boolean
  
  // Editing-specific fields
  id?: string
}

/**
 * Validation error interface
 */
export interface ValidationErrors {
  [key: string]: string
}

/**
 * Validation state interface
 */
export interface ValidationState {
  isValid: boolean
  errors: ValidationErrors
  touched: { [key: string]: boolean }
}

/**
 * UI state interface shared between creation and editing
 */
export interface ScenarioUIState {
  isLoading: boolean
  isSaving: boolean
  isGenerating?: boolean
  showPreview?: boolean
  sidebarCollapsed: boolean
  activeTab?: string
  currentStep?: number
  scenarioError?: string | null
}

// TestManagementState removed - now using unified test management store

/**
 * Folder management state interface
 */
export interface FolderState {
  newFolderOpen: boolean
  newFolderName: string
  newFolderColor: string
  isCreatingFolder: boolean
}

/**
 * Default form data factory
 */
export function createDefaultFormData(isEditing = false): ExtendedScenarioFormData {
  return {
    name: '',
    description: '',
    tags: [],
    folderId: 'none',
    platform: 'web',
    steps: [],
    testrailCases: [],
    testrailSync: false,
    zephyrscaleCases: [],
    zephyrscaleSync: false,
    newTag: '',
    // Creation-specific fields
    ...(isEditing ? {} : {
      aiPrompt: '',
      useAIForDetails: false,
    })
  }
}

/**
 * Default validation state factory
 */
export function createDefaultValidationState(): ValidationState {
  return {
    isValid: false,
    errors: {},
    touched: {}
  }
}

/**
 * Default UI state factory
 */
export function createDefaultUIState(isEditing = false): ScenarioUIState {
  return {
    isLoading: false,
    isSaving: false,
    sidebarCollapsed: false,
    ...(isEditing ? {
      activeTab: 'details',
      scenarioError: null
    } : {
      isGenerating: false,
      showPreview: false,
      currentStep: 0
    })
  }
}

// createDefaultTestManagementState removed - now using unified test management store

/**
 * Default folder state factory
 */
export function createDefaultFolderState(): FolderState {
  return {
    newFolderOpen: false,
    newFolderName: '',
    newFolderColor: 'blue',
    isCreatingFolder: false
  }
}

/**
 * Validate a form field
 */
export function validateField(field: string, value: any, formData: ExtendedScenarioFormData): string | null {
  switch (field) {
    case 'name':
      // AI powered details aktifse name validation'ını skip et
      if (formData.useAIForDetails) {
        return null;
      }
      if (!value || value.trim().length === 0) {
        return 'Scenario name is required'
      }
      if (value.trim().length < 3) {
        return 'Scenario name must be at least 3 characters'
      }
      return null
      
    case 'steps':
      if (!Array.isArray(value) || value.length === 0) {
        return 'At least one test step is required'
      }
      return null
      
    case 'platform':
      if (!value) {
        return 'Platform selection is required'
      }
      return null
      
    default:
      return null
  }
}

/**
 * Validate entire form
 */
export function validateForm(formData: ExtendedScenarioFormData): ValidationState {
  const errors: ValidationErrors = {}
  // AI powered details aktifse name field'ını validation'dan çıkar
  const fields = formData.useAIForDetails ? ['steps', 'platform'] : ['name', 'steps', 'platform']
  
  fields.forEach(field => {
    const error = validateField(field, formData[field as keyof ExtendedScenarioFormData], formData)
    if (error) {
      errors[field] = error
    }
  })
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    touched: fields.reduce((acc, field) => ({ ...acc, [field]: true }), {})
  }
}

/**
 * AI method types that need transformation from legacy format
 */
const AI_METHOD_TYPES = [
  'aiAssertion',
  'aiAction',
  'aiWaitElement',
  'aiTap',
  'aiHover',
  'aiBoolean',
  'aiQuery',
  'aiString',
  'aiNumber',
  'aiLocate',
  'aiRightClick',
  'aiScroll',
  'aiKeyboardPress',
  'aiInput'
] as const;

/**
 * Transform legacy step format to new format for backward compatibility
 * Converts old 'value' field to new 'prompt' field for AI methods and 'url' field for goto steps
 */
function transformLegacySteps(steps: any[]): any[] {
  return steps.map(step => {
    const transformedStep = { ...step };

    // Handle goto steps: value -> url
    if (step.type === 'goto') {
      if (step.value && !transformedStep.url) {
        transformedStep.url = step.value;
      }
      return transformedStep;
    }

    // Handle AI methods
    if (!AI_METHOD_TYPES.includes(step.type)) {
      return transformedStep;
    }

    // Skip if already has prompt field (already in new format)
    if (step.prompt) {
      return transformedStep;
    }

    if (step.type === 'aiInput') {
      // For aiInput: value stays as text input, name/description becomes target
      if (step.value && !transformedStep.target) {
        // Keep value as is for text input
        // Use name or description as target if target is missing
        if (step.name && step.name !== step.value) {
          transformedStep.target = step.name;
        } else if (step.description && step.description !== step.value) {
          transformedStep.target = step.description;
        }
      }
    } else {
      // For other AI methods: value becomes prompt
      if (step.value && !transformedStep.prompt) {
        transformedStep.prompt = step.value;
      } else if (step.name && !transformedStep.prompt) {
        transformedStep.prompt = step.name;
      } else if (step.description && !transformedStep.prompt) {
        transformedStep.prompt = step.description;
      }
    }

    return transformedStep;
  });
}

/**
 * Map API scenario to form data (for editing)
 */
export function mapApiScenarioToFormData(apiScenario: Scenario): ExtendedScenarioFormData & { id: string } {
  return {
    id: apiScenario.id,
    name: apiScenario.name || (apiScenario as any).title || '',
    description: apiScenario.description || '',
    tags: [...(apiScenario.tags || [])],
    steps: transformLegacySteps([...(apiScenario.steps || [])]),
    folderId: apiScenario.folderId || 'none',
    platform: (apiScenario as any).platform || 'web',
    testrailCases: (apiScenario.testrailIntegration?.caseIds || []).map(id => String(id)),
    testrailSync: apiScenario.testrailIntegration?.sync !== undefined ? apiScenario.testrailIntegration.sync : true,
    zephyrscaleCases: ((apiScenario as any).zephyrscaleIntegration?.caseIds || []).map((id: any) => String(id)),
    zephyrscaleSync: (apiScenario as any).zephyrscaleIntegration?.sync !== undefined ? (apiScenario as any).zephyrscaleIntegration.sync : false,
    newTag: ''
  }
}

/**
 * Map form data to API scenario (for saving)
 */
export function mapFormDataToApiScenario(formData: ExtendedScenarioFormData): Partial<Scenario> {
  return {
    ...(formData.id ? { id: formData.id } : {}),
    name: formData.name || 'Untitled Scenario',
    title: formData.name || 'Untitled Scenario',
    description: formData.description,
    tags: formData.tags,
    steps: formData.steps,
    ...(formData.platform ? { platform: formData.platform } : {}),
    folderId: formData.folderId && formData.folderId !== 'none' ? formData.folderId : undefined,
    testrailIntegration: formData.testrailCases.length > 0 ? {
      caseIds: formData.testrailCases,
      sync: formData.testrailSync
    } : undefined,
    zephyrscaleIntegration: formData.zephyrscaleCases?.length > 0 ? {
      caseIds: formData.zephyrscaleCases,
      sync: formData.zephyrscaleSync || false
    } : undefined
  }
}

/**
 * Get active test management provider from form data
 */
export function getActiveProviderFromFormData(formData: ExtendedScenarioFormData): TestManagementProvider | null {
  if (formData.testrailCases?.length > 0) {
    return 'testrail'
  }
  if (formData.zephyrscaleCases?.length > 0) {
    return 'zephyrscale'
  }
  return null
}

/**
 * Clear all test management selections
 */
export function clearTestManagementSelections(formData: ExtendedScenarioFormData): ExtendedScenarioFormData {
  return {
    ...formData,
    testrailCases: [],
    testrailSync: false,
    zephyrscaleCases: [],
    zephyrscaleSync: false
  }
}

/**
 * Clear test management selections for a specific provider
 */
export function clearProviderSelections(formData: ExtendedScenarioFormData, provider: TestManagementProvider): ExtendedScenarioFormData {
  switch (provider) {
    case 'testrail':
      return {
        ...formData,
        testrailCases: [],
        testrailSync: false
      }
    case 'zephyrscale':
      return {
        ...formData,
        zephyrscaleCases: [],
        zephyrscaleSync: false
      }
    default:
      return formData
  }
}

/**
 * Set test cases for a specific provider
 */
export function setProviderTestCases(
  formData: ExtendedScenarioFormData,
  provider: TestManagementProvider,
  caseIds: string[],
  sync: boolean = true
): ExtendedScenarioFormData {
  switch (provider) {
    case 'testrail':
      return {
        ...formData,
        testrailCases: caseIds,
        testrailSync: sync
      }
    case 'zephyrscale':
      return {
        ...formData,
        zephyrscaleCases: caseIds,
        zephyrscaleSync: sync
      }
    default:
      return formData
  }
}

/**
 * Toggle sync for a specific provider
 */
export function toggleProviderSync(formData: ExtendedScenarioFormData, provider: TestManagementProvider): ExtendedScenarioFormData {
  switch (provider) {
    case 'testrail':
      return {
        ...formData,
        testrailSync: !formData.testrailSync
      }
    case 'zephyrscale':
      return {
        ...formData,
        zephyrscaleSync: !formData.zephyrscaleSync
      }
    default:
      return formData
  }
}

/**
 * Check if any test management integration is configured
 */
export function hasAnyTestManagementIntegration(formData: ExtendedScenarioFormData): boolean {
  return (formData.testrailCases?.length > 0) || (formData.zephyrscaleCases?.length > 0)
}

/**
 * Check if any test management sync is enabled
 */
export function hasAnyTestManagementSync(formData: ExtendedScenarioFormData): boolean {
  return (formData.testrailSync && formData.testrailCases?.length > 0) ||
         (formData.zephyrscaleSync && formData.zephyrscaleCases?.length > 0)
}

/**
 * Get total test case count across all providers
 */
export function getTotalTestCaseCount(formData: ExtendedScenarioFormData): number {
  return (formData.testrailCases?.length || 0) + (formData.zephyrscaleCases?.length || 0)
}
