"use client"

import { useState, useTransition, useCallback } from "react"
import { useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { ProtectedRoute } from "@/components/protected-route"
import { PermissionGuard } from "@/components/permission-guard"
import { useScenarioStoreContext } from "@/hooks/useStoreContext"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { TableIcon, Database, Globe, CheckCircle2, XCircle, Lock } from "lucide-react"

// Components
import { TestDataHeader } from "@/components/test-data/test-data-header"
import { SearchFilterBar } from "@/components/test-data/search-filter-bar"
import { DataSetsTable } from "@/components/test-data/data-sets-table"
import { DataSourcesTable } from "@/components/test-data/data-sources-table"
import { EnvironmentsTable } from "@/components/test-data/environments-table"
import { EnvironmentDialogs } from "@/components/test-data/environment-dialogs"

// Hooks and utilities
import { useTestData } from "@/hooks/useTestData"
import { decryptValue, isEncrypted } from "@/lib/encryption"

// Types and constants
import type { EnvironmentType, CustomEnvironment, DataVariable } from "@/types/test-data"
import { colorOptions } from "@/lib/test-data-constants"

function TestDataPageContent() {
  // Store context management for company changes
  useScenarioStoreContext()
  
  const router = useRouter()
  const { toast } = useToast()
  const [isPending, startTransition] = useTransition()

  // UI State
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [activeTab, setActiveTab] = useState("data-sets")
  const [searchQuery, setSearchQuery] = useState("")
  const [environmentFilter, setEnvironmentFilter] = useState<EnvironmentType>("all")

  // Dialog states
  const [newEnvironmentDialogOpen, setNewEnvironmentDialogOpen] = useState(false)
  const [editEnvironmentDialogOpen, setEditEnvironmentDialogOpen] = useState(false)
  const [connectionTestDialogOpen, setConnectionTestDialogOpen] = useState(false)
  const [encryptionDialogOpen, setEncryptionDialogOpen] = useState(false)

  // Selected items
  const [selectedEnvironment, setSelectedEnvironment] = useState<CustomEnvironment | null>(null)
  const [variableToEncrypt, setVariableToEncrypt] = useState<{ dataSetId: string; variable: DataVariable } | null>(null)
  const [connectionTestResult, setConnectionTestResult] = useState<any>(null)

  // New environment form
  const [newEnvironment, setNewEnvironment] = useState<Partial<CustomEnvironment>>({
    name: "",
    description: "",
    color: "blue",
    type: "custom",
    isActive: true,
  })

  // Use custom hook for data management
  const {
    dataSources,
    dataSets,
    customEnvironments,
    isLoadingDataSets,
    isLoadingDataSources,
    isLoadingEnvironments,
    isUpdatingEnvironment,
    dataSetError,
    dataSourceError,
    environmentError,
    loadDataSets,
    loadDataSources,
    loadEnvironments,
    handleAddEnvironment,
    handleEditEnvironment,
    handleDeleteEnvironment,
    handleToggleEnvironmentStatus,
    handleDeleteDataSource,
    toggleDataSourceStatus,
    handleDeleteDataSet,
    toggleEncryption,
    getFilteredDataSets,
    getFilteredDataSources,
    getFilteredEnvironments,
  } = useTestData()

  // Navigation handlers

  const handleNewDataSource = useCallback(() => {
    router.push("/test-data/sources/new")
  }, [router])

  const handleNewDataSet = useCallback(() => {
    router.push("/test-data/sets/new")
  }, [router])

  const handleEditDataSet = useCallback((id: string) => {
    router.push(`/test-data/sets/${id}/edit`)
  }, [router])

  const handleEditDataSource = useCallback((id: string) => {
    router.push(`/test-data/sources/${id}/edit`);
  }, [router]);

  // Environment dialog handlers
  const handleCreateEnvironment = useCallback(async () => {
    const success = await handleAddEnvironment(newEnvironment);
    if (success) {
      setNewEnvironment({
        name: "",
        description: "",
        color: "blue",
        type: "custom",
        isActive: true,
      });
      setNewEnvironmentDialogOpen(false);
    }
  }, [newEnvironment, handleAddEnvironment]);

  const handleUpdateEnvironment = useCallback(async () => {
    if (selectedEnvironment) {
      const success = await handleEditEnvironment(selectedEnvironment);
      if (success) {
        setEditEnvironmentDialogOpen(false);
        setSelectedEnvironment(null);
      }
    }
  }, [selectedEnvironment, handleEditEnvironment]);

  const handleEditEnvironmentClick = useCallback((environment: CustomEnvironment) => {
    setSelectedEnvironment(environment);
    setEditEnvironmentDialogOpen(true);
  }, []);

  // Encryption dialog handlers
  const openEncryptionDialog = useCallback((dataSetId: string, variable: DataVariable) => {
    setVariableToEncrypt({ dataSetId, variable })
    setEncryptionDialogOpen(true)
  }, [])

  const handleToggleEncryption = useCallback(() => {
    if (variableToEncrypt) {
      toggleEncryption(variableToEncrypt.dataSetId, variableToEncrypt.variable.id)
      setEncryptionDialogOpen(false)
      setVariableToEncrypt(null)
    }
  }, [variableToEncrypt, toggleEncryption])

  // Get filtered data
  const filteredDataSets = getFilteredDataSets(searchQuery, environmentFilter)
  const filteredDataSources = getFilteredDataSources(searchQuery)
  const filteredEnvironments = getFilteredEnvironments(searchQuery)

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <TestDataHeader />

        <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
          <div className="max-w-7xl mx-auto">
            <SearchFilterBar
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              activeTab={activeTab}
              environmentFilter={environmentFilter}
              onEnvironmentFilterChange={setEnvironmentFilter}
              customEnvironments={customEnvironments}
              onNewEnvironment={() => setNewEnvironmentDialogOpen(true)}
              onNewDataSource={handleNewDataSource}
              onNewDataSet={handleNewDataSet}
            />

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList className="grid w-full grid-cols-3 max-w-lg">
                <TabsTrigger value="data-sets" className="flex items-center gap-2">
                  <TableIcon className="h-4 w-4" />
                  <span>Data Sets</span>
                </TabsTrigger>
                <TabsTrigger value="data-sources" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  <span>Data Sources</span>
                </TabsTrigger>
                <TabsTrigger value="environments" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  <span>Environments</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="data-sets" className="space-y-4">
                <DataSetsTable
                  dataSets={filteredDataSets}
                  customEnvironments={customEnvironments}
                  onNewDataSet={handleNewDataSet}
                  onEditDataSet={handleEditDataSet}
                  onDeleteDataSet={handleDeleteDataSet}
                  isLoading={isLoadingDataSets}
                  error={dataSetError}
                  onRetry={() => loadDataSets()}
                />
              </TabsContent>

              <TabsContent value="data-sources" className="space-y-4">
                <DataSourcesTable
                  dataSources={filteredDataSources}
                  onNewDataSource={handleNewDataSource}
                  onEditDataSource={handleEditDataSource}
                  onDeleteDataSource={handleDeleteDataSource}
                  onToggleDataSourceStatus={toggleDataSourceStatus}
                  onTestConnection={() => {}}
                  isLoading={isLoadingDataSources}
                  error={dataSourceError}
                  onRetry={() => loadDataSources()}
                />
              </TabsContent>

              <TabsContent value="environments" className="space-y-4">
                <EnvironmentsTable
                  environments={filteredEnvironments}
                  onNewEnvironment={() => setNewEnvironmentDialogOpen(true)}
                  onEditEnvironment={handleEditEnvironmentClick}
                  onDeleteEnvironment={handleDeleteEnvironment}
                  onToggleEnvironmentStatus={handleToggleEnvironmentStatus}
                  isLoading={isLoadingEnvironments}
                  isUpdatingEnvironment={isUpdatingEnvironment}
                  error={environmentError}
                  onRetry={() => loadEnvironments()}
                />
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>

      <Toaster />

      {/* Environment Dialogs */}
      <EnvironmentDialogs
        newEnvironmentDialogOpen={newEnvironmentDialogOpen}
        onNewEnvironmentDialogOpenChange={setNewEnvironmentDialogOpen}
        newEnvironment={newEnvironment}
        onNewEnvironmentChange={setNewEnvironment}
        onCreateEnvironment={handleCreateEnvironment}
        editEnvironmentDialogOpen={editEnvironmentDialogOpen}
        onEditEnvironmentDialogOpenChange={setEditEnvironmentDialogOpen}
        selectedEnvironment={selectedEnvironment}
        onSelectedEnvironmentChange={setSelectedEnvironment}
        onUpdateEnvironment={handleUpdateEnvironment}
        colorOptions={colorOptions}
      />

      {/* Connection Test Dialog */}
      <Dialog open={connectionTestDialogOpen} onOpenChange={setConnectionTestDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {connectionTestResult?.success ? (
                <>
                  <CheckCircle2 className="h-5 w-5 text-emerald-500" />
                  <span>Connection Successful</span>
                </>
              ) : (
                <>
                  <XCircle className="h-5 w-5 text-red-500" />
                  <span>Connection Failed</span>
                </>
              )}
            </DialogTitle>
            <DialogDescription>{connectionTestResult?.message}</DialogDescription>
          </DialogHeader>

          {connectionTestResult?.success && connectionTestResult?.details && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-1">Database Type</h4>
                  <p className="text-sm">{connectionTestResult.details.databaseType}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Version</h4>
                  <p className="text-sm">{connectionTestResult.details.version}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Connection Time</h4>
                  <p className="text-sm">{connectionTestResult.details.connectionTime}ms</p>
                </div>
              </div>

              {connectionTestResult.details.tables && connectionTestResult.details.tables.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Available Tables</h4>
                  <div className="bg-gray-50 dark:bg-gray-900 p-3 rounded-md max-h-[200px] overflow-y-auto">
                    <ul className="space-y-1">
                      {connectionTestResult.details.tables.map((table: string) => (
                        <li key={table} className="text-sm flex items-center gap-2">
                          <TableIcon className="h-3.5 w-3.5 text-gray-500" />
                          {table}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setConnectionTestDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Encryption Dialog */}
      <Dialog open={encryptionDialogOpen} onOpenChange={setEncryptionDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-indigo-500" />
              <span>Variable Encryption</span>
            </DialogTitle>
            <DialogDescription>Encrypt sensitive variables to protect confidential information.</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 text-sm text-amber-800">
              <p className="font-medium mb-1">About Encryption</p>
              <p>
                Encrypted values are stored securely in the database and exported files. Encrypted values can only be
                viewed by authorized users.
              </p>
            </div>

            {variableToEncrypt && (
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium">Variable Name</label>
                  <div className="mt-1 font-medium">{variableToEncrypt.variable.name}</div>
                </div>

                <div>
                  <label className="text-sm font-medium">Current Value</label>
                  <div className="mt-1 bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm font-mono">
                    {variableToEncrypt.variable.type === "secret" && isEncrypted(variableToEncrypt.variable.value)
                      ? decryptValue(variableToEncrypt.variable.value)
                      : variableToEncrypt.variable.value}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Encryption Status</label>
                  <div className="mt-1 flex items-center gap-2">
                    {variableToEncrypt.variable.type === "secret" && isEncrypted(variableToEncrypt.variable.value) ? (
                      <>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Encrypted
                        </span>
                        <span className="text-sm text-gray-500">This value is currently encrypted.</span>
                      </>
                    ) : (
                      <>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Not Encrypted
                        </span>
                        <span className="text-sm text-gray-500">This value is currently not encrypted.</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setEncryptionDialogOpen(false)}>
              Cancel
            </Button>
            {variableToEncrypt && (
              <Button onClick={handleToggleEncryption}>
                {variableToEncrypt.variable.type === "secret" && isEncrypted(variableToEncrypt.variable.value)
                  ? "Remove Encryption"
                  : "Encrypt"}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default function TestDataPage() {
  return (
    <ProtectedRoute>
      <PermissionGuard resource="TestData" action="view">
        <TestDataPageContent />
      </PermissionGuard>
    </ProtectedRoute>
  )
}
