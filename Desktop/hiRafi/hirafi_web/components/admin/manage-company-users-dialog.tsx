"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { adminApi } from "@/lib/api"
import { ApiResponse } from "@/lib/api/fetch-wrapper"
import { ArrowLeft, ArrowRight, Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ManageCompanyUsersDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  companyId: string
  companyName: string
  onSuccess?: () => void
}

export function ManageCompanyUsersDialog({
  open,
  onOpenChange,
  companyId,
  companyName,
  onSuccess
}: ManageCompanyUsersDialogProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [companyUsers, setCompanyUsers] = useState<any[]>([])
  const [availableUsers, setAvailableUsers] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  // Kullanıcıları getiren fonksiyon
  const fetchUsers = async () => {
    if (!open) return

    setIsLoading(true)
    try {
      // Şirkete ait kullanıcıları getir
      const companyResponse = await adminApi.getCompanyUsers(companyId)

      // Debug log to see the actual response
      console.log('Company users response:', companyResponse)

      if (companyResponse.success && companyResponse.users) {
        setCompanyUsers(companyResponse.users)
      } else {
        setCompanyUsers([])
        const errorMessage = companyResponse.error || "Unknown error occurred while fetching company users"
        console.error("Failed to fetch company users:", errorMessage)
      }

      // Şirkete ait olmayan kullanıcıları getir
      const allUsersResponse = await adminApi.getUsers({
        page: 1,
        limit: 1000
      })

      // Debug log to see the actual response
      console.log('All users response:', allUsersResponse)

      if (allUsersResponse.success && allUsersResponse.users) {
        // Şirkete ait olmayan kullanıcıları filtrele
        const nonCompanyUsers = allUsersResponse.users.filter(
          (user: any) => !user.companyId || user.companyId !== companyId
        )
        setAvailableUsers(nonCompanyUsers)
      } else {
        setAvailableUsers([])
        const errorMessage = allUsersResponse.error || "Unknown error occurred while fetching available users"
        console.error("Failed to fetch available users:", errorMessage)
      }
    } catch (error) {
      console.error("Error fetching users:", error)
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Kullanıcıları yükle
  useEffect(() => {
    if (open) {
      fetchUsers()
    }
  }, [open, companyId])

  // Kullanıcıyı şirkete ekle
  const addUserToCompany = async (userId: string) => {
    setIsProcessing(true)
    try {
      // Önce şirket bilgilerini getir (takım ID'sini almak için)
      const companyResponse = await adminApi.getCompanyById(companyId)

      // Debug log to see the actual response
      console.log('Company info response:', companyResponse)

      if (!companyResponse.success || !companyResponse.company) {
        throw new Error("Failed to fetch company information")
      }

      // Şirketin takım ID'sini al
      const teamId = companyResponse.company.teamId

      if (!teamId) {
        throw new Error("Company has no associated team")
      }

      console.log(`Adding user ${userId} to company ${companyId} with teamId ${teamId}`)

      // ÖNCE: Kullanıcıyı takıma eklemek için API'yi çağır
      let shouldContinue = false;
      try {
        // Takım üyesi olarak ekle - varsayılan rol olarak team_member kullan
        const teamResponse = await adminApi.addTeamMember(teamId, userId, "team_member")

        // Debug log to see the actual response
        console.log('Add team member response:', teamResponse)

        if (teamResponse.success) {
          shouldContinue = true;
        } else {
          // Eğer hata "zaten var" ise, bu durumda kullanıcı zaten role_permissions'da var demektir
          // Bu durumda işleme devam edebiliriz
          if (teamResponse.error?.includes("already") || teamResponse.error?.includes("duplicate")) {
            console.warn("User already exists in role_permissions collection, continuing with user update")
            shouldContinue = true;
          } else {
            console.warn("Failed to add user to role_permissions collection:", teamResponse.error)
            throw new Error(teamResponse.error || "Failed to add user to team")
          }
        }
      } catch (teamError: any) {
        // Eğer hata "zaten var" ile ilgiliyse, devam et
        if (teamError.message?.includes("already") || teamError.message?.includes("duplicate")) {
          console.warn("User already exists in role_permissions collection, continuing with user update")
          shouldContinue = true;
        } else {
          console.error("Error adding user to role_permissions collection:", teamError)
          throw new Error(teamError.message || "Failed to add user to team")
        }
      }

      // SONRA: Kullanıcıyı güncelle - hem şirket hem de takım ID'sini güncelle
      if (shouldContinue) {
        const response = await adminApi.updateUser(userId, {
          companyId: companyId,
          teamId: teamId, // Takım ID'sini de güncelle
        })

        // Debug log to see the actual response
        console.log('Update user response:', response)

        if (response.success) {
          // Kullanıcı listelerini güncelle
          const updatedUser = response.user

          // Kullanıcıyı mevcut kullanıcılar listesinden kaldır
          setAvailableUsers(prev => prev.filter(user => user.id !== userId))

          // Kullanıcıyı şirket kullanıcıları listesine ekle
          if (updatedUser) {
            setCompanyUsers(prev => [...prev, updatedUser])
          } else {
            // Kullanıcı bilgilerini yeniden getir
            fetchUsers()
          }
        } else {
          // Kullanıcı güncellenemedi, role_permissions kaydını geri al
          try {
            await adminApi.removeTeamMember(teamId, userId)
            console.warn("Rolled back role_permissions addition due to user update failure")
          } catch (rollbackError) {
            console.error("Failed to rollback team_member addition:", rollbackError)
          }
          throw new Error(response.error || "Failed to update user information")
        }
      }

        toast({
          title: "Success",
          description: "User added to company and team successfully",
        })
    } catch (error: any) {
      console.error("Error adding user to company:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to add user to company",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Kullanıcıyı şirketten çıkar
  const removeUserFromCompany = async (userId: string) => {
    setIsProcessing(true)
    try {
      // Önce kullanıcının mevcut takım ID'sini al
      const userResponse = await adminApi.getUserById(userId)

      // Debug log to see the actual response
      console.log('Get user response:', userResponse)

      // Kullanıcının takım ID'sini al
      const teamId = userResponse.success ? userResponse.user?.teamId : null

      // Eğer kullanıcının takım ID'si yoksa, doğrudan kullanıcı güncellemesine geç
      if (!teamId) {
        console.warn("User has no associated team, skipping role_permissions removal")

        // Kullanıcıyı güncelle - şirket ve takım ID'lerini temizle
        const response = await adminApi.updateUser(userId, {
          companyId: null,
          teamId: null
        })

        // Debug log to see the actual response
        console.log('Update user response:', response)

        if (response.success) {
          // Kullanıcı listelerini güncelle
          const updatedUser = response.user

          // Kullanıcıyı şirket kullanıcıları listesinden kaldır
          setCompanyUsers(prev => prev.filter(user => user.id !== userId))

          // Kullanıcıyı mevcut kullanıcılar listesine ekle
          if (updatedUser) {
            setAvailableUsers(prev => [...prev, updatedUser])
          }

          toast({
            title: "Success",
            description: "User removed from company successfully",
          })
          return
        } else {
          throw new Error(response.error || "Failed to update user information")
        }
      }

      // ÖNCE: Kullanıcıyı role_permissions koleksiyonundan kaldırmaya çalış
      // Ancak bulunamazsa da devam et
      let shouldContinue = false;

      try {
        // Admin API'sini kullanarak takım üyeliğini kaldır
        const teamResponse = await adminApi.removeTeamMember(teamId, userId)

        // Debug log to see the actual response
        console.log('Remove team member response:', teamResponse)

        if (teamResponse.success) {
          shouldContinue = true;
        } else {
          // Eğer hata "bulunamadı" ise, bu durumda kullanıcı zaten role_permissions'da yok demektir
          // Bu durumda işleme devam edebiliriz
          if (teamResponse.error?.includes("not found") || teamResponse.error?.includes("404")) {
            console.warn("User not found in role_permissions collection, continuing with user update")
            shouldContinue = true;
          } else {
            console.warn("Failed to remove user from role_permissions collection:", teamResponse.error)
            throw new Error(teamResponse.error || "Failed to remove user from team")
          }
        }
      } catch (teamError: any) {
        // Eğer hata "bulunamadı" ile ilgiliyse, devam et
        if (teamError.message?.includes("not found") || teamError.message?.includes("404")) {
          console.warn("User not found in role_permissions collection, continuing with user update")
          shouldContinue = true;
        } else {
          console.error("Error removing user from role_permissions collection:", teamError)
          throw new Error(teamError.message || "Failed to remove user from team")
        }
      }

      // SONRA: Kullanıcıyı güncelle - şirket ve takım ID'lerini temizle
      if (shouldContinue) {
        try {
          const response = await adminApi.updateUser(userId, {
            companyId: null,
            teamId: null, // Takım ID'sini de temizle
          })

          // Debug log to see the actual response
          console.log('Update user response:', response)

          if (response.success) {
            // Kullanıcı listelerini güncelle
            const updatedUser = response.user

            // Kullanıcıyı şirket kullanıcıları listesinden kaldır
            setCompanyUsers(prev => prev.filter(user => user.id !== userId))

            // Kullanıcıyı mevcut kullanıcılar listesine ekle
            if (updatedUser) {
              setAvailableUsers(prev => [...prev, updatedUser])
            }
          } else {
            // Kullanıcı güncellenemedi, ama role_permissions kaydı silindi
            // Bu durumda kullanıcı bilgilerini yeniden getir
            console.warn("User was removed from role_permissions but user update failed:", response.error)
            // Kullanıcı listelerini yeniden getir
            fetchUsers()
            // Hata fırlatma, sadece uyarı göster
            toast({
              title: "Uyarı",
              description: "Kullanıcı takımdan çıkarıldı ancak kullanıcı bilgileri güncellenemedi. Kullanıcı listesi yenilendi."
            })
          }
        } catch (updateError) {
          console.error("Error updating user:", updateError)
          // Kullanıcı güncellenemedi, ama role_permissions kaydı silindi
          // Bu durumda kullanıcı bilgilerini yeniden getir
          fetchUsers()
          // Hata fırlatma, sadece uyarı göster
          toast({
            title: "Uyarı",
            description: "Kullanıcı takımdan çıkarıldı ancak kullanıcı bilgileri güncellenemedi. Kullanıcı listesi yenilendi."
          })
        }
      }

      toast({
        title: "Success",
        description: "User removed from company and team successfully",
      })
    } catch (error) {
      console.error("Error removing user from company:", error)
      toast({
        title: "Error",
        description: "Failed to remove user from company",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Arama işlevi
  const filteredAvailableUsers = availableUsers.filter(user =>
    user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredCompanyUsers = companyUsers.filter(user =>
    user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>Manage Users for {companyName}</DialogTitle>
          <DialogDescription>
            Add or remove users from this company
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center gap-2 mb-4">
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
          <Button variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex gap-4 h-[400px]">
          {/* Sol taraf - Mevcut kullanıcılar */}
          <div className="flex-1 border rounded-md">
            <div className="p-3 border-b bg-muted/50">
              <h3 className="font-medium">Available Users</h3>
              <p className="text-sm text-muted-foreground">
                Users not in this company
              </p>
            </div>
            {isLoading ? (
              <div className="flex h-[300px] items-center justify-center">
                <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <ScrollArea className="h-[330px]">
                <div className="p-4 space-y-4">
                  {filteredAvailableUsers.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No available users found
                    </div>
                  ) : (
                    filteredAvailableUsers.map((user) => (
                      <div key={user.id} className="flex items-center justify-between p-3 border rounded-md">
                        <div>
                          <div className="font-medium">{user.name || "N/A"}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                          <div className="flex gap-2 mt-1">
                            <Badge variant="outline">{user.accountType || "user"}</Badge>
                            {user.teamRole && (
                              <Badge variant="secondary">{user.teamRole.replace('_', ' ')}</Badge>
                            )}
                          </div>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => addUserToCompany(user.id)}
                          disabled={isProcessing}
                        >
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Add
                        </Button>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            )}
          </div>

          {/* Sağ taraf - Şirket kullanıcıları */}
          <div className="flex-1 border rounded-md">
            <div className="p-3 border-b bg-muted/50">
              <h3 className="font-medium">Company Users</h3>
              <p className="text-sm text-muted-foreground">
                Users in {companyName}
              </p>
            </div>
            {isLoading ? (
              <div className="flex h-[300px] items-center justify-center">
                <div className="h-8 w-8 animate-spin rounded-full border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <ScrollArea className="h-[330px]">
                <div className="p-4 space-y-4">
                  {filteredCompanyUsers.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No company users found
                    </div>
                  ) : (
                    filteredCompanyUsers.map((user) => (
                      <div key={user.id} className="flex items-center justify-between p-3 border rounded-md">
                        <div>
                          <div className="font-medium">{user.name || "N/A"}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                          <div className="flex gap-2 mt-1">
                            <Badge variant="outline">{user.accountType || "user"}</Badge>
                            {user.teamRole && (
                              <Badge variant="secondary">{user.teamRole.replace('_', ' ')}</Badge>
                            )}
                          </div>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeUserFromCompany(user.id)}
                          disabled={isProcessing}
                        >
                          <ArrowLeft className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
          <Button
            type="button"
            onClick={() => {
              onOpenChange(false)
              if (onSuccess) onSuccess()
            }}
          >
            Done
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
