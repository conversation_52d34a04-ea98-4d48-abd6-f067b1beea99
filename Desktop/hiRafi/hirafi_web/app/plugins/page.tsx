"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Puzzle, 
  Search, 
  Github, 
  Smartphone, 
  ArrowRight, 
  Circle,
  GitBranch,
  Users
} from "lucide-react"
import { CompanyOwnerRoute } from "@/components/company-owner-route"
import { cn } from "@/lib/utils"

// Plugin categories and data
const pluginCategories = [
  {
    id: "integration",
    name: "Integration Tools",
    description: "Connect with your favorite development and project management tools",
    icon: <GitBranch className="h-6 w-6" />,
    color: "blue",
    plugins: [
      {
        id: "github",
        name: "GitHub",
        description: "Integrate with GitHub to link test cases to repositories and track commits",
        icon: <Github className="h-8 w-8" />,
        active: false,
        color: "purple",
        features: ["Repository linking", "Commit tracking", "Issue integration", "Branch management"],
        category: "Source Control"
      },
      {
        id: "jira",
        name: "Jira",
        description: "Connect with Jira to link test cases to issues and track project progress",
        icon: (
          <svg className="h-8 w-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.0001 1L1 11.9999L6.06064 17.0605L12.0001 11.1211L17.9395 17.0605L23.0001 11.9999L12.0001 1Z" fill="currentColor" />
            <path d="M12.0001 11.1211L6.06064 17.0605L12.0001 23.0001L17.9395 17.0606L12.0001 11.1211Z" fill="currentColor" opacity="0.8" />
          </svg>
        ),
        active: false,
        color: "indigo",
        features: ["Issue linking", "Sprint tracking", "Epic management", "Workflow integration"],
        category: "Project Management"
      }
    ]
  },
  {
    id: "testing",
    name: "Device Farms",
    description: "Enhance your testing capabilities with cloud and device testing services",
    icon: <Smartphone className="h-6 w-6" />,
    color: "green",
    plugins: [
      {
        id: "saucelabs",
        name: "SauceLabs",
        description: "Integrate with SauceLabs for comprehensive mobile and web testing on real devices",
        icon: (
          <img
            src="/images/sauce-labs.svg"
            alt="SauceLabs"
            className="h-8 w-8"
            width="32"
            height="32"
          />
        ),
        active: false,
        color: "orange",
        features: ["Real device testing", "Cross-browser testing", "Automated scaling", "Live debugging"],
        category: "Cloud Testing"
      },
      {
        id: "testinium",
        name: "Testinium",
        description: "Integrate with Testinium for mobile device testing using DevicePark SDK",
        icon: (
          <img
            src="/images/testinium_logo.svg"
            alt="Testinium"
            className="h-8 w-8"
            width="32"
            height="32"
          />
        ),
        active: false,
        color: "blue",
        features: ["Mobile testing", "DevicePark SDK", "Device management", "Performance monitoring"],
        category: "Mobile Testing"
      }
    ]
  },
  {
    id: "management",
    name: "Test Management",
    description: "Organize and track your testing activities with professional test management tools",
    icon: <Users className="h-6 w-6" />,
    color: "purple",
    plugins: [
      {
        id: "testrail",
        name: "TestRail",
        description: "Integrate with TestRail to sync test cases, organize test runs and track results",
        icon: (
          <img
            src="/images/testrail-icon.svg"
            alt="TestRail"
            className="h-8 w-8"
            width="32"
            height="32"
          />
        ),
        active: false,
        color: "blue",
        features: ["Test case management", "Test run organization", "Result tracking", "Reporting"],
        category: "Test Management"
      },
      {
        id: "zephyrscale",
        name: "Zephyr Scale",
        description: "Integrate with Zephyr Scale Cloud to sync test cases, manage test cycles and track execution results",
        icon: (
          <svg className="h-8 w-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor" />
            <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        ),
        active: false,
        color: "indigo",
        features: ["Test case synchronization", "Test cycle management", "Execution tracking", "Jira integration"],
        category: "Test Management"
      }
    ]
  }
]

export default function PluginsPage() {
  const router = useRouter()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // Filter plugins based on search and category
  const filteredCategories = pluginCategories.map(category => ({
    ...category,
    plugins: category.plugins.filter(plugin => {
      const matchesSearch = plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           plugin.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = !selectedCategory || category.id === selectedCategory
      return matchesSearch && matchesCategory
    })
  })).filter(category => category.plugins.length > 0)

  const allPlugins = pluginCategories.flatMap(category => category.plugins)

  const handlePluginClick = (pluginId: string) => {
    router.push(`/plugins/${pluginId}`)
  }

  const getCategoryColor = (color: string) => {
    const colors = {
      blue: "from-blue-500/20 to-cyan-500/20 border-blue-500/30",
      green: "from-green-500/20 to-emerald-500/20 border-green-500/30",
      purple: "from-purple-500/20 to-pink-500/20 border-purple-500/30"
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  const getPluginColor = (color: string) => {
    const colors = {
      purple: "text-purple-500",
      indigo: "text-indigo-500",
      orange: "text-orange-500",
      blue: "text-blue-500"
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <CompanyOwnerRoute>
      <div className="flex h-screen bg-white dark:bg-gray-950">
        {/* Sidebar */}
        <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <header className="border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-indigo-500">
                  <Puzzle className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Plugins</h1>
                  <p className="text-gray-500 dark:text-gray-400">
                    Extend your testing capabilities with powerful integrations
                  </p>
                </div>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search plugins..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={selectedCategory === null ? "default" : "outline"}
                    onClick={() => setSelectedCategory(null)}
                    size="sm"
                  >
                    All Categories
                  </Button>
                  {pluginCategories.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      onClick={() => setSelectedCategory(category.id)}
                      size="sm"
                      className="hidden sm:inline-flex"
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </header>

          {/* Plugin Categories */}
          <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
            <div className="max-w-7xl mx-auto space-y-8">
              {filteredCategories.map((category) => (
                <div key={category.id} className="space-y-4">
                  {/* Category Header */}
                  <div className={cn(
                    "p-6 rounded-xl border bg-gradient-to-r",
                    getCategoryColor(category.color)
                  )}>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-white/10 backdrop-blur-sm">
                        {category.icon}
                      </div>
                      <div>
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                          {category.name}
                        </h2>
                        <p className="text-gray-600 dark:text-gray-300">
                          {category.description}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Category Plugins */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {category.plugins.map((plugin) => (
                      <Card 
                        key={plugin.id} 
                        className="border border-gray-200 dark:border-gray-800 hover:shadow-lg transition-all duration-200 group cursor-pointer"
                        onClick={() => handlePluginClick(plugin.id)}
                      >
                        <CardHeader className="pb-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-4">
                              <div className={cn(
                                "flex items-center justify-center w-12 h-12 rounded-xl",
                                getPluginColor(plugin.color)
                              )}>
                                {plugin.icon}
                              </div>
                              <div>
                                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                                  {plugin.name}
                                  {plugin.active && (
                                    <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 border-0">
                                      Active
                                    </Badge>
                                  )}
                                </CardTitle>
                                <Badge variant="outline" className="text-xs">
                                  {plugin.category}
                                </Badge>
                              </div>
                            </div>
                            <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <CardDescription className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                            {plugin.description}
                          </CardDescription>
                          
                          {/* Features */}
                          <div className="space-y-2">
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                              Key Features
                            </p>
                            <div className="grid grid-cols-2 gap-1">
                              {plugin.features.slice(0, 4).map((feature, index) => (
                                <div key={index} className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                                  <Circle className="h-1.5 w-1.5 fill-current" />
                                  {feature}
                                </div>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}

              {filteredCategories.length === 0 && (
                <div className="text-center py-12">
                  <Puzzle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No plugins found
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    Try adjusting your search terms or category filters.
                  </p>
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </CompanyOwnerRoute>
  )
} 