"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON>back, ReactElement } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  PlayCircle, ArrowLeft, Clock, X, LoaderCircle, CalendarClock,
  CheckCircle, Square, BarChart, Eye, Play, Download, RefreshCw,
  Clipboard, Layers, Settings2, HelpCircle, AlertTriangle, ChevronDown,
  Monitor, Globe, Smartphone, Maximize2, Camera, Video, LineChart, Network, Settings, Flame, Zap, Database
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/lib/utils/toast-utils"
import { format, formatDistanceToNow } from "date-fns"
import { Sidebar } from "@/components/sidebar/sidebar"
import { ProtectedRoute } from "@/components/protected-route"
import { useRuns, Run } from "@/hooks/useRuns"
import { useRunDetail } from "@/hooks/useRunDetail"
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from "@/components/ui/collapsible"
import { NestedStepProgress } from "@/components/ui/nested-step-progress"
import { testHubApi } from "@/lib/api"
import { getBestTestResults } from "@/lib/utils/run-utils"


function RunDetailContent(): ReactElement {
  const params = useParams()
  const router = useRouter()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [refreshingStatus, setRefreshingStatus] = useState(false)

  const runId = Array.isArray(params?.runId) ? params.runId[0] : params?.runId as string

  // Use the optimized hook for all data fetching
  const {
    loading,
    run,
    tests,
    reports: scenarioReports,
    stepProgressSummary,
    refresh
  } = useRunDetail({
    runId,
    autoRefresh: true,
    refreshInterval: 2000  // Faster refresh for real-time step progress updates
  })

  const {
    executeRun,
    stopRun
  } = useRuns({ autoFetch: false })



  // Helper function to check if we should show the reports table
  const shouldShowReports = useCallback(() => {
    if (!scenarioReports || scenarioReports.length === 0) return false

    // Show reports if at least one test has started (not just queued)
    return scenarioReports.some(report =>
      report.status &&
      !['queued'].includes(report.status.toLowerCase())
    )
  }, [scenarioReports])

  // Helper function to get formatted duration
  const getFormattedDuration = (report: any, options: { isRunning?: boolean } = {}) => {
    const formatMs = (ms: number) => {
      const seconds = Math.floor(ms / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);

      if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
      } else {
        return `${seconds}s`;
      }
    };

    if (options.isRunning) {
      // For running tests, calculate elapsed time
      const startTime = report.startTime || report.createdAt || report.date;
      if (startTime) {
        const elapsed = Date.now() - new Date(startTime).getTime();
        return formatMs(elapsed);
      }
      return 'Running...';
    }

    // For completed tests, use duration or calculate from start/end times
    if (report.duration) {
      return formatMs(report.duration);
    }

    const startTime = report.startTime || report.createdAt || report.date;
    const endTime = report.endTime || report.completedAt;

    if (startTime && endTime) {
      const duration = new Date(endTime).getTime() - new Date(startTime).getTime();
      return formatMs(duration);
    }

    return '-';
  };

  // Helper function to count tests by status
  const countTestsByStatus = () => {
    // Öncelikle run.testResults'ı kullan (en güvenilir kaynak)
    if (run?.testResults) {
      return {
        total: run.testResults.total || 0,
        running: run.testResults.running || 0,
        queued: run.testResults.queued || 0,
        completed: run.testResults.completed || 0,
        failed: run.testResults.failed || 0,
        stopped: run.testResults.stopped || 0
      };
    }

    // Fallback: tests array'ini kullan
    if (!Array.isArray(tests)) {
      return {
        total: 0,
        running: 0,
        queued: 0,
        completed: 0,
        failed: 0,
        stopped: 0
      };
    }

    const counts = tests.reduce((acc, test) => {
      acc.total++;
      switch (test.status) {
        case 'running':
          acc.running++;
          break;
        case 'queued':
          acc.queued++;
          break;
        case 'completed':
          acc.completed++;
          break;
        case 'failed':
          acc.failed++;
          break;
        case 'stopped':
          acc.stopped++;
          break;
      }
      return acc;
    }, {
      total: 0,
      running: 0,
      queued: 0,
      completed: 0,
      failed: 0,
      stopped: 0
    });

    return counts;
  }

  // Handle run actions (start, stop)
  const handleRunAction = async (action: 'start' | 'stop') => {
    setRefreshingStatus(true);

    try {
      if (action === 'start') {
        // Run başlatma işlemi
        const result = await executeRun(runId);

        if (!result.success) {
          // Use the actual API error message instead of generic message
          let errorMessage = result.error || "Failed to start run";
          // Show error with sonner toast
          toast.error(errorMessage);
          return;
        }

        // Immediately refresh data to get updated status
        refresh();

        // Show single success toast
        toast.success("Run started successfully");
      } else if (action === 'stop') {
        // Run durdurma işlemi
        const result = await stopRun(runId);

        if (!result.success) {
          // Hata durumunda toast ile bildir ve throw etme
          toast.error(result.error || "Failed to stop run");
          return;
        }

        // Immediately refresh data to get updated status
        refresh();

        // Show single success toast
        toast.success("Run stopped successfully");
      }
    } catch (error) {
      toast.error(`Failed to ${action} run: ${error}`);
    } finally {
      setRefreshingStatus(false);
    }
  };

  // Handle individual test stop
  const handleStopTest = async (testId: string) => {
    try {
      toast.info("Stopping test...", {
        description: "Sending stop command to the test node"
      });

      // Centralized API kullanarak testi durdur
      const result = await testHubApi.stopTest(testId);

      if (!result.success) {
        // Hata durumunda toast ile bildir ve throw etme
        toast.error(result.error || "Failed to stop test");
        return;
      }

      // UI will be automatically updated by useRunDetail hook

      toast.success("Test stopped successfully");

      // Data will be automatically refreshed by useRunDetail hook

    } catch (error) {
      toast.error(`Failed to stop test: ${error}`);
    }
  };

  // Get status badge for a run
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "running":
        return (
          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 inline-flex items-center">
            <div className="h-2 w-2 rounded-full bg-blue-500 mr-1.5 animate-pulse"></div>
            Running
          </Badge>
        )
      case "completed":
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 inline-flex items-center">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        )
      case "failed":
        return (
          <Badge className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 inline-flex items-center">
            <X className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        )
      case "partial":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 inline-flex items-center">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Partial
          </Badge>
        )
      case "stopped":
        return (
          <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 inline-flex items-center">
            <Square className="h-3 w-3 mr-1" />
            Stopped
          </Badge>
        )
      case "queued":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 inline-flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            In Queue
          </Badge>
        )
      case "created":
        return (
          <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 inline-flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            Created
          </Badge>
        )
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 inline-flex items-center">
            {status}
          </Badge>
        )
    }
  }

  // Improved calculation of progress based on actual test results
  const calculateProgress = () => {
    // If we have tests data, use that for more accurate calculation
    if (tests && tests.length > 0) {
      const testCounts = countTestsByStatus();
      const completedCount = testCounts.completed;
      const failedCount = testCounts.failed;
      const totalCompleted = completedCount + failedCount;

      // If no tests are completed or failed, return 0%
      if (totalCompleted === 0) return 0;

      // Calculate percentage based on completed tests
      // For example, if 2 passed and 1 failed out of 3 completed, show 66% passed
      return Math.round((completedCount / totalCompleted) * 100);
    }

    // Fallback to run.testResults if available
    if (!run?.testResults) return 0;

    const { completed, failed } = run.testResults;
    const totalCompleted = completed + failed;

    // If no tests are completed or failed, return 0%
    if (totalCompleted === 0) return 0;

    // Calculate percentage based on completed tests
    return Math.round((completed / totalCompleted) * 100);
  }





  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              className="mr-2"
              onClick={() => router.push('/runs')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {loading ? <Skeleton className="h-8 w-64" /> : run?.name || 'Run Details'}
                </h1>
                {!loading && run && (
                  <div className="ml-2 flex items-center gap-2">
                    {getStatusBadge(run.status)}

                    {/* Platform indicator */}
                    <Badge
                      variant="outline"
                      className={`${
                        run.platform === "web"
                          ? "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800"
                          : run.platform === "android"
                          ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800"
                          : "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900 dark:text-gray-400 dark:border-gray-800"
                      }`}
                    >
                      {run.platform === "web" ? (
                        <>
                          <Globe className="h-3 w-3 mr-1" />
                          Web Platform
                        </>
                      ) : run.platform === "android" ? (
                        <>
                          <Smartphone className="h-3 w-3 mr-1" />
                          Android Platform
                        </>
                      ) : (
                        run.platform || "Unknown Platform"
                      )}
                    </Badge>
                  </div>
                )}
              </div>
              <div className="text-gray-500 dark:text-gray-400">
                {loading ? <Skeleton className="h-4 w-36 mt-1" /> : (run?.description || 'View detailed information about this test run')}
              </div>
            </div>

            <div className="ml-auto space-x-2">
              {!loading && run && (
                run.status === "running" || run.status === "queued" ? (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleRunAction('stop')}
                    disabled={refreshingStatus}
                  >
                    <Square className="h-4 w-4 mr-1" />
                    Stop Run
                  </Button>
                ) : (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleRunAction('start')}
                    disabled={refreshingStatus}
                    className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700"
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Start Run
                  </Button>
                )
              )}
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto p-4">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 animate-pulse">
              {Array(4).fill(0).map((_, i) => (
                <Skeleton key={i} className="h-32 rounded-lg" />
              ))}
            </div>
          ) : run ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {/* Run Stats Cards */}
                <Card className="border border-gray-200 dark:border-gray-800 overflow-hidden group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                    <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                      <Layers className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                      Total Scenarios
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="flex flex-col items-center justify-center">
                      <div className="text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent animate-counter relative">
                        {(() => {
                          // Calculate actual test count for multiple device runs
                          const testResults = getBestTestResults(run);
                          return testResults.total || run.scenarioIds.length;
                        })()}
                        <div className="absolute -top-1 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="animate-ping h-2 w-2 rounded-full bg-blue-400"></div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                        {run.status === 'running' && (
                          <span className="text-blue-600 dark:text-blue-400 inline-flex items-center">
                            <div className="h-1.5 w-1.5 rounded-full bg-blue-500 mr-1.5 animate-pulse"></div>
                            Test run in progress
                          </span>
                        )}
                        {run.status !== 'running' && (
                          (() => {
                            const testResults = getBestTestResults(run);
                            const isMultipleDeviceRun = run.platform === 'android' && 
                              run.environment?.sauceLabs?.selectedDevices?.length > 1 &&
                              run.environment?.testDistribution?.strategy === 'all-on-all';
                            
                            if (isMultipleDeviceRun) {
                              return (
                                <div className="text-center">
                                  <div>Tests in this run</div>
                                  <div className="text-xs text-gray-400 mt-0.5">
                                    {run.scenarioIds.length} scenarios × {run.environment?.sauceLabs?.selectedDevices?.length} devices
                                  </div>
                                </div>
                              );
                            } else {
                              return <span>Tests in this run</span>;
                            }
                          })()
                        )}
                      </div>
                      {/* Add Total Steps information - only show when run is not running */}
                      {run.status !== 'running' && run.status !== 'queued' && (
                        <div className="mt-4 w-full">
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 text-center">Total Steps</div>
                          <div className="text-2xl font-bold text-center bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                            {Array.isArray(scenarioReports) && scenarioReports.length > 0 ?
                              (() => {
                                // Calculate total steps from all reports
                                let totalSteps = 0;
                                scenarioReports.forEach(report => {
                                  if (report.steps && report.steps.length) {
                                    totalSteps += report.steps.length;
                                  } else if (report.scenario?.steps && report.scenario.steps.length) {
                                    totalSteps += report.scenario.steps.length;
                                  } else if (report.stepsCount) {
                                    totalSteps += report.stepsCount;
                                  }
                                });
                                return totalSteps;
                              })() : 0
                            }
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
                            Total steps across all scenarios
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border border-gray-200 dark:border-gray-800 overflow-hidden group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardHeader className="pb-2 bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20">
                    <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center justify-between">
                      <div className="flex items-center">
                        <BarChart className="h-4 w-4 mr-2 text-emerald-600 dark:text-emerald-400" />
                        Completion Status
                      </div>
                      {run && run.status === 'completed' && (
                        <Badge className="ml-2 bg-gradient-to-r from-emerald-500 to-green-500 text-white">
                          <CheckCircle className="h-3 w-3 mr-1" /> Complete
                        </Badge>
                      )}
                      {run && run.status === 'failed' && (
                        <Badge className="ml-2 bg-gradient-to-r from-red-500 to-pink-500 text-white">
                          <X className="h-3 w-3 mr-1" />
                          Failed
                        </Badge>
                      )}
                      {run && run.status === 'running' && (
                        <Badge className="ml-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white pulse-animation">
                          <div className="h-1.5 w-1.5 rounded-full bg-white mr-1.5 animate-pulse"></div> Running
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    {run && (run.status === 'running' || run.status === 'queued') ? (
                      <div className="flex flex-col items-center justify-center mb-4">
                        <div className="relative h-24 w-24 flex items-center justify-center">
                          <div className="h-16 w-16 rounded-full border-4 border-blue-200 dark:border-blue-900/30 border-t-blue-500 dark:border-t-blue-400 animate-spin"></div>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <Clock className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                          </div>
                        </div>
                        <div className="mt-4 text-center">
                          <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                            Waiting for run to complete
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {countTestsByStatus().running} running, {countTestsByStatus().queued} queued
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center mb-4">
                        <div className="relative h-24 w-24">
                          {/* Circular progress background */}
                          <svg className="w-full h-full" viewBox="0 0 100 100">
                            <circle
                              className="text-gray-200 dark:text-gray-700 stroke-current"
                              strokeWidth="10"
                              cx="50"
                              cy="50"
                              r="40"
                              fill="transparent"
                            ></circle>
                            <circle
                              className={`text-emerald-500 dark:text-emerald-400 stroke-current transform -rotate-90 origin-center transition-all duration-1000 ease-in-out`}
                              strokeWidth="10"
                              strokeLinecap="round"
                              cx="50"
                              cy="50"
                              r="40"
                              fill="transparent"
                              strokeDasharray="251.2"
                              strokeDashoffset={251.2 - (251.2 * calculateProgress()) / 100}
                            ></circle>
                          </svg>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className={`text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent`}>
                              {calculateProgress()}%
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                      <span>Pass Rate</span>
                      <span>{countTestsByStatus().completed} / {countTestsByStatus().completed + countTestsByStatus().failed} completed</span>
                    </div>
                    <Progress
                      value={calculateProgress()}
                      className={`h-2 mt-2 ${
                        calculateProgress() > 0 ? 'bg-green-100 dark:bg-green-900/20 [&>div]:bg-green-500 dark:[&>div]:bg-green-400' :
                        'bg-green-100 dark:bg-green-900/20 [&>div]:bg-green-500 dark:[&>div]:bg-green-400'
                      }`}
                    />
                    <div className="grid grid-cols-2 gap-2 mt-3">
                      <div className="flex items-center justify-center p-1 rounded-md bg-green-50 dark:bg-green-900/20 text-xs text-green-700 dark:text-green-300">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {countTestsByStatus().completed} passed
                      </div>
                      <div className="flex items-center justify-center p-1 rounded-md bg-red-50 dark:bg-red-900/20 text-xs text-red-700 dark:text-red-300">
                        <X className="h-3 w-3 mr-1" />
                        {countTestsByStatus().failed} failed
                      </div>
                    </div>


                    {/* Add status counts when relevant - show inline for better space utilization */}
                    {(countTestsByStatus().running > 0 || countTestsByStatus().queued > 0 || countTestsByStatus().stopped > 0) ? (
                      <div className="flex flex-wrap gap-2 mt-2 justify-center">
                        {countTestsByStatus().running > 0 ? (
                          <div className="flex items-center p-1 rounded-md bg-blue-50 dark:bg-blue-900/20 text-xs text-blue-700 dark:text-blue-300">
                            <div className="h-2 w-2 rounded-full bg-blue-500 mr-1.5 animate-pulse"></div>
                            {countTestsByStatus().running} running
                          </div>
                        ) : null}

                        {countTestsByStatus().queued > 0 ? (
                          <div className="flex items-center p-1 rounded-md bg-yellow-50 dark:bg-yellow-900/20 text-xs text-yellow-700 dark:text-yellow-300">
                            <Clock className="h-3 w-3 mr-1" />
                            {countTestsByStatus().queued} queued
                          </div>
                        ) : null}

                        {countTestsByStatus().stopped > 0 ? (
                          <div className="flex items-center p-1 rounded-md bg-gray-50 dark:bg-gray-800 text-xs text-gray-700 dark:text-gray-300">
                            <Square className="h-3 w-3 mr-1" />
                            {countTestsByStatus().stopped} stopped
                          </div>
                        ) : null}
                      </div>
                    ) : (countTestsByStatus().completed > 0 || countTestsByStatus().failed > 0) && run && run.status === 'completed' ? (
                      <div className="flex justify-center mt-2">
                        <div className="flex items-center p-1 rounded-md bg-emerald-50 dark:bg-emerald-900/20 text-xs text-emerald-700 dark:text-emerald-300">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {countTestsByStatus().failed > 0
                            ? `All tests completed (${countTestsByStatus().failed} failed)`
                            : `All ${countTestsByStatus().completed} tests passed`}
                        </div>
                      </div>
                    ) : null}
                  </CardContent>
                </Card>

                <Card className="border border-gray-200 dark:border-gray-800 overflow-hidden group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 md:col-span-2">
                  <CardHeader className="pb-2 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20">
                    <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                      <CalendarClock className="h-4 w-4 mr-2 text-purple-600 dark:text-purple-400" />
                      Run Timeline
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="relative pl-8 pb-1">
                      {/* Vertical line */}
                      <div className="absolute left-3 top-1 bottom-0 w-0.5 bg-gradient-to-b from-purple-500 to-blue-500 dark:from-purple-400 dark:to-blue-400"></div>

                      {/* Created time */}
                      <div className="mb-6 relative">
                        <div className="absolute left-[-29px] top-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                          <div className="h-3 w-3 rounded-full bg-purple-500 dark:bg-purple-400"></div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm text-gray-800 dark:text-gray-200">Created</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Run was created and configured</p>
                          </div>
                          <div className="text-sm font-medium bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-3 py-1 rounded-full">
                            {format(new Date(run.createdAt), 'MMM d, HH:mm:ss')}
                          </div>
                        </div>
                      </div>

                      {/* Started time */}
                      {run.startedAt && (
                        <div className="mb-6 relative">
                          <div className="absolute left-[-29px] top-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                            <div className="h-3 w-3 rounded-full bg-blue-500 dark:bg-blue-400"></div>
                          </div>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-sm text-gray-800 dark:text-gray-200">Started</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Test execution began</p>
                            </div>
                            <div className="text-sm font-medium bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full">
                              {format(new Date(run.startedAt), 'MMM d, HH:mm:ss')}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Completed time - only show if run is not running or queued */}
                      {run.completedAt && run.status !== 'running' && run.status !== 'queued' && (
                        <div className="relative">
                          <div className="absolute left-[-29px] top-0 h-6 w-6 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                            <div className="h-3 w-3 rounded-full bg-green-500 dark:bg-green-400"></div>
                          </div>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-sm text-gray-800 dark:text-gray-200">Completed</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {run.status === 'completed' ? 'Successfully completed' :
                                 run.status === 'failed' ? 'Failed to complete' : 'Run was stopped'}
                              </p>
                            </div>
                            <div className={`text-sm font-medium px-3 py-1 rounded-full ${
                              run.status === 'completed' ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' :
                              run.status === 'failed' ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300' :
                              'bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                            }`}>
                              {format(new Date(run.completedAt), 'MMM d, HH:mm:ss')}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* If running, show current status */}
                      {run.status === 'running' && !run.completedAt && (
                        <div className="relative">
                          <div className="absolute left-[-29px] top-0 h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                            <div className="h-3 w-3 rounded-full bg-blue-500 dark:bg-blue-400 animate-pulse"></div>
                          </div>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-sm text-gray-800 dark:text-gray-200">In Progress</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Tests are currently running</p>
                            </div>
                            <div className="text-sm font-medium bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full animate-pulse">
                              {formatDistanceToNow(run.startedAt ? new Date(run.startedAt) : new Date(), { addSuffix: true })}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Tabs for different views */}
              <Tabs defaultValue="scenarios" className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="scenarios">Active Scenarios</TabsTrigger>
                  <TabsTrigger value="reports">Test Reports</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="scenarios" className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Active Test Scenarios</h3>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {run?.status === 'running' || run?.status === 'queued'
                        ? `${countTestsByStatus().running + countTestsByStatus().queued} Active Scenarios`
                        : `${countTestsByStatus().total} Total Scenarios`}
                    </div>
                  </div>

                  {/* Modern List View */}
                  <div className="bg-white dark:bg-gray-900 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-800">
                    {(() => {
                      // Use step progress summary for active tests if available, otherwise fallback to scenarioStatuses
                      const activeTests = stepProgressSummary && stepProgressSummary.tests && stepProgressSummary.tests.length > 0
                        ? stepProgressSummary.tests.filter((test: any) =>
                            // Only show running and queued tests, exclude completed/failed/stopped
                            test.currentStepStatus === 'started' || test.currentStepStatus === 'running' || test.currentStepStatus === 'queued'
                          )
                        : Array.isArray(run?.scenarioStatuses) ? run.scenarioStatuses.filter((scenario: any) =>
                            scenario.status === 'running' || scenario.status === 'queued'
                          ).map((scenario: any) => ({
                            // Map scenario status to test format for compatibility
                            id: scenario.testId || scenario.scenarioId,
                            testId: scenario.testId || scenario.scenarioId,
                            scenarioId: scenario.scenarioId,
                            status: scenario.status,
                            scenarioName: scenario.scenarioName || `Scenario ${scenario.scenarioId}`,
                            name: scenario.scenarioName || `Scenario ${scenario.scenarioId}`,
                            startTime: scenario.startTime,
                            updatedAt: scenario.updatedAt
                          })) : Array.isArray(tests) ? tests.filter((test: any) =>
                            test.status === 'running' || test.status === 'queued'
                          ) : [];



                      if (activeTests.length > 0) {
                        return (
                          <div className="divide-y divide-gray-200 dark:divide-gray-800">
                            {/* Show only running and queued tests during active run */}
                            {activeTests.map((test: any) => {
                              // Handle both step progress summary format and regular test format
                              const testId = test.testId || test.id;
                              const testStatus = test.currentStepStatus === 'started' ? 'running' : (test.currentStepStatus || test.status || 'running');
                              // Improved scenario name fallback logic - prioritize scenarioName from step progress
                              const scenarioName = test.scenarioName || test.name || `Scenario ${test.scenarioId || testId}`;

                              return (
                                <div
                                  key={testId}
                                  className={`
                                    relative overflow-hidden transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-900/60
                                    ${testStatus === 'running' ? 'border-l-4 border-blue-400 dark:border-blue-700' : ''}
                                    ${testStatus === 'queued' ? 'border-l-4 border-yellow-400 dark:border-yellow-700' : ''}
                                    ${testStatus === 'completed' ? 'border-l-4 border-green-400 dark:border-green-700' : ''}
                                    ${testStatus === 'failed' ? 'border-l-4 border-red-400 dark:border-red-700' : ''}
                                    ${testStatus === 'stopped' ? 'border-l-4 border-gray-400 dark:border-gray-700' : ''}
                                  `}
                                >
                            <div className="p-4 md:p-5 flex flex-col md:flex-row md:items-center gap-4">
                              {/* Status Badge */}
                              <div className="md:w-28">
                                {testStatus === 'running' && (
                                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 w-full justify-center">
                                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500 mr-1.5 animate-pulse"></div>
                                    Running
                                  </Badge>
                                )}
                                {testStatus === 'queued' && (
                                  <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 w-full justify-center">
                                    <Clock className="h-3 w-3 mr-1" />
                                    Queued
                                  </Badge>
                                )}
                                {testStatus === 'completed' && (
                                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 w-full justify-center">
                                    <CheckCircle className="h-3 w-3 mr-1" />
                                    Completed
                                  </Badge>
                                )}
                                {testStatus === 'failed' && (
                                  <Badge className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 w-full justify-center">
                                    <X className="h-3 w-3 mr-1" />
                                    Failed
                                  </Badge>
                                )}
                                {testStatus === 'stopped' && (
                                  <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 w-full justify-center">
                                    <Square className="h-3 w-3 mr-1" />
                                    Stopped
                                  </Badge>
                                )}
                              </div>

                              {/* Scenario Info */}
                              <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">{scenarioName}</h3>
                                <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">ID: {testId}</div>

                                {/* Progress for running tests */}
                                {testStatus === 'running' && (
                                  <div className="mt-2 space-y-2 max-w-md">
                                    {/* Step progress from optimized hook */}
                                    {(test.currentStep !== undefined && test.totalSteps > 0) || (test.steps && test.steps.length > 0) ? (
                                      <NestedStepProgress
                                        steps={test.steps || []}
                                        currentStep={test.currentStep || 0}
                                        totalSteps={test.totalSteps || 0}
                                        currentStepName={test.currentStepName || 'Processing...'}
                                        showExpanded={true}
                                      />
                                    ) : (
                                      <span className="text-blue-600 dark:text-blue-400 font-medium truncate mr-2">
                                        Executing test...
                                      </span>
                                    )}

                                    {/* Time information */}
                                    {test.lastUpdated && (
                                      <div className="flex justify-between items-center text-xs pt-1">
                                        <div className="text-gray-500 dark:text-gray-400">
                                          <span className="font-medium">Last Updated:</span>
                                          <span className="ml-1">
                                            {formatDistanceToNow(new Date(test.lastUpdated), { addSuffix: true })}
                                          </span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {/* Queue information for queued tests */}
                                {testStatus === 'queued' && (
                                  <div className="mt-2 space-y-1 max-w-md">
                                    <div className="flex items-center justify-between text-xs">
                                      <span className="text-yellow-600 dark:text-yellow-400 font-medium">
                                        Waiting to start...
                                      </span>
                                      <span className="text-gray-500 dark:text-gray-400">
                                        In queue
                                      </span>
                                    </div>

                                    <div className="w-full bg-gray-100 dark:bg-gray-800 rounded-full h-1.5 overflow-hidden">
                                      <div
                                        className="bg-gradient-to-r from-yellow-300 to-yellow-400 h-1.5 rounded-full animate-pulse"
                                        style={{ width: '15%' }}
                                      />
                                    </div>

                                    {test.lastUpdated && (
                                      <div className="text-xs pt-1 mt-1 border-t border-gray-100 dark:border-gray-800">
                                        <span className="text-gray-500 dark:text-gray-400">
                                          Last updated: {formatDistanceToNow(new Date(test.lastUpdated), { addSuffix: true })}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>

                              {/* Timing info */}
                              <div className="flex gap-4 text-xs">
                                <div className="text-gray-500 dark:text-gray-400">
                                  <span className="font-medium">Started:</span>
                                  <div>{test.lastUpdated ? format(new Date(test.lastUpdated), 'HH:mm:ss') : '-'}</div>
                                </div>
                              </div>

                              {/* Actions */}
                              <div className="flex gap-2 md:flex-col lg:flex-row">
                                {testStatus === 'running' && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-8 text-red-600 dark:text-red-400 border border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20"
                                    onClick={() => handleStopTest(testId)}
                                  >
                                    <Square className="h-3.5 w-3.5 mr-1" />
                                    Stop
                                  </Button>
                                )}
                                {((testStatus === 'completed' || testStatus === 'failed') && scenarioReports) && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-8 text-blue-600 dark:text-blue-400 border border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                                    onClick={() => {
                                      // Find the corresponding report and navigate to it
                                      const report = scenarioReports.find(r => r.scenarioId === testId);
                                      if (report) {
                                        router.push(`/reports/scenarios/${report.id}`);
                                      }
                                    }}
                                  >
                                    <Eye className="h-3.5 w-3.5 mr-1" />
                                    View Report
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                              );
                            })}
                          </div>
                        );
                      } else {
                        return (
                          <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                            {run.status === 'created' ? (
                              <>
                                <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-full">
                                  <Play className="h-6 w-6 text-gray-400 dark:text-gray-500" />
                                </div>
                                <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">No Tests Created</p>
                                <p>Start the run to create and execute test scenarios.</p>
                                <div className="mt-4">
                                  <Button
                                    variant="default"
                                    onClick={() => handleRunAction('start')}
                                    disabled={refreshingStatus}
                                    className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700"
                                  >
                                    <Play className="h-4 w-4 mr-1" />
                                    Start Run
                                  </Button>
                                </div>
                              </>
                            ) : run.status === 'running' || run.status === 'queued' ? (
                              <>
                                <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-blue-100 dark:bg-blue-900/30 rounded-full">
                                  <div className="h-10 w-10 rounded-full border-4 border-blue-200 dark:border-blue-900/30 border-t-blue-500 dark:border-t-blue-400 animate-spin"></div>
                                </div>
                                <p className="text-lg font-medium text-blue-600 dark:text-blue-400 mb-2">Preparing Tests</p>
                                <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                                  Test scenarios are being created and will appear above once they start executing.
                                </p>
                              </>
                            ) : (
                              <>
                                <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-full">
                                  <CheckCircle className="h-6 w-6 text-green-400 dark:text-green-500" />
                                </div>
                                <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Run Completed</p>
                                <p>This run has finished. View the Reports tab to see test results.</p>
                                <div className="mt-4">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      // Run report sayfasına yönlendir - run.latestRunReportId varsa onu kullan, yoksa runId'yi kullan
                                      const reportId = run.latestRunReportId || runId;
                                      router.push(`/reports/${reportId}`);
                                    }}
                                  >
                                    <BarChart className="h-4 w-4 mr-1" />
                                    View Run Report
                                  </Button>
                                </div>
                              </>
                            )}
                          </div>
                        );
                      }
                    })()}
                  </div>
                </TabsContent>

                <TabsContent value="reports" className="space-y-4">
                  <div className="bg-white dark:bg-gray-900 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-800">
                    <div className="bg-gray-50 dark:bg-gray-900/60 p-4 border-b border-gray-200 dark:border-gray-800">
                      <h3 className="text-lg font-medium">Scenario Reports</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Showing all test reports from the latest execution of this run. Click on a report to see details.
                      </p>
                    </div>

                    <div className="overflow-x-auto">
                      {shouldShowReports() ? (
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800 rounded-md overflow-hidden">
                          <thead className="bg-gray-50 dark:bg-gray-900/50">
                            <tr>
                              <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">Status</th>
                              <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">Scenario</th>
                              {/* Show Device column for Android runs */}
                              {run.platform === 'android' && (
                                <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">Device</th>
                              )}
                              <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">Start Time</th>
                              <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">Duration</th>
                              <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">Steps</th>
                              <th className="px-4 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-left">Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {/* Tüm senaryo raporlarını göster */}
                            {Array.isArray(scenarioReports) && scenarioReports.map((report, index) => (
                              <tr key={report.id} className={`border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-900/60 transition-colors duration-150 ${index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-900/60'}`}>
                                <td className="px-4 py-2">
                                  <div className="flex items-center">
                                    {report.status === 'running' && (
                                      <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                        <div className="h-1.5 w-1.5 rounded-full bg-blue-500 mr-1.5 animate-pulse"></div>
                                        Running
                                      </Badge>
                                    )}
                                    {(report.status === 'passed' || report.status === 'completed') && (
                                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                        <CheckCircle className="h-3 w-3 mr-1" />
                                        Passed
                                      </Badge>
                                    )}
                                    {report.status === 'failed' && (
                                      <Badge className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                        <X className="h-3 w-3 mr-1" />
                                        Failed
                                      </Badge>
                                    )}
                                    {report.status === 'queued' && (
                                      <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                                        <Clock className="h-3 w-3 mr-1" />
                                        Queued
                                      </Badge>
                                    )}
                                    {report.status === 'stopped' && (
                                      <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                                        <Square className="h-3 w-3 mr-1" />
                                        Stopped
                                      </Badge>
                                    )}
                                    {!report.status && (
                                      <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                                        <HelpCircle className="h-3 w-3 mr-1" />
                                        Unknown
                                      </Badge>
                                    )}
                                  </div>
                                </td>
                                <td className="px-4 py-2">
                                  <div className="font-medium text-sm">{report.scenarioName || report.name || 'Unnamed Scenario'}</div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400">{report.scenarioId}</div>
                                </td>
                                {/* Show Device information for Android runs */}
                                {run.platform === 'android' && (
                                  <td className="px-4 py-2">
                                    {(() => {
                                      // Try deviceInfo first (computed by backend)
                                      if (report.deviceInfo) {
                                        return (
                                          <div className="text-sm">
                                            <div className="font-medium text-gray-900 dark:text-gray-100">
                                              {report.deviceInfo.name || report.deviceInfo.id || 'Unknown Device'}
                                            </div>
                                            {report.deviceInfo.osVersion && (
                                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                                Android {report.deviceInfo.osVersion}
                                              </div>
                                            )}
                                          </div>
                                        );
                                      }
                                      
                                      // Fallback to environmentSettings
                                      const envSettings = report.environmentSettings;
                                      if (envSettings?.platform === 'android' && 
                                          envSettings.sauceLabs?.selectedDevices?.length > 0) {
                                        const device = envSettings.sauceLabs.selectedDevices[0];
                                        return (
                                          <div className="text-sm">
                                            <div className="font-medium text-gray-900 dark:text-gray-100">
                                              {device.name || device.id || 'Unknown Device'}
                                            </div>
                                            {device.osVersion && (
                                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                                Android {device.osVersion}
                                              </div>
                                            )}
                                          </div>
                                        );
                                      }
                                      
                                      return (
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                          Unknown Device
                                        </div>
                                      );
                                    })()}
                                  </td>
                                )}
                                <td className="px-4 py-2 text-sm">
                                  {report.startTime ? format(new Date(report.startTime), 'yyyy-MM-dd HH:mm:ss') :
                                   report.createdAt ? format(new Date(report.createdAt), 'yyyy-MM-dd HH:mm:ss') :
                                   report.date ? format(new Date(report.date), 'yyyy-MM-dd HH:mm:ss') : '-'}
                                </td>
                                <td className="px-4 py-2 text-sm">
                                  {getFormattedDuration(report, { isRunning: report.status === 'running' })}
                                </td>
                                <td className="px-4 py-2 text-sm">
                                  {report.scenario?.steps?.length ? (
                                    <div className="flex items-center space-x-1">
                                      <span className="text-green-600 dark:text-green-400">{Array.isArray(report.steps) ? report.steps.filter((s: any) => s.success === true).length : 0}</span>
                                      <span>/</span>
                                      <span>{report.scenario.steps.length}</span>
                                      {Array.isArray(report.steps) && report.steps.some((s: any) => s.success === false) && (
                                        <span className="text-red-600 dark:text-red-400 ml-1">
                                          ({report.steps.filter((s: any) => s.success === false).length} failed)
                                        </span>
                                      )}
                                      <div className="ml-2 w-16 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                        <div
                                          className={`h-full ${Array.isArray(report.steps) && report.steps.some((s: any) => s.success === false)
                                            ? "bg-gradient-to-r from-red-400 to-red-500"
                                            : "bg-gradient-to-r from-green-400 to-green-500"}`}
                                          style={{width: `${report.scenario.steps.length ? ((Array.isArray(report.steps) ? report.steps.filter((s: any) => s.success === true).length : 0) / report.scenario.steps.length * 100) : 0}%`}}
                                        />
                                      </div>
                                    </div>
                                  ) : Array.isArray(report.steps) && report.steps.length > 0 ? (
                                    <div className="flex items-center space-x-1">
                                      <span className="text-green-600 dark:text-green-400">{report.steps.filter((s: any) => s.success === true).length}</span>
                                      <span>/</span>
                                      <span>{report.steps.length}</span>
                                      {report.steps.some((s: any) => s.success === false) && (
                                        <span className="text-red-600 dark:text-red-400 ml-1">
                                          ({report.steps.filter((s: any) => s.success === false).length} failed)
                                        </span>
                                      )}
                                      <div className="ml-2 w-16 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                        <div
                                          className={`h-full ${report.steps.some((s: any) => s.success === false)
                                            ? "bg-gradient-to-r from-red-400 to-red-500"
                                            : "bg-gradient-to-r from-green-400 to-green-500"}`}
                                          style={{width: `${report.steps.length ? (report.steps.filter((s: any) => s.success === true).length / report.steps.length * 100) : 0}%`}}
                                        />
                                      </div>
                                    </div>
                                  ) : report.stepsCount ? (
                                    <div className="flex items-center space-x-1">
                                      <span className="text-green-600 dark:text-green-400">{report.completedStepsCount || 0}</span>
                                      <span>/</span>
                                      <span>{report.summary?.total || report.stepsCount}</span>
                                      {report.status === 'failed' && (
                                        <span className="text-red-600 dark:text-red-400 ml-1">
                                          (failed)
                                        </span>
                                      )}
                                      <div className="ml-2 w-16 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                        <div
                                          className={`h-full ${report.status === 'failed'
                                            ? "bg-gradient-to-r from-red-400 to-red-500"
                                            : "bg-gradient-to-r from-green-400 to-green-500"}`}
                                          style={{width: `${(report.summary?.total || report.stepsCount) ? ((report.completedStepsCount || 0) / (report.summary?.total || report.stepsCount) * 100) : 0}%`}}
                                        />
                                      </div>
                                    </div>
                                  ) : '-'}
                                </td>
                                <td className="px-4 py-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 px-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 hover:scale-105"
                                    onClick={() => router.push(`/reports/scenarios/${report.id}`)}
                                  >
                                    <Eye className="h-3.5 w-3.5 mr-1" />
                                    View Report
                                  </Button>
                                  {run.latestRunReportId && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 px-2 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200 hover:scale-105 ml-1"
                                      onClick={() => router.push(`/reports/${run.latestRunReportId}`)}
                                    >
                                      <BarChart className="h-3.5 w-3.5 mr-1" />
                                      Run Report
                                    </Button>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : run && (run.status === 'completed' || run.status === 'failed' || run.status === 'stopped') ? (
                        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                          No reports found for this run. Reports may not be available for older runs.
                        </div>
                      ) : run && (run.status === 'running' || run.status === 'queued') ? (
                        <div className="p-8 text-center">
                          <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-blue-100 dark:bg-blue-900/30 rounded-full">
                            <div className="h-10 w-10 rounded-full border-4 border-blue-200 dark:border-blue-900/30 border-t-blue-500 dark:border-t-blue-400 animate-spin"></div>
                          </div>
                          <p className="text-lg font-medium text-blue-600 dark:text-blue-400 mb-2">Waiting for Tests to Start</p>
                          <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                            Test reports will appear here as soon as scenarios start running.
                          </p>
                          <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                            <span className="font-medium">{countTestsByStatus().running}</span> running,
                            <span className="font-medium ml-1">{countTestsByStatus().queued}</span> queued,
                            <span className="font-medium ml-1">{countTestsByStatus().completed}</span> completed
                          </div>
                        </div>
                      ) : (
                        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                          Reports will be available after the run is completed.
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Environment Card */}
                    <Card className="border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-md transition-all duration-200">
                      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 py-3">
                        <CardTitle className="flex items-center text-base">
                          <Settings2 className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                          Environment
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 space-y-2">
                        {/* Platform indicator */}
                        <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                          <div className="flex items-center">
                            {run.platform === "web" ? (
                              <Globe className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                            ) : (
                              <Smartphone className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                            )}
                            <span className="text-sm font-medium">Platform</span>
                          </div>
                          <Badge
                            variant="outline"
                            className={`capitalize ${
                              run.platform === "web"
                                ? "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800"
                                : run.platform === "android"
                                ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800"
                                : ""
                            }`}
                          >
                            {run.platform === "web" ? "Web" : run.platform === "android" ? "Android" : run.platform || "Unknown"}
                          </Badge>
                        </div>

                        {/* Platform-specific environment details */}
                        {run.platform === "web" ? (
                          <>
                            <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                              <div className="flex items-center">
                                <Monitor className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                                <span className="text-sm font-medium">Browser</span>
                              </div>
                              <Badge variant="outline" className="capitalize">
                                {run.environment?.browser || 'chrome'}
                              </Badge>
                            </div>

                            <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                              <div className="flex items-center">
                                <Maximize2 className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                                <span className="text-sm font-medium">Viewport</span>
                              </div>
                              <Badge variant="outline">
                                {run.environment?.viewport ? `${run.environment.viewport.width}x${run.environment.viewport.height}` : '1920x1080'}
                              </Badge>
                            </div>

                            <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                              <div className="flex items-center">
                                <Network className="h-4 w-4 mr-2 text-blue-600 dark:text-blue-400" />
                                <span className="text-sm font-medium">Network</span>
                              </div>
                              <Badge variant="outline" className="capitalize">
                                {run.environment?.networkSpeed || 'Normal'}
                              </Badge>
                            </div>
                          </>
                        ) : run.platform === "android" ? (
                          <>
                            <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                              <div className="flex items-center">
                                <Smartphone className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                                <span className="text-sm font-medium">Device Provider</span>
                              </div>
                              <Badge variant="outline" className="capitalize">
                                {run.deviceProvider === 'sauceLabs' ? 'SauceLabs' :
                                 run.deviceProvider === 'testinium' ? 'Testinium' :
                                 run.deviceProvider || 'SauceLabs'}
                              </Badge>
                            </div>

                            {run.environment?.sauceLabs?.selectedDevices?.length > 0 && (
                              <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                                <div className="flex items-center">
                                  <Smartphone className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                                  <span className="text-sm font-medium">Devices</span>
                                </div>
                                <Badge variant="outline">
                                  {run.environment.sauceLabs.selectedDevices.length} device{run.environment.sauceLabs.selectedDevices.length > 1 ? 's' : ''}
                                </Badge>
                              </div>
                            )}

                            {run.environment?.testDistribution?.strategy && (
                              <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                                <div className="flex items-center">
                                  <Settings2 className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                                  <span className="text-sm font-medium">Distribution</span>
                                </div>
                                <Badge variant="outline" className="capitalize">
                                  {run.environment.testDistribution.strategy.replace('-', ' ')}
                                </Badge>
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="p-2 rounded-md bg-gray-50 dark:bg-gray-900/60 text-center">
                            <span className="text-sm text-gray-500 dark:text-gray-400">Unknown platform configuration</span>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* AI Model Card */}
                    <Card className="border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-md transition-all duration-200">
                      <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 py-3">
                        <CardTitle className="flex items-center text-base">
                          <Zap className="h-4 w-4 mr-2 text-emerald-600 dark:text-emerald-400" />
                          AI Model
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 space-y-2">
                        <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                          <div className="flex items-center">
                            <Layers className="h-4 w-4 mr-2 text-emerald-600 dark:text-emerald-400" />
                            <span className="text-sm font-medium">Model</span>
                          </div>
                          <Badge variant="outline" className="bg-emerald-50 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800">
                            {run.environment?.aiModelName || 'Default'}
                          </Badge>
                        </div>

                        {run.environment?.aiModelConfig ? (
                          <div className="p-2 rounded-md bg-emerald-50/50 dark:bg-emerald-900/10 border border-emerald-100 dark:border-emerald-900/20">
                            <p className="text-xs text-emerald-700 dark:text-emerald-300">
                              Using custom AI model configuration
                            </p>
                          </div>
                        ) : (
                          <div className="p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Using default AI model settings
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Test Data Environment Card */}
                    {(run as any).testDataEnvironmentName && (
                      <Card className="border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-md transition-all duration-200">
                        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 py-3">
                          <CardTitle className="flex items-center text-base">
                            <Database className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                            Test Data Environment
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="p-3 space-y-2">
                          <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                            <div className="flex items-center">
                              <Database className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                              <span className="text-sm font-medium">Environment</span>
                            </div>
                            <Badge variant="outline" className="bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">
                              {(run as any).testDataEnvironmentName}
                            </Badge>
                          </div>

                          <div className="p-2 rounded-md bg-green-50/50 dark:bg-green-900/10 border border-green-100 dark:border-green-900/20">
                            <p className="text-xs text-green-700 dark:text-green-300">
                              Test data variables will use values from this environment
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Report Settings Card */}
                    <Card className="border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-md transition-all duration-200">
                      <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 py-3">
                        <CardTitle className="flex items-center text-base">
                          <BarChart className="h-4 w-4 mr-2 text-purple-600 dark:text-purple-400" />
                          Reporting
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                            <div className="flex items-center">
                              <Camera className="h-3.5 w-3.5 mr-1.5 text-purple-600 dark:text-purple-400" />
                              <span className="text-xs font-medium">Screenshots</span>
                            </div>
                            <Badge
                              variant={run.reportSettings?.takeScreenshots ? "default" : "outline"}
                              className={run.reportSettings?.takeScreenshots
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 text-xs"}
                            >
                              {run.reportSettings?.takeScreenshots ? 'On' : 'Off'}
                            </Badge>
                          </div>

                          <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                            <div className="flex items-center">
                              <Video className="h-3.5 w-3.5 mr-1.5 text-purple-600 dark:text-purple-400" />
                              <span className="text-xs font-medium">Videos</span>
                            </div>
                            <Badge
                              variant={run.reportSettings?.takeVideos ? "default" : "outline"}
                              className={run.reportSettings?.takeVideos
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 text-xs"}
                            >
                              {run.reportSettings?.takeVideos ? 'On' : 'Off'}
                            </Badge>
                          </div>

                          <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                            <div className="flex items-center">
                              <LineChart className="h-3.5 w-3.5 mr-1.5 text-purple-600 dark:text-purple-400" />
                              <span className="text-xs font-medium">Metrics</span>
                            </div>
                            <Badge
                              variant={run.reportSettings?.pageMetrics ? "default" : "outline"}
                              className={run.reportSettings?.pageMetrics
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 text-xs"}
                            >
                              {run.reportSettings?.pageMetrics ? 'On' : 'Off'}
                            </Badge>
                          </div>

                          <div className="flex items-center justify-between p-2 rounded-md bg-gray-50 dark:bg-gray-900/60">
                            <div className="flex items-center">
                              <Network className="h-3.5 w-3.5 mr-1.5 text-purple-600 dark:text-purple-400" />
                              <span className="text-xs font-medium">Network</span>
                            </div>
                            <Badge
                              variant={run.reportSettings?.networkData ? "default" : "outline"}
                              className={run.reportSettings?.networkData
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 text-xs"}
                            >
                              {run.reportSettings?.networkData ? 'On' : 'Off'}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                <X className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">Run Not Found</h3>
              <p className="text-gray-500 dark:text-gray-400 text-center max-w-md mb-6">
                The test run you're looking for doesn't exist or you don't have permission to view it.
              </p>
              <Button
                onClick={() => router.push('/runs')}
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Runs
              </Button>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}

export default function RunDetailPage() {
  return (
    <ProtectedRoute>
      <RunDetailContent />
    </ProtectedRoute>
  )
}