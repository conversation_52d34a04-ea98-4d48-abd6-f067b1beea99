/**
 * Test Execution Factories
 *
 * Exports factory implementations for the test execution module.
 */
export { TestRunnerFactory } from './test-runner-factory.js';
export { TestCommunicationFactory } from './test-communication-factory.js';
export { createTestQueueWorker, TestQueueWorkerFactory } from './test-queue-worker-factory.js';
export type { Platform, TestRunnerDependencies } from './test-runner-factory.js';
export type { TestCommunicationFactoryConfig } from './test-communication-factory.js';
