import { useState, useEffect, useCallback, useRef } from "react";

/**
 * A hook that delays updating a value until a specified delay has passed
 * @param value The value to debounce
 * @param delay The delay in milliseconds (default: 500ms)
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number = 500): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set a timeout to update the debounced value after the delay
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Clean up the timeout if the value changes before the delay has passed
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Options for debounced input handling
 */
interface UseDebouncedInputOptions {
  delay?: number;
  immediate?: boolean;
}

/**
 * Enhanced debounced input hook for form inputs and store updates
 * Provides local state management with debounced callbacks
 */
export function useDebouncedInput(
  initialValue: string,
  onDebouncedChange: (value: string) => void,
  options: UseDebouncedInputOptions = {},
) {
  const { delay = 300, immediate = false } = options;

  // Local state for immediate UI updates
  const [localValue, setLocalValue] = useState(initialValue);

  // Ref to track if we should update local value from props
  const isUpdatingFromProps = useRef(false);

  // Debounced effect for store updates
  useEffect(() => {
    if (isUpdatingFromProps.current) {
      isUpdatingFromProps.current = false;
      return;
    }

    const timer = setTimeout(() => {
      if (localValue !== initialValue) {
        onDebouncedChange(localValue);
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [localValue, delay, onDebouncedChange, initialValue]);

  // Update local value when prop changes (external updates)
  useEffect(() => {
    if (initialValue !== localValue) {
      isUpdatingFromProps.current = true;
      setLocalValue(initialValue);
    }
  }, [initialValue]);

  // Handler for input changes
  const handleChange = useCallback(
    (value: string) => {
      setLocalValue(value);

      // If immediate mode, also call the callback immediately
      if (immediate) {
        onDebouncedChange(value);
      }
    },
    [immediate, onDebouncedChange],
  );

  // Force immediate update (useful for blur events)
  const forceUpdate = useCallback(() => {
    if (localValue !== initialValue) {
      onDebouncedChange(localValue);
    }
  }, [localValue, initialValue, onDebouncedChange]);

  return {
    value: localValue,
    onChange: handleChange,
    forceUpdate,
  };
}

/**
 * Specialized hook for variable input fields
 */
export function useDebouncedVariableInput<K extends string>(
  variableId: string,
  field: K,
  initialValue: string,
  onUpdate: (id: string, field: K, value: string) => void,
  delay = 300,
) {
  const handleDebouncedChange = useCallback(
    (value: string) => {
      onUpdate(variableId, field, value);
    },
    [variableId, field, onUpdate],
  );

  return useDebouncedInput(initialValue, handleDebouncedChange, { delay });
}
