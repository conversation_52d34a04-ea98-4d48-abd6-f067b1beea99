/**
 * Browser Manager Interface
 * Defines the contract for browser management functionality
 */

import { <PERSON><PERSON><PERSON>, <PERSON> } from 'rebrowser-puppeteer';

/**
 * Browser Manager Interface
 * Handles browser initialization, page creation, and agent management
 */
export interface IBrowserManager {
  /**
   * Create a browser instance
   * @param options Browser options
   * @returns Promise resolving to a browser instance
   */
  createBrowser(options?: any): Promise<Browser>;

  /**
   * Create a page in a browser
   * @param browser Browser instance
   * @param options Page options
   * @returns Promise resolving to a page
   */
  createPage(browser: Browser, options?: any): Promise<Page>;

  /**
   * Close a browser instance
   * @param browser Browser instance to close
   */
  closeBrowser(browser: Browser): Promise<void>;

  /**
   * Create an AI agent for a page
   * @param page Page to create agent for
   * @param testId Test identifier for caching
   * @returns Promise resolving to an agent
   */
  createAgent(page: Page, testId?: string): Promise<any>;

  /**
   * Get the AI agent for a page
   * @param page Page to get agent for
   * @returns Agent or undefined if not found
   */
  getPageAgent(page: Page): any;

  /**
   * Clean up the agent for a specific page
   * @param page Page to clean up agent for
   * @returns Promise resolving to true if cleanup was successful
   */
  cleanupPageAgent(page: Page): Promise<boolean>;

  /**
   * Initialize the browser manager
   * @param config Configuration object
   */
  initialize(config: any): Promise<void>;
}
