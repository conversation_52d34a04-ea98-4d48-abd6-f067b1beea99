export interface Report {
  id: string
  name: string
  description?: string
  date: string
  createdAt?: string
  rawDate?: string
  duration: string | number
  rawDuration?: number
  status: string
  result?: string
  passed: number
  failed: number
  skipped?: number
  totalTests: number
  passRate: number
  browser?: string
  platform?: 'web' | 'android' // Platform the test was run on (web or android)
  triggeredBy: string
  folderId?: string
  folderName?: string
}

export interface ReportFilters {
  startDate?: string
  endDate?: string
  status?: string
  folderId?: string
  search?: string
}

export interface ReportSortOptions {
  field: 'date' | 'name' | 'duration' | 'passRate'
  order: 'asc' | 'desc'
}