/**
 * While Loop Control Flow Handler
 * Handles while loop control flow steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';

/**
 * While Loop Control Flow Handler
 * Executes a loop while a condition remains true based on AI boolean evaluation
 */
export class WhileLoopHandler implements IStepHandler {
  private stepExecutor: any; // Will be injected by the registry
  private progressReporter: any = null; // Will be injected by the registry
  private static readonly DEFAULT_MAX_ITERATIONS = 50; // Default safety limit

  /**
   * Set the step executor (used for executing nested steps)
   * @param executor Function to execute nested steps
   */
  setStepExecutor(executor: any): void {
    this.stepExecutor = executor;
  }

  /**
   * Set the progress reporter (used for reporting nested step progress)
   * @param progressReporter Progress reporter instance
   */
  setProgressReporter(progressReporter: any): void {
    this.progressReporter = progressReporter;
  }

  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'whileLoop';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'WhileLoopHandler';
  }

  /**
   * Execute a while loop step
   * @param target The target to execute the step on
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    const condition = step.condition || step.value || step.prompt;
    
    if (!condition) {
      const errorMessage = 'Missing condition for while loop step';
      logs.push(`❌ While Loop Error: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    if (!step.loopSteps || step.loopSteps.length === 0) {
      const errorMessage = 'While loop step must have loop steps to execute';
      logs.push(`❌ While Loop Error: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    // Set maximum iterations to prevent infinite loops
    const maxIterations = step.maxIterations || WhileLoopHandler.DEFAULT_MAX_ITERATIONS;
    
    if (maxIterations > 100) {
      const errorMessage = `Maximum iterations too high (${maxIterations}). Maximum allowed is 100 for safety.`;
      logs.push(`❌ While Loop Error: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }

    try {
      logs.push(`🔄 Starting while loop with condition: "${condition}"`);
      logs.push(`🛡️ Safety limit: ${maxIterations} iterations`);
      logger.debug(`WhileLoopHandler: Starting while loop with condition "${condition}"`);

      let iteration = 0;
      let totalStepsExecuted = 0;
      let lastConditionResult = false;

      while (iteration < maxIterations) {
        iteration++;
        
        logs.push(`🔍 Iteration ${iteration}: Evaluating condition "${condition}"`);
        
        // Check if agent is available for AI operations
        if (!agent) {
          const errorMessage = `AI agent is not available for condition evaluation in iteration ${iteration}`;
          logs.push(`❌ While Loop Error: ${errorMessage}`);
          logger.error(`WhileLoopHandler: Agent is null - this indicates an issue with agent passing in nested execution`);
          return { success: false, error: errorMessage };
        }

        // Evaluate the condition using AI - use aiBoolean for boolean evaluation
        const conditionEvaluation = await agent.aiBoolean(condition);
        
        if (conditionEvaluation.error) {
          const errorMessage = `Condition evaluation failed in iteration ${iteration}: ${conditionEvaluation.error}`;
          logs.push(`❌ While Loop Error: ${errorMessage}`);
          return { success: false, error: errorMessage };
        }

        lastConditionResult = conditionEvaluation.result === true;
        logs.push(`✅ Condition result: ${lastConditionResult ? 'TRUE' : 'FALSE'}`);

        // If condition is false, exit the loop
        if (!lastConditionResult) {
          logs.push(`🏁 While loop completed: condition is FALSE`);
          break;
        }

        logs.push(`📍 Executing ${step.loopSteps.length} steps for iteration ${iteration}`);

        // Execute the loop steps
        for (let stepIndex = 0; stepIndex < step.loopSteps.length; stepIndex++) {
          const loopStep = step.loopSteps[stepIndex];
          
          if (!this.stepExecutor) {
            const errorMessage = 'Step executor not available for nested step execution';
            logs.push(`❌ While Loop Error: ${errorMessage}`);
            return { success: false, error: errorMessage };
          }

          const stepResult = await this.stepExecutor(target, loopStep, agent, logs, step.id || `while-loop-${Date.now()}`, 1);
          totalStepsExecuted++;

          // Store step result with screenshot URLs for reporting (optional enhancement)
          if (stepResult.beforeScreenshotUrl || stepResult.afterScreenshotUrl) {
            logs.push(`📸 While Loop: Screenshots captured for step ${stepIndex + 1} in iteration ${iteration}`);
          }

          if (!stepResult.success) {
            logs.push(`❌ While Loop: Step ${stepIndex + 1} failed in iteration ${iteration}: ${stepResult.error}`);
            return { 
              success: false, 
              error: `Loop step failed in iteration ${iteration}: ${stepResult.error}`,
              data: { 
                maxIterations,
                completedIterations: iteration - 1,
                totalStepsExecuted,
                failedIteration: iteration,
                failedStepIndex: stepIndex,
                lastConditionResult
              }
            };
          }
        }

        logs.push(`✅ Completed iteration ${iteration}`);
      }

      // Check if we hit the maximum iteration limit
      if (iteration >= maxIterations && lastConditionResult) {
        const warningMessage = `While loop reached maximum iteration limit (${maxIterations}) - stopping for safety`;
        logs.push(`⚠️ While Loop Warning: ${warningMessage}`);
        logger.warn(`WhileLoopHandler: ${warningMessage}`);
        
        return { 
          success: true, 
          error: null, 
          data: { 
            maxIterations,
            completedIterations: iteration,
            totalStepsExecuted,
            hitMaxIterations: true,
            lastConditionResult
          }
        };
      }

      logs.push(`✅ While loop completed successfully (${iteration} iterations, ${totalStepsExecuted} total steps executed)`);
      return { 
        success: true, 
        error: null, 
        data: { 
          maxIterations,
          completedIterations: iteration,
          totalStepsExecuted,
          hitMaxIterations: false,
          lastConditionResult
        }
      };

    } catch (error: any) {
      const errorMessage = `While loop execution failed: ${error.message}`;
      logs.push(`❌ While Loop Error: ${errorMessage}`);
      logger.error(`WhileLoopHandler: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }
} 