/**
 * Team API
 *
 * Takımlar ile ilgili API isteklerini yönetir.
 */

import { get, post, put, del } from './fetch-wrapper';
import { TEAM_ENDPOINTS } from './constants';

/**
 * Tüm takımları getirir
 */
export async function getTeams() {
  return get(TEAM_ENDPOINTS.GET_ALL);
}

/**
 * Belirli bir takımı getirir
 *
 * @param id Takım ID'si
 */
export async function getTeamById(id: string) {
  return get(TEAM_ENDPOINTS.GET_BY_ID(id));
}

/**
 * Yeni bir takım oluşturur
 *
 * @param team Takım verileri
 */
export async function createTeam(team: {
  name: string;
  description?: string;
  companyId?: string;
}) {
  return post(TEAM_ENDPOINTS.CREATE, team, {
    showSuccessToast: true,
    successMessage: 'Takım başarıyla oluşturuldu'
  });
}

/**
 * Bir takımı günceller
 *
 * @param id Takım ID'si
 * @param team Güncellenecek takım verileri
 */
export async function updateTeam(id: string, team: {
  name?: string;
  description?: string;
}) {
  return put(TEAM_ENDPOINTS.UPDATE(id), team, {
    showSuccessToast: true,
    successMessage: 'Takım başarıyla güncellendi'
  });
}

/**
 * Bir takımı siler
 *
 * @param id Takım ID'si
 */
export async function deleteTeam(id: string) {
  return del(TEAM_ENDPOINTS.DELETE(id), {
    showSuccessToast: true,
    successMessage: 'Takım başarıyla silindi'
  });
}

/**
 * Bir takımın üyelerini getirir
 *
 * @param teamId Takım ID'si
 */
export async function getTeamMembers(teamId: string) {
  return get(TEAM_ENDPOINTS.MEMBERS.GET_ALL(teamId));
}

/**
 * Belirli bir takım üyesini getirir
 *
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 */
export async function getTeamMemberById(teamId: string, userId: string) {
  return get(TEAM_ENDPOINTS.MEMBERS.GET_BY_ID(teamId, userId));
}

/**
 * Bir takıma üye ekler
 *
 * @param teamId Takım ID'si
 * @param member Üye verileri
 */
export async function addTeamMember(teamId: string, member: {
  userId: string;
  roleId: string;
}) {
  return post(TEAM_ENDPOINTS.MEMBERS.ADD(teamId), member, {
    showSuccessToast: true,
    successMessage: 'Takım üyesi başarıyla eklendi'
  });
}

/**
 * Bir takım üyesini günceller
 *
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 * @param member Güncellenecek üye verileri
 */
export async function updateTeamMember(teamId: string, userId: string, member: {
  roleId?: string;
  status?: string;
}) {
  return put(TEAM_ENDPOINTS.MEMBERS.UPDATE(teamId, userId), member, {
    showSuccessToast: true,
    successMessage: 'Takım üyesi başarıyla güncellendi'
  });
}

/**
 * Bir takımdan üye çıkarır
 *
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 */
export async function removeTeamMember(teamId: string, userId: string) {
  return del(TEAM_ENDPOINTS.MEMBERS.DELETE(teamId, userId), {
    showSuccessToast: true,
    successMessage: 'Takım üyesi başarıyla çıkarıldı'
  });
}

/**
 * Bir takıma toplu davet gönderir
 *
 * @param teamId Takım ID'si
 * @param data Davet verileri
 */
export async function bulkInviteTeamMembers(teamId: string, data: {
  emails: string[];
  roleId: string;
  password?: string;
}) {
  return post(TEAM_ENDPOINTS.MEMBERS.BULK_INVITE(teamId), data, {
    showSuccessToast: true,
    successMessage: 'Davetler başarıyla gönderildi'
  });
}

/**
 * Bir takım üyesinin durumunu günceller
 *
 * @param teamId Takım ID'si
 * @param userId Kullanıcı ID'si
 * @param status Yeni durum
 */
export async function updateTeamMemberStatus(teamId: string, userId: string, status: 'active' | 'inactive') {
  return put(TEAM_ENDPOINTS.MEMBERS.UPDATE_STATUS(teamId, userId), { status }, {
    showSuccessToast: true,
    successMessage: `Takım üyesi durumu başarıyla ${status === 'active' ? 'aktif' : 'deaktif'} edildi`
  });
}

/**
 * Bir takımın rollerini getirir
 *
 * @param teamId Takım ID'si
 */
export async function getTeamRoles(teamId: string) {
  return get(TEAM_ENDPOINTS.ROLES.GET_ALL(teamId));
}

/**
 * Belirli bir takım rolünü getirir
 *
 * @param teamId Takım ID'si
 * @param roleId Rol ID'si
 */
export async function getTeamRoleById(teamId: string, roleId: string) {
  return get(TEAM_ENDPOINTS.ROLES.GET_BY_ID(teamId, roleId));
}

/**
 * Bir takıma rol ekler
 *
 * @param teamId Takım ID'si
 * @param role Rol verileri
 */
export async function createTeamRole(teamId: string, role: {
  name: string;
  description?: string;
  permissions?: any[];
}) {
  return post(TEAM_ENDPOINTS.ROLES.CREATE(teamId), role, {
    showSuccessToast: true,
    successMessage: 'Takım rolü başarıyla oluşturuldu'
  });
}

/**
 * Bir takım rolünü günceller
 *
 * @param teamId Takım ID'si
 * @param roleId Rol ID'si
 * @param role Güncellenecek rol verileri
 */
export async function updateTeamRole(teamId: string, roleId: string, role: {
  name?: string;
  description?: string;
  permissions?: any[];
}) {
  return put(TEAM_ENDPOINTS.ROLES.UPDATE(teamId, roleId), role, {
    showSuccessToast: true,
    successMessage: 'Takım rolü başarıyla güncellendi'
  });
}

/**
 * Bir takım rolünü siler
 *
 * @param teamId Takım ID'si
 * @param roleId Rol ID'si
 */
export async function deleteTeamRole(teamId: string, roleId: string) {
  return del(TEAM_ENDPOINTS.ROLES.DELETE(teamId, roleId), {
    showSuccessToast: true,
    successMessage: 'Takım rolü başarıyla silindi'
  });
}
