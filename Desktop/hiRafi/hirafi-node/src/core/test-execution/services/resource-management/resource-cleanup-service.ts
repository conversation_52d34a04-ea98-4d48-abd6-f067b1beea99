/**
 * Resource Cleanup Service Implementation
 * 
 * Manages comprehensive resource cleanup including processes, browser instances,
 * temporary files, and network connections. Replaces the singleton ResourceManager
 * with a dependency-injected implementation.
 */

import {
  IResourceManager,
  ResourceCleanupOptions,
  ResourceCleanupResult,
  ResourceType,
  BrowserCleanupInfo,
  ProcessCleanupInfo,
  ResourceStatistics
} from '../../interfaces/resource-manager.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

/**
 * Resource tracking entry
 */
interface ResourceEntry {
  id: string;
  type: ResourceType;
  testId: string;
  createdAt: Date;
  metadata?: any;
}

/**
 * Resource Manager Implementation
 * 
 * Provides comprehensive resource management with proper cleanup
 * and tracking capabilities.
 */
export class ResourceManager implements IResourceManager {
  private logger: ILoggerService;
  private resources: Map<string, ResourceEntry> = new Map();
  private browserCleanupInfo: Map<string, BrowserCleanupInfo> = new Map();
  private processCleanupInfo: Map<string, ProcessCleanupInfo> = new Map();
  private testResources: Map<string, Set<string>> = new Map(); // testId -> resource IDs

  constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('ResourceManager');
  }

  /**
   * Initialize the resource manager
   */
  async initialize(): Promise<void> {
    this.resources.clear();
    this.browserCleanupInfo.clear();
    this.processCleanupInfo.clear();
    this.testResources.clear();

    this.logger.debug('ResourceManager: Initialized');
  }

  /**
   * Register browser cleanup information for a test
   */
  registerBrowserCleanup(testId: string, cleanupInfo: BrowserCleanupInfo): void {
    this.browserCleanupInfo.set(testId, cleanupInfo);
    this.addResource(testId, `browser-${testId}`, ResourceType.BROWSER);
    this.logger.debug(`ResourceManager: Registered browser cleanup info for test ${testId}`);
  }

  /**
   * Register process cleanup information for a test
   */
  registerProcessCleanup(testId: string, cleanupInfo: ProcessCleanupInfo): void {
    this.processCleanupInfo.set(testId, cleanupInfo);
    this.addResource(testId, `process-${cleanupInfo.processId}`, ResourceType.PROCESS);
    this.logger.debug(`ResourceManager: Registered process cleanup info for test ${testId}`);
  }

  /**
   * Add a resource to track for a test
   */
  addResource(testId: string, resourceId: string, resourceType: ResourceType): void {
    const resource: ResourceEntry = {
      id: resourceId,
      type: resourceType,
      testId,
      createdAt: new Date()
    };

    this.resources.set(resourceId, resource);

    // Add to test resources tracking
    if (!this.testResources.has(testId)) {
      this.testResources.set(testId, new Set());
    }
    this.testResources.get(testId)!.add(resourceId);

    this.logger.debug(`ResourceManager: Added ${resourceType} resource ${resourceId} for test ${testId}`);
  }

  /**
   * Remove a resource from tracking
   */
  removeResource(testId: string, resourceId: string): void {
    this.resources.delete(resourceId);

    const testResourceSet = this.testResources.get(testId);
    if (testResourceSet) {
      testResourceSet.delete(resourceId);
      if (testResourceSet.size === 0) {
        this.testResources.delete(testId);
      }
    }

    this.logger.debug(`ResourceManager: Removed resource ${resourceId} for test ${testId}`);
  }

  /**
   * Cleanup all resources for a specific test
   */
  async cleanupTestResources(options: ResourceCleanupOptions): Promise<ResourceCleanupResult> {
    const { testId, reason = 'Test completion', forceCleanup = false, timeout = 10000, cleanupTypes } = options;
    
    const startTime = Date.now();
    const cleanedResources: string[] = [];
    const errors: Error[] = [];

    this.logger.info(`ResourceManager: Starting cleanup for test ${testId}. Reason: ${reason}`);

    try {
      // Get resources for this test
      const testResourceIds = this.testResources.get(testId) || new Set();
      const resourcesToClean = Array.from(testResourceIds)
        .map(id => this.resources.get(id))
        .filter(resource => resource !== undefined) as ResourceEntry[];

      // Filter by cleanup types if specified
      const filteredResources = cleanupTypes 
        ? resourcesToClean.filter(resource => cleanupTypes.includes(resource.type))
        : resourcesToClean;

      // Cleanup each resource type
      for (const resourceType of Object.values(ResourceType)) {
        const resourcesOfType = filteredResources.filter(r => r.type === resourceType);
        if (resourcesOfType.length > 0) {
          try {
            await this.cleanupResourceType(testId, resourceType, resourcesOfType, timeout);
            cleanedResources.push(...resourcesOfType.map(r => r.id));
          } catch (error: any) {
            this.logger.error(`ResourceManager: Error cleaning ${resourceType} resources for test ${testId}: ${error.message}`);
            errors.push(error);
          }
        }
      }

      // Remove all resources for this test from tracking
      this.testResources.delete(testId);
      this.browserCleanupInfo.delete(testId);
      this.processCleanupInfo.delete(testId);

      // Remove individual resource entries
      for (const resourceId of testResourceIds) {
        this.resources.delete(resourceId);
      }

    } catch (error: any) {
      this.logger.error(`ResourceManager: Critical error during cleanup for test ${testId}: ${error.message}`);
      errors.push(error);
    }

    const duration = Date.now() - startTime;
    const success = errors.length === 0;

    if (success) {
      this.logger.info(`ResourceManager: Successfully cleaned up ${cleanedResources.length} resources for test ${testId} in ${duration}ms`);
    } else {
      this.logger.warn(`ResourceManager: Cleanup completed for test ${testId} with ${errors.length} errors in ${duration}ms`);
    }

    return {
      success,
      cleanedResources,
      errors,
      duration
    };
  }

  /**
   * Cleanup specific resource types for a test
   */
  async cleanupResourceTypes(
    testId: string,
    resourceTypes: ResourceType[],
    options: Partial<ResourceCleanupOptions> = {}
  ): Promise<ResourceCleanupResult> {
    return this.cleanupTestResources({
      testId,
      cleanupTypes: resourceTypes,
      ...options
    });
  }

  /**
   * Emergency cleanup of all resources
   */
  async emergencyCleanupAll(reason: string = 'Emergency cleanup'): Promise<void> {
    this.logger.warn(`ResourceManager: Performing emergency cleanup: ${reason}`);

    const allTestIds = Array.from(this.testResources.keys());
    const cleanupPromises = allTestIds.map(testId =>
      this.cleanupTestResources({
        testId,
        reason,
        forceCleanup: true,
        timeout: 5000
      }).catch(error => {
        this.logger.error(`ResourceManager: Error in emergency cleanup for test ${testId}: ${error.message}`);
      })
    );

    await Promise.allSettled(cleanupPromises);
    this.logger.info('ResourceManager: Emergency cleanup completed');
  }

  /**
   * Get resource statistics
   */
  getResourceStatistics(): ResourceStatistics {
    const resourcesByType: Record<ResourceType, number> = {
      [ResourceType.BROWSER]: 0,
      [ResourceType.PROCESS]: 0,
      [ResourceType.TEMPORARY_FILES]: 0,
      [ResourceType.NETWORK_CONNECTIONS]: 0,
      [ResourceType.LOCK_EXTENSIONS]: 0
    };

    const resourcesByTest: Record<string, number> = {};

    for (const resource of this.resources.values()) {
      resourcesByType[resource.type]++;
      resourcesByTest[resource.testId] = (resourcesByTest[resource.testId] || 0) + 1;
    }

    return {
      activeTests: this.testResources.size,
      totalResources: this.resources.size,
      resourcesByType,
      resourcesByTest
    };
  }

  /**
   * Get resources for a specific test
   */
  getResourcesForTest(testId: string): string[] {
    const testResourceIds = this.testResources.get(testId);
    return testResourceIds ? Array.from(testResourceIds) : [];
  }

  /**
   * Check if a test has any active resources
   */
  hasActiveResources(testId: string): boolean {
    const testResourceIds = this.testResources.get(testId);
    return testResourceIds ? testResourceIds.size > 0 : false;
  }

  /**
   * Wait for pending operations to complete
   */
  async waitForPendingOperations(testId: string, timeout: number = 10000): Promise<void> {
    // This is a placeholder for waiting for pending operations like video uploads
    // In a real implementation, this would check for active operations and wait for them
    this.logger.debug(`ResourceManager: Waiting for pending operations for test ${testId}`);
    
    // Simulate waiting with a small delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Dispose of the resource manager
   */
  async dispose(): Promise<void> {
    try {
      await this.emergencyCleanupAll('Resource manager disposal');
      
      this.resources.clear();
      this.browserCleanupInfo.clear();
      this.processCleanupInfo.clear();
      this.testResources.clear();

      this.logger.debug('ResourceManager: Disposed');
    } catch (error: any) {
      this.logger.error(`ResourceManager: Error during disposal: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cleanup resources of a specific type
   */
  private async cleanupResourceType(
    testId: string,
    resourceType: ResourceType,
    resources: ResourceEntry[],
    timeout: number
  ): Promise<void> {
    switch (resourceType) {
      case ResourceType.BROWSER:
        await this.cleanupBrowserResources(testId);
        break;
      case ResourceType.PROCESS:
        await this.cleanupProcessResources(testId);
        break;
      case ResourceType.TEMPORARY_FILES:
        await this.cleanupTemporaryFiles(testId);
        break;
      case ResourceType.NETWORK_CONNECTIONS:
        await this.cleanupNetworkConnections(testId);
        break;
      case ResourceType.LOCK_EXTENSIONS:
        await this.cleanupLockExtensions(testId);
        break;
      default:
        this.logger.warn(`ResourceManager: Unknown resource type: ${resourceType}`);
    }
  }

  /**
   * Cleanup browser resources
   */
  private async cleanupBrowserResources(testId: string): Promise<void> {
    const browserInfo = this.browserCleanupInfo.get(testId);
    if (!browserInfo) {
      return;
    }

    this.logger.debug(`ResourceManager: Cleaning up browser resources for test ${testId}`);
    
    // In a real implementation, this would:
    // - Close browser instances
    // - Clean up profile directories
    // - Remove temporary files
    // - Kill browser processes
  }

  /**
   * Cleanup process resources
   */
  private async cleanupProcessResources(testId: string): Promise<void> {
    const processInfo = this.processCleanupInfo.get(testId);
    if (!processInfo) {
      return;
    }

    this.logger.debug(`ResourceManager: Cleaning up process resources for test ${testId}`);
    
    // In a real implementation, this would:
    // - Terminate processes gracefully
    // - Force kill if necessary
    // - Clean up process-related files
  }

  /**
   * Cleanup temporary files
   */
  private async cleanupTemporaryFiles(testId: string): Promise<void> {
    this.logger.debug(`ResourceManager: Cleaning up temporary files for test ${testId}`);
    
    // In a real implementation, this would:
    // - Remove screenshot files
    // - Remove video recordings
    // - Remove log files
    // - Remove downloaded files
  }

  /**
   * Cleanup network connections
   */
  private async cleanupNetworkConnections(testId: string): Promise<void> {
    this.logger.debug(`ResourceManager: Cleaning up network connections for test ${testId}`);
    
    // In a real implementation, this would:
    // - Close WebSocket connections
    // - Close HTTP connections
    // - Close proxy connections
  }

  /**
   * Cleanup lock extensions
   */
  private async cleanupLockExtensions(testId: string): Promise<void> {
    this.logger.debug(`ResourceManager: Cleaning up lock extensions for test ${testId}`);
    
    // This would be handled by the cleanup orchestrator
    // but included here for completeness
  }
}
