/**
 * Node Identity Service Interface
 * 
 * Defines the contract for node identity management services.
 * Handles node ID generation, persistence, and retrieval.
 */

/**
 * Node Identity Service Interface
 * 
 * Manages node identity including ID generation, persistence, and retrieval.
 * Replaces the singleton NodeIdentityManager with dependency injection.
 */
export interface INodeIdentityService {
  /**
   * Initialize the identity service
   * @returns Promise that resolves when initialized
   */
  initialize(): Promise<void>;

  /**
   * Get the current node ID
   * @returns Node ID or null if not assigned
   */
  getNodeId(): string | null;

  /**
   * Get the client ID
   * @returns Client ID
   */
  getClientId(): string;

  /**
   * Get the persistent node ID
   * @returns Persistent node ID or null
   */
  getPersistentNodeId(): string | null;

  /**
   * Update the node ID (typically from hub registration)
   * @param nodeId New node ID
   * @returns Promise that resolves when updated
   */
  updateNodeId(nodeId: string): Promise<void>;

  /**
   * Generate a new random node ID
   * @returns Generated node ID
   */
  generateNewNodeId(): string;

  /**
   * Generate an AI-themed random name
   * @returns Generated AI-themed name
   */
  generateAIThemedName(): string;

  /**
   * Generate a client ID
   * @returns Generated client ID
   */
  generateClientId(): string;

  /**
   * Load persisted node ID from storage
   * @returns Promise that resolves when loaded
   */
  loadPersistedNodeId(): Promise<void>;

  /**
   * Persist node ID to storage
   * @param nodeId Node ID to persist
   * @returns Promise that resolves when persisted
   */
  persistNodeId(nodeId: string): Promise<void>;

  /**
   * Check if node has a valid ID
   * @returns True if node has valid ID
   */
  hasValidNodeId(): boolean;
}
