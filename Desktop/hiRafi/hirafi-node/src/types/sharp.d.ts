declare module "sharp" {
  export default function(input?: string | Buffer): Sharp;
  
  export interface Sharp {
    resize(width?: number, height?: number, options?: any): Sharp;
    toBuffer(): Promise<Buffer>;
    toFile(file: string): Promise<void>;
    metadata(): Promise<{ width?: number; height?: number; format?: string; [key: string]: any }>;
    jpeg(options?: any): Sharp;
    png(options?: any): Sharp;
    webp(options?: any): Sharp;
    tiff(options?: any): Sharp;
    composite(images: any[]): Sharp;
    rotate(angle?: number, options?: any): Sharp;
    extract(region: any): Sharp;
    trim(threshold?: number): Sharp;
    extend(extension: any): Sharp;
    flatten(options?: any): Sharp;
    negate(options?: any): Sharp;
    normalise(options?: any): Sharp;
    normalize(options?: any): Sharp;
    convolve(kernel: any): Sharp;
    threshold(threshold?: number, options?: any): Sharp;
    boolean(operand: any, operator: string, options?: any): Sharp;
    linear(a?: number | number[], b?: number | number[]): Sharp;
    recomb(matrix: number[][]): Sharp;
    modulate(options?: any): Sharp;
    toFormat(format: string | number, options?: any): Sharp;
    gamma(gamma: number): Sharp;
    grayscale(grayscale?: boolean): Sharp;
    greyscale(greyscale?: boolean): Sharp;
    blur(sigma?: number): Sharp;
    sharpen(sigma?: number, flat?: number, jagged?: number): Sharp;
  }
} 