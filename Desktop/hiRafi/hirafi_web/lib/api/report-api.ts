/**
 * Report API
 *
 * Raporlar ile ilgili API isteklerini yönetir.
 */

import { get, post, put, del, fetchApi } from './fetch-wrapper';
import { API_BASE_URL, REPORT_ENDPOINTS } from './constants';
import { toast } from 'sonner';

/**
 * Tüm raporları getirir (hafif versiyon)
 *
 * @param params Filtreleme parametreleri
 */
export async function getReports(params?: {
  limit?: number;
  page?: number;
  search?: string;
}) {
  return get(REPORT_ENDPOINTS.GET_LIGHT, { params });
}

/**
 * <PERSON>irli bir raporu getirir
 *
 * @param id Rapor ID'si
 */
export async function getReportById(id: string) {
  return get(REPORT_ENDPOINTS.GET_BY_ID(id));
}

/**
 * Belirli bir raporu analiz eder
 *
 * @param id Rapor ID'si
 */
export async function analyzeReport(id: string) {
  return post(REPORT_ENDPOINTS.ANALYZE(id), {});
}

/**
 * Tüm run raporlarını getirir
 *
 * @param params Filtreleme parametreleri
 */
export async function getRunReports(params?: {
  limit?: number;
  page?: number;
  search?: string;
}) {
  return get(REPORT_ENDPOINTS.RUNS.GET_ALL, { params });
}

/**
 * Belirli bir run raporunu getirir
 *
 * @param id Run rapor ID'si
 */
export async function getRunReportById(id: string) {
  return get(REPORT_ENDPOINTS.RUNS.GET_BY_ID(id));
}

/**
 * Belirli bir run raporunu siler
 *
 * @param id Run rapor ID'si
 * @param data Silme işlemi için gerekli veriler
 */
export async function deleteRunReport(id: string, data: {
  runId: string;
  executionId?: string;
}) {
  return del(REPORT_ENDPOINTS.RUNS.DELETE(id), { body: data });
}

/**
 * Belirli bir run raporunun detaylı bilgilerini getirir
 *
 * @param id Run rapor ID'si
 */
export async function getRunReportDetail(id: string) {
  return get(REPORT_ENDPOINTS.RUN_DETAIL(id));
}

/**
 * Birden fazla run raporunu toplu siler
 *
 * @param reportIds Silinecek rapor ID'leri
 */
export async function deleteMultipleRunReports(reportIds: string[]) {
  return fetchApi(REPORT_ENDPOINTS.RUNS_BULK, {
    method: 'POST',
    body: { reportIds }
  });
}

/**
 * Belirli bir execution ID'ye ait senaryoları getirir
 *
 * @param executionId Execution ID
 */
export async function getScenariosByExecutionId(executionId: string) {
  return get(REPORT_ENDPOINTS.SCENARIOS_BY_EXECUTION(executionId));
}

/**
 * Download a report as PDF
 *
 * @param reportId Report ID
 * @returns Result of the download operation
 */
export async function downloadReportAsPdf(reportId: string) {
  try {
    // Use the fetch-wrapper to make the request with blob response type
    const response = await fetchApi(REPORT_ENDPOINTS.DOWNLOAD_PDF(reportId), {
      method: 'GET',
      responseType: 'blob',
      showErrorToast: true,
      showSuccessToast: false,
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to download report');
    }

    // Get the blob from the response
    const blob = response.data;

    // Verify that we have a valid blob
    if (!(blob instanceof Blob)) {
      throw new Error('Invalid response format: expected a Blob');
    }

    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a link element
    const a = document.createElement('a');
    a.href = url;
    a.download = `report-${reportId}.pdf`;

    // Append the link to the body
    document.body.appendChild(a);

    // Click the link
    a.click();

    // Remove the link after a short delay to ensure the download starts
    setTimeout(() => {
      document.body.removeChild(a);
      // Revoke the URL to free up memory
      window.URL.revokeObjectURL(url);
    }, 100);

    toast.success('PDF report downloaded successfully');
    return { success: true };
  } catch (error: any) {
    console.error('Error downloading report as PDF:', error);
    toast.error(`Failed to download report: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Rapor paylaşımını etkinleştirir
 *
 * @param reportId Rapor ID
 * @param options Paylaşım seçenekleri
 */
export async function enableReportSharing(reportId: string, options: {
  expiresIn?: number | null;
  password?: string;
  allowComments?: boolean;
}) {
  return post(REPORT_ENDPOINTS.SHARE.ENABLE(reportId), options);
}

/**
 * Rapor paylaşımını devre dışı bırakır
 *
 * @param reportId Rapor ID
 */
export async function disableReportSharing(reportId: string) {
  return del(REPORT_ENDPOINTS.SHARE.DISABLE(reportId));
}

/**
 * Paylaşılan raporu getirir
 *
 * @param token Paylaşım token'ı
 * @param password Şifre (eğer gerekiyorsa)
 */
export async function getSharedReport(token: string, password?: string) {
  const params = password ? { password } : undefined;
  return get(REPORT_ENDPOINTS.SHARE.GET_BY_TOKEN(token), { params });
}