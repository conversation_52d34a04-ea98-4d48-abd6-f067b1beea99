/**
 * Generate Steps API
 *
 * AI ile senaryo adımları oluşturma ile ilgili API isteklerini yönetir.
 */

import { post } from './fetch-wrapper';
import { GENERATE_STEPS_ENDPOINTS } from './constants';

/**
 * AI ile senaryo adımları oluşturur
 *
 * @param params Oluşturma parametreleri
 */
export async function generateSteps(params: {
  prompt: string;
  name?: string;
  description?: string;
  isTestManagement?: boolean;
  testManagementProvider?: 'testrail' | 'zephyrscale';
  testDataVariables?: Array<{
    name: string;
    type: string;
    description?: string;
  }>;
  creativityLevel?: number;
}) {
  return post(GENERATE_STEPS_ENDPOINTS.GENERATE, params);
}
