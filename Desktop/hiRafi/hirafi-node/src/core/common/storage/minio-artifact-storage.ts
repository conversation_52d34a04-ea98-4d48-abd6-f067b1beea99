import * as fs from 'fs';
import * as path from 'path';
import { Client } from 'minio';
import { Readable } from 'stream';
import { logger } from '../../../utils/logger.js';
import { IArtifactStorage, IArtifact, IUploadResult } from './artifact-storage.interface.js';

/**
 * MinioArtifactStorage class for storing artifacts in MinIO
 * Implements the IArtifactStorage interface
 */
export class MinioArtifactStorage implements IArtifactStorage {
  private minioClient!: Client; // Definite assignment assertion - initialized in initialize()
  private bucket: string;
  private config: any;
  private initialized: boolean = false;
  private publicBaseUrl: string | undefined;

  /**
   * Gets the name of the storage provider
   * @returns The provider name as a string
   */
  getProviderName(): string {
    return 'minio';
  }

  /**
   * Create a new MinioArtifactStorage
   * @param customConfig Configuration object
   */
  constructor(customConfig: any = {}) {
    this.config = customConfig;
    this.bucket = customConfig.bucket || 'test-artifacts';
    this.publicBaseUrl = customConfig.publicBaseUrl || customConfig.publicEndPoint;

    // Remove client creation from constructor to prevent double initialization
    // Client will be created only once during initialize() method
    logger.debug(`MinioArtifactStorage: Created with bucket: ${this.bucket}`);
  }

  /**
   * Initialize the storage provider with necessary configurations.
   * @param config Provider-specific configuration.
   */
  async initialize(config: any): Promise<void> {
    if (this.initialized) {
      logger.debug('MinioArtifactStorage: Already initialized, skipping');
      return;
    }

    // Merge config with existing config
    this.config = { ...this.config, ...config };
    this.bucket = this.config.bucket || this.bucket;
    this.publicBaseUrl = this.config.publicBaseUrl || this.config.publicEndPoint || this.publicBaseUrl;

    // Create MinIO client only once during initialization
    this.minioClient = this.createMinioClient(this.config);

    // Log initialization details
    logger.serviceInit('MinioArtifactStorage', `Initialized with bucket: ${this.bucket}`);
    const endPoint = this.config.endPoint || 'localhost';
    const port = this.config.port || 9000;
    logger.serviceInit('MinioArtifactStorage', `Using endpoint: ${endPoint}:${port}`);

    // Determine public URL configuration
    if (this.publicBaseUrl) {
      logger.serviceInit('MinioArtifactStorage', `Using public URL: ${this.publicBaseUrl}`);
    } else {
      logger.info(`MinioArtifactStorage: No public URL configuration found, will use direct presigned URLs`);
    }

    // Ensure bucket exists
    await this.ensureBucketExists();
    this.initialized = true;
  }

  /**
   * Upload a file to storage (Legacy method for backward compatibility)
   * @param filePath Path to the file to upload
   * @param destinationKey Key to store the file under
   * @param contentType MIME type of the file
   * @returns Promise resolving to the URL of the stored file
   */
  async uploadFile(filePath: string, destinationKey: string, contentType?: string): Promise<string> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Determine content type if not provided
      const finalContentType = contentType || this.getContentTypeFromPath(filePath);

      // Create an IArtifact from the file
      const artifact: IArtifact = {
        metadata: {
          testId: 'unknown', // These fields would be filled by caller in newer code
          timestamp: new Date(),
          platform: 'web',
          artifactType: this.getArtifactTypeFromContentType(finalContentType),
          contentType: finalContentType
        },
        fileName: path.basename(filePath),
        filePath: filePath
      };

      // Upload using the new method
      const result = await this.uploadArtifact(artifact);

      // For backward compatibility, just return the URL
      return result.url || result.storagePath;
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server. Test will continue without uploading artifacts: ${error.message}`);
        // Return a placeholder URL so the test can continue
        return `minio-connection-error://${this.bucket}/${destinationKey}`;
      } else {
        logger.error(`MinioArtifactStorage: Error uploading file: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Upload a buffer to storage (Legacy method for backward compatibility)
   * @param buffer Buffer to upload
   * @param destinationKey Key to store the buffer under
   * @param contentType MIME type of the buffer
   * @returns Promise resolving to the URL of the stored buffer
   */
  async uploadBuffer(buffer: Buffer, destinationKey: string, contentType: string): Promise<string> {
    try {
      // Create an IArtifact from the buffer
      const artifact: IArtifact = {
        metadata: {
          testId: 'unknown', // These fields would be filled by caller in newer code
          timestamp: new Date(),
          platform: 'web',
          artifactType: this.getArtifactTypeFromContentType(contentType),
          contentType: contentType
        },
        fileName: path.basename(destinationKey),
        contentBuffer: buffer
      };

      // Upload using the new method
      const result = await this.uploadArtifact(artifact, path.dirname(destinationKey));

      // For backward compatibility, just return the URL
      return result.url || result.storagePath;
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server. Test will continue without uploading artifacts: ${error.message}`);
        // Return a placeholder URL so the test can continue
        return `minio-connection-error://${this.bucket}/${destinationKey}`;
      } else {
        logger.error(`MinioArtifactStorage: Error uploading buffer: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Get the URL for a stored file (Legacy method for backward compatibility)
   * @param destinationKey Key the file is stored under
   * @returns Promise resolving to the URL of the file
   */
  async getFileUrl(destinationKey: string): Promise<string> {
    try {
      // Generate presigned URL
      const url = await this.getPresignedUrl(destinationKey, 24 * 60 * 60, 'getObject');

      // Handle public URL base if configured
      if (this.publicBaseUrl) {
        const parsedUrl = new URL(url);
        const publicUrl = new URL(this.publicBaseUrl);

        parsedUrl.hostname = publicUrl.hostname;
        parsedUrl.port = publicUrl.port;
        parsedUrl.protocol = publicUrl.protocol;

        logger.debug(`MinioArtifactStorage: Using public URL base: ${this.publicBaseUrl}`);
        return parsedUrl.toString();
      }

      logger.debug(`MinioArtifactStorage: No public URL configuration found, using direct presigned URL`);
      return url;
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server when getting file URL. Test will continue: ${error.message}`);
        // Return a placeholder URL so the test can continue
        return `minio-connection-error://${this.bucket}/${destinationKey}`;
      } else {
        logger.error(`MinioArtifactStorage: Error getting file URL: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Uploads an artifact to the storage.
   * @param artifact The artifact to upload.
   * @param destinationPath Optional path prefix within the bucket.
   * @returns A promise resolving to the storage path and URL of the uploaded artifact.
   */
  async uploadArtifact(artifact: IArtifact, destinationPath?: string): Promise<IUploadResult> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // Build the storage key using destination path and filename
      let key = artifact.fileName;
      if (destinationPath) {
        key = `${destinationPath}/${key}`;
      }

      // Set metadata - this might need to be stringified for MinIO
      const metadata: Record<string, string> = {
        'X-Amz-Meta-TestId': artifact.metadata.testId,
        'X-Amz-Meta-Timestamp': artifact.metadata.timestamp.toISOString(),
        'X-Amz-Meta-Platform': artifact.metadata.platform,
        'X-Amz-Meta-ArtifactType': artifact.metadata.artifactType,
        'Content-Type': artifact.metadata.contentType
      };

      if (artifact.metadata.stepName) {
        metadata['X-Amz-Meta-StepName'] = artifact.metadata.stepName;
      }

      if (artifact.metadata.customTags) {
        for (const [tagKey, tagValue] of Object.entries(artifact.metadata.customTags)) {
          metadata[`X-Amz-Meta-${tagKey}`] = tagValue;
        }
      }

      // Determine the artifact content and upload it
      if (artifact.contentBuffer) {
        // Upload buffer
        await this.minioClient.putObject(
          this.bucket,
          key,
          artifact.contentBuffer,
          artifact.contentBuffer.length,
          metadata
        );
      } else if (artifact.contentStream) {
        // Upload stream
        // For MinIO, we need to know the size of the stream
        // This is a limitation, but we can work around it if needed
        // For now, this approach handles most common cases
        throw new Error('Stream uploads not yet implemented for MinIO storage');
      } else if (artifact.filePath) {
        // Upload file
        await this.minioClient.fPutObject(
          this.bucket,
          key,
          artifact.filePath,
          metadata
        );
      } else {
        throw new Error('Artifact must have either contentBuffer, contentStream, or filePath');
      }

      logger.debug(`MinioArtifactStorage: Uploaded artifact to ${key}`);

      // Generate the URL
      const url = await this.getPresignedUrl(key, 24 * 60 * 60, 'getObject');

      // Return the result
      return {
        storagePath: `minio://${this.bucket}/${key}`,
        url
      };
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server. Test will continue without uploading artifacts: ${error.message}`);
        // Return a placeholder result so the test can continue
        return {
          storagePath: `minio-connection-error://${this.bucket}/${artifact.fileName}`,
          url: `minio-connection-error://${this.bucket}/${artifact.fileName}`
        };
      } else {
        logger.error(`MinioArtifactStorage: Error uploading artifact: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Downloads an artifact from the storage.
   * @param storagePath The full path of the artifact in storage.
   * @returns A promise resolving to a readable stream of the artifact content.
   */
  async downloadArtifact(storagePath: string): Promise<NodeJS.ReadableStream> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // Extract the key from the storage path
      const key = this.extractKeyFromStoragePath(storagePath);

      // Get the object from MinIO
      const stream = await this.minioClient.getObject(this.bucket, key);
      return stream;
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server when downloading artifact. Test will continue: ${error.message}`);
        // Return an empty stream so the test can continue
        return Readable.from(Buffer.from([]));
      } else {
        logger.error(`MinioArtifactStorage: Error downloading artifact: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Deletes an artifact from the storage.
   * @param storagePath The full path of the artifact in storage.
   * @returns A promise resolving when the artifact is deleted.
   */
  async deleteArtifact(storagePath: string): Promise<void> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // Extract the key from the storage path
      const key = this.extractKeyFromStoragePath(storagePath);

      // Delete the object from MinIO
      await this.minioClient.removeObject(this.bucket, key);
      logger.debug(`MinioArtifactStorage: Deleted artifact ${key}`);
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server when deleting artifact. Test will continue: ${error.message}`);
        // Don't throw, just log and continue
      } else {
        logger.error(`MinioArtifactStorage: Error deleting artifact: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Lists artifacts in a given path.
   * @param pathPrefix The path prefix to list artifacts from.
   * @returns A promise resolving to a list of artifact names or full paths.
   */
  async listArtifacts(pathPrefix: string): Promise<string[]> {
    try {
      if (!this.initialized) {
        await this.initialize(this.config);
      }

      // List objects in the bucket with the given prefix
      const stream = this.minioClient.listObjectsV2(this.bucket, pathPrefix, true);

      // Collect the objects into an array
      const objects: string[] = [];

      await new Promise<void>((resolve, reject) => {
        stream.on('data', (obj: any) => {
          if (obj && typeof obj.name === 'string') {
            objects.push(`minio://${this.bucket}/${obj.name}`);
          }
        });

        stream.on('error', (err: Error) => {
          reject(err);
        });

        stream.on('end', () => {
          resolve();
        });
      });

      return objects;
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server when listing artifacts. Test will continue: ${error.message}`);
        // Return an empty array so the test can continue
        return [];
      } else {
        logger.error(`MinioArtifactStorage: Error listing artifacts: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Generates a pre-signed URL for temporary access to an artifact.
   * @param storagePath The full path of the artifact in storage.
   * @param expiresInSeconds The duration for which the URL is valid.
   * @param operation 'getObject' | 'putObject'
   * @returns A promise resolving to the pre-signed URL.
   */
  async getPresignedUrl(storagePath: string, expiresInSeconds: number, operation: 'getObject' | 'putObject'): Promise<string> {
    try {
      if (!this.initialized && operation !== 'getObject') {
        await this.initialize(this.config);
      }

      // Extract the key from the storage path if it's a full storage path
      let key = storagePath;
      if (storagePath.startsWith('minio://')) {
        key = this.extractKeyFromStoragePath(storagePath);
      }

      // Generate the presigned URL based on the operation
      let url: string;
      if (operation === 'getObject') {
        url = await this.minioClient.presignedGetObject(this.bucket, key, expiresInSeconds);
      } else {
        url = await this.minioClient.presignedPutObject(this.bucket, key, expiresInSeconds);
      }

      // If a public base URL is configured, modify the URL to use it
      if (this.publicBaseUrl) {
        const parsedUrl = new URL(url);
        const publicUrl = new URL(this.publicBaseUrl);

        parsedUrl.hostname = publicUrl.hostname;
        parsedUrl.port = publicUrl.port;
        parsedUrl.protocol = publicUrl.protocol;

        return parsedUrl.toString();
      }

      return url;
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server when generating presigned URL. Test will continue: ${error.message}`);
        // Return a placeholder URL so the test can continue
        return `minio-connection-error://${this.bucket}/${storagePath}`;
      } else {
        logger.error(`MinioArtifactStorage: Error generating presigned URL: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Create a MinIO client with the given configuration
   * @param config Configuration object
   * @returns MinIO client
   * @private
   */
  private createMinioClient(config: any): Client {
    // Build config with defaults
    const minioConfig = {
      endPoint: config.endPoint || process.env.MINIO_ENDPOINT || 'localhost',
      port: parseInt(config.port || process.env.MINIO_PORT || '9000'),
      useSSL: config.useSSL || process.env.MINIO_USE_SSL === 'true' || false,
      accessKey: config.accessKey || process.env.MINIO_ACCESS_KEY || 'minioadmin',
      secretKey: config.secretKey || process.env.MINIO_SECRET_KEY || 'minioadmin',
      region: config.region || process.env.MINIO_REGION || ''
    };

    return new Client(minioConfig);
  }

  /**
   * Extract the key from a storage path
   * @param storagePath Storage path to extract key from (format: minio://bucket/key)
   * @returns Key part of the path
   * @private
   */
  private extractKeyFromStoragePath(storagePath: string): string {
    // Handle different formats
    if (storagePath.startsWith('minio://')) {
      // Format: minio://bucket/key
      const parts = storagePath.substring(8).split('/');
      const bucket = parts[0];

      if (bucket !== this.bucket) {
        logger.warn(`MinioArtifactStorage: Storage path bucket (${bucket}) does not match configured bucket (${this.bucket})`);
      }

      return parts.slice(1).join('/');
    }

    // If it's not a full storage path, assume it's just the key
    return storagePath;
  }

  /**
   * Ensure the bucket exists and has correct permissions
   * @private
   */
  private async ensureBucketExists(): Promise<void> {
    try {
      // Check if bucket exists
      const exists = await this.minioClient.bucketExists(this.bucket);

      if (!exists) {
        // Create bucket
        await this.minioClient.makeBucket(this.bucket, this.config.region || '');
        logger.info(`MinioArtifactStorage: Created bucket: ${this.bucket}`);

        // Set bucket policy to public read
        const policy = {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Principal: { AWS: ['*'] },
              Action: ['s3:GetObject'],
              Resource: [`arn:aws:s3:::${this.bucket}/*`]
            }
          ]
        };

        await this.minioClient.setBucketPolicy(this.bucket, JSON.stringify(policy));
        logger.info(`MinioArtifactStorage: Set bucket policy to public read`);
      } else {
        logger.debug(`MinioArtifactStorage: Bucket already exists: ${this.bucket}`);
      }
    } catch (error: any) {
      // Check if this is a connection error
      if (error.message && (error.message.includes('ECONNREFUSED') || error.message.includes('connect') || error.message.includes('connection'))) {
        logger.warn(`MinioArtifactStorage: Connection refused to MinIO server when ensuring bucket exists. Test will continue without artifact storage: ${error.message}`);
        // Don't throw the error, allow the test to continue
      } else {
        logger.error(`MinioArtifactStorage: Error ensuring bucket exists: ${error.message}`);
        throw error;
      }
    }
  }

  /**
   * Get content type from file path
   * @param filePath Path to the file
   * @returns Content type
   * @private
   */
  private getContentTypeFromPath(filePath: string): string {
    const extension = path.extname(filePath).toLowerCase();

    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.mp4':
        return 'video/mp4';
      case '.webm':
        return 'video/webm';
      case '.json':
        return 'application/json';
      case '.txt':
        return 'text/plain';
      case '.html':
        return 'text/html';
      case '.css':
        return 'text/css';
      case '.js':
        return 'application/javascript';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Determine artifact type from content type
   * @param contentType MIME type
   * @returns Artifact type
   * @private
   */
  private getArtifactTypeFromContentType(contentType: string): 'screenshot' | 'video' | 'log' | 'other' {
    if (contentType.startsWith('image/')) {
      return 'screenshot';
    } else if (contentType.startsWith('video/')) {
      return 'video';
    } else if (contentType === 'text/plain' || contentType === 'application/json') {
      return 'log';
    } else {
      return 'other';
    }
  }
}
