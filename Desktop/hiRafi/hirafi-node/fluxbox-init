# Complete Fluxbox Configuration - Prevents all "Failed to read" warnings

# Global session settings
session.configVersion: 13
session.keyFile: ~/.fluxbox/keys
session.styleFile: /usr/share/fluxbox/styles/Squared_for_Debian
session.menuFile: ~/.fluxbox/menu
session.appsFile: ~/.fluxbox/apps
session.slitlistFile: ~/.fluxbox/slitlist
session.tabsAttachArea: Window
session.cacheLife: 5
session.cacheMax: 200
session.colorsPerChannel: 4
session.doubleClickInterval: 250
session.autoRaiseDelay: 250
session.ignoreBorder: false
session.forcePseudoTransparency: false
session.tabPadding: 0
session.styleOverlay: ~/.fluxbox/overlay

# Screen 0 workspace settings
session.screen0.workspaces: 1
session.screen0.workspaceNames: Workspace 1

# Screen 0 window behavior
session.screen0.opaqueMove: true
session.screen0.fullMaximization: false
session.screen0.maxIgnoreIncrement: true
session.screen0.maxDisableMove: false
session.screen0.maxDisableResize: false
session.screen0.workspacewarping: true
session.screen0.showwindowposition: false
session.screen0.autoRaise: true
session.screen0.clickRaises: true
session.screen0.defaultDeco: NORMAL
session.screen0.windowPlacement: RowSmartPlacement
session.screen0.rowPlacementDirection: LeftToRight
session.screen0.colPlacementDirection: TopToBottom
session.screen0.edgeSnapThreshold: 10

# Screen 0 focus settings
session.screen0.focusNewWindows: true
session.screen0.focusModel: ClickFocus
session.screen0.tabFocusModel: ClickTabFocus
session.screen0.focusLastWindow: true
session.screen0.focusSameHead: true
session.screen0.noFocusWhileTypingDelay: 0

# Screen 0 tab settings
session.screen0.tab.placement: Top
session.screen0.tab.width: 64
session.screen0.tabs.usePixmap: true
session.screen0.tabs.maxOver: false
session.screen0.tabs.intitlebar: true

# Screen 0 window transparency
session.screen0.window.focus.alpha: 255
session.screen0.window.unfocus.alpha: 255
session.screen0.menu.alpha: 255

# Screen 0 toolbar settings
session.screen0.toolbar.visible: false
session.screen0.toolbar.autoHide: true
session.screen0.toolbar.maxOver: false
session.screen0.toolbar.alpha: 255
session.screen0.toolbar.layer: Normal
session.screen0.toolbar.onhead: 1
session.screen0.toolbar.placement: TopCenter
session.screen0.toolbar.height: 0

# Screen 0 slit settings
session.screen0.slit.autoHide: true
session.screen0.slit.placement: RightBottom
session.screen0.slit.acceptKdeDockapps: true
session.screen0.slit.maxOver: false
session.screen0.slit.alpha: 255
session.screen0.slit.onhead: 1
session.screen0.slit.layer: Normal

# Screen 0 iconbar settings
session.screen0.iconbar.mode: Workspace
session.screen0.iconbar.alignment: Left
session.screen0.iconbar.iconWidth: 128
session.screen0.iconbar.iconTextPadding: 10
session.screen0.iconbar.usePixmap: true

# Screen 0 client menu settings
session.screen0.clientMenu.usePixmap: true

# Screen 0 menu settings
session.screen0.menuDelay: 200
session.screen0.menuDelayClose: 0
session.screen0.menuMode: Delay
session.screen0.menuSearch: itemstart
session.screen0.typeAheadSearch: true

# Screen 0 other settings
session.screen0.allowRemoteActions: false
session.screen0.strftimeFormat: %k:%M
session.screen0.dateFormat: %a %b %d
session.screen0.clockFormat: 24
session.screen0.followModel: Ignore
session.screen0.userFollowModel: Follow
session.screen0.decorateTransient: true
session.screen0.desktopwheeling: true
session.screen0.reversewheeling: false
session.screen0.wheelingheight: 0
session.screen0.wheelingwidth: 0
session.screen0.antialias: true
session.screen0.windowMenu: ~/.fluxbox/windowmenu

# Screen 0 overlay settings
session.screen0.overlay.lineWidth: 1
session.screen0.overlay.lineStyle: LineSolid
session.screen0.overlay.joinStyle: JoinMiter
session.screen0.overlay.capStyle: CapNotLast

# Screen 0 titlebar settings
session.screen0.titlebar.left: Stick
session.screen0.titlebar.right: Minimize Maximize Close
