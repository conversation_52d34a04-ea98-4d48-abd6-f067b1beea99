import React from 'react';
import {
  <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle
} from '@/components/ui/card';
import {
  Tooltip, TooltipContent, TooltipProvider, TooltipTrigger
} from '@/components/ui/tooltip';
import { Cpu, Zap, Code, Info, Smartphone, Globe, Monitor, Wifi } from 'lucide-react';
import { ReportDetail } from '@/hooks/useReport';
import { PageMetrics as PageMetricsType } from '@/types/metrics';
import {
  detectReportPlatform,
  getAndroidDeviceInfo,
  getWebBrowserInfo
} from '@/lib/utils/platform-utils';

interface ReportSummaryProps {
  report: ReportDetail | null;
}

function DonutChart({ summary }: { summary: ReportDetail['summary'] }) {
  const total = summary.total || 1; // Avoid division by zero
  const completedSteps = summary.passed;
  const failedSteps = summary.failed;

  const completedPercentage = (completedSteps / total) * 100;
  const failedPercentage = (failedSteps / total) * 100;

  // Determine the dominant result (completed or failed)
  const isDominantFailed = failedSteps >= completedSteps;
  const dominantColor = isDominantFailed ? '#ef4444' : '#10b981';
  const dominantPercentage = isDominantFailed ? failedPercentage : completedPercentage;
  const dominantText = isDominantFailed ? 'Failure Rate' : 'Completion Rate';

  // Calculate the circumference and offset for each segment
  const circumference = 251.2; // 2 * PI * radius
  const completedCircumference = (completedPercentage / 100) * circumference;
  const failedCircumference = (failedPercentage / 100) * circumference;

  const completedOffset = 0;
  const failedOffset = -completedCircumference;

  return (
    <div className="flex-1 flex flex-col items-center justify-center">
      <div className="relative w-48 h-48">
        <svg viewBox="0 0 100 100" className="w-full h-full">
          {/* Background circle */}
          <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="15" />

          {/* Completed segment */}
          {completedSteps > 0 && (
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="#10b981"
              strokeWidth="15"
              strokeDasharray={`${completedCircumference} ${circumference}`}
              strokeDashoffset={`${completedOffset}`}
              transform="rotate(-90 50 50)"
            />
          )}

          {/* Failed segment */}
          {failedSteps > 0 && (
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="#ef4444"
              strokeWidth="15"
              strokeDasharray={`${failedCircumference} ${circumference}`}
              strokeDashoffset={`${failedOffset}`}
              transform="rotate(-90 50 50)"
            />
          )}
        </svg>

        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className={`text-3xl font-bold ${isDominantFailed ? 'text-red-600 dark:text-red-500' : 'text-emerald-600 dark:text-emerald-500'}`}>
            {Math.round(dominantPercentage)}%
          </span>
          <span className="text-sm text-gray-500">{dominantText}</span>
        </div>
      </div>

      <div className="flex gap-6 mt-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-gray-400"></div>
          <span className="text-sm">Total Steps: {total}</span>
        </div>
        {completedSteps > 0 && (
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
            <span className="text-sm">Completed Steps: {completedSteps}</span>
          </div>
        )}
        {failedSteps > 0 && (
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-rose-500"></div>
            <span className="text-sm">Failed Steps: {failedSteps}</span>
          </div>
        )}
      </div>
    </div>
  );
}

function PageMetricsDisplay({ metrics }: { metrics: PageMetricsType | undefined }) {
  if (!metrics) {
    return (
      <div className="flex-1">
        <h3 className="font-medium mb-4">Page Metrics</h3>
        <div className="bg-gray-50 dark:bg-gray-900/30 p-6 rounded-lg border border-gray-200 dark:border-gray-800">
          <div className="text-center py-4">
            <Info className="h-10 w-10 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 font-medium mb-1">No Page Metrics available</p>
            <p className="text-sm text-gray-400 max-w-md mx-auto">
              Page performance metrics may not be enabled for this test or no data was collected during execution.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1">
      <h3 className="font-medium mb-4">Page Metrics</h3>
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Cpu className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Documents</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Number of documents in the DOM tree</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">{metrics?.documents || 'N/A'}</span>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Cpu className="h-4 w-4 text-purple-500" />
              <span className="text-sm font-medium">Nodes</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Total number of DOM nodes on the page</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">{metrics?.nodes || 'N/A'}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Zap className="h-4 w-4 text-emerald-500" />
              <span className="text-sm font-medium">JS Heap Used</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Amount of memory used by JavaScript runtime</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">
                {metrics?.jsHeapUsedSize
                  ? (metrics.jsHeapUsedSize / (1024 * 1024)).toFixed(2)
                  : 'N/A'}
              </span>
              <span className="text-sm text-gray-500">MB</span>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Code className="h-4 w-4 text-amber-500" />
              <span className="text-sm font-medium">Script Duration</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>Time spent executing JavaScript code</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-2xl font-bold">
                {metrics?.scriptDuration?.toFixed(2) || 'N/A'}
              </span>
              <span className="text-sm text-gray-500">ms</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Android-specific metrics display
function AndroidMetricsDisplay({ report }: { report: ReportDetail }) {
  const deviceInfo = getAndroidDeviceInfo(report);

  return (
    <div className="flex-1 space-y-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
        <Smartphone className="h-5 w-5" />
        Device Information
      </h3>

      {deviceInfo ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Monitor className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">Device</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              {deviceInfo.deviceName}
            </p>
            {deviceInfo.manufacturer && (
              <p className="text-sm text-gray-500 dark:text-gray-500">
                {deviceInfo.manufacturer}
              </p>
            )}
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Code className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">OS Version</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              {deviceInfo.osVersion || 'Unknown'}
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Wifi className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">Provider</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              {deviceInfo.provider || 'Unknown'}
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="font-medium text-gray-900 dark:text-white">Test Duration</span>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              {report.duration ? `${Math.round(report.duration)}s` : 'N/A'}
            </p>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            No device information available for this test.
          </p>
        </div>
      )}
    </div>
  );
}

export function ReportSummary({ report }: ReportSummaryProps) {
  if (!report) return null;

  // Detect platform for conditional rendering
  const platform = detectReportPlatform(report);
  const isAndroid = platform === 'android';

  // Check for metrics data in either enhancedMetrics or metrics field
  const metricsData = report?.enhancedMetrics?.pageMetrics || report?.metrics?.pageMetrics;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isAndroid ? (
            <>
              <Smartphone className="h-5 w-5" />
              Android Test Results Summary
            </>
          ) : (
            <>
              <Globe className="h-5 w-5" />
              Web Test Results Summary
            </>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col lg:flex-row gap-6">
          <DonutChart summary={report.summary} />
          {isAndroid ? (
            <AndroidMetricsDisplay report={report} />
          ) : (
            <PageMetricsDisplay metrics={metricsData} />
          )}
        </div>
      </CardContent>
    </Card>
  );
}