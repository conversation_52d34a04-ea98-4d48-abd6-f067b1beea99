"use client"

import { useRunForm } from "../run-context"
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card"
import { FileText } from "lucide-react"
import { WebReportSettings } from "./web-report-settings"
import { AndroidReportSettings } from "./android-report-settings"
import { BaseReportSettings } from "./base-report-settings"
import { PlatformType } from "../run-context"

export function ReportSettings() {
  const { formData } = useRunForm()
  const platform = formData.platform as PlatformType || 'web'

  return (
    <div className="h-full">
      <Card className="h-full">
        <CardHeader className="bg-gradient-to-r from-emerald-50/50 to-teal-50/50 dark:from-emerald-950/40 dark:to-teal-950/40 border-b border-emerald-100 dark:border-emerald-900/30">
          <CardTitle className="text-lg flex items-center">
            <FileText className="mr-2 h-5 w-5 text-emerald-500" />
            {platform === 'android' ? 'Android Report Settings' : 'Browser Report Settings'}
          </CardTitle>
          <CardDescription>
            Configure how the test run results will be reported and what data will be collected.
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-6 overflow-y-auto max-h-[600px] p-5">
          {/* Common report settings */}
          <BaseReportSettings />
          
          {/* Platform-specific report settings */}
          {platform === 'android' ? (
            <AndroidReportSettings />
          ) : (
            <WebReportSettings />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
