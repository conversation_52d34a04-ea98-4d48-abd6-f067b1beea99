"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { Sidebar } from "@/components/sidebar/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Check, ChevronRight, Puzzle, AlertCircle, ArrowRight, CheckCircle2, LoaderCircle, TestTube } from "lucide-react"
import { CompanyOwnerRoute } from "@/components/company-owner-route"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { pluginApi } from "@/lib/api"
import ErrorBoundary from "@/components/error-boundary"

export default function TestRailPluginPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [configStep, setConfigStep] = useState(1)
  const [configData, setConfigData] = useState({
    url: "",
    apiKey: "",
    email: "",
    selectedProjects: [] as string[],
    selectedSuites: [] as string[],
    projectsData: [] as {id: string, name: string}[],
    suitesData: [] as {id: string, name: string}[],
  })
  const [isActive, setIsActive] = useState(false)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<"idle" | "success" | "error">("idle")
  const [connectionError, setConnectionError] = useState("")
  const [projects, setProjects] = useState<any[]>([])
  const [isLoadingProjects, setIsLoadingProjects] = useState(false)
  const [suites, setSuites] = useState<any[]>([])
  const [suitesByProject, setSuitesByProject] = useState<Record<string, any[]>>({})
  const [isLoadingSuites, setIsLoadingSuites] = useState(false)
  const [activeConfigData, setActiveConfigData] = useState<any>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(false);
  const [lastSyncedTime, setLastSyncedTime] = useState<string>('');
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isSavingConfig, setIsSavingConfig] = useState(false);



  const fetchProjects = async () => {
    setIsLoadingProjects(true)
    try {
      // Yeni API yapısını kullan
      const response = await pluginApi.getTestRailProjects({
        url: configData.url,
        username: configData.email,
        apiKey: configData.apiKey
      })

      if (response.success) {
        // Handle nested structure where projects array is in response.data.projects.projects
        let projectsArray = [];

        if (response.data?.projects?.projects && Array.isArray(response.data.projects.projects)) {
          projectsArray = response.data.projects.projects;
        } else if (Array.isArray(response.data?.projects)) {
          projectsArray = response.data.projects;
        }

        setProjects(projectsArray);

        if (projectsArray.length > 0) {
          return true;
        } else {
          toast({
            title: "Uyarı",
            description: "Hiç proje bulunamadı.",
            variant: "default"
          });
          return false;
        }
      } else {
        toast({
          title: "Hata",
          description: response.error || "TestRail projeleri alınamadı.",
          variant: "destructive"
        })
        return false
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "TestRail projeleri alınırken bir hata oluştu.",
        variant: "destructive"
      })
      return false
    } finally {
      setIsLoadingProjects(false)
    }
  }



  // Save basic configuration (only credentials) to database
  const saveBasicConfiguration = async () => {
    try {
      setIsSavingConfig(true);

      // Input değerlerini trim ederek temizle
      const trimmedUrl = configData.url?.trim() || '';
      const trimmedEmail = configData.email?.trim() || '';
      const trimmedApiKey = configData.apiKey?.trim() || '';

      // Temel bilgilerin varlığını kontrol et
      if (!trimmedUrl || !trimmedEmail || !trimmedApiKey) {
        toast({
          title: "Hata",
          description: "TestRail bağlantı bilgileri eksik.",
          variant: "destructive"
        });
        return false;
      }

      // Sadece temel konfigürasyonu kaydet (projeler ve suite'ler olmadan)
      const response = await pluginApi.saveTestRailConfig({
        url: trimmedUrl,
        username: trimmedEmail,
        apiKey: trimmedApiKey,
        projectsData: [], // Boş array
        suitesData: []    // Boş array
      });

      if (response.success) {
        toast({
          title: "Başarılı",
          description: "TestRail bağlantı bilgileri kaydedildi.",
        });
        return true;
      } else {
        toast({
          title: "Hata",
          description: response.error || "Konfigürasyon kaydedilemedi.",
          variant: "destructive"
        });
        return false;
      }
    } catch (error: any) {
      toast({
        title: "Hata",
        description: "Konfigürasyon kaydedilirken bir hata oluştu.",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsSavingConfig(false);
    }
  };

  const handleNextStep = async () => {
    try {
      if (configStep === 1 && connectionStatus === "success") {
        // Önce temel konfigürasyonu kaydet
        const configSaved = await saveBasicConfiguration();
        if (configSaved) {
          // Sonra projeleri çek
          const success = await fetchProjects();
          if (success) {
            setConfigStep(2);
          }
        }
      } else if (configStep === 2 && configData.selectedProjects.length > 0) {
        // Check if suites are available (they should be auto-fetched by useEffect)
        if (suites.length > 0) {
          setConfigStep(3)
        } else {
          // If no suites available, show appropriate message
          toast({
            title: "Test Suite Bulunamadı",
            description: "Seçilen projelerde hiç test suite bulunamadı. Lütfen farklı projeler seçin.",
            variant: "default"
          })
        }
      } else if (configStep === 3 && configData.selectedSuites.length > 0) {
        // Save configuration to server
        await saveConfiguration()
      } else {
        // Handle validation errors
        if (configStep === 1 && connectionStatus !== "success") {
          toast({
            title: "Bağlantı Gerekli",
            description: "Devam etmek için önce bağlantıyı test edin ve başarılı olduğundan emin olun.",
            variant: "destructive"
          })
        } else if (configStep === 2 && configData.selectedProjects.length === 0) {
          toast({
            title: "Proje Seçimi Gerekli",
            description: "Devam etmek için en az bir proje seçmelisiniz.",
            variant: "destructive"
          })
        } else if (configStep === 3 && configData.selectedSuites.length === 0) {
          toast({
            title: "Test Suite Seçimi Gerekli",
            description: "Devam etmek için en az bir test suite seçmelisiniz.",
            variant: "destructive"
          })
        }
      }
    } catch (error) {
      toast({
        title: "İşlem Hatası",
        description: "İşlem sırasında beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive"
      })
    }
  }

  // Save the complete TestRail configuration
  const saveConfiguration = async () => {
    try {
      // Input değerlerini trim ederek temizle
      const trimmedUrl = configData.url?.trim() || '';
      const trimmedEmail = configData.email?.trim() || '';
      const trimmedApiKey = configData.apiKey?.trim() || '';

      // Tüm gerekli verilerin varlığını kontrol et
      if (!trimmedUrl || !trimmedEmail || !trimmedApiKey) {
        toast({
          title: "Hata",
          description: "TestRail bağlantı bilgileri eksik.",
          variant: "destructive"
        });
        return false;
      }

      if (!configData.projectsData || configData.projectsData.length === 0) {
        toast({
          title: "Hata", 
          description: "En az bir proje seçmelisiniz.",
          variant: "destructive"
        });
        return false;
      }

      if (!configData.suitesData || configData.suitesData.length === 0) {
        toast({
          title: "Hata",
          description: "En az bir test suite seçmelisiniz.", 
          variant: "destructive"
        });
        return false;
      }

      // Yeni API yapısını kullan
      const response = await pluginApi.saveTestRailConfig({
        url: trimmedUrl,
        username: trimmedEmail,
        apiKey: trimmedApiKey,
        projectsData: configData.projectsData,
        suitesData: configData.suitesData
      });

      if (response.success) {
        toast({
          title: "Başarılı",
          description: response.message || "TestRail entegrasyonu başarıyla kaydedildi.",
        });
        setIsActive(true);

        // Başarılı kayıttan sonra aktif yapılandırmayı yeniden yükle
        await fetchActiveConfig();
        return true;
      } else {
        toast({
          title: "Hata",
          description: response.error || "TestRail yapılandırması kaydedilemedi.",
          variant: "destructive"
        });
        return false;
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "TestRail yapılandırması kaydedilirken bir hata oluştu.",
        variant: "destructive"
      });
      return false;
    }
  };

  const handlePrevStep = () => {
    if (configStep > 1) {
      setConfigStep(configStep - 1)
      // Reset connection status when going back
      if (configStep === 2) {
        setConnectionStatus("idle")
        setConnectionError("")
      }
    }
  }

  const testConnection = async () => {
    setIsTestingConnection(true)
    setConnectionStatus("idle")
    setConnectionError("")

    try {
      // Input değerlerini trim ederek temizle
      const trimmedUrl = configData.url?.trim() || '';
      const trimmedEmail = configData.email?.trim() || '';
      const trimmedApiKey = configData.apiKey?.trim() || '';

      // Boş değer kontrolü
      if (!trimmedUrl || !trimmedEmail || !trimmedApiKey) {
        setConnectionStatus("error")
        setConnectionError("Tüm alanlar doldurulmalıdır.")
        toast({
          title: "Eksik Bilgi",
          description: "Lütfen tüm alanları doldurunuz.",
          variant: "destructive"
        });
        return;
      }

      // Yeni API yapısını kullan
      const response = await pluginApi.testTestRailConnection({
        url: trimmedUrl,
        email: trimmedEmail,
        apiKey: trimmedApiKey
      });

      if (response.success) {
        // Bağlantı başarılı
        setConnectionStatus("success")
        toast({
          title: "Bağlantı Başarılı",
          description: "TestRail bağlantısı başarıyla kuruldu.",
        });
      } else {
        // Bağlantı başarısız - daha anlamlı hata mesajları
        setConnectionStatus("error")
        
        // Backend'den gelen error code'a göre özel mesaj
        let errorMessage = response.error || "Bağlantı sırasında bir hata oluştu.";
        let toastTitle = "Bağlantı Hatası";
        
        if ((response as any).errorCode) {
          switch ((response as any).errorCode) {
            case 'AUTHENTICATION_FAILED':
              toastTitle = "Kimlik Doğrulama Hatası";
              break;
            case 'ACCESS_DENIED':
              toastTitle = "Erişim Reddedildi";
              break;
            case 'INSTANCE_NOT_FOUND':
              toastTitle = "TestRail Bulunamadı";
              break;
            case 'INVALID_URL_FORMAT':
              toastTitle = "Geçersiz URL";
              break;
            case 'TIMEOUT':
              toastTitle = "Zaman Aşımı";
              break;
            case 'NETWORK_ERROR':
              toastTitle = "Ağ Hatası";
              break;
            default:
              toastTitle = "Bağlantı Hatası";
          }
        }
        
        setConnectionError(errorMessage)
        toast({
          title: toastTitle,
          description: errorMessage,
          variant: "destructive"
        });
      }
    } catch (error) {
      // API isteği başarısız
      setConnectionStatus("error")
      setConnectionError("Sunucuya bağlanırken bir hata oluştu.")
      toast({
        title: "Bağlantı Hatası",
        description: "Sunucu ile iletişim kurulamadı.",
        variant: "destructive"
      });
    } finally {
      setIsTestingConnection(false)
    }
  }

  const getStepTitle = () => {
    switch (configStep) {
      case 1:
        return "Connection Details"
      case 2:
        return "Select Project"
      case 3:
        return "Select Test Suite"
      default:
        return "Configure TestRail"
    }
  }

  const toggleProjectSelection = (projectId: string) => {
    setConfigData(prev => {
      // Mevcut seçimleri al
      const currentProjects = Array.isArray(prev.selectedProjects)
        ? [...prev.selectedProjects]
        : [];
      const currentProjectsData = Array.isArray(prev.projectsData)
        ? [...prev.projectsData]
        : [];

      // Bu proje seçili mi kontrol et
      const isSelected = currentProjects.includes(projectId);

      if (isSelected) {
        // Seçiliyi kaldır
        const newSelectedProjects = currentProjects.filter(id => id !== projectId);
        return {
          ...prev,
          selectedProjects: newSelectedProjects,
          projectsData: currentProjectsData.filter(p => p.id !== projectId),
          // Clear suite selections when project selection changes
          selectedSuites: [],
          suitesData: []
        };
      } else {
        // Projenin detaylarını bul
        const projectDetails = projects.find(p => p.id.toString() === projectId);
        const newProject = {
          id: projectId,
          name: projectDetails?.name || `Project ${projectId}`
        };

        // Yeni proje ekle
        return {
          ...prev,
          selectedProjects: [...currentProjects, projectId],
          projectsData: [...currentProjectsData, newProject],
          // Clear suite selections when project selection changes
          selectedSuites: [],
          suitesData: []
        };
      }
    });
  };

  const toggleSuiteSelection = (suiteId: string) => {
    setConfigData(prev => {
      // Mevcut seçimleri al
      const currentSuites = Array.isArray(prev.selectedSuites)
        ? [...prev.selectedSuites]
        : [];
      const currentSuitesData = Array.isArray(prev.suitesData)
        ? [...prev.suitesData]
        : [];

      // Bu suite seçili mi kontrol et
      const isSelected = currentSuites.includes(suiteId);

      if (isSelected) {
        // Seçiliyi kaldır
        return {
          ...prev,
          selectedSuites: currentSuites.filter(id => id !== suiteId),
          suitesData: currentSuitesData.filter(s => s.id !== suiteId)
        };
      } else {
        // Suite'in detaylarını bul
        const suiteDetails = suites.find(s => s.id === suiteId);
        const newSuite = {
          id: suiteId,
          name: suiteDetails?.name || `Suite ${suiteId}`
        };

        // Yeni suite ekle
        return {
          ...prev,
          selectedSuites: [...currentSuites, suiteId],
          suitesData: [...currentSuitesData, newSuite]
        };
      }
    });
  };

  // Disconnect fonksiyonu
  const handleDisconnect = async () => {
    setIsDisconnecting(true);
    try {
      const response = await pluginApi.disconnectTestRail();

      if (response.success) {
        toast({
          title: "Disconnected Successfully",
          description: "TestRail integration has been disconnected successfully",
          variant: "default",
        });

        // UI'ı güncelle
        setIsActive(false);
        setActiveConfigData(null);
        setShowDisconnectDialog(false);

        // Konfigürasyon verilerini temizle
        setConfigData({
          url: "",
          apiKey: "",
          email: "",
          selectedProjects: [],
          selectedSuites: [],
          projectsData: [],
          suitesData: [],
        });

        // İlk adıma dön
        setConfigStep(1);
        setConnectionStatus("idle");
        setConnectionError("");
      } else {
        toast({
          title: "Disconnect Failed",
          description: response.error || "Failed to disconnect TestRail integration",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "An error occurred while disconnecting",
        variant: "destructive",
      });
    } finally {
      setIsDisconnecting(false);
    }
  };

  // Sayfa ilk yüklendiğinde, mevcut bir TestRail konfigürasyonu var mı kontrol et
  useEffect(() => {
    const checkExistingConfig = async () => {
      try {
        // Yeni API yapısını kullan
        const response = await pluginApi.getTestRailConfig();

        if (response.success && response.data && response.data.plugin) {
          // Mevcut konfigurasyon var, aktif duruma geç
          setIsActive(true);
          setActiveConfigData(response.data);

          // Son güncelleme zamanını formatla
          if (response.data?.plugin?.updatedAt) {
            formatLastUpdatedTime(response.data.plugin.updatedAt);
          }
        } else {
          // Konfigürasyon yok, yeni konfigürasyon ekranını göster
          setIsActive(false);
          setActiveConfigData(null);
        }
      } catch (error) {
        // Hata olursa, sessizce başlangıç konfigürasyon ekranını göster
        setIsActive(false);
        setActiveConfigData(null);
      }
    };

    // Sayfa yüklendiğinde mevcut konfigürasyonu kontrol et
    checkExistingConfig();
  }, []);

  // Son güncelleme zamanını formatlama yardımcı fonksiyonu
  const formatLastUpdatedTime = (updatedAt: string) => {
    const date = new Date(updatedAt);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);

    if (diffMins < 60) {
      setLastSyncedTime(`${diffMins} dakika önce`);
    } else if (diffMins < 1440) {
      setLastSyncedTime(`${Math.floor(diffMins / 60)} saat önce`);
    } else {
      setLastSyncedTime(`${Math.floor(diffMins / 1440)} gün önce`);
    }
  };

  // fetchActiveConfig fonksiyonunu güncelle - zaten bir formatlama fonksiyonumuz var
  const fetchActiveConfig = async () => {
    setIsLoadingConfig(true);
    try {
      // Yeni API yapısını kullan
      const response = await pluginApi.getTestRailConfig();

      if (response.success) {
        setActiveConfigData(response.data);
        setIsActive(true); // Başarılı yanıt durumunda isActive'i true yap

        // Son güncelleme zamanını formatla
        if (response.data?.plugin?.updatedAt) {
          formatLastUpdatedTime(response.data.plugin.updatedAt);
        }
      } else {
        // Configuration not found - this is normal if not configured yet
        setIsActive(false);
        setActiveConfigData(null);
        // Don't show error toast for missing configuration - it's expected
        console.debug('TestRail configuration not found - user has not configured it yet');
      }
    } catch (error) {
      // Error occurred - could be network issue or configuration not found
      setIsActive(false);
      setActiveConfigData(null);
      console.debug('Error fetching TestRail configuration:', error);
      // Only show error toast for actual errors, not missing configuration
      if (error && typeof error === 'object' && 'message' in error &&
          !error.message.includes('not configured') && !error.message.includes('not found')) {
        toast({
          title: "Error",
          description: "Failed to fetch TestRail configuration due to network error.",
          variant: "destructive"
        });
      }
    } finally {
      setIsLoadingConfig(false);
    }
  };

  // Burada sadece isActive değiştiğinde tekrar yüklememiz yeterli
  useEffect(() => {
    if (isActive) {
      fetchActiveConfig();
    }
  }, [isActive]);

  // Automatically fetch suites when projects are selected/deselected
  useEffect(() => {
    const fetchSuitesForSelectedProjects = async () => {
      // Only fetch if we have selected projects and we're in configuration mode (not active)
      if (!isActive && configData.selectedProjects && configData.selectedProjects.length > 0 && configData.url && configData.email && configData.apiKey) {
        try {
          setIsLoadingSuites(true);

          const response = await pluginApi.getTestRailSuites({
            url: configData.url,
            username: configData.email,
            apiKey: configData.apiKey,
            projectIds: configData.selectedProjects
          });

          if (response.success) {
            setSuites(response.data?.suites || []);
            setSuitesByProject(response.data?.suitesByProject || {});
          } else {
            // Clear suites on error but don't show toast to avoid spam
            setSuites([]);
            setSuitesByProject({});
          }
        } catch (error) {
          setSuites([]);
          setSuitesByProject({});
        } finally {
          setIsLoadingSuites(false);
        }
      } else if (configData.selectedProjects && configData.selectedProjects.length === 0) {
        // Clear suites when no projects are selected
        setSuites([]);
        setSuitesByProject({});
      }
    };

    // Debounce the API call to avoid too many requests
    const timeoutId = setTimeout(fetchSuitesForSelectedProjects, 300);

    return () => clearTimeout(timeoutId);
  }, [configData.selectedProjects, configData.url, configData.email, configData.apiKey, isActive]);

  // "Aktif" durumda olduğumuzda gösterilecek içeriği güncelle
  const renderActiveCard = () => {
    // Eğer activeConfigData null ise veya plugin alanı yoksa, aktif değil demektir
    if (!activeConfigData || !activeConfigData.plugin) {
      return (
        <Card className="border-0 shadow-md dark:bg-gray-900">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 rounded-full flex items-center justify-center">
                <AlertCircle className="h-5 w-5" />
              </div>
              <div>
                <CardTitle>TestRail Integration Not Configured</CardTitle>
                <CardDescription>Your TestRail integration is not set up yet. Please configure it below.</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="p-8 text-center text-gray-500">
              <p>No configuration data available. Please set up your TestRail integration.</p>
              <Button
                className="mt-4"
                onClick={() => setIsActive(false)}
              >
                Configure TestRail
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card className="border-0 shadow-md dark:bg-gray-900">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 rounded-full flex items-center justify-center">
              <Check className="h-5 w-5" />
            </div>
            <div>
              <CardTitle>TestRail Integration Active</CardTitle>
              <CardDescription>Your TestRail integration is set up and working correctly.</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoadingConfig ? (
            <div className="flex items-center justify-center p-8">
              <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-3 text-sm text-gray-500">Loading configuration...</span>
            </div>
          ) : activeConfigData ? (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Connection</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">TestRail URL</div>
                      <p className="text-sm font-medium">{activeConfigData.config?.url || "URL not available"}</p>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">Email</div>
                      <p className="text-sm font-medium">
                        {activeConfigData.config?.username || "Username not available"}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Project Settings</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">Projects</div>
                      <p className="text-sm font-medium">
                        {activeConfigData.stats?.projectCount || activeConfigData.config?.projectsData?.length || 0} project(s) selected
                      </p>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">Test Suites</div>
                      <p className="text-sm font-medium">
                        {activeConfigData.stats?.suiteCount || activeConfigData.config?.suitesData?.length || 0} suite(s) selected
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Seçilen Projeler ve Suitleri gösteren tablo */}
              <div className="border rounded-lg overflow-hidden">
                <div className="bg-gray-50 dark:bg-gray-800/50 px-4 py-3 border-b">
                  <h3 className="text-sm font-medium">Selected Items</h3>
                </div>

                {/* Projeler Tablosu */}
                <div className="p-4 border-b">
                  <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center justify-between">
                    <span>Projects</span>
                    <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded text-xs">
                      {activeConfigData.stats?.projectCount || 0}
                    </span>
                  </h4>

                  {activeConfigData.config?.projectsData && activeConfigData.config.projectsData.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                      {activeConfigData.config.projectsData.map((project: any, index: number) => (
                        <div
                          key={index}
                          className="py-1 px-2 bg-gray-50 dark:bg-gray-800/30 text-sm rounded flex items-center"
                        >
                          <div className="h-3 w-3 bg-blue-100 dark:bg-blue-900/50 rounded-full mr-2"></div>
                          <span className="truncate" title={project.name || `Project ${project.id}`}>
                            {project.name || `Project ${project.id}`}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : activeConfigData.config?.projectIds && activeConfigData.config.projectIds.length > 0 ? (
                    // Geriye dönük uyumluluk için sadece ID'ler ile gösterme
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                      {activeConfigData.config.projectIds.map((projectId: string | number, index: number) => (
                        <div
                          key={index}
                          className="py-1 px-2 bg-gray-50 dark:bg-gray-800/30 text-sm rounded flex items-center"
                        >
                          <div className="h-3 w-3 bg-blue-100 dark:bg-blue-900/50 rounded-full mr-2"></div>
                          <span className="truncate">{`Project ID: ${projectId}`}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No projects selected</p>
                  )}
                </div>

                {/* Suitler Tablosu */}
                <div className="p-4">
                  <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center justify-between">
                    <span>Test Suites</span>
                    <span className="px-2 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded text-xs">
                      {activeConfigData.stats?.suiteCount || 0}
                    </span>
                  </h4>

                  {activeConfigData.config?.suitesData && activeConfigData.config.suitesData.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                      {activeConfigData.config.suitesData.map((suite: any, index: number) => (
                        <div
                          key={index}
                          className="py-1 px-2 bg-gray-50 dark:bg-gray-800/30 text-sm rounded flex items-center"
                        >
                          <div className="h-3 w-3 bg-green-100 dark:bg-green-900/50 rounded-full mr-2"></div>
                          <span className="truncate" title={suite.name || `Suite ${suite.id}`}>
                            {suite.name || `Suite ${suite.id}`}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : activeConfigData.config?.suiteIds && activeConfigData.config.suiteIds.length > 0 ? (
                    // Geriye dönük uyumluluk için sadece ID'ler ile gösterme
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                      {activeConfigData.config.suiteIds.map((suiteId: string | number, index: number) => (
                        <div
                          key={index}
                          className="py-1 px-2 bg-gray-50 dark:bg-gray-800/30 text-sm rounded flex items-center"
                        >
                          <div className="h-3 w-3 bg-green-100 dark:bg-green-900/50 rounded-full mr-2"></div>
                          <span className="truncate">{`Suite ID: ${suiteId}`}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No test suites selected</p>
                  )}
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200 dark:border-gray-800">
                <h3 className="text-sm font-medium mb-3">Sync Status</h3>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs text-gray-500">
                    Last updated {lastSyncedTime || 'recently'}
                  </span>
                  <span className="text-xs font-medium">
                    {activeConfigData.plugin?.name || 'TestRail'} Integration
                  </span>
                </div>
                <Progress value={100} className="h-2 bg-gray-100 dark:bg-gray-800" />
              </div>
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              No configuration data available.
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t border-gray-200 dark:border-gray-800 px-6 py-4">
          <Button variant="outline" onClick={() => setIsActive(false)}>
            Reconfigure
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => setShowDisconnectDialog(true)}
            disabled={isDisconnecting}
          >
            {isDisconnecting ? "Disconnecting..." : "Disconnect"}
          </Button>
        </CardFooter>
      </Card>
    );
  };

  return (
    <CompanyOwnerRoute>
      <ErrorBoundary>
        <div className="flex h-screen bg-white dark:bg-gray-950">
          {/* Sidebar */}
          <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

          {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg flex items-center justify-center">
                <svg className="h-6 w-6" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 4L3 9L12 14L21 9L12 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  <path d="M3 14L12 19L21 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">TestRail Integration</h1>
                <p className="text-gray-500 dark:text-gray-400">
                  Connect your TestRail account to sync test cases and results
                </p>
              </div>
            </div>
          </header>

          <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950 p-6">
            <div className="max-w-3xl mx-auto">
              {isActive ? (
                renderActiveCard()
              ) : (
                <Card className="border-0 shadow-md dark:bg-gray-900">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-xl">{getStepTitle()}</CardTitle>
                    <CardDescription>
                      {configStep === 1 && "Enter your TestRail connection details and test the connection before proceeding."}
                      {configStep === 2 && "Select the project you want to connect with."}
                      {configStep === 3 && "Select the test suite you want to use."}
                    </CardDescription>
                  </CardHeader>

                  <CardContent>
                    <div className="mb-6">
                      <Progress
                        value={(configStep / 3) * 100}
                        className="h-2 bg-gray-100 dark:bg-gray-800"
                      />
                      <div className="flex justify-between mt-4">
                        <div className="flex flex-col items-center">
                          <div
                            className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              configStep >= 1
                                ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                                : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                            }`}
                          >
                            {configStep > 1 ? (
                              <Check className="h-5 w-5" />
                            ) : (
                              <span className="text-sm font-medium">1</span>
                            )}
                          </div>
                          <span className={`text-xs mt-2 ${
                            configStep >= 1
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-gray-400 dark:text-gray-500"
                          }`}>Connection</span>
                        </div>

                        <div className="w-full mx-4 mt-5">
                          <div className={`h-1 ${
                            configStep >= 2
                              ? "bg-blue-500 dark:bg-blue-600"
                              : "bg-gray-200 dark:bg-gray-700"
                          }`}></div>
                        </div>

                        <div className="flex flex-col items-center">
                          <div
                            className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              configStep >= 2
                                ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                                : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                            }`}
                          >
                            {configStep > 2 ? (
                              <Check className="h-5 w-5" />
                            ) : (
                              <span className="text-sm font-medium">2</span>
                            )}
                          </div>
                          <span className={`text-xs mt-2 ${
                            configStep >= 2
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-gray-400 dark:text-gray-500"
                          }`}>Project</span>
                        </div>

                        <div className="w-full mx-4 mt-5">
                          <div className={`h-1 ${
                            configStep >= 3
                              ? "bg-blue-500 dark:bg-blue-600"
                              : "bg-gray-200 dark:bg-gray-700"
                          }`}></div>
                        </div>

                        <div className="flex flex-col items-center">
                          <div
                            className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              configStep >= 3
                                ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                                : "bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500"
                            }`}
                          >
                            <span className="text-sm font-medium">3</span>
                          </div>
                          <span className={`text-xs mt-2 ${
                            configStep >= 3
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-gray-400 dark:text-gray-500"
                          }`}>Test Suite</span>
                        </div>
                      </div>
                    </div>

                    {configStep === 1 && (
                      <div className="space-y-5 pt-2">
                        {connectionStatus === "success" && (
                          <Alert className="bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-900 text-emerald-700 dark:text-emerald-400">
                            <CheckCircle2 className="h-5 w-5" />
                            <AlertTitle>Connection Successful</AlertTitle>
                            <AlertDescription>
                              Successfully connected to TestRail! You can now proceed to the next step.
                            </AlertDescription>
                          </Alert>
                        )}

                        {connectionStatus === "error" && (
                          <Alert className="bg-rose-50 dark:bg-rose-950/30 border-rose-200 dark:border-rose-900 text-rose-700 dark:text-rose-400">
                            <AlertCircle className="h-5 w-5" />
                            <AlertTitle>Connection Failed</AlertTitle>
                            <AlertDescription>
                              {connectionError || "Failed to connect to TestRail. Please check your credentials and try again."}
                            </AlertDescription>
                          </Alert>
                        )}

                        <div className="grid gap-3">
                          <Label htmlFor="url">TestRail URL <span className="text-rose-500">*</span></Label>
                          <Input
                            id="url"
                            placeholder="https://yourdomain.testrail.io"
                            value={configData.url}
                            onChange={(e) => setConfigData({ ...configData, url: e.target.value })}
                            className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Enter the URL of your TestRail instance (e.g., https://yourdomain.testrail.io)
                          </p>
                        </div>

                        <div className="grid gap-3">
                          <Label htmlFor="apiKey">API Key <span className="text-rose-500">*</span></Label>
                          <Input
                            id="apiKey"
                            type="password"
                            placeholder="Enter your API key"
                            value={configData.apiKey}
                            onChange={(e) => setConfigData({ ...configData, apiKey: e.target.value })}
                            className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            You can find your API key in TestRail under Admin &gt; Site Settings &gt; API
                          </p>
                        </div>

                        <div className="grid gap-3">
                          <Label htmlFor="email">Email <span className="text-rose-500">*</span></Label>
                          <Input
                            id="email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={configData.email}
                            onChange={(e) => setConfigData({ ...configData, email: e.target.value })}
                            className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            The email address associated with your TestRail account
                          </p>
                        </div>

                        <Button
                          onClick={testConnection}
                          disabled={isTestingConnection || !configData.url || !configData.apiKey || !configData.email}
                          variant="outline"
                          className="w-full mt-2 bg-white dark:bg-gray-800"
                        >
                          {isTestingConnection ? (
                            <>
                              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                              Testing Connection...
                            </>
                          ) : (
                            "Test Connection"
                          )}
                        </Button>
                      </div>
                    )}

                    {configStep === 2 && (
                      <div className="space-y-4 pt-2">
                        <div className="flex justify-between items-center">
                          <Label className="text-base">Select Project(s)</Label>
                          <div className="flex items-center gap-2">
                            {isLoadingSuites && (
                              <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
                                <LoaderCircle className="h-4 w-4 animate-spin mr-1" />
                                Loading suites...
                              </div>
                            )}
                            {configData.selectedProjects?.length > 0 && (
                              <span className="text-sm text-blue-600 dark:text-blue-400">
                                {configData.selectedProjects.length} project(s) selected
                              </span>
                            )}
                          </div>
                        </div>

                        {isLoadingProjects ? (
                          <div className="flex items-center justify-center p-8">
                            <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
                            <span className="ml-3 text-sm text-gray-500">Loading projects...</span>
                          </div>
                        ) : projects.length === 0 ? (
                          <div className="p-8 text-center">
                            <AlertCircle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
                            <p className="text-gray-600 dark:text-gray-400">No projects found.</p>
                          </div>
                        ) : (
                          <>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              {projects.map((project) => (
                              <div
                                key={project.id}
                                  className={`p-3 border rounded-lg cursor-pointer transition-all ${
                                    configData.selectedProjects?.includes(project.id.toString())
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700"
                                    : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-700"
                                }`}
                                  onClick={() => toggleProjectSelection(project.id.toString())}
                                >
                                  <div className="flex items-start justify-between">
                                    <div className="flex-1 pr-2 overflow-hidden">
                                      <h3 className="font-medium text-sm whitespace-nowrap overflow-hidden text-ellipsis" title={project.name}>
                                        {project.name}
                                      </h3>
                                      {project.announcement && (
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2 overflow-hidden" title={project.announcement}>
                                          {project.announcement}
                                        </p>
                                      )}
                                    </div>
                                    {configData.selectedProjects?.includes(project.id.toString()) && (
                                      <div className="h-6 w-6 flex-shrink-0 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center">
                                        <Check className="h-4 w-4" />
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Show suite count when projects are selected */}
                          {configData.selectedProjects?.length > 0 && !isLoadingSuites && (
                            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                              <div className="flex items-center text-sm text-blue-700 dark:text-blue-300">
                                <TestTube className="h-4 w-4 mr-2" />
                                {suites.length > 0 ? (
                                  <span>Found {suites.length} test suite(s) in selected project(s)</span>
                                ) : (
                                  <span>No test suites found in selected project(s)</span>
                                )}
                              </div>
                            </div>
                          )}
                          </>
                        )}
                      </div>
                    )}

                    {configStep === 3 && (
                      <div className="space-y-4 pt-2">
                        <div className="flex justify-between items-center">
                          <Label className="text-base">Select Test Suite(s)</Label>
                          {configData.selectedSuites?.length > 0 && (
                            <span className="text-sm text-blue-600 dark:text-blue-400">
                              {configData.selectedSuites.length} suite(s) selected
                            </span>
                          )}
                        </div>

                        {isLoadingSuites ? (
                          <div className="flex items-center justify-center p-8">
                            <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
                            <span className="ml-3 text-sm text-gray-500">Loading test suites...</span>
                          </div>
                        ) : suites.length === 0 ? (
                          <div className="p-8 text-center">
                            <AlertCircle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
                            <p className="text-gray-600 dark:text-gray-400">No test suites found for the selected projects.</p>
                          </div>
                        ) : (
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                            {suites.map((suite) => (
                            <div
                              key={suite.id}
                              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                                  configData.selectedSuites?.includes(suite.id)
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700"
                                  : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-700"
                              }`}
                                onClick={() => toggleSuiteSelection(suite.id)}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 pr-2 overflow-hidden">
                                    <h3 className="font-medium text-sm whitespace-nowrap overflow-hidden text-ellipsis" title={suite.name}>
                                      {suite.name}
                                    </h3>
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                                      {suite.description || "No description available"}
                                  </p>
                                </div>
                                  {configData.selectedSuites?.includes(suite.id) && (
                                    <div className="h-6 w-6 flex-shrink-0 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center">
                                    <Check className="h-4 w-4" />
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                        )}
                      </div>
                    )}
                  </CardContent>

                  <CardFooter className="flex justify-between border-t border-gray-200 dark:border-gray-800 px-6 py-4">
                    {configStep > 1 ? (
                      <Button variant="outline" onClick={handlePrevStep} className="bg-white dark:bg-gray-800">
                        Back
                      </Button>
                    ) : (
                      <Button variant="outline" onClick={() => setIsActive(true)} className="bg-white dark:bg-gray-800">
                        Cancel
                      </Button>
                    )}
                    <Button
                      onClick={handleNextStep}
                      disabled={
                        (configStep === 1 && connectionStatus !== "success") ||
                        (configStep === 2 && (!configData.selectedProjects || configData.selectedProjects.length === 0 || isLoadingSuites)) ||
                        (configStep === 3 && (!configData.selectedSuites || configData.selectedSuites.length === 0)) ||
                        isLoadingProjects ||
                        isLoadingSuites ||
                        isSavingConfig
                      }
                      className="gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                    >
                      {configStep === 1 ? (
                        <>
                          {isSavingConfig ? (
                            <>
                              <LoaderCircle className="h-4 w-4 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            <>
                              Save & Continue
                              <ArrowRight className="h-4 w-4" />
                            </>
                          )}
                        </>
                      ) : configStep < 3 ? (
                        <>
                          Next
                          <ArrowRight className="h-4 w-4" />
                        </>
                      ) : (
                        "Save & Activate"
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              )}
            </div>
          </main>
        </div>
      </div>

      {/* Disconnect Confirmation Dialog */}
      <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Disconnect TestRail Integration
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-400">
              Are you sure you want to disconnect TestRail integration? This will remove all configuration settings and you'll need to reconfigure the integration if you want to use it again.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-900 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-red-800 dark:text-red-300 mb-1">
                    This action cannot be undone
                  </p>
                  <p className="text-red-700 dark:text-red-400">
                    All TestRail configuration data including project selections, suite mappings, and authentication details will be permanently removed.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowDisconnectDialog(false)}
              disabled={isDisconnecting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDisconnect}
              disabled={isDisconnecting}
              className="gap-2"
            >
              {isDisconnecting ? (
                <>
                  <LoaderCircle className="h-4 w-4 animate-spin" />
                  Disconnecting...
                </>
              ) : (
                "Yes, Disconnect"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </ErrorBoundary>
    </CompanyOwnerRoute>
  )
}

