/**
 * Storage Configuration Domain
 * Handles MinIO and AWS S3 storage configuration settings
 */

import { getEnv, getEnvBoolean, getEnvInt } from '../utils/env-utils.js';
import { MinioConfigDomain, S3ConfigDomain, WebSocketConfigDomain } from '../types/config-types.js';

/**
 * Creates MinIO configuration from environment variables
 * @returns MinIO configuration object
 */
export function createMinioConfig(): MinioConfigDomain {
  const endPoint = getEnv('MINIO_ENDPOINT', 'localhost');
  const port = getEnvInt('MINIO_PORT', 9000);

  return {
    enabled: process.env.MINIO_ENABLED === 'true', // Default false unless explicitly set to true
    endPoint,
    port,
    useSSL: getEnvBoolean('MINIO_USE_SSL', false),
    accessKey: getEnv('MINIO_ACCESS_KEY', 'minioadmin'),
    secretKey: getEnv('MINIO_SECRET_KEY', 'minioadmin'),
    region: getEnv('MINIO_REGION', ''),
    screenshotBucket: getEnv('MINIO_SCREENSHOT_BUCKET', 'test-screenshots'),
    reportBucket: getEnv('MINIO_REPORT_BUCKET', 'test-reports'),
    // Use MINIO_PUBLIC_URL if provided, otherwise construct from endPoint and port
    publicBaseUrl: process.env.MINIO_PUBLIC_URL || `http://${endPoint}:${port}`
  };
}

/**
 * Creates AWS S3 configuration from environment variables
 * @returns S3 configuration object
 */
export function createS3Config(): S3ConfigDomain {
  const region = getEnv('AWS_REGION', 'eu-west-1');
  const accessKey = getEnv('AWS_ACCESS_KEY_ID', ''); // No default value for security
  const secretKey = getEnv('AWS_SECRET_ACCESS_KEY', ''); // No default value for security
  const endpoint = process.env.S3_ENDPOINT; // Optional custom endpoint
  const screenshotBucket = getEnv('S3_SCREENSHOT_BUCKET', 'test-screenshots');

  // Construct the public base URL
  const publicBaseUrl = process.env.S3_PUBLIC_URL ||
    (endpoint ?
      `${endpoint}` :
      (accessKey && secretKey ?
        `https://${screenshotBucket}.s3.${region}.amazonaws.com` : ''));

  return {
    enabled: process.env.S3_ENABLED === 'true', // Default false unless explicitly set to true
    region,
    accessKey,
    secretKey,
    endpoint,
    forcePathStyle: getEnvBoolean('S3_FORCE_PATH_STYLE', false),
    screenshotBucket,
    reportBucket: getEnv('S3_REPORT_BUCKET', 'test-reports'),
    publicBaseUrl
  };
}

/**
 * Creates WebSocket configuration from environment variables
 * @returns WebSocket configuration object
 */
export function createWebSocketConfig(): WebSocketConfigDomain {
  return {
    enabled: process.env.WEBSOCKET_ENABLED !== 'false' // Default true unless explicitly set to false
  };
}

/**
 * Validates MinIO configuration
 * @param config MinIO configuration to validate
 * @returns Validation result
 */
export function validateMinioConfig(config: MinioConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.enabled) {
    return { isValid: true, errors: [] }; // Skip validation if disabled
  }

  // Validate required fields
  if (!config.endPoint || config.endPoint.trim() === '') {
    errors.push('MinIO endpoint is required when MinIO is enabled');
  }

  if (config.port < 1 || config.port > 65535) {
    errors.push('MinIO port must be between 1 and 65535');
  }

  if (!config.accessKey || config.accessKey.trim() === '') {
    errors.push('MinIO access key is required when MinIO is enabled');
  }

  if (!config.secretKey || config.secretKey.trim() === '') {
    errors.push('MinIO secret key is required when MinIO is enabled');
  }

  if (!config.screenshotBucket || config.screenshotBucket.trim() === '') {
    errors.push('MinIO screenshot bucket is required when MinIO is enabled');
  }

  if (!config.reportBucket || config.reportBucket.trim() === '') {
    errors.push('MinIO report bucket is required when MinIO is enabled');
  }

  // Validate bucket names (basic S3 bucket naming rules)
  const bucketNameRegex = /^[a-z0-9][a-z0-9.-]*[a-z0-9]$/;
  if (config.screenshotBucket && !bucketNameRegex.test(config.screenshotBucket)) {
    errors.push('MinIO screenshot bucket name must follow S3 naming conventions');
  }

  if (config.reportBucket && !bucketNameRegex.test(config.reportBucket)) {
    errors.push('MinIO report bucket name must follow S3 naming conventions');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates S3 configuration
 * @param config S3 configuration to validate
 * @returns Validation result
 */
export function validateS3Config(config: S3ConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.enabled) {
    return { isValid: true, errors: [] }; // Skip validation if disabled
  }

  // Validate required fields
  if (!config.region || config.region.trim() === '') {
    errors.push('S3 region is required when S3 is enabled');
  }

  if (!config.accessKey || config.accessKey.trim() === '') {
    errors.push('S3 access key is required when S3 is enabled');
  }

  if (!config.secretKey || config.secretKey.trim() === '') {
    errors.push('S3 secret key is required when S3 is enabled');
  }

  if (!config.screenshotBucket || config.screenshotBucket.trim() === '') {
    errors.push('S3 screenshot bucket is required when S3 is enabled');
  }

  if (!config.reportBucket || config.reportBucket.trim() === '') {
    errors.push('S3 report bucket is required when S3 is enabled');
  }

  // Validate bucket names (S3 bucket naming rules)
  const bucketNameRegex = /^[a-z0-9][a-z0-9.-]*[a-z0-9]$/;
  if (config.screenshotBucket && !bucketNameRegex.test(config.screenshotBucket)) {
    errors.push('S3 screenshot bucket name must follow S3 naming conventions');
  }

  if (config.reportBucket && !bucketNameRegex.test(config.reportBucket)) {
    errors.push('S3 report bucket name must follow S3 naming conventions');
  }

  // Validate region format
  const regionRegex = /^[a-z0-9-]+$/;
  if (config.region && !regionRegex.test(config.region)) {
    errors.push('S3 region must be a valid AWS region identifier');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets default MinIO configuration
 * @returns Default MinIO configuration
 */
export function getDefaultMinioConfig(): MinioConfigDomain {
  return {
    enabled: false,
    endPoint: 'localhost',
    port: 9000,
    useSSL: false,
    accessKey: 'minioadmin',
    secretKey: 'minioadmin',
    region: '',
    screenshotBucket: 'test-screenshots',
    reportBucket: 'test-reports',
    publicBaseUrl: 'http://localhost:9000'
  };
}

/**
 * Gets default S3 configuration
 * @returns Default S3 configuration
 */
export function getDefaultS3Config(): S3ConfigDomain {
  return {
    enabled: false,
    region: 'eu-west-1',
    accessKey: '',
    secretKey: '',
    endpoint: undefined,
    forcePathStyle: false,
    screenshotBucket: 'test-screenshots',
    reportBucket: 'test-reports',
    publicBaseUrl: ''
  };
}

/**
 * Gets default WebSocket configuration
 * @returns Default WebSocket configuration
 */
export function getDefaultWebSocketConfig(): WebSocketConfigDomain {
  return {
    enabled: true
  };
}
