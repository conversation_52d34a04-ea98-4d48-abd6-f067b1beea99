/**
 * User service module
 * Handles user authentication and management
 */

import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { logger } from '../../utils/logger.js';
import {
  isMongoDBInitialized,
  usersCollection,
  db,
  mongoTimestamp,
  ensureMongoDBConnection,
  teamsCollection,
  rolesCollection,
  teamInvitesCollection
} from './dbConnection.js';
import { createTeam } from './teamService.js';
import { addTeamMember } from './teamService.js';
import { createDefaultTeamRoles } from './roleService.js';
import { AccountType, TeamRole } from '../../models/user.js';
import { Permission, MembershipStatus } from '../../models/team.js';
import { config } from '../../config/index.js';
import { operationQueue } from '../../utils/operationQueue.js';

// JWT Token sabitleri
const JWT_SECRET = process.env.JWT_SECRET || 'default_jwt_secret_for_development';
const JWT_EXPIRY = process.env.JWT_EXPIRY || '7d';

/**
 * Register a new user
 * @param userData User registration data
 * @returns Registration result with JWT token
 */
export async function registerUser(userData: any) {
  try {
    if (!isMongoDBInitialized() || !usersCollection || !teamsCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!userData || !userData.email || !userData.password || !userData.name) {
      return { success: false, message: 'Missing required fields: email, password, name', data: null };
    }

    // Check if user already exists - this should be done before queueing
    const existingUser = await usersCollection.findOne({ email: userData.email });
    if (existingUser) {
      return { success: false, message: 'User already exists with this email', data: null };
    }

    // Hash password - this can be done before queueing
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(userData.password, salt);

    // Create new user object
    const newUser = {
      id: uuidv4(),
      email: userData.email,
      name: userData.name,
      password: hashedPassword,
      accountType: userData.accountType || 'user', // accountType kullan, role yerine
      teamRole: userData.teamRole || null, // Takım rolü opsiyonel, varsa kullan
      active: userData.active !== undefined ? userData.active : true,
      companyId: userData.companyId || null, // Company ID if provided
      teamId: userData.teamId || null, // Team ID if provided
      createdAt: mongoTimestamp(),
      updatedAt: mongoTimestamp(),
      lastLogin: null
    };

    // Queue the database operation
    return await operationQueue.enqueue(
      `register-user-${newUser.id}`,
      async () => {
        // Insert new user
        await usersCollection!.insertOne(newUser as any);
        const isFromAdminPanel = userData.fromAdminPanel === true;
        
        logger.info(`[USER-SERVICE] User creation context - fromAdminPanel: ${isFromAdminPanel}, accountType: ${newUser.accountType}, teamId: ${newUser.teamId}`);
        
        if (newUser.teamId && newUser.accountType !== 'admin' && !isFromAdminPanel) {
          try {
            const { addTeamMember } = await import('./teamService.js');

            // Null kontrolü yap
            if (!newUser.teamId || !newUser.id) {
              logger.error(`Cannot add user to team: teamId or userId is null. teamId=${newUser.teamId}, userId=${newUser.id}`);
            } else {
              // Takımın ve kullanıcının var olduğunu doğrula
              // DB bağlantısını kontrol et
              if (!teamsCollection) {
                logger.error(`Database connection not available, cannot validate team`);
                return;
              }

              // Takımın var olduğunu kontrol et
              const team = await teamsCollection.findOne({ id: newUser.teamId });

              if (!team) {
                logger.error(`Team with ID ${newUser.teamId} not found, cannot add user to team`);
              } else {
                // Kullanıcı ve takım var, şimdi takım üyesi ekle
                const teamMemberResult = await addTeamMember(
                  newUser.teamId,
                  newUser.id,
                  userData.teamRole || 'team_member',
                  userData.addedBy || newUser.id
                );

                if (!teamMemberResult.success) {
                  logger.warn(`Failed to add user ${newUser.id} to team ${newUser.teamId}: ${teamMemberResult.message}`);
                }
              }
            }
          } catch (error) {
            logger.error(`Error adding user to team: ${error}`);
            // Takıma ekleme hatası kullanıcı oluşturmayı etkilemeyecek
          }
        } else if (newUser.accountType === 'admin') {
          logger.info(`[USER-SERVICE] User ${newUser.id} is an admin account, skipping team member creation`);
        } else if (newUser.accountType === 'company_owner') {
          logger.info(`[USER-SERVICE] User ${newUser.id} is a company owner account, skipping team member creation here - will be handled by admin-routes`);
        } else if (isFromAdminPanel) {
          logger.info(`[USER-SERVICE] User ${newUser.id} is created from admin panel, skipping team member creation here - will be handled by admin-routes`);
        }
        logger.info(`User registered: ${newUser.email}`);

        // Generate JWT token
        // Yeni kayıt olan kullanıcılar için role ve permissions'ları al
        let roleId = newUser.teamRole; // Fallback olarak mevcut teamRole'ü kullan
        let permissions: Permission[] = [];

        if (newUser.teamId) {
          const roleData = await getUserRoleAndPermissions(newUser.id, newUser.teamId);
          if (roleData.roleId) {
            roleId = roleData.roleId;
          }
          if (roleData.permissions) {
            permissions = roleData.permissions;
          }
        }

        const payload = {
          userId: newUser.id,
          email: newUser.email,
          name: newUser.name,
          accountType: newUser.accountType,
          roleId: roleId, // teamRole yerine roleId kullan
          companyId: newUser.companyId,
          teamId: newUser.teamId,
          permissions: permissions // Permissions'ları ekle
        };

        const token = generateToken(payload);

        return {
          success: true,
          message: 'User registered successfully',
          data: {
            user: {
              id: newUser.id,
              email: newUser.email,
              name: newUser.name,
              accountType: newUser.accountType,
              teamRole: newUser.teamRole, // Geriye dönük uyumluluk için
              roleId: roleId, // Yeni roleId field'ı
              companyId: newUser.companyId || null,
              teamId: newUser.teamId || null,
              permissions: permissions // Permissions'ları da response'a ekle
            },
            token
          }
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error) {
    logger.error(`Error registering user: ${error}`);
    return { success: false, message: `Error registering user: ${error}`, data: null };
  }
}

/**
 * Login user - asenkron kuyruk kullanarak
 * @param email User email
 * @param password User password
 * @returns Login result with JWT token
 */
export async function loginUser(email: string, password: string) {
  try {
    if (!isMongoDBInitialized() || !usersCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!email || !password) {
      return { success: false, message: 'Email and password are required', data: null };
    }

    // Find user - sadece gerekli alanları getir
    // This needs to be done before queueing to validate credentials
    const user = await usersCollection.findOne(
      { email },
      { projection: { _id: 0 } }
    );
    if (!user) {
      return { success: false, message: 'Invalid credentials', data: null };
    }

    // Check password - this needs to be done before queueing
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return { success: false, message: 'Invalid credentials', data: null };
    }

    // Check if user is active
    if (user.active === false) {
      return { success: false, message: 'Account is disabled', data: null };
    }

    // Queue the login update operation
    return await operationQueue.enqueue(
      `login-user-${user.id}-${Date.now()}`,
      async () => {
        // Update last login
        if (usersCollection) {
          await usersCollection.updateOne(
            { email },
            { $set: {
              lastLogin: mongoTimestamp(),
              updatedAt: mongoTimestamp()
            }}
          );
        }

        // Generate JWT token
        // Kullanıcının role ve permissions'larını al
        let roleId = user.teamRole; // Fallback olarak mevcut teamRole'ü kullan
        let permissions: Permission[] = [];

        if (user.teamId) {
          const roleData = await getUserRoleAndPermissions(user.id, user.teamId);
          if (roleData.roleId) {
            roleId = roleData.roleId;
          }
          if (roleData.permissions) {
            permissions = roleData.permissions;
          }
        }

        const payload = {
          userId: user.id,
          email: user.email,
          name: user.name,
          accountType: user.accountType || 'user',
          roleId: roleId, // teamRole yerine roleId kullan
          companyId: user.companyId,
          teamId: user.teamId,
          permissions: permissions // Permissions'ları ekle
        };

        const token = generateToken(payload);

        return {
          success: true,
          message: 'Login successful',
          data: {
            user: {
              id: user.id,
              email: user.email,
              name: user.name,
              accountType: user.accountType || 'user',
              teamRole: user.teamRole, // Geriye dönük uyumluluk için
              roleId: roleId, // Yeni roleId field'ı
              companyId: user.companyId || null,
              teamId: user.teamId || null,
              permissions: permissions // Permissions'ları da response'a ekle
            },
            token
          }
        };
      },
      { priority: 'HIGH', maxRetries: 3 }
    );
  } catch (error) {
    logger.error(`Error logging in user: ${error}`);
    return { success: false, message: `Error logging in user: ${error}`, data: null };
  }
}

/**
 * Get user by ID
 * @param userId User ID
 * @returns User data
 */
export async function getUserById(userId: string) {
  try {
    if (!isMongoDBInitialized() || !usersCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!userId) {
      return { success: false, message: 'User ID is required', data: null };
    }

    const user = await usersCollection.findOne(
      { id: userId },
      { projection: { _id: 0 } }
    );
    if (!user) {
      return { success: false, message: 'User not found', data: null };
    }

    // Remove sensitive information
    const { password, ...userWithoutPassword } = user;

    return {
      success: true,
      message: 'User retrieved successfully',
      data: userWithoutPassword
    };
  } catch (error) {
    logger.error(`Error getting user: ${error}`);
    return { success: false, message: `Error getting user: ${error}`, data: null };
  }
}

/**
 * Şirket kullanıcılarını getir
 * @param companyId Şirket ID'si
 * @returns Şirket kullanıcıları
 */
export async function getCompanyUsers(companyId: string) {
  try {
    if (!isMongoDBInitialized() || !usersCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!companyId) {
      return { success: false, message: 'Company ID is required', data: null };
    }

    // Şirket kullanıcılarını getir - şifre ve _id hariç
    const users = await usersCollection.find(
      { companyId },
      { projection: { password: 0, _id: 0 } }
    ).toArray();

    // Projection ile zaten şifreleri hariç tuttuk, ek işleme gerek yok

    return {
      success: true,
      message: 'Company users retrieved successfully',
      users: users
    };
  } catch (error) {
    logger.error(`Error getting company users: ${error}`);
    return { success: false, message: `Error getting company users: ${error}`, data: null };
  }
}

/**
 * Update user - asenkron kuyruk kullanarak
 * @param userId User ID
 * @param updateData Data to update
 * @returns Updated user data
 */
export async function updateUser(userId: string, updateData: any) {
  try {
    if (!isMongoDBInitialized() || !usersCollection || !db) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!userId) {
      return { success: false, message: 'User ID is required', data: null };
    }

    // Remove fields that shouldn't be updated
    const { id, password, email, createdAt, ...filteredUpdateData } = updateData;

    // Add updatedAt
    const updateObj = {
      ...filteredUpdateData,
      updatedAt: mongoTimestamp()
    };

    // Queue the database operation
    return await operationQueue.enqueue(
      `update-user-${userId}-${Date.now()}`,
      async () => {
        if (!usersCollection) {
          return { success: false, message: 'Database connection not established', data: null };
        }

        // Önce kullanıcının var olup olmadığını kontrol et
        const user = await usersCollection.findOne({ id: userId });

        if (!user) {
          logger.error(`User with ID ${userId} not found for update`);
          return { success: false, message: 'User not found', data: null };
        }

        // Update user
        try {
          // Önceki kullanıcı bilgilerini sakla
          const previousTeamRole = user.teamRole;
          // We'll only use previousTeamRole for now, but keep these commented for future use
          // const previousCompanyId = user.companyId;
          // const previousTeamId = user.teamId;

          // Use atomic findOneAndUpdate with retry logic for write conflicts
          let updateResult;
          let retryCount = 0;
          const maxRetries = 3;

          while (retryCount < maxRetries) {
            try {
              updateResult = await usersCollection.findOneAndUpdate(
                { id: userId },
                { $set: updateObj },
                { returnDocument: 'after' }
              );
              break; // Success, exit retry loop
            } catch (error: any) {
              retryCount++;
              if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
                // Duplicate key error or write conflict, or max retries reached
                logger.error(`User update failed for userId ${userId} after ${retryCount} attempts: ${error.message}`);
                throw error;
              }
              // Wait before retry with exponential backoff
              const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
              logger.warn(`Retrying user update for userId ${userId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          logger.info(`Update result for user ${userId}: matchedCount=${updateResult?.value ? 1 : 0}`);

          if (!updateResult || !updateResult.value) {
            return { success: false, message: 'User not found', data: null };
          }

          const updatedUser = updateResult.value;

          if (!updatedUser) {
            return { success: false, message: 'User updated but could not be retrieved', data: null };
          }

          // Team role değiştiğinde team_members koleksiyonunu güncelle
          if (updateObj.teamRole && updateObj.teamRole !== previousTeamRole) {
            try {
              await ensureMongoDBConnection();
              if (!db) {
                throw new Error('Database connection not available for team role update.');
              }

              const rolePermissionsCollection = db.collection('role_permissions');

              // Kullanıcının tüm takım üyeliklerini bul
              const memberships = await rolePermissionsCollection.find({
                user_id: userId,
                is_system: false
              }).toArray();

              if (memberships && memberships.length > 0) {
                logger.info(`Updating team role for user ${userId} in ${memberships.length} team memberships`);

                // Sistem rollerinden rol bilgilerini al
                let roleInfo = await rolePermissionsCollection.findOne({
                  role_id: updateObj.teamRole,
                  is_system: true
                });

                // Rol bulunamadıysa, küçük harfle kontrol et
                if (!roleInfo && typeof updateObj.teamRole === 'string') {
                  const lowerCaseRoleId = updateObj.teamRole.toLowerCase();
                  logger.info(`Role with ID ${updateObj.teamRole} not found, trying lowercase: ${lowerCaseRoleId}`);
                  roleInfo = await rolePermissionsCollection.findOne({
                    role_id: lowerCaseRoleId,
                    is_system: true
                  });
                }

                // Hala bulunamadıysa, varsayılan rol olarak 'viewer' kullan
                if (!roleInfo) {
                  logger.warn(`Role with ID ${updateObj.teamRole} not found, using default 'viewer' role`);
                  roleInfo = await rolePermissionsCollection.findOne({
                    role_id: 'viewer',
                    is_system: true
                  });

                  if (!roleInfo) {
                    logger.error(`Default role 'viewer' is also missing`);
                    // Bu durumda güncelleme yapmadan devam et
                    roleInfo = null; // roleInfo null olarak bırakılır ve aşağıdaki if kontrolü ile işlem atlanır
                  }
                }

                if (roleInfo) {
                  // Tüm takım üyeliklerini güncelle
                  const updatePromises = memberships.map(membership => {
                    return rolePermissionsCollection.updateOne(
                      { id: membership.id },
                      {
                        $set: {
                          role_id: updateObj.teamRole,
                          role_name: roleInfo.role_name,
                          permissions: roleInfo.permissions,
                          updated_at: new Date()
                        }
                      }
                    );
                  });

                  await Promise.all(updatePromises);

                } else {
                  logger.warn(`Role with ID ${updateObj.teamRole} not found in any role collections`);
                }
              }
            } catch (teamRoleError) {
              logger.error(`Error updating team role for user ${userId}: ${teamRoleError}`);
              // Hata durumunda işleme devam et, kullanıcı güncellendi ama takım rolü güncellenemedi
            }
          }

          // Remove sensitive information from the response
          const { password, ...userWithoutPassword } = updatedUser;

          return {
            success: true,
            message: 'User updated successfully',
            data: userWithoutPassword
          };
        } catch (error) {
          logger.error(`Error updating user: ${error}`);
          return { success: false, message: `Error updating user: ${error}`, data: null };
        }
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error) {
    logger.error(`Error updating user: ${error}`);
    return { success: false, message: `Error updating user: ${error}`, data: null };
  }
}

/**
 * Change user password
 * @param userId User ID
 * @param currentPassword Current password
 * @param newPassword New password
 * @returns Password change result
 */
export async function changePassword(userId: string, currentPassword: string, newPassword: string) {
  try {
    if (!isMongoDBInitialized() || !usersCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!userId || !currentPassword || !newPassword) {
      return { success: false, message: 'Missing required fields', data: null };
    }

    // Find user - sadece gerekli alanları getir
    const user = await usersCollection.findOne(
      { id: userId },
      { projection: { _id: 0 } }
    );
    if (!user) {
      return { success: false, message: 'User not found', data: null };
    }

    // Verify current password
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return { success: false, message: 'Current password is incorrect', data: null };
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password with retry logic for write conflicts
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const result = await usersCollection.updateOne(
          { id: userId },
          { $set: { password: hashedPassword, updatedAt: mongoTimestamp() } }
        );

        if (result.matchedCount === 0) {
          return { success: false, message: 'User not found during password update', data: null };
        }
        break; // Success, exit retry loop
      } catch (error: any) {
        retryCount++;
        if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
          // Duplicate key error or write conflict, or max retries reached
          logger.error(`Password update failed for userId ${userId} after ${retryCount} attempts: ${error.message}`);
          throw error;
        }
        // Wait before retry with exponential backoff
        const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
        logger.warn(`Retrying password update for userId ${userId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return { success: true, message: 'Password changed successfully', data: null };
  } catch (error) {
    logger.error(`Error changing password: ${error}`);
    return { success: false, message: `Error changing password: ${error}`, data: null };
  }
}

/**
 * Delete user
 * @param userId User ID
 * @returns Deletion result
 */
export async function deleteUser(userId: string) {
  try {
    if (!isMongoDBInitialized() || !usersCollection || !db) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!userId) {
      return { success: false, message: 'User ID is required', data: null };
    }

    // Önce kullanıcının var olup olmadığını kontrol et
    const user = await usersCollection.findOne({ id: userId });

    if (!user) {
      return { success: false, message: 'User not found', data: null };
    }

    // Kullanıcının takım üyeliklerini sil (role_permissions'dan)
    try {
      await ensureMongoDBConnection();

      if (db) {
        const rolePermissionsCollection = db.collection('role_permissions');
        const deleteResult = await rolePermissionsCollection.deleteMany({
          user_id: userId,
          is_system: false
        });
        logger.info(`Deleted ${deleteResult.deletedCount} team memberships for user ${userId}`);
      } else {
        logger.error('Database connection not available');
      }
    } catch (teamError) {
      logger.error(`Error deleting team memberships for user ${userId}: ${teamError}`);
      // Takım üyeliklerini silme hatası kullanıcı silme işlemini etkilemeyecek
    }

    // Kullanıcıyı sil
    const result = await usersCollection.deleteOne({ id: userId });

    if (result.deletedCount === 0) {
      return { success: false, message: 'User not found or could not be deleted', data: null };
    }

    return { success: true, message: 'User and all team memberships deleted successfully', data: null };
  } catch (error) {
    logger.error(`Error deleting user: ${error}`);
    return { success: false, message: `Error deleting user: ${error}`, data: null };
  }
}

/**
 * Verify JWT token
 * @param token JWT token
 * @returns Token verification result
 */
export function verifyToken(token: string) {
  try {
    if (!token) {
      return { success: false, message: 'Token is required', data: null };
    }

    const decoded = jwt.verify(token, JWT_SECRET) as any;

    // Map userId to id if it exists, to match UserTokenData interface
    if (decoded && decoded.userId) {
      decoded.id = decoded.userId;
    }

    logger.debug(`Token verified for user ID: ${decoded.id || decoded.userId || 'missing'}`);

    // Admin kullanıcıları için companyId ve teamId kontrolü yapmaya gerek yok
    if (decoded.accountType === 'admin') {
      logger.debug(`[TOKEN] Admin user token verified, skipping companyId and teamId check`);
    } else {
      // Normal kullanıcılar için companyId ve teamId kontrolü
      if (decoded.companyId) {
        logger.debug(`[TOKEN] Token contains companyId: ${decoded.companyId}`);
      } else {
        logger.warn(`[TOKEN] Token does not contain companyId`);
      }

      if (decoded.teamId) {
        logger.debug(`[TOKEN] Token contains teamId: ${decoded.teamId}`);
      } else {
        logger.warn(`[TOKEN] Token does not contain teamId`);
      }
    }

    return { success: true, message: 'Token is valid', data: decoded };
  } catch (error) {
    logger.error(`Error verifying token: ${error}`);
    return { success: false, message: 'Invalid token', data: null };
  }
}

/**
 * Generate JWT token
 * @param userData User data to include in token
 * @returns Generated JWT token
 */
export function generateToken(userData: any): string {
  const tokenPayload = {
    userId: userData.userId || userData.id,
    email: userData.email,
    accountType: userData.accountType || 'user',
    roleId: userData.roleId || userData.teamRole, // roleId'yi kullan, fallback olarak teamRole
    companyId: userData.companyId,
    teamId: userData.teamId,
    permissions: userData.permissions || [] // Permissions'ları ekle
  };

  // Token oluşturma detaylarını logla
  logger.debug(`Generating token for user: ${tokenPayload.email} (${tokenPayload.userId})`);

  // Log companyId and teamId if present
  if (tokenPayload.companyId) {
    logger.debug(`[TOKEN] Including companyId in token: ${tokenPayload.companyId}`);
  } else {
    logger.warn(`[TOKEN] No companyId found to include in token`);
  }

  if (tokenPayload.teamId) {
    logger.debug(`[TOKEN] Including teamId in token: ${tokenPayload.teamId}`);
  } else {
    logger.warn(`[TOKEN] No teamId found to include in token`);
  }

  const options: jwt.SignOptions = {
    expiresIn: JWT_EXPIRY as jwt.SignOptions['expiresIn'],
    issuer: 'testinium-ai-platform'
  };

  return jwt.sign(tokenPayload, JWT_SECRET, options);
}

/**
 * Register a new user and automatically create a personal team for them
 * @param userData User registration data
 * @returns Registration result with JWT token and team info
 */
export async function registerUserWithTeam(userData: any) {
  try {
    if (!isMongoDBInitialized() || !usersCollection) {
      return { success: false, message: 'Database connection not established', data: null };
    }

    if (!userData || !userData.email || !userData.password) {
      return { success: false, message: 'Missing required fields: email, password', data: null };
    }

    // Check if user already exists - projection kullanarak performansı artır
    const existingUser = await usersCollection.findOne(
      { email: userData.email },
      { projection: { id: 1, email: 1, _id: 0 } }
    );

    if (existingUser) {
      return { success: false, message: 'User already exists with this email', data: null };
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(userData.password, salt);

    // Takım yönetimi için Şirket Lideri rolü
    const defaultTeamRole = userData.accountType === 'companyLeader' ?
      TeamRole.COMPANY_LEADER : TeamRole.TEAM_MEMBER;

    // Create new user object
    const newUser = {
      id: uuidv4(),
      email: userData.email,
      password: hashedPassword,
      name: userData.name || userData.email.split('@')[0], // İsim yoksa e-posta adresinden oluştur
      accountType: userData.accountType || 'user', // accountType kullan, role yerine
      teamRole: defaultTeamRole, // Takım rolünü ayarla
      active: true,
      createdAt: mongoTimestamp(),
      updatedAt: mongoTimestamp(),
      lastLogin: null
    };

    try {
      // 1. Kullanıcıyı kaydet
      const userResult = await usersCollection.insertOne(newUser);

      if (!userResult || !userResult.acknowledged || !userResult.insertedId) {
        return { success: false, message: 'Failed to insert user', data: null };
      }

      // 2. Takım oluştur
      const userId = newUser.id;
      const defaultTeamName = userData.teamName || `${newUser.name}'s Team`;

      const teamResult = await createTeam({
        name: defaultTeamName,
        createdBy: userId,
        description: `Default team for ${newUser.name}`
      });

      if (!teamResult.success) {
        // Kullanıcı oluşturuldu ama takım oluşturulamadı, kullanıcıyı sil
        await usersCollection.deleteOne({ id: userId });
        return { success: false, message: `Failed to create team: ${teamResult.message}`, data: null };
      }

      const teamId = teamResult.teamId;

      // 3. Kullanıcıyı takıma ekle - team_admin rolü ile
      if (teamId) {
        // ✅ DÜZELTME: 'owner' rolü yerine team'e özel 'team_admin' rolü kullan
        const teamSpecificAdminRole = `team_admin_${teamId}`;
        const teamMemberResult = await addTeamMember(teamId, userId, teamSpecificAdminRole, userId);

        if (!teamMemberResult.success) {
          // Takım oluşturuldu ama kullanıcı eklenemedi, temizlik yap
          await usersCollection.deleteOne({ id: userId });
          return {
            success: false,
            message: `Failed to add user to team: ${teamMemberResult.message}`,
            data: null
          };
        }
        
        logger.info(`[USER-SERVICE] Successfully added team owner ${userId} to team ${teamId} with role ${teamSpecificAdminRole}`);
      }

      // 4. Kullanıcıyı güncelle - teamId alanını set et
      if (teamId) {
        await usersCollection.updateOne(
          { id: userId },
          {
            $set: {
              teamId: teamId,
              updatedAt: mongoTimestamp()
            }
          }
        );
      }

      // 5. JWT token oluştur
      const token = generateToken({
        userId: newUser.id,
        email: newUser.email,
        name: newUser.name,
        accountType: newUser.accountType,
        teamRole: newUser.teamRole,
        companyId: null,
        teamId: teamId
      });

      // 6. Sonucu hazırla
      return {
        success: true,
        message: 'User and team created successfully',
        data: {
          user: {
            id: newUser.id,
            email: newUser.email,
            name: newUser.name,
            accountType: newUser.accountType,
            teamRole: newUser.teamRole
          },
          team: {
            id: teamId,
            name: defaultTeamName
          },
          token
        }
      };
    } catch (error: any) {
      logger.error(`Error in registerUserWithTeam: ${error.message}`);
      return { success: false, message: `Error registering user with team: ${error.message}`, data: null };
    }
  } catch (error: any) {
    logger.error(`Error registering user with team: ${error.message}`);
    return { success: false, message: `Error registering user with team: ${error.message}`, data: null };
  }
}

/**
 * Kullanıcıyı bir takıma davet et ve otomatik ekle
 * @param userId İşlemi yapan kullanıcı ID'si
 * @param teamId Takım ID'si
 * @param invitedEmail Eklenecek kullanıcı e-posta adresi
 * @param roleId Verilecek rol ID'si
 * @param password Kullanıcı şifresi (opsiyonel, verilmezse otomatik oluşturulur)
 * @returns İşlem sonucu
 */
export async function inviteUserToTeam(
  userId: string,
  teamId: string,
  invitedEmail: string,
  roleId: string,
  password?: string
): Promise<{
  success: boolean;
  message?: string;
  newUserId?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !usersCollection || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Takım bilgilerini al
    const team = await teamsCollection.findOne({ id: teamId });

    if (!team) {
      return { success: false, message: 'Team not found' };
    }

    // Davet edilen kullanıcı zaten kayıtlı mı?
    const existingUser = await usersCollection.findOne({ email: invitedEmail });

    if (existingUser) {
      // Kullanıcı zaten var, doğrudan takıma ekle
      const result = await addTeamMember(
        teamId,
        existingUser.id,
        roleId,
        userId
      );

      // Kullanıcının teams dizisini güncelle (eğer yoksa)
      if (!existingUser.teams || !existingUser.teams.includes(teamId)) {
        const teams = existingUser.teams || [];
        await usersCollection.updateOne(
          { id: existingUser.id },
          { $set: { teams: [...teams, teamId] } }
        );
      }

      return {
        success: result.success,
        message: result.message || (result.success ? 'User added to team successfully' : 'Failed to add user to team')
      };
    }

    // Kullanıcı kayıtlı değil, otomatik oluştur ve ekle
    const newUserId = uuidv4();

    // Şifre oluştur - ya kullanıcının belirlediği ya da otomatik oluşturulan
    let hashedPassword;
    if (password) {
      // El ile girilen şifreyi hash'le
      const salt = await bcrypt.genSalt(10);
      hashedPassword = await bcrypt.hash(password, salt);
      logger.info(`Using manually provided password for user ${invitedEmail}`);
    } else {
      // Otomatik şifre oluştur
      const autoPassword = generateRandomPassword();
      const salt = await bcrypt.genSalt(10);
      hashedPassword = await bcrypt.hash(autoPassword, salt);
      logger.info(`Auto-generated password for user ${invitedEmail}: ${autoPassword} (would be sent via email)`);
    }

    // Kullanıcı adı olarak email'in @ işaretinden önceki kısmını al
    const defaultName = invitedEmail.split('@')[0];

    // Yeni kullanıcı oluştur
    const newUser = {
      id: newUserId,
      email: invitedEmail,
      name: defaultName,
      password: hashedPassword,
      accountType: 'user',        // accountType kullan, role yerine
      teamRole: roleId,           // Takım rolü olarak roleId'yi kullan
      companyId: team.companyId,
      active: true,
      createdAt: mongoTimestamp(),
      updatedAt: mongoTimestamp(),
      lastLogin: null,
      teamId: teamId              // Ana takım ID'si
    };

    // Kullanıcıyı ekle
    await usersCollection.insertOne(newUser as any);
    logger.info(`Auto-registered user with ID: ${newUserId} for team: ${teamId}`);

    // Takıma ekle
    const memberResult = await addTeamMember(
      teamId,
      newUserId,
      roleId,
      userId
    );

    if (!memberResult.success) {
      logger.error(`Failed to add auto-created user to team: ${memberResult.message}`);
      return { success: false, message: memberResult.message };
    }

    logger.info(`Auto-created user ${newUserId} added to team ${teamId} with role ${roleId}`);

    return {
      success: true,
      message: `User auto-created and added to team with role: ${roleId}`,
      newUserId
    };
  } catch (error: any) {
    logger.error(`Error auto-adding user to team: ${error.message}`);
    return {
      success: false,
      message: `Failed to auto-add user: ${error.message}`
    };
  }
}

/**
 * Rastgele güvenli şifre oluşturur
 * @returns Oluşturulan şifre
 */
function generateRandomPassword(): string {
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  const symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const allChars = lowercase + uppercase + numbers + symbols;

  let password = "";
  // Ensure at least one character from each category
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];

  // Add more random characters to reach desired length (12)
  for (let i = 0; i < 8; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Shuffle the password
  return password
    .split("")
    .sort(() => 0.5 - Math.random())
    .join("");
}

/**
 * Kullanıcının takım davetini kabul et
 * @param inviteId Davet ID'si
 * @param userData Yeni kullanıcı bilgileri (kayıtlı değilse)
 * @returns İşlem sonucu
 */
export async function acceptTeamInvite(
  inviteId: string,
  userData?: {
    name: string;
    password: string;
  }
): Promise<{
  success: boolean;
  message?: string;
  userId?: string;
  teamId?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !usersCollection || !teamsCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    // Davet bilgilerini al
    if (!teamInvitesCollection) {
      logger.error('teamInvitesCollection is not initialized');
      return { success: false, message: 'Team invites collection not available' };
    }
    const invitesCollection = teamInvitesCollection;
    const invite = await invitesCollection.findOne({ id: inviteId, status: 'pending' });

    if (!invite) {
      return { success: false, message: 'Invalid or expired invitation' };
    }

    // Davet süresi doldu mu?
    if (new Date() > new Date(invite.expiresAt)) {
      await invitesCollection.updateOne(
        { id: inviteId },
        { $set: { status: 'expired' } }
      );
      return { success: false, message: 'Invitation has expired' };
    }

    // Davet edilen e-postaya sahip kullanıcı var mı?
    const existingUser = await usersCollection.findOne({ email: invite.email });
    let userId: string;

    if (existingUser) {
      // Kullanıcı zaten var
      userId = existingUser.id;
    } else {
      // Kullanıcı yok, kaydı oluştur
      if (!userData || !userData.name || !userData.password) {
        return { success: false, message: 'Missing user data for registration' };
      }

      // Şifreyi hashle
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // Team ve company bilgilerini al
      const team = await teamsCollection.findOne({ id: invite.teamId });

      if (!team) {
        return { success: false, message: 'Team not found' };
      }

      // Yeni kullanıcı oluştur
      userId = uuidv4();
      const newUser = {
        id: userId,
        email: invite.email,
        name: userData.name,
        password: hashedPassword,
        accountType: 'user',
        teamRole: invite.roleId,      // Takım rolü olarak davet rolünü kullan
        companyId: team.companyId,
        active: true,
        createdAt: mongoTimestamp(),
        updatedAt: mongoTimestamp(),
        lastLogin: null,
        teamId: invite.teamId         // Ana takım ID'si
      };

      await usersCollection.insertOne(newUser as any);
      logger.info(`User registered via invitation: ${userId}`);
    }

    // Kullanıcıyı takıma ekle
    const result = await addTeamMember(
      invite.teamId,
      userId,
      invite.roleId,
      invite.invitedBy
    );

    if (!result.success) {
      return {
        success: false,
        message: result.message || 'Failed to add user to team'
      };
    }

    // Daveti kabul edildi olarak işaretle
    await invitesCollection.updateOne(
      { id: inviteId },
      { $set: { status: 'accepted', acceptedAt: new Date() } }
    );

    return {
      success: true,
      message: 'Team invitation accepted successfully',
      userId,
      teamId: invite.teamId
    };
  } catch (error: any) {
    logger.error(`Error accepting team invitation: ${error.message}`);
    return {
      success: false,
      message: `Failed to accept invitation: ${error.message}`
    };
  }
}

/**
 * Kullanıcı profilini getir
 * @param userId Kullanıcı ID
 * @returns Kullanıcı profili
 */
export async function getUserProfile(userId: string): Promise<{
  success: boolean;
  message?: string;
  profile?: {
    name: string;
    email: string;
    jobTitle?: string;
    company?: string;
    phone?: string;
    avatar?: string;
  };
}> {
  try {
    if (!isMongoDBInitialized() || !db || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!userId) {
      return { success: false, message: 'User ID is required' };
    }

    // Kullanıcıyı bul
    const user = await usersCollection.findOne(
      { id: userId },
      { projection: { password: 0, _id: 0 } } // Hassas verileri hariç tut
    );

    if (!user) {
      return { success: false, message: 'User not found' };
    }

    return {
      success: true,
      profile: {
        name: user.name,
        email: user.email,
        jobTitle: user.jobTitle || '',
        company: user.company || '',
        phone: user.phone || '',
        avatar: user.avatar || '',
      }
    };
  } catch (error: any) {
    logger.error(`Error getting user profile: ${error.message}`);
    return { success: false, message: error.message };
  }
}

/**
 * Tüm kullanıcıları getir (admin için)
 * @param options Filtreleme ve sayfalama seçenekleri
 * @returns Kullanıcı listesi
 */
export async function getAllUsers(options: {
  limit?: number;
  skip?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  includeInactive?: boolean;
  accountType?: string;
  role?: string; // role parametresi eklendi
  active?: string; // active parametresi eklendi
  companyId?: string;
  page?: number;
} = {}) {
  try {
    if (!isMongoDBInitialized() || !usersCollection) {
      return { success: false, message: 'Database connection not established', users: [], total: 0 };
    }

    // Varsayılan değerler
    const page = options.page || 1;
    const limit = options.limit || 50;
    const skip = options.skip || (page - 1) * limit;
    const sortBy = options.sortBy || 'createdAt';
    const sortOrder = options.sortOrder || 'desc';
    const includeInactive = options.includeInactive || false;

    // Filtreleme kriterleri
    const filter: any = {};

    // Active parametresi kontrolü
    if (options.active === 'true') {
      filter.active = true;
    } else if (options.active === 'false') {
      filter.active = false;
    } else if (!includeInactive) {
      // Eğer active parametresi yoksa ve includeInactive false ise, sadece aktif kullanıcıları göster
      filter.active = true;
    }

    // Hesap türü filtresi
    if (options.accountType) {
      filter.accountType = options.accountType;
    }

    // Rol filtresi (teamRole)
    if (options.role) {
      filter.teamRole = options.role;
    }

    // Şirket filtresi
    if (options.companyId) {
      filter.companyId = options.companyId;
    }

    logger.info(`User filter criteria: ${JSON.stringify(filter)}`);


    // Arama filtresi - regex kullan (text indeksi apiStrict modunda çalışmıyor)
    if (options.search) {
      const searchRegex = new RegExp(options.search, 'i');
      filter.$or = [
        { name: searchRegex },
        { email: searchRegex }
      ];
    }

    // Toplam kayıt sayısını al - performans için estimate kullan
    let total;
    if (Object.keys(filter).length === 0 || (Object.keys(filter).length === 1 && filter.active === true)) {
      // Boş filtre veya sadece active=true için hızlı tahmin kullan
      total = await usersCollection.estimatedDocumentCount();
      if (!includeInactive) {
        // Aktif olmayan kullanıcıları tahmin et ve çıkar
        const inactiveCount = await usersCollection.countDocuments({ active: false });
        total -= inactiveCount;
      }
    } else {
      // Karmaşık filtreler için normal sayım
      total = await usersCollection.countDocuments(filter);
    }

    // Sıralama seçenekleri
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Kullanıcıları getir - performans için hint kullan
    let query = usersCollection.find(filter, {
      projection: { password: 0, _id: 0 } // Şifreleri ve MongoDB ID'lerini hariç tut
    });

    // Uygun indeksi seç
    if (options.accountType) {
      query = query.hint({ accountType: 1 });
    } else if (options.companyId) {
      query = query.hint({ companyId: 1 });
    }
    // Text indeksi kaldırıldığı için hint kısmını da kaldırdık

    const users = await query
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray();

    return {
      success: true,
      users,
      total,
      page: Math.floor(skip / limit) + 1,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error: any) {
    logger.error(`Error getting all users: ${error.message}`);
    return { success: false, message: error.message, users: [], total: 0 };
  }
}

/**
 * Kullanıcı profilini güncelle - asenkron kuyruk kullanarak
 * @param userId Kullanıcı ID
 * @param updateData Güncellenecek veriler
 * @returns İşlem sonucu
 */
export async function updateUserProfile(
  userId: string,
  updateData: {
    name?: string;
    jobTitle?: string;
    company?: string;
    phone?: string;
    avatar?: string;
  }
): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    if (!isMongoDBInitialized() || !db || !usersCollection) {
      return { success: false, message: 'Database connection not established' };
    }

    if (!userId) {
      return { success: false, message: 'User ID is required' };
    }

    // Güncelleme için geçerli alanları içeren bir nesne oluştur
    const updates: any = {};

    if (updateData.name !== undefined) updates.name = updateData.name;
    if (updateData.jobTitle !== undefined) updates.jobTitle = updateData.jobTitle;
    if (updateData.company !== undefined) updates.company = updateData.company;
    if (updateData.phone !== undefined) updates.phone = updateData.phone;
    if (updateData.avatar !== undefined) updates.avatar = updateData.avatar;

    // Güncellenecek veri yoksa hata döndür
    if (Object.keys(updates).length === 0) {
      return { success: false, message: 'No valid update data provided' };
    }

    // Kullanıcıyı güncelle - asenkron kuyruk kullanarak
    updates.updatedAt = mongoTimestamp();

    // Queue the database operation
    return await operationQueue.enqueue(
      `update-user-profile-${userId}-${Date.now()}`,
      async () => {
        if (!usersCollection) {
          return { success: false, message: 'Database connection not established' };
        }

        const result = await usersCollection.updateOne(
          { id: userId },
          { $set: updates },
          { hint: { id: 1 } } // id indeksini kullan
        );

        if (result.matchedCount === 0) {
          return { success: false, message: 'User not found' };
        }

        return {
          success: true,
          message: 'Profile updated successfully'
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`Error updating user profile: ${error.message}`);
    return { success: false, message: error.message };
  }
}

/**
 * Kullanıcının role ID'sini ve permissions'larını alır
 * @param userId Kullanıcı ID'si
 * @param teamId Takım ID'si
 * @returns Role ID ve permissions
 */
export async function getUserRoleAndPermissions(userId: string, teamId: string): Promise<{
  roleId?: string;
  permissions?: Permission[];
}> {
  try {
    if (!isMongoDBInitialized() || !db) {
      logger.warn('Database not initialized for getUserRoleAndPermissions');
      return {};
    }

    const rolePermissionsCollection = db.collection('role_permissions');

    // Kullanıcının takım üyeliğini role_permissions'dan bul
    const userRoleAssignment = await rolePermissionsCollection.findOne({
      user_id: userId,
      team_id: teamId,
      is_system: false, // Kullanıcı ataması
      status: 'active'
    });

    if (!userRoleAssignment) {
      logger.debug(`No active role assignment found for user ${userId} in team ${teamId}`);
      return {};
    }

    const roleId = userRoleAssignment.role_id;
    const permissions = userRoleAssignment.permissions || [];

    logger.debug(`Found role ${userRoleAssignment.role_name} with ${permissions.length} permissions for user ${userId}`);
    return {
      roleId,
      permissions
    };

  } catch (error: any) {
    logger.error(`Error getting user role and permissions: ${error.message}`);
    return {};
  }
}