/**
 * Scenario Utilities
 *
 * Helper functions for working with scenarios
 */

import type { ScenarioStatus } from '@/types/scenario'
import type { ScenarioWithMeta, ExtendedScenarioStatus } from '@/types/scenario-extended'

/**
 * Checks if a scenario is considered uncategorized
 * This function provides a consistent way to determine if a scenario belongs to the "uncategorized" folder
 *
 * @param folderId The folder ID to check
 * @returns True if the scenario should be considered uncategorized
 */
export function isScenarioUncategorized(folderId: string | null | undefined): boolean {
  return !folderId || folderId === "" || folderId === "none" || folderId === "1" || folderId === "uncategorized"
}

/**
 * Determines the status of a scenario based on a string value
 *
 * @param statusValue The status string to convert
 * @returns A valid ScenarioStatus
 */
export function determineStatus(statusValue: string | undefined): ScenarioStatus {
  if (!statusValue) return "unknown" as ScenarioStatus;

  const status = statusValue.toLowerCase();

  // Exact matches
  if (status === "passed" || status === "Passed") return "passed" as ScenarioStatus;
  if (status === "completed" || status === "complete") return "passed" as ScenarioStatus; // "completed" durumunu "passed" olarak işle
  if (status === "failed" || status === "fail") return "failed" as ScenarioStatus;
  if (status === "running" || status === "run") return "running" as ScenarioStatus;
  if (status === "queue" || status === "queued") return "queue" as ScenarioStatus;
  if (status === "new" || status === "New") return "new" as ScenarioStatus;
  if (status === "active") return "active" as ScenarioStatus;
  if (status === "inactive" || status === "passive") return "inactive" as ScenarioStatus;

  // Partial matches
  if (status.includes("pass")) return "passed" as ScenarioStatus;
  if (status.includes("complet")) return "passed" as ScenarioStatus; // "completed" veya "complete" içeren durumları "passed" olarak işle
  if (status.includes("fail") || status.includes("error")) return "failed" as ScenarioStatus;
  if (status.includes("run")) return "running" as ScenarioStatus;
  if (status.includes("queue")) return "queue" as ScenarioStatus;
  if (status.includes("new")) return "new" as ScenarioStatus;
  if (status.includes("active")) return "active" as ScenarioStatus;
  if (status.includes("inactive") || status.includes("passive")) return "inactive" as ScenarioStatus;

  return "unknown" as ScenarioStatus;
}

/**
 * Converts an API scenario to a ScenarioWithMeta
 *
 * @param apiScenario The scenario data from the API
 * @param existingScenario Optional existing scenario to merge with
 * @returns A ScenarioWithMeta object
 */
export function scenarioToScenarioWithMeta(apiScenario: any, existingScenario?: ScenarioWithMeta): ScenarioWithMeta {
  // Get the main status (active/inactive)
  const mainStatus = determineStatus(apiScenario.status);

  // Get the run status from runStatus field or lastRun.status
  const runStatus = apiScenario.runStatus ?
    determineStatus(apiScenario.runStatus) :
    apiScenario.lastRun?.status ?
      determineStatus(apiScenario.lastRun.status) :
      undefined;

  const newScenario: ScenarioWithMeta = {
    id: apiScenario.id,
    name: apiScenario.name || apiScenario.title || "Adsız Senaryo",
    description: apiScenario.description || "",
    testrailIntegration: apiScenario.testrailIntegration,
    status: mainStatus,
    runStatus: runStatus,
    // Map the platform field from API response
    platform: apiScenario.platform || 'web',
    // Map testType field - if not present, try to infer from platform
    testType: apiScenario.testType || (apiScenario.platform === 'android' || apiScenario.platform === 'ios' ? 'mobile' :
                                      apiScenario.platform === 'api' ? 'api' : 'web'),
    lastRun: (() => {
      // Check if lastRun exists and has a valid date
      if (apiScenario.lastRun?.date) {
        try {
          const date = new Date(apiScenario.lastRun.date);
          // Validate the date is valid
          if (!isNaN(date.getTime())) {
            return apiScenario.lastRun.date; // Return the ISO date string for consistent formatting
          }
        } catch (e) {
          console.warn("Invalid lastRun date format:", apiScenario.lastRun.date);
        }
      }

      // If we have updatedAt, use that instead
      if (apiScenario.updatedAt) {
        try {
          const date = new Date(apiScenario.updatedAt);
          // Validate the date is valid
          if (!isNaN(date.getTime())) {
            return apiScenario.updatedAt; // Return the ISO date string for consistent formatting
          }
        } catch (e) {
          console.warn("Invalid updatedAt date format:", apiScenario.updatedAt);
        }
      }

      // If no valid date found
      return "";
    })(),
    folderId: apiScenario.folderId !== undefined ? apiScenario.folderId : "1",
    tags: apiScenario.tags || [],
    steps: {
      total: parseInt(apiScenario.lastRun?.totalStep || '0'),
      completed: parseInt(apiScenario.lastRun?.succesStep || '0'),
      failed: parseInt(apiScenario.lastRun?.failedStep || '0')
    },
    duration: (apiScenario.lastRun?.duration || 0) / 1000,
    url: apiScenario.url || "",
    // Keep step progress info if existing
    stepProgress: existingScenario?.stepProgress,
    currentStepName: existingScenario?.currentStepName,
    // Add updatedAt and createdAt fields
    updatedAt: apiScenario.updatedAt || null,
    createdAt: apiScenario.createdAt || null,
  };

  return newScenario;
}
