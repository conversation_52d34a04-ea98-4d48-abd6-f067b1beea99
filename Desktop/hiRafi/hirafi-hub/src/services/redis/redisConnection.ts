/**
 * Redis Connection Service
 * Manages Redis connection and provides client instances
 */

import { Redis as RedisClient } from 'ioredis';
import { logger } from '../../utils/logger.js';
import { config } from '../../config/index.js';

class RedisConnectionService {
  private static instance: RedisConnectionService;
  private client: RedisClient | null = null;
  private _isConnected: boolean = false;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 10;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): RedisConnectionService {
    if (!RedisConnectionService.instance) {
      RedisConnectionService.instance = new RedisConnectionService();
    }
    return RedisConnectionService.instance;
  }

  /**
   * Initialize Redis connection
   */
  public async initialize(): Promise<boolean> {
    if (this.isConnected() && this.client) {
      return true;
    }

    try {
      const { host, port, password, username } = config.connections.redis;
      logger.info(`Redis: Connecting to ${host}:${port}`);

      // Create Redis client with individual connection parameters
      // BullMQ requires maxRetriesPerRequest to be null
      this.client = new RedisClient({
        host: host,
        port: port,
        username: username || undefined,
        password: password || undefined,
        maxRetriesPerRequest: null, // Required for BullMQ
        retryStrategy: (times: number) => {
          // Maximum retry delay is 10 seconds
          const delay = Math.min(times * 500, 10000);
          logger.debug(`Redis: Reconnect attempt ${times} in ${delay}ms`);
          return delay;
        }
      });

      // Set up event handlers
      this.setupEventHandlers();

      // IORedis automatically connects when instantiated, no need to call connect()
      this._isConnected = true;
      this.reconnectAttempts = 0;

      logger.info(`Redis: Connected to ${config.connections.redis.host}:${config.connections.redis.port}`);
      return true;
    } catch (error) {
      logger.error(`Redis: Connection error: ${error}`);
      this._isConnected = false;
      this.scheduleReconnect();
      return false;
    }
  }

  /**
   * Set up event handlers for Redis client
   */
  private setupEventHandlers(): void {
    if (!this.client) {
      return;
    }

    this.client.on('error', (error: Error) => {
      logger.error(`Redis: Client error: ${error}`);
      this._isConnected = false;
    });

    this.client.on('reconnecting', () => {
      logger.info('Redis: Attempting to reconnect...');
    });

    this.client.on('connect', () => {
      logger.info('Redis: Client connected');
      this._isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.client.on('end', () => {
      logger.info('Redis: Client connection closed');
      this._isConnected = false;
      this.scheduleReconnect();
    });
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error(`Redis: Maximum reconnection attempts (${this.maxReconnectAttempts}) reached`);
      return;
    }

    const delay = Math.min(Math.pow(2, this.reconnectAttempts) * 1000, 30000);
    logger.info(`Redis: Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);

    this.reconnectTimer = setTimeout(async () => {
      this.reconnectAttempts++;
      logger.info(`Redis: Reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      await this.initialize();
    }, delay);
  }

  /**
   * Get Redis client instance
   */
  public getClient(): RedisClient | null {
    return this.client;
  }

  /**
   * Check if Redis is connected
   */
  public isConnected(): boolean {
    return this._isConnected && this.client !== null;
  }

  /**
   * Close Redis connection
   */
  public async close(): Promise<void> {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.client) {
      try {
        await this.client.quit();
        logger.info('Redis: Connection closed');
      } catch (error) {
        logger.error(`Redis: Error closing connection: ${error}`);
      } finally {
        this.client = null;
        this._isConnected = false;
      }
    }
  }
}

// Export singleton instance
export const redisConnection = RedisConnectionService.getInstance();
export default redisConnection;
