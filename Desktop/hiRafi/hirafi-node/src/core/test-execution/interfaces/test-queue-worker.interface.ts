/**
 * Test Queue Worker Interface
 * Defines the contract for test queue processing
 * Refactored to use dependency injection and break circular dependencies
 */

import { EventEmitter } from 'events';
import { TestRequest } from '../../../models/types.js';

export interface ITestQueueWorker extends EventEmitter {
  /**
   * Initialize the worker
   */
  initialize(): Promise<boolean>;

  /**
   * Check if the worker is initialized
   */
  isInitialized(): boolean;

  /**
   * Pause the worker
   */
  pause(): Promise<void>;

  /**
   * Resume the worker
   */
  resume(): Promise<void>;

  /**
   * Check if the worker is paused
   */
  isPaused(): boolean;

  /**
   * Close the worker
   */
  close(): Promise<void>;

  /**
   * Stop a specific test if it's currently running
   * @param testId Test ID to stop
   * @returns Promise resolving to true if stopped successfully
   */
  stopTest(testId: string): Promise<boolean>;

  /**
   * Get worker statistics
   */
  getStats(): TestQueueWorkerStats;
}

export interface TestQueueWorkerEvents {
  'test:received': (testData: TestRequest) => void;
  'test:locked': (testId: string) => void;
  'test:error': (error: Error) => void;
  'test:released': (testId: string) => void;
  'testClaimedByWorker': (testId: string, nodeId: string) => void;
}

export interface TestQueueWorkerStats {
  nodeId: string;
  isInitialized: boolean;
  isPaused: boolean;
  processedJobs: number;
  failedJobs: number;
  currentJob: string | null;
  uptime: number;
  lastActivity: string | null;
}

export interface TestQueueWorkerConfig {
  concurrency?: number;
  maxStalled?: number;
  stalledInterval?: number;
  enableLogging?: boolean;
}
