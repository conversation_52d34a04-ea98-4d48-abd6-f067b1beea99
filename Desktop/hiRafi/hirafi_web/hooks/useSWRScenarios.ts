/**
 * Unified Scenario Data Hook
 * 
 * Single source of truth for all scenario and folder data management
 * Combines data fetching, caching, and CRUD operations in one optimized hook
 */

import useSWR from 'swr'
import { useCallback, useState, useMemo } from 'react'
// toast removed - not used in this data fetching hook
import { scenarioApi } from '@/lib/api'
import { useScenarioStore } from '@/store/scenarioStore'
import { CacheManager } from '@/lib/utils/cache-utils'
import type { Scenario, FolderType } from '@/types/scenario'
import type { ScenarioWithMeta } from '@/types/scenario-extended'
import { scenarioToScenarioWithMeta, isScenarioUncategorized } from '@/lib/utils/scenario-utils'

// Combined response type for scenarios and folders
interface UnifiedDataResponse {
  scenarios: ScenarioWithMeta[]
  folders: FolderType[]
}

/**
 * Unified fetcher function that gets both scenarios and folders in one call
 */
const fetchUnifiedData = async (key: string): Promise<UnifiedDataResponse> => {
  // Parse the key to get the parameters
  const params = JSON.parse(key)
  
  const requestId = Math.random().toString(36).substring(2, 8)
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${requestId}] Unified fetch with params:`, params)
  }

  try {
    // Fetch scenarios and folders in parallel for better performance
    const [scenariosResponse, foldersResponse] = await Promise.all([
      scenarioApi.getScenariosWithFolders(params),
      scenarioApi.getFolders({ limit: 50, skip: 0 })
    ])

    if (!scenariosResponse.success) {
      throw new Error(scenariosResponse.error || 'Failed to fetch scenarios')
    }

    if (!foldersResponse.success) {
      throw new Error(foldersResponse.error || 'Failed to fetch folders')
    }

    // Extract and transform data
    const scenariosData = scenariosResponse.data || { scenarios: [] }
    const foldersData = foldersResponse.folders || foldersResponse.data?.folders || []

    const scenarios = (scenariosData.scenarios || []).map((scenario: Scenario) =>
      scenarioToScenarioWithMeta(scenario)
    )

    if (process.env.NODE_ENV === 'development') {
      console.log(`[${requestId}] Unified fetch completed. Found ${scenarios.length} scenarios and ${foldersData.length} folders.`)
    }

    return {
      scenarios,
      folders: foldersData
    }
  } catch (error) {
    console.error(`[${requestId}] Exception in fetchUnifiedData:`, error)
    throw error
  }
}

/**
 * Options for the unified hook
 */
interface UseUnifiedScenarioDataOptions {
  autoFetch?: boolean
}

/**
 * Unified hook for all scenario and folder data management
 * Provides both data fetching and CRUD operations in a single, optimized hook
 */
export function useSWRScenarios(options: UseUnifiedScenarioDataOptions = {}) {
  const { autoFetch = true } = options

  // Get filters from Zustand store
  const {
    searchQuery,
    statusFilter,
    tagFilter,
    testTypeFilter,
    startDate,
    endDate,
    testManagementStatus,
    userInfo
  } = useScenarioStore()

  // Loading states for CRUD operations
  const [loadingOperations, setLoadingOperations] = useState<Set<string>>(new Set())

  // Create SWR key based on filters
  const params: Record<string, string | number | undefined> = {
    search: searchQuery || undefined,
    status: statusFilter || undefined,
    tag: tagFilter || undefined,
    testType: testTypeFilter || undefined,
    startDate: startDate || undefined,
    endDate: endDate || undefined,
    testManagementStatus: testManagementStatus || undefined,
    limit: 100,
    skip: 0
  }

  // Remove undefined values
  Object.keys(params).forEach(key => {
    if (params[key] === undefined) {
      delete params[key]
    }
  })

  // Convert params to string key for SWR
  const key = JSON.stringify(params)
  const swrKey = autoFetch ? key : null

  // SWR data fetching
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate
  } = useSWR(swrKey, fetchUnifiedData, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
    errorRetryCount: 3,
    onError: (err) => {
      console.error("SWR error fetching unified data:", err)
    },
    shouldRetryOnError: true,
    focusThrottleInterval: 5000,
    keepPreviousData: true,
    revalidateIfStale: true,
    revalidateOnMount: true,
    refreshInterval: 0,
    compare: (a, b) => {
      if (!a && !b) return true
      if (!a || !b) return false
      
      const aScenarioCount = a.scenarios?.length || 0
      const bScenarioCount = b.scenarios?.length || 0
      const aFolderCount = a.folders?.length || 0
      const bFolderCount = b.folders?.length || 0
      
      return aScenarioCount === bScenarioCount && aFolderCount === bFolderCount
    }
  })

  // Helper functions for operation loading states
  const isOperationInProgress = useCallback((operation: string) => {
    return loadingOperations.has(operation)
  }, [loadingOperations])

  const setOperationLoading = useCallback((operation: string, loading: boolean) => {
    setLoadingOperations(prev => {
      const newSet = new Set(prev)
      if (loading) {
        newSet.add(operation)
      } else {
        newSet.delete(operation)
      }
      return newSet
    })
  }, [])

  // Global cache invalidation helper
  const invalidateCache = useCallback(async () => {
    await CacheManager.invalidateScenarioData()
  }, [])

  // Refresh function
  const refresh = useCallback(async () => {
    if (!autoFetch) {
      return mutate(async () => {
        return await fetchUnifiedData(key)
      })
    }
    return mutate()
  }, [mutate, autoFetch, key])

  // Optimistic update for scenario deletion
  const optimisticDeleteScenario = useCallback(async (scenarioId: string) => {
    if (!data) return

    // Optimistically update the cache
    await mutate(
      {
        ...data,
        scenarios: data.scenarios.filter(scenario => scenario.id !== scenarioId)
      },
      false // Don't revalidate immediately
    )
  }, [data, mutate])

  // Optimistic update for bulk scenario deletion
  const optimisticBulkDeleteScenarios = useCallback(async (scenarioIds: string[]) => {
    if (!data) return

    // Optimistically update the cache
    await mutate(
      {
        ...data,
        scenarios: data.scenarios.filter(scenario => !scenarioIds.includes(scenario.id))
      },
      false // Don't revalidate immediately
    )
  }, [data, mutate])

  // Extract data with defaults
  const scenarios = data?.scenarios || []
  const folders = data?.folders || []

  // Helper function to normalize folder ID
  const normalizeFolderId = useCallback((folderId: string | null | undefined): string => {
    if (isScenarioUncategorized(folderId)) {
      return 'uncategorized'
    }
    return folderId || 'uncategorized'
  }, [])

  // Memoized computed values
  const uncategorizedScenarios = useMemo(() => {
    return scenarios.filter(scenario => {
      const normalizedId = normalizeFolderId(scenario.folderId)
      return normalizedId === 'uncategorized'
    })
  }, [scenarios, normalizeFolderId])

  const scenariosByFolder = useMemo(() => {
    const grouped: Record<string, ScenarioWithMeta[]> = {}
    scenarios.forEach(scenario => {
      const folderId = normalizeFolderId(scenario.folderId)
      if (!grouped[folderId]) {
        grouped[folderId] = []
      }
      grouped[folderId].push(scenario)
    })
    return grouped
  }, [scenarios, normalizeFolderId])

  return {
    // Data
    scenarios,
    folders,
    uncategorizedScenarios,
    scenariosByFolder,

    // Loading states
    isLoading,
    isValidating,
    error,

    // Counts
    scenariosTotal: scenarios.length,
    foldersTotal: folders.length,

    // Operations state
    isOperationInProgress,

    // Actions
    refresh,
    invalidateCache,
    optimisticDeleteScenario,
    optimisticBulkDeleteScenarios,

    // Internal utilities (for CRUD operations)
    mutate,
    setOperationLoading,
    userInfo
  }
}
