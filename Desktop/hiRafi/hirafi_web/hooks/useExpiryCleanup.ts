/**
 * Expiry Cleanup Hook
 * Automatically cleans up expired data when the app initializes
 */

import { useEffect } from 'react';
import { CompanyExpiryStorage } from '@/lib/utils/expiry-storage';

export function useExpiryCleanup() {
  useEffect(() => {
    // Run cleanup on app initialization
    const cleanupExpiredData = () => {
      try {
        console.log('[ExpiryCleanup] Starting automatic cleanup of expired data...');
        
        // Clean up expired company-specific data
        const cleanedCount = CompanyExpiryStorage.cleanupCompanyExpiredItems();
        
        if (cleanedCount > 0) {
          console.log(`[ExpiryCleanup] Successfully cleaned up ${cleanedCount} expired items`);
        } else {
          console.log('[ExpiryCleanup] No expired items found');
        }
      } catch (error) {
        console.warn('[ExpiryCleanup] Error during cleanup:', error);
      }
    };

    // Run immediately
    cleanupExpiredData();

    // Set up periodic cleanup every 10 minutes to catch items that expire during the session
    const intervalId = setInterval(cleanupExpiredData, 10 * 60 * 1000); // 10 minutes

    // Cleanup interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, []); // Run only once on mount
}

/**
 * Hook specifically for scenario creation pages
 * Shows user notifications about auto-expiry
 */
export function useScenarioExpiryCleanup(options?: {
  showNotifications?: boolean;
  customExpiryTime?: number;
}) {
  const { showNotifications = false, customExpiryTime } = options || {};

  useExpiryCleanup(); // Run general cleanup

  useEffect(() => {
    if (showNotifications) {
      // Show user notification about auto-expiry
      const expiryTime = customExpiryTime || 60; // minutes
      console.log(`[ScenarioExpiry] Scenario creation data will auto-expire after ${expiryTime} minutes of inactivity`);
      
      // You could show a toast notification here if desired
      // toast.info(`Your scenario draft will be saved for ${expiryTime} minutes`, { duration: 5000 });
    }
  }, [showNotifications, customExpiryTime]);

  // Return cleanup function for manual cleanup
  return {
    manualCleanup: () => {
      const cleanedCount = CompanyExpiryStorage.cleanupCompanyExpiredItems();
      console.log(`[ManualCleanup] Cleaned up ${cleanedCount} expired items`);
      return cleanedCount;
    },
    
    getRemainingTime: (storeName: string) => {
      // This would need to be implemented if we want to show remaining time
      // For now, return null
      return null;
    }
  };
} 