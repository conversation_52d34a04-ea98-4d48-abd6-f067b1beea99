import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON>rkles,
  Loader2,
  Zap,
  Wifi,
  Accessibility,
  AlertCircle,
  CheckCircle2,
  AlertTriangle,
  Info,
} from 'lucide-react';
import { useReport } from '@/hooks/useReport';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ReportAInalysisProps {
  hookResult: ReturnType<typeof useReport>;
}

function AInalysisScoreCard({ title, score, comment, icon: Icon, iconColor }: {
  title: string;
  score: string | number;
  comment: string;
  icon: React.ElementType;
  iconColor: string;
}) {
  const getBadgeClass = (s: string | number) => {
    const numericScore = Number(s);
    if (isNaN(numericScore)) return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"; // Handle non-numeric score
    if (numericScore < 50) return "bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300";
    if (numericScore <= 70) return "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300";
    return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300";
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Icon className={`h-5 w-5 ${iconColor}`} />
          {title}
        </h3>
        <Badge className={getBadgeClass(score)}>
          Score: {score}/100
        </Badge>
      </div>
      <Card>
        <CardContent className="p-4">
          <p className="whitespace-pre-line">{comment}</p>
        </CardContent>
      </Card>
    </div>
  );
}

function OverallScoreCard({ analysis }: { analysis: NonNullable<ReturnType<typeof useReport>['aiAnalysis']> }) {
  const score = analysis.OverallOptimizationScore;
  const getScoreColor = (s: string | number) => {
    const numericScore = Number(s);
    if (isNaN(numericScore)) return 'text-gray-600 dark:text-gray-400';
    if (numericScore < 50) return 'text-rose-600 dark:text-rose-400';
    if (numericScore <= 70) return 'text-amber-600 dark:text-amber-400';
    return 'text-emerald-600 dark:text-emerald-400';
  };
  const getGradientClass = (s: string | number) => {
    const numericScore = Number(s);
    if (isNaN(numericScore)) return "bg-gray-500";
    if (numericScore < 50) return "h-full bg-gradient-to-r from-rose-500 to-pink-500";
    if (numericScore <= 70) return "h-full bg-gradient-to-r from-amber-500 to-yellow-500";
    return "h-full bg-gradient-to-r from-emerald-500 to-green-500";
  };
  const getBgGradientClass = (s: string | number) => {
    const numericScore = Number(s);
    if (isNaN(numericScore)) return "bg-gray-50 dark:bg-gray-950/50 border-gray-100 dark:border-gray-900";
    if (numericScore < 50) return "bg-gradient-to-r from-rose-50 to-pink-50 dark:from-rose-950/50 dark:to-pink-950/50 border-rose-100 dark:border-rose-900";
    if (numericScore <= 70) return "bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-950/50 dark:to-yellow-950/50 border-amber-100 dark:border-amber-900";
    return "bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-950/50 dark:to-green-950/50 border-emerald-100 dark:border-emerald-900";
  };

  return (
    <div className={`rounded-lg p-6 border ${getBgGradientClass(score)}`}>
      <div className="flex flex-col md:flex-row items-center justify-between gap-4">
        <div>
          <h2 className="text-xl font-bold">Overall Health Score</h2>
          <p className="text-gray-600 dark:text-gray-400">Based on test results and performance metrics</p>
        </div>
        <div className="flex items-center gap-2">
          <div className={`text-5xl font-bold ${getScoreColor(score)}`}>{score}</div>
          <div className="text-xl font-medium text-gray-500 dark:text-gray-400">/100</div>
        </div>
      </div>
      <div className="mt-4 grid grid-cols-4 gap-2">
        <div className="col-span-4 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div className={getGradientClass(score)} style={{ width: `${Number.isNaN(Number(score)) ? 0 : Number(score)}%` }}></div>
        </div>
        <div className="text-center">
          <div className="text-sm font-medium">Page Metrics</div>
          <div className={`text-lg font-bold ${getScoreColor(analysis.PageMetrics.Score)}`}>
            {analysis.PageMetrics.Score}/100
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm font-medium">Network Stats</div>
          <div className={`text-lg font-bold ${getScoreColor(analysis.NetworkStatistics.Score)}`}>
            {analysis.NetworkStatistics.Score}/100
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm font-medium">Accessibility</div>
          <div className={`text-lg font-bold ${getScoreColor(analysis.TotalViolations.Score)}`}>
            {analysis.TotalViolations.Score}/100
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm font-medium">Network Logs</div>
          <div className={`text-lg font-bold ${getScoreColor(analysis.NetworkLogs.Score)}`}>
            {analysis.NetworkLogs.Score}/100
          </div>
        </div>
      </div>
    </div>
  );
}

export function ReportAInalysis({ hookResult }: ReportAInalysisProps) {
  const {
    report,
    aiAnalysis,
    isAnalyzing,
    handleRunAiAnalysis,
  } = hookResult;

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [clientSideAnalysis, setClientSideAnalysis] = useState<typeof aiAnalysis | null>(null);

  // Use useEffect to set the client-side state after hydration
  useEffect(() => {
    setClientSideAnalysis(aiAnalysis);
  }, [aiAnalysis]);

  // Check if metrics data is limited or missing
  const hasLimitedMetrics = () => {
    if (!report) return false;

    // Check if any of these metrics are missing or limited
    const missingMetrics = [];

    // Get metrics data from either enhancedMetrics or metrics field
    const metricsData = report.enhancedMetrics || report.metrics;

    // Check page metrics
    if (!metricsData?.pageMetrics ||
        Object.keys(metricsData.pageMetrics).length === 0) {
      missingMetrics.push('Page Metrics');
    }

    // Check network statistics
    if (!metricsData?.networkData?.requests ||
        Object.keys(metricsData.networkData.requests).length === 0) {
      missingMetrics.push('Network Statistics');
    }

    // Check accessibility data
    if (!metricsData?.accessibilityData?.violations ||
        !metricsData.accessibilityData.violations.items ||
        metricsData.accessibilityData.violations.items.length === 0) {
      // If there are no violations, this might be intentional (good accessibility)
      // So we don't add a warning here
    }

    // Check network logs
    if (!metricsData?.networkData?.logs ||
        (Array.isArray(metricsData.networkData.logs) && metricsData.networkData.logs.length === 0)) {
      missingMetrics.push('Network Logs');
    }

    // Return the missing metrics or false if none are missing
    return missingMetrics.length > 0 ? missingMetrics : false;
  };

  const handleAnalysisButtonClick = () => {
    setConfirmDialogOpen(true);
  };

  const handleConfirmAnalysis = () => {
    setConfirmDialogOpen(false);
    handleRunAiAnalysis();
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-indigo-500" />
            <CardTitle className="text-lg">AI Analysis</CardTitle>
          </div>
          <Button
            onClick={handleAnalysisButtonClick}
            disabled={isAnalyzing}
            className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
          >
            {isAnalyzing ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Analyzing...</>
            ) : report?.aiInsight ? (
              <><Sparkles className="mr-2 h-4 w-4" /> Re Analyze</>
            ) : (
              <><Sparkles className="mr-2 h-4 w-4" /> Run AI Analysis</>
            )}
          </Button>

          {/* Confirmation Dialog */}
          <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {hasLimitedMetrics() ? (
                    <>
                      <AlertTriangle className="h-5 w-5 text-amber-500" />
                      Limited Data Warning
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-5 w-5 text-indigo-500" />
                      Confirm AI Analysis
                    </>
                  )}
                </DialogTitle>
                <DialogDescription>
                  {!hasLimitedMetrics() &&
                    "AI will analyze your test results and provide insights about performance, accessibility, and potential issues."
                  }
                </DialogDescription>

                {hasLimitedMetrics() && (
                  <div className="mt-2 space-y-2">
                    <div className="text-amber-600 dark:text-amber-400 font-medium text-sm">
                      The following data is limited or missing:
                    </div>
                    <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                      {(hasLimitedMetrics() as string[]).map((metric, index) => (
                        <li key={index}>{metric}</li>
                      ))}
                    </ul>
                    <div className="pt-2 text-sm text-gray-600 dark:text-gray-400">
                      AI analysis may be less accurate with limited data. Do you want to continue?
                    </div>
                  </div>
                )}
              </DialogHeader>
              <DialogFooter className="flex gap-2 sm:justify-end">
                <Button
                  variant="outline"
                  onClick={() => setConfirmDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleConfirmAnalysis}
                  className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                >
                  <Sparkles className="mr-2 h-4 w-4" />
                  {hasLimitedMetrics() ? "Analyze Anyway" : "Run Analysis"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {clientSideAnalysis ? (
          <div className="space-y-6">
            <OverallScoreCard analysis={clientSideAnalysis} />
            <AInalysisScoreCard
              title="Page Metrics"
              score={clientSideAnalysis.PageMetrics.Score}
              comment={clientSideAnalysis.PageMetrics.Comment}
              icon={Zap}
              iconColor="text-amber-500"
            />
            <AInalysisScoreCard
              title="Network Statistics"
              score={clientSideAnalysis.NetworkStatistics.Score}
              comment={clientSideAnalysis.NetworkStatistics.Comment}
              icon={Wifi}
              iconColor="text-rose-500"
            />
            <AInalysisScoreCard
              title="Accessibility Analysis"
              score={clientSideAnalysis.TotalViolations.Score}
              comment={clientSideAnalysis.TotalViolations.Comment}
              icon={Accessibility}
              iconColor="text-emerald-500"
            />
            <AInalysisScoreCard
              title="Network Logs Analysis"
              score={clientSideAnalysis.NetworkLogs.Score}
              comment={clientSideAnalysis.NetworkLogs.Comment}
              icon={AlertCircle}
              iconColor="text-blue-500"
            />
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  Overall Recommendations
                </h3>
              </div>
              <Card>
                <CardContent className="p-4">
                  <p className="whitespace-pre-line">{clientSideAnalysis.OverallComment}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <Sparkles className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {isAnalyzing
                ? "AI is analyzing your test results..."
                : "Click the 'Run AI Analysis' button to get insights about your test results"}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}