/**
 * Redis Connection Manager
 * Refactored to use dependency injection instead of singleton pattern
 * Provides centralized Redis connection management
 */

import { createClient, RedisClientType } from 'redis';
import { logger } from '../../../../utils/logger.js';
import { config } from '../../../../config/index.js';
import { IRedisConnectionManager } from '../../interfaces/redis-connector.interface.js';

export class RedisConnectionManager implements IRedisConnectionManager {
  private client: RedisClientType | null = null;
  private connectionStatus: 'connected' | 'disconnected' | 'connecting' | 'error' = 'disconnected';

  constructor() {
    logger.debug('RedisConnectionManager: Instance created with dependency injection');
  }

  /**
   * Get the Redis client instance
   */
  public getClient(): RedisClientType | null {
    return this.client;
  }

  /**
   * Check if Redis is connected
   */
  public isConnected(): boolean {
    return this.connectionStatus === 'connected' && this.client !== null;
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): 'connected' | 'disconnected' | 'connecting' | 'error' {
    return this.connectionStatus;
  }

  /**
   * Connect to Redis
   */
  public async connect(): Promise<boolean> {
    if (this.isConnected()) {
      logger.debug('RedisConnectionManager: Already connected to Redis');
      return true;
    }

    this.connectionStatus = 'connecting';
    logger.info('RedisConnectionManager: Connecting to Redis...');

    try {
      // Create Redis client based on configuration
      // BullMQ requires maxRetriesPerRequest to be null
      if (config.redisUrl) {
        this.client = createClient({
          url: config.redisUrl,
          socket: {
            reconnectStrategy: (retries) => {
              const delay = Math.min(retries * 500, 10000);
              return delay;
            }
          }
        });
      } else {
        this.client = createClient({
          socket: {
            host: config.redis?.host || 'localhost',
            port: config.redis?.port || 6379,
            reconnectStrategy: (retries) => {
              const delay = Math.min(retries * 500, 10000);
              return delay;
            }
          },
          password: config.redis?.password || undefined,
          ...(process.env.REDIS_USERNAME ? { username: process.env.REDIS_USERNAME } : {}),
          database: config.redis?.db || 0
        });
      }

      // Set up event handlers
      this.client.on('error', (error) => {
        logger.error(`RedisConnectionManager: Redis error: ${error.message}`);
        this.connectionStatus = 'error';
      });

      this.client.on('connect', () => {
        this.connectionStatus = 'connected';
      });

      this.client.on('disconnect', () => {
        logger.warn('RedisConnectionManager: Disconnected from Redis');
        this.connectionStatus = 'disconnected';
      });

      // Connect to Redis
      await this.client.connect();
      this.connectionStatus = 'connected';

      return true;

    } catch (error: any) {
      logger.error(`RedisConnectionManager: Failed to connect to Redis: ${error.message}`);
      this.connectionStatus = 'error';
      this.client = null;
      return false;
    }
  }

  /**
   * Disconnect from Redis
   */
  public async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.disconnect();
        logger.info('RedisConnectionManager: Disconnected from Redis');
      } catch (error: any) {
        logger.error(`RedisConnectionManager: Error disconnecting from Redis: ${error.message}`);
      } finally {
        this.client = null;
        this.connectionStatus = 'disconnected';
      }
    }
  }
}
