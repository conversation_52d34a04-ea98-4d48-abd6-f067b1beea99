/**
 * Pause Routes
 * API endpoints for pausing and resuming test execution
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { pauseService } from '../../services/redis/pauseService.js';
import { authenticateAdmin } from '../middleware/adminAuthMiddleware.js';
import { nodeManager } from '../../core/node-manager/index.js';

const router = Router();

/**
 * @route   POST /api/admin/system/pause
 * @desc    Pause the entire system
 * @access  Admin
 */
router.post('/system/pause', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    const success = await pauseService.pauseSystem();

    if (success) {
      logger.info('API: System paused by admin');
      return res.json({
        success: true,
        message: 'System paused successfully'
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Failed to pause system'
      });
    }
  } catch (error: any) {
    logger.error(`API: Error pausing system: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/system/resume
 * @desc    Resume the entire system
 * @access  Admin
 */
router.post('/system/resume', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    const success = await pauseService.resumeSystem();

    if (success) {
      logger.info('API: System resumed by admin');
      return res.json({
        success: true,
        message: 'System resumed successfully'
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Failed to resume system'
      });
    }
  } catch (error: any) {
    logger.error(`API: Error resuming system: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/system/pause-status
 * @desc    Get system pause status
 * @access  Admin
 */
router.get('/system/pause-status', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    const isPaused = await pauseService.isSystemPaused();

    return res.json({
      success: true,
      isPaused
    });
  } catch (error: any) {
    logger.error(`API: Error getting system pause status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/nodes/:nodeId/pause
 * @desc    Pause a specific node
 * @access  Admin
 */
router.post('/nodes/:nodeId/pause', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { nodeId } = req.params;

    // Check if node exists
    const node = nodeManager.getNode(nodeId);
    if (!node) {
      return res.status(404).json({
        success: false,
        error: `Node ${nodeId} not found`
      });
    }

    const success = await pauseService.pauseNode(nodeId);

    if (success) {
      logger.info(`API: Node ${nodeId} paused by admin`);
      return res.json({
        success: true,
        message: `Node ${nodeId} paused successfully`
      });
    } else {
      return res.status(500).json({
        success: false,
        error: `Failed to pause node ${nodeId}`
      });
    }
  } catch (error: any) {
    logger.error(`API: Error pausing node: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/admin/nodes/:nodeId/resume
 * @desc    Resume a specific node
 * @access  Admin
 */
router.post('/nodes/:nodeId/resume', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { nodeId } = req.params;

    // Check if node exists
    const node = nodeManager.getNode(nodeId);
    if (!node) {
      return res.status(404).json({
        success: false,
        error: `Node ${nodeId} not found`
      });
    }

    const success = await pauseService.resumeNode(nodeId);

    if (success) {
      logger.info(`API: Node ${nodeId} resumed by admin`);
      return res.json({
        success: true,
        message: `Node ${nodeId} resumed successfully`
      });
    } else {
      return res.status(500).json({
        success: false,
        error: `Failed to resume node ${nodeId}`
      });
    }
  } catch (error: any) {
    logger.error(`API: Error resuming node: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/nodes/:nodeId/pause-status
 * @desc    Get node pause status
 * @access  Admin
 */
router.get('/nodes/:nodeId/pause-status', authenticateAdmin, async (req: Request, res: Response) => {
  try {
    const { nodeId } = req.params;

    // Check if node exists
    const node = nodeManager.getNode(nodeId);
    if (!node) {
      return res.status(404).json({
        success: false,
        error: `Node ${nodeId} not found`
      });
    }

    const isPaused = await pauseService.isNodePaused(nodeId);

    return res.json({
      success: true,
      nodeId,
      isPaused
    });
  } catch (error: any) {
    logger.error(`API: Error getting node pause status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/admin/nodes/pause-status
 * @desc    Get pause status for all nodes
 * @access  Admin
 */
router.get('/nodes/pause-status', authenticateAdmin, async (_req: Request, res: Response) => {
  try {
    const nodes = nodeManager.getAllNodes();
    const nodeIds = nodes.map(node => node.id);

    const pauseStatus = await pauseService.getNodesPauseStatus(nodeIds);

    return res.json({
      success: true,
      nodes: nodes.map(node => ({
        id: node.id,
        name: node.name,
        status: node.status,
        isPaused: pauseStatus[node.id] || false
      }))
    });
  } catch (error: any) {
    logger.error(`API: Error getting nodes pause status: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
