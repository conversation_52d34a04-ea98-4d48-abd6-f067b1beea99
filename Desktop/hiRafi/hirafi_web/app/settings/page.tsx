"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Sidebar } from "@/components/sidebar/sidebar"
import { ProfileSettings } from "@/components/settings/profile-settings"
import { AIModelSettings } from "@/components/settings/ai-model-settings"
import { SecuritySettings } from "@/components/settings/security-settings"
import { NotificationSettings } from "@/components/settings/notification-settings"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/lib/api/auth"
import { Loader } from "lucide-react"
import { ProtectedRoute } from "@/components/protected-route"
import { userApi } from "@/lib/api"

// Define the AIModelSettings interface to match the component
interface AIModelSettingsType {
  preferredModel: string;
}

function SettingsContent() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const { user } = useAuth()

  // User data
  const [userData, setUserData] = useState({
    name: "",
    email: "",
    jobTitle: "",
    company: "",
    phone: "",
    avatar: "/placeholder.svg?height=200&width=200",
  })

  // AI model settings
  const [aiSettings, setAISettings] = useState<AIModelSettingsType>({
    preferredModel: "gpt-4o"
  })

  // Profil verilerini çek
  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    setIsLoading(true);
    try {
      const response = await userApi.getUserProfile();

      if (response.success && response.data?.profile) {
        setUserData({
          name: response.data.profile.name || "",
          email: response.data.profile.email || "",
          jobTitle: response.data.profile.jobTitle || "",
          company: response.data.profile.company || "",
          phone: response.data.profile.phone || "",
          avatar: response.data.profile.avatar || "/placeholder.svg?height=200&width=200",
        });
      } else {
        toast({
          title: "Hata",
          description: response.error || "Profil verisi alınamadı",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Profil verisi alınamadı",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateProfile = async (newData: Partial<typeof userData>) => {
    try {
      // Yeni API yapısını kullan
      const response = await userApi.updateUserProfile(newData);

      if (response.success) {
        // Başarılı olursa state'i güncelle
        setUserData({ ...userData, ...newData });

        toast({
          title: "Profil güncellendi",
          description: "Profil bilgileriniz başarıyla güncellendi.",
        });
      } else {
        toast({
          title: "Hata",
          description: response.error || "Profil güncellenirken bir hata oluştu.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Profil güncellenirken bir hata oluştu.",
        variant: "destructive"
      });
    }
  }

  const handleUpdateAISettings = (newSettings: Partial<AIModelSettingsType>) => {
    setAISettings({ ...aiSettings, ...newSettings })
    toast({
      title: "AI settings updated",
      description: "Your AI model settings have been updated successfully.",
    })
  }

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-white dark:bg-gray-950">
        <Loader className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Navigation Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage your account and application preferences</p>
        </header>

        <main className="flex-1 overflow-auto p-6 bg-gray-50 dark:bg-gray-950">
          <div className="max-w-4xl mx-auto">
            <Tabs defaultValue="profile" className="w-full">
              <TabsList className={`grid ${user?.accountType === 'company_owner' ? 'grid-cols-4' : 'grid-cols-3'} mb-8`}>
                <TabsTrigger value="profile">Profile</TabsTrigger>
                {user?.accountType === 'company_owner' && (
                  <TabsTrigger value="ai-models">AI Models</TabsTrigger>
                )}
                <TabsTrigger value="security">Security</TabsTrigger>
                <TabsTrigger value="notifications">Notifications</TabsTrigger>
              </TabsList>

              <TabsContent value="profile" className="space-y-4">
                <ProfileSettings userData={userData} onUpdate={handleUpdateProfile} />
              </TabsContent>

              {user?.accountType === 'company_owner' && (
                <TabsContent value="ai-models" className="space-y-4">
                  <AIModelSettings
                    aiSettings={aiSettings}
                    onUpdateSettings={handleUpdateAISettings}
                  />
                </TabsContent>
              )}

              <TabsContent value="security" className="space-y-4">
                <SecuritySettings />
              </TabsContent>

              <TabsContent value="notifications" className="space-y-4">
                <NotificationSettings />
              </TabsContent>


            </Tabs>
          </div>
        </main>
      </div>
    </div>
  )
}

export default function SettingsPage() {
  return (
    <ProtectedRoute>
      <SettingsContent />
    </ProtectedRoute>
  )
}