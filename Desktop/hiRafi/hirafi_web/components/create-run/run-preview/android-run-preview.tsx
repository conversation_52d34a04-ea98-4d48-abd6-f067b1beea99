"use client"

import { useRunForm } from "../run-context"
import {
  Smartphone,
  Tablet,
  Camera,
  Video,
  Bot,
  ListOrdered,
  Share2,
  Database
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import { AndroidEnvironmentSettings } from "@/types/android-environment"

export function AndroidRunPreview() {
  const {
    formData,
    environmentSettings,
    reportSettings,
    selectedTestDataEnvironment
  } = useRunForm()

  // Type guard to check if environmentSettings is AndroidEnvironmentSettings
  const isAndroidSettings = (settings: any): settings is AndroidEnvironmentSettings => {
    return settings && settings.platform === 'android';
  };

  // Cast to AndroidEnvironmentSettings if it's an Android environment
  const androidSettings = isAndroidSettings(environmentSettings)
    ? environmentSettings
    : {
        platform: 'android',
        sauceLabs: { username: '', accessKey: '', selectedDevices: [] },
        aiModelName: '',
        testDistribution: { strategy: 'all-on-all' }
      } as AndroidEnvironmentSettings;

  return (
    <div className="space-y-4">
      {/* Android-specific settings */}
      {/* Device Section */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.2,
          type: "spring",
          stiffness: 450,
          damping: 20
        }}
        className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 p-3 rounded-md border border-green-100 dark:border-green-900/50 hover:shadow-md transition-all"
        whileHover={{
          y: -2,
          transition: { duration: 0.2 }
        }}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <div className="bg-green-100 dark:bg-green-900/50 p-1.5 rounded-full mr-2">
              <Smartphone className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <p className="text-sm font-medium text-green-700 dark:text-green-300">
              {androidSettings.sauceLabs?.selectedDevices && androidSettings.sauceLabs.selectedDevices.length > 0
                ? `Selected Devices (${androidSettings.sauceLabs.selectedDevices.length})`
                : 'Device'}
            </p>
          </div>
          <div className="flex items-center">
            {androidSettings.sauceLabs && (
              <Badge className="bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300 border-green-200 dark:border-green-800 text-xs">
                SauceLabs
              </Badge>
            )}
          </div>
        </div>

        {/* If we have multiple devices, show them in a grid */}
        {androidSettings.sauceLabs?.selectedDevices && androidSettings.sauceLabs.selectedDevices.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-2">
            {androidSettings.sauceLabs.selectedDevices.map((device: any) => (
              <div
                key={`${device.provider}-${device.id}`}
                className="bg-white dark:bg-gray-800 rounded-md p-2 border border-green-200 dark:border-green-800 flex items-center gap-2"
              >
                <div className={`p-1 rounded-full ${device.deviceType === 'tablet' ? 'bg-purple-100 dark:bg-purple-900/50' : 'bg-green-100 dark:bg-green-900/50'}`}>
                  {device.deviceType === 'tablet' ? (
                    <Tablet className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />
                  ) : (
                    <Smartphone className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs font-medium truncate">{device.name}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Android {device.osVersion}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-2">
            No device selected
          </p>
        )}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.3,
          type: "spring",
          stiffness: 450,
          damping: 20
        }}
        className="flex justify-between items-center bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 p-1.5 rounded-md border border-green-100 dark:border-green-900/50 hover:shadow-md hover:scale-[1.02] transition-all"
        whileHover={{
          y: -2,
          transition: { duration: 0.2 }
        }}
      >
        <div className="flex items-center p-2">
          <Smartphone className="h-5 w-5 mr-2 text-green-500" />
          <div>
            <p className="text-sm font-medium">App</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {androidSettings.sauceLabs?.selectedApp?.name || 'Not specified'}
              {androidSettings.sauceLabs?.selectedApp?.version && ` (v${androidSettings.sauceLabs.selectedApp.version})`}
            </p>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.4,
          type: "spring",
          stiffness: 450,
          damping: 20
        }}
        className="flex justify-between items-center bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-950/30 dark:to-teal-950/30 p-1.5 rounded-md border border-emerald-100 dark:border-emerald-900/50 hover:shadow-md hover:scale-[1.02] transition-all"
        whileHover={{
          y: -2,
          transition: { duration: 0.2 }
        }}
      >
        <div className="flex items-center p-2">
          <Bot className="h-5 w-5 mr-2 text-emerald-500" />
          <div>
            <p className="text-sm font-medium">AI Model</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {androidSettings.aiModelName || 'Default AI Model'}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Test Distribution Strategy */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          delay: 0.5,
          type: "spring",
          stiffness: 450,
          damping: 20
        }}
        className="flex justify-between items-center bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-950/30 dark:to-purple-950/30 p-1.5 rounded-md border border-indigo-100 dark:border-indigo-900/50 hover:shadow-md hover:scale-[1.02] transition-all"
        whileHover={{
          y: -2,
          transition: { duration: 0.2 }
        }}
      >
        <div className="flex items-center p-2">
          <Share2 className="h-5 w-5 mr-2 text-indigo-500" />
          <div>
            <p className="text-sm font-medium">Test Distribution</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {androidSettings.testDistribution?.strategy === 'distribute'
                ? 'Distribute Scenarios Across Devices'
                : 'Run All Scenarios on All Devices'}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Test Data Environment */}
      {selectedTestDataEnvironment && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: 0.6,
            type: "spring",
            stiffness: 450,
            damping: 20
          }}
          className="flex justify-between items-center bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 p-1.5 rounded-md border border-green-100 dark:border-green-900/50 hover:shadow-md hover:scale-[1.02] transition-all"
          whileHover={{
            y: -2,
            transition: { duration: 0.2 }
          }}
        >
          <div className="flex items-center p-2">
            <Database className="h-5 w-5 mr-2 text-green-500" />
            <div>
              <p className="text-sm font-medium">Test Data Environment</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {selectedTestDataEnvironment.name}
                {selectedTestDataEnvironment.description && (
                  <span className="ml-1">- {selectedTestDataEnvironment.description}</span>
                )}
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Report settings */}
      <div className="mt-6">
        <p className="text-sm font-medium mb-2 text-indigo-700 dark:text-indigo-300">Report Settings</p>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.1, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.takeScreenshots
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <Camera className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.takeScreenshots ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.takeScreenshots ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Screenshots
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.15, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${reportSettings.takeVideos
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <Video className={`h-3.5 w-3.5 mr-1.5 ${reportSettings.takeVideos ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${reportSettings.takeVideos ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Videos
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8, rotateX: 45 }}
            animate={{ opacity: 1, scale: 1, rotateX: 0 }}
            transition={{ delay: 0.2, type: "spring" }}
            whileHover={{ scale: 1.05, rotateX: 5, rotateY: 5, transition: { duration: 0.2 } }}
            className={`flex items-center p-1.5 rounded-md border ${formData.options?.sequentialRun
              ? "border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 shadow-sm"
              : "border-gray-200 dark:border-gray-800 opacity-60"}`}
          >
            <ListOrdered className={`h-3.5 w-3.5 mr-1.5 ${formData.options?.sequentialRun ? "text-blue-600" : "text-gray-400"}`} />
            <span className={`text-[10px] ${formData.options?.sequentialRun ? "text-blue-600 dark:text-blue-400" : "text-gray-500"}`}>
              Sequential
            </span>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
