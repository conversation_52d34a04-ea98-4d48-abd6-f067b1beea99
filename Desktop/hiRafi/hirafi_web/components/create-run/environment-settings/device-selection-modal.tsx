"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON><PERSON> } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON><PERSON>eader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

import { Card } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Sheet, SheetContent } from "@/components/ui/sheet"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  Smartphone,
  Search,
  X,
  Check,
  Tablet,
  Info,
  Layers,
  CheckCircle2,
  Plus,
  Server,
  Filter,
  ChevronDown
} from "lucide-react"
import { pluginApi } from "@/lib/api"
import { toast } from "@/lib/utils/toast-utils"
import { cn } from "@/lib/utils"
import { DeviceImage } from "./device-image"
// Custom hook for media queries
function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(false)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const media = window.matchMedia(query)

      // Initial check
      setMatches(media.matches)

      // Update matches when the media query changes
      const listener = (event: MediaQueryListEvent) => {
        setMatches(event.matches)
      }

      // Add event listener
      media.addEventListener("change", listener)

      // Clean up
      return () => {
        media.removeEventListener("change", listener)
      }
    }
    return undefined
  }, [query])

  return matches
}

// Import device provider types
import { DeviceProvider, DeviceProviderUtils } from '../../../types/device-provider';

// Device interface
interface Device {
  id: string;
  name: string;
  osVersion: string;
  provider: DeviceProvider;
  state?: string;
  is_available?: boolean;
  available?: boolean;
  deviceType?: 'phone' | 'tablet' | 'other';
  manufacturer?: string | string[];
  modelNumber?: string;
  serialNumber?: string;
  screenResolution?: string;
  screenSize?: string;
  ram?: string;
  cpu?: string;
  apiLevel?: string;
  releaseDate?: string;
  os?: string;
  resolution?: string;
  width?: number;
  height?: number;
  form?: string;
  memory?: string;
  api_level?: string;
  supportsAppiumWebAppTesting?: boolean;
  supportsAppiumNativeAppTesting?: boolean;
  supportsTestObject?: boolean;
  supportsXcuiTest?: boolean;
  supportsMockLocations?: boolean;
  [key: string]: any;
}

// Helper function to determine device type
function getDeviceType(device: Device): 'phone' | 'tablet' | 'other' {
  if (device.deviceType) return device.deviceType;

  // Try to determine from name or other properties
  const name = device.name?.toLowerCase() || '';
  if (name.includes('tablet') || name.includes('tab') || name.includes('pad')) {
    return 'tablet';
  } else if (name.includes('phone') ||
             name.includes('pixel') ||
             name.includes('galaxy') ||
             name.includes('iphone') ||
             name.includes('xiaomi') ||
             name.includes('oneplus')) {
    return 'phone';
  }

  return 'other';
}

interface DeviceSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (selectedDevices: Device[]) => void;
  initialSelectedDevices?: Device[];
  activeProvider?: DeviceProvider;
  onProviderChange?: (newProvider: DeviceProvider) => void;
  updateEnvironmentSettings?: (settings: any) => void;
}

export function DeviceSelectionModal({
  isOpen,
  onClose,
  onSave,
  initialSelectedDevices = [],
  activeProvider,
  onProviderChange,
  updateEnvironmentSettings
}: DeviceSelectionModalProps) {
  // Responsive breakpoints
  const isDesktop = useMediaQuery("(min-width: 1024px)");
  const isTablet = useMediaQuery("(min-width: 768px) and (max-width: 1023px)");
  const isMobile = useMediaQuery("(max-width: 767px)");

  // State for devices and selection
  const [devices, setDevices] = useState<Device[]>([]);
  const [selectedDevices, setSelectedDevices] = useState<Device[]>(initialSelectedDevices);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [provider, setProvider] = useState<DeviceProvider>(activeProvider || "sauceLabs");

  // Filter panel state
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);
  const [isSelectedDevicesPanelOpen, setIsSelectedDevicesPanelOpen] = useState(true);

  // Filtering state
  const [filters, setFilters] = useState({
    showOnlyAvailable: false,
    osVersions: [] as string[],
    manufacturers: [] as string[],
    deviceTypes: [] as string[]
  });

  // Selected filter values for dropdowns
  const [selectedOsVersion, setSelectedOsVersion] = useState<string>("_all");
  const [selectedManufacturer, setSelectedManufacturer] = useState<string>("_all");
  const [selectedDeviceType, setSelectedDeviceType] = useState<string>("_all");

  // Device loading state - simplified since we load all devices at once
  const [hasMoreDevices] = useState(false); // Always false since we load all devices
  const devicesPerPage = 30; // Kept for potential future use



  // Update provider when activeProvider prop changes
  useEffect(() => {
    if (activeProvider && activeProvider !== provider) {
      setProvider(activeProvider);
    }
  }, [activeProvider]);

  // Load devices when the modal opens or provider changes
  useEffect(() => {
    if (isOpen) {
      // Reset state when modal opens or provider changes
      setDevices([]);
      setSelectedDevices([]);
      loadAllDevicesAtOnce();
    }
  }, [isOpen, provider]);

  // Load all devices at once for better search experience
  const loadAllDevicesAtOnce = async () => {
    setIsLoading(true);
    try {
      if (provider === 'sauceLabs') {
        const response = await pluginApi.getSauceLabsDevices();
        const typedResponse = response as any;

        if (typedResponse.success && typedResponse.devices && Array.isArray(typedResponse.devices)) {
          const allDevicesWithProvider = typedResponse.devices.map((device: any) => ({
            ...device,
            provider: 'sauceLabs',
            deviceType: device.form === 'tablet' ? 'tablet' : (device.form === 'phone' ? 'phone' : undefined),
            screenResolution: device.resolution || device.screenResolution || (device.width && device.height ? `${device.width}x${device.height}` : undefined),
            screenSize: device.screenSize || (device.displaySize ? `${device.displaySize}"` : undefined),
            ram: device.ram || device.memory ? (device.ram || device.memory) + (isNaN(Number(device.ram || device.memory)) ? '' : ' GB') : undefined,
            modelNumber: device.modelNumber || device.id,
            osVersion: device.osVersion || (device.os === 'ANDROID' && device.version ? device.version : ''),
            apiLevel: device.apiLevel || device.api_level || (device.osVersion ? `API ${getApiLevelFromAndroidVersion(device.osVersion)}` : undefined),
            manufacturer: device.manufacturer || extractManufacturerFromName(device.name),
            serialNumber: device.serialNumber || device.serial || device.deviceSerial || device.id
          }));
          
          setDevices(allDevicesWithProvider);
        }
      } else if (provider === 'testinium') {
        const response = await pluginApi.getTestiniumDevices();
        const typedResponse = response as any;

        if (typedResponse.success && typedResponse.devices && Array.isArray(typedResponse.devices)) {
          const allDevicesWithProvider = typedResponse.devices.map((device: any) => {
            // Helper function to safely convert any value to string
            const safeString = (value: any, fallback: string = ''): string => {
              if (value == null) return fallback;
              if (typeof value === 'string') return value;
              if (Array.isArray(value)) return value.join(', ') || fallback;
              return String(value) || fallback;
            };

            // Safely extract device properties
            const deviceName = safeString(device.name || device.marketName, `Device ${device.id || 'Unknown'}`);
            const osVersion = safeString(device.osVersion || device.version || device.platformVersion, '');
            const deviceManufacturer = safeString(device.manufacturer) || extractManufacturerFromName(deviceName);

            return {
              ...device,
              name: deviceName,
              provider: 'testinium',
              serial: safeString(device.serial || device.deviceSerial || device.serialNumber), // Add serial field for DevicePark API
              deviceType: device.deviceType || getDeviceType({...device, name: deviceName}),
              screenResolution: device.resolution || device.screenResolution || (device.width && device.height ? `${device.width}x${device.height}` : undefined),
              screenSize: device.screenSize || (device.displaySize ? `${device.displaySize}"` : undefined),
              ram: device.ram || device.memory ? (device.ram || device.memory) + (isNaN(Number(device.ram || device.memory)) ? '' : ' GB') : undefined,
              modelNumber: safeString(device.modelNumber || device.id),
              osVersion: osVersion,
              apiLevel: device.apiLevel || device.api_level || (osVersion ? `API ${getApiLevelFromAndroidVersion(osVersion)}` : undefined),
              manufacturer: deviceManufacturer,
              serialNumber: safeString(device.serialNumber || device.serial || device.deviceSerial || device.id),
              available: device.available !== false
            };
          });
          
          setDevices(allDevicesWithProvider);
        }
      }
    } catch (error) {
      console.error(`Error loading ${provider} devices:`, error);
      toast.error(`Failed to load ${provider} devices`, {
        description: `Please check your ${provider} configuration and try again.`
      });
      setDevices([]);
    } finally {
      setIsLoading(false);
    }
  };

  // REMOVED: Old pagination functions that were causing duplicate devices

  // Helper function to extract manufacturer from device name with type safety
  const extractManufacturerFromName = (name: any): string => {
    // Handle null, undefined, or non-string values
    if (!name || typeof name !== 'string') return 'Unknown';

    const commonManufacturers = [
      'Samsung', 'Google', 'Xiaomi', 'Huawei', 'OnePlus', 'Motorola',
      'LG', 'Sony', 'Nokia', 'HTC', 'ASUS', 'Lenovo', 'OPPO', 'Vivo'
    ];

    for (const manufacturer of commonManufacturers) {
      if (name.includes(manufacturer)) {
        return manufacturer;
      }
    }

    // If no known manufacturer found, try to extract the first word
    const firstWord = name.split(' ')[0];
    return firstWord || 'Unknown';
  };

  // Helper function to get Android API level from version
  const getApiLevelFromAndroidVersion = (version: string): number => {
    const versionMap: Record<string, number> = {
      '4.0': 14, '4.1': 16, '4.2': 17, '4.3': 18, '4.4': 19,
      '5.0': 21, '5.1': 22, '6.0': 23, '7.0': 24, '7.1': 25,
      '8.0': 26, '8.1': 27, '9.0': 28, '10.0': 29, '11.0': 30,
      '12.0': 31, '13.0': 33, '14.0': 34
    };

    // Try to match the version to the map
    const majorVersion = version.split('.').slice(0, 2).join('.');
    return versionMap[majorVersion] || parseInt(version) || 0;
  };

  // Helper function to safely clean device name for display
  const safeCleanDeviceName = (name: any): string => {
    if (!name) return 'Unknown Device';
    const nameStr = typeof name === 'string' ? name : String(name);
    return nameStr.replace(/\([^)]*\)/g, '').trim() || 'Unknown Device';
  };

  // Handle device selection toggle with provider validation
  const toggleDeviceSelection = (device: Device) => {
    setSelectedDevices(prev => {
      const isSelected = prev.some(d => d.id === device.id && d.provider === device.provider);

      if (isSelected) {
        // Remove device if already selected
        return prev.filter(d => !(d.id === device.id && d.provider === device.provider));
      } else {
        // Validate that device is from the selected provider
        if (device.provider !== provider) {
          console.warn(`Device ${device.name} is from ${device.provider} but current provider is ${provider}`);
          return prev;
        }

        // Add device if not selected and from correct provider
        return [...prev, device];
      }
    });
  };

  // Check if a device is selected
  const isDeviceSelected = (device: Device) => {
    return selectedDevices.some(d => d.id === device.id && d.provider === device.provider);
  };

  // Function to check if a device is available
  const isDeviceAvailable = (device: Device): boolean => {
    // If state is explicitly set to AVAILABLE, device is available
    if (device.state === 'AVAILABLE') return true;

    // Check various availability flags
    if (device.is_available === true || device.available === true) return true;

    // If we have a state but it's not AVAILABLE, device is unavailable
    if (device.state && device.state !== 'AVAILABLE') return false;

    // If we don't have any availability information, default to unknown
    return false;
  };



  // Extract unique filter options from devices
  const filterOptions = useMemo(() => {
    const osVersions = new Set<string>();
    const manufacturers = new Set<string>();
    const deviceTypes = new Set<string>();

    devices.forEach(device => {
      if (device.osVersion) osVersions.add(device.osVersion);

      // Handle manufacturer which could be string or array
      if (device.manufacturer) {
        if (Array.isArray(device.manufacturer)) {
          // If it's an array, add each manufacturer
          device.manufacturer.forEach(mfr => {
            if (mfr) manufacturers.add(mfr);
          });
        } else {
          // If it's a string, add it directly
          manufacturers.add(device.manufacturer);
        }
      }

      const deviceType = device.deviceType || getDeviceType(device);
      deviceTypes.add(deviceType);
    });

    return {
      osVersions: Array.from(osVersions).sort((a, b) => {
        // Sort OS versions in descending order (newest first)
        const versionA = parseFloat(a) || 0;
        const versionB = parseFloat(b) || 0;
        return versionB - versionA;
      }),
      manufacturers: Array.from(manufacturers).sort(),
      deviceTypes: Array.from(deviceTypes).sort()
    };
  }, [devices]);

  // Filter devices based on search query and filters
  const filteredDevices = useMemo(() => {
    return devices.filter(device => {
      // Helper function to safely check if a value contains search term
      const safeIncludes = (value: any, searchTerm: string): boolean => {
        if (!value) return false;
        if (typeof value === 'string') return value.toLowerCase().includes(searchTerm);
        if (Array.isArray(value)) return value.some(v => v && String(v).toLowerCase().includes(searchTerm));
        return String(value).toLowerCase().includes(searchTerm);
      };

      // Search query filter
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = !searchQuery || (
        safeIncludes(device.name, searchLower) ||
        safeIncludes(device.osVersion, searchLower) ||
        safeIncludes(device.manufacturer, searchLower) ||
        safeIncludes(device.apiLevel, searchLower) ||
        safeIncludes(device.serialNumber, searchLower)
      );

      if (!matchesSearch) return false;

      // Availability filter
      if (filters.showOnlyAvailable && !isDeviceAvailable(device)) {
        return false;
      }

      // OS version filter
      if (filters.osVersions.length > 0 && !filters.osVersions.includes(device.osVersion)) {
        return false;
      }

      // Manufacturer filter
      if (filters.manufacturers.length > 0) {
        // Handle manufacturer which could be string or array
        if (Array.isArray(device.manufacturer)) {
          // If it's an array, check if any manufacturer matches
          if (!device.manufacturer.some(mfr => filters.manufacturers.includes(mfr))) {
            return false;
          }
        } else {
          // If it's a string, check direct match
          if (!filters.manufacturers.includes(device.manufacturer || '')) {
            return false;
          }
        }
      }

      // Device type filter
      const deviceType = device.deviceType || getDeviceType(device);
      if (filters.deviceTypes.length > 0 && !filters.deviceTypes.includes(deviceType)) {
        return false;
      }

      return true;
    });
  }, [devices, searchQuery, filters]);

  // Handle OS version filter change
  const handleOsVersionChange = (value: string) => {
    setSelectedOsVersion(value);
    setFilters(prev => ({
      ...prev,
      osVersions: value && value !== "_all" ? [value] : []
    }));
  };

  // Handle manufacturer filter change
  const handleManufacturerChange = (value: string) => {
    setSelectedManufacturer(value);
    setFilters(prev => ({
      ...prev,
      manufacturers: value && value !== "_all" ? [value] : []
    }));
  };

  // Handle device type filter change
  const handleDeviceTypeChange = (value: string) => {
    setSelectedDeviceType(value);
    setFilters(prev => ({
      ...prev,
      deviceTypes: value && value !== "_all" ? [value] : []
    }));
  };

  // Toggle availability filter
  const toggleAvailabilityFilter = () => {
    setFilters(prev => ({
      ...prev,
      showOnlyAvailable: !prev.showOnlyAvailable
    }));
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      showOnlyAvailable: false,
      osVersions: [],
      manufacturers: [],
      deviceTypes: []
    });
    setSelectedOsVersion("_all");
    setSelectedManufacturer("_all");
    setSelectedDeviceType("_all");
    setSearchQuery("");
  };

  // Handle save button click with validation
  const handleSave = () => {
    // Validate that all selected devices are from the current provider
    const validation = DeviceProviderUtils.validateDeviceSelection(selectedDevices, provider);

    if (!validation.isValid) {
      console.error('Device selection validation failed:', validation.error);
      return;
    }

    // Ensure provider is set correctly on all devices
    const devicesWithProvider = selectedDevices.map(device => ({
      ...device,
      provider: provider
    }));

    onSave(devicesWithProvider);
    onClose();
  };

  // Handle provider change with device clearing for mutual exclusivity
  const handleProviderChange = (value: string) => {
    const newProvider = value as DeviceProvider;

    // Clear selected devices when switching providers to enforce mutual exclusivity
    setSelectedDevices([]);

    // Reset state for new provider
    setDevices([]);

    // Update provider
    setProvider(newProvider);

    // Notify parent component about provider change
    if (onProviderChange) {
      onProviderChange(newProvider);
    }

    // Update environment settings with new device provider if callback is provided
    if (updateEnvironmentSettings) {
      updateEnvironmentSettings({
        deviceProvider: newProvider
      });
    }
  };

  // Determine modal size based on screen size
  const modalSizeClass = useMemo(() => {
    if (isMobile) return "w-full h-[100vh] max-w-none rounded-none";
    if (isTablet) return "sm:max-w-[90vw] max-h-[90vh]";
    return "sm:max-w-[90vw] max-h-[90vh] xl:max-w-[1400px]";
  }, [isMobile, isTablet]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        className={cn(
          "flex flex-col overflow-hidden",
          modalSizeClass,
          "!fixed !left-[50%] !top-[50%] !translate-x-[-50%] !translate-y-[-50%]"
        )}
        style={{
          animation: "fadeIn 0.3s ease-out",
          paddingRight: "2.5rem" // Add extra padding on the right to ensure the close button is fully visible
        }}
      >
        <DialogHeader className="pb-2 border-b sticky top-0 z-10 bg-background">
          <div className="flex justify-between items-center">
            <DialogTitle className="flex items-center text-blue-700 dark:text-blue-300">
              <div className="bg-blue-100 dark:bg-blue-900/50 p-1.5 rounded-full mr-3">
                <Smartphone className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              Select Android Devices
            </DialogTitle>

            <div className="flex items-center gap-6 mr-8">
              {/* Filter toggle button for mobile/tablet */}
              {!isDesktop && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsFilterSheetOpen(true)}
                  className="flex items-center gap-1"
                >
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">Filters</span>
                </Button>
              )}

              {/* Provider selection */}
              <Select value={provider} onValueChange={handleProviderChange}>
                <SelectTrigger className="w-[160px] border-blue-200 dark:border-blue-800">
                  <SelectValue placeholder="Select provider" className="truncate" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sauceLabs">
                    <div className="flex items-center gap-2">
                      <Server className="h-4 w-4 text-green-500" />
                      <span>SauceLabs</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="testinium">
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4 text-blue-500" />
                      <span>Testinium</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </DialogHeader>

        {/* Mobile filter sheet */}
        {!isDesktop && (
          <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
            <SheetContent side={isMobile ? "bottom" : "right"} className={cn(
              "w-full sm:max-w-md p-0",
              isMobile ? "h-[80vh] rounded-t-xl" : ""
            )}>
              <div className="p-6 h-full overflow-auto">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold flex items-center">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter Devices
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsFilterSheetOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Selected devices section - only show if devices are selected */}
                {selectedDevices.length > 0 && (
                  <div className="bg-blue-50 dark:bg-blue-900/10 p-3 rounded-md shadow-sm mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium flex items-center text-blue-700 dark:text-blue-300">
                        <Smartphone className="h-4 w-4 mr-2 text-blue-500" />
                        Selected Devices ({selectedDevices.length})
                      </h4>
                      {selectedDevices.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 text-xs text-gray-500 hover:text-red-500"
                          onClick={() => setSelectedDevices([])}
                        >
                          Clear All
                        </Button>
                      )}
                    </div>
                    {/* List container with proper overflow handling */}
                    <div className="max-h-[150px] overflow-y-auto pr-1">
                      <ul className="divide-y divide-gray-100 dark:divide-gray-800">
                        {selectedDevices.map(device => (
                          <li
                            key={`mobile-${device.provider}-${device.id}`}
                            className="py-2 first:pt-0 last:pb-0"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2 min-w-0 flex-1">
                                <div className="w-8 h-8 rounded-md overflow-hidden border border-blue-200 dark:border-blue-700 flex-shrink-0">
                                  <DeviceImage
                                    deviceId={device.id}
                                    deviceName={device.name}
                                    deviceType={device.deviceType || getDeviceType(device)}
                                    className="w-full h-full"
                                    fallbackClassName="bg-blue-100 dark:bg-blue-800"
                                    alt={`${device.name} thumbnail`}
                                  />
                                </div>
                                <div className="min-w-0 flex-1">
                                  {/* Device name with truncation */}
                                  <p className="font-medium text-sm truncate" title={safeCleanDeviceName(device.name)}>
                                    {safeCleanDeviceName(device.name)}
                                  </p>
                                  {/* Device details */}
                                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                                    <span className="flex items-center gap-1" title={`Android ${device.osVersion}`}>
                                      <Layers className="h-3 w-3" />
                                      {device.osVersion}
                                    </span>
                                    <span className="flex items-center gap-1 truncate max-w-[80px]" title={
                                      Array.isArray(device.manufacturer)
                                        ? device.manufacturer[0] || 'Unknown manufacturer'
                                        : device.manufacturer || 'Unknown manufacturer'
                                    }>
                                      <Smartphone className="h-3 w-3" />
                                      {Array.isArray(device.manufacturer)
                                        ? device.manufacturer[0] || 'Unknown'
                                        : device.manufacturer || 'Unknown'}
                                    </span>
                                  </div>
                                  {/* Serial Number */}
                                  {device.serialNumber && (
                                    <div className="text-xs text-gray-400 dark:text-gray-500 mt-1 truncate" title={`Serial: ${device.serialNumber}`}>
                                      Serial: {device.serialNumber}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full flex-shrink-0"
                                onClick={() => toggleDeviceSelection(device)}
                                title="Remove device"
                              >
                                <X className="h-3.5 w-3.5" />
                              </Button>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}

                {/* Filter content */}
                <div className="space-y-4">
                  {/* Availability filter */}
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                    <div className="flex items-center">
                      <Checkbox
                        id="available-only-mobile"
                        checked={filters.showOnlyAvailable}
                        onCheckedChange={() => toggleAvailabilityFilter()}
                        className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                      />
                      <Label htmlFor="available-only-mobile" className="text-sm cursor-pointer ml-2 flex items-center">
                        <CheckCircle2 className={`h-3.5 w-3.5 mr-1.5 ${filters.showOnlyAvailable ? 'text-green-500' : 'text-gray-400'}`} />
                        Available devices only
                      </Label>
                    </div>
                  </div>

                  {/* OS Version filter */}
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                    <Label className="text-sm font-medium mb-2 block flex items-center">
                      <Layers className="h-4 w-4 mr-2 text-blue-500" />
                      Android Version
                    </Label>
                    <Select value={selectedOsVersion} onValueChange={handleOsVersionChange}>
                      <SelectTrigger className="w-full h-9 bg-transparent border-gray-200 dark:border-gray-700">
                        <SelectValue placeholder="All versions" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_all">All versions</SelectItem>
                        {filterOptions.osVersions.map((version, index) => (
                          <SelectItem key={`mobile-os-version-${version}-${index}`} value={version}>
                            {version}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Manufacturer filter */}
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                    <Label className="text-sm font-medium mb-2 block flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-blue-500" />
                      Manufacturer
                    </Label>
                    <Select value={selectedManufacturer} onValueChange={handleManufacturerChange}>
                      <SelectTrigger className="w-full h-9 bg-transparent border-gray-200 dark:border-gray-700">
                        <SelectValue placeholder="All manufacturers" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_all">All manufacturers</SelectItem>
                        {filterOptions.manufacturers.map((manufacturer, index) => (
                          <SelectItem key={`mobile-manufacturer-${manufacturer}-${index}`} value={manufacturer}>
                            {manufacturer}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Device Type filter */}
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                    <Label className="text-sm font-medium mb-2 block flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-blue-500" />
                      Device Type
                    </Label>
                    <Select value={selectedDeviceType} onValueChange={handleDeviceTypeChange}>
                      <SelectTrigger className="w-full h-9 bg-transparent border-gray-200 dark:border-gray-700">
                        <SelectValue placeholder="All device types" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_all">All device types</SelectItem>
                        {filterOptions.deviceTypes.map((type, index) => (
                          <SelectItem key={`mobile-device-type-${type}-${index}`} value={type}>
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Active filters display */}
                  {(filters.osVersions.length > 0 || filters.manufacturers.length > 0 || filters.deviceTypes.length > 0) && (
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                      <h4 className="text-sm font-medium mb-2">Active Filters</h4>
                      <div className="flex flex-wrap gap-2">
                        {filters.osVersions.map(version => (
                          <Badge
                            key={`mobile-active-filter-os-${version}`}
                            variant="secondary"
                            className="flex items-center gap-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 shadow-sm"
                          >
                            <Layers className="h-3 w-3 mr-1" />
                            {version}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 ml-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full"
                              onClick={() => handleOsVersionChange("_all")}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                        {filters.manufacturers.map(manufacturer => (
                          <Badge
                            key={`mobile-active-filter-manufacturer-${manufacturer}`}
                            variant="secondary"
                            className="flex items-center gap-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 shadow-sm"
                          >
                            <Smartphone className="h-3 w-3 mr-1" />
                            {manufacturer}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 ml-1 hover:bg-green-200 dark:hover:bg-green-800 rounded-full"
                              onClick={() => handleManufacturerChange("_all")}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                        {filters.deviceTypes.map(type => (
                          <Badge
                            key={`mobile-active-filter-type-${type}`}
                            variant="secondary"
                            className="flex items-center gap-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800 shadow-sm"
                          >
                            {type === 'tablet' ? <Tablet className="h-3 w-3 mr-1" /> : <Smartphone className="h-3 w-3 mr-1" />}
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 ml-1 hover:bg-purple-200 dark:hover:bg-purple-800 rounded-full"
                              onClick={() => handleDeviceTypeChange("_all")}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-6 flex justify-end gap-2">
                  <Button variant="outline" onClick={resetFilters}>Reset Filters</Button>
                  <Button onClick={() => setIsFilterSheetOpen(false)}>Apply Filters</Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        )}

        {/* Main content area with responsive layout - three column for desktop */}
        <div className="flex flex-1 overflow-hidden">
          {/* Desktop filter sidebar - only visible on desktop */}
          {isDesktop && (
            <div className="w-[20%] min-w-[220px] max-w-[280px] border-r p-4 overflow-y-auto">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium flex items-center text-blue-700 dark:text-blue-300">
                  <Filter className="h-4 w-4 mr-2 text-blue-500" />
                  Filter Devices
                </h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetFilters}
                  className="h-7 text-xs hover:bg-blue-100 dark:hover:bg-blue-900/20"
                  disabled={!filters.showOnlyAvailable &&
                            filters.osVersions.length === 0 &&
                            filters.manufacturers.length === 0 &&
                            filters.deviceTypes.length === 0 &&
                            !searchQuery}
                >
                  Reset
                </Button>
              </div>

              {/* Filter components */}
              <div className="space-y-4">
                {/* Availability filter */}
                <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                  <div className="flex items-center">
                    <Checkbox
                      id="available-only"
                      checked={filters.showOnlyAvailable}
                      onCheckedChange={() => toggleAvailabilityFilter()}
                      className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    />
                    <Label htmlFor="available-only" className="text-sm cursor-pointer ml-2 flex items-center">
                      <CheckCircle2 className={`h-3.5 w-3.5 mr-1.5 ${filters.showOnlyAvailable ? 'text-green-500' : 'text-gray-400'}`} />
                      Available devices only
                    </Label>
                  </div>
                </div>

                {/* OS Version filter */}
                <Collapsible defaultOpen className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                  <CollapsibleTrigger className="flex items-center justify-between w-full text-sm font-medium">
                    <div className="flex items-center">
                      <Layers className="h-4 w-4 mr-2 text-blue-500" />
                      Android Version
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-500 transition-transform ui-open:rotate-180" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pt-2">
                    <Select value={selectedOsVersion} onValueChange={handleOsVersionChange}>
                      <SelectTrigger className="w-full h-8 mt-1 bg-transparent border-gray-200 dark:border-gray-700">
                        <SelectValue placeholder="All versions" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_all">All versions</SelectItem>
                        {filterOptions.osVersions.map((version, index) => (
                          <SelectItem key={`os-version-${version}-${index}`} value={version}>
                            {version}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </CollapsibleContent>
                </Collapsible>

                {/* Manufacturer filter */}
                <Collapsible defaultOpen className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                  <CollapsibleTrigger className="flex items-center justify-between w-full text-sm font-medium">
                    <div className="flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-blue-500" />
                      Manufacturer
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-500 transition-transform ui-open:rotate-180" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pt-2">
                    <Select value={selectedManufacturer} onValueChange={handleManufacturerChange}>
                      <SelectTrigger className="w-full h-8 mt-1 bg-transparent border-gray-200 dark:border-gray-700">
                        <SelectValue placeholder="All manufacturers" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_all">All manufacturers</SelectItem>
                        {filterOptions.manufacturers.map((manufacturer, index) => (
                          <SelectItem key={`manufacturer-${manufacturer}-${index}`} value={manufacturer}>
                            {manufacturer}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </CollapsibleContent>
                </Collapsible>

                {/* Device Type filter */}
                <Collapsible defaultOpen className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                  <CollapsibleTrigger className="flex items-center justify-between w-full text-sm font-medium">
                    <div className="flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-blue-500" />
                      Device Type
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-500 transition-transform ui-open:rotate-180" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="pt-2">
                    <Select value={selectedDeviceType} onValueChange={handleDeviceTypeChange}>
                      <SelectTrigger className="w-full h-8 mt-1 bg-transparent border-gray-200 dark:border-gray-700">
                        <SelectValue placeholder="All device types" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="_all">All device types</SelectItem>
                        {filterOptions.deviceTypes.map((type, index) => (
                          <SelectItem key={`device-type-${type}-${index}`} value={type}>
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </CollapsibleContent>
                </Collapsible>

                {/* Active filters display */}
                {(filters.osVersions.length > 0 || filters.manufacturers.length > 0 || filters.deviceTypes.length > 0) && (
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow-sm">
                    <h4 className="text-sm font-medium mb-2">Active Filters</h4>
                    <div className="flex flex-wrap gap-2">
                      {filters.osVersions.map(version => (
                        <Badge
                          key={`active-filter-os-${version}`}
                          variant="secondary"
                          className="flex items-center gap-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 shadow-sm"
                        >
                          <Layers className="h-3 w-3 mr-1" />
                          {version}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 ml-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full"
                            onClick={() => handleOsVersionChange("_all")}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                      {filters.manufacturers.map(manufacturer => (
                        <Badge
                          key={`active-filter-manufacturer-${manufacturer}`}
                          variant="secondary"
                          className="flex items-center gap-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 shadow-sm"
                        >
                          <Smartphone className="h-3 w-3 mr-1" />
                          {manufacturer}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 ml-1 hover:bg-green-200 dark:hover:bg-green-800 rounded-full"
                            onClick={() => handleManufacturerChange("_all")}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                      {filters.deviceTypes.map(type => (
                        <Badge
                          key={`active-filter-type-${type}`}
                          variant="secondary"
                          className="flex items-center gap-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800 shadow-sm"
                        >
                          {type === 'tablet' ? <Tablet className="h-3 w-3 mr-1" /> : <Smartphone className="h-3 w-3 mr-1" />}
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 ml-1 hover:bg-purple-200 dark:hover:bg-purple-800 rounded-full"
                            onClick={() => handleDeviceTypeChange("_all")}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Main content area - middle column */}
          <div className={cn(
            "flex flex-col overflow-hidden",
            isDesktop ? "flex-[3_3_0%]" : "flex-1"
          )}>
            {/* Search bar */}
            <div className="p-4 border-b">
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-500">
                  <Search className="h-4 w-4" />
                </div>
                <Input
                  placeholder="Search devices by name, manufacturer, OS version, serial number..."
                  className="pl-10 pr-10 border-blue-200 dark:border-blue-800 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/20"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4 text-gray-500" />
                  </Button>
                )}
              </div>
            </div>

            {/* Selected devices panel - collapsible - only for tablet and mobile */}
            {!isDesktop && (
              <Collapsible
                open={isSelectedDevicesPanelOpen}
                onOpenChange={setIsSelectedDevicesPanelOpen}
                className={cn(
                  "border-b",
                  selectedDevices.length === 0 && "hidden"
                )}
              >
              <CollapsibleTrigger asChild>
                <div className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-900/10">
                  <div className="flex items-center text-blue-700 dark:text-blue-300">
                    <Smartphone className="h-4 w-4 mr-2 text-blue-500" />
                    <span className="font-medium">Selected Devices ({selectedDevices.length})</span>
                  </div>
                  <ChevronDown className={cn(
                    "h-4 w-4 text-gray-500 transition-transform duration-200",
                    isSelectedDevicesPanelOpen && "transform rotate-180"
                  )} />
                </div>
              </CollapsibleTrigger>

              <CollapsibleContent>
                <div className="p-3 border-t bg-blue-50/50 dark:bg-blue-900/5">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {selectedDevices.length} {selectedDevices.length === 1 ? 'device' : 'devices'} selected
                    </span>
                    {selectedDevices.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs text-gray-500 hover:text-red-500"
                        onClick={() => setSelectedDevices([])}
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                  {/* List container with proper overflow handling */}
                  <div className="max-h-[200px] overflow-y-auto pr-1">
                    <ul className="divide-y divide-gray-100 dark:divide-gray-800">
                      {selectedDevices.map(device => (
                        <li
                          key={`${device.provider}-${device.id}`}
                          className="py-2 first:pt-0 last:pb-0 animate-fade-in"
                          style={{
                            animationDelay: `${selectedDevices.indexOf(device) * 0.05}s`
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 min-w-0 flex-1">
                              <div className="w-8 h-8 rounded-md overflow-hidden border border-blue-200 dark:border-blue-700 flex-shrink-0">
                                <DeviceImage
                                  deviceId={device.id}
                                  deviceName={device.name}
                                  deviceType={device.deviceType || getDeviceType(device)}
                                  className="w-full h-full"
                                  fallbackClassName="bg-blue-100 dark:bg-blue-800"
                                  alt={`${device.name} thumbnail`}
                                />
                              </div>
                              <div className="min-w-0 flex-1">
                                {/* Device name with truncation */}
                                <p className="font-medium text-sm truncate" title={safeCleanDeviceName(device.name)}>
                                  {safeCleanDeviceName(device.name)}
                                </p>
                                {/* Device details */}
                                <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                                  <span className="flex items-center gap-1" title={`Android ${device.osVersion}`}>
                                    <Layers className="h-3 w-3" />
                                    {device.osVersion}
                                  </span>
                                  <span className="flex items-center gap-1" title={
                                    Array.isArray(device.manufacturer)
                                      ? device.manufacturer[0] || 'Unknown manufacturer'
                                      : device.manufacturer || 'Unknown manufacturer'
                                  }>
                                    <Smartphone className="h-3 w-3" />
                                    {Array.isArray(device.manufacturer)
                                      ? device.manufacturer[0] || 'Unknown'
                                      : device.manufacturer || 'Unknown'}
                                  </span>
                                  {device.provider && (
                                    <Badge variant="outline" className="px-1.5 py-0 h-4 text-[10px]">
                                      {device.provider}
                                    </Badge>
                                  )}
                                </div>
                                {/* Serial Number */}
                                {device.serialNumber && (
                                  <div className="text-xs text-gray-400 dark:text-gray-500 mt-1" title={`Serial: ${device.serialNumber}`}>
                                    Serial: {device.serialNumber}
                                  </div>
                                )}
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full flex-shrink-0"
                              onClick={() => toggleDeviceSelection(device)}
                              title="Remove device"
                            >
                              <X className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
            )}

            {/* Device list */}
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                {isLoading ? (
                  <div className="flex flex-col items-center justify-center h-[300px] p-6">
                    <div className="relative mb-4">
                      <div className="w-12 h-12 rounded-full border-4 border-blue-200 dark:border-blue-800 opacity-25"></div>
                      <div className="w-12 h-12 rounded-full border-4 border-t-blue-600 dark:border-t-blue-400 border-r-transparent border-b-transparent border-l-transparent animate-spin absolute top-0 left-0"></div>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-medium mb-1 text-blue-700 dark:text-blue-300">Loading Devices</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Fetching available Android devices from {provider === 'sauceLabs' ? 'SauceLabs' : 'Testinium'}...</p>
                    </div>
                    <div className="mt-6 flex flex-wrap justify-center gap-4">
                      {[...Array(6)].map((_, i) => (
                        <Skeleton
                          key={`skeleton-${i}`}
                          className="w-[220px] h-[120px] rounded-md bg-gray-100 dark:bg-gray-800"
                          style={{
                            animation: `pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite`,
                            animationDelay: `${i * 0.1}s`
                          }}
                        />
                      ))}
                    </div>
                  </div>
                ) : filteredDevices.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-[300px] text-gray-500">
                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-full mb-4">
                      <Smartphone className="h-8 w-8 text-gray-400" />
                    </div>
                    <p className="text-lg font-medium mb-1">No devices found</p>
                    <p className="text-sm text-gray-400">
                      {searchQuery ? "Try adjusting your search or filters" : "No devices available from this provider"}
                    </p>
                  </div>
                ) : (
                  <div className="p-4">
                    <div className={cn(
                      "grid gap-3",
                      isDesktop
                        ? "grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4"
                        : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5"
                    )}>
                      {filteredDevices.map(device => (
                        <Card
                          key={`${device.provider}-${device.id}`}
                          className={cn(
                            "overflow-hidden transition-all duration-200 hover:shadow-lg border cursor-pointer h-[180px]",
                            isDeviceSelected(device)
                              ? 'ring-2 ring-blue-500 dark:ring-blue-400 bg-blue-50/50 dark:bg-blue-900/10 border-blue-300 dark:border-blue-700 shadow-md'
                              : 'hover:border-blue-200 dark:hover:border-blue-700 hover:shadow-md',
                            !isDeviceAvailable(device) ? 'opacity-70' : ''
                          )}
                          style={{
                            animation: `fadeIn 0.3s ease-out, slideUp 0.3s ease-out`,
                            animationDelay: `${(filteredDevices.indexOf(device) % 10) * 0.05}s`
                          }}
                          onClick={() => toggleDeviceSelection(device)}
                        >
                          {/* Device card with real device image */}
                          <div className="flex flex-col h-full">
                            {/* Device image frame - clean borderless design */}
                            <div className={cn(
                              "mx-auto mt-2 rounded-lg overflow-hidden w-[80px] h-[130px] relative",
                              isDeviceSelected(device)
                                ? 'shadow-md shadow-blue-200/50 dark:shadow-blue-900/30'
                                : ''
                            )}>
                              {/* Real device image with lazy loading - fills container better */}
                              <DeviceImage
                                deviceId={device.id}
                                deviceName={device.name}
                                deviceType={device.deviceType || getDeviceType(device)}
                                className="w-full h-full p-1"
                                fallbackClassName="bg-transparent"
                                alt={`${device.name} device`}
                              />

                              {/* OS Version overlay - larger and more readable */}
                              <div className="absolute bottom-1 left-1 right-1">
                                <div className="bg-black/85 text-white text-[9px] px-1.5 py-1 rounded text-center font-semibold leading-none shadow-sm">
                                  Android {device.osVersion}
                                </div>
                              </div>
                            </div>

                            {/* Device info below the phone - improved readability */}
                            <div className="p-2 text-center flex-1 flex flex-col justify-between">
                              <div>
                                <p className="text-sm font-medium truncate leading-tight" title={safeCleanDeviceName(device.name)}>
                                  {safeCleanDeviceName(device.name)}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1" title={
                                  Array.isArray(device.manufacturer)
                                    ? device.manufacturer[0] || 'Unknown manufacturer'
                                    : device.manufacturer || 'Unknown manufacturer'
                                }>
                                  {Array.isArray(device.manufacturer)
                                    ? device.manufacturer[0] || 'Unknown'
                                    : device.manufacturer || 'Unknown'}
                                </p>
                                {/* Serial Number */}
                                {device.serialNumber && (
                                  <p className="text-xs text-gray-400 dark:text-gray-500 truncate mt-0.5" title={`Serial: ${device.serialNumber}`}>
                                    Serial: {device.serialNumber}
                                  </p>
                                )}
                              </div>

                              {/* Device specs - improved visibility */}
                              {device.screenResolution && (
                                <div className="mt-1.5">
                                  <Badge variant="secondary" className="text-[10px] px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400">
                                    {device.screenResolution}
                                  </Badge>
                                </div>
                              )}
                            </div>

                            {/* Selection status - improved readability */}
                            <div className="mt-auto p-1.5">
                              {isDeviceSelected(device) ? (
                                <div className="w-full text-center text-xs text-blue-600 font-semibold flex items-center justify-center">
                                  <Check className="h-3.5 w-3.5 mr-1" />
                                  Selected
                                </div>
                              ) : (
                                <div className="w-full text-center text-xs text-gray-500 font-medium">
                                  Tap to select
                                </div>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </ScrollArea>
            </div>
          </div>

          {/* Third column - Selected Devices - only visible on desktop */}
          {isDesktop && (
            <div className="w-[25%] min-w-[250px] max-w-[320px] border-l p-4 overflow-y-auto">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium flex items-center text-blue-700 dark:text-blue-300">
                  <Smartphone className="h-4 w-4 mr-2 text-blue-500" />
                  Selected Devices ({selectedDevices.length})
                </h4>
                {selectedDevices.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 text-xs text-gray-500 hover:text-red-500"
                    onClick={() => setSelectedDevices([])}
                  >
                    Clear All
                  </Button>
                )}
              </div>

              {/* Selected devices list */}
              {selectedDevices.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-4 text-center bg-gray-50 dark:bg-gray-800/50 rounded-md">
                  <Smartphone className="h-8 w-8 text-gray-300 mb-2" />
                  <p>No devices selected yet</p>
                  <p className="text-xs mt-1">Select devices from the list to see them here</p>
                </div>
              ) : (
                <div className="max-h-[calc(100vh-250px)] overflow-y-auto pr-1">
                  <ul className="divide-y divide-gray-100 dark:divide-gray-800">
                    {selectedDevices.map(device => (
                      <li
                        key={`selected-${device.provider}-${device.id}`}
                        className="py-2 first:pt-0 last:pb-0 animate-fade-in"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 min-w-0 flex-1">
                            <div className="w-8 h-8 rounded-md overflow-hidden border border-blue-200 dark:border-blue-700 flex-shrink-0">
                              <DeviceImage
                                deviceId={device.id}
                                deviceName={device.name}
                                deviceType={device.deviceType || getDeviceType(device)}
                                className="w-full h-full"
                                fallbackClassName="bg-blue-100 dark:bg-blue-800"
                                alt={`${device.name} thumbnail`}
                              />
                            </div>
                            <div className="min-w-0 flex-1">
                              {/* Device name with truncation */}
                              <p className="font-medium text-sm truncate" title={safeCleanDeviceName(device.name)}>
                                {safeCleanDeviceName(device.name)}
                              </p>
                              {/* Device details */}
                              <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                                <span className="flex items-center gap-1" title={`Android ${device.osVersion}`}>
                                  <Layers className="h-3 w-3" />
                                  {device.osVersion}
                                </span>
                                <span className="flex items-center gap-1 truncate max-w-[80px]" title={
                                  Array.isArray(device.manufacturer)
                                    ? device.manufacturer[0] || 'Unknown manufacturer'
                                    : device.manufacturer || 'Unknown manufacturer'
                                }>
                                  <Smartphone className="h-3 w-3" />
                                  {Array.isArray(device.manufacturer)
                                    ? device.manufacturer[0] || 'Unknown'
                                    : device.manufacturer || 'Unknown'}
                                </span>
                              </div>
                              {/* Serial Number */}
                              {device.serialNumber && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 mt-1 truncate" title={`Serial: ${device.serialNumber}`}>
                                  Serial: {device.serialNumber}
                                </div>
                              )}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full flex-shrink-0"
                            onClick={() => toggleDeviceSelection(device)}
                            title="Remove device"
                          >
                            <X className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer with actions */}
        <div className="border-t mt-auto">
          {/* Action footer */}
          <DialogFooter className="p-4 flex justify-between items-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {selectedDevices.length > 0 ? (
                <span className="flex items-center">
                  <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                  {selectedDevices.length} {selectedDevices.length === 1 ? 'device' : 'devices'} selected
                </span>
              ) : (
                <span className="flex items-center">
                  <Info className="h-4 w-4 mr-2 text-amber-500" />
                  No devices selected
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                className="border-gray-300 dark:border-gray-700"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={selectedDevices.length === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Check className="h-4 w-4 mr-2" />
                Save Selection
              </Button>
            </div>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
