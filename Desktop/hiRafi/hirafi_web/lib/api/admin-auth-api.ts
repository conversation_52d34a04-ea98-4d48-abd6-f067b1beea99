/**
 * Admin Auth API
 *
 * Admin kimlik doğrulama ile ilgili API isteklerini yönetir.
 */

import { get, post, put } from './fetch-wrapper';
import { ADMIN_ENDPOINTS } from './constants';

/**
 * Admin girişi yapar
 *
 * @param email Admin e-posta adresi
 * @param password Admin şifresi
 */
export async function login(email: string, password: string) {
  return post(ADMIN_ENDPOINTS.LOGIN, { email, password });
}

/**
 * Token doğrulaması yapar
 *
 * @param token JWT token
 */
export async function validateToken(token: string) {
  return post(ADMIN_ENDPOINTS.VALIDATE, {}, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Admin şifresini değiştirir
 *
 * @param currentPassword Mevcut şifre
 * @param newPassword Yeni şifre
 */
export async function changePassword(currentPassword: string, newPassword: string) {
  return post(ADMIN_ENDPOINTS.CHANGE_PASSWORD, { currentPassword, newPassword });
}
