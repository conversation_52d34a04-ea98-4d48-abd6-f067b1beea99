/**
 * Scenario Test Data Store
 * Zustand store for managing test data sets in scenario creation
 */

import { create } from "zustand";
import { dataSetApi } from "@/lib/api/test-data";
import type { DataSet } from "@/types/test-data";
import { storeManager } from "@/lib/utils/store-management";

interface TestDataVariable {
  id: string;
  name: string;
  type: string;
  description?: string;
  environmentValues: Record<string, string>;
  environments?: Array<{ id: string; name: string }>;
  // Source type for visual differentiation
  sourceType?: "api" | "database" | "csv" | "excel";
  sourceId?: string;
  // API source fields
  jsonPath?: string;
  // Database source fields
  nameColumn?: string;
  valueColumn?: string;
  dbMode?: "visual" | "raw";
}

// Lightweight data set interface for dropdown performance
interface LightweightDataSet {
  id: string;
  name: string;
  description: string;
  variableNames: string[]; // For backward compatibility
  variables?: Array<{
    name: string;
    environments: Array<{
      id: string;
      name: string;
    }>;
  }>;
}

interface ScenarioTestDataState {
  // Selected test data set (lightweight data set)
  selectedDataSet: LightweightDataSet | null;

  // Available data sets for the team/company (lightweight for dropdown)
  availableDataSets: LightweightDataSet[];

  // Variables from the selected data set (just names for autocomplete)
  variables: TestDataVariable[];

  // Loading states
  isLoadingDataSets: boolean;
  isLoadingVariables: boolean;

  // Error states
  dataSetError: string | null;

  // UI state
  showVariablePreview: boolean;
  searchQuery: string;

  // Cache management
  lastDataSetsLoad: number | null;
  dataSetsInitialized: boolean;
}

interface ScenarioTestDataActions {
  // Data set operations
  loadDataSets: (forceRefresh?: boolean) => Promise<void>;
  selectDataSet: (dataSet: LightweightDataSet | null) => void;
  refreshSelectedDataSet: () => Promise<void>;

  // Variable operations
  getVariableByName: (name: string) => TestDataVariable | undefined;
  getVariableNames: () => string[];

  // UI operations
  setShowVariablePreview: (show: boolean) => void;
  setSearchQuery: (query: string) => void;

  // Reset operations
  reset: () => void;

  // Clear persisted state for store manager
  clearPersistedState: () => void;
}

type ScenarioTestDataStore = ScenarioTestDataState & ScenarioTestDataActions;

const initialState: ScenarioTestDataState = {
  selectedDataSet: null,
  availableDataSets: [],
  variables: [],
  isLoadingDataSets: false,
  isLoadingVariables: false,
  dataSetError: null,
  showVariablePreview: true,
  searchQuery: "",
  lastDataSetsLoad: null,
  dataSetsInitialized: false,
};

export const useScenarioTestDataStore = create<ScenarioTestDataStore>(
  (set, get) => ({
    ...initialState,

    // Load available data sets for the team/company (lightweight for dropdown performance)
    loadDataSets: async (forceRefresh = false) => {
      const state = get();

      // Prevent duplicate requests if already loading
      if (state.isLoadingDataSets) {
        return;
      }

      // Smart caching: only load if not initialized or force refresh is requested
      // Cache is valid for 5 minutes (300000ms) to balance freshness with performance
      const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
      const now = Date.now();
      const isCacheValid =
        state.lastDataSetsLoad &&
        now - state.lastDataSetsLoad < CACHE_DURATION &&
        state.dataSetsInitialized;

      if (!forceRefresh && isCacheValid && state.availableDataSets.length > 0) {
        // Use cached data
        return;
      }

      set({ isLoadingDataSets: true, dataSetError: null });

      try {
        const response = await dataSetApi.getKeys();
        console.log('[ScenarioTestDataStore] getKeys response:', response);

        if (response.success) {
          // API response contains lightweight data sets with variables and environment info
          const dataSets = response.dataSets || response.data?.dataSets || [];
          console.log('[ScenarioTestDataStore] extracted dataSets:', dataSets);

          const processedDataSets = Array.isArray(dataSets)
            ? dataSets.map((dataSet: any) => ({
                ...dataSet,
                // For backward compatibility, extract variable names from the new structure
                variableNames: dataSet.variables
                  ? dataSet.variables.map((v: any) => v.name)
                  : [],
              }))
            : [];

          set({
            availableDataSets: processedDataSets,
            isLoadingDataSets: false,
            lastDataSetsLoad: now,
            dataSetsInitialized: true,
            dataSetError: null,
          });
        } else {
          console.error('[ScenarioTestDataStore] Failed to load data sets:', response);
          set({
            dataSetError: response.error || response.message || "Failed to load data sets",
            isLoadingDataSets: false,
          });
        }
      } catch (error) {
        console.error("Error loading data sets:", error);
        set({
          dataSetError: "Failed to load data sets",
          isLoadingDataSets: false,
        });
      }
    },

    // Select a data set (lightweight data only)
    selectDataSet: async (dataSet: LightweightDataSet | null) => {
      if (!dataSet) {
        set({ selectedDataSet: null, variables: [] });
        return;
      }

      set({ selectedDataSet: dataSet, isLoadingVariables: true });

      try {
        // Load full data set details to get variable values
        const response = await dataSetApi.getById(dataSet.id);

        if (response.success && response.data) {
          const fullDataSet = response.data;
          let variables: TestDataVariable[] = [];

          if (
            fullDataSet.metadata?.variables &&
            Array.isArray(fullDataSet.metadata.variables)
          ) {
            // Use metadata variables with environment values
            variables = fullDataSet.metadata.variables.map(
              (variable: any, index: number) => {
                // Handle different environmentValues structures
                let environmentValues: Record<string, string> = {};

                if (
                  variable.environmentValues &&
                  typeof variable.environmentValues === "object"
                ) {
                  // If environmentValues is structured like { envName: { value, environmentId, environmentName } }
                  Object.entries(variable.environmentValues).forEach(
                    ([key, val]: [string, any]) => {
                      if (
                        val &&
                        typeof val === "object" &&
                        val.value !== undefined
                      ) {
                        environmentValues[val.environmentId || key] = val.value;
                      } else if (typeof val === "string") {
                        environmentValues[key] = val;
                      }
                    },
                  );
                }

                // Extract environment info
                const environments: Array<{ id: string; name: string }> = [];
                if (
                  variable.environmentValues &&
                  typeof variable.environmentValues === "object"
                ) {
                  Object.entries(variable.environmentValues).forEach(
                    ([key, val]: [string, any]) => {
                      if (
                        val &&
                        typeof val === "object" &&
                        val.environmentId &&
                        val.environmentName
                      ) {
                        environments.push({
                          id: val.environmentId,
                          name: val.environmentName,
                        });
                      }
                    },
                  );
                }

                // Try to find database mapping info from different possible locations
                let sourceType = variable.sourceType || "csv";
                let nameColumn = variable.nameColumn;
                let valueColumn = variable.valueColumn;
                let dbMode = variable.dbMode;
                let jsonPath = variable.jsonPath;

                // Check if this is a database variable with source info
                if (variable.source || variable.sourceInfo) {
                  const sourceInfo = variable.source || variable.sourceInfo;
                  if (sourceInfo.type === "database") {
                    sourceType = "database";
                    nameColumn =
                      sourceInfo.nameColumn ||
                      sourceInfo.nameField ||
                      nameColumn;
                    valueColumn =
                      sourceInfo.valueColumn ||
                      sourceInfo.valueField ||
                      valueColumn;
                    dbMode = sourceInfo.mode || sourceInfo.dbMode || dbMode;
                  } else if (sourceInfo.type === "api") {
                    sourceType = "api";
                    jsonPath =
                      sourceInfo.jsonPath || sourceInfo.path || jsonPath;
                  }
                }

                // Check if sourceType can be inferred from other fields
                if (nameColumn || valueColumn) {
                  sourceType = "database";
                } else if (jsonPath) {
                  sourceType = "api";
                }

                const processedVariable = {
                  id: variable.id || `var-${variable.name}-${index}`,
                  name: variable.name,
                  type: variable.type || "string",
                  description:
                    variable.description || `Variable from ${dataSet.name}`,
                  environmentValues,
                  environments,
                  // Source information for UI differentiation
                  sourceType,
                  sourceId: variable.sourceId,
                  jsonPath,
                  nameColumn,
                  valueColumn,
                  dbMode,
                };

                return processedVariable;
              },
            );
          } else if (
            fullDataSet.variables &&
            Array.isArray(fullDataSet.variables)
          ) {
            // Fallback to basic variables
            variables = fullDataSet.variables.map(
              (variable: any, index: number) => ({
                id: variable.id || `var-${variable.name}-${index}`,
                name: variable.name,
                type: variable.type || "string",
                description:
                  variable.description || `Variable from ${dataSet.name}`,
                environmentValues: { default: variable.value || "" },
              }),
            );
          } else {
            // Fallback to variable names only
            variables = dataSet.variableNames.map((varName, index) => ({
              id: `var-${varName}-${index}`,
              name: varName,
              type: "string",
              description: `Variable from ${dataSet.name}`,
              environmentValues: {},
            }));
          }

          set({ variables, isLoadingVariables: false });
        } else {
          // Fallback to variable names only if API call fails
          const variables: TestDataVariable[] = dataSet.variableNames.map(
            (varName, index) => ({
              id: `var-${varName}-${index}`,
              name: varName,
              type: "string",
              description: `Variable from ${dataSet.name}`,
              environmentValues: {},
            }),
          );

          set({ variables, isLoadingVariables: false });
        }
      } catch (error) {
        console.error("Error loading variable details:", error);

        // Fallback to variable names only
        const variables: TestDataVariable[] = dataSet.variableNames.map(
          (varName, index) => ({
            id: `var-${varName}-${index}`,
            name: varName,
            type: "string",
            description: `Variable from ${dataSet.name}`,
            environmentValues: {},
          }),
        );

        set({ variables, isLoadingVariables: false });
      }
    },

    // Get variable by name
    getVariableByName: (name: string) => {
      const { variables } = get();
      return Array.isArray(variables)
        ? variables.find((variable) => variable.name === name)
        : undefined;
    },

    // Get all variable names
    getVariableNames: () => {
      const { variables } = get();
      return Array.isArray(variables)
        ? variables.map((variable) => variable.name)
        : [];
    },

    // UI operations
    setShowVariablePreview: (show: boolean) => {
      set({ showVariablePreview: show });
    },

    setSearchQuery: (query: string) => {
      set({ searchQuery: query });
    },

    // Refresh currently selected data set variables
    refreshSelectedDataSet: async () => {
      const { selectedDataSet } = get();
      if (selectedDataSet) {
        // Re-select the same dataset to refresh its variables
        await get().selectDataSet(selectedDataSet);
      }
    },

    // Reset store to initial state
    reset: () => {
      set(initialState);
    },

    // Clear persisted state for store manager
    clearPersistedState: () => {
      set({ ...initialState });
    },
  }),
);

// Selectors for easier state access
export const useScenarioTestDataSelectors = () => {
  const store = useScenarioTestDataStore();

  const filteredDataSets = Array.isArray(store.availableDataSets)
    ? store.availableDataSets.filter(
        (dataSet) =>
          dataSet.name
            .toLowerCase()
            .includes(store.searchQuery.toLowerCase()) ||
          (dataSet.description &&
            dataSet.description
              .toLowerCase()
              .includes(store.searchQuery.toLowerCase())) ||
          dataSet.variableNames.some((varName) =>
            varName.toLowerCase().includes(store.searchQuery.toLowerCase()),
          ),
      )
    : [];

  return {
    // Filtered data sets based on search query
    filteredDataSets,

    // Check if any data set is selected
    hasSelectedDataSet: !!store.selectedDataSet,

    // Check if variables are available
    hasVariables: Array.isArray(store.variables)
      ? store.variables.length > 0
      : false,

    // Get variable suggestions for autocomplete
    getVariableSuggestions: (query: string) => {
      return Array.isArray(store.variables)
        ? store.variables
            .filter((variable: TestDataVariable) =>
              variable.name.toLowerCase().includes(query.toLowerCase()),
            )
            .map((variable: TestDataVariable) => ({
              name: variable.name,
              description: variable.description,
              type: variable.type,
            }))
        : [];
    },
  };
};

// Register this store with the store manager for automatic cleanup
if (typeof window !== "undefined") {
  storeManager.registerStore({
    clearPersistedState: () =>
      useScenarioTestDataStore.getState().clearPersistedState(),
  });
}
