"use client";

import { <PERSON>, ListOrdered, FileCheck } from "lucide-react";
// Define TabType locally since it's no longer exported from the deleted hook
type TabType = "details" | "steps" | "testmanagement";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useUnifiedTestManagementContext } from "@/contexts/UnifiedTestManagementContext";

interface TabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

export function TabNavigation({ activeTab, onTabChange }: TabNavigationProps) {
  const [previousTab, setPreviousTab] = useState<TabType>(activeTab);
  const [hoverTab, setHoverTab] = useState<TabType | null>(null);

  // Get test management integration info
  const { store } = useUnifiedTestManagementContext();
  const { availableProviders, activeProvider } = store;

  // Provider display name helper
  const getProviderDisplayName = (provider: any) => {
    return provider === 'testrail' ? 'TestRail' : 'Zephyr Scale';
  };

  // Always include test management tab in edit scenario
  const getTabOrder = () => {
    const baseTabs = ["details", "steps", "testmanagement"];
    return baseTabs;
  };

  const tabOrder = getTabOrder();

  useEffect(() => {
    if (activeTab !== previousTab) {
      setPreviousTab(activeTab);
    }
  }, [activeTab, previousTab]);

  // Tab değişim yönetimi
  const handleTabChange = (tab: TabType) => {
    if (tab !== activeTab) {
      onTabChange(tab);
    }
  };

  // Tab içeriği ve ikonu
  const getTabContent = () => {
    const baseContent = {
      details: {
        icon: <Eye className="h-4 w-4" />,
        label: "Scenario Details",
        activeColor: "text-blue-600 dark:text-blue-400",
        indicatorColor: "bg-blue-600 dark:bg-blue-400",
      },
      steps: {
        icon: <ListOrdered className="h-4 w-4" />,
        label: "Test Steps",
        activeColor: "text-purple-600 dark:text-purple-400",
        indicatorColor: "bg-purple-600 dark:bg-purple-400",
      },
    };

    // Always show test management tab in edit scenario
    const testManagementLabel =
      availableProviders.length === 1 && activeProvider
        ? getProviderDisplayName(activeProvider)
        : "Test Management";

    baseContent.testmanagement = {
      icon: <FileCheck className="h-4 w-4" />,
      label: testManagementLabel,
      activeColor: "text-indigo-600 dark:text-indigo-400",
      indicatorColor: "bg-indigo-600 dark:bg-indigo-400",
    };

    return baseContent;
  };

  const tabContent = getTabContent();

  return (
    <div className="mb-4">
      <div className="flex justify-center bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        {tabOrder.map((tab) => {
          const isActive = activeTab === tab;
          const tabInfo = tabContent[tab as TabType];

          return (
            <button
              key={tab}
              onClick={() => handleTabChange(tab as TabType)}
              onMouseEnter={() => setHoverTab(tab as TabType)}
              onMouseLeave={() => setHoverTab(null)}
              className={cn(
                "flex-1 relative py-2.5 px-2 flex items-center justify-center gap-2",
                "text-sm font-medium transition-all duration-200",
                isActive
                  ? "bg-gray-50 dark:bg-gray-800/70"
                  : "text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700/30",
              )}
            >
              <div
                className={cn(
                  "flex items-center gap-1.5 transition-all duration-200",
                  isActive ? tabInfo.activeColor : "",
                )}
              >
                {tabInfo.icon}
                <span>{tabInfo.label}</span>
              </div>

              {/* Indicator for active tab */}
              {isActive && (
                <span
                  className={cn(
                    "absolute bottom-0 left-0 w-full h-0.5",
                    tabInfo.indicatorColor,
                  )}
                ></span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
}
