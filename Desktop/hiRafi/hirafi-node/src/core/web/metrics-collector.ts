/**
 * Optimized Web Metrics Collector
 * CDP-based high-performance metrics collection with clean architecture
 * Ensures exact same return type as EnhancedMetrics interface
 */

import { Page, CDPSession } from 'rebrowser-puppeteer';
import { logger } from '../../utils/logger.js';
import { EnhancedMetrics, NetworkCollectorConfig } from '../../models/types.js';
import { NetworkTempStorage } from './network-temp-storage.js';
import { getNetworkCollectorConfig } from '../../config/domains/network-collector-config.js';
import { BaseCDPCollector, CollectorState, withTimeout } from './collector-base.js';

// Type definitions for internal use
interface MetricsCollectionOptions {
  // Metrics collection flags
  includePageMetrics?: boolean;
  includePerformanceMetrics?: boolean;
  includeNavigationTiming?: boolean;

  // Race condition prevention
  maxTimeout?: number; // Maximum timeout for metrics collection

  // Reporting settings
  reportSettings?: {
    pageMetrics?: boolean;
    networkData?: boolean;
    tracingData?: boolean;
    accessibilityData?: boolean;
  };
}



/**
 * CDP-based Network Collector
 * High-performance network monitoring using Chrome DevTools Protocol
 * Features memory-safe storage with temporary file support to prevent memory leaks
 */
export class NetworkCollector extends BaseCDPCollector {
  private requests: Map<string, any> = new Map(); // Changed to Map for easier lookup
  private responses: Map<string, any> = new Map(); // Changed to Map for easier lookup
  private loadingFinished: Map<string, any> = new Map(); // Track loading finished events
  private networkEntries: any[] = []; // Final processed network entries

  // Streaming statistics for improved performance
  private networkStats = {
    total: 0,
    successful: 0,
    failed: 0,
    byType: {} as Record<string, number>,
    totalTransferred: 0,
  };

  private requestHandler: ((params: any) => void) | null = null;
  private responseHandler: ((params: any) => void) | null = null;
  private loadingFinishedHandler: ((params: any) => void) | null = null;

  // Memory management properties
  private config: NetworkCollectorConfig;
  private tempStorage: NetworkTempStorage | null = null;
  private storedEntryFiles: string[] = [];
  private memoryWarningIssued: boolean = false;
  private testId?: string;

  constructor(page: Page, testId?: string, config?: Partial<NetworkCollectorConfig>) {
    super(page as any, 'NetworkCollector');

    this.testId = testId;

    // Load configuration with defaults
    const defaultConfig = getNetworkCollectorConfig();
    this.config = {
      ...defaultConfig,
      ...config
    };

    // Initialize temporary storage if enabled
    if (this.config.enableTemporaryStorage) {
      this.tempStorage = new NetworkTempStorage(this.config, testId);
    }

    logger.debug(`NetworkCollector: Initialized with memory limit ${this.config.maxInMemoryEntries}, temp storage: ${this.config.enableTemporaryStorage}`);
  }

  /**
   * Implement abstract method for collector-specific cleanup
   */
  protected async performSpecificCleanup(): Promise<void> {
    // Cleanup CDP resources and event listeners
    await this.cleanup();

    // Clear data structures to free memory
    this.requests.clear();
    this.responses.clear();
    this.loadingFinished.clear();
    this.networkEntries = [];
    this.storedEntryFiles = [];
    this.resetStats();

    // Cleanup temporary storage
    if (this.tempStorage && this.config.cleanupOnDestroy) {
      try {
        await this.tempStorage.cleanup();
        this.tempStorage = null;
      } catch (error: any) {
        logger.warn(`NetworkCollector: Error cleaning up temporary storage: ${error.message}`);
      }
    }
  }

  /**
   * Start collecting network data using CDP
   */
  async start(): Promise<void> {
    // Check if we can transition to collecting state
    if (this.isDestroyed()) {
      logger.warn('NetworkCollector: Cannot start - collector is destroyed or being destroyed');
      return;
    }

    if (this.getState() === CollectorState.COLLECTING) {
      logger.debug('NetworkCollector: Already collecting');
      return;
    }

    // Transition to collecting state
    const canTransition = await this.transitionState(CollectorState.COLLECTING);
    if (!canTransition) {
      logger.warn('NetworkCollector: Failed to transition to collecting state');
      return;
    }

    try {
      // Create CDP session for direct protocol access
      this.cdpSession = await this.ensureCDPSession();

      // Enable Network domain
      await this.cdpSession.send('Network.enable');

      // Clear previous data
      this.requests.clear();
      this.responses.clear();
      this.loadingFinished.clear();
      this.networkEntries = [];

      // Create event handlers with proper cleanup tracking and memory management
      this.requestHandler = (params) => {
        if (this.getState() === CollectorState.COLLECTING) {
          const requestData = {
            requestId: params.requestId,
            url: params.request.url,
            method: params.request.method,
            headers: params.request.headers,
            timestamp: new Date(params.timestamp * 1000).toISOString(),
            initiator: params.initiator?.type || 'unknown',
            startTime: params.timestamp
          };

          this.requests.set(params.requestId, requestData);

          // Check memory usage and flush if necessary
          this.checkAndFlushMemory().catch((error: any) => {
            logger.warn(`NetworkCollector: Error during memory check: ${error.message}`);
          });
        }
      };

      this.responseHandler = (params) => {
        if (this.getState() === CollectorState.COLLECTING) {
          const responseData = {
            requestId: params.requestId,
            url: params.response.url,
            status: params.response.status,
            statusText: params.response.statusText,
            headers: params.response.headers,
            mimeType: params.response.mimeType,
            timestamp: new Date(params.timestamp * 1000).toISOString(),
            fromCache: params.response.fromDiskCache || params.response.fromServiceWorker,
            encodedDataLength: params.response.encodedDataLength,
            responseTime: params.timestamp,
            timing: params.response.timing // CDP timing information
          };

          this.responses.set(params.requestId, responseData);

          // Check memory usage and flush if necessary
          this.checkAndFlushMemory().catch((error: any) => {
            logger.warn(`NetworkCollector: Error during memory check: ${error.message}`);
          });
        }
      };

      // Create loading finished handler for timing information
      this.loadingFinishedHandler = (params) => {
        if (this.getState() === CollectorState.COLLECTING) {
          const loadingData = {
            requestId: params.requestId,
            timestamp: params.timestamp,
            encodedDataLength: params.encodedDataLength,
            shouldReportCorbBlocking: params.shouldReportCorbBlocking
          };

          this.loadingFinished.set(params.requestId, loadingData);

          // Process and create final network entry when loading is finished
          this.processNetworkEntry(params.requestId);

          // Check memory usage and flush if necessary
          this.checkAndFlushMemory().catch((error: any) => {
            logger.warn(`NetworkCollector: Error during memory check: ${error.message}`);
          });
        }
      };

      // Listen for network events via CDP (more efficient than page events)
      this.cdpSession.on('Network.requestWillBeSent', this.requestHandler);
      this.cdpSession.on('Network.responseReceived', this.responseHandler);
      this.cdpSession.on('Network.loadingFinished', this.loadingFinishedHandler);

      logger.debug('NetworkCollector: Started CDP-based network collection');
    } catch (error: any) {
      logger.error(`NetworkCollector: Failed to start CDP session: ${error.message}`);
      // Transition back to idle state on error
      await this.transitionState(CollectorState.IDLE);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Stop collecting network data
   */
  async stop(): Promise<void> {
    if (this.getState() !== CollectorState.COLLECTING) {
      logger.debug(`NetworkCollector: Cannot stop - current state is ${this.getState()}`);
      return;
    }

    // Transition to stopping state
    const canTransition = await this.transitionState(CollectorState.STOPPING);
    if (!canTransition) {
      logger.warn('NetworkCollector: Failed to transition to stopping state');
      return;
    }

    try {
      await this.cleanup();
      await this.transitionState(CollectorState.STOPPED);
      logger.debug('NetworkCollector: Stopped CDP-based network collection');
    } catch (error: any) {
      logger.error(`NetworkCollector: Error during stop: ${error.message}`);
      // Force transition to stopped state even on error
      await this.transitionState(CollectorState.STOPPED);
      throw error;
    }
  }

  /**
   * Process network entry when all data is available
   * Only processes API requests and documents, skips static assets
   */
  private processNetworkEntry(requestId: string): void {
    const request = this.requests.get(requestId);
    const response = this.responses.get(requestId);
    const loadingFinished = this.loadingFinished.get(requestId);

    // Only process if we have both request and response data
    if (!request || !response) {
      return;
    }

    // Determine resource type and skip static assets
    const resourceType = this.getResourceType(response.mimeType, request.url);

    // Skip static assets (null return from getResourceType)
    if (resourceType === null) {
      // Clean up skipped entries to save memory
      this.requests.delete(requestId);
      this.responses.delete(requestId);
      this.loadingFinished.delete(requestId);
      return;
    }

    // Calculate timing information
    const startTime = request.startTime;
    const responseTime = response.responseTime;
    const endTime = loadingFinished?.timestamp || responseTime;

    // Calculate total time in milliseconds
    const totalTime = ((endTime - startTime) * 1000);

    // Extract timing details from CDP timing object if available
    const timing = response.timing || {};

    // Format size
    const sizeBytes = response.encodedDataLength || loadingFinished?.encodedDataLength || 0;
    const sizeFormatted = this.formatSize(sizeBytes);

    // Create the formatted network entry for API requests and documents only
    const networkEntry = {
      url: request.url,
      method: request.method,
      status: response.status,
      type: resourceType,
      size: sizeFormatted,
      time: `${totalTime.toFixed(6)} ms`,
      timing: {
        dnsResolution: timing.dnsEnd ? (timing.dnsEnd - timing.dnsStart) : 0,
        tcpConnection: timing.connectEnd ? (timing.connectEnd - timing.connectStart) : 0,
        tlsNegotiation: timing.sslEnd ? (timing.sslEnd - timing.sslStart) : 0,
        timeToFirstByte: timing.receiveHeadersEnd ? (timing.receiveHeadersEnd - timing.sendStart) : 0,
        download: timing.receiveHeadersEnd && endTime ? ((endTime - startTime) * 1000 - (timing.receiveHeadersEnd - timing.sendStart)) : 0
      },
      // Add additional metadata for better filtering
      isApiRequest: resourceType === 'xhr',
      isFailed: response.status >= 400,
      isSlow: totalTime > 1000 // Consider requests over 1 second as slow
    };

    // Update statistics in a streaming fashion
    this.networkStats.total++;
    if (networkEntry.isFailed) {
      this.networkStats.failed++;
    } else {
      this.networkStats.successful++;
    }
    const type = networkEntry.type || 'unknown';
    this.networkStats.byType[type] = (this.networkStats.byType[type] || 0) + 1;
    this.networkStats.totalTransferred += sizeBytes;

    this.networkEntries.push(networkEntry);

    // Clean up processed entries to save memory
    this.requests.delete(requestId);
    this.responses.delete(requestId);
    this.loadingFinished.delete(requestId);
  }

  /**
   * Format size in human readable format
   */
  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * Determine if request is an API request (xfetch) and get its type
   * Only processes API requests and documents, filters out static assets
   */
  private getResourceType(mimeType: string, url: string): string | null {
    // Check if this is an API request first
    if (this.isApiRequest(url, mimeType)) {
      return 'xhr'; // API requests
    }

    // Allow documents (HTML pages)
    if (!mimeType) {
      const extension = url.split('.').pop()?.toLowerCase();
      if (!extension || extension === 'html' || extension === 'htm') {
        return 'document';
      }
      // Filter out static assets by extension
      if (['js', 'css', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'woff', 'woff2', 'ttf', 'otf', 'ico'].includes(extension)) {
        return null; // Skip static assets
      }
      return 'other';
    }

    if (mimeType.includes('text/html')) return 'document';

    // Filter out static assets by MIME type
    if (mimeType.includes('text/css') ||
        mimeType.includes('javascript') ||
        mimeType.includes('application/javascript') ||
        mimeType.includes('image/') ||
        mimeType.includes('font/') ||
        mimeType.includes('application/font') ||
        mimeType.includes('video/') ||
        mimeType.includes('audio/')) {
      return null; // Skip static assets
    }

    // Allow JSON and other API-related content types
    if (mimeType.includes('application/json') ||
        mimeType.includes('application/xml') ||
        mimeType.includes('text/xml') ||
        mimeType.includes('text/plain')) {
      return 'xhr';
    }

    return 'other';
  }

  /**
   * Check if a request is an API request (XHR/fetch)
   */
  private isApiRequest(url: string, mimeType?: string): boolean {
    // Check for common API patterns in URL
    const apiPatterns = [
      '/api/',
      '/v1/',
      '/v2/',
      '/v3/',
      '/graphql',
      '/rest/',
      '/endpoint/',
      '.json',
      '/data/'
    ];

    const urlLower = url.toLowerCase();
    const hasApiPattern = apiPatterns.some(pattern => urlLower.includes(pattern));

    // Check MIME type for API responses
    const isApiMimeType = mimeType && (
      mimeType.includes('application/json') ||
      mimeType.includes('application/xml') ||
      mimeType.includes('text/xml')
    );

    return hasApiPattern || !!isApiMimeType;
  }

  /**
   * Cleanup CDP resources and event listeners with timeout protection
   */
  private async cleanup(): Promise<void> {
    try {
      if (this.cdpSession) {
        // Remove event listeners first
        if (this.requestHandler) {
          this.cdpSession.off('Network.requestWillBeSent', this.requestHandler);
          this.requestHandler = null;
        }

        if (this.responseHandler) {
          this.cdpSession.off('Network.responseReceived', this.responseHandler);
          this.responseHandler = null;
        }

        if (this.loadingFinishedHandler) {
          this.cdpSession.off('Network.loadingFinished', this.loadingFinishedHandler);
          this.loadingFinishedHandler = null;
        }
      }

      // Use base class cleanup for CDP session
      await this.cleanupCDPSession(['Network']);
    } catch (error: any) {
      logger.warn(`NetworkCollector: Error during cleanup: ${error.message}`);
    }
  }



  /**
   * Get collected network data
   * Returns data in the new format with processed network entries
   * Combines in-memory data with data from temporary storage
   */
  async getData(): Promise<{ networkEntries: any[]; stats: any; }> {
    try {
      // Process any remaining unprocessed entries
      this.processRemainingEntries();

      let allNetworkEntries = [...this.networkEntries];

      // Load processed entries from temporary storage if available
      if (this.tempStorage && this.storedEntryFiles.length > 0) {
        for (const filePath of this.storedEntryFiles) {
          try {
            const storedEntries = await this.tempStorage.loadRequests(filePath); // Reusing loadRequests as it reads and parses a JSON array
            allNetworkEntries.push(...storedEntries);
          } catch (error: any) {
            logger.warn(`NetworkCollector: Error loading processed entries from ${filePath}: ${error.message}`);
          }
        }
      }

      return {
        networkEntries: allNetworkEntries,
        stats: this.networkStats
      };
    } catch (error: any) {
      logger.error(`NetworkCollector: Error getting data: ${error.message}`);
      // Fallback to in-memory data only
      return {
        networkEntries: [...this.networkEntries],
        stats: this.networkStats
      };
    }
  }

  /**
   * Process any remaining unprocessed entries
   */
  private processRemainingEntries(): void {
    // Process entries that have request and response but no loading finished event
    const requestIds = Array.from(this.requests.keys());
    for (const requestId of requestIds) {
      if (this.responses.has(requestId)) {
        this.processNetworkEntry(requestId);
      }
    }
  }

  /**
   * Process stored data efficiently without legacy conversion
   * Only processes API requests and documents, skips static assets
   */
  private async processStoredData(requests: any[], responses: any[]): Promise<any[]> {
    const processedEntries: any[] = [];

    requests.forEach(request => {
      const response = responses.find(r => r.requestId === request.requestId);
      if (response) {
        const resourceType = this.getResourceType(response.mimeType, request.url);

        // Skip static assets (null return from getResourceType)
        if (resourceType === null) {
          return;
        }

        const startTime = new Date(request.timestamp).getTime();
        const endTime = new Date(response.timestamp).getTime();
        const totalTime = Math.max(0, endTime - startTime);

        const sizeBytes = response.encodedDataLength || 0;
        const sizeFormatted = this.formatSize(sizeBytes);

        processedEntries.push({
          url: request.url,
          method: request.method,
          status: response.status,
          type: resourceType,
          size: sizeFormatted,
          time: `${totalTime.toFixed(2)} ms`,
          timing: {
            timeToFirstByte: totalTime,
            download: 0
          },
          // Add additional metadata for better filtering
          isApiRequest: resourceType === 'xhr',
          isFailed: response.status >= 400,
          isSlow: totalTime > 1000 // Consider requests over 1 second as slow
        });
      }
    });

    this.requests.clear();
    this.responses.clear();
    this.loadingFinished.clear();
    this.networkEntries = [];
    this.storedEntryFiles = [];
    this.memoryWarningIssued = false;
    this.resetStats();

    // Clear temporary storage
    if (this.tempStorage) {
      await this.tempStorage.cleanup();
    }

    return processedEntries;
  }

  /**
   * Reset streaming statistics
   */
  private resetStats(): void {
    this.networkStats = {
      total: 0,
      successful: 0,
      failed: 0,
      byType: {},
      totalTransferred: 0,
    };
  }

  /**
   * Clear collected data
   * Clears both in-memory data and temporary storage
   */
  async clear(): Promise<void> {
    try {
      this.requests.clear();
      this.responses.clear();
      this.loadingFinished.clear();
      this.networkEntries = [];
      this.storedEntryFiles = [];
      this.memoryWarningIssued = false;
      this.resetStats();

      // Clear temporary storage
      if (this.tempStorage) {
        await this.tempStorage.cleanup();
        // Recreate temp storage for continued use
        if (this.config.enableTemporaryStorage) {
          this.tempStorage = new NetworkTempStorage(this.config, this.testId);
        }
      }

      logger.debug('NetworkCollector: Cleared all collected data');
    } catch (error: any) {
      logger.error(`NetworkCollector: Error clearing data: ${error.message}`);
      // At least clear in-memory data
      this.requests.clear();
      this.responses.clear();
      this.loadingFinished.clear();
      this.networkEntries = [];
      this.storedEntryFiles = [];
    }
  }

  /**
   * Get network statistics including memory management info
   */
  getStats(): any {
    const responseTimes: number[] = [];

    // Calculate response times from processed network entries
    this.networkEntries.forEach(entry => {
      const timeMatch = entry.time.match(/(\d+\.?\d*)/);
      if (timeMatch) {
        responseTimes.push(parseFloat(timeMatch[1]));
      }
    });

    // Also calculate from unprocessed entries
    for (const [requestId, request] of this.requests) {
      const response = this.responses.get(requestId);
      if (response) {
        const requestTime = new Date(request.timestamp).getTime();
        const responseTime = new Date(response.timestamp).getTime();
        responseTimes.push(responseTime - requestTime);
      }
    }

    const baseStats = {
      totalRequests: this.requests.size,
      totalResponses: this.responses.size,
      totalNetworkEntries: this.networkEntries.length,
      isCollecting: this.getState() === CollectorState.COLLECTING,
      state: this.getState(),
      avgResponseTime: responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0,
      failedRequests: Array.from(this.responses.values()).filter(r => r.status >= 400).length,
      cachedRequests: Array.from(this.responses.values()).filter(r => r.fromCache).length,
      ...this.networkStats // Include streaming stats
    };

    // Add memory management statistics
    const memoryStats = {
      inMemoryRequests: this.requests.size,
      inMemoryResponses: this.responses.size,
      inMemoryLoadingFinished: this.loadingFinished.size,
      processedNetworkEntries: this.networkEntries.length,
      storedEntryFiles: this.storedEntryFiles.length,
      maxInMemoryEntries: this.config.maxInMemoryEntries,
      flushThreshold: this.config.flushThreshold,
      memoryWarningThreshold: this.config.memoryWarningThreshold,
      memoryWarningIssued: this.memoryWarningIssued,
      tempStorageEnabled: this.config.enableTemporaryStorage,
      tempStorageStats: this.tempStorage ? this.tempStorage.getStats() : null
    };

    return {
      ...baseStats,
      memoryManagement: memoryStats
    };
  }

  /**
   * Check memory usage and flush to temporary storage if necessary
   * Optimized for better performance and reduced overhead
   */
  private async checkAndFlushMemory(): Promise<void> {
    const totalInMemory = this.requests.size + this.responses.size + this.loadingFinished.size + this.networkEntries.length;

    // Early return if no action needed
    if (totalInMemory < this.config.memoryWarningThreshold) {
      return;
    }

    // Issue memory warning if threshold is reached (only once)
    if (this.config.enableMemoryMonitoring &&
        !this.memoryWarningIssued &&
        totalInMemory >= this.config.memoryWarningThreshold) {
      logger.warn(`NetworkCollector: Memory warning - ${totalInMemory} entries in memory (threshold: ${this.config.memoryWarningThreshold})`);
      this.memoryWarningIssued = true;
    }

    // Flush to temporary storage if threshold is reached
    if (this.config.enableTemporaryStorage &&
        this.tempStorage &&
        totalInMemory >= this.config.flushThreshold) {

      try {
        // Use batch flushing for better performance
        await this.flushToTempStorage();
        logger.debug(`NetworkCollector: Flushed data to temporary storage. In-memory entries: ${this.requests.size + this.responses.size + this.loadingFinished.size + this.networkEntries.length}`);

        // Reset memory warning flag after successful flush
        if (this.requests.size + this.responses.size + this.loadingFinished.size + this.networkEntries.length < this.config.memoryWarningThreshold) {
          this.memoryWarningIssued = false;
        }
      } catch (error: any) {
        logger.error(`NetworkCollector: Error flushing to temporary storage: ${error.message}`);
      }
    }
  }

  /**
   * Flush current in-memory data to temporary storage
   */
  private async flushToTempStorage(): Promise<void> {
    if (!this.tempStorage || !this.config.enableTemporaryStorage) {
      return;
    }

    // Flush only processed network entries for efficiency
    const totalEntries = this.networkEntries.length;
    if (totalEntries > this.config.flushThreshold) {
        logger.debug(`NetworkCollector: Flushing ${totalEntries - this.config.maxInMemoryEntries} processed entries to temp storage.`);
        const entriesToFlush = this.networkEntries.splice(0, totalEntries - this.config.maxInMemoryEntries);
        if (entriesToFlush.length > 0) {
            // Reusing storeRequests as it simply stringifies an array to a file.
            const filePath = await this.tempStorage.storeRequests(entriesToFlush);
            if (filePath) {
                this.storedEntryFiles.push(filePath);
            }
        }
    }
  }
}

/**
 * CDP-based Metrics Collector
 * Optimized metrics collection using Chrome DevTools Protocol
 * Features: Session reuse, batch operations, reduced overhead
 */
class CDPMetricsCollector {
  private page: Page;
  private cdpSession: CDPSession | null = null;
  private state: CollectorState = CollectorState.IDLE;
  private stateLock: Promise<void> = Promise.resolve();
  private domainsEnabled: Set<string> = new Set();

  constructor(page: Page) {
    this.page = page;

    // Listen for page close/disconnect events for automatic cleanup
    // Note: These handlers coordinate with BrowserManager's state machine
    this.page.on('close', () => {
      this.handlePageClose().catch((error: any) => {
        logger.warn(`CDPMetricsCollector: Error during coordinated cleanup on page close: ${error.message}`);
      });
    });

    // Listen for browser disconnect
    if (this.page.browser()) {
      this.page.browser().on('disconnected', () => {
        this.handleBrowserDisconnect().catch((error: any) => {
          logger.warn(`CDPMetricsCollector: Error during coordinated cleanup on browser disconnect: ${error.message}`);
        });
      });
    }
  }

  /**
   * Handle page close with coordinated cleanup
   * @private
   */
  private async handlePageClose(): Promise<void> {
    try {
      logger.debug('CDPMetricsCollector: Handling page close with coordinated cleanup');
      await this.cleanup();
    } catch (error: any) {
      logger.error(`CDPMetricsCollector: Error during page close cleanup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Handle browser disconnect with coordinated cleanup
   * @private
   */
  private async handleBrowserDisconnect(): Promise<void> {
    try {
      logger.debug('CDPMetricsCollector: Handling browser disconnect with coordinated cleanup');
      await this.cleanup();
    } catch (error: any) {
      logger.error(`CDPMetricsCollector: Error during browser disconnect cleanup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Safely transition state with proper locking
   */
  private async transitionState(newState: CollectorState): Promise<boolean> {
    return new Promise((resolve) => {
      this.stateLock = this.stateLock.then(async () => {
        const currentState = this.state;

        // Validate state transition
        const validTransitions: Record<CollectorState, CollectorState[]> = {
          [CollectorState.IDLE]: [CollectorState.COLLECTING, CollectorState.DESTROYING],
          [CollectorState.COLLECTING]: [CollectorState.STOPPING, CollectorState.DESTROYING],
          [CollectorState.STOPPING]: [CollectorState.STOPPED, CollectorState.DESTROYING],
          [CollectorState.STOPPED]: [CollectorState.COLLECTING, CollectorState.DESTROYING],
          [CollectorState.DESTROYING]: [CollectorState.DESTROYED],
          [CollectorState.DESTROYED]: []
        };

        if (validTransitions[currentState].includes(newState)) {
          this.state = newState;
          logger.debug(`CDPMetricsCollector: State transition ${currentState} -> ${newState}`);
          resolve(true);
        } else {
          logger.warn(`CDPMetricsCollector: Invalid state transition ${currentState} -> ${newState}`);
          resolve(false);
        }
      });
    });
  }

  /**
   * Ensure CDP session exists and is connected
   */
  private async ensureCDPSession(): Promise<void> {
    if (!this.cdpSession) {
      this.cdpSession = await this.page.createCDPSession();
    }
  }

  /**
   * Ensure a CDP domain is enabled (with caching to avoid redundant calls)
   */
  private async ensureDomainEnabled(domain: string): Promise<void> {
    if (!this.domainsEnabled.has(domain)) {
      await this.cdpSession!.send(`${domain}.enable` as any);
      this.domainsEnabled.add(domain);
    }
  }

  // Removed clearCache method - no caching to prevent race conditions

  /**
   * Check if CDP session is still connected
   */
  private async isSessionConnected(): Promise<boolean> {
    if (!this.cdpSession) {
      return false;
    }

    try {
      // Try a simple CDP command to check if session is alive
      await withTimeout(
        this.cdpSession.send('Runtime.evaluate', { expression: '1' }),
        1000,
        'session connectivity check'
      );
      return true;
    } catch (error: any) {
      logger.debug(`CDPMetricsCollector: Session connectivity check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Check if error is related to session closure (expected during cleanup)
   */
  private isSessionClosureError(error: any): boolean {
    const message = error.message || '';
    return message.includes('Session closed') ||
           message.includes('Target closed') ||
           message.includes('Protocol error') ||
           message.includes('Connection closed') ||
           message.includes('WebSocket is not open') ||
           message.includes('Session detached');
  }

  /**
   * Collect all metrics in a single optimized call
   * Combines page metrics, performance metrics, and navigation timing
   */
  async collectMetrics(options: MetricsCollectionOptions = {}): Promise<{
    pageMetrics?: any;
    performanceMetrics?: any;
    navigationTiming?: any;
    collectionTime: number;
  }> {
    if (this.state === CollectorState.DESTROYED || this.state === CollectorState.DESTROYING) {
      throw new Error('CDPMetricsCollector has been destroyed');
    }

    // Default options
    const {
      includePageMetrics = true,
      includePerformanceMetrics = true,
      includeNavigationTiming = true
    } = options;

    // Removed caching to prevent race conditions - collect fresh data each time
    const now = Date.now();

    // Transition to collecting state if idle
    if (this.state === CollectorState.IDLE) {
      await this.transitionState(CollectorState.COLLECTING);
    }

    const startTime = now;
    const result: any = {};

    try {
      await this.ensureCDPSession();
      
      // Collect metrics in parallel with enhanced error handling
      const [pageMetrics, performanceData, navigationData] = await Promise.allSettled([
        includePageMetrics ? this.collectPageMetricsInternal() : Promise.resolve(null),
        includePerformanceMetrics ? this.collectPerformanceMetricsInternal() : Promise.resolve(null),
        includeNavigationTiming ? this.collectNavigationTiming() : Promise.resolve(null)
      ]).then(results => [
        results[0].status === 'fulfilled' ? results[0].value : null,
        results[1].status === 'fulfilled' ? results[1].value : null,
        results[2].status === 'fulfilled' ? results[2].value : null
      ]);

      if (includePageMetrics && pageMetrics) {
        result.pageMetrics = pageMetrics;
      }
      
      if (includePerformanceMetrics && performanceData) {
        result.performanceMetrics = performanceData;
      }
      
      if (includeNavigationTiming && navigationData) {
        result.navigationTiming = navigationData;
      }

      result.collectionTime = Date.now() - startTime;

      return result;
    } catch (error: any) {
      logger.warn(`CDPMetricsCollector: Error collecting metrics: ${error.message}`);
      throw error; // Let the caller handle the error
    }
  }

  /**
   * Internal method to collect basic page metrics
   * @private
   */
  private async collectPageMetricsInternal(): Promise<any> {
    try {
      await this.ensureDomainEnabled('Runtime');
      const metrics = await this.page.metrics();
      
      return {
        documents: metrics.Documents || 0,
        nodes: metrics.Nodes || 0,
        jsHeapUsedSize: metrics.JSHeapUsedSize || 0,
        scriptDuration: metrics.ScriptDuration || 0,
        jsHeapTotalSize: metrics.JSHeapTotalSize || 0
      };
    } catch (error: any) {
      logger.warn(`CDPMetricsCollector: Error in collectPageMetricsInternal: ${error.message}`);
      return null;
    }
  }

  /**
   * Internal method to collect performance metrics
   * @private
   */
  private async collectPerformanceMetricsInternal(): Promise<any> {
    try {
      await this.ensureDomainEnabled('Performance');
      const { metrics } = await this.cdpSession!.send('Performance.getMetrics');
      
      // Convert metrics array to object for easier access
      return metrics.reduce((acc: any, metric: any) => {
        acc[metric.name] = metric.value;
        return acc;
      }, {});
    } catch (error: any) {
      logger.warn(`CDPMetricsCollector: Error in collectPerformanceMetricsInternal: ${error.message}`);
      return null;
    }
  }

  /**
   * Internal method to collect navigation timing data
   * @private
   */
  private async collectNavigationTiming(): Promise<any> {
    try {
      return await this.page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = performance.getEntriesByType('paint');
        
        return {
          domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart || 0,
          loadComplete: navigation?.loadEventEnd - navigation?.loadEventStart || 0,
          firstPaint: paint.find((p) => p.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: paint.find((p) => p.name === 'first-contentful-paint')?.startTime || 0,
          timeToFirstByte: navigation.responseStart - navigation.startTime || 0,
          domInteractive: navigation.domInteractive - navigation.startTime || 0,
          domComplete: navigation.domComplete - navigation.startTime || 0
        };
      });
    } catch (error: any) {
      logger.warn(`CDPMetricsCollector: Error in collectNavigationTiming: ${error.message}`);
      return null;
    }
  }


  /**
   * Cleanup CDP session and resources
   * Fixed race condition with proper state management and defensive session handling
   */
  async cleanup(): Promise<void> {
    if (this.state === CollectorState.DESTROYED || this.state === CollectorState.DESTROYING) {
      return;
    }

    // Transition to destroying state
    const canTransition = await this.transitionState(CollectorState.DESTROYING);
    if (!canTransition) {
      logger.warn('CDPMetricsCollector: Failed to transition to destroying state');
      return;
    }

    try {
      // No cache to clear - simplified cleanup

      if (this.cdpSession) {
        try {
          // Check if session is still connected before attempting cleanup
          const isSessionConnected = await this.isSessionConnected();

          if (isSessionConnected) {
            // Disable all enabled domains efficiently
            const disablePromises = Array.from(this.domainsEnabled).map(domain =>
              withTimeout(
                this.cdpSession!.send(`${domain}.disable` as any),
                2000, // Reduced timeout for faster cleanup
                `${domain}.disable`
              ).catch((error: any) => {
                // During cleanup, it's common for the target to close before we can disable domains.
                // We'll specifically check for these errors and log them at a debug level.
                const errorMessage = (error?.message || '').toLowerCase();
                if (errorMessage.includes('target closed') || errorMessage.includes('session detached')) {
                  logger.debug(`CDPMetricsCollector: Could not disable ${domain} domain as target was already closed.`);
                } else {
                  // Log other errors as warnings, as they might be unexpected.
                  logger.warn(`CDPMetricsCollector: Unexpected error disabling ${domain} domain: ${error.message}`);
                }
              })
            );

            await Promise.all(disablePromises);
            this.domainsEnabled.clear();

            // Detach session with timeout protection
            await withTimeout(
              this.cdpSession.detach(),
              3000, // Reduced timeout
              'CDP session detach'
            );
          } else {
            logger.debug('CDPMetricsCollector: Session already disconnected, skipping domain cleanup');
            this.domainsEnabled.clear();
          }

          this.cdpSession = null;
        } catch (error: any) {
          // Check if this is an expected session closure error
          const errorMessage = (error?.message || '').toLowerCase();
          if (errorMessage.includes('target closed') || errorMessage.includes('session detached')) {
            logger.debug(`CDPMetricsCollector: Session was already closed during cleanup: ${error.message}`);
          } else {
            logger.warn(`CDPMetricsCollector: Unexpected error during cleanup: ${error.message}`);
          }
          this.cdpSession = null; // Always clear the reference
        }
      }

      // Remove page event listeners
      try {
        this.page.removeAllListeners('close');
        if (this.page.browser()) {
          this.page.browser().removeAllListeners('disconnected');
        }
      } catch (error: any) {
        logger.warn(`CDPMetricsCollector: Error removing page listeners: ${error.message}`);
      }

      // Final state transition
      await this.transitionState(CollectorState.DESTROYED);
      logger.debug('CDPMetricsCollector: Cleaned up all resources');
    } catch (error: any) {
      logger.error(`CDPMetricsCollector: Error during cleanup: ${error.message}`);
      // Force transition to destroyed state even on error
      await this.transitionState(CollectorState.DESTROYED);
      throw error;
    }
  }
}

/**
 * Convert axe-core results to custom format
 */
function convertAxeResultsToCustomFormat(axeResults: any): any {
  if (!axeResults || !axeResults.violations) {
    return {
      violations: {
        count: 0,
        items: [],
        byType: {},
        bySeverity: {}
      }
    };
  }

  const violations = axeResults.violations || [];
  const items: any[] = [];
  const byType: Record<string, number> = {};
  const bySeverity: Record<string, number> = {};

  // Process each violation
  violations.forEach((violation: any) => {
    const violationType = violation.id;
    const severity = mapImpactToSeverity(violation.impact);

    // Count by type
    byType[violationType] = (byType[violationType] || 0) + (violation.nodes?.length || 1);

    // Count by severity
    bySeverity[severity] = (bySeverity[severity] || 0) + (violation.nodes?.length || 1);

    // Process each node for this violation
    if (violation.nodes && violation.nodes.length > 0) {
      violation.nodes.forEach((node: any, index: number) => {
        items.push({
          type: violationType,
          severity: severity,
          description: violation.description || violation.help,
          element: getElementType(node.html),
          elementHtml: node.html || '',
          expectedResult: violation.help || 'Element should meet accessibility standards',
          impact: getImpactDescription(violation.impact, violationType),
          tags: violation.tags || [],
          target: node.target || [],
          failureSummary: node.failureSummary || 'Accessibility violation detected'
        });
      });
    } else {
      // If no nodes, create a single item
      items.push({
        type: violationType,
        severity: severity,
        description: violation.description || violation.help,
        element: 'unknown',
        elementHtml: '',
        expectedResult: violation.help || 'Element should meet accessibility standards',
        impact: getImpactDescription(violation.impact, violationType),
        tags: violation.tags || [],
        target: [],
        failureSummary: 'Accessibility violation detected'
      });
    }
  });

  return {
    violations: {
      count: items.length,
      items: items,
      byType: byType,
      bySeverity: bySeverity
    },
    // Keep original data for reference
    originalAxeResults: axeResults
  };
}

/**
 * Map axe-core impact levels to severity levels
 */
function mapImpactToSeverity(impact: string): string {
  switch (impact) {
    case 'critical':
      return 'critical';
    case 'serious':
      return 'serious';
    case 'moderate':
      return 'moderate';
    case 'minor':
      return 'minor';
    default:
      return 'moderate';
  }
}

/**
 * Extract element type from HTML string
 */
function getElementType(html: string): string {
  if (!html) return 'unknown';

  const match = html.match(/<(\w+)/);
  if (match) {
    return match[1].toLowerCase();
  }
  return 'generic';
}

/**
 * Get impact description based on violation type
 */
function getImpactDescription(impact: string, violationType: string): string {
  const impactDescriptions: Record<string, string> = {
    'image-alt': 'Screen readers cannot identify the purpose of the image',
    'label': 'Screen readers cannot identify the purpose of the form element',
    'missing-name': 'Screen readers cannot identify the purpose of the element',
    'color-contrast': 'Text may be difficult to read for users with visual impairments',
    'heading-order': 'Screen readers may have difficulty navigating the page structure',
    'link-name': 'Screen readers cannot identify the purpose of the link'
  };

  return impactDescriptions[violationType] || `Accessibility issue with ${impact} impact`;
}

/**
 * Create empty accessibility result structure
 */
function createEmptyAccessibilityResult(reason: string): any {
  return {
    error: `Accessibility analysis not performed: ${reason}`,
    violations: [],
    passes: [],
    incomplete: [],
    inapplicable: [],
    timestamp: new Date().toISOString(),
    url: reason,
    testEngine: {
      name: 'hirafi-fallback',
      version: '1.0.0'
    },
    testRunner: {
      name: 'hirafi-accessibility'
    }
  };
}

/**
 * Professional Accessibility Data Collector using axe-core
 * Provides detailed accessibility violations and compliance information
 * Industry-standard accessibility testing with comprehensive reporting
 * Enhanced with timeout handling and race condition prevention
 */
async function collectAccessibilityData(page: Page, timeoutMs: number = 3000): Promise<any> {
  const startTime = Date.now();

  try {
    // Check if page is still valid before starting
    if (typeof page.isClosed === 'function' && page.isClosed()) {
      logger.warn('collectAccessibilityData: Page is already closed, returning empty result');
      return createEmptyAccessibilityResult('page-closed');
    }

    // Check if axe-core is already injected to avoid re-injection
    let axeInjected = await page.evaluate(() => typeof (window as any).axe?.run === 'function').catch(() => false);

    if (!axeInjected) {
      logger.debug('collectAccessibilityData: axe-core not found, attempting injection.');
      // First, try to inject axe-core from CDN as fallback with timeout
      try {
        // Try to inject axe-core from CDN with timeout
        await Promise.race([
          page.addScriptTag({
            url: 'https://unpkg.com/axe-core@4.8.2/axe.min.js'
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('CDN injection timeout')), 2000)
          )
        ]);
        axeInjected = true;
        logger.debug('collectAccessibilityData: Successfully injected axe-core from CDN');
      } catch (cdnError: any) {
        logger.warn(`collectAccessibilityData: Failed to inject axe-core from CDN: ${cdnError.message}`);

        // Fallback: try to inject from local node_modules if available
        try {
          const axePath = require.resolve('axe-core/axe.min.js');
          await Promise.race([
            page.addScriptTag({ path: axePath }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Local injection timeout')), 1000)
            )
          ]);
          axeInjected = true;
          logger.debug('collectAccessibilityData: Successfully injected axe-core from local node_modules');
        } catch (localError: any) {
          logger.warn(`collectAccessibilityData: Failed to inject axe-core from local: ${localError.message}`);
        }
      }
    } else {
      logger.debug('collectAccessibilityData: Found existing axe-core instance, skipping injection.');
    }

    if (axeInjected) {
      // Run axe-core accessibility analysis with timeout protection
      const remainingTime = timeoutMs - (Date.now() - startTime);
      if (remainingTime <= 500) {
        logger.warn('collectAccessibilityData: Insufficient time remaining for axe analysis, using basic fallback');
        return await collectBasicAccessibilityData(page);
      }

      const accessibilityResults = await Promise.race([
        page.evaluate(async () => {
          // Wait for axe to be available with timeout
          let attempts = 0;
          const maxAttempts = 30; // Reduced from 50 for faster timeout
          while (!(window as any).axe && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 50)); // Reduced from 100ms
            attempts++;
          }

          if (!(window as any).axe) {
            throw new Error('axe-core not available after injection');
          }

          // Configure axe for comprehensive but faster testing
          const axeConfig = {
            rules: {
              // Enable critical rules only for faster execution
              'color-contrast': { enabled: true },
              'image-alt': { enabled: true },
              'label': { enabled: true },
              'heading-order': { enabled: true },
              'landmark-one-main': { enabled: false }, // Disable slower rules
              'page-has-heading-one': { enabled: false },
              'region': { enabled: false }
            },
            // Add performance optimizations
            timeout: 2000, // 2 second timeout for axe analysis
            performanceTimer: true
          };

        // Run axe analysis
        const results = await (window as any).axe.run(document, axeConfig);

        return {
          violations: results.violations.map((violation: any) => ({
            id: violation.id,
            impact: violation.impact,
            description: violation.description,
            help: violation.help,
            helpUrl: violation.helpUrl,
            tags: violation.tags,
            nodes: violation.nodes.map((node: any) => ({
              html: node.html,
              target: node.target,
              failureSummary: node.failureSummary,
              any: node.any,
              all: node.all,
              none: node.none
            }))
          })),
          passes: results.passes.map((pass: any) => ({
            id: pass.id,
            description: pass.description,
            help: pass.help,
            tags: pass.tags,
            nodes: pass.nodes.length
          })),
          incomplete: results.incomplete.map((incomplete: any) => ({
            id: incomplete.id,
            description: incomplete.description,
            help: incomplete.help,
            tags: incomplete.tags,
            nodes: incomplete.nodes.length
          })),
          inapplicable: results.inapplicable.map((inapplicable: any) => ({
            id: inapplicable.id,
            description: inapplicable.description,
            help: inapplicable.help,
            tags: inapplicable.tags
          })),
          timestamp: results.timestamp,
          url: results.url,
          toolOptions: results.toolOptions,
          testEngine: results.testEngine,
          testRunner: results.testRunner,
          testEnvironment: results.testEnvironment
        };
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Accessibility analysis timeout')), remainingTime - 200)
        )
      ]);

      logger.debug(`collectAccessibilityData: axe-core analysis completed with ${(accessibilityResults as any).violations?.length || 0} violations`);

      // Convert to the desired format
      const formattedResults = convertAxeResultsToCustomFormat(accessibilityResults);
      return formattedResults;

    } else {
      // Fallback to basic accessibility checks if axe-core injection fails
      logger.warn('collectAccessibilityData: axe-core injection failed, falling back to basic checks');
      const basicResults = await collectBasicAccessibilityData(page);
      return convertAxeResultsToCustomFormat(basicResults);
    }

  } catch (error: any) {
    logger.warn(`collectAccessibilityData: Error during axe-core analysis: ${error.message}`);

    // Fallback to basic checks if possible.
    try {
      logger.warn('collectAccessibilityData: Falling back to basic accessibility checks after error');
      const basicResults = await collectBasicAccessibilityData(page);
      return convertAxeResultsToCustomFormat(basicResults);
    } catch (fallbackError: any) {
      logger.error(`collectAccessibilityData: Fallback basic checks also failed: ${fallbackError.message}`);
      return convertAxeResultsToCustomFormat(createEmptyAccessibilityResult('error_and_fallback_failed'));
    }
  }
}

/**
 * Basic accessibility data collection as fallback
 * Used when axe-core is not available - optimized for performance
 */
async function collectBasicAccessibilityData(page: Page): Promise<any> {
  try {
    const basicResults = await page.evaluate(() => {
      // More efficient selectors for specific elements
      const images = document.querySelectorAll('img');
      const formElements = document.querySelectorAll('input, textarea, select');
      const violations: any[] = [];
      const passes: any[] = [];

      let missingAltText = 0;
      let missingLabels = 0;
      let missingHeadings = 0;
      let colorContrastIssues = 0;

      // Check images only (more efficient than all elements)
      images.forEach((img, index) => {
        if (!img.getAttribute('alt')) {
          missingAltText++;
          violations.push({
            id: 'image-alt',
            impact: 'critical',
            description: 'Images must have alternate text',
            help: 'Images must have alternate text',
            helpUrl: 'https://dequeuniversity.com/rules/axe/4.8/image-alt',
            tags: ['cat.text-alternatives', 'wcag2a', 'wcag111'],
            nodes: [{
              html: img.outerHTML.substring(0, 200),
              target: [`img:nth-child(${index + 1})`],
              failureSummary: 'Fix this: Element does not have an alt attribute'
            }]
          });
        } else {
          passes.push({
            id: 'image-alt',
            description: 'Images must have alternate text',
            help: 'Images must have alternate text',
            tags: ['cat.text-alternatives', 'wcag2a', 'wcag111'],
            nodes: 1
          });
        }
      });

      // Check form elements only (more efficient)
      formElements.forEach((element, index) => {
        if (!element.getAttribute('aria-label') &&
            !element.getAttribute('placeholder') &&
            !element.getAttribute('title') &&
            !element.getAttribute('aria-labelledby')) {
          missingLabels++;
          violations.push({
            id: 'label',
            impact: 'critical',
            description: 'Form elements must have labels',
            help: 'Form elements must have labels',
            helpUrl: 'https://dequeuniversity.com/rules/axe/4.8/label',
            tags: ['cat.forms', 'wcag2a', 'wcag412'],
            nodes: [{
              html: element.outerHTML.substring(0, 200),
              target: [`${element.tagName.toLowerCase()}:nth-child(${index + 1})`],
              failureSummary: 'Fix this: Form element does not have an accessible name'
            }]
          });
        }
      });

      return {
        violations,
        passes,
        incomplete: [],
        inapplicable: [],
        timestamp: new Date().toISOString(),
        url: window.location.href,
        testEngine: {
          name: 'basic-fallback',
          version: '1.0.0'
        },
        testRunner: {
          name: 'hirafi-basic-accessibility'
        },
        summary: {
          totalElements: images.length + formElements.length,
          imagesChecked: images.length,
          formElementsChecked: formElements.length,
          missingAltText,
          missingLabels,
          missingHeadings,
          colorContrastIssues,
          accessibilityScore: Math.max(0, 100 - (missingAltText + missingLabels + missingHeadings + colorContrastIssues))
        }
      };
    });

    logger.debug('collectAccessibilityData: Basic accessibility analysis completed');
    return basicResults;

  } catch (error: any) {
    logger.error(`collectBasicAccessibilityData: Error: ${error.message}`);
    return {
      error: `Basic accessibility analysis failed: ${error.message}`,
      violations: [],
      passes: [],
      incomplete: [],
      inapplicable: [],
      timestamp: new Date().toISOString(),
      url: 'unknown'
    };
  }
}

/**
 * Master Promise Wrapper for Race Condition Prevention
 * Ensures all metrics collection operations complete before browser session closure
 * Provides comprehensive error handling and timeout management
 */
export async function ensureAllMetricsCompleted(
  page: Page,
  testId: string,
  networkCollector: NetworkCollector | null,
  options: MetricsCollectionOptions = {}
): Promise<EnhancedMetrics> {
  const startTime = Date.now();
  const maxTimeout = options.maxTimeout || 15000; // 15 second maximum timeout

  logger.info(`ensureAllMetricsCompleted: Starting coordinated metrics collection for test ${testId} with ${maxTimeout}ms timeout`);

  try {
    // Pre-flight checks to prevent race conditions
    if (!page || (typeof page.isClosed === 'function' && page.isClosed())) {
      logger.warn(`ensureAllMetricsCompleted: Page is already closed for test ${testId}, returning empty metrics`);
      return createEmptyMetricsResult(testId, 'page-closed');
    }

    const browser = page.browser();
    if (!browser || !browser.connected) {
      logger.warn(`ensureAllMetricsCompleted: Browser is disconnected for test ${testId}, returning empty metrics`);
      return createEmptyMetricsResult(testId, 'browser-disconnected');
    }

    // Use Promise.race with timeout to prevent hanging
    const metricsPromise = collectAllMetrics(page, testId, networkCollector, options);
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Metrics collection timeout after ${maxTimeout}ms`));
      }, maxTimeout);
    });

    const result = await Promise.race([metricsPromise, timeoutPromise]);

    const duration = Date.now() - startTime;
    logger.info(`ensureAllMetricsCompleted: Successfully completed metrics collection for test ${testId} in ${duration}ms`);

    return result;

  } catch (error: any) {
    const duration = Date.now() - startTime;
    logger.error(`ensureAllMetricsCompleted: Error during coordinated metrics collection for test ${testId} after ${duration}ms: ${error.message}`);

    // Return safe fallback instead of throwing to prevent test failure
    return createEmptyMetricsResult(testId, `error: ${error.message}`);
  }
}

/**
 * Create empty metrics result for fallback scenarios
 */
function createEmptyMetricsResult(testId: string, reason: string): EnhancedMetrics {
  logger.warn(`createEmptyMetricsResult: Creating empty metrics for test ${testId}, reason: ${reason}`);

  return {
    timestamp: new Date().toISOString(),
    pageMetrics: {
      documents: 0,
      nodes: 0,
      jsHeapUsedSize: 0,
      jsHeapTotalSize: 0,
      scriptDuration: 0,
      layoutCount: 0,
      recalcStyleCount: 0
    },
    networkData: {
      requests: { total: 0, successful: 0, failed: 0, byType: {} },
      transferred: { total: 0, unit: 'bytes' },
      logs: []
    },
    accessibilityData: {
      violations: {
        count: 0,
        items: [],
        byType: {},
        bySeverity: {}
      },
      passes: [],
      summary: {
        totalElements: 0,
        accessibilityScore: 0
      }
    },
    performanceMetrics: {
      collectionPerformance: {
        totalTime: 0,
        pageMetricsCollection: 0,
        networkDataCollection: 0,
        accessibilityDataCollection: 0,
        reason
      }
    },
    tracingData: {}
  };
}

/**
 * Main optimized metrics collection function
 * Uses CDP for high-performance data collection
 * Returns exact same type as EnhancedMetrics interface
 */
export async function collectAllMetrics(
  page: Page,
  testId: string,
  networkCollector: NetworkCollector | null,
  options: MetricsCollectionOptions = {}
): Promise<EnhancedMetrics> {
  const startTime = Date.now();
  const performanceMetrics = {
    startTime,
    pageValidation: 0,
    pageMetricsCollection: 0,
    networkDataCollection: 0,
    accessibilityDataCollection: 0,
    tracingDataCollection: 0,
    totalTime: 0
  };

  try {
    logger.debug(`collectAllMetrics: Starting optimized CDP-based metrics collection for test ${testId}`);

    // Validate page is still connected before collecting metrics
    const validationStart = Date.now();
    if (!page || page.isClosed()) {
      throw new Error('Page is closed or invalid');
    }

    // Check if browser is still connected
    const browser = page.browser();
    if (!browser || !browser.connected) {
      throw new Error('Browser is disconnected');
    }
    performanceMetrics.pageValidation = Date.now() - validationStart;

    // Extract report settings with correct defaults
    const reportSettings = options.reportSettings || {};
    const shouldCollectPageMetrics = reportSettings.pageMetrics === true;
    const shouldCollectNetworkData = reportSettings.networkData === true;
    const shouldCollectTracingData = reportSettings.tracingData === true;
    const shouldCollectAccessibilityData = reportSettings.accessibilityData === true;

    // Initialize result object with exact EnhancedMetrics structure
    const result: EnhancedMetrics = {
      timestamp: new Date().toISOString()
    };

    // Create CDP metrics collector for efficient data collection
    const cdpCollector = new CDPMetricsCollector(page);

    try {
      // Collect page metrics only if enabled
      if (shouldCollectPageMetrics) {
        const pageMetricsStart = Date.now();
        logger.debug(`collectAllMetrics: Collecting page metrics for test ${testId}`);

        // Use the coordinated collectMetrics method with enhanced error handling
        const metrics = await cdpCollector.collectMetrics({
          includePageMetrics: true,
          includePerformanceMetrics: true,
          includeNavigationTiming: true
        }).catch(error => {
          logger.warn(`collectAllMetrics: Error in CDP metrics collection: ${error.message}`);
          return { pageMetrics: null, performanceMetrics: null, navigationTiming: null, collectionTime: 0 };
        });

        result.pageMetrics = metrics.pageMetrics;
        result.performanceMetrics = {
          ...(metrics.performanceMetrics || {}),
          ...(metrics.navigationTiming || {})
        };

        performanceMetrics.pageMetricsCollection = Date.now() - pageMetricsStart;
        logger.debug(`collectAllMetrics: Page metrics collected in ${performanceMetrics.pageMetricsCollection}ms`);
      } else {
        logger.debug(`collectAllMetrics: Skipping page metrics collection (disabled) for test ${testId}`);
      }

      // Collect network data only if enabled and collector is available
      if (shouldCollectNetworkData && networkCollector) {
        const networkStart = Date.now();
        logger.debug(`collectAllMetrics: Collecting network data for test ${testId}`);
        try {
          const rawNetworkData = await networkCollector.getData();

          // Transform raw network data to EnhancedMetrics format using pre-calculated stats
          if (rawNetworkData && rawNetworkData.networkEntries && rawNetworkData.stats) {
            const { networkEntries, stats } = rawNetworkData;

            result.networkData = {
              requests: {
                total: stats.total,
                successful: stats.successful,
                failed: stats.failed,
                byType: stats.byType
              },
              transferred: {
                total: stats.totalTransferred,
                unit: 'bytes'
              },
              logs: networkEntries // Use networkEntries as logs for backward compatibility
            };
          } else {
            // Fallback for old format or empty data
            result.networkData = {
              requests: { total: 0, successful: 0, failed: 0, byType: {} },
              transferred: { total: 0, unit: 'bytes' },
              logs: []
            };
          }

          performanceMetrics.networkDataCollection = Date.now() - networkStart;
          logger.debug(`collectAllMetrics: Network data collected in ${performanceMetrics.networkDataCollection}ms`);
        } catch (error: any) {
          logger.warn(`collectAllMetrics: Error collecting network data: ${error.message}`);

          // Fallback to empty network data to prevent test failure
          result.networkData = {
            requests: { total: 0, successful: 0, failed: 0, byType: {} },
            transferred: { total: 0, unit: 'bytes' },
            logs: []
          };
          // Log the error instead of adding it to the data structure
          logger.error(`collectAllMetrics: Network data collection failed: ${error.message}`);
          performanceMetrics.networkDataCollection = Date.now() - networkStart;
        }
      } else {
        if (!shouldCollectNetworkData) {
          logger.debug(`collectAllMetrics: Skipping network data collection (disabled) for test ${testId}`);
        } else {
          logger.debug(`collectAllMetrics: Skipping network data collection (no collector) for test ${testId}`);
        }
      }

      // Collect accessibility data only if enabled
      if (shouldCollectAccessibilityData) {
        const accessibilityStart = Date.now();
        logger.debug(`collectAllMetrics: Collecting accessibility data for test ${testId}`);
        try {
          // Calculate remaining time for accessibility collection
          const elapsedTime = Date.now() - startTime;
          const remainingTime = Math.max(2000, 8000 - elapsedTime); // At least 2 seconds, max 8 seconds total
          result.accessibilityData = await collectAccessibilityData(page, remainingTime);
          performanceMetrics.accessibilityDataCollection = Date.now() - accessibilityStart;
          logger.debug(`collectAllMetrics: Accessibility data collected in ${performanceMetrics.accessibilityDataCollection}ms`);
        } catch (accessibilityError: any) {
          logger.warn(`collectAllMetrics: Error collecting accessibility data: ${accessibilityError.message}`);

          logger.warn(`collectAllMetrics: Error collecting accessibility data: ${accessibilityError.message}`);

          // Fallback to error structure matching EnhancedMetrics format
          result.accessibilityData = {
            violations: {
              count: 0,
              items: [],
              byType: {},
              bySeverity: {}
            },
            passes: [],
            summary: {
              totalElements: 0,
              accessibilityScore: 0
            }
          };
          // Log the error instead of adding it to the data structure
          logger.error(`collectAllMetrics: Accessibility analysis failed: ${accessibilityError.message}`);
          performanceMetrics.accessibilityDataCollection = Date.now() - accessibilityStart;
        }
      } else {
        logger.debug(`collectAllMetrics: Skipping accessibility data collection (disabled) for test ${testId}`);
      }

      // Collect tracing data only if enabled
      if (shouldCollectTracingData) {
        logger.debug(`collectAllMetrics: Collecting tracing data for test ${testId}`);
        // Tracing data collection is not implemented yet - returning empty structure
        result.tracingData = {};
        logger.debug(`collectAllMetrics: Tracing data collected in ${Date.now() - startTime}ms`);
      } else {
        logger.debug(`collectAllMetrics: Skipping tracing data collection (disabled) for test ${testId}`);
      }

    } finally {
      // Always cleanup CDP resources
      await cdpCollector.cleanup();
    }

    // Calculate final performance metrics
    performanceMetrics.totalTime = Date.now() - startTime;

    // Add performance metrics to existing performanceMetrics if available
    if (result.performanceMetrics) {
      (result.performanceMetrics as any).collectionPerformance = performanceMetrics;
    }

    logger.info(`collectAllMetrics: Completed optimized metrics collection for test ${testId} in ${performanceMetrics.totalTime}ms - Page: ${performanceMetrics.pageMetricsCollection}ms, Network: ${performanceMetrics.networkDataCollection}ms, Accessibility: ${performanceMetrics.accessibilityDataCollection}ms`);

    return result;

  } catch (error: any) {
    const totalTime = Date.now() - startTime;
    logger.error(`collectAllMetrics: Error collecting metrics for test ${testId} after ${totalTime}ms: ${error.message}`);
    throw error;
  }
}

// Removed deprecated Web Vitals monitoring functions
// These functions were causing unnecessary code expansion and race conditions
// Web Vitals data is now derived from existing performance metrics in the UI layer