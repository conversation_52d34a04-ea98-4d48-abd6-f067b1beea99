/**
 * Device Provider Types and Core Validation
 * Unified types and utilities for device provider selection
 */

/**
 * Supported device providers for Android testing
 */
export type DeviceProvider = "sauceLabs" | "testinium";

/**
 * Device provider configuration interface
 */
export interface DeviceProviderConfig {
  /** The selected device provider */
  provider: DeviceProvider;

  /** Whether the provider is active/enabled */
  active: boolean;

  /** Provider-specific configuration */
  config?: Record<string, any>;
}

/**
 * Device provider selection state
 */
export interface DeviceProviderState {
  /** Currently selected provider */
  selectedProvider: DeviceProvider | null;

  /** Available providers */
  availableProviders: DeviceProvider[];

  /** Whether provider selection is locked */
  locked?: boolean;
}

/**
 * Unified validation result interface
 */
export interface DeviceProviderValidationResult {
  /** Whether the selection is valid */
  isValid: boolean;

  /** Error message if invalid */
  error?: string;

  /** Warning message if applicable */
  warning?: string;

  /** Selected provider if valid */
  selectedProvider?: DeviceProvider;
}

/**
 * Core Device Provider Utilities
 * Consolidated utility functions for device provider management
 */
export class DeviceProviderUtils {
  /**
   * Validate that only one provider is selected
   */
  static validateMutualExclusivity(
    sauceLabsActive: boolean,
    testiniumActive: boolean,
  ): DeviceProviderValidationResult {
    const activeCount = [sauceLabsActive, testiniumActive].filter(
      Boolean,
    ).length;

    if (activeCount === 0) {
      return {
        isValid: false,
        error: "At least one device provider must be selected",
      };
    }

    if (activeCount > 1) {
      return {
        isValid: false,
        error: "Only one device provider can be selected at a time",
      };
    }

    return { isValid: true };
  }

  /**
   * Get the active provider from boolean flags
   */
  static getActiveProvider(
    sauceLabsActive: boolean,
    testiniumActive: boolean,
  ): DeviceProvider | null {
    if (sauceLabsActive && !testiniumActive) return "sauceLabs";
    if (testiniumActive && !sauceLabsActive) return "testinium";
    return null;
  }

  /**
   * Create provider state from active flags
   */
  static createProviderState(
    sauceLabsActive: boolean,
    testiniumActive: boolean,
  ): DeviceProviderState {
    return {
      selectedProvider: this.getActiveProvider(
        sauceLabsActive,
        testiniumActive,
      ),
      availableProviders: ["sauceLabs", "testinium"],
      locked: false,
    };
  }

  /**
   * Validate device selection against provider
   */
  static validateDeviceSelection(
    devices: Array<{ provider: string }>,
    selectedProvider: DeviceProvider | null,
  ): DeviceProviderValidationResult {
    if (!selectedProvider) {
      return {
        isValid: false,
        error: "No device provider selected",
      };
    }

    const invalidDevices = devices.filter(
      (device) => device.provider !== selectedProvider,
    );

    if (invalidDevices.length > 0) {
      return {
        isValid: false,
        error: `Selected devices must be from the ${selectedProvider} provider only`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate provider value
   */
  static isValidProvider(provider: string): provider is DeviceProvider {
    return provider === "sauceLabs" || provider === "testinium";
  }

  /**
   * Comprehensive validation for Android run data including device and app selection
   */
  static validateAndroidRun(runData: any): DeviceProviderValidationResult {
    // Only validate Android runs
    if (runData.platform !== "android") {
      return { isValid: true };
    }

    // Check if environment settings exist
    if (!runData.environment) {
      return {
        isValid: false,
        error: "Android testleri için ortam ayarları gereklidir",
      };
    }

    const deviceProvider =
      runData.deviceProvider || runData.environment.deviceProvider;

    if (!deviceProvider) {
      return {
        isValid: false,
        error: "Android testleri için cihaz sağlayıcısı belirtilmelidir",
      };
    }

    if (!this.isValidProvider(deviceProvider)) {
      return {
        isValid: false,
        error: `Geçersiz cihaz sağlayıcısı: ${deviceProvider}. 'sauceLabs' veya 'testinium' olmalıdır`,
      };
    }

    // Validate SauceLabs specific requirements
    if (deviceProvider === "sauceLabs") {
      const sauceLabsConfig = runData.environment.sauceLabs;
      
      if (!sauceLabsConfig) {
        return {
          isValid: false,
          error: "SauceLabs sağlayıcısı kullanılırken SauceLabs yapılandırması gereklidir",
        };
      }

      // Check if devices are selected
      if (!sauceLabsConfig.selectedDevices || sauceLabsConfig.selectedDevices.length === 0) {
        return {
          isValid: false,
          error: "SauceLabs testleri için en az bir cihaz seçilmelidir",
        };
      }

      // Check if app is selected
      if (!sauceLabsConfig.selectedApp || !sauceLabsConfig.selectedApp.id) {
        return {
          isValid: false,
          error: "SauceLabs testleri için bir APK dosyası seçilmelidir",
        };
      }
    }

    // Validate Testinium specific requirements
    if (deviceProvider === "testinium") {
      const testiniumConfig = runData.environment.testinium;
      
      if (!testiniumConfig) {
        return {
          isValid: false,
          error: "Testinium sağlayıcısı kullanılırken Testinium yapılandırması gereklidir",
        };
      }

      // Check if devices are selected
      if (!testiniumConfig.selectedDevices || testiniumConfig.selectedDevices.length === 0) {
        return {
          isValid: false,
          error: "Testinium testleri için en az bir cihaz seçilmelidir",
        };
      }

      // Check if app is selected
      if (!testiniumConfig.selectedApp || !testiniumConfig.selectedApp.id) {
        return {
          isValid: false,
          error: "Testinium testleri için bir APK dosyası seçilmelidir",
        };
      }
    }

    return {
      isValid: true,
      selectedProvider: deviceProvider,
    };
  }
}

/**
 * Default device provider configuration
 */
export const DEFAULT_DEVICE_PROVIDER_CONFIG: DeviceProviderConfig = {
  provider: "sauceLabs",
  active: true,
  config: {},
};
