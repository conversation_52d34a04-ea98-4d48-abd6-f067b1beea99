/**
 * Report Service
 * Test raporları için servis modülü
 */

import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import {
  db,
  reportsCollection,
  ensureMongoDBConnection,
  isoStringToMongoDate
} from './dbConnection.js';
import { Document, Sort } from 'mongodb';
import { getUserById } from './userService.js';
import { getRunById } from './runService.js';
import { getScenarioById } from './scenarioService.js';
import { sendReportToTestRail } from './testRailService.js';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';

/**
 * Senaryo ID'sine göre raporları getirir
 */
export async function getReportsByScenarioId(scenarioId: string): Promise<{
  success: boolean;
  reports?: any[];
  count?: number;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure reports collection is initialized
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    // Find all reports for the given scenario ID
    const reports = await reportsCollection
      .find({ scenarioId })
      .sort({ date: -1 })
      .toArray();

    return {
      success: true,
      reports,
      count: reports.length
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching reports for scenario ID ${scenarioId}:`, error);
    return {
      success: false,
      message: `Error fetching reports: ${error.message}`
    };
  }
}

/**
 * Create a new report and return ID
 */
export async function createReport(reportData: any): Promise<{
  success: boolean;
  reportId?: string;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure reports collection is initialized
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    const reportId = reportData.id || uuidv4();
    const now = new Date();

    // Prepare the report for MongoDB storage
    const report = prepareReportForDb(reportId, now, reportData);

    // Check for pending video URL in Redis before creating the report - Atomic operation
    try {
      const { redisConnection } = await import('../../services/redis/redisConnection.js');
      const redis = redisConnection.getClient();

      if (!redis) {
        logger.warn(`[MONGODB] Redis client not available, skipping pending video URL check for report ${reportId}`);
      } else {
        const pendingKey = `pending-video-url:${reportId}`;

        // Use atomic GET and DEL operation to prevent race conditions
        const multi = redis.multi();
        multi.get(pendingKey);
        multi.del(pendingKey);
        const results = await multi.exec();

        if (results && results[0] && results[0][1]) {
          const pendingVideoUrl = results[0][1] as string;
          report.videoUrl = pendingVideoUrl;
          logger.info(`[MONGODB] Applied and cleaned up pending video URL for report ${reportId}: ${pendingVideoUrl}`);
        } else {
          logger.debug(`[MONGODB] No pending video URL found in Redis for report ${reportId}`);
        }
      }
    } catch (redisError: any) {
      logger.warn(`[MONGODB] Could not check for pending video URL for report ${reportId}: ${redisError.message}`);
    }

    // Enhance user name if missing (fallback only)
    if (report.executedUser && !report.executedUserName) {
      try {
        const userResult = await getUserById(report.executedUser);
        if (userResult.success && userResult.data) {
          report.executedUserName = userResult.data.name || '';
          logger.info(`[MONGODB] Added missing executedUserName ${report.executedUserName} for user ${report.executedUser}`);
        }
      } catch (userError) {
        logger.warn(`[MONGODB] Error getting user name for ${report.executedUser}: ${userError}`);
      }
    }

    // Enhance run metadata if missing (fallback only)
    if (report.runId && (!report.teamId || !report.companyId || !report.platform)) {
      try {
        const runResult = await getRunById(report.runId);
        if (runResult.success && runResult.run) {
          if (!report.teamId) report.teamId = runResult.run.teamId || null;
          if (!report.companyId) report.companyId = runResult.run.companyId || null;
          if (!report.platform) report.platform = runResult.run.platform || 'web';
          logger.info(`[MONGODB] Enhanced missing metadata from run ${report.runId}`);
        }
      } catch (runError) {
        logger.warn(`[MONGODB] Error getting run info for ${report.runId}: ${runError}`);
      }
    }

    // Insert/update the report in MongoDB
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    // Log critical fields for debugging
    logger.info(`[MONGODB] Creating/updating report with ID: ${reportId}, runId: ${report.runId || 'none'}, executionId: ${report.executionId || 'none'}, executedUser: ${report.executedUser || 'none'}, executedUserName: ${report.executedUserName || 'none'}, testiniumSessionId: ${(report as any).testiniumSessionId || 'none'}`);

    await reportsCollection.replaceOne(
      { id: reportId },
      report,
      { upsert: true }
    );



    // Check if the scenario has TestRail integration enabled and sync the report to TestRail
    if (report.scenarioId) {
      try {
        // Get the scenario to check if it has TestRail integration
        const scenarioResult = await getScenarioById(report.scenarioId);

        if (scenarioResult.success && scenarioResult.scenario) {
          const scenario = scenarioResult.scenario;

          // Use unified test management system for automatic provider detection and result submission
          try {
            const { sendReportToUnifiedTestManagement } = await import('./unifiedTestManagementService.js');

            // Attempt to send report to unified test management system
            // This will automatically detect the provider and submit results if sync is enabled
            sendReportToUnifiedTestManagement(reportId, report.runId)
              .then(results => {
                if (results.length > 0) {
                  const successfulResults = results.filter(r => r.success);
                  const failedResults = results.filter(r => !r.success);

                  if (successfulResults.length > 0) {
                    logger.info(`[MONGODB] Successfully synced report ${reportId} to test management system. Provider: ${successfulResults[0].provider}, Results: ${successfulResults.length}/${results.length}`);
                  }

                  if (failedResults.length > 0) {
                    logger.warn(`[MONGODB] Some test management sync operations failed for report ${reportId}. Failed: ${failedResults.length}/${results.length}`);
                    failedResults.forEach(result => {
                      logger.warn(`[MONGODB] Failed sync: ${result.provider} - ${result.message}`);
                    });
                  }
                } else {
                  logger.info(`[MONGODB] No test management integration found or sync disabled for scenario ${report.scenarioId}`);
                }
              })
              .catch(error => {
                logger.error(`[MONGODB] Error syncing report ${reportId} to unified test management: ${error.message}`);
              });
          } catch (importError: any) {
            logger.error(`[MONGODB] Error importing unified test management service: ${importError.message}`);

            // Fallback to legacy TestRail integration for backward compatibility
            if (scenario.testrailIntegration &&
                scenario.testrailIntegration.sync === true &&
                scenario.testrailIntegration.caseIds &&
                scenario.testrailIntegration.caseIds.length > 0) {

              logger.info(`[MONGODB] Falling back to legacy TestRail integration for scenario ${report.scenarioId}`);

              const testCaseId = scenario.testrailIntegration.caseIds[0];
              const userId = report.executedUser || report.userId || scenario.userId;

              if (userId && testCaseId) {
                const { sendReportToTestRail } = await import('./testRailService.js');
                sendReportToTestRail(reportId, userId, testCaseId, report.runId)
                  .then(testrailResult => {
                    if (testrailResult.success) {
                      logger.info(`[MONGODB] Legacy TestRail sync successful for report ${reportId}`);
                    } else {
                      logger.warn(`[MONGODB] Legacy TestRail sync failed for report ${reportId}: ${testrailResult.message}`);
                    }
                  })
                  .catch(error => {
                    logger.error(`[MONGODB] Legacy TestRail sync error for report ${reportId}: ${error.message}`);
                  });
              } else {
                logger.warn(`[MONGODB] Cannot sync report ${reportId} to TestRail: Missing userId or testCaseId`);
              }
            }
          }
        }
      } catch (error) {
        logger.error(`[MONGODB] Error checking TestRail integration for scenario ${report.scenarioId}:`, error);
      }
    }

    return {
      success: true,
      reportId: reportId
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error creating report:`, error);
    return {
      success: false,
      message: `Error creating report: ${error.message}`
    };
  }
}

/**
 * Prepare report data for MongoDB storage
 *
 * This function now expects a clean TestReportDTO and performs minimal processing.
 * The heavy lifting of data transformation is done in centralResultProcessor.
 */
function prepareReportForDb(reportId: string, now: Date, reportData: any): any {
  // The reportData should already be a clean TestReportDTO
  // We only add MongoDB-specific metadata here
  const report = {
    ...reportData,
    id: reportId,
    date: now,
    createdAt: now,
    updatedAt: now
  };

  // Basic validation and defaults
  if (!report.steps) {
    report.steps = [];
  }

  if (!report.summary) {
    report.summary = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: 0
    };
  }

  // Log warnings for missing important fields
  if (!report.enhancedMetrics) {
    logger.warn(`[MONGODB] Report ${reportId} does not contain enhancedMetrics structure`);
  }

  if (!report.platform) {
    logger.warn(`[MONGODB] Report ${reportId} does not include platform field`);
  }

  return report;
}

/**
 * ID'ye göre rapor getirir
 */
export async function getReportById(reportId: string): Promise<{
  success: boolean;
  report?: any;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure reports collection is initialized
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    logger.info(`[MONGODB] Fetching report with ID: ${reportId}`);

    // Find the report
    const report = await reportsCollection.findOne({ id: reportId });

    if (!report) {
      logger.info(`[MONGODB] Report with ID ${reportId} not found`);
      return {
        success: false,
        message: `Report with ID ${reportId} not found`
      };
    }



    return {
      success: true,
      report
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching report with ID ${reportId}:`, error);
    return {
      success: false,
      message: `Error fetching report: ${error.message}`
    };
  }
}

/**
 * Tüm raporları getirir
 */
export async function getAllReports(options: {
  limit?: number;
  skip?: number;
  scenarioId?: string;
  userId?: string;
  companyId?: string;
  teamId?: string;
  startDate?: string;
  endDate?: string;
  projection?: Document;
  sort?: Sort;
} = {}): Promise<{
  success: boolean;
  reports?: any[];
  count?: number;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure reports collection is initialized
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    const {
      limit = 10,
      skip = 0,
      scenarioId,
      userId,
      companyId,
      teamId,
      startDate,
      endDate,
      projection = { steps: 0 }, // Varsayılan olarak steps alanını çıkar
      sort = { date: -1 } // Varsayılan olarak tarihe göre azalan sıralama
    } = options;

    logger.info(`[MONGODB] Fetching reports with options:`, options);

    // Build query based on options
    const query: any = {};

    if (scenarioId) {
      query.scenarioId = scenarioId;
    }

    if (userId) {
      query.userId = userId;
    }

    // Company/Team filtering for data isolation
    if (companyId && teamId) {
      // User can see reports from their company and team, or their own reports
      query.$or = [
        { $and: [{ companyId }, { teamId }] },
        { userId }
      ];
    } else if (companyId) {
      // If only companyId is provided, filter by company
      query.companyId = companyId;
    } else if (teamId) {
      // If only teamId is provided, filter by team
      query.teamId = teamId;
    }

    // Add date range filtering if provided
    if (startDate || endDate) {
      query.date = {};

      if (startDate) {
        query.date.$gte = isoStringToMongoDate(startDate);
      }

      if (endDate) {
        query.date.$lte = isoStringToMongoDate(endDate);
      }
    }

    // Count total matching documents
    const total = await reportsCollection.countDocuments(query);

    // Get reports with pagination, custom sorting and projection
    const reports = await reportsCollection
      .find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .project(projection)
      .toArray();



    return {
      success: true,
      reports,
      count: total
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching reports:`, error);
    return {
      success: false,
      message: `Error fetching reports: ${error.message}`
    };
  }
}

/**
 * Raporu ID'ye göre günceller - Atomic operation with retry logic
 */
export async function updateReportById(
  reportId: string,
  updateData: any
): Promise<{
  success: boolean;
  message: string;
  report?: any;
}> {
  try {
    // Import operation queue
    const { operationQueue } = await import('../../utils/operationQueue.js');

    // Generate transaction ID for duplicate detection
    const transactionId = `update-report-${reportId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;

    logger.info(`[MONGODB] Updating report with ID: ${reportId} (transaction: ${transactionId})`);

    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `update-report-${reportId}-${transactionId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure reports collection is initialized
        if (!reportsCollection) {
          return { success: false, message: "Reports collection not initialized" };
        }

        // Prevent updating the ID and other protected fields
        const safeUpdateData = { ...updateData };
        delete safeUpdateData.id;
        delete safeUpdateData.createdAt;
        delete safeUpdateData.date;

        // Prepare atomic update operation
        const updateOperation: any = {};

        // Handle MongoDB operators if provided
        if (safeUpdateData.$set || safeUpdateData.$inc || safeUpdateData.$push || safeUpdateData.$pull) {
          // Use provided operators
          if (safeUpdateData.$set) updateOperation.$set = safeUpdateData.$set;
          if (safeUpdateData.$inc) updateOperation.$inc = safeUpdateData.$inc;
          if (safeUpdateData.$push) updateOperation.$push = safeUpdateData.$push;
          if (safeUpdateData.$pull) updateOperation.$pull = safeUpdateData.$pull;

          // Add updatedAt to $set
          if (updateOperation.$set) {
            updateOperation.$set.updatedAt = new Date();
          } else {
            updateOperation.$set = { updatedAt: new Date() };
          }

          // Remove operators from updateData to avoid duplication
          const filteredUpdateData = { ...safeUpdateData };
          delete filteredUpdateData.$set;
          delete filteredUpdateData.$inc;
          delete filteredUpdateData.$push;
          delete filteredUpdateData.$pull;

          // Add remaining fields to $set
          if (Object.keys(filteredUpdateData).length > 0) {
            updateOperation.$set = { ...updateOperation.$set, ...filteredUpdateData };
          }
        } else {
          // Regular update with $set
          updateOperation.$set = {
            ...safeUpdateData,
            updatedAt: new Date()
          };
        }

        // Perform update with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await reportsCollection.findOneAndUpdate(
              { id: reportId },
              updateOperation,
              { returnDocument: 'after' }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`[MONGODB] Report update failed for reportId ${reportId} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`[MONGODB] Retrying report update for reportId ${reportId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!result || !result.value) {
          logger.info(`[MONGODB] Report with ID ${reportId} not found`);
          return {
            success: false,
            message: `Report with ID ${reportId} not found`
          };
        }

        logger.info(`[MONGODB] Report with ID ${reportId} updated successfully`);

        return {
          success: true,
          message: "Report updated successfully",
          report: result.value
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error updating report with ID ${reportId}:`, error);
    return {
      success: false,
      message: `Error updating report: ${error.message}`
    };
  }
}

/**
 * Raporun TestRail durum bilgisini günceller - Atomic operation with retry logic
 * @param reportId Report ID
 * @param testRailStatus TestRail status object with testCaseId, runId, status, etc.
 */
export async function updateReportTestRailStatus(
  reportId: string,
  testRailStatus: any
): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Import operation queue
    const { operationQueue } = await import('../../utils/operationQueue.js');

    // Generate transaction ID for duplicate detection
    const transactionId = `update-testrail-${reportId}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    logger.info(`[MONGODB] Updating TestRail status for report with ID: ${reportId} (transaction: ${transactionId})`);

    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `update-testrail-${reportId}-${transactionId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Ensure reports collection is initialized
        if (!reportsCollection) {
          return { success: false, message: "Reports collection not initialized" };
        }

        // Perform update with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await reportsCollection.updateOne(
              { id: reportId },
              {
                $set: {
                  testRailStatus,
                  updatedAt: new Date()
                }
              }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`[MONGODB] TestRail status update failed for reportId ${reportId} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`[MONGODB] Retrying TestRail status update for reportId ${reportId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!result || result.matchedCount === 0) {
          logger.info(`[MONGODB] Report with ID ${reportId} not found`);
          return {
            success: false,
            message: `Report with ID ${reportId} not found`
          };
        }

        logger.info(`[MONGODB] TestRail status for report with ID ${reportId} updated successfully`);

        return {
          success: true,
          message: "TestRail status updated successfully"
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error updating TestRail status for report with ID ${reportId}:`, error);
    return {
      success: false,
      message: `Error updating TestRail status: ${error.message}`
    };
  }
}

/**
 * Raporu ID'ye göre siler
 */
export async function deleteReportById(reportId: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure reports collection is initialized
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    logger.info(`[MONGODB] Deleting report with ID: ${reportId}`);

    // Delete the report
    const result = await reportsCollection.deleteOne({ id: reportId });

    if (result.deletedCount === 0) {
      logger.info(`[MONGODB] Report with ID ${reportId} not found`);
      return {
        success: false,
        message: `Report with ID ${reportId} not found`
      };
    }

    logger.info(`[MONGODB] Report with ID ${reportId} deleted successfully`);

    return {
      success: true,
      message: "Report deleted successfully"
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error deleting report with ID ${reportId}:`, error);
    return {
      success: false,
      message: `Error deleting report: ${error.message}`
    };
  }
}

/**
 * Tüm raporların özet bilgilerini getirir
 */
export async function getReportsSummary(options: {
  limit?: number;
  skip?: number;
  scenarioId?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
  sort?: Sort;
} = {}): Promise<{
  success: boolean;
  reports?: any[];
  count?: number;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure reports collection is initialized
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    const {
      limit = 50,
      skip = 0,
      scenarioId,
      userId,
      startDate,
      endDate,
      sort = { createdAt: -1 } // Varsayılan olarak oluşturma tarihine göre azalan sıralama
    } = options;

    logger.info(`[MONGODB] Fetching report summaries with options: ${JSON.stringify(options)}`);

    // Build query based on options
    const query: any = {};

    if (scenarioId) {
      query.scenarioId = scenarioId;
      logger.debug(`[MONGODB] Added scenarioId filter: ${scenarioId}`);
    }

    if (userId) {
      query.userId = userId;
      logger.debug(`[MONGODB] Added userId filter: ${userId}`);
    }

    // Add date range filtering if provided
    if (startDate || endDate) {
      query.createdAt = {};

      if (startDate) {
        query.createdAt.$gte = isoStringToMongoDate(startDate);
        logger.debug(`[MONGODB] Added startDate filter using createdAt: ${startDate} -> ${query.createdAt.$gte}`);
      }

      if (endDate) {
        query.createdAt.$lte = isoStringToMongoDate(endDate);
        logger.debug(`[MONGODB] Added endDate filter using createdAt: ${endDate} -> ${query.createdAt.$lte}`);
      }
    }

    // Log the constructed query
    logger.info(`[MONGODB] Query for reports: ${JSON.stringify(query)}`);

    // Sadece özet bilgileri içeren projeksiyon
    const projection = {
      _id: 1, // MongoDB ID'si
      id: 1,
      name: 1,
      scenarioId: 1,
      scenarioTitle: 1,
      status: 1,
      result: 1,
      duration: 1,
      createdAt: 1,
      startTime: 1,
      endTime: 1,
      summary: 1,
      environment: 1,
      platform: 1, // Include platform field in summary
      steps: {
        $slice: 0 // Step detaylarını alma, sadece sayıyı hesaplamak için
      },
      userId: 1,
      browser: 1,
      device: 1,
      testRailStatus: 1
    };

    // Use aggregation pipeline for atomic count and data retrieval to prevent race conditions
    const aggregationPipeline = [
      { $match: query },
      {
        $facet: {
          totalCount: [{ $count: "count" }],
          reports: [
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            { $project: projection }
          ]
        }
      }
    ];

    const aggregationResult = await reportsCollection.aggregate(aggregationPipeline).toArray();
    const result = aggregationResult[0];

    const total = result.totalCount.length > 0 ? result.totalCount[0].count : 0;
    const reports = result.reports || [];

    logger.info(`[MONGODB] Reports matching query: ${total} reports found`);

    // Eğer hiç rapor bulunamadıysa, userId filtresi olmadan deneyelim
    if (total === 0 && userId) {
      logger.warn(`[MONGODB] No reports found with userId: ${userId}, trying without userId filter`);
      const queryWithoutUser = {...query};
      delete queryWithoutUser.userId;

      const totalWithoutUser = await reportsCollection.countDocuments(queryWithoutUser);
      logger.info(`[MONGODB] Reports without userId filter: ${totalWithoutUser}`);

      // Eğer userId olmadan kayıt bulunursa, userId sütun ismiyle ilgili bir sorun olabilir
      if (totalWithoutUser > 0) {
        // Örnekleme için bir kayıt al
        const sampleReport = await reportsCollection.findOne({}, { projection: { _id: 1, id: 1, userId: 1, createdAt: 1 } });
        logger.info(`[MONGODB] Sample report fields: ${JSON.stringify(sampleReport)}`);
      }
    }



    // İlk raporu log olarak göster (eğer varsa)
    if (reports.length > 0) {
      const sampleFields = {
        _id: reports[0]._id,
        id: reports[0].id,
        name: reports[0].name,
        status: reports[0].status,
        userId: reports[0].userId,
        createdAt: reports[0].createdAt
      };
      logger.info(`[MONGODB] Sample report fields: ${JSON.stringify(sampleFields)}`);
    }

    // İşlem sonrası minimal veri hazırlama
    const summaryReports = reports.map((report: any) => {
      // Step sayılarını hesapla
      const stepInfo = report.summary || { total: 0, passed: 0, failed: 0 };
      const totalSteps = stepInfo.total || 0;
      const passedSteps = stepInfo.passed || 0;
      const failedSteps = stepInfo.failed || 0;

      // Başarı oranını hesapla
      const passRate = totalSteps > 0 ? Math.round((passedSteps / totalSteps) * 100) : 100;

      // Süre bilgisini hesapla
      let duration = report.duration;
      if (!duration && report.startTime && report.endTime) {
        duration = (report.endTime - report.startTime) / 1000; // saniye cinsinden süre
      }

      // Browser/device/environment bilgilerini varsayılan değerlerle düzenle
      const browser = report.browser || 'Chrome';
      const device = report.device || 'Desktop';

      // Environment değeri obje veya boş ise string'e dönüştür
      let environment = 'Linux';
      if (report.environment) {
        if (typeof report.environment === 'string') {
          environment = report.environment;
        } else if (typeof report.environment === 'object') {
          // Eğer boş obje değilse ve içeriği varsa JSON stringify yap
          if (Object.keys(report.environment).length > 0) {
            try {
              environment = JSON.stringify(report.environment);
            } catch (e) {
              environment = 'Linux';
            }
          }
        }
      }

      // Tarih bilgisi için mutlaka createdAt kullan
      const date = report.createdAt;

      return {
        id: report.id,
        name: report.name || `Test ${report.id}`,
        date: date ? new Date(date).toISOString() : null,
        scenarioId: report.scenarioId,
        scenarioTitle: report.scenarioTitle,
        status: report.status || 'unknown',
        result: report.result?.status || report.status || 'unknown',
        duration: duration ? `${Math.round(duration)}s` : 'N/A',
        passRate,
        totalTests: totalSteps,
        passed: passedSteps,
        failed: failedSteps,
        skipped: (totalSteps - passedSteps - failedSteps) || 0,
        environment,
        triggeredBy: report.executedUserName || report.executedUser || report.userId || 'System',
        browser,
        device
      };
    });

    return {
      success: true,
      reports: summaryReports,
      count: total
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching report summaries:`, error);
    return {
      success: false,
      message: `Error fetching report summaries: ${error.message}`
    };
  }
}

/**
 * Update a report's AI Insight field - Atomic operation with retry logic
 * @param reportId
 * @param aiInsight
 * @returns
 */
export async function updateReportAIInsight(reportId: string, aiInsight: any) {
  try {
    // Import operation queue
    const { operationQueue } = await import('../../utils/operationQueue.js');

    // Generate transaction ID for duplicate detection
    const transactionId = `update-ai-insight-${reportId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;

    logger.info(`[MongoDB] Updating AI Insight for report: ${reportId} (transaction: ${transactionId})`);

    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `update-ai-insight-${reportId}-${transactionId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db || !reportsCollection) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Perform update with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await reportsCollection.updateOne(
              { id: reportId },
              {
                $set: {
                  aiInsight,
                  updatedAt: new Date()
                }
              }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`[MongoDB] AI Insight update failed for reportId ${reportId} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`[MongoDB] Retrying AI Insight update for reportId ${reportId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!result || result.matchedCount === 0) {
          logger.warn(`[MongoDB] No report found with ID: ${reportId}`);
          return {
            success: false,
            message: `Report not found with ID: ${reportId}`
          };
        }

        return {
          success: true,
          message: 'AI Insight updated successfully'
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MongoDB] Error updating AI Insight for report: ${reportId} - ${error.message}`);
    return {
      success: false,
      message: `Error updating AI Insight: ${error.message}`
    };
  }
}

/**
 * Update a report's Video URL - Atomic operation with Redis coordination
 * @param reportId Report identifier
 * @param videoUrl Video URL
 * @returns Operation result
 */
export async function updateVideoUrl(reportId: string, videoUrl: string) {
  try {
    // Import operation queue
    const { operationQueue } = await import('../../utils/operationQueue.js');

    // Generate transaction ID for duplicate detection
    const transactionId = `update-video-url-${reportId}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    logger.info(`[MongoDB] Updating Video URL for report: ${reportId} to ${videoUrl} (transaction: ${transactionId})`);

    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `update-video-url-${reportId}-${transactionId}`,
      async () => {
        await ensureMongoDBConnection();
        if (!db || !reportsCollection) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Perform update with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await reportsCollection.updateOne(
              { id: reportId },
              {
                $set: {
                  videoUrl,
                  updatedAt: new Date()
                }
              }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`[MongoDB] Video URL update failed for reportId ${reportId} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`[MongoDB] Retrying video URL update for reportId ${reportId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        logger.info(`[MongoDB] Video URL update result for report ${reportId}: matchedCount=${result?.matchedCount}, modifiedCount=${result?.modifiedCount}`);

        if (result && result.matchedCount > 0) {
          logger.info(`[MongoDB] Video URL updated successfully for report ${reportId}`);

          // Clean up any pending video URL from Redis since we successfully updated MongoDB
          try {
            const { redisConnection } = await import('../../services/redis/redisConnection.js');
            const redis = redisConnection.getClient();
            if (redis) {
              await redis.del(`pending-video-url:${reportId}`);
              logger.debug(`[MongoDB] Cleaned up pending video URL from Redis for report ${reportId}`);
            }
          } catch (redisError: any) {
            logger.warn(`[MongoDB] Failed to clean up pending video URL from Redis for report ${reportId}: ${redisError.message}`);
          }

          return {
            success: true,
            message: 'Video URL updated successfully'
          };
        } else {
          // Report not found yet - store video URL in Redis as pending with atomic operation
          logger.warn(`[MongoDB] No report found with ID: ${reportId}, storing video URL as pending in Redis`);

          try {
            const { redisConnection } = await import('../../services/redis/redisConnection.js');
            const redis = redisConnection.getClient();

            if (!redis) {
              logger.error(`[MongoDB] Redis client not available, cannot store pending video URL for report ${reportId}`);
              return {
                success: false,
                message: 'Report not found and Redis not available to store pending video URL'
              };
            }

            // Use atomic Redis operation to check and set
            const pendingKey = `pending-video-url:${reportId}`;

            // Set the video URL with a TTL of 5 minutes (300 seconds)
            // Use SET with EX and NX to ensure atomicity
            const setResult = await redis.set(pendingKey, videoUrl, 'EX', 300, 'NX');

            if (setResult === 'OK') {
              logger.info(`[MongoDB] Stored pending video URL for report ${reportId} in Redis with 5-minute TTL`);
            } else {
              // Key already exists, update it
              await redis.setex(pendingKey, 300, videoUrl);
              logger.info(`[MongoDB] Updated existing pending video URL for report ${reportId} in Redis`);
            }

            return {
              success: true,
              message: 'Video URL stored as pending - will be applied when report is created'
            };
          } catch (redisError: any) {
            logger.error(`[MongoDB] Failed to store pending video URL in Redis for report ${reportId}: ${redisError.message}`);
            return {
              success: false,
              message: `Report not found and failed to store pending video URL: ${redisError.message}`
            };
          }
        }
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MongoDB] Error updating Video URL for report: ${reportId} - ${error.message}`);
    return {
      success: false,
      message: `Error updating Video URL: ${error.message}`
    };
  }
}

/**
 * Belirli bir test ID'siyle ilişkili raporları getir
 * @param testId Test ID'si
 * @returns Raporların listesi
 */
export async function getReportsByTestId(testId: string): Promise<{
  success: boolean;
  reports?: any[];
  count?: number;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();
    if (!db) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Ensure reports collection is initialized
    if (!reportsCollection) {
      return { success: false, message: "Reports collection not initialized" };
    }

    logger.info(`[MONGODB] Fetching reports for test ID: ${testId}`);

    // Simply search by ID directly
    const report = await reportsCollection.findOne({ id: testId });

    if (report) {

      return {
        success: true,
        reports: [report],
        count: 1
      };
    }

    logger.info(`[MONGODB] No report found with ID: ${testId}`);
    return {
      success: true,
      reports: [],
      count: 0
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error fetching reports for test ID ${testId}:`, error);
    return {
      success: false,
      message: `Error fetching reports: ${error.message}`
    };
  }
}

/**
 * Rapor paylaşımını etkinleştirir - Atomic operation with retry logic
 *
 * @param reportId Rapor ID
 * @param options Paylaşım seçenekleri
 */
export async function enableReportSharing(reportId: string, options: {
  expiresIn?: number | null; // Gün cinsinden (null=süresiz)
  password?: string;
  allowComments?: boolean;
}): Promise<{
  success: boolean;
  message?: string;
  token?: string;
}> {
  try {
    // Import operation queue
    const { operationQueue } = await import('../../utils/operationQueue.js');

    // Generate transaction ID for duplicate detection
    const transactionId = `enable-sharing-${reportId}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${process.hrtime.bigint()}`;

    logger.info(`[MONGODB] Enabling sharing for report ${reportId} (transaction: ${transactionId})`);

    // Use operation queue to ensure atomic updates
    return await operationQueue.enqueue(
      `enable-sharing-${reportId}-${transactionId}`,
      async () => {
        await ensureMongoDBConnection();

        if (!db || !reportsCollection) {
          return { success: false, message: "MongoDB not initialized" };
        }

        // Raporu kontrol et
        const report = await reportsCollection.findOne({ id: reportId });
        if (!report) {
          return { success: false, message: `Report not found: ${reportId}` };
        }

        // Benzersiz token oluştur
        const token = crypto.randomBytes(32).toString('hex');

        // Son kullanma tarihi hesapla (eğer belirtilmişse)
        let expiresAt = null;
        if (options.expiresIn) {
          expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + options.expiresIn);
        }

        // Şifreyi hash'le (eğer belirtilmişse)
        let hashedPassword = null;
        if (options.password) {
          hashedPassword = await bcrypt.hash(options.password, 10);
        }

        // Paylaşım bilgilerini güncelle
        const sharingInfo = {
          enabled: true,
          token: token,
          expiresAt: expiresAt,
          password: hashedPassword,
          allowComments: !!options.allowComments,
          createdAt: new Date(),
          accessCount: 0
        };

        // Perform update with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            result = await reportsCollection.updateOne(
              { id: reportId },
              {
                $set: {
                  sharing: sharingInfo,
                  updatedAt: new Date()
                }
              }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`[MONGODB] Enable sharing failed for reportId ${reportId} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`[MONGODB] Retrying enable sharing for reportId ${reportId} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!result || result.matchedCount === 0) {
          return { success: false, message: `Report not found: ${reportId}` };
        }

        logger.info(`[MONGODB] Enabled sharing for report ${reportId} with token ${token}`);

        return {
          success: true,
          token: token
        };
      },
      { priority: 'NORMAL', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error enabling sharing for report ${reportId}:`, error);
    return {
      success: false,
      message: `Error enabling sharing: ${error.message}`
    };
  }
}

/**
 * Rapor paylaşımını devre dışı bırakır
 *
 * @param reportId Rapor ID
 */
export async function disableReportSharing(reportId: string): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    await ensureMongoDBConnection();

    if (!db || !reportsCollection) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Raporu kontrol et
    const report = await reportsCollection.findOne({ id: reportId });
    if (!report) {
      return { success: false, message: `Report not found: ${reportId}` };
    }

    // Paylaşımı devre dışı bırak
    await reportsCollection.updateOne(
      { id: reportId },
      { $set: { "sharing.enabled": false } }
    );

    logger.info(`[MONGODB] Disabled sharing for report ${reportId}`);

    return { success: true };
  } catch (error: any) {
    logger.error(`[MONGODB] Error disabling sharing for report ${reportId}:`, error);
    return {
      success: false,
      message: `Error disabling sharing: ${error.message}`
    };
  }
}

/**
 * Paylaşım token'ı ile raporu getirir
 *
 * @param token Paylaşım token'ı
 * @param password Şifre (eğer gerekiyorsa)
 */
export async function getReportByShareToken(token: string, password?: string): Promise<{
  success: boolean;
  message?: string;
  report?: any;
  passwordRequired?: boolean;
}> {
  try {
    await ensureMongoDBConnection();

    if (!db || !reportsCollection) {
      return { success: false, message: "MongoDB not initialized" };
    }

    // Token ile raporu bul
    const report = await reportsCollection.findOne({
      "sharing.token": token,
      "sharing.enabled": true
    });

    if (!report) {
      return { success: false, message: "Shared report not found" };
    }

    // Son kullanma tarihini kontrol et
    if (report.sharing.expiresAt && new Date() > new Date(report.sharing.expiresAt)) {
      return { success: false, message: "Shared report has expired" };
    }

    // Şifre kontrolü
    if (report.sharing.password) {
      // Şifre sağlanmadıysa, şifre gerektiğini bildir
      if (!password) {
        return { success: false, passwordRequired: true, message: "Password required" };
      }

      // Şifre doğrulaması
      const passwordMatch = await bcrypt.compare(password, report.sharing.password);
      if (!passwordMatch) {
        return { success: false, message: "Invalid password" };
      }
    }

    // Hassas bilgileri temizle
    delete report.sharing.password;

    return {
      success: true,
      report: report
    };
  } catch (error: any) {
    logger.error(`[MONGODB] Error getting report by share token ${token}:`, error);
    return {
      success: false,
      message: `Error getting shared report: ${error.message}`
    };
  }
}

/**
 * Paylaşılan rapora erişim bilgilerini günceller - Atomic operation with retry logic
 *
 * @param token Paylaşım token'ı
 */
export async function updateReportShareAccess(token: string): Promise<void> {
  try {
    // Import operation queue
    const { operationQueue } = await import('../../utils/operationQueue.js');

    // Generate transaction ID for duplicate detection
    const transactionId = `update-share-access-${token}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    logger.debug(`[MONGODB] Updating share access for token ${token} (transaction: ${transactionId})`);

    // Use operation queue to ensure atomic updates
    await operationQueue.enqueue(
      `update-share-access-${token}-${transactionId}`,
      async () => {
        await ensureMongoDBConnection();

        if (!db || !reportsCollection) {
          logger.error("[MONGODB] Cannot update share access: MongoDB not initialized");
          return;
        }

        // Perform atomic increment and update with retry logic for write conflicts
        let result;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            // Use atomic $inc and $set operations
            result = await reportsCollection.updateOne(
              { "sharing.token": token },
              {
                $inc: { "sharing.accessCount": 1 },
                $set: { "sharing.lastAccessedAt": new Date() }
              }
            );
            break; // Success, exit retry loop
          } catch (error: any) {
            retryCount++;
            if (error.code === 11000 || error.message.includes('WriteConflict') || retryCount >= maxRetries) {
              // Duplicate key error or write conflict, or max retries reached
              logger.error(`[MONGODB] Share access update failed for token ${token} after ${retryCount} attempts: ${error.message}`);
              throw error;
            }
            // Wait before retry with exponential backoff
            const delay = Math.pow(2, retryCount) * 100; // 200ms, 400ms, 800ms
            logger.warn(`[MONGODB] Retrying share access update for token ${token} in ${delay}ms (attempt ${retryCount}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (result && result.matchedCount > 0) {
          logger.debug(`[MONGODB] Updated access info for shared report with token ${token}`);
        } else {
          logger.warn(`[MONGODB] No shared report found with token ${token}`);
        }
      },
      { priority: 'LOW', maxRetries: 3 }
    );
  } catch (error: any) {
    logger.error(`[MONGODB] Error updating share access for token ${token}:`, error);
  }
}