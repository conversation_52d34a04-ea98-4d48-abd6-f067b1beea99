"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { AddMemberDialog } from "./add-member-dialog"
import { EditMemberDialog } from "./edit-member-dialog"
import { ConfirmDialog } from "./confirm-dialog"
import { MoreHorizontal, Search, Plus, Edit, Trash2, UserX, UserCheck } from "lucide-react"
import type { TeamMember, Role } from "@/types/team"
import { useAuth } from "@/lib/api/auth"

interface TeamMembersProps {
  members: TeamMember[]
  roles: Role[]
  onAddMember: (member: TeamMember) => void
  onUpdateMember: (member: TeamMember) => void
  onDeleteMember: (id: string) => void
  onDeactivateMember: (id: string) => void
  onActivateMember: (id: string) => void
}

export function TeamMembers({
  members,
  roles,
  onAddMember,
  onUpdateMember,
  onDeleteMember,
  onDeactivateMember,
  onActivateMember,
}: TeamMembersProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null)
  const { user } = useAuth()

  // Kullanıcının company_owner olup olmadığını kontrol et
  const isCompanyOwner = user?.accountType === 'company_owner'

  // Mevcut kullanıcının ID'si
  const currentUserId = user?.id

  const filteredMembers = members.filter(
    (member) => {
      const memberName = member.user?.name || member.name || "";
      const memberEmail = member.user?.email || member.email || "";
      let memberRole = "";
      if (typeof member.role === 'string') {
        memberRole = member.role;
      } else if (member.role && typeof member.role === 'object') {
        memberRole = member.role.name || member.role.id || "";
      } else if (member.roleId) {
        // Rol objesi yoksa ama roleId varsa
        const roleObj = roles.find(r => r.id === member.roleId);
        memberRole = roleObj?.name || member.roleId;
      }

      return memberName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        memberEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
        memberRole.toLowerCase().includes(searchQuery.toLowerCase());
    }
  )

  const handleEdit = (member: TeamMember) => {
    // Detaylı hata ayıklama bilgisi
    console.log("Original member object:", JSON.stringify(member, null, 2));

    // Kullanıcı bilgilerini düzgün şekilde ayarla
    const memberToEdit = {
      ...member,
      // Kullanıcı adı için doğru sırayla kontrol et
      name: member.user?.name ||
            (typeof member.name === 'string' && member.name && member.name !== member.role_name ? member.name : ""),
      email: member.user?.email || member.email || "",
      role: typeof member.role === 'object' ? member.role.name :
            (typeof member.role === 'string' ? member.role : member.role_name || "")
    }

    console.log("Prepared member for edit:", JSON.stringify(memberToEdit, null, 2));
    setSelectedMember(memberToEdit)
    setShowEditDialog(true)
  }

  const handleDelete = (member: TeamMember) => {
    setSelectedMember(member)
    setShowDeleteDialog(true)
  }

  const handleDeactivate = (member: TeamMember) => {
    onDeactivateMember(member.id)
  }

  const handleActivate = (member: TeamMember) => {
    onActivateMember(member.id)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Aktif</Badge>
      case "inactive":
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">Deaktif</Badge>
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Beklemede</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const getRoleBadge = (role: string | { id: string; name: string; permissions?: any }, member: TeamMember) => {
    if ((role === null || role === undefined) && member.roleId) {
      const roleObj = roles.find((r) => r.id === member.roleId)
      if (roleObj) {
        return <Badge className={roleObj.color || ""}>{roleObj.name}</Badge>
      }
      return <Badge>{member.roleId}</Badge>
    }

    if (typeof role === 'object' && role !== null) {
      // Role objesi null değilse ve name özelliği varsa işlem yap
      if (role.name) {
        const roleObj = roles.find((r) => r.name === role.name)
        return <Badge className={roleObj?.color || ""}>{role.name}</Badge>
      } else if (role.id) {
        // Name yoksa ama id varsa, id ile rol bul
        const roleObj = roles.find((r) => r.id === role.id)
        return <Badge className={roleObj?.color || ""}>{roleObj?.name || role.id}</Badge>
      }
      // Hiçbir özellik yoksa belirsiz göster
      return <Badge>Belirsiz</Badge>
    }
    const roleObj = roles.find((r) => r.name === role)
    return <Badge className={roleObj?.color || ""}>{role || "Belirsiz"}</Badge>
  }

  const getInitials = (name: string | undefined) => {
    if (!name) return "U"
    try {
      return name
        .split(" ")
        .map((part) => part && part[0] || "")
        .join("")
        .toUpperCase()
    } catch (error) {
      console.error("Error getting initials:", error);
      return "U";
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Üye ara..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button className="flex items-center gap-2" onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4" />
            Yeni Üye Ekle
          </Button>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Üye</TableHead>
                <TableHead>Rol</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead>Katılma Tarihi</TableHead>
                <TableHead className="text-right">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMembers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                    Üye bulunamadı
                  </TableCell>
                </TableRow>
              ) : (
                filteredMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          {member.avatar ? (
                            <AvatarImage src={member.avatar} alt={member.user?.name || member.name || ""} />
                          ) : (
                            <AvatarImage
                              src={`https://ui-avatars.com/api/?name=${encodeURIComponent(member.user?.name || member.name || "User")}&background=random&color=fff&size=40`}
                              alt={member.user?.name || member.name || ""}
                            />
                          )}
                          <AvatarFallback>{getInitials(member.user?.name || member.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.user?.name || member.name}</div>
                          <div className="text-sm text-gray-500">{member.user?.email || member.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getRoleBadge(member.role, member)}</TableCell>
                    <TableCell>{getStatusBadge(member.status)}</TableCell>
                    <TableCell>
                      {(member.addedAt || member.createdAt)
                        ? new Date(member.addedAt || member.createdAt || "").toLocaleDateString('tr-TR', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit'
                          })
                        : '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      {/* Company owner kendisini göremez, diğer kullanıcılar sadece company owner ise üç noktayı görebilir */}
                      {(isCompanyOwner && member.user?.id !== currentUserId) && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Menüyü aç</span>
                            </Button>
                          </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {/* Company owner kendisini düzenleyemez */}
                          {!(isCompanyOwner && member.user?.id === currentUserId) && (
                            <DropdownMenuItem onClick={() => handleEdit(member)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Düzenle
                            </DropdownMenuItem>
                          )}

                          {/* Sadece company_owner kullanıcıları aktif/deaktif edebilir ve kendisini deaktif edemez */}
                          {isCompanyOwner && member.user?.id !== currentUserId && (
                            member.status === "active" ? (
                              <DropdownMenuItem onClick={() => handleDeactivate(member)}>
                                <UserX className="mr-2 h-4 w-4" />
                                Deaktif Et
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => handleActivate(member)}>
                                <UserCheck className="mr-2 h-4 w-4" />
                                Aktif Et
                              </DropdownMenuItem>
                            )
                          )}

                          {/* Company owner kendisini silemez */}
                          {!(isCompanyOwner && member.user?.id === currentUserId) && (
                            <DropdownMenuItem onClick={() => handleDelete(member)} className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Sil
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        <AddMemberDialog open={showAddDialog} onOpenChange={setShowAddDialog} roles={roles} onAddMember={onAddMember} />

        {selectedMember && (
          <>
            <EditMemberDialog
              open={showEditDialog}
              onOpenChange={setShowEditDialog}
              member={selectedMember}
              roles={roles}
              onUpdateMember={onUpdateMember}
            />

            <ConfirmDialog
              open={showDeleteDialog}
              onOpenChange={setShowDeleteDialog}
              title="Üyeyi Sil"
              description={`${selectedMember.user?.name || selectedMember.name} adlı üyeyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
              onConfirm={() => onDeleteMember(selectedMember.id)}
              confirmLabel="Evet, Sil"
              cancelLabel="İptal"
              variant="destructive"
            />
          </>
        )}
      </CardContent>
    </Card>
  )
}
