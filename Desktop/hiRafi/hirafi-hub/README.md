# AIDrive Test Hub

## İçindekiler

1. [<PERSON><PERSON>](#genel-bakış)
2. [<PERSON><PERSON><PERSON>](#mimari)
3. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#teknoloji-yığını)
4. [<PERSON>](#an<PERSON><PERSON>m<PERSON><PERSON><PERSON>)
5. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#i̇letişim-kanalları)
6. [API Endpointleri](#api-endpointleri)
7. [<PERSON><PERSON><PERSON>m Senaryoları](#kullanım-senaryoları)
8. [Test Node İletişimi](#test-node-i̇letişimi)
9. [<PERSON><PERSON><PERSON> ve Çalıştırma](#kurulum-ve-çalıştırma)
10. [Konfigürasyon](#konfigürasyon)

## Genel Bakış

Test Hub, AIDrive test otomasyon sisteminin merkezi bileşenidir. Otomasyon test isteklerini yönetir, test node'ları izler ve sonuçları toplar. Test Hub, web arayüzleri ve test node'ları arasında ortak bir koordinasyon noktası sağlar.

Test Hub, aşağıdaki ana görevleri yerine getirir:
- Test isteklerini yönetme ve kuyruğa alma
- Test node'larını kaydetme ve izleme
- Test atamalarını yönetme
- Test sonuçlarını toplama ve saklama
- WebSocket ve RabbitMQ aracılığıyla test node'larıyla iletişim kurma
- Web arayüzleri için API endpoints sağlama

Sistem, WebSocket bağlantıları üzerinden aynı anda 100'e kadar node ile iletişim kurabilir, gerçek zamanlı durum güncellemeleri ve komutlar sağlar. MongoDB entegrasyonu, test sonuçlarının kalıcı depolanmasını sağlar ve tarihsel analiz ve raporlamaya imkan tanır.

## Mimari

Test Hub, kaygıları ayırırken aynı zamanda uyumlu bir sistem sürdüren modüler bir mimariyi takip eder:

### Sistem Mimarisi

```
┌─────────────┐      ┌───────────────┐      ┌─────────────┐
│             │      │               │      │             │
│  Test Node  │◄────►│   Test Hub    │◄────►│  MongoDB    │
│             │      │               │      │             │
└─────────────┘      └───────────────┘      └─────────────┘
      ▲               ▲     │     ▲
      │               │     │     │
      │               │     ▼     │
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│             │      │             │      │             │
│  Test Node  │◄────►│ RabbitMQ    │◄────►│  Test Node  │
│             │      │             │      │             │
└─────────────┘      └─────────────┘      └─────────────┘
```

### Dosya Yapısı

Test Hub mimarisi şu temel bileşenlerden oluşur:

```
test_hub/
├── src/
│   ├── api/              # API endpoints ve route'lar
│   ├── config/           # Konfigürasyon ayarları
│   ├── connectors/       # İletişim bağlantıları (WebSocket, RabbitMQ)
│   ├── core/             # Ana iş mantığı
│   │   ├── node-manager/ # Test node'larını yönetme
│   │   └── test-manager/ # Test süreçlerini yönetme
│   ├── models/           # Veri modelleri ve tip tanımları
│   ├── services/         # Harici servis entegrasyonları (MongoDB, MinIO)
│   ├── utils/            # Yardımcı fonksiyonlar
│   ├── index.ts          # Kütüphane ihraç noktası
│   └── server.ts         # Ana sunucu başlatma
├── public/               # Statik dosyalar
├── .env                  # Ortam değişkenleri
├── package.json          # Bağımlılıklar ve betikler
└── tsconfig.json         # TypeScript yapılandırması
```

## Teknoloji Yığını

Test Hub aşağıdaki teknolojileri kullanır:

- **Node.js**: Ana çalışma ortamı
- **TypeScript**: Tip güvenli kodlama
- **Express**: HTTP API sunucusu
- **WebSocket (ws)**: Gerçek zamanlı iletişim
- **RabbitMQ (amqplib)**: Mesaj kuyruklama sistemi
- **MongoDB**: Test sonuçları ve senaryoları için veritabanı
- **JWT**: Kimlik doğrulama için
- **OpenAI API**: Otomatik adım oluşturma için
- **Winston**: Loglama
- **Firebase**: Kullanıcı yönetimi (opsiyonel)

## Ana Modüller

### Test Manager (`core/test-manager`)

Test Manager, tüm test süreçlerini yönetir:

- Test kuyruğunu yönetir (`testQueue`)
- Çalışan testleri izler (`runningTests`)
- Tamamlanmış testleri saklar (`completedTests`)
- Test durum geçişlerini yönetir (queued → running → completed/failed)
- RabbitMQ ve WebSocket üzerinden gelen test sonuçlarını işler
- Test sonuçlarını MongoDB'ye kaydeder

Temel Metodlar:
- `addTest()`: Yeni test kuyruğa eklenir
- `stopTest()`: Belirli bir testi durdurur
- `assignTestToNode()`: Bir testi belirli bir node'a atar
- `updateTestStatus()`: Test durumunu günceller
- `getQueueStatus()`: Kuyruk durumunu getirir
- `handleRabbitMQTestResult()`: RabbitMQ sonuçlarını işler

### Node Manager (`core/node-manager`)

Node Manager, test node'larını yönetir:

- Node kayıtlarını tutar (`nodes`)
- Node'ların durumunu izler (available, busy, inactive, offline)
- Uygun node seçimini gerçekleştirir
- Node sağlık kontrolünü sağlar
- VNC bağlantılarını yönetir (uzaktan izleme için)

Temel Metodlar:
- `registerNode()`: Yeni node kaydeder
- `updateNodeHeartbeat()`: Node heartbeat'ini günceller
- `markNodeAsBusy()`: Node'u meşgul olarak işaretler
- `markNodeAsAvailable()`: Node'u müsait olarak işaretler
- `findSuitableNode()`: Gereksinimlere uygun node bulur
- `assignAndSendTest()`: Test atama ve gönderme işlemini yapar

## İletişim Kanalları

Test Hub çoklu protokol yaklaşımı kullanarak sağlam iletişim sağlar:

### WebSocket Connector (`connectors/websocket`)

WebSocket, gerçek zamanlı çift yönlü iletişim sağlar:

- UI istemcileri ile iletişim (`/ws/ui` endpoint'i)
- Test node'ları ile iletişim (`/ws/node` endpoint'i)
- Test ilerlemesini gerçek zamanlı izleme
- Test sonuçlarını alma
- Node heartbeat'leri ve sağlık durumu

İleti Türleri:
- `register`: Node kaydı
- `heartbeat`: Node durumu güncelleme
- `test-result`: Test sonucu gönderimi
- `step-progress`: Test adım ilerlemesi
- `test-assignment`: Node'a test atama
- `stop-test`: Çalışan testi durdurma

### RabbitMQ Connector (`connectors/rabbitmq`)

RabbitMQ, mesaj kuyruğu tabanlı iletişim sağlar (asenkron):

- Mesaj kuyrukları: `test_queue`, `result_queue`, `node_registration_queue`
- Test node'lara test ataması gönderir
- Test sonuçlarını alır
- Node kayıtlarını işler

## API Endpointleri

Test Hub aşağıdaki ana API rotalarını sunar:

- `/api/test-hub`: Test yönetimi
  - `GET /api/test-hub` - Opsiyonel filtrelerle tüm testleri listele
  - `POST /api/test-hub` - Kuyruğa yeni bir test ekle
  - `GET /api/test-hub/:testId` - Belirli bir testin detaylarını al
  - `POST /api/test-hub/:testId/status` - Test durumunu güncelle
  - `POST /api/test-hub/:testId/stop` - Çalışan bir testi durdur
  - `POST /api/test-hub/:testId/report` - Test raporu gönder
- `/api/scenarios`: Senaryo yönetimi
- `/api/nodes`: Node yönetimi ve durumu
  - `GET /api/nodes` - Tüm bağlı node'ları listele
  - `GET /api/nodes/:nodeId` - Belirli bir node'un detaylarını al
- `/api/reports`: Test raporları
- `/api/dashboard`: Dashboard istatistikleri
- `/api/plugins`: Eklenti yönetimi
- `/api/generate-steps`: Adım oluşturma servisi
- `/api/vnc`: VNC bağlantıları
- `/api/auth`: Kimlik doğrulama
- `/api/health`: Sistem sağlık durumu
- `/api/feedback`: Geri bildirim servisi

## Kullanım Senaryoları

1. **Test Başlatma Süreci**:
   - Test API üzerinden bir test isteği alınır
   - TestManager testin kuyruğa ekler
   - Uygun bir test node bulunur
   - Test, WebSocket veya RabbitMQ aracılığıyla node'a atanır

2. **Test İlerleme İzleme**:
   - Node, WebSocket üzerinden adım ilerlemelerini gönderir
   - TestManager bu güncellemeleri alır ve saklar
   - UI istemcilerine gerçek zamanlı güncelleme gönderilir

3. **Test Sonuç İşleme**:
   - Test node, WebSocket veya RabbitMQ ile final sonuçları gönderir
   - TestManager sonuçları işler ve veritabanına kaydeder
   - Test ve senaryo durumları güncellenir

4. **Node Yönetimi**:
   - Yeni node'lar WebSocket üzerinden kaydolur
   - NodeManager node'ları takip eder ve sağlık kontrolünü gerçekleştirir
   - Düzenli heartbeat ile node durumu güncellenir

## Test Node İletişimi

### WebSocket Protokolü
Node'lar Test Hub ile öncelikle WebSocket mesajları ile iletişim kurar:

#### Node Kaydı
```javascript
// Node → Hub: Node kaydı
{
  "type": "register",
  "data": {
    "name": "Windows-Chrome-Node-1",
    "capabilities": ["chrome", "windows"],
    "status": "available"
  }
}

// Hub → Node: Kayıt onayı
{
  "type": "registered",
  "data": {
    "nodeId": "node-12345",
    "status": "registered"
  }
}
```

#### Test Atama ve Çalıştırma
```javascript
// Hub → Node: Test ataması
{
  "type": "test-assignment",
  "data": {
    "testId": "test-abc123",
    "scenarioId": "scenario-xyz789",
    "scenario": {
      "title": "Ana Sayfa Testi",
      "url": "https://example.com",
      "steps": [...]
    }
  }
}

// Node → Hub (Progress):
{
  "type": "step-progress",
  "data": {
    "nodeId": "node-12345",
    "testId": "test-abc123",
    "stepName": "Sayfaya Git",
    "stepIndex": 1,
    "totalSteps": 3,
    "additionalData": {
      "type": "goto",
      "status": "started",
      "timestamp": "2023-08-15T14:32:40.123Z"
    }
  }
}

// Node → Hub (Result):
{
  "type": "test-result",
  "data": {
    "nodeId": "node-12345",
    "testId": "test-abc123",
    "status": "success",
    "result": {
      "id": "test-abc123",
      "status": "success",
      "duration": 12540,
      "steps": [...],
      "enhancedMetrics": {...}
    }
  }
}
```

### RabbitMQ Üzerinden Test İletişimi (Alternatif Kanal):

```
# Test Atama (test_queue)
{
  "testId": "test-abc123",
  "scenarioId": "scenario-xyz789",
  "nodeId": "node-12345",
  "scenario": {...}
}

# Test Sonucu (result_queue)
{
  "testId": "test-abc123",
  "nodeId": "node-12345",
  "status": "success",
  "result": {...}
}
```

## Kurulum ve Çalıştırma

### Ön Koşullar
- Node.js 16.x veya daha yüksek
- npm 7.x veya daha yüksek
- MongoDB instance (rapor depolama için)
- RabbitMQ sunucusu (opsiyonel, mesaj kuyruklama için)

### Bağımlılıkları Yükleme
```bash
# Repo'yu klonla
git clone https://github.com/your-repo/test-hub.git

# Proje dizinine git
cd test-hub

# Bağımlılıkları yükle
npm install
```

### Projeyi Derleme
```bash
# TypeScript kodunu derle
npm run build
```

### Test Hub'ı Başlatma
```bash
# Geliştirme modu (canlı yeniden yükleme)
npm run dev

# Üretim modu
npm start
```

### Temel Konfigürasyon Seçenekleri
Test Hub davranışını çevre değişkenlerini değiştirerek ayarlayabilirsiniz:
```bash
# Özel port ile çalıştır
PORT=8000 npm start

# RabbitMQ olmadan çalıştır
RABBITMQ_ENABLED=false npm start
```

## Konfigürasyon

Konfigürasyon `.env` dosyasında tanımlanır:

```
# Sunucu ayarları
PORT=3000
NODE_ENV=development

# MongoDB bağlantısı
MONGODB_URI=mongodb://localhost:27017/aidrive_testhub

# RabbitMQ
RABBITMQ_ENABLED=true
RABBITMQ_URL=amqp://localhost

# WebSocket
WEBSOCKET_ENABLED=true

# Test ayarları
TEST_TIMEOUT_SECONDS=300
COMPLETED_TEST_CLEANUP_MINS=120

# JWT için gizli anahtar
JWT_SECRET=your_jwt_secret_key

# OpenAI API
OPENAI_API_KEY=your_openai_api_key
```

## Özellikler

- **Merkezi Test Dağıtımı**: Testleri mevcut node'lara verimli bir şekilde atar
- **Gerçek Zamanlı İletişim**: Anlık durum güncellemeleri için WebSocket tabanlı iletişim
- **Test Kuyruğu Yönetimi**: Test çalıştırma sırasını önceliklendirir ve yönetir
- **MongoDB Entegrasyonu**: Tarihsel analiz için test raporlarını depolar
- **Eşzamanlı Yürütme**: Node'lar arasında aynı anda çalışan birden fazla testi destekler
- **Node Sağlık İzleme**: Node durumunu ve uygunluğunu takip eder
- **Test Yürütme Kontrolü**: Testleri gerçek zamanlı olarak başlatın, durdurun ve izleyin
- **RESTful API**: Diğer sistemlerle entegrasyon için HTTP endpoint'leri
- **Canlı Test İzleme**: Testleri çalıştırırken gözlemleme yeteneği
- **Hata Toleransı**: Node arızaları için yeniden bağlanma ve kurtarma mekanizmaları

Bu README, Test Hub'ın ana yapı, işlev ve iletişim kanallarını içermektedir. Test Hub, otomasyon testleri için merkezi bir koordinasyon noktası olarak çalışır ve test node'ları ile verimli bir şekilde iletişim kurar.