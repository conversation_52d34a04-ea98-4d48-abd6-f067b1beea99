/**
 * State Manager Interface
 * 
 * Defines the contract for test execution state management.
 * This interface handles atomic state transitions and prevents race conditions.
 */

import { TestExecutionState } from '../../node-manager/types/node-manager-types.js';

/**
 * State transition callback function type
 */
export type StateTransitionCallback = (
  fromState: TestExecutionState,
  toState: TestExecutionState,
  testId?: string
) => void;

/**
 * State transition result
 */
export interface StateTransitionResult {
  success: boolean;
  previousState: TestExecutionState;
  currentState: TestExecutionState;
  message?: string;
}

/**
 * State manager configuration options
 */
export interface StateManagerConfig {
  nodeId: string;
  enableStateLogging?: boolean;
  transitionTimeout?: number;
}

/**
 * Interface for managing test execution state
 * 
 * This interface provides atomic state management to prevent race conditions
 * during test execution state transitions.
 */
export interface IStateManager {
  /**
   * Initialize the state manager
   * @param config Configuration options
   * @returns Promise that resolves when initialization is complete
   */
  initialize(config: StateManagerConfig): Promise<void>;

  /**
   * Get the current execution state
   * @returns Current execution state
   */
  getCurrentState(): TestExecutionState;

  /**
   * Get the current test ID being processed
   * @returns Current test ID or null if no test is running
   */
  getCurrentTestId(): string | null;

  /**
   * Atomically transition from one state to another (async version with proper mutex)
   * @param fromState Expected current state
   * @param toState Desired new state
   * @param testId Test ID (required when transitioning to non-IDLE states)
   * @returns Promise resolving to state transition result
   */
  tryTransitionState(
    fromState: TestExecutionState,
    toState: TestExecutionState,
    testId?: string
  ): Promise<StateTransitionResult>;



  /**
   * Force transition to a specific state (async version with proper mutex)
   * @param toState Desired new state
   * @param testId Test ID
   * @param reason Reason for force transition
   * @returns Promise resolving to state transition result
   */
  forceTransitionState(
    toState: TestExecutionState,
    testId?: string,
    reason?: string
  ): Promise<StateTransitionResult>;



  /**
   * Check if the manager can accept a new test
   * @returns True if can accept new test, false otherwise
   */
  canAcceptNewTest(): boolean;

  /**
   * Check if a test is currently running
   * @returns True if a test is running, false otherwise
   */
  isTestRunning(): boolean;

  /**
   * Check if a specific test is currently running
   * @param testId The test ID to check
   * @returns True if the specified test is running, false otherwise
   */
  isSpecificTestRunning(testId: string): boolean;

  /**
   * Register a callback for state transitions
   * @param callback Function to call when state transitions occur
   */
  onStateTransition(callback: StateTransitionCallback): void;

  /**
   * Remove a state transition callback
   * @param callback Function to remove from callbacks
   */
  offStateTransition(callback: StateTransitionCallback): void;

  /**
   * Get state transition history (for debugging)
   * @param limit Maximum number of transitions to return
   * @returns Array of recent state transitions
   */
  getStateHistory(limit?: number): Array<{
    timestamp: Date;
    fromState: TestExecutionState;
    toState: TestExecutionState;
    testId?: string;
    success: boolean;
  }>;

  /**
   * Reset the state manager to initial state
   * @param reason Reason for reset
   * @returns Promise that resolves when reset is complete
   */
  reset(reason?: string): Promise<void>;

  /**
   * Dispose of the state manager
   * @returns Promise that resolves when disposal is complete
   */
  dispose(): Promise<void>;
}
