/**
 * Shared types and interfaces for Node Manager components
 * 
 * These types are shared across the refactored node manager module
 * and maintain compatibility with the original types.
 */

export interface NodeInfo {
  nodeId: string;
  clientId: string;
  name: string;
  capabilities: string[];
  status: 'available' | 'busy';
  timestamp: number;
  currentTestId?: string | null;
}

export interface HeartbeatData extends NodeInfo {
  ip: string;
  version: string;
}

export interface TestExecutionInfo {
  isRunningTest: boolean;
  currentTestId: string | null;
  currentTestData: any | null;
  testsExecuted: number;
  testsFailed: number;
  testsSucceeded: number;
  startTime: Date;
}

export interface ConnectionStatus {
  websocket: boolean;
  redis: boolean;
  overall: boolean;
}

export interface PeriodicTask {
  id: string;
  interval: NodeJS.Timeout;
  description: string;
}

export enum NodeStatus {
  AVAILABLE = 'available',
  BUSY = 'busy',
  STOPPING = 'stopping',  // New intermediate state for test cleanup
  OFFLINE = 'offline',
  ERROR = 'error'
}

export enum TestExecutionState {
  IDLE = 'idle',
  PREPARING = 'preparing',
  RUNNING = 'running',
  CLEANING_UP = 'cleaning_up',
  ERROR = 'error'
}

export enum ConnectionType {
  WEBSOCKET = 'websocket',
  REDIS = 'redis'
}

export interface NodeManagerConfig {
  nodeId?: string;
  name: string;
  capabilities: string[];
  websocketEnabled: boolean;
  redisEnabled: boolean;
  version: string;
}

export interface ReconnectionOptions {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
}

/**
 * Service Names for dependency injection
 */
export const ServiceNames = {
  LOGGER: 'logger',
  NODE_IDENTITY_SERVICE: 'nodeIdentityService',
  NODE_STATE_SERVICE: 'nodeStateService',
  CONNECTION_SERVICE: 'connectionService',
  EVENT_SERVICE: 'eventService',
  PERIODIC_TASK_SERVICE: 'periodicTaskService',
  NODE_MANAGER: 'nodeManager',
  WEBSOCKET_CONNECTOR: 'websocketConnector',
  CONFIG: 'config',
  ATOMIC_STATE_MANAGER: 'atomicStateManager',

  // Test Execution Services
  EXECUTION_COORDINATOR: 'executionCoordinator',
  STATE_MANAGER: 'stateManager',
  RESOURCE_MANAGER: 'resourceManager',
  CONFIGURATION_MANAGER: 'configurationManager',
  CLEANUP_ORCHESTRATOR: 'cleanupOrchestrator',
  TEST_QUEUE_WORKER: 'testQueueWorker',
  PROGRESS_SERVICE: 'progressService',
  TEST_COMMUNICATION_SERVICE: 'testCommunicationService',
  STORAGE: 'storage'
} as const;
