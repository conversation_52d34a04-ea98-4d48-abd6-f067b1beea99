# Test Hub Docker Kuru<PERSON> rehber, Test Hub uygulamasını Docker içinde çalıştırmak için gereken adımları içerir.

## İçindekiler

- [<PERSON><PERSON><PERSON><PERSON><PERSON>](#gere<PERSON><PERSON><PERSON>)
- [<PERSON><PERSON>](#docker-kurulum<PERSON>)
- [<PERSON><PERSON>şti<PERSON><PERSON>tam<PERSON>](#geliştirme-ortamı)
- [<PERSON>ret<PERSON>](#üretim-ortamı)
- [Çevre <PERSON>](#çevre-değişkenleri)
- [Sorun Giderme](#sorun-giderme)

## Gereksinimler

- [Docker](https://www.docker.com/get-started) (20.10.0 veya üzeri)
- [Docker Compose](https://docs.docker.com/compose/install/) (v2 veya üzeri)

## Docker Kurulumu

### Geliştirme Ortamı

Geliştirme ortamında çalıştırmak için:

```bash
# Geliştirme ortamını başlat
./docker-dev-start.sh

# veya manuel o<PERSON>ak
docker-compose -f docker-compose.dev.yml up -d
```

<PERSON><PERSON> komut, aşağıdaki servisleri başlatır:
- **test-hub**: Test Hub uygulaması (geliştirme modunda)
- **redis**: Redis veritabanı
- **mongodb**: MongoDB veritabanı
- **minio**: MinIO nesne depolama

Geliştirme ortamında, kaynak kodunuzdaki değişiklikler otomatik olarak algılanır ve uygulama yeniden başlatılır.

### Üretim Ortamı

Üretim ortamında çalıştırmak için:

```bash
# Üretim ortamını başlat
./docker-start.sh

# veya manuel olarak
docker-compose up -d
```

## Çevre Değişkenleri

Docker Compose dosyasında veya `.env` dosyasında aşağıdaki çevre değişkenlerini ayarlayabilirsiniz:

| Değişken | Açıklama | Varsayılan Değer |
|----------|----------|-----------------|
| `MONGODB_USERNAME` | MongoDB kullanıcı adı | `admin` |
| `MONGODB_PASSWORD` | MongoDB şifresi | `password` |
| `JWT_SECRET` | JWT imzalama anahtarı | `your-secret-key` |

## Servisler ve Portlar

| Servis | Port | Açıklama |
|--------|------|----------|
| Test Hub | 5000 | Ana uygulama |
| Redis | 6379 | Redis veritabanı |
| MongoDB | 27017 | MongoDB veritabanı |

## Sorun Giderme

### Logları Görüntüleme

```bash
# Tüm servislerin loglarını görüntüle
docker-compose logs

# Belirli bir servisin loglarını görüntüle
docker-compose logs test-hub

# Logları canlı olarak takip et
docker-compose logs -f
```

### Container Shell'ine Erişim

```bash
# Test Hub container'ına bağlan
docker-compose exec test-hub sh

# MongoDB container'ına bağlan
docker-compose exec mongodb bash
```

### Veritabanı Yedekleme ve Geri Yükleme

MongoDB veritabanını yedeklemek için:

```bash
docker-compose exec mongodb mongodump --username admin --password password --authenticationDatabase admin --db testinium_ai --out /data/backup
```

MongoDB veritabanını geri yüklemek için:

```bash
docker-compose exec mongodb mongorestore --username admin --password password --authenticationDatabase admin --db testinium_ai /data/backup/testinium_ai
```

## Lisans

Bu proje [LICENSE](./LICENSE) dosyasında belirtilen lisans kapsamında lisanslanmıştır.
