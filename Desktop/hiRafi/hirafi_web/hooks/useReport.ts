import { useState, useEffect, useCallback, useMemo } from 'react'
import { useAuth } from "@/lib/api/auth"
import { toast } from "@/lib/utils/toast-utils"
import { EnhancedMetrics, AccessibilityViolationItem, NetworkLogEntry } from "@/types/metrics"
import { reportApi } from "@/lib/api"

// Add this type declaration to extend Window interface
declare global {
  interface Window {
    lastReportRefreshTime?: number;
  }
}

// Single test report interface with the structure from API
export interface ReportDetail {
  _id: string
  id: string
  date: string
  createdAt: string
  status: string
  scenarioId: string
  startTime: number
  endTime: number
  logs: any[]
  error: string | null
  scenarioTitle: string
  assignedNode: string
  platform?: 'web' | 'android' // Platform the test was run on (web or android)
  environmentSettings?: any // Environment settings for platform detection
  summary: {
    total: number
    passed: number
    failed: number
    errors: number
  }
  steps: Array<{
    success: boolean
    error: string | null
    logs: string[]
    duration: number
    id: string
    name: string
    type: string
    beforeScreenshotUrl?: string
    afterScreenshotUrl?: string
    // Control Flow Results for enhanced report visualization
    controlFlowData?: {
      // IF-ELSE specific
      conditionResult?: boolean;
      executedBranch?: 'true' | 'false';
      trueSteps?: any[];
      falseSteps?: any[];
      
      // FOR/WHILE LOOP specific  
      iterationCount?: number;
      completedIterations?: number;
      totalStepsExecuted?: number;
      loopSteps?: any[][];
      hitMaxIterations?: boolean;
      failedIteration?: number;
      failedStepIndex?: number;
    };
    // Additional fields that might be present in steps
    url?: string;
    value?: string;
    prompt?: string;
    target?: string;
  }>
  name: string
  url: string
  duration: number
  result: string | null
  userId: string
  executedUser?: string
  executedUserName?: string
  videoUrl?: string
  aiInsight?: {
    PageMetrics: { Score: string | number, Comment: string },
    NetworkStatistics: { Score: string | number, Comment: string },
    TotalViolations: { Score: string | number, Comment: string },
    NetworkLogs: { Score: string | number, Comment: string },
    OverallOptimizationScore: string | number,
    OverallComment: string
  }
  enhancedMetrics?: EnhancedMetrics
  testrailRunLink?: string
  testrailRunId?: number
}

interface AiAnalysisState {
  PageMetrics: { Score: string | number, Comment: string },
  NetworkStatistics: { Score: string | number, Comment: string },
  TotalViolations: { Score: string | number, Comment: string },
  NetworkLogs: { Score: string | number, Comment: string },
  OverallOptimizationScore: string | number,
  OverallComment: string
}

interface UseReportResult {
  report: ReportDetail | null
  isLoading: boolean
  isError: boolean
  error: string | undefined
  refetch: () => Promise<void>
  // UI State & Handlers
  aiAnalysis: AiAnalysisState | null
  isAnalyzing: boolean
  handleRunAiAnalysis: () => void
  activeTab: string
  setActiveTab: (tab: string) => void
  selectedSeverityFilter: string
  setSelectedSeverityFilter: (filter: string) => void
  selectedNetworkTypeFilter: string
  setSelectedNetworkTypeFilter: (filter: string) => void
  selectedNetworkStatusFilter: string
  setSelectedNetworkStatusFilter: (filter: string) => void
  selectedNetworkPerformanceFilter: string
  setSelectedNetworkPerformanceFilter: (filter: string) => void
  selectedNetworkMethodFilter: string
  setSelectedNetworkMethodFilter: (filter: string) => void
  showAllViolations: boolean
  setShowAllViolations: (show: boolean) => void
  showAllNetworkLogs: boolean
  setShowAllNetworkLogs: (show: boolean) => void
  // Derived Data / Filters
  filteredViolations: AccessibilityViolationItem[]
  filteredNetworkLogs: NetworkLogEntry[]
  uniqueNetworkTypes: string[]
  uniqueHttpMethods: string[]
  // Helpers
  formatTime: (timeStr: string) => string
}

// Helper function to parse network logs safely
function parseNetworkLogs(logsData: string | any[] | undefined): NetworkLogEntry[] {
  if (!logsData) return [];
  if (Array.isArray(logsData)) return logsData as NetworkLogEntry[];
  if (typeof logsData === 'string') {
    try {
      const parsedLogs = JSON.parse(logsData);
      return Array.isArray(parsedLogs) ? parsedLogs as NetworkLogEntry[] : [];
    } catch (e) {
      console.error('Error parsing network logs JSON:', e);
      return [];
    }
  }
  return [];
}

export function useReport(reportId: string): UseReportResult {
  const { isAuthenticated } = useAuth()
  const [report, setReport] = useState<ReportDetail | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isError, setIsError] = useState(false)
  const [error, setError] = useState<string | undefined>()

  // UI State moved from page component
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [aiAnalysis, setAiAnalysis] = useState<AiAnalysisState | null>(null)
  const [showAllViolations, setShowAllViolations] = useState(false)
  const [showAllNetworkLogs, setShowAllNetworkLogs] = useState(false)
  const [selectedSeverityFilter, setSelectedSeverityFilter] = useState("all")
  const [selectedNetworkTypeFilter, setSelectedNetworkTypeFilter] = useState("all")
  const [selectedNetworkStatusFilter, setSelectedNetworkStatusFilter] = useState("all")
  const [selectedNetworkPerformanceFilter, setSelectedNetworkPerformanceFilter] = useState("all")
  const [selectedNetworkMethodFilter, setSelectedNetworkMethodFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("failed-tests")

  const fetchReport = useCallback(async () => {
    if (!isAuthenticated || !reportId) return

    setIsLoading(true)
    setIsError(false)
    setError(undefined)
    // Reset AI analysis on refetch unless it's already loading
    // if (!isAnalyzing) {
    //   setAiAnalysis(null);
    // }

    try {
      const response = await reportApi.getReportById(reportId)

      if (!response.success) {
        throw new Error(response.error || "Report data could not be retrieved")
      }

      const reportData = response.report || response.data?.report;
      if (reportData) {
        if (reportData._id && !reportData.id) {
          reportData.id = reportData._id;
        }
        setReport(reportData)
        // Set AI analysis if available in the fetched report
        if (reportData.aiInsight) {
          setAiAnalysis(reportData.aiInsight);
        }
      }
    } catch (err: any) {
      console.error('Report fetch error:', err)
      setIsError(true)
      setError(err.message)
      toast.error(`Failed to load report: ${err.message}`)
    } finally {
      setIsLoading(false)
    }
  }, [isAuthenticated, reportId]) // Removed isAnalyzing dependency

  // Fetch on mount and when reportId changes
  useEffect(() => {
    if (reportId && isAuthenticated) {
      fetchReport()
    }
    // Reset state if reportId becomes null/undefined
    if (!reportId) {
        setReport(null);
        setIsLoading(true);
        setIsError(false);
        setError(undefined);
        setAiAnalysis(null);
    }
  }, [reportId, isAuthenticated, fetchReport])

  // Handle AI Analysis
  const handleRunAiAnalysis = useCallback(() => {
    if (!reportId) return;
    setIsAnalyzing(true)
    setAiAnalysis(null) // Clear previous analysis

    reportApi.analyzeReport(reportId)
      .then(response => {
        if (!response.success) {
          throw new Error(response.error || 'Invalid analysis response format')
        }

        if (response.data?.analysis) {
          setAiAnalysis(response.data.analysis)
          // Update the main report state as well to persist the analysis
          setReport(prevReport => prevReport ? { ...prevReport, aiInsight: response.data.analysis } : null);
          toast.success("AI Analysis complete!")
        } else {
          throw new Error('Invalid analysis response format')
        }
      })
      .catch(error => {
        toast.error(`AI Analysis failed: ${error.message}`)
        // Set a specific error state for AI analysis maybe?
        setAiAnalysis({
          PageMetrics: { Score: "Error", Comment: error.message },
          NetworkStatistics: { Score: "Error", Comment: error.message },
          TotalViolations: { Score: "Error", Comment: error.message },
          NetworkLogs: { Score: "Error", Comment: error.message },
          OverallOptimizationScore: "Error",
          OverallComment: `Analysis failed: ${error.message}`
        })
      })
      .finally(() => {
        setIsAnalyzing(false)
      })
  }, [reportId])

  // Filtered Violations Calculation
  const filteredViolations = useMemo(() => {
    // Check for violations in either enhancedMetrics or metrics
    const violations = report?.enhancedMetrics?.accessibilityData?.violations?.items ||
                       report?.metrics?.accessibilityData?.violations?.items || [];
    if (selectedSeverityFilter === "all") return violations;
    return violations.filter(violation => violation.severity === selectedSeverityFilter);
  }, [report, selectedSeverityFilter]);

  // Raw Network Logs Parsing
  const networkLogs = useMemo(() => {
      // Check for network logs in either enhancedMetrics or metrics
      return parseNetworkLogs(report?.enhancedMetrics?.networkData?.logs ||
                             report?.metrics?.networkData?.logs);
  }, [report]);

  // Filtered Network Logs Calculation
  const filteredNetworkLogs = useMemo(() => {
    let filtered = networkLogs;

    // Filter by type
    if (selectedNetworkTypeFilter !== "all") {
      filtered = filtered.filter(log => log.type?.toLowerCase() === selectedNetworkTypeFilter.toLowerCase());
    }

    // Filter by status code
    if (selectedNetworkStatusFilter !== "all") {
      switch (selectedNetworkStatusFilter) {
        case "success":
          filtered = filtered.filter(log => log.status >= 200 && log.status < 300);
          break;
        case "redirect":
          filtered = filtered.filter(log => log.status >= 300 && log.status < 400);
          break;
        case "client-error":
          filtered = filtered.filter(log => log.status >= 400 && log.status < 500);
          break;
        case "server-error":
          filtered = filtered.filter(log => log.status >= 500);
          break;
        case "failed":
          filtered = filtered.filter(log => log.status >= 400);
          break;
      }
    }

    // Filter by performance
    if (selectedNetworkPerformanceFilter !== "all") {
      switch (selectedNetworkPerformanceFilter) {
        case "slow":
          filtered = filtered.filter(log => {
            const timeMatch = log.time.match(/(\d+\.?\d*)/);
            const timeMs = timeMatch ? parseFloat(timeMatch[1]) : 0;
            return timeMs > 1000; // Requests over 1 second
          });
          break;
        case "fast":
          filtered = filtered.filter(log => {
            const timeMatch = log.time.match(/(\d+\.?\d*)/);
            const timeMs = timeMatch ? parseFloat(timeMatch[1]) : 0;
            return timeMs <= 500; // Requests under 500ms
          });
          break;
        case "api-only":
          filtered = filtered.filter(log => log.type === 'xhr' || (log as any).isApiRequest);
          break;
      }
    }

    // Filter by HTTP method
    if (selectedNetworkMethodFilter !== "all") {
      filtered = filtered.filter(log => log.method?.toUpperCase() === selectedNetworkMethodFilter.toUpperCase());
    }

    return filtered;
  }, [networkLogs, selectedNetworkTypeFilter, selectedNetworkStatusFilter, selectedNetworkPerformanceFilter, selectedNetworkMethodFilter]);

  // Unique Network Types Calculation - Only show relevant types for API monitoring
  const uniqueNetworkTypes = useMemo(() => {
    const types = new Set<string>();
    const relevantTypes = ['xhr', 'document', 'other']; // Only show API-relevant types

    networkLogs.forEach(log => {
      if (log.type && relevantTypes.includes(log.type)) {
        types.add(log.type);
      }
    });

    // Sort types with xhr (API requests) first
    const sortedTypes = Array.from(types).sort((a, b) => {
      if (a === 'xhr') return -1;
      if (b === 'xhr') return 1;
      if (a === 'document') return -1;
      if (b === 'document') return 1;
      return a.localeCompare(b);
    });

    return sortedTypes;
  }, [networkLogs]);

  // Unique HTTP Methods Calculation
  const uniqueHttpMethods = useMemo(() => {
    const methods = new Set<string>();
    networkLogs.forEach(log => {
      if (log.method) {
        methods.add(log.method.toUpperCase());
      }
    });

    // Sort methods with common ones first
    const sortedMethods = Array.from(methods).sort((a, b) => {
      const order = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
      const aIndex = order.indexOf(a);
      const bIndex = order.indexOf(b);

      if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;
      return a.localeCompare(b);
    });

    return sortedMethods;
  }, [networkLogs]);

  // Format Time Helper
  const formatTime = useCallback((timeStr: string): string => {
    const timeNum = parseFloat(timeStr);
    if (!isNaN(timeNum)) return timeNum.toFixed(2) + 'ms';
    return timeStr;
  }, []);

  // Refresh data on visibility change (debounced slightly)
   useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && reportId && isAuthenticated) {
         if (timeoutId) clearTimeout(timeoutId); // Clear previous timeout if exists
        // Debounce the refresh call
         timeoutId = setTimeout(() => {
            const now = Date.now();
            const lastRefreshTime = window.lastReportRefreshTime || 0;
            if (now - lastRefreshTime > 30000) { // 30 seconds minimum between refreshes
              window.lastReportRefreshTime = now;
              console.log("Refreshing report due to visibility change...")
              fetchReport();
            }
        }, 500); // 500ms debounce
      }
    }
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
       if (timeoutId) clearTimeout(timeoutId);
    }
  }, [reportId, isAuthenticated, fetchReport])


  return {
    report,
    isLoading,
    isError,
    error,
    refetch: fetchReport,
    // UI State & Handlers
    aiAnalysis,
    isAnalyzing,
    handleRunAiAnalysis,
    activeTab,
    setActiveTab,
    selectedSeverityFilter,
    setSelectedSeverityFilter,
    selectedNetworkTypeFilter,
    setSelectedNetworkTypeFilter,
    selectedNetworkStatusFilter,
    setSelectedNetworkStatusFilter,
    selectedNetworkPerformanceFilter,
    setSelectedNetworkPerformanceFilter,
    selectedNetworkMethodFilter,
    setSelectedNetworkMethodFilter,
    showAllViolations,
    setShowAllViolations,
    showAllNetworkLogs,
    setShowAllNetworkLogs,
    // Derived Data / Filters
    filteredViolations,
    filteredNetworkLogs,
    uniqueNetworkTypes,
    uniqueHttpMethods,
    // Helpers
    formatTime,
  }
}