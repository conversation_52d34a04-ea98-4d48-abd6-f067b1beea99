"use client"

import React, { useState, useEffect, createContext, useContext, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { API_BASE_URL, ADMIN_ENDPOINTS } from './constants'
import { adminStorageManager } from "@/lib/utils/admin-storage-management"

// Admin User Interface
interface AdminUser {
  id: string
  email: string
  name?: string
  role: string
  accountType: string
}

// Admin Auth Context Interface
interface AdminAuthContextType {
  admin: AdminUser | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  logout: () => void
}

// Create context with default value
const AdminAuthContext = createContext<AdminAuthContextType>({
  admin: null,
  isLoading: true,
  isAuthenticated: false,
  login: async () => ({ success: false }),
  logout: () => {},
})

// Admin Auth Provider Component
export function AdminAuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter()
  const [admin, setAdmin] = useState<AdminUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if admin is already logged in
    const checkAuth = async () => {
      // Öncelikle localStorage'dan token'ı al, yoksa sessionStorage'dan dene
      const token = localStorage.getItem("adminAuthToken") || sessionStorage.getItem("adminAuthToken")

      // Eğer token sessionStorage'da bulunursa, localStorage'a taşı
      if (!localStorage.getItem("adminAuthToken") && sessionStorage.getItem("adminAuthToken")) {
        localStorage.setItem("adminAuthToken", sessionStorage.getItem("adminAuthToken") || "")
        // Admin user bilgisini de taşı
        if (sessionStorage.getItem("adminUser")) {
          localStorage.setItem("adminUser", sessionStorage.getItem("adminUser") || "")
        }
      }
      if (!token) {
        setIsLoading(false)
        return
      }

      try {
        const response = await fetch(`${API_BASE_URL}${ADMIN_ENDPOINTS.VALIDATE}`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        const data = await response.json()

        // Backend artık data wrapper kullanıyor, bu yüzden data.data.admin kullanmalıyız
        const authData = data.data || data; // Geriye dönük uyumluluk için
        const admin = authData.admin || data.admin; // Geriye dönük uyumluluk için

        if (data.success && admin) {
          // Ensure admin object has all required fields
          const adminData = {
            id: admin.id,
            email: admin.email,
            name: admin.name || "",
            role: admin.role,
            accountType: admin.accountType || "admin", // accountType yoksa 'admin' olarak ayarla
          }

          setAdmin(adminData)

          // Also update the stored admin data
          localStorage.setItem("adminUser", JSON.stringify(adminData))
        } else {
          // Token is invalid, clear it
          localStorage.removeItem("adminAuthToken")
          sessionStorage.removeItem("adminAuthToken")
          localStorage.removeItem("adminUser")
        }
      } catch (error) {
        console.error("Admin auth validation error:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      const response = await fetch(`${API_BASE_URL}${ADMIN_ENDPOINTS.LOGIN}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (!data.success) {
        return { success: false, error: data.error || "Login failed" }
      }

      // Backend artık data wrapper kullanıyor, bu yüzden data.data.admin ve data.data.token kullanmalıyız
      const authData = data.data || data; // Geriye dönük uyumluluk için
      const admin = authData.admin || data.admin; // Geriye dönük uyumluluk için
      const token = authData.token || data.token; // Geriye dönük uyumluluk için

      // Ensure admin object includes required fields
      const adminData = {
        id: admin.id,
        email: admin.email,
        name: admin.name || "",
        role: admin.role,
        accountType: admin.accountType || "admin", // accountType yoksa 'admin' olarak ayarla
      }

      // Store token and admin data
      localStorage.setItem("adminAuthToken", token)
      localStorage.setItem("adminUser", JSON.stringify(adminData))

      setAdmin(adminData)
      return { success: true }
    } catch (error) {
      console.error("Admin login error:", error)
      return { success: false, error: "Connection error. Please try again." }
    }
  }

  const logout = () => {
    // Clear admin storage through admin storage manager
    adminStorageManager.clearOnLogout()
    setAdmin(null)
    router.push("/admin/auth")
  }

  return (
    <AdminAuthContext.Provider
      value={{
        admin,
        isLoading,
        isAuthenticated: !!admin,
        login,
        logout,
      }}
    >
      {children}
    </AdminAuthContext.Provider>
  )
}

// Hook to use admin auth context
export function useAdminAuth() {
  return useContext(AdminAuthContext)
}

// Route guard component for admin routes
export function AdminRouteGuard({ children }: { children: ReactNode }) {
  const { isAuthenticated, isLoading } = useAdminAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/admin/auth")
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}

// Helper function for authenticated API requests
export async function fetchWithAdminAuth(url: string, options: RequestInit = {}) {
  // Öncelikle localStorage'dan token'ı al, yoksa sessionStorage'dan dene
  const token = localStorage.getItem("adminAuthToken") || sessionStorage.getItem("adminAuthToken")

  // Eğer token sessionStorage'da bulunursa, localStorage'a taşı
  if (!localStorage.getItem("adminAuthToken") && sessionStorage.getItem("adminAuthToken")) {
    localStorage.setItem("adminAuthToken", sessionStorage.getItem("adminAuthToken") || "")
    // Admin user bilgisini de taşı
    if (sessionStorage.getItem("adminUser")) {
      localStorage.setItem("adminUser", sessionStorage.getItem("adminUser") || "")
    }
  }

  const headers = {
    ...options.headers,
    "Authorization": token ? `Bearer ${token}` : "",
    "Content-Type": options.method === "GET" ? undefined : (options.headers as any)?.["Content-Type"] || "application/json",
  }

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    })

    // If unauthorized, redirect to login
    if (response.status === 401) {
      localStorage.removeItem("adminAuthToken")
      sessionStorage.removeItem("adminAuthToken")
      localStorage.removeItem("adminUser")
      sessionStorage.removeItem("adminUser")

      if (typeof window !== "undefined") {
        window.location.href = "/admin/auth"
      }
    }

    return response
  } catch (error) {
    console.error("Fetch error:", error)
    throw error
  }
}
