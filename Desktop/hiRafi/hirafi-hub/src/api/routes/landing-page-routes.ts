/**
 * Landing Page Routes
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import { getSystemSettingByType } from '../../services/mongo/systemSettingsService.js';
import { SystemSettingType } from '../../models/system-settings.js';
import { sendLandingPageEmail } from '../../services/mail/landingPageMailService.js';

const router = Router();

/**
 * @route   POST /api/landing-page/contact
 * @desc    Submit contact form from landing page
 * @access  Public
 */
router.post('/contact', async (req: Request, res: Response) => {
  try {
    logger.info('[LANDING PAGE] Contact form submission');

    // Validate request body
    const { name, email, company, message, phone, preferredDate } = req.body;

    if (!name || !email || !company || !message) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // Validate business email (not gmail, hotmail, etc.)
    const personalEmailDomains = [
      // Çok Yaygın Global Sağlayıcılar
      'gmail.com',
      'googlemail.com', // Gmail'in eski/alternatif alan adı
      'hotmail.com',
      'outlook.com',
      'live.com',       // Microsoft'un diğer alan adları
      'msn.com',
      'yahoo.com',
      'aol.com',
      'icloud.com',     // Apple
      'me.com',         // Apple (eski)
      'mac.com',        // Apple (daha eski)
    
      // Diğer Popüler Global ve Bölgesel Sağlayıcılar
      'mail.com',
      'gmx.com',
      'gmx.net',        // GMX varyasyonu
      'yandex.com',     // Rusya ve BDT'de popüler
      'mail.ru',        // Rusya ve BDT'de popüler
      'protonmail.com', // Gizlilik odaklı
      'pm.me',
      'proton.me',      // Proton'un yeni alan adı
      'zoho.com',       // Hem kişisel hem iş planları var, dikkatli kullanılmalı
      'zohomail.com',   // Zoho'nun mail odaklı alan adı (genellikle ücretsiz/kişisel)
      'inbox.com',
      'web.de',         // Almanya'da popüler
      'seznam.cz',      // Çek Cumhuriyeti'nde popüler
      'wp.pl',          // Polonya'da popüler
      'onet.pl',        // Polonya'da popüler
      'interia.pl',     // Polonya'da popüler
      'libero.it',      // İtalya'da popüler
      'virgilio.it',    // İtalya'da popüler
      'orange.fr',      // Fransa'da popüler (ISP bağlantılı olabilir)
      'free.fr',        // Fransa'da popüler (ISP bağlantılı olabilir)
    
      'yahoo.co.uk', 'yahoo.fr', 'yahoo.de', 'yahoo.it', 'yahoo.es', // Yahoo ülke varyasyonları
      'hotmail.co.uk', 'hotmail.fr', 'hotmail.de', // Hotmail/Outlook ülke varyasyonları
      'live.co.uk', 'live.fr', 'live.de',
      'outlook.de', 'outlook.fr', 'outlook.es',
      'yandex.ru', 'yandex.ua', 'yandex.by', 'yandex.kz' // Yandex ülke varyasyonları
    ];

    const domain = email.split('@')[1]?.toLowerCase();
    if (domain && personalEmailDomains.includes(domain)) {
      return res.status(400).json({
        success: false,
        error: 'Please use your company email address'
      });
    }

    // Validate future date
    if (preferredDate) {
      const selectedDate = new Date(preferredDate);
      selectedDate.setHours(0, 0, 0, 0);

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        return res.status(400).json({
          success: false,
          error: 'Please select a future date for your meeting'
        });
      }
    }

    // Get landing page settings
    const landingPageSettingResult = await getSystemSettingByType(SystemSettingType.LANDING_PAGE);
    let thankYouMessage = 'Thank you for your interest! We will contact you shortly.';

    if (landingPageSettingResult.success && landingPageSettingResult.setting) {
      const landingPageSetting = landingPageSettingResult.setting;
      thankYouMessage = landingPageSetting.config.thankYouMessage || thankYouMessage;

      // Send email notifications asynchronously
      // We don't await this promise to make the API response faster
      // Instead, we fire and forget, logging any errors that occur
      const emailData = {
        name,
        email,
        company,
        message,
        phone: phone || 'Not provided',
        preferredDate: preferredDate || 'Not specified',
        landingPageSetting
      };
      // Use Promise to handle the email sending asynchronously
      Promise.resolve().then(async () => {
        try {
          logger.info(`[LANDING PAGE] Attempting to send emails for contact from ${email}`);

          const emailResult = await sendLandingPageEmail(emailData);

          if (emailResult) {
            logger.info(`[LANDING PAGE] Email notifications successfully sent for contact from ${email}`);
          } else {
            logger.warn(`[LANDING PAGE] Email sending returned false for contact from ${email}`);
          }
        } catch (emailError: any) {
          logger.error(`[LANDING PAGE] Error sending email notifications: ${emailError.message}`);
          logger.error(`[LANDING PAGE] Error stack: ${emailError.stack}`);
        }
      }).catch(err => {
        logger.error(`[LANDING PAGE] Unhandled error in async email sending: ${err.message}`);
      });
    } else {
      logger.warn('[LANDING PAGE] Landing page settings not found, using default thank you message');
    }

    return res.json({
      success: true,
      message: 'Contact form submitted successfully',
      thankYouMessage
    });
  } catch (error: any) {
    logger.error(`[LANDING PAGE] Error in POST /api/landing-page/contact: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
