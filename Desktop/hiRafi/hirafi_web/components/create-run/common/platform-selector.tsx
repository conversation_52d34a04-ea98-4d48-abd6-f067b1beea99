"use client"

import * as React from "react"
import { useRunForm } from "../run-context"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Laptop, Smartphone } from "lucide-react"
import { PlatformType } from "../run-context"
import { motion } from "framer-motion"

export function PlatformSelector() {
  const { formData, handlePlatformChange } = useRunForm()
  
  const handleChange = (value: string) => {
    handlePlatformChange(value as PlatformType)
  }

  return (
    <div className="space-y-4">
      <Label className="text-base mb-3 block">Platform</Label>
      <RadioGroup
        value={formData.platform || "web"}
        onValueChange={handleChange}
        className="grid grid-cols-2 gap-4"
      >
        <div>
          <RadioGroupItem value="web" id="platform-web" className="peer sr-only" />
          <Label
            htmlFor="platform-web"
            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-blue-500 peer-data-[state=checked]:bg-blue-50 [&:has([data-state=checked])]:border-blue-500 [&:has([data-state=checked])]:bg-blue-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-blue-950/20 dark:[&:has([data-state=checked])]:bg-blue-950/20 transition-all duration-200"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="mb-2"
            >
              <Laptop className="h-6 w-6 text-blue-500" />
            </motion.div>
            <div className="text-center">
              <p className="text-sm font-medium">Web</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Browser-based testing</p>
            </div>
          </Label>
        </div>

        <div>
          <RadioGroupItem value="android" id="platform-android" className="peer sr-only" />
          <Label
            htmlFor="platform-android"
            className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 hover:border-gray-200 peer-data-[state=checked]:border-green-500 peer-data-[state=checked]:bg-green-50 [&:has([data-state=checked])]:border-green-500 [&:has([data-state=checked])]:bg-green-50 dark:bg-gray-950 dark:hover:bg-gray-900 dark:peer-data-[state=checked]:bg-green-950/20 dark:[&:has([data-state=checked])]:bg-green-950/20 transition-all duration-200"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="mb-2"
            >
              <Smartphone className="h-6 w-6 text-green-500" />
            </motion.div>
            <div className="text-center">
              <p className="text-sm font-medium">Android</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">Mobile app testing</p>
            </div>
          </Label>
        </div>
      </RadioGroup>
      
      {/* Platform change notification */}
      {formData.platform && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 p-2 rounded-md border border-gray-200 dark:border-gray-800"
        >
          <p>
            {formData.platform === 'web' 
              ? "Web platform selected. Configure browser settings in the Environment tab."
              : "Android platform selected. Configure device and app settings in the Environment tab."
            }
          </p>
        </motion.div>
      )}
    </div>
  )
}
