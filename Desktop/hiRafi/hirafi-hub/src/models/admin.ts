/**
 * Admin Model
 * Admin kullanıcıları için veri modeli
 */

import { ObjectId } from 'mongodb';

/**
 * Admin kullanıcı rolleri
 */
export enum AdminRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

/**
 * Admin kullanıcı modeli
 */
export interface Admin {
  _id?: ObjectId;
  id: string;
  email: string;
  password: string; // Hashed password
  name?: string;
  role: AdminRole;
  accountType: string; // Admin hesap türü
  permissions?: string[];
  lastLogin?: Date;
  createdAt: Date;
  updatedAt?: Date;
  active: boolean;
}

/**
 * Admin login request
 */
export interface AdminLoginRequest {
  email: string;
  password: string;
}

/**
 * Admin login response
 */
export interface AdminLoginResponse {
  success: boolean;
  token?: string;
  admin?: {
    id: string;
    email: string;
    name?: string;
    role: string;
    accountType: string;
  };
  error?: string;
}

/**
 * Admin token payload
 */
export interface AdminTokenPayload {
  id: string;
  email: string;
  role: string;
  accountType: string;
  iat?: number;
  exp?: number;
}

/**
 * Admin validation response
 */
export interface AdminValidationResponse {
  success: boolean;
  admin?: {
    id: string;
    email: string;
    name?: string;
    role: string;
    accountType: string;
  };
  error?: string;
}
