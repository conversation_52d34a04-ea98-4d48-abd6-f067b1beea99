import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Sparkles, Video, TestTube, Bug, Smartphone, Globe } from 'lucide-react';

// Import other tab content components
import { ReportTestSteps } from './ReportTestSteps';
import { ReportTimeline } from './ReportTimeline';
import { ReportScreenshots } from './ReportScreenshots';
import { ReportAdvancedMetrics } from './ReportAdvancedMetrics';
import { ReportAInalysis } from './ReportAInalysis';
import { TestRailIntegration } from './TestRailIntegration';
import { RunReportJiraIntegration } from '../run-report/JiraIntegration';
import { useReport } from '@/hooks/useReport'; // Import the hook
import { detectReportPlatform } from '@/lib/utils/platform-utils';

interface ReportTabsProps {
  hookResult: ReturnType<typeof useReport>; // Pass the entire hook result
}

export function ReportTabs({ hookResult }: ReportTabsProps) {
  const {
    report,
    activeTab,
    setActiveTab,
    // Pass other needed states/handlers to child components
  } = hookResult;

  if (!report) return null;

  // Detect platform for conditional rendering
  const platform = detectReportPlatform(report);
  const isAndroid = platform === 'android';

  // Video tab should only be shown if videoUrl exists
  const hasVideo = !!report.videoUrl;

  // TestRail tab should only be shown if testrailRunLink exists
  const hasTestRail = !!report.testrailRunLink;

  // Jira integration should always be available if configured
  const hasJira = true; // We'll let the component handle configuration checking internally

  // Advanced metrics should be shown if relevant data exists
  const hasRelevantMetrics = report.enhancedMetrics || report.metrics;

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="mb-4">
        <TabsTrigger value="failed-tests">Test Steps</TabsTrigger>
        <TabsTrigger value="timeline">Timeline</TabsTrigger>
        <TabsTrigger value="screenshots">Screenshots</TabsTrigger>
        {hasVideo && (
          <TabsTrigger value="video">
            <Video className="h-4 w-4 mr-1.5" />
            Video
          </TabsTrigger>
        )}
        {hasTestRail && (
          <TabsTrigger value="testrail">
            <TestTube className="h-4 w-4 mr-1.5" />
            TestRail
          </TabsTrigger>
        )}
        {hasJira && (
          <TabsTrigger value="jira">
            <Bug className="h-4 w-4 mr-1.5" />
            Jira
          </TabsTrigger>
        )}
        {/* Advanced Metrics - platform-aware label and icon */}
        {hasRelevantMetrics && (
          <TabsTrigger value="advanced-metrics">
            {isAndroid ? (
              <>
                <Smartphone className="h-4 w-4 mr-1.5" />
                Device Metrics
              </>
            ) : (
              <>
                <Globe className="h-4 w-4 mr-1.5" />
                Web Metrics
              </>
            )}
          </TabsTrigger>
        )}

        {/* AI Analysis - platform-aware label */}
        <TabsTrigger
          value="aianalyze"
          className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:from-indigo-600 hover:to-purple-700 shadow-md hover:shadow-lg transition-all duration-200"
        >
          <Sparkles className="h-4 w-4 mr-1.5 text-yellow-300" />
          {isAndroid ? 'AI Device Analysis' : 'AI Web Analysis'}
        </TabsTrigger>
      </TabsList>

      <TabsContent value="failed-tests" className="space-y-4">
        <ReportTestSteps steps={report.steps || []} report={report} />
      </TabsContent>

      <TabsContent value="timeline" className="space-y-4">
        <ReportTimeline report={report} />
      </TabsContent>

      <TabsContent value="screenshots" className="space-y-4">
        <ReportScreenshots steps={report.steps || []} report={report} />
      </TabsContent>

      {hasVideo && (
        <TabsContent value="video" className="space-y-4">
          <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
            <h3 className="text-lg font-medium mb-3">
              {isAndroid ? 'Device Screen Recording' : 'Test Video Recording'}
            </h3>
            <div className="relative aspect-video w-full overflow-hidden rounded-lg">
              <video
                src={report.videoUrl}
                controls
                className="w-full h-full"
                poster="/images/video-poster.svg"
              >
                Your browser does not support the video tag.
              </video>
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {isAndroid
                ? 'This video was recorded during the test execution and shows the actual device interactions.'
                : 'This video was recorded during the test execution and shows the actual browser interactions.'
              }
            </p>
          </div>
        </TabsContent>
      )}

      {hasTestRail && (
        <TabsContent value="testrail" className="space-y-4">
          <TestRailIntegration report={report} />
        </TabsContent>
      )}

      {hasJira && (
        <TabsContent value="jira" className="space-y-4">
          <RunReportJiraIntegration report={report as any} />
        </TabsContent>
      )}

      {hasRelevantMetrics && (
        <TabsContent value="advanced-metrics" className="space-y-6">
          <ReportAdvancedMetrics hookResult={hookResult} />
        </TabsContent>
      )}

      <TabsContent value="aianalyze" className="space-y-6">
        <ReportAInalysis hookResult={hookResult} />
      </TabsContent>
    </Tabs>
  );
}