/**
 * Redis Lock Service
 * Redlock algoritması kullanarak dağıtık kilitleme mekanizması sağlar
 */

import { Redis } from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../utils/logger.js';
import { redisConnection } from './redisConnection.js';
import { EventEmitter } from 'events';

export class LockService extends EventEmitter {
  private static instance: LockService;
  private client: Redis | null = null;
  private lockMap: Map<string, { value: string, expiresAt: number }>;
  private lockExtendIntervals: Map<string, NodeJS.Timeout>;
  private isInitialized: boolean = false;

  // Kilitleme için Lua scriptleri
  private readonly acquireLockScript = `
    if redis.call('exists', KEYS[1]) == 0 then
      return redis.call('set', KEYS[1], ARGV[1], 'PX', ARGV[2])
    else
      return nil
    end
  `;

  private readonly extendLockScript = `
    if redis.call('get', KEYS[1]) == ARGV[1] then
      return redis.call('pexpire', KEYS[1], ARGV[2])
    else
      return 0
    end
  `;

  private readonly releaseLockScript = `
    if redis.call('get', KEYS[1]) == ARGV[1] then
      return redis.call('del', KEYS[1])
    else
      return 0
    end
  `;

  private constructor() {
    super();
    this.lockMap = new Map();
    this.lockExtendIntervals = new Map();

    // Try to initialize the client, but don't fail if it's not available yet
    this.initializeClient();
  }

  /**
   * Initialize the Redis client - SIMPLIFIED to use centralized connection
   * @returns True if client was initialized successfully
   */
  private initializeClient(): boolean {
    if (this.isInitialized && this.client) {
      return true;
    }

    try {
      const client = redisConnection.getClient();

      if (!client) {
        logger.warn('LockService: Redis client not available yet, will retry on first use');
        return false;
      }

      this.client = client as Redis;
      this.isInitialized = true;
      logger.info('LockService: Redis client initialized successfully');
      return true;
    } catch (error) {
      logger.error(`LockService: Error initializing Redis client: ${error}`);
      return false;
    }
  }

  /**
   * Singleton instance'ı al
   */
  public static getInstance(): LockService {
    if (!LockService.instance) {
      LockService.instance = new LockService();
    }
    return LockService.instance;
  }

  /**
   * Initialize the lock service with Redis connection
   * This should be called after Redis connection is established
   * @returns True if initialization was successful
   */
  public initialize(): boolean {
    return this.ensureClient();
  }

  /**
   * Kilit oluştur
   * @param resource Kilitlenecek kaynak adı
   * @param ttl Kilit süresi (ms)
   * @param retryCount Yeniden deneme sayısı
   * @param retryDelay Yeniden deneme aralığı (ms)
   * @returns Kilit değeri (başarılı) veya null (başarısız)
   */
  /**
   * Ensure Redis client is initialized
   * @returns True if client is available
   */
  private ensureClient(): boolean {
    if (!this.client || !this.isInitialized) {
      return this.initializeClient();
    }
    return true;
  }

  public async acquireLock(
    resource: string,
    ttl: number = 30000,
    retryCount: number = 3,
    retryDelay: number = 200
  ): Promise<string | null> {
    // Ensure Redis client is available
    if (!this.ensureClient()) {
      logger.error(`LockService: Cannot acquire lock for ${resource} - Redis client not available`);
      return null;
    }

    const lockKey = `lock:${resource}`;
    const lockValue = uuidv4();

    // Kilidi almayı dene
    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        // Lua script ile atomik kilitleme
        const result = await this.client!.eval(
          this.acquireLockScript,
          1,
          lockKey,
          lockValue,
          ttl.toString()
        );

        if (result === 'OK') {
          logger.debug(`LockService: Acquired lock for ${resource} with value ${lockValue}`);

          // Kilidi yerel olarak sakla
          this.lockMap.set(resource, {
            value: lockValue,
            expiresAt: Date.now() + ttl
          });

          // Otomatik kilit yenileme başlat
          this.startLockExtension(resource, lockKey, lockValue, ttl);

          // Kilit olayını yayınla
          this.emit('lockAcquired', resource, lockValue);

          return lockValue;
        }

        // Başarısız olursa ve yeniden deneme varsa bekle
        if (attempt < retryCount) {
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      } catch (error) {
        logger.error(`LockService: Error acquiring lock for ${resource}: ${error}`);

        // Son denemede hata olursa null döndür
        if (attempt === retryCount) {
          return null;
        }

        // Bekle ve yeniden dene
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    logger.warn(`LockService: Failed to acquire lock for ${resource} after ${retryCount} attempts`);
    return null;
  }

  /**
   * Kilidi yenile
   * @param resource Kilitli kaynak adı
   * @param lockValue Kilit değeri
   * @param ttl Yeni kilit süresi (ms)
   * @returns Başarılı mı
   */
  public async extendLock(
    resource: string,
    lockValue: string,
    ttl: number = 30000
  ): Promise<boolean> {
    // Ensure Redis client is available
    if (!this.ensureClient()) {
      logger.error(`LockService: Cannot extend lock for ${resource} - Redis client not available`);
      return false;
    }

    const lockKey = `lock:${resource}`;

    try {
      // Lua script ile atomik kilit yenileme
      const result = await this.client!.eval(
        this.extendLockScript,
        1,
        lockKey,
        lockValue,
        ttl.toString()
      );

      if (result === 1) {
        logger.debug(`LockService: Extended lock for ${resource}`);

        // Yerel kilit bilgisini güncelle
        const lockInfo = this.lockMap.get(resource);
        if (lockInfo) {
          lockInfo.expiresAt = Date.now() + ttl;
          this.lockMap.set(resource, lockInfo);
        }

        return true;
      }

      logger.warn(`LockService: Failed to extend lock for ${resource}, lock value mismatch or expired`);
      return false;
    } catch (error) {
      logger.error(`LockService: Error extending lock for ${resource}: ${error}`);
      return false;
    }
  }

  /**
   * Kilidi serbest bırak
   * @param resource Kilitli kaynak adı
   * @param lockValue Kilit değeri
   * @returns Başarılı mı
   */
  public async releaseLock(resource: string, lockValue: string): Promise<boolean> {
    // Ensure Redis client is available
    if (!this.ensureClient()) {
      logger.error(`LockService: Cannot release lock for ${resource} - Redis client not available`);
      return false;
    }

    const lockKey = `lock:${resource}`;

    try {
      // Lua script ile atomik kilit serbest bırakma
      const result = await this.client!.eval(
        this.releaseLockScript,
        1,
        lockKey,
        lockValue
      );

      // Kilit yenileme interval'ını temizle
      this.stopLockExtension(resource);

      // Yerel kilit bilgisini temizle
      this.lockMap.delete(resource);

      if (result === 1) {
        logger.debug(`LockService: Released lock for ${resource}`);

        // Kilit serbest bırakma olayını yayınla
        this.emit('lockReleased', resource);

        return true;
      }

      logger.warn(`LockService: Failed to release lock for ${resource}, lock value mismatch or expired`);
      return false;
    } catch (error) {
      logger.error(`LockService: Error releasing lock for ${resource}: ${error}`);
      return false;
    }
  }

  /**
   * Otomatik kilit yenileme başlat
   * @param resource Kaynak adı
   * @param lockKey Kilit anahtarı
   * @param lockValue Kilit değeri
   * @param ttl Kilit süresi (ms)
   */
  private startLockExtension(
    resource: string,
    lockKey: string,
    lockValue: string,
    ttl: number
  ): void {
    // Önceki interval varsa temizle
    this.stopLockExtension(resource);

    // TTL'in 2/3'ünde yenileme yap
    const extensionInterval = Math.floor(ttl * 2 / 3);

    // Periyodik yenileme interval'ı oluştur
    const interval = setInterval(async () => {
      try {
        const success = await this.extendLock(resource, lockValue, ttl);

        if (!success) {
          logger.warn(`LockService: Auto-extension failed for ${resource}, stopping extension`);
          this.stopLockExtension(resource);
        }
      } catch (error) {
        logger.error(`LockService: Error in lock extension for ${resource}: ${error}`);
        this.stopLockExtension(resource);
      }
    }, extensionInterval);

    // Interval'ı sakla
    this.lockExtendIntervals.set(resource, interval);
  }

  /**
   * Kilit yenileme durdur
   * @param resource Kaynak adı
   */
  private stopLockExtension(resource: string): void {
    const interval = this.lockExtendIntervals.get(resource);

    if (interval) {
      clearInterval(interval);
      this.lockExtendIntervals.delete(resource);
    }
  }

  /**
   * Tüm kilitleri temizle
   */
  private cleanupAllLocks(): void {
    // Tüm interval'ları temizle
    for (const [resource, interval] of this.lockExtendIntervals.entries()) {
      clearInterval(interval);
      logger.debug(`LockService: Cleaned up lock extension for ${resource}`);
    }

    // Map'leri temizle
    this.lockExtendIntervals.clear();
    this.lockMap.clear();

    logger.info('LockService: Cleaned up all locks');
  }

  /**
   * Check if a lock exists for a resource without acquiring it
   * @param resource Resource name to check
   * @returns Promise resolving to true if lock exists, false otherwise
   */
  public async checkLockExists(resource: string): Promise<boolean> {
    // Ensure Redis client is available
    if (!this.ensureClient()) {
      logger.error(`LockService: Cannot check lock for ${resource} - Redis client not available`);
      return false;
    }

    const lockKey = `lock:${resource}`;
    try {
      const exists = await this.client!.exists(lockKey);
      return exists === 1;
    } catch (error) {
      logger.error(`LockService: Error checking lock existence for ${resource}: ${error}`);
      return false;
    }
  }

  /**
   * Get information about a lock
   * @param resource Resource name
   * @returns Promise resolving to lock value if lock exists, null otherwise
   */
  public async getLockInfo(resource: string): Promise<{ value: string, ttl: number } | null> {
    // Ensure Redis client is available
    if (!this.ensureClient()) {
      logger.error(`LockService: Cannot get lock info for ${resource} - Redis client not available`);
      return null;
    }

    const lockKey = `lock:${resource}`;
    try {
      // Get the lock value
      const value = await this.client!.get(lockKey);

      if (!value) {
        return null;
      }

      // Get the remaining TTL
      const ttl = await this.client!.pttl(lockKey);

      return {
        value,
        ttl: ttl > 0 ? ttl : 0
      };
    } catch (error) {
      logger.error(`LockService: Error getting lock info for ${resource}: ${error}`);
      return null;
    }
  }
}

// Singleton instance'ı dışa aktar
export const lockService = LockService.getInstance();
export default lockService;
