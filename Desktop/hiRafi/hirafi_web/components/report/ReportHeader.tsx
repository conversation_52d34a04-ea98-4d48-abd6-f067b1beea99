import React, { useState } from 'react';
import {
  ArrowLeft,
  Download,
  Share2,
  Loader2,
  AlertTriangle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ReportDetail } from '@/hooks/useReport';
import { downloadReportAsPdf } from '@/lib/api/report-api';
import { toast } from '@/lib/utils/toast-utils';
import { ShareReportModal } from './ShareReportModal';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ReportHeaderProps {
  report: ReportDetail | null;
}

export function ReportHeader({ report }: ReportHeaderProps) {
  const router = useRouter();
  const [isDownloading, setIsDownloading] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  if (!report) return null;

  const isPassed = report.summary.failed === 0 && report.summary.errors === 0;

  const handleDownloadPdf = async () => {
    if (!report.id) {
      toast.error("Report ID is missing");
      return;
    }

    setIsDownloading(true);
    toast.info("Preparing PDF report for download...");

    try {
      const result = await downloadReportAsPdf(report.id);
      if (!result.success) {
        toast.error(`Failed to download report: ${result.error || 'Unknown error'}`);
      } else {
        toast.success("PDF report downloaded successfully");
      }
    } catch (error: any) {
      // Check for authentication errors
      if (error.message.includes('Authentication token not found') ||
          error.message.includes('No authorization token provided')) {
        toast.error("Authentication error", {
          description: "Please log in again to download the report."
        });
      } else if (error.message.includes('401') || error.message.includes('403')) {
        toast.error("Authorization error", {
          description: "You don't have permission to download this report."
        });
      } else {
        toast.error(`Error downloading report: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setIsDownloading(false);
    }
  };

  // Check if the report has a SauceLabs error
  const hasSauceLabsError = report.error && (
    report.error.includes('SauceLabs Credentials Error:') ||
    report.error.includes('SauceLabs Connection Error:')
  );

  return (
    <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" className="rounded-full" onClick={() => router.push("/reports")}>
          <ArrowLeft className="h-5 w-5" />
        </Button>

        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            {report.scenarioTitle}
            <Badge
              className={`ml-2 ${isPassed
                ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300"
                : "bg-rose-100 text-rose-700 dark:bg-rose-900/30 dark:text-rose-300"
              } border-0`}
            >
              {isPassed ? "Passed" : "Failed"}
            </Badge>
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            {hasSauceLabsError ? "SauceLabs configuration issue detected" : (report.error || "Detailed report showing test steps and results")}
          </p>
        </div>

        <div className="ml-auto flex items-center gap-2">
          <Button
            variant="outline"
            className="gap-1"
            onClick={() => setIsShareModalOpen(true)}
          >
            <Share2 className="h-4 w-4" />
            Share
          </Button>
          <Button
            variant="outline"
            className="gap-1"
            onClick={handleDownloadPdf}
            disabled={isDownloading}
          >
            {isDownloading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Downloading...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Download PDF
              </>
            )}
          </Button>

          {/* Share Modal */}
          <ShareReportModal
            isOpen={isShareModalOpen}
            onClose={() => setIsShareModalOpen(false)}
            reportId={report.id}
            reportName={report.name}
          />
        </div>
      </div>

      {/* SauceLabs Error Alert */}
      {hasSauceLabsError && (
        <div className="mt-4">
          <Alert className="bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
              <div>
                <AlertDescription className="font-medium text-amber-800 dark:text-amber-300">
                  {report.error}
                </AlertDescription>
                <AlertDescription className="text-amber-700 dark:text-amber-400 mt-1">
                  Please check your SauceLabs configuration and credentials in the plugin settings. Make sure your SauceLabs account has valid credentials and the selected device is available.
                </AlertDescription>
              </div>
            </div>
          </Alert>
        </div>
      )}
    </header>
  );
}