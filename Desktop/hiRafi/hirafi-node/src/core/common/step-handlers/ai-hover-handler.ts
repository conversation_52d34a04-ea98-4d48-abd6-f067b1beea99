/**
 * Common AI Hover Handler
 * Handles executing AI hover steps
 */

import { ScenarioStep } from '../../../models/types.js';
import { IStepHandler } from '../interfaces/step-handler.interface.js';
import { logger } from '../../../utils/logger.js';
import { formatAIResultForLog } from '../../../utils/ai-log-formatter.js';
import { simplifyErrorMessage } from '../utils/error-handling.js';

/**
 * Common AI Hover Handler
 * Handles executing AI hover steps using the AI agent
 */
export class CommonAiHoverHandler implements IStepHandler {
  /**
   * Check if this handler can handle the given step
   * @param step The step to check
   * @returns True if this handler can handle the step
   */
  canHandle(step: ScenarioStep): boolean {
    return step.type === 'aiHover';
  }

  /**
   * Get the name of this handler
   * @returns The handler name
   */
  getName(): string {
    return 'CommonAiHoverHandler';
  }

  /**
   * Execute an AI hover step
   * @param target The target to execute the step on (not used for AI steps)
   * @param step The step to execute
   * @param agent The AI agent to use
   * @param logs Array to append log messages to
   * @returns Promise resolving to the result of the step execution
   */
  async execute(_target: any, step: ScenarioStep, agent: any, logs: string[]): Promise<{ success: boolean; error: string | null; data?: any }> {
    // For aiHover, use prompt field as primary source, fallback to value/name/description for backward compatibility
    const hoverTarget = step.prompt || step.value || step.name || step.description;

    if (!hoverTarget) {
      return { success: false, error: 'Missing hover target for aiHover step. Please provide a prompt field.' };
    }

    try {
      const options = step.deepThink ? { deepThink: true } : {};

      logger.debug(`CommonAiHoverHandler: Executing hover over "${hoverTarget}" with options: ${JSON.stringify(options)}`);

      const result = await agent.aiHover(hoverTarget || '', options);
      logger.debug(`aiHover result: ${JSON.stringify(result)}`);

      const isSuccess = result && !result.error;

      const logEntry = formatAIResultForLog('aiHover', hoverTarget, result);
      logEntry.success = isSuccess;

      if (result && result.error) {
        logEntry.error = simplifyErrorMessage(result.error);
      }

      logs.push(JSON.stringify(logEntry));

      if (!isSuccess) {
        const errorMessage = result.error || `Hover failed: ${hoverTarget}`;
        return {
          success: false,
          error: simplifyErrorMessage(errorMessage)
        };
      }

      return {
        success: true,
        error: null,
        data: {
          aiDuration: result?.metadata?.totalTime
        }
      };
    } catch (error: any) {
      const errorMessage = error.message || error.toString();
      logger.info('AI Hover Error:', errorMessage);
      const simplifiedError = simplifyErrorMessage(errorMessage);

      const errorLogEntry = {
        operation: 'aiHover',
        description: hoverTarget,
        success: false,
        error: simplifiedError,
        metadata: {
          status: 'failed'
        }
      };
      logs.push(JSON.stringify(errorLogEntry));

      logger.error(`CommonAiHoverHandler: Hover failed: ${simplifiedError}`);
      return {
        success: false,
        error: simplifiedError
      };
    }
  }
} 