"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TestTube, Download, CheckCircle2, AlertCircle, Loader2 } from "lucide-react"
import { TestManagementProvider } from "@/store/testManagementStore"
import { useUnifiedTestManagementContext } from "@/contexts/UnifiedTestManagementContext"
import { UnifiedImportService } from "@/lib/services/unifiedImportService"

interface UnifiedImportPanelProps {
  onImportComplete?: (prompt: string, provider: TestManagementProvider, caseDetails: any) => void
  disabled?: boolean
  className?: string
}

export function UnifiedImportPanel({
  onImportComplete,
  disabled = false,
  className = ""
}: UnifiedImportPanelProps) {
  const { store, fetchTestCaseDetails } = useUnifiedTestManagementContext();

  // Extract values from store
  const {
    activeProvider,
    availableProviders,
    testCases,
    providers,
    import: { isEnabled: importMode, selectedCaseId: selectedImportCase, caseDetails: importedCaseDetails },
    loading: { isLoadingImportDetails },
  } = store;

  // Derived values
  const isConnected = activeProvider ? providers[activeProvider]?.isConnected || false : false;

  // Actions from store
  const switchProvider = store.switchProvider;
  const enableImportMode = store.enableImportMode;
  const disableImportMode = store.disableImportMode;
  const selectImportCase = store.setSelectedImportCase;

  // Provider display name helper
  const getProviderDisplayName = (provider: TestManagementProvider) => {
    return provider === 'testrail' ? 'TestRail' : 'Zephyr Scale';
  };



  // Get basic case info from the test cases list (without detailed API call)
  const selectedCaseInfo = selectedImportCase
    ? testCases.find(tc => tc.id === selectedImportCase || tc.key === selectedImportCase)
    : null

  // Handle import completion using UnifiedImportService
  const handleImport = async () => {
    if (!selectedImportCase || !activeProvider || !onImportComplete) return

    try {
      console.log('[Import] Starting import for case:', selectedImportCase, 'provider:', activeProvider)

      // Use UnifiedImportService to import test case with proper prompt generation
      const importResult = await UnifiedImportService.importTestCase({
        caseId: selectedImportCase,
        provider: activeProvider
      })

      if (!importResult.success || !importResult.data) {
        console.error('[Import] Import failed:', importResult.error)
        throw new Error(importResult.error || `Failed to import test case ${selectedImportCase}. Please try again or select a different test case.`)
      }

      console.log('[Import] Import successful:', importResult.data)

      // Call onImportComplete with the properly generated prompt and case details
      onImportComplete(
        importResult.data.prompt,
        activeProvider,
        importResult.data.caseDetails
      )
    } catch (error: any) {
      console.error('[Import] Failed to import test case:', {
        caseId: selectedImportCase,
        provider: activeProvider,
        error: error.message || error
      })
      // Error handling is done in UnifiedImportService
    }
  }

  // No providers available
  if (availableProviders.length === 0) {
    return (
      <Card className={`border-dashed border-gray-300 dark:border-gray-600 ${className}`}>
        <CardContent className="flex flex-col items-center justify-center py-8 text-center">
          <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-4 mb-4">
            <TestTube className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No Test Management Integration
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
            Configure TestRail or Zephyr Scale to import test cases for AI step generation.
          </p>
          <div className="flex gap-2">
            <Button 
              size="sm"
              onClick={() => window.location.href = '/plugins/testrail'}
            >
              Configure TestRail
            </Button>
            <Button 
              size="sm"
              variant="outline"
              onClick={() => window.location.href = '/plugins/zephyrscale'}
            >
              Configure Zephyr Scale
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Only show import panel if there's a selected case
  if (!selectedImportCase) {
    return null
  }

  return (
    <Card className={className}>
      <CardContent className="space-y-4 pt-6">

        {/* Loading state during import */}
        {isLoadingImportDetails && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
            <span className="text-sm text-blue-700 dark:text-blue-300">
              Fetching test case details for import...
            </span>
          </div>
        )}

        {/* Selected case ready for import (no detailed content shown) */}
        {selectedImportCase && !isLoadingImportDetails && (
          <div className="space-y-3">
            <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700 dark:text-green-300">
                Test case selected for import
              </span>
            </div>

            <div className="p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="mb-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                  {selectedCaseInfo?.name || selectedCaseInfo?.title || 'Test Case'}
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Case ID: {selectedImportCase} • Provider: {getProviderDisplayName(activeProvider!)}
                </p>
              </div>

              {/* Basic case info without detailed content (performance optimization) */}
              <div className="space-y-2">
                {selectedCaseInfo?.description && (
                  <div>
                    <h6 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {activeProvider === 'zephyrscale' ? 'Objective' : 'Description'}
                    </h6>
                    <div className="p-2 rounded-md bg-gray-50 dark:bg-gray-800 text-xs">
                      <p className="text-gray-900 dark:text-gray-100 line-clamp-2">
                        {selectedCaseInfo.description}
                      </p>
                    </div>
                  </div>
                )}

                {/* Priority and Status info */}
                <div className="flex gap-2">
                  {selectedCaseInfo?.priority && (
                    <Badge variant="outline" className="text-xs">
                      {selectedCaseInfo.priority}
                    </Badge>
                  )}
                  {selectedCaseInfo?.status && (
                    <Badge variant="outline" className="text-xs">
                      {selectedCaseInfo.status}
                    </Badge>
                  )}
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400 italic">
                  Detailed test steps will be fetched when you click "Import & Generate"
                </div>
              </div>
            </div>

            <Button
              onClick={handleImport}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              disabled={disabled || isLoadingImportDetails}
            >
              {isLoadingImportDetails ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Import & Generate Steps
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
