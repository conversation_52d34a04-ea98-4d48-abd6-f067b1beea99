"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  CheckCircle2,
  Clock,
  Folder,
  FileText,
  Link as LinkIcon,
  X
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"
import { useFolders } from "@/hooks"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"

import { pluginApi } from "@/lib/api"
import { getActiveProviderFromFormData } from "@/lib/utils/scenario-form-utils"

export function ScenarioPreview() {
  const { formData } = useUnifiedScenarioContext()
  const { folders = [] } = useFolders({ autoFetch: true }) // Use unified folders hook
  const [testCaseDetails, setTestCaseDetails] = useState<any>(null)

  // Sanitize steps to prevent "0" from showing up in the UI
  const sanitizedSteps = useMemo(() => {
    if (formData.steps && formData.steps.length > 0) {
      // Deep clone the steps to avoid modifying the original
      return formData.steps.map(step => {
        // For AI steps, ensure prompt and description are the same
        if (["aiAction", "aiAssertion", "aiWaitElement", "aiTap", "aiHover", "aiKeyboardPress", "aiScroll", "aiRightClick", "aiQuery", "aiString", "aiNumber", "aiBoolean", "aiLocate"].includes(step.type)) {
          return {
            ...step,
            // Ensure prompt is not 0 or "0"
            prompt: step.description || step.prompt || ""
          };
        }
        return step;
      });
    }
    return formData.steps;
  }, [formData.steps]);

  // Find selected folder name from ID if it exists
  const selectedFolder = folders.find(folder => folder.id === formData.folderId)

  // Fetch test case details for active provider
  useEffect(() => {
    const activeProvider = getActiveProviderFromFormData(formData)

    if (!activeProvider) {
      setTestCaseDetails(null)
      return
    }

    const fetchTestCaseDetails = async () => {
      try {
        let result
        let caseId

        if (activeProvider === 'testrail' && formData.testrailCases?.length > 0) {
          caseId = formData.testrailCases[0]
          console.log("Fetching TestRail case details for case ID:", caseId)
          result = await pluginApi.getTestRailCaseSteps(caseId)
          console.log("TestRail API response:", result)

          if (result.success) {
            // The API returns caseData directly in the response
            const caseData = result.caseData || result.data?.caseData || result.case
            if (caseData) {
              console.log("Setting TestRail case details")
              setTestCaseDetails({ ...caseData, provider: 'testrail' })
            } else {
              console.error("TestRail API response is missing case data")
              setTestCaseDetails(null)
            }
          } else {
            console.error("Failed to fetch TestRail case details:", result.error || "Unknown error")
            setTestCaseDetails(null)
          }
        } else if (activeProvider === 'zephyrscale' && formData.zephyrscaleCases?.length > 0) {
          caseId = formData.zephyrscaleCases[0]
          console.log("Fetching Zephyr Scale case details for case ID:", caseId)
          result = await pluginApi.getZephyrScaleTestCaseDetails({ testCaseKey: caseId })
          console.log("Zephyr Scale API response:", result)

          if (result.success) {
            // The API returns data in the response
            const testCaseData = result.data
            if (testCaseData) {
              console.log("Setting Zephyr Scale case details")
              setTestCaseDetails({ ...testCaseData, provider: 'zephyrscale' })
            } else {
              console.error("Zephyr Scale API response is missing test case data")
              setTestCaseDetails(null)
            }
          } else {
            console.error("Failed to fetch Zephyr Scale case details:", result.error || "Unknown error")
            setTestCaseDetails(null)
          }
        }
      } catch (error) {
        console.error("Error fetching test case details:", error)
        setTestCaseDetails(null)
      }
    }

    fetchTestCaseDetails()
  }, [formData.testrailCases, formData.zephyrscaleCases])

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Review Your Scenario</h2>
      <p className="text-gray-500 dark:text-gray-400">
        Please review all details of your scenario before saving.
      </p>

      {/* Scenario Overview */}
      <Card className="overflow-hidden border shadow-sm">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 p-6">
          <h3 className="text-xl font-semibold">{formData.name || "Untitled Scenario"}</h3>
          <p className="mt-2 text-gray-600 dark:text-gray-400">{formData.description || "No description provided"}</p>

          {/* Tags and Folder */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex flex-wrap gap-2 items-center">
              {formData.tags && formData.tags.length > 0 ? (
                formData.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="bg-white/80 dark:bg-gray-800/80">
                    {tag}
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-gray-500 dark:text-gray-400">No tags added</span>
              )}
            </div>

            {selectedFolder && (
              <div className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-white/80 dark:bg-gray-800/60 border border-gray-200 dark:border-gray-700">
                <Folder className="h-4 w-4 text-indigo-500" />
                <span className="text-sm font-medium">{selectedFolder.name}</span>
              </div>
            )}
          </div>
        </div>

        {/* TestRail Integration */}
        {formData.testrailCases && formData.testrailCases.length > 0 && (
          <div className="p-6 border-t border-gray-200 dark:border-gray-800">
            <h4 className="font-medium flex items-center gap-2 mb-4">
              <LinkIcon className="h-4 w-4 text-gray-500" />
              TestRail Integration
            </h4>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <h5 className="text-sm font-medium text-gray-500 dark:text-gray-400">Integration Status</h5>
                <p className="mt-1 flex items-center">
                  <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                  {getActiveProviderFromFormData(formData) === 'testrail'
                    ? 'Linked with TestRail'
                    : 'Linked with Zephyr Scale'
                  }
                </p>
              </div>
              <div>
                <h5 className="text-sm font-medium text-gray-500 dark:text-gray-400">Sync Status</h5>
                <p className="mt-1 flex items-center">
                  {(() => {
                    const activeProvider = getActiveProviderFromFormData(formData)
                    const syncEnabled = activeProvider === 'testrail'
                      ? formData.testrailSync
                      : formData.zephyrscaleSync
                    const providerName = activeProvider === 'testrail' ? 'TestRail' : 'Zephyr Scale'

                    return syncEnabled ? (
                      <>
                        <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                        Results will sync with {providerName}
                      </>
                    ) : (
                      <>
                        <X className="mr-2 h-4 w-4 text-red-500" />
                        Results will not sync with {providerName}
                      </>
                    )
                  })()}
                </p>
              </div>
            </div>

            {testCaseDetails && (
              <div className="rounded-md border overflow-hidden">
                <div className="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-b">
                  <h5 className="font-medium">
                    {testCaseDetails.provider === 'testrail'
                      ? `TestRail Case: C${testCaseDetails.id}`
                      : `Zephyr Scale Case: ${testCaseDetails.key || testCaseDetails.id}`
                    }
                  </h5>
                </div>
                <div className="p-4 bg-white dark:bg-gray-900">
                  <div className="mb-4">
                    <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      {testCaseDetails.provider === 'zephyrscale' ? 'Name' : 'Title'}
                    </h6>
                    <p className="text-gray-900 dark:text-gray-100">
                      {testCaseDetails.title || testCaseDetails.name}
                    </p>
                  </div>

                  {/* Objective/Description for Zephyr Scale */}
                  {testCaseDetails.provider === 'zephyrscale' && testCaseDetails.objective && (
                    <div className="mb-4">
                      <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Objective</h6>
                      <div className="p-3 rounded-md bg-gray-50 dark:bg-gray-800 text-sm">
                        <p className="text-gray-900 dark:text-gray-100">{testCaseDetails.objective}</p>
                      </div>
                    </div>
                  )}

                  {/* Precondition for Zephyr Scale */}
                  {testCaseDetails.provider === 'zephyrscale' && testCaseDetails.precondition && (
                    <div className="mb-4">
                      <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Precondition</h6>
                      <div className="p-3 rounded-md bg-gray-50 dark:bg-gray-800 text-sm">
                        <p className="text-gray-900 dark:text-gray-100">{testCaseDetails.precondition}</p>
                      </div>
                    </div>
                  )}

                  {/* Test Steps for Zephyr Scale */}
                  {testCaseDetails.provider === 'zephyrscale' && testCaseDetails.testScript?.steps && testCaseDetails.testScript.steps.length > 0 && (
                    <div className="mb-4">
                      <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Test Steps</h6>
                      <div className="p-3 rounded-md bg-gray-50 dark:bg-gray-800 text-sm">
                        <div className="space-y-2">
                          {testCaseDetails.testScript.steps.map((step: any, index: number) => (
                            <div key={index} className="text-gray-900 dark:text-gray-100">
                              <div className="font-medium">Step {index + 1}: {step.description || step.action || ''}</div>
                              {step.expectedResult && (
                                <div className="text-gray-600 dark:text-gray-400 ml-2 mt-1">
                                  Expected: {step.expectedResult}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* TestRail Steps (legacy format) */}
                  {testCaseDetails.provider === 'testrail' && (testCaseDetails.custom_steps || testCaseDetails.steps) && (
                    <div className="mb-4">
                      <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Steps</h6>
                      <div className="p-3 rounded-md bg-gray-50 dark:bg-gray-800 text-sm">
                        <pre className="whitespace-pre-wrap font-sans text-gray-900 dark:text-gray-100">
                          {testCaseDetails.custom_steps || testCaseDetails.steps}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* TestRail Expected Results (legacy format) */}
                  {testCaseDetails.provider === 'testrail' && (testCaseDetails.custom_expected || testCaseDetails.expected_result) && (
                    <div>
                      <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Expected Results</h6>
                      <div className="p-3 rounded-md bg-gray-50 dark:bg-gray-800 text-sm">
                        <pre className="whitespace-pre-wrap font-sans text-gray-900 dark:text-gray-100">
                          {testCaseDetails.custom_expected || testCaseDetails.expected_result}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        <CardContent className="p-6">
          {/* Basic Details */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Created By</h4>
              <p className="mt-1">Current User</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">Estimated Duration</h4>
              <p className="mt-1 flex items-center">
                <Clock className="mr-2 h-4 w-4 text-gray-400" />
                {formData.steps.length * 5} seconds
              </p>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Test Steps Summary as a Table */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-500" />
              Test Steps ({formData.steps.length})
            </h4>

            {formData.steps.length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[60px] text-center">#</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="w-[60%]">Parameters & Details</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sanitizedSteps.map((step, index) => (
                      <TableRow key={index} className="hover:bg-gray-50 dark:hover:bg-gray-900/20">
                        <TableCell className="text-center font-medium">
                          <div className="flex h-6 w-6 mx-auto items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 text-xs font-medium">
                            {index + 1}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {step.type === "aiAction" ? "AI Action" :
                           step.type === "aiAssertion" ? "AI Assertion" :
                           step.type === "aiWaitElement" ? "AI Wait Element" :
                           step.type === "sleep" ? "Sleep" :
                           step.type === "goto" ? "Navigate" :
                           step.type || "-"}
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2.5 text-sm">
                          {step.description && (
                              <div className="p-2 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-100 dark:border-gray-800">
                                <span className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Description:</span>
                                <p>{step.description}</p>
                              </div>
                            )}

                            {step.url && (
                              <div className="flex items-center gap-2 mt-2">
                                <span className="text-gray-500 dark:text-gray-400 min-w-[60px]">URL:</span>
                                <code className="flex-1 bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-xs">{step.url}</code>
                              </div>
                            )}

                            {step.value &&
                             typeof step.value === 'string' &&
                             step.value.trim() !== '' &&
                             step.value !== '0' &&
                             // Don't show value for AI steps if it's the same as description
                             !(["aiAction", "aiAssertion", "aiWaitElement"].includes(step.type) && step.value === step.description) && (
                              <div className="flex items-center gap-2 mt-2">
                                <span className="text-gray-500 dark:text-gray-400 min-w-[60px]">Value:</span>
                                <code className="flex-1 bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-xs">{step.value}</code>
                              </div>
                            )}

                            {!step.description &&
                              !step.url &&
                              (!step.value ||
                                step.value === "0" ||
                                typeof step.value === 'number' && step.value === 0 ||
                                typeof step.value !== 'string' ||
                                (typeof step.value === 'string' && step.value.trim() === '') ||
                                (["aiAction", "aiAssertion", "aiWaitElement"].includes(step.type) && step.value === step.description)
                              ) &&
                              (!step.duration || step.duration === 0) && (
                              <span className="text-gray-400 italic text-xs">No parameters</span>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center p-8 border border-dashed rounded-md">
                <p className="text-gray-500 dark:text-gray-400">No test steps have been added yet.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}