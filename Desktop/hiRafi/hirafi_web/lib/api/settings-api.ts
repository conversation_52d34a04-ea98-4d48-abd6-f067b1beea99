/**
 * Settings API
 *
 * Ayarlar ile ilgili API isteklerini yönetir.
 */

import { get, post, put, del } from './fetch-wrapper';
import { SETTINGS_ENDPOINTS } from './constants';

/**
 * Tüm ayarları getirir
 */
export async function getSettings() {
  return get(SETTINGS_ENDPOINTS.GET_ALL);
}

/**
 * <PERSON><PERSON><PERSON><PERSON> günceller
 *
 * @param settings Güncellenecek ayarlar
 */
export async function updateSettings(settings: any) {
  return put(SETTINGS_ENDPOINTS.UPDATE, settings, {
    showSuccessToast: true,
    successMessage: '<PERSON>yar<PERSON> başarıyla güncellendi'
  });
}

/**
 * Tüm AI modellerini getirir
 */
export async function getAIModels() {
  return get(SETTINGS_ENDPOINTS.AI_MODELS.GET_ALL);
}

/**
 * Belirli bir AI modelini getirir
 *
 * @param id AI model ID'si
 */
export async function getAIModelById(id: string) {
  return get(SETTINGS_ENDPOINTS.AI_MODELS.GET_BY_ID(id));
}

/**
 * Yeni bir AI modeli oluşturur
 *
 * @param model AI model verileri
 */
export async function createAIModel(model: {
  name: string;
  provider: string;
  apiKey?: string;
  endpoint?: string;
  parameters?: any;
  isActive?: boolean;
  teamId?: string;
  companyId?: string;
}) {
  // Backend API'nin beklediği formata dönüştür
  const backendModel = {
    name: model.name,
    api: model.endpoint,
    apiKey: model.apiKey,
    supportsImageProcessing: model.parameters?.supportsImageProcessing || false
  };

  return post(SETTINGS_ENDPOINTS.AI_MODELS.CREATE, backendModel, {
    showSuccessToast: true,
    successMessage: 'AI modeli başarıyla oluşturuldu'
  });
}

/**
 * Bir AI modelini günceller
 *
 * @param id AI model ID'si
 * @param model Güncellenecek AI model verileri
 */
export async function updateAIModel(id: string, model: {
  name?: string;
  provider?: string;
  apiKey?: string;
  endpoint?: string;
  parameters?: any;
  isActive?: boolean;
}) {
  return put(SETTINGS_ENDPOINTS.AI_MODELS.UPDATE(id), model, {
    showSuccessToast: true,
    successMessage: 'AI modeli başarıyla güncellendi'
  });
}

/**
 * Bir AI modelini siler
 *
 * @param id AI model ID'si
 */
export async function deleteAIModel(id: string) {
  return del(SETTINGS_ENDPOINTS.AI_MODELS.DELETE(id), {
    showSuccessToast: true,
    successMessage: 'AI modeli başarıyla silindi'
  });
}

/**
 * Bir AI modelini aktifleştirir
 *
 * @param id AI model ID'si
 */
export async function activateAIModel(id: string) {
  return post(SETTINGS_ENDPOINTS.AI_MODELS.ACTIVATE(id), {}, {
    showSuccessToast: true,
    successMessage: 'AI modeli başarıyla aktifleştirildi'
  });
}

/**
 * Bir AI modelini devre dışı bırakır
 *
 * @param id AI model ID'si
 */
export async function deactivateAIModel(id: string) {
  return post(SETTINGS_ENDPOINTS.AI_MODELS.DEACTIVATE(id), {}, {
    showSuccessToast: true,
    successMessage: 'AI modeli başarıyla devre dışı bırakıldı'
  });
}
