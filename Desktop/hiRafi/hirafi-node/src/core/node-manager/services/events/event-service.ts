/**
 * Event Service
 * 
 * Consolidates event handling responsibilities with dependency injection.
 * Replaces the singleton EventHandler with a proper service implementation.
 * 
 * NOTE: Test lifecycle events (test:claimed, test:started, etc.) are now handled 
 * by the Event Bus pattern. This service only handles WebSocket coordination and Redis events.
 */

import { EventEmitter } from 'events';
import { config } from '../../../../config/index.js';
import { ConnectorFactory } from '../../../connectors/factories/connector-factory.js';
import { IEventService } from '../../interfaces/event-service.interface.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

/**
 * Event Service
 *
 * Single responsibility class that handles ONLY external communication:
 * - Redis event communication
 * - WebSocket event coordination
 * - NO test lifecycle events (handled by Event Bus)
 * - NO state management or state queries
 */
export class EventService extends EventEmitter implements IEventService {
  // Event setup state
  private eventsSetup: boolean = false;
  private logger: ILoggerService;

  // Component references
  private websocketConnector: any;
  private redisConnector: any;
  private nodeEvents: EventEmitter;

  constructor(nodeEvents: EventEmitter) {
    super();
    this.logger = LoggerServiceFactory.createServiceLogger('EventService');
    this.nodeEvents = nodeEvents;

    this.logger.serviceInit('EventService', 'Initialized for WebSocket coordination only (test lifecycle events handled by Event Bus)');
  }

  /**
   * Initialize the EventService and its connectors
   */
  async initialize(): Promise<void> {
    this.logger.serviceInit('EventService', 'Starting async initialization');

    // Initialize connectors asynchronously
    await this.initializeWebSocketConnector();
    await this.initializeRedisConnector();

    this.logger.serviceInit('EventService', 'Async initialization completed');
  }

  /**
   * Initialize WebSocket connector
   */
  private async initializeWebSocketConnector(): Promise<void> {
    try {
      // Get or create a connector manager
      const connectorManager = await ConnectorFactory.createConnectorManager({
        enableLogging: false
      });

      // Create WebSocket connector
      this.websocketConnector = await connectorManager.createConnector('websocket');
    } catch (error: any) {
      this.logger.error(`EventService: Error initializing WebSocket connector: ${error.message}`);
    }
  }

  /**
   * Initialize Redis connector
   */
  private async initializeRedisConnector(): Promise<void> {
    try {
      // Get or create a connector manager
      const connectorManager = await ConnectorFactory.createConnectorManager({
        enableLogging: false
      });

      // Create Redis connector
      this.redisConnector = await connectorManager.createConnector('redis');
    } catch (error: any) {
      this.logger.error(`EventService: Error initializing Redis connector: ${error.message}`);
    }
  }

  /**
   * Check if events are setup
   */
  areEventsSetup(): boolean {
    return this.eventsSetup;
  }

  /**
   * Setup all event handlers
   */
  setupAllEvents(): void {
    if (this.eventsSetup) {
      this.logger.debug('EventService: Events already setup, skipping');
      return;
    }

    this.logger.serviceInit('EventService', 'Setting up WebSocket and Redis event handlers (test lifecycle events handled by Event Bus)');

    // Clean up any existing listeners first to prevent duplicates
    this.teardownAllEvents();

    this.setupRedisEvents();
    this.setupWebSocketEvents();
    this.setupInternalEventHandlers();

    this.eventsSetup = true;
    this.logger.serviceInit('EventService', 'WebSocket and Redis event handlers setup completed');
  }

  /**
   * Setup Redis event handlers
   */
  setupRedisEvents(): void {
    if (!config.redisEnabled || !this.redisConnector) {
      this.logger.debug('EventService: Redis disabled or not available, skipping Redis event setup');
      return;
    }

    this.logger.debug('EventService: Setting up Redis event handlers');

    // Redis coordination events removed - migrated to WebSocket
    // Only test queue and result queue remain on Redis

    // Redis query handlers removed - migrated to WebSocket coordination
    // All coordination now happens via WebSocket for cleaner architecture

    this.logger.debug('EventService: Redis event handlers setup completed');
  }

  /**
   * Setup WebSocket event handlers
   */
  setupWebSocketEvents(): void {
    if (!this.websocketConnector) {
      this.logger.warn('EventService: WebSocket connector not available, skipping WebSocket event setup');
      return;
    }

    this.logger.debug('EventService: Setting up WebSocket event handlers');

    // Handle test assignments - simplified for new architecture
    this.websocketConnector.on('test-assignment', (message: any) => {
      if (!message.testData) {
        this.logger.warn('EventService: Received test assignment with missing testData');
        return;
      }

      this.logger.debug(`EventService: Test assignment received for test ${message.testData.id || 'unknown'}, handled by TestQueueWorker`);
    });

    // Handle coordination events (migrated from Redis)
    this.setupWebSocketCoordinationEvents();

    // Handle stop test commands
    this.websocketConnector.on('stop-test', async (message: any) => {
      const testId = message.testId;
      if (!testId) {
        this.logger.warn('EventService: Received stop-test command with missing testId');
        return;
      }

      this.logger.info(`EventService: Received stop-test command for test ${testId} via WebSocket`);

      // Forward stop-test event to the main node event system for NodeManager to handle
      this.nodeEvents.emit('stop-test', testId);
      this.logger.info(`EventService: Forwarded stop-test event for test ${testId} to NodeManager`);
    });

    this.logger.debug('EventService: WebSocket event handlers setup completed');
  }

  /**
   * Setup WebSocket coordination events (migrated from Redis)
   */
  private setupWebSocketCoordinationEvents(): void {
    if (!this.websocketConnector) {
      return;
    }

    this.logger.debug('EventService: Setting up WebSocket coordination event handlers');

    // Handle test released (replaces Redis 'test:released')
    this.websocketConnector.on('test-released', (data: any) => {
      const testId = data?.testId || data;
      this.logger.info(`EventService: Received test-released event from WebSocket for test ${testId}`);
      this.emit('test-released', testId);
    });

    // Handle node status query (replaces Redis 'check:running')
    this.websocketConnector.on('node-status-query', (data: any) => {
      this.logger.debug(`EventService: Received node-status-query from WebSocket - forwarding to NodeManager`);
      this.emit('websocket:node-status-query', data);
    });

    // Handle get current test ID query (replaces Redis 'get:current-test-id')
    this.websocketConnector.on('get-current-test-id', (data: any) => {
      this.logger.debug(`EventService: Received get-current-test-id from WebSocket - forwarding to NodeManager`);
      this.emit('websocket:get-current-test-id', data);
    });

    this.logger.debug('EventService: WebSocket coordination event handlers setup completed');
  }

  /**
   * @deprecated Test lifecycle events are now handled by Event Bus pattern
   * Setup test processor event handlers
   */
  setupTestProcessorEvents(): void {
    this.logger.warn('EventService: setupTestProcessorEvents is deprecated - test lifecycle events are now handled by Event Bus pattern');
    // No longer setting up test lifecycle event handlers - handled by Event Bus
  }

  /**
   * Setup internal event handlers
   */
  setupInternalEventHandlers(): void {
    this.logger.debug('EventService: Setting up internal event handlers');

    // Handle our own stop-test events as a backup mechanism
    this.on('stop-test', async (testId: string) => {
      this.logger.info(`EventService: Processing internal stop-test event for test ${testId}`);
      // This provides redundancy in case the WebSocket handler doesn't work
    });

    this.logger.debug('EventService: Internal event handlers setup completed');
  }

  /**
   * Teardown all event handlers
   */
  teardownAllEvents(): void {
    if (!this.eventsSetup) {
      return; // Already torn down
    }

    this.logger.info('EventService: Tearing down all event handlers');

    this.teardownRedisEvents();
    this.teardownWebSocketEvents();
    this.teardownInternalEventHandlers();

    this.eventsSetup = false;
    this.logger.info('EventService: All event handlers teardown completed');
  }

  /**
   * Teardown Redis event handlers (removed - migrated to WebSocket)
   */
  teardownRedisEvents(): void {
    this.logger.debug('EventService: Redis coordination events removed - migrated to WebSocket');
    // No Redis coordination events to teardown - only test queue and result queue remain
  }

  /**
   * Teardown WebSocket event handlers
   */
  teardownWebSocketEvents(): void {
    this.logger.debug('EventService: Tearing down WebSocket event handlers');
    if (this.websocketConnector) {
      // Remove all listeners from websocket connector
      this.websocketConnector.removeAllListeners('test-assignment');
      this.websocketConnector.removeAllListeners('stop-test');

      // Remove WebSocket coordination event listeners (migrated from Redis)
      this.websocketConnector.removeAllListeners('test-released');
      this.websocketConnector.removeAllListeners('node-status-query');
      this.websocketConnector.removeAllListeners('get-current-test-id');
    }
  }

  /**
   * @deprecated Test lifecycle events are now handled by Event Bus pattern
   * Teardown test execution manager event handlers
   */
  teardownTestProcessorEvents(): void {
    this.logger.warn('EventService: teardownTestProcessorEvents is deprecated - test lifecycle events are handled by Event Bus pattern');
    // No longer tearing down test lifecycle event handlers - handled by Event Bus
  }

  /**
   * Teardown internal event handlers
   */
  teardownInternalEventHandlers(): void {
    this.logger.debug('EventService: Tearing down internal event handlers');
    this.removeAllListeners('stop-test');
  }

  /**
   * Emit test released event
   */
  emitTestReleased(testId: string): void {
    this.logger.debug(`EventService: Emitting test:released event for test ${testId}`);

    try {
      // Only emit locally to prevent circular event propagation
      // Redis events are handled by this same class
      this.emit('test:released', testId);
      this.logger.debug(`EventService: Successfully emitted local test:released event for test ${testId}`);
    } catch (error) {
      this.logger.error(`EventService: Error emitting test:released event for test ${testId}: ${error}`);
    }
  }

  // ===== REDIS EVENT HANDLERS (REMOVED - migrated to WebSocket) =====
  // All Redis coordination events have been migrated to WebSocket for cleaner architecture

  // ===== TEST LIFECYCLE EVENT HANDLERS (REMOVED - migrated to Event Bus) =====
  // All test lifecycle events are now handled by the Event Bus pattern
  // EventService only handles WebSocket coordination and Redis events
}
