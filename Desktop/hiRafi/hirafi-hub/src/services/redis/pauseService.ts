/**
 * Pause Service
 * Manages system and node pause flags in Redis
 */

import { logger } from '../../utils/logger.js';
import { redisConnection } from './redisConnection.js';

// Redis key prefixes
const SYSTEM_PAUSE_KEY = 'system:paused';
const NODE_PAUSE_KEY_PREFIX = 'node:';
const NODE_PAUSE_KEY_SUFFIX = ':paused';

class PauseService {
  private static instance: PauseService;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): PauseService {
    if (!PauseService.instance) {
      PauseService.instance = new PauseService();
    }
    return PauseService.instance;
  }

  /**
   * Pause the entire system
   */
  public async pauseSystem(): Promise<boolean> {
    try {
      const client = redisConnection.getClient();
      if (!client) {
        logger.error('PauseService: Cannot pause system - Redis client not available');
        return false;
      }

      await client.set(SYSTEM_PAUSE_KEY, 'true');
      logger.info('PauseService: System paused');
      return true;
    } catch (error) {
      logger.error(`PauseService: Error pausing system: ${error}`);
      return false;
    }
  }

  /**
   * Resume the entire system
   */
  public async resumeSystem(): Promise<boolean> {
    try {
      const client = redisConnection.getClient();
      if (!client) {
        logger.error('PauseService: Cannot resume system - Redis client not available');
        return false;
      }

      await client.set(SYSTEM_PAUSE_KEY, 'false');
      logger.info('PauseService: System resumed');
      return true;
    } catch (error) {
      logger.error(`PauseService: Error resuming system: ${error}`);
      return false;
    }
  }

  /**
   * Check if the system is paused
   */
  public async isSystemPaused(): Promise<boolean> {
    try {
      const client = redisConnection.getClient();
      if (!client) {
        logger.error('PauseService: Cannot check system pause status - Redis client not available');
        return false;
      }

      const value = await client.get(SYSTEM_PAUSE_KEY);
      return value === 'true';
    } catch (error) {
      logger.error(`PauseService: Error checking system pause status: ${error}`);
      return false;
    }
  }

  /**
   * Pause a specific node
   * @param nodeId Node ID
   */
  public async pauseNode(nodeId: string): Promise<boolean> {
    try {
      const client = redisConnection.getClient();
      if (!client) {
        logger.error(`PauseService: Cannot pause node ${nodeId} - Redis client not available`);
        return false;
      }

      const key = `${NODE_PAUSE_KEY_PREFIX}${nodeId}${NODE_PAUSE_KEY_SUFFIX}`;
      await client.set(key, 'true');
      logger.info(`PauseService: Node ${nodeId} paused`);
      return true;
    } catch (error) {
      logger.error(`PauseService: Error pausing node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Resume a specific node
   * @param nodeId Node ID
   */
  public async resumeNode(nodeId: string): Promise<boolean> {
    try {
      const client = redisConnection.getClient();
      if (!client) {
        logger.error(`PauseService: Cannot resume node ${nodeId} - Redis client not available`);
        return false;
      }

      const key = `${NODE_PAUSE_KEY_PREFIX}${nodeId}${NODE_PAUSE_KEY_SUFFIX}`;
      await client.set(key, 'false');
      logger.info(`PauseService: Node ${nodeId} resumed`);
      return true;
    } catch (error) {
      logger.error(`PauseService: Error resuming node ${nodeId}: ${error}`);
      return false;
    }
  }

  /**
   * Check if a node is paused
   * @param nodeId Node ID
   */
  public async isNodePaused(nodeId: string): Promise<boolean> {
    try {
      const client = redisConnection.getClient();
      if (!client) {
        logger.error(`PauseService: Cannot check node ${nodeId} pause status - Redis client not available`);
        return false;
      }

      const key = `${NODE_PAUSE_KEY_PREFIX}${nodeId}${NODE_PAUSE_KEY_SUFFIX}`;
      const value = await client.get(key);
      return value === 'true';
    } catch (error) {
      logger.error(`PauseService: Error checking node ${nodeId} pause status: ${error}`);
      return false;
    }
  }

  /**
   * Get pause status for all nodes
   * @param nodeIds List of node IDs
   */
  public async getNodesPauseStatus(nodeIds: string[]): Promise<Record<string, boolean>> {
    const result: Record<string, boolean> = {};
    
    try {
      const client = redisConnection.getClient();
      if (!client) {
        logger.error('PauseService: Cannot get nodes pause status - Redis client not available');
        return result;
      }

      // Check each node
      for (const nodeId of nodeIds) {
        const key = `${NODE_PAUSE_KEY_PREFIX}${nodeId}${NODE_PAUSE_KEY_SUFFIX}`;
        const value = await client.get(key);
        result[nodeId] = value === 'true';
      }

      return result;
    } catch (error) {
      logger.error(`PauseService: Error getting nodes pause status: ${error}`);
      return result;
    }
  }
}

// Export singleton instance
export const pauseService = PauseService.getInstance();
export default pauseService;
