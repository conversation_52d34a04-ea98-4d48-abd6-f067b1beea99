/**
 * Periodic Task Service
 * 
 * Handles scheduling and lifecycle of recurring tasks with dependency injection.
 * Replaces the singleton PeriodicTaskManager with a proper service implementation.
 */

import { IPeriodicTaskService } from '../../interfaces/periodic-task-service.interface.js';
import { PeriodicTask } from '../../types/node-manager-types.js';
import { ILoggerService, LoggerServiceFactory } from '../../../../utils/logger-service.js';

export class PeriodicTaskService implements IPeriodicTaskService {
  private tasks: Map<string, PeriodicTask> = new Map();
  private isStarted: boolean = false;
  private logger: ILoggerService;

  constructor() {
    this.logger = LoggerServiceFactory.createServiceLogger('PeriodicTaskService');
    this.logger.debug('PeriodicTaskService: Initialized with dependency injection');
  }

  /**
   * Start all periodic tasks
   */
  startAllTasks(): void {
    if (this.isStarted) {
      this.logger.debug('PeriodicTaskService: Tasks already started, skipping');
      return;
    }

    this.logger.info('PeriodicTaskService: Starting all periodic tasks');

    // Start each task
    for (const [id, task] of this.tasks) {
      try {
        this.logger.debug(`PeriodicTaskService: Starting task ${id}: ${task.description}`);
        // Tasks are already scheduled when added, so we just log here
      } catch (error: any) {
        this.logger.error(`PeriodicTaskService: Error starting task ${id}: ${error.message}`);
      }
    }

    this.isStarted = true;
    this.logger.info(`PeriodicTaskService: Started ${this.tasks.size} periodic tasks`);
  }

  /**
   * Stop all periodic tasks
   */
  stopAllTasks(): void {
    if (!this.isStarted) {
      this.logger.debug('PeriodicTaskService: Tasks not started, skipping stop');
      return;
    }

    this.logger.info('PeriodicTaskService: Stopping all periodic tasks');

    // Stop each task
    for (const [id, task] of this.tasks) {
      try {
        this.logger.debug(`PeriodicTaskService: Stopping task ${id}: ${task.description}`);
        clearInterval(task.interval);
      } catch (error: any) {
        this.logger.error(`PeriodicTaskService: Error stopping task ${id}: ${error.message}`);
      }
    }

    this.isStarted = false;
    this.logger.info(`PeriodicTaskService: Stopped ${this.tasks.size} periodic tasks`);
  }

  /**
   * Add a new periodic task
   */
  addTask(id: string, callback: () => void | Promise<void>, interval: number, description: string): void {
    if (this.tasks.has(id)) {
      this.logger.warn(`PeriodicTaskService: Task ${id} already exists, removing old task first`);
      this.removeTask(id);
    }

    this.logger.info(`PeriodicTaskService: Adding task ${id}: ${description} (interval: ${interval}ms)`);

    // Create wrapper function for error handling
    const wrappedCallback = async () => {
      try {
        await callback();
      } catch (error: any) {
        this.logger.error(`PeriodicTaskService: Error in task ${id}: ${error.message}`);
      }
    };

    // Schedule the task
    const intervalId = setInterval(wrappedCallback, interval);

    // Store task information
    const task: PeriodicTask = {
      id,
      interval: intervalId,
      description
    };

    this.tasks.set(id, task);
    this.logger.debug(`PeriodicTaskService: Task ${id} added successfully`);
  }

  /**
   * Remove a periodic task
   */
  removeTask(id: string): void {
    const task = this.tasks.get(id);
    if (!task) {
      this.logger.warn(`PeriodicTaskService: Task ${id} not found, cannot remove`);
      return;
    }

    this.logger.info(`PeriodicTaskService: Removing task ${id}: ${task.description}`);

    // Clear the interval
    clearInterval(task.interval);

    // Remove from map
    this.tasks.delete(id);

    this.logger.debug(`PeriodicTaskService: Task ${id} removed successfully`);
  }

  /**
   * Check if a task exists
   */
  hasTask(id: string): boolean {
    return this.tasks.has(id);
  }

  /**
   * Get all active tasks
   */
  getActiveTasks(): PeriodicTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * Get task by ID
   */
  getTask(id: string): PeriodicTask | undefined {
    return this.tasks.get(id);
  }

  /**
   * Update task interval
   */
  updateTaskInterval(id: string, newInterval: number): void {
    const task = this.tasks.get(id);
    if (!task) {
      this.logger.warn(`PeriodicTaskService: Task ${id} not found, cannot update interval`);
      return;
    }

    this.logger.info(`PeriodicTaskService: Updating interval for task ${id} to ${newInterval}ms`);

    // Clear the old interval
    clearInterval(task.interval);

    // Create new interval with the same callback
    // Note: We need to recreate the callback since we don't store it
    this.logger.warn(`PeriodicTaskService: Cannot update interval for task ${id} - callback not stored. Please remove and re-add the task.`);
  }

  /**
   * Get task statistics
   */
  getTaskStats(): {
    totalTasks: number;
    activeTasks: number;
    taskList: string[];
  } {
    const taskList = Array.from(this.tasks.keys());
    
    return {
      totalTasks: this.tasks.size,
      activeTasks: this.isStarted ? this.tasks.size : 0,
      taskList
    };
  }
}
