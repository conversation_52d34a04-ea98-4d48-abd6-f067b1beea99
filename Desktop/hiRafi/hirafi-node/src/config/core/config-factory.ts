/**
 * Configuration Factory
 * Factory for creating and assembling configuration objects
 */

import { NodeConfig } from '../../models/types.js';
import { createNodeConfig } from '../domains/node-config.js';
import { createFileSystemConfig, initializeFileSystemDirectories } from '../domains/filesystem-config.js';
import { createRedisConfig } from '../domains/redis-config.js';
import { createBullMQConfig } from '../domains/bullmq-config.js';
import { createMinioConfig, createS3Config, createWebSocketConfig } from '../domains/storage-config.js';
import { createTestConfig } from '../domains/test-config.js';
import { createPlatformConfig } from '../domains/platform-config.js';
import { ConfigurationValidator } from './config-validator.js';

/**
 * Configuration factory for creating complete NodeConfig objects
 */
export class ConfigurationFactory {
  /**
   * Creates a complete configuration object from environment variables
   * @returns Complete NodeConfig object
   */
  public static createFromEnvironment(): NodeConfig {
    // Create domain-specific configurations
    const nodeConfig = createNodeConfig();
    const fileSystemConfig = createFileSystemConfig();
    const redisConfig = createRedisConfig();
    const bullmqConfig = createBullMQConfig();
    const minioConfig = createMinioConfig();
    const s3Config = createS3Config();
    const webSocketConfig = createWebSocketConfig();
    const testConfig = createTestConfig();
    const platformConfig = createPlatformConfig();

    // Assemble the complete configuration
    const config: NodeConfig = {
      // Node Configuration
      name: nodeConfig.name,
      hubUrl: nodeConfig.hubUrl,
      capabilities: nodeConfig.capabilities,
      version: nodeConfig.version,
      environment: nodeConfig.environment,
      useSystemInfo: nodeConfig.useSystemInfo,
      port: nodeConfig.port,
      nodeId: nodeConfig.nodeId,

      // File Storage Configuration
      screenshotsDir: fileSystemConfig.screenshotsDir,
      nodeScenarioDir: fileSystemConfig.nodeScenarioDir,

      // Redis Configuration
      redisEnabled: redisConfig.enabled,
      redisUrl: redisConfig.url,
      redis: {
        host: redisConfig.host,
        port: redisConfig.port,
        password: redisConfig.password,
        url: redisConfig.url,
        db: redisConfig.db,
        prefix: redisConfig.prefix
      },

      // BullMQ Configuration
      bullmq: {
        jobLockRefreshInterval: bullmqConfig.jobLockRefreshInterval,
        stalledJobLockExtension: bullmqConfig.stalledJobLockExtension,
        lockDuration: bullmqConfig.lockDuration,
        stalledInterval: bullmqConfig.stalledInterval,
        maxStalledCount: bullmqConfig.maxStalledCount,
        activeJobMonitoringTimeout: bullmqConfig.activeJobMonitoringTimeout,
        activeJobLockExtension: bullmqConfig.activeJobLockExtension,
        concurrency: bullmqConfig.concurrency,
        drainDelay: bullmqConfig.drainDelay
      },

      // WebSocket Configuration
      websocketEnabled: webSocketConfig.enabled,

      // Storage Configuration
      minioEnabled: minioConfig.enabled,
      s3Enabled: s3Config.enabled,

      // MinIO Configuration
      minio: {
        endPoint: minioConfig.endPoint,
        port: minioConfig.port,
        useSSL: minioConfig.useSSL,
        accessKey: minioConfig.accessKey,
        secretKey: minioConfig.secretKey,
        region: minioConfig.region,
        screenshotBucket: minioConfig.screenshotBucket,
        reportBucket: minioConfig.reportBucket,
        publicBaseUrl: minioConfig.publicBaseUrl
      },

      // AWS S3 Configuration
      s3: {
        region: s3Config.region,
        accessKey: s3Config.accessKey,
        secretKey: s3Config.secretKey,
        endpoint: s3Config.endpoint,
        forcePathStyle: s3Config.forcePathStyle,
        screenshotBucket: s3Config.screenshotBucket,
        reportBucket: s3Config.reportBucket,
        publicBaseUrl: s3Config.publicBaseUrl
      },

      // Test Options Configuration
      testOptions: {
        maxRetries: testConfig.maxRetries,
        timeout: testConfig.timeout
      },

      // Platform Configuration
      platforms: platformConfig
    };

    return config;
  }

  /**
   * Creates configuration with custom overrides
   * @param overrides Partial configuration to override defaults
   * @returns Complete NodeConfig object with overrides applied
   */
  public static createWithOverrides(overrides: Partial<NodeConfig>): NodeConfig {
    const baseConfig = ConfigurationFactory.createFromEnvironment();
    return ConfigurationFactory.mergeConfigurations(baseConfig, overrides);
  }

  /**
   * Creates configuration for testing with safe defaults
   * @param testOverrides Test-specific overrides
   * @returns Configuration suitable for testing
   */
  public static createForTesting(testOverrides: Partial<NodeConfig> = {}): NodeConfig {
    const testDefaults: Partial<NodeConfig> = {
      environment: 'test',
      redisEnabled: false,
      minioEnabled: false,
      s3Enabled: false,
      websocketEnabled: false,
      useSystemInfo: false,
      testOptions: {
        maxRetries: 1, // Reduced retries for faster test execution
        timeout: 30000 // Shorter timeout for tests
      }
    };

    const baseConfig = ConfigurationFactory.createFromEnvironment();
    const configWithTestDefaults = ConfigurationFactory.mergeConfigurations(baseConfig, testDefaults);
    return ConfigurationFactory.mergeConfigurations(configWithTestDefaults, testOverrides);
  }

  /**
   * Creates configuration for development with appropriate defaults
   * @param devOverrides Development-specific overrides
   * @returns Configuration suitable for development
   */
  public static createForDevelopment(devOverrides: Partial<NodeConfig> = {}): NodeConfig {
    const devDefaults: Partial<NodeConfig> = {
      environment: 'development',
      useSystemInfo: true,
      testOptions: {
        maxRetries: 2,
        timeout: 45000
      }
    };

    const baseConfig = ConfigurationFactory.createFromEnvironment();
    const configWithDevDefaults = ConfigurationFactory.mergeConfigurations(baseConfig, devDefaults);
    return ConfigurationFactory.mergeConfigurations(configWithDevDefaults, devOverrides);
  }

  /**
   * Creates configuration for production with secure defaults
   * @param prodOverrides Production-specific overrides
   * @returns Configuration suitable for production
   */
  public static createForProduction(prodOverrides: Partial<NodeConfig> = {}): NodeConfig {
    const prodDefaults: Partial<NodeConfig> = {
      environment: 'production',
      useSystemInfo: false,
      testOptions: {
        maxRetries: 3, // More retries for production stability
        timeout: 60000 // Longer timeout for production reliability
      }
    };

    const baseConfig = ConfigurationFactory.createFromEnvironment();
    const configWithProdDefaults = ConfigurationFactory.mergeConfigurations(baseConfig, prodDefaults);
    return ConfigurationFactory.mergeConfigurations(configWithProdDefaults, prodOverrides);
  }

  /**
   * Initializes configuration and performs necessary setup
   * @param config Configuration to initialize
   * @returns Initialized configuration
   */
  public static initializeConfiguration(config: NodeConfig): NodeConfig {
    // Initialize file system directories
    const fileSystemConfig = {
      screenshotsDir: config.screenshotsDir,
      nodeScenarioDir: config.nodeScenarioDir
    };
    initializeFileSystemDirectories(fileSystemConfig);

    return config;
  }

  /**
   * Merges two configuration objects deeply
   * @param baseConfig Base configuration
   * @param overrides Configuration overrides
   * @returns Merged configuration
   */
  private static mergeConfigurations(baseConfig: NodeConfig, overrides: Partial<NodeConfig>): NodeConfig {
    const merged = { ...baseConfig };

    Object.entries(overrides).forEach(([key, value]) => {
      if (value !== undefined) {
        const configKey = key as keyof NodeConfig;
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // Deep merge for objects
          (merged as any)[configKey] = {
            ...((merged as any)[configKey] || {}),
            ...value
          };
        } else {
          // Direct assignment for primitives and arrays
          (merged as any)[configKey] = value;
        }
      }
    });

    return merged;
  }

  /**
   * Validates a configuration object using the dedicated validator
   * @param config Configuration to validate
   * @returns Validation result
   */
  public static validateConfiguration(config: NodeConfig): { isValid: boolean; errors: string[]; warnings: string[] } {
    return ConfigurationValidator.validateComplete(config);
  }
}
