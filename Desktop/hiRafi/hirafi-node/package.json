{"name": "test-node", "version": "1.6.2", "description": "Test node for executing browser automation tests", "main": "dist/index.js", "type": "module", "imports": {"#*": "./dist/*"}, "scripts": {"build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": "node dist/index.js", "dev": "node --loader ts-node/esm --watch src/index.ts", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"@aws-sdk/client-s3": "^3.803.0", "@aws-sdk/lib-storage": "^3.803.0", "@aws-sdk/s3-request-presigner": "^3.803.0", "@devicepark/sdk": "^1.0.2", "axe-core": "^4.10.3", "axios": "^1.6.8", "bullmq": "^5.56.3", "chalk": "^5.4.1", "dotenv": "^16.3.1", "minio": "^8.0.5", "rfi-ai-android": "0.20.6", "rfi-ai-web": "0.21.4", "puppeteer-screen-recorder": "^3.0.6", "rebrowser-patches": "^1.0.19", "rebrowser-puppeteer": "^24.8.1", "redis": "^5.6.0", "sharp": "^0.34.2", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.18.1", "yargs": "^17.7.2"}, "devDependencies": {"@types/jest": "^29.5.10", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.25.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}, "overrides": {"rfi-ai-core": "0.20.0"}}