"use client"

import { useRunForm } from "../run-context"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Camera, Video, HelpCircle } from "lucide-react"
import { motion } from "framer-motion"

export function BaseReportSettings() {
  const { reportSettings, updateReportSettings } = useRunForm()

  // Toggle switches için handler fonksi<PERSON><PERSON><PERSON>
  const handleToggleScreenshots = (checked: boolean) => {
    updateReportSettings({ takeScreenshots: checked })
  }

  const handleToggleVideos = (checked: boolean) => {
    updateReportSettings({ takeVideos: checked })
  }

  return (
    <TooltipProvider>
      {/* Ekran görüntüleri */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-full">
            <Camera className="h-5 w-5 text-emerald-500" />
          </div>
          <div>
            <Label htmlFor="screenshots" className="text-sm font-medium">
              Take Screenshots
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Capture screenshots at each step for detailed visual reporting
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="screenshots"
            checked={reportSettings.takeScreenshots}
            onCheckedChange={handleToggleScreenshots}
            className="data-[state=checked]:bg-emerald-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">Screenshots are taken at each step of the test execution, providing a visual record of what happened.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>

      {/* Video kaydı */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-full">
            <Video className="h-5 w-5 text-emerald-500" />
          </div>
          <div>
            <Label htmlFor="videos" className="text-sm font-medium">
              Record Videos
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Record videos of the entire test execution for comprehensive analysis
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="videos"
            checked={reportSettings.takeVideos}
            onCheckedChange={handleToggleVideos}
            className="data-[state=checked]:bg-emerald-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">Video recording captures the entire test execution, which can be useful for debugging complex issues.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
