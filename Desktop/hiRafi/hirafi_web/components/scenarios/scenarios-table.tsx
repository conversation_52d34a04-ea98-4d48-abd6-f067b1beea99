"use client";

import { useState } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  FileText,
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  Smartphone,
  Globe,
  ServerIcon,
  FolderIcon,
  FileCheck
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import type { Scenario, FolderType, ScenarioStatus } from "@/types/scenario"

// Extended Scenario type with metadata
interface ScenarioWithMeta extends Scenario {
  lastRun?: string;
  updatedAt?: string;
  testType?: 'web' | 'mobile' | 'api';
  runStatus?: ScenarioStatus; // Added runStatus field to display current run status
}

interface ScenariosTableProps {
  scenarios: ScenarioWithMeta[];
  folders: FolderType[];
  selectedScenarios: string[];
  selectedFolder?: string | null;
  onSelectScenario: (id: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  sortColumn: string;
  sortDirection: "asc" | "desc";
  onSort: (column: string) => void;
  onEditScenario: (id: string) => void;
  onDuplicateScenario: (id: string) => void;
  onDeleteScenario: (id: string) => void;
  onDragScenario?: (scenarioId: string, e: React.DragEvent) => void;
}

export function ScenariosTable({
  scenarios,
  folders,
  selectedScenarios,
  selectedFolder,
  onSelectScenario,
  onSelectAll,
  sortColumn,
  sortDirection,
  onSort,
  onEditScenario,
  onDuplicateScenario,
  onDeleteScenario,
  onDragScenario,
}: ScenariosTableProps) {
  const allSelected =
    scenarios.length > 0 && selectedScenarios.length === scenarios.length;
  const someSelected =
    selectedScenarios.length > 0 && selectedScenarios.length < scenarios.length;

  // State to track which scenario is being dragged
  const [draggedScenarioId, setDraggedScenarioId] = useState<string | null>(null);

  // Helper function to get folder with icon
  const getFolderWithIcon = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId)
    const folderName = folder?.name || "Uncategorized"
    const folderColor = folder?.color || "#6b7280"

    return (
      <div className="flex items-center gap-2">
        <FolderIcon
          className="h-4 w-4"
          style={{ color: folderColor }}
        />
        <span>{folderName}</span>
      </div>
    )
  }

  // Helper function to get TestRail integration badge
  const getTestRailBadge = (scenario: ScenarioWithMeta) => {
    // Check if scenario has TestRail integration
    if (scenario.testrailIntegration &&
        scenario.testrailIntegration.caseIds &&
        scenario.testrailIntegration.caseIds.length > 0) {

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800">
                <FileCheck className="h-3 w-3 mr-1" />
                {scenario.testrailIntegration.caseIds.length > 1
                  ? `${scenario.testrailIntegration.caseIds.length} Cases`
                  : `C${scenario.testrailIntegration.caseIds[0]}`}
              </Badge>
            </TooltipTrigger>
            <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
              <p className="text-xs">
                TestRail Cases: {scenario.testrailIntegration.caseIds.map(id => `C${id}`).join(', ')}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return null;
  }

  // Helper function to get status badge
  const getStatusBadge = (scenario: ScenarioWithMeta) => {
    // Use runStatus if available, otherwise fall back to status
    let status = scenario.runStatus || scenario.status;

    // "completed" durumunu "passed" olarak işle
    if (typeof status === 'string' && status.toLowerCase() === 'completed') {
      status = "passed" as ScenarioStatus;
    }

    switch (status) {
      case "passed":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/50">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Passed
          </Badge>
        )
      case "failed":
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/50">
            <XCircle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        )
      case "running":
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/50">
            <Clock className="h-3 w-3 mr-1" />
            Running
          </Badge>
        )
      case "queue":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-800 dark:hover:bg-yellow-900/50">
            <Clock className="h-3 w-3 mr-1" />
            Queued
          </Badge>
        )
      case "inactive":
      case "passive":
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700">
            <AlertCircle className="h-3 w-3 mr-1" />
            Inactive
          </Badge>
        )
      case "active":
      case "new":
        return (
          <Badge className="bg-green-50 text-green-700 border-green-100 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400 dark:border-green-900 dark:hover:bg-green-900/40">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Active
          </Badge>
        )
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700">
            {status || "Unknown"}
          </Badge>
        )
    }
  }

  // Helper function to get test type icon
  const getTestTypeIcon = (scenario: ScenarioWithMeta) => {
    // Check if scenario has testType property
    const testType = scenario.testType || 'web'

    switch (testType) {
      case 'mobile':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center">
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800">
                    <Smartphone className="h-3 w-3" />
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
                <p className="text-xs">Mobile Test</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      case 'api':
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center">
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800">
                    <ServerIcon className="h-3 w-3" />
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
                <p className="text-xs">API Test</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800">
                    <Globe className="h-3 w-3" />
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent className="dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200">
                <p className="text-xs">Web Test</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
    }
  }

  // Helper function to render sort indicator
  const renderSortIndicator = (column: string) => {
    if (sortColumn !== column) return null;

    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDown className="h-4 w-4 ml-1" />
    );
  };

  // Helper function to safely format dates
  const formatDate = (dateString: string | undefined | null): string => {
    if (!dateString) return "Never";

    // Handle empty strings
    if (dateString === "") return "Never";

    try {
      // Try to parse the date string
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn(`Invalid date format received: ${dateString}`);
        return "Never"; // Return "Never" instead of "Invalid Date" for better UX
      }

      // Format date as "YYYY-MM-DD"
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error("Error formatting date:", error, "for dateString:", dateString);
      return "Never"; // Return "Never" instead of "Invalid Date" for better UX
    }
  }

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden bg-white dark:bg-gray-900">
      <Table className="dark:text-gray-300">
        <TableHeader className="bg-gray-50 dark:bg-gray-900">
          <TableRow className="dark:border-gray-800">
            <TableHead className="w-[40px] text-center dark:text-gray-300">
              <Checkbox
                checked={allSelected}
                ref={(input) => {
                  if (input) {
                    input.indeterminate = someSelected;
                  }
                }}
                onCheckedChange={onSelectAll}
                aria-label="Select all scenarios"
                className="dark:border-gray-600"
              />
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-300"
              onClick={() => onSort("id")}
            >
              <div className="flex items-center">
                ID
                {renderSortIndicator("id")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-300"
              onClick={() => onSort("name")}
            >
              <div className="flex items-center">
                Name
                {renderSortIndicator("name")}
              </div>
            </TableHead>
            <TableHead className="dark:text-gray-300">Type</TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-300"
              onClick={() => onSort("folderId")}
            >
              <div className="flex items-center">
                Folder
                {renderSortIndicator("folderId")}
              </div>
            </TableHead>
            <TableHead className="dark:text-gray-300">TestRail</TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-300"
              onClick={() => onSort("status")}
            >
              <div className="flex items-center">
                Status
                {renderSortIndicator("status")}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-gray-300"
              onClick={() => onSort("lastRun")}
            >
              <div className="flex items-center">
                Last Updated
                {renderSortIndicator("lastRun")}
              </div>
            </TableHead>
            <TableHead className="w-[80px] text-right dark:text-gray-300">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {scenarios.length === 0 ? (
            <TableRow className="dark:border-gray-800">
              <TableCell
                colSpan={9}
                className="h-24 text-center dark:text-gray-400"
              >
                No scenarios found.
              </TableCell>
            </TableRow>
          ) : (
            scenarios.map((scenario) => (
              <TableRow
                key={scenario.id}
                className={cn(
                  "hover:bg-gray-50 dark:hover:bg-gray-800 dark:border-gray-800",
                  selectedScenarios.includes(scenario.id) && "bg-blue-50 dark:bg-blue-900/20",
                  draggedScenarioId === scenario.id && "bg-blue-100 dark:bg-blue-900/30 ring-1 ring-blue-400 dark:ring-blue-600 opacity-70"
                )}
                draggable={!!onDragScenario}
                onDragStart={(e) => {
                  if (onDragScenario) {
                    // Make drag image transparent (not fully supported in all browsers)
                    if (e.dataTransfer.setDragImage) {
                      const dragImage = new Image();
                      dragImage.src = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"; // Transparent 1x1 pixel
                      e.dataTransfer.setDragImage(dragImage, 0, 0);
                    }

                    // Set the data transfer
                    e.dataTransfer.setData('text/plain', scenario.id);
                    e.dataTransfer.effectAllowed = 'move';

                    // Set the dragged scenario ID for visual feedback
                    setDraggedScenarioId(scenario.id);

                    onDragScenario(scenario.id, e);
                  }
                }}
                onDragEnd={() => {
                  // Clear the dragged scenario ID when drag ends
                  setDraggedScenarioId(null);
                }}
              >
                <TableCell className="text-center">
                  <Checkbox
                    checked={selectedScenarios.includes(scenario.id)}
                    onCheckedChange={(checked) =>
                      onSelectScenario(scenario.id, !!checked)
                    }
                    aria-label={`Select scenario ${scenario.name}`}
                    className="dark:border-gray-600"
                  />
                </TableCell>
                <TableCell className="font-mono text-xs dark:text-gray-300">
                  {scenario.id.substring(0, 8)}
                </TableCell>
                <TableCell className="font-medium dark:text-gray-200">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                    <span>{scenario.name}</span>
                  </div>
                </TableCell>
                <TableCell>{getTestTypeIcon(scenario)}</TableCell>
                <TableCell className="dark:text-gray-300">
                  {getFolderWithIcon(scenario.folderId)}
                </TableCell>
                <TableCell>
                  {getTestRailBadge(scenario)}
                </TableCell>
                <TableCell>
                  {getStatusBadge(scenario)}
                </TableCell>
                <TableCell className="dark:text-gray-300">
                  {formatDate(scenario.lastRun || scenario.updatedAt)}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="dark:bg-gray-800 dark:border-gray-700"
                    >
                      <DropdownMenuItem
                        onClick={() => onEditScenario(scenario.id)}
                        className="dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                      >
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onDuplicateScenario(scenario.id)}
                        className="dark:text-gray-200 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                      >
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="dark:bg-gray-700" />
                      <DropdownMenuItem
                        onClick={() => onDeleteScenario(scenario.id)}
                        className="text-red-600 dark:text-red-400 dark:focus:bg-gray-700 dark:hover:bg-gray-700"
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
