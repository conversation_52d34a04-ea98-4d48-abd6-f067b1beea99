{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "lib": ["ESNext", "DOM"], "declaration": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"#*": ["./*"]}, "strict": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitAny": true, "noImplicitThis": true, "useUnknownInCatchVariables": true, "esModuleInterop": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "typeRoots": ["./node_modules/@types", "./src/types"], "allowJs": true, "checkJs": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist"]}