"use client"

import React, { createContext, useContext, ReactNode } from 'react'
import { useUnifiedScenario, UseUnifiedScenarioReturn, UseUnifiedScenarioOptions } from '@/hooks/useUnifiedScenario'

interface UnifiedScenarioContextType extends UseUnifiedScenarioReturn {}

const UnifiedScenarioContext = createContext<UnifiedScenarioContextType | null>(null)

interface UnifiedScenarioProviderProps {
  children: ReactNode
  options: UseUnifiedScenarioOptions
}

export function UnifiedScenarioProvider({ children, options }: UnifiedScenarioProviderProps) {
  const scenarioData = useUnifiedScenario(options)

  return (
    <UnifiedScenarioContext.Provider value={scenarioData}>
      {children}
    </UnifiedScenarioContext.Provider>
  )
}

export function useUnifiedScenarioContext(): UnifiedScenarioContextType {
  const context = useContext(UnifiedScenarioContext)
  if (!context) {
    throw new Error('useUnifiedScenarioContext must be used within a UnifiedScenarioProvider')
  }
  return context
}
