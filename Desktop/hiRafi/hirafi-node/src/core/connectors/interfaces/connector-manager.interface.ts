/**
 * Connector Manager Interface
 * Defines the contract for connector managers
 */

import { IBaseConnector } from './base-connector.interface.js';

/**
 * Connector manager interface
 */
export interface IConnectorManager {
  /**
   * Initialize the connector manager
   * @param config Manager configuration
   */
  initialize(config?: any): Promise<void>;

  /**
   * Create a connector by type
   * @param type Connector type
   * @param config Connector configuration
   */
  createConnector(type: string, config?: any): Promise<IBaseConnector>;

  /**
   * Get an existing connector by ID
   * @param connectorId Connector ID
   */
  getConnector(connectorId: string): IBaseConnector | null;

  /**
   * Register a connector
   * @param connectorId Connector ID
   * @param connector Connector instance
   */
  registerConnector(connectorId: string, connector: IBaseConnector): void;

  /**
   * Unregister a connector
   * @param connectorId Connector ID
   */
  unregisterConnector(connectorId: string): Promise<void>;

  /**
   * Get all registered connectors
   */
  getAllConnectors(): Map<string, IBaseConnector>;

  /**
   * Get connectors by type
   * @param type Connector type
   */
  getConnectorsByType(type: string): IBaseConnector[];

  /**
   * Close all connectors
   */
  closeAllConnectors(): Promise<void>;

  /**
   * Get manager statistics
   */
  getStats(): Record<string, any>;

  /**
   * Check if manager is initialized
   */
  isInitialized(): boolean;

  /**
   * Dispose the manager and all resources
   */
  dispose(): Promise<void>;
}

/**
 * Connector manager configuration interface
 */
export interface IConnectorManagerConfig {
  enableLogging?: boolean;
  maxConnectors?: number;
  defaultConnectorType?: string;
  autoCleanup?: boolean;
  cleanupInterval?: number;
  hubUrl?: string;
  secretKey?: string;
  nodeId?: string;
  clientId?: string;
}

/**
 * Connector registration info interface
 */
export interface IConnectorRegistrationInfo {
  id: string;
  type: string;
  connector: IBaseConnector;
  createdAt: string;
  lastUsed: string;
  usageCount: number;
}

/**
 * Connector manager statistics interface
 */
export interface IConnectorManagerStats {
  totalConnectors: number;
  connectorsByType: Record<string, number>;
  activeConnections: number;
  totalConnectionAttempts: number;
  successfulConnections: number;
  failedConnections: number;
  uptime: number;
  memoryUsage: Record<string, any>;
  connectorStats: Record<string, any>;
}
