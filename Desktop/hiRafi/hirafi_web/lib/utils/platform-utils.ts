/**
 * Platform Detection Utilities
 * Utilities for detecting and handling platform-specific functionality in reports
 */

import { ReportDetail } from '@/hooks/useReport';

export type Platform = 'web' | 'android';

/**
 * Determines the platform of a test report
 * @param report The report detail object
 * @returns The platform type ('web' or 'android')
 */
export function detectReportPlatform(report: ReportDetail | null): Platform {
  if (!report) return 'web';

  // Check explicit platform field first
  if (report.platform) {
    return report.platform;
  }

  // Check environment settings
  if (report.environmentSettings?.platform) {
    return report.environmentSettings.platform;
  }

  // Check for Android-specific indicators in environment settings
  if (report.environmentSettings) {
    // Check for SauceLabs Android settings
    if (report.environmentSettings.sauceLabs?.selectedDevices?.length > 0) {
      return 'android';
    }

    // Check for Testinium Android settings
    if (report.environmentSettings.testinium?.selectedDevices?.length > 0) {
      return 'android';
    }

    // Check for browser settings (indicates web)
    if (report.environmentSettings.browserName || report.environmentSettings.browser) {
      return 'web';
    }
  }

  // Default to web if no clear indicators
  return 'web';
}

/**
 * Checks if a report is for Android platform
 * @param report The report detail object
 * @returns True if the report is for Android platform
 */
export function isAndroidReport(report: ReportDetail | null): boolean {
  return detectReportPlatform(report) === 'android';
}

/**
 * Checks if a report is for Web platform
 * @param report The report detail object
 * @returns True if the report is for Web platform
 */
export function isWebReport(report: ReportDetail | null): boolean {
  return detectReportPlatform(report) === 'web';
}

/**
 * Gets platform-specific display name
 * @param platform The platform type
 * @returns Human-readable platform name
 */
export function getPlatformDisplayName(platform: Platform): string {
  switch (platform) {
    case 'android':
      return 'Android';
    case 'web':
      return 'Web';
    default:
      return 'Unknown';
  }
}

/**
 * Gets platform-specific icon name (for Lucide icons)
 * @param platform The platform type
 * @returns Icon name for the platform
 */
export function getPlatformIcon(platform: Platform): string {
  switch (platform) {
    case 'android':
      return 'Smartphone';
    case 'web':
      return 'Globe';
    default:
      return 'HelpCircle';
  }
}

/**
 * Gets platform-specific color scheme
 * @param platform The platform type
 * @returns Color classes for the platform
 */
export function getPlatformColors(platform: Platform): {
  bg: string;
  text: string;
  border: string;
} {
  switch (platform) {
    case 'android':
      return {
        bg: 'bg-green-100 dark:bg-green-900/20',
        text: 'text-green-800 dark:text-green-200',
        border: 'border-green-200 dark:border-green-800'
      };
    case 'web':
      return {
        bg: 'bg-blue-100 dark:bg-blue-900/20',
        text: 'text-blue-800 dark:text-blue-200',
        border: 'border-blue-200 dark:border-blue-800'
      };
    default:
      return {
        bg: 'bg-gray-100 dark:bg-gray-900/20',
        text: 'text-gray-800 dark:text-gray-200',
        border: 'border-gray-200 dark:border-gray-800'
      };
  }
}

/**
 * Extracts device information from Android report
 * @param report The report detail object
 * @returns Device information if available
 */
export function getAndroidDeviceInfo(report: ReportDetail | null): {
  deviceName?: string;
  osVersion?: string;
  manufacturer?: string;
  provider?: string;
} | null {
  if (!report || !isAndroidReport(report)) return null;

  const envSettings = report.environmentSettings;
  if (!envSettings) return null;

  // Check SauceLabs devices
  if (envSettings.sauceLabs?.selectedDevices?.length > 0) {
    const device = envSettings.sauceLabs.selectedDevices[0];
    return {
      deviceName: device.deviceName || device.name,
      osVersion: device.osVersion || device.os,
      manufacturer: device.manufacturer,
      provider: 'SauceLabs'
    };
  }

  // Check Testinium devices
  if (envSettings.testinium?.selectedDevices?.length > 0) {
    const device = envSettings.testinium.selectedDevices[0];
    return {
      deviceName: device.deviceName || device.name,
      osVersion: device.osVersion || device.os,
      manufacturer: device.manufacturer,
      provider: 'Testinium'
    };
  }

  return null;
}

/**
 * Extracts browser information from Web report
 * @param report The report detail object
 * @returns Browser information if available
 */
export function getWebBrowserInfo(report: ReportDetail | null): {
  browserName?: string;
  browserVersion?: string;
  viewportSize?: string;
} | null {
  if (!report || !isWebReport(report)) return null;

  const envSettings = report.environmentSettings;
  if (!envSettings) return null;

  return {
    browserName: envSettings.browserName || envSettings.browser,
    browserVersion: envSettings.browserVersion,
    viewportSize: envSettings.viewportSize
  };
}

/**
 * Determines if metrics are relevant for the platform
 * @param platform The platform type
 * @param metricType The type of metric
 * @returns True if the metric is relevant for the platform
 */
export function isMetricRelevantForPlatform(platform: Platform, metricType: string): boolean {
  const webOnlyMetrics = ['LCP', 'FID', 'CLS', 'TTFB', 'INP', 'pageMetrics', 'networkData'];
  const androidOnlyMetrics = ['appLaunchTime', 'memoryUsage', 'cpuUsage', 'batteryUsage'];

  if (platform === 'web') {
    return !androidOnlyMetrics.includes(metricType);
  }

  if (platform === 'android') {
    return !webOnlyMetrics.includes(metricType);
  }

  return true;
}
