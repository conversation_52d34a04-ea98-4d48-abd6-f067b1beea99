"use client"

import { useRef, useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { MessageSquareText, Camera, Send, GripVertical } from "lucide-react"
import { useAuth } from "@/lib/api/auth"
import { useFeedback } from "@/hooks/useFeedback"

export function FeedbackButton() {
  const { user } = useAuth()
  const {
    isDialogOpen,
    setIsDialogOpen,
    feedback,
    setFeedback,
    subject,
    setSubject,
    email,
    setEmail,
    screenshot,
    setScreenshot,
    loading,
    captureScreenshot,
    handleSubmit
  } = useFeedback(user?.id)

  // For draggable functionality
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [buttonPosition, setButtonPosition] = useState({ right: '20px', bottom: '20px' })
  const dragRef = useRef(null)

  // Load saved position from localStorage
  useEffect(() => {
    const savedPosition = localStorage.getItem('feedbackButtonPosition')
    if (savedPosition) {
      try {
        setButtonPosition(JSON.parse(savedPosition))
      } catch (e) {
        console.error('Failed to parse saved position', e)
      }
    }
  }, [])

  // Handle dragging logic
  const handleMouseDown = (e) => {
    if (isDialogOpen) return

    setIsDragging(true)
    const startX = e.clientX
    const startY = e.clientY

    const handleMouseMove = (moveEvent) => {
      const deltaX = moveEvent.clientX - startX
      const deltaY = moveEvent.clientY - startY

      setPosition({ x: deltaX, y: deltaY })
    }

    const handleMouseUp = () => {
      setIsDragging(false)

      // Calculate new position in viewport
      const button = dragRef.current
      if (button) {
        const rect = button.getBoundingClientRect()
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight

        // Convert to right/bottom positioning
        const newRight = Math.max(0, viewportWidth - rect.right)
        const newBottom = Math.max(0, viewportHeight - rect.bottom)

        const newPosition = { right: `${newRight}px`, bottom: `${newBottom}px` }
        setButtonPosition(newPosition)

        // Save to localStorage
        localStorage.setItem('feedbackButtonPosition', JSON.stringify(newPosition))
      }

      // Reset delta
      setPosition({ x: 0, y: 0 })

      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    e.preventDefault()
  }

  const buttonStyle = {
    right: buttonPosition.right,
    bottom: buttonPosition.bottom,
    transform: isDragging ? `translate(${position.x}px, ${position.y}px)` : 'none',
    cursor: isDragging ? 'grabbing' : 'grab',
    transition: isDragging ? 'none' : 'box-shadow 0.2s ease-in-out',
  }

  return (
    <div
      ref={dragRef}
      className="fixed z-50 select-none"
      style={buttonStyle}
      onMouseDown={handleMouseDown}
    >
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button
            size="icon"
            className="rounded-full h-12 w-12 bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700 border-none shadow-lg flex items-center justify-center"
          >
            <MessageSquareText className="h-5 w-5" />
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="text-xl">Geri Bildirim Gönder</DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4 mt-4">
            <div className="space-y-1.5">
              <Label htmlFor="email">E-posta (isteğe bağlı)</Label>
              <Input
                id="email"
                placeholder="E-posta adresiniz"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div className="space-y-1.5">
              <Label htmlFor="subject">Konu</Label>
              <Select value={subject} onValueChange={setSubject}>
                <SelectTrigger id="subject">
                  <SelectValue placeholder="Konu seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bug">Hata Bildirimi</SelectItem>
                  <SelectItem value="feature">Özellik Önerisi</SelectItem>
                  <SelectItem value="improvement">İyileştirme Önerisi</SelectItem>
                  <SelectItem value="other">Diğer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1.5">
              <Label htmlFor="feedback">Geri Bildiriminiz</Label>
              <Textarea
                id="feedback"
                placeholder="Geri bildiriminizi buraya yazın..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="min-h-[120px]"
              />
            </div>

            <div className="space-y-1.5">
              <Label>Ekran Görüntüsü</Label>
              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={captureScreenshot}
                  disabled={loading}
                  className="flex-shrink-0"
                >
                  <Camera className="h-4 w-4 mr-2" />
                  {screenshot ? "Yeni Görüntü Al" : "Ekran Görüntüsü Al"}
                </Button>

                {screenshot && (
                  <div className="text-sm text-green-600">
                    Ekran görüntüsü eklendi ✓
                  </div>
                )}
              </div>

              {screenshot && (
                <div className="mt-2 border rounded p-2 bg-gray-50 dark:bg-gray-800">
                  <div className="text-xs text-gray-500 mb-1">Önizleme:</div>
                  <div className="relative w-full aspect-video overflow-hidden rounded border">
                    <img
                      src={screenshot}
                      alt="Ekran görüntüsü"
                      className="object-cover w-full h-full"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="mt-1 h-auto p-1 text-xs text-gray-500"
                    onClick={() => setScreenshot(null)}
                  >
                    Kaldır
                  </Button>
                </div>
              )}
            </div>

            <div className="pt-4 flex justify-end">
              <Button
                type="submit"
                disabled={loading}
                className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700 border-none"
              >
                {loading ? (
                  <>
                    <span className="mr-2">Gönderiliyor...</span>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-opacity-50 border-t-transparent rounded-full"></div>
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Gönder
                  </>
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Draggable indicator */}
      {isDragging && (
        <div className="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 bg-gray-800 text-white rounded-full p-1">
          <GripVertical className="h-3 w-3" />
        </div>
      )}
    </div>
  )
}