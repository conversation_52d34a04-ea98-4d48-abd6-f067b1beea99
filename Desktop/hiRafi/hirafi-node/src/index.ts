/**
 * Test Node Main Entry Point - Refactored with Dependency Injection
 * Orchestrates test node lifecycle with proper DI container management
 */

import { ApplicationContainer } from './core/common/di/application-container.js';
import { ILoggerService } from './utils/logger-service.js';
import { config, updateConfig } from './config/index.js';
import { logger } from './utils/logger.js'; // Backward compatibility
import nodeManager from './core/node-manager/index.js';
import { RedisConnector } from './core/connectors/redis/redis-connector.js';
import { TestPlatform } from './models/platform-types.js';

// Application DI container
let appContainer: ApplicationContainer | null = null;
let appLogger: ILoggerService | null = null;
let redisClient: RedisConnector | null = null;

/**
 * Initialize the application with dependency injection
 */
async function initializeApplication(): Promise<void> {
  try {
    // Initialize the DI container
    appContainer = new ApplicationContainer();
    await appContainer.initialize();

    // Get services from the container
    appLogger = await appContainer.getLoggerService();

    // The container should be responsible for creating and managing the RedisConnector.
    if (config.redisEnabled) {
      redisClient = await appContainer.getRedisConnector();
    }

    const currentLogger = appLogger || logger;

    // Log startup banner using DI logger
    currentLogger.info(`🚀 Test Node v${config.version} | ${config.name} | ${config.environment}`);

    // Also log with backward compatibility logger for existing code
    logger.info(`🚀 Test Node v${config.version} | ${config.name} | ${config.environment}`);
  } catch (error: any) {
    // Use the global logger as a fallback if appLogger is not yet initialized
    (appLogger || logger).error('Failed to initialize application:', error);
    throw error;
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  const currentLogger = appLogger || logger;
  currentLogger.info('Received SIGINT. Shutting down gracefully...');

  // Notify TestExecutionManager about graceful shutdown before shutting down
  try {
    const testExecutionManager = nodeManager.getTestExecutionManager();
    if (testExecutionManager) {
      testExecutionManager.handleGracefulShutdown('SIGINT received');
    }
  } catch (error) {
    currentLogger.warn('Could not notify TestExecutionManager about graceful shutdown:', error);
  }

  await shutdown();
});

process.on('SIGTERM', async () => {
  const currentLogger = appLogger || logger;
  currentLogger.info('Received SIGTERM. Shutting down gracefully...');

  // Notify TestExecutionManager about graceful shutdown before shutting down
  try {
    const testExecutionManager = nodeManager.getTestExecutionManager();
    if (testExecutionManager) {
      testExecutionManager.handleGracefulShutdown('SIGTERM received');
    }
  } catch (error) {
    currentLogger.warn('Could not notify TestExecutionManager about graceful shutdown:', error);
  }

  await shutdown();
});

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
  const currentLogger = appLogger || logger;
  currentLogger.error('Uncaught exception:', error);

  // Notify TestExecutionManager about crash before shutting down
  try {
    const testExecutionManager = nodeManager.getTestExecutionManager();
    if (testExecutionManager) {
      testExecutionManager.handleProcessCrash(`Uncaught exception: ${error.message}`);
    }
  } catch (notifyError) {
    currentLogger.warn('Could not notify TestExecutionManager about crash:', notifyError);
  }

  await shutdown();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  const currentLogger = appLogger || logger;
  currentLogger.error('Unhandled promise rejection:', reason);

  // Özel hata durumlarını kontrol et
  const errorStr = String(reason);

  // Browser/Puppeteer related errors that should not shutdown the node
  // Reduced list since coordinated metrics collection handles most race conditions
  const browserErrorPatterns = [
    'Navigating frame was detached',
    'TargetCloseError',
    'Connection closed',
    'WebSocket is not open',
    'Page crashed',
    'Browser has been closed',
    'rebrowser-patches'
  ];

  const isBrowserError = browserErrorPatterns.some(pattern =>
    errorStr.includes(pattern)
  );

  if (isBrowserError) {
    currentLogger.warn('Browser/Puppeteer error detected. Not shutting down node.');
    currentLogger.warn(`Error details: ${errorStr}`);
    // Bu tür hatalar için node'u kapatma, sadece loglama yap
    return;
  }

  // Notify TestExecutionManager about crash before shutting down for critical errors
  try {
    const testExecutionManager = nodeManager.getTestExecutionManager();
    if (testExecutionManager) {
      testExecutionManager.handleProcessCrash(`Unhandled rejection: ${errorStr}`);
    }
  } catch (notifyError) {
    currentLogger.warn('Could not notify TestExecutionManager about crash:', notifyError);
  }

  // Diğer kritik hatalar için shutdown işlemini başlat
  await shutdown();
});

// Graceful shutdown function with DI cleanup
async function shutdown(): Promise<void> {
  try {
    const currentLogger = appLogger || logger;

    currentLogger.info('Stopping node manager...');
    await nodeManager.stop();
    currentLogger.info('Node manager stopped successfully');

    // Close Redis connection if it was initialized
    if (redisClient) {
      currentLogger.info('Closing Redis connection...');
      await redisClient.close();
      currentLogger.info('Redis connection closed successfully');
    }

    // Dispose of the DI container
    if (appContainer) {
      currentLogger.info('Disposing application container...');
      await appContainer.dispose();
      currentLogger.info('Application container disposed successfully');
    }
  } catch (error) {
    const currentLogger = appLogger || logger;
    currentLogger.error('Error during shutdown:', error);
  } finally {
    const finalLogger = appLogger || logger;
    finalLogger.info('Test node shutdown complete');
    process.exit(0);
  }
}

// Start the node manager with DI
async function startServer(): Promise<void> {
  try {
    // Initialize the application first
    await initializeApplication();

    const currentLogger = appLogger || logger;

    // Get storage provider from DI container
    const artifactStorage = await appContainer!.getStorageProvider();

    // Update node capabilities based on platform configuration
    updateNodeCapabilities();

    // The application container is now passed to the node manager
    const success = await nodeManager.start(appContainer!);

    if (success) {
      currentLogger.info(`✅ Test node started successfully | Storage: ${artifactStorage.getProviderName()} | Capabilities: ${config.capabilities.join(', ')}`);

      // Mark startup as complete for the application logger
      currentLogger.markStartupComplete();
    } else {
      currentLogger.error('Failed to start test node');
      await shutdown();
    }
  } catch (error) {
    const currentLogger = appLogger || logger;
    currentLogger.error('💥 Error starting test node:', error);
    await shutdown();
  }
}

/**
 * Update node capabilities based on platform configuration
 * This ensures that the node registers with the correct capabilities
 * for the platforms it supports (web, android)
 */
function updateNodeCapabilities(): void {
  const capabilities = [...config.capabilities]; // Clone existing capabilities

  // Always add web capability regardless of configuration
  if (!capabilities.includes(TestPlatform.WEB)) {
    capabilities.push(TestPlatform.WEB);
    (appLogger || logger).debug('Adding web platform capability');
  }

  // Always add android capability regardless of configuration
  if (!capabilities.includes(TestPlatform.ANDROID)) {
    capabilities.push(TestPlatform.ANDROID);
    (appLogger || logger).debug('Adding android platform capability');
  }

  // Update the config with the new capabilities
  updateConfig('capabilities', capabilities);
}

// Start the application
startServer();