"use client"

import { useRunForm } from "../run-context"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { LineChart, Network, Zap, Accessibility, HelpCircle } from "lucide-react"
import { motion } from "framer-motion"

export function WebReportSettings() {
  const { reportSettings, updateReportSettings } = useRunForm()

  // Toggle switches için handler fonksi<PERSON>lar<PERSON>
  const handleTogglePageMetrics = (checked: boolean) => {
    updateReportSettings({ pageMetrics: checked })
  }

  const handleToggleNetworkData = (checked: boolean) => {
    updateReportSettings({ networkData: checked })
  }

  const handleToggleTracingData = (checked: boolean) => {
    updateReportSettings({ tracingData: checked })
  }

  const handleToggleAccessibilityData = (checked: boolean) => {
    updateReportSettings({ accessibilityData: checked })
  }

  return (
    <TooltipProvider>
      {/* Sayfa metrikleri */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-full">
            <LineChart className="h-5 w-5 text-emerald-500" />
          </div>
          <div>
            <Label htmlFor="metrics" className="text-sm font-medium">
              Collect Page Metrics
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Collect performance metrics such as loading times and resource usage
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="metrics"
            checked={reportSettings.pageMetrics}
            onCheckedChange={handleTogglePageMetrics}
            className="data-[state=checked]:bg-emerald-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">Page metrics include load time, DOM size, memory usage, and other performance indicators.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>

      {/* Ağ verileri */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-full">
            <Network className="h-5 w-5 text-emerald-500" />
          </div>
          <div>
            <Label htmlFor="network" className="text-sm font-medium">
              Collect Network Data
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Record network requests, responses, and timings for detailed analysis
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="network"
            checked={reportSettings.networkData}
            onCheckedChange={handleToggleNetworkData}
            className="data-[state=checked]:bg-emerald-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">Network data includes all HTTP requests, responses, headers, and timing information.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>

      {/* İzleme verileri */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-full">
            <Zap className="h-5 w-5 text-emerald-500" />
          </div>
          <div>
            <Label htmlFor="tracing" className="text-sm font-medium">
              Enable Tracing
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Collect detailed browser tracing data for advanced performance analysis
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="tracing"
            checked={reportSettings.tracingData}
            onCheckedChange={handleToggleTracingData}
            className="data-[state=checked]:bg-emerald-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">Tracing provides detailed performance data about JavaScript execution, rendering, and browser events.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>

      {/* Erişilebilirlik verileri */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        className="flex items-start justify-between space-x-4 p-4 rounded-lg bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="flex items-start gap-3">
          <div className="mt-1 bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-full">
            <Accessibility className="h-5 w-5 text-emerald-500" />
          </div>
          <div>
            <Label htmlFor="accessibility" className="text-sm font-medium">
              Collect Accessibility Data
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Analyze pages for accessibility issues and WCAG compliance
            </p>
          </div>
        </div>
        <div className="flex gap-2 items-center">
          <Switch
            id="accessibility"
            checked={reportSettings.accessibilityData}
            onCheckedChange={handleToggleAccessibilityData}
            className="data-[state=checked]:bg-emerald-500"
          />
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-gray-400 cursor-help" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">Accessibility data helps identify issues that might affect users with disabilities, such as missing alt text, contrast issues, and keyboard navigation problems.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
