/**
 * Permission Middleware
 * Kullanıcının belirli bir kaynağa erişim yetkisini kontrol eder
 */

import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from './authMiddleware.js';
import { logger } from '../../utils/logger.js';
import { db } from '../../services/mongo/dbConnection.js';

/**
 * Kullanıcının belirli bir kaynağa erişim yetkisini kontrol eder
 * @param resource Erişilmek istenen kaynak (scenario, run, report, schedule)
 * @param action İşlem türü (create, read, update, delete, execute)
 */
export function checkPermission(resource: string, action: string) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const teamId = req.user?.teamId;
      const accountType = req.user?.accountType;

      logger.debug(`[PERMISSION] Checking permission for user ${userId}, resource: ${resource}, action: ${action}`);

      // Company owner ve admin her zaman erişebilir
      if (accountType === 'company_owner' || accountType === 'admin') {
        logger.debug(`[PERMISSION] User ${userId} is ${accountType}, granting access to ${resource}:${action}`);
        return next();
      }

      // Kullanıcı kimliği kontrolü
      if (!userId || !teamId) {
        logger.warn(`[PERMISSION] Permission check failed: User ID (${userId}) or Team ID (${teamId}) missing`);
        return res.status(401).json({
          success: false,
          error: 'Unauthorized: User ID or Team ID is missing'
        });
      }

      // Kullanıcının takım üyeliğini ve rolünü kontrol et
      if (!db) {
        logger.error('Database connection is not initialized');
        return res.status(500).json({
          success: false,
          error: 'Internal server error: Database connection not available'
        });
      }

      const rolePermissionsCollection = db.collection('role_permissions');

      logger.debug(`[PERMISSION] Querying team membership for user ${userId} in team ${teamId}`);
      const teamMember = await rolePermissionsCollection.findOne({
        user_id: userId,
        team_id: teamId,
        status: 'active',
        is_system: false
      });

      logger.debug(`[PERMISSION] Team member query result:`, teamMember);

      if (!teamMember) {
        logger.warn(`[PERMISSION] User ${userId} is not an active member of team ${teamId}`);
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to execute this run'
        });
      }

      let roleId = teamMember.role_id;
      logger.debug(`[PERMISSION] User ${userId} has role_id: ${roleId} in team ${teamId}`);

      if (!roleId) {
        logger.warn(`[PERMISSION] User ${userId} has no role assigned in team ${teamId}`);
        return res.status(403).json({
          success: false,
          error: 'You do not have permission to execute this run'
        });
      }

      // Role ID format handling - support both UUID and legacy formats
      const originalRoleId = roleId;
      logger.debug(`[PERMISSION] Original role ID: ${originalRoleId}`);

      // Prepare multiple search strategies for role lookup
      const roleSearchStrategies = [
        originalRoleId,  // Direct UUID or exact role ID
      ];

      // If role ID doesn't look like UUID, try team-specific format
      if (!originalRoleId.includes('-') && !originalRoleId.includes('_')) {
        roleSearchStrategies.push(`${originalRoleId}_${teamId}`);
      }

      // If role ID contains underscore, try base role ID
      if (originalRoleId.includes('_')) {
        const parts = originalRoleId.split('_');
        roleSearchStrategies.push(parts[0]);
      }

      logger.debug(`[PERMISSION] Role search strategies: ${roleSearchStrategies.join(', ')}`);

      // İzinleri role_permissions koleksiyonundan al
      let permissions = [];

      // role_permissions'da zaten permissions mevcut
      if (teamMember.permissions && Array.isArray(teamMember.permissions)) {
        logger.debug(`[PERMISSION] Using permissions from role_permissions for user ${userId}:`, teamMember.permissions);
        permissions = teamMember.permissions;
      } else {
        logger.warn(`[PERMISSION] No permissions found for user ${userId} in team ${teamId}`);
        permissions = [];
      }



      // Wildcard izni varsa (team_admin gibi), her şeye erişim ver
      const hasWildcardPermission = permissions.some(
        (p: any) => (p.resource === '*' && p.action === 'manage') ||
                    (p.resource === '*' && p.action === '*') ||
                    (p.resource === resource && p.action === 'manage') ||
                    (p.resource === resource && p.action === '*')
      );

      if (hasWildcardPermission) {
        logger.debug(`[PERMISSION] User ${userId} has wildcard permission for ${resource}, granting access`);
        return next();
      }

      // Belirli kaynak ve işlem için izin kontrolü
      const hasPermission = permissions.some(
        (p: any) => {
          // Kaynak kontrolü
          const resourceMatch = p.resource === resource || p.resource === '*';

          // İşlem kontrolü - view ve read işlemlerini eşdeğer olarak kabul et
          let actionMatch = p.action === action || p.action === 'manage';

          // Eğer action 'view' ise, 'read' izni de kabul et
          if (action === 'view' && p.action === 'read') {
            actionMatch = true;
          }

          // Eğer action 'read' ise, 'view' izni de kabul et
          if (action === 'read' && p.action === 'view') {
            actionMatch = true;
          }

          // Eğer action 'update' ise, 'edit' izni de kabul et
          if (action === 'update' && p.action === 'edit') {
            actionMatch = true;
          }

          // Eğer action 'edit' ise, 'update' izni de kabul et
          if (action === 'edit' && p.action === 'update') {
            actionMatch = true;
          }

          logger.debug(`[PERMISSION] Checking permission: resource=${p.resource} (match: ${resourceMatch}), action=${p.action} (match: ${actionMatch})`);
          return resourceMatch && actionMatch;
        }
      );

      if (hasPermission) {
        logger.debug(`[PERMISSION] User ${userId} has permission for ${resource}:${action}, granting access`);
        return next();
      }

      // İzin yoksa erişimi reddet
      logger.warn(`[PERMISSION] User ${userId} does not have permission for ${resource}:${action}. Available permissions:`, permissions);
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to execute this run'
      });
    } catch (error: any) {
      logger.error(`Error in permission middleware: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: 'Internal server error during permission check'
      });
    }
  };
}
