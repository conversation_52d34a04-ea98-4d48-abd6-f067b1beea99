"use client"

import { useRunForm } from "../run-context"
import { WebRunPreview } from "./web-run-preview"
import { AndroidRunPreview } from "./android-run-preview"
import { RunPreviewBase } from "./run-preview-base"
import { PlatformType } from "../run-context"

// Define interfaces for the result of submitRun
interface SubmitRunResult {
  success: boolean;
  runId?: string;
  error?: string;
}

// RunPreview props için interface
interface RunPreviewProps {
  isEditing?: boolean;
  runId?: string;
}

export function RunPreview({ isEditing = false, runId }: RunPreviewProps) {
  const { formData } = useRunForm()
  const platform = formData.platform as PlatformType || 'web'

  return (
    <div className="space-y-6">
      {/* Base preview component with common elements */}
      <RunPreviewBase isEditing={isEditing} runId={runId}>
        {/* Platform-specific preview components */}
        {platform === 'android' ? (
          <AndroidRunPreview />
        ) : (
          <WebRunPreview />
        )}
      </RunPreviewBase>
    </div>
  )
}
