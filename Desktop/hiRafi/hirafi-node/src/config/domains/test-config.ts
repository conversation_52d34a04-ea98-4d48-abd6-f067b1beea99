/**
 * Test Configuration Domain
 * Handles test execution and options configuration
 */

import { TestOptionsConfigDomain } from '../types/config-types.js';

/**
 * Creates test options configuration with optimized static values
 * @returns Test options configuration object
 */
export function createTestConfig(): TestOptionsConfigDomain {
  return {
    // Optimized retry count - 2 retries should be sufficient for most cases
    maxRetries: 2,
    // Optimized timeout - 45 seconds provides good balance between speed and reliability
    timeout: 45000
  };
}

/**
 * Validates test configuration
 * @param config Test configuration to validate
 * @returns Validation result
 */
export function validateTestConfig(config: TestOptionsConfigDomain): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate max retries
  if (config.maxRetries < 0) {
    errors.push('Max retries must be 0 or greater');
  }

  if (config.maxRetries > 10) {
    errors.push('Max retries should not exceed 10 for practical reasons');
  }

  // Validate timeout
  if (config.timeout < 1000) {
    errors.push('Timeout must be at least 1000ms (1 second)');
  }

  if (config.timeout > 300000) {
    errors.push('Timeout should not exceed 300000ms (5 minutes) for practical reasons');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets default test configuration
 * @returns Default test configuration
 */
export function getDefaultTestConfig(): TestOptionsConfigDomain {
  return {
    maxRetries: 2,
    timeout: 45000
  };
}

/**
 * Merges test configuration with overrides
 * @param baseConfig Base configuration
 * @param overrides Configuration overrides
 * @returns Merged configuration
 */
export function mergeTestConfig(
  baseConfig: TestOptionsConfigDomain,
  overrides: Partial<TestOptionsConfigDomain>
): TestOptionsConfigDomain {
  return {
    ...baseConfig,
    ...overrides
  };
}

/**
 * Calculates total test timeout including retries
 * @param config Test configuration
 * @returns Total timeout in milliseconds
 */
export function calculateTotalTimeout(config: TestOptionsConfigDomain): number {
  return config.timeout * (config.maxRetries + 1);
}

/**
 * Gets timeout configuration for different test phases
 * @param config Test configuration
 * @returns Timeout configuration object
 */
export function getTimeoutConfiguration(config: TestOptionsConfigDomain) {
  const baseTimeout = config.timeout;

  return {
    step: baseTimeout,
    scenario: calculateTotalTimeout(config),
    navigation: Math.min(baseTimeout * 0.3, 10000), // 30% of step timeout, max 10s
    element: Math.min(baseTimeout * 0.2, 5000),     // 20% of step timeout, max 5s
    assertion: Math.min(baseTimeout * 0.1, 3000)    // 10% of step timeout, max 3s
  };
}

/**
 * Validates timeout value
 * @param timeout Timeout value in milliseconds
 * @param context Context for the timeout (e.g., 'step', 'scenario')
 * @returns Validation result
 */
export function validateTimeout(timeout: number, context: string = 'general'): { isValid: boolean; error?: string } {
  if (timeout < 0) {
    return { isValid: false, error: `${context} timeout cannot be negative` };
  }

  if (timeout === 0) {
    return { isValid: false, error: `${context} timeout cannot be zero` };
  }

  if (timeout < 1000) {
    return { isValid: false, error: `${context} timeout should be at least 1000ms` };
  }

  const maxTimeouts = {
    step: 300000,     // 5 minutes
    scenario: 1800000, // 30 minutes
    navigation: 30000, // 30 seconds
    element: 10000,    // 10 seconds
    assertion: 5000    // 5 seconds
  };

  const maxTimeout = maxTimeouts[context as keyof typeof maxTimeouts] || 300000;

  if (timeout > maxTimeout) {
    return {
      isValid: false,
      error: `${context} timeout should not exceed ${maxTimeout}ms`
    };
  }

  return { isValid: true };
}

/**
 * Gets retry configuration for different test operations
 * @param config Test configuration
 * @returns Retry configuration object
 */
export function getRetryConfiguration(config: TestOptionsConfigDomain) {
  return {
    maxRetries: config.maxRetries,
    stepRetries: config.maxRetries,
    elementRetries: Math.min(config.maxRetries, 3), // Limit element retries
    assertionRetries: Math.min(config.maxRetries, 2), // Limit assertion retries
    navigationRetries: Math.min(config.maxRetries, 1) // Limit navigation retries
  };
}
