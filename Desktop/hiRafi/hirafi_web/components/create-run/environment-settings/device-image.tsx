"use client"

import { useState, useRef, useEffect } from "react"
import { Smartphone, Tablet } from "lucide-react"
import { cn } from "@/lib/utils"

interface DeviceImageProps {
  deviceId: string
  deviceName: string
  deviceType?: 'phone' | 'tablet' | 'other'
  className?: string
  fallbackClassName?: string
  alt?: string
}

// CloudFront base URL for device images
const CLOUDFRONT_BASE_URL = "https://d3ty40hendov17.cloudfront.net/device-pictures"

/**
 * Converts device name/ID to CloudFront image URL
 * Replaces spaces with underscores and appends the optimized suffix
 * Tries both _real_optimised.png and _optimised.png formats
 *
 * Examples:
 * - "Samsung Galaxy S23 FE" -> "Samsung_Galaxy_S23_FE_real_optimised.png" or "Samsung_Galaxy_S23_FE_optimised.png"
 * - "Motorola moto e20" -> "Motorola_moto_e20_real_optimised.png"
 * - "Google Pixel 7 Pro" -> "Google_Pixel_7_Pro_optimised.png"
 */
function getDeviceImageUrls(deviceId: string, deviceName: string): string[] {
  // Use device name if available, otherwise use device ID
  const nameToUse = deviceName || deviceId

  if (!nameToUse) {
    return [
      `${CLOUDFRONT_BASE_URL}/unknown_device_real_optimised.png`,
      `${CLOUDFRONT_BASE_URL}/unknown_device_optimised.png`
    ]
  }

  // Replace spaces with underscores and remove special characters
  const cleanName = nameToUse
    .trim()
    .replace(/\s+/g, '_')
    .replace(/[()[\]]/g, '')
    .replace(/[^\w\-_.]/g, '')
    .replace(/_+/g, '_')
    .replace(/^_|_$/g, '')

  // Fallback to device ID if name becomes empty after cleaning
  const finalName = cleanName || deviceId || 'unknown_device'

  // Return both possible URL formats - try _real_optimised first, then _optimised
  return [
    `${CLOUDFRONT_BASE_URL}/${finalName}_real_optimised.png`,
    `${CLOUDFRONT_BASE_URL}/${finalName}_optimised.png`
  ]
}

/**
 * Custom hook for intersection observer-based lazy loading
 */
function useIntersectionObserver(
  elementRef: React.RefObject<HTMLElement | null>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options,
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [hasIntersected, options])

  return { isIntersecting, hasIntersected }
}

export function DeviceImage({
  deviceId,
  deviceName,
  deviceType = 'phone',
  className,
  fallbackClassName,
  alt
}: DeviceImageProps) {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading')
  const [currentUrlIndex, setCurrentUrlIndex] = useState(0)
  const [retryCount, setRetryCount] = useState(0)
  const imageRef = useRef<HTMLDivElement>(null)
  const imgRef = useRef<HTMLImageElement>(null)

  const { hasIntersected } = useIntersectionObserver(imageRef, {
    rootMargin: '100px',
    threshold: 0.1
  })

  const imageUrls = getDeviceImageUrls(deviceId, deviceName)
  const currentImageUrl = imageUrls[currentUrlIndex]
  const maxRetries = 2

  // Handle image load success
  const handleImageLoad = () => {
    setImageState('loaded')
  }

  // Handle image load error with URL fallback and retry logic
  const handleImageError = () => {
    // First, try the next URL format if available
    if (currentUrlIndex < imageUrls.length - 1) {
      setCurrentUrlIndex(prev => prev + 1)
      setRetryCount(0) // Reset retry count for new URL
      return
    }

    // If we've tried all URL formats, try retrying the current URL
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1)
      // Retry after a short delay with exponential backoff
      const delay = Math.min(1000 * Math.pow(2, retryCount), 5000)
      setTimeout(() => {
        if (imgRef.current) {
          imgRef.current.src = `${currentImageUrl}?retry=${retryCount + 1}&t=${Date.now()}`
        }
      }, delay)
    } else {
      // All URLs and retries exhausted, show error state
      setImageState('error')
    }
  }

  // Reset state when device changes
  useEffect(() => {
    setRetryCount(0)
    setCurrentUrlIndex(0)
    setImageState('loading')
  }, [deviceId, deviceName])

  // Get appropriate fallback icon
  const FallbackIcon = deviceType === 'tablet' ? Tablet : Smartphone

  return (
    <div
      ref={imageRef}
      className={cn(
        "relative overflow-hidden flex items-center justify-center",
        className
      )}
    >
      {hasIntersected ? (
        <>
          {/* Actual device image - optimized for larger containers */}
          <img
            ref={imgRef}
            src={currentImageUrl}
            alt={alt || `${deviceName} device image`}
            className={cn(
              "w-full h-full object-contain transition-opacity duration-300 scale-105",
              imageState === 'loaded' ? 'opacity-100' : 'opacity-0'
            )}
            onLoad={handleImageLoad}
            onError={handleImageError}
            loading="lazy"
          />

          {/* Loading state - optimized for larger containers */}
          {imageState === 'loading' && (
            <div className="absolute inset-0 flex items-center justify-center bg-transparent">
              <div className="bg-white/90 dark:bg-gray-900/90 rounded-full p-3 shadow-md">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              </div>
            </div>
          )}

          {/* Error fallback - optimized for larger containers */}
          {imageState === 'error' && (
            <div className={cn(
              "absolute inset-0 flex items-center justify-center bg-transparent",
              fallbackClassName
            )}>
              <div className="flex flex-col items-center gap-2 bg-gray-50/90 dark:bg-gray-800/90 rounded-lg p-3 shadow-sm">
                <FallbackIcon className="h-6 w-6 text-gray-400" />
                <span className="text-xs text-gray-500 text-center leading-tight font-medium">
                  {deviceType}
                </span>
              </div>
            </div>
          )}
        </>
      ) : (
        /* Placeholder before intersection - optimized for larger containers */
        <div className={cn(
          "absolute inset-0 flex items-center justify-center bg-transparent",
          fallbackClassName
        )}>
          <div className="bg-gray-50/70 dark:bg-gray-800/70 rounded-lg p-3">
            <FallbackIcon className="h-6 w-6 text-gray-400" />
          </div>
        </div>
      )}
    </div>
  )
}
