/**
 * Node-side Step Progress Queue Service
 * Sends step progress directly to Redis BullMQ step-progress-queue instead of using WebSocket
 * This provides more reliable step progress transmission and consistent architecture
 */

import { Queue, QueueOptions } from 'bullmq';
import { EventEmitter } from 'events';
import { randomBytes } from 'crypto';
import { logger } from '../../../../utils/logger.js';
import { config } from '../../../../config/index.js';
import { REDIS_QUEUE_NAMES, REDIS_JOB_TYPES } from '../../../connectors/interfaces/redis-connector.interface.js';
import { StepProgressData } from '../../interfaces/test-progress-reporter.interface.js';

export interface StepProgressQueueServiceEvents {
  'step-progress:added': (testId: string, data: any) => void;
  'step-progress:error': (testId: string, error: Error) => void;
}

/**
 * Node-side Step Progress Queue Service
 * Handles direct transmission of step progress to Redis BullMQ step-progress-queue
 */
class NodeStepProgressQueueService extends EventEmitter {
  private stepProgressQueue: Queue | null = null;
  private isInitialized: boolean = false;

  constructor() {
    super();
  }

  /**
   * Initialize the step progress queue service
   */
  public async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) {
        logger.debug('NodeStepProgressQueueService: Already initialized');
        return true;
      }

      if (!config.redisEnabled) {
        logger.error('NodeStepProgressQueueService: Redis is not enabled, cannot initialize step progress queue service');
        return false;
      }

      // Create Redis connection options for BullMQ
      const connectionOptions = {
        host: config.redis?.host || 'localhost',
        port: config.redis?.port || 6379,
        password: config.redis?.password || undefined,
        db: config.redis?.db || 0,
        maxRetriesPerRequest: null, // Required for BullMQ
      };

      // Create queue options
      const queueOptions: QueueOptions = {
        connection: connectionOptions,
        defaultJobOptions: {
          attempts: 3,
          backoff: {
            type: 'fixed',
            delay: 500 // Fast retry for step progress
          },
          removeOnComplete: 50, // Keep fewer completed jobs since they're temporary
          removeOnFail: 100,
          delay: 0 // Process immediately
        }
      };

      // Create the step progress queue
      this.stepProgressQueue = new Queue(REDIS_QUEUE_NAMES.STEP_PROGRESS_QUEUE, queueOptions);

      // Set up queue event handlers
      this.stepProgressQueue.on('error', (error: Error) => {
        logger.error(`NodeStepProgressQueueService: Queue error: ${error.message}`);
        this.emit('step-progress:error', 'queue', error);
      });

      this.isInitialized = true;
      logger.info('NodeStepProgressQueueService: Successfully initialized step progress queue');
      return true;

    } catch (error: any) {
      logger.error(`NodeStepProgressQueueService: Initialization failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Add step progress to the queue
   * @param testId Test ID
   * @param stepProgressData Step progress data
   * @param nodeId Node ID that is processing the step
   * @returns Promise resolving to the job ID if successful, null otherwise
   */
  public async addStepProgress(
    testId: string, 
    stepProgressData: StepProgressData, 
    nodeId: string
  ): Promise<string | null> {
    if (!this.isInitialized || !this.stepProgressQueue) {
      logger.error('NodeStepProgressQueueService: Service not initialized');
      return null;
    }

    try {
      // Generate transaction ID for tracking
      const transactionId = randomBytes(8).toString('hex');

      // Prepare step progress data for queue
      const queueData = {
        testId,
        nodeId,
        transactionId,
        timestamp: new Date().toISOString(),
        stepProgress: {
          ...stepProgressData,
          nodeId, // Ensure nodeId is included in step progress data
        }
      };

      // Log with better context for nested steps
      const stepInfo = stepProgressData.stepIndex !== undefined 
        ? `${stepProgressData.stepIndex}/${stepProgressData.totalSteps || 'N/A'}`
        : 'nested';
      const stepName = stepProgressData.stepName || stepProgressData.stepType || 'unknown';
      
      logger.info(`NodeStepProgressQueueService: Adding step progress to queue for test ${testId}. TransactionId: ${transactionId}, step: ${stepInfo}, name: "${stepName}", status: ${stepProgressData.status}`);

      // Add job to queue with high priority for real-time updates
      const job = await this.stepProgressQueue.add(
        REDIS_JOB_TYPES.STEP_PROGRESS,
        queueData,
        {
          // HIGH PRIORITY: Use priority to ensure step progress is processed immediately
          priority: 15, // Higher priority than test results for real-time updates
          attempts: 3,
          backoff: {
            type: 'fixed',
            delay: 200 // Very fast retry for step progress
          },
          // Keep step progress for UI display - remove after 30 minutes for active monitoring
          removeOnComplete: {
            count: 50, // Keep more completed jobs for UI display
            age: 30 * 60 * 1000 // Remove after 30 minutes to allow UI monitoring
          },
          removeOnFail: {
            count: 20, // Keep only last 20 failed jobs
            age: 30 * 60 * 1000 // Remove after 30 minutes
          },
          delay: 0 // Process immediately
        }
      );

      if (job && job.id) {
        logger.debug(`NodeStepProgressQueueService: Successfully added step progress job ${job.id} for test ${testId}`);
        this.emit('step-progress:added', testId, queueData);
        return job.id;
      } else {
        logger.error(`NodeStepProgressQueueService: Failed to add step progress job for test ${testId} - no job ID returned`);
        return null;
      }

    } catch (error: any) {
      logger.error(`NodeStepProgressQueueService: Error adding step progress for test ${testId}: ${error.message}`);
      this.emit('step-progress:error', testId, error);
      return null;
    }
  }

  /**
   * Get queue statistics
   */
  public async getStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  } | null> {
    if (!this.stepProgressQueue) {
      return null;
    }

    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.stepProgressQueue.getWaitingCount(),
        this.stepProgressQueue.getActiveCount(),
        this.stepProgressQueue.getCompletedCount(),
        this.stepProgressQueue.getFailedCount(),
        this.stepProgressQueue.getDelayedCount()
      ]);

      return { waiting, active, completed, failed, delayed };
    } catch (error: any) {
      logger.error(`NodeStepProgressQueueService: Error getting stats: ${error.message}`);
      return null;
    }
  }

  /**
   * Check if the service is initialized
   */
  public isServiceInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Close the queue service
   */
  public async close(): Promise<void> {
    if (this.stepProgressQueue) {
      await this.stepProgressQueue.close();
      this.stepProgressQueue = null;
    }
    this.isInitialized = false;
    logger.info('NodeStepProgressQueueService: Closed step progress queue service');
  }
}

/**
 * Factory function for creating NodeStepProgressQueueService instances
 * Used by DI container for dependency injection
 */
export function createNodeStepProgressQueueService(): NodeStepProgressQueueService {
  return new NodeStepProgressQueueService();
}

// Export class for DI container registration
export { NodeStepProgressQueueService };
