import PDFDocument from 'pdfkit';
import { Readable } from 'stream';

// <PERSON><PERSON> boyutları ve kenar boşlukları
const PAGE_WIDTH = 595.28;
const PAGE_MARGIN = 50;
const CONTENT_WIDTH = PAGE_WIDTH - (PAGE_MARGIN * 2);
const ROW_GAP = 5;

/**
 * PDF formatında senaryo export işlemleri için yardımcı fonksiyonlar
 */
export async function createPdfFromScenarios(
  scenarios: any[],
  includeFields: {
    steps: boolean;
    description: boolean;
    tags: boolean;
    status: boolean;
    lastRun: boolean;
    duration: boolean;
  }
): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    try {
      // PDF dökümanı oluştur - A4 boyutunda ve kenar boşlukları ile
      const doc = new PDFDocument({
        margin: PAGE_MARGIN,
        size: 'A4',
        bufferPages: true,
        autoFirstPage: true,
        info: {
          Title: 'Test Scenarios Export',
          Author: 'AI Drive Test Hub',
          Subject: 'Test Scenarios',
          Keywords: 'test, scenarios, export',
          Creator: 'AI Drive Test Hub',
          Producer: 'PDFKit'
        }
      });

      // Buffer'a yazmak için stream oluştur
      const buffers: Buffer[] = [];

      doc.on('data', buffers.push.bind(buffers));
      doc.on('end', () => {
        const pdfData = Buffer.concat(buffers);
        resolve(pdfData);
      });

      // Başlık ve üst bilgileri ekle
      addHeader(doc, scenarios.length);

      // Senaryoları tabloya ekle
      addScenariosTable(doc, scenarios, includeFields);

      // Sayfa numaralarını ekle
      addPageNumbers(doc);

      // PDF'i sonlandır
      doc.end();

    } catch (error) {
      reject(error);
    }
  });
}

/**
 * PDF'e başlık ve üst bilgileri ekler
 */
function addHeader(doc: PDFKit.PDFDocument, totalScenarios: number) {
  // Logo veya ikon eklenebilir
  doc.rect(PAGE_MARGIN, PAGE_MARGIN, CONTENT_WIDTH, 60)
     .fillAndStroke('#f0f4f8', '#e1e8ed');

  // Başlık ekle
  doc.fontSize(24)
     .font('Helvetica-Bold')
     .fillColor('#1a365d')
     .text('Test Scenarios Export', PAGE_MARGIN + 10, PAGE_MARGIN + 15, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Alt başlık ekle
  doc.fontSize(10)
     .font('Helvetica')
     .fillColor('#4a5568')
     .text(`Generated on: ${new Date().toLocaleString()}`, PAGE_MARGIN + 10, PAGE_MARGIN + 40, {
       width: CONTENT_WIDTH - 20,
       align: 'center'
     });

  // Toplam senaryo sayısını ekle
  doc.moveDown(2)
     .fontSize(12)
     .font('Helvetica-Bold')
     .fillColor('#2d3748')
     .text(`Total Scenarios: ${totalScenarios}`, { align: 'left' });

  doc.moveDown(1);
}

/**
 * PDF'e senaryoları tablo olarak ekler
 */
function addScenariosTable(doc: PDFKit.PDFDocument, scenarios: any[], includeFields: any) {
  // Tablo başlıklarını oluştur
  const headers = ['Name', 'Folder'];
  if (includeFields.description) headers.push('Description');
  if (includeFields.tags) headers.push('Tags');
  if (includeFields.status) headers.push('Status');
  if (includeFields.lastRun) headers.push('Last Run');
  if (includeFields.duration) headers.push('Duration');
  if (includeFields.steps) headers.push('Steps');
  headers.push('Created At');

  // Sütun genişliklerini hesapla
  const colWidths = calculateColumnWidths(headers, CONTENT_WIDTH);

  // Tablo başlığını çiz
  const tableTop = doc.y;
  const tableLeft = PAGE_MARGIN;

  // Başlık arka planı
  doc.rect(tableLeft, tableTop, CONTENT_WIDTH, 25)
     .fillAndStroke('#e6f0ff', '#b3d1ff');

  // Başlık metinleri
  doc.font('Helvetica-Bold').fontSize(10).fillColor('#1a365d');

  let headerXPos = tableLeft + 5;
  headers.forEach((header, i) => {
    doc.text(header, headerXPos, tableTop + 8, {
      width: colWidths[i] - 10,
      align: 'left'
    });
    headerXPos += colWidths[i];
  });

  // Veri satırlarını ekle
  let rowTop = tableTop + 25;
  let rowColor = false; // Satır renklendirme için değişken

  scenarios.forEach((scenario, index) => {
    // Satır yüksekliğini hesapla
    const rowHeight = calculateRowHeight(scenario, includeFields, colWidths);

    // Sayfa kontrolü
    if (rowTop + rowHeight > doc.page.height - PAGE_MARGIN) {
      doc.addPage();
      rowTop = PAGE_MARGIN;

      // Yeni sayfada başlık satırını tekrar çiz
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, 25)
         .fillAndStroke('#e6f0ff', '#b3d1ff');

      doc.font('Helvetica-Bold').fontSize(10).fillColor('#1a365d');

      let headerXPos = tableLeft + 5;
      headers.forEach((header, i) => {
        doc.text(header, headerXPos, rowTop + 8, {
          width: colWidths[i] - 10,
          align: 'left'
        });
        headerXPos += colWidths[i];
      });

      rowTop += 25;
    }

    // Satır arka planı
    if (rowColor) {
      doc.rect(tableLeft, rowTop, CONTENT_WIDTH, rowHeight)
         .fillAndStroke('#f8fafc', '#edf2f7');
    }
    rowColor = !rowColor;

    // Satır verilerini ekle
    doc.font('Helvetica').fontSize(9).fillColor('#2d3748');

    let cellXPos = tableLeft + 5;
    let colIndex = 0;

    // İsim
    doc.text(scenario.name, cellXPos, rowTop + 5, {
      width: colWidths[colIndex] - 10,
      align: 'left'
    });
    cellXPos += colWidths[colIndex++];

    // Klasör
    doc.text(scenario.folder, cellXPos, rowTop + 5, {
      width: colWidths[colIndex] - 10,
      align: 'left'
    });
    cellXPos += colWidths[colIndex++];

    // Açıklama
    if (includeFields.description) {
      doc.text(scenario.description || '', cellXPos, rowTop + 5, {
        width: colWidths[colIndex] - 10,
        align: 'left'
      });
      cellXPos += colWidths[colIndex++];
    }

    // Etiketler
    if (includeFields.tags) {
      doc.text(scenario.tags || '', cellXPos, rowTop + 5, {
        width: colWidths[colIndex] - 10,
        align: 'left'
      });
      cellXPos += colWidths[colIndex++];
    }

    // Durum
    if (includeFields.status) {
      // Durum için renk belirle
      let statusColor = '#2d3748';
      if (scenario.status === 'active') statusColor = '#38a169';
      if (scenario.status === 'passive') statusColor = '#718096';

      doc.fillColor(statusColor)
         .text(scenario.status || '', cellXPos, rowTop + 5, {
           width: colWidths[colIndex] - 10,
           align: 'left'
         })
         .fillColor('#2d3748'); // Rengi geri al

      cellXPos += colWidths[colIndex++];
    }

    // Son çalıştırma
    if (includeFields.lastRun) {
      doc.text(scenario.lastRun || 'Never', cellXPos, rowTop + 5, {
        width: colWidths[colIndex] - 10,
        align: 'left'
      });
      cellXPos += colWidths[colIndex++];
    }

    // Süre
    if (includeFields.duration) {
      doc.text(scenario.duration || '0s', cellXPos, rowTop + 5, {
        width: colWidths[colIndex] - 10,
        align: 'left'
      });
      cellXPos += colWidths[colIndex++];
    }

    // Adımlar
    if (includeFields.steps) {
      const stepsText = `${scenario.totalSteps || 0} steps (${scenario.completedSteps || 0} completed, ${scenario.failedSteps || 0} failed)`;
      doc.text(stepsText, cellXPos, rowTop + 5, {
        width: colWidths[colIndex] - 10,
        align: 'left'
      });

      // Adım detaylarını ekle
      if (scenario.stepsDetails && scenario.stepsDetails.length > 0) {
        let stepsY = rowTop + 20;

        // Adım başlığı
        doc.font('Helvetica-Bold').fontSize(8)
           .text('Step Details:', cellXPos, stepsY, {
             width: colWidths[colIndex] - 10,
             align: 'left'
           });

        stepsY += 15;

        // Adımları listele
        doc.font('Helvetica').fontSize(8);
        scenario.stepsDetails.forEach((step: any, stepIndex: number) => {
          // Adım adı için gereken yüksekliği hesapla
          const stepNameText = `${step.index}. ${step.name}`;
          const stepNameWidth = colWidths[colIndex] - 15;
          const stepNameHeight = estimateTextHeight(stepNameText, stepNameWidth, 8);

          // Adım adını ekle
          doc.text(stepNameText, cellXPos, stepsY, {
            width: stepNameWidth,
            align: 'left'
          });

          stepsY += stepNameHeight + 2; // 2 piksel ekstra boşluk

          // Adım açıklaması varsa ekle
          if (step.description) {
            const stepDescWidth = colWidths[colIndex] - 25;
            const stepDescHeight = estimateTextHeight(step.description, stepDescWidth, 7);

            doc.font('Helvetica-Oblique').fontSize(7)
               .text(step.description, cellXPos + 10, stepsY, {
                 width: stepDescWidth,
                 align: 'left'
               });

            stepsY += stepDescHeight + 5; // 5 piksel ekstra boşluk
          } else {
            stepsY += 5; // Adım açıklaması yoksa daha az boşluk bırak
          }
        });
      }

      cellXPos += colWidths[colIndex++];
    }

    // Oluşturulma tarihi
    doc.text(
      scenario.createdAt ? new Date(scenario.createdAt).toLocaleDateString() : '',
      cellXPos, rowTop + 5,
      {
        width: colWidths[colIndex] - 10,
        align: 'left'
      }
    );

    // Sonraki satıra geç
    rowTop += rowHeight + ROW_GAP;
  });
}

/**
 * Sütun genişliklerini hesaplar
 */
function calculateColumnWidths(headers: string[], totalWidth: number): number[] {
  const widths: number[] = [];

  // Sütun genişliklerini belirle
  const columnWeights: { [key: string]: number } = {
    'Name': 2,
    'Folder': 1.5,
    'Description': 2,
    'Tags': 1.5,
    'Status': 1,
    'Last Run': 1.5,
    'Duration': 1,
    'Steps': 3,
    'Created At': 1.5
  };

  // Toplam ağırlığı hesapla
  let totalWeight = 0;
  headers.forEach(header => {
    totalWeight += columnWeights[header] || 1;
  });

  // Genişlikleri hesapla
  headers.forEach(header => {
    const weight = columnWeights[header] || 1;
    widths.push((weight / totalWeight) * totalWidth);
  });

  return widths;
}

/**
 * Satır yüksekliğini hesaplar
 */
function calculateRowHeight(scenario: any, includeFields: any, colWidths: number[]): number {
  let maxHeight = 30; // Minimum satır yüksekliği

  // Adımlar varsa ve gösterilecekse, adımların yüksekliğini hesapla
  if (includeFields.steps && scenario.stepsDetails && scenario.stepsDetails.length > 0) {
    // Adım sütununun genişliğini bul
    let stepsColIndex = 0;
    let stepsColWidth = 0;

    // Adımlar sütununun indeksini bul
    if (includeFields.description) stepsColIndex++;
    if (includeFields.tags) stepsColIndex++;
    if (includeFields.status) stepsColIndex++;
    if (includeFields.lastRun) stepsColIndex++;
    if (includeFields.duration) stepsColIndex++;

    // Adımlar sütununun genişliğini al
    stepsColWidth = colWidths[stepsColIndex + 2]; // +2 for Name and Folder columns

    // Her adım için gereken yüksekliği hesapla
    let totalStepsHeight = 25; // Başlık için boşluk

    scenario.stepsDetails.forEach((step: any) => {
      // Adım adı için gereken yüksekliği hesapla
      const stepNameText = `${step.index}. ${step.name}`;
      const stepNameWidth = stepsColWidth - 15;
      const stepNameHeight = estimateTextHeight(stepNameText, stepNameWidth, 8);

      // Adım açıklaması için gereken yüksekliği hesapla
      let stepDescHeight = 0;
      if (step.description) {
        const stepDescWidth = stepsColWidth - 25;
        stepDescHeight = estimateTextHeight(step.description, stepDescWidth, 7);
      }

      // Bu adım için toplam yüksekliği ekle
      totalStepsHeight += stepNameHeight + stepDescHeight + 5; // 5 piksel ekstra boşluk
    });

    maxHeight = Math.max(maxHeight, totalStepsHeight + 10); // 10 piksel ekstra boşluk
  }

  return maxHeight;
}

/**
 * Belirli bir genişlikte metin için gereken yüksekliği tahmin eder
 */
function estimateTextHeight(text: string, width: number, fontSize: number): number {
  // Ortalama karakter genişliği (piksel cinsinden)
  const avgCharWidth = fontSize * 0.6;

  // Bir satırdaki yaklaşık karakter sayısı
  const charsPerLine = Math.floor(width / avgCharWidth);

  // Satır sayısını hesapla
  const lines = Math.ceil(text.length / charsPerLine);

  // Satır yüksekliği (piksel cinsinden)
  const lineHeight = fontSize * 1.2;

  return lines * lineHeight;
}

/**
 * Sayfa numaralarını ekler
 */
function addPageNumbers(doc: PDFKit.PDFDocument) {
  const pageCount = doc.bufferedPageRange().count;

  for (let i = 0; i < pageCount; i++) {
    doc.switchToPage(i);

    doc.fontSize(8)
       .fillColor('#718096')
       .text(
         `Page ${i + 1} of ${pageCount}`,
         PAGE_MARGIN,
         doc.page.height - 20,
         { align: 'center', width: CONTENT_WIDTH }
       );
  }
}
