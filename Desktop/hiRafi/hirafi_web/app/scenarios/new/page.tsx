"use client";

import React, {
  useState,
  useRef,
  useMemo,
  useEffect,
  useCallback,
} from "react";
import { TestManagementLinkDialog } from "@/components/ui/test-management-link-dialog";
import { TestManagementTab } from "@/components/shared/test-management-tab";
import { TestManagementProvider as TestManagementProviderType } from "@/store/testManagementStore";
import {
  UnifiedTestManagementProvider,
  useUnifiedTestManagementContext,
} from "@/contexts/UnifiedTestManagementContext";
import { UnifiedImportPanel } from "@/components/shared/unified-import-panel";
import { UnifiedTestCaseSelector } from "@/components/shared/unified-test-case-selector";

import {
  updateFormDataWithProvider,
  getProviderFormFieldName,
} from "@/lib/utils/test-management-utils";
import { getProviderDisplayName } from "@/lib/utils/unified-test-management-utils";
import { TestTube } from "lucide-react";
import { CreateScenarioLayout } from "@/components/create-scenario/create-scenario-layout";
import { ScenarioDetails } from "@/components/create-scenario/scenario-details";
import { ScenarioSteps } from "@/components/shared/scenario-steps/ScenarioSteps";
import { ScenarioPreview } from "@/components/create-scenario/scenario-preview";
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext";
import { UnifiedScenarioProvider } from "@/contexts/UnifiedScenarioContext";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { DraftStatusDebug } from "@/components/scenario-creation/draft-status";
import { Button } from "@/components/ui/button";
import { CreativityControl } from "@/components/ui/creativity-control";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { TestDataSelector } from "@/components/create-scenario/test-data-selector";
import { VariableAutocomplete } from "@/components/create-scenario/variable-autocomplete";
import { useScenarioTestDataStore } from "@/store/scenarioTestDataStore";
import {
  Sparkles,
  Check,
  AlertCircle,
  LoaderCircle,
  BarChart,
  Network,
  Camera,
  Video,
  ChevronRight,
  Loader2,
  Search,
  X,
  Plus,
  GripVertical,
  MousePointer,
  Clock,
  Eye,
  ArrowRight,
  Trash2,
  ChevronLeft,
  Filter,
  Folder,
  LayoutGrid,
  ListChecks,
  FileText,
  CheckSquare,
  AlertTriangle,
  Download,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/lib/api/auth";
import { scenarioApi, pluginApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { ProtectedRoute } from "@/components/protected-route";
import { useScenarioStoreContext } from "@/hooks/useStoreContext";
import { useScenarioExpiryCleanup } from "@/hooks/useExpiryCleanup";

import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import { TestStep, ActionType } from "@/models/scenario";
// TestRail context removed - using unified test management system

// Validation error display component - removed as per user feedback
// Users prefer not to see validation errors until they try to proceed
// The disabled state of navigation buttons provides sufficient feedback

export default function NewScenarioPage() {
  return (
    <ProtectedRoute>
      <UnifiedTestManagementProvider autoInitialize={true}>
        <UnifiedScenarioProvider options={{ mode: "create" }}>
          <CreateScenarioLayout>
            <ScenarioContent />
          </CreateScenarioLayout>
        </UnifiedScenarioProvider>
      </UnifiedTestManagementProvider>
    </ProtectedRoute>
  );
}

function AIGeneratePanel() {
  const { formData, updateFormField, ui, generateSteps } =
    useUnifiedScenarioContext();
  const isGenerating = ui.isGenerating || false;
  const { toast } = useToast();
  const { user } = useAuth();
  const testStepsRef = useRef<HTMLDivElement>(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [useImportMode, setUseImportMode] = useState(false);
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [creativityLevel, setCreativityLevel] = useState(1); // Default to "Balanced"

  // Test data store'dan variables'ları al
  const { variables: testDataVariables } = useScenarioTestDataStore();

  // Test management integration will be conditionally loaded when import mode is active

  // Local state for selected test case (for AI generation)
  const [selectedTestCase, setSelectedTestCase] = useState<string | null>(null);

  // Track the current provider for the selected test case
  const [currentProvider, setCurrentProvider] =
    useState<TestManagementProviderType | null>(null);

  // Track if form data was populated from import to make cleanup more selective
  const [hasImportedData, setHasImportedData] = useState<boolean>(false);

  // Use refs to store current values and avoid dependency issues
  const updateFormFieldRef = React.useRef(updateFormField);
  const setSelectedTestCaseRef = React.useRef(setSelectedTestCase);
  const setUseImportModeRef = React.useRef(setUseImportMode);
  const setHasImportedDataRef = React.useRef(setHasImportedData);
  const hasImportedDataRef = React.useRef(hasImportedData);

  // Update refs on each render
  React.useEffect(() => {
    updateFormFieldRef.current = updateFormField;
    setSelectedTestCaseRef.current = setSelectedTestCase;
    setUseImportModeRef.current = setUseImportMode;
    setHasImportedDataRef.current = setHasImportedData;
    hasImportedDataRef.current = hasImportedData;
  });

  // Stable cleanup function that uses refs to avoid dependency issues
  const clearFormFieldsFromImport = React.useCallback(() => {
    // Only clear if we actually have imported data to avoid clearing user input
    if (!hasImportedDataRef.current) {
      console.log(
        "[Provider Switch] No imported data to clear, skipping cleanup",
      );
      return;
    }

    console.log(
      "[Provider Switch] Clearing imported form data due to provider switch",
    );

    // Clear form fields that might have been populated from imported test case data
    updateFormFieldRef.current("aiPrompt", "");
    updateFormFieldRef.current("name", "");
    updateFormFieldRef.current("description", "");
    updateFormFieldRef.current("useAIForDetails", false);

    // Clear provider-specific test case selections
    updateFormFieldRef.current("testrailCases", []);
    updateFormFieldRef.current("zephyrscaleCases", []);
    updateFormFieldRef.current("testrailSync", false);
    updateFormFieldRef.current("zephyrscaleSync", false);

    // Clear local import state
    setSelectedTestCaseRef.current(null);
    setUseImportModeRef.current(false);

    // Reset the imported data flag
    setHasImportedDataRef.current(false);
  }, []); // No dependencies - function is stable

  // Enhanced provider switching with comprehensive cleanup (will be used when import mode is active)
  const handleProviderSwitch = (provider: TestManagementProviderType) => {
    // Clear form fields to prevent cross-contamination (only if imported data exists)
    clearFormFieldsFromImport();

    // Note: Provider state clearing will be handled by the TestManagementContext when it's active
  };

  // Handle import mode toggle
  const handleImportToggle = async (enabled: boolean) => {
    setUseImportMode(enabled);
    // Clear any selected test case and provider when toggling
    if (!enabled) {
      setSelectedTestCase(null);
      setCurrentProvider(null);
    }
  };

  // Handle test case selection for AI generation
  const handleTestCaseSelectionLocal = (caseId: string) => {
    setSelectedTestCase(caseId);
    // Note: This function is part of the legacy flow and should be replaced with unified import
    // For now, we don't update form fields here as the provider selection should be handled
    // by the unified import flow instead
  };

  // Unified import handlers with lazy loading optimization
  const handleUnifiedImport = async (
    prompt: string,
    provider: TestManagementProviderType,
    caseDetails: any,
  ) => {
    try {
      // Validate case details before processing
      if (!caseDetails) {
        throw new Error("No test case details received. Please try selecting the test case again.");
      }

      const caseId = caseDetails.id || caseDetails.key;
      if (!caseId) {
        throw new Error("Test case is missing required ID. Please try selecting a different test case.");
      }

      console.log(
        "[Import] Starting unified import with lazy-loaded case details:",
        {
          provider,
          caseId,
          hasPrompt: !!prompt,
        },
      );

      // Update form data with imported content
      updateFormField("aiPrompt", prompt);
      updateFormField(
        "name",
        caseDetails.title || caseDetails.name || "Imported Test Case",
      );
      updateFormField(
        "description",
        caseDetails.description || `Imported from ${provider}`,
      );
      updateFormField("useAIForDetails", true);

      // Update provider-specific fields using utility function
      updateFormDataWithProvider(
        updateFormField as (field: string, value: any) => void,
        provider,
        [caseId],
      );

      // Mark that we have imported data for selective cleanup
      setHasImportedData(true);
      console.log(
        "[Import] Unified import completed, form data populated from",
        provider,
      );

      toast({
        title: "Test Case Imported",
        description: `Successfully imported test case from ${getProviderDisplayName(provider)}`,
      });

      // Auto-generate steps after import
      // Pass the imported data directly to avoid state timing issues
      await handleGenerateStepsWithImportedData(prompt, provider, caseDetails);
    } catch (error: any) {
      console.error("[Import] Unified import failed:", error);
      toast({
        title: "Import Failed",
        description: error.message || "Failed to import test case",
        variant: "destructive",
      });
    }
  };

  const handleUnifiedCaseSelect = (caseId: string) => {
    setSelectedTestCase(caseId);
  };

  // Generate steps with imported data (avoids state timing issues)
  const handleGenerateStepsWithImportedData = async (
    importedPrompt: string,
    provider: TestManagementProviderType,
    caseDetails: any,
  ) => {
    try {
      console.log("[Debug] handleGenerateStepsWithImportedData called with:", {
        importedPrompt: importedPrompt.substring(0, 100) + "...",
        provider,
        caseId: caseDetails.id,
      });

      // Use imported data directly
      const formattedVariables = testDataVariables.map((variable) => ({
        name: variable.name,
        type: variable.type,
        description: variable.description,
      }));

      await generateSteps(
        true, // isTestManagementGeneration
        caseDetails.title || caseDetails.name || "Imported Test Case",
        caseDetails.description || `Imported from ${provider}`,
        formattedVariables,
        creativityLevel,
        provider,
        importedPrompt, // Pass the formatted prompt directly to avoid state timing issues
      );

      // Post-generation actions - set provider and show dialog
      setCurrentProvider(provider);
      setShowLinkDialog(true);
    } catch (error: any) {
      console.error(
        "[Error] Failed to generate steps with imported data:",
        error,
      );
      toast({
        title: "Step Generation Failed",
        description:
          error.message || "Failed to generate steps from imported test case",
        variant: "destructive",
      });
    }
  };

  // Unified generate steps - works for both unified import and manual scenarios
  const handleGenerateSteps = async () => {
    try {
      let prompt = formData.aiPrompt || "";
      let scenarioName = formData.name || "";
      let scenarioDescription = formData.description || "";
      let isTestManagementGeneration = false;

      // Check if we have test management import data
      let testManagementProvider: "testrail" | "zephyrscale" | undefined;

      // Debug logging to understand the current state
      console.log("[Debug] handleGenerateSteps called with:", {
        aiPrompt: formData.aiPrompt,
        useImportMode,
        selectedTestCase,
        testrailCases: formData.testrailCases,
        zephyrscaleCases: formData.zephyrscaleCases,
        hasImportedData,
      });

      // Check if we have test management data (either from unified import or provider-specific fields)
      const hasTestRailCases = formData.testrailCases?.length > 0;
      const hasZephyrScaleCases = formData.zephyrscaleCases?.length > 0;
      const hasTestManagementData = hasTestRailCases || hasZephyrScaleCases;

      if (formData.aiPrompt || hasTestManagementData) {
        // Use existing prompt data (from unified import or manual entry)
        prompt = formData.aiPrompt || "";
        scenarioName = formData.name || "";
        scenarioDescription = formData.description || "";
        isTestManagementGeneration = hasTestManagementData;

        // Determine the provider based on which cases are selected
        if (hasTestRailCases) {
          testManagementProvider = "testrail";
        } else if (hasZephyrScaleCases) {
          testManagementProvider = "zephyrscale";
        }

        console.log("[Debug] Using unified flow with:", {
          prompt: prompt.substring(0, 100) + "...",
          isTestManagementGeneration,
          testManagementProvider,
        });
      } else if (useImportMode && selectedTestCase) {
        // Legacy fallback - this should rarely be used now with unified import
        console.warn(
          "Using legacy import flow - consider using unified import instead",
        );

        // This case should not occur with the new conditional loading approach
        throw new Error("Import mode active but no provider context available");
      }

      // Validate that we have a prompt for generation
      if (!prompt && !isTestManagementGeneration) {
        throw new Error("Please enter a prompt for AI generation");
      }

      // Use unified generation flow
      const formattedVariables = testDataVariables.map((variable) => ({
        name: variable.name,
        type: variable.type,
        description: variable.description,
      }));

      await generateSteps(
        isTestManagementGeneration,
        scenarioName,
        scenarioDescription,
        formattedVariables,
        creativityLevel,
        testManagementProvider,
      );

      // Post-generation actions
      if (isTestManagementGeneration && testManagementProvider) {
        setCurrentProvider(testManagementProvider);
        setShowLinkDialog(true);
      }

      // UI feedback
      setIsCollapsed(true);
      setTimeout(() => {
        if (testStepsRef.current) {
          testStepsRef.current.scrollIntoView({ behavior: "smooth" });
        }
      }, 300);
    } catch (error: any) {
      console.error("Step generation failed:", error);
      toast({
        title: "Adım oluşturma hatası",
        description:
          error.message || "Test adımları oluşturulurken hata oluştu",
        variant: "destructive",
      });
    }
  };

  // Provider-aware link confirmation function
  const handleLinkConfirm = () => {
    if (selectedTestCase && currentProvider) {
      if (currentProvider === "testrail") {
        // TestRail case'ini form verisine ekle
        updateFormField("testrailCases", [selectedTestCase]);
        // TestRail sync'i aktif et
        updateFormField("testrailSync", true);

        toast({
          title: "TestRail Case Linked",
          description: `This scenario is now linked with TestRail case C${selectedTestCase}. TestRail synchronization has been enabled.`,
          variant: "default",
        });
      } else if (currentProvider === "zephyrscale") {
        // Zephyr Scale case'ini form verisine ekle
        updateFormField("zephyrscaleCases", [selectedTestCase]);
        // Zephyr Scale sync'i aktif et
        updateFormField("zephyrscaleSync", true);

        toast({
          title: "Zephyr Scale Case Linked",
          description: `This scenario is now linked with Zephyr Scale case ${selectedTestCase}. Zephyr Scale synchronization has been enabled.`,
          variant: "default",
        });
      }
    }
    setShowLinkDialog(false);
    setCurrentProvider(null);
  };

  return (
    <>
      {/* Test Management Link Confirmation Dialog */}
      <TestManagementLinkDialog
        open={showLinkDialog}
        onOpenChange={setShowLinkDialog}
        caseId={selectedTestCase}
        provider={currentProvider}
        onConfirm={handleLinkConfirm}
      />

      <Card className="overflow-hidden border border-slate-200 dark:border-slate-800 shadow-sm bg-white dark:bg-slate-950">
        <div
          className="px-6 py-4 border-b border-slate-100 dark:border-slate-800 bg-slate-50/50 dark:bg-slate-900/50 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-900/70 transition-colors"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 shadow-sm">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-lg text-slate-900 dark:text-slate-100">
                  Generate Steps with AI
                </h3>
                <p className="text-sm text-slate-600 dark:text-slate-400 mt-0.5">
                  Let AI create test steps based on your description and test
                  data
                </p>
              </div>
            </div>
            <ChevronRight
              className={`h-5 w-5 text-slate-400 transition-transform duration-200 ${isCollapsed ? "" : "rotate-90"}`}
            />
          </div>
        </div>

        {!isCollapsed && (
          <div className="p-6 space-y-6">
            {/* Test Data Selector */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm text-slate-900 dark:text-slate-100">
                  Test Data Set
                </h4>
                <div className="flex items-center gap-3">
                  <Badge variant="secondary" className="text-xs">
                    Optional
                  </Badge>

                  {/* Import Toggle - Unified */}
                  <div className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
                    <Download className="h-4 w-4 text-blue-500" />
                    <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                      Import Test Cases
                    </span>
                    <Switch
                      checked={useImportMode}
                      onCheckedChange={handleImportToggle}
                      className="data-[state=checked]:bg-blue-600 scale-75"
                    />
                  </div>
                </div>
              </div>
              <TestDataSelector
                className={
                  useImportMode ? "opacity-50 pointer-events-none" : ""
                }
              />
            </div>

            {useImportMode ? (
              <ImportModeContent
                onCaseSelect={handleUnifiedCaseSelect}
                onImportComplete={handleUnifiedImport}
                selectedCaseId={selectedTestCase}
                isGenerating={isGenerating}
              />
            ) : (
              <div className="space-y-3">
                <Label
                  htmlFor="ai-prompt"
                  className="text-sm font-medium text-slate-900 dark:text-slate-100"
                >
                  Describe Your Test Scenario
                </Label>
                <VariableAutocomplete
                  value={formData.aiPrompt || ""}
                  onChange={(value) => {
                    // Prompt değiştiğinde, adımları temizleme işlemini burada yapmıyoruz
                    // Çünkü kullanıcı henüz generate butonuna basmamış olabilir
                    // Sadece prompt'u güncelliyoruz
                    updateFormField("aiPrompt", value);

                    // Eğer prompt alanı boşsa ve useAIForDetails true ise, name ve description alanlarını da temizle
                    // Bu, kullanıcı prompt'u sildiğinde name ve description alanlarının da temizlenmesini sağlar
                    if (value.trim() === "" && formData.useAIForDetails) {
                      updateFormField("name", "");
                      updateFormField("description", "");
                    }
                  }}
                  placeholder="Describe what you want to test. Example: Go to google.com, search for '@userName', and verify search results appear. Use @ to reference test data variables."
                  className={`min-h-[180px] max-h-[400px] border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-950 focus:border-blue-500 focus:ring-blue-500 resize-none overflow-auto ${useImportMode ? "opacity-50" : ""}`}
                  rows={8}
                  disabled={useImportMode}
                  maxLength={2000}
                  showCharacterCount={true}
                />
                <div className="text-xs text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-900/50 p-3 rounded-md">
                  <p className="flex items-center gap-2 mb-2">
                    <span className="font-medium">💡 Tip:</span>
                    {useImportMode
                      ? "Import mode is active. Test data variables and manual prompt editing are disabled."
                      : "Type @ followed by a variable name to use test data in your scenario. Select a test data set above to see available variables."}
                  </p>
                  {!useImportMode && (
                    <p className="flex items-center gap-2 text-slate-400 dark:text-slate-500">
                      <span className="font-medium">📝 Note:</span>
                      Maximum 2000 characters allowed. You can resize the text
                      area vertically if needed.
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Creativity Control */}
            <div className="space-y-3">
              <CreativityControl
                value={creativityLevel}
                onChange={setCreativityLevel}
                disabled={isGenerating}
                compact={true}
              />
            </div>

            {/* Only show Generate Test Steps button when not in import mode, or when in import mode but no case is selected yet */}
            {(!useImportMode || (useImportMode && !selectedTestCase)) && (
              <Button
                onClick={handleGenerateSteps}
                disabled={
                  isGenerating ||
                  (useImportMode
                    ? !selectedTestCase
                    : !formData.aiPrompt?.trim())
                }
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-sm h-11 font-medium"
              >
                {isGenerating ? (
                  <>
                    <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                    Generating Steps...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Test Steps
                  </>
                )}
              </Button>
            )}

            <div className="text-center text-xs text-slate-600 dark:text-slate-400 bg-slate-50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
              <p className="flex items-center justify-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
                AI will analyze your{" "}
                {useImportMode ? "imported test case" : "description"} and
                create a sequence of automated test steps
              </p>
            </div>
          </div>
        )}
      </Card>
    </>
  );
}

function ScenarioContent() {
  // Store context management for company changes
  useScenarioStoreContext();

  // Automatic expiry cleanup for scenario creation drafts (1 hour)
  useScenarioExpiryCleanup({
    showNotifications: true,
    customExpiryTime: 60, // 60 minutes
  });

  const {
    ui,
    formData,
    updateFormField,
    setCurrentStep,
    handleTestManagementSelection,
    addTag,
    removeTag,
    clearDraft,
  } = useUnifiedScenarioContext();
  const currentStep = ui.currentStep || 0;
  const [isStepLoading, setIsStepLoading] = useState(false);
  const testStepsRef = useRef<HTMLDivElement>(null);

  // Optimized: Handle step loading with direct state management
  const handleStepChange = (newStep: number) => {
    setIsStepLoading(true);
    setTimeout(() => setIsStepLoading(false), 500);
  };

  if (isStepLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh]">
        <div className="relative w-16 h-16">
          <div className="w-16 h-16 rounded-full border-4 border-indigo-200 border-t-indigo-600 animate-spin"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 rounded-full bg-white dark:bg-gray-900"></div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <LoaderCircle className="w-5 h-5 text-indigo-600 animate-pulse" />
          </div>
        </div>
        <p className="mt-4 text-indigo-600 dark:text-indigo-400 font-medium">
          Loading{" "}
          {currentStep === 0
            ? "Scenario Details"
            : currentStep === 1
              ? "Test Steps"
              : currentStep === 2
                ? "TestRail Integration"
                : currentStep === 3
                  ? "Environment & Reports"
                  : "Preview"}
          ...
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Step 1: Sadece Scenario Details */}
      {currentStep === 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Sol kolon: Scenario Details */}
          <div className="lg:col-span-2">
            <ScenarioDetails />
          </div>

          {/* Sağ kolon: Tips and Debug */}
          <div className="space-y-6">
            {/* Debug Panel - Only show in development */}
            {process.env.NODE_ENV === "development" && (
              <DraftStatusDebug onClearDraft={clearDraft} />
            )}

            <Card className="overflow-hidden border-0 shadow-md p-5">
              <h3 className="font-semibold text-lg mb-4 flex items-center text-indigo-700 dark:text-indigo-300">
                <AlertCircle className="h-5 w-5 mr-2" />
                How to Create a Scenario
              </h3>
              <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 rounded-full bg-emerald-100 p-1 dark:bg-emerald-900/30">
                    <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <span>
                    Give your scenario a descriptive name and description
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 rounded-full bg-emerald-100 p-1 dark:bg-emerald-900/30">
                    <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <span>Add relevant tags to make scenarios searchable</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="mt-0.5 rounded-full bg-emerald-100 p-1 dark:bg-emerald-900/30">
                    <Check className="h-3 w-3 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <span>
                    In the next step, you'll define test steps to execute
                  </span>
                </li>
              </ul>
            </Card>
          </div>
        </div>
      )}

      {/* Step 2: Test Steps - Vertical layout with AI Generator on top */}
      {currentStep === 1 && (
        <div className="space-y-6">
          {/* AI Generator Panel first */}
          <AIGeneratePanel />

          {/* Test Steps below */}
          <div ref={testStepsRef}>
            <ScenarioSteps
              steps={formData.steps}
              onStepsChange={(newSteps) => {
                updateFormField("steps", newSteps);
              }}
            />
          </div>
        </div>
      )}

      {/* Step 3: Test Management Integration */}
      {currentStep === 2 && <TestManagementIntegration />}

      {/* Step 4: Preview */}
      {currentStep === 3 && (
        <div className="space-y-6">
          <ScenarioPreview />
        </div>
      )}
    </div>
  );
}

// Import Mode Content - Only rendered when import toggle is active
function ImportModeContent({
  onCaseSelect,
  onImportComplete,
  selectedCaseId,
  isGenerating,
}: {
  onCaseSelect: (caseId: string) => void;
  onImportComplete: (
    prompt: string,
    provider: TestManagementProviderType,
    caseDetails: any,
  ) => void;
  selectedCaseId: string | null;
  isGenerating: boolean;
}) {
  const { store } = useUnifiedTestManagementContext();
  const { availableProviders } = store;

  // Show loading state while providers are being determined
  if (availableProviders.length === 0) {
    return (
      <div className="space-y-6">
        <Card className="border-dashed border-gray-300 dark:border-gray-600">
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <div className="rounded-full bg-gray-100 dark:bg-gray-800 p-4 mb-4">
              <TestTube className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No Test Management Integration
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
              Configure TestRail or Zephyr Scale to import test cases for AI
              step generation.
            </p>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => (window.location.href = "/plugins/testrail")}
              >
                Configure TestRail
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => (window.location.href = "/plugins/zephyrscale")}
              >
                Configure Zephyr Scale
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Unified Test Case Selector with Import */}
      <UnifiedTestCaseSelector
        onCaseSelect={onCaseSelect}
        onImportComplete={onImportComplete}
        selectedCaseId={selectedCaseId}
        showImportMode={true}
      />

      {/* Import Status Panel - Only shows when there's an active import */}
      <UnifiedImportPanel
        onImportComplete={onImportComplete}
        disabled={isGenerating}
      />
    </div>
  );
}

// Test Management integration component (supports multiple providers)
function TestManagementIntegration() {
  const { formData, handleTestManagementSelection } =
    useUnifiedScenarioContext();
  const { toast } = useToast();

  return (
    <div className="space-y-6">
      <TestManagementTab
        scenarioData={formData}
        handleTestCaseSelection={handleTestManagementSelection}
      />
    </div>
  );
}

// Legacy TestRail integration component removed - using unified test management system
