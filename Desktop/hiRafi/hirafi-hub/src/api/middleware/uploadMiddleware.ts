/**
 * Upload Middleware
 * Handles file uploads using multer
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger.js';

/**
 * Middleware to handle file uploads for SauceLabs apps
 * @param req Express request
 * @param res Express response
 * @param next Next function
 */
export const handleSauceLabsAppUpload = (req: Request, res: Response, next: NextFunction) => {
  const upload = req.app.locals.upload;
  
  if (!upload) {
    logger.error('Multer upload middleware not found in app.locals');
    return res.status(500).json({
      success: false,
      error: 'File upload middleware not configured'
    });
  }

  // Use m<PERSON>'s single file upload with field name 'app'
  const uploadMiddleware = upload.single('app');

  uploadMiddleware(req, res, (err: any) => {
    if (err) {
      logger.error(`File upload error: ${err.message}`);
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }
    
    // Continue to the next middleware or route handler
    next();
  });
};
