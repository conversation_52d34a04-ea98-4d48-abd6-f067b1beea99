# API Structure

This directory contains all the API modules for the web application. All API calls should be made through these modules to ensure consistency and maintainability.

## Directory Structure

- `constants.ts` - Contains all API endpoints as constants
- `fetch-wrapper.ts` - Provides wrapper functions for making API requests
- `index.ts` - Exports all API modules
- `auth-api.ts` - Authentication related API calls
- `admin-auth-api.ts` - Admin authentication related API calls
- `admin-api.ts` - Admin panel related API calls
- `dashboard-api.ts` - Dashboard related API calls
- `scenario-api.ts` - Scenario related API calls
- `run-api.ts` - Run related API calls
- `report-api.ts` - Report related API calls
- `settings-api.ts` - Settings related API calls
- `plugin-api.ts` - Plugin related API calls
- `team-api.ts` - Team related API calls
- `user-api.ts` - User related API calls
- `feedback-api.ts` - Feedback related API calls
- `vnc-api.ts` - VNC related API calls
- `test-hub-api.ts` - Test Hub related API calls
- `generate-steps-api.ts` - AI step generation related API calls

## Usage

Import the API modules from the index file:

```typescript
import { scenarioApi, runApi, reportApi } from '@web_app/lib/api';

// Use the API modules
const scenarios = await scenarioApi.getScenarios();
const run = await runApi.getRunById(runId);
const report = await reportApi.getReportById(reportId);
```

## Guidelines

1. All API calls should be made through these modules
2. Do not use direct fetch calls in components, pages, or hooks
3. Use the constants from `constants.ts` for API endpoints
4. Use the wrapper functions from `fetch-wrapper.ts` for making API requests
5. Add new API modules for new features
6. Update existing API modules when adding new endpoints
7. Document all API functions with JSDoc comments
