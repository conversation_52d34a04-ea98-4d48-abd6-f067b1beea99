/**
 * Connector Service Registry
 * 
 * Provides service registration and discovery for the connector module.
 * This replaces the singleton pattern with a proper service registry approach.
 */

import { logger } from '../../../utils/logger.js';
import { DependencyContainer } from '../../common/di/dependency-container.js';

// Import interfaces
import type {
  IConnectorManager,
  IHeartbeatService,
  IRegistrationService,
  IConnectorServiceRegistry,
  IServiceRegistryConfig
} from '../interfaces/index.js';

import { ServiceNames } from '../types/connector-types.js';

/**
 * Connector Service Registry
 * 
 * Manages service registration and provides a centralized way to access
 * all connector services. Replaces singleton patterns with proper
 * dependency injection and service discovery.
 */
export class ConnectorServiceRegistry implements IConnectorServiceRegistry {
  private container: DependencyContainer;
  private config: IServiceRegistryConfig;
  private initialized = false;

  constructor(config: IServiceRegistryConfig) {
    this.config = config;
    this.container = new DependencyContainer({
      enableLogging: config.enableLogging || false,
      enableCircularDependencyDetection: config.enableCircularDependencyDetection !== false,
      maxResolutionDepth: 15
    });

    if (config.enableLogging) {
      logger.debug('ConnectorServiceRegistry: Initialized with config', config);
    }
  }

  /**
   * Initialize the service registry
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      logger.debug('ConnectorServiceRegistry: Already initialized, skipping');
      return;
    }

    logger.debug('ConnectorServiceRegistry: Initializing service registry');

    // Register core services
    await this.registerCoreServices();

    this.initialized = true;
    logger.debug('ConnectorServiceRegistry: Service registry initialized');
  }

  /**
   * Register core services with the container
   */
  private async registerCoreServices(): Promise<void> {
    // Register basic dependencies first (no dependencies)
    this.container.registerSingleton(
      ServiceNames.LOGGER,
      () => logger
    );

    // Register config if provided
    if (this.config) {
      this.container.registerSingleton(
        ServiceNames.CONFIG,
        () => this.config
      );
    }

    // Heartbeat Service (depends on logger and config)
    this.container.registerSingleton(
      ServiceNames.HEARTBEAT_SERVICE,
      async (logger, config) => {
        const { HeartbeatService } = await import('../services/heartbeat/heartbeat-service.js');
        return new HeartbeatService(logger, config);
      },
      [ServiceNames.LOGGER, ServiceNames.CONFIG]
    );

    // Registration Service (depends on logger and config)
    this.container.registerSingleton(
      ServiceNames.REGISTRATION_SERVICE,
      async (logger, config) => {
        const { RegistrationService } = await import('../services/registration/registration-service.js');
        return new RegistrationService(logger, config);
      },
      [ServiceNames.LOGGER, ServiceNames.CONFIG]
    );

    // HTTP Services
    await this.registerHttpServices();

    // WebSocket Services
    await this.registerWebSocketServices();

    // Connectors
    await this.registerConnectors();

    // Connector Manager (depends on all other services)
    this.container.registerSingleton(
      ServiceNames.CONNECTOR_MANAGER,
      async (logger, config, heartbeatService, registrationService) => {
        const { ConnectorManager } = await import('../core/connector-manager.js');
        return new ConnectorManager(
          logger,
          config,
          heartbeatService,
          registrationService
        );
      },
      [
        ServiceNames.LOGGER,
        ServiceNames.CONFIG,
        ServiceNames.HEARTBEAT_SERVICE,
        ServiceNames.REGISTRATION_SERVICE
      ]
    );

    logger.debug('ConnectorServiceRegistry: Core services registered');
  }

  /**
   * Register HTTP-specific services
   */
  private async registerHttpServices(): Promise<void> {
    // HTTP Registration Service
    this.container.registerSingleton(
      ServiceNames.HTTP_REGISTRATION_SERVICE,
      async (logger, config, registrationService) => {
        const { HttpRegistrationService } = await import('../services/http/http-registration-service.js');
        return new HttpRegistrationService(logger, config, registrationService);
      },
      [ServiceNames.LOGGER, ServiceNames.CONFIG, ServiceNames.REGISTRATION_SERVICE]
    );

    // HTTP Heartbeat Service
    this.container.registerSingleton(
      ServiceNames.HTTP_HEARTBEAT_SERVICE,
      async (logger, config, heartbeatService) => {
        const { HttpHeartbeatService } = await import('../services/http/http-heartbeat-service.js');
        return new HttpHeartbeatService(logger, config, heartbeatService);
      },
      [ServiceNames.LOGGER, ServiceNames.CONFIG, ServiceNames.HEARTBEAT_SERVICE]
    );

    // HTTP Test Service
    this.container.registerSingleton(
      ServiceNames.HTTP_TEST_SERVICE,
      async (logger, config) => {
        const { HttpTestService } = await import('../services/http/http-test-service.js');
        return new HttpTestService(logger, config);
      },
      [ServiceNames.LOGGER, ServiceNames.CONFIG]
    );
  }

  /**
   * Register WebSocket-specific services
   */
  private async registerWebSocketServices(): Promise<void> {
    // WebSocket Connection Manager
    this.container.registerSingleton(
      ServiceNames.WEBSOCKET_CONNECTION_MANAGER,
      async (logger, config) => {
        const { WebSocketConnectionManager } = await import('../services/websocket/websocket-connection-manager.js');
        return new WebSocketConnectionManager(logger, config);
      },
      [ServiceNames.LOGGER, ServiceNames.CONFIG]
    );

    // WebSocket Message Handler
    this.container.registerSingleton(
      ServiceNames.WEBSOCKET_MESSAGE_HANDLER,
      async (logger, config) => {
        const { WebSocketMessageHandler } = await import('../services/websocket/websocket-message-handler.js');
        return new WebSocketMessageHandler(logger, config);
      },
      [ServiceNames.LOGGER, ServiceNames.CONFIG]
    );
  }

  /**
   * Register connector implementations
   */
  private async registerConnectors(): Promise<void> {
    // HTTP Connector
    this.container.registerSingleton(
      ServiceNames.HTTP_CONNECTOR,
      async (logger, config, httpRegistrationService, httpHeartbeatService, httpTestService) => {
        const { HttpConnector } = await import('../http/http-connector.js');
        return new HttpConnector(
          logger,
          config,
          httpRegistrationService,
          httpHeartbeatService,
          httpTestService
        );
      },
      [
        ServiceNames.LOGGER,
        ServiceNames.CONFIG,
        ServiceNames.HTTP_REGISTRATION_SERVICE,
        ServiceNames.HTTP_HEARTBEAT_SERVICE,
        ServiceNames.HTTP_TEST_SERVICE
      ]
    );

    // WebSocket Connector
    this.container.registerSingleton(
      ServiceNames.WEBSOCKET_CONNECTOR,
      async (logger, config, connectionManager, messageHandler, registrationService, heartbeatService) => {
        const { WebSocketConnector } = await import('../websocket/websocket-connector.js');
        return new WebSocketConnector(
          logger,
          config,
          connectionManager,
          messageHandler,
          registrationService,
          heartbeatService
        );
      },
      [
        ServiceNames.LOGGER,
        ServiceNames.CONFIG,
        ServiceNames.WEBSOCKET_CONNECTION_MANAGER,
        ServiceNames.WEBSOCKET_MESSAGE_HANDLER,
        ServiceNames.REGISTRATION_SERVICE,
        ServiceNames.HEARTBEAT_SERVICE
      ]
    );
  }

  /**
   * Register an external service
   */
  registerExternalService<T>(name: string, instance: T): void {
    this.container.registerSingleton(name, () => instance);

    if (this.config.enableLogging) {
      logger.debug(`ConnectorServiceRegistry: Registered external service '${name}'`);
    }
  }

  /**
   * Get the connector manager
   */
  async getConnectorManager(): Promise<IConnectorManager> {
    return this.container.resolve<IConnectorManager>(ServiceNames.CONNECTOR_MANAGER);
  }

  /**
   * Get the heartbeat service
   */
  async getHeartbeatService(): Promise<IHeartbeatService> {
    return this.container.resolve<IHeartbeatService>(ServiceNames.HEARTBEAT_SERVICE);
  }

  /**
   * Get the registration service
   */
  async getRegistrationService(): Promise<IRegistrationService> {
    return this.container.resolve<IRegistrationService>(ServiceNames.REGISTRATION_SERVICE);
  }

  /**
   * Dispose all services
   */
  async dispose(): Promise<void> {
    logger.debug('ConnectorServiceRegistry: Disposing all services');

    try {
      // Get and stop the connector manager first
      if (this.initialized) {
        const connectorManager = await this.getConnectorManager();
        await connectorManager.dispose();
      }
    } catch (error: any) {
      logger.warn(`ConnectorServiceRegistry: Error stopping connector manager during disposal: ${error.message}`);
    }

    this.initialized = false;
    logger.debug('ConnectorServiceRegistry: All services disposed');
  }

  /**
   * Check if registry is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}
