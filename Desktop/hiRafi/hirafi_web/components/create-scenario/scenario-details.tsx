"use client"

import { useState, useEffect, useCallback } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { X, FolderPlus, Folder, Check, Loader2, Sparkles } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog"
import { ColorPickerButton } from "@/components/ui/color-picker-button"
import { useUnifiedScenarioContext } from "@/contexts/UnifiedScenarioContext"
import { useFolders } from "@/hooks"
import { toast } from "sonner"
import {
  ChevronDown,
  ChevronRight,
  FolderOpen,
  Plus,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ScrollArea } from "@/components/ui/scroll-area"
import { LoaderCircle } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { PlatformSelection } from "./platform-selection"

// Popular tags
const popularTags = [
  "smoke",
  "regression",
  "api",
  "ui",
  "performance",
  "security",
  "integration",
  "functional",
  "acceptance",
  "e2e"
]

export function ScenarioDetails() {
  const {
    formData,
    updateFormField,
    addTag,
    removeTag,
    folder,
    setNewFolderOpen,
    setNewFolderName,
    setNewFolderColor,
    setIsCreatingFolder
  } = useUnifiedScenarioContext()

  // Extract folder state
  const { newFolderOpen, newFolderName, newFolderColor, isCreatingFolder } = folder

  // AI toggle is managed through formData.useAIForDetails
  const handleAIToggleChange = (checked: boolean) => {
    updateFormField("useAIForDetails", checked);
  };

  // Fetch folders and actions using unified hook
  const {
    folders,
    isLoading: foldersLoading,
    error: foldersError,
    refresh: refreshFolders,
    createFolder
  } = useFolders({
    autoFetch: true // Safe to auto-fetch since this doesn't subscribe to scenario store
  })

  // Folder color utility
  const getFolderColorClass = (color?: string) => {
    // HEX renk kodu ise doğrudan stil objesi döndür
    if (color?.startsWith('#')) {
      return color;
    }

    // Önceden tanımlı renk isimleri için CSS sınıfını döndür
    switch (color) {
      case "red":
      case "rose":
        return "text-rose-500"
      case "green":
      case "emerald":
        return "text-emerald-500"
      case "blue":
      case "sky":
        return "text-sky-500"
      case "purple":
      case "violet":
        return "text-violet-500"
      case "yellow":
      case "amber":
        return "text-amber-500"
      case "orange":
        return "text-orange-500"
      case "pink":
        return "text-pink-500"
      case "indigo":
        return "text-indigo-500"
      case "teal":
        return "text-teal-500"
      default:
        return "text-gray-500"
    }
  }

  // Create new folder
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    setIsCreatingFolder(true);

    try {
      const result = await createFolder({
        name: newFolderName.trim(),
        color: newFolderColor
      });

      if (result?.success) {
        setNewFolderOpen(false);
        setNewFolderName("");

        // Refresh folders list to show the new folder
        refreshFolders();

        // Yeni oluşturulan klasörü seç
        if (result.folder?.id) {
          updateFormField("folderId", result.folder.id);
        }
      }
    } catch (error) {
      console.error("Error creating folder:", error);
    } finally {
      setIsCreatingFolder(false);
    }
  }

  return (
    <Card className="overflow-hidden border-0 shadow-md">
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
        <CardTitle>Scenario Details</CardTitle>
        <CardDescription>Define the basic information about your test scenario</CardDescription>
      </CardHeader>
      <CardContent className="p-6 space-y-5">
        {/* AI Details Toggle - Moved to the top */}
        <div className="p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-100 dark:border-indigo-800/30 flex justify-between items-center">
          <div className="space-y-1.5">
            <h3 className="font-medium flex items-center text-indigo-700 dark:text-indigo-300">
              <Sparkles className="h-4 w-4 mr-2 text-amber-500" />
              AI-Powered Details
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Let AI generate the scenario name and description for you automatically when you use the AI generation in the next step.
            </p>
          </div>
          <Switch
            id="ai-details-toggle"
            checked={formData.useAIForDetails}
            onCheckedChange={handleAIToggleChange}
            className="data-[state=checked]:bg-indigo-600"
          />
        </div>

        {/* Platform Selection */}
        <PlatformSelection />

        <div className="space-y-2">
          <Label htmlFor="name" className="text-base">
            Scenario Name
          </Label>
          <Input
            id="name"
            placeholder={formData.useAIForDetails ? "Will be generated from AI" : "Enter scenario name"}
            value={formData.name}
            onChange={(e) => updateFormField("name", e.target.value)}
            className={`h-10 ${formData.useAIForDetails ? 'bg-gray-50 dark:bg-gray-800/50' : ''}`}
            disabled={formData.useAIForDetails}
          />
          {formData.useAIForDetails && (
            <p className="text-xs text-indigo-600 dark:text-indigo-400 mt-1">
              {formData.name && formData.name.trim() !== "" ?
                "Name auto-filled from AI or TestRail" :
                "Name will be generated automatically when you use AI in the next step"}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="description" className="text-base">
            Description
          </Label>
          <Textarea
            id="description"
            placeholder={formData.useAIForDetails ? "Will be generated from AI" : "Describe what this scenario tests"}
            value={formData.description}
            onChange={(e) => updateFormField("description", e.target.value)}
            className={`min-h-[100px] resize-none ${formData.useAIForDetails ? 'bg-gray-50 dark:bg-gray-800/50' : ''}`}
            disabled={formData.useAIForDetails}
          />
          {formData.useAIForDetails && (
            <p className="text-xs text-indigo-600 dark:text-indigo-400 mt-1">
              {formData.description && formData.description.trim() !== "" ?
                "Description auto-filled from AI or TestRail" :
                "Description will be generated automatically when you use AI in the next step"}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="folder" className="text-base">
            Folder
          </Label>
          <div className="flex gap-2">
            <div className="flex-1">
              <Select
                value={formData.folderId}
                onValueChange={(value) => updateFormField("folderId", value)}
              >
                <SelectTrigger id="folder">
                  <SelectValue placeholder="Select a folder" />
                </SelectTrigger>
                <SelectContent>
                  {foldersLoading ? (
                    <div className="flex items-center justify-center py-2">
                      <Loader2 className="h-4 w-4 animate-spin text-indigo-500 mr-2" />
                      <span className="text-sm">Loading folders...</span>
                    </div>
                  ) : folders && folders.length > 0 ? (
                    folders.map((folder) => (
                      <SelectItem key={folder.id} value={folder.id}>
                        <div className="flex items-center gap-2">
                          {folder.color?.startsWith('#') ? (
                            <Folder
                              className="h-4 w-4"
                              style={{ color: folder.color }}
                            />
                          ) : (
                            <Folder className={`h-4 w-4 ${getFolderColorClass(folder.color)}`} />
                          )}
                          <span>{folder.name}</span>
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-folders" disabled>
                      No folders available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <Button
              variant="outline"
              className="flex items-center"
              onClick={() => setNewFolderOpen(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              New Folder
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="tags" className="text-base">
            Tags
          </Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.tags.map((tag) => (
              <Badge
                key={tag}
                className="flex items-center gap-1 bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300 hover:bg-indigo-200 px-3 py-1 text-sm"
              >
                {tag}
                <X className="h-3 w-3 cursor-pointer ml-1" onClick={() => removeTag(tag)} />
              </Badge>
            ))}
            {formData.tags.length === 0 && (
              <span className="text-sm text-gray-500 dark:text-gray-400">No tags added yet</span>
            )}
          </div>
          <div className="flex gap-2">
            <Input
              id="tags"
              placeholder="Add a tag"
              value={formData.newTag}
              onChange={(e) => updateFormField("newTag", e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault()
                  addTag()
                }
              }}
              className="h-10"
            />
            <Button type="button" onClick={addTag} variant="outline" className="px-4">
              Add
            </Button>
          </div>

          {/* Popüler tagler */}
          <div className="mt-2">
            <Label className="text-xs text-gray-500 dark:text-gray-400 mt-1 block">
              Suggested tags:
            </Label>
            <div className="flex flex-wrap gap-1 mt-1">
              {["Login", "Checkout", "Search", "UI", "API", "E2E", "Regression", "Smoke"].map((tag) => (
                <Badge
                  key={tag}
                  className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer px-2 py-0.5 text-xs"
                  onClick={() => {
                    if (!formData.tags.includes(tag)) {
                      updateFormField("tags", [...formData.tags, tag])
                    }
                  }}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>


      </CardContent>

      {/* Yeni klasör oluşturma dialog */}
      <Dialog open={newFolderOpen} onOpenChange={setNewFolderOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Create a new folder to organize your test scenarios
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-folder-name" className="text-sm font-medium">
                Folder Name
              </Label>
              <Input
                id="new-folder-name"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="Enter folder name"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">
                Folder Color
              </Label>
              <div className="grid grid-cols-5 gap-3">
                {[
                  { value: "blue", hex: "#3b82f6" },
                  { value: "green", hex: "#10b981" },
                  { value: "red", hex: "#f43f5e" },
                  { value: "purple", hex: "#8b5cf6" },
                  { value: "yellow", hex: "#f59e0b" },
                  { value: "orange", hex: "#f97316" },
                  { value: "pink", hex: "#ec4899" },
                  { value: "indigo", hex: "#6366f1" },
                  { value: "teal", hex: "#14b8a6" },
                  { value: "gray", hex: "#6b7280" }
                ].map(color => (
                  <button
                    key={color.value}
                    type="button"
                    onClick={() => setNewFolderColor(color.value)}
                    className={`
                      w-full aspect-square rounded-full border-2 relative
                      ${newFolderColor === color.value
                        ? 'border-white ring-2 ring-offset-2 ring-black dark:ring-white'
                        : 'border-transparent hover:border-white hover:scale-110'
                      }
                      transition-all duration-200
                    `}
                    title={color.value.charAt(0).toUpperCase() + color.value.slice(1)}
                    style={{ backgroundColor: color.hex }}
                  >
                    {newFolderColor === color.value && (
                      <span className="absolute inset-0 flex items-center justify-center text-white">
                        <Check className="h-4 w-4 stroke-[3]" />
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>

            <div className="mt-4">
              <Label className="text-sm font-medium mb-2 block">
                Custom Color
              </Label>
              <div className="flex gap-3 items-center">
                <ColorPickerButton
                  currentColor={newFolderColor.startsWith('#') ? newFolderColor : '#3b82f6'}
                  onColorChange={setNewFolderColor}
                />
                <div className="flex-1">
                  <Input
                    type="text"
                    placeholder="#RRGGBB"
                    value={newFolderColor.startsWith('#') ? newFolderColor : ''}
                    onChange={(e) => {
                      const value = e.target.value
                      // Geçerli bir HEX renk kodu girildiğinde veya # ile başladığında update et
                      if (/^#([0-9A-F]{3}){1,2}$/i.test(value) || value === '#' || value === '') {
                        setNewFolderColor(value || '#3b82f6')
                      }
                    }}
                    className="font-mono"
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setNewFolderOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleCreateFolder}
              disabled={!newFolderName.trim() || isCreatingFolder}
            >
              {isCreatingFolder ? (
                <>
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>Create Folder</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}