/**
 * Result Queue Worker
 * Processes test results from the result queue asynchronously
 */

import { Worker } from 'bullmq';
import { EventEmitter } from 'events';
import { logger } from '../../utils/logger.js';
import { centralResultProcessor } from './centralResultProcessor.js';
import { QUEUE_NAMES } from '../redis/queueService.js';

import { config } from '../../config/index.js';

export interface ResultQueueWorkerEvents {
  'result:processed': (testId: string, status: string, source: string) => void;
  'result:failed': (testId: string, error: Error) => void;
  'worker:error': (error: Error) => void;
}

export class ResultQueueWorker extends EventEmitter {
  private static instance: ResultQueueWorker;
  private worker: Worker | null = null;
  private isInitialized: boolean = false;
  private processingStats = {
    processed: 0,
    failed: 0,
    lastProcessed: null as Date | null,
    startTime: new Date()
  };

  private constructor() {
    super();
  }

  public static getInstance(): ResultQueueWorker {
    if (!ResultQueueWorker.instance) {
      ResultQueueWorker.instance = new ResultQueueWorker();
    }
    return ResultQueueWorker.instance;
  }

  /**
   * Initialize the result queue worker
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Create a worker for the result queue
      this.worker = new Worker(
        QUEUE_NAMES.RESULT_QUEUE,
        async (job) => {
          try {
            const resultData = job.data;
            const testId = resultData.testId;

            if (!testId) {
              logger.warn(`ResultQueueWorker: Received result without testId, job ID: ${job.id}`);
              return { success: false, error: 'Missing testId' };
            }

            // Daha detaylı loglama
            const status = resultData.status || 'unknown';
            const source = resultData.source || 'unknown';
            const runId = resultData.runId || 'unknown';
            const executionId = resultData.executionId || 'unknown';
            const scenarioId = resultData.scenarioId || 'unknown';
            const executedUser = resultData.executedUser || 'unknown';
            const executedUserName = resultData.executedUserName || 'unknown';

            // Eğer resultData içinde bir transactionId varsa onu kullan, yoksa job.id'yi kullan.
            // Bu, aynı işin yeniden denemelerinde transactionId'nin sabit kalmasını sağlar.
            const transactionId = resultData.transactionId || job.id;

            logger.info(`ResultQueueWorker: Processing result for test ${testId} from queue. Status: ${status}, Source: ${source}, RunId: ${runId}, ExecutionId: ${executionId}, ScenarioId: ${scenarioId}, TransactionId: ${transactionId}`);

            // Log additional fields for debugging
            logger.info(`ResultQueueWorker: Result data details - ExecutedUser: ${executedUser}, ExecutedUserName: ${executedUserName}`);

            // Validate required fields for run report updates
            if (!executionId || executionId === 'unknown') {
              logger.warn(`ResultQueueWorker: Missing executionId for test ${testId}, which is required for run report updates`);
            }

            if (!executedUserName || executedUserName === 'unknown') {
              logger.warn(`ResultQueueWorker: Missing executedUserName for test ${testId}, which is required for run report updates`);
            }

            // Process the result using the central result processor
            const success = await centralResultProcessor.processResult(
              resultData,
              'ResultQueue',
              transactionId
            );

            if (success) {
              // Update stats
              this.processingStats.processed++;
              this.processingStats.lastProcessed = new Date();

              // Emit event
              this.emit('result:processed', testId, resultData.status || 'unknown', 'ResultQueue');


              return { success: true };
            } else {
              // When centralResultProcessor returns false, it means the result was a duplicate
              // or already processed. This is NOT a failure - it's the system working correctly.
              // We should treat this as a successful completion to prevent BullMQ retries.

              // Update stats - count as processed since duplicate detection is successful operation
              this.processingStats.processed++;
              this.processingStats.lastProcessed = new Date();

              logger.info(`ResultQueueWorker: Result for test ${testId} was already processed (duplicate detected) - marking job as successful to prevent retries`);

              // Return success to prevent BullMQ from retrying this job
              return { success: true, message: 'Result already processed (duplicate)' };
            }
          } catch (error) {
            // Update stats
            this.processingStats.failed++;

            // Emit event
            if (job.data && job.data.testId) {
              this.emit('result:failed', job.data.testId, error);
            }

            // Enhanced error classification
            const errorMessage = error instanceof Error ? error.message : String(error);
            const isRetryable = this.isRetryableError(error);
            logger.error(`ResultQueueWorker: Error processing result (retryable: ${isRetryable}): ${errorMessage}`);

            if (isRetryable) {
              throw error; // Re-throw to trigger BullMQ retry
            } else {
              // Non-retryable error, return failure
              return { success: false, error: errorMessage };
            }
          }
        },
        {
          connection: {
            host: config.connections.redis.host,
            port: config.connections.redis.port,
            password: config.connections.redis.password,
            maxRetriesPerRequest: null, // Required for BullMQ
          },
          concurrency: 2, // Further reduced to prevent database overload
          lockDuration: 300000, 
          skipLockRenewal: false, // Ensure lock renewal is enabled
          drainDelay: 1000, // Wait 1 second before checking for new jobs when queue is empty
          removeOnComplete: {
            count: 100, // Keep last 100 completed jobs
            age: 60 * 60 * 1000 // Remove completed jobs after 1 hour
          },
          removeOnFail: {
            count: 200, // Keep last 200 failed jobs
            age: 24 * 60 * 60 * 1000 // Remove failed jobs after 24 hours
          },
          settings: {
            backoffStrategy: (attemptsMade: number) => {
              // More aggressive backoff for database operations
              const baseDelay = 2000; // 2 seconds base delay
              const maxDelay = 60000; // 60 seconds max delay
              const jitter = Math.random() * 1000; // Random jitter up to 1 second
              return Math.min(baseDelay * Math.pow(2, attemptsMade) + jitter, maxDelay);
            }
          }
        }
      );

      // Set up event handlers
      this.worker.on('completed', (job) => {
        logger.debug(`ResultQueueWorker: Completed processing job ${job.id}`);
      });

      this.worker.on('failed', (job, error) => {
        logger.error(`ResultQueueWorker: Failed to process job ${job?.id}: ${error}`);
        this.emit('worker:error', error);
      });

      this.worker.on('error', (error) => {
        logger.error(`ResultQueueWorker: Worker error: ${error}`);
        this.emit('worker:error', error);
      });

      this.isInitialized = true;
      logger.info('ResultQueueWorker: Initialized successfully');
      return true;
    } catch (error) {
      logger.error(`ResultQueueWorker: Initialization error: ${error}`);
      return false;
    }
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorString = errorMessage.toLowerCase();

    // Database connection errors - retryable
    if (errorString.includes('connection') ||
        errorString.includes('timeout') ||
        errorString.includes('network') ||
        errorString.includes('econnreset') ||
        errorString.includes('enotfound') ||
        errorString.includes('socket') ||
        errorString.includes('mongo')) {
      return true;
    }

    // MongoDB specific errors - retryable
    if (errorString.includes('mongodb') ||
        errorString.includes('pool') ||
        errorString.includes('server selection') ||
        errorString.includes('topology')) {
      return true;
    }

    // Temporary service unavailable - retryable
    if (errorString.includes('service unavailable') ||
        errorString.includes('too many requests') ||
        errorString.includes('rate limit')) {
      return true;
    }

    // Data validation errors - not retryable
    if (errorString.includes('validation') ||
        errorString.includes('invalid') ||
        errorString.includes('missing required') ||
        errorString.includes('duplicate key')) {
      return false;
    }

    // Default to retryable for unknown errors
    return true;
  }

  /**
   * Get processing statistics
   */
  public getStats(): {
    processed: number;
    failed: number;
    lastProcessed: Date | null;
    startTime: Date;
    uptime: number;
    rate: number;
  } {
    const now = new Date();
    const uptime = (now.getTime() - this.processingStats.startTime.getTime()) / 1000; // in seconds
    const rate = uptime > 0 ? this.processingStats.processed / uptime : 0; // results per second

    return {
      ...this.processingStats,
      uptime,
      rate
    };
  }

  /**
   * Close the worker
   */
  public async close(): Promise<void> {
    if (this.worker) {
      await this.worker.close();
      this.worker = null;
      this.isInitialized = false;
      logger.info('ResultQueueWorker: Closed');
    }
  }
}

// Export singleton instance
export const resultQueueWorker = ResultQueueWorker.getInstance();
export default resultQueueWorker;
