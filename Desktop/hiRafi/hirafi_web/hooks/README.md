# Hooks Documentation

## Overview

This directory contains all React hooks used throughout the Hirafi Web application. The hooks are organized by functionality and follow consistent patterns for maintainability and developer experience.

## 📁 Structure

```
hooks/
├── index.ts                    # Centralized exports
├── types.ts                    # Shared TypeScript types
├── utils.ts                    # Common patterns and utilities
├── README.md                   # This documentation
│
├── 🎨 UI & Interaction
│   ├── useMediaQuery.ts        # Media queries & responsive design
│   ├── useDebounce.ts          # Input debouncing & performance
│   ├── useInterval.ts          # Interval management
│   └── useFeedback.ts          # User feedback system
│
├── 📊 Data Management
│   ├── useScenarioManager.ts   # Scenario list management
│   ├── useUnifiedScenario.ts   # Single scenario CRUD
│   ├── useSWRScenarios.ts      # SWR data fetching
│   ├── useScenariosActions.ts  # Scenario actions
│   ├── useFolders.ts           # Folder management
│   ├── useRuns.ts              # Test runs
│   ├── useRunDetail.ts         # Single run details
│   ├── useRunsFilter.ts        # Run filtering
│   ├── useRunReports.ts        # Run reports
│   ├── useTests.ts             # Test execution
│   ├── useReport.ts            # Single report
│   ├── useReports.ts           # Report lists
│   ├── useDashboard.ts         # Dashboard data
│   └── useSchedules.ts         # Schedule management
│
├── 🧪 Test Data
│   ├── useTestData.ts          # Test data management
│   └── useTestDataSets.ts      # Test data sets
│
├── 🔗 Integrations
│   ├── useTestManagementIntegration.ts  # Unified test management
│   ├── useTestRailIntegration.ts        # TestRail integration
│   ├── useZephyrScaleIntegration.ts     # Zephyr Scale integration
│   └── useAIModels.ts                   # AI model management
│
└── ⚙️ System
    ├── useStoreContext.ts      # Store context management
    └── useExpiryCleanup.ts     # Data expiry cleanup
```

## 🏗️ Architecture Patterns

### 1. SWR-Based Data Fetching

```typescript
import useSWR from 'swr'
import { createSWRKey } from './utils'

export function useExampleData(filters: QueryParams) {
  const key = createSWRKey('example-data', filters)
  
  const { data, error, isLoading, mutate } = useSWR(
    key,
    () => fetchExampleData(filters),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      // ... other options
    }
  )

  return {
    data: data?.items || [],
    isLoading,
    error,
    refresh: mutate
  }
}
```

### 2. CRUD Operations

```typescript
import { useOperationStates, handleHookError, handleHookSuccess } from './utils'

export function useExampleCRUD() {
  const { setOperationLoading, isOperationLoading } = useOperationStates()

  const create = async (data: CreateData) => {
    if (isOperationLoading('create')) return { success: false }
    
    setOperationLoading('create', true)
    try {
      const response = await api.create(data)
      return handleHookSuccess(response, 'create')
    } catch (error) {
      return { success: false, error: handleHookError(error, 'create') }
    } finally {
      setOperationLoading('create', false)
    }
  }

  return { create, isCreating: isOperationLoading('create') }
}
```

### 3. Form Management

```typescript
import { useFormValidation } from './utils'

export function useExampleForm(initialData: FormData) {
  const [formData, setFormData] = useState(initialData)
  
  const validators = {
    name: (value: string) => !value?.trim() ? 'Name is required' : undefined,
    email: (value: string) => !value?.includes('@') ? 'Invalid email' : undefined
  }

  const { validation, validateForm, touchField } = useFormValidation(formData, validators)

  const updateField = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    touchField(field)
  }

  return {
    formData,
    validation,
    updateField,
    validateForm
  }
}
```

## 📋 Naming Conventions

### Hook Names
- **Prefix**: Always start with `use`
- **Format**: `camelCase`
- **Pattern**: `use{Entity}{Action?}`

```typescript
// ✅ Good
useScenarios()      // Entity management
useScenarioManager() // Entity with manager pattern
useRunDetail()      // Single entity detail
useRunsFilter()     // Entity collection with feature

// ❌ Bad
use-scenarios()     // kebab-case
UseScenarios()      // PascalCase
useGetScenarios()   // Verbose with action
```

### File Names
- **Format**: `camelCase`
- **Extension**: `.ts` (avoid `.tsx` unless JSX is used)

```
✅ useScenarios.ts
✅ useMediaQuery.ts
❌ use-scenarios.ts
❌ UseScenarios.ts
```

## 🎯 Hook Categories

### UI & Interaction
Hooks for user interface interactions, responsive design, and user experience.

**Key Hooks:**
- `useMediaQuery` - Media query matching
- `useDebounce` - Input debouncing
- `useInterval` - Interval management
- `useFeedback` - User feedback system

### Data Management
Hooks for business logic, data fetching, and state management.

**Key Hooks:**
- `useScenarioManager` - Scenario list management
- `useUnifiedScenario` - Single scenario operations
- `useRuns` - Test run management
- `useDashboard` - Dashboard data aggregation

### Test Data
Hooks specifically for test data management and generation.

**Key Hooks:**
- `useTestData` - General test data operations
- `useTestDataSets` - Test data set management

### Integrations
Hooks for external service integrations and third-party APIs.

**Key Hooks:**
- `useTestManagementIntegration` - Unified test management
- `useAIModels` - AI service integration

### System
Hooks for application lifecycle, context management, and system utilities.

**Key Hooks:**
- `useStoreContext` - Store context management
- `useExpiryCleanup` - Data cleanup utilities

## 🛠️ Shared Utilities

### Types (`types.ts`)
Common TypeScript interfaces and types used across hooks.

```typescript
import type { ApiResponse, LoadingState, QueryParams } from './types'
```

### Utils (`utils.ts`)
Shared patterns, helpers, and utilities.

```typescript
import { 
  createSWRKey, 
  handleHookError, 
  useOperationStates, 
  useFormValidation 
} from './utils'
```

## 📖 Usage Examples

### Basic Data Fetching

```typescript
import { useScenarios } from '@/hooks'

function ScenarioList() {
  const { scenarios, isLoading, error, refresh } = useScenarios({
    autoFetch: true
  })

  if (isLoading) return <Loading />
  if (error) return <Error message={error} />

  return (
    <div>
      {scenarios.map(scenario => (
        <ScenarioItem key={scenario.id} scenario={scenario} />
      ))}
      <button onClick={refresh}>Refresh</button>
    </div>
  )
}
```

### CRUD Operations

```typescript
import { useFolders } from '@/hooks'

function FolderManager() {
  const { 
    folders, 
    createFolder, 
    deleteFolder, 
    isOperationInProgress 
  } = useFolders()

  const handleCreate = async () => {
    const result = await createFolder({ name: 'New Folder' })
    if (result.success) {
      console.log('Folder created:', result.folder)
    }
  }

  return (
    <div>
      <button 
        onClick={handleCreate}
        disabled={isOperationInProgress('createFolder')}
      >
        Create Folder
      </button>
      {/* ... folder list */}
    </div>
  )
}
```

### Form Management

```typescript
import { useUnifiedScenario } from '@/hooks'

function ScenarioForm({ mode, scenarioId }) {
  const {
    formData,
    validation,
    updateFormField,
    saveScenario
  } = useUnifiedScenario({ mode, scenarioId })

  return (
    <form onSubmit={saveScenario}>
      <input
        value={formData.name}
        onChange={(e) => updateFormField('name', e.target.value)}
        className={validation.errors.name ? 'error' : ''}
      />
      {validation.errors.name && (
        <span className="error">{validation.errors.name}</span>
      )}
      <button type="submit" disabled={!validation.isValid}>
        Save
      </button>
    </form>
  )
}
```

## 🔄 Migration Guide

### From Old Patterns

If you're migrating from older hook patterns:

1. **Replace separated hooks** with unified ones:
   ```typescript
   // ❌ Old
   import { useFoldersOnly } from '@/hooks/useFoldersOnly'
   import { useFoldersActions } from '@/hooks/useFoldersActions'
   
   // ✅ New
   import { useFolders } from '@/hooks'
   ```

2. **Update media query hooks**:
   ```typescript
   // ❌ Old
   import { useMediaQuery } from '@/hooks/use-media-query'
   import { useIsMobile } from '@/hooks/use-mobile'
   
   // ✅ New
   import { useMediaQuery, useIsMobile } from '@/hooks'
   ```

3. **Use shared types and utilities**:
   ```typescript
   // ✅ New
   import { useOperationStates, type ApiResponse } from '@/hooks'
   ```

## 🚀 Best Practices

### 1. Error Handling
Always handle errors consistently using shared utilities:

```typescript
import { handleHookError } from './utils'

try {
  const result = await api.call()
  return { success: true, data: result }
} catch (error) {
  return { success: false, error: handleHookError(error, 'operation') }
}
```

### 2. Loading States
Use operation states for better UX:

```typescript
import { useOperationStates } from './utils'

const { setOperationLoading, isOperationLoading } = useOperationStates()

// Prevent concurrent operations
if (isOperationLoading('save')) return
```

### 3. Cache Management
Leverage SWR's cache management:

```typescript
import { mutate } from 'swr'

// Invalidate specific cache
mutate('scenarios')

// Invalidate pattern
mutate(key => typeof key === 'string' && key.startsWith('scenarios'))
```

### 4. Type Safety
Always use TypeScript interfaces:

```typescript
import type { Scenario, ApiResponse } from '@/types'

interface UseScenarioReturn {
  scenarios: Scenario[]
  create: (data: CreateScenarioData) => Promise<ApiResponse<Scenario>>
  // ...
}
```

## 🧪 Testing Hooks

### Unit Testing
Use React Testing Library's `renderHook`:

```typescript
import { renderHook } from '@testing-library/react'
import { useScenarios } from '@/hooks'

test('should fetch scenarios', async () => {
  const { result } = renderHook(() => useScenarios())
  
  expect(result.current.isLoading).toBe(true)
  
  await waitFor(() => {
    expect(result.current.scenarios).toHaveLength(3)
  })
})
```

### Integration Testing
Test hooks with real API calls in component tests:

```typescript
import { render, screen } from '@testing-library/react'
import { ScenarioList } from '@/components'

test('should display scenarios', async () => {
  render(<ScenarioList />)
  
  expect(screen.getByText('Loading...')).toBeInTheDocument()
  
  await waitFor(() => {
    expect(screen.getByText('Test Scenario')).toBeInTheDocument()
  })
})
```

## 🤝 Contributing

### Adding New Hooks

1. **Choose the right category** based on functionality
2. **Follow naming conventions** (use{Entity}{Action})
3. **Use shared types and utilities** when possible
4. **Add proper TypeScript types**
5. **Include error handling**
6. **Update the index.ts** exports
7. **Document in this README**

### Code Review Checklist

- [ ] Follows naming conventions
- [ ] Uses shared types and utilities
- [ ] Includes proper error handling
- [ ] Has TypeScript interfaces
- [ ] Loading states are managed
- [ ] SWR patterns are used correctly
- [ ] No duplicate functionality
- [ ] Updated exports in index.ts

## 🔧 Troubleshooting

### Common Issues

1. **Hook not found after cleanup**
   - Check if it was merged into another hook
   - Look in the index.ts exports

2. **TypeScript errors**
   - Import types from `./types`
   - Use shared interfaces

3. **Performance issues**
   - Use debouncing for frequent updates
   - Leverage SWR's caching
   - Check for unnecessary re-renders

### Debugging

Enable SWR dev tools for debugging:

```typescript
import { SWRConfig } from 'swr'

function App() {
  return (
    <SWRConfig
      value={{
        onError: (error) => console.error('SWR Error:', error),
        onSuccess: (data, key) => console.log('SWR Success:', key, data)
      }}
    >
      {/* Your app */}
    </SWRConfig>
  )
}
```

## 📚 Further Reading

- [SWR Documentation](https://swr.vercel.app/)
- [React Hooks Best Practices](https://react.dev/reference/react)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Testing Library Hooks](https://testing-library.com/docs/react-testing-library/api#renderhook)

---

*Last updated: December 2024*
*Version: 2.0.0 (Post-cleanup)*