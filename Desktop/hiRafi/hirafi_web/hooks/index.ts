/**
 * Hooks Index
 *
 * Centralized exports for all application hooks
 * Organized by functionality for better developer experience
 */

// =============================================================================
// CORE UTILITIES
// =============================================================================
export * from "./types";
export * from "./utils";

// =============================================================================
// UI & INTERACTION HOOKS
// =============================================================================
export {
  useMediaQuery,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  useBreakpoint,
} from "./useMediaQuery";
export {
  useDebounce,
  useDebouncedInput,
  useDebouncedVariableInput,
} from "./useDebounce";
export { useInterval } from "./useInterval";
export { useFeedback } from "./useFeedback";

// =============================================================================
// DATA MANAGEMENT HOOKS
// =============================================================================

// Scenarios
export { useScenarioManager } from "./useScenarioManager";
export { useUnifiedScenario } from "./useUnifiedScenario";
export { useSWRScenarios } from "./useSWRScenarios";
export { useScenariosActions } from "./useScenariosActions";

// Folders
export { useFolders } from "./useFolders";

// Runs & Execution
export { useRuns } from "./useRuns";
export { useRunDetail } from "./useRunDetail";
export { useRunsFilter } from "./useRunsFilter";
export { useRunReports } from "./useRunReports";
export { useTests } from "./useTests";

// Reports & Analytics
export { useReport } from "./useReport";
export { useReports } from "./useReports";
export { useDashboard } from "./useDashboard";

// Schedules
export { useSchedules } from "./useSchedules";

// =============================================================================
// TEST DATA MANAGEMENT
// =============================================================================
export { useTestData } from "./useTestData";
export { useTestDataSets } from "./useTestDataSets";

// =============================================================================
// INTEGRATIONS
// =============================================================================
export { useTestRailIntegration } from "./useTestRailIntegration";
export { useZephyrScaleIntegration } from "./useZephyrScaleIntegration";
export { useAIModels } from "./useAIModels";

// =============================================================================
// SYSTEM & LIFECYCLE
// =============================================================================
export { useStoreContext } from "./useStoreContext";
export { useExpiryCleanup } from "./useExpiryCleanup";
