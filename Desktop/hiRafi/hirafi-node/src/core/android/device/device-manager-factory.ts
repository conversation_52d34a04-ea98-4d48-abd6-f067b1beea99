/**
 * Device Manager Factory Service
 * Pure DI-compliant factory for creating device managers with pre-constructed dependencies
 */

import { IDeviceManager } from './device-manager.interface.js';
import { ILoggerService } from '../../../utils/logger-service.js';
import { DeviceProviderConfigService, ProviderConfiguration } from './services/device-provider-config.service.js';
import { DeviceProviderRegistryService } from './services/device-provider-registry.service.js';
import { DeviceManagerUtils } from './shared/device-manager-utils.js';
import { DeviceProviderDependencies } from './interfaces/device-provider-registry.interface.js';

/**
 * Legacy device provider configuration interface for backward compatibility
 * @deprecated Use ProviderConfiguration from DeviceProviderConfigService instead
 */
export interface DeviceProviderConfig {
  provider?: 'sauceLabs' | 'testinium';
  android?: {
    sauceLabs?: {
      enabled?: boolean;
      user?: string;
      username?: string;
      key?: string;
      accessKey?: string;
      region?: string;
      options?: any;
    };
    testinium?: {
      enabled?: boolean;
      apiUrl?: string;
      clientId?: string;
      clientSecret?: string;
      issuerUri?: string;
      options?: any;
    };
    capabilities?: any; // AppiumBaseCapabilities
  };
}

/**
 * Pure Device Manager Factory Service
 * DI-compliant factory using pre-constructed dependencies and registry pattern
 */
export class DeviceManagerFactory {
  private logger: ILoggerService;
  private configService: DeviceProviderConfigService;
  private registry: DeviceProviderRegistryService;
  private utils: DeviceManagerUtils;

  constructor(
    logger: ILoggerService,
    configService: DeviceProviderConfigService,
    registry: DeviceProviderRegistryService,
    utils: DeviceManagerUtils
  ) {
    this.logger = logger;
    this.configService = configService;
    this.registry = registry;
    this.utils = utils;
  }

  /**
   * Create a device manager using pure factory pattern with pre-constructed dependencies
   * @param config Configuration object
   * @param deviceProvider Selected device provider for mutual exclusivity
   * @returns Device manager
   */
  createDeviceManager(config: DeviceProviderConfig, deviceProvider?: 'sauceLabs' | 'testinium'): IDeviceManager {
    // Auto-detect provider if not specified
    const selectedProvider = deviceProvider || this.configService.autoDetectProvider(config) || 'sauceLabs';

    this.logger.info(`DeviceManagerFactory: Preparing device manager configuration for provider: ${selectedProvider}`);

    // Get provider configuration using config service
    const providerConfig = this.configService.getProviderConfiguration(config, selectedProvider);

    // Validate configuration
    const validation = this.configService.validateConfiguration(providerConfig);
    if (!validation.valid) {
      throw new Error(`DeviceManagerFactory: Invalid configuration for ${selectedProvider}: ${validation.errors.join(', ')}`);
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => this.logger.warn(`DeviceManagerFactory: ${warning}`));
    }

    // Create dependencies object
    const dependencies: DeviceProviderDependencies = {
      logger: this.logger,
      utils: this.utils
    };

    // Use registry to create device manager
    return this.registry.createDeviceManager(selectedProvider, providerConfig, dependencies);
  }

  /**
   * Create device manager with provider configuration (new pure method)
   * @param providerConfig Provider configuration
   * @param dependencies Pre-constructed dependencies
   * @returns Device manager
   */
  createDeviceManagerWithDependencies(
    providerConfig: ProviderConfiguration,
    dependencies: DeviceProviderDependencies
  ): IDeviceManager {
    this.logger.info(`DeviceManagerFactory: Validating configuration and delegating to registry for provider: ${providerConfig.provider}`);

    // Validate configuration
    const validation = this.configService.validateConfiguration(providerConfig);
    if (!validation.valid) {
      throw new Error(`DeviceManagerFactory: Invalid configuration for ${providerConfig.provider}: ${validation.errors.join(', ')}`);
    }

    // Use registry to create device manager
    return this.registry.createDeviceManager(providerConfig.provider, providerConfig, dependencies);
  }
}

/**
 * Legacy factory function for backward compatibility
 * @deprecated Use DeviceManagerFactory service with proper DI instead
 */
export function createDeviceManager(config: any, deviceProvider?: 'sauceLabs' | 'testinium'): IDeviceManager {
  // Create temporary services for backward compatibility
  const tempLogger = {
    info: (message: string) => console.log(`[Legacy] ${message}`),
    warn: (message: string) => console.warn(`[Legacy] ${message}`),
    error: (message: string) => console.error(`[Legacy] ${message}`)
  } as ILoggerService;

  const configService = new DeviceProviderConfigService(tempLogger);
  const registry = new DeviceProviderRegistryService(tempLogger, configService);
  const utils = new DeviceManagerUtils(tempLogger);

  const factory = new DeviceManagerFactory(tempLogger, configService, registry, utils);
  return factory.createDeviceManager(config, deviceProvider);
}
