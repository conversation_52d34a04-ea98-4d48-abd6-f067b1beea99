/**
 * Authentication Routes
 * Kimlik doğrulama ve kullanıcı işlemleri için API rotaları
 */

import { Router, Request, Response } from 'express';
import { logger } from '../../utils/logger.js';
import {
  registerUser,
  registerUserWithTeam,
  loginUser,
  getUserById
} from '../../services/mongo/userService.js';
import { authenticate, AuthRequest } from '../../api/middleware/authMiddleware.js';
const router = Router();

/**
 * POST /api/auth/register
 * Yeni kullanıcı kaydı oluşturur (Şu an devre dışı)
 */
router.post('/register', async (req: Request, res: Response) => {
  // Kayıt işlemi geçici olarak devre dışı bırakıldı

  return res.status(403).json({
    success: false,
    error: '<PERSON><PERSON>t iş<PERSON>i şu anda devre dışıdır. Lütfen sistem yöneticinizle iletişime geçin.'
  });
});

/**
 * POST /api/auth/login
 * Kullanıcı girişi yapar
 */
router.post('/login', async (req: Request, res: Response) => {
  try {
    const credentials = {
      email: req.body.email,
      password: req.body.password
    };

    // Zorunlu alanları kontrol et
    if (!credentials.email || !credentials.password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    const result = await loginUser(credentials.email, credentials.password);

    if (!result.success) {
      return res.status(401).json({
        success: false,
        error: result.message || 'Authentication failed'
      });
    }

    // Web app ile uyumlu olması için token değerini düzenle
    res.json({
      success: true,
      data: {
        user: {
          id: result.data && result.data.user ? result.data.user.id : undefined,
          email: result.data && result.data.user ? result.data.user.email : undefined,
          name: result.data && result.data.user && result.data.user.name ? result.data.user.name : '',
          role: result.data && result.data.user && 'role' in result.data.user ? result.data.user.role : 'user',
          accountType: result.data && result.data.user && result.data.user.accountType ? result.data.user.accountType : 'basic',
          teamRole: result.data && result.data.user && result.data.user.teamRole ? result.data.user.teamRole : null,
          companyId: result.data && result.data.user && result.data.user.companyId ? result.data.user.companyId : null,
          teamId: result.data && result.data.user && result.data.user.teamId ? result.data.user.teamId : null
        },
        token: result.data ? result.data.token : undefined
      }
    });
  } catch (error: any) {
    logger.error('[API] Error in POST /auth/login:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Login error'
    });
  }
});

/**
 * GET /api/auth/profile
 * Kullanıcı profil bilgilerini getirir (kimlik doğrulama gerekli)
 */
router.get('/profile', authenticate as any, async (req: AuthRequest, res: Response) => {
  try {
    // Kimlik doğrulama middleware'inden gelen kullanıcı bilgisi
    const userId = req.user ? req.user.id : undefined;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const result = await getUserById(userId);

    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: result.message
      });
    }

    const firstName = result.data && result.data.firstName ? result.data.firstName : '';
    const lastName = result.data && result.data.lastName ? result.data.lastName : '';
    const defaultName = `${firstName} ${lastName}`.trim();

    res.json({
      success: true,
      data: {
        user: {
          id: result.data ? result.data.id : undefined,
          email: result.data ? result.data.email : undefined,
          name: result.data && result.data.name ? result.data.name : defaultName,
          role: result.data && 'role' in result.data ? result.data.role : 'user',
          accountType: result.data && result.data.accountType ? result.data.accountType : 'basic',
          teamRole: result.data && result.data.teamRole ? result.data.teamRole : null,
          companyId: result.data && result.data.companyId ? result.data.companyId : null,
          teamId: result.data && result.data.teamId ? result.data.teamId : null
        }
      }
    });
  } catch (error: any) {
    logger.error('[API] Error in GET /auth/profile:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Error fetching profile'
    });
  }
});

/**
 * POST /api/auth/validate
 * Token doğrulaması yapar (token kontrolü için)
 */
router.post('/validate', authenticate as any, (req: AuthRequest, res: Response) => {
  // Token geçerliyse user bilgisini dön
  res.json({
    success: true,
    data: {
      user: {
        id: req.user ? req.user.id : undefined,
        email: req.user ? req.user.email : undefined,
        role: req.user && 'role' in req.user ? req.user.role : undefined,
        accountType: req.user ? req.user.accountType : undefined,
        teamRole: req.user && 'teamRole' in req.user ? req.user.teamRole : null,
        companyId: req.user && 'companyId' in req.user ? req.user.companyId : null,
        teamId: req.user && 'teamId' in req.user ? req.user.teamId : null
      }
    }
  });
});

export default router;