/**
 * Unified Scenario Hook
 *
 * Single hook for both scenario creation and editing operations
 */

import { useState, useCallback, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/api/auth";
import { toast } from "@/lib/utils/toast-utils";
import { CacheManager } from "@/lib/utils/cache-utils";
import { UnifiedScenarioService } from "@/lib/services/unifiedScenarioService";
import {
  ExtendedScenarioFormData,
  ValidationState,
  ScenarioUIState,
  FolderState,
  createDefaultFormData,
  createDefaultValidationState,
  createDefaultUIState,
  createDefaultFolderState,
  validateField,
  validateForm,
  mapApiScenarioToFormData,
} from "@/lib/utils/scenario-form-utils";
import { updateFormDataWithProvider } from "@/lib/utils/test-management-utils";
import { TestManagementProvider } from "@/store/testManagementStore";
import { TestStep } from "@/models/scenario";
import { useScenarioCreationStore } from "@/store/scenarioCreationStore";
import { useTestManagementStore } from "@/store/testManagementStore";

export interface UseUnifiedScenarioOptions {
  mode: "create" | "edit";
  scenarioId?: string;
}

export interface UseUnifiedScenarioReturn {
  // Data
  formData: ExtendedScenarioFormData;
  validation: ValidationState;
  ui: ScenarioUIState;
  folder: FolderState;

  // Form actions
  updateFormField: (field: keyof ExtendedScenarioFormData, value: any) => void;
  resetForm: () => void;
  validateFormData: () => ValidationState;

  // Step management
  addStep: (position?: number) => void;
  removeStep: (stepId: string) => void;
  updateStep: (stepId: string, updates: Partial<TestStep>) => void;
  updateStepType: (stepId: string, newType: string) => void;
  reorderSteps: (sourceIndex: number, destinationIndex: number) => void;
  clearAllSteps: () => void;

  // Tag management
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;

  // Test management
  handleTestManagementSelection: (
    caseIds: string[],
    provider: TestManagementProvider,
  ) => void;

  // Operations
  saveScenario: () => Promise<void>;
  generateSteps: (
    isTestManagement: boolean,
    name?: string,
    description?: string,
    testDataVariables?: any[],
    creativityLevel?: number,
    provider?: TestManagementProvider,
    customPrompt?: string,
  ) => Promise<void>;
  deleteScenario?: () => Promise<void>;

  // UI actions
  setCurrentStep?: (step: number) => void;
  setActiveTab?: (tab: string) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;

  // Folder actions
  setNewFolderOpen: (open: boolean) => void;
  setNewFolderName: (name: string) => void;
  setNewFolderColor: (color: string) => void;
  setIsCreatingFolder: (creating: boolean) => void;
}

export function useUnifiedScenario(
  options: UseUnifiedScenarioOptions,
): UseUnifiedScenarioReturn {
  const { mode, scenarioId } = options;
  const router = useRouter();
  const { user } = useAuth();
  // Function to invalidate scenarios cache after mutations
  const mutateScenariosCache = useCallback(async () => {
    // Use global cache invalidation for consistent updates
    await CacheManager.invalidateScenarioData();
  }, []);

  // Enhanced persistence using direct store access
  const store = useScenarioCreationStore();

  // Track initialization state to prevent auto-save during setup
  const [isInitializing, setIsInitializing] = useState(true);

  // Initialize persistence for creation mode
  useEffect(() => {
    if (mode === "create" && !store.isInitialized) {
      store.initializePersistence({
        enableAutoSave: true,
        autoSaveInterval: 30000, // 30 seconds
        debounceDelay: 1000, // 1 second debounce for immediate saves
        expiryTime: 60, // 60 minutes
        showSaveNotifications: false, // Don't show auto-save notifications
        showExpiryWarnings: true,
        onDraftLoaded: (draftData: ExtendedScenarioFormData) => {
          // Schedule state updates for next tick to avoid setState-in-render
          setTimeout(() => {
            setFormData(draftData);
            setValidation(validateForm(draftData));
            console.log("Draft loaded and applied to form state");
          }, 0);
        },
        onDraftSaved: () => {
          // Optional: Add any UI feedback for saves
        },
        onDraftExpired: () => {
          if (mode === "create") {
            toast.warning("Previous draft has expired and was cleared");
          }
        },
      });
    }
  }, [mode, store.isInitialized]);

  // Create persistence interface for backward compatibility
  const persistence = useMemo(() => ({
    isInitialized: store.isInitialized,
    hasDraft: !!store.currentDraft && !store.isExpired(),
    draftAge: store.getDraftAge(),
    clearDraft: () => {
      store.clearCurrentDraft();
      store._stopAutoSaveTimer();
      store._clearDebounceTimer();
    },
    createDraft: (data: ExtendedScenarioFormData) => store.createDraft(data),
    saveDraftDebounced: (data: ExtendedScenarioFormData) => store.saveDraftDebounced(data),
    loadDraft: () => store.currentDraft?.formData || null,
  }), [store]);

  // Optimized state initialization - start with default data, load draft after persistence is ready
  const [formData, setFormData] = useState<ExtendedScenarioFormData>(() => {
    return createDefaultFormData(mode === "edit");
  });
  // Initialize validation based on loaded form data
  const [validation, setValidation] = useState<ValidationState>(() => {
    // If we loaded draft data, validate it immediately
    return validateForm(formData);
  });
  const [ui, setUI] = useState<ScenarioUIState>(() => ({
    ...createDefaultUIState(mode === "edit"),
    // Start with loading state for edit mode to prevent showing empty form
    isLoading: mode === "edit" && !!scenarioId,
  }));
  const [folder, setFolder] = useState<FolderState>(createDefaultFolderState);

  // Use unified test management store instead of embedded state
  const testManagementStore = useTestManagementStore();
  const [originalData, setOriginalData] =
    useState<ExtendedScenarioFormData | null>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(mode === "create"); // Track if data has been loaded

  // Define loadScenario callback first
  const loadScenario = useCallback(async () => {
    if (!scenarioId || mode !== "edit") return;

    // Only load if data hasn't been loaded yet
    if (isDataLoaded) return;

    setUI((prev) => ({ ...prev, isLoading: true, scenarioError: null }));

    try {
      const result = await UnifiedScenarioService.loadScenario(scenarioId);

      if (result.success && result.data) {
        const mappedData = mapApiScenarioToFormData(result.data);
        setFormData(mappedData);
        setOriginalData(mappedData);
        setValidation(validateForm(mappedData));
        setIsDataLoaded(true);
      } else {
        setUI((prev) => ({
          ...prev,
          scenarioError: result.error || "Failed to load scenario",
        }));
      }
    } catch (error: any) {
      setUI((prev) => ({
        ...prev,
        scenarioError: error.message || "Failed to load scenario",
      }));
    } finally {
      setUI((prev) => ({ ...prev, isLoading: false }));
    }
  }, [scenarioId, mode, isDataLoaded]);

  // Optimized initialization with proper draft handling
  useEffect(() => {
    // Handle edit mode scenario loading
    if (mode === "edit" && scenarioId && !isDataLoaded) {
      loadScenario();
      return; // Early return to avoid other initialization logic
    }

    // Handle creation mode draft initialization
    if (mode === "create" && persistence.isInitialized) {
      // Try to load existing draft first
      if (persistence.hasDraft) {
        const draftData = persistence.loadDraft();
        if (draftData) {
          // Schedule state updates for next tick to avoid setState-in-render
          setTimeout(() => {
            setFormData(draftData);
            setValidation(validateForm(draftData));
            setIsInitializing(false); // Mark initialization as complete
            console.log("Existing draft loaded on initialization");
          }, 0);
        }
      } else {
        // Create initial draft if none exists - schedule for next tick
        setTimeout(() => {
          persistence.createDraft(formData);
          setIsInitializing(false); // Mark initialization as complete
          console.log("Initial draft created for scenario creation");
        }, 0);
      }
    }
  }, [mode, scenarioId, isDataLoaded, loadScenario, persistence.isInitialized, persistence.hasDraft]); // Added persistence state dependencies

  // Form actions
  const updateFormField = useCallback(
    (field: keyof ExtendedScenarioFormData, value: any) => {
      setFormData((prev) => {
        const newData = { ...prev, [field]: value };

        // Validate the field
        const error = validateField(field as string, value, newData);
        setValidation((prevValidation) => ({
          ...prevValidation,
          errors: error
            ? { ...prevValidation.errors, [field]: error }
            : { ...prevValidation.errors, [field]: "" },
          touched: { ...prevValidation.touched, [field]: true },
          isValid:
            Object.values({
              ...prevValidation.errors,
              [field]: error || "",
            }).filter(Boolean).length === 0,
        }));

        // Optimized auto-save for creation mode using debounced save
        // Only save if not in initialization phase to prevent setState-in-render
        if (mode === "create" && persistence.isInitialized && !isInitializing) {
          setTimeout(() => {
            persistence.saveDraftDebounced(newData);
          }, 0);
        }

        return newData;
      });
    },
    [mode, persistence],
  );

  const resetForm = useCallback(() => {
    const defaultData = createDefaultFormData(mode === "edit");
    setFormData(defaultData);
    setValidation(createDefaultValidationState());
    setOriginalData(mode === "edit" ? defaultData : null);
    setIsDataLoaded(mode === "create"); // Reset data loaded state

    // Clear persistence store for creation mode
    if (mode === "create") {
      persistence.clearDraft();
    }
  }, [mode, persistence]);

  // Memoized validation to prevent unnecessary recalculations
  const validateFormData = useCallback(() => {
    const validationResult = validateForm(formData);
    setValidation(validationResult);
    return validationResult;
  }, [formData]);

  // Memoized form state checks for performance
  const formState = useMemo(() => ({
    hasChanges: mode === "edit" && originalData ?
      JSON.stringify(formData) !== JSON.stringify(originalData) : false,
    isValid: validation.isValid,
    hasSteps: formData.steps.length > 0,
    hasRequiredFields: formData.name.trim().length > 0
  }), [formData, originalData, validation.isValid, mode]);

  // Step management with persistence
  const addStep = useCallback((position?: number) => {
    setFormData((prev) => {
      const newSteps = [...prev.steps];
      const newStep: TestStep = {
        id: `step-${Date.now()}`,
        type: "aiAction",
        name: "New Step",
        target: "",
        value: "",
        description: "",
      };

      if (position !== undefined) {
        newSteps.splice(position, 0, newStep);
      } else {
        newSteps.push(newStep);
      }

      const newData = { ...prev, steps: newSteps };

      // Save to persistence for creation mode
      if (mode === "create" && persistence.isInitialized) {
        persistence.saveDraftDebounced(newData);
      }

      return newData;
    });
  }, [mode, persistence]);

  const removeStep = useCallback((stepId: string) => {
    setFormData((prev) => {
      const newData = {
        ...prev,
        steps: prev.steps.filter((step) => step.id !== stepId),
      };

      // Save to persistence for creation mode
      if (mode === "create" && persistence.isInitialized) {
        persistence.saveDraftDebounced(newData);
      }

      return newData;
    });
  }, [mode, persistence]);

  const updateStep = useCallback(
    (stepId: string, updates: Partial<TestStep>) => {
      setFormData((prev) => {
        const newData = {
          ...prev,
          steps: prev.steps.map((step) =>
            step.id === stepId ? { ...step, ...updates } : step,
          ),
        };

        // Save to persistence for creation mode
        if (mode === "create" && persistence.isInitialized) {
          persistence.saveDraftDebounced(newData);
        }

        return newData;
      });
    },
    [mode, persistence],
  );

  const updateStepType = useCallback(
    (stepId: string, newType: string) => {
      updateStep(stepId, { type: newType as any });
    },
    [updateStep],
  );

  const reorderSteps = useCallback(
    (sourceIndex: number, destinationIndex: number) => {
      setFormData((prev) => {
        const newSteps = [...prev.steps];
        const [removed] = newSteps.splice(sourceIndex, 1);
        newSteps.splice(destinationIndex, 0, removed);
        const newData = { ...prev, steps: newSteps };

        // Save to persistence for creation mode
        if (mode === "create" && persistence.isInitialized) {
          persistence.saveDraftDebounced(newData);
        }

        return newData;
      });
    },
    [mode, persistence],
  );

  const clearAllSteps = useCallback(() => {
    setFormData((prev) => {
      const newData = { ...prev, steps: [] };

      // Save to persistence for creation mode
      if (mode === "create" && persistence.isInitialized) {
        persistence.saveDraftDebounced(newData);
      }

      return newData;
    });
  }, [mode, persistence]);

  // Tag management
  const addTag = useCallback(
    (tag: string) => {
      if (tag.trim() && !formData.tags.includes(tag.trim())) {
        setFormData((prev) => ({
          ...prev,
          tags: [...prev.tags, tag.trim()],
          newTag: "",
        }));
      }
    },
    [formData.tags],
  );

  const removeTag = useCallback((tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }));
  }, []);

  // Test management - now integrated with unified store
  const handleTestManagementSelection = useCallback(
    (caseIds: string[], provider: TestManagementProvider) => {
      // Update form data
      updateFormDataWithProvider(
        updateFormField as (field: string, value: any) => void,
        provider,
        caseIds,
      );

      // Sync with unified test management store
      testManagementStore.syncFormDataWithProvider(provider, caseIds);
    },
    [updateFormField, testManagementStore],
  );

  // Operations
  const saveScenario = useCallback(async () => {
    const validationResult = validateFormData();
    if (!validationResult.isValid) {
      toast.error("Please fix validation errors before saving");
      return;
    }

    setUI((prev) => ({ ...prev, isSaving: true }));

    try {
      // Prepare user info for the service
      const userInfo = user
        ? {
            userId: user.id,
            teamId: user.teamId || "",
            companyId: user.companyId || "",
          }
        : undefined;

      const result =
        mode === "create"
          ? await UnifiedScenarioService.createScenario(formData, userInfo)
          : await UnifiedScenarioService.updateScenario(formData);

      if (result.success) {
        toast.success(
          `Scenario ${mode === "create" ? "created" : "updated"} successfully`,
        );

        // Invalidate cache to ensure fresh data and wait for completion
        await mutateScenariosCache();

        if (mode === "create") {
          // Reset form data and clear the draft since scenario was successfully created
          resetForm();

          // Add small delay to ensure cache invalidation is complete
          setTimeout(() => {
            router.push("/scenarios");
          }, 100);
        } else {
          // For update mode, redirect to scenarios page after successful save
          setTimeout(() => {
            router.push("/scenarios");
          }, 100);
        }
      } else {
        toast.error(result.error || `Failed to ${mode} scenario`);
      }
    } catch (error: any) {
      toast.error(error.message || `Failed to ${mode} scenario`);
    } finally {
      setUI((prev) => ({ ...prev, isSaving: false }));
    }
  }, [
    formData,
    mode,
    validateFormData,
    mutateScenariosCache,
    router,
    persistence,
    resetForm,
  ]);

  const generateSteps = useCallback(
    async (
      isTestManagement: boolean,
      name?: string,
      description?: string,
      testDataVariables?: any[],
      creativityLevel?: number,
      provider?: TestManagementProvider,
      customPrompt?: string, // New parameter to override formData.aiPrompt
    ) => {
      // Use custom prompt if provided, otherwise fall back to formData.aiPrompt
      const promptToUse = customPrompt || formData.aiPrompt;

      if (!promptToUse?.trim()) {
        toast.error("Please enter a prompt for AI generation");
        return;
      }

      setUI((prev) => ({ ...prev, isGenerating: true }));

      try {
        const result = await UnifiedScenarioService.generateSteps({
          prompt: promptToUse,
          name: name || formData.name,
          description: description || formData.description,
          isTestManagement,
          testManagementProvider: provider,
          testDataVariables,
          creativityLevel,
        });

        if (result.success && result.steps) {
          setFormData((prev) => ({
            ...prev,
            steps: result.steps || [],
            // AI powered details aktifse name ve description'ı da güncelle
            ...(formData.useAIForDetails && {
              name: result.name || prev.name,
              description: result.description || prev.description
            })
          }));
          toast.success("Steps generated successfully");
        } else {
          toast.error(result.error || "Failed to generate steps");
        }
      } catch (error: any) {
        toast.error(error.message || "Failed to generate steps");
      } finally {
        setUI((prev) => ({ ...prev, isGenerating: false }));
      }
    },
    [formData.aiPrompt, formData.name, formData.description],
  );

  const deleteScenario = useCallback(async () => {
    if (mode !== "edit" || !scenarioId) return;

    setUI((prev) => ({ ...prev, isLoading: true }));

    try {
      const result = await UnifiedScenarioService.deleteScenario(scenarioId);

      if (result.success) {
        toast.success("Scenario deleted successfully");
        await mutateScenariosCache();

        // Add small delay to ensure cache invalidation is complete
        setTimeout(() => {
          router.push("/scenarios");
        }, 100);
      } else {
        toast.error(result.error || "Failed to delete scenario");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete scenario");
    } finally {
      setUI((prev) => ({ ...prev, isLoading: false }));
    }
  }, [mode, scenarioId, mutateScenariosCache, router]);

  // UI actions
  const setCurrentStep = useCallback((step: number) => {
    setUI((prev) => ({ ...prev, currentStep: step }));
  }, []);

  const setActiveTab = useCallback((tab: string) => {
    setUI((prev) => ({ ...prev, activeTab: tab }));
  }, []);

  const setSidebarCollapsed = useCallback((collapsed: boolean) => {
    setUI((prev) => ({ ...prev, sidebarCollapsed: collapsed }));
  }, []);

  // Folder actions
  const setNewFolderOpen = useCallback((open: boolean) => {
    setFolder((prev) => ({ ...prev, newFolderOpen: open }));
  }, []);

  const setNewFolderName = useCallback((name: string) => {
    setFolder((prev) => ({ ...prev, newFolderName: name }));
  }, []);

  const setNewFolderColor = useCallback((color: string) => {
    setFolder((prev) => ({ ...prev, newFolderColor: color }));
  }, []);

  const setIsCreatingFolder = useCallback((creating: boolean) => {
    setFolder((prev) => ({ ...prev, isCreatingFolder: creating }));
  }, []);

  // Memoized return object to prevent unnecessary re-renders
  return useMemo(() => ({
    // Data
    formData,
    validation,
    ui,
    folder,
    formState, // Add computed form state

    // Form actions
    updateFormField,
    resetForm,
    validateFormData,

    // Step management
    addStep,
    removeStep,
    updateStep,
    updateStepType,
    reorderSteps,
    clearAllSteps,

    // Tag management
    addTag,
    removeTag,

    // Test management
    handleTestManagementSelection,

    // Operations
    saveScenario,
    generateSteps,
    ...(mode === "edit" ? { deleteScenario } : {}),
    ...(mode === "create"
      ? {
          clearDraft: resetForm,
          hasDraft: persistence.hasDraft,
          draftAge: persistence.draftAge,
        }
      : {}),

    // UI actions
    ...(mode === "create" ? { setCurrentStep } : {}),
    ...(mode === "edit" ? { setActiveTab } : {}),
    setSidebarCollapsed,

    // Folder actions
    setNewFolderOpen,
    setNewFolderName,
    setNewFolderColor,
    setIsCreatingFolder,
  }), [
    // Core data - these change frequently
    formData,
    validation,
    ui,
    folder,
    formState,

    // Stable functions - these rarely change
    updateFormField,
    resetForm,
    validateFormData,
    addStep,
    removeStep,
    updateStep,
    updateStepType,
    reorderSteps,
    clearAllSteps,
    addTag,
    removeTag,
    handleTestManagementSelection,
    saveScenario,
    generateSteps,
    deleteScenario,

    // Mode and persistence - stable values
    mode,
    persistence.clearDraft,
    persistence.hasDraft,
    persistence.draftAge,

    // UI setters - stable functions
    setCurrentStep,
    setActiveTab,
    setSidebarCollapsed,
    setNewFolderOpen,
    setNewFolderName,
    setNewFolderColor,
    setIsCreatingFolder
  ]);
}
