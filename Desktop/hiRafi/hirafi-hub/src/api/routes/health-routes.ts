/**
 * Health API Routes
 * Provides endpoints for health checks and system information
 */

import { Router, Request, Response } from 'express';
import { testManager } from '../../core/test-manager/index.js';
import { nodeManager } from '../../core/node-manager/index.js';
import { config } from '../../config/index.js';
import { logger } from '../../utils/logger.js';
import { queueService } from '../../services/redis/queueService.js';
import { redisConnection } from '../../services/redis/redisConnection.js';
import { isMongoDBInitialized, getMongoDBInitError } from '../../services/mongo/dbConnection.js';
import { authenticateAdmin } from '../middleware/adminAuthMiddleware.js';
import { AdminRequest } from '../middleware/adminAuthMiddleware.js';

const router = Router();

/**
 * GET /health
 * Health check endpoint
 * @access Admin
 */
router.get('/health', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const availableNodes = nodeManager.getAvailableNodes().length;
    const totalNodes = nodeManager.getAllNodes().length;
    const testQueueStatus = await testManager.getQueueStatus();

    // Get Redis queue status if Redis is enabled
    let redisStatus = {
      connected: false,
      queues: {}
    };

    if (config.connections?.redis?.enabled) {
      try {
        redisStatus.connected = redisConnection.isConnected();
        if (redisStatus.connected) {
          redisStatus.queues = await queueService.getQueueStatus();
        }
      } catch (redisError) {
        logger.error(`Health Check Redis Error: ${redisError}`);
        redisStatus.connected = false;
      }
    }

    // Get MongoDB status
    const mongoDBStatus = {
      initialized: isMongoDBInitialized(),
      error: getMongoDBInitError()?.message
    };

    res.json({
      success: true,
      status: 'UP',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || 'unknown',
      environment: process.env.NODE_ENV || 'development',
      nodes: {
        total: totalNodes,
        available: availableNodes,
        busy: totalNodes - availableNodes
      },
      tests: {
        queued: testQueueStatus.queued,
        running: testQueueStatus.running,
      },
      config: {
        port: config.server.port,
        websocketEnabled: config.connections.websocket.enabled,
        redisEnabled: config.connections.redis.enabled
      },
      connections: {
        redis: redisStatus,
        mongodb: mongoDBStatus
      }
    });
  } catch (error: any) {
    logger.error(`Health Check Error: ${error.message}`);
    res.status(500).json({
      success: false,
      status: 'DOWN',
      error: error.message
    });
  }
});

/**
 * GET /stats
 * Get system statistics
 * @access Admin
 */
router.get('/stats', authenticateAdmin, async (req: AdminRequest, res: Response) => {
  try {
    const nodes = nodeManager.getAllNodes();
    const allTests = await testManager.getAllTests();

    // Calculate node statistics
    const nodeStats = {
      total: nodes.length,
      available: nodes.filter(node => node.status === 'available').length,
      busy: nodes.filter(node => node.status === 'busy').length,
      inactive: nodes.filter(node => node.status === 'inactive').length
    };

    // Calculate test statistics
    const testStats = {
      total: allTests.length,
      queued: allTests.filter(test => test.status === 'queued').length,
      running: allTests.filter(test => test.status === 'running').length,
      completed: allTests.filter(test => test.status === 'completed').length,
      failed: allTests.filter(test => test.status === 'failed').length,
      stopped: allTests.filter(test => test.status === 'stopped').length
    };

    // Memory usage
    const memoryUsage = process.memoryUsage();

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      nodes: nodeStats,
      tests: testStats,
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`
      }
    });
  } catch (error: any) {
    logger.error(`Stats Error: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;