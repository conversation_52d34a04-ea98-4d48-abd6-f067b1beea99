/**
 * HTTP Test Service
 * 
 * Handles HTTP-based test operations with dependency injection.
 * Replaces the singleton HttpTestService with a proper service implementation.
 */

import { BaseService } from '../base/base-service.js';
import { IHttpTestService } from '../../interfaces/http-connector.interface.js';
import { IServiceConfig, IServiceStats } from '../../interfaces/connector-service.interface.js';

/**
 * HTTP Test Service Class
 * Implements HTTP-specific test functionality with dependency injection
 */
export class HttpTestService extends BaseService implements IHttpTestService {
  private hubUrl: string = '';
  private nodeSecretKey: string = '';

  constructor(logger: any, config: IServiceConfig) {
    super('HttpTestService', logger, config);
  }

  /**
   * Initialize the service
   * @param config Service configuration
   */
  protected async doInitialize(config: IServiceConfig): Promise<void> {
    this.validateConfig(config, ['hubUrl', 'secretKey']);
    
    this.hubUrl = config.hubUrl!;
    this.nodeSecretKey = config.secretKey!;
    
    this.logger.debug('HttpTestService: Initialized with hub URL:', this.hubUrl);
  }

  /**
   * Close the service
   */
  protected async doClose(): Promise<void> {
    this.hubUrl = '';
    this.nodeSecretKey = '';
    this.logger.debug('HttpTestService: Closed successfully');
  }

  /**
   * Initialize the service (backward compatibility)
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  async initialize(config?: IServiceConfig): Promise<void>;
  async initialize(hubUrl: string, nodeSecretKey: string): Promise<void>;
  async initialize(configOrHubUrl?: IServiceConfig | string, nodeSecretKey?: string): Promise<void> {
    if (typeof configOrHubUrl === 'string') {
      // Legacy signature
      this.hubUrl = configOrHubUrl;
      this.nodeSecretKey = nodeSecretKey!;
      await super.initialize({ hubUrl: this.hubUrl, secretKey: this.nodeSecretKey });
    } else {
      // New signature
      await super.initialize(configOrHubUrl);
    }
  }

  /**
   * Initialize the service (backward compatibility)
   * @param hubUrl Hub URL
   * @param nodeSecretKey Node secret key
   */
  async initializeLegacy(hubUrl: string, nodeSecretKey: string): Promise<void> {
    await super.initialize({
      hubUrl,
      secretKey: nodeSecretKey
    });
  }

  /**
   * Get test by ID
   * @param testId Test ID
   */
  async getTest(testId: string): Promise<any> {
    try {
      this.logOperation('httpGetTestRequest', {
        url: `${this.hubUrl}/api/tests/${testId}`,
        testId
      });

      const response = await fetch(`${this.hubUrl}/api/tests/${testId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.nodeSecretKey
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to get test: ${response.statusText}`);
      }

      const testData = await response.json();
      
      this.logOperation('httpGetTestSuccess', { testId });
      return testData;
    } catch (error) {
      this.handleError(error, `getTest(${testId})`);
      return null;
    }
  }

  /**
   * Update test status
   * @param testId Test ID
   * @param status New status
   * @param result Test result
   * @param nodeId Node ID
   */
  async updateTestStatus(testId: string, status: string, result: any, nodeId: string): Promise<boolean> {
    try {
      const updateData = {
        status,
        result,
        nodeId,
        timestamp: new Date().toISOString()
      };

      this.logOperation('httpUpdateTestStatusRequest', {
        url: `${this.hubUrl}/api/tests/${testId}/status`,
        testId,
        status,
        nodeId
      });

      const response = await fetch(`${this.hubUrl}/api/tests/${testId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.nodeSecretKey
        },
        body: JSON.stringify(updateData)
      });

      const success = response.ok;
      
      if (success) {
        this.logOperation('httpUpdateTestStatusSuccess', { testId, status });
      } else {
        this.handleError(new Error(`HTTP ${response.status}: ${response.statusText}`), 'updateTestStatus');
      }

      return success;
    } catch (error) {
      this.handleError(error, `updateTestStatus(${testId}, ${status})`);
      return false;
    }
  }

  /**
   * Get next test
   * @param nodeId Node ID
   */
  async getNextTest(nodeId: string): Promise<any> {
    try {
      this.logOperation('httpGetNextTestRequest', {
        url: `${this.hubUrl}/api/tests/next`,
        nodeId
      });

      const response = await fetch(`${this.hubUrl}/api/tests/next`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Node-Key': this.nodeSecretKey
        },
        body: JSON.stringify({ nodeId })
      });

      if (!response.ok) {
        if (response.status === 404) {
          // No tests available
          this.logOperation('httpGetNextTestNoTests', { nodeId });
          return null;
        }
        throw new Error(`Failed to get next test: ${response.statusText}`);
      }

      const testData = await response.json();
      
      this.logOperation('httpGetNextTestSuccess', { nodeId, testId: testData?.id });
      return testData;
    } catch (error) {
      this.handleError(error, `getNextTest(${nodeId})`);
      return null;
    }
  }

  /**
   * Close the service
   */
  async close(): Promise<void> {
    // HTTP test service doesn't need special cleanup
    this.hubUrl = '';
    this.nodeSecretKey = '';
  }

  /**
   * Get service statistics
   */
  getStats(): IServiceStats {
    return super.getStats();
  }

  /**
   * Get detailed HTTP test service statistics
   */
  getDetailedStats(): Record<string, any> {
    const baseStats = super.getStats();
    return {
      ...baseStats,
      hubUrl: this.hubUrl,
      initialized: !!this.hubUrl
    };
  }
}
