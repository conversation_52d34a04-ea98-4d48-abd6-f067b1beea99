import { useState, useEffect, useCallback } from 'react'
import { toast } from '@/lib/utils/toast-utils'
import { settingsApi } from '@/lib/api'

export interface AIModel {
  id: string
  name: string
  api: string
  isActive: boolean
  supportsImageProcessing?: boolean
}

export function useAIModels() {
  const [models, setModels] = useState<AIModel[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  // Using sonner toast directly

  // Şirketin AI modellerini getir
  const fetchModels = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Yeni API yapısını kullan
      const response = await settingsApi.getAIModels()

      if (!response.success) {
        throw new Error(response.error || "AI modelleri yüklenirken bir hata oluştu")
      }

      setModels(response.data?.models || [])
      return response.data?.models || []
    } catch (err: any) {
      setError(err.message)
      console.error("AI modelleri yüklenirken hata:", err)
      return []
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Sayfa yüklendiğinde AI modellerini getir
  useEffect(() => {
    fetchModels()
  }, [fetchModels])

  return {
    models,
    isLoading,
    error,
    fetchModels
  }
}
