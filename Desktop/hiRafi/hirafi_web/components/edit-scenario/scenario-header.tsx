"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON>, Loader2, Save, Trash2, X } from "lucide-react"
import { ExtendedScenarioFormData } from "@/lib/utils/scenario-form-utils"
import { useState } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface ScenarioHeaderProps {
  scenarioData: ExtendedScenarioFormData
  isLoading: boolean
  onSave: () => Promise<void>
  onBack: () => void
  onDelete?: () => void
}

export function ScenarioHeader({ 
  scenarioData, 
  isLoading, 
  onSave, 
  onBack,
  onDelete
}: ScenarioHeaderProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true)
  }

  const handleConfirmDelete = () => {
    setDeleteDialogOpen(false)
    if (onDelete) {
      onDelete()
    }
  }

  return (
    <>
      <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-gradient-to-r from-indigo-600 to-violet-600 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full text-white hover:bg-white/20"
              onClick={onBack}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Edit Scenario</h1>
              <p className="text-white/80">Modify your test scenario details and steps</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={onBack}
              className="bg-transparent border border-white/30 text-white hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200 shadow-sm"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            
            {onDelete && (
              <Button
                onClick={handleDeleteClick}
                className="bg-rose-600/90 text-white hover:bg-rose-600 border-none rounded-lg transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            )}
            
            <Button
              onClick={onSave}
              disabled={!scenarioData.name || scenarioData.steps.length === 0 || isLoading}
              className="bg-white text-indigo-600 hover:bg-white/90 border-none rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </div>
      </header>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md border-none shadow-xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Senaryoyu Sil</DialogTitle>
            <DialogDescription className="text-gray-500 dark:text-gray-400 mt-2">
              Bu senaryoyu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
              <br />
              <span className="text-amber-600 dark:text-amber-500 font-medium mt-2 block">
                ⚠️ Not: Eğer bu senaryo aktif run'larda kullanılıyorsa silme işlemi başarısız olacaktır.
              </span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-3 sm:justify-start mt-5">
            <Button
              type="button"
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              className="sm:w-auto w-full"
            >
              İptal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleConfirmDelete}
              className="bg-rose-600 hover:bg-rose-700 sm:w-auto w-full"
            >
              Senaryoyu Sil
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}