"use client"

import { useState } from "react"
import { AdminLayout } from "@/components/admin/admin-layout"
import { AdminRouteGuard } from "@/lib/api/admin-auth"
import { UserList } from "@/components/admin/user-list"
import { Button } from "@/components/ui/button"
import { UserPlus } from "lucide-react"
import { AddUserDialog } from "@/components/admin/add-user-dialog"

export default function AdminUsersPage() {
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [userListKey, setUserListKey] = useState(0) // Listeyi yenilemek için key kullan

  const handleUserAdded = () => {
    // Kullanıcı eklendiğinde listeyi yenile
    setUserListKey(prev => prev + 1)
  }

  return (
    <AdminRouteGuard>
      <AdminLayout>
        <div className="flex flex-col gap-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
              <p className="text-muted-foreground">
                View, add, edit, and delete users in the system
              </p>
            </div>
            <Button onClick={() => setIsAddUserDialogOpen(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>

          <UserList key={userListKey} />

          <AddUserDialog
            open={isAddUserDialogOpen}
            onOpenChange={setIsAddUserDialogOpen}
            onSuccess={handleUserAdded}
          />
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  )
}
