/**
 * Service Type Definitions
 * Defines types for service management
 */

/**
 * Service registration type
 */
export interface ServiceRegistration<T = any> {
  name: string;
  factory: (...args: any[]) => Promise<T> | T;
  dependencies?: string[];
  lifecycle: 'singleton' | 'transient';
  instance?: T;
  initialized?: boolean;
}

/**
 * Service dependency type
 */
export interface ServiceDependency {
  name: string;
  required: boolean;
  version?: string;
}

/**
 * Service health status type
 */
export interface ServiceHealthStatus {
  healthy: boolean;
  message?: string;
  details?: Record<string, any>;
  timestamp: string;
}

/**
 * Service event type
 */
export interface ServiceEvent {
  type: string;
  serviceName: string;
  timestamp: string;
  data?: any;
}

/**
 * Service lifecycle hooks type
 */
export interface ServiceLifecycleHooks {
  beforeInitialize?: () => Promise<void>;
  afterInitialize?: () => Promise<void>;
  beforeClose?: () => Promise<void>;
  afterClose?: () => Promise<void>;
}

/**
 * Service container options type
 */
export interface ServiceContainerOptions {
  enableLogging?: boolean;
  enableCircularDependencyDetection?: boolean;
  maxResolutionDepth?: number;
  enableHealthChecks?: boolean;
  healthCheckInterval?: number;
}

/**
 * Service resolution context type
 */
export interface ServiceResolutionContext {
  serviceName: string;
  resolutionStack: string[];
  startTime: number;
  dependencies: string[];
}

/**
 * Service metrics type
 */
export interface ServiceMetrics {
  totalServices: number;
  singletonServices: number;
  transientServices: number;
  healthyServices: number;
  unhealthyServices: number;
  totalResolutions: number;
  averageResolutionTime: number;
  circularDependencies: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
}
