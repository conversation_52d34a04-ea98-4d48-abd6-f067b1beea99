/**
 * Network Collector Configuration Domain
 * Handles memory management and performance settings for NetworkCollector
 */

import { getEnv, getEnvBoolean, getEnvInt } from '../utils/env-utils.js';
import { NetworkCollectorConfigDomain } from '../types/config-types.js';
import { NetworkCollectorConfig } from '../../models/types.js';

/**
 * Creates NetworkCollector configuration from environment variables
 * @returns NetworkCollector configuration object
 */
export function createNetworkCollectorConfig(): NetworkCollectorConfigDomain {
  return {
    enabled: getEnvBoolean('NETWORK_COLLECTOR_ENABLED', true),
    
    // Memory management settings
    maxInMemoryEntries: getEnvInt('NETWORK_COLLECTOR_MAX_MEMORY_ENTRIES', 1000),
    enableTemporaryStorage: getEnvBoolean('NETWORK_COLLECTOR_TEMP_STORAGE', true),
    tempFilePrefix: getEnv('NETWORK_COLLECTOR_TEMP_PREFIX', 'network-data'),
    cleanupOnDestroy: getEnvBoolean('NETWORK_COLLECTOR_CLEANUP_ON_DESTROY', true),
    
    // Performance settings
    flushThreshold: getEnvInt('NETWORK_COLLECTOR_FLUSH_THRESHOLD', 800),
    compressionEnabled: getEnvBoolean('NETWORK_COLLECTOR_COMPRESSION', true),
    
    // Monitoring settings
    memoryWarningThreshold: getEnvInt('NETWORK_COLLECTOR_MEMORY_WARNING', 500),
    enableMemoryMonitoring: getEnvBoolean('NETWORK_COLLECTOR_MEMORY_MONITORING', true)
  };
}



/**
 * Validates NetworkCollector configuration
 * @param config Configuration to validate
 * @returns Validated configuration with corrected values
 */
export function validateNetworkCollectorConfig(config: NetworkCollectorConfigDomain): NetworkCollectorConfigDomain {
  const validated = { ...config };
  
  // Ensure maxInMemoryEntries is reasonable (between 100 and 10000)
  if (validated.maxInMemoryEntries < 100) {
    validated.maxInMemoryEntries = 100;
  } else if (validated.maxInMemoryEntries > 10000) {
    validated.maxInMemoryEntries = 10000;
  }
  
  // Ensure flushThreshold is less than maxInMemoryEntries
  if (validated.flushThreshold >= validated.maxInMemoryEntries) {
    validated.flushThreshold = Math.floor(validated.maxInMemoryEntries * 0.8);
  }
  
  // Ensure memoryWarningThreshold is less than flushThreshold
  if (validated.memoryWarningThreshold >= validated.flushThreshold) {
    validated.memoryWarningThreshold = Math.floor(validated.flushThreshold * 0.6);
  }
  
  // Ensure tempFilePrefix is not empty
  if (!validated.tempFilePrefix || validated.tempFilePrefix.trim() === '') {
    validated.tempFilePrefix = 'network-data';
  }
  
  return validated;
}

/**
 * Gets the current NetworkCollector configuration with validation
 * @returns Validated NetworkCollector configuration
 */
export function getNetworkCollectorConfig(): NetworkCollectorConfig {
  const domainConfig = createNetworkCollectorConfig();
  const validatedConfig = validateNetworkCollectorConfig(domainConfig);

  // Convert from domain config to NetworkCollectorConfig (remove 'enabled' field)
  const { enabled, ...networkConfig } = validatedConfig;
  return networkConfig as NetworkCollectorConfig;
}
