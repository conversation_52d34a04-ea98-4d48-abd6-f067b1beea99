"use client"

import React, { useState, useEffect } from 'react'

interface HtmlEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
}

export function HtmlEditor({ value, onChange, placeholder = "Enter HTML content..." }: HtmlEditorProps) {
  const [htmlValue, setHtmlValue] = useState<string>(value)

  // Update internal state when external value changes
  useEffect(() => {
    setHtmlValue(value)
  }, [value])

  // Handle changes in the HTML textarea
  const handleHtmlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setHtmlValue(newValue)
    onChange(newValue)
  }

  return (
    <div className="border rounded-md">
      {/* Simple textarea with fixed height */}
      <textarea
        value={htmlValue}
        onChange={handleHtmlChange}
        placeholder={placeholder}
        className="w-full p-4 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        style={{ height: "400px" }}
      />

      {/* Footer with placeholders info */}
      <div className="p-3 border-t bg-gray-50 text-xs text-gray-500">
        <strong>Available placeholders:</strong> {`{{name}}, {{email}}, {{company}}, {{phone}}, {{message}}, and {{preferredDate}}`}
      </div>
    </div>
  )
}
